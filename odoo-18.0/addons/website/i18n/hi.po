# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website
# 
# Translators:
# <PERSON><PERSON><PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2025
# <PERSON>, 2025
# <PERSON><PERSON><PERSON>, 2025
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 13:22+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Hindi (https://app.transifex.com/odoo/teams/41243/hi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_image_gallery/options.js:0
msgid " Add Images"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel_minimal
msgid ""
"\" A trusted partner for growth. <br/>Professional, efficient, and always "
"ahead of the curve. \""
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel_minimal
msgid ""
"\" Outstanding service and results! <br/>They exceeded our expectations in "
"every project. \""
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel_minimal
msgid ""
"\" This company transformed our business. <br/>Their solutions are "
"innovative and reliable. \""
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_blockquote
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
msgid ""
"\" Write a quote here from one of your customers. Quotes are a great way to "
"build confidence in your products or services. \""
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "\" alert with a"
msgstr ""

#. module: website
#. odoo-python
#: code:addons/website/controllers/form.py:0
msgid "\"%(company)s form submission\" <%(email)s>"
msgstr ""

#. module: website
#. odoo-python
#: code:addons/website/models/website_rewrite.py:0
msgid "\"URL from\" can not be empty."
msgstr ""

#. module: website
#. odoo-python
#: code:addons/website/models/website_rewrite.py:0
msgid "\"URL to\" can not be empty."
msgstr ""

#. module: website
#. odoo-python
#: code:addons/website/models/website_rewrite.py:0
msgid ""
"\"URL to\" cannot be set to \"/\". To change the homepage content, use the "
"\"Homepage URL\" field in the website settings or the page properties on any"
" custom page."
msgstr ""

#. module: website
#. odoo-python
#: code:addons/website/models/website_rewrite.py:0
msgid "\"URL to\" cannot be set to an existing page."
msgstr ""

#. module: website
#. odoo-python
#: code:addons/website/models/website_rewrite.py:0
msgid "\"URL to\" cannot contain parameter %s which is not used in \"URL from\"."
msgstr ""

#. module: website
#. odoo-python
#: code:addons/website/models/website_rewrite.py:0
msgid "\"URL to\" is invalid: %s"
msgstr ""

#. module: website
#. odoo-python
#: code:addons/website/models/website_rewrite.py:0
msgid "\"URL to\" must contain parameter %s used in \"URL from\"."
msgstr ""

#. module: website
#. odoo-python
#: code:addons/website/models/website_rewrite.py:0
msgid "\"URL to\" must start with a leading slash."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_banner
msgid ""
"\"Write a quote here from one of your customers. Quotes are a great way to "
"build confidence in your products.\""
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__page_count
msgid "# Visited Pages"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__visit_count
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "# Visits"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.navbar
msgid "#{_navbar_name if _navbar_name else 'Main'}"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "#{display_label} #{depth}"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "#{heading_label} #{depth}"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers_charts
msgid "$ 32M"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "$1.50"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_pricelist_boxed
msgid "$12.00"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_pricelist_boxed
msgid "$13.00"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_pricelist_boxed
msgid "$13.50"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_pricelist_boxed
msgid "$14.50"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_pricelist_boxed
msgid "$15.00"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_pricelist_boxed
msgid "$16.00"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "$25.00"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "$26.00"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "$28.00"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_pricelist_cafe
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "$3.00"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_pricelist_cafe
msgid "$3.50"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_pricelist_cafe
msgid "$4.00"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_pricelist_cafe
msgid "$4.10"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_pricelist_cafe
msgid "$4.25"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_pricelist_cafe
msgid "$4.50"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "$5.00"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/views/page_list.js:0
msgid "%s record(s) selected, are you sure you want to publish them all?"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "&amp;lt;head&amp;gt; and &amp;lt;/body&amp;gt;"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_public_pages
msgid "' did not match any pages."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_hybrid
msgid "' did not match anything."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_hybrid
msgid ""
"' did not match anything.\n"
"                        Results are displayed for '"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/web_editor.xml:0
msgid "' to link to an anchor."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/web_editor.xml:0
msgid ""
"' to search a page.\n"
"                    '"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_searchbar/000.xml:0
#: model_terms:ir.ui.view,arch_db:website.list_website_public_pages
msgid "'. Showing results for '"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "+ Field"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "+ New Website"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.template_footer_centered
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_descriptive
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
msgid "******-555-5556"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "******-555-5556\""
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_mosaic_template
msgid "+12"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_contact_info
msgid "+1************"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers_charts
msgid "+25.000"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid ", author:"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
".\n"
"                                                The website will still work if you reject or discard those cookies."
msgstr ""

#. module: website
#: model:website,contact_us_button_url:website.default_website
#: model:website,contact_us_button_url:website.website2
#: model_terms:ir.ui.view,arch_db:website.layout
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "/contactus"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_key_images
msgid "01"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_key_images
msgid "02"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_key_images
msgid "03"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_key_images
msgid "04"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "1 km"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "1/2 - 1/2"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "1/3 - 2/3"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "1/4 - 3/4"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "10 m"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "100 km"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "100 m"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "100%"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "1000 km"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "12"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers_showcase
msgid "12k"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "15 km"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown
msgid "16"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "2 <span class=\"visually-hidden\">(current)</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "2 km"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "2.5 m"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "20 m"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "200 km"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "200 m"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "2000 km"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_mosaic_template
msgid "22%"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_wave
msgid "24/7 Customer Support"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_key_benefits
msgid "24/7 Support"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "25%"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid ""
"250 Executive Park Blvd, Suite 3400 <br/> San Francisco CA 94134 <br/>United"
" States"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_centered
msgid ""
"250 Executive Park Blvd, Suite 3400 • San Francisco CA 94134 • United States"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown
msgid "30"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "30 km"
msgstr ""

#. module: website
#: model:ir.model.fields.selection,name:website.selection__website_page_properties__redirect_type__301
#: model:ir.model.fields.selection,name:website.selection__website_rewrite__redirect_type__301
#: model_terms:ir.ui.view,arch_db:website.view_rewrite_search
msgid "301 Moved permanently"
msgstr ""

#. module: website
#: model:ir.model.fields.selection,name:website.selection__website_page_properties__redirect_type__302
#: model:ir.model.fields.selection,name:website.selection__website_rewrite__redirect_type__302
#: model_terms:ir.ui.view,arch_db:website.view_rewrite_search
msgid "302 Moved temporarily"
msgstr ""

#. module: website
#: model:ir.model.fields.selection,name:website.selection__website_rewrite__redirect_type__308
#: model_terms:ir.ui.view,arch_db:website.view_rewrite_search
msgid "308 Redirect / Rewrite"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_contact_info
msgid "3575 Fake Buena Vista Avenue"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "4 km"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
msgid "4 steps"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "400 km"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "400 m"
msgstr ""

#. module: website
#: model:ir.model.fields.selection,name:website.selection__website_rewrite__redirect_type__404
#: model_terms:ir.ui.view,arch_db:website.view_rewrite_search
msgid "404 Not Found"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown
msgid "45"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers_showcase
msgid "45%"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "5 m"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "50 km"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "50 m"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "50%"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_cta_card
msgid ""
"50,000+ companies run Odoo <br class=\"d-none d-lg-inline\"/>to grow their "
"businesses."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_call_to_action
#: model_terms:ir.ui.view,arch_db:website.template_footer_call_to_action
msgid "50,000+ companies run Odoo to grow their businesses."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_cta_box
msgid "50,000+ companies run Odoo<br/>to grow their businesses."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_cta_mockups
msgid "50,000+ companies trust Odoo."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.s_numbers_charts
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "75%"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers_charts
msgid ""
"75% of clients use the service for over a decade consistently.<br/>This "
"showcases remarkable loyalty and satisfaction with the quality provided."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "8 km"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.xml:0
msgid ": Once loaded, follow the"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
msgid "<b>Add</b> the selected image."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
msgid "<b>Click Edit</b> dropdown"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
msgid "<b>Click Edit</b> to start designing your homepage."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
msgid "<b>Click on a snippet</b> to access its options menu."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
msgid "<b>Click on a text</b> to start editing it."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
msgid "<b>Click</b> on this column to access its options."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
msgid "<b>Click</b> on this header to configure it."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
msgid "<b>Click</b> on this option to change the %s of the block."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
msgid ""
"<b>Customize</b> any block through this menu. Try to change the background "
"color of this block."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
msgid ""
"<b>Customize</b> any block through this menu. Try to change the background "
"image of this block."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
msgid "<b>Designed</b> <br/>for Companies"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_descriptive
msgid "<b>Designed</b> for companies"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
msgid "<b>Double click on an image</b> to change it with one of your choice."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_descriptive
msgid ""
"<b>My Company</b><br/>250 Executive Park Blvd, Suite 3400 <br/> San "
"Francisco CA 94134 <br/>United States"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
msgid "<b>Select</b> a %s."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
msgid "<b>Select</b> a Color Palette."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
msgid "<b>Slide</b> this button to change the %s padding"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
msgid "<b>Slide</b> this button to change the column size."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_edit_robots
msgid ""
"<br/><br/>\n"
"                    Example of rule:<br/>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team_detail
msgid "<br/>Alexander Rivera"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_references_social
msgid "<br/>Amsterdam"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team_detail
msgid "<br/>Daniel Foster"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_empowerment
msgid ""
"<br/>Delivering tailored, innovative tools to help you overcome challenges "
"and<br/> achieve your goals, ensuring your journey is fully "
"supported.<br/><br/>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team_detail
msgid "<br/>Emily Carter"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_sidegrid
msgid ""
"<br/>Every groundbreaking innovation, whether meticulously engineered or "
"born from spontaneous creativity, contains stories waiting to be "
"discovered.<br/><br/>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_references_social
msgid "<br/>Firenze"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers_list
msgid ""
"<br/>From revenue growth to customer retention and market expansion, our key"
" metrics of company achievements underscore our strategic prowess and "
"dedication to driving sustainable business success."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team_detail
msgid "<br/>James Mitchell"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_references_social
msgid "<br/>Madrid"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_references_social
msgid "<br/>Nairobi"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team_detail
msgid "<br/>Olivia Reed"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_shape_image
msgid ""
"<br/>Our product line offers a range of innovative solutions designed to "
"meet your needs. Each product is crafted for quality and reliability."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_cover
msgid ""
"<br/>Sell online easily with a user-friendly platform that streamlines all "
"the steps, including setup, inventory management, and payment "
"processing.<br/>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team_detail
msgid "<br/>Sophia Benett"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_horizontal
msgid ""
"<br/>The first step in the onboarding process is <b>account creation</b>. "
"This involves signing up on our platform using your email address or social "
"media accounts. Once you’ve created an account, you will receive a "
"confirmation email with a link to activate your account. Upon activation, "
"you’ll be prompted to complete your profile, which includes setting up your "
"preferences, adding any necessary payment information, and selecting the "
"initial features or modules you wish to use."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_banner
msgid ""
"<br/>This is a simple hero unit, a simple jumbotron-style component for "
"calling extra attention to featured content or information.<br/><br/>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_horizontal
msgid ""
"<br/>Users can participate in beta testing programs, providing feedback on "
"upcoming releases and influencing the future direction of the platform. By "
"staying current with updates, you can take advantage of the latest tools and"
" features, ensuring your business remains competitive and efficient."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_big_number
msgid ""
"<font class=\"text-gradient\" style=\"background-image: linear-gradient(0deg, rgb(222, 222, 222) 25%, rgb(29, 32, 48) 80%);\">\n"
"                            87%\n"
"                        </font>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
msgid ""
"<i class=\"fa fa-1x fa-fw fa-envelope "
"me-2\"/><span><EMAIL></span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_call_to_action
msgid ""
"<i class=\"fa fa-1x fa-fw fa-map-marker me-2\"/>250 Executive Park Blvd, "
"Suite 3400 • San Francisco CA 94134 • United States"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.header_text_element
msgid ""
"<i class=\"fa fa-1x fa-fw fa-phone me-1\"/>\n"
"                        <span class=\"o_force_ltr\"><small>******-555-5556</small></span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.header_text_element
msgid "<i class=\"fa fa-1x fa-fw fa-phone me-1\"/>͏"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_controller_pages_kanban_view
msgid "<i class=\"fa fa-2x fa-arrow-right text-primary\" title=\"URL\"/>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_cta_card
msgid "<i class=\"fa fa-check text-o-color-1\" role=\"img\"/>  Complete access"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_cta_card
msgid "<i class=\"fa fa-check text-o-color-1\" role=\"img\"/>  Quick support"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_cta_card
msgid "<i class=\"fa fa-check text-o-color-1\" role=\"img\"/>  Wonderful experience"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "<i class=\"fa fa-check text-success\" role=\"img\"/>  24/7 toll-free support"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "<i class=\"fa fa-check text-success\" role=\"img\"/>  Access all modules"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "<i class=\"fa fa-check text-success\" role=\"img\"/>  Account management"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid ""
"<i class=\"fa fa-check text-success\" role=\"img\"/>  All modules &amp; "
"features"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid ""
"<i class=\"fa fa-check text-success\" role=\"img\"/>  Complete CRM for any "
"team"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "<i class=\"fa fa-check text-success\" role=\"img\"/>  Email support"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "<i class=\"fa fa-check text-success\" role=\"img\"/>  Limited customization"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid ""
"<i class=\"fa fa-check text-success\" role=\"img\"/>  Sales &amp; marketing "
"for 2"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "<i class=\"fa fa-check text-success\" role=\"img\"/>  Unlimited CRM support"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "<i class=\"fa fa-check text-success\" role=\"img\"/>  Unlimited customization"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_contact_info
msgid ""
"<i class=\"fa fa-fw fa-building-o\" role=\"presentation\"/>\n"
"                        Office"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<i class=\"fa fa-fw fa-circle\"/> Circles"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_contact_info
msgid ""
"<i class=\"fa fa-fw fa-envelope-o\" role=\"presentation\"/>\n"
"                        Email"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<i class=\"fa fa-fw fa-heart\"/> Hearts"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_cta_badge
#: model_terms:ir.ui.view,arch_db:website.s_discovery
#: model_terms:ir.ui.view,arch_db:website.s_empowerment
msgid ""
"<i class=\"fa fa-fw fa-info-circle o_not-animable\" role=\"img\"/>  What do "
"you want to promote&amp;nbsp;?"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_contact_info
msgid ""
"<i class=\"fa fa-fw fa-phone\" role=\"presentation\"/>\n"
"                        Phone"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<i class=\"fa fa-fw fa-refresh me-1\"/> Replace Icon"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<i class=\"fa fa-fw fa-square\"/> Squares"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<i class=\"fa fa-fw fa-star\"/> Stars"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<i class=\"fa fa-fw fa-thumbs-up\"/> Thumbs"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid "<i class=\"fa fa-fw o_button_icon fa-circle text-danger\" title=\"Offline\"/>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid ""
"<i class=\"fa fa-fw o_button_icon fa-circle text-success\" "
"title=\"Connected\"/>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_kanban_view
msgid "<i class=\"fa fa-globe me-1\" title=\"Website\"/>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.page_404
msgid ""
"<i class=\"fa fa-info-circle\"/> Edit the content below this line to adapt "
"the default <strong>Page not found</strong> page."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.protected_403
msgid ""
"<i class=\"fa fa-lock fa-2x\"/><br/>\n"
"                            <span class=\"mt-1\">A password is required to access this page.</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.protected_403
msgid ""
"<i class=\"fa fa-lock fa-2x\"/><br/>\n"
"                            <span class=\"mt-1\">Wrong password</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
msgid ""
"<i class=\"fa fa-map-marker fa-fw me-2\"/><span class=\"o_force_ltr\">3575 "
"Fake Buena Vista Avenue</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
msgid ""
"<i class=\"fa fa-phone fa-fw me-2\"/><span class=\"o_force_ltr\">+1 "
"************</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "<i class=\"fa fa-times text-danger\" role=\"img\"/>  No customization"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "<i class=\"fa fa-times text-danger\" role=\"img\"/>  No support"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-calendar fa-fw me-2\"/>\n"
"                            <b>Events</b>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-eye fa-fw me-2\"/>\n"
"                            <b>About us</b>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-group fa-fw me-2\"/>\n"
"                            <b>Partners</b>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-handshake-o fa-fw me-2\"/>\n"
"                            <b>Services</b>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-headphones fa-fw me-2\"/>\n"
"                            <b>Help center</b>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-map-o fa-fw me-2\"/>\n"
"                            <b>Guides</b>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-newspaper-o fa-fw me-2\"/>\n"
"                            <b>Our blog</b>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-star-o fa-fw me-2\"/>\n"
"                            <b>Customers</b>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-tags fa-fw me-2\"/>\n"
"                            <b>Products</b>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "<i class=\"s_mega_menu_thumbnails_icon fa fa-comments me-2\"/> Contact us"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "<i class=\"s_mega_menu_thumbnails_icon fa fa-cube me-2\"/> Free returns"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid ""
"<i class=\"s_mega_menu_thumbnails_icon fa fa-shopping-basket me-2\"/> Pickup"
" in store"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid ""
"<i class=\"s_mega_menu_thumbnails_icon fa fa-truck me-2\"/> Express delivery"
msgstr ""

#. module: website
#. odoo-python
#: code:addons/website/controllers/form.py:0
msgid "<p>Attached files: </p>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid ""
"<select id=\"exampleSelect\" class=\"form-select\">\n"
"                                                            <option selected=\"true\">Open this select menu</option>\n"
"                                                            <option value=\"1\">One</option>\n"
"                                                            <option value=\"2\">Two</option>\n"
"                                                            <option value=\"3\">Three</option>\n"
"                                                        </select>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.header_text_element
msgid ""
"<small class=\"d-flex align-items-center\">\n"
"                        <i class=\"fa fa-1x fa-fw fa-usd fa-stack me-1\"/>\n"
"                        Low Price Guarantee\n"
"                    </small>\n"
"                    <small class=\"d-flex align-items-center\">\n"
"                        <i class=\"fa fa-1x fa-fw fa-shopping-basket fa-stack me-1\"/>\n"
"                        30 Days Online Returns\n"
"                    </small>\n"
"                    <small class=\"d-flex align-items-center\">\n"
"                        <i class=\"fa fa-1x fa-fw fa-truck fa-stack me-1\"/>\n"
"                        Standard Shipping\n"
"                    </small>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid ""
"<small class=\"text-center\">Instant setup, satisfied or reimbursed.</small>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<small class=\"text-muted\">\n"
"                                        Block 3rd-party services that track users (e.g. YouTube, Google Maps, Facebook...) when the user has not given their consent.\n"
"                                    </small>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<small class=\"text-muted\">\n"
"                                    <i class=\"fa fa-info\"/>: type some of the first chars after 'google' is enough, we'll guess the rest.\n"
"                                </small>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "<small class=\"text-muted\">(rounded-0)</small>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "<small class=\"text-muted\">(rounded-1)</small>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "<small class=\"text-muted\">(rounded-2)</small>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "<small class=\"text-muted\">(rounded-3)</small>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "<small class=\"text-muted\">(shadow)</small>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "<small class=\"text-muted\">(shadow-lg)</small>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "<small class=\"text-muted\">(shadow-sm)</small>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid "<small class=\"text-muted\">13/06/2019</small>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid "<small class=\"text-muted\">21/03/2021</small>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid "<small class=\"text-muted\">25/12/2024</small>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "<small class=\"text-muted\">Last updated 3 mins ago</small>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid ""
"<small id=\"emailHelp\" class=\"form-text text-muted\">Form field help "
"text</small>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid ""
"<small id=\"emailHelp\" class=\"form-text text-muted\">We'll never share "
"your email with anyone else.</small>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.header_text_element
msgid ""
"<small>\n"
"                            <i class=\"fa fa-1x fa-fw fa-envelope me-1\"/>\n"
"                            <EMAIL>\n"
"                        </small>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.header_text_element
msgid ""
"<small><i class=\"fa fa-1x fa-fw fa-envelope me-1\"/> "
"<EMAIL></small>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.header_text_element
msgid "<small>Free Returns and Standard Shipping</small>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "<small>TABS</small>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "<span class=\"badge bg-primary\">14</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel_intro
msgid ""
"<span class=\"carousel-control-next-icon\" aria-hidden=\"true\"/>\n"
"                            <span class=\"visually-hidden\">Next</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery
msgid ""
"<span class=\"carousel-control-next-icon\" aria-hidden=\"true\"/>\n"
"                        <span class=\"visually-hidden\">Next</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel_minimal
msgid ""
"<span class=\"carousel-control-next-icon\" aria-hidden=\"true\"/>\n"
"                <span class=\"visually-hidden\">Next</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel_intro
msgid ""
"<span class=\"carousel-control-prev-icon\" aria-hidden=\"true\"/>\n"
"                            <span class=\"visually-hidden\">Previous</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery
msgid ""
"<span class=\"carousel-control-prev-icon\" aria-hidden=\"true\"/>\n"
"                        <span class=\"visually-hidden\">Previous</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel_minimal
msgid ""
"<span class=\"carousel-control-prev-icon\" aria-hidden=\"true\"/>\n"
"                    <span class=\"visually-hidden\">Previous</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid ""
"<span class=\"carousel-control-prev-icon\" aria-hidden=\"true\"/>\n"
"                <span class=\"visually-hidden\">Previous</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid ""
"<span class=\"d-block p-2 small\">\n"
"                            <b>Discover our new products</b>\n"
"                        </span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_key_benefits
msgid "<span class=\"display-3-fs text-o-color-1\">1</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_key_benefits
msgid "<span class=\"display-3-fs text-o-color-1\">2</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_key_benefits
msgid "<span class=\"display-3-fs text-o-color-1\">3</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_adventure
msgid ""
"<span class=\"display-4-fs\">Embark on your</span>\n"
"                <br/>Next Adventure"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers_grid
msgid "<span class=\"display-5\">$50M</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers_grid
msgid "<span class=\"display-5\">+225</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers_grid
msgid "<span class=\"display-5\">100,000</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers_grid
msgid "<span class=\"display-5\">235,403</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers_grid
msgid "<span class=\"display-5\">45,958</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers_grid
msgid "<span class=\"display-5\">4x</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers_grid
msgid "<span class=\"display-5\">54%</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers_grid
msgid "<span class=\"display-5\">85%</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_accordion
msgid "<span class=\"flex-grow-1\">How can I contact customer support ?</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_accordion
msgid "<span class=\"flex-grow-1\">What is your return policy ?</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_accordion
msgid "<span class=\"flex-grow-1\">What services does your company offer ?</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers_list
msgid "<span class=\"h2-fs\">$50M</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers_list
msgid "<span class=\"h2-fs\">100,000</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers_list
msgid "<span class=\"h2-fs\">15%</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers_list
msgid "<span class=\"h2-fs\">20+</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers_list
msgid "<span class=\"h2-fs\">4x</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers_list
msgid "<span class=\"h2-fs\">85%</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.language_selector_inline
msgid "<span class=\"list-inline-item\">|</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_controller_pages_kanban_view
msgid "<span class=\"me-1\">On:</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<span class=\"mx-2\">/</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<span class=\"mx-2\">to</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline_list
msgid ""
"<span class=\"o_dot o_not_editable position-absolute translate-middle-x rounded-circle pe-none text-o-color-1\" contenteditable=\"false\"/>\n"
"                        <small class=\"text-muted\">Apr 03, 2024</small>\n"
"                        <strong>New Dashboard Features for Custom Reports</strong>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline_list
msgid ""
"<span class=\"o_dot o_not_editable position-absolute translate-middle-x rounded-circle pe-none text-o-color-1\" contenteditable=\"false\"/>\n"
"                        <small class=\"text-muted\">Aug 27, 2024</small>\n"
"                        <strong>Improved Security Protocols Implemented</strong>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline_list
msgid ""
"<span class=\"o_dot o_not_editable position-absolute translate-middle-x rounded-circle pe-none text-o-color-1\" contenteditable=\"false\"/>\n"
"                        <small class=\"text-muted\">Dec 22, 2024</small>\n"
"                        <strong>Advanced Analytics Tools Introduced</strong>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline_list
msgid ""
"<span class=\"o_dot o_not_editable position-absolute translate-middle-x rounded-circle pe-none text-o-color-1\" contenteditable=\"false\"/>\n"
"                        <small class=\"text-muted\">Feb 11, 2024</small>\n"
"                        <strong>Enhanced User Interface for Better Navigation</strong>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline_list
msgid ""
"<span class=\"o_dot o_not_editable position-absolute translate-middle-x rounded-circle pe-none text-o-color-1\" contenteditable=\"false\"/>\n"
"                        <small class=\"text-muted\">Jun 15, 2024</small>\n"
"                        <strong>Integrated Multi-Language Support Added</strong>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline_list
msgid ""
"<span class=\"o_dot o_not_editable position-absolute translate-middle-x rounded-circle pe-none text-o-color-1\" contenteditable=\"false\"/>\n"
"                        <small class=\"text-muted\">Oct 09, 2024</small>\n"
"                        <strong>Mobile App Compatibility Expanded</strong>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_copyright_company_name
msgid ""
"<span class=\"o_footer_copyright_name me-2\">Copyright &amp;copy; Company "
"name</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.header_text_element
msgid "<span class=\"o_force_ltr\">******-555-5556</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel_minimal
msgid ""
"<span class=\"o_small\">\n"
"                                    <strong>Iris DOE</strong><br/>\n"
"                                    <span class=\"text-muted\">Manager of MyCompany</span>\n"
"                                </span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel_minimal
msgid ""
"<span class=\"o_small\">\n"
"                                    <strong>Jane DOE</strong><br/>\n"
"                                    <span class=\"text-muted\">CEO of MyCompany</span>\n"
"                                </span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel_minimal
msgid ""
"<span class=\"o_small\">\n"
"                                    <strong>John DOE</strong><br/>\n"
"                                    <span class=\"text-muted\">CCO of MyCompany</span>\n"
"                                </span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_banner
msgid ""
"<span class=\"o_small\">\n"
"                                    <strong>Paul Dawson</strong><br/>\n"
"                                    <span>CEO of MyCompany</span>\n"
"                                </span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_blockquote
msgid ""
"<span class=\"o_small\">\n"
"                    <strong>Paul Dawson</strong><br/>\n"
"                    <span class=\"text-muted\">CEO of MyCompany</span>\n"
"                </span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid "<span class=\"o_stat_text\">Connected</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid "<span class=\"o_stat_text\">Offline</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookies_bar
msgid ""
"<span class=\"pe-1\">We use cookies to provide you a better user experience "
"on this website.</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_badge
msgid ""
"<span class=\"s_badge badge text-bg-secondary o_animable\" data-name=\"Badge\" data-vxml=\"001\">\n"
"        <i class=\"fa fa-fw fa-folder o_not-animable\"/>Category\n"
"    </span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid ""
"<span class=\"s_number display-1-fs\">12k</span><br/>\n"
"                                <span class=\"h5-fs\">Useful options</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid ""
"<span class=\"s_number display-1-fs\">45%</span><br/>\n"
"                                <span class=\"h5-fs\">More leads</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid ""
"<span class=\"s_number display-1-fs\">8+</span><br/>\n"
"                                <span class=\"h5-fs\">Amazing pages</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar
msgid "<span class=\"s_progress_bar_text small\">80%</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
msgid "<span class=\"s_website_form_label_content\">Company</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
msgid "<span class=\"s_website_form_label_content\">Email To</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Email</span>\n"
"                                                        <span class=\"s_website_form_mark\"> *</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Name</span>\n"
"                                                        <span class=\"s_website_form_mark\"> *</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.s_website_form
msgid "<span class=\"s_website_form_label_content\">Phone Number</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Question</span>\n"
"                                                        <span class=\"s_website_form_mark\"> *</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Subject</span>\n"
"                                                        <span class=\"s_website_form_mark\"> *</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form
msgid ""
"<span class=\"s_website_form_label_content\">Subject</span>\n"
"                                <span class=\"s_website_form_mark\"> *</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form
msgid "<span class=\"s_website_form_label_content\">Your Company</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form
msgid ""
"<span class=\"s_website_form_label_content\">Your Email</span>\n"
"                                <span class=\"s_website_form_mark\"> *</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form
msgid ""
"<span class=\"s_website_form_label_content\">Your Name</span>\n"
"                                <span class=\"s_website_form_mark\"> *</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form
msgid ""
"<span class=\"s_website_form_label_content\">Your Question</span>\n"
"                                <span class=\"s_website_form_mark\"> *</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_controller_pages_form_view
msgid "<span invisible=\"name_slugified\" class=\"bg-300\">...</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_page_properties_view_form
msgid ""
"<span invisible=\"website_indexed\">Won't appear in search engine "
"results</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_controller_pages_form_view
msgid "<span>/model/</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid ""
"<strong class=\"h2-fs\">$ 15.00</strong>\n"
"                                <small class=\"text-muted\">/ month</small>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid ""
"<strong class=\"h2-fs\">$ 25.00</strong>\n"
"                                <small class=\"text-muted\">/ month</small>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid ""
"<strong class=\"h2-fs\">$ 45.00</strong>\n"
"                                <small class=\"text-muted\">/ month</small>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_list
msgid "<strong>Are links to other websites approved?</strong>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_list
msgid "<strong>Can you trust our partners?</strong>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_edit_third_party_domains
msgid ""
"<strong>Google services:</strong> Google Maps, Google Analytics, Google Tag "
"Manager, etc."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_list
msgid "<strong>How is your data secured?</strong>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_list
msgid "<strong>Is the website user-friendly?</strong>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_edit_third_party_domains
msgid ""
"<strong>Social platforms:</strong> Facebook, Instagram, Twitter, TikTok"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_edit_third_party_domains
msgid ""
"<strong>Video hosting platforms:</strong> YouTube, Vimeo, Dailymotion, Youku"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_list
msgid "<strong>What sets us apart?</strong>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_list
msgid "<strong>What support do we offer?</strong>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_opening_hours
msgid "<u>Friday</u><br/>8.00am-6.00pm"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_opening_hours
msgid "<u>Monday</u><br/>8.00am-6.00pm"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_opening_hours
msgid "<u>Saturday</u><br/>8.00am-6.00pm"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_opening_hours
msgid "<u>Sunday</u><br/>Closed"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_opening_hours
msgid "<u>Thursday</u><br/>8.00am-6.00pm"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_opening_hours
msgid "<u>Tuesday</u><br/>8.00am-6.00pm"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_opening_hours
msgid "<u>Wednesday</u><br/>8.00am-12.00am"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_opening_hours
msgid "<u><EMAIL></u>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"A CDN helps you serve your website’s content with high availability and high"
" performance to any visitor wherever they are located."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart
msgid "A Chart Title"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_title
msgid "A Deep Dive into Innovation and Excellence"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
msgid ""
"A Google Map error occurred. Make sure to read the key configuration popup "
"carefully."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_punchy
msgid "A PUNCHY HEADLINE"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid ""
"A buttery, flaky pastry with a golden-brown crust, perfect for breakfast or "
"a light snack."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_card
msgid ""
"A card is a flexible and extensible content container. It includes options "
"for headers and footers, a wide variety of content, contextual background "
"colors, and powerful display options."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_pricelist_cafe
msgid ""
"A classic black tea blend infused with the aromatic essence of bergamot, "
"offering a fragrant, citrusy flavor."
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_ir_actions_server__website_published
#: model:ir.model.fields,help:website.field_ir_cron__website_published
msgid ""
"A code server action can be executed from the website, using a dedicated "
"controller. The address is <base>/website/action/<website_path>. Set this "
"field as True to allow users to run this action. If it is set to False the "
"action cannot be run through the website."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_color_blocks_2
msgid "A color block"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_images_constellation
msgid "A constellation of amazing solutions tailored for your needs"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid ""
"A creamy, smooth cheesecake with a graham cracker crust, topped with a layer"
" of fresh fruit or chocolate ganache."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid ""
"A crusty loaf with a chewy interior, made with a naturally fermented "
"sourdough starter for a tangy flavor."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_freegrid
msgid "A deep dive into what makes our products innovative"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_pricelist_boxed
msgid ""
"A delicious mix of four toppings: mushrooms, artichokes, ham, and olives, "
"all on a bed of mozzarella and tomato sauce."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_showcase
msgid ""
"A feature section allows you to clearly showcase the main benefits and "
"unique aspects of your product."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_showcase
msgid ""
"A features section highlights your product’s key attributes, engaging "
"visitors and boosting conversions."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
msgid ""
"A font with the same name already exists.\n"
"Try renaming the uploaded file."
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_website_snippet_filter__field_names
msgid "A list of comma-separated field names"
msgstr ""

#. module: website
#: model:website.configurator.feature,description:website.feature_module_stores_locator
msgid "A map and a listing of your stores"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid ""
"A moist, red-hued cake with layers of cream cheese frosting, perfect for any"
" special occasion."
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__visit_count
msgid ""
"A new visit is considered if last connection was more than 8 hours ago."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "A second item"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid ""
"A spiced cake loaded with grated carrots, nuts, and a hint of cinnamon, "
"topped with a tangy cream cheese frosting."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "A third item"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid ""
"A timeline is a graphical representation on which important events are "
"marked."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid ""
"A timeline is a visual display that highlights significant events in "
"chronological order."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_pricelist_cafe
msgid ""
"A vibrant spot known for its expertly crafted coffee, sourced directly from "
"farmers and roasted to perfection."
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__is_connected
msgid ""
"A visitor is considered as connected if his last page view was within the "
"last 5 minutes."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/web_editor.xml:0
msgid "AI"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
msgid "API Key"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_groups
msgid "About"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_personal_s_image_text
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_timeline_s_text_block_h2
msgid "About Me"
msgstr ""

#. module: website
#: model:website.configurator.feature,name:website.feature_page_about_us
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_s_cover
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_s_text_block_h1
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_4_s_text_block_h2
msgid "About Us"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_shape_image
msgid "About our product line"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.aboutus
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
msgid "About us"
msgstr ""

#. module: website
#. odoo-python
#: code:addons/website/models/website.py:0
msgid "Access Denied"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__access_token
msgid "Access Token"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.protected_403
msgid "Access to this page"
msgstr ""

#. module: website
#: model:ir.model.constraint,message:website.constraint_website_visitor_access_token_unique
msgid "Access token should be unique."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Accessories"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Accordion"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Accordion Image"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Accounts are usable across all your multiple websites"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_2_s_three_columns
msgid ""
"Achieve holistic health with personalized nutritional advice that "
"complements your workouts, promoting overall well-being."
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_rewrite__redirect_type
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Action"
msgstr "कार्रवाई"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.js:0
msgid "Activating the last features."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.js:0
msgid "Activating your %s."
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__active
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__active
#: model:ir.model.fields,field_description:website.field_website_controller_page__active
#: model:ir.model.fields,field_description:website.field_website_page__active
#: model:ir.model.fields,field_description:website.field_website_rewrite__active
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
msgid "Active"
msgstr "सक्रिय"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid ""
"Adapt these three columns to fit your design need. To duplicate, delete or "
"move columns, select the column and use the top icons to perform your "
"action."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.xml:0
msgid "Adapting Building Blocks."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#: code:addons/website/static/src/components/dialog/seo.xml:0
#: model_terms:ir.ui.view,arch_db:website.s_card_options
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Add"
msgstr "जोड़ना"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_edit_third_party_domains
msgid ""
"Add 3rd-party service domains <em>(\"www.example.com\" or "
"\"example.com\")</em>, one per line."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline_options
msgid "Add Date"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.grid_layout_options
msgid "Add Elements"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/000.js:0
msgid "Add Files"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
msgid "Add Item"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "Add Media"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/edit_menu.xml:0
msgid "Add Mega Menu Item"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/edit_menu.xml:0
msgid "Add Menu Item"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_horizontal_options
#: model_terms:ir.ui.view,arch_db:website.s_timeline_list_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Add New"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_social_media_options
msgid "Add New Social Network"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_pricelist_boxed_add_product_widget
#: model_terms:ir.ui.view,arch_db:website.s_pricelist_cafe_add_product_widget
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog_add_product_widget
msgid "Add Product"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Add Row"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Add Serie"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_carousel
msgid "Add Slide"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Add Tab"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
msgid "Add Text Highlight Effects"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
msgid "Add a Custom Font"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
msgid "Add a Google font or upload a custom font"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Add a Language"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/shared_options/pricelist.js:0
msgid "Add a description here"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/edit_menu.xml:0
msgid "Add a menu item"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Add a new field after this one"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Add a new field at the end"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Add domains to the block list"
msgstr ""

#. module: website
#. odoo-python
#: code:addons/website/models/res_config_settings.py:0
msgid "Add external websites"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_view_form_extend
msgid "Add groups in the \"Access Rights\" tab below."
msgstr ""

#. module: website
#. odoo-python
#: code:addons/website/models/res_lang.py:0
msgid "Add languages"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
msgid "Add option"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_edit_third_party_domains
msgid "Add other domains here"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/add_page_dialog.xml:0
msgid "Add page template"
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_website_page__is_new_page_template
#: model:ir.model.fields,help:website.field_website_page_properties__is_new_page_template
msgid ""
"Add this page to the \"+New\" page templates. It will be added to the "
"\"Custom\" category."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Add to Cart Button"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Add to cart"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/add_page_dialog.js:0
#: code:addons/website/static/src/components/dialog/add_page_dialog.xml:0
msgid "Add to menu"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
msgid ""
"Adding a language requires to leave the editor. This will save all your "
"changes, are you sure you want to proceed?"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.js:0
msgid "Adding features."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Additional colors"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_horizontal
msgid ""
"Additionally, we offer a comprehensive knowledge base, including detailed "
"documentation, video tutorials, and community forums where you can connect "
"with other users and share insights."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "Address"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_card_options
msgid "Adjust the image width"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_s_showcase
msgid ""
"Adjust volume, skip tracks, answer calls, and activate voice assistants with"
" a simple tap, keeping your hands free and your focus on what matters most."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
msgid "Advanced"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_image_template
msgid "Advanced <br class=\"d-none d-lg-inline\"/>Capabilities"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid ""
"Advanced solution for enterprises. Cutting-edge features and top-tier "
"support for maximum performance."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Adventure"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Advertising &amp; Marketing<br/>(optional)"
msgstr ""

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__after
msgid "After"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Alert"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Alerts"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team_detail
msgid "Alexander drives our marketing campaigns and brand presence."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
#: model_terms:ir.ui.view,arch_db:website.s_quadrant_options
#: model_terms:ir.ui.view,arch_db:website.vertical_alignment_option
msgid "Align Bottom"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline_list_options
msgid "Align Center"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline_list_options
msgid "Align Left"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
#: model_terms:ir.ui.view,arch_db:website.s_quadrant_options
#: model_terms:ir.ui.view,arch_db:website.vertical_alignment_option
msgid "Align Middle"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline_list_options
msgid "Align Right"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
#: model_terms:ir.ui.view,arch_db:website.s_quadrant_options
#: model_terms:ir.ui.view,arch_db:website.vertical_alignment_option
msgid "Align Top"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_card_options
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.s_timeline_list_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Alignment"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
#: model_terms:ir.ui.view,arch_db:website.s_company_team_shapes
msgid "Aline Turner"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_team_0_s_three_columns
#: model_terms:ir.ui.view,arch_db:website.new_page_template_team_s_image_text_2nd
#: model_terms:ir.ui.view,arch_db:website.new_page_template_team_s_media_list
#: model_terms:ir.ui.view,arch_db:website.s_company_team_basic
msgid "Aline Turner, CTO"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_team_0_s_three_columns
#: model_terms:ir.ui.view,arch_db:website.new_page_template_team_s_image_text_2nd
#: model_terms:ir.ui.view,arch_db:website.new_page_template_team_s_media_list
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid ""
"Aline is one of the iconic people in life who can say they love what they "
"do. She mentors 100+ in-house developers and looks after the community of "
"thousands of developers."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/resource_editor/resource_editor.js:0
msgid "All SCSS Files"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_website_route
msgid "All Website Route"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/views/page_views_mixin.js:0
msgid "All Websites"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns_menu
msgid "All You Can Eat"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "All informations you need"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "All pages"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_searchbar/000.xml:0
msgid "All results"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "All these icons are completely free for commercial use."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_3_s_three_columns
msgid "All-Day Comfort"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
msgid "Allow all cookies"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
msgid "Allow the use of cookies from this website on this browser?"
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_ir_ui_view__track
#: model:ir.model.fields,help:website.field_website_controller_page__track
#: model:ir.model.fields,help:website.field_website_page__track
msgid "Allow to specify for one page of the website to be trackable or not"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_model__website_form_access
msgid "Allowed to use in forms"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Allows to do mass mailing campaigns to contacts"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Allows your visitors to chat with you"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
msgid "Already installed"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Alternate Image Text"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Alternate Text"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Alternate Text Image"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Alternate Text Image Text"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_list
msgid ""
"Although this Website may be linked to other websites, we are not, directly "
"or indirectly, implying any approval."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Always Underline"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Always Visible"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_3_s_three_columns
msgid "Amazing Sound Quality"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers_showcase
msgid "Amazing pages"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map
msgid "An address must be specified for a map to be embedded"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/000.js:0
msgid "An error has occured, the form has not been sent."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "An error occurred while rendering the template"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "An example alert with an icon"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "An item"
msgstr ""

#. module: website
#: model:ir.actions.client,name:website.backend_dashboard
#: model:ir.ui.menu,name:website.menu_website_analytics
msgid "Analytics"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Analytics cookies and privacy information."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Analytics<br/>(optional)"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid ""
"Analyzing the numbers behind our success: <br class=\"d-none d-xxl-"
"inline\"/>an in-depth look at the key metrics driving our company's "
"achievements"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers_showcase
msgid ""
"Analyzing the numbers behind our success: an in-depth look at the key "
"metrics driving our company's achievements."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
msgid "Anchor copied to clipboard<br>Link: %s"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
msgid "Anchor name"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
msgid "Animate"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
msgid "Animate text"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
msgid "Animated"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Animation"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_color_blocks_2
msgid "Another color block"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Another link"
msgstr ""

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__append
msgid "Append"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.xml:0
msgid "Applying your colors and design."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.js:0
msgid "Applying your colors and design..."
msgstr ""

#. module: website
#: model:ir.actions.act_window,name:website.action_website_add_features
#: model:ir.ui.menu,name:website.menu_website_add_features
msgid "Apps"
msgstr ""

#. module: website
#. odoo-python
#: code:addons/website/controllers/main.py:0
msgid "Apps url"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__arch
msgid "Arch"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_controller_page__arch_db
#: model:ir.model.fields,field_description:website.field_website_page__arch_db
msgid "Arch Blob"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_controller_page__arch_fs
#: model:ir.model.fields,field_description:website.field_website_page__arch_fs
msgid "Arch Filename"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__arch_fs
msgid "Arch Fs"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_rewrite_search
msgid "Archived"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/page_properties.xml:0
msgid "Are you sure you want to delete this page?"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/page_properties.xml:0
msgid "Are you sure you want to delete those pages?"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_carousel
msgid "Arrows"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_pricelist_cafe
msgid ""
"Artisanal espresso with a focus on direct trade and exceptional quality in a"
" chic, comfortable setting."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_countdown/options.xml:0
msgid "As promised, we will offer 4 free tickets to our next summit."
msgstr ""

#. module: website
#: model:ir.model,name:website.model_ir_asset
msgid "Asset"
msgstr "सक्रिय"

#. module: website
#: model:ir.model,name:website.model_web_editor_assets
msgid "Assets Utils"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__copy_ids
msgid "Assets using a copy of me"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "At The End"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_ir_attachment
msgid "Attachment"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__copy_ids
msgid "Attachment using a copy of me"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Authenticate users, protect user data and allow the website to deliver the services users expects,\n"
"                                                such as maintaining the content of their cart, or allowing file uploads."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_view_search
msgid "Author"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_blockquote_options
msgid "Author Alignment"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_page_properties_view_form
msgid "Authorized Groups"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Auto"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid ""
"Automatically opens the pop-up if the user stays on a page longer than the "
"specified time."
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website__auto_redirect_lang
msgid "Autoredirect Language"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Autosizing"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_actions_server__website_published
#: model:ir.model.fields,field_description:website.field_ir_cron__website_published
msgid "Available on the Website"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
msgid "BSgzTvR5L1GB9jriT451iTN4huVPxHmltG6T6eo"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "BTS Base Colors"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Backdrop"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_s_features
msgid "Backend"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Background"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/configurator_tour.js:0
msgid "Background Shape"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.background.video.xml:0
msgid "Background video"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Badge"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Bags"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_list
msgid "Balanced"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Banner"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Bar Horizontal"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Bar Vertical"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_carousel
msgid "Bars"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_base
msgid "Base"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_controller_page__arch_base
#: model:ir.model.fields,field_description:website.field_website_page__arch_base
msgid "Base View Architecture"
msgstr ""

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_ui_view__mode__primary
msgid "Base view"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/add_page_dialog.js:0
#: model_terms:ir.ui.view,arch_db:website.new_page_template_groups
msgid "Basic"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Basic example"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns_menu
msgid "Beef Carpaccio, Filet Mignon 8oz and Cheesecake"
msgstr ""

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__before
msgid "Before"
msgstr "पहले"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Beginner"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_key_benefits
msgid ""
"Benefit from tax-free shopping, simplifying your purchase and enhancing your"
" savings without any extra costs."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Big"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Big Boxes"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Big Icons Subtitles"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Big number"
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_ir_model_fields__website_form_blacklisted
msgid "Blacklist this field for web forms"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_model_fields__website_form_blacklisted
msgid "Blacklisted in web forms"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/add_page_dialog.xml:0
msgid "Blank Page"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Blazers"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Block"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_block_third_party_domains
#: model:ir.model.fields,field_description:website.field_website__block_third_party_domains
msgid "Block 3rd-party domains"
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__website_block_third_party_domains
#: model:ir.model.fields,help:website.field_website__block_third_party_domains
msgid ""
"Block 3rd-party domains that may track users (YouTube, Google Maps, etc.)."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Block tracking 3rd-party services"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Blockquote"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Blog"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/new_content.js:0
msgid "Blog Post"
msgstr ""

#. module: website
#: model:website.configurator.feature,description:website.feature_module_news
msgid "Blogging and posting relevant content"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Blogs"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "Blur"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
msgid "Bold"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_call_to_action_menu
msgid "Book your table today"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers_showcase
msgid "Boost your pipeline with an increase in potential leads."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_showcase
msgid "Boosts Conversions"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid "Bootstrap-Based Templates"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_border_widgets
msgid "Border"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Border Bottom"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Border Radius"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Border Width"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Border radius large"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Border radius medium"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Border radius none"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Border radius small"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.template_header_sales_four
#: model_terms:ir.ui.view,arch_db:website.template_header_sales_one
#: model_terms:ir.ui.view,arch_db:website.template_header_sales_three
#: model_terms:ir.ui.view,arch_db:website.template_header_sales_two
#: model_terms:ir.ui.view,arch_db:website.template_header_search
msgid "Bottom"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "Bottom to Top"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Bounce"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Box Call to Action"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_accordion_options
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_carousel
msgid "Boxed"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Boxes"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "Breadcrumb"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
msgid "Build my website"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid "Building blocks system"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_default_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_image_texts_image_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_mosaic_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_reversed_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_texts_image_texts_template
msgid "Building connections"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/new_content.js:0
msgid "Building your %s"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.js:0
msgid "Building your website."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.xml:0
msgid "Building your website..."
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__bundle
msgid "Bundle"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.grid_layout_options
#: model_terms:ir.ui.view,arch_db:website.s_button
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Button"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Button Position"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Buttons"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_1_s_banner
msgid ""
"By Crafting unique and compelling brand identities that leave a lasting "
"impact."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_edit_third_party_domains
msgid "By default, the domains of the following services are already blocked:"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__cdn_url
#: model:ir.model.fields,field_description:website.field_website__cdn_url
msgid "CDN Base URL"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__cdn_filters
#: model:ir.model.fields,field_description:website.field_website__cdn_filters
msgid "CDN Filters"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "CTA Badge"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "CTA, button, btn, action, engagement, link, offer, appeal"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"CTA, button, btn, action, engagement, link, offer, appeal, call to action, "
"prompt, interact, trigger"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"CTA, button, btn, action, engagement, link, offer, appeal, call to action, "
"prompt, interact, trigger, items, checklists, entries, sequences, bullets, "
"points, list, group, benefits, features, advantages"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"CTA, button, btn, action, engagement, link, offer, appeal, call to action, "
"prompt, interact, trigger, mockup"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"CTA, button, btn, action, engagement, link, offer, appeal, call to action, "
"prompt, interact, trigger, mockups"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Cakes"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_contact_info
msgid "Call Customer Service"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Call to Action"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Call to Action Mockups"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
msgid "Call us"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Call-to-action"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Camera"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_partner__can_publish
#: model:ir.model.fields,field_description:website.field_res_users__can_publish
#: model:ir.model.fields,field_description:website.field_website_controller_page__can_publish
#: model:ir.model.fields,field_description:website.field_website_page__can_publish
#: model:ir.model.fields,field_description:website.field_website_page_properties__can_publish
#: model:ir.model.fields,field_description:website.field_website_page_properties_base__can_publish
#: model:ir.model.fields,field_description:website.field_website_published_mixin__can_publish
#: model:ir.model.fields,field_description:website.field_website_published_multi_mixin__can_publish
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__can_publish
msgid "Can Publish"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/dialog.js:0
#: code:addons/website/static/src/components/dialog/page_properties.xml:0
#: code:addons/website/static/src/js/utils.js:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#: code:addons/website/static/src/xml/website.xml:0
#: model_terms:ir.ui.view,arch_db:website.qweb_500
#: model_terms:ir.ui.view,arch_db:website.view_edit_robots
#: model_terms:ir.ui.view,arch_db:website.view_edit_third_party_domains
#: model_terms:ir.ui.view,arch_db:website.view_website_form_view_themes_modal
msgid "Cancel"
msgstr "रद्द"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
msgid ""
"Cannot apply this option on current text selection. Try clearing the format "
"and try again."
msgstr ""

#. module: website
#. odoo-python
#: code:addons/website/models/res_lang.py:0
msgid "Cannot deactivate a language that is currently used on a website."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/content/website_root.js:0
msgid "Cannot load google map."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_pricelist_cafe
msgid "Cappuccino"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Card"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Card Call to Action"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Card Offset"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_card_options
msgid "Card Width"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Card link"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_card
msgid "Card title"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Cards"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Cards Grid"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Cards Soft"
msgstr ""

#. module: website
#: model:website.configurator.feature,name:website.feature_module_career
msgid "Career"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/resource_editor/resource_editor.js:0
msgid "Careful"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_dynamic_snippet_carousel/000.xml:0
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Carousel"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Carousel Intro"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/snippets/s_image_gallery/001.xml:0
#: model_terms:ir.ui.view,arch_db:website.new_page_template_team_s_image_gallery
#: model_terms:ir.ui.view,arch_db:website.s_carousel
#: model_terms:ir.ui.view,arch_db:website.s_carousel_intro
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel_minimal
msgid "Carousel indicator"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Carrot Cake"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Case Studies"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Categories"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_view_search
msgid "Category"
msgstr "वर्ग"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Category of Cookie"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_blockquote_options
#: model_terms:ir.ui.view,arch_db:website.s_card_options
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Center"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_carousel_intro
msgid "Centered"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_pricelist_cafe
msgid "Chamomile Tea"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Change Icons"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
msgid ""
"Changing the color palette will reset all your color customizations, are you"
" sure you want to proceed?"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
msgid ""
"Changing theme requires to leave the editor. This will save all your "
"changes, are you sure you want to proceed? Be careful that changing the "
"theme will reset all your color customizations."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Chart"
msgstr ""

#. module: website
#: model:website.configurator.feature,description:website.feature_module_live_chat
msgid "Chat with visitors to improve traction"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup
msgid "Check out now and get $20 off your first order."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_template
msgid "Check out what's new in our company !"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/content/website_root.js:0
msgid "Check your configuration."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
msgid "Check your connection and try again"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Checkbox"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
msgid "Checkbox List"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid "Chief Commercial Officer"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
#: model_terms:ir.ui.view,arch_db:website.s_company_team_basic
msgid "Chief Executive Officer"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
#: model_terms:ir.ui.view,arch_db:website.s_company_team_basic
msgid "Chief Financial Officer"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team_basic
msgid "Chief Operational Officer"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
#: model_terms:ir.ui.view,arch_db:website.s_company_team_basic
#: model_terms:ir.ui.view,arch_db:website.s_company_team_detail
msgid "Chief Technical Officer"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__child_id
#: model_terms:ir.ui.view,arch_db:website.website_menus_form_view
msgid "Child Menus"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Children"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
msgid "Choose"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_frame
msgid ""
"Choose a vibrant image and write an inspiring paragraph about it. It does "
"not have to be long, but it should reinforce your image."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
msgid "Choose an anchor name"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
msgid "Choose from list"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
msgid "Choose your favorite"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Cinnamon Roll"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Circle"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_carousel
msgid "Classic"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Classic Cheesecake"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_pricelist_boxed
msgid ""
"Classic pizza with fresh mozzarella, San Marzano tomatoes, and basil leaves,"
" drizzled with extra virgin olive oil."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Clean"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid "Clever Slogan"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid ""
"Click and change content directly from the front-end, avoiding complex "
"backend processes. This tool allows quick updates to text, images, and "
"elements right on the page, streamlining your workflow and maintaining "
"control over your content."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
msgid "Click here to go back to block tab."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_social_media/options.js:0
msgid "Click here to setup your social networks"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/website_preview/website_preview.xml:0
msgid "Click on"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_embed_code
msgid ""
"Click on <b>\"Edit\"</b> in the right panel to replace this with your own "
"HTML code"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
msgid "Click on the <b>%s</b> building block."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
msgid "Click on the <b>%s</b> category."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Click on the number to adapt it to your purpose."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
msgid "Click to choose more images"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
msgid "Click to select"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_personal_s_numbers
msgid "Clients"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers_charts
msgid "Clients saved $32 million with our services."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/resource_editor/resource_editor.xml:0
#: code:addons/website/static/src/components/views/theme_preview.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/001.xml:0
#: code:addons/website/static/src/xml/website.xml:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.qweb_500
#: model_terms:ir.ui.view,arch_db:website.s_popup
#: model_terms:ir.ui.view,arch_db:website.show_website_info
#: model_terms:ir.ui.view,arch_db:website.template_header_hamburger
#: model_terms:ir.ui.view,arch_db:website.template_header_mobile
msgid "Close"
msgstr "बंद"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Close Button Color"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/resource_editor/resource_editor_warning.xml:0
msgid "Close editor"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Closer Look"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
msgid "Clothes, Marketing, ..."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Code"
msgstr "कोड"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Code Injection"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_pricelist_cafe
msgid "Coffee Latte"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_pricelist_cafe
msgid "Coffees"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.s_blockquote_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "Color"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "Color Filter"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Color Presets"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_color_blocks_2
msgid ""
"Color blocks are a simple and effective way to <b>present and highlight your"
" content</b>. Choose an image or a color for the background. You can even "
"resize and duplicate the blocks to create your own layout. Add images or "
"icons to customize the blocks."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#: model_terms:ir.ui.view,arch_db:website.s_accordion_options
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Colors"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel_intro_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Cols"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Columns"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_0_s_cover
msgid "Coming Soon"
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_website_configurator_feature__website_config_preselection
msgid ""
"Comma-separated list of website type/purpose for which this feature should "
"be pre-selected"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_default_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_image_texts_image_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_reversed_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_texts_image_texts_template
msgid "Community <br class=\"d-none d-lg-inline\"/>Focus"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_template
msgid "Community Focus"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_res_company
msgid "Companies"
msgstr ""

#. module: website
#. odoo-python
#: code:addons/website/models/website.py:0
#: model:ir.model.fields,field_description:website.field_website__company_id
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Company"
msgstr "संस्था"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Comparisons"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Competitive pricing"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Components"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_cards_soft
msgid "Comprehensive Support"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid ""
"Comprehensive tools for growing businesses. Optimize your processes and "
"productivity across your team."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Computers"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Computers &amp; Devices"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Conditionally"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: website
#: model:ir.ui.menu,name:website.menu_website_global_configuration
msgid "Configuration"
msgstr "कॉन्फ़िगरेशन"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__configurator_done
msgid "Configurator Done"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.js:0
msgid "Configuring your %s."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/dialog.js:0
msgid "Confirmation"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/website_dashboard/website_dashboard.xml:0
msgid "Connect Plausible"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
msgid "Connect with us"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Connected"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps_options
msgid "Connector"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
#: model_terms:ir.ui.view,arch_db:website.s_features_wave
msgid ""
"Consistent performance and uptime ensure efficient, reliable service with "
"minimal interruptions and quick response times."
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__has_google_search_console
msgid "Console Google Search"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_s_features
msgid "Consulting"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_res_partner
#: model:ir.model.fields,field_description:website.field_website_visitor__partner_id
#: model_terms:ir.ui.view,arch_db:website.s_tabs
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Contact"
msgstr "संपर्क"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Contact & Forms"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Contact Info"
msgstr ""

#. module: website
#. odoo-python
#: code:addons/website/models/website.py:0
#: model_terms:ir.ui.view,arch_db:website.header_call_to_action
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
#: model_terms:ir.ui.view,arch_db:website.s_contact_info
#: model_terms:ir.ui.view,arch_db:website.s_cta_box
#: model_terms:ir.ui.view,arch_db:website.s_image_hexagonal
#: model_terms:ir.ui.view,arch_db:website.s_text_block_h2_contact
msgid "Contact Us"
msgstr ""

#. module: website
#. odoo-python
#: code:addons/website/models/website_visitor.py:0
msgid "Contact Visitor"
msgstr ""

#. module: website
#: model:website.menu,name:website.menu_contactus
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.s_call_to_action
#: model_terms:ir.ui.view,arch_db:website.s_carousel
#: model_terms:ir.ui.view,arch_db:website.s_closer_look
#: model_terms:ir.ui.view,arch_db:website.s_cover
#: model_terms:ir.ui.view,arch_db:website.s_cta_card
#: model_terms:ir.ui.view,arch_db:website.s_cta_mockups
#: model_terms:ir.ui.view,arch_db:website.s_discovery
#: model_terms:ir.ui.view,arch_db:website.s_text_cover
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
msgid "Contact us"
msgstr "हमसे संपर्क करें"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
msgid ""
"Contact us about anything related to our company or services.<br/>\n"
"                                    We'll do our best to get back to you as soon as possible."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
msgid "Contact us anytime"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Contact us for any issue or question"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_2_s_call_to_action
msgid ""
"Contact us today to embark on your path to a healthier, more vibrant you. "
"Your fitness journey begins here."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Contacts"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Contain"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Contains"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_custom_blocked_third_party_domains__content
#: model:ir.model.fields,field_description:website.field_website_robots__content
#: model:ir.ui.menu,name:website.menu_content
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Content"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__cdn_activated
#: model:ir.model.fields,field_description:website.field_website__cdn_activated
msgid "Content Delivery Network (CDN)"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel_intro_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Content Width"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/editor/editor.js:0
msgid "Content saved."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/translator/translator.xml:0
msgid "Content to translate"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid "Continue reading <i class=\"fa fa-long-arrow-right align-middle ms-1\"/>"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__controller_page_ids
#: model:ir.model.fields,field_description:website.field_website_controller_page__controller_page_ids
#: model:ir.model.fields,field_description:website.field_website_page__controller_page_ids
msgid "Controller Page"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_carousel_intro
msgid "Controllers"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
#: model_terms:ir.ui.view,arch_db:website.cookies_bar
msgid "Cookie Policy"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_cookies_bar
#: model:ir.model.fields,field_description:website.field_website__cookies_bar
msgid "Cookies Bar"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Cookies are small bits of text sent by our servers to your computer or device when you access our services.\n"
"                            They are stored in your browser and later sent back to our servers so that we can provide contextual content.\n"
"                            Without cookies, using the web would be a much more frustrating experience.\n"
"                            We use them to support your activities on our website. For example, your session (so you don't have to login again) or your shopping cart.\n"
"                            <br/>\n"
"                            Cookies are also used to help us understand your preferences based on previous or current activity on our website (the pages you have\n"
"                            visited), your language and country, which enables us to provide you with improved services.\n"
"                            We also use cookies to help us compile aggregate data about site traffic and site interaction so that we can offer\n"
"                            better site experiences and tools in the future."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/add_page_dialog.js:0
msgid "Copy of %s"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Copyright"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Core Features"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/page_properties.xml:0
msgid "Could be used in many places, see here:"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Countdown"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_countdown/000.js:0
msgid "Countdown ends in"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_countdown/options.xml:0
msgid "Countdown is over - Firework"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__country_id
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Country"
msgstr "देश"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__country_flag
msgid "Country Flag"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/new_content.js:0
msgid "Course"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.record_cover
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Cover"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_card_options
msgid "Cover Image"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page_options
msgid "Cover Photo"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_cover_properties_mixin__cover_properties
msgid "Cover Properties"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_website_cover_properties_mixin
msgid "Cover Properties Website Mixin"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_striped_center_top
msgid "Crafted with precision and care"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_services_s_text_cover
msgid "Crafting Your Digital Success Story"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Cras justo odio"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/add_page_dialog.js:0
#: code:addons/website/static/src/components/dialog/add_page_dialog.xml:0
#: code:addons/website/static/src/js/utils.js:0
#: model_terms:ir.ui.view,arch_db:website.view_website_form_view_themes_modal
msgid "Create"
msgstr "बनाएँ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.page_404
msgid "Create Page"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.brand_promotion
msgid "Create a"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
msgid "Create a Google Project and Get a Key"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Create a link to target this section"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
msgid "Create new"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid ""
"Create pages from scratch by dragging and dropping customizable building "
"blocks. This system simplifies web design, making it accessible to all skill"
" levels. Combine headers, images, and text sections to build cohesive "
"layouts quickly and efficiently."
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__create_uid
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__create_uid
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__create_uid
#: model:ir.model.fields,field_description:website.field_theme_website_menu__create_uid
#: model:ir.model.fields,field_description:website.field_theme_website_page__create_uid
#: model:ir.model.fields,field_description:website.field_website__create_uid
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__create_uid
#: model:ir.model.fields,field_description:website.field_website_controller_page__create_uid
#: model:ir.model.fields,field_description:website.field_website_custom_blocked_third_party_domains__create_uid
#: model:ir.model.fields,field_description:website.field_website_menu__create_uid
#: model:ir.model.fields,field_description:website.field_website_page__create_uid
#: model:ir.model.fields,field_description:website.field_website_page_properties__create_uid
#: model:ir.model.fields,field_description:website.field_website_page_properties_base__create_uid
#: model:ir.model.fields,field_description:website.field_website_rewrite__create_uid
#: model:ir.model.fields,field_description:website.field_website_robots__create_uid
#: model:ir.model.fields,field_description:website.field_website_route__create_uid
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__create_uid
#: model:ir.model.fields,field_description:website.field_website_visitor__create_uid
#: model_terms:ir.ui.view,arch_db:website.view_rewrite_search
msgid "Created by"
msgstr "द्वारा निर्मित"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"Created in 2021, the company is young and dynamic. Discover the composition "
"of the team and their skills."
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__create_date
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__create_date
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__create_date
#: model:ir.model.fields,field_description:website.field_theme_website_menu__create_date
#: model:ir.model.fields,field_description:website.field_theme_website_page__create_date
#: model:ir.model.fields,field_description:website.field_website__create_date
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__create_date
#: model:ir.model.fields,field_description:website.field_website_controller_page__create_date
#: model:ir.model.fields,field_description:website.field_website_custom_blocked_third_party_domains__create_date
#: model:ir.model.fields,field_description:website.field_website_menu__create_date
#: model:ir.model.fields,field_description:website.field_website_page__create_date
#: model:ir.model.fields,field_description:website.field_website_page_properties__create_date
#: model:ir.model.fields,field_description:website.field_website_page_properties_base__create_date
#: model:ir.model.fields,field_description:website.field_website_rewrite__create_date
#: model:ir.model.fields,field_description:website.field_website_robots__create_date
#: model:ir.model.fields,field_description:website.field_website_route__create_date
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__create_date
msgid "Created on"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel_intro
msgid "Creating solutions that drive growth and long-term value."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Croissant"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_s_color_blocks_2
msgid "Crystal Clear Sound"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps_options
msgid "Curved arrow"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
#: model_terms:ir.ui.view,arch_db:website.column_count_option
#: model_terms:ir.ui.view,arch_db:website.new_page_template_groups
#: model_terms:ir.ui.view,arch_db:website.s_card_options
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Custom"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website__custom_code_head
msgid "Custom <head> code"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_form
msgid "Custom Code"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Custom Field"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
msgid "Custom Font"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Custom Inner Content"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Custom Key"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_card_options
msgid "Custom Ratio"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
msgid "Custom Text"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
msgid "Custom Url"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website__custom_code_footer
msgid "Custom end of <body> code"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__auth_signup_uninvited
#: model:ir.model.fields,field_description:website.field_website__auth_signup_uninvited
msgid "Customer Account"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers_grid
msgid "Customer Retention"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_cards_grid
#: model_terms:ir.ui.view,arch_db:website.s_features_wall
#: model_terms:ir.ui.view,arch_db:website.s_wavy_grid
msgid ""
"Customer satisfaction is our priority. Our support team is always ready to "
"assist, ensuring you have a smooth and successful experience."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Customers"
msgstr "साथी"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_wave
msgid "Customizable Settings"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid "Customization tool"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__customize_show
msgid "Customize Show"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "D - H - M"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "D - H - M - S"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
msgid "DRAG BUILDING BLOCKS HERE"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.s_badge_options
msgid "Danger"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team_detail
msgid "Daniel ensures efficient daily operations and process optimization."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_badge_options
msgid "Dark"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_border_line_widgets
msgid "Dashed"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/services/website_service.js:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "Data"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_chart/options.js:0
msgid "Data Border"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_chart/options.js:0
msgid "Data Color"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_chart/options.js:0
msgid "Dataset Border"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_chart/options.js:0
msgid "Dataset Color"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
msgid "Date"
msgstr "तिथि"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Date &amp; Time"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_countdown/000.js:0
#: model_terms:ir.ui.view,arch_db:website.s_countdown
msgid "Days"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Debug"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Decimal Number"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_blockquote_options
msgid "Decoration"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
#: model_terms:ir.ui.view,arch_db:website.s_company_team_detail
msgid "Dedicated professionals driving our success"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_carousel
#: model_terms:ir.ui.view,arch_db:website.snippet_options_carousel_intro
msgid "Default"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Default Access Rights"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Default Input Style"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website__default_lang_id
msgid "Default Language"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_controller_page__default_layout
#: model_terms:ir.ui.view,arch_db:website.s_website_controller_page_listing_layout
msgid "Default Layout"
msgstr ""

#. module: website
#: model:website.menu,name:website.main_menu
msgid "Default Main Menu"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Default Reversed"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__social_default_image
#: model:ir.model.fields,field_description:website.field_website__social_default_image
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Default Social Share Image"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Default Value"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Default checkbox"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_default_lang_id
msgid "Default language"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_default_lang_code
msgid "Default language code"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Default radio"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Default switch checkbox input"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Delay"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Delete Blocks"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/edit_menu.xml:0
msgid "Delete Menu Item"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/page_properties.xml:0
msgid "Delete Page"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid ""
"Delete the above image or replace it with a picture that illustrates your "
"message. Click on the picture to change its <em>rounded corner</em> style."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
msgid "Delete this font"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
msgid ""
"Deleting a font requires a reload of the page. This will save all your "
"changes and reload the page, are you sure you want to proceed?"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_pricelist_cafe
msgid ""
"Delicate green tea scented with jasmine blossoms, providing a soothing and "
"floral experience."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid "Deliveries"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_controller_page__url_demo
msgid "Demo URL"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid "Departments"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/page_properties.js:0
#: code:addons/website/static/src/components/dialog/page_properties.xml:0
msgid "Dependencies"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
msgid "Describe your field here."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__description
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_map_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Description"
msgstr "विवरण"

#. module: website
#: model:website.configurator.feature,description:website.feature_page_our_services
msgid "Description of your services offer"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_pricelist_boxed_options
#: model_terms:ir.ui.view,arch_db:website.s_pricelist_cafe_options
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog_options
msgid "Descriptions"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Descriptive"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Design"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid ""
"Design Odoo templates easily with clean HTML and Bootstrap CSS. These "
"templates offer a responsive, mobile-first design, making them simple to "
"customize and perfect for any web project, from corporate sites to personal "
"blogs."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid "Design features"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_motto
msgid ""
"Design is the intermediary between <strong>information</strong> and "
"<strong>understanding</strong>"
msgstr ""

#. module: website
#: model:website.configurator.feature,description:website.feature_page_pricing
msgid "Designed to drive conversion"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_s_text_cover
msgid "Designed to provide an immersive audio experience on the go."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/views/theme_preview.xml:0
msgid "Desktop"
msgstr "डेस्कटॉप"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Desktop computers"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Detail"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid "Details"
msgstr "विवरण"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
msgid "Detect"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_pricelist_boxed
msgid "Diavola"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_services_s_image_text_2nd
msgid "Digital Consulting Expertise"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Direction"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__directive
msgid "Directive"
msgstr ""

#. module: website
#: model:ir.actions.server,name:website.website_disable_unused_snippets_assets_ir_actions_server
msgid "Disable unused snippets assets"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "Disabled"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Disappearing"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Disappears"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/edit_head_body_dialog/edit_head_body_dialog.xml:0
#: code:addons/website/static/src/xml/website.editor.xml:0
msgid "Discard"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_card
#: model_terms:ir.ui.view,arch_db:website.s_framed_intro
#: model_terms:ir.ui.view,arch_db:website.s_quadrant
#: model_terms:ir.ui.view,arch_db:website.s_striped_top
msgid "Discover"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_text
msgid "Discover New <strong>Opportunities</strong>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_gallery_s_banner
msgid "Discover Our Univers"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_showcase
msgid "Discover all the features"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_cover
msgid "Discover more"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid "Discover more <i class=\"fa fa-long-arrow-right align-middle ms-1\"/>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_services_s_image_text
msgid ""
"Discover our comprehensive marketing service designed to amplify your "
"brand's reach and impact."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Discover our culture and our values"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_pricelist_cafe
msgid "Discover our drinks"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team_basic
msgid "Discover our executive team"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_images_mosaic
msgid "Discover our latest solutions for your business."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "Discover our legal notice"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "Discover our realisations"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_discovery
msgid "Discover our solutions"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid "Discover our team"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_key_benefits
msgid "Discover our<br/>main three benefits"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers_showcase
msgid "Discover outstanding and highly engaging web pages."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Discovery"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Discrete"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Discussion Group"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Disk"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Display"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Display 1"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Display 2"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Display 3"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Display 4"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Display 5"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Display 6"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
msgid "Display After"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
msgid "Display Below"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
msgid "Display Inside"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__display_name
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__display_name
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__display_name
#: model:ir.model.fields,field_description:website.field_theme_website_menu__display_name
#: model:ir.model.fields,field_description:website.field_theme_website_page__display_name
#: model:ir.model.fields,field_description:website.field_website__display_name
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__display_name
#: model:ir.model.fields,field_description:website.field_website_controller_page__display_name
#: model:ir.model.fields,field_description:website.field_website_custom_blocked_third_party_domains__display_name
#: model:ir.model.fields,field_description:website.field_website_menu__display_name
#: model:ir.model.fields,field_description:website.field_website_page__display_name
#: model:ir.model.fields,field_description:website.field_website_page_properties__display_name
#: model:ir.model.fields,field_description:website.field_website_page_properties_base__display_name
#: model:ir.model.fields,field_description:website.field_website_rewrite__display_name
#: model:ir.model.fields,field_description:website.field_website_robots__display_name
#: model:ir.model.fields,field_description:website.field_website_route__display_name
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__display_name
#: model:ir.model.fields,field_description:website.field_website_track__display_name
#: model:ir.model.fields,field_description:website.field_website_visitor__display_name
msgid "Display Name"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Display a customizable cookies bar on your website"
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__website_cookies_bar
#: model:ir.model.fields,help:website.field_website__cookies_bar
msgid "Display a customizable cookies bar on your website."
msgstr ""

#. module: website
#. odoo-python
#: code:addons/website/models/ir_qweb_fields.py:0
msgid "Display the badges"
msgstr ""

#. module: website
#. odoo-python
#: code:addons/website/models/ir_qweb_fields.py:0
msgid "Display the biography"
msgstr ""

#. module: website
#. odoo-python
#: code:addons/website/models/ir_qweb_fields.py:0
msgid "Display the website description"
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__website_logo
#: model:ir.model.fields,help:website.field_website__logo
msgid "Display this logo on the website."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Display this website when users visit this domain"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_key_images
msgid "Dive deeper into our company’s abilities."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/edit_head_body_dialog/edit_head_body_dialog.xml:0
#: code:addons/website/static/src/xml/website.editor.xml:0
msgid ""
"Do not copy/paste code you do not understand, this could put your data at "
"risk."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid ""
"Do you need specific information? Our specialists will help you with "
"pleasure."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/new_content.js:0
msgid "Do you want to install the \"%s\" App?"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_horizontal
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Documentation"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Doesn't contain"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Dolly Zoom"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_controller_page__record_domain
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Domain"
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_website_controller_page__record_domain
msgid "Domain to restrict records that can be viewed publicly"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/page_properties.xml:0
msgid "Don't forget to update all links referring to it."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/resource_editor/resource_editor_warning.xml:0
msgid "Don't show again"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/views/theme_preview.xml:0
msgid "Don't worry, you can switch later."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Donation"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Donation Button"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Dot Color"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Dot Lines Color"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_carousel
msgid "Dots"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_border_line_widgets
msgid "Dotted"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_border_line_widgets
msgid "Double"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Double click an icon to replace it with one of your choice."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Doughnut"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
msgid "Drag the <b>%s</b> block and drop it at the bottom of the page."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/edit_menu.xml:0
msgid "Drag to the right to get a submenu"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Dresses"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel_intro
msgid "Driving innovation together"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Dropdown"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Dropdown List"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/edit_menu.xml:0
msgid "Dropdown menu"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Due Date"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Duplicate"
msgstr "नकली "

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/page_properties.xml:0
msgid "Duplicate Page"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Duplicate blocks and columns to add more features."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Duplicate blocks to add more steps."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Duration"
msgstr "अवधि"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Dynamic Carousel"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Dynamic Snippet"
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__website_homepage_url
#: model:ir.model.fields,help:website.field_website__homepage_url
msgid "E.g. /contactus or /shop"
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__website_domain
#: model:ir.model.fields,help:website.field_website__domain
msgid "E.g. https://www.mydomain.com"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_horizontal
msgid ""
"Each update is thoroughly tested to guarantee compatibility and reliability,"
" and we provide detailed release notes to keep you informed of new features "
"and improvements."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_pricelist_cafe
msgid "Earl Grey"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/website_dashboard/website_dashboard.xml:0
msgid "Easily track your visitor with Plausible"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_s_text_cover
msgid "EchoTunes Wireless Earbuds"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_s_showcase
msgid ""
"EchoTunes comes with customizable ear tip sizes that provide a secure and "
"comfortable fit."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_cards_grid
#: model_terms:ir.ui.view,arch_db:website.s_wavy_grid
msgid "Eco-Friendly Solutions"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_blockquote_options
msgid "Edge Spacing"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/website_preview/website_preview.xml:0
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/systray_items/edit_website.js:0
#: model_terms:ir.ui.view,arch_db:website.publish_management
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
msgid "Edit"
msgstr "संपादित"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/services/website_custom_menus.js:0
msgid "Edit %s"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/edit_website.xml:0
msgid "Edit -"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/resource_editor/resource_editor_warning.xml:0
msgid "Edit HTML anyway"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/edit_head_body_dialog/edit_head_body_dialog.xml:0
msgid "Edit Head and Body Code"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/edit_menu.xml:0
#: code:addons/website/static/src/js/widgets/link_popover_widget.js:0
#: model:ir.ui.menu,name:website.custom_menu_edit_menu
#: model_terms:ir.ui.view,arch_db:website.website_page_properties_base_view_form
msgid "Edit Menu"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/edit_menu.xml:0
msgid "Edit Menu Item"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Edit Message"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Edit Styles"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_embed_code/options.js:0
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
msgid "Edit embedded code"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.publish_management
msgid "Edit in backend"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Edit robots.txt"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.layout
msgid "Edit this content"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid "Edit this title"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "Edit video"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.layout
msgid "Editor"
msgstr "संपादक "

#. module: website
#: model:res.groups,name:website.group_website_designer
msgid "Editor and Designer"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Effect"
msgstr ""

#. module: website
#. odoo-python
#: code:addons/website/models/website_snippet_filter.py:0
msgid "Either action_server_id or filter_id must be provided."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Electronics"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_list
msgid "Elegant"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Elements"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_3_s_call_to_action
msgid "Elevate Your Audio Journey Today"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_1_s_banner
msgid "Elevate Your Brand With Us"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__email
#: model_terms:ir.ui.view,arch_db:website.s_share
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_tree
msgid "Email"
msgstr "ईमेल"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Email & Marketing"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Email Marketing"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Email address"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_full_s_image_text
msgid ""
"Embark on a journey through time as we share the story of our humble "
"beginnings. What started as a simple idea in a garage has evolved into an "
"innovative force in the industry."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Embed Code"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team_detail
msgid "Emily manages talent acquisition and workplace culture."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_empowerment
msgid "Empowering Your Success<br/>with Every Solution."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel_intro
msgid ""
"Empowering teams to collaborate and innovate, creating impactful solutions "
"that drive business growth and deliver lasting value."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_s_features
msgid ""
"Empowering your business through strategic digital insights and expertise."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_hexagonal
msgid "Empowering<br/>Innovative<br/>Solutions"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Empowerment"
msgstr ""

#. module: website
#. odoo-python
#: code:addons/website/models/website_snippet_filter.py:0
msgid "Empty field name in “%s”"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
msgid "Enable billing on your Google Project"
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_ir_model__website_form_access
msgid "Enable the form builder feature for this model."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
msgid "Enable the right google map APIs in your google account"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.js:0
msgid "Enabling your %s."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_showcase
msgid "Engages Visitors"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_image
msgid "Enhance Your <strong>Experience</strong>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_shape_image
msgid ""
"Enhance your experience with our user-focused designs, ensuring you get the "
"best value.<br/>"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
msgid "Enter an API Key"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/edit_head_body_dialog/edit_head_body_dialog.xml:0
msgid ""
"Enter code that will be added before the </body> of every page of your site."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Enter code that will be added into every page of your site"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/edit_head_body_dialog/edit_head_body_dialog.xml:0
msgid ""
"Enter code that will be added into the <head> of every page of your site."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Enter email"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Equal Widths"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website_form.xml:0
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Error"
msgstr "त्रुटि!"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_pricelist_cafe
msgid "Espresso"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Essential tools for your success."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/new_content.js:0
msgid "Event"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid "Event heading"
msgstr ""

#. module: website
#: model:website.configurator.feature,name:website.feature_module_event
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page_options
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Events"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_s_carousel
msgid "Every Friday From 6PM to 7PM !"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Every Time"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Everything"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
#: model_terms:ir.ui.view,arch_db:website.s_features_wave
msgid "Everything you need"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Everything you need to get started."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Examples"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
msgid "Existing Fields"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/resource_editor/utils.js:0
msgid "Expected %(char)s"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers_grid
#: model_terms:ir.ui.view,arch_db:website.s_numbers_list
msgid "Expected revenue"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_services_s_text_cover
msgid ""
"Experience a digital transformation like never before with our range of "
"innovative solutions, designed to illuminate your brand's potential."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_striped_top
msgid "Experience the Future of Innovation in Every Interaction"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_pricing_s_text_block_2nd
msgid ""
"Experience the power of our software without breaking the bank – choose a "
"plan that suits you best and start unlocking its full potential today."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_intro_pill
msgid "Experience the<br/>best quality services"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_picture
msgid ""
"Experience unparalleled comfort, cutting-edge design, and performance-"
"enhancing<br/>technology with this latest innovation, crafted to elevate "
"every athlete's journey."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_framed_intro
msgid "Experience<br/>the World's Best<br/><strong>Quality Services</strong>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_sidegrid
msgid "Experience<br/>the real<br/>innovation"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_s_banner
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_s_text_cover
msgid "Experienced fullstack developer."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_s_features
msgid ""
"Experienced in effective project management, adept at leading cross-"
"functional teams and delivering successful outcomes with a strategic "
"approach."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Expert"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_cards_grid
#: model_terms:ir.ui.view,arch_db:website.s_features_wall
#: model_terms:ir.ui.view,arch_db:website.s_wavy_grid
msgid "Expertise and Knowledge"
msgstr ""

#. module: website
#: model:website.configurator.feature,description:website.feature_page_privacy_policy
msgid "Explain how you protect privacy"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert
msgid ""
"Explain the benefits you offer. <br/>Don't write about products or services "
"here, write about solutions."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Explore"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers_showcase
msgid "Explore a vast array of practical and beneficial choices."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_quadrant
msgid ""
"Explore how our cutting-edge solutions redefine industry standards. To "
"achieve excellence, we focus on what truly matters to our customers. "
"<br/><br/> Begin by identifying their needs and exceed their expectations."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
msgid "Explore on"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_gallery_s_text_block_2nd
msgid ""
"Explore our captivating gallery, a visual journey showcasing our finest work"
" and creative projects. Immerse yourself in a collection of images that "
"capture the essence of our craftsmanship, innovation, and dedication to "
"excellence."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_pricelist_cafe
msgid ""
"Explore our curated selection of coffee, tea, and more. Delight in every "
"sip!"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers_showcase
msgid "Explore our<br/> key statistics"
msgstr ""

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_ui_view__mode__extension
msgid "Extension View"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_actions_server__xml_id
#: model:ir.model.fields,field_description:website.field_ir_cron__xml_id
#: model:ir.model.fields,field_description:website.field_website_controller_page__xml_id
#: model:ir.model.fields,field_description:website.field_website_page__xml_id
msgid "External ID"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Extra Large"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Extra Link"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.layout
msgid "Extra items button"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
msgid "Extra page"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Extra-Large"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Extra-Small"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "F.A.Q."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "FAQ Block"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "FAQ List"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_facebook_page/000.js:0
#: code:addons/website/static/src/snippets/s_social_media/options.js:0
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.header_social_links
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
#: model_terms:ir.ui.view,arch_db:website.s_references_social
#: model_terms:ir.ui.view,arch_db:website.s_share
#: model_terms:ir.ui.view,arch_db:website.s_social_media
#: model_terms:ir.ui.view,arch_db:website.snippets
#: model_terms:ir.ui.view,arch_db:website.template_footer_centered
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Facebook"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website__social_facebook
msgid "Facebook Account"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_carousel
msgid "Fade"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fade Out"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/new_content.js:0
msgid "Failed to install \"%s\""
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_key_benefits
msgid "Fair pricing"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__favicon
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Favicon"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Feature One"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Feature Three"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Feature Two"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__feature_url
msgid "Feature Url"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Features"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Features Grid"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Features Wall"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Features Wave"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_showcase
msgid "Features showcase"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_cards_grid
msgid "Features that set us apart"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_options_template
msgid "Fetched Elements"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
msgid "Field"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__field_names
msgid "Field Names"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_model__website_form_default_field_id
msgid "Field for custom form data"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_ir_model_fields
msgid "Fields"
msgstr ""

#. module: website
#. odoo-python
#: code:addons/website/controllers/main.py:0
msgid "File '%s' exceeds maximum allowed file size"
msgstr ""

#. module: website
#. odoo-python
#: code:addons/website/controllers/main.py:0
msgid "File '%s' is not recognized as a font"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "File Upload"
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_website_controller_page__arch_fs
#: model:ir.model.fields,help:website.field_website_page__arch_fs
msgid ""
"File from where the view originates.\n"
"                                                          Useful to (hard) reset broken views or to read arch from file in dev-xml mode."
msgstr ""

#. module: website
#: model:ir.model,name:website.model_ir_binary
msgid "File streaming helper model for controllers"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fill"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Fill and Justify"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/resource_editor/resource_editor.xml:0
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__filter_id
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_options_template
msgid "Filter"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Filter Intensity"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.js:0
msgid "Finalizing."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team_detail
msgid "Financial Analyst"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "Find a store near you"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid ""
"Find all information about our deliveries, express deliveries and all you "
"need to know to return a product."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid ""
"Find out how we were able helping them and set in place solutions adapted to"
" their needs."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Find the perfect solution for you"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__create_date
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "First Connection"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid "First Feature"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_multi_menus
msgid "First Menu"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "First Time Only"
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_ir_ui_view__first_page_id
#: model:ir.model.fields,help:website.field_website_controller_page__first_page_id
#: model:ir.model.fields,help:website.field_website_page__first_page_id
msgid "First page linked to this view"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fit content"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fit text"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "Fixed"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Flag"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Flag and Code"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Flag and Text"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Flash"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Flat"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Flip-In-X"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Flip-In-Y"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Float"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_references_social
msgid "Flourishing together since 2016"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_mosaic_template
msgid "Focus"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
msgid "Follow Us"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.header_social_links
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
#: model_terms:ir.ui.view,arch_db:website.template_footer_centered
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_descriptive
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
msgid "Follow us"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Font"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Font Family"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Font Size"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
msgid "Font access"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
msgid "Font exists"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
msgid "Font name already used"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_page__footer_visible
#: model:ir.model.fields,field_description:website.field_website_page__footer_visible
msgid "Footer Visible"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_title_form
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Form"
msgstr "फार्म"

#. module: website
#: model:ir.model.fields,help:website.field_ir_model__website_form_label
msgid ""
"Form action label. Ex: crm.lead could be 'Send an e-mail' and project.issue "
"could be 'Create an Issue'."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/resource_editor/resource_editor.xml:0
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Format"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/new_content.js:0
#: model:website.configurator.feature,name:website.feature_module_forum
msgid "Forum"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Foundation Package"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid ""
"Founder and chief visionary, Tony is the driving force behind the company. He loves\n"
"                                to keep his hands full by participating in the development of the software,\n"
"                                marketing, and customer experience strategies."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_team_0_s_three_columns
msgid ""
"Founder and chief visionary, Tony is the driving force behind the company. "
"He loves to keep his hands full by participating in the development of the "
"software, marketing, and customer experience strategies."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_team_s_media_list
msgid ""
"Founder, Tony is the driving force behind the company. He loves to keep his "
"hands full by participating in the development of the software, marketing, "
"and UX strategies."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_team_s_image_text
msgid ""
"Founder, Tony is the driving force behind the company. He loves to keep his "
"hands full by participating in the development of the software, marketing, "
"and customer experience."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Framed"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Framed Intro"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Free grid"
msgstr ""

#. module: website
#: model:ir.model.fields.selection,name:website.selection__website__auth_signup_uninvited__b2c
msgid "Free sign up"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid "Frequently asked questions"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "From Bottom"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "From Bottom Left"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "From Bottom Right"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "From Left"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "From Right"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_s_features
msgid ""
"From SEO to social media, we create campaigns that not only get you noticed "
"but also drive engagement and conversions."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "From Top"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "From Top Left"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "From Top Right"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_services_s_text_image
msgid ""
"From revitalizing your visual identity to realigning your messaging for the "
"digital landscape, we'll guide you through a strategic process that ensures "
"your brand remains relevant and resonates with your audience."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid ""
"From seminars to team building activities, we offer a wide choice of events "
"to organize."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_cards_soft
msgid ""
"From the initial stages to completion, we offer support every step of the "
"way, ensuring you feel confident in your choices and that your project is a "
"success."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_s_features
msgid "Frontend"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_references_social
msgid "Fruitful collaboration since 2014"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel_intro_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Full"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Full Screen"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Full Width"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Full screen"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Full-Width"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "G-XXXXXXXXXX"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "GPS &amp; navigation"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_gallery_s_text_block_h2
#: model_terms:ir.ui.view,arch_db:website.new_page_template_groups
msgid "Gallery"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Gaming"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.xml:0
msgid "Generating inspiring text."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.js:0
msgid "Generating inspiring text..."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Get Delivered"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_intro_pill
msgid "Get Started"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
msgid "Get in touch"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_title_form
msgid ""
"Get in touch with your customers to provide them with better service. You "
"can modify the form fields to gather more precise information."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_empowerment
#: model_terms:ir.ui.view,arch_db:website.s_striped_center_top
msgid "Get started"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_horizontal
msgid "Getting Started"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_horizontal
msgid ""
"Getting started with our product is a breeze, thanks to our well-structured "
"and comprehensive onboarding process."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_social_media/options.js:0
#: model_terms:ir.ui.view,arch_db:website.s_company_team_detail
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
#: model_terms:ir.ui.view,arch_db:website.s_social_media
msgid "GitHub"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website__social_github
msgid "GitHub Account"
msgstr ""

#. module: website
#: model:website.configurator.feature,description:website.feature_module_forum
msgid "Give visitors the information they need"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Glasses"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/views/theme_preview.xml:0
msgid "Go Back"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/fields/redirect_field.xml:0
msgid "Go to"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
msgid "Go to Homepage"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_view_form_extend
msgid "Go to Page Manager"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/backend/view_hierarchy/view_hierarchy.xml:0
msgid "Go to View"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/website_dashboard/website_dashboard.xml:0
msgid "Go to Website"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
msgid "Go to the Theme tab"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.layout
msgid "Go to your Odoo Apps"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid ""
"Good copy starts with understanding how your product or service helps your "
"customers. Simple words communicate better than big words and pompous "
"language."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
msgid "Good job! It's time to <b>Save</b> your work."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid "Good writing is simple, but not simplistic."
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__has_google_analytics
msgid "Google Analytics"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__google_analytics_key
#: model:ir.model.fields,field_description:website.field_website__google_analytics_key
msgid "Google Analytics Key"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Google Map"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website__google_maps_api_key
msgid "Google Maps API Key"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__google_search_console
#: model:ir.model.fields,field_description:website.field_website__google_search_console
msgid "Google Search Console"
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__google_search_console
#: model:ir.model.fields,help:website.field_website__google_search_console
msgid "Google key, or Enable to access first reply"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Gray #{grayCode}"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Grays"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Great Value"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_block
msgid ""
"Great stories are <b>for everyone</b> even when only written <b>for just one"
" person</b>. If you try to write with a wide, general audience in mind, your"
" story will sound fake and lack emotion. No one will be interested. Write "
"for one person. If it’s genuine for the one, it’s genuine for the rest."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_block
msgid ""
"Great stories have a <b>personality</b>. Consider telling a great story that"
" provides personality. Writing a story with personality for potential "
"clients will assist with making a relationship connection. This shows up in "
"small quirks like word choices or phrases. Write from your point of view, "
"not from someone else's experience."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_default_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_reversed_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_texts_image_texts_template
msgid "Greater <br class=\"d-none d-lg-inline\"/>Impact"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_template
msgid "Greater Impact"
msgstr ""

#. module: website
#: model:ir.model.fields.selection,name:website.selection__website_controller_page__default_layout__grid
#: model_terms:ir.ui.view,arch_db:website.s_carousel_intro_options
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.s_website_controller_page_listing_layout
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Grid"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Group"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.menu_search
#: model_terms:ir.ui.view,arch_db:website.view_rewrite_search
#: model_terms:ir.ui.view,arch_db:website.website_controller_pages_search_view
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Group By"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_controller_page__groups_id
#: model:ir.model.fields,field_description:website.field_website_page__groups_id
#: model:ir.model.fields,field_description:website.field_website_page_properties__groups_id
msgid "Groups"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_personal_s_numbers
msgid "Growth Rate"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
#: model_terms:ir.ui.view,arch_db:website.s_features_wave
msgid ""
"Growth capability is a system's ability to scale and adapt, meeting "
"increasing demands and evolving needs for long-term success."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
msgid "H1"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
msgid "H2"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "H4 Card title"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "H5 Card subtitle"
msgstr ""

#. module: website
#: model:ir.ui.menu,name:website.menu_ace_editor
msgid "HTML / CSS Editor"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_ir_http
msgid "HTTP Routing"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Half Screen"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Half screen"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Hamburger menu"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Handcrafted Delights: Everything Homemade, Just for You."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_s_carousel
msgid "Happy Hour"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_countdown/options.xml:0
msgid "Happy Odoo Anniversary!"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website__has_social_default_image
msgid "Has Social Default Image"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Header"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_page__header_color
#: model:ir.model.fields,field_description:website.field_website_page__header_color
msgid "Header Color"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_page__header_overlay
#: model:ir.model.fields,field_description:website.field_website_page__header_overlay
msgid "Header Overlay"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Header Position"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__header_text_color
msgid "Header Text Color"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_page__header_visible
#: model:ir.model.fields,field_description:website.field_website_page__header_visible
msgid "Header Visible"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Heading"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Heading 1"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#: code:addons/website/static/src/xml/website.editor.xml:0
msgid "Headings"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Headings 1"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Headings 2"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Headings 3"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Headings 4"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Headings 5"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Headings 6"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Headline"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Height"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Height (Scrolled)"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_s_banner
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_s_text_cover
msgid "Hello, I'm Tony Fred"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Help center"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_pricelist_cafe
msgid ""
"Herbal tea made from dried chamomile flowers, known for its calming "
"properties and gentle, apple-like flavor."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid "Here are some common questions about our company."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/translator/translator.xml:0
msgid "Here are the visuals used to help you translate efficiently:"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Here is an overview of the cookies that may be stored on your device when "
"you visit our website:"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_carousel
msgid "Hidden"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_conditional_visibility
msgid "Hidden for"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Hide"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_accordion_options
msgid "Hide Borders"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Hide For"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_popup/000.js:0
msgid "Hide the cookies bar"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_page_properties_view_form
msgid "Hide this page from search results"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "High"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Highlight"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_accordion_options
msgid "Highlight Active"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
msgid "Highlight Animated Text"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
#: model_terms:ir.ui.view,arch_db:website.s_timeline_list
msgid "Highlight your history, showcase growth and key milestones."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_showcase
msgid "Highlights Key Attributes"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
msgid ""
"Hint: How to use Google Map on your website (Contact Us page and as a "
"snippet)"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/edit_menu.xml:0
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/xml/web_editor.xml:0
msgid ""
"Hint: Type '/' to search an existing page and '#' to link to an anchor."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/edit_menu.xml:0
#: model:website.menu,name:website.menu_home
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.s_tabs
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
msgid "Home"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Home <span class=\"visually-hidden\">(current)</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Home audio"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/views/page_list.xml:0
#: model_terms:ir.ui.view,arch_db:website.website_pages_kanban_view
msgid "Home page of the current website"
msgstr ""

#. module: website
#. odoo-javascript
#. odoo-python
#: code:addons/website/models/website.py:0
#: code:addons/website/static/src/client_actions/website_preview/website_preview.xml:0
#: model:ir.model.fields,field_description:website.field_website_page__is_homepage
#: model:ir.model.fields,field_description:website.field_website_page_properties__is_homepage
#: model:ir.model.fields,field_description:website.field_website_page_properties_base__is_homepage
#: model:ir.ui.menu,name:website.menu_website_preview
msgid "Homepage"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_homepage_url
#: model:ir.model.fields,field_description:website.field_website__homepage_url
msgid "Homepage Url"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Hoodies"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Horizontal"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_countdown/000.js:0
#: model_terms:ir.ui.view,arch_db:website.s_countdown
msgid "Hours"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
msgid "How can we help?"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_mosaic_template
msgid "How ideas come to life"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "How to create my Plausible Shared Link"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "How to get my Measurement ID"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Hue"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team_detail
msgid "Human Resources Manager"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
msgid "Hybrid"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: model_terms:ir.ui.view,arch_db:website.cookies_bar
msgid "I agree"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/page_properties.xml:0
msgid "I am sure about this."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
msgid "I want"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_personal_s_image_text
msgid ""
"I'm a fullstack developer with a background in management. My analytical "
"skills, coupled with effective communication, enable me to lead cross-"
"functional teams to success."
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__id
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__id
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__id
#: model:ir.model.fields,field_description:website.field_theme_website_menu__id
#: model:ir.model.fields,field_description:website.field_theme_website_page__id
#: model:ir.model.fields,field_description:website.field_website__id
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__id
#: model:ir.model.fields,field_description:website.field_website_controller_page__id
#: model:ir.model.fields,field_description:website.field_website_custom_blocked_third_party_domains__id
#: model:ir.model.fields,field_description:website.field_website_menu__id
#: model:ir.model.fields,field_description:website.field_website_page__id
#: model:ir.model.fields,field_description:website.field_website_page_properties__id
#: model:ir.model.fields,field_description:website.field_website_page_properties_base__id
#: model:ir.model.fields,field_description:website.field_website_rewrite__id
#: model:ir.model.fields,field_description:website.field_website_robots__id
#: model:ir.model.fields,field_description:website.field_website_route__id
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__id
#: model:ir.model.fields,field_description:website.field_website_track__id
#: model:ir.model.fields,field_description:website.field_website_visitor__id
msgid "ID"
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_ir_actions_server__xml_id
#: model:ir.model.fields,help:website.field_ir_cron__xml_id
msgid "ID of the action if defined in a XML file"
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_website_controller_page__xml_id
#: model:ir.model.fields,help:website.field_website_page__xml_id
msgid "ID of the view defined in xml file"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__iap_page_code
msgid "Iap Page Code"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__icon
#: model_terms:ir.ui.view,arch_db:website.s_blockquote_options
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "Icon"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_accordion_options
msgid "Icon Position"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid ""
"Ideal for newcomers. Essential features to kickstart sales and marketing. "
"Perfect for small teams."
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_website__specific_user_account
msgid "If True, new accounts will be associated to the current website"
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_website_configurator_feature__menu_sequence
msgid "If set, a website menu will be created for the feature."
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_website_configurator_feature__menu_company
msgid ""
"If set, add the menu as a second level menu, as a child of \"Company\" menu."
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__social_default_image
#: model:ir.model.fields,help:website.field_website__social_default_image
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "If set, replaces the website logo as the default social share image."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid ""
"If this error is caused by a change of yours in the templates, you have the "
"possibility to reset the template to its <strong>factory settings</strong>."
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_website_controller_page__groups_id
#: model:ir.model.fields,help:website.field_website_page__groups_id
#: model:ir.model.fields,help:website.field_website_page_properties__groups_id
msgid ""
"If this field is empty, the view applies to all users. Otherwise, the view "
"applies to the users of those groups only."
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_website_controller_page__active
#: model:ir.model.fields,help:website.field_website_page__active
msgid ""
"If this view is inherited,\n"
"* if True, the view always extends its parent\n"
"* if False, the view currently does not extend its parent but can be enabled\n"
"         "
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
msgid ""
"If you discard the current edits, all unsaved changes will be lost. You can "
"cancel to return to edit mode."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/resource_editor/resource_editor_warning.xml:0
#: code:addons/website/static/src/xml/website.editor.xml:0
msgid ""
"If you need to add analytics or marketing tags, inject code in your <head> "
"or <body> instead."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/resource_editor/resource_editor.js:0
msgid ""
"If you reset this file, all your customizations will be lost as it will be "
"reverted to the default file."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_unveil
msgid "Illustrate your services or your product’s main features."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_image_gallery/options.js:0
#: model:ir.model.fields,field_description:website.field_website_visitor__partner_image
#: model_terms:ir.ui.view,arch_db:website.grid_layout_options
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Image"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Image - Text"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Image - Text Overlap"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.xml:0
msgid "Image 1"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.xml:0
msgid "Image 2"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.xml:0
msgid "Image 3"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.xml:0
msgid "Image 4"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.xml:0
msgid "Image 5"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.xml:0
msgid "Image 6"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.xml:0
msgid "Image 7"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.xml:0
msgid "Image 8"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Image Frame"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Image Gallery"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/001.xml:0
msgid "Image Gallery Dialog"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Image Hexagonal"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Image Menu"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "Image Size"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Image Text Box"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Image Text Image"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Image Title"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_card_options
msgid "Image default"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/fields/fields.js:0
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Images"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Images Constellation"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Images Mosaic"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Images Spacing"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Images Subtitles"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Images Wall"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "In"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_kanban_view
msgid "In Main Menu"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_page_properties_base_view_form
msgid "In Menu"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "In Place"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website_form_editor.xml:0
msgid "In the meantime we invite you to visit our"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/translator/translator.xml:0
msgid ""
"In this mode, you can only translate texts. To change the structure of the page, you must edit the master page.\n"
"            Each modification on the master page is automatically applied to all translated versions."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_accordion_image
#: model_terms:ir.ui.view,arch_db:website.s_faq_list
msgid "In this section, you can address common questions efficiently."
msgstr ""

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__include
msgid "Include"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_image_template
msgid "Incredible <br class=\"d-none d-lg-inline\"/>Features"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_page_properties_view_form
msgid "Indexed"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_carousel
msgid "Indicators"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_carousel
msgid "Indicators outside"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.s_badge_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Info"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Info Page"
msgstr ""

#. module: website
#: model:website.configurator.feature,description:website.feature_page_about_us
msgid "Info and stats about your company"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Information about the"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__inherit_id
msgid "Inherit"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_controller_page__inherit_id
#: model:ir.model.fields,field_description:website.field_website_page__inherit_id
msgid "Inherited View"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/resource_editor/resource_editor_warning.xml:0
msgid "Inject code in <head> or <body>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Inline"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Inner"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Inner content"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel_intro
msgid "Innovating for business success"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_mosaic_template
msgid "Innovation"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_default_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_image_texts_image_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_images_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_reversed_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_texts_image_texts_template
msgid "Innovation <br class=\"d-none d-lg-inline\"/>Hub"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_image_text_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_image_text_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_template
msgid "Innovation Hub"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_striped_top
msgid "Innovation transforms possibilities into reality."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_cards_soft
msgid "Innovative Ideas"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Input Aligned"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
msgid "Input Fields"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Input Type"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
msgid "Insert a badge snippet"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
msgid "Insert a blockquote snippet"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
msgid "Insert a card snippet"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
msgid "Insert a chart snippet"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
msgid "Insert a progress bar snippet"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
msgid "Insert a rating snippet"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
msgid "Insert a share snippet"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
msgid "Insert a text Highlight snippet"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
msgid "Insert an alert snippet"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
msgid "Insert an horizontal separator snippet"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid ""
"Insert text styles like headers, bold, italic, lists, and fonts with a "
"simple WYSIWYG editor. Flexible and easy to use, it lets you design and "
"format documents in real time. No coding knowledge is needed, making content"
" creation straightforward and enjoyable for everyone."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "Inset"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_instagram_page/000.js:0
#: code:addons/website/static/src/snippets/s_social_media/options.js:0
#: model_terms:ir.ui.view,arch_db:website.header_social_links
#: model_terms:ir.ui.view,arch_db:website.s_company_team_detail
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
#: model_terms:ir.ui.view,arch_db:website.s_references_social
#: model_terms:ir.ui.view,arch_db:website.s_social_media
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Instagram"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website__social_instagram
msgid "Instagram Account"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_instagram_page_options
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Instagram Page"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/new_content.js:0
msgid "Install"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_base_language_install
msgid "Install Language"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Install languages"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Installed Applications"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Installed Localizations / Account Charts"
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_website__theme_id
msgid "Installed theme"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/new_content.js:0
msgid "Installing \"%s\""
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.js:0
msgid "Installing your %s."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.xml:0
msgid "Installing your features"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.js:0
msgid "Integrating your %s."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "Intensity"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Interaction History<br/>(optional)"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Intro"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Intro Pill"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_s_showcase
msgid "Intuitive Touch Controls"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid "Intuitive system"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
msgid "Invalid API Key. The following error was returned by Google: %(error)s"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers_grid
#: model_terms:ir.ui.view,arch_db:website.s_numbers_list
msgid "Inventory turnover"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_carousel
msgid "Invert colors"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
#: model_terms:ir.ui.view,arch_db:website.s_company_team_shapes
msgid "Iris Joe"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team_basic
msgid "Iris Joe, CFO"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid ""
"Iris, with her international experience, helps us easily understand the "
"numbers and improves them. She is determined to drive success and delivers "
"her professional acumen to bring the company to the next level."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_page_properties_base_view_form
msgid "Is Homepage"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_tree_view
msgid "Is In Main Menu"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__is_in_menu
#: model:ir.model.fields,field_description:website.field_website_page_properties__is_in_menu
#: model:ir.model.fields,field_description:website.field_website_page_properties_base__is_in_menu
msgid "Is In Menu"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__website_indexed
#: model:ir.model.fields,field_description:website.field_website_page_properties__website_indexed
msgid "Is Indexed"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_module_module__is_installed_on_current_website
msgid "Is Installed On Current Website"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__is_mega_menu
msgid "Is Mega Menu"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_partner__is_published
#: model:ir.model.fields,field_description:website.field_res_users__is_published
#: model:ir.model.fields,field_description:website.field_theme_website_page__is_published
#: model:ir.model.fields,field_description:website.field_website_controller_page__is_published
#: model:ir.model.fields,field_description:website.field_website_page__is_published
#: model:ir.model.fields,field_description:website.field_website_page_properties__is_published
#: model:ir.model.fields,field_description:website.field_website_page_properties_base__is_published
#: model:ir.model.fields,field_description:website.field_website_published_mixin__is_published
#: model:ir.model.fields,field_description:website.field_website_published_multi_mixin__is_published
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__is_published
msgid "Is Published"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__is_visible
#: model:ir.model.fields,field_description:website.field_website_page__is_visible
msgid "Is Visible"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_page_properties_view_form
msgid "Is a Template"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is after"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is after or equal to"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is before"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is before or equal to"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is between (included)"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__is_connected
msgid "Is connected?"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is equal to"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is greater than"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is greater than or equal to"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is less than"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is less than or equal to"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is not between (excluded)"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is not equal to"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is not set"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is set"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid ""
"It appears your website is still using the old color system of\n"
"            Odoo 13.0 in some places. We made sure it is still working but\n"
"            we recommend you to try to use the new color system, which is\n"
"            still customizable."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_showcase
msgid ""
"It captures your visitors' attention and helps them quickly understand the "
"value of your product."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_services_s_text_block_2nd
msgid ""
"It's time to elevate your fitness journey with coaching that's as unique as "
"you are. Choose your path, embrace the guidance, and transform your life."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Items"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/resource_editor/resource_editor.js:0
msgid "JS file: %s"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Jacket"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team_detail
msgid "James leads the tech strategy and innovation efforts at the company."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_pricelist_cafe
msgid "Jasmine Green Tea"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Jeans"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/new_content.js:0
msgid "Job Position"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_call_to_action
#: model_terms:ir.ui.view,arch_db:website.s_cta_card
#: model_terms:ir.ui.view,arch_db:website.s_cta_mockups
#: model_terms:ir.ui.view,arch_db:website.template_footer_call_to_action
msgid "Join us and make your company a better place."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_cta_box
msgid "Join us and make your company a better place.<br/><br/>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_call_to_action_menu
msgid ""
"Join us for a remarkable dining experience that blends exquisite flavors "
"with a warm ambiance."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
msgid "Keep empty to use default value"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_asset__key
#: model:ir.model.fields,field_description:website.field_ir_attachment__key
#: model:ir.model.fields,field_description:website.field_product_document__key
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__key
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__key
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__key
#: model:ir.model.fields,field_description:website.field_website_controller_page__key
#: model:ir.model.fields,field_description:website.field_website_page__key
msgid "Key"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_default_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_images_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_reversed_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_texts_image_texts_template
msgid "Key <br class=\"d-none d-lg-inline\"/>Milestone"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Key Images"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers_list
msgid "Key Metrics of Company's Achievements"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers_charts
msgid "Key Metrics of Company's<br/>Achievements"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "Key Metrics of<br/>Company's Achievements"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_image_text_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_image_text_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_template
msgid "Key Milestone"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Key benefits"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Keyboards"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
msgid "Keyword"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
msgid "Keywords"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Kickoff"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Label"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_model__website_form_label
msgid "Label for form action"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Labels Width"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_groups
msgid "Landing Pages"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_card_options
msgid "Landscape - 4/3"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__lang_id
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Language"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Language Selector"
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__lang_id
msgid "Language from the website when visitor has been created"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_res_lang
#: model:ir.model.fields,field_description:website.field_res_config_settings__language_ids
#: model:ir.model.fields,field_description:website.field_website__language_ids
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Languages"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Languages available on your website"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Laptops"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Large"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_pricelist_boxed
msgid "Lasagna al Forno"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Last 7 Days"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
msgid "Last Action"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__last_connection_datetime
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Last Connection"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_multi_menus
msgid "Last Menu"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_tree
msgid "Last Page"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__write_uid
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__write_uid
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__write_uid
#: model:ir.model.fields,field_description:website.field_theme_website_menu__write_uid
#: model:ir.model.fields,field_description:website.field_theme_website_page__write_uid
#: model:ir.model.fields,field_description:website.field_website__write_uid
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__write_uid
#: model:ir.model.fields,field_description:website.field_website_controller_page__write_uid
#: model:ir.model.fields,field_description:website.field_website_custom_blocked_third_party_domains__write_uid
#: model:ir.model.fields,field_description:website.field_website_menu__write_uid
#: model:ir.model.fields,field_description:website.field_website_page__write_uid
#: model:ir.model.fields,field_description:website.field_website_page_properties__write_uid
#: model:ir.model.fields,field_description:website.field_website_page_properties_base__write_uid
#: model:ir.model.fields,field_description:website.field_website_rewrite__write_uid
#: model:ir.model.fields,field_description:website.field_website_robots__write_uid
#: model:ir.model.fields,field_description:website.field_website_route__write_uid
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__write_uid
#: model:ir.model.fields,field_description:website.field_website_visitor__write_uid
msgid "Last Updated by"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__write_date
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__write_date
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__write_date
#: model:ir.model.fields,field_description:website.field_theme_website_menu__write_date
#: model:ir.model.fields,field_description:website.field_theme_website_page__write_date
#: model:ir.model.fields,field_description:website.field_website__write_date
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__write_date
#: model:ir.model.fields,field_description:website.field_website_controller_page__write_date
#: model:ir.model.fields,field_description:website.field_website_custom_blocked_third_party_domains__write_date
#: model:ir.model.fields,field_description:website.field_website_menu__write_date
#: model:ir.model.fields,field_description:website.field_website_page__write_date
#: model:ir.model.fields,field_description:website.field_website_page_properties__write_date
#: model:ir.model.fields,field_description:website.field_website_page_properties_base__write_date
#: model:ir.model.fields,field_description:website.field_website_rewrite__write_date
#: model:ir.model.fields,field_description:website.field_website_robots__write_date
#: model:ir.model.fields,field_description:website.field_website_route__write_date
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__write_date
#: model:ir.model.fields,field_description:website.field_website_visitor__write_date
msgid "Last Updated on"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__last_visited_page_id
msgid "Last Visited Page"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__time_since_last_action
msgid "Last action"
msgstr ""

#. module: website
#. odoo-python
#: code:addons/website/controllers/main.py:0
msgid "Last modified pages"
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__last_connection_datetime
msgid "Last page view date"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid "Latest Feature"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid "Latest news"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Latests news and case studies"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_pricelist_boxed
msgid ""
"Layers of pasta, rich meat ragu, béchamel sauce, and melted mozzarella, "
"baked to perfection."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel_intro_options
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
#: model_terms:ir.ui.view,arch_db:website.s_website_controller_page_listing_layout
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Layout"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Layout Background"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Layout Background Color"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Lead text"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel_intro
msgid "Leading the future with innovation and strategy"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_text_box
msgid "Learn about our offerings"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_striped
msgid "Learn about the key decisions that have shaped our identity."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_horizontal
msgid ""
"Learn how to quickly set up and start using our services with our step-by-"
"step onboarding process."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_empowerment
#: model_terms:ir.ui.view,arch_db:website.s_freegrid
#: model_terms:ir.ui.view,arch_db:website.s_image_text
#: model_terms:ir.ui.view,arch_db:website.s_image_text_box
#: model_terms:ir.ui.view,arch_db:website.s_image_text_overlap
#: model_terms:ir.ui.view,arch_db:website.s_mockup_image
#: model_terms:ir.ui.view,arch_db:website.s_shape_image
#: model_terms:ir.ui.view,arch_db:website.s_text_image
msgid "Learn more"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_accordion_options
#: model_terms:ir.ui.view,arch_db:website.s_blockquote_options
#: model_terms:ir.ui.view,arch_db:website.s_card_options
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_options_template
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Left"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menu_image_menu
msgid "Left Menu"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_blockquote_options
msgid "Left line"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
msgid "Legal"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "Legal Notice"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Legend"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Let your customers log in to see their documents"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Let your customers understand your process."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_title_form
msgid "Let's Connect"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_call_to_action_about
msgid ""
"Let's collaborate to create innovative solutions that stand out in the "
"digital landscape. Reach out today and let's build something extraordinary "
"together."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
msgid "Let's do it"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
msgid "Let's go!"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_kickoff
msgid "Let's kick<br/>things off !"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_call_to_action_digital
msgid ""
"Let's turn your vision into reality. Contact us today to set your brand on "
"the path to digital excellence with us."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_opening_hours
msgid "Let’s get in touch"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "Library"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#: model_terms:ir.ui.view,arch_db:website.s_badge_options
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Light"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Light &amp; Dark"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__limit
msgid "Limit"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_process_steps_options
msgid "Line"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Line Height"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "Link"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
msgid "Link Anchor"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Link Style"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Link button"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "Link text"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_list
msgid "Link to product"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_social_media/options.js:0
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.header_social_links
#: model_terms:ir.ui.view,arch_db:website.s_company_team_detail
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
#: model_terms:ir.ui.view,arch_db:website.s_share
#: model_terms:ir.ui.view,arch_db:website.s_social_media
#: model_terms:ir.ui.view,arch_db:website.template_footer_centered
msgid "LinkedIn"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website__social_linkedin
msgid "LinkedIn Account"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Linkedin"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Links"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Links Color"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Links Style"
msgstr ""

#. module: website
#: model:ir.model.fields.selection,name:website.selection__website_controller_page__default_layout__list
#: model_terms:ir.ui.view,arch_db:website.s_website_controller_page_listing_layout
msgid "List"
msgstr "सूची"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
#: model_terms:ir.ui.view,arch_db:website.s_features_wave
msgid "List and describe the key features of your solution or service."
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website__blocked_third_party_domains
msgid "List of blocked 3rd-party domains"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "List-group"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_controller_page__view_id
msgid "Listing view"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid ""
"Listing your product pricing helps potential customers quickly determine if "
"it fits their budget and needs."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Little Icons"
msgstr ""

#. module: website
#: model:website.configurator.feature,name:website.feature_module_live_chat
msgid "Live Chat"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Livechat"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/new_content.js:0
msgid "Livechat Widget"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/website_preview/website_preview.xml:0
#: code:addons/website/static/src/components/dialog/add_page_dialog.js:0
#: code:addons/website/static/src/components/dialog/seo.xml:0
#: code:addons/website/static/src/xml/web_editor.xml:0
#: code:addons/website/static/src/xml/website.background.video.xml:0
msgid "Loading..."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Logo"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_centered
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
msgid "Logo of MyCompany"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Logos"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Long Text"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Low"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Main"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website__menu_id
msgid "Main Menu"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
msgid "Make sure billing is enabled"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
msgid ""
"Make sure to wait if errors keep being shown: sometimes enabling an API "
"allows to use it immediately but Google keeps triggering errors for a while"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
msgid "Make sure your settings are properly configured:"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_default_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_reversed_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_texts_image_texts_template
msgid "Making a difference every day"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_s_features
msgid "Management"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/utils.js:0
#: model_terms:ir.ui.view,arch_db:website.s_map
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Map"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
msgid "Maps JavaScript API"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
msgid "Maps Static API"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_pricelist_boxed
msgid "Margherita"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Margins"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Mark Text"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_mosaic_template
msgid "Mark the difference"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Marked Fields"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
msgid "Marker Style"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_s_features
msgid "Marketing"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team_detail
msgid "Marketing Director"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Marketplace"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Masonry"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_s_features
msgid ""
"Mastering frontend craftsmanship with expertise in HTML, CSS, and JavaScript"
" to craft captivating and responsive user experiences."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Max # of Files"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Max Axis"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Max File Size"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Measurement ID"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "Media"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Media List"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid "Media heading"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/content/snippets.animation.js:0
msgid "Media video"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns_menu
msgid "Mediterranean buffet of starters, main dishes and desserts"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Medium"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_full_1_s_text_block_h2
msgid "Meet The Team"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
#: model_terms:ir.ui.view,arch_db:website.s_company_team_detail
msgid "Meet our team"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/edit_menu.xml:0
msgid "Mega Menu"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_menu__mega_menu_classes
#: model:ir.model.fields,field_description:website.field_website_menu__mega_menu_classes
msgid "Mega Menu Classes"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_menu__mega_menu_content
#: model:ir.model.fields,field_description:website.field_website_menu__mega_menu_content
msgid "Mega Menu Content"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menu_image_menu
msgid "Mega menu default image"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Men"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_ir_ui_menu
#: model:ir.model.fields,field_description:website.field_website_menu__name
#: model:ir.model.fields,field_description:website.field_website_page_properties__menu_ids
#: model:ir.model.fields,field_description:website.field_website_page_properties_base__menu_ids
msgid "Menu"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Menu - Sales 1"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Menu - Sales 2"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Menu - Sales 3"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Menu - Sales 4"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__menu_company
msgid "Menu Company"
msgstr ""

#. module: website
#: model:ir.ui.menu,name:website.menu_edit_menu
msgid "Menu Editor"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menu_image_menu
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_multi_menus
msgid "Menu Item %s"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns_menu
msgid "Menu One"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__menu_sequence
msgid "Menu Sequence"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns_menu
msgid "Menu Two"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_menu__copy_ids
msgid "Menu using a copy of me"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Menu with Search bar"
msgstr ""

#. module: website
#: model:ir.ui.menu,name:website.menu_website_menu_list
#: model_terms:ir.ui.view,arch_db:website.website_controller_pages_form_view
msgid "Menus"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_base_partner_merge_automatic_wizard
msgid "Merge Partner Wizard"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page_options
msgid "Messages"
msgstr "संदेश"

#. module: website
#. odoo-python
#: code:addons/website/controllers/form.py:0
msgid "Metadata"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
#: model_terms:ir.ui.view,arch_db:website.s_company_team_shapes
msgid "Mich Stark"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_team_0_s_three_columns
#: model_terms:ir.ui.view,arch_db:website.new_page_template_team_s_media_list
#: model_terms:ir.ui.view,arch_db:website.new_page_template_team_s_text_image
#: model_terms:ir.ui.view,arch_db:website.s_company_team_basic
msgid "Mich Stark, COO"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_team_s_text_image
msgid ""
"Mich loves taking on challenges. With his multi-year experience as "
"Commercial Director in the software industry, Mich has helped the company to"
" get where it is today."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_team_0_s_three_columns
#: model_terms:ir.ui.view,arch_db:website.new_page_template_team_s_media_list
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid ""
"Mich loves taking on challenges. With his multi-year experience as "
"Commercial Director in the software industry, Mich has helped the company to"
" get where it is today. Mich is among the best minds."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.template_header_sales_two
msgid "Middle"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline_list_options
msgid "Milestones"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Min Axis"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
msgid "Min-Height"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Minimalist"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_countdown/000.js:0
#: model_terms:ir.ui.view,arch_db:website.s_countdown
msgid "Minutes"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Mirror Blur"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/views/theme_preview.xml:0
#: model:ir.model.fields,field_description:website.field_website_visitor__mobile
msgid "Mobile"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Mobile Alignment"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
msgid "Mobile Preview"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/mobile_preview.xml:0
msgid "Mobile preview"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Mockup Image"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Modal"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Modal title"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__mode
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Mode"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_controller_page__model
#: model:ir.model.fields,field_description:website.field_website_page__model
#: model_terms:ir.ui.view,arch_db:website.website_controller_pages_search_view
msgid "Model"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_ir_model_data
#: model:ir.model.fields,field_description:website.field_website_controller_page__model_data_id
#: model:ir.model.fields,field_description:website.field_website_page__model_data_id
msgid "Model Data"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_website_controller_page
msgid "Model Page"
msgstr ""

#. module: website
#: model:ir.ui.menu,name:website.menu_website_controller_pages_list
msgid "Model Pages"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__model_name
msgid "Model name"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_controller_page__model_id
#: model:ir.model.fields,field_description:website.field_website_page__model_id
msgid "Model of the view"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_ir_model
msgid "Models"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_list
msgid "Modern"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_controller_page__arch_updated
#: model:ir.model.fields,field_description:website.field_website_page__arch_updated
msgid "Modified Architecture"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_ir_module_module
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__module_id
msgid "Module"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__module_marketing_automation
msgid "Module Marketing Automation"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__module_website_livechat
msgid "Module Website Livechat"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Monitor Google Search results data"
msgstr ""

#. module: website
#: model_terms:digest.tip,tip_description:website.digest_tip_website_0
msgid ""
"Monitor your visitors while they are browsing your website with the Odoo "
"Social app. Engage with them in just a click using a live chat request or a "
"push notification. If they have completed one of your forms, you can send "
"them an SMS, or call them right away while they are browsing your website."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Monitors"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_color_blocks_2
msgid "More Details"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers_showcase
msgid "More details"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers_showcase
msgid "More leads"
msgstr ""

#. module: website
#: model_terms:digest.tip,tip_description:website.digest_tip_website_4
msgid ""
"More than 90 shapes exist and their colors are picked to match your Theme."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Mosaic"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
msgid "Most searched topics related to your keyword, ordered by importance"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Motto"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Mouse"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Move Backward"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Move Forward"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Move to first"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Move to last"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Move to next"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Move to previous"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Multi Menus"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_website_multi_mixin
msgid "Multi Website Mixin"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_website_published_multi_mixin
msgid "Multi Website Published Mixin"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__group_multi_website
#: model:res.groups,name:website.group_multi_website
msgid "Multi-website"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Multiple Checkboxes"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/backend/view_hierarchy/view_hierarchy.xml:0
msgid "Multiple tree exists for this view"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
msgid "My Company"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_s_features
msgid "My Skills"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.option_header_brand_name
msgid "My Website"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_centered
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
msgid "MyCompany"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/edit_menu.xml:0
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__name
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__name
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__name
#: model:ir.model.fields,field_description:website.field_theme_website_menu__name
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__name
#: model:ir.model.fields,field_description:website.field_website_rewrite__name
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__name
#: model:ir.model.fields,field_description:website.field_website_visitor__name
#: model_terms:ir.ui.view,arch_db:website.menu_search
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_tree
msgid "Name"
msgstr "नाम"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Name (A-Z)"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/backend/view_hierarchy/hierarchy_navbar.xml:0
msgid "Name, id or key"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Narrow"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Nav and tabs"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Navbar"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_list
msgid "Need help?"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid ""
"Need to pick up your order at one of our stores? Discover the nearest to "
"you."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Network Advertising Initiative opt-out page"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Networks"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.neutralize_ribbon
msgid "Neutralized"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/new_content.xml:0
msgid "New"
msgstr "नया"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/add_page_dialog.js:0
#: code:addons/website/static/src/components/dialog/add_page_dialog.xml:0
#: code:addons/website/static/src/systray_items/new_content.xml:0
msgid "New Page"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_page__is_new_page_template
#: model:ir.model.fields,field_description:website.field_website_page__is_new_page_template
#: model:ir.model.fields,field_description:website.field_website_page_properties__is_new_page_template
msgid "New Page Template"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_menu__new_window
#: model:ir.model.fields,field_description:website.field_website_menu__new_window
msgid "New Window"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "New collection"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup
msgid "New customer"
msgstr ""

#. module: website
#: model:website.configurator.feature,name:website.feature_module_news
msgid "News"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Newsletter"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Newsletter Block"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Newsletter Popup"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_dynamic_snippet_carousel/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/001.xml:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_carousel
#: model_terms:ir.ui.view,arch_db:website.s_carousel_intro
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel_minimal
msgid "Next"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_horizontal
msgid ""
"Next, you will be introduced to our <b>setup wizard</b>, which is designed "
"to guide you through the basic configuration of the platform. The wizard "
"will help you configure essential settings such as language, time zone, and "
"notifications."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.step_wizard
msgid "Next:"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "No Slide Effect"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "No Underline"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/fields/widget_iframe.xml:0
msgid "No Url"
msgstr ""

#. module: website
#: model_terms:ir.actions.act_window,help:website.website_visitors_action
msgid "No Visitors yet!"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "No condition"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website_form_editor.xml:0
msgid "No matching record!"
msgstr ""

#. module: website
#: model_terms:ir.actions.act_window,help:website.website_visitor_page_action
msgid "No page views yet for this visitor"
msgstr ""

#. module: website
#: model_terms:ir.actions.act_window,help:website.visitor_partner_action
msgid "No partner linked for this visitor"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/add_page_dialog.js:0
msgid "No preview for the %s block because it is dynamically rendered."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_searchbar/000.xml:0
#: model_terms:ir.ui.view,arch_db:website.list_website_public_pages
msgid "No results found for '"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_searchbar/000.xml:0
msgid "No results found. Please try another search."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_options_template
msgid "No title"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/website_switcher.js:0
msgid "No website domain configured for this website."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.column_count_option
#: model_terms:ir.ui.view,arch_db:website.s_blockquote_options
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_options_template
#: model_terms:ir.ui.view,arch_db:website.s_process_steps_options
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_carousel
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "None"
msgstr ""

#. module: website
#: model:website,prevent_zero_price_sale_text:website.default_website
#: model:website,prevent_zero_price_sale_text:website.website2
msgid "Not Available For Sale"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_kanban_view
msgid "Not Published"
msgstr ""

#. module: website
#: model_terms:digest.tip,tip_description:website.digest_tip_website_3
msgid ""
"Not only can you search for royalty-free illustrations, their colors are "
"also converted so that they always fit your Theme."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_view_search
msgid "Not published"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_view_search
msgid "Not tracked"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Note that some third-party services may install additional cookies on your "
"browser in order to identify you."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_menus_form_view
msgid ""
"Note that the \"Website / Editor and Designer\" group is implicitly added "
"when saving if any group is specified."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/resource_editor/resource_editor_warning.xml:0
msgid ""
"Note: To embed code in this specific page, use the \"Embed Code\" building "
"block"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid ""
"Note: To hide this page, uncheck it from the Customize tab in edit mode."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Nothing"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Number"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_language_count
#: model:ir.model.fields,field_description:website.field_website__language_count
msgid "Number of languages"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_carousel
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Numbers"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Numbers Charts"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Numbers Grid"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Numbers Showcase"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Numbers list"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_2_s_three_columns
msgid "Nutritional Guidance"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
msgid "OR"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Odoo Information"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Odoo Menu"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Odoo Version"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Office audio"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Office screens"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
msgid "Offline"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "Offset (X, Y)"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/dialog.js:0
#: code:addons/website/static/src/components/dialog/page_properties.xml:0
#: code:addons/website/static/src/components/translator/translator.js:0
msgid "Ok"
msgstr "ठीक है"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/translator/translator.js:0
msgid "Ok, never show me this again"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page_properties__old_url
msgid "Old Url"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team_detail
msgid "Olivia oversees product development from concept to launch."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "On Appearance"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "On Click"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "On Click (via link)"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "On Click Effect"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "On Exit"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "On Hover"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "On Scroll"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "On Success"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_controller_pages_kanban_view
msgid "On all websites"
msgstr ""

#. module: website
#: model:ir.model.fields.selection,name:website.selection__website__auth_signup_uninvited__b2b
msgid "On invitation"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_s_showcase
msgid "On-the-Go Charging"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid ""
"Once the user closes the popup, it won't be shown again for that period of "
"time."
msgstr ""

#. module: website
#. odoo-python
#: code:addons/website/models/website_configurator_feature.py:0
msgid ""
"One and only one of the two fields 'page_view_id' and 'module_id' should be "
"set"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
msgid "Online"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers_grid
msgid "Online Members"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/resource_editor/resource_editor.js:0
msgid "Only Custom SCSS Files"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/resource_editor/resource_editor.js:0
msgid "Only Page SCSS Files"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/resource_editor/resource_editor.js:0
msgid "Only Views"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
msgid "Only allow essential cookies"
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_website_controller_page__mode
#: model:ir.model.fields,help:website.field_website_page__mode
msgid ""
"Only applies if this view inherits from an other one (inherit_id is not False/Null).\n"
"\n"
"* if extension (default), if this view is requested the closest primary view\n"
"is looked up (via inherit_id), then all views inheriting from it with this\n"
"view's model are applied\n"
"* if primary, the closest primary view is fully resolved (even if it uses a\n"
"different model than this one), then this view's inheritance specs\n"
"(<xpath/>) are applied, and the result is used as if it were this view's\n"
"actual arch.\n"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: model_terms:ir.ui.view,arch_db:website.cookies_bar
msgid "Only essentials"
msgstr ""

#. module: website
#: model:ir.actions.client,name:website.open_custom_menu
msgid "Open Custom Menu"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Open Source ERP"
msgstr ""

#. module: website
#: model:ir.actions.client,name:website.action_open_website_configurator
msgid "Open Website Configurator"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Opening Hours"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team_detail
msgid "Operations Manager"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.js:0
#: code:addons/website/static/src/js/utils.js:0
#: model:ir.ui.menu,name:website.menu_optimize_seo
msgid "Optimize SEO"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
msgid "Option 1"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
msgid "Option 2"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
msgid "Option 3"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
msgid "Option List"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Optional"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Order by"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_showcase
msgid ""
"Organizing and presenting key information effectively increases the "
"likelihood of turning your visitors into customers."
msgstr ""

#. module: website
#. odoo-python
#: code:addons/website/controllers/form.py:0
msgid "Other Information:"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_social_media/options.js:0
msgid "Other social network"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_s_text_image
msgid "Our Approach"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_services_0_s_three_columns
msgid ""
"Our Coaching combines personalized fitness plans with mindfulness practices,"
" ensuring you achieve harmony in your body and peace in your mind."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Our Company"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_full_s_text_image
msgid "Our Goals"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_pricelist_boxed
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Our Menu"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_pricing_5_s_text_block_h1
msgid "Our Menus"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_s_three_columns
msgid "Our Mission"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_3_s_text_block_h2
msgid "Our Offer"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_map_s_text_block_h2
msgid "Our Offices"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_2_s_text_block_h2
#: model_terms:ir.ui.view,arch_db:website.new_page_template_services_s_text_block_h1
msgid "Our Services"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_full_s_image_text
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_mini_s_text_block_h2
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_s_three_columns
msgid "Our Story"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_s_picture
#: model_terms:ir.ui.view,arch_db:website.new_page_template_team_s_text_block_h1
msgid "Our Team"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_s_three_columns
msgid "Our Values"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_accordion
msgid ""
"Our company specializes in consulting, product development, and customer "
"support. We tailor our services to fit the unique needs of businesses across"
" various sectors, helping them grow and succeed in a competitive market."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_popup/000.js:0
msgid "Our cookies bar was blocked by your browser or an extension."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_cards_soft
msgid ""
"Our creativity is at the forefront of everything we do, delivering "
"innovative solutions that make your project stand out while maintaining a "
"balance between originality and functionality."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid ""
"Our design features offer a range of tools to create visually stunning "
"websites. Utilize WYSIWYG editors, drag-and-drop building blocks, and "
"Bootstrap-based templates for effortless customization. With professional "
"themes and an intuitive system, you can design with ease and precision, "
"ensuring a polished, responsive result."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_horizontal
msgid ""
"Our development team works tirelessly to enhance the platform's performance,"
" security, and functionality, ensuring it remains at the cutting edge of "
"innovation."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_2_s_three_columns
msgid ""
"Our experienced fitness coaches design workouts that align with your goals, "
"fitness level, and preferences."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_list
msgid "Our finest selection"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid ""
"Our intuitive system ensures effortless navigation for users of all skill "
"levels. Its clean interface and logical organization make tasks easy to "
"complete. With tooltips and contextual help, users quickly become "
"productive, enjoying a smooth and efficient experience."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers_charts
msgid ""
"Our key metrics, from revenue growth to customer retention and market "
"expansion, highlight our strategic prowess and commitment to sustainable "
"business success."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_template
msgid "Our latest content"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_s_three_columns
msgid ""
"Our mission is to create transformative experiences and foster growth, "
"driven by a relentless pursuit of innovation and a commitment to exceeding "
"expectations."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Our process in four easy steps"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_services_s_image_text_2nd
msgid ""
"Our seasoned consultants provide tailored guidance, leveraging their deep "
"industry knowledge to analyze your current strategies, identify "
"opportunities, and formulate data-driven recommendations."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Our seminars and trainings for you"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_cards_soft
msgid ""
"Our services are built to last, ensuring that every solution we provide is "
"of the highest quality, bringing lasting value to your investment and "
"ultimate customer satisfaction."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_pricing_s_text_block_2nd
msgid ""
"Our software plans are designed to cater to a variety of needs, ensuring "
"that you find the perfect fit for your requirements. From individual users "
"to businesses of all sizes, we offer pricing options that provide "
"exceptional value without compromising on features or performance."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_discovery
msgid "Our store"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_key_benefits
msgid ""
"Our support team is available 24/7 to assist with any inquiries or issues, "
"ensuring you get help whenever you need it."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_horizontal
msgid ""
"Our support team is available 24/7 to assist with any issues or questions "
"you may have, ensuring that help is always within reach."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team_shapes
msgid "Our talented crew"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid "Our team"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website_form_editor.xml:0
msgid "Our team will message you back as soon as possible."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_references_social
msgid "Our valued partners"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_s_three_columns
msgid ""
"Our values shape our culture, influence our decisions, and guide us in "
"providing exceptional service. They reflect our dedication to integrity, "
"collaboration, and client satisfaction."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_list
msgid ""
"Our website is designed for easy navigation, allowing you to find the "
"information you need quickly and efficiently."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Out"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Outline"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "Outset"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Outside, at left"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Outside, at right"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Outside, center"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Over The Content"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Overlay"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Padding"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Padding (Y, X)"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Paddings"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/new_content.xml:0
#: model:ir.model,name:website.model_website_page
#: model:ir.model.fields,field_description:website.field_ir_ui_view__page_ids
#: model:ir.model.fields,field_description:website.field_theme_website_menu__page_id
#: model:ir.model.fields,field_description:website.field_website_controller_page__page_ids
#: model:ir.model.fields,field_description:website.field_website_page__page_ids
#: model:ir.model.fields,field_description:website.field_website_track__page_id
#: model_terms:ir.ui.view,arch_db:website.website_controller_pages_form_view
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
msgid "Page"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/xml/web_editor.xml:0
msgid "Page Anchor"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_controller_pages_form_view
msgid "Page Details"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_page__website_indexed
msgid "Page Indexed"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Page Layout"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/page_properties.xml:0
msgid "Page Name"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/page_properties.js:0
#: model:ir.model,name:website.model_website_page_properties
msgid "Page Properties"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_website_page_properties_base
msgid "Page Properties Base"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/add_page_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:website.website_controller_pages_tree_view
#: model_terms:ir.ui.view,arch_db:website.website_page_properties_view_form
#: model_terms:ir.ui.view,arch_db:website.website_pages_tree_view
msgid "Page Title"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__url
#: model:ir.model.fields,field_description:website.field_website_page_properties__url
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page_options
#: model_terms:ir.ui.view,arch_db:website.website_page_properties_view_form
#: model_terms:ir.ui.view,arch_db:website.website_pages_tree_view
msgid "Page URL"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__page_view_id
msgid "Page View"
msgstr ""

#. module: website
#: model:ir.actions.act_window,name:website.website_visitor_view_action
#: model:ir.model.fields,field_description:website.field_website_visitor__visitor_page_count
#: model:ir.ui.menu,name:website.menu_visitor_view_menu
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid "Page Views"
msgstr ""

#. module: website
#: model:ir.actions.act_window,name:website.website_visitor_page_action
msgid "Page Views History"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Page Visibility"
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_website_configurator_feature__iap_page_code
msgid ""
"Page code used to tell IAP website_service for which page a snippet list "
"should be generated"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/utils.js:0
msgid "Page description not set."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/utils.js:0
msgid "Page title not set."
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_page__copy_ids
msgid "Page using a copy of me"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#: model:ir.ui.menu,name:website.menu_website_pages_list
#: model_terms:ir.ui.view,arch_db:website.list_website_public_pages
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_tree
msgid "Pages"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Pagination"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Pants"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
msgid "Paragraph"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid ""
"Paragraph text. Lorem <b>ipsum dolor sit amet</b>, consectetur adipiscing "
"elit. <i>Integer posuere erat a ante</i>."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid ""
"Paragraph with <strong>bold</strong>, <span class=\"text-"
"muted\">muted</span> and <em>italic</em> texts"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "Paragraph."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Parallax"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_menu__parent_id
msgid "Parent"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__parent_id
msgid "Parent Menu"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__parent_path
msgid "Parent Path"
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__partner_id
msgid "Partner of the last logged in user."
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_website__partner_id
msgid "Partner-related data of the user"
msgstr ""

#. module: website
#: model:ir.actions.act_window,name:website.visitor_partner_action
msgid "Partners"
msgstr "साथी"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_references
msgid "Partners and references"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_page_properties_view_form
msgid "Password"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Pastries"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__path
msgid "Path"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Pattern"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Pay"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_pricelist_boxed
msgid "Penne all'Arrabbiata"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_pricelist_boxed
msgid ""
"Penne pasta tossed in a spicy tomato and garlic sauce with a hint of chili "
"peppers, finished with fresh parsley."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "People"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
#: model_terms:ir.ui.view,arch_db:website.s_features_wave
msgid "Performance"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_2_s_cover
msgid "Personalized Fitness"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_2_s_three_columns
msgid "Personalized Workouts"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team_shapes
msgid "Pete Bluestork"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/send_mail_form.js:0
msgid "Phone Number"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Phones"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/views/theme_preview.xml:0
#: model:ir.actions.act_window,name:website.theme_install_kanban_action
msgid "Pick a Theme"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Pie"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Pills"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_share
msgid "Pinterest"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Placeholder"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
msgid "Places API"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Plain"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__has_plausible_shared_key
msgid "Plausible Analytics"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website__plausible_shared_key
msgid "Plausible Shared Key"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website__plausible_site
msgid "Plausible Site"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__plausible_site
msgid "Plausible Site (e.g. domain.com)"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__plausible_shared_key
msgid "Plausible auth Key"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/000.js:0
msgid "Please fill in the form correctly."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/000.js:0
msgid ""
"Please fill in the form correctly. The file “%(file name)s” is too large. "
"(Maximum %(max)s MB)"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/000.js:0
msgid ""
"Please fill in the form correctly. You uploaded too many files. (Maximum %s "
"files)"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid "Points of sale"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Popup"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_portal_wizard_user
msgid "Portal User Config"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "Portfolio"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Position"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid "Post heading"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Postcard"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Preferences<br/>(essential)"
msgstr ""

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__prepend
msgid "Prepend"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "Preset"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/fullscreen_indication/fullscreen_indication.js:0
msgid "Press %(key)s to exit full screen"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/backend/view_hierarchy/view_hierarchy.xml:0
msgid "Press <Tab> for next ["
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
#: code:addons/website/static/src/xml/website.editor.xml:0
msgid "Preview"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
msgid "Preview of"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
msgid "Preview this URL in a new tab"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_dynamic_snippet_carousel/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/001.xml:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_carousel
#: model_terms:ir.ui.view,arch_db:website.s_carousel_intro
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel_minimal
msgid "Previous"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_controller_page__arch_prev
#: model:ir.model.fields,field_description:website.field_website_page__arch_prev
msgid "Previous View Architecture"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Pricelist"
msgstr "मूल्य सूची"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Pricelist Boxed"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Pricelist Cafe"
msgstr ""

#. module: website
#: model:website.configurator.feature,name:website.feature_page_pricing
#: model_terms:ir.ui.view,arch_db:website.pricing
msgid "Pricing"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_groups
#: model_terms:ir.ui.view,arch_db:website.new_page_template_pricing_s_text_block_h1
msgid "Pricing Plans"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.s_badge_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Primary"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
msgid "Primary Buttons"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Primary Style"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Printers"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__priority
msgid "Priority"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Privacy"
msgstr ""

#. module: website
#. odoo-python
#: code:addons/website/models/website.py:0
#: model:website.configurator.feature,name:website.feature_page_privacy_policy
#: model_terms:ir.ui.view,arch_db:website.privacy_policy
msgid "Privacy Policy"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/new_content.js:0
#: model_terms:ir.ui.view,arch_db:website.s_pricelist_boxed_add_product_widget
#: model_terms:ir.ui.view,arch_db:website.s_pricelist_cafe_add_product_widget
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog_add_product_widget
msgid "Product"
msgstr "उत्पाद"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team_detail
msgid "Product Manager"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_frame
msgid "Product highlight"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
#: model_terms:ir.ui.view,arch_db:website.snippets
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
msgid "Products"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Professional"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_s_features
msgid ""
"Proficient in backend development, specializing in Python, Django, and "
"database management to create efficient and scalable solutions."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs
msgid "Profile"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Progress Bar"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Progress Bar Color"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Progress Bar Style"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Progress Bar Weight"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_2_s_three_columns
msgid "Progress Tracking"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers_charts
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar
msgid "Progress bar"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Projectors"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers_grid
#: model_terms:ir.ui.view,arch_db:website.s_numbers_list
msgid "Projects deployed"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Promotions"
msgstr ""

#. module: website
#: model:ir.ui.menu,name:website.menu_page_properties
msgid "Properties"
msgstr ""

#. module: website
#: model:ir.model.fields.selection,name:website.selection__ir_ui_view__visibility__
msgid "Public"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website__partner_id
msgid "Public Partner"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website__user_id
msgid "Public User"
msgstr ""

#. module: website
#: model:res.groups,name:website.website_page_controller_expose
msgid "Public access to arbitrary exposed model"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/fields/publish_button.xml:0
#: code:addons/website/static/src/components/views/page_list.js:0
msgid "Publish"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/views/page_list.js:0
msgid "Publish Website Content"
msgstr ""

#. module: website
#: model:website.configurator.feature,description:website.feature_module_career
msgid "Publish job offers and let people apply"
msgstr ""

#. module: website
#: model:website.configurator.feature,description:website.feature_module_event
msgid "Publish on-site and online events"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/fields/publish_button.xml:0
#: code:addons/website/static/src/components/fields/redirect_field.js:0
#: code:addons/website/static/src/systray_items/publish.js:0
#: model_terms:ir.ui.view,arch_db:website.publish_management
#: model_terms:ir.ui.view,arch_db:website.website_page_properties_base_view_form
#: model_terms:ir.ui.view,arch_db:website.website_pages_kanban_view
#: model_terms:ir.ui.view,arch_db:website.website_pages_view_search
msgid "Published"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__date_publish
#: model:ir.model.fields,field_description:website.field_website_page_properties__date_publish
msgid "Publishing Date"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Pulse"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Punchy Image"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Purpose"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_highlight
msgid "Put the focus on what you have to say!"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Quadrant"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating
msgid "Quality"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_cards_grid
#: model_terms:ir.ui.view,arch_db:website.s_features_wall
#: model_terms:ir.ui.view,arch_db:website.s_wavy_grid
msgid "Quality and Excellence"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_pricelist_boxed
msgid "Quattro Stagioni"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_pricing_s_text_block_h2
#: model_terms:ir.ui.view,arch_db:website.new_page_template_services_s_text_block_h2
msgid "Questions?"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Quotes"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Quotes Minimal"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_ir_qweb
msgid "Qweb"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_ir_qweb_field_contact
msgid "Qweb Field Contact"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_ir_qweb_field_html
msgid "Qweb Field HTML"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Radar"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Radio"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
msgid "Radio Button List"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Radio Buttons"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Rating"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_card_options
msgid "Ratio"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Re-order"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_default_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_mosaic_template
msgid "Reaching new heights"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_image_text_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_image_text_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_images_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_reversed_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_texts_image_texts_template
msgid "Reaching new heights together"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_horizontal
msgid "Read More <i class=\"fa fa-angle-right\" role=\"img\"/>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_2_s_call_to_action
msgid "Ready to Embrace Your Fitness Journey?"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_call_to_action_about
msgid "Ready to bring your digital vision to life?"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
msgid "Ready to build the"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_call_to_action_digital
msgid "Ready to embark on a journey of digital transformation?"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_3_s_call_to_action
msgid ""
"Ready to embark on your auditory adventure? Order your EchoTunes Wireless "
"Earbuds today and let the symphony begin."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_s_features
msgid "Rebranding"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/send_mail_form.js:0
msgid "Recipient Email"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_ir_rule
msgid "Record Rule"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_controller_page__record_view_id
msgid "Record view"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Red Velvet Cake"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Redirect"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_page_properties_view_form
msgid "Redirect Old URL"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page_properties__redirect_old_url
msgid "Redirect Old Url"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page_properties__redirect_type
msgid "Redirect Type"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/website_preview/website_preview.js:0
msgid "Redirecting..."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_rewrite_search
msgid "Redirection Type"
msgstr ""

#. module: website
#: model:ir.ui.menu,name:website.menu_website_rewrite
msgid "Redirects"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "References"
msgstr "संदर्भ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "References Grid"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "References Social"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_rewrite_form
msgid "Refresh route's list"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#: model_terms:ir.ui.view,arch_db:website.s_carousel_intro_options
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Regular"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_controller_pages_form_view
#: model_terms:ir.ui.view,arch_db:website.website_pages_form_view
msgid "Related Menu Items"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_controller_page__menu_ids
#: model:ir.model.fields,field_description:website.field_website_page__menu_ids
msgid "Related Menus"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__controller_page_id
msgid "Related Model Page"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__page_id
msgid "Related Page"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
msgid "Related keywords"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
#: model_terms:ir.ui.view,arch_db:website.s_features_wave
msgid "Reliability"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Remember information about the preferred look or behavior of the website, "
"such as your preferred language or region."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__remove
msgid "Remove"
msgstr "हटाओ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_card_options
msgid "Remove Cover"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_chart/options.js:0
msgid "Remove Row"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_chart/options.js:0
msgid "Remove Serie"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Remove Slide"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Remove Tab"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Remove all"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_view_kanban
msgid "Remove theme"
msgstr ""

#. module: website
#. odoo-python
#: code:addons/website/models/res_users.py:0
msgid "Remove website on related partner before they become internal user."
msgstr ""

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__replace
msgid "Replace"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/000.js:0
msgid "Replace File"
msgstr ""

#. module: website
#: model:ir.ui.menu,name:website.menu_reporting
msgid "Reporting"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Required"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/resource_editor/resource_editor.xml:0
msgid "Reset"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "Reset templates"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Reset to Headings Font Family"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Reset to Paragraph Font Family"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "Reset to initial version (hard reset)."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/resource_editor/resource_editor.js:0
msgid "Reseting views is not supported yet"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Resources"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
msgid "Respecting your privacy is our priority."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "Restore previous version (soft reset)."
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_res_partner__website_id
#: model:ir.model.fields,help:website.field_res_users__website_id
#: model:ir.model.fields,help:website.field_website_controller_page__website_id
#: model:ir.model.fields,help:website.field_website_multi_mixin__website_id
#: model:ir.model.fields,help:website.field_website_page__website_id
#: model:ir.model.fields,help:website.field_website_published_multi_mixin__website_id
#: model:ir.model.fields,help:website.field_website_snippet_filter__website_id
msgid "Restrict to a specific website."
msgstr ""

#. module: website
#: model:res.groups,name:website.group_website_restricted_editor
msgid "Restricted Editor"
msgstr ""

#. module: website
#: model:ir.model.fields.selection,name:website.selection__ir_ui_view__visibility__restricted_group
msgid "Restricted Group"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers_grid
#: model_terms:ir.ui.view,arch_db:website.s_numbers_list
msgid "Revenue Growth"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_carousel_intro
msgid "Reversed"
msgstr ""

#. module: website
#: model:ir.actions.act_window,name:website.action_website_rewrite_list
msgid "Rewrite"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_accordion_options
#: model_terms:ir.ui.view,arch_db:website.s_blockquote_options
#: model_terms:ir.ui.view,arch_db:website.s_card_options
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Right"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menu_image_menu
msgid "Right Menu"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Ripple"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "Road"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
msgid "RoadMap"
msgstr ""

#. module: website
#. odoo-python
#: code:addons/website/models/res_config_settings.py:0
#: model:ir.model.fields,field_description:website.field_website__robots_txt
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Robots.txt"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_website_robots
msgid "Robots.txt Editor"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"Robots.txt: This file tells to search engine crawlers which pages or files "
"they can or can't request from your site."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Rotate"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_accordion_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_border_widgets
msgid "Round Corners"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_wave
msgid ""
"Round-the-clock assistance is available, ensuring issues are resolved "
"quickly, keeping your operations running smoothly."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_carousel
msgid "Rounded"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Rounded Miniatures"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Rounded box menu"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_rewrite__route_id
#: model:ir.model.fields,field_description:website.field_website_route__path
msgid "Route"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/resource_editor/resource_editor.js:0
msgid "SCSS file: %s"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "SEO"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_kanban_view
msgid "SEO Optimized"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_website_seo_metadata
msgid "SEO metadata"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__is_seo_optimized
#: model:ir.model.fields,field_description:website.field_website_controller_page__is_seo_optimized
#: model:ir.model.fields,field_description:website.field_website_page__is_seo_optimized
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__is_seo_optimized
msgid "SEO optimized"
msgstr ""

#. module: website
#. odoo-python
#: code:addons/website/models/website_snippet_filter.py:0
msgid "Sample %s"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Sample Icons"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "Satellite"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Saturation"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/edit_menu.xml:0
#: code:addons/website/static/src/components/dialog/seo.js:0
#: code:addons/website/static/src/components/edit_head_body_dialog/edit_head_body_dialog.xml:0
#: code:addons/website/static/src/components/resource_editor/resource_editor.xml:0
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#: model_terms:ir.ui.view,arch_db:website.view_edit_robots
#: model_terms:ir.ui.view,arch_db:website.view_edit_third_party_domains
msgid "Save"
msgstr "सहेज"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
msgid "Save & copy"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
msgid "Save and Reload"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Save changes"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Save the block to use it elsewhere"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_pricelist_boxed
msgid ""
"Savor our fresh, local cuisine with a modern twist.<br/>Deliciously crafted "
"for every taste!"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
#: model_terms:ir.ui.view,arch_db:website.s_features_wave
msgid "Scalability"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "Score"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Screens"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_module_module__image_ids
msgid "Screenshots"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Scroll"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Scroll Down Button"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Scroll Effect"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.option_footer_scrolltop
msgid "Scroll To Top"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Scroll Top Button"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Scroll Zone"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
msgid "Scroll down to next section"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/backend/view_hierarchy/hierarchy_navbar.xml:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.header_search_box
#: model_terms:ir.ui.view,arch_db:website.snippets
#: model_terms:ir.ui.view,arch_db:website.website_search_box
msgid "Search"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_2_s_text_block_h2
#: model_terms:ir.ui.view,arch_db:website.s_searchbar
msgid "Search Input"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.menu_search
msgid "Search Menus"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_rewrite_search
msgid "Search Redirect"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_hybrid
msgid "Search Results"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Search Visitor"
msgstr ""

#. module: website
#: model_terms:digest.tip,tip_description:website.digest_tip_website_1
msgid ""
"Search in the media dialogue when you need photos to illustrate your "
"website. Odoo's integration with Unsplash, featuring millions of royalty "
"free and high quality photos, makes it possible for you to get the perfect "
"picture, in just a few clicks."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_searchbar
msgid "Search on our website"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Search within"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_search_box
msgid "Search..."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.xml:0
msgid "Searching your images."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.js:0
msgid "Searching your images...."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid "Second Feature"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_multi_menus
msgid "Second Menu"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Second default radio"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.s_badge_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Secondary"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
msgid "Secondary Buttons"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Secondary Style"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_countdown/000.js:0
#: model_terms:ir.ui.view,arch_db:website.s_countdown
msgid "Seconds"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_options_template
msgid "Section Title"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_s_showcase
msgid "Secure and Comfortable Fit"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_template
msgid "See All"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_template
msgid "See all <i class=\"fa fa-long-arrow-right ms-2\"/>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_cta_badge
#: model_terms:ir.ui.view,arch_db:website.s_discovery
#: model_terms:ir.ui.view,arch_db:website.s_empowerment
msgid "See more <i class=\"fa fa-long-arrow-right\" role=\"img\"/>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_references_grid
msgid "See our case studies"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_references
msgid "See our case studies <i class=\"fa fa-long-arrow-right ms-2\"/>"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
msgid "Select a Google Font..."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
msgid "Select an image for social share"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Select and delete blocks to remove features."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Select and delete blocks to remove some steps."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Selection"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Selection type"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_cover
msgid "Sell Online. <br/>Easily."
msgstr ""

#. module: website
#: model:website.configurator.feature,description:website.feature_module_shop
msgid "Sell more with an eCommerce"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid "Send Email"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_contact_info
msgid "Send an email"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
msgid "Send us a message"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__seo_name
#: model:ir.model.fields,field_description:website.field_website_controller_page__seo_name
#: model:ir.model.fields,field_description:website.field_website_page__seo_name
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__seo_name
msgid "Seo name"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
msgid "Separate email addresses with a comma."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Separated link"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_s_text_cover
#: model_terms:ir.ui.view,arch_db:website.s_pricelist_boxed_options
#: model_terms:ir.ui.view,arch_db:website.s_pricelist_cafe_options
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog_options
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Separator"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__sequence
#: model:ir.model.fields,field_description:website.field_theme_website_menu__sequence
#: model:ir.model.fields,field_description:website.field_website__sequence
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__sequence
#: model:ir.model.fields,field_description:website.field_website_controller_page__priority
#: model:ir.model.fields,field_description:website.field_website_menu__sequence
#: model:ir.model.fields,field_description:website.field_website_page__priority
#: model:ir.model.fields,field_description:website.field_website_rewrite__sequence
msgid "Sequence"
msgstr "अनुक्रम"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
msgid "Serve font from Google servers"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_ir_actions_server
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__action_server_id
msgid "Server Action"
msgstr ""

#. module: website
#: model:website.configurator.feature,name:website.feature_page_our_services
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.new_page_template_groups
#: model_terms:ir.ui.view,arch_db:website.our_services
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
msgid "Services"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_text_overlap
msgid "Services we offer"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Session &amp; Security<br/>(essential)"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.js:0
msgid "Setting up your %s."
msgstr ""

#. module: website
#: model:ir.actions.act_window,name:website.action_website_configuration
#: model:ir.ui.menu,name:website.menu_website_website_settings
#: model_terms:ir.ui.view,arch_db:website.website_controller_pages_form_view
#: model_terms:ir.ui.view,arch_db:website.website_pages_kanban_view
#: model_terms:ir.ui.view,arch_db:website.website_pages_tree_view
msgid "Settings"
msgstr "कॉन्फ़िगरेशन"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Settings of Website"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Settings on this page will apply to this website"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "Shadow"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Shadow large"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Shadow medium"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Shadow small"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Shake"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Shape image"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#: model_terms:ir.ui.view,arch_db:website.s_share
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Share"
msgstr ""

#. module: website
#: model:website.configurator.feature,description:website.feature_module_elearning
msgid "Share knowledge publicly or for a fee"
msgstr ""

#. module: website
#: model:website.configurator.feature,description:website.feature_module_success_stories
msgid "Share your best case studies"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__shared_user_account
msgid "Shared Customer Accounts"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Shared Link Auth"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Shoes"
msgstr ""

#. module: website
#: model:website.configurator.feature,name:website.feature_module_shop
msgid "Shop"
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_website__auto_redirect_lang
msgid "Should users be redirected to their browser's language"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/backend/view_hierarchy/view_hierarchy.xml:0
msgid "Show Arch Diff"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_controller_page__customize_show
#: model:ir.model.fields,field_description:website.field_website_page__customize_show
msgid "Show As Optional Inherit"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Show Header"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Show Message"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Show Message and hide countdown"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Show Message and keep countdown"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/backend/view_hierarchy/hierarchy_navbar.xml:0
msgid "Show inactive views"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Show on"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Show reCaptcha Policy"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_view_form_extend
msgid "Show site map"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_popup/000.js:0
msgid "Show the cookies bar"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Show/Hide on Desktop"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Show/Hide on Mobile"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Show/hide button"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Show/hide language selector"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Show/hide logo"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Show/hide search bar"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Show/hide sign in button"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Show/hide social links"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Show/hide text element"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Showcase"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Side grid"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Sidebar"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Sign in"
msgstr ""

#. module: website
#: model:ir.model.fields.selection,name:website.selection__ir_ui_view__visibility__connected
msgid "Signed In"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_list
msgid "Simple"
msgstr ""

#. module: website
#: model:ir.ui.menu,name:website.menu_site
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Site"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Size"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
msgid "Skip and start from scratch"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_list
msgid "Sleek"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_pricelist_cafe
msgid ""
"Sleek, minimalist space offering meticulously brewed coffee and espresso "
"drinks using freshly roasted beans."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_carousel
msgid "Slide"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Slide Down"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Slide Hover"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Slide Left"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Slide Right"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid "Slide Title"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Slide Up"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Slideout Effect"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.dynamic_snippet_carousel_options_template
msgid "Slider Speed"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Slideshow"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.s_carousel_intro_options
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Small"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page_options
msgid "Small Header"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid ""
"Small text. Lorem <b>ipsum dolor sit amet</b>, consectetur adipiscing elit. "
"<i>Integer posuere erat a ante</i>."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Smartphones"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_s_text_cover
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Social"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team_detail
#: model_terms:ir.ui.view,arch_db:website.s_references_social
#: model_terms:ir.ui.view,arch_db:website.s_social_media
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Social Media"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_social_media_options
msgid "Social Networks"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
msgid "Social Preview"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid ""
"Soft, sweet dough rolled with cinnamon and sugar, topped with a rich cream "
"cheese frosting."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_border_line_widgets
msgid "Solid"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/page_properties.xml:0
msgid "Some of these pages are used as new page templates."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid ""
"Some quick example text to build on the card title and make up the bulk of "
"the card's content."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Something else here"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
msgid "Something went wrong."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team_shapes
msgid "Sophia Langston"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team_detail
msgid "Sophia evaluates financial data and provides strategic insights."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Sound"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Sourdough Bread"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Spacing"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.grid_layout_options
msgid "Spacing (Y, X)"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_pricelist_boxed
msgid "Spaghetti Carbonara"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid ""
"Speakers from all over the world will join our experts to give inspiring "
"talks on various topics. Stay on top of the latest business management "
"trends &amp; technologies"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website__specific_user_account
msgid "Specific User Account"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_hybrid
msgid "Specify a search term."
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_ir_model__website_form_default_field_id
msgid ""
"Specify the field which will contain meta and custom form fields datas."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_carousel
msgid "Speed"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
#: model_terms:ir.ui.view,arch_db:website.s_features_wave
msgid ""
"Speed and efficiency ensure tasks are completed quickly and resources are "
"used optimally, enhancing productivity and satisfaction."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_pricelist_boxed
msgid ""
"Spicy pepperoni paired with fiery chili flakes, mozzarella, and tomato sauce"
" for a flavorful kick."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "Spread"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Spring collection has arrived!"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_card_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Square"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Squared Miniatures"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Stacked"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Standard"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Start After"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_call_to_action
msgid "Start Button"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_adventure
#: model_terms:ir.ui.view,arch_db:website.s_closer_look
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Start Now"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_banner
msgid "Start Now <span class=\"fa fa-angle-right ms-2\"/>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_card_offset
#: model_terms:ir.ui.view,arch_db:website.s_image_hexagonal
#: model_terms:ir.ui.view,arch_db:website.s_image_text
#: model_terms:ir.ui.view,arch_db:website.s_image_text_box
#: model_terms:ir.ui.view,arch_db:website.s_image_text_overlap
#: model_terms:ir.ui.view,arch_db:website.s_mockup_image
#: model_terms:ir.ui.view,arch_db:website.s_tabs
#: model_terms:ir.ui.view,arch_db:website.s_text_image
msgid "Start with the customer – find out what they want and give it to them."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid "Start your journey"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Status Colors"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid ""
"Stay informed of our latest news and discover what will happen in the next "
"weeks."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_picture
msgid "Step Up Your Game"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_s_three_columns
msgid ""
"Step into our past and witness the transformation of a simple idea into an "
"innovative force. Our journey, born in a garage, reflects the power of "
"passion and hard work."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
#: model_terms:ir.ui.view,arch_db:website.step_wizard
msgid "Steps"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
msgid "Sticky"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Storage"
msgstr ""

#. module: website
#: model:website.configurator.feature,name:website.feature_module_stores_locator
msgid "Stores Locator"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid "Storytelling is powerful.<br/> It draws readers in and engages them."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps_options
msgid "Straight arrow"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_services_s_image_text
msgid "Strategic Marketing Solutions"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_services_0_s_three_columns
msgid "Strength Training:"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Stretch menu"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.vertical_alignment_option
msgid "Stretch to Equal Height"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
msgid "Striped"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Striped Center Top"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Striped Top"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Striped section"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Stroke Width"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Structure"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_accordion_options
#: model_terms:ir.ui.view,arch_db:website.s_badge_options
#: model_terms:ir.ui.view,arch_db:website.s_blockquote_options
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_carousel
msgid "Style"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Sub Menus"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Subheading"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/send_mail_form.js:0
msgid "Subject"
msgstr "विषय"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.s_website_form
msgid "Submit"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_list
msgid "Subtle"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website_form.xml:0
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.s_badge_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Success"
msgstr ""

#. module: website
#: model:website.configurator.feature,name:website.feature_module_success_stories
msgid "Success Stories"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_references_social
msgid "Successful collaboration since 2019"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Suggestions"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_horizontal
msgid "Support and Resources"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Surrounded"
msgstr ""

#. module: website
#. odoo-python
#: code:addons/website/controllers/form.py:0
msgid "Suspicious activity detected by Google reCaptcha."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Switch Theme"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
msgid "System Fonts"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "T-shirts"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.xml:0
msgid "TIP"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Table of Content"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Tablets"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Tabs"
msgstr "टैब्स"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Tada"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_wave
msgid ""
"Tailor the platform to your needs, offering flexibility and control over "
"your user experience."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_cards_grid
#: model_terms:ir.ui.view,arch_db:website.s_features_wall
#: model_terms:ir.ui.view,arch_db:website.s_wavy_grid
msgid "Tailored Solutions"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_closer_look
msgid "Take a closer look"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__target
msgid "Target"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page_properties__target_model_id
#: model:ir.model.fields,field_description:website.field_website_page_properties_base__target_model_id
msgid "Target Model"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_key_benefits
msgid "Tax free"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_groups
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Team"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Team Basic"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Team Detail"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Team Shapes"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_pricelist_cafe
msgid "Teas"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Technical name:"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Telephone"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Televisions"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_options_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Template"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/resource_editor/resource_editor.js:0
msgid "Template ID: %s"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "Template fallback"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
msgid "Terms of Services"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
msgid "Terrain"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_edit_robots
msgid "Test your robots.txt with Google Search Console"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#: model_terms:ir.ui.view,arch_db:website.grid_layout_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Text"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Text - Image"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Text Alignment"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Text Color"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Text Cover"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#: model_terms:ir.ui.view,arch_db:website.s_text_highlight
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Text Highlight"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Text Image Text"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Text Inline"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "Text Position"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "Text muted. Lorem <b>ipsum dolor sit amet</b>, consectetur."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
msgid "Thank You!"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website_form_editor.xml:0
msgid "Thank you for your feedback!"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mockup_image
msgid "The Innovation Behind Our Product"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_instagram_page/options.js:0
msgid "The Instagram page name is not valid"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
msgid "The chosen name already exists"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_s_showcase
msgid ""
"The compact charging case offers convenient on-the-go charging with a "
"battery life that lasts up to 17h, you can enjoy your favorite tunes without"
" interruption."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "The company this website belongs to"
msgstr ""

#. module: website
#. odoo-python
#: code:addons/website/models/res_company.py:0
msgid ""
"The company “%(company_name)s” cannot be archived because it has a linked website “%(website_name)s”.\n"
"Change that website's company first."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.js:0
msgid ""
"The description will be generated by search engines based on page content "
"unless you specify one."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.js:0
msgid ""
"The description will be generated by social media based on page content "
"unless you specify one."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_striped
msgid "The evolution of our company"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
msgid "The field “%(field)s” is mandatory for the action “%(action)s”."
msgstr ""

#. module: website
#. odoo-python
#: code:addons/website/wizard/blocked_third_party_domains.py:0
msgid "The following domain is not valid:"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website_form.xml:0
msgid "The form has been sent successfully."
msgstr ""

#. module: website
#. odoo-python
#: code:addons/website/controllers/form.py:0
msgid "The form's specified model does not exist"
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_res_partner__website_url
#: model:ir.model.fields,help:website.field_res_users__website_url
#: model:ir.model.fields,help:website.field_website_controller_page__website_url
#: model:ir.model.fields,help:website.field_website_page__website_url
#: model:ir.model.fields,help:website.field_website_published_mixin__website_url
#: model:ir.model.fields,help:website.field_website_published_multi_mixin__website_url
#: model:ir.model.fields,help:website.field_website_snippet_filter__website_url
msgid "The full URL to access the document through the website."
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_ir_actions_server__website_url
#: model:ir.model.fields,help:website.field_ir_cron__website_url
msgid "The full URL to access the server action through the website."
msgstr ""

#. module: website
#. odoo-python
#: code:addons/website/models/website.py:0
msgid "The homepage URL should be relative and start with '/'."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/new_content.js:0
msgid "The installation of an App is already in progress."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_wave
msgid ""
"The intuitive design ensures smooth navigation, enhancing user experience "
"without needing technical expertise."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
msgid "The language of the keyword and related keywords."
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_website_snippet_filter__limit
msgid "The limit is the maximum number of records retrieved"
msgstr ""

#. module: website
#. odoo-python
#: code:addons/website/models/website_snippet_filter.py:0
msgid "The limit must be between 1 and 16."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "The maximum number of files that can be uploaded."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "The maximum size (in MB) an uploaded file can have."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "The message will be visible once the countdown ends"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_controller_page__name
msgid ""
"The name is used to generate the URL and is shown in the browser title bar"
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_website_controller_page__name_slugified
msgid "The name of the page usable in a URL"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
msgid "The quick brown fox jumps over the lazy dog."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
msgid "The selected font cannot be accessed."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "The selected templates will be reset to their factory settings."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid "The team"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
msgid "The title will take a default value unless you specify one."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"The website will not work properly if you reject or discard those cookies."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "The website will still work if you reject or discard those cookies."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#: model:ir.model.fields,field_description:website.field_website__theme_id
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.theme_view_search
msgid "Theme"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_theme_ir_asset
msgid "Theme Asset"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_theme_ir_attachment
msgid "Theme Attachments"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "Theme Colors"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_asset__theme_template_id
#: model:ir.model.fields,field_description:website.field_ir_attachment__theme_template_id
#: model:ir.model.fields,field_description:website.field_ir_ui_view__theme_template_id
#: model:ir.model.fields,field_description:website.field_product_document__theme_template_id
#: model:ir.model.fields,field_description:website.field_website_controller_page__theme_template_id
#: model:ir.model.fields,field_description:website.field_website_menu__theme_template_id
#: model:ir.model.fields,field_description:website.field_website_page__theme_template_id
msgid "Theme Template"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_theme_ir_ui_view
msgid "Theme UI View"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_theme_utils
msgid "Theme Utils"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/add_page_dialog.xml:0
msgid "Then enable the \"Is a Template\" option."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_public_pages
msgid "There are currently no pages for this website."
msgstr ""

#. module: website
#. odoo-python
#: code:addons/website/models/website_visitor.py:0
msgid "There are no contact and/or no email linked to this visitor."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
msgid "There is no field available for this option."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "They trust us since years"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Thick"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Thickness"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Thin"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid "Third Feature"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_multi_menus
msgid "Third Menu"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/page_properties.xml:0
msgid "This URL is contained in the “%(field)s” of the following “%(model)s”"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_services_0_s_three_columns
msgid ""
"This coaching program offers specialized strength-focused workouts, "
"nutrition guidance, and expert coaching. Elevate your fitness level and "
"achieve feats you never thought possible."
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__favicon
#: model:ir.model.fields,help:website.field_website__favicon
msgid "This field holds the image used to display a favicon on the website."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
msgid ""
"This field is mandatory for this action. You cannot remove it. Try hiding it"
" with the 'Visibility' option instead and add it a default value."
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_website_controller_page__arch_base
#: model:ir.model.fields,help:website.field_website_page__arch_base
msgid "This field is the same as `arch` field without translations"
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__website_default_lang_code
msgid "This field is used to set/get locales for user"
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_website_controller_page__arch
#: model:ir.model.fields,help:website.field_website_page__arch
msgid ""
"This field should be used when accessing view arch. It will use translation.\n"
"                               Note that it will read `arch_db` or `arch_fs` if in dev-xml mode."
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_website_controller_page__arch_db
#: model:ir.model.fields,help:website.field_website_page__arch_db
msgid "This field stores the view arch."
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_website_controller_page__arch_prev
#: model:ir.model.fields,help:website.field_website_page__arch_prev
msgid ""
"This field will save the current `arch_db` before writing on it.\n"
"                                                                         Useful to (soft) reset a broken view."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
msgid ""
"This font already exists, you can only add it as a local font to replace the"
" server version."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/xml/website.editor.xml:0
msgid "This font is hosted and served to your visitors by Google servers"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "This is a \""
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid ""
"This is a paragraph. Ambitioni dedisse scripsisse iudicaretur. Nihilne te "
"nocturnum praesidium Palati, nihil urbis vigiliae. Unam incolunt Belgae, "
"aliam Aquitani, tertiam. Integer legentibus erat a ante historiarum dapibus."
" Phasellus laoreet lorem vel dolor tempus vehicula."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_framed_intro
msgid ""
"This is a simple hero unit, a simple jumbotron-style component for calling "
"extra attention to featured content or information."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_key_images
msgid "This is a small title related to the current image"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid ""
"This is a wider card with supporting text below as a natural lead-in to "
"additional content. This content is a little bit longer."
msgstr ""

#. module: website
#: model:ir.ui.view,website_meta_description:website.contactus
msgid "This is the contact us page of the website"
msgstr ""

#. module: website
#: model:ir.ui.view,website_meta_description:website.homepage
msgid "This is the homepage of the website"
msgstr ""

#. module: website
#. odoo-python
#: code:addons/website/controllers/form.py:0
msgid "This message has been posted on your website!"
msgstr ""

#. module: website
#. odoo-python
#: code:addons/website/models/website_visitor.py:0
msgid "This operator is not supported"
msgstr ""

#. module: website
#: model:ir.ui.menu,name:website.menu_current_page
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "This page"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.page_404
msgid ""
"This page does not exist, but you can create it as you are editor of this "
"site."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/page_properties.xml:0
msgid "This page is used as a new page template."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/translator/translator.js:0
msgid "This translation is not editable."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
msgid ""
"This uploaded font already exists.\n"
"To replace an existing font, remove it first."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.js:0
msgid ""
"This value will be escaped to be compliant with all major browsers and used "
"in url. Keep it empty to use the default name of the record."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/backend/view_hierarchy/view_hierarchy.xml:0
msgid "This view arch has been modified"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/website_switcher.js:0
#: code:addons/website/static/src/systray_items/website_switcher.xml:0
msgid "This website does not have a domain configured."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/website_switcher.js:0
msgid ""
"This website does not have a domain configured. To avoid unexpected behaviours during website edition, we recommend closing (or refreshing) other browser tabs.\n"
"To remove this message please set a domain in your website settings"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_edit_third_party_domains
msgid ""
"Those services will be blocked on your website for users until they accept "
"optional cookies."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_references_social
msgid "Thriving partnership since 2021"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Thumbnails"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_social_media/options.js:0
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
#: model_terms:ir.ui.view,arch_db:website.s_social_media
msgid "TikTok"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website__social_tiktok
msgid "TikTok Account"
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__time_since_last_action
msgid "Time since last page view. E.g.: 2 minutes ago"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_countdown/000.xml:0
msgid "Time's up! You can now visit"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_cards_soft
msgid "Timeless Quality"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page_options
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Timeline"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Timeline List"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__timezone
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Timezone"
msgstr ""

#. module: website
#: model:digest.tip,name:website.digest_tip_website_4
#: model_terms:digest.tip,tip_description:website.digest_tip_website_4
msgid "Tip: Add shapes to energize your Website"
msgstr ""

#. module: website
#: model:digest.tip,name:website.digest_tip_website_0
#: model_terms:digest.tip,tip_description:website.digest_tip_website_0
msgid "Tip: Engage with visitors to convert them into leads"
msgstr ""

#. module: website
#: model:digest.tip,name:website.digest_tip_website_2
#: model_terms:digest.tip,tip_description:website.digest_tip_website_2
msgid "Tip: Search Engine Optimization (SEO)"
msgstr ""

#. module: website
#: model:digest.tip,name:website.digest_tip_website_3
#: model_terms:digest.tip,tip_description:website.digest_tip_website_3
msgid "Tip: Use illustrations to spice up your website"
msgstr ""

#. module: website
#: model:digest.tip,name:website.digest_tip_website_1
#: model_terms:digest.tip,tip_description:website.digest_tip_website_1
msgid "Tip: Use royalty-free photos"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_map_s_text_block_h1
#: model_terms:ir.ui.view,arch_db:website.s_text_block_h1
#: model_terms:ir.ui.view,arch_db:website.s_text_block_h2
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Title"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Title - Form"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Title - Image"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Title Position"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid ""
"To add a fourth column, reduce the size of these three columns using the "
"right icon of each block. Then, duplicate one of the columns to create a new"
" one as a copy."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/add_page_dialog.xml:0
msgid ""
"To add your page to this category, open the page properties: \"Site -> "
"Properties\"."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs
msgid "To be successful your content needs to be useful to your readers."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
msgid "To comply with some local regulations"
msgstr ""

#. module: website
#: model_terms:digest.tip,tip_description:website.digest_tip_website_2
msgid ""
"To get more visitors, you should target keywords that are often searched in "
"Google. With the built-in SEO tool, once you define a few keywords, Odoo "
"will recommend you the best keywords to target. Then adapt your title and "
"description accordingly to boost your traffic."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.cookies_warning.xml:0
msgid "To see the interactive content, you need to accept optional cookies."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"To send invitations in B2B mode, open a contact or select several ones in "
"list view and click on 'Portal Access Management' option in the dropdown "
"menu *Action*."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Toggle"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.template_header_mobile
msgid "Toggle navigation"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
#: model_terms:ir.ui.view,arch_db:website.s_company_team_shapes
msgid "Tony Fred"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_team_0_s_three_columns
#: model_terms:ir.ui.view,arch_db:website.new_page_template_team_s_image_text
#: model_terms:ir.ui.view,arch_db:website.new_page_template_team_s_media_list
#: model_terms:ir.ui.view,arch_db:website.s_company_team_basic
msgid "Tony Fred, CEO"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Tooltip"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_card_options
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_options_template
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.template_header_sales_four
#: model_terms:ir.ui.view,arch_db:website.template_header_sales_one
#: model_terms:ir.ui.view,arch_db:website.template_header_sales_three
#: model_terms:ir.ui.view,arch_db:website.template_header_sales_two
#: model_terms:ir.ui.view,arch_db:website.template_header_search
msgid "Top"
msgstr ""

#. module: website
#. odoo-python
#: code:addons/website/models/website.py:0
msgid "Top Menu for Website %s"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_accordion_image
msgid "Top questions answered"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "Top to Bottom"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_horizontal
msgid "Topic Walkthrough"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_horizontal_options
msgid "Topics"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Topics List"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Tops"
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__page_count
msgid "Total number of tracked page visited"
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__visitor_page_count
msgid "Total number of visits on tracked pages"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__track
#: model:ir.model.fields,field_description:website.field_website_controller_page__track
#: model:ir.model.fields,field_description:website.field_website_page__track
msgid "Track"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Track visits using Google Analytics"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_view_search
msgid "Tracked"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_pricelist_boxed
msgid ""
"Traditional Roman dish with creamy egg, crispy pancetta, and Pecorino "
"Romano, topped with black pepper."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers_grid
msgid "Transactions"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_services_s_text_image
msgid "Transform Your Brand"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_title
msgid ""
"Transform your environment with our new design collection, where elegance "
"meets functionality. Elevate your space with pieces that blend style and "
"comfort seamlessly."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_carousel
msgid "Transition"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/edit_website.xml:0
msgid "Translate -"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/translator/translator.js:0
msgid "Translate Attribute"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/translator/translator.js:0
msgid "Translate Selection Option"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/translator/translator.js:0
msgid "Translate header in the text. Menu is generated automatically."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/translator/translator.xml:0
msgid "Translated content"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Trigger"
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_website__configurator_done
msgid "True if configurator has been completed or ignored"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_references_grid
msgid "Trusted references"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Turn every feature into a benefit for your reader."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_striped_center_top
msgid "Turning Vision into Reality"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__type
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_map_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.website_page_properties_view_form
msgid "Type"
msgstr "प्रकार"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/web_editor.xml:0
msgid "Type '"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid ""
"Type '<i class=\"confirm_word\">yes</i>' in the box below if you want to "
"confirm."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
msgid "Type in text here..."
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_website_rewrite__redirect_type
msgid ""
"Type of redirect/Rewrite:\n"
"\n"
"        301 Moved permanently: The browser will keep in cache the new url.\n"
"        302 Moved temporarily: The browser will not keep in cache the new url and ask again the next time the new url.\n"
"        404 Not Found: If you want remove a specific page/controller (e.g. Ecommerce is installed, but you don't want /shop on a specific website)\n"
"        308 Redirect / Rewrite: If you want rename a controller with a new url. (Eg: /shop -> /garden - Both url will be accessible but /shop will automatically be redirected to /garden)\n"
"    "
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_controller_page__name_slugified
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.website_controller_pages_form_view
msgid "URL"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_rewrite__url_from
#: model_terms:ir.ui.view,arch_db:website.view_website_rewrite_form
msgid "URL from"
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__cdn_filters
#: model:ir.model.fields,help:website.field_website__cdn_filters
msgid "URL matching those filters will be rewritten using the CDN Base URL"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_rewrite__url_to
msgid "URL to"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "UTM Campaign"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "UTM Medium"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "UTM Source"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_card_options
msgid "Ultrawide - 21/9"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
msgid "Unalterable unique identifier"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Underline"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Underline On Hover"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Understand how visitors engage with our website, via Google Analytics.\n"
"                                                Learn more about"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_quadrant
msgid "Understanding the Innovation"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/resource_editor/utils.js:0
msgid "Unexpected %(char)s"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_banner
msgid "Unleash your <strong>potential.</strong>"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/fields/publish_button.xml:0
#: code:addons/website/static/src/components/views/page_list.js:0
msgid "Unpublish"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/fields/publish_button.xml:0
#: code:addons/website/static/src/components/fields/redirect_field.js:0
#: code:addons/website/static/src/systray_items/publish.js:0
#: model_terms:ir.ui.view,arch_db:website.publish_management
msgid "Unpublished"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Unregistered"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Unveil"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_wall
msgid "Unveil Our Exclusive Collections"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_unveil
msgid "Unveiling our newest products"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_images_mosaic
msgid "Unveiling our newest solutions"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_view_kanban
msgid "Update theme"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_horizontal
msgid "Updates and Improvements"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
msgid "Upload"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/000.js:0
msgid "Uploaded file is too large."
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__url
#: model:ir.model.fields,field_description:website.field_theme_website_menu__url
#: model:ir.model.fields,field_description:website.field_theme_website_page__url
#: model:ir.model.fields,field_description:website.field_website_menu__url
#: model:ir.model.fields,field_description:website.field_website_page_properties_base__url
#: model:ir.model.fields,field_description:website.field_website_track__url
#: model_terms:ir.ui.view,arch_db:website.menu_search
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
msgid "Url"
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__country_flag
msgid "Url of static flag image"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/edit_menu.xml:0
msgid "Url or Email"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
msgid "Urls & Pages"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
msgid "Use Google Map on your website (Contact Us page, snippets, etc)."
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_menu__use_main_menu_as_parent
msgid "Use Main Menu As Parent"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"Use Plausible.io, Simple and privacy-friendly Google Analytics alternative"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Use a CDN to optimize the availability of your website's content"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__has_default_share_image
msgid "Use a image by default for sharing"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/fields/fields.js:0
msgid "Use an array to list the images to use in the radio selection."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_view_kanban
msgid "Use default theme"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid ""
"Use this component for creating a list of featured elements to which you "
"want to bring attention."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_references
msgid "Use this section to boost your company's credibility."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid ""
"Use this snippet to build various types of components that feature a left- "
"or right-aligned image alongside textual content. Duplicate the element to "
"create a list that fits your needs."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid ""
"Use this snippet to presents your content in a slideshow-like format. Don't "
"write about products or services here, write about solutions."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/views/theme_preview.xml:0
msgid "Use this theme"
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_ir_model__website_form_key
msgid "Used in FormBuilder Registry"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
msgid "Used in page content"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
msgid "Used in page description"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
msgid "Used in page first level heading"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
msgid "Used in page second level heading"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
msgid "Used in page title"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Used to collect information about your interactions with the website, the pages you've seen,\n"
"                                                and any specific marketing campaign that brought you to the website."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Used to make advertising more engaging to users and more valuable to publishers and advertisers,\n"
"                                                such as providing more relevant ads when you visit other websites that display ads or to improve reporting on ad campaign performance."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
msgid "Useful Links"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers_showcase
msgid "Useful options"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_res_users
msgid "User"
msgstr "उपयोगकर्ता"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers_list
msgid "User Retention"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_website_custom_blocked_third_party_domains
#: model:ir.model.fields,field_description:website.field_website__custom_blocked_third_party_domains
msgid "User list of blocked 3rd-party domains"
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_website_menu__group_ids
msgid "User needs to be at least in one of these groups to see the menu"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_wave
msgid "User-Friendly Interface"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Users"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Utilities &amp; Typography"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
msgid "Value"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns_menu
msgid "Vegetable Salad, Beef Burger and Mango Ice Cream"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
#: model_terms:ir.ui.view,arch_db:website.s_quadrant_options
#: model_terms:ir.ui.view,arch_db:website.vertical_alignment_option
msgid "Vert. Alignment"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Vertical"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
#: model_terms:ir.ui.view,arch_db:website.s_quadrant_options
#: model_terms:ir.ui.view,arch_db:website.vertical_alignment_option
msgid "Vertical Alignment"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_video
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Video"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_ir_ui_view
#: model:ir.model.fields,field_description:website.field_theme_website_page__view_id
#: model:ir.model.fields,field_description:website.field_website_page__view_id
#: model_terms:ir.ui.view,arch_db:website.website_controller_pages_form_view
msgid "View"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_controller_page__arch
#: model:ir.model.fields,field_description:website.field_website_page__arch
msgid "View Architecture"
msgstr ""

#. module: website
#: model:ir.actions.client,name:website.action_website_view_hierarchy
msgid "View Hierarchy"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__name
#: model:ir.model.fields,field_description:website.field_website_page_properties__name
msgid "View Name"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_controller_page__type
#: model:ir.model.fields,field_description:website.field_website_page__type
msgid "View Type"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_controller_page__mode
#: model:ir.model.fields,field_description:website.field_website_page__mode
msgid "View inheritance mode"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
msgid "View more themes"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/resource_editor/resource_editor.js:0
msgid "Views and Assets bundles"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__copy_ids
msgid "Views using a copy of me"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_controller_page__inherit_children_ids
#: model:ir.model.fields,field_description:website.field_website_page__inherit_children_ids
msgid "Views which inherit from this one"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__visibility
#: model:ir.model.fields,field_description:website.field_website_controller_page__visibility
#: model:ir.model.fields,field_description:website.field_website_page__visibility
#: model:ir.model.fields,field_description:website.field_website_page_properties__visibility
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Visibility"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__visibility_password
#: model:ir.model.fields,field_description:website.field_website_controller_page__visibility_password
#: model:ir.model.fields,field_description:website.field_website_page__visibility_password
#: model_terms:ir.ui.view,arch_db:website.view_view_form_extend
msgid "Visibility Password"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__visibility_password_display
#: model:ir.model.fields,field_description:website.field_website_controller_page__visibility_password_display
#: model:ir.model.fields,field_description:website.field_website_page__visibility_password_display
#: model:ir.model.fields,field_description:website.field_website_page_properties__visibility_password_display
msgid "Visibility Password Display"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__group_ids
msgid "Visible Groups"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_conditional_visibility
msgid "Visible for"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Visible for Everyone"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Visible for Logged In"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Visible for Logged Out"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_partner__website_published
#: model:ir.model.fields,field_description:website.field_res_users__website_published
#: model:ir.model.fields,field_description:website.field_website_controller_page__website_published
#: model:ir.model.fields,field_description:website.field_website_page__website_published
#: model:ir.model.fields,field_description:website.field_website_published_mixin__website_published
#: model:ir.model.fields,field_description:website.field_website_published_multi_mixin__website_published
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__website_published
msgid "Visible on current website"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Visible only if"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_track__visit_datetime
msgid "Visit Date"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_countdown/options.xml:0
msgid "Visit our Facebook page to know if you are one of the lucky winners."
msgstr ""

#. module: website
#: model:ir.model,name:website.model_website_track
#: model:ir.model.fields,field_description:website.field_website_visitor__page_ids
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
msgid "Visited Pages"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__website_track_ids
msgid "Visited Pages History"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_track__visitor_id
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
msgid "Visitor"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_graph
msgid "Visitor Page Views"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_tree
msgid "Visitor Page Views History"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_track_view_graph
msgid "Visitor Views"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_track_view_tree
msgid "Visitor Views History"
msgstr ""

#. module: website
#: model:ir.actions.act_window,name:website.website_visitors_action
#: model:ir.model.fields,field_description:website.field_res_partner__visitor_ids
#: model:ir.model.fields,field_description:website.field_res_users__visitor_ids
#: model:ir.ui.menu,name:website.website_visitor_menu
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_graph
msgid "Visitors"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
msgid "Visits"
msgstr ""

#. module: website
#: model_terms:ir.actions.act_window,help:website.website_visitor_view_action
msgid ""
"Wait for visitors to come to your website to see the pages they viewed."
msgstr ""

#. module: website
#: model_terms:ir.actions.act_window,help:website.website_visitors_action
msgid ""
"Wait for visitors to come to your website to see their history and engage "
"with them."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.s_badge_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Warning"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_controller_page__warning_info
#: model:ir.model.fields,field_description:website.field_website_page__warning_info
msgid "Warning information"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Watches"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Wavy Grid"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_4_s_cover
msgid "We Are Coming Soon"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_5_s_banner
msgid "We Are Down for Maintenance"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_horizontal
msgid ""
"We also provide regular updates and new features based on user feedback, "
"ensuring that our platform continues to evolve to meet your needs."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
msgid ""
"We are a team of passionate people whose goal is to improve everyone's life through disruptive products. We build great products to solve your business problems.\n"
"                            <br/><br/>Our products are designed for small to medium size companies willing to optimize their performance."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_descriptive
msgid ""
"We are a team of passionate people whose goal is to improve everyone's life "
"through disruptive products. We build great products to solve your business "
"problems. Our products are designed for small to medium size companies "
"willing to optimize their performance."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
msgid ""
"We are a team of passionate people whose goal is to improve everyone's "
"life.<br/>Our services are designed for small to medium size companies."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar
msgid "We are almost done!"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_horizontal
msgid ""
"We are committed to continuous improvement, regularly releasing updates and "
"new features based on user feedback and technological advancements."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_horizontal
msgid ""
"We are committed to providing exceptional support and resources to help you "
"succeed with our platform."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_references_grid
#: model_terms:ir.ui.view,arch_db:website.s_references_social
msgid "We are in good company."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_s_text_image
msgid ""
"We believe that every fitness journey is unique. Our approach begins with "
"understanding your fitness aspirations, your current lifestyle, and any "
"challenges you face."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_list
msgid ""
"We collaborate with trusted, high-quality partners to bring you reliable and"
" top-notch products and services."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel_intro
msgid ""
"We combine strategic insights and innovative solutions to drive business "
"success, ensuring sustainable growth and competitive advantage in a dynamic "
"market."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_facebook_page/options.js:0
msgid "We couldn't find the Facebook page"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_list
msgid ""
"We deliver personalized solutions, ensuring that every customer receives "
"top-tier service tailored to their needs."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_striped_center_top
msgid ""
"We deliver seamless, innovative solutions that not only meet your needs but "
"exceed expectations, driving meaningful results and lasting success."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"We do not currently support Do Not Track signals, as there is no industry "
"standard for compliance."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/page_properties.xml:0
msgid "We found these ones:"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_cards_soft
msgid "We make every moment count with solutions designed just for you."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"We may not be able to provide the best service to you if you reject those "
"cookies, but the website will work."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_2_s_three_columns
msgid ""
"We monitor your progress meticulously, adjusting your plan as needed to "
"ensure continuous improvement and results."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_accordion
msgid ""
"We offer a 30-day return policy for all products. Items must be in their "
"original condition, unused, and include the receipt or proof of purchase. "
"Refunds are processed within 5-7 business days of receiving the returned "
"item."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_cards_grid
#: model_terms:ir.ui.view,arch_db:website.s_features_wall
#: model_terms:ir.ui.view,arch_db:website.s_wavy_grid
msgid ""
"We offer cutting-edge products and services to tackle modern challenges. "
"Leveraging the latest technology, we help you achieve your goals."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid "We offer tailor-made products according to your needs and your budget."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers_charts
msgid "We proudly serves over 25,000 clients."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_list
msgid ""
"We provide 24/7 support through various channels, including live chat, "
"email, and phone, to assist with any queries."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_cards_grid
#: model_terms:ir.ui.view,arch_db:website.s_wavy_grid
msgid ""
"We provide personalized solutions to meet your unique needs. Our team works "
"with you to ensure optimal results from start to finish."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_key_benefits
msgid ""
"We provide transparent pricing that offers great value, ensuring you always "
"get the best deal without hidden costs."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_horizontal
msgid ""
"We understand that the initial setup can be daunting, especially if you are "
"new to our platform, so we have designed a step-by-step guide to walk you "
"through every stage, ensuring that you can hit the ground running.<br/>"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
msgid ""
"We use cookies to provide improved experience on this website. You can learn"
" more about our cookies and how we use them in our"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
msgid ""
"We use cookies to provide you a better user experience on this website."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
msgid "We will get back to you shortly."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_contact_info
msgid ""
"We'd love to hear from you! If you have any questions, feedback, or need "
"assistance, please feel free to reach out to us using the contact details "
"provided. Our team is here to help and will respond as soon as possible. "
"Thank you for getting in touch!"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
msgid "We'll set you up and running in"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_full_s_text_image
msgid ""
"We're driven by the aspiration to redefine industry standards, to exceed the"
" expectations of our clients, and to foster a culture of continuous growth."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_tree
msgid "Web Visitors"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/fields/redirect_field.xml:0
#: code:addons/website/static/src/components/views/page_list.xml:0
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#: model:ir.actions.act_url,name:website.action_website
#: model:ir.model,name:website.model_website
#: model:ir.model.fields,field_description:website.field_ir_asset__website_id
#: model:ir.model.fields,field_description:website.field_ir_attachment__website_id
#: model:ir.model.fields,field_description:website.field_ir_ui_view__website_id
#: model:ir.model.fields,field_description:website.field_product_document__website_id
#: model:ir.model.fields,field_description:website.field_res_company__website_id
#: model:ir.model.fields,field_description:website.field_res_partner__website_id
#: model:ir.model.fields,field_description:website.field_res_users__website_id
#: model:ir.model.fields,field_description:website.field_website_controller_page__website_id
#: model:ir.model.fields,field_description:website.field_website_menu__website_id
#: model:ir.model.fields,field_description:website.field_website_multi_mixin__website_id
#: model:ir.model.fields,field_description:website.field_website_page__website_id
#: model:ir.model.fields,field_description:website.field_website_page_properties__website_id
#: model:ir.model.fields,field_description:website.field_website_page_properties_base__website_id
#: model:ir.model.fields,field_description:website.field_website_published_multi_mixin__website_id
#: model:ir.model.fields,field_description:website.field_website_rewrite__website_id
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__website_id
#: model:ir.model.fields,field_description:website.field_website_visitor__website_id
#: model:ir.ui.menu,name:website.menu_website_configuration
#: model_terms:ir.ui.view,arch_db:website.menu_search
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.view_server_action_search_website
#: model_terms:ir.ui.view,arch_db:website.website_controller_pages_search_view
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Website"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_company_id
msgid "Website Company"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__website_config_preselection
msgid "Website Config Preselection"
msgstr ""

#. module: website
#: model:ir.actions.client,name:website.website_configurator
msgid "Website Configurator"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_website_configurator_feature
msgid "Website Configurator Feature"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_domain
#: model:ir.model.fields,field_description:website.field_website__domain
msgid "Website Domain"
msgstr ""

#. module: website
#: model:ir.model.constraint,message:website.constraint_website_domain_unique
msgid "Website Domain should be unique."
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website__favicon
msgid "Website Favicon"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_model__website_form_key
msgid "Website Form Key"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.ir_model_view
msgid "Website Forms"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Website Info"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_logo
#: model:ir.model.fields,field_description:website.field_website__logo
msgid "Website Logo"
msgstr ""

#. module: website
#: model:ir.actions.act_window,name:website.action_website_menu
#: model:ir.model,name:website.model_website_menu
msgid "Website Menu"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_menus_form_view
msgid "Website Menus Settings"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_controller_pages_form_view
msgid "Website Model Page Settings"
msgstr ""

#. module: website
#: model:ir.actions.act_window,name:website.action_website_controller_pages_list
msgid "Website Model Pages"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_name
#: model:ir.model.fields,field_description:website.field_website__name
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Website Name"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__first_page_id
#: model:ir.model.fields,field_description:website.field_website_controller_page__first_page_id
#: model:ir.model.fields,field_description:website.field_website_page__first_page_id
msgid "Website Page"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_form_view
msgid "Website Page Settings"
msgstr ""

#. module: website
#: model:ir.actions.act_window,name:website.action_website_pages_list
#: model_terms:ir.ui.view,arch_db:website.website_pages_view_search
msgid "Website Pages"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_actions_server__website_path
#: model:ir.model.fields,field_description:website.field_ir_cron__website_path
msgid "Website Path"
msgstr ""

#. module: website
#: model:ir.actions.client,name:website.website_preview
msgid "Website Preview"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_website_published_mixin
msgid "Website Published Mixin"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_website_searchable_mixin
msgid "Website Searchable Mixin"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_form
msgid "Website Settings"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_website_snippet_filter
msgid "Website Snippet Filter"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_theme_website_menu
msgid "Website Theme Menu"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_theme_website_page
msgid "Website Theme Page"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_partner__website_url
#: model:ir.model.fields,field_description:website.field_res_users__website_url
#: model:ir.model.fields,field_description:website.field_website_controller_page__website_url
#: model:ir.model.fields,field_description:website.field_website_page__website_url
#: model:ir.model.fields,field_description:website.field_website_published_mixin__website_url
#: model:ir.model.fields,field_description:website.field_website_published_multi_mixin__website_url
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__website_url
msgid "Website URL"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_actions_server__website_url
#: model:ir.model.fields,field_description:website.field_ir_cron__website_url
msgid "Website Url"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_website_visitor
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid "Website Visitor"
msgstr ""

#. module: website
#. odoo-python
#: code:addons/website/models/website_visitor.py:0
msgid "Website Visitor #%s"
msgstr ""

#. module: website
#: model:ir.actions.server,name:website.website_visitor_cron_ir_actions_server
msgid "Website Visitor : clean inactive visitors"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.menu_tree
msgid "Website menu"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__website_meta_description
#: model:ir.model.fields,field_description:website.field_website_controller_page__website_meta_description
#: model:ir.model.fields,field_description:website.field_website_page__website_meta_description
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__website_meta_description
msgid "Website meta description"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__website_meta_keywords
#: model:ir.model.fields,field_description:website.field_website_controller_page__website_meta_keywords
#: model:ir.model.fields,field_description:website.field_website_page__website_meta_keywords
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__website_meta_keywords
msgid "Website meta keywords"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__website_meta_title
#: model:ir.model.fields,field_description:website.field_website_controller_page__website_meta_title
#: model:ir.model.fields,field_description:website.field_website_page__website_meta_title
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__website_meta_title
msgid "Website meta title"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__website_meta_og_img
#: model:ir.model.fields,field_description:website.field_website_controller_page__website_meta_og_img
#: model:ir.model.fields,field_description:website.field_website_page__website_meta_og_img
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__website_meta_og_img
msgid "Website opengraph image"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_website_rewrite
msgid "Website rewrite"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_rewrite_form
msgid "Website rewrite Settings"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.action_website_rewrite_tree
msgid "Website rewrites"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers_grid
#: model_terms:ir.ui.view,arch_db:website.s_numbers_list
msgid "Website visitors"
msgstr ""

#. module: website
#: model:ir.actions.server,name:website.ir_actions_server_website_analytics
msgid "Website: Analytics"
msgstr ""

#. module: website
#: model:ir.actions.server,name:website.ir_actions_server_website_dashboard
msgid "Website: Dashboard"
msgstr ""

#. module: website
#: model:ir.actions.act_window,name:website.action_website_list
#: model:ir.ui.menu,name:website.menu_website_websites_list
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_personal_s_numbers
#: model_terms:ir.ui.view,arch_db:website.view_website_tree
msgid "Websites"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_base_language_install__website_ids
msgid "Websites to translate"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_services_0_s_three_columns
msgid "Weight Loss Transformation"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_services_s_text_block_2nd
msgid ""
"Welcome to our comprehensive range of Tailored Fitness Coaching Services, "
"with personalized workouts, customized nutrition plans, and unwavering "
"support, we're committed to helping you achieve lasting results that align "
"with your aspirations."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/website_preview/website_preview.xml:0
msgid "Welcome to your"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_services_0_s_three_columns
msgid "Wellness Coaching"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_accordion
msgid ""
"We’re committed to providing prompt and effective solutions to ensure your "
"satisfaction."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_personal_s_text_block_h2
msgid "What Makes Me Proud"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_wavy_grid
msgid "What we offer to our customers"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_key_images
msgid "What we propose to our customers"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid "What you see is what you get"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_cta_card
msgid "What you will get"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline_list
msgid "What's new"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_share
msgid "WhatsApp"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_image_text_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_image_text_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_default_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_image_texts_image_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_images_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_reversed_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_texts_image_texts_template
msgid "Where ideas come to life"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_picture
msgid "Where innovation meets performance"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_card_offset
msgid "Why Our Product is the Future of Innovation"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_card_options
msgid "Wide - 16/9"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_card_options
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Width"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup
msgid "Win $20"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_3_s_three_columns
msgid "Wireless Freedom"
msgstr ""

#. module: website
#: model:ir.model.fields.selection,name:website.selection__ir_ui_view__visibility__password
msgid "With Password"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_cards_grid
#: model_terms:ir.ui.view,arch_db:website.s_features_wall
#: model_terms:ir.ui.view,arch_db:website.s_wavy_grid
msgid ""
"With extensive experience and deep industry knowledge, we provide insights "
"and solutions that keep you ahead of the curve."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_services_0_s_three_columns
msgid ""
"With personalized fitness plans, tailored nutrition guidance, and consistent"
" support, you'll shed unwanted pounds while building healthy habits that "
"last."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Women"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
msgid ""
"Would you like to save before being redirected? Unsaved changes will be "
"discarded."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs
msgid "Write one or two paragraphs describing your product or services."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_card_offset
#: model_terms:ir.ui.view,arch_db:website.s_features_wall
#: model_terms:ir.ui.view,arch_db:website.s_image_hexagonal
#: model_terms:ir.ui.view,arch_db:website.s_image_text
#: model_terms:ir.ui.view,arch_db:website.s_image_text_box
#: model_terms:ir.ui.view,arch_db:website.s_image_text_overlap
#: model_terms:ir.ui.view,arch_db:website.s_mockup_image
#: model_terms:ir.ui.view,arch_db:website.s_text_image
msgid ""
"Write one or two paragraphs describing your product or services. To be "
"successful your content needs to be useful to your readers."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_freegrid
msgid ""
"Write one or two paragraphs describing your product or services. To be "
"successful your content needs to be useful to your readers. Start with the "
"customer – find out what they want and give it to them."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_closer_look
#: model_terms:ir.ui.view,arch_db:website.s_cover
msgid ""
"Write one or two paragraphs describing your product, services or a specific "
"feature.<br/> To be successful your content needs to be useful to your "
"readers."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_discovery
msgid ""
"Write one or two paragraphs describing your product, services or a specific "
"feature.<br/> To be successful your content needs to be useful to your "
"readers.<br/><br/>"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website__social_twitter
msgid "X Account"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website_form_editor.xml:0
msgid "Yes"
msgstr "हाँ"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/website_preview/website_preview.js:0
msgid ""
"You are about to be redirected to the domain configured for your website ( "
"%s ). This is necessary to edit or view your website from the Website app. "
"You might need to log back in."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/translator/translator.xml:0
msgid "You are about to enter the translation mode."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"You can choose to have your computer warn you each time a cookie is being sent, or you can choose to turn off all cookies.\n"
"                            Each browser is a little different, so look at your browser's Help menu to learn the correct way to modify your cookies."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "You can edit colors and backgrounds to highlight features."
msgstr ""

#. module: website
#. odoo-python
#: code:addons/website/models/res_users.py:0
#: model:ir.model.constraint,message:website.constraint_res_users_login_key
msgid "You can not have two users with the same login!"
msgstr ""

#. module: website
#. odoo-python
#: code:addons/website/models/website_snippet_filter.py:0
msgid "You can only use template prefixed by dynamic_filter_template_ "
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_accordion
msgid ""
"You can reach our customer support team by emailing "
"<EMAIL>, calling ******-555-5556, or using the live "
"chat on our website. Our dedicated team is available 24/7 to assist with any"
" inquiries or issues."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
msgid "You can't duplicate the submit button of the form."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
msgid "You can't remove the submit button of the form"
msgstr ""

#. module: website
#. odoo-python
#: code:addons/website/models/website.py:0
msgid ""
"You cannot delete default website %s. Try to change its settings instead"
msgstr ""

#. module: website
#. odoo-python
#: code:addons/website/models/website_menu.py:0
msgid ""
"You cannot delete this website menu as this serves as the default parent "
"menu for new websites (e.g., /shop, /event, ...)."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
msgid "You cannot duplicate this field."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_facebook_page/options.js:0
msgid "You didn't provide a valid Facebook link"
msgstr ""

#. module: website
#. odoo-python
#: code:addons/website/models/mixins.py:0
msgid "You do not have the rights to publish/unpublish"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
msgid "You don't have permissions to edit this record."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
msgid ""
"You have hidden this page from search results. It won't be indexed by search"
" engines."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "You may opt-out of a third-party's use of cookies by visiting the"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_searchbar
msgid "You will get results from blog posts, products, etc"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
msgid "You'll be able to create your pages later on."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_social_media/options.js:0
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
#: model_terms:ir.ui.view,arch_db:website.s_social_media
msgid "YouTube"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/send_mail_form.js:0
msgid "Your Company"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_template
msgid ""
"Your Dynamic Snippet will be displayed here... This message is displayed "
"because you did not provide both a filter and a template to use.<br/>"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/send_mail_form.js:0
msgid "Your Email"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_embed_code/000.js:0
msgid ""
"Your Embed Code snippet doesn't have anything to display. Click on Edit to "
"modify it."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_cards_soft
msgid "Your Journey Begins Here"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_kickoff
msgid "Your Journey Starts Here,"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/send_mail_form.js:0
msgid "Your Name"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/send_mail_form.js:0
msgid "Your Question"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_title
msgid "Your Site Title"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_s_features
msgid ""
"Your brand is your story. We help you tell it through cohesive visual "
"identity and messaging that resonates with your audience."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/resource_editor/resource_editor_warning.xml:0
msgid "Your changes might be lost during future Odoo upgrade."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_list
msgid ""
"Your data is protected by advanced encryption and security protocols, "
"keeping your personal information safe."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.js:0
msgid "Your description looks too long."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.js:0
msgid "Your description looks too short."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Your experience may be degraded if you discard those cookies, but the "
"website will still work."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_instagram_page_options
msgid ""
"Your instagram page must be public to be integrated into an Odoo website."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_cover
msgid "Your journey starts here"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
msgid "Your message has been sent."
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/editor/editor.js:0
msgid "Your modifications were saved to apply this option."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_hybrid
#: model_terms:ir.ui.view,arch_db:website.list_website_public_pages
msgid "Your search '"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website__social_youtube
msgid "Youtube Account"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "Zoom"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Zoom In"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Zoom Out"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"__gads (Google)<br/>\n"
"                                            __gac (Google)"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"_ga (Google)<br/>\n"
"                                            _gat (Google)<br/>\n"
"                                            _gid (Google)<br/>\n"
"                                            _gac_* (Google)"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
msgid "a Color Palette"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.js:0
msgid "a blog"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.js:0
msgid "a business website"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
msgid "a new image"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.js:0
msgid "an elearning platform"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.js:0
msgid "an event website"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.js:0
msgid "an online store"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
msgid "and"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "block, blog, post, catalog, feed, items, entries, entry, collection"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.js:0
msgid "blog"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "breadcrumb"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "btn-outline-primary"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "btn-outline-secondary"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "btn-primary"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "btn-secondary"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
msgid "business"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "celebration, launch"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"cite, slogan, tagline, mantra, catchphrase, statements, sayings, comments, "
"mission, citations, maxim, quotes, principle, ethos, values"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"cite, testimonials, endorsements, reviews, feedback, statements, references,"
" sayings, comments, appreciations, citations"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/views/theme_preview.xml:0
msgid "close"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "columns, containers, layouts, large, panels, modules"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"columns, gallery, pictures, photos, media, text, content, album, showcase, "
"visuals, portfolio, arrangement, collection, visual-grid, split, alignment, "
"added value"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "common answers, common questions"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"common answers, common questions, faq, QA, collapse, expandable, toggle, "
"collapsible, hide-show, movement, information, image, picture, photo, "
"illustration, media, visual"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"contact, collect, submission, input, fields, questionnaire, survey, "
"registration, request"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "content, paragraph, article, body, description, information"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"content, picture, photo, illustration, media, visual, article, story, "
"combination"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"content, picture, photo, illustration, media, visual, article, story, "
"combination, engage, call to action, cta, box, showcase"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"content, picture, photo, illustration, media, visual, article, story, "
"combination, heading, headline"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"content, picture, photo, illustration, media, visual, article, story, "
"combination, more, hexagon, geometric"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"content, picture, photo, illustration, media, visual, article, story, "
"combination, trendy, pattern, design"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"content, picture, photo, illustration, media, visual, article, story, "
"combination, trendy, pattern, design, shape, geometric, patterned"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"content, picture, photo, illustration, media, visual, article, story, "
"combination, trendy, pattern, design, shape, geometric, patterned, contrast"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"content, picture, photo, illustration, media, visual, article, story, "
"combination, trendy, pattern, design, shape, geometric, patterned, contrast,"
" collage, arrangement, gallery, creative, mosaic"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"content, picture, photo, illustration, media, visual, focus, in-depth, "
"analysis, more, contact, detailed, mockup, explore, insight"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_big_number
msgid "customer satisfaction"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"customers, clients, sponsors, partners, supporters, case-studies, "
"collaborators, associations, associates, testimonials, endorsements"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"customers, clients, sponsors, partners, supporters, case-studies, "
"collaborators, associations, associates, testimonials, endorsements, social"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "days"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"description, containers, layouts, structures, multi-columns, modules, boxes"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"description, containers, layouts, structures, multi-columns, modules, boxes,"
" content, picture, photo, illustration, media, visual, article, story, "
"combination, showcase, announcement, reveal"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"description, containers, layouts, structures, multi-columns, modules, boxes,"
" content, picture, photo, illustration, media, visual, article, story, "
"combination, showcase, announcement, reveal, trendy, design, shape, "
"geometric, engage, call to action, cta, button"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.js:0
msgid "develop the brand"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.js:0
msgid "e-learning platform"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "e.g. /my-awesome-page"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "e.g. De Brouckere, Brussels, Belgium"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_page_properties_view_form
msgid "e.g. Home Page"
msgstr ""

#. module: website
#: model:ir.ui.menu,name:website.menu_website_dashboard
msgid "eCommerce"
msgstr ""

#. module: website
#: model:website.configurator.feature,name:website.feature_module_elearning
msgid "eLearning"
msgstr ""

#. module: website
#. odoo-python
#: code:addons/website/controllers/form.py:0
msgid "email"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
msgid "fonts.google.com"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
msgid "for my"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.js:0
msgid "forum"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/page_properties.xml:0
msgid "found(s)"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_search_box
msgid "found)"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"four sections, column, grid, division, split, segments, pictures, "
"illustration, media, photos, tiles, arrangement, gallery, images-grid, "
"mixed, collection, stacking, visual-grid, showcase, visuals, portfolio, "
"thumbnails, engage, call to action, cta, showcase"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.brand_promotion
msgid "free website"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
msgid "from Logo"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "frontend_lang (Odoo)"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"gallery, carousel, pictures, photos, album, showcase, visuals, portfolio, "
"thumbnails, slideshow"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"gallery, carousel, slider, slideshow, picture, photo, image-slider, "
"rotating, swipe, transition, media-carousel, movement"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.js:0
msgid "get leads"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "google1234567890123456.html"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"graph, table, diagram, pie, plot, bar, metrics, figures, data-visualization,"
" statistics, stats, analytics, infographic, skills, report"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"grid, gallery, pictures, photos, album, showcase, visuals, portfolio, "
"mosaic, collage, arrangement, collection, visual-grid"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"grid, gallery, pictures, photos, media, text, content, album, showcase, "
"visuals, portfolio, mosaic, collage, arrangement, collection, visual-grid, "
"split, alignment"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"header, introduction, home, content, introduction, overview, spotlight, "
"presentation, welcome, context, description, primary, highlight, lead"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"heading, h1, headline, header, main, top, caption, introductory, principal, "
"key"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"headline, header, content, picture, photo, illustration, media, visual, "
"article, combination, trendy, pattern, design, bold, impactful, vibrant, "
"standout"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"headline, header, content, picture, photo, illustration, media, visual, "
"combination"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_descriptive
msgid "<EMAIL>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"hero, jumbotron, headline, header, branding, intro, home, showcase, "
"spotlight, lead, welcome, announcement, splash, top, main"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"hero, jumbotron, headline, header, branding, intro, home, showcase, "
"spotlight, main, landing, presentation, top, splash"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"hero, jumbotron, headline, header, intro, home, content, description, "
"primary, highlight, lead"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"hero, jumbotron, headline, header, introduction, home, content, "
"introduction, overview, spotlight, presentation, welcome, context, "
"description, primary, highlight, lead"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"hero, jumbotron, headline, header, introduction, home, content, "
"introduction, overview, spotlight, presentation, welcome, context, "
"description, primary, highlight, lead, CTA, promote, promotion"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"hero, jumbotron, headline, header, introduction, home, content, "
"introduction, overview, spotlight, presentation, welcome, context, "
"description, primary, highlight, lead, discover, exploration, reveal"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"hero, jumbotron, headline, header, introduction, home, content, "
"introduction, overview, spotlight, presentation, welcome, context, "
"description, primary, highlight, lead, journey, skills, expertises, experts,"
" accomplishments, knowledge"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"hero, jumbotron, headline, header, introduction, home, content, picture, "
"photo, illustration, media, visual, article, combination, trendy, pattern, "
"design"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"hero, jumbotron, headline, header, introduction, home, content, picture, "
"photo, illustration, media, visual, article, combination, trendy, pattern, "
"design, centered"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"history, story, events, milestones, chronology, sequence, progression, "
"achievements"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"history, story, events, milestones, chronology, sequence, progression, "
"achievements, changelog, updates, announcements, recent, latest"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "https://www.odoo.com"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "iPhone"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"im_livechat_previous_operator (Odoo)<br/>\n"
"                                            utm_campaign (Odoo)<br/>\n"
"                                            utm_source (Odoo)<br/>\n"
"                                            utm_medium (Odoo)"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/website_preview/website_preview.xml:0
msgid "in the top right corner to start designing."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "<EMAIL>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.s_contact_info
#: model_terms:ir.ui.view,arch_db:website.template_footer_call_to_action
#: model_terms:ir.ui.view,arch_db:website.template_footer_centered
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
msgid "<EMAIL>"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.js:0
msgid "inform customers"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "instance of Odoo, the"
msgstr ""

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_ui_view__inherit_id__ir_ui_view
msgid "ir.ui.view"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"journey, exploration, travel, outdoor, excitement, quest, start, onboarding,"
" discovery, thrill"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "link"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"masonry, grid, column, pictures, photos, album, showcase, visuals, "
"portfolio, mosaic, collage, arrangement, collection, visual-grid"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"menu, pricing, shop, table, cart, product, cost, charges, fees, rates, "
"prices, expenses"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"menu, pricing, shop, table, cart, product, cost, charges, fees, rates, "
"prices, expenses, columns"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "navigation, index, outline, chapters, sections, overview, menu"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "navigation, sections, multi-tab, panel, toggle"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "o-color-"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_instagram_page_options
msgid "odoo.official"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.step_wizard
msgid "of"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.js:0
msgid "online appointment system"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.js:0
msgid "online store"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"organization, company, people, column, members, staffs, profiles, bios, "
"roles, personnel, crew"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"organization, company, people, members, staffs, profiles, bios, roles, "
"personnel, crew, patterned, trendy"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"organization, company, people, members, staffs, profiles, bios, roles, "
"personnel, crew, patterned, trendy, social"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"organization, structure, people, team, name, role, position, image, "
"portrait, photo, employees, shapes"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
msgid "perfect website?"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/website_preview/website_preview.xml:0
#: code:addons/website/static/src/components/fields/widget_iframe.xml:0
msgid "phone"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"picture, photo, illustration, media, visual, start, launch, commencement, "
"initiation, opening, kick-off, kickoff, beginning, events"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "placeholder"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.xml:0
msgid "pointer to discover features."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"pricing, promotion, price, feature-comparison, side-by-side, evaluation, "
"competitive, overview"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"process, progression, guide, workflow, sequence, instructions, stages, "
"procedure, roadmap"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"promotion, characteristic, quality, highlights, specs, advantages, "
"functionalities, exhibit, details, capabilities, attributes, promotion"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"promotion, characteristic, quality, highlights, specs, advantages, "
"functionalities, exhibit, details, capabilities, attributes, promotion, "
"headline, content, overview, spotlight"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"promotion, characteristic, quality, highlights, specs, advantages, "
"functionalities, exhibit, details, capabilities, attributes, promotion, "
"presentation, demo, feature"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"promotion, characteristic, quality, highlights, specs, advantages, "
"functionalities, features, exhibit, details, capabilities, attributes, "
"promotion, headline, content, overview, spotlight"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"questions, answers, common answers, common questions, faq, help, support, "
"information, knowledge, guide, troubleshooting, assistance, QA, terms of "
"services"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"questions, answers, common answers, common questions, faq, help, support, "
"information, knowledge, guide, troubleshooting, assistance, columns, QA"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.js:0
msgid "recruitment platform"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "results"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"reveal, showcase, launch, presentation, announcement, content, picture, "
"photo, illustration, media, visual, article, combination"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"reveal, showcase, launch, presentation, announcement, content, picture, "
"photo, illustration, media, visual, combination"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "rows"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.js:0
msgid "schedule appointments"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "scrolling, depth, effect, background, layer, visual, movement"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.js:0
msgid "sell more"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "session_id (Odoo)<br/>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"shop, group, list, card, cart, products, inventory, catalog, merchandise, "
"goods"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "social media, ig, feed"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"statistics, stats, KPI, metrics, dashboard, analytics, highlights, figures, "
"skills, achievements, benchmarks, milestones, indicators, data, "
"measurements, reports, trends, results"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"statistics, stats, KPI, metrics, dashboard, analytics, highlights, figures, "
"skills, achievements, benchmarks, milestones, indicators, data, "
"measurements, reports, trends, results, analytics, cta, call to action, "
"button"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"statistics, stats, KPI, metrics, dashboard, analytics, highlights, figures, "
"skills, achievements, benchmarks, milestones, indicators, data, "
"measurements, reports, trends, results, analytics, summaries, summary"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid ""
"statistics, stats, KPI, metrics, dashboard, analytics, highlights, figures, "
"skills, achievements, benchmarks, milestones, indicators, data, "
"measurements, reports, trends, results, analytics, summaries, summary, "
"large-figures, prominent, standout"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "text link"
msgstr ""

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_ui_view__inherit_id__theme_ir_ui_view
msgid "theme.ir.ui.view"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_countdown/000.xml:0
msgid "this page"
msgstr ""

#. module: website
#: model:ir.model.constraint,message:website.constraint_website_controller_page_unique_name_slugified
msgid "url should be unique"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_id
msgid "website"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
msgid "with the main objective to"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "yes"
msgstr ""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
msgid "zip, ttf, woff, woff2, otf"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "⌙ Active"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "⌙ Delay"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "⌙ Inactive"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
msgid "⌙ Style"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_pricelist_boxed
msgid "✽  Pastas"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_pricelist_boxed
msgid "✽  Pizzas"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_key_benefits
msgid "✽  What We Offer"
msgstr ""
