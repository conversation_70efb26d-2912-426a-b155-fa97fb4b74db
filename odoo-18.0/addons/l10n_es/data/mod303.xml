<?xml version="1.0" encoding="utf-8"?>
<odoo>
<data auto_sequence="1">
    <record id="mod_303" model="account.report">
        <field name="name">Tax Report (Mod 303)</field>
        <field name="sequence">303</field>
        <field name="filter_analytic" eval="False"/>
        <field name="filter_date_range" eval="True"/>
        <field name="filter_period_comparison" eval="False"/>
        <field name="filter_unfold_all" eval="True"/>
        <field name="filter_journals" eval="True"/>
        <field name="country_id" ref="base.es"/>
        <field name="filter_multi_company">tax_units</field>
        <field name="root_report_id" ref="account.generic_tax_report"/>
        <field name="column_ids">
            <record id="mod_303_column" model="account.report.column">
                <field name="name">Balance</field>
                <field name="expression_label">balance</field>
            </record>
        </field>
        <field name="line_ids">
            <record id="mod_303_title_1" model="account.report.line">
                <field name="name">General regime</field>
                <field name="code">aeat_mod_303_title_1</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="mod_303_title_2" model="account.report.line">
                        <field name="name">VAT accrued</field>
                        <field name="code">aeat_mod_303_title_2</field>
                        <field name="children_ids">
                            <record id="mod_303_casilla_150" model="account.report.line">
                                <field name="name">[150] Base taxable 0%</field>
                                <field name="code">aeat_mod_303_150</field>
                                <field name="groupby">account_id</field>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="mod_303_casilla_150_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">mod303[150]</field>
                                    </record>
                                </field>
                            </record>
                            <record id="mod_303_casilla_152" model="account.report.line">
                                <field name="name">[152] Contribution 0%</field>
                                <field name="code">aeat_mod_303_152</field>
                                <field name="groupby">account_id</field>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="mod_303_casilla_152_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">mod303[152]</field>
                                    </record>
                                </field>
                            </record>
                            <record id="mod_303_casilla_165" model="account.report.line">
                                <field name="name">[165] Base taxable 2%</field>
                                <field name="code">aeat_mod_303_165</field>
                                <field name="groupby">account_id</field>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="mod_303_casilla_165_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">mod303[165]</field>
                                    </record>
                                </field>
                            </record>
                            <record id="mod_303_casilla_167" model="account.report.line">
                                <field name="name">[167] Contributions 2%</field>
                                <field name="code">aeat_mod_303_167</field>
                                <field name="groupby">account_id</field>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="mod_303_casilla_167_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">mod303[167]</field>
                                    </record>
                                </field>
                            </record>
                            <record id="mod_303_casilla_01" model="account.report.line">
                                <field name="name">[01] Base taxable 4%</field>
                                <field name="code">aeat_mod_303_01</field>
                                <field name="groupby">account_id</field>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="mod_303_casilla_01_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">mod303[01]</field>
                                    </record>
                                </field>
                            </record>
                            <record id="mod_303_casilla_03" model="account.report.line">
                                <field name="name">[03] Contribution 4%</field>
                                <field name="code">aeat_mod_303_03</field>
                                <field name="groupby">account_id</field>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="mod_303_casilla_03_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">mod303[03]</field>
                                    </record>
                                </field>
                            </record>
                            <record id="mod_303_casilla_153" model="account.report.line">
                                <field name="name">[153] Base taxable 5%</field>
                                <field name="code">aeat_mod_303_153</field>
                                <field name="groupby">account_id</field>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="mod_303_casilla_153_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">mod303[153]</field>
                                    </record>
                                </field>
                            </record>
                            <record id="mod_303_casilla_155" model="account.report.line">
                                <field name="name">[155] Contributions 5%</field>
                                <field name="code">aeat_mod_303_155</field>
                                <field name="groupby">account_id</field>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="mod_303_casilla_155_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">mod303[155]</field>
                                    </record>
                                </field>
                            </record>
                            <record id="mod_303_casilla_04" model="account.report.line">
                                <field name="name">[04] Base taxable 10%</field>
                                <field name="code">aeat_mod_303_04</field>
                                <field name="groupby">account_id</field>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="mod_303_casilla_04_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">mod303[04]</field>
                                    </record>
                                </field>
                            </record>
                            <record id="mod_303_casilla_06" model="account.report.line">
                                <field name="name">[06] Contribution 10%</field>
                                <field name="code">aeat_mod_303_06</field>
                                <field name="groupby">account_id</field>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="mod_303_casilla_06_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">mod303[06]</field>
                                    </record>
                                </field>
                            </record>
                            <record id="mod_303_casilla_07" model="account.report.line">
                                <field name="name">[07] Base taxable 21%</field>
                                <field name="code">aeat_mod_303_07</field>
                                <field name="groupby">account_id</field>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="mod_303_casilla_07_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">mod303[07]</field>
                                    </record>
                                </field>
                            </record>
                            <record id="mod_303_casilla_09" model="account.report.line">
                                <field name="name">[09] Contribution 21%</field>
                                <field name="code">aeat_mod_303_09</field>
                                <field name="groupby">account_id</field>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="mod_303_casilla_09_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">mod303[09]</field>
                                    </record>
                                </field>
                            </record>
                            <record id="mod_303_title_3" model="account.report.line">
                                <field name="name">Intra-community purchases of goods and services</field>
                                <field name="code">aeat_mod_303_title_3</field>
                                <field name="children_ids">
                                    <record id="mod_303_casilla_10" model="account.report.line">
                                        <field name="name">[10] Base taxable</field>
                                        <field name="code">aeat_mod_303_10</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="mod_303_casilla_10_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">tax_tags</field>
                                                <field name="formula">mod303[10]</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="mod_303_casilla_11" model="account.report.line">
                                        <field name="name">[11] Contributions</field>
                                        <field name="code">aeat_mod_303_11</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="mod_303_casilla_11_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">tax_tags</field>
                                                <field name="formula">mod303[11]</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="mod_303_title_4" model="account.report.line">
                                <field name="name">Other operations with reverse charge (except intracom. adq.)</field>
                                <field name="code">aeat_mod_303_title_4</field>
                                <field name="children_ids">
                                    <record id="mod_303_casilla_12" model="account.report.line">
                                        <field name="name">[12] Base taxable</field>
                                        <field name="code">aeat_mod_303_12</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="mod_303_casilla_12_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">tax_tags</field>
                                                <field name="formula">mod303[12]</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="mod_303_casilla_13" model="account.report.line">
                                        <field name="name">[13] Contributions</field>
                                        <field name="code">aeat_mod_303_13</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="mod_303_casilla_13_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">tax_tags</field>
                                                <field name="formula">mod303[13]</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="mod_303_title_5" model="account.report.line">
                                <field name="name">Modification bases and contributions</field>
                                <field name="code">aeat_mod_303_title_5</field>
                                <field name="children_ids">
                                    <record id="mod_303_casilla_14" model="account.report.line">
                                        <field name="name">[14] Base taxable</field>
                                        <field name="code">aeat_mod_303_14</field>
                                        <field name="expression_ids">
                                            <record id="mod_303_casilla_14_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">aggregation</field>
                                                <field name="formula">aeat_mod_303_14.aeat_mod_303_14_sale_balance - aeat_mod_303_14.aeat_mod_303_14_purchase_balance</field>
                                            </record>
                                            <record id="mod_303_casilla_14_aeat_mod_303_14_sale_balance" model="account.report.expression">
                                                <field name="label">aeat_mod_303_14_sale_balance</field>
                                                <field name="engine">tax_tags</field>
                                                <field name="formula">mod303[14_sale]</field>
                                            </record>
                                            <record id="mod_303_casilla_14_aeat_mod_303_14_purchase_balance" model="account.report.expression">
                                                <field name="label">aeat_mod_303_14_purchase_balance</field>
                                                <field name="engine">tax_tags</field>
                                                <field name="formula">mod303[14_purchase]</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="mod_303_casilla_15" model="account.report.line">
                                        <field name="name">[15] Contributions</field>
                                        <field name="code">aeat_mod_303_15</field>
                                        <field name="expression_ids">
                                            <record id="mod_303_casilla_15_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">tax_tags</field>
                                                <field name="formula">mod303[15]</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="mod_303_title_6" model="account.report.line">
                                <field name="name">Surcharge equivalence</field>
                                <field name="code">aeat_mod_303_title_6</field>
                                <field name="children_ids">
                                    <record id="mod_303_casilla_156" model="account.report.line">
                                        <field name="name">[156] Base taxable 1.75%</field>
                                        <field name="code">aeat_mod_303_156</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="mod_303_casilla_156_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">tax_tags</field>
                                                <field name="formula">mod303[156]</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="mod_303_casilla_158" model="account.report.line">
                                        <field name="name">[158] Contributions 1.75%</field>
                                        <field name="code">aeat_mod_303_158</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="mod_303_casilla_158_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">tax_tags</field>
                                                <field name="formula">mod303[158]</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="mod_303_casilla_168" model="account.report.line">
                                        <field name="name">[168] Base taxable 0.26%</field>
                                        <field name="code">aeat_mod_303_168</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="mod_303_casilla_168_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">tax_tags</field>
                                                <field name="formula">mod303[168]</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="mod_303_casilla_170" model="account.report.line">
                                        <field name="name">[170] Contribution 0.26%</field>
                                        <field name="code">aeat_mod_303_170</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="mod_303_casilla_170_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">tax_tags</field>
                                                <field name="formula">mod303[170]</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="mod_303_casilla_16" model="account.report.line">
                                        <field name="name">[16] Base taxable 1%</field>
                                        <field name="code">aeat_mod_303_16</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="mod_303_casilla_16_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">tax_tags</field>
                                                <field name="formula">mod303[16]</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="mod_303_casilla_18" model="account.report.line">
                                        <field name="name">[18] Contribution 1%</field>
                                        <field name="code">aeat_mod_303_18</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="mod_303_casilla_18_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">tax_tags</field>
                                                <field name="formula">mod303[18]</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="mod_303_casilla_19" model="account.report.line">
                                        <field name="name">[19] Base taxable 1.4%</field>
                                        <field name="code">aeat_mod_303_19</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="mod_303_casilla_19_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">tax_tags</field>
                                                <field name="formula">mod303[19]</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="mod_303_casilla_21" model="account.report.line">
                                        <field name="name">[21] Contributions 1.4%</field>
                                        <field name="code">aeat_mod_303_21</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="mod_303_casilla_21_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">tax_tags</field>
                                                <field name="formula">mod303[21]</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="mod_303_casilla_22" model="account.report.line">
                                        <field name="name">[22] Base taxable 5.2%</field>
                                        <field name="code">aeat_mod_303_22</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="mod_303_casilla_22_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">tax_tags</field>
                                                <field name="formula">mod303[22]</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="mod_303_casilla_24" model="account.report.line">
                                        <field name="name">[24] Contributions 5.2%</field>
                                        <field name="code">aeat_mod_303_24</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="mod_303_casilla_24_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">tax_tags</field>
                                                <field name="formula">mod303[24]</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="mod_303_title_7" model="account.report.line">
                                <field name="name">Modification bases and contributions of the equivalence surcharge</field>
                                <field name="code">aeat_mod_303_title_7</field>
                                <field name="children_ids">
                                    <record id="mod_303_casilla_25" model="account.report.line">
                                        <field name="name">[25] Base taxable</field>
                                        <field name="code">aeat_mod_303_25</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="mod_303_casilla_25_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">tax_tags</field>
                                                <field name="formula">mod303[25]</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="mod_303_casilla_26" model="account.report.line">
                                        <field name="name">[26] Contributions</field>
                                        <field name="code">aeat_mod_303_26</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="mod_303_casilla_26_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">tax_tags</field>
                                                <field name="formula">mod303[26]</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="mod_303_casilla_27" model="account.report.line">
                                <field name="name">[27] Total accrual contributions</field>
                                <field name="code">aeat_mod_303_27</field>
                                <field name="aggregation_formula">aeat_mod_303_152.balance + aeat_mod_303_167.balance + aeat_mod_303_03.balance + aeat_mod_303_155.balance + aeat_mod_303_06.balance + aeat_mod_303_09.balance + aeat_mod_303_11.balance + aeat_mod_303_13.balance + aeat_mod_303_15.balance + aeat_mod_303_158.balance + aeat_mod_303_170.balance + aeat_mod_303_18.balance + aeat_mod_303_21.balance + aeat_mod_303_24.balance + aeat_mod_303_26.balance</field>
                            </record>
                        </field>
                    </record>
                    <record id="mod_303_title_8" model="account.report.line">
                        <field name="name">VAT receivable</field>
                        <field name="code">aeat_mod_303_title_8</field>
                        <field name="children_ids">
                            <record id="mod_303_title_9" model="account.report.line">
                                <field name="name">For contributions supported in the current internal operations</field>
                                <field name="code">aeat_mod_303_title_9</field>
                                <field name="children_ids">
                                    <record id="mod_303_casilla_28" model="account.report.line">
                                        <field name="name">[28] Base</field>
                                        <field name="code">aeat_mod_303_28</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="mod_303_casilla_28_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">tax_tags</field>
                                                <field name="formula">mod303[28]</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="mod_303_casilla_29" model="account.report.line">
                                        <field name="name">[29] Contributions</field>
                                        <field name="code">aeat_mod_303_29</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="mod_303_casilla_29_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">tax_tags</field>
                                                <field name="formula">mod303[29]</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="mod_303_title_10" model="account.report.line">
                                <field name="name">For contributions supported in internal operations with investment goods</field>
                                <field name="code">aeat_mod_303_title_10</field>
                                <field name="children_ids">
                                    <record id="mod_303_casilla_30" model="account.report.line">
                                        <field name="name">[30] Base</field>
                                        <field name="code">aeat_mod_303_30</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="mod_303_casilla_30_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">tax_tags</field>
                                                <field name="formula">mod303[30]</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="mod_303_casilla_31" model="account.report.line">
                                        <field name="name">[31] Contributions</field>
                                        <field name="code">aeat_mod_303_31</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="mod_303_casilla_31_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">tax_tags</field>
                                                <field name="formula">mod303[31]</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="mod_303_title_11" model="account.report.line">
                                <field name="name">For contributions supported in imports of current goods</field>
                                <field name="code">aeat_mod_303_title_11</field>
                                <field name="children_ids">
                                    <record id="mod_303_casilla_32" model="account.report.line">
                                        <field name="name">[32] Base</field>
                                        <field name="code">aeat_mod_303_32</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="mod_303_casilla_32_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">tax_tags</field>
                                                <field name="formula">mod303[32]</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="mod_303_casilla_33" model="account.report.line">
                                        <field name="name">[33] Contributions</field>
                                        <field name="code">aeat_mod_303_33</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="mod_303_casilla_33_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">tax_tags</field>
                                                <field name="formula">mod303[33]</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="mod_303_title_12" model="account.report.line">
                                <field name="name">For contributions supported in import of investment goods</field>
                                <field name="code">aeat_mod_303_title_12</field>
                                <field name="children_ids">
                                    <record id="mod_303_casilla_34" model="account.report.line">
                                        <field name="name">[34] Base</field>
                                        <field name="code">aeat_mod_303_34</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="mod_303_casilla_34_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">tax_tags</field>
                                                <field name="formula">mod303[34]</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="mod_303_casilla_35" model="account.report.line">
                                        <field name="name">[35] Contributions</field>
                                        <field name="code">aeat_mod_303_35</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="mod_303_casilla_35_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">tax_tags</field>
                                                <field name="formula">mod303[35]</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="mod_303_title_13" model="account.report.line">
                                <field name="name">In intra-community purchases of current goods and services</field>
                                <field name="code">aeat_mod_303_title_13</field>
                                <field name="children_ids">
                                    <record id="mod_303_casilla_36" model="account.report.line">
                                        <field name="name">[36] Base</field>
                                        <field name="code">aeat_mod_303_36</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="mod_303_casilla_36_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">tax_tags</field>
                                                <field name="formula">mod303[36]</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="mod_303_casilla_37" model="account.report.line">
                                        <field name="name">[37] Contributions</field>
                                        <field name="code">aeat_mod_303_37</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="mod_303_casilla_37_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">tax_tags</field>
                                                <field name="formula">mod303[37]</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="mod_303_title_14" model="account.report.line">
                                <field name="name">In intra-community purchases of investment goods</field>
                                <field name="code">aeat_mod_303_title_14</field>
                                <field name="children_ids">
                                    <record id="mod_303_casilla_38" model="account.report.line">
                                        <field name="name">[38] Base</field>
                                        <field name="code">aeat_mod_303_38</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="mod_303_casilla_38_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">tax_tags</field>
                                                <field name="formula">mod303[38]</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="mod_303_casilla_39" model="account.report.line">
                                        <field name="name">[39] Contributions</field>
                                        <field name="code">aeat_mod_303_39</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="mod_303_casilla_39_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">tax_tags</field>
                                                <field name="formula">mod303[39]</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="mod_303_title_15" model="account.report.line">
                                <field name="name">Rectification of deductions</field>
                                <field name="code">aeat_mod_303_title_15</field>
                                <field name="children_ids">
                                    <record id="mod_303_casilla_40" model="account.report.line">
                                        <field name="name">[40] Base</field>
                                        <field name="code">aeat_mod_303_40</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="mod_303_casilla_40_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">tax_tags</field>
                                                <field name="formula">mod303[40]</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="mod_303_casilla_41" model="account.report.line">
                                        <field name="name">[41] Contributions</field>
                                        <field name="code">aeat_mod_303_41</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="mod_303_casilla_41_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">tax_tags</field>
                                                <field name="formula">mod303[41]</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="mod_303_casilla_42" model="account.report.line">
                                <field name="name">[42] Compensations Special Regime A. G. y P. - Purchases contributions</field>
                                <field name="code">aeat_mod_303_42</field>
                                <field name="groupby">account_id</field>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="mod_303_casilla_42_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">mod303[42]</field>
                                    </record>
                                </field>
                            </record>
                            <record id="mod_303_casilla_43" model="account.report.line">
                                <field name="name">[43] Regularization of investment assets</field>
                                <field name="code">aeat_mod_303_43</field>
                                <field name="expression_ids">
                                    <record id="mod_303_casilla_43_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=2</field>
                                    </record>
                                </field>
                            </record>
                            <record id="mod_303_casilla_44" model="account.report.line">
                                <field name="name">[44] Regularization by application of the definitive prorrata percentage</field>
                                <field name="code">aeat_mod_303_44</field>
                                <field name="expression_ids">
                                    <record id="mod_303_casilla_44_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=2</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="mod_303_casilla_45" model="account.report.line">
                        <field name="name">[45] Total to deduct</field>
                        <field name="code">aeat_mod_303_45</field>
                        <field name="aggregation_formula">aeat_mod_303_29.balance + aeat_mod_303_31.balance + aeat_mod_303_33.balance + aeat_mod_303_35.balance + aeat_mod_303_37.balance + aeat_mod_303_39.balance + aeat_mod_303_41.balance + aeat_mod_303_42.balance + aeat_mod_303_43.balance + aeat_mod_303_44.balance</field>
                    </record>
                    <record id="mod_303_casilla_46" model="account.report.line">
                        <field name="name">[46] Result of general regime</field>
                        <field name="code">aeat_mod_303_46</field>
                        <field name="aggregation_formula">aeat_mod_303_27.balance - aeat_mod_303_45.balance</field>
                    </record>
                </field>
            </record>
            <record id="mod_303_title_16" model="account.report.line">
                <field name="name">Additional information</field>
                <field name="code">aeat_mod_303_title_16</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="mod_303_casilla_59" model="account.report.line">
                        <field name="name">[59] Intra-community sales of goods and services - Base sales</field>
                        <field name="code">aeat_mod_303_59</field>
                        <field name="groupby">account_id</field>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="mod_303_casilla_59_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">mod303[59]</field>
                            </record>
                        </field>
                    </record>
                    <record id="mod_303_casilla_60" model="account.report.line">
                        <field name="name">[60] Exportations and similar operations - Base sales</field>
                        <field name="code">aeat_mod_303_60</field>
                        <field name="groupby">account_id</field>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="mod_303_casilla_60_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">mod303[60]</field>
                            </record>
                        </field>
                    </record>
                    <record id="mod_303_casilla_120" model="account.report.line">
                        <field name="name">[120] Operations not subject to location rules (excepted those included in box 123)</field>
                        <field name="code">aeat_mod_303_120</field>
                        <field name="groupby">account_id</field>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="mod_303_casilla_120_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">mod303[120]</field>
                            </record>
                        </field>
                    </record>
                    <record id="mod_303_casilla_122" model="account.report.line">
                        <field name="name">[122] Operations subject to reverse charge</field>
                        <field name="code">aeat_mod_303_122</field>
                        <field name="groupby">account_id</field>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="mod_303_casilla_122_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">mod303[122]</field>
                            </record>
                        </field>
                    </record>
                    <record id="mod_303_casilla_123" model="account.report.line">
                        <field name="name">[123] Operation not subjected to rule of location covered by the special one-stop shop regime</field>
                        <field name="code">aeat_mod_303_123</field>
                        <field name="groupby">account_id</field>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="mod_303_casilla_123_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">mod303[123]</field>
                            </record>
                        </field>
                    </record>
                    <record id="mod_303_casilla_124" model="account.report.line">
                        <field name="name">[124] Operations subjected and covered by special regime of one-stop shop</field>
                        <field name="code">aeat_mod_303_124</field>
                        <field name="groupby">account_id</field>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="mod_303_casilla_124_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">mod303[124]</field>
                            </record>
                        </field>
                    </record>
                    <record id="mod_303_title_17" model="account.report.line">
                        <field name="name">Amount of sale of goods and services to which special regime of the cash criterion ha dbeen applied would have been accrued in accordance with the general accrual rule contained in art. 75 LIVA</field>
                        <field name="code">aeat_mod_303_title_17</field>
                        <field name="children_ids">
                            <record id="mod_303_casilla_62" model="account.report.line">
                                <field name="name">[62] Base taxable</field>
                                <field name="code">aeat_mod_303_62</field>
                                <field name="expression_ids">
                                    <record id="mod_303_casilla_62_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=2</field>
                                    </record>
                                </field>
                            </record>
                            <record id="mod_303_casilla_63" model="account.report.line">
                                <field name="name">[63] Contributions</field>
                                <field name="code">aeat_mod_303_63</field>
                                <field name="expression_ids">
                                    <record id="mod_303_casilla_63_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=2</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="mod_303_title_18" model="account.report.line">
                <field name="name">Result</field>
                <field name="code">aeat_mod_303_title_18</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="mod_303_casilla_65" model="account.report.line">
                        <field name="name">[65] Percentage attributable to the State Administration</field>
                        <field name="code">aeat_mod_303_65</field>
                        <field name="expression_ids">
                            <record id="mod_303_casilla_65_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">external</field>
                                <field name="formula">sum</field>
                                <field name="figure_type">percentage</field>
                                <field name="subformula">editable;rounding=2</field>
                            </record>
                        </field>
                    </record>
                    <record id="mod_303_casilla_66" model="account.report.line">
                        <field name="name">[66] Attributable to the State Administration</field>
                        <field name="code">aeat_mod_303_66</field>
                        <field name="aggregation_formula">aeat_mod_303_65.balance/100 * aeat_mod_303_46.balance</field>
                    </record>
                    <record id="mod_303_casilla_67" model="account.report.line">
                        <field name="name">[67] Contributions to be compensated from previous periods</field>
                        <field name="code">aeat_mod_303_67</field>
                        <field name="expression_ids">
                            <record id="mod_303_casilla_67_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">external</field>
                                <field name="formula">sum</field>
                                <field name="subformula">editable;rounding=2</field>
                            </record>
                        </field>
                    </record>
                    <record id="mod_303_casilla_68" model="account.report.line">
                        <field name="name">[68] Result of the annual regularization</field>
                        <field name="code">aeat_mod_303_68</field>
                        <field name="expression_ids">
                            <record id="mod_303_casilla_68_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">external</field>
                                <field name="formula">sum</field>
                                <field name="subformula">editable;rounding=2</field>
                            </record>
                        </field>
                    </record>
                    <record id="mod_303_casilla_108" model="account.report.line">
                        <field name="name">[108] Exclusively for certain cases of corrective self-assessment due to a discrepancy in administrative criteria that should not be included in other boxes. Other adjustments</field>
                        <field name="code">aeat_mod_303_108</field>
                        <field name="expression_ids">
                            <record id="mod_303_casilla_108_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">external</field>
                                <field name="formula">sum</field>
                                <field name="subformula">editable;rounding=2</field>
                            </record>
                        </field>
                    </record>
                    <record id="mod_303_casilla_69" model="account.report.line">
                        <field name="name">[69] Result of self-liquidation</field>
                        <field name="code">aeat_mod_303_69</field>
                        <field name="aggregation_formula">aeat_mod_303_66.balance + aeat_mod_303_77.balance - aeat_mod_303_78.balance + aeat_mod_303_68.balance + aeat_mod_303_108.balance</field>
                    </record>
                    <record id="mod_303_casilla_70" model="account.report.line">
                        <field name="name">[70] Results to be paid from previous self-assessments or administrative liquidations corresponding to the year and period of self-assessment</field>
                        <field name="code">aeat_mod_303_70</field>
                        <field name="expression_ids">
                            <record id="mod_303_casilla_70_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">external</field>
                                <field name="formula">sum</field>
                                <field name="subformula">editable;rounding=2</field>
                            </record>
                        </field>
                    </record>
                    <record id="mod_303_casilla_109" model="account.report.line">
                        <field name="name">[109] Refunds agreed by the Tax Agency as a consequence of the processing of previous self-assessments corresponding to the year and period of the self-assessment</field>
                        <field name="code">aeat_mod_303_109</field>
                        <field name="expression_ids">
                            <record id="mod_303_casilla_109_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">external</field>
                                <field name="formula">sum</field>
                                <field name="subformula">editable;rounding=2</field>
                            </record>
                        </field>
                    </record>
                    <record id="mod_303_casilla_71" model="account.report.line">
                        <field name="name">[71] Result of settlement</field>
                        <field name="code">aeat_mod_303_71</field>
                        <field name="expression_ids">
                            <record id="mod_303_casilla_71_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">aeat_mod_303_69.balance - aeat_mod_303_70.balance + aeat_mod_303_109.balance</field>
                            </record>
                        </field>
                    </record>
                    <record id="mod_303_title_19" model="account.report.line">
                        <field name="name">Amount of the purchases of goods and services to which the special regime of cash criterion is applicable or affected</field>
                        <field name="code">aeat_mod_303_title_19</field>
                        <field name="children_ids">
                            <record id="mod_303_casilla_74" model="account.report.line">
                                <field name="name">[74] Base taxable</field>
                                <field name="code">aeat_mod_303_74</field>
                                <field name="expression_ids">
                                    <record id="mod_303_casilla_74_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=2</field>
                                    </record>
                                </field>
                            </record>
                            <record id="mod_303_casilla_75" model="account.report.line">
                                <field name="name">[75] Contributions</field>
                                <field name="code">aeat_mod_303_75</field>
                                <field name="expression_ids">
                                    <record id="mod_303_casilla_75_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=2</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="mod_303_casilla_77" model="account.report.line">
                        <field name="name">[77] Import VAT settled by custom pending entry</field>
                        <field name="code">aeat_mod_303_77</field>
                        <field name="expression_ids">
                            <record id="mod_303_casilla_77_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">external</field>
                                <field name="formula">sum</field>
                                <field name="subformula">editable;rounding=2</field>
                            </record>
                        </field>
                    </record>
                    <record id="mod_303_casilla_78" model="account.report.line">
                        <field name="name">[78] Contributions to be compensated from previous periods applied in this period</field>
                        <field name="code">aeat_mod_303_78</field>
                        <field name="expression_ids">
                            <record id="mod_303_casilla_78_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">external</field>
                                <field name="formula">sum</field>
                                <field name="subformula">editable;rounding=2</field>
                            </record>
                        </field>
                    </record>
                    <record id="mod_303_casilla_87" model="account.report.line">
                        <field name="name">[87] Contributions to be compensated from previous periods pending for subsequent periods</field>
                        <field name="code">aeat_mod_303_87</field>
                        <field name="aggregation_formula">aeat_mod_303_110.balance - aeat_mod_303_78.balance</field>
                    </record>
                    <record id="mod_303_casilla_110" model="account.report.line">
                        <field name="name">[110] Contributions to be compensated pending from previous periods</field>
                        <field name="code">aeat_mod_303_110</field>
                        <field name="expression_ids">
                            <record id="mod_303_casilla_110_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">external</field>
                                <field name="formula">sum</field>
                                <field name="subformula">editable;rounding=2</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="mod_303_title_20" model="account.report.line">
                <field name="name">Rectification</field>
                <field name="code">aeat_mod_303_title_20</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="mod_303_casilla_111" model="account.report.line">
                        <field name="name">[111] Rectification - Amount</field>
                        <field name="code">aeat_mod_303_111</field>
                        <field name="expression_ids">
                            <record id="mod_303_casilla_111_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">external</field>
                                <field name="formula">sum</field>
                                <field name="subformula">editable;rounding=2</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
        </field>
    </record>
</data>
</odoo>
