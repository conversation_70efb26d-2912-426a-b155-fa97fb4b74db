# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* gamification
# 
# Translators:
# <PERSON>il <PERSON>, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:41+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: gamification
#: model:mail.template,body_html:gamification.email_template_goal_reminder
msgid ""
"\n"
"    <strong>Reminder</strong><br/>\n"
"    You have not updated your progress for the goal <t t-out=\"object.definition_id.name or ''\"/> (currently reached at <t t-out=\"object.completeness or ''\"/>%) for at least <t t-out=\"object.remind_update_delay or ''\"/> days. Do not forget to do it.\n"
"    <br/><br/>\n"
"    Thank you,\n"
"    <t t-if=\"object.challenge_id.manager_id.signature\">\n"
"        <br/>\n"
"        <t t-out=\"object.challenge_id.manager_id.signature or ''\"/>\n"
"    </t>\n"
msgstr ""
"\n"
"    <strong>Nhắc nhở</strong><br/>\n"
"    Bạn chưa cập nhật tiến độ cho mục tiêu <t t-out=\"object.definition_id.name or ''\"/> (hiện đã đạt được <t t-out=\"object.completeness or ''\"/>%) trong ít nhất <t t-out=\"object.remind_update_delay or ''\"/> ngày. Đừng quên tiếp tục nhé.\n"
"    <br/><br/>\n"
"    Cảm ơn bạn!\n"
"    <t t-if=\"object.challenge_id.manager_id.signature\">\n"
"        <br/>\n"
"        <t t-out=\"object.challenge_id.manager_id.signature or ''\"/>\n"
"    </t>\n"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__user_count
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__rank_users_count
msgid "# Users"
msgstr "# Người dùng"

#. module: gamification
#. odoo-python
#: code:addons/gamification/models/gamification_challenge.py:0
msgid "%s has joined the challenge"
msgstr "%s đã tham gia thử thách"

#. module: gamification
#. odoo-python
#: code:addons/gamification/models/gamification_challenge.py:0
msgid "%s has refused the challenge"
msgstr "%s đã từ chối thử thách"

#. module: gamification
#: model:mail.template,body_html:gamification.mail_template_data_new_rank_reached
msgid ""
"<div style=\"color:#515166;padding:10px 0px;font-family:Arial,Helvetica,sans-serif;font-size:14px;\">\n"
"<table style=\"width:600px;margin:0px auto;background:white;border:1px solid #e1e1e1;\">\n"
"    <tbody>\n"
"        <tr>\n"
"            <td style=\"padding:15px 20px 10px 20px;\">\n"
"                <p>\n"
"                    Congratulations\n"
"                    <span t-out=\"object.name or ''\">Joel Willis</span>!\n"
"                </p>\n"
"                <p>\n"
"                    You just reached a new rank: <strong t-out=\"object.rank_id.name or ''\">Newbie</strong>\n"
"                </p>\n"
"                <t t-if=\"object.next_rank_id.name\">\n"
"                    <p>Continue your work to become a <strong t-out=\"object.next_rank_id.name or ''\">Student</strong>!</p>\n"
"                </t>\n"
"                <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                    <t t-set=\"gamification_redirection_data\" t-value=\"object.get_gamification_redirection_data()\"/>\n"
"                    <t t-foreach=\"gamification_redirection_data\" t-as=\"data\">\n"
"                        <t t-set=\"url\" t-value=\"data['url']\"/>\n"
"                        <t t-set=\"label\" t-value=\"data['label']\"/>\n"
"                        <a t-att-href=\"url\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\" t-out=\"label or ''\">LABEL</a>\n"
"                    </t>\n"
"                </div>\n"
"            </td>\n"
"            <td style=\"padding:15px 20px 10px 20px;\">\n"
"                <p style=\"text-align: center;\">\n"
"                    <img t-attf-src=\"/web/image/gamification.karma.rank/{{ object.rank_id.id }}/image_128\"/>\n"
"                </p>\n"
"            </td>\n"
"        </tr>\n"
"        <tr>\n"
"            <td t-if=\"not user.share and user.active\" style=\"padding:15px 20px 10px 20px;\" data-o-mail-quote-container=\"1\">\n"
"                <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"            </td>\n"
"            <td t-else=\"\" style=\"padding:15px 20px 10px 20px;\">\n"
"                <t t-out=\"user.company_id.name or ''\"/>\n"
"            </td>\n"
"        </tr>\n"
"    </tbody>\n"
" </table>\n"
"</div>"
msgstr ""
"<div style=\"color:#515166;padding:10px 0px;font-family:Arial,Helvetica,sans-serif;font-size:14px;\">\n"
"<table style=\"width:600px;margin:0px auto;background:white;border:1px solid #e1e1e1;\">\n"
"    <tbody>\n"
"        <tr>\n"
"            <td style=\"padding:15px 20px 10px 20px;\">\n"
"                <p>\n"
"                    Xin chúc mừng\n"
"                    <span t-out=\"object.name or ''\">Joel Willis</span>!\n"
"                </p>\n"
"                <p>\n"
"                    Bạn đã đạt được thứ hạng mới: <strong t-out=\"object.rank_id.name or ''\">Gương mặt mới</strong>\n"
"                </p>\n"
"                <t t-if=\"object.next_rank_id.name\">\n"
"                    <p>Hãy cố gắng để đạt thứ hạng <strong t-out=\"object.next_rank_id.name or ''\">Học viên</strong>!</p>\n"
"                </t>\n"
"                <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                    <t t-set=\"gamification_redirection_data\" t-value=\"object.get_gamification_redirection_data()\"/>\n"
"                    <t t-foreach=\"gamification_redirection_data\" t-as=\"data\">\n"
"                        <t t-set=\"url\" t-value=\"data['url']\"/>\n"
"                        <t t-set=\"label\" t-value=\"data['label']\"/>\n"
"                        <a t-att-href=\"url\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\" t-out=\"label or ''\">NHÃN</a>\n"
"                    </t>\n"
"                </div>\n"
"            </td>\n"
"            <td style=\"padding:15px 20px 10px 20px;\">\n"
"                <p style=\"text-align: center;\">\n"
"                    <img t-attf-src=\"/web/image/gamification.karma.rank/{{ object.rank_id.id }}/image_128\"/>\n"
"                </p>\n"
"            </td>\n"
"        </tr>\n"
"        <tr>\n"
"            <td t-if=\"not user.share and user.active\" style=\"padding:15px 20px 10px 20px;\" data-o-mail-quote-container=\"1\">\n"
"                <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"            </td>\n"
"            <td t-else=\"\" style=\"padding:15px 20px 10px 20px;\">\n"
"                <t t-out=\"user.company_id.name or ''\"/>\n"
"            </td>\n"
"        </tr>\n"
"    </tbody>\n"
" </table>\n"
"</div>"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_kanban_view
msgid ""
"<i role=\"img\" class=\"text-700 fa fa-clock-o fa-3x\" title=\"Goal in "
"Progress\" aria-label=\"Goal in Progress\"/>"
msgstr ""
"<i role=\"img\" class=\"text-700 fa fa-clock-o fa-3x\" title=\"Mục tiêu đang"
" thực hiện\" aria-label=\"Mục tiêu đang thực hiện\"/>"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_kanban_view
msgid ""
"<i role=\"img\" class=\"text-danger fa fa-times fa-3x\" title=\"Goal "
"Failed\" aria-label=\"Goal Failed\"/>"
msgstr ""
"<i role=\"img\" class=\"text-danger fa fa-times fa-3x\" title=\"Mục tiêu "
"không thành công\" aria-label=\"Mục tiêu không thành công\"/>"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_kanban_view
msgid ""
"<i role=\"img\" class=\"text-success fa fa-check fa-3x\" title=\"Goal "
"Reached\" aria-label=\"Goal Reached\"/>"
msgstr ""
"<i role=\"img\" class=\"text-success fa fa-check fa-3x\" title=\"Mục tiêu đã"
" đạt được\" aria-label=\"Mục tiêu đã đạt được\"/>"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "<span class=\"o_stat_text\">Related Goals</span>"
msgstr "<span class=\"o_stat_text\">Mục tiêu liên quan</span>"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_rank_view_form
msgid "<span class=\"o_stat_text\">Users</span>"
msgstr "<span class=\"o_stat_text\">Người dùng</span>"

#. module: gamification
#: model:mail.template,body_html:gamification.email_template_badge_received
msgid ""
"<table border=\"0\" cellpadding=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" width=\"590\" cellpadding=\"0\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\" summary=\"o_mail_notification\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Your Badge</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.badge_id.name or ''\"/>\n"
"                </td><td valign=\"middle\" align=\"right\" t-if=\"not object.user_id.company_id.uses_default_logo\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ object.user_id.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.user_id.company_id.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <div>\n"
"                        Congratulations <t t-out=\"object.user_id.name or ''\"/>!<br/>\n"
"                        You just received badge <strong t-out=\"object.badge_id.name or ''\"/>!<br/>\n"
"                        <table t-if=\"not is_html_empty(object.badge_id.description)\" cellspacing=\"0\" cellpadding=\"0\" border=\"0\" style=\"width: 560px; margin-top: 5px;\">\n"
"                            <tbody><tr>\n"
"                                <td valign=\"center\">\n"
"                                    <img t-attf-src=\"/web/image/gamification.badge/{{ object.badge_id.id }}/image_128/80x80\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"user.company_id.name\"/>\n"
"                                </td>\n"
"                                <td valign=\"center\">\n"
"                                    <cite t-out=\"object.badge_id.description or ''\"/>\n"
"                                </td>\n"
"                            </tr></tbody>\n"
"                        </table>\n"
"                        <br/>\n"
"                        <t t-if=\"object.sender_id\">\n"
"                            This badge was granted by <strong t-out=\"object.sender_id.name or ''\"/>.\n"
"                        </t>\n"
"                        <br/>\n"
"                        <t t-if=\"object.comment\" t-out=\"object.comment or ''\"/>\n"
"                        <br/><br/>\n"
"                        Thank you,\n"
"                        <t t-if=\"object.sender_id.signature\">\n"
"                            <br/>\n"
"                            <t t-out=\"object.sender_id.signature or ''\"/>\n"
"                        </t>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" style=\"min-width: 590px; background-color: white; font-size: 12px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"object.user_id.company_id.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"object.user_id.company_id.phone or ''\">******-123-4567</t>\n"
"                    <t t-if=\"object.user_id.company_id.email\">\n"
"                        | <a t-attf-href=\"'mailto:%s' % {{ object.user_id.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.user_id.company_id.email or ''\"><EMAIL></a>\n"
"                    </t>\n"
"                    <t t-if=\"object.user_id.company_id.website\">\n"
"                        | <a t-attf-href=\"'%s' % {{ object.user_id.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.user_id.company_id.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table width=\"590\" border=\"0\" cellpadding=\"0\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 14px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=gamification\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>"
msgstr ""
"<table border=\"0\" cellpadding=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" width=\"590\" cellpadding=\"0\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\" summary=\"o_mail_notification\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Huy hiệu của bạn</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.badge_id.name or ''\"/>\n"
"                </td><td valign=\"middle\" align=\"right\" t-if=\"not object.user_id.company_id.uses_default_logo\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ object.user_id.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.user_id.company_id.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <div>\n"
"                        Xin chúc mừng <t t-out=\"object.user_id.name or ''\"/>!<br/>\n"
"                        Bạn vừa nhận được huy hiệu <strong t-out=\"object.badge_id.name or ''\"/>!<br/>\n"
"                        <table t-if=\"not is_html_empty(object.badge_id.description)\" cellspacing=\"0\" cellpadding=\"0\" border=\"0\" style=\"width: 560px; margin-top: 5px;\">\n"
"                            <tbody><tr>\n"
"                                <td valign=\"center\">\n"
"                                    <img t-attf-src=\"/web/image/gamification.badge/{{ object.badge_id.id }}/image_128/80x80\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"user.company_id.name\"/>\n"
"                                </td>\n"
"                                <td valign=\"center\">\n"
"                                    <cite t-out=\"object.badge_id.description or ''\"/>\n"
"                                </td>\n"
"                            </tr></tbody>\n"
"                        </table>\n"
"                        <br/>\n"
"                        <t t-if=\"object.sender_id\">\n"
"                            Huy hiệu này được trao bởi <strong t-out=\"object.sender_id.name or ''\"/>.\n"
"                        </t>\n"
"                        <br/>\n"
"                        <t t-if=\"object.comment\" t-out=\"object.comment or ''\"/>\n"
"                        <br/><br/>\n"
"                        Cảm ơn bạn!\n"
"                        <t t-if=\"object.sender_id.signature\">\n"
"                            <br/>\n"
"                            <t t-out=\"object.sender_id.signature or ''\"/>\n"
"                        </t>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" style=\"min-width: 590px; background-color: white; font-size: 12px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"object.user_id.company_id.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"object.user_id.company_id.phone or ''\">******-123-4567</t>\n"
"                    <t t-if=\"object.user_id.company_id.email\">\n"
"                        | <a t-attf-href=\"'mailto:%s' % {{ object.user_id.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.user_id.company_id.email or ''\"><EMAIL></a>\n"
"                    </t>\n"
"                    <t t-if=\"object.user_id.company_id.website\">\n"
"                        | <a t-attf-href=\"'%s' % {{ object.user_id.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.user_id.company_id.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table width=\"590\" border=\"0\" cellpadding=\"0\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 14px;\">\n"
"        Cung cấp bởi <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=gamification\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>"

#. module: gamification
#: model:mail.template,body_html:gamification.simple_report_template
msgid ""
"<table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"background-color: #EEE; border-collapse: collapse;\">\n"
"<tr>\n"
"    <td valign=\"top\" align=\"center\">\n"
"        <t t-set=\"object_ctx\" t-value=\"ctx.get('object')\"/>\n"
"        <t t-set=\"company\" t-value=\"object_ctx and object_ctx.company_id or user.company_id\"/>\n"
"        <t t-set=\"challenge_lines\" t-value=\"ctx.get('challenge_lines', [])\"/>\n"
"        <table cellspacing=\"0\" cellpadding=\"0\" width=\"600\" style=\"margin: 0 auto; width: 570px;\">\n"
"            <tr><td>\n"
"                <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\">\n"
"                    <tr>\n"
"                        <div>\n"
"                            <t t-if=\"object.visibility_mode == 'ranking'\">\n"
"                                <td style=\"padding:15px;\">\n"
"                                    <p style=\"font-size:20px;color:#666666;\" align=\"center\">Leaderboard</p>\n"
"                                </td>\n"
"                            </t>\n"
"                        </div>\n"
"                    </tr>\n"
"                </table>\n"
"                <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" bgcolor=\"#fff\" style=\"background-color:#fff;\">\n"
"                    <tr><td style=\"padding: 15px;\">\n"
"                        <t t-if=\"object.visibility_mode == 'personal'\">\n"
"                            <span style=\"color:#666666;font-size:13px;\">Here is your current progress in the challenge <strong t-out=\"object.name or ''\"/>.</span>\n"
"                            <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"margin-top:20px;\">\n"
"                                <tr>\n"
"                                    <td align=\"center\">\n"
"                                        <div>Personal Performance</div>\n"
"                                    </td>\n"
"                                </tr>\n"
"                            </table>\n"
"                            <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"margin-top:30px;color:#666666;\">\n"
"                                <thead>\n"
"                                    <tr style=\"color:#9A6C8E; font-size:12px;\">\n"
"                                        <th align=\"left\" style=\"padding-bottom: 0px;width:40%;text-align:left;\">Goals</th>\n"
"                                        <th style=\"width:20%;text-align:right;\" align=\"left\">Target</th>\n"
"                                        <th style=\"width:20%;text-align:right;\" align=\"right\">Current</th>\n"
"                                        <th style=\"width:20%;text-align:right;\" align=\"right\">Completeness</th>\n"
"                                    </tr>\n"
"                                    <tr>\n"
"                                        <td colspan=\"5\" style=\"height:1px;background-color:#9A6C8E;\"/>\n"
"                                    </tr>\n"
"                                </thead>\n"
"                                <tbody t-foreach=\"challenge_lines\" t-as=\"line\">\n"
"                                    <tr style=\"font-weight:bold;\">\n"
"                                        <td style=\"padding: 20px 0;\" align=\"left\">\n"
"                                            <t t-out=\"line['name'] or ''\"/>\n"
"                                            <t t-if=\"line['suffix'] or line['monetary']\">\n"
"                                                (<t t-out=\"line['full_suffix'] or ''\"/>)\n"
"                                            </t>\n"
"                                        </td>\n"
"                                        <td style=\"padding: 20px 0;\" align=\"right\"><t t-out=\"&quot;%.2f&quot; % line['target'] or ''\"/>\n"
"                                            <t t-if=\"line['suffix']\" t-out=\"line['suffix'] or ''\"/>\n"
"                                        </td>\n"
"                                        <td style=\"padding: 20px 0;\" align=\"right\"><t t-out=\"&quot;%.2f&quot; % line['current'] or ''\"/>\n"
"                                            <t t-if=\"line['suffix']\" t-out=\"line['suffix'] or ''\"/>\n"
"                                        </td>\n"
"                                        <td style=\"padding: 20px 0;font-size:25px;color:#9A6C8E;\" align=\"right\"><strong><t t-out=\"int(line['completeness']) or ''\"/>%</strong></td>\n"
"                                    </tr>\n"
"                                    <tr>\n"
"                                        <td colspan=\"5\" style=\"height:1px;background-color:#e3e3e3;\"/>\n"
"                                    </tr>\n"
"                                </tbody>\n"
"                            </table>                   \n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <span style=\"color:#A8A8A8;font-size:13px;\">\n"
"                                Challenge: <strong t-out=\"object.name or ''\"/>.\n"
"                            </span> \n"
"                            <t t-foreach=\"challenge_lines\" t-as=\"line\">\n"
"                                <!-- Header + Button table -->\n"
"                                <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"margin-top:35px;\">\n"
"                                    <tr>\n"
"                                        <td width=\"50%\">\n"
"                                            <div>Top Achievers for goal <strong t-out=\"line['name'] or ''\"/></div>\n"
"                                        </td>\n"
"                                    </tr>\n"
"                                </table>\n"
"                                <!-- Podium -->\n"
"                                <t t-if=\"len(line['goals']) == 2\">\n"
"                                    <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"margin-top:10px;\">\n"
"                                        <tr><td style=\"padding:0 30px;\">\n"
"                                            <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"table-layout: fixed;\">\n"
"                                                <tr>\n"
"                                                    <t t-set=\"top_goals\" t-value=\"line['goals'][:3]\"/>\n"
"                                                    <t t-foreach=\"top_goals\" t-as=\"goal\">\n"
"                                                        <td align=\"center\" style=\"width:32%;\">\n"
"                                                            <t t-if=\"loop.index == 1\">\n"
"                                                                <t t-set=\"extra_div\" t-value=\"'&lt;div style=&quot;height:40px;&quot;&gt;&lt;/div&gt;'\"/>\n"
"                                                                <t t-set=\"heightA\" t-value=\"95\"/>\n"
"                                                                <t t-set=\"heightB\" t-value=\"75\"/>\n"
"                                                                <t t-set=\"bgColor\" t-value=\"'#b898b0'\"/>\n"
"                                                                <t t-set=\"fontSize\" t-value=\"50\"/>\n"
"                                                                <t t-set=\"podiumPosition\" t-value=\"'2'\"/>\n"
"                                                            </t>\n"
"                                                            <t t-elif=\"loop.index == 2\">\n"
"                                                                <t t-set=\"extra_div\" t-value=\"''\"/>\n"
"                                                                <t t-set=\"heightA\" t-value=\"55\"/>\n"
"                                                                <t t-set=\"heightB\" t-value=\"115\"/>\n"
"                                                                <t t-set=\"bgColor\" t-value=\"'#9A6C8E'\"/>\n"
"                                                                <t t-set=\"fontSize\" t-value=\"85\"/>\n"
"                                                                <t t-set=\"podiumPosition\" t-value=\"'1'\"/>\n"
"                                                            </t>\n"
"                                                            <t t-elif=\"loop.index == 3\">\n"
"                                                                <t t-set=\"extra_div\" t-value=\"'&lt;div style=&quot;height:60px;&quot;&gt;&lt;/div&gt;'\"/>\n"
"                                                                <t t-set=\"heightA\" t-value=\"115\"/>\n"
"                                                                <t t-set=\"heightB\" t-value=\"55\"/>\n"
"                                                                <t t-set=\"bgColor\" t-value=\"'#c8afc1'\"/>\n"
"                                                                <t t-set=\"fontSize\" t-value=\"35\"/>\n"
"                                                                <t t-set=\"podiumPosition\" t-value=\"'3'\"/>\n"
"                                                            </t>\n"
"                                                            <div style=\"margin:0 3px 0 3px;height:220px;\">\n"
"                                                                <div t-attf-style=\"height:{{ heightA }}px;\">\n"
"                                                                    <t t-out=\"extra_div or ''\"/>\n"
"                                                                    <div style=\"height:55px;\">\n"
"                                                                        <img style=\"margin-bottom:5px;width:50px;height:50px;border-radius:50%;object-fit:cover;\" t-att-src=\"image_data_uri(object.env['res.users'].browse(goal['user_id']).partner_id.image_128)\" t-att-alt=\"goal['name']\"/>\n"
"                                                                    </div>\n"
"                                                                    <div align=\"center\" t-attf-style=\"color:{{ bgColor }};height:20px\">\n"
"                                                                        <t t-out=\"goal['name'] or ''\"/>\n"
"                                                                    </div>\n"
"                                                                </div>\n"
"                                                                <div t-attf-style=\"background-color:{{ bgColor }};height:{{ heightB }}px;\">\n"
"                                                                    <strong><span t-attf-style=\"color:#fff;font-size:{{ fontSize }}px;\" t-out=\"podiumPosition or ''\"/></strong>\n"
"                                                                </div>\n"
"                                                                <div style=\"height:30px;\">\n"
"                                                                    <t t-out=\"&quot;%.2f&quot; % goal['current'] or ''\"/>\n"
"                                                                    <t t-if=\"line['suffix'] or line['monetary']\">\n"
"                                                                        <t t-out=\"line['full_suffix'] or ''\"/>\n"
"                                                                    </t>\n"
"                                                                </div>\n"
"                                                            </div>\n"
"                                                        </td>\n"
"                                                    </t>\n"
"                                                </tr>\n"
"                                            </table>\n"
"                                            </td>\n"
"                                        </tr>\n"
"                                    </table>\n"
"                                </t>\n"
"                                <!-- data table -->\n"
"                                <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"margin-bottom:5px\">\n"
"                                    <tr>\n"
"                                        <td>\n"
"                                            <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"margin-top:30px;margin-bottom:5px;color:#666666;\">\n"
"                                                <thead>\n"
"                                                    <tr style=\"color:#9A6C8E; font-size:12px;\">\n"
"                                                        <th style=\"width:15%;text-align:center;\">Rank</th>\n"
"                                                        <th style=\"width:25%;text-align:left;\">Name</th>\n"
"                                                        <th style=\"width:30%;text-align:right;\">Performance \n"
"                                                            <t t-if=\"line['suffix']\">\n"
"                                                                (<t t-out=\"line['suffix'] or ''\"/>)\n"
"                                                            </t>\n"
"                                                            <t t-elif=\"line['monetary']\">\n"
"                                                                (<t t-out=\"company.currency_id.symbol or ''\"/>)\n"
"                                                            </t>\n"
"                                                        </th>\n"
"                                                        <th style=\"width:30%;text-align:right;\">Completeness</th>\n"
"                                                    </tr>\n"
"                                                    <tr>\n"
"                                                        <td colspan=\"5\" style=\"height:1px;background-color:#9A6C8E;\"/>\n"
"                                                    </tr>\n"
"                                                </thead>\n"
"                                                <tbody t-foreach=\"line['goals']\" t-as=\"goal\">\n"
"                                                    <tr>\n"
"                                                        <t t-set=\"tdBgColor\" t-value=\"'#fff'\"/>\n"
"                                                        <t t-set=\"tdColor\" t-value=\"'gray'\"/>\n"
"                                                        <t t-set=\"mutedColor\" t-value=\"'#AAAAAA'\"/>\n"
"                                                        <t t-set=\"tdPercentageColor\" t-value=\"'#9A6C8E'\"/>\n"
"                                                        <td width=\"15%\" align=\"center\" valign=\"middle\" t-attf-style=\"background-color:{{ tdBgColor }};padding :5px 0;font-size:20px;\"><t t-out=\"goal['rank']+1 or ''\"/>\n"
"                                                        </td>\n"
"                                                        <td width=\"25%\" align=\"left\" valign=\"middle\" t-attf-style=\"background-color:{{ tdBgColor }};padding :5px 0;font-size:13px;\"><t t-out=\"goal['name'] or ''\"/></td>\n"
"                                                        <td width=\"30%\" align=\"right\" t-attf-style=\"background-color:{{ tdBgColor }};padding:5px 0;line-height:1;\"><t t-out=\"&quot;%.2f&quot; % goal['current'] or ''\"/><br/><span t-attf-style=\"font-size:13px;color:{{ mutedColor }};\">on <t t-out=\"&quot;%.2f&quot; % line['target'] or ''\"/></span>\n"
"                                                        </td>\n"
"                                                        <td width=\"30%\" t-attf-style=\"color:{{ tdPercentageColor }};background-color:{{ tdBgColor }};padding-right:15px;font-size:22px;\" align=\"right\"><strong><t t-out=\"int(goal['completeness']) or ''\"/>%</strong></td>\n"
"                                                    </tr>\n"
"                                                    <tr>\n"
"                                                        <td colspan=\"5\" style=\"height:1px;background-color:#DADADA;\"/>\n"
"                                                    </tr>\n"
"                                                </tbody>\n"
"                                            </table>\n"
"                                        </td>\n"
"                                    </tr>\n"
"                                </table> \n"
"                            </t>\n"
"                        </t>\n"
"                    </td></tr>\n"
"                </table>\n"
"            </td></tr>\n"
"        </table>\n"
"    </td>\n"
"</tr>\n"
"</table>\n"
"            "
msgstr ""
"<table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"background-color: #EEE; border-collapse: collapse;\">\n"
"<tr>\n"
"    <td valign=\"top\" align=\"center\">\n"
"        <t t-set=\"object_ctx\" t-value=\"ctx.get('object')\"/>\n"
"        <t t-set=\"company\" t-value=\"object_ctx and object_ctx.company_id or user.company_id\"/>\n"
"        <t t-set=\"challenge_lines\" t-value=\"ctx.get('challenge_lines', [])\"/>\n"
"        <table cellspacing=\"0\" cellpadding=\"0\" width=\"600\" style=\"margin: 0 auto; width: 570px;\">\n"
"            <tr><td>\n"
"                <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\">\n"
"                    <tr>\n"
"                        <div>\n"
"                            <t t-if=\"object.visibility_mode == 'ranking'\">\n"
"                                <td style=\"padding:15px;\">\n"
"                                    <p style=\"font-size:20px;color:#666666;\" align=\"center\">Bảng xếp hạng</p>\n"
"                                </td>\n"
"                            </t>\n"
"                        </div>\n"
"                    </tr>\n"
"                </table>\n"
"                <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" bgcolor=\"#fff\" style=\"background-color:#fff;\">\n"
"                    <tr><td style=\"padding: 15px;\">\n"
"                        <t t-if=\"object.visibility_mode == 'personal'\">\n"
"                            <span style=\"color:#666666;font-size:13px;\">Đây là tiến độ của bạn trong thử thách <strong t-out=\"object.name or ''\"/> này.</span>\n"
"                            <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"margin-top:20px;\">\n"
"                                <tr>\n"
"                                    <td align=\"center\">\n"
"                                        <div>Hiệu suất cá nhân</div>\n"
"                                    </td>\n"
"                                </tr>\n"
"                            </table>\n"
"                            <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"margin-top:30px;color:#666666;\">\n"
"                                <thead>\n"
"                                    <tr style=\"color:#9A6C8E; font-size:12px;\">\n"
"                                        <th align=\"left\" style=\"padding-bottom: 0px;width:40%;text-align:left;\">Mục tiêu</th>\n"
"                                        <th style=\"width:20%;text-align:right;\" align=\"left\">Chỉ tiêu</th>\n"
"                                        <th style=\"width:20%;text-align:right;\" align=\"right\">Hiện tại</th>\n"
"                                        <th style=\"width:20%;text-align:right;\" align=\"right\">Mức hoàn thành</th>\n"
"                                    </tr>\n"
"                                    <tr>\n"
"                                        <td colspan=\"5\" style=\"height:1px;background-color:#9A6C8E;\"/>\n"
"                                    </tr>\n"
"                                </thead>\n"
"                                <tbody t-foreach=\"challenge_lines\" t-as=\"line\">\n"
"                                    <tr style=\"font-weight:bold;\">\n"
"                                        <td style=\"padding: 20px 0;\" align=\"left\">\n"
"                                            <t t-out=\"line['name'] or ''\"/>\n"
"                                            <t t-if=\"line['suffix'] or line['monetary']\">\n"
"                                                (<t t-out=\"line['full_suffix'] or ''\"/>)\n"
"                                            </t>\n"
"                                        </td>\n"
"                                        <td style=\"padding: 20px 0;\" align=\"right\"><t t-out=\"&quot;%.2f&quot; % line['target'] or ''\"/>\n"
"                                            <t t-if=\"line['suffix']\" t-out=\"line['suffix'] or ''\"/>\n"
"                                        </td>\n"
"                                        <td style=\"padding: 20px 0;\" align=\"right\"><t t-out=\"&quot;%.2f&quot; % line['current'] or ''\"/>\n"
"                                            <t t-if=\"line['suffix']\" t-out=\"line['suffix'] or ''\"/>\n"
"                                        </td>\n"
"                                        <td style=\"padding: 20px 0;font-size:25px;color:#9A6C8E;\" align=\"right\"><strong><t t-out=\"int(line['completeness']) or ''\"/>%</strong></td>\n"
"                                    </tr>\n"
"                                    <tr>\n"
"                                        <td colspan=\"5\" style=\"height:1px;background-color:#e3e3e3;\"/>\n"
"                                    </tr>\n"
"                                </tbody>\n"
"                            </table>                   \n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <span style=\"color:#A8A8A8;font-size:13px;\">\n"
"                                Thử thách: <strong t-out=\"object.name or ''\"/>.\n"
"                            </span> \n"
"                            <t t-foreach=\"challenge_lines\" t-as=\"line\">\n"
"                                <!-- Header + Button table -->\n"
"                                <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"margin-top:35px;\">\n"
"                                    <tr>\n"
"                                        <td width=\"50%\">\n"
"                                            <div>Top đầu hoàn thành mục đích <strong t-out=\"line['name'] or ''\"/></div>\n"
"                                        </td>\n"
"                                    </tr>\n"
"                                </table>\n"
"                                <!-- Podium -->\n"
"                                <t t-if=\"len(line['goals']) == 2\">\n"
"                                    <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"margin-top:10px;\">\n"
"                                        <tr><td style=\"padding:0 30px;\">\n"
"                                            <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"table-layout: fixed;\">\n"
"                                                <tr>\n"
"                                                    <t t-set=\"top_goals\" t-value=\"line['goals'][:3]\"/>\n"
"                                                    <t t-foreach=\"top_goals\" t-as=\"goal\">\n"
"                                                        <td align=\"center\" style=\"width:32%;\">\n"
"                                                            <t t-if=\"loop.index == 1\">\n"
"                                                                <t t-set=\"extra_div\" t-value=\"'&lt;div style=&quot;height:40px;&quot;&gt;&lt;/div&gt;'\"/>\n"
"                                                                <t t-set=\"heightA\" t-value=\"95\"/>\n"
"                                                                <t t-set=\"heightB\" t-value=\"75\"/>\n"
"                                                                <t t-set=\"bgColor\" t-value=\"'#b898b0'\"/>\n"
"                                                                <t t-set=\"fontSize\" t-value=\"50\"/>\n"
"                                                                <t t-set=\"podiumPosition\" t-value=\"'2'\"/>\n"
"                                                            </t>\n"
"                                                            <t t-elif=\"loop.index == 2\">\n"
"                                                                <t t-set=\"extra_div\" t-value=\"''\"/>\n"
"                                                                <t t-set=\"heightA\" t-value=\"55\"/>\n"
"                                                                <t t-set=\"heightB\" t-value=\"115\"/>\n"
"                                                                <t t-set=\"bgColor\" t-value=\"'#9A6C8E'\"/>\n"
"                                                                <t t-set=\"fontSize\" t-value=\"85\"/>\n"
"                                                                <t t-set=\"podiumPosition\" t-value=\"'1'\"/>\n"
"                                                            </t>\n"
"                                                            <t t-elif=\"loop.index == 3\">\n"
"                                                                <t t-set=\"extra_div\" t-value=\"'&lt;div style=&quot;height:60px;&quot;&gt;&lt;/div&gt;'\"/>\n"
"                                                                <t t-set=\"heightA\" t-value=\"115\"/>\n"
"                                                                <t t-set=\"heightB\" t-value=\"55\"/>\n"
"                                                                <t t-set=\"bgColor\" t-value=\"'#c8afc1'\"/>\n"
"                                                                <t t-set=\"fontSize\" t-value=\"35\"/>\n"
"                                                                <t t-set=\"podiumPosition\" t-value=\"'3'\"/>\n"
"                                                            </t>\n"
"                                                            <div style=\"margin:0 3px 0 3px;height:220px;\">\n"
"                                                                <div t-attf-style=\"height:{{ heightA }}px;\">\n"
"                                                                    <t t-out=\"extra_div or ''\"/>\n"
"                                                                    <div style=\"height:55px;\">\n"
"                                                                        <img style=\"margin-bottom:5px;width:50px;height:50px;border-radius:50%;object-fit:cover;\" t-att-src=\"image_data_uri(object.env['res.users'].browse(goal['user_id']).partner_id.image_128)\" t-att-alt=\"goal['name']\"/>\n"
"                                                                    </div>\n"
"                                                                    <div align=\"center\" t-attf-style=\"color:{{ bgColor }};height:20px\">\n"
"                                                                        <t t-out=\"goal['name'] or ''\"/>\n"
"                                                                    </div>\n"
"                                                                </div>\n"
"                                                                <div t-attf-style=\"background-color:{{ bgColor }};height:{{ heightB }}px;\">\n"
"                                                                    <strong><span t-attf-style=\"color:#fff;font-size:{{ fontSize }}px;\" t-out=\"podiumPosition or ''\"/></strong>\n"
"                                                                </div>\n"
"                                                                <div style=\"height:30px;\">\n"
"                                                                    <t t-out=\"&quot;%.2f&quot; % goal['current'] or ''\"/>\n"
"                                                                    <t t-if=\"line['suffix'] or line['monetary']\">\n"
"                                                                        <t t-out=\"line['full_suffix'] or ''\"/>\n"
"                                                                    </t>\n"
"                                                                </div>\n"
"                                                            </div>\n"
"                                                        </td>\n"
"                                                    </t>\n"
"                                                </tr>\n"
"                                            </table>\n"
"                                            </td>\n"
"                                        </tr>\n"
"                                    </table>\n"
"                                </t>\n"
"                                <!-- data table -->\n"
"                                <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"margin-bottom:5px\">\n"
"                                    <tr>\n"
"                                        <td>\n"
"                                            <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"margin-top:30px;margin-bottom:5px;color:#666666;\">\n"
"                                                <thead>\n"
"                                                    <tr style=\"color:#9A6C8E; font-size:12px;\">\n"
"                                                        <th style=\"width:15%;text-align:center;\">Thứ hạng</th>\n"
"                                                        <th style=\"width:25%;text-align:left;\">Tên</th>\n"
"                                                        <th style=\"width:30%;text-align:right;\">Hiệu suất \n"
"                                                            <t t-if=\"line['suffix']\">\n"
"                                                                (<t t-out=\"line['suffix'] or ''\"/>)\n"
"                                                            </t>\n"
"                                                            <t t-elif=\"line['monetary']\">\n"
"                                                                (<t t-out=\"company.currency_id.symbol or ''\"/>)\n"
"                                                            </t>\n"
"                                                        </th>\n"
"                                                        <th style=\"width:30%;text-align:right;\">Mức hoàn thành</th>\n"
"                                                    </tr>\n"
"                                                    <tr>\n"
"                                                        <td colspan=\"5\" style=\"height:1px;background-color:#9A6C8E;\"/>\n"
"                                                    </tr>\n"
"                                                </thead>\n"
"                                                <tbody t-foreach=\"line['goals']\" t-as=\"goal\">\n"
"                                                    <tr>\n"
"                                                        <t t-set=\"tdBgColor\" t-value=\"'#fff'\"/>\n"
"                                                        <t t-set=\"tdColor\" t-value=\"'gray'\"/>\n"
"                                                        <t t-set=\"mutedColor\" t-value=\"'#AAAAAA'\"/>\n"
"                                                        <t t-set=\"tdPercentageColor\" t-value=\"'#9A6C8E'\"/>\n"
"                                                        <td width=\"15%\" align=\"center\" valign=\"middle\" t-attf-style=\"background-color:{{ tdBgColor }};padding :5px 0;font-size:20px;\"><t t-out=\"goal['rank']+1 or ''\"/>\n"
"                                                        </td>\n"
"                                                        <td width=\"25%\" align=\"left\" valign=\"middle\" t-attf-style=\"background-color:{{ tdBgColor }};padding :5px 0;font-size:13px;\"><t t-out=\"goal['name'] or ''\"/></td>\n"
"                                                        <td width=\"30%\" align=\"right\" t-attf-style=\"background-color:{{ tdBgColor }};padding:5px 0;line-height:1;\"><t t-out=\"&quot;%.2f&quot; % goal['current'] or ''\"/><br/><span t-attf-style=\"font-size:13px;color:{{ mutedColor }};\">trên <t t-out=\"&quot;%.2f&quot; % line['target'] or ''\"/></span>\n"
"                                                        </td>\n"
"                                                        <td width=\"30%\" t-attf-style=\"color:{{ tdPercentageColor }};background-color:{{ tdBgColor }};padding-right:15px;font-size:22px;\" align=\"right\"><strong><t t-out=\"int(goal['completeness']) or ''\"/>%</strong></td>\n"
"                                                    </tr>\n"
"                                                    <tr>\n"
"                                                        <td colspan=\"5\" style=\"height:1px;background-color:#DADADA;\"/>\n"
"                                                    </tr>\n"
"                                                </tbody>\n"
"                                            </table>\n"
"                                        </td>\n"
"                                    </tr>\n"
"                                </table> \n"
"                            </t>\n"
"                        </t>\n"
"                    </td></tr>\n"
"                </table>\n"
"            </td></tr>\n"
"        </table>\n"
"    </td>\n"
"</tr>\n"
"</table>\n"
"            "

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.badge_list_action
msgid ""
"A badge is a symbolic token granted to a user as a sign of reward.\n"
"                It can be deserved automatically when some conditions are met or manually by users.\n"
"                Some badges are harder than others to get with specific conditions."
msgstr ""
"Một huy hiệu được cấp cho người dùng như một phần thưởng.\n"
"                 Nó có thể cấp tự động khi một số điều kiện được đáp ứng hoặc bằng tay bởi người dùng.\n"
"                 Một số huy hiệu khó hơn với những điều kiện cụ thể."

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.goal_definition_list_action
msgid ""
"A goal definition is a technical specification of a condition to reach.\n"
"                The dates, values to reach or users are defined in goal instance."
msgstr ""
"Định nghĩa mục đích là một đặc điểm kỹ thuật của một điều kiện cần đạt được.\n"
"                Ngày, giá trị cần đạt hoặc người dùng được xác định trong phần mục đích."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge_line__condition
#: model:ir.model.fields,help:gamification.field_gamification_goal__definition_condition
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__condition
msgid ""
"A goal is considered as completed when the current value is compared to the "
"value to reach"
msgstr ""
"Mục tiêu được coi là hoàn thành khi giá trị hiện tại được so sánh với giá "
"trị cần đạt được"

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.goal_list_action
msgid ""
"A goal is defined by a user and a goal definition.\n"
"                Goals can be created automatically by using challenges."
msgstr ""
"Một mục tiêu được xác định bởi người dùng và định nghĩa mục tiêu.\n"
"                 Mục tiêu có thể được tạo ra tự động bằng cách sử dụng các thách thức."

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.gamification_karma_ranks_action
msgid ""
"A rank correspond to a fixed karma level. The more you have karma, the more your rank is high.\n"
"                    This is used to quickly know which user is new or old or highly or not active."
msgstr ""
"Thứ hạng tương ứng với một mức karma cố định. Điểm karma càng lớn, thứ hạng của bạn càng cao.\n"
"                    Đây là cách để nhận biết nhanh ai là người dùng mới, cũ, bậc cao hoặc không hoạt động."

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_badge__rule_auth__users
msgid "A selected list of users"
msgstr "Danh sách người dùng được chọn"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__action_id
msgid "Action"
msgstr "Tác vụ"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__message_needaction
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__message_needaction
msgid "Action Needed"
msgstr "Tác vụ cần thiết"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__active
msgid "Active"
msgstr "Đang hoạt động"

#. module: gamification
#. odoo-python
#: code:addons/gamification/models/gamification_karma_tracking.py:0
#: code:addons/gamification/models/res_users.py:0
#: code:addons/gamification/tests/test_karma_tracking.py:0
msgid "Add Manually"
msgstr "Thêm thủ công"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Advanced Options"
msgstr "Tùy chọn nâng cao"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__rule_auth
msgid "Allowance to Grant"
msgstr "Trợ cấp để cấp"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__challenge_category
msgid "Appears in"
msgstr "Xuất hiện ở"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
#: model_terms:ir.ui.view,arch_db:gamification.gamification_badge_view_search
msgid "Archived"
msgstr "Đã lưu trữ"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Assign Challenge to"
msgstr "Giao thử thách cho"

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.challenge_list_action
msgid ""
"Assign a list of goals to chosen users to evaluate them.\n"
"                The challenge can use a period (weekly, monthly...) for automatic creation of goals.\n"
"                The goals are created for the specified users or member of the group."
msgstr ""
"Gán một danh sách các mục tiêu đến những người dùng được chọn để đánh giá họ.\n"
"                    Thử thách có thể sử dụng một chu kỳ (hàng tuần, hàng tháng...) để tự động tạo các mục tiêu.\n"
"                    Các mục tiêu được tạo cho các người dùng cụ thể hoặc thành viên của nhóm."

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__message_attachment_count
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__message_attachment_count
msgid "Attachment Count"
msgstr "Số lượng tệp đính kèm"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__rule_auth_user_ids
msgid "Authorized Users"
msgstr "Người dùng được ủy quyền"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_goal_definition__computation_mode__python
msgid "Automatic: execute a specific Python code"
msgstr "Tự động: thực thi một lệnh python cụ thể"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_goal_definition__computation_mode__count
msgid "Automatic: number of records"
msgstr "Tự động: số lượng bản ghi"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_goal_definition__computation_mode__sum
msgid "Automatic: sum on a field"
msgstr "Tự động: tổng trên một trường"

#. module: gamification
#: model:gamification.karma.rank,name:gamification.rank_bachelor
msgid "Bachelor"
msgstr "Cử nhân"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__name
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user__badge_id
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_wizard__badge_id
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
msgid "Badge"
msgstr "Huy hiệu"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
msgid "Badge Description"
msgstr "Mô tả huy hiệu"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user__level
msgid "Badge Level"
msgstr "Cấp độ huy hiệu"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_list_view
msgid "Badge List"
msgstr "Danh sách huy hiệu"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user__badge_name
msgid "Badge Name"
msgstr "Tên huy hiệu"

#. module: gamification
#: model:ir.actions.act_window,name:gamification.badge_list_action
#: model:ir.model.fields,field_description:gamification.field_res_users__badge_ids
#: model:ir.ui.menu,name:gamification.gamification_badge_menu
msgid "Badges"
msgstr "Huy chương"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid ""
"Badges are granted when a challenge is finished. This is either at the end "
"of a running period (eg: end of the month for a monthly challenge), at the "
"end date of a challenge (if no periodicity is set) or when the challenge is "
"manually closed."
msgstr ""
"Huy hiệu được cấp khi một thách thức kết thúc. Đây là vào cuối giai đoạn "
"chạy (ví dụ: cuối tháng cho thử thách hàng tháng), vào ngày kết thúc thử "
"thách (nếu không đặt định kỳ) hoặc khi đóng thử thách."

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__batch_mode
msgid "Batch Mode"
msgstr "Chế độ theo lô"

#. module: gamification
#: model:gamification.badge,name:gamification.badge_idea
msgid "Brilliant"
msgstr "Tuyệt vời"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_badge__level__bronze
msgid "Bronze"
msgstr "Đồng"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_res_users__bronze_badge
msgid "Bronze badges count"
msgstr "Số huy hiệu đồng"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_kanban_view
msgid "Can not grant"
msgstr "Không thể trao"

#. module: gamification
#. odoo-python
#: code:addons/gamification/models/gamification_goal.py:0
msgid "Can not modify the configuration of a started goal"
msgstr "Không thể sửa đổi cấu hình của mục tiêu đã bắt đầu"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.view_badge_wizard_grant
#: model_terms:ir.ui.view,arch_db:gamification.view_goal_wizard_update_current
msgid "Cancel"
msgstr "Hủy"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_goal__state__canceled
msgid "Cancelled"
msgstr "Đã hủy"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Category"
msgstr "Danh mục"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user__challenge_id
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__challenge_id
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__challenge_id
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Challenge"
msgstr "Thử thách"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__line_id
msgid "Challenge Line"
msgstr "Chi tiết thử thách"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_line_list_view
msgid "Challenge Lines"
msgstr "Chi tiết thử thách"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__name
msgid "Challenge Name"
msgstr "Tên thử thách"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal__challenge_id
msgid ""
"Challenge that generated the goal, assign challenge to users to generate "
"goals with a value in this field."
msgstr ""
"Thử thách tạo ra mục tiêu, chỉ định thử thách cho người dùng để tạo mục tiêu"
" có giá trị trong trường này."

#. module: gamification
#: model:ir.actions.act_window,name:gamification.challenge_list_action
#: model:ir.ui.menu,name:gamification.gamification_challenge_menu
#: model_terms:ir.ui.view,arch_db:gamification.challenge_list_view
#: model_terms:ir.ui.view,arch_db:gamification.view_challenge_kanban
msgid "Challenges"
msgstr "Thử thách"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__rule_max
msgid "Check to set a monthly limit per person of sending this badge"
msgstr "Kiểm tra để thiết lập số lượng người tối đa nhận được huy hiệu này"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_form_view
msgid "Clickable Goals"
msgstr "Mục tiêu có thể nhấp"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__closed
msgid "Closed goal"
msgstr "Mục tiêu đã đóng"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__color
msgid "Color Index"
msgstr "Mã màu"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user__comment
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_wizard__comment
msgid "Comment"
msgstr "Bình luận"

#. module: gamification
#: model:gamification.challenge,name:gamification.challenge_base_discover
msgid "Complete your Profile"
msgstr "Hoàn thành hồ sơ"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__completeness
msgid "Completeness"
msgstr "Hoàn thành"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__computation_mode
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__computation_mode
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_search_view
msgid "Computation Mode"
msgstr "Chế độ tính toán"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__condition
msgid "Condition"
msgstr "Điều kiện"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_tracking__consolidated
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_tracking_view_search
msgid "Consolidated"
msgstr "Đã hợp nhất"

#. module: gamification
#. odoo-python
#: code:addons/gamification/models/gamification_karma_tracking.py:0
msgid "Consolidation from %(from_date)s to %(end_date)s"
msgstr "Hợp nhất từ %(from_date)s đến %(end_date)s"

#. module: gamification
#: model:ir.actions.act_window,name:gamification.action_new_simplified_res_users
msgid "Create User"
msgstr "Người tạo"

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.badge_list_action
msgid "Create a new badge"
msgstr "Tạo mới huy hiệu"

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.challenge_list_action
msgid "Create a new challenge"
msgstr "Tạo mới thử thách"

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.goal_list_action
msgid "Create a new goal"
msgstr "Tạo mới mục tiêu"

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.goal_definition_list_action
msgid "Create a new goal definition"
msgstr "Tạo mới định nghĩa mục tiêu"

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.gamification_karma_ranks_action
msgid "Create a new rank"
msgstr "Tạo thứ hạng mới"

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.action_new_simplified_res_users
msgid ""
"Create and manage users that will connect to the system. Users can be "
"deactivated should there be a period of time during which they will/should "
"not connect to the system. You can assign them groups in order to give them "
"specific access to the applications they need to use in the system."
msgstr ""
"Tạo và quản lý người dùng sẽ kết nối với hệ thống. Người dùng bị vô hiệu hoá"
" có thể có một khoảng thời gian không kết nối tới hệ thống. Bạn có thể gán "
"họ vào các nhóm để trao cho họ quyền truy cập vào ứng dụng mà họ cần sử dụng"
" trong hệ thống."

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__create_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user__create_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_wizard__create_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__create_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__create_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__create_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__create_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_wizard__create_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__create_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_tracking__create_uid
msgid "Created by"
msgstr "Được tạo bởi"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__create_date
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user__create_date
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_wizard__create_date
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__create_date
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__create_date
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__create_date
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__create_date
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_wizard__create_date
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__create_date
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_tracking__create_date
msgid "Created on"
msgstr "Được tạo vào"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_wizard__current
msgid "Current"
msgstr "Hiện tại"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__current
msgid "Current Value"
msgstr "Giá trị hiện tại"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__period__daily
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__report_message_frequency__daily
msgid "Daily"
msgstr "Hàng ngày"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "Data"
msgstr "Dữ liệu"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_tracking_view_tree
msgid "Date"
msgstr "Ngày"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__field_date_id
msgid "Date Field"
msgstr "Trường ngày"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal__computation_mode
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__computation_mode
msgid ""
"Define how the goals will be computed. The result of the operation will be "
"stored in the field 'Current'."
msgstr ""
"Xác định cách tính toán mục đích. Kết quả của hoạt động sẽ được lưu trong "
"trường 'Hiện tại'."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge__challenge_category
msgid "Define the visibility of the challenge through menus"
msgstr "Xác định khả năng hiển thị của thử thách thông qua các menu"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__definition_condition
msgid "Definition Condition"
msgstr "Điều kiện định nghĩa"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__definition_description
msgid "Definition Description"
msgstr "Miêu tả"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Depending on the Display mode, reports will be individual or shared."
msgstr ""
"Tùy thuộc vào chế độ hiển thị, các báo cáo sẽ là cá nhân hoặc được chia sẻ."

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid ""
"Describe the challenge: what is does, who it targets, why it matters..."
msgstr ""
"Mô tả thách thức: những gì nó làm, mục tiêu của ai, tại sao nó quan trọng "
"..."

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.view_badge_wizard_grant
msgid "Describe what they did and why it matters (will be public)"
msgstr "Mô tả cái chúng làm và tại sao nó quan trọng (sẽ mang tính công cộng)"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__description
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__description
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__description
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_tracking__reason
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_rank_view_form
msgid "Description"
msgstr "Mô tả"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__visibility_mode
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__definition_display
msgid "Display Mode"
msgstr "Chế độ hiển thị"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__display_name
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user__display_name
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_wizard__display_name
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__display_name
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__display_name
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__display_name
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__display_name
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_wizard__display_name
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__display_name
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_tracking__display_name
msgid "Display Name"
msgstr "Tên hiển thị"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__display_mode
msgid "Displayed as"
msgstr "Hiển thị"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__batch_distinctive_field
msgid "Distinctive field for batch user"
msgstr "Trường đặc biệt cho người dùng theo lô"

#. module: gamification
#: model:gamification.karma.rank,name:gamification.rank_doctor
msgid "Doctor"
msgstr "Tiến sĩ"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__domain
msgid ""
"Domain for filtering records. General rule, not user depending, e.g. "
"[('state', '=', 'done')]. The expression can contain reference to 'user' "
"which is a browse record of the current user if not in batch mode."
msgstr ""
"Tên miền để lọc bản ghi. Quy tắc chung, không phụ thuộc vào người dùng, ví "
"dụ: [('trạng thái', '=', 'xong')]. Biểu thức có thể chứa tham chiếu đến "
"'người dùng' là bản ghi duyệt của người dùng hiện tại nếu không ở chế độ "
"theo lô."

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__state__done
#: model_terms:ir.ui.view,arch_db:gamification.goal_search_view
msgid "Done"
msgstr "Hoàn tất"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__state__draft
#: model:ir.model.fields.selection,name:gamification.selection__gamification_goal__state__draft
#: model_terms:ir.ui.view,arch_db:gamification.goal_search_view
msgid "Draft"
msgstr "Nháp"

#. module: gamification
#: model_terms:gamification.karma.rank,description_motivational:gamification.rank_newbie
msgid "Earn your first points and join the adventure!"
msgstr "Hãy giành những điểm số đầu tiên và tham gia cuộc phiêu lưu!"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__end_date
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__end_date
#: model_terms:ir.ui.view,arch_db:gamification.goal_search_view
msgid "End Date"
msgstr "Ngày kết thúc"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__batch_mode
msgid "Evaluate the expression in batch instead of once for each user"
msgstr "Đánh giá biểu thức theo đợt thay vì một lần cho mỗi người dùng"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__batch_user_expression
msgid "Evaluated expression for batch mode"
msgstr "Biểu thức đánh giá cho chế độ theo lô"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_badge__rule_auth__everyone
msgid "Everyone"
msgstr "Mọi người"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_goal_definition__display_mode__boolean
msgid "Exclusive (done or not-done)"
msgstr "Độc quyền (thực hiện hoặc không thực hiện)"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_goal__state__failed
msgid "Failed"
msgstr "Không đạt"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__field_id
msgid "Field to Sum"
msgstr "Trường để tính tổng"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__domain
msgid "Filter Domain"
msgstr "Lọc tên miền"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__message_follower_ids
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__message_follower_ids
msgid "Followers"
msgstr "Người theo dõi"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__message_partner_ids
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__message_partner_ids
msgid "Followers (Partners)"
msgstr "Người theo dõi (Đối tác)"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__reward_first_id
msgid "For 1st user"
msgstr "Với người đứng đầu"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__reward_second_id
msgid "For 2nd user"
msgstr "Với người thứ hai"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__reward_third_id
msgid "For 3rd user"
msgstr "Với người thứ ba"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__reward_id
msgid "For Every Succeeding User"
msgstr "Cho mỗi tài khoản người dùng thành công"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_form_view
msgid "Formatting Options"
msgstr "Tùy chọn định dạng"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__level
msgid "Forum Badge Level"
msgstr "Cấp huy hiệu diễn đàn"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_kanban_view
msgid "From"
msgstr "Từ"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__full_suffix
msgid "Full Suffix"
msgstr "Hậu tố đầy đủ"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_tracking__gain
msgid "Gain"
msgstr "Lời"

#. module: gamification
#: model:ir.model,name:gamification.model_gamification_badge
msgid "Gamification Badge"
msgstr "Huy hiệu trò chơi hoá"

#. module: gamification
#: model:ir.model,name:gamification.model_gamification_challenge
msgid "Gamification Challenge"
msgstr "Thử thách trò chơi hoá"

#. module: gamification
#: model:ir.model,name:gamification.model_gamification_goal
msgid "Gamification Goal"
msgstr "Mục tiêu trò chơi hóa"

#. module: gamification
#: model:ir.model,name:gamification.model_gamification_goal_definition
msgid "Gamification Goal Definition"
msgstr "Định nghĩa mục tiêu trò chơi hóa"

#. module: gamification
#: model:ir.model,name:gamification.model_gamification_goal_wizard
msgid "Gamification Goal Wizard"
msgstr "Công cụ mục tiêu trò chơi hóa"

#. module: gamification
#: model:ir.ui.menu,name:gamification.gamification_menu
msgid "Gamification Tools"
msgstr "Công cụ trò chơi hóa"

#. module: gamification
#: model:ir.model,name:gamification.model_gamification_badge_user
msgid "Gamification User Badge"
msgstr "Huy hiệu người dùng trò chơi hóa"

#. module: gamification
#: model:ir.model,name:gamification.model_gamification_badge_user_wizard
msgid "Gamification User Badge Wizard"
msgstr "Công cụ huy hiệu người dùng trò chơi hóa"

#. module: gamification
#: model:ir.model,name:gamification.model_gamification_challenge_line
msgid "Gamification generic goal for challenge"
msgstr "Mục tiêu chung cho trò chơi"

#. module: gamification
#: model:mail.template,name:gamification.email_template_badge_received
msgid "Gamification: Badge Received"
msgstr "Trò chơi hoá: Đã nhận được huy hiệu"

#. module: gamification
#: model:mail.template,name:gamification.simple_report_template
msgid "Gamification: Challenge Report"
msgstr "Trò chơi hoá: Báo cáo thử thách"

#. module: gamification
#: model:ir.actions.server,name:gamification.ir_cron_check_challenge_ir_actions_server
msgid "Gamification: Goal Challenge Check"
msgstr "Trò chơi hóa: Kiểm tra thử thách mục tiêu."

#. module: gamification
#: model:ir.actions.server,name:gamification.ir_cron_consolidate_ir_actions_server
msgid "Gamification: Karma tracking consolidation"
msgstr "Trò chơi hóa: Hợp nhất theo dõi karma"

#. module: gamification
#: model:mail.template,name:gamification.mail_template_data_new_rank_reached
msgid "Gamification: New Rank Reached"
msgstr "Trò chơi hoá: Đã đạt thứ hạng mới"

#. module: gamification
#: model:mail.template,name:gamification.email_template_goal_reminder
msgid "Gamification: Reminder For Goal Update"
msgstr "Trò chơi hoá: Nhắc nhở cập nhật mục tiêu"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_wizard__goal_id
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "Goal"
msgstr "Mục tiêu"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__definition_id
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__definition_id
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__name
#: model_terms:ir.ui.view,arch_db:gamification.goal_search_view
msgid "Goal Definition"
msgstr "Định nghĩa mục tiêu"

#. module: gamification
#: model:ir.actions.act_window,name:gamification.goal_definition_list_action
#: model:ir.ui.menu,name:gamification.gamification_definition_menu
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_list_view
msgid "Goal Definitions"
msgstr "Định nghĩa mục tiêu"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__description
msgid "Goal Description"
msgstr "Miêu tả mục tiêu"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "Goal Failed"
msgstr "Mục tiêu thất bại"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_list_view
msgid "Goal List"
msgstr "Danh sách mục tiêu"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__condition
msgid "Goal Performance"
msgstr "Hiệu suất mục tiêu"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "Goal Reached"
msgstr "Đạt được mục tiêu"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_form_view
msgid "Goal definitions"
msgstr "Định nghĩa mục tiêu"

#. module: gamification
#: model:ir.actions.act_window,name:gamification.goal_list_action
#: model:ir.ui.menu,name:gamification.gamification_goal_menu
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
#: model_terms:ir.ui.view,arch_db:gamification.view_challenge_kanban
msgid "Goals"
msgstr "Mục tiêu"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_badge__level__gold
msgid "Gold"
msgstr "Vàng"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_res_users__gold_badge
msgid "Gold badges count"
msgstr "Số lượng huy hiệu vàng"

#. module: gamification
#: model:gamification.badge,name:gamification.badge_good_job
msgid "Good Job"
msgstr "Tốt lắm!"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_kanban_view
msgid "Grant"
msgstr "Trao tặng"

#. module: gamification
#: model:ir.actions.act_window,name:gamification.action_grant_wizard
#: model_terms:ir.ui.view,arch_db:gamification.view_badge_wizard_grant
msgid "Grant Badge"
msgstr "Trao huy hiệu"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.view_badge_wizard_grant
#: model_terms:ir.ui.view,arch_db:gamification.view_goal_wizard_update_current
msgid "Grant Badge To"
msgstr "Trao huy hiệu cho"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
msgid "Grant this Badge"
msgstr "Trao huy hiệu này"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_tracking_view_search
msgid "Granted By"
msgstr "Được trao bởi"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_user_kanban_view
msgid "Granted by"
msgstr "Được trao bời"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
msgid "Granting"
msgstr "Trao"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_search_view
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_tracking_view_search
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_search_view
#: model_terms:ir.ui.view,arch_db:gamification.goal_search_view
msgid "Group By"
msgstr "Nhóm theo"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge__report_message_group_id
msgid "Group that will receive a copy of the report in addition to the user"
msgstr "Nhóm sẽ nhận được một bản sao của báo cáo ngoài người dùng"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_search_view
msgid "HR Challenges"
msgstr "Thử thách của phân hệ HR"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__has_message
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__has_message
msgid "Has Message"
msgstr "Có tin nhắn"

#. module: gamification
#: model:gamification.badge,name:gamification.badge_hidden
msgid "Hidden"
msgstr "Ẩn"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_form_view
msgid "How is the goal computed?"
msgstr "Cách tính toán mục tiêu?"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__challenge_category__hr
msgid "Human Resources / Engagement"
msgstr "Nhân sự / Tham gia"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__id
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user__id
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_wizard__id
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__id
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__id
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__id
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__id
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_wizard__id
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__id
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_tracking__id
msgid "ID"
msgstr "ID"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__res_id_field
msgid "ID Field of user"
msgstr "ID của người dùng"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__remaining_sending
msgid "If a maximum is set"
msgstr "Nếu giá trị tối đa được cấu hình"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__message_needaction
#: model:ir.model.fields,help:gamification.field_gamification_challenge__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Nếu chọn, bạn cần chú ý tới các tin nhắn mới."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__message_has_error
#: model:ir.model.fields,help:gamification.field_gamification_challenge__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Nếu chọn, một số tin nhắn sẽ có lỗi gửi."

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__image_1920
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__image_1920
msgid "Image"
msgstr "Hình ảnh"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__image_1024
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__image_1024
msgid "Image 1024"
msgstr "Hình ảnh 1024"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__image_128
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__image_128
msgid "Image 128"
msgstr "Hình ảnh 128"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__image_256
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__image_256
msgid "Image 256"
msgstr "Hình ảnh 256"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__image_512
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__image_512
msgid "Image 512"
msgstr "Hình ảnh 512"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__state__inprogress
msgid "In Progress"
msgstr "Đang thực hiện"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_form_view
msgid ""
"In batch mode, the domain is evaluated globally. If enabled, do not use "
"keyword 'user' in above filter domain."
msgstr ""
"Trong chế độ theo lô, điều kiện được đánh giá trên toàn hệ thống. Nếu được "
"bật, không sử dụng từ khóa 'người dùng' trong miền bộ lọc ở trên."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__batch_distinctive_field
msgid ""
"In batch mode, this indicates which field distinguishes one user from the "
"other, e.g. user_id, partner_id..."
msgstr ""
"Trong chế độ theo lô, đây là yếu tố chỉ ra trường nào phân biệt các người "
"dùng, ví dụ: user_id, partner_id, v.v."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal__last_update
msgid ""
"In case of manual goal, reminders are sent if the goal as not been updated "
"for a while (defined in challenge). Ignored in case of non-manual goal or "
"goal not linked to a challenge."
msgstr ""
"Trong trường hợp mục tiêu tạo tay, lời nhắc được gửi nếu mục tiêu không được"
" cập nhật trong một thời gian (được xác định trong thử thách). Bỏ qua trong "
"trường hợp mục tiêu tạo tay hoặc mục tiêu không liên quan đến một thách "
"thức."

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_goal__state__inprogress
msgid "In progress"
msgstr "Đang thực hiện"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__visibility_mode__personal
msgid "Individual Goals"
msgstr "Mục tiêu cá nhân"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__model_inherited_ids
msgid "Inherited models"
msgstr "Mô hình kế thừa"

#. module: gamification
#: model:gamification.goal.definition,name:gamification.definition_base_invite
msgid "Invite new Users"
msgstr "Mời người dùng"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__message_is_follower
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__message_is_follower
msgid "Is Follower"
msgstr "Là người theo dõi"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_res_users__karma
#: model_terms:ir.ui.view,arch_db:gamification.res_users_view_form
msgid "Karma"
msgstr "Karma"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_res_users__karma_tracking_ids
msgid "Karma Changes"
msgstr "Thay đổi karma"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_tracking_view_form
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_tracking_view_search
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_tracking_view_tree
msgid "Karma Owner"
msgstr "Người sở hữu karma"

#. module: gamification
#: model:ir.actions.act_window,name:gamification.gamification_karma_tracking_action
#: model:ir.ui.menu,name:gamification.gamification_karma_tracking_menu
msgid "Karma Tracking"
msgstr "Theo dõi karma"

#. module: gamification
#. odoo-python
#: code:addons/gamification/models/res_users.py:0
msgid "Karma Updates"
msgstr "Cập nhật karma"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__last_report_date
msgid "Last Report Date"
msgstr "Ngày báo cáo gần nhất"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__last_update
msgid "Last Update"
msgstr "Cập nhật Lần cuối"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__write_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user__write_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_wizard__write_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__write_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__write_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__write_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__write_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_wizard__write_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__write_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_tracking__write_uid
msgid "Last Updated by"
msgstr "Cập nhật lần cuối bởi"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__write_date
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user__write_date
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_wizard__write_date
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__write_date
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__write_date
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__write_date
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__write_date
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_wizard__write_date
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__write_date
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_tracking__write_date
msgid "Last Updated on"
msgstr "Cập nhật lần cuối vào"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__visibility_mode__ranking
msgid "Leader Board (Group Ranking)"
msgstr "Ban lãnh đạo (Xếp hạng nhóm)"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__rule_max_number
msgid "Limitation Number"
msgstr "Số giới hạn"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Line List"
msgstr "Danh sách dòng"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__line_ids
msgid "Lines"
msgstr "Chi tiết"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge__line_ids
msgid "List of goals that will be set"
msgstr "Danh sách mục tiêu sẽ được cấu hình"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_tracking_view_search
msgid "Manual"
msgstr "Thủ công"

#. module: gamification
#: model:gamification.karma.rank,name:gamification.rank_master
msgid "Master"
msgstr "Chuyên gia"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__message_has_error
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__message_has_error
msgid "Message Delivery error"
msgstr "Lỗi gửi tin nhắn"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__message_ids
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__message_ids
msgid "Messages"
msgstr "Tin nhắn"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__model_id
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_search_view
msgid "Model"
msgstr "Mô hình"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__definition_monetary
msgid "Monetary"
msgstr "Tiền tệ"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__monetary
msgid "Monetary Value"
msgstr "Giá trị tiền tệ"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__period__monthly
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__report_message_frequency__monthly
msgid "Monthly"
msgstr "Hàng tháng"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__rule_max
msgid "Monthly Limited Sending"
msgstr "Số lượng gửi giới hạn mỗi tháng"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__stat_this_month
msgid "Monthly total"
msgstr "Tổng hàng tháng"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__description_motivational
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_rank_view_form
msgid "Motivational"
msgstr "Động lực"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_karma_rank__description_motivational
msgid "Motivational phrase to reach this rank on your profile page"
msgstr "Lời khích lệ khi đạt thứ hạng này trên trang cá nhân của bạn"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_search_view
msgid "My Goals"
msgstr "Mục tiêu của tôi"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_tracking_view_search
msgid "My Karma"
msgstr "Karma của tôi"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__stat_my_monthly_sending
msgid "My Monthly Sending Total"
msgstr "Tổng số gửi hàng tháng của tôi"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__stat_my_this_month
msgid "My Monthly Total"
msgstr "Tổng hàng tháng của tôi"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__stat_my
msgid "My Total"
msgstr "Tổng của tôi"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__name
msgid "Name"
msgstr "Tên"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__report_message_frequency__never
msgid "Never"
msgstr "Không bao giờ"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge__remind_update_delay
msgid "Never reminded if no value or zero is specified."
msgstr "Không bao giờ nhắc nếu không có giá trị hoặc không được chỉ định."

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_tracking__new_value
msgid "New Karma Value"
msgstr "Giá trị karma mới"

#. module: gamification
#: model:mail.template,subject:gamification.email_template_badge_received
msgid "New badge {{ object.badge_id.name }} granted"
msgstr "Huy hiệu mới {{ object.badge_id.name }} đã được trao"

#. module: gamification
#: model:mail.template,subject:gamification.mail_template_data_new_rank_reached
msgid "New rank: {{ object.rank_id.name }}"
msgstr "Thứ hạng mới: {{ object.rank_id.name }}"

#. module: gamification
#: model:gamification.karma.rank,name:gamification.rank_newbie
msgid "Newbie"
msgstr "Thành viên mới"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_res_users__next_rank_id
msgid "Next Rank"
msgstr "Thứ hạng kế tiếp"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__next_report_date
msgid "Next Report Date"
msgstr "Ngày báo cáo tiếp theo"

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.gamification_karma_tracking_action
msgid "No Karma Tracking"
msgstr "Không theo dõi karma"

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.goals_from_challenge_act
msgid "No goal found"
msgstr "Không tìm thấy mục tiêu"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
msgid "No monthly sending limit"
msgstr "Không giới hạn gửi hàng tháng"

#. module: gamification
#: model_terms:gamification.badge,description:gamification.badge_problem_solver
msgid "No one can solve challenges like you do."
msgstr "Không ai có thể giải quyết những thách thức như bạn làm."

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_badge__rule_auth__nobody
msgid "No one, assigned through challenges"
msgstr "Không ai, được giao qua thử thách"

#. module: gamification
#. odoo-python
#: code:addons/gamification/models/gamification_challenge.py:0
msgid ""
"Nobody has succeeded to reach every goal, no badge is rewarded for this "
"challenge."
msgstr ""
"Không ai hoàn thành mọi mục tiêu, huy hiệu không được thưởng cho thử thách "
"này."

#. module: gamification
#. odoo-python
#: code:addons/gamification/models/gamification_challenge.py:0
msgid "Nobody reached the required conditions to receive special badges."
msgstr ""
"Không ai đạt được các điều kiện cần thiết để nhận được huy hiệu đặc biệt."

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__period__once
msgid "Non recurring"
msgstr "Không định kỳ"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__remind_update_delay
msgid "Non-updated manual goals will be reminded after"
msgstr "Các mục tiêu thủ công không được cập nhật sẽ được nhắc nhở sau"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Notification Messages"
msgstr "Tin nhắn thông báo"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__message_needaction_counter
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__message_needaction_counter
msgid "Number of Actions"
msgstr "Số lượng tác vụ"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__message_has_error_counter
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__message_has_error_counter
msgid "Number of errors"
msgstr "Số lượng lỗi"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__message_needaction_counter
#: model:ir.model.fields,help:gamification.field_gamification_challenge__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Số tin nhắn cần xử lý"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__message_has_error_counter
#: model:ir.model.fields,help:gamification.field_gamification_challenge__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Số tin nhắn bị gửi lỗi"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__granted_users_count
msgid "Number of users"
msgstr "Số lượng người dùng"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_tracking__old_value
msgid "Old Karma Value"
msgstr "Giá trị karma cũ"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__report_message_frequency__onchange
msgid "On change"
msgstr "Thay đổi"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__rule_auth_badge_ids
msgid "Only the people having these badges can give this badge"
msgstr "Chỉ những người có những huy hiệu này mới có thể tặng huy hiệu này"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__rule_auth_user_ids
msgid "Only these people can give this badge"
msgstr "Chỉ những người dùng này có thể tặng huy hiệu này"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_form_view
msgid "Optimisation"
msgstr "Tối ưu hóa"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__owner_ids
msgid "Owners"
msgstr "Sở hữu"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__user_ids
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
#: model_terms:ir.ui.view,arch_db:gamification.view_challenge_kanban
msgid "Participants"
msgstr "Người tham gia"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_badge__rule_auth__having
msgid "People having some badges"
msgstr "Người có một số huy hiệu"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_search_view
msgid "Period"
msgstr "Giai đoạn"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge__period
msgid ""
"Period of automatic goal assignment. If none is selected, should be launched"
" manually."
msgstr ""
"Khoảng thời gian phân công mục tiêu tự động. Nếu không chọn, nên được khởi "
"động thủ công."

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__period
msgid "Periodicity"
msgstr "Định kỳ"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_tracking_view_tree
msgid "Previous Total"
msgstr "Tổng số trước đó"

#. module: gamification
#: model:gamification.badge,name:gamification.badge_problem_solver
msgid "Problem Solver"
msgstr "Người giải quyết vấn đề"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_goal_definition__display_mode__progress
msgid "Progressive (using numerical values)"
msgstr "Tiến bộ (sử dụng các giá trị số)"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__compute_code
msgid "Python Code"
msgstr "Mã Python"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__compute_code
msgid ""
"Python code to be executed for each user. 'result' should contains the new "
"current value. Evaluated user can be access through object.user_id."
msgstr ""
"Mã Python sẽ được thực thi cho mỗi người dùng. 'kết quả' nên chứa giá trị "
"hiện tại mới. Người dùng được đánh giá có thể được truy cập thông qua "
"object.user_id."

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_res_users__rank_id
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_rank_view_form
msgid "Rank"
msgstr "Thứ hạng"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__name
msgid "Rank Name"
msgstr "Tên thứ hạng"

#. module: gamification
#: model:ir.model,name:gamification.model_gamification_karma_rank
msgid "Rank based on karma"
msgstr "Thứ hạng dựa trên karma"

#. module: gamification
#: model:ir.actions.act_window,name:gamification.gamification_karma_ranks_action
#: model:ir.ui.menu,name:gamification.gamification_karma_ranks_menu
msgid "Ranks"
msgstr "Thứ hạng"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_ranks_view_tree
msgid "Ranks List"
msgstr "Danh sách thứ hạng"

#. module: gamification
#: model_terms:gamification.karma.rank,description_motivational:gamification.rank_master
msgid "Reach the next rank and become a Master!"
msgstr "Hãy đạt thứ hạng tiếp theo và trở thành Chuyên gia!"

#. module: gamification
#: model_terms:gamification.karma.rank,description_motivational:gamification.rank_doctor
msgid "Reach the next rank and become a powerful user!"
msgstr "Hãy đạt thứ hạng tiếp theo và một người dùng toàn năng!"

#. module: gamification
#: model_terms:gamification.karma.rank,description_motivational:gamification.rank_bachelor
msgid "Reach the next rank and gain a very magic wand!"
msgstr "Hãy đạt thứ hạng tiếp theo để nhận được một chiếc đũa phép!"

#. module: gamification
#: model_terms:gamification.karma.rank,description_motivational:gamification.rank_master
msgid "Reach the next rank and gain a very nice hat!"
msgstr "Hãy đạt thứ hạng tiếp theo để nhận được một chiếc mũ thật xinh!"

#. module: gamification
#: model_terms:gamification.karma.rank,description_motivational:gamification.rank_student
msgid "Reach the next rank and gain a very nice mug!"
msgstr "Hãy đạt thứ hạng tiếp theo để nhận được một chiếc cốc rất đẹp!"

#. module: gamification
#: model_terms:gamification.karma.rank,description_motivational:gamification.rank_doctor
msgid "Reach the next rank and gain a very nice unicorn!"
msgstr "Hãy đạt thứ hạng tiếp theo để nhận được một chú kỳ lân dễ thương!"

#. module: gamification
#: model_terms:gamification.karma.rank,description_motivational:gamification.rank_bachelor
msgid "Reach the next rank to improve your status!"
msgstr "Hãy đạt thứ hạng tiếp theo và nâng cấp trạng thái của bạn!"

#. module: gamification
#: model_terms:gamification.karma.rank,description_motivational:gamification.rank_student
msgid "Reach the next rank to show the rest of the world you exist."
msgstr "Hãy đạt thứ hạng tiếp theo để thể hiện mình với cả thế giới."

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_goal__state__reached
msgid "Reached"
msgstr "Đã đạt"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "Reached when current value is"
msgstr "Đạt được khi giá trị hiện tại bằng"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_goal_definition__computation_mode__manually
msgid "Recorded manually"
msgstr "Ghi nhận thủ công"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "Reference"
msgstr "Tham chiếu"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Refresh Challenge"
msgstr "Làm mới thử thách"

#. module: gamification
#: model:ir.actions.act_window,name:gamification.goals_from_challenge_act
msgid "Related Goals"
msgstr "Mục tiêu liên quan"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__remaining_sending
msgid "Remaining Sending Allowed"
msgstr "Số lượng gửi còn lại"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__remind_update_delay
msgid "Remind delay"
msgstr "Hoãn lại nhắc nhở"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Reminders for Manual Goals"
msgstr "Nhắc nhở cho các mục tiêu thủ công"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__report_message_frequency
msgid "Report Frequency"
msgstr "Tần suất báo cáo"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__report_template_id
msgid "Report Template"
msgstr "Mẫu báo cáo"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__rule_auth_badge_ids
msgid "Required Badges"
msgstr "Huy hiệu yêu cầu"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__karma_min
msgid "Required Karma"
msgstr "Karma cần có"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "Reset Completion"
msgstr "Đặt lại hoàn thành"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__manager_id
msgid "Responsible"
msgstr "Người phụ trách"

#. module: gamification
#. odoo-python
#: code:addons/gamification/models/gamification_challenge.py:0
msgid "Retrieving progress for personal challenge without user information"
msgstr "Lấy tiến độ cho thử thách cá nhân mà không có thông tin người dùng"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Reward"
msgstr "Phần thưởng"

#. module: gamification
#. odoo-python
#: code:addons/gamification/models/gamification_challenge.py:0
msgid ""
"Reward (badge %(badge_name)s) for every succeeding user was sent to "
"%(users)s."
msgstr ""
"Phần thưởng (badge %(badge_name)s) cho mỗi người dùng thành công được gửi "
"tới %(users)s."

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__reward_failure
msgid "Reward Bests if not Succeeded?"
msgstr "Thưởng người dùng hoàn thành tốt nhất nếu không ai thành công?"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__reward_realtime
msgid "Reward as soon as every goal is reached"
msgstr "Thưởng ngay khi đạt được mọi mục tiêu"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__challenge_ids
msgid "Reward of Challenges"
msgstr "Phần thưởng của thử thách"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__goal_definition_ids
msgid "Rewarded by"
msgstr "Thưởng bởi"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
msgid "Rewards for challenges"
msgstr "Phần thưởng của thử thách"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_search_view
msgid "Running"
msgstr "Đang chạy"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_search_view
msgid "Running Challenges"
msgstr "Thử thách đang chạy"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "Schedule"
msgstr "Lịch trình"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.gamification_badge_view_search
msgid "Search Badge"
msgstr "Tìm kiếm huy hiệu"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_search_view
msgid "Search Challenges"
msgstr "Tìm kiếm thử thách"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_search_view
msgid "Search Goal Definitions"
msgstr "Định nghĩa mục tiêu tìm kiếm"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_search_view
msgid "Search Goals"
msgstr "Mục tiêu tìm kiếm"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_ranks_view_search
msgid "Search Ranks"
msgstr "Tìm kiếm thứ hạng"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_tracking_view_search
msgid "Search Trackings"
msgstr "Tìm kiếm theo dõi"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
msgid ""
"Security rules to define who is allowed to manually grant badges. Not "
"enforced for administrator."
msgstr ""
"Quy tắc bảo mật để xác định ai được phép cấp phù hiệu bằng tay. Không thi "
"hành cho quản trị viên."

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Send Report"
msgstr "Gửi báo cáo"

#. module: gamification
#: model:mail.template,description:gamification.simple_report_template
msgid "Send a challenge report to all participants"
msgstr "Gửi báo cáo thử thách cho tất cả người tham gia"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__report_message_group_id
msgid "Send a copy to"
msgstr "Gửi bản sao đến"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user__sender_id
msgid "Sender"
msgstr "Người gửi"

#. module: gamification
#: model:mail.template,description:gamification.email_template_goal_reminder
msgid "Sent automatically to participant who haven't updated their goal"
msgstr "Tự động gửi cho người tham gia chưa cập nhật mục tiêu"

#. module: gamification
#: model:mail.template,description:gamification.email_template_badge_received
msgid "Sent automatically to the user who received a badge"
msgstr "Tự động gửi cho người dùng đã nhận được huy hiệu"

#. module: gamification
#: model:mail.template,description:gamification.mail_template_data_new_rank_reached
msgid "Sent automatically when user reaches a new rank"
msgstr "Tự động gửi khi người dùng đạt được một thứ hạng mới"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__sequence
msgid "Sequence"
msgstr "Trình tự"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.view_goal_wizard_update_current
msgid "Set the current value you have reached for this goal"
msgstr "Đặt giá trị hiện tại bạn đã đạt được cho mục tiêu này"

#. module: gamification
#: model:gamification.goal.definition,name:gamification.definition_base_company_data
msgid "Set your Company Data"
msgstr "Thiết lập dữ liệu công ty"

#. module: gamification
#: model:gamification.goal.definition,name:gamification.definition_base_company_logo
msgid "Set your Company Logo"
msgstr "Thiết lập logo công ty"

#. module: gamification
#: model:gamification.goal.definition,name:gamification.definition_base_timezone
msgid "Set your Timezone"
msgstr "Thiết lập múi giờ"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__challenge_category__other
msgid "Settings / Gamification Tools"
msgstr "Thiết lập"

#. module: gamification
#: model:gamification.challenge,name:gamification.challenge_base_configure
msgid "Setup your Company"
msgstr "Thiết lập công ty"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_badge__level__silver
msgid "Silver"
msgstr "Bạc"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_res_users__silver_badge
msgid "Silver badges count"
msgstr "Số lượng huy hiệu bạc"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_tracking__origin_ref
msgid "Source"
msgstr "Nguồn"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_tracking__origin_ref_model_name
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_tracking_view_search
msgid "Source Type"
msgstr "Loại nguồn"

#. module: gamification
#. odoo-python
#: code:addons/gamification/models/gamification_challenge.py:0
msgid ""
"Special rewards were sent to the top competing users. The ranking for this "
"challenge is:"
msgstr ""
"Phần thưởng đặc biệt đã được gửi đến những người dùng cạnh tranh top đầu. "
"Thứ hạng cho thử thách này là:"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Start Challenge"
msgstr "Bắt đầu thử thách"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__start_date
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__start_date
msgid "Start Date"
msgstr "Ngày bắt đầu"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "Start goal"
msgstr "Bắt đầu mục tiêu"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__state
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__state
#: model_terms:ir.ui.view,arch_db:gamification.challenge_search_view
#: model_terms:ir.ui.view,arch_db:gamification.goal_search_view
msgid "State"
msgstr "Trạng thái"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
msgid "Statistics"
msgstr "Thống kê"

#. module: gamification
#: model:gamification.karma.rank,name:gamification.rank_student
msgid "Student"
msgstr "Học sinh"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Subscriptions"
msgstr "Gói đăng ký"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__definition_full_suffix
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__definition_suffix
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__suffix
msgid "Suffix"
msgstr "Hậu tố"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__invited_user_ids
msgid "Suggest to users"
msgstr "Gợi ý cho người dùng"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
#: model_terms:ir.ui.view,arch_db:gamification.challenge_line_list_view
msgid "Target"
msgstr "Mục tiêu"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__target_goal
msgid "Target Value to Reach"
msgstr "Giá trị mục tiêu để đạt được"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_kanban_view
msgid "Target: less than"
msgstr "Mục tiêu: ít hơn"

#. module: gamification
#. odoo-python
#: code:addons/gamification/tests/test_karma_tracking.py:0
msgid "Test Reason"
msgstr "Lý do kiểm tra"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__action_id
msgid "The action that will be called to update the goal value."
msgstr "Tác vụ sẽ được gọi để cập nhật giá trị mục tiêu."

#. module: gamification
#. odoo-python
#: code:addons/gamification/models/gamification_challenge.py:0
msgid "The challenge %s is finished."
msgstr "Thử thách %s đã hoàn thành"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge_line__definition_full_suffix
#: model:ir.model.fields,help:gamification.field_gamification_goal__definition_suffix
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__full_suffix
msgid "The currency and suffix field"
msgstr "Trường tiền tệ và hậu tố"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__field_date_id
msgid "The date to use for the time period evaluated"
msgstr "Ngày sử dụng cho khoảng thời gian được đánh giá"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge__end_date
msgid ""
"The day a new challenge will be automatically closed. If no periodicity is "
"set, will use this date as the goal end date."
msgstr ""
"Ngày mà thử thách mới sẽ tự động đóng lại. Nếu không có định kỳ được đặt, sẽ"
" sử dụng ngày này làm ngày kết thúc mục tiêu."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge__start_date
msgid ""
"The day a new challenge will be automatically started. If no periodicity is "
"set, will use this date as the goal start date."
msgstr ""
"Ngày mà thử thách mới sẽ tự động bắt đầu. Nếu không có định kỳ được đặt, sẽ "
"sử dụng ngày này làm ngày bắt đầu mục tiêu."

#. module: gamification
#. odoo-python
#: code:addons/gamification/models/gamification_goal_definition.py:0
msgid ""
"The domain for the definition %(definition)s seems incorrect, please check it.\n"
"\n"
"%(error_message)s"
msgstr ""
"Miền cho định nghĩa %(definition)s có vẻ không chính xác, vui lòng kiểm tra.\n"
"\n"
"%(error_message)s"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__res_id_field
msgid ""
"The field name on the user profile (res.users) containing the value for "
"res_id for action."
msgstr ""
"Trường tên trên hồ sơ người dùng (res.users) có chứa giá trị cho res_id cho "
"tác vụ."

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_goal_definition__condition__higher
msgid "The higher the better"
msgstr "Càng cao càng tốt"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__owner_ids
msgid "The list of instances of this badge granted to users"
msgstr "Danh sách các trường hợp của huy hiệu này được cấp cho người dùng"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__model_inherited_ids
msgid "The list of models that extends the current model."
msgstr "Danh sách các mô hình mở rộng mô hình hiện tại."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__unique_owner_ids
msgid "The list of unique users having received this badge."
msgstr "Danh sách người dùng duy nhất đã nhận được huy hiệu này."

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_goal_definition__condition__lower
msgid "The lower the better"
msgstr "Càng thấp càng tốt"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__rule_max_number
msgid ""
"The maximum number of time this badge can be sent per month per person."
msgstr ""
"Số lượng thời gian tối đa huy hiệu này có thể được gửi mỗi tháng cho mỗi "
"người."

#. module: gamification
#. odoo-python
#: code:addons/gamification/models/gamification_goal_definition.py:0
msgid ""
"The model configuration for the definition %(name)s seems incorrect, please check it.\n"
"\n"
"%(error)s not found"
msgstr ""
"Cấu hình mô hình của định nghĩa %(name)s có vẻ chưa chính xác, hãy kiểm tra lại.\n"
"\n"
"Không tìm thấy %(error)s "

#. module: gamification
#. odoo-python
#: code:addons/gamification/models/gamification_goal_definition.py:0
msgid ""
"The model configuration for the definition %(name)s seems incorrect, please check it.\n"
"\n"
"%(field_name)s not stored"
msgstr ""
"Cấu hình mô hình của định nghĩa %(name)s có vẻ chưa chính xác, hãy kiểm tra lại.\n"
"\n"
"Không lưu trữ %(field_name)s"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal__remind_update_delay
msgid ""
"The number of days after which the user assigned to a manual goal will be "
"reminded. Never reminded if no value is specified."
msgstr ""
"Số ngày mà người dùng được chỉ định cho mục tiêu thủ công sẽ được nhắc nhở. "
"Không bao giờ nhắc nếu không có giá trị được chỉ định."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__stat_my_this_month
msgid ""
"The number of time the current user has received this badge this month."
msgstr "Số lần người dùng nhận được huy hiệu trong tháng này"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__stat_my
msgid "The number of time the current user has received this badge."
msgstr "Số lần người dùng nhận được huy hiệu này"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__stat_my_monthly_sending
msgid "The number of time the current user has sent this badge this month."
msgstr "Số lần người dùng hiện tại đã gửi huy hiệu này trong tháng này."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__granted_users_count
msgid "The number of time this badge has been received by unique users."
msgstr "Số lượng thời gian huy hiệu này đã được nhận bởi người dùng duy nhất."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__stat_this_month
msgid "The number of time this badge has been received this month."
msgstr "Số lần huy hiệu này đã được nhận trong tháng này."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__granted_count
msgid "The number of time this badge has been received."
msgstr "Số lần huy hiệu này được nhận"

#. module: gamification
#: model:ir.model.constraint,message:gamification.constraint_gamification_karma_rank_karma_min_check
msgid "The required karma has to be above 0."
msgstr "Điểm karma cần có phải lớn hơn 0."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge_line__definition_monetary
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__monetary
msgid "The target and current value are defined in the company currency."
msgstr "Mục tiêu và giá trị hiện tại được xác định bằng tiền tệ của công ty."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge_line__definition_suffix
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__suffix
msgid "The unit of the target and current values"
msgstr "Đơn vị của các giá trị đích và hiện tại"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__goal_definition_ids
msgid ""
"The users that have succeeded these goals will receive automatically the "
"badge."
msgstr ""
"Những người dùng đã đạt được các mục tiêu này thành công sẽ tự động nhận "
"được huy hiệu."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__batch_user_expression
msgid ""
"The value to compare with the distinctive field. The expression can contain "
"reference to 'user' which is a browse record of the current user, e.g. "
"user.id, user.partner_id.id..."
msgstr ""
"Giá trị để so sánh với trường đặc biệt. Biểu thức có thể chứa tham chiếu đến"
" 'người dùng' là bản ghi duyệt của người dùng hiện tại, ví dụ: user.id, "
"user.partner_id.id ..."

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.goals_from_challenge_act
msgid ""
"There is no goal associated to this challenge matching your search.\n"
"            Make sure that your challenge is active and assigned to at least one user."
msgstr ""
"Không có mục tiêu liên quan đến thử thách này phù hợp với tìm kiếm của bạn.\n"
"            Đảm bảo rằng thử thách của bạn đang hoạt động và được chỉ định cho ít nhất một người dùng."

#. module: gamification
#. odoo-python
#: code:addons/gamification/models/gamification_badge.py:0
msgid "This badge can not be sent by users."
msgstr "Người dùng không thể gửi huy hiệu này."

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_kanban_view
msgid "To"
msgstr "Đến"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__target_goal
msgid "To Reach"
msgstr "Để đạt được"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__to_update
msgid "To update"
msgstr "Cập nhật"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__granted_count
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_tracking_view_tree
msgid "Total"
msgstr "Tổng"

#. module: gamification
#: model:ir.model,name:gamification.model_gamification_karma_tracking
msgid "Track Karma Changes"
msgstr "Theo dõi thay đổi karma"

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.gamification_karma_tracking_action
msgid "Track the sources of the users karma and monitor"
msgstr "Theo dõi và giám sát nguồn gốc karma của người dùng."

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_tracking_view_form
msgid "Tracking"
msgstr "Theo dõi"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_tracking__tracking_date
msgid "Tracking Date"
msgstr "Ngày theo dõi"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_tracking_view_tree
msgid "Trackings"
msgstr "Theo dõi"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__unique_owner_ids
msgid "Unique Owners"
msgstr "Chủ sở hữu duy nhất"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__definition_suffix
msgid "Unit"
msgstr "Đơn vị"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.view_goal_wizard_update_current
msgid "Update"
msgstr "Cập nhật"

#. module: gamification
#. odoo-python
#: code:addons/gamification/models/gamification_goal.py:0
msgid "Update %s"
msgstr "Cập nhật %s"

#. module: gamification
#. odoo-python
#: code:addons/gamification/models/gamification_karma_tracking.py:0
#: model:ir.model,name:gamification.model_res_users
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user__user_id
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_wizard__user_id
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__user_id
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_tracking__user_id
#: model_terms:ir.ui.view,arch_db:gamification.goal_search_view
msgid "User"
msgstr "Người dùng"

#. module: gamification
#. odoo-python
#: code:addons/gamification/models/res_users.py:0
msgid "User Creation"
msgstr "Tạo người dùng"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__user_domain
msgid "User domain"
msgstr "Miền người dùng"

#. module: gamification
#: model:ir.actions.act_window,name:gamification.action_current_rank_users
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__user_ids
msgid "Users"
msgstr "Người dùng"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__period__weekly
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__report_message_frequency__weekly
msgid "Weekly"
msgstr "Hàng tuần"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__rule_auth
msgid "Who can grant this badge"
msgstr "Ai có thể cấp huy hiệu này"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.view_badge_wizard_grant
msgid "Who would you like to reward?"
msgstr "Bạn muốn thưởng cho ai?"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge__reward_realtime
msgid ""
"With this option enabled, a user can receive a badge only once. The top 3 "
"badges are still rewarded only at the end of the challenge."
msgstr ""
"Với tùy chọn này được bật, người dùng chỉ có thể nhận được huy hiệu một lần."
" 3 huy hiệu hàng đầu vẫn chỉ được thưởng khi kết thúc thử thách."

#. module: gamification
#: model_terms:gamification.badge,description:gamification.badge_idea
msgid "With your brilliant ideas, you are an inspiration to others."
msgstr ""
"Với những ý tưởng tuyệt vời của bạn, bạn là nguồn cảm hứng cho người khác."

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__period__yearly
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__report_message_frequency__yearly
msgid "Yearly"
msgstr "Hàng năm"

#. module: gamification
#. odoo-python
#: code:addons/gamification/models/gamification_badge.py:0
msgid "You are not in the user allowed list."
msgstr "Bạn không có trong danh sách người dùng được phép"

#. module: gamification
#. odoo-python
#: code:addons/gamification/wizard/grant_badge.py:0
msgid "You can not grant a badge to yourself."
msgstr "Bạn không được trao huy hiệu cho chính mình"

#. module: gamification
#. odoo-python
#: code:addons/gamification/models/gamification_challenge.py:0
msgid "You can not reset a challenge with unfinished goals."
msgstr "Bạn không thể đặt lại một thách thức với các mục tiêu còn dang dở."

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
msgid "You can still grant"
msgstr "Bạn vẫn có thể trao"

#. module: gamification
#: model_terms:gamification.badge,description:gamification.badge_good_job
msgid "You did great at your job."
msgstr "Bạn đã làm rất tốt trong công việc của bạn."

#. module: gamification
#. odoo-python
#: code:addons/gamification/models/gamification_badge.py:0
msgid "You do not have the required badges."
msgstr "Bạn không có huy hiệu cần thiết."

#. module: gamification
#. odoo-python
#: code:addons/gamification/models/gamification_badge.py:0
msgid "You have already sent this badge too many time this month."
msgstr "Bạn đã gửi huy hiệu này quá nhiều lần trong tháng này."

#. module: gamification
#: model_terms:gamification.badge,description:gamification.badge_hidden
msgid "You have found the hidden badge"
msgstr "Bạn đã tìm thấy huy hiệu ẩn"

#. module: gamification
#: model_terms:gamification.karma.rank,description:gamification.rank_doctor
msgid "You have reached the last rank. Congratulations!"
msgstr "Bạn đã đạt được thứ hạng cao nhất. Xin chúc mừng!"

#. module: gamification
#: model_terms:gamification.karma.rank,description:gamification.rank_newbie
msgid "You just began the adventure! Welcome!"
msgstr "Bạn vừa mới bắt đầu cuộc phiêu lưu! Chào mừng bạn!"

#. module: gamification
#: model_terms:gamification.karma.rank,description:gamification.rank_master
msgid "You know what you are talking about. People learn from you."
msgstr "Bạn hiểu rõ những gì bạn nói. Mọi người học được nhiều điều từ bạn."

#. module: gamification
#: model_terms:gamification.karma.rank,description:gamification.rank_bachelor
msgid "You love learning things. Curiosity is a good way to progress."
msgstr "Bạn là người yêu học hỏi. Tò mò là cách hiệu quả giúp bạn tiến bộ."

#. module: gamification
#: model_terms:gamification.karma.rank,description:gamification.rank_student
msgid "You're a young padawan now. May the force be with you!"
msgstr "Bạn đã trở thành một padawan trẻ tuổi. Mong thần lực luôn bên bạn!"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
msgid "badges this month"
msgstr "huy hiệu tháng này"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "days"
msgstr "ngày"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_rank_view_form
msgid ""
"e.g. A Master Chief knows quite everything on the forum! You cannot beat "
"him!"
msgstr ""
"VD: Một chuyên gia biết mọi thứ trong diễn đàn! Không ai có thể vượt qua "
"được!"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_form_view
msgid "e.g. Get started"
msgstr "VD: Bắt đầu"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_rank_view_form
msgid "e.g. Master Chief"
msgstr "VD: Chuyên gia"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "e.g. Monthly Sales Objectives"
msgstr "VD: Mục tiêu doanh thu hàng tháng"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
msgid "e.g. Problem Solver"
msgstr "VD: Người giải quyết vấn đề"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_rank_view_form
msgid "e.g. Reach this rank to gain a free mug!"
msgstr "VD: Đạt tới thứ hạng này để nhận được cốc miễn phí!"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_form_view
msgid "e.g. Register to the platform"
msgstr "VD: Đăng ký với nền tảng"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_form_view
msgid "e.g. days"
msgstr "vd: ngày"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_form_view
msgid "e.g. user.partner_id.id"
msgstr "vd: user.partner_id.id"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_kanban_view
msgid "granted,"
msgstr "đã được trao, "

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "refresh"
msgstr "làm mới"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "than the target."
msgstr "hơn mục tiêu."

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_user_kanban_view
msgid "the"
msgstr " "

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_kanban_view
msgid "this month"
msgstr "tháng này"
