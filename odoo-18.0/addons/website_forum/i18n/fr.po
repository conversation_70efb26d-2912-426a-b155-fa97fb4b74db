# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_forum
# 
# Translators:
# Wil <PERSON>do<PERSON>, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
msgid " Flag"
msgstr "Signaler"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
msgid " Flagged"
msgstr "Signalé"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__total_answers
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_tree
msgid "# Answers"
msgstr "# Réponses"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__total_favorites
msgid "# Favorites"
msgstr "# Favoris"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__total_posts
msgid "# Posts"
msgstr "# Posts"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__total_views
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_tree
msgid "# Views"
msgstr "# Vues"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to accept or refuse an answer."
msgstr "%d karma requis pour accepter ou refuser une réponse."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to answer a question."
msgstr "%d karma requis pour répondre à une question."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to close or reopen a post."
msgstr "%d karma requis pour fermer ou rouvrir un post."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to comment."
msgstr "%d karma requis pour commenter."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to convert a comment to an answer."
msgstr "%d karma requis pour convertir un commentaire en réponse."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to convert an answer to a comment."
msgstr "%d karma requis pour convertir une réponse en commentaire."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to convert your comment to an answer."
msgstr "%d karma requis pour convertir votre commentaire en réponse."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_tag.py:0
msgid "%d karma required to create a new Tag."
msgstr "%d karma requis pour créer une nouvelle étiquette."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to create a new question."
msgstr "%d karma requis pour créer une nouvelle question."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to delete a comment."
msgstr "%d karma requis pour supprimer un commentaire."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to delete or reactivate a post."
msgstr "%d karma requis pour supprimer ou réactiver un post."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post_vote.py:0
msgid "%d karma required to downvote."
msgstr "%d karma requis pour voter négativement."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to edit a post."
msgstr "%d karma requis pour modifier un post."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to flag a post."
msgstr "%d karma requis pour signaler un post."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/controllers/website_forum.py:0
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to mark a post as offensive."
msgstr "%d karma requis pour marquer un post comme insultant."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to post an image or link."
msgstr "%d karma requis pour publier une image ou un lien."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to refuse a post."
msgstr "%d karma requis pour refuser un post."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to retag."
msgstr "%d karma requis pour réapposer une étiquette."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to unlink a post."
msgstr "%d karma requis pour délier un post."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post_vote.py:0
msgid "%d karma required to upvote."
msgstr "%d karma requis pour voter positivement."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to validate a post."
msgstr "%d karma requis pour valider un post."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index_tags
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/website_forum_tags_wrapper.xml:0
msgid "'Please enter 2 or more characters'"
msgstr "'Veuillez entrer 2 caractères ou plus'"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/website_forum_tags_wrapper.xml:0
msgid "'Tags'"
msgstr "'Étiquettes'"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "(The above section was adapted from Stackoverflow’s FAQ.)"
msgstr "(La section ci-dessus a été adaptée depuis la FAQ de Stackoverflow)"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
msgid "(votes - 1) **"
msgstr "(votes - 1) **"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
msgid "/ (days + 2) **"
msgstr "/ (jours + 2) **"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
msgid "45% of questions shared"
msgstr "45% des questions publiées"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
msgid ""
"65% more chance to get an\n"
"        answer"
msgstr "65% de chances en plus d'avoir une réponse"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid ""
"<b class=\"d-block \">This answer has been flagged</b>\n"
"                            As a moderator, you can either validate or reject this answer."
msgstr ""
"<b class=\"d-block \">Cette réponse a été signalée</b>\n"
"                            En tant que modérateur, vous pouvez soit valider, soit rejeter cette réponse."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid ""
"<b class=\"d-block\">You have a pending post</b>\n"
"                        Please wait for a moderator to validate your previous post to be allowed replying questions."
msgstr ""
"<b class=\"d-block\">Vous avez un post en attente</b>\n"
"Veuillez attendre qu'un modérateur valide votre post précédent pour pouvoir répondre aux questions."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers should not add or expand questions</b>. Instead either edit the "
"question or add a question comment."
msgstr ""
"<b>Les réponses ne doivent pas ajouter ou développer les questions</b>. "
"Modifiez plutôt la question ou ajoutez un commentaire."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers should not add or expand questions</b>. Instead, either edit the "
"question or add a comment."
msgstr ""
"<b>Les réponses ne devraient pas ajouter ou développer des questions</b>. "
"Modifiez plutôt la question ou ajoutez un commentaire."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers should not comment other answers</b>. Instead add a comment on "
"the other answers."
msgstr ""
"<b>Les réponses ne doivent pas commenter d'autres réponses</b>. Ajoutez "
"plutôt un commentaire sur les autres réponses."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers should not start debates</b> This community Q&amp;A is not a "
"discussion group. Please avoid holding debates in your answers as they tend "
"to dilute the essence of questions and answers. For brief discussions please"
" use commenting facility."
msgstr ""
"<b>Les réponses ne doivent pas lancer des débats</b>.Cette communauté de Q&R"
" n'est pas un groupe de discussion. Merci d'éviter de tenir des débats dans "
"vos réponses afin de minimiser le risque de diluer l'essence même de la "
"question et de la réponse. Pour une brève discussion, veuillez utiliser les "
"commentaires."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers shouldn't just point to other Questions</b>. Instead add a "
"question comment indication \"Possible duplicate of...\". However, it's ok "
"to include links to other questions or answers providing relevant additional"
" information."
msgstr ""
"<b>Les réponses ne devraient pas simplement renvoyer à d'autres "
"questions</b>. Ajoutez plutôt une indication dans les commentaires du type "
"\"Doublon probable de...\". Cependant, vous pouvez ajouter des liens à "
"d'autres questions ou réponses en fournissant des informations "
"supplémentaires."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers shouldn't just point to other questions</b>.Instead add a comment"
" indicating <i>\"Possible duplicate of...\"</i>. However, it's fine to "
"include links to other questions or answers providing relevant additional "
"information."
msgstr ""
"<b>Les réponses ne devraient pas simplement renvoyer à d'autres "
"questions</b>. Ajoutez plutôt une indication dans les commentaires, du type "
"<i>\"Doublon probable de...\"</i>. Cependant, vous pouvez ajouter des liens "
"à d'autres questions ou réponses fournissant des informations "
"supplémentaires utiles."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers shouldn't just provide a link a solution</b>. Instead provide the"
" solution description text in your answer, even if it's just a copy/paste. "
"Links are welcome, but should be complementary to answer, referring sources "
"or additional reading."
msgstr ""
"<b>Les réponses ne doivent pas simplement fournir un lien vers une "
"solution</b>. Fournissez le texte décrivant la solution dans votre réponse, "
"même s'il ne s'agit que d'un copier/coller. Les liens sont les bienvenus, "
"mais doivent être complémentaires pour répondre, renvoyer à des sources ou à"
" des lectures supplémentaires."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Before you ask - please make sure to search for a similar question.</b> "
"You can search questions by their title or tags. It’s also OK to answer your"
" own question."
msgstr ""
"<b>Avant de demander - faites une recherche pour une question similaire.</b> Vous pouvez chercher dans les questions via leur titre ou leurs étiquettes. Vous pouvez également\n"
"répondre à votre propre question."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Please avoid asking questions that are too subjective and "
"argumentative</b> or not relevant to this community."
msgstr ""
"<b>Veuillez éviter de poser des questions qui sont trop subjectives et "
"controversées</b> ou encore non pertinentes pour cette communauté."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
msgid ""
"<b>Please try to give a substantial answer.</b> If you wanted to comment on the question or answer, just\n"
"            <b>use the commenting tool.</b> Please remember that you can always <b>revise your answers</b>\n"
"            - no need to answer the same question twice. Also, please <b>don't forget to vote</b>\n"
"            - it really helps to select the best questions and answers!"
msgstr ""
"<b>Veuillez essayer de donner une réponse substantielle.</b> Si vous voulez commenter la question ou la réponse,\n"
" <b>utilisez simplement l'outil de commentaire.</b> Rappelez-vous que vous pouvez toujours <b>réviser vos réponses</b>\n"
"        - pas besoin de répondre deux fois à la même question. Aussi <b>n'oubliez pas de voter</b>\n"
"        - cela aide vraiment à sélectionner les meilleures questions et réponses !"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "<b>Why can other people edit my questions/answers?</b>"
msgstr ""
"<b>Pourquoi d'autres personnes peuvent modifier mes questions/réponses ?</b>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "<b>You already have a pending post.</b><br/>"
msgstr "<b>Vous avez déjà un post en attente.</b><br/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.contact
msgid ""
"<b>|</b>\n"
"                    <span class=\"fa fa-trophy o-text-gold ms-2\" role=\"img\" aria-label=\"Gold badge\" title=\"Gold badge\"/>"
msgstr ""
"<b>|</b>\n"
"                    <span class=\"fa fa-trophy o-text-gold ms-2\" role=\"img\" aria-label=\"Gold badge\" title=\"Badge d'or\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<br/>by"
msgstr "<br/>par"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "<i class=\"fa fa-angle-left me-2\"/>Back to All Posts"
msgstr "<i class=\"fa fa-angle-left me-2\"/>Retour à Tous les posts"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_votes
msgid "<i class=\"fa fa-arrow-right\"/> Go To Forums"
msgstr "<i class=\"fa fa-arrow-right\"/> Aller aux Forums"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "<i class=\"fa fa-bug me-1\"/>Filter Tool"
msgstr "<i class=\"fa fa-bug me-1\"/>Outil de filtrage"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.vote
msgid ""
"<i class=\"fa fa-caret-down\" data-bs-toggle=\"tooltip\" data-bs-"
"placement=\"top\" title=\"Downvote\"/>"
msgstr ""
"<i class=\"fa fa-caret-down\" data-bs-toggle=\"tooltip\" data-bs-"
"placement=\"top\" title=\"Vote négatif\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.vote
msgid ""
"<i class=\"fa fa-caret-up\" data-bs-toggle=\"tooltip\" data-bs-"
"placement=\"top\" title=\"Upvote\"/>"
msgstr ""
"<i class=\"fa fa-caret-up\" data-bs-toggle=\"tooltip\" data-bs-"
"placement=\"top\" title=\"Vote positif\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<i class=\"fa fa-check fa-fw me-1\"/>Accept"
msgstr "<i class=\"fa fa-check fa-fw me-1\"/>Accepter"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid ""
"<i class=\"fa fa-check fa-fw me-1\"/>Be less specific in your wording for a "
"wider search result."
msgstr ""
"<i class=\"fa fa-check fa-fw me-1\"/>Soyez moins précis dans votre "
"formulation pour obtenir un résultat de recherche plus large."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "<i class=\"fa fa-check fa-fw me-1\"/>Check your spelling and try again."
msgstr ""
"<i class=\"fa fa-check fa-fw me-1\"/>Vérifiez votre orthographe et "
"réessayez."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "<i class=\"fa fa-check fa-fw me-1\"/>Try searching for one or two words."
msgstr ""
"<i class=\"fa fa-check fa-fw me-1\"/>Essayez de rechercher un ou deux mots."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "<i class=\"fa fa-check me-1\"/>Solved"
msgstr "<i class=\"fa fa-check me-1\"/>Résolu"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.show_flag_validator
msgid "<i class=\"fa fa-check\"/> Accept"
msgstr "<i class=\"fa fa-check\"/> Accepter"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "<i class=\"fa fa-check\"/> How to configure TPS and TVQ's canadian taxes?"
msgstr ""
"<i class=\"fa fa-check\"/> Comment configurer les taxes canadiennes de TPS "
"et TVQ ?"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "<i class=\"fa fa-check\"/><span class=\"ms-2\">Accept</span>"
msgstr "<i class=\"fa fa-check\"/><span class=\"ms-2\">Accepter</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_kanban
msgid "<i class=\"fa fa-comments-o me-1\" title=\"Forum\"/>"
msgstr "<i class=\"fa fa-comments-o me-1\" title=\"Forum\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_dropdown
msgid ""
"<i class=\"fa fa-ellipsis-h\" data-bs-toggle=\"tooltip\" data-bs-"
"placement=\"top\" title=\"More\"/>"
msgstr ""
"<i class=\"fa fa-ellipsis-h\" data-bs-toggle=\"tooltip\" data-bs-"
"placement=\"top\" title=\"Plus\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_kanban
msgid "<i class=\"fa fa-eye me-1\" title=\"Views\"/>"
msgstr "<i class=\"fa fa-eye me-1\" title=\"Vues\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "<i class=\"fa fa-flag\"/> Country"
msgstr "<i class=\"fa fa-flag\"/>Pays"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "<i class=\"fa fa-font\"/> Text"
msgstr "<i class=\"fa fa-font\"/> Texte"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.follow
msgid "<i class=\"fa fa-fw fa-check me-1\"/>Following"
msgstr "<i class=\"fa fa-fw fa-check me-1\"/>Abonné"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_kanban
msgid "<i class=\"fa fa-globe me-1\" title=\"Website\"/>"
msgstr "<i class=\"fa fa-globe me-1\" title=\"Site Web\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_mobile
msgid "<i class=\"fa fa-info-circle fa-fw\"/> About this forum"
msgstr "<i class=\"fa fa-info-circle fa-fw\"/> À propos de ce forum"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid ""
"<i class=\"fa fa-pencil\"/>\n"
"                                Edit<span class=\"d-none d-lg-inline\"> your answer</span>"
msgstr ""
"<i class=\"fa fa-pencil\"/>\n"
"                                Modifier<span class=\"d-none d-lg-inline\"> votre réponse</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post
msgid ""
"<i class=\"fa fa-reply me-1 d-lg-none\"/>\n"
"                0"
msgstr ""
"<i class=\"fa fa-reply me-1 d-lg-none\"/>\n"
"                0"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid "<i class=\"fa fa-reply me-1\"/>Reply"
msgstr "<i class=\"fa fa-reply me-1\"/>Répondre"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid ""
"<i class=\"fa fa-share-alt\" data-bs-toggle=\"tooltip\" data-bs-"
"placement=\"top\" title=\"Share\"/>"
msgstr ""
"<i class=\"fa fa-share-alt\" data-bs-toggle=\"tooltip\" data-bs-"
"placement=\"top\" title=\"Partager\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_body
msgid "<i class=\"fa fa-shield fa-fw opacity-50\"/> Badges"
msgstr "<i class=\"fa fa-shield fa-fw opacity-50\"/> Badges"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid ""
"<i class=\"fa fa-thumbs-down text-danger ms-3\" role=\"img\" aria-"
"label=\"Negative votes\" title=\"Negative votes\"/>"
msgstr ""
"<i class=\"fa fa-thumbs-down text-danger ms-3\" role=\"img\" aria-"
"label=\"Votes négatifs\" title=\"Votes négatifs\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid ""
"<i class=\"fa fa-thumbs-up text-success\" role=\"img\" aria-label=\"Positive"
" votes\" title=\"Positive votes\"/>"
msgstr ""
"<i class=\"fa fa-thumbs-up text-success\" role=\"img\" aria-label=\"Votes "
"positifs\" title=\"Votes positifs\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<i class=\"fa fa-times fa-fw me-1\"/>Reject"
msgstr "<i class=\"fa fa-times fa-fw me-1\"/>Rejeter"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ""
"<i class=\"fa fa-times\"/> Good morning to all! Please, can someone help "
"solve my tax computation problem in Canada? Thanks!"
msgstr ""
"<i class=\"fa fa-times\"/> Bonjour à tous! Quelqu'un peut-il m'aider à "
"résoudre mon problème de calcul fiscal au Canada ? Merci !"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.show_flag_validator
msgid "<i class=\"fa fa-times\"/> Reject"
msgstr "<i class=\"fa fa-times\"/> Rejeter "

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "<i class=\"fa fa-times\"/><span class=\"ms-2\">Offensive</span>"
msgstr "<i class=\"fa fa-times\"/><span class=\"ms-2\">Insultant</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "<i class=\"fa fa-times\"/><span class=\"ms-2\">Reject</span>"
msgstr "<i class=\"fa fa-times\"/><span class=\"ms-2\">Rejeter</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "<i class=\"fa fa-user\"/> User"
msgstr "<i class=\"fa fa-user\"/> Utilisateur"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_body
msgid "<i class=\"fa fa-users fa-fw opacity-50\"/> People"
msgstr "<i class=\"fa fa-users fa-fw opacity-50\"/> Personnes"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "<i class=\"oi oi-arrow-right d-inline-block\"/> Go to Forums"
msgstr "<i class=\"oi oi-arrow-right d-inline-block\"/> Aller aux Forums"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.404
msgid ""
"<i class=\"oi oi-arrow-right display-inline-block\"/> Return to questions "
"list"
msgstr ""
"<i class=\"oi oi-arrow-right display-inline-block\"/> Retour à la liste des "
"questions"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_sub_nav
msgid "<i class=\"oi oi-chevron-left small\"/> Back"
msgstr "<i class=\"oi oi-chevron-left small\"/> Retour"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "<small class=\"fw-bold\">Votes</small>"
msgstr "<small class=\"fw-bold\">Votes</small>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_body
msgid "<small>(View all)</small>"
msgstr "<small>(Voir toutl)</small>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all_all_entries
msgid "<span class=\"badge bg-dark me-1\">Last post:</span>"
msgstr "<span class=\"badge bg-dark me-1\">Dernier post :</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.contact
msgid ""
"<span class=\"fa fa-trophy o-text-bronze ms-2\" role=\"img\" aria-"
"label=\"Bronze badge\" title=\"Bronze badge\"/>"
msgstr ""
"<span class=\"fa fa-trophy o-text-bronze ms-2\" role=\"img\" aria-"
"label=\"Bronze badge\" title=\"Badge de bronze\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.contact
msgid ""
"<span class=\"fa fa-trophy o-text-silver ms-2\" role=\"img\" aria-"
"label=\"Silver badge\" title=\"Silver badge\"/>"
msgstr ""
"<span class=\"fa fa-trophy o-text-silver ms-2\" role=\"img\" aria-"
"label=\"Silver badge\" title=\"Badge d'argent\"/>"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "<span class=\"flex-grow-1\">What kind of questions can I ask here?</span>"
msgstr ""
"<span class=\"flex-grow-1\">Quel type de questions puis-je poser ici "
"?</span>"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "<span class=\"flex-grow-1\">What should I avoid in my answers?</span>"
msgstr ""
"<span class=\"flex-grow-1\">Que devrais-je éviter dans mes réponses ?</span>"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "<span class=\"flex-grow-1\">What should I avoid in my questions?</span>"
msgstr ""
"<span class=\"flex-grow-1\">Que devrais-je éviter dans mes questions "
"?</span>"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<span class=\"flex-grow-1\">Why can other people edit my "
"questions/answers?</span>"
msgstr ""
"<span class=\"flex-grow-1\">Pourquoi d'autres personnes peuvent-elles "
"modifier mes questions/réponses ?</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ""
"<span class=\"form-text small text-muted d-block\">consider adding an "
"example.</span>"
msgstr ""
"<span class=\"form-text small text-muted d-block\">ajoutez éventuellement un"
" exemple.</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ""
"<span class=\"form-text small text-muted d-block\">select text to format "
"it.</span>"
msgstr ""
"<span class=\"form-text small text-muted d-block\">sélectionnez le texte "
"pour le formater.</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ""
"<span class=\"form-text small text-muted d-block\">use '/' to insert "
"images.</span>"
msgstr ""
"<span class=\"form-text small text-muted d-block\">utilisez '/' pour insérer"
" des images.</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ""
"<span class=\"form-text small text-muted me-1\"><i class=\"fa fa-"
"lightbulb-o\"/> Tip:</span>"
msgstr ""
"<span class=\"form-text small text-muted me-1\"><i class=\"fa fa-"
"lightbulb-o\"/> Astuce :</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
msgid "<span class=\"o_stat_text\">Favorites</span>"
msgstr "<span class=\"o_stat_text\">Favoris</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_form
msgid "<span class=\"o_stat_text\">Go to <br/>Website</span>"
msgstr "<span class=\"o_stat_text\">Aller au <br/>Site web</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
msgid "<span class=\"o_stat_text\">Posts</span>"
msgstr "<span class=\"o_stat_text\">Posts</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_activities
msgid "<span class=\"text-muted\">There is no activity yet.</span>"
msgstr "<span class=\"text-muted\">Il n'y a pas encore d'activité.</span>"

#. module: website_forum
#: model_terms:web_tour.tour,rainbow_man_message:website_forum.question
msgid "<span><b>Good job!</b> You went through all steps of this tour.</span>"
msgstr ""
"<span><b>Beau travail !</b> Vous avez terminé toutes les étapes de cette "
"visite.</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<span>Be the first to answer this question</span>"
msgstr ""
"<span>Soyez le premier ou la première à répondre à cette question</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "<span>Moderation</span>"
msgstr "<span>Modération</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid ""
"<span>Please wait for a moderator to validate your previous post to be "
"allowed to reply to questions.</span>"
msgstr ""
"<span>Attendez qu'un modérateur valide votre post précédent pour pouvoir "
"répondre aux questions.</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_header
msgid "<span>You need to be registered to interact with the community.</span>"
msgstr ""
"<span>Vous devez être inscrit pour interagir avec la communauté.</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_answer
msgid "A new answer on"
msgstr "Une nouvelle réponse sur"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_question
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_validation
msgid "A new question"
msgstr "Une nouvelle question"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_answer_accept_own
msgid "Accept an answer on own questions"
msgstr "Valider une réponse à ses propres questions"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_answer_accept_all
msgid "Accept an answer to all questions"
msgstr "Valider une réponse à toutes les questions"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_search
msgid "Accepted Answer"
msgstr "Réponse validée"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "Accepted answer removed"
msgstr "Réponse acceptée supprimée"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_answer_accept
msgid "Accepting an answer"
msgstr "Valider une réponse"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
msgid "Access Denied"
msgstr "Accès refusé"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_needaction
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_needaction
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_needaction
msgid "Action Needed"
msgstr "Nécessite une action"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__active
#: model:ir.model.fields,field_description:website_forum.field_forum_post__active
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post__state__active
msgid "Active"
msgstr "Actif"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Activities"
msgstr "Activités"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
#: model_terms:ir.ui.view,arch_db:website_forum.forum_related_posts
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Activity"
msgstr "Activité"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
msgid "Add a comment"
msgstr "Ajouter un commentaire"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__allow_share
msgid ""
"After posting the user will be proposed to share its question or answer on "
"social networks, enabling social network propagation of the forum content."
msgstr ""
"Après avoir publié, le système proposera à l'utilisateur de partager sa "
"question ou sa réponse sur les réseaux sociaux, permettant la propagation du"
" contenu du forum."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/controllers/website_forum.py:0
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "All"
msgstr "Tous"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_body
msgid "All Posts"
msgstr "Toutes les publications"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "All Topics"
msgstr "Tous les sujets"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "All forums"
msgstr "Tous les forums"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "Animation of a pen checking a checkbox"
msgstr "Animation d'un style cochant une case"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post_vote.py:0
msgid "Answer %s"
msgstr "Réponse %s"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
#: model:mail.message.subtype,description:website_forum.mt_answer_edit
#: model:mail.message.subtype,name:website_forum.mt_answer_edit
msgid "Answer Edited"
msgstr "Réponse modifiée"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_answer_accepted
msgid "Answer accepted"
msgstr "Réponse validée"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_6
msgid "Answer accepted with 15 or more votes"
msgstr "Réponse validée après 15 votes ou plus"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_answer_downvote
msgid "Answer downvoted"
msgstr "Réponse rejetée"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_answer_flagged
msgid "Answer flagged"
msgstr "Réponse signalée"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_answer
msgid "Answer questions"
msgstr "Répondre aux questions"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_answer_upvote
msgid "Answer upvoted"
msgstr "Réponse approuvée"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_4
msgid "Answer voted up 15 times"
msgstr "Réponse votée plus de 15 fois"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_2
msgid "Answer voted up 4 times"
msgstr "Réponse votée plus de 4 fois"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_3
msgid "Answer voted up 6 times"
msgstr "Réponse votée plus de 6 fois"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_5
msgid "Answer was accepted with 3 or more votes"
msgstr "Réponse validée avec au moins 3 votes"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Answer:"
msgstr "Réponse :"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__default_order__child_count_desc
msgid "Answered"
msgstr "Répondu"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_search
msgid "Answered Posts"
msgstr "Posts répondus"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_form
msgid "Answered by"
msgstr "Répondu par"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post
msgid "Answered on"
msgstr "Répondu le"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_8
msgid "Answered own question with at least 4 up votes"
msgstr "A répondu à sa propre question avec au moins 4 votes favorables"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__child_count
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_form
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_kanban
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_search
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Answers"
msgstr "Réponses"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_gamification_challenge__challenge_category
msgid "Appears in"
msgstr "Apparaît sur"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_search
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_form
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_search
msgid "Archived"
msgstr "Archivé"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
msgid "Are you sure you want to delete this comment?"
msgstr "Êtes-vous sûr de vouloir supprimer ce commentaire ?"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "As a moderator, you can either validate or reject this answer."
msgstr ""
"En tant que modérateur, vous pouvez soit valider, soit rejeter cette "
"réponse."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "Ask a new question"
msgstr "Poser une nouvelle question"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "Ask a question"
msgstr "Poser une question"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_ask
msgid "Ask questions"
msgstr "Poser des questions"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_post
msgid "Ask questions without validation"
msgstr "Poser une question sans validation"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Ask your question"
msgstr "Poser votre question"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_26
msgid "Asked a question and accepted an answer"
msgstr "A posé une question et validé une réponse"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_1
msgid "Asked a question with at least 150 views"
msgstr "A posé une question avec au moins 150 vues"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_2
msgid "Asked a question with at least 250 views"
msgstr "A posé une question avec au moins 250 vues"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_3
msgid "Asked a question with at least 500 views"
msgstr "A posé une question avec au moins 500 vues"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_7
msgid "Asked first question with at least one up vote"
msgstr "A posé sa première question avec au moins un vote favorable"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__create_date
msgid "Asked on"
msgstr "Posée le"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_question_new
msgid "Asking a question"
msgstr "Poser une question"

#. module: website_forum
#: model:ir.model,name:website_forum.model_ir_attachment
msgid "Attachment"
msgstr "Pièce jointe"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_attachment_count
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_attachment_count
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_attachment_count
msgid "Attachment Count"
msgstr "Nombre de pièces jointes"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_search
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid "Author"
msgstr "Auteur"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__authorized_group_id
msgid "Authorized Group"
msgstr "Groupe autorisé"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_p_1
msgid "Autobiographer"
msgstr "Autobiographe"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.author_box
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_header
msgid "Avatar"
msgstr "Avatar"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Back to Question"
msgstr "Retour à la question"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/controllers/website_forum.py:0
msgid "Bad \"filters\" value \"%(filters)s\"."
msgstr "Mauvaise valeur de \"filters\" \"%(filters)s\"."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/controllers/website_forum.py:0
msgid "Bad \"tag_char\" value \"%(tag_char)s\""
msgstr "Mauvaise valeur de \"tag_char\" \"%(tag_char)s\""

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/controllers/website_forum.py:0
msgid "Bad Request"
msgstr "Mauvaise requête"

#. module: website_forum
#: model:ir.ui.menu,name:website_forum.menu_forum_badges
msgid "Badges"
msgstr "Badges"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post_reason__reason_type__basic
msgid "Basic"
msgstr "Basique"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "Because there are no posts in this forum yet."
msgstr "Parce qu'il n'y a pas encore de posts dans ce forum."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid "Best Answer"
msgstr "Meilleure réponse"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
msgid "By sharing you answer, you will get additional"
msgstr "En partageant votre réponse, vous gagnerez plus de"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_accept
msgid "Can Accept"
msgstr "Peut valider "

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_answer
msgid "Can Answer"
msgstr "Peut répondre"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_ask
msgid "Can Ask"
msgstr "Peut demander"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_post
msgid "Can Automatically be Validated"
msgstr "Peut être automatiquement validé"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_close
msgid "Can Close"
msgstr "Peut fermer"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_comment
msgid "Can Comment"
msgstr "Peut commenter"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_comment_convert
msgid "Can Convert to Comment"
msgstr "Peut convertir en commentaire"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_downvote
msgid "Can Downvote"
msgstr "Peut voter négativement"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_edit
msgid "Can Edit"
msgstr "Peut modifier"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_flag
msgid "Can Flag"
msgstr "Peut signaler"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_moderate
msgid "Can Moderate"
msgstr "Peut modérer"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_unlink
msgid "Can Unlink"
msgstr "Peut délier"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_upvote
msgid "Can Upvote"
msgstr "Peut voter positivement"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_use_full_editor
msgid "Can Use Full Editor"
msgstr "Peut utiliser l'éditeur complet"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_view
msgid "Can View"
msgstr "Peut voir"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_edit_retag
msgid "Change question tags"
msgstr "Changer les étiquettes des questions"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_p_4
#: model:gamification.challenge,name:website_forum.challenge_chief_commentator
msgid "Chief Commentator"
msgstr "Commentateur en chef"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
msgid "Click here to accept this answer."
msgstr "Cliquez ici pour valider cette réponse."

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
msgid "Click to post your answer."
msgstr "Cliquez pour publier votre réponse."

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
msgid "Click to post your question."
msgstr "Cliquez pour publier votre question."

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
msgid "Click to reply."
msgstr "Cliquer pour répondre."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_dropdown
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_mobile
msgid "Close"
msgstr "Fermer"

#. module: website_forum
#: model:ir.ui.menu,name:website_forum.menu_forum_post_reasons
msgid "Close Reasons"
msgstr "Motifs de fermeture"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_close_all
msgid "Close all posts"
msgstr "Fermer tous les posts"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_close_own
msgid "Close own posts"
msgstr "Fermer ses propres posts"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.mark_as_offensive
msgid "Close post"
msgstr "Fermer le post"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post__state__close
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_body
msgid "Closed"
msgstr "Fermé"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Closed Posts"
msgstr "Posts fermés"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__closed_uid
msgid "Closed by"
msgstr "Fermé par"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__closed_date
msgid "Closed on"
msgstr "Fermé le"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.mark_as_offensive
msgid "Closing"
msgstr "Fermeture"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__name
msgid "Closing Reason"
msgstr "Motif de fermeture"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__color
msgid "Color"
msgstr "Couleur"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid "Comment"
msgstr "Commentaire"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_all
msgid "Comment all posts"
msgstr "Commenter tous les posts"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_own
msgid "Comment own posts"
msgstr "Commenter ses propres posts"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
msgid "Comment this post"
msgstr "Commenter ce post"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_p_2
#: model:gamification.challenge,name:website_forum.challenge_commentator
#: model:gamification.goal.definition,name:website_forum.definition_commentator
msgid "Commentator"
msgstr "Commentateur"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all_oe_structure_forum_all_top
msgid "Community Forums"
msgstr "Forums de la communauté"

#. module: website_forum
#: model:gamification.challenge,name:website_forum.challenge_configure_profile
msgid "Complete own biography"
msgstr "Compléter sa propre biographie"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_configure_profile
#: model_terms:gamification.badge,description:website_forum.badge_p_1
msgid "Completed own biography"
msgstr "A complété sa propre biographie"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_7
msgid "Contains offensive or malicious remarks"
msgstr "Contient des remarques insultantes ou malveillantes"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__content
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_search
msgid "Content"
msgstr "Contenu"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_convert_all
msgid "Convert all comments to answers"
msgstr "Convertir tous les commentaires en réponses"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_accept
msgid "Convert comment to answer"
msgstr "Convertir les commentaires en questions"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_convert_own
msgid "Convert own comments to answers"
msgstr "Convertir vos propres commentaires en réponses"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_dropdown
msgid "Convert to Comment"
msgstr "Convertir en commentaire"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
msgid "Convert to answer"
msgstr "Convertir en réponse"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__is_correct
msgid "Correct"
msgstr "Bonne réponse"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_post__is_correct
msgid "Correct answer or answer accepted"
msgstr "Réponse correcte ou validée"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__create_date
#: model:ir.model.fields,field_description:website_forum.field_res_users__create_date
msgid "Create Date"
msgstr "Date de création"

#. module: website_forum
#: model_terms:ir.actions.act_window,help:website_forum.forum_post_action
msgid "Create a new forum post"
msgstr "Créez un nouveau post sur le forum"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
msgid "Create a new post in this forum by clicking on the button."
msgstr "Créez un nouveau post dans ce forum en cliquant sur le bouton."

#. module: website_forum
#: model_terms:ir.actions.act_window,help:website_forum.forum_tag_action
msgid "Create a new tag"
msgstr "Créer une nouvelle étiquette"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_sign_up_cta
msgid ""
"Create an account today to enjoy exclusive features and engage with our "
"awesome community!"
msgstr ""
"Créez un compte dès aujourd'hui pour profiter de fonctionnalités exclusives "
"et échanger avec notre formidable communauté !"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_tag_create
msgid "Create new tags"
msgstr "Créer de nouvelles étiquettes"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/website_forum_tags_wrapper.xml:0
msgid "Create option \""
msgstr "Créer une option \""

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_32
msgid "Created a tag used by 15 questions"
msgstr "A créé une étiquette utilisée par 15 questions"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__create_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post__create_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__create_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__create_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__create_date
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__create_date
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__create_date
#: model_terms:ir.ui.view,arch_db:website_forum.display_post
msgid "Created on"
msgstr "Créé le"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_4
#: model:gamification.challenge,name:website_forum.challenge_favorite_question_1
msgid "Credible Question"
msgstr "Question crédible"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_5
#: model:gamification.challenge,name:website_forum.challenge_critic
#: model:gamification.goal.definition,name:website_forum.definition_critic
msgid "Critic"
msgstr "Critique"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_searchbar_input_snippet_options
msgid "Date"
msgstr "Date"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_searchbar_input_snippet_options
msgid "Date (high to low)"
msgstr "Date (récent à ancien)"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_searchbar_input_snippet_options
msgid "Date (low to high)"
msgstr "Date (ancien à récent)"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__default_order
msgid "Default"
msgstr "Par défaut"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
msgid "Default Sort"
msgstr "Tri par défaut"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_gamification_challenge__challenge_category
msgid "Define the visibility of the challenge through menus"
msgstr "Définir la visibilité du défi via les menus"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
#: model_terms:ir.ui.view,arch_db:website_forum.post_dropdown
msgid "Delete"
msgstr "Supprimer"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_unlink_all
msgid "Delete all comments"
msgstr "Supprimer tous les commentaires"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_unlink_all
msgid "Delete all posts"
msgstr "Supprimer tous les posts"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_unlink_own
msgid "Delete own comments"
msgstr "Supprimer vos propres commentaires"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_unlink_own
msgid "Delete own posts"
msgstr "Supprimer ses propres posts"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "Delete the accepted answer"
msgstr "Supprimer la réponse acceptée"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "Deleted"
msgstr "Supprimé"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_23
msgid "Deleted own post with 3 or more downvotes"
msgstr "A supprimé son propre post avec 3 votes négatifs ou plus"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_6
msgid "Deleted own post with 3 or more upvotes"
msgstr "A supprimé son propre post avec 3 votes positifs ou plus"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__description
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.forum_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Description"
msgstr "Description"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
msgid "Description visible on website"
msgstr "Description visible sur le site web"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.mark_as_offensive
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
msgid "Discard"
msgstr "Ignorer"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_6
#: model:gamification.challenge,name:website_forum.challenge_disciplined
#: model:gamification.goal.definition,name:website_forum.definition_disciplined
msgid "Disciplined"
msgstr "Discipliné"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__mode__discussions
msgid "Discussions (multiple answers)"
msgstr "Discussions (réponses multiples)"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_forum.py:0
#: model_terms:forum.forum,welcome_message:website_forum.forum_help
msgid "Dismiss"
msgstr "Annuler"

#. module: website_forum
#: model_terms:forum.forum,welcome_message:website_forum.forum_help
msgid "Dismiss message"
msgstr "Annuler le message"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__display_name
#: model:ir.model.fields,field_description:website_forum.field_forum_post__display_name
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__display_name
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__display_name
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__display_name
msgid "Display Name"
msgstr "Nom d'affichage"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_user_bio
msgid "Display detailed user biography"
msgstr "Afficher la biographie détaillée de l'utilisateur"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_downvote
#: model_terms:ir.ui.view,arch_db:website_forum.vote
msgid "Downvote"
msgstr "Voter négativement"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "Downvote for posting offensive contents"
msgstr "Vote négatif pour la publication de contenus insultants"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_1
msgid "Duplicate post"
msgstr "Dupliquer le post"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_dropdown
msgid "Edit"
msgstr "Modifier"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "Edit Answer"
msgstr "Modifier la réponse"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "Edit Question"
msgstr "Modifier la question"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_edit_all
msgid "Edit all posts"
msgstr "Modifier tous les posts"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_edit_own
msgid "Edit own posts"
msgstr "Modifier ses propres posts"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Edit your Post"
msgstr "Modifier votre post"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Edit your answer"
msgstr "Modifier votre réponse"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Edit your question"
msgstr "Modifier votre question"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_7
#: model:gamification.challenge,name:website_forum.challenge_editor
#: model:gamification.goal.definition,name:website_forum.definition_editor
msgid "Editor"
msgstr "Éditeur"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_editor
msgid "Editor Features: image and links"
msgstr "Fonctionnalités de l'éditeur: image et liens"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "Empty box"
msgstr "Case vide"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_sign_up_cta
msgid "Enjoying the discussion? Don't just read, join in!"
msgstr ""
"Vous appréciez la discussion ? Ne vous contentez pas de lire, rejoignez-nous"
" !"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_5
#: model:gamification.challenge,name:website_forum.challenge_enlightened
#: model:gamification.goal.definition,name:website_forum.definition_enlightened
msgid "Enlightened"
msgstr "Éclairé"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ""
"Example\n"
"                            <i class=\"fa fa-question-circle\"/>"
msgstr ""
"Exemple\n"
"<i class=\"fa fa-question-circle\"/>"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid "Facebook"
msgstr "Facebook"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_3
#: model:gamification.challenge,name:website_forum.challenge_famous_question
msgid "Famous Question"
msgstr "Question célèbre"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__favourite_count
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "Favorite"
msgstr "Favori"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_5
#: model:gamification.challenge,name:website_forum.challenge_favorite_question_5
msgid "Favorite Question"
msgstr "Question favorite"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__favourite_ids
msgid "Favourite"
msgstr "Favori"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_favorite_question_1
msgid "Favourite Question (1)"
msgstr "Question favorite (1)"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_stellar_question_25
msgid "Favourite Question (25)"
msgstr "Question favorite (25)"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_favorite_question_5
msgid "Favourite Question (5)"
msgstr "Question favorite (5)"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Favourite Questions"
msgstr "Questions favorites"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_body
msgid "Favourites"
msgstr "Favoris"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_post__last_activity_date
msgid ""
"Field to keep track of a post's last activity. Updated whenever it is "
"replied to, or when a comment is added on the post or one of its replies."
msgstr ""
"Champ permettant de suivre la dernière activité d'un post. Il est mis à jour"
" chaque fois qu'une réponse ou un commentaire est ajouté sur le post ou "
"l'une de ses réponses."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Filter by:"
msgstr "Filtrer par :"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__relevancy_post_vote
msgid "First Relevance Parameter"
msgstr "Paramètre d'adéquation"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_5
msgid "First downvote"
msgstr "Premier vote négatif"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_7
msgid "First edit"
msgstr "Première modification"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_31
msgid "First upvote"
msgstr "Premier vote positif"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_dropdown
msgid "Flag"
msgstr "Drapeau"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_flag
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_flag
msgid "Flag a post as offensive"
msgstr "Signaler un post comme insultant"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post__state__flagged
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_body
msgid "Flagged"
msgstr "Signalé"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__flag_user_id
msgid "Flagged by"
msgstr "Signalé par"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.follow
msgid "Follow"
msgstr "Suivre"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Followed Questions"
msgstr "Questions suivies"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "Followed Tags"
msgstr "Étiquettes suivies"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_follower_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_follower_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_follower_ids
msgid "Followers"
msgstr "Abonnés"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_partner_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_partner_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_partner_ids
msgid "Followers (Partners)"
msgstr "Abonnés (Partenaires)"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"For example, if you ask an interesting question or give a helpful answer, "
"your input will be upvoted. On the other hand if the answer is misleading - "
"it will be downvoted. Each vote in favor will generate 10 points, each vote "
"against will subtract 2 points. There is a limit of 200 points that can be "
"accumulated for a question or answer per day. The table given at the end "
"explains reputation point requirements for each type of moderation task."
msgstr ""
"Par exemple, si vous posez une question intéressante ou donnez une réponse "
"utile, votre participation sera votée favorablement. D'autre part, si la "
"réponse est trompeuse, cela vous sera défavorable. Chaque vote positif "
"génère 10 points, chaque vote négatif soustrait 2 points. Par jour, un "
"maximum de 200 points peuvent être accumulés pour une question ou une "
"réponse. Le tableau donné à la fin explique les exigences en matière de "
"points de réputation pour chaque type de tâche de modération."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/website.py:0
#: model:ir.actions.act_url,name:website_forum.action_open_forum
#: model:ir.model,name:website_forum.model_forum_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__forum_id
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__forum_id
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__forum_id
#: model:ir.ui.menu,name:website_forum.menu_website_forum_global
#: model:website.menu,name:website_forum.menu_website_forums
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_search
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_search
#: model_terms:ir.ui.view,arch_db:website_forum.gamification_karma_tracking_view_search
msgid "Forum"
msgstr "Forum"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_website__forum_count
msgid "Forum Count"
msgstr "Nombre de forums"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form_add
msgid "Forum Mode"
msgstr "Mode forum"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__name
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form_add
msgid "Forum Name"
msgstr "Nom du forum"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.snippet_options
msgid "Forum Page"
msgstr "Page du forum"

#. module: website_forum
#: model:ir.model,name:website_forum.model_forum_post
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_form
msgid "Forum Post"
msgstr "Post sur le forum"

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.forum_post_action
msgid "Forum Post Pages"
msgstr "Pages des posts du forum"

#. module: website_forum
#: model:ir.ui.menu,name:website_forum.menu_forum_post_pages
msgid "Forum Posts"
msgstr "Posts du forum"

#. module: website_forum
#: model:ir.model,name:website_forum.model_forum_tag
msgid "Forum Tag"
msgstr "Étiquette du forum"

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.forum_tag_action
msgid "Forum Tags"
msgstr "Étiquettes du forum"

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.forum_forum_action
#: model:ir.ui.menu,name:website_forum.menu_forum_global
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_tree
#: model_terms:ir.ui.view,arch_db:website_forum.forum_searchbar_input_snippet_options
msgid "Forums"
msgstr "Forums"

#. module: website_forum
#: model:ir.model,name:website_forum.model_gamification_challenge
msgid "Gamification Challenge"
msgstr "Défi de ludification"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.follow
msgid "Get notified when there's activity on this post"
msgstr "Recevez une notification lorsqu'il y a de l'activité sur ce poste"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.follow
msgid "Get notified when this tag is used"
msgstr "Recevez une notification lorsque cette étiquette est utilisée"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
msgid "Give your post title."
msgstr "Donnez un titre à votre post."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "Go back to the list of"
msgstr "Retour à la liste de"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "Go enjoy a cup of coffee."
msgstr "Allez prendre une tasse de café."

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_3
#: model:gamification.challenge,name:website_forum.challenge_good_answer
msgid "Good Answer"
msgstr "Bonne réponse"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_good_answer
msgid "Good Answer (6)"
msgstr "Bonne réponse (6)"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_9
#: model:gamification.challenge,name:website_forum.challenge_good_question
msgid "Good Question"
msgstr "Bonne question "

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_graph
msgid "Graph of Posts"
msgstr "Graphique des posts"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_4
#: model:gamification.challenge,name:website_forum.challenge_great_answer
msgid "Great Answer"
msgstr "Super réponse"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_great_answer
msgid "Great Answer (15)"
msgstr "Super réponse (15)"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_10
#: model:gamification.challenge,name:website_forum.challenge_great_question
msgid "Great Question"
msgstr "Super question"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.snippet_options
msgid "Grid"
msgstr "Grid"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_search
msgid "Group By"
msgstr "Regrouper par"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__faq
#: model_terms:ir.ui.view,arch_db:website_forum.faq
msgid "Guidelines"
msgstr "Lignes de conduite"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_6
#: model:gamification.challenge,name:website_forum.challenge_guru
msgid "Guru"
msgstr "Gourou"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_guru
msgid "Guru (15)"
msgstr "Gourou (15)"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__uid_has_answered
msgid "Has Answered"
msgstr "A répondu"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__has_message
#: model:ir.model.fields,field_description:website_forum.field_forum_post__has_message
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__has_message
msgid "Has Message"
msgstr "A un message"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__has_pending_post
msgid "Has pending post"
msgstr "A un post en attente"

#. module: website_forum
#: model:forum.forum,name:website_forum.forum_help
msgid "Help"
msgstr "Aide"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_votes
msgid "Help moderating the forums by upvoting and downvoting posts. <br/>"
msgstr ""
"Participer à la modération des forums en votant les posts positivement ou "
"négativement. <br/>"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "Here a table with the privileges and the karma level"
msgstr "Voici un tableau avec les privilèges et le niveau de karma"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__id
#: model:ir.model.fields,field_description:website_forum.field_forum_post__id
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__id
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__id
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__id
msgid "ID"
msgstr "ID"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_needaction
#: model:ir.model.fields,help:website_forum.field_forum_post__message_needaction
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Si coché, de nouveaux messages demandent votre attention."

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_has_error
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_has_sms_error
#: model:ir.model.fields,help:website_forum.field_forum_post__message_has_error
#: model:ir.model.fields,help:website_forum.field_forum_post__message_has_sms_error
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_has_error
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Si coché, certains messages ont une erreur de livraison."

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__karma_dofollow
msgid ""
"If the author has not enough karma, a nofollow attribute is added to links"
msgstr ""
"Si l'auteur n'a pas assez de karma, un attribut no-follow est ajouté aux "
"liens"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "If this approach is not for you, please respect the community."
msgstr ""
"Si cette approche ne vous convient pas, veuillez respecter la communauté."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.mark_as_offensive
msgid ""
"If you close this post, it will be hidden for most users. Only\n"
"            users having a high karma can see closed posts to moderate\n"
"            them."
msgstr ""
"Si vous fermez ce post, il sera masqué pour la plupart des utilisateurs. "
"Seuls les utilisateurs ayant un karma élevé peuvent voir les posts fermés, "
"afin de les modérer."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"If you fit in one of these example or if your motivation for asking the "
"question is “I would like to participate in a discussion about ______”, then"
" you should not be asking here but on our mailing lists. However, if your "
"motivation is “I would like others to explain ______ to me”, then you are "
"probably OK."
msgstr ""
"Si vous correspondez à l'un de ces exemples ou si votre motivation à poser "
"la question est “Je voudrai participer à une discussion au sujet de ______”,"
" dans ce cas vous devriez vous rendre sur nos listes de diffusion. "
"Cependant, si votre motivation est “J'aimerai que les autres m'expliquent "
"________” alors vous êtes au bon endroit. "

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.mark_as_offensive
msgid ""
"If you mark this post as offensive, it will be hidden for most users. Only\n"
"            users having a high karma can see offensive posts to moderate\n"
"            them."
msgstr ""
"Si vous signalez ce post comme insultant, il sera masqué pour la plupart des"
" utilisateurs. Seuls les utilisateurs ayant un karma élevé peuvent voir les "
"posts insultants, afin de les modérer."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__image_1920
msgid "Image"
msgstr "Image"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__image_1024
msgid "Image 1024"
msgstr "Image 1024"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__image_128
msgid "Image 128"
msgstr "Image 128"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__image_256
msgid "Image 256"
msgstr "Image 256"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__image_512
msgid "Image 512"
msgstr "Image 512"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_13
msgid "Inappropriate and unacceptable statements"
msgstr "Déclarations inappropriées et inacceptables"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
msgid "Insert tags related to your question."
msgstr "Insérez des étiquettes liées à votre question."

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_11
msgid "Insulting and offensive language"
msgstr "Langage insultant et offensant"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__user_favourite
msgid "Is Favourite"
msgstr "Est favori"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_is_follower
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_is_follower
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_is_follower
msgid "Is Follower"
msgstr "Est un abonné"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__can_moderate
msgid "Is a moderator"
msgstr "Est un modérateur"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__has_validated_answer
msgid "Is answered"
msgstr "A été répondu"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_display_biography
msgid "Is the author's biography visible from his post"
msgstr "C'est la biographie de l'auteur visible depuis ses posts"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post_vote.py:0
msgid "It is not allowed to modify someone else's vote."
msgstr "Il n'est pas permis de modifier le vote de quelqu'un d'autre."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post_vote.py:0
msgid "It is not allowed to vote for its own post."
msgstr "Il n'est pas permis de voter pour son propre post."

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
msgid "Karma Error"
msgstr "Erreur de karma"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
msgid "Karma Gains"
msgstr "Gains de karma"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
msgid "Karma Related Rights"
msgstr "Droits liés au Karma"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_close
msgid "Karma to close"
msgstr "Karma pour fermer"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_comment
msgid "Karma to comment"
msgstr "Karma pour commenter"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_comment_convert
msgid "Karma to convert comment to answer"
msgstr "Karma pour convertir un commentaire en réponse"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_edit
msgid "Karma to edit"
msgstr "Karma pour modifier"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_unlink
msgid "Karma to unlink"
msgstr "Karma pour délier"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post
msgid "Last Activity"
msgstr "Dernière activité"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__last_post_id
#: model_terms:ir.ui.view,arch_db:website_forum.snippet_options
msgid "Last Post"
msgstr "Dernier post"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__default_order__last_activity_date_desc
msgid "Last Updated"
msgstr "Dernière mise à jour"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__write_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__write_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__write_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__write_uid
msgid "Last Updated by"
msgstr "Mis à jour par"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__write_date
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__write_date
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__write_date
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__write_date
msgid "Last Updated on"
msgstr "Mis à jour le"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__last_activity_date
msgid "Last activity on"
msgstr "Dernière activité le"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.snippet_options
msgid "Layout"
msgstr "Agencement"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_25
msgid "Left 10 answers with score of 10 or more"
msgstr "A laissé 10 réponses avec un score de 10 ou plus"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_url
msgid "Link to questions with the tag"
msgstr "Lien vers les questions avec l'étiquette"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid "LinkedIn"
msgstr "LinkedIn"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.snippet_options
msgid "List"
msgstr "Liste"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid "Mark as Best Answer"
msgstr "Marquer comme meilleure réponse"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.show_flag_validator
msgid "Mark as Offensive"
msgstr "Marquer comme insultant"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.mark_as_offensive
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Mark as offensive"
msgstr "Marquer comme insultant"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Mark as spam"
msgstr "Marquer comme spam"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all_oe_structure_forum_all_top
msgid "Meet our community members"
msgstr "Rencontrez les membres de notre communauté"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_has_error
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_has_error
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_has_error
msgid "Message Delivery error"
msgstr "Erreur d'envoi du message"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_ids
msgid "Messages"
msgstr "Messages"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__mode
msgid "Mode"
msgstr "Mode"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_moderate
msgid "Moderate posts"
msgstr "Modérer les posts"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_body
msgid "Moderation tools"
msgstr "Outils de modération"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "More over:"
msgstr "Plus d'informations sur :"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "Most Used Tags"
msgstr "Étiquettes les plus utilisées"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__default_order__vote_count_desc
msgid "Most Voted"
msgstr "Le plus voté"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "Most used Tags"
msgstr "Étiquettes les plus utilisées"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__tag_most_used_ids
msgid "Most used tags"
msgstr "Étiquettes les plus utilisées"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "My Favorites"
msgstr "Mes favoris"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_body
msgid "My Posts"
msgstr "Mes posts"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__user_vote
msgid "My Vote"
msgstr "Mon vote"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_body
msgid "My forums"
msgstr "Mes forums"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_header
msgid "My profile"
msgstr "Mon profil"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__name
msgid "Name"
msgstr "Nom"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_votes
msgid "Negative vote"
msgstr "Vote négatif"

#. module: website_forum
#: model:mail.message.subtype,description:website_forum.mt_answer_new
#: model:mail.message.subtype,name:website_forum.mt_answer_new
#: model:mail.message.subtype,name:website_forum.mt_forum_answer_new
msgid "New Answer"
msgstr "Nouvelle réponse"

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.forum_forum_action_add
msgid "New Forum"
msgstr "Nouveau forum"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "New Post"
msgstr "Nouveau post"

#. module: website_forum
#: model:mail.message.subtype,description:website_forum.mt_question_new
#: model:mail.message.subtype,name:website_forum.mt_forum_question_new
#: model:mail.message.subtype,name:website_forum.mt_question_new
msgid "New Question"
msgstr "Nouvelle question"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__default_order__create_date_desc
msgid "Newest"
msgstr "Le plus récent"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_2
#: model:gamification.challenge,name:website_forum.challenge_nice_answer
msgid "Nice Answer"
msgstr "Chouette réponse"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_nice_answer
msgid "Nice Answer (4)"
msgstr "Chouette réponse (4)"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_8
#: model:gamification.challenge,name:website_forum.challenge_nice_question
msgid "Nice Question"
msgstr "Chouette question"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all
msgid "No forum is available yet."
msgstr "Forum pas encore disponible."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all_all_entries
msgid "No posts yet"
msgstr "Pas encore de post"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_dofollow
msgid "Nofollow links"
msgstr "Liens nofollow"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_4
msgid "Not a real post"
msgstr "N'est pas un vrai post"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_6
msgid "Not relevant or out dated"
msgstr "Pas pertinent ou obsolète"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_2
#: model:gamification.challenge,name:website_forum.challenge_notable_question
msgid "Notable Question"
msgstr "Question pertinente"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_needaction_counter
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_needaction_counter
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_needaction_counter
msgid "Number of Actions"
msgstr "Nombre d'actions"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__posts_count
msgid "Number of Posts"
msgstr "Nombre de posts"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_has_error_counter
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_has_error_counter
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_has_error_counter
msgid "Number of errors"
msgstr "Nombre d'erreurs"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__count_flagged_posts
msgid "Number of flagged posts"
msgstr "Nombre de posts signalés"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_needaction_counter
#: model:ir.model.fields,help:website_forum.field_forum_post__message_needaction_counter
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Nombre de messages nécessitant une action"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_has_error_counter
#: model:ir.model.fields,help:website_forum.field_forum_post__message_has_error_counter
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Nombre de messages avec des erreurs d'envoi"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__count_posts_waiting_validation
msgid "Number of posts waiting for validation"
msgstr "Nombre de posts en attente de validation"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_2
msgid "Off-topic or not relevant"
msgstr "Hors sujet ou non pertinent"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post__state__offensive
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post_reason__reason_type__offensive
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
#: model_terms:ir.ui.view,arch_db:website_forum.mark_as_offensive
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Offensive"
msgstr "Insultant"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
msgid "Offensive Post"
msgstr "Post insultant"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Offensive Posts"
msgstr "Posts insultants"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
msgid "Oh no! Please <a href=\"%s\">sign in</a> to perform this action"
msgstr ""
"Oh non ! Veuillez vous <a href=\"%s\">connecter</a> pour effectuer cette "
"action"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
msgid "On average,"
msgstr "En moyenne,"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "Oops!"
msgstr "Oups !"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
msgid "Options"
msgstr "Options"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
msgid "Order and Visibility"
msgstr "Ordre et visibilité"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_23
#: model:gamification.challenge,name:website_forum.challenge_peer_pressure
#: model:gamification.goal.definition,name:website_forum.definition_peer_pressure
msgid "Peer Pressure"
msgstr "Pression du groupe"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__plain_content
msgid "Plain Content"
msgstr "Contenu simplifié"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ""
"Please wait for a moderator to validate your previous post before "
"continuing."
msgstr ""
"Veuillez attendre qu'un modérateur valide votre post précédent avant de "
"continuer."

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_1
#: model:gamification.challenge,name:website_forum.challenge_popular_question
msgid "Popular Question"
msgstr "Question populaire"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_popular_question
msgid "Popular Question (150)"
msgstr "Question populaire (150)"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_notable_question
msgid "Popular Question (250)"
msgstr "Question populaire (250)"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_famous_question
msgid "Popular Question (500)"
msgstr "Question populaire (500)"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_votes
msgid "Positive vote"
msgstr "Vote positif"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__post_id
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_search
msgid "Post"
msgstr "Publier"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
msgid "Post Answer"
msgstr "Publier une réponse "

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__child_ids
msgid "Post Answers"
msgstr "Publier des réponses"

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.forum_post_reason_action
msgid "Post Close Reason"
msgstr "Motif de fermeture du post"

#. module: website_forum
#: model:ir.model,name:website_forum.model_forum_post_reason
msgid "Post Closing Reason"
msgstr "Motif de fermeture"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.snippet_options
msgid "Post Count"
msgstr "Nombre de posts"

#. module: website_forum
#: model:ir.model,name:website_forum.model_forum_post_vote
msgid "Post Vote"
msgstr "Publier un vote"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Post Your Question"
msgstr "Publier votre question"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "Post is closed and marked as offensive content"
msgstr "Le post est fermé et marqué comme insultant"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "Post is closed and marked as spam"
msgstr "Le post est fermé et marqué comme spam"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.mark_as_offensive
msgid "Post:"
msgstr "Post :"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_p_2
msgid "Posted 10 comments"
msgstr "10 commentaires publiés"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_p_4
msgid "Posted 100 comments"
msgstr "100 commentaires publiés"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "Posting answer on a [Deleted] or [Closed] question is not possible."
msgstr ""
"Il n'est pas possible de poster une réponse à une question [Supprimée] ou "
"[Clôturée]."

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.forum_post_action_forum_main
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__post_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__post_ids
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_search
msgid "Posts"
msgstr "Posts"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__privacy
msgid "Privacy"
msgstr "Confidentialité"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__privacy__public
msgid "Public"
msgstr "Public"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__privacy
msgid ""
"Public: Forum is public\n"
"Signed In: Forum is visible for signed in users\n"
"Some users: Forum and their content are hidden for non members of selected group"
msgstr ""
"Public : Le forum est public\n"
"Utilisateurs connectés : Le forum est visible pour les utilisateurs connectés\n"
"Certains utilisateurs : Le forum et ses contenus sont cachés pour les non membres des groupes sélectionnés"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_25
#: model:gamification.challenge,name:website_forum.challenge_pundit
#: model:gamification.goal.definition,name:website_forum.definition_pundit
msgid "Pundit"
msgstr "Expert"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
msgid "Put your answer here."
msgstr "Postez votre réponse ici."

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
msgid "Put your question here."
msgstr "Postez votre question ici."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__parent_id
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Question"
msgstr "Question"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post_vote.py:0
msgid "Question %s"
msgstr "Question %s"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
#: model:mail.message.subtype,description:website_forum.mt_question_edit
#: model:mail.message.subtype,name:website_forum.mt_question_edit
msgid "Question Edited"
msgstr "Questions modifiées"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_question_downvote
msgid "Question downvoted"
msgstr "Question votée négativement"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.404
msgid "Question not found!"
msgstr "Question non trouvée"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_4
msgid "Question set as favorite by 1 user"
msgstr "Question ajoutée en favori par 1 utilisateur"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_6
msgid "Question set as favorite by 25 users"
msgstr "Question ajoutée en favori par 25 utilisateurs"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_5
msgid "Question set as favorite by 5 users"
msgstr "Question ajoutée en favori par 5 utilisateurs"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/controllers/website_forum.py:0
msgid "Question should not be empty."
msgstr "La question ne peut pas être vide."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_question_upvote
msgid "Question upvoted"
msgstr "Question votée positivement"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_10
msgid "Question voted up 15 times"
msgstr "Question votée plus de 15 fois"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_8
msgid "Question voted up 4 times"
msgstr "Question votée plus de 4 fois"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_9
msgid "Question voted up 6 times"
msgstr "Question votée plus de 6 fois"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Question:"
msgstr "Question :"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Questions"
msgstr "Questions"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__mode__questions
msgid "Questions (1 answer)"
msgstr "Questions (1 réponse)"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__mode
msgid ""
"Questions mode: only one answer allowed\n"
" Discussions mode: multiple answers allowed"
msgstr ""
"Mode questions: une seule réponse autorisée\n"
"  Mode discussions: plusieurs réponses autorisées"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_15
msgid "Racist and hate speech"
msgstr "Les discours haineux ou raciste"

#. module: website_forum
#: model:ir.ui.menu,name:website_forum.menu_forum_rank_global
msgid "Ranks"
msgstr "Rangs"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__rating_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_post__rating_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__rating_ids
msgid "Ratings"
msgstr "Évaluations"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "Re: %s"
msgstr "Re : %s"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Read: #{question.name}"
msgstr "Lire : #{question.name}"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__closed_reason_id
msgid "Reason"
msgstr "Motif"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__reason_type
msgid "Reason Type"
msgstr "Type de motif"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.mark_as_offensive
msgid "Reason:"
msgstr "Motif :"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_reason_view_list
msgid "Reasons"
msgstr "Motifs"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_1
msgid "Received at least 3 upvote for an answer for the first time"
msgstr "Au moins 3 votes ont été reçus pour une réponse pour la première fois"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Refuse"
msgstr "Refuser"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_related_posts
msgid "Related Posts"
msgstr "Publications associées"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__relevancy
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__default_order__relevancy_desc
msgid "Relevance"
msgstr "Pertinence"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
msgid "Relevance Computation"
msgstr "Calcul de pertinence"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "Remove validated answer"
msgstr "Supprimer la réponse validée"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
#: model_terms:ir.ui.view,arch_db:website_forum.post_dropdown
msgid "Reopen"
msgstr "Rouvrir"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "Reopen a banned question"
msgstr "Rouvrir une question bannie"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
#: model_terms:ir.ui.view,arch_db:website_forum.forum_related_posts
#: model_terms:ir.ui.view,arch_db:website_forum.post_stats
msgid "Replies"
msgstr "Réponses"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_stats
msgid "Reply"
msgstr "Répondre"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/controllers/website_forum.py:0
msgid "Reply should not be empty."
msgstr "La réponse ne devrait pas être vide."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__self_reply
msgid "Reply to own question"
msgstr "Répondre à sa propre question"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__website_id
#: model:ir.model.fields,help:website_forum.field_forum_post__website_id
msgid "Restrict to a specific website."
msgstr "Restreindre à un site Web spécifique"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.profile_access_denied
msgid "Return to the forum"
msgstr "Retour au forum"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__moderator_id
msgid "Reviewed by"
msgstr "Revu par"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__is_seo_optimized
#: model:ir.model.fields,field_description:website_forum.field_forum_post__is_seo_optimized
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__is_seo_optimized
msgid "SEO optimized"
msgstr "SEO optimisé"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_has_sms_error
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_has_sms_error
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Erreur d'envoi SMS"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Save Changes"
msgstr "Enregistrer les changements"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_26
#: model:gamification.challenge,name:website_forum.challenge_scholar
#: model:gamification.goal.definition,name:website_forum.definition_scholar
msgid "Scholar"
msgstr "Erudit"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_search
msgid "Search in Post"
msgstr "Chercher dans les posts"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Search..."
msgstr "Rechercher…"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__relevancy_time_decay
msgid "Second Relevance Parameter"
msgstr "Deuxième paramètre d'adéquation"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/res_users.py:0
msgid "See our Forum"
msgstr "Voir notre forum"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_answer
msgid "See post"
msgstr "Voir le post"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_question
msgid "See question"
msgstr "Voir la question"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Select All"
msgstr "Tout sélectionner"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_8
#: model:gamification.challenge,name:website_forum.challenge_self_learner
#: model:gamification.goal.definition,name:website_forum.definition_self_learner
msgid "Self-Learner"
msgstr "Auto-didacte"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__seo_name
#: model:ir.model.fields,field_description:website_forum.field_forum_post__seo_name
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__seo_name
msgid "Seo name"
msgstr "SEO nom"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__sequence
msgid "Sequence"
msgstr "Séquence"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_forum.py:0
#: model_terms:forum.forum,welcome_message:website_forum.forum_help
msgid ""
"Share and discuss the best content and new marketing ideas, build your "
"professional profile and become a better marketer together."
msgstr ""
"Partagez et discutez du meilleur contenu et des nouvelles idées de "
"marketing, développez votre profil professionnel et devenez ensemble un "
"meilleur spécialiste du marketing."

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
msgid ""
"Share this content to increase your chances to be featured on the front page"
" and attract more visitors."
msgstr ""
"Partagez ce contenu pour augmenter vos chances d'être mis en avant sur la "
"première page et attirez plus de visiteurs."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__allow_share
msgid "Sharing Options"
msgstr "Options de partage"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index_tags
msgid "Show Tags Starting with"
msgstr "Montrer les étiquettes commençant par"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "Showing results for"
msgstr "Affichage des résultats pour"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_forum.py:0
#: model_terms:forum.forum,welcome_message:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.forum_sign_up_cta
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_header
msgid "Sign up"
msgstr "S'inscrire"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__privacy__connected
msgid "Signed In"
msgstr "Utilisateurs connectés"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "Solved"
msgstr "Résolu"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__privacy__private
msgid "Some users"
msgstr "Certains utilisateurs"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.404
msgid "Sorry, this question is not available anymore."
msgstr "Désolé, cette question n'est plus disponible."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "Sorry, we could not find any <b>solved</b> results"
msgstr "Désolé, nous n'avons trouvé aucun résultat <b>résolu</b>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "Sorry, we could not find any <b>unanswered</b> results"
msgstr "Désolé, nous n'avons trouvé aucun résultat <b>sans réponse</b> "

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "Sorry, we could not find any <b>unsolved</b> results"
msgstr "Désolé, nous n'avons trouvé aucun résultat <b>non résolu</b>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "Sorry, we could not find any results"
msgstr "Désolé, nous n'avons trouvé aucun résultat"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
msgid "Sorry, you cannot select your own posts as best answer"
msgstr ""
"Désolé, vous ne pouvez pas sélectionner vos propres posts comme étant la "
"meilleure réponse"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
msgid "Sorry, you cannot vote for your own posts"
msgstr "Il n'est pas possible de voter pour votre propre post. "

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Spam all post"
msgstr "Spammer tous les posts"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_8
msgid "Spam or advertising"
msgstr "Spam ou publicité"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "Start by creating a post"
msgstr "Commencez par créer un post"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__state
msgid "Status"
msgstr "Statut"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_6
#: model:gamification.challenge,name:website_forum.challenge_stellar_question_25
msgid "Stellar Question"
msgstr "Question stellaire"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_7
#: model:gamification.challenge,name:website_forum.challenge_student
msgid "Student"
msgstr "Étudiant"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.follow
msgid "Subscribe"
msgstr "S'inscrire"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_31
#: model:gamification.challenge,name:website_forum.challenge_supporter
#: model:gamification.goal.definition,name:website_forum.definition_supporter
msgid "Supporter"
msgstr "Supporter"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_search
#: model_terms:ir.ui.view,arch_db:website_forum.forum_tag_view_form
msgid "Tag"
msgstr "Étiquette"

#. module: website_forum
#: model:ir.model.constraint,message:website_forum.constraint_forum_tag_name_uniq
msgid "Tag name already exists!"
msgstr "Ce nom d'étiquette existe déjà !"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__tag_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_post__tag_ids
#: model:ir.ui.menu,name:website_forum.menu_forum_tag_global
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index_tags
#: model_terms:ir.ui.view,arch_db:website_forum.forum_tag_view_list
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_body
msgid "Tags"
msgstr "Étiquettes"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all_oe_structure_forum_all_top
msgid ""
"Tap into the collective knowledge of our community by asking your questions "
"in our forums,<br/> where helpful members are ready to assist you."
msgstr ""
"Puisez dans les connaissances collectives de notre communauté en postant vos"
" questions dans nos forums,<br/> où des membres compétents sont prêts à vous"
" aider."

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_32
#: model:gamification.challenge,name:website_forum.challenge_taxonomist
#: model:gamification.goal.definition,name:website_forum.definition_taxonomist
msgid "Taxonomist"
msgstr "Taxonomiste"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_1
#: model:gamification.challenge,name:website_forum.challenge_teacher
#: model:gamification.goal.definition,name:website_forum.definition_teacher
msgid "Teacher"
msgstr "Professeur"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__teaser
msgid "Teaser"
msgstr "Teaser"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
msgid "Thanks for posting!"
msgstr "Merci pour votre message ! "

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "The accepted answer is deleted"
msgstr "La réponse acceptée est supprimée"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid ""
"The goal of this site is create a relevant knowledge base that would answer "
"questions related to Odoo."
msgstr ""
"Le but de ce site est de créer une base de connaissances pertinente qui "
"répond aux questions liées à Odoo."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "The question has been closed"
msgstr "La question est close"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "There are no answers yet"
msgstr "Il n'y a pas encore de réponses"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index_tags
msgid "There are no tags being used in this forum."
msgstr "Aucune étiquette n'est utilisée dans ce forum."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index_tags
msgid "There are no tags matching the selected filter"
msgstr "Aucune étiquette ne correspond au filtre sélectionné"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index_tags
msgid "There are no tags matching the selected search."
msgstr "Aucune étiquette ne correspond à la recherche sélectionnée."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid ""
"Therefore questions and answers can be edited like wiki pages by experienced"
" users of this site in order to improve the overall quality of the knowledge"
" base content. Such privileges are granted based on user karma level: you "
"will be able to do the same once your karma gets high enough."
msgstr ""
"Par conséquent, les questions et réponses peuvent être modifiées comme des "
"pages wiki par des utilisateurs expérimentés de ce site afin d'améliorer la "
"qualité globale du contenu de la base de connaissances. Ces privilèges sont "
"accordés en fonction du niveau de karma de l'utilisateur : vous pourrez "
"faire de même une fois que votre karma sera suffisamment élevé."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"This community is for professional and enthusiast users, partners and "
"programmers. You can ask questions about:"
msgstr ""
"Cette communauté est destinée aux utilisateurs, partenaires et programmeurs "
"professionnels et passionnés. Vous pouvez poser des questions sur :"

#. module: website_forum
#: model:forum.forum,description:website_forum.forum_help
msgid ""
"This community is for professionals and enthusiasts of our products and "
"services. Share and discuss the best content and new marketing ideas, build "
"your professional profile and become a better marketer together."
msgstr ""
"Cette communauté est dédiée aux professionnels et utilisateurs enthousiastes"
" de nos produits et services. Partagez et discutez du meilleur contenu et de"
" nouvelles idées marketing, construisez votre profil professionnel et "
"devenez de meilleurs vendeurs tous ensemble."

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__relevancy_post_vote
msgid ""
"This formula is used in order to sort by relevance. The variable 'votes' "
"represents number of votes for a post, and 'days' is number of days since "
"the post creation"
msgstr ""
"Cette formule est utilisée afin de trier par pertinence. La variable 'votes'"
" représente le nombre de votes, et 'days' est le nombre de jours depuis la "
"création du message."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "This forum has been archived."
msgstr "Ce forum a été archivé."

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
msgid "This post can not be flagged"
msgstr "Ce post ne peut être signalé"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
msgid "This post is already flagged"
msgstr "Ce post a déjà été signalé"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "This post is awaiting validation"
msgstr "Ce post est en attente de validation"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid ""
"This post is currently awaiting moderation and is not published yet.<br/>\n"
"                    As a moderator you can either <b>Accept</b> or <b>Reject</b> this post."
msgstr ""
"Ce post est actuellement en attente de modération et n'est pas encore publié.<br/>\n"
"                    En tant que modérateur, vous pouvez soit <b>accepter</b> ou <b>rejeter</b> ce post."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "This question has been flagged"
msgstr "Cette question a été signalée"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "This user hasn't answered any questions yet. <br/>"
msgstr "Cet utilisateur n'a répondu à aucune question jusqu'à présent. <br/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "This user hasn't posted any questions yet.<br/>"
msgstr "Cet utilisateur n'a publié aucune question jusqu'à présent.<br/>"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_14
msgid "Threatening language"
msgstr "Langage menaçant "

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__name
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Title"
msgstr "Titre"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Title must not be empty"
msgstr "Le titre ne peut être vide"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/controllers/website_forum.py:0
msgid "Title should not be empty."
msgstr "Le titre ne devrait pas être vide"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__recipient_id
msgid "To"
msgstr "Vers"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_body
msgid "To Validate"
msgstr "À valider"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"To prevent your question from being flagged and possibly removed, avoid "
"asking subjective questions where …"
msgstr ""
"Pour éviter que votre question soit signalée et éventuellement supprimée, "
"évitez de poser des questions subjectives où…"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_9
msgid "Too localized"
msgstr "Trop localisé"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_3
msgid "Too subjective and argumentative"
msgstr "Trop subjectif et argumentatif"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index_tags
msgid "Toolbar with button groups"
msgstr "Barre d'outils avec groupes de boutons"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_tree
msgid "Total Answers"
msgstr "Total des réponses"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_tree
msgid "Total Posts"
msgstr "Total des posts"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_tree
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_tree
msgid "Total Views"
msgstr "Total des vues"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__vote_count
msgid "Total Votes"
msgstr "Total des votes"

#. module: website_forum
#: model:ir.model,name:website_forum.model_gamification_karma_tracking
msgid "Track Karma Changes"
msgstr "Suivre les changements de Karma"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid "Twitter"
msgstr "Twitter"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "Unanswered"
msgstr "Sans réponse"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_dropdown
msgid "Undelete"
msgstr "Annuler la suppression"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.follow
msgid "Unfollow"
msgstr "Ne plus suivre"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid "Unmark as Best Answer"
msgstr "Désélectionner comme meilleure réponse"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "Unsolved"
msgstr "Non résolu"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "Unused Tags"
msgstr "Étiquettes non utilisées"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__tag_unused_ids
msgid "Unused tags"
msgstr "Étiquettes non utilisées"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__write_uid
msgid "Updated by"
msgstr "Mis à jour par"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__write_date
msgid "Updated on"
msgstr "Mis à jour le "

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_upvote
#: model_terms:ir.ui.view,arch_db:website_forum.vote
msgid "Upvote"
msgstr "Voter positivement"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_student
msgid "Upvoted question (1)"
msgstr "Question votée (1)"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_great_question
msgid "Upvoted question (15)"
msgstr "Question votée (15)"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_nice_question
msgid "Upvoted question (4)"
msgstr "Question votée (4)"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_good_question
msgid "Upvoted question (6)"
msgstr "Question votée (6)"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Use a clear, explicit and concise title"
msgstr "Utilisez un titre clair, explicite et concis"

#. module: website_forum
#: model:ir.model,name:website_forum.model_res_users
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__user_id
msgid "User"
msgstr "Utilisateur"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "User answer accepted"
msgstr "Réponse de l'utilisateur acceptée"

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.forum_post_action_favorites
msgid "Users favorite posts"
msgstr "Posts favoris des utilisateurs"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
#: model_terms:ir.ui.view,arch_db:website_forum.post_dropdown
#: model_terms:ir.ui.view,arch_db:website_forum.show_flag_validator
msgid "Validate"
msgstr "Valider"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "Validate an answer"
msgstr "Valider une réponse"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_validation
msgid "Validate question"
msgstr "Valider la question"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_stats
msgid "View"
msgstr "Vue"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid "View my answer <i class=\"oi oi-arrow-right ms-1\"/>"
msgstr "Voir ma réponse <i class=\"oi oi-arrow-right ms-1\"/>"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__views
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
#: model_terms:ir.ui.view,arch_db:website_forum.forum_related_posts
#: model_terms:ir.ui.view,arch_db:website_forum.post_stats
msgid "Views"
msgstr "Vues"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_12
msgid "Violent language"
msgstr "Langage violent"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__vote
msgid "Vote"
msgstr "Voter"

#. module: website_forum
#: model:ir.model.constraint,message:website_forum.constraint_forum_post_vote_vote_uniq
msgid "Vote already exists!"
msgstr "Le vote existe déjà !"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__vote_ids
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Votes"
msgstr "Votes"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post__state__pending
msgid "Waiting Validation"
msgstr "En attente de validation"

#. module: website_forum
#: model:ir.model,name:website_forum.model_website
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_id
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_id
msgid "Website"
msgstr "Site Web"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__gamification_challenge__challenge_category__forum
msgid "Website / Forum"
msgstr "Site web / Forum"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_message_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_message_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_message_ids
msgid "Website Messages"
msgstr "Messages du site web"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_url
msgid "Website URL"
msgstr "URL de site web"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__website_message_ids
#: model:ir.model.fields,help:website_forum.field_forum_post__website_message_ids
#: model:ir.model.fields,help:website_forum.field_forum_tag__website_message_ids
msgid "Website communication history"
msgstr "Historique de communication du site web"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_meta_description
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_meta_description
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_meta_description
msgid "Website meta description"
msgstr "Méta-description du site web"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_meta_keywords
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_meta_keywords
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_meta_keywords
msgid "Website meta keywords"
msgstr "Méta mots-clés site web"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_meta_title
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_meta_title
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_meta_title
msgid "Website meta title"
msgstr "Méta titre site web"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_meta_og_img
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_meta_og_img
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_meta_og_img
msgid "Website opengraph image"
msgstr "Image opengraph site web "

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__welcome_message
msgid "Welcome Message"
msgstr "Message d'accueil"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_forum.py:0
#: model_terms:forum.forum,welcome_message:website_forum.forum_help
msgid "Welcome!"
msgstr "Bienvenue !"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"When a question or answer is upvoted, the user who posted them will gain "
"some points, which are called \"karma points\". These points serve as a "
"rough measure of the community trust to him/her. Various moderation tasks "
"are gradually assigned to the users based on those points."
msgstr ""
"Quand une question ou réponse est votée, l'utilisateur qui l'a publié gagne "
"des points, qui sont appelés \"points de karma\". Ces points servent de "
"mesure approximative pour la confiance de la communauté envers cette "
"personne. Différentes tâches de modération sont attribuées graduellement à "
"l'utilisateur sur base de ces points."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Write a clear, explicit and concise title"
msgstr "Écrivez un titre clair, explicite et concis"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_header
msgid "XP"
msgstr "XP"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "You already have a pending post"
msgstr "Vous avez déjà un post en attente"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
msgid "You can share your question once it has been validated"
msgstr "Vous pouvez partager votre question dès qu'elle a été validée."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid "You can't vote for your own post"
msgstr "Vous ne pouvez pas voter pour votre propre post"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "You cannot create recursive forum posts."
msgstr "Vous ne pouvez pas créer des posts de forum récursifs. "

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
msgid "You cannot post an empty answer"
msgstr "Vous ne pouvez pas publier une réponse vide"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid "You don't have enough karma"
msgstr "Vous n'avez pas assez de karma"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "You have a pending post"
msgstr "Vous avez un post en attente"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "You have not answered any questions yet. <br/>"
msgstr "Vous n'avez encore répondu à aucune question. <br/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "You have not posted any questions yet. <br/>"
msgstr "Vous n'avez encore publié aucune question. <br/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_votes
msgid "You haven't given any votes yet."
msgstr "Vous n'avez pas encore voté."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "You may now participate in our forums."
msgstr "Vous pouvez maintenant participer à nos forums."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "You need to have sufficient karma to edit tags"
msgstr "Vous devez avoir un meilleur karma pour modifier les étiquettes"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"You should only ask practical, answerable questions based on actual problems"
" that you face. Chatty, open-ended questions diminish the usefulness of this"
" site and push other questions off the front page."
msgstr ""
"Vous ne devriez poser que des questions pratiques, auxquelles il est "
"possible de répondre et qui sont basées sur des problèmes réels que vous "
"rencontrez. Les questions ouvertes et qui portent débat diminuent l'utilité "
"de ce site et poussent les autres questions hors de la première page."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "You're following this post"
msgstr "Vous suivez ce poste"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "You've Completely Caught&amp;nbsp;Up!"
msgstr "Vous êtes à jour !"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
msgid "Your Answer"
msgstr "Votre réponse"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "Your favourite"
msgstr "Votre favori"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "accept any answer"
msgstr "valider toute réponse"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "and join this Forum"
msgstr "et rejoignez ce forum"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index_tags
msgid "and search"
msgstr "et rechercher"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.author_box
msgid "at"
msgstr "à"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "breadcrumb"
msgstr "breadcrumb"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "by"
msgstr "par"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "close any posts"
msgstr "fermer tout post"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "delete any comment"
msgstr "supprimer tout commentaire"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "delete any question or answer"
msgstr "supprimer toute question ou réponse"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "delete own comment"
msgstr "supprimer son propre commentaire"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "downvote"
msgstr "vote négatif"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post_vote.py:0
msgid "downvoted"
msgstr "vote négatif"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
msgid "e.g. Help"
msgstr "par ex. Aide"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form_add
msgid "e.g. Technical Assistance"
msgstr "par ex. Assistance technique"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_form
msgid "e.g. When should I plant my tomatoes?"
msgstr "par ex. Quand dois-je planter mes tomates ?"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "edit any post, view offensive flags"
msgstr "modifier tout post, voir les posts signalés insultants"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "every answer is equally valid: “What’s your favorite ______?”"
msgstr "chaque réponse est valable: \"Quel est votre _______ préféré ?\""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "flag offensive, close own questions"
msgstr ""
"signaler des publications comme insultantes, fermer vos propres questions"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "for reason:"
msgstr "pour la raison suivante :"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_validation
msgid ""
"has been posted and require your validation. Click here to access the "
"question :"
msgstr ""
"a été publiée et nécessite votre validation. Cliquez ici pour accéder à la "
"question :"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_answer
msgid "has been posted. Click here to access the post :"
msgstr "a été publiée. Cliquez ici pour accéder à la publication :"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_question
msgid "has been posted. Click here to access the question :"
msgstr "a été publiée. Cliquez ici pour accéder à la question :"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
msgid "here"
msgstr "ici"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "how to configure or customize Odoo to specific business needs,"
msgstr ""
"comment configurer ou personnaliser Odoo aux besoins spécifiques de "
"l'entreprise,"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "how to develop modules for your own need,"
msgstr "comment développer des modules pour vos propres besoins,"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "how to install Odoo on a specific infrastructure,"
msgstr "Comment installer Odoo pour une infrastructure spécifique,"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
msgid ""
"if your\n"
"        answer is selected as the right one. See what you can do with karma"
msgstr ""
"si votre\n"
"réponse est sélectionnée comme la réponse correcte. Regardez ce que vous pouvez faire avec vos points de karma"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "in your favourites"
msgstr "dans vos favoris"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "in your posts"
msgstr "dans vos posts"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "insert text link, upload files"
msgstr "insérer des liens hypertextes, charger des documents"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "instead."
msgstr "à la place."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "it is a rant disguised as a question: “______ sucks, am I right?”"
msgstr "C'est une critique déguisée en question: \"_____ est nul, pas vrai ?\""

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
msgid "karma is required to perform this action. "
msgstr "karma requis pour effectuer cette action."

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
msgid "karma points"
msgstr "points de karma"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "matching \""
msgstr "correspondant à \""

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post_vote.py:0
msgid "no changes"
msgstr "aucun changement"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post_vote.py:0
msgid "no more downvoted"
msgstr "plus de vote négatif"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post_vote.py:0
msgid "no more upvoted"
msgstr "plus de vote positif"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_question
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_validation
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "on"
msgstr "le"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
msgid ""
"on social networks get an answer within\n"
"        5 hours. Questions shared on two social networks have"
msgstr ""
"sur les réseaux sociaux obtiennent une réponse endéans\n"
"les 5 heures. Les questions partagées sur deux réseaux sociaux ont"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all_all_entries
msgid "post"
msgstr "post"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all_all_entries
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "posts"
msgstr "post"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "specific questions about Odoo service offers, etc."
msgstr "questions spécifiques sur les services, offres,... d'Odoo."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "tag"
msgstr "étiquette"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index_tags
msgid "tags"
msgstr "étiquettes"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"there is no actual problem to be solved: “I’m curious if other people feel "
"like I do.”"
msgstr ""
"il n'y a pas de problème réel à résoudre: \"Je suis curieux de savoir si "
"d'autres personnes ressentent la même chose que moi.\""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "upvote, add comments"
msgstr "voter positivement, ajouter des commentaires"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post_vote.py:0
msgid "upvoted"
msgstr "vote positif"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "using the"
msgstr "utilisant"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"we are being asked an open-ended, hypothetical question: “What if ______ "
"happened?”"
msgstr ""
"on nous pose une question ouverte et hypothétique : \"Que se passerait-il si"
"  ________ ?\""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "what's the best way to use Odoo for a specific business need,"
msgstr ""
"quelle est la meilleure manière d'utiliser Odoo pour un besoin spécifique de"
" l'entreprise,"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.author_box
msgid "xp"
msgstr "xp"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"your answer is provided along with the question, and you expect more "
"answers: “I use ______ for ______, what do you use?”"
msgstr ""
"votre réponse est fournie avec la question et d'autres réponses sont "
"attendues: \"J'utilise_____ pour _____, qu'utilisez-vous ?\""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "your biography can be seen as tooltip"
msgstr "votre biographie est visible en infobulle"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.follow
msgid "your email..."
msgstr "votre adresse e-mail..."
