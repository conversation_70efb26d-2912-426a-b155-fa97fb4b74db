# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_forum
# 
# Translators:
# Wil <PERSON>, 2024
# <PERSON><PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON>, 2025\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
msgid " Flag"
msgstr "ธง"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
msgid " Flagged"
msgstr "ปักธงแล้ว"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__total_answers
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_tree
msgid "# Answers"
msgstr "# คำตอบ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__total_favorites
msgid "# Favorites"
msgstr "# รายการโปรด"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__total_posts
msgid "# Posts"
msgstr "# โพสต์"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__total_views
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_tree
msgid "# Views"
msgstr "# ยอดเข้าชม"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to accept or refuse an answer."
msgstr "%dคะแนน Karma ที่ต้องยอมรับหรือปฏิเสธคำตอบ"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to answer a question."
msgstr "%dคะแนน Karma เพื่อตอบคำถาม"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to close or reopen a post."
msgstr "%dคะแนน Karma เพื่่อปิดหรือเปิดโพสต์ใหม่อีกครั้ง"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to comment."
msgstr "%dคะแนน Karma เพื่อแสดงความคิดเห็น"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to convert a comment to an answer."
msgstr "%d คะแนน Karma จำเป็น เพื่อแปลงความคิดเห็นเป็นคำตอบ"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to convert an answer to a comment."
msgstr "%dคะแนน Karma จำเป็น เพื่อแปลงความคิดเห็นเป็นคำตอบ"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to convert your comment to an answer."
msgstr "%d  คะแนน Karma จำเป็น เพื่อแปลงความคิดเห็นเป็นคำตอบ"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_tag.py:0
msgid "%d karma required to create a new Tag."
msgstr "%d  คะแนน Karma จำเป็น เพื่อสร้างแท็กใหม่"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to create a new question."
msgstr "%d คะแนน Karma จำเป็น เพื่อสร้างคำถามใหม่"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to delete a comment."
msgstr "%d karma จำเป็นต้องลบความคิดเห็น"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to delete or reactivate a post."
msgstr "%dคะแนน Karma จำเป็น เพื่อลบหรือเปิดใช้งานโพสต์อีกครั้ง"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post_vote.py:0
msgid "%d karma required to downvote."
msgstr "%d คะแนน Karma จำเป็น เพื่อโหวตไม่เห็นด้วย"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to edit a post."
msgstr "%d คะแนน Karma จำเป็น เพื่อแก้ไขโพสต์"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to flag a post."
msgstr "%d คะแนน Karma จำเป็น เพื่อตั้งค่าสถานะโพสต์"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/controllers/website_forum.py:0
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to mark a post as offensive."
msgstr "%d คะแนน Karma จำเป็น เพื่อตั้งค่าโพสต์เป็นละเมิด"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to post an image or link."
msgstr "%d คะแนน Karma จำเป็น เพื่อโพสต์ภาพหรือลิงก์"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to refuse a post."
msgstr "%d คะแนน Karma จำเป็น เพื่อปฏิเสธโพสต์"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to retag."
msgstr "%dคะแนน Karma จำเป็น เพื่อแท็กอีกครั้ง"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to unlink a post."
msgstr "%dคะแนน Karma จำเป็น เพื่อยกเลิกการเชื่อมโยงโพสต์"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post_vote.py:0
msgid "%d karma required to upvote."
msgstr "%d คะแนน Karma จำเป็น เพื่อโหวตไม่เห็นด้วย"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to validate a post."
msgstr "%dคะแนน Karma จำเป็น เพื่อตรวจสอบโพสต์"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index_tags
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/website_forum_tags_wrapper.xml:0
msgid "'Please enter 2 or more characters'"
msgstr ""

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/website_forum_tags_wrapper.xml:0
msgid "'Tags'"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "(The above section was adapted from Stackoverflow’s FAQ.)"
msgstr "(ส่วนด้านบนนี้ดัดแปลงมาจากคำถามที่พบบ่อยของ Stackoverflow)"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
msgid "(votes - 1) **"
msgstr "(โหวต - 1) **"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
msgid "/ (days + 2) **"
msgstr "/ (วัน + 2) **"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
msgid "45% of questions shared"
msgstr "45% ของคำถามถูกแชร์"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
msgid ""
"65% more chance to get an\n"
"        answer"
msgstr ""
"65% ของการเพิ่มโอกาสเพื่อได้รับ\n"
"        คำตอบ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid ""
"<b class=\"d-block \">This answer has been flagged</b>\n"
"                            As a moderator, you can either validate or reject this answer."
msgstr ""
"<b class=\"d-block \">คำตอบนี้ถูกตั้งค่าสถานะ</b>\n"
"                            ในฐานะผู้ดูแล คุณสามารถตรวจสอบหรือปฏิเสธคำตอบนี้ได้"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid ""
"<b class=\"d-block\">You have a pending post</b>\n"
"                        Please wait for a moderator to validate your previous post to be allowed replying questions."
msgstr ""
"<b class=\"d-block\">คุณมีโพสต์ที่ค้างอยู่</b>\n"
"                       โปรดรอให้ผู้ดูแลตรวจสอบโพสต์ก่อนหน้าของคุณจึงจะได้รับอนุญาตให้ตอบคำถามได้"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers should not add or expand questions</b>. Instead either edit the "
"question or add a question comment."
msgstr ""
"<b>คำตอบไม่ควรเพิ่มหรือขยายคำถาม</b> "
"แก้ไขคำถามหรือเพิ่มความคิดเห็นของคำถามแทน"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers should not add or expand questions</b>. Instead, either edit the "
"question or add a comment."
msgstr ""
"<b>คำตอบไม่ควรเพิ่มหรือขยายคำถาม</b> ให้แก้ไขคำถามหรือเพิ่มความคิดเห็นแทน"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers should not comment other answers</b>. Instead add a comment on "
"the other answers."
msgstr ""
"<b>คำตอบไม่ควรแสดงความคิดเห็นต่อคำตอบอื่น ๆ </b> "
"ให้เพิ่มความคิดเห็นในคำตอบอื่น ๆ แทน"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers should not start debates</b> This community Q&amp;A is not a "
"discussion group. Please avoid holding debates in your answers as they tend "
"to dilute the essence of questions and answers. For brief discussions please"
" use commenting facility."
msgstr ""
"<b>คำตอบไม่ควรเริ่มด้วยการโต้เถียง</b> คำถามและคำตอบของชุมชนนี้ "
"ไม่ใช่กลุ่มสำหรับการสนทนา โปรดหลีกเลี่ยงการโต้เถียงในคำตอบของคุณ "
"เนื่องจากมักจะทำให้สาระสำคัญของคำถามและคำตอบเจือจางลง สำหรับการอภิปรายสั้น ๆ"
" โปรดใช้เครื่องมืออำนวยความสะดวกในการแสดงความคิดเห็น"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers shouldn't just point to other Questions</b>. Instead add a "
"question comment indication \"Possible duplicate of...\". However, it's ok "
"to include links to other questions or answers providing relevant additional"
" information."
msgstr ""
"<b>คำตอบไม่ควรชี้ไปที่คำถามอื่น ๆ</b> "
"ให้เพิ่มการบ่งชี้ความคิดเห็นของคำถามแทน \"อาจซ้ำกับ...\" อย่างไรก็ตาม "
"การรวมลิงก์ไปยังคำถามหรือคำตอบอื่น ๆ "
"ที่ให้ข้อมูลเพิ่มเติมที่เกี่ยวข้องนั้นเป็นเรื่องธรรมดา"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers shouldn't just point to other questions</b>.Instead add a comment"
" indicating <i>\"Possible duplicate of...\"</i>. However, it's fine to "
"include links to other questions or answers providing relevant additional "
"information."
msgstr ""
"<b>คำตอบไม่ควรชี้ไปที่คำถามอื่นเท่านั้น</b>แทนที่จะเพิ่มความคิดเห็นที่ระบุ "
"<i>\"อาจซ้ำซ้อนของ...\"</i>อย่างไรก็ตาม การรวมลิงก์ไปยังคำถามหรือคำตอบอื่นๆ "
"ที่ให้ข้อมูลเพิ่มเติมที่เกี่ยวข้องนั้นเป็นเรื่องปกติ"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers shouldn't just provide a link a solution</b>. Instead provide the"
" solution description text in your answer, even if it's just a copy/paste. "
"Links are welcome, but should be complementary to answer, referring sources "
"or additional reading."
msgstr ""
"<b>คำตอบไม่ควรให้แค่ลิงก์เพื่อแก้ปัญหา</b> "
"ให้ใส่ข้อความคำอธิบายโซลูชันในคำตอบของคุณ "
"แม้ว่าจะเป็นเพียงการคัดลอก/วางก็ตาม ลิงก์นั้นสามารถใช้ได้ "
"แต่ควรเพื่อเสริมคำตอบ อ้างอิงแหล่งที่มา หรือการอ่านเพิ่มเติม"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Before you ask - please make sure to search for a similar question.</b> "
"You can search questions by their title or tags. It’s also OK to answer your"
" own question."
msgstr ""
"<b>ก่อนที่คุณจะถาม - โปรดตรวจสอบให้แน่ใจว่าได้ค้นหาคำถามที่คล้ายกัน</b> "
"คุณสามารถค้นหาคำถามตามชื่อหรือแท็กได้ "
"นอกจากนี้ยังสามารถตอบคำถามของคุณเองได้อีกด้วย"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Please avoid asking questions that are too subjective and "
"argumentative</b> or not relevant to this community."
msgstr ""
"<b>โปรดหลีกเลี่ยงคำถามที่เป็นความคิดเห็นส่วนตัวและมีข้อโต้แย้งมากเกินไป</b> "
"หรือไม่มีความเกี่ยวข้องกับชุมชนนี้"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
msgid ""
"<b>Please try to give a substantial answer.</b> If you wanted to comment on the question or answer, just\n"
"            <b>use the commenting tool.</b> Please remember that you can always <b>revise your answers</b>\n"
"            - no need to answer the same question twice. Also, please <b>don't forget to vote</b>\n"
"            - it really helps to select the best questions and answers!"
msgstr ""
"<b>โปรดพยายามให้คำตอบที่เป็นรูปธรรม</b>หากคุณต้องการแสดงความคิดเห็นเกี่ยวกับคำถามหรือคำตอบ เพียงแค่\n"
"            <b>ใช้เครื่องมือเพื่อแสดงความคิดเห็น</b> โปรดจำไว้ว่าคุณสามารถ <b>ทบทวนคำตอบของคุณ</b>ได้เสมอ\n"
"            - ไม่จำเป็นต้องตามคำถามเดิมเป็นครั้งที่สอง และโปรดจำไว้ว่า <b>อย่าลืมการโหวต</b>\n"
"            - มันดีที่สุดในการช่วยคัดเลือกคำถามและคำตอบที่ดีที่สุด!"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "<b>Why can other people edit my questions/answers?</b>"
msgstr "<b>เหตุใดบุคคลอื่นจึงสามารถแก้ไขคำถาม/คำตอบของฉันได้?</b>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "<b>You already have a pending post.</b><br/>"
msgstr "<b>คุณมีโพสต์ที่่ค้างอยู่</b><br/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.contact
msgid ""
"<b>|</b>\n"
"                    <span class=\"fa fa-trophy o-text-gold ms-2\" role=\"img\" aria-label=\"Gold badge\" title=\"Gold badge\"/>"
msgstr ""
"<b>|</b>\n"
"                    <span class=\"fa fa-trophy o-text-gold ms-2\" role=\"รูปภาพ\" aria-label=\"เหรียญทอง\" title=\"เหรียญทอง\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<br/>by"
msgstr "<br/>โดย"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "<i class=\"fa fa-angle-left me-2\"/>Back to All Posts"
msgstr "<i class=\"fa fa-angle-left me-2\"/>กลับไปที่โพสต์ทั้งหมด"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_votes
msgid "<i class=\"fa fa-arrow-right\"/> Go To Forums"
msgstr "<i class=\"fa fa-arrow-right\"/> ไปที่ฟอรั่ม"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "<i class=\"fa fa-bug me-1\"/>Filter Tool"
msgstr "<i class=\"fa fa-bug me-1\"/>เครื่องมือกรอง"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.vote
msgid ""
"<i class=\"fa fa-caret-down\" data-bs-toggle=\"tooltip\" data-bs-"
"placement=\"top\" title=\"Downvote\"/>"
msgstr ""
"<i class=\"fa fa-caret-down\" data-bs-toggle=\"เคล็ดลับเครื่องมือ\" data-bs-"
"placement=\"บน\" title=\"โหวตลง\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.vote
msgid ""
"<i class=\"fa fa-caret-up\" data-bs-toggle=\"tooltip\" data-bs-"
"placement=\"top\" title=\"Upvote\"/>"
msgstr ""
"<i class=\"fa fa-caret-up\" data-bs-toggle=\"เคล็ดลับเครื่องมือ\" data-bs-"
"placement=\"บน\" title=\"โหวตขึ้น\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<i class=\"fa fa-check fa-fw me-1\"/>Accept"
msgstr "<i class=\"fa fa-check fa-fw me-1\"/>ยอมรับ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid ""
"<i class=\"fa fa-check fa-fw me-1\"/>Be less specific in your wording for a "
"wider search result."
msgstr ""
"<i class=\"fa fa-check fa-fw "
"me-1\"/>ใช้คำเฉพาะเจาะจงน้อยลงเพื่อให้ได้ผลการค้นหาที่กว้างขึ้น"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "<i class=\"fa fa-check fa-fw me-1\"/>Check your spelling and try again."
msgstr "<i class=\"fa fa-check fa-fw me-1\"/>ตรวจสอบการสะกดของคุณแล้วลองอีกครั้ง"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "<i class=\"fa fa-check fa-fw me-1\"/>Try searching for one or two words."
msgstr "<i class=\"fa fa-check fa-fw me-1\"/>ลองค้นหาหนึ่งหรือสองคำ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "<i class=\"fa fa-check me-1\"/>Solved"
msgstr "<i class=\"fa fa-check me-1\"/>แก้ไขแล้ว"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.show_flag_validator
msgid "<i class=\"fa fa-check\"/> Accept"
msgstr "<i class=\"fa fa-check\"/>ยอมรับ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "<i class=\"fa fa-check\"/> How to configure TPS and TVQ's canadian taxes?"
msgstr ""
"<i class=\"fa fa-check\"/> จะกำหนดค่าภาษีแคนาดาของ TPS และ TVQ ได้อย่างไร?"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "<i class=\"fa fa-check\"/><span class=\"ms-2\">Accept</span>"
msgstr "<i class=\"fa fa-check\"/><span class=\"ms-2\">ยอมรับ</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_kanban
msgid "<i class=\"fa fa-comments-o me-1\" title=\"Forum\"/>"
msgstr "<i class=\"fa fa-comments-o me-1\" title=\"ฟอรั่ม\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_dropdown
msgid ""
"<i class=\"fa fa-ellipsis-h\" data-bs-toggle=\"tooltip\" data-bs-"
"placement=\"top\" title=\"More\"/>"
msgstr ""
"<i class=\"fa fa-ellipsis-h\" data-bs-toggle=\"เคล็ดลับเครื่องมือ\" data-bs-"
"placement=\"บน\" title=\"เพิ่มเติม\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_kanban
msgid "<i class=\"fa fa-eye me-1\" title=\"Views\"/>"
msgstr "<i class=\"fa fa-eye me-1\" title=\"จำนวนการดู\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "<i class=\"fa fa-flag\"/> Country"
msgstr "<i class=\"fa fa-flag\"/> ประเทศ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "<i class=\"fa fa-font\"/> Text"
msgstr "<i class=\"fa fa-font\"/> ข้อความ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.follow
msgid "<i class=\"fa fa-fw fa-check me-1\"/>Following"
msgstr "<i class=\"fa fa-fw fa-check me-1\"/>กำลังติดตาม"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_kanban
msgid "<i class=\"fa fa-globe me-1\" title=\"Website\"/>"
msgstr "<i class=\"fa fa-globe me-1\" title=\"เว็บไซต์\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_mobile
msgid "<i class=\"fa fa-info-circle fa-fw\"/> About this forum"
msgstr "<i class=\"fa fa-info-circle fa-fw\"/> เกี่ยวกับฟอรั่มนี้"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid ""
"<i class=\"fa fa-pencil\"/>\n"
"                                Edit<span class=\"d-none d-lg-inline\"> your answer</span>"
msgstr ""
"<i class=\"fa fa-pencil\"/>\n"
"                                แก้ไข<span class=\"d-none d-lg-inline\">คำตอบของคุณ</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post
msgid ""
"<i class=\"fa fa-reply me-1 d-lg-none\"/>\n"
"                0"
msgstr ""
"<i class=\"fa fa-reply me-1 d-lg-none\"/>\n"
"                0"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid "<i class=\"fa fa-reply me-1\"/>Reply"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid ""
"<i class=\"fa fa-share-alt\" data-bs-toggle=\"tooltip\" data-bs-"
"placement=\"top\" title=\"Share\"/>"
msgstr ""
"<i class=\"fa fa-share-alt\" data-bs-toggle=\"เคล็ดลับเครื่องมือ\" data-bs-"
"placement=\"บน\" title=\"แชร์\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_body
msgid "<i class=\"fa fa-shield fa-fw opacity-50\"/> Badges"
msgstr "<i class=\"fa fa-shield fa-fw opacity-50\"/> เหรียญรางวัล"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid ""
"<i class=\"fa fa-thumbs-down text-danger ms-3\" role=\"img\" aria-"
"label=\"Negative votes\" title=\"Negative votes\"/>"
msgstr ""
"<i class=\"fa fa-thumbs-down text-danger ms-3\" role=\"รูปภาพ\" aria-"
"label=\"คะแนนเสียงเชิงลบ\" title=\"คะแนนเสียงเชิงลบ\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid ""
"<i class=\"fa fa-thumbs-up text-success\" role=\"img\" aria-label=\"Positive"
" votes\" title=\"Positive votes\"/>"
msgstr ""
"<i class=\"fa fa-thumbs-up text-success\" role=\"img\" aria-"
"label=\"โหวตด้านบวก\" title=\"Positive votes\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<i class=\"fa fa-times fa-fw me-1\"/>Reject"
msgstr "<i class=\"fa fa-times fa-fw me-1\"/>ปฏิเสธ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ""
"<i class=\"fa fa-times\"/> Good morning to all! Please, can someone help "
"solve my tax computation problem in Canada? Thanks!"
msgstr ""
"<i class=\"fa fa-times\"/> อรุณสวัสดิ์ทุกคน! ได้โปรด "
"ใครสามารถช่วยแก้ปัญหาการคำนวณภาษีของฉันในแคนาดาได้บ้าง ขอบคุณ!"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.show_flag_validator
msgid "<i class=\"fa fa-times\"/> Reject"
msgstr "<i class=\"fa fa-times\"/> ปฏิเสธ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "<i class=\"fa fa-times\"/><span class=\"ms-2\">Offensive</span>"
msgstr "<i class=\"fa fa-times\"/><span class=\"ms-2\">ก้าวร้าว</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "<i class=\"fa fa-times\"/><span class=\"ms-2\">Reject</span>"
msgstr "<i class=\"fa fa-times\"/><span class=\"ms-2\">ปฏิเสธ</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "<i class=\"fa fa-user\"/> User"
msgstr "<i class=\"fa fa-user\"/> ผู้ใช้"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_body
msgid "<i class=\"fa fa-users fa-fw opacity-50\"/> People"
msgstr "<i class=\"fa fa-users fa-fw opacity-50\"/> คน"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "<i class=\"oi oi-arrow-right d-inline-block\"/> Go to Forums"
msgstr "<i class=\"oi oi-arrow-right d-inline-block\"/> ไปที่ฟอรั่ม"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.404
msgid ""
"<i class=\"oi oi-arrow-right display-inline-block\"/> Return to questions "
"list"
msgstr ""
"<i class=\"oi oi-arrow-right display-inline-block\"/> กลับไปยังรายการคำถาม"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_sub_nav
msgid "<i class=\"oi oi-chevron-left small\"/> Back"
msgstr "<i class=\"oi oi-chevron-left small\"/>กลับ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "<small class=\"fw-bold\">Votes</small>"
msgstr "<small class=\"fw-bold\">โหวต</small>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_body
msgid "<small>(View all)</small>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all_all_entries
msgid "<span class=\"badge bg-dark me-1\">Last post:</span>"
msgstr "<span class=\"badge bg-dark me-1\">โพสต์ล่าสุด:</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.contact
msgid ""
"<span class=\"fa fa-trophy o-text-bronze ms-2\" role=\"img\" aria-"
"label=\"Bronze badge\" title=\"Bronze badge\"/>"
msgstr ""
"<span class=\"fa fa-trophy o-text-bronze ms-2\" role=\"รูปภาพ\" aria-"
"label=\"เหรียญทองแดง\" title=\"เหรียญทองแดง\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.contact
msgid ""
"<span class=\"fa fa-trophy o-text-silver ms-2\" role=\"img\" aria-"
"label=\"Silver badge\" title=\"Silver badge\"/>"
msgstr ""
"<span class=\"fa fa-trophy o-text-silver ms-2\" role=\"รูปภาพ\" aria-"
"label=\"เหรียญเงิน\" title=\"เหรียญเงิน\"/>"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "<span class=\"flex-grow-1\">What kind of questions can I ask here?</span>"
msgstr "<span class=\"flex-grow-1\">ฉันสามารถถามคำถามอะไรได้บ้างที่นี่?</span>"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "<span class=\"flex-grow-1\">What should I avoid in my answers?</span>"
msgstr "<span class=\"flex-grow-1\">ฉันควรหลีกเลี่ยงอะไรในคำตอบของฉันบ้าง?</span>"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "<span class=\"flex-grow-1\">What should I avoid in my questions?</span>"
msgstr "<span class=\"flex-grow-1\">ฉันควรหลีกเลี่ยงอะไรในคำถามของฉันบ้าง?</span>"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<span class=\"flex-grow-1\">Why can other people edit my "
"questions/answers?</span>"
msgstr ""
"<span class=\"flex-"
"grow-1\">เพราะเหตุใดบุคคลอื่นสามารถแก้ไขคำถาม/คำตอบของฉันได้?</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ""
"<span class=\"form-text small text-muted d-block\">consider adding an "
"example.</span>"
msgstr ""
"<span class=\"form-text small text-muted d-block\">ลองเพิ่มตัวอย่าง</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ""
"<span class=\"form-text small text-muted d-block\">select text to format "
"it.</span>"
msgstr ""
"<span class=\"form-text small text-muted "
"d-block\">เลือกข้อความเพื่อจัดรูปแบบ</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ""
"<span class=\"form-text small text-muted d-block\">use '/' to insert "
"images.</span>"
msgstr ""
"<span class=\"form-text small text-muted d-block\">ใช้ '/' "
"เพื่อแทรกรูปภาพ</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ""
"<span class=\"form-text small text-muted me-1\"><i class=\"fa fa-"
"lightbulb-o\"/> Tip:</span>"
msgstr ""
"<span class=\"form-text small text-muted me-1\"><i class=\"fa fa-"
"lightbulb-o\"/> เคล็ดลับ:</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
msgid "<span class=\"o_stat_text\">Favorites</span>"
msgstr "<span class=\"o_stat_text\">รายการโปรด</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_form
msgid "<span class=\"o_stat_text\">Go to <br/>Website</span>"
msgstr "<span class=\"o_stat_text\">ไปที่ <br/>เว็บไซต์</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
msgid "<span class=\"o_stat_text\">Posts</span>"
msgstr "<span class=\"o_stat_text\">โพสต์</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_activities
msgid "<span class=\"text-muted\">There is no activity yet.</span>"
msgstr "<span class=\"text-muted\">ยังไม่มีกิจกรรม</span>"

#. module: website_forum
#: model_terms:web_tour.tour,rainbow_man_message:website_forum.question
msgid "<span><b>Good job!</b> You went through all steps of this tour.</span>"
msgstr "<span><b>ทำได้ดีมาก!</b> คุณได้ผ่านทุกขั้นตอนของทัวร์นี้แล้ว</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<span>Be the first to answer this question</span>"
msgstr "<span>เป็นคนแรกที่จะตอบคำถามนี้</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "<span>Moderation</span>"
msgstr "<span>การกลั่นกรอง</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid ""
"<span>Please wait for a moderator to validate your previous post to be "
"allowed to reply to questions.</span>"
msgstr ""
"<span>โปรดรอให้ผู้ดูแลตรวจสอบโพสต์ก่อนหน้าของคุณจึงจะสามารถตอบคำถามได้</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_header
msgid "<span>You need to be registered to interact with the community.</span>"
msgstr "<span>คุณต้องลงทะเบียนเพื่อโต้ตอบกับคอมมูนิตี้</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_answer
msgid "A new answer on"
msgstr "คำตอบใหม่บน"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_question
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_validation
msgid "A new question"
msgstr "คำถามใหม่"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_answer_accept_own
msgid "Accept an answer on own questions"
msgstr "ยอมรับคำตอบสำหรับคำถามของตัวเอง"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_answer_accept_all
msgid "Accept an answer to all questions"
msgstr "ยอมรับคำตอบของทุกคำถาม"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_search
msgid "Accepted Answer"
msgstr "ยอมรับคำตอบแล้ว"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "Accepted answer removed"
msgstr "ลบคำตอบที่ยอมรับแล้ว"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_answer_accept
msgid "Accepting an answer"
msgstr "ยอมรับคำตอบ"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
msgid "Access Denied"
msgstr "การเข้าถึงถูกปฏิเสธ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_needaction
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_needaction
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_needaction
msgid "Action Needed"
msgstr "จำเป็นต้องดำเนินการ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__active
#: model:ir.model.fields,field_description:website_forum.field_forum_post__active
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post__state__active
msgid "Active"
msgstr "เปิดใช้งาน"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Activities"
msgstr "กิจกรรม"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
#: model_terms:ir.ui.view,arch_db:website_forum.forum_related_posts
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Activity"
msgstr "กิจกรรม"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
msgid "Add a comment"
msgstr "เพิ่มความคิดเห็น"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__allow_share
msgid ""
"After posting the user will be proposed to share its question or answer on "
"social networks, enabling social network propagation of the forum content."
msgstr ""
"หลังจากโพสต์ ผู้ใช้จะได้รับการเสนอให้แชร์คำถามหรือคำตอบบนสื่อสังคมต่าง ๆ "
"ซึ่งทำให้สามารถเผยแพร่เนื้อหาในฟอรั่มสื่อสังคมได้"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/controllers/website_forum.py:0
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "All"
msgstr "ทั้งหมด"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_body
msgid "All Posts"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "All Topics"
msgstr "หัวข้อทั้งหมด"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "All forums"
msgstr "ฟอรั่มทั้งหมด"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "Animation of a pen checking a checkbox"
msgstr "ภาพเคลื่อนไหวของปากกากำลังเลือกช่องทำเครื่องหมาย"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post_vote.py:0
msgid "Answer %s"
msgstr "คำตอบ %s"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
#: model:mail.message.subtype,description:website_forum.mt_answer_edit
#: model:mail.message.subtype,name:website_forum.mt_answer_edit
msgid "Answer Edited"
msgstr "แก้ไขคำตอบ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_answer_accepted
msgid "Answer accepted"
msgstr "ยอมรับคำตอบแล้ว"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_6
msgid "Answer accepted with 15 or more votes"
msgstr "คำตอบถูกยอมรับด้วย 15 โหวตหรือมากกว่า"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_answer_downvote
msgid "Answer downvoted"
msgstr "คำตอบถูกโหวตไม่เห็นด้วย"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_answer_flagged
msgid "Answer flagged"
msgstr "คำตอบถูกตั้งค่าสถานะ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_answer
msgid "Answer questions"
msgstr "ตอบคำถาม"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_answer_upvote
msgid "Answer upvoted"
msgstr "คำตอบถูกโหวตเห็นด้วย"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_4
msgid "Answer voted up 15 times"
msgstr "คำตอบถูกโหวตเห็นด้วย 15 ครั้ง"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_2
msgid "Answer voted up 4 times"
msgstr "คำตอบถูกโหวตเห็นด้วย 4 ครั้ง"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_3
msgid "Answer voted up 6 times"
msgstr "คำตอบถูกโหวตเห็นด้วย 6 ครั้ง"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_5
msgid "Answer was accepted with 3 or more votes"
msgstr "คำตอบถูกยอมรับด้วย 3 โหวตหรือมากกว่า"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Answer:"
msgstr "คำตอบ:"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__default_order__child_count_desc
msgid "Answered"
msgstr "ตอบ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_search
msgid "Answered Posts"
msgstr "ตอบโพสต์"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_form
msgid "Answered by"
msgstr "ตอบแล้วโดย"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post
msgid "Answered on"
msgstr "ตอบเมื่อ"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_8
msgid "Answered own question with at least 4 up votes"
msgstr "ตอบคำถามของตัวเองด้วยคะแนนโหวตเห็นด้วยอย่างน้อย 4 คะแนน"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__child_count
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_form
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_kanban
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_search
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Answers"
msgstr "คำตอบ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_gamification_challenge__challenge_category
msgid "Appears in"
msgstr "ปรากฎใน"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_search
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_form
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_search
msgid "Archived"
msgstr "เก็บถาวรแล้ว"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
msgid "Are you sure you want to delete this comment?"
msgstr "คุณแน่ใจว่าต้องการลบความคิดเห็นนี้หรือไม่?"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "As a moderator, you can either validate or reject this answer."
msgstr "ในฐานะผู้ดูแล คุณสามารถตรวจสอบหรือปฏิเสธคำตอบนี้ได้"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "Ask a new question"
msgstr "ถามคำถามใหม่"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "Ask a question"
msgstr "ถามคำถาม"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_ask
msgid "Ask questions"
msgstr "ถามคำถาม"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_post
msgid "Ask questions without validation"
msgstr "ถามคำถามโดยไม่ตรวจสอบความถูกต้อง"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Ask your question"
msgstr "ถามคำถาม"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_26
msgid "Asked a question and accepted an answer"
msgstr "ถามคำถามแล้วรับคำตอบ"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_1
msgid "Asked a question with at least 150 views"
msgstr "ถามคำถามที่มียอดดูอย่างน้อย 150 ครั้ง"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_2
msgid "Asked a question with at least 250 views"
msgstr "ถามคำถามที่มียอดดูอย่างน้อย 250 ครั้ง"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_3
msgid "Asked a question with at least 500 views"
msgstr "ถามคำถามที่มียอดดูอย่างน้อย 500 ครั้ง"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_7
msgid "Asked first question with at least one up vote"
msgstr "ถามคำถามแรกด้วยคะแนนโหวตเห็นด้วยอย่างน้อยหนึ่งรายการ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__create_date
msgid "Asked on"
msgstr "ถามบน"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_question_new
msgid "Asking a question"
msgstr "ถามคำถาม"

#. module: website_forum
#: model:ir.model,name:website_forum.model_ir_attachment
msgid "Attachment"
msgstr "การแนบ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_attachment_count
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_attachment_count
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_attachment_count
msgid "Attachment Count"
msgstr "จำนวนสิ่งที่แนบมา"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_search
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid "Author"
msgstr "ผู้เขียน"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__authorized_group_id
msgid "Authorized Group"
msgstr "กลุ่มที่ได้รับสิทธิ์"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_p_1
msgid "Autobiographer"
msgstr "อัตชีวประวัติ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.author_box
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_header
msgid "Avatar"
msgstr "อวตาร"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Back to Question"
msgstr "กลับไปที่คำถาม"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/controllers/website_forum.py:0
msgid "Bad \"filters\" value \"%(filters)s\"."
msgstr "ค่า \"ตัวกรอง\" ไม่ถูกต้อง \"%(filters)s\""

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/controllers/website_forum.py:0
msgid "Bad \"tag_char\" value \"%(tag_char)s\""
msgstr "ค่า \"tag_char\" ไม่ถูกต้อง \"%(tag_char)s\""

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/controllers/website_forum.py:0
msgid "Bad Request"
msgstr "คำขอที่ไม่ดี"

#. module: website_forum
#: model:ir.ui.menu,name:website_forum.menu_forum_badges
msgid "Badges"
msgstr "เหรียญรางวัล"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post_reason__reason_type__basic
msgid "Basic"
msgstr "พื้นฐาน"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "Because there are no posts in this forum yet."
msgstr "เพราะยังไม่มีกระทู้ในฟอรั่มนี้"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid "Best Answer"
msgstr "คำตอบที่ดีที่สุด"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
msgid "By sharing you answer, you will get additional"
msgstr "แชร์คำตอบของคุณ คุณจะได้รับเพิ่ม"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_accept
msgid "Can Accept"
msgstr "สามารถยอมรับ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_answer
msgid "Can Answer"
msgstr "สามารถตอบ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_ask
msgid "Can Ask"
msgstr "สามารถถาม"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_post
msgid "Can Automatically be Validated"
msgstr "สามารถตรวจสอบได้โดยอัตโนมัติ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_close
msgid "Can Close"
msgstr "สามารถปิด"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_comment
msgid "Can Comment"
msgstr "สามารถแสดงความคิดเห็น"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_comment_convert
msgid "Can Convert to Comment"
msgstr "สามารถแปลงเป็นความคิดเห็น"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_downvote
msgid "Can Downvote"
msgstr "สามารถโหวตไม่เห็นด้วย"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_edit
msgid "Can Edit"
msgstr "สามารถแก้ไข"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_flag
msgid "Can Flag"
msgstr "สามารถตั้งสถานะ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_moderate
msgid "Can Moderate"
msgstr "สามารถกลั่นกรอง"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_unlink
msgid "Can Unlink"
msgstr "สามารถยกเลิกการเชื่อมโยง"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_upvote
msgid "Can Upvote"
msgstr "สามารถโหวตเห็นด้วย"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_use_full_editor
msgid "Can Use Full Editor"
msgstr "สามารถใช้โปรแกรมแก้ไขแบบเต็มได้"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_view
msgid "Can View"
msgstr "สามารถดู"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_edit_retag
msgid "Change question tags"
msgstr "เปลี่ยนแท็กคำถาม"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_p_4
#: model:gamification.challenge,name:website_forum.challenge_chief_commentator
msgid "Chief Commentator"
msgstr "หัวหน้านักวิจารณ์"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
msgid "Click here to accept this answer."
msgstr "คลิดที่นี้เพื่อยอมรับคำตอบ"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
msgid "Click to post your answer."
msgstr "คลิกเพื่อโพสต์คำตอบ"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
msgid "Click to post your question."
msgstr "คลิกเพื่อโพสต์คำถามของคุณ"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
msgid "Click to reply."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_dropdown
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_mobile
msgid "Close"
msgstr "ปิด"

#. module: website_forum
#: model:ir.ui.menu,name:website_forum.menu_forum_post_reasons
msgid "Close Reasons"
msgstr "เหตุผลการปิด"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_close_all
msgid "Close all posts"
msgstr "ปิดโพสต์ทั้งหมด"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_close_own
msgid "Close own posts"
msgstr "ปิดโพสต์ของตัวเอง"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.mark_as_offensive
msgid "Close post"
msgstr "ปิดโพสต์"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post__state__close
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_body
msgid "Closed"
msgstr "ปิดแล้ว"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Closed Posts"
msgstr "กระทู้ปิด"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__closed_uid
msgid "Closed by"
msgstr "ปิดโดย"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__closed_date
msgid "Closed on"
msgstr "ปิดเมือ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.mark_as_offensive
msgid "Closing"
msgstr "ปิด"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__name
msgid "Closing Reason"
msgstr "เหตุผลการปิด"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__color
msgid "Color"
msgstr "สี"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid "Comment"
msgstr "ความคิดเห็น"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_all
msgid "Comment all posts"
msgstr "แสดงความคิดเห็นในทุกโพสต์"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_own
msgid "Comment own posts"
msgstr "แสดงความคิดเห็นบนโพสต์ของตัวเอง"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
msgid "Comment this post"
msgstr "แสดงความคิดเห็นโพสต์นี้"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_p_2
#: model:gamification.challenge,name:website_forum.challenge_commentator
#: model:gamification.goal.definition,name:website_forum.definition_commentator
msgid "Commentator"
msgstr "ผู้แสดงความคิดเห็น"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all_oe_structure_forum_all_top
msgid "Community Forums"
msgstr "ฟอรัมของคอมมูนิตี้"

#. module: website_forum
#: model:gamification.challenge,name:website_forum.challenge_configure_profile
msgid "Complete own biography"
msgstr "กรอกประวัติตัวเอง"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_configure_profile
#: model_terms:gamification.badge,description:website_forum.badge_p_1
msgid "Completed own biography"
msgstr "กรอกประวัติตัวเอง"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_7
msgid "Contains offensive or malicious remarks"
msgstr "มีคำพูดที่ไม่เหมาะสมหรือเป็นอันตราย"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__content
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_search
msgid "Content"
msgstr "เนื้อหา"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_convert_all
msgid "Convert all comments to answers"
msgstr "แปลงความคิดเห็นทั้งหมดเป็นคำตอบ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_accept
msgid "Convert comment to answer"
msgstr "แปลงความคิดเห็นเป็นคำตอบ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_convert_own
msgid "Convert own comments to answers"
msgstr "แปลงความคิดเห็นของตัวเองเป็นคำตอบ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_dropdown
msgid "Convert to Comment"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
msgid "Convert to answer"
msgstr "แปลงเป็นคำตอบ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__is_correct
msgid "Correct"
msgstr "ถูกต้อง"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_post__is_correct
msgid "Correct answer or answer accepted"
msgstr "คำตอบที่ถูกต้องหรือคำตอบได้รับการยอมรับ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__create_date
#: model:ir.model.fields,field_description:website_forum.field_res_users__create_date
msgid "Create Date"
msgstr "วันที่สร้าง"

#. module: website_forum
#: model_terms:ir.actions.act_window,help:website_forum.forum_post_action
msgid "Create a new forum post"
msgstr "สร้างโพสต์ในฟอรั่มใหม่"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
msgid "Create a new post in this forum by clicking on the button."
msgstr "สร้างโพสต์ใหม่ในฟอรั่มนี้โดยการคลิกที่ปุ่ม"

#. module: website_forum
#: model_terms:ir.actions.act_window,help:website_forum.forum_tag_action
msgid "Create a new tag"
msgstr "สร้างแท็กใหม่"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_sign_up_cta
msgid ""
"Create an account today to enjoy exclusive features and engage with our "
"awesome community!"
msgstr ""
"สร้างบัญชีวันนี้เพื่อเพลิดเพลินไปกับฟีเจอร์พิเศษและมีส่วนร่วมกับคอมมูนิตี้ที่ยอดเยี่ยมของเรา!"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_tag_create
msgid "Create new tags"
msgstr "สร้างแท็กใหม่"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/website_forum_tags_wrapper.xml:0
msgid "Create option \""
msgstr "สร้างตัวเลือก \""

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_32
msgid "Created a tag used by 15 questions"
msgstr "สร้างแท็กที่ใช้โดย 15 คำถาม"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__create_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post__create_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__create_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__create_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__create_date
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__create_date
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__create_date
#: model_terms:ir.ui.view,arch_db:website_forum.display_post
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_4
#: model:gamification.challenge,name:website_forum.challenge_favorite_question_1
msgid "Credible Question"
msgstr "คำถามที่น่าเชื่อถือ"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_5
#: model:gamification.challenge,name:website_forum.challenge_critic
#: model:gamification.goal.definition,name:website_forum.definition_critic
msgid "Critic"
msgstr "นักวิจารณ์"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_searchbar_input_snippet_options
msgid "Date"
msgstr "วันที่"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_searchbar_input_snippet_options
msgid "Date (high to low)"
msgstr "วันที่ (สูงไปต่ำ)"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_searchbar_input_snippet_options
msgid "Date (low to high)"
msgstr "วันที่ (ต่ำไปสูง)"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__default_order
msgid "Default"
msgstr "เริ่มต้น"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
msgid "Default Sort"
msgstr "ชนิดค่าเริ่มต้น"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_gamification_challenge__challenge_category
msgid "Define the visibility of the challenge through menus"
msgstr "กำหนดการมองเห็นความท้าทายผ่านเมนู"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
#: model_terms:ir.ui.view,arch_db:website_forum.post_dropdown
msgid "Delete"
msgstr "ลบ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_unlink_all
msgid "Delete all comments"
msgstr "ลบความคิดเห็นทั้งหมด"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_unlink_all
msgid "Delete all posts"
msgstr "ลบโพสต์ทั้งหมดด"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_unlink_own
msgid "Delete own comments"
msgstr "ลบความคิดเห็นของตัวเอง"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_unlink_own
msgid "Delete own posts"
msgstr "ลบโพสต์ของตัวเอง"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "Delete the accepted answer"
msgstr "ลบคำตอบที่ได้รับการยอมรับ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "Deleted"
msgstr "ลบ"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_23
msgid "Deleted own post with 3 or more downvotes"
msgstr "ลบโพสต์ของตัวเองที่มีการโหวตไม่เห็นด้วย 3 ครั้งขึ้นไป"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_6
msgid "Deleted own post with 3 or more upvotes"
msgstr "ลบโพสต์ของตัวเองที่มีการโหวตเห็นด้วย 3 ครั้งขึ้นไป"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__description
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.forum_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Description"
msgstr "คำอธิบาย"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
msgid "Description visible on website"
msgstr "คำอธิบายปรากฏบนเว็บไซต์"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.mark_as_offensive
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
msgid "Discard"
msgstr "ละทิ้ง"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_6
#: model:gamification.challenge,name:website_forum.challenge_disciplined
#: model:gamification.goal.definition,name:website_forum.definition_disciplined
msgid "Disciplined"
msgstr "มีระเบียบ"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__mode__discussions
msgid "Discussions (multiple answers)"
msgstr "การสนทนา (หลายคำตอบ)"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_forum.py:0
#: model_terms:forum.forum,welcome_message:website_forum.forum_help
msgid "Dismiss"
msgstr "ยกเลิก"

#. module: website_forum
#: model_terms:forum.forum,welcome_message:website_forum.forum_help
msgid "Dismiss message"
msgstr "ยกเลิกข้อความ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__display_name
#: model:ir.model.fields,field_description:website_forum.field_forum_post__display_name
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__display_name
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__display_name
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__display_name
msgid "Display Name"
msgstr "แสดงชื่อ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_user_bio
msgid "Display detailed user biography"
msgstr "แสดงประวัติผู้ใช้โดยละเอียด"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_downvote
#: model_terms:ir.ui.view,arch_db:website_forum.vote
msgid "Downvote"
msgstr "โหวตไม่เห็นด้วย"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "Downvote for posting offensive contents"
msgstr "โหวตลงสำหรับการโพสต์เนื้อหาที่ไม่เหมาะสม"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_1
msgid "Duplicate post"
msgstr "โพสต์ซ้ำ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_dropdown
msgid "Edit"
msgstr "แก้ไข"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "Edit Answer"
msgstr "แก้ไขคำตอบ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "Edit Question"
msgstr "แก้ไขคำถาม"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_edit_all
msgid "Edit all posts"
msgstr "แก้ไขโพสต์ทั้งหมด"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_edit_own
msgid "Edit own posts"
msgstr "แก้ไขโพสตัวเอง"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Edit your Post"
msgstr "แก้ไขโพสของคุณ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Edit your answer"
msgstr "แก้ไขคำตอบของคุณ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Edit your question"
msgstr "แก้ไขคำถามของคุณ"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_7
#: model:gamification.challenge,name:website_forum.challenge_editor
#: model:gamification.goal.definition,name:website_forum.definition_editor
msgid "Editor"
msgstr "ผู้แก้ไข"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_editor
msgid "Editor Features: image and links"
msgstr "ฟีเจอร์ตัวแก้ไข: รูปภาพและลิงก์"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "Empty box"
msgstr "กล่องว่าง"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_sign_up_cta
msgid "Enjoying the discussion? Don't just read, join in!"
msgstr "สนุกกับการพูดคุยนี้อยู่ใช่ไหม? เข้าร่วมเลย!"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_5
#: model:gamification.challenge,name:website_forum.challenge_enlightened
#: model:gamification.goal.definition,name:website_forum.definition_enlightened
msgid "Enlightened"
msgstr "ผู้ตื่นรู้"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ""
"Example\n"
"                            <i class=\"fa fa-question-circle\"/>"
msgstr ""
"ตัวอย่าง\n"
"                            <i class=\"fa fa-question-circle\"/>"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid "Facebook"
msgstr "Facebook"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_3
#: model:gamification.challenge,name:website_forum.challenge_famous_question
msgid "Famous Question"
msgstr "คำถามยอดฮิต"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__favourite_count
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "Favorite"
msgstr "รายการโปรด"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_5
#: model:gamification.challenge,name:website_forum.challenge_favorite_question_5
msgid "Favorite Question"
msgstr "คำถามโปรด"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__favourite_ids
msgid "Favourite"
msgstr "รายการโปรด"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_favorite_question_1
msgid "Favourite Question (1)"
msgstr "คำถามโปรด (1)"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_stellar_question_25
msgid "Favourite Question (25)"
msgstr "คำถามโปรด (25)"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_favorite_question_5
msgid "Favourite Question (5)"
msgstr "คำถามโปรด (5)"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Favourite Questions"
msgstr "คำถามโปรด"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_body
msgid "Favourites"
msgstr "รายการโปรด"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_post__last_activity_date
msgid ""
"Field to keep track of a post's last activity. Updated whenever it is "
"replied to, or when a comment is added on the post or one of its replies."
msgstr ""
"ช่องสำหรับติดตามกิจกรรมล่าสุดของโพสต์ อัปเดตทุกครั้งที่มีการตอบกลับ "
"หรือเมื่อมีการเพิ่มความคิดเห็นในโพสต์หรือการตอบกลับอย่างใดอย่างหนึ่ง"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Filter by:"
msgstr "กรองโดย:"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__relevancy_post_vote
msgid "First Relevance Parameter"
msgstr "พารามิเตอร์ที่เกี่ยวข้องแรก"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_5
msgid "First downvote"
msgstr "โหวตไม่เห็นด้วยแรก"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_7
msgid "First edit"
msgstr "แก้ไขครั้งแรก"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_31
msgid "First upvote"
msgstr "โหวตไม่เห็นด้วยครั้งแรก"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_dropdown
msgid "Flag"
msgstr "ตั้งค่าสถานะ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_flag
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_flag
msgid "Flag a post as offensive"
msgstr "ตั้งค่าสถานะโพสต์ว่าเป็นการล่วงละเมิด"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post__state__flagged
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_body
msgid "Flagged"
msgstr "ตั้งค่าสถานะ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__flag_user_id
msgid "Flagged by"
msgstr "ตั้งค่าสถานะโดย"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.follow
msgid "Follow"
msgstr "ติดตาม"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Followed Questions"
msgstr "คำถามที่ติดตาม"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "Followed Tags"
msgstr "แท็กที่ติดตาม"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_follower_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_follower_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_follower_ids
msgid "Followers"
msgstr "ผู้ติดตาม"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_partner_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_partner_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_partner_ids
msgid "Followers (Partners)"
msgstr "ผู้ติดตาม (พาร์ทเนอร์)"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"For example, if you ask an interesting question or give a helpful answer, "
"your input will be upvoted. On the other hand if the answer is misleading - "
"it will be downvoted. Each vote in favor will generate 10 points, each vote "
"against will subtract 2 points. There is a limit of 200 points that can be "
"accumulated for a question or answer per day. The table given at the end "
"explains reputation point requirements for each type of moderation task."
msgstr ""
"ตัวอย่างเช่น หากคุณถามคำถามที่น่าสนใจหรือให้คำตอบที่เป็นประโยชน์ "
"ข้อมูลของคุณจะถูกโหวตเห็นด้วย ในทางกลับกัน หากคำตอบนั้นทำให้เข้าใจผิด "
"คำตอบนั้นจะถูกลบคะแนน การลงคะแนนเสียงเห็นด้วยแต่ละครั้งจะได้ 10 คะแนน "
"การลงคะแนนเสียงที่ไม่เห็นด้วยแต่ละครั้งจะถูกลบ 2 คะแนน "
"มีการจำกัดคะแนนที่สามารถสะสมได้ 200 คะแนนสำหรับคำถามหรือคำตอบต่อวัน "
"ตารางที่ให้ไว้ตอนท้ายจะอธิบายข้อกำหนดคะแนนความน่าเชื่อถือสำหรับงานตรวจสอบแต่ละประเภท"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/website.py:0
#: model:ir.actions.act_url,name:website_forum.action_open_forum
#: model:ir.model,name:website_forum.model_forum_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__forum_id
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__forum_id
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__forum_id
#: model:ir.ui.menu,name:website_forum.menu_website_forum_global
#: model:website.menu,name:website_forum.menu_website_forums
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_search
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_search
#: model_terms:ir.ui.view,arch_db:website_forum.gamification_karma_tracking_view_search
msgid "Forum"
msgstr "ฟอรั่ม"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_website__forum_count
msgid "Forum Count"
msgstr "จำนวนฟอรั่ม"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form_add
msgid "Forum Mode"
msgstr "โหมดฟอรั่ม"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__name
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form_add
msgid "Forum Name"
msgstr "ชื่อฟอรั่ม"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.snippet_options
msgid "Forum Page"
msgstr "หน้าฟอรั่ม"

#. module: website_forum
#: model:ir.model,name:website_forum.model_forum_post
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_form
msgid "Forum Post"
msgstr "โพสต์ฟอรั่ม"

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.forum_post_action
msgid "Forum Post Pages"
msgstr "หน้าโพสต์ฟอรั่ม"

#. module: website_forum
#: model:ir.ui.menu,name:website_forum.menu_forum_post_pages
msgid "Forum Posts"
msgstr "โพสต์ฟอรั่ม"

#. module: website_forum
#: model:ir.model,name:website_forum.model_forum_tag
msgid "Forum Tag"
msgstr "แท็กฟอรั่ม"

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.forum_tag_action
msgid "Forum Tags"
msgstr ""

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.forum_forum_action
#: model:ir.ui.menu,name:website_forum.menu_forum_global
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_tree
#: model_terms:ir.ui.view,arch_db:website_forum.forum_searchbar_input_snippet_options
msgid "Forums"
msgstr "ฟอรั่ม"

#. module: website_forum
#: model:ir.model,name:website_forum.model_gamification_challenge
msgid "Gamification Challenge"
msgstr "ความท้าทายในรูปแบบเกม"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.follow
msgid "Get notified when there's activity on this post"
msgstr "รับการแจ้งเตือนเมื่อมีกิจกรรมในโพสต์นี้"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.follow
msgid "Get notified when this tag is used"
msgstr "รับการแจ้งเตือนเมื่อมีการใช้แท็กนี้"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
msgid "Give your post title."
msgstr "มอบชื่อให้โพสต์ของคุณ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "Go back to the list of"
msgstr "กลับไปที่รายการของ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "Go enjoy a cup of coffee."
msgstr "ไปจิบกาแฟกันเพลินๆ"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_3
#: model:gamification.challenge,name:website_forum.challenge_good_answer
msgid "Good Answer"
msgstr "คำตอบที่ดี"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_good_answer
msgid "Good Answer (6)"
msgstr "คำตอบที่ดี (6)"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_9
#: model:gamification.challenge,name:website_forum.challenge_good_question
msgid "Good Question"
msgstr "คำถามที่ดี"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_graph
msgid "Graph of Posts"
msgstr "กราฟของโพสต์"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_4
#: model:gamification.challenge,name:website_forum.challenge_great_answer
msgid "Great Answer"
msgstr "คำตอบที่่ยอดเยี่ยม"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_great_answer
msgid "Great Answer (15)"
msgstr "คำตอบที่่ยอดเยี่ยม (15)"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_10
#: model:gamification.challenge,name:website_forum.challenge_great_question
msgid "Great Question"
msgstr "คำถามที่ยอดเยี่ยม"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.snippet_options
msgid "Grid"
msgstr "ตาราง"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_search
msgid "Group By"
msgstr "กลุ่มโดย"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__faq
#: model_terms:ir.ui.view,arch_db:website_forum.faq
msgid "Guidelines"
msgstr "แนวทางการปฏิบัติ"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_6
#: model:gamification.challenge,name:website_forum.challenge_guru
msgid "Guru"
msgstr "กูรู"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_guru
msgid "Guru (15)"
msgstr "กูรู (15)"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__uid_has_answered
msgid "Has Answered"
msgstr "ตอบคำถามแล้ว"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__has_message
#: model:ir.model.fields,field_description:website_forum.field_forum_post__has_message
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__has_message
msgid "Has Message"
msgstr "มีข้อความ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__has_pending_post
msgid "Has pending post"
msgstr "มีกระทู้รอดำเนินการ"

#. module: website_forum
#: model:forum.forum,name:website_forum.forum_help
msgid "Help"
msgstr "ช่วยเหลือ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_votes
msgid "Help moderating the forums by upvoting and downvoting posts. <br/>"
msgstr "ช่วยกลั่นกรองฟอรั่มโดยการอัปโหวตและดาวน์โหวตโพสต์ <br/>"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "Here a table with the privileges and the karma level"
msgstr "ที่นี่คือตารางสิทธิพิเศษและระดับ Karma"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__id
#: model:ir.model.fields,field_description:website_forum.field_forum_post__id
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__id
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__id
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__id
msgid "ID"
msgstr "ไอดี"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_needaction
#: model:ir.model.fields,help:website_forum.field_forum_post__message_needaction
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_needaction
msgid "If checked, new messages require your attention."
msgstr "ถ้าเลือก ข้อความใหม่จะต้องการความสนใจจากคุณ"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_has_error
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_has_sms_error
#: model:ir.model.fields,help:website_forum.field_forum_post__message_has_error
#: model:ir.model.fields,help:website_forum.field_forum_post__message_has_sms_error
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_has_error
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "ถ้าเลือก ข้อความบางข้อความมีข้อผิดพลาดในการส่ง"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__karma_dofollow
msgid ""
"If the author has not enough karma, a nofollow attribute is added to links"
msgstr ""
"หากผู้เขียนมีคะแนน Karma ไม่เพียงพอ คุณลักษณะไม่ติดตามจะถูกเพิ่มลงในลิงก์"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "If this approach is not for you, please respect the community."
msgstr "หากแนวทางนี้ไม่เหมาะกับคุณ โปรดเคารพในชุมชน"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.mark_as_offensive
msgid ""
"If you close this post, it will be hidden for most users. Only\n"
"            users having a high karma can see closed posts to moderate\n"
"            them."
msgstr ""
"หากคุณปิดโพสต์นี้ โพสต์นั้นจะถูกซ่อนสำหรับผู้ใช้ส่วนใหญ่ เฉพาะ\n"
"            ผู้ใช้ที่มีคะแนน Karma สูงสามารถเห็นโพสต์ที่ปิดเพื่อกลั่นกรอง\n"
"            ได้"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"If you fit in one of these example or if your motivation for asking the "
"question is “I would like to participate in a discussion about ______”, then"
" you should not be asking here but on our mailing lists. However, if your "
"motivation is “I would like others to explain ______ to me”, then you are "
"probably OK."
msgstr ""
"หากคุณเหมาะสมกับตัวอย่างใดตัวอย่างหนึ่งเหล่านี้ "
"หรือถ้าแรงจูงใจของคุณในการถามคำถามคือ "
"“ฉันต้องการมีส่วนร่วมในการอภิปรายเกี่ยวกับ ______” "
"คุณไม่ควรถามที่นี่แต่ควรถามในรายชื่อผู้รับจดหมายของเรา อย่างไรก็ตาม "
"หากแรงจูงใจของคุณคือ “ฉันอยากให้คนอื่นอธิบาย ______ ให้ฉันฟัง” "
"คุณก็อาจจะโอเค"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.mark_as_offensive
msgid ""
"If you mark this post as offensive, it will be hidden for most users. Only\n"
"            users having a high karma can see offensive posts to moderate\n"
"            them."
msgstr ""
"หากคุณทำเครื่องหมายโพสต์นี้ว่าไม่เหมาะสม โพสต์นั้นจะถูกซ่อนสำหรับผู้ใช้ส่วนใหญ่ เฉพาะ\n"
"           ผู้ใช้ที่มีคะแนน Karma สูงจึงสามารถเห็นโพสต์ที่ไม่เหมาะสมเพื่อกลั่นกรอง\n"
"            ได้"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__image_1920
msgid "Image"
msgstr "รูปภาพ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__image_1024
msgid "Image 1024"
msgstr "รูปภาพ 1024"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__image_128
msgid "Image 128"
msgstr "รูปภาพ 128"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__image_256
msgid "Image 256"
msgstr "รูปภาพ 256"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__image_512
msgid "Image 512"
msgstr "รูปภาพ 512"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_13
msgid "Inappropriate and unacceptable statements"
msgstr "ข้อความที่ไม่เหมาะสมและไม่สามารถยอมรับได้"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
msgid "Insert tags related to your question."
msgstr "แทรกแท็กที่เกี่ยวข้องกับคำถามของคุณ"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_11
msgid "Insulting and offensive language"
msgstr "ข้อความดูหมิ่นและหยาบคาย"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__user_favourite
msgid "Is Favourite"
msgstr "เป็นรายการโปรด"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_is_follower
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_is_follower
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_is_follower
msgid "Is Follower"
msgstr "เป็นผู้ติดตาม"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__can_moderate
msgid "Is a moderator"
msgstr "เป็นผู้ดำเนินรายการ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__has_validated_answer
msgid "Is answered"
msgstr "ตอบแล้ว"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_display_biography
msgid "Is the author's biography visible from his post"
msgstr "ชีวประวัติของผู้เขียนมองเห็นได้จากโพสต์ของเขาหรือไม่"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post_vote.py:0
msgid "It is not allowed to modify someone else's vote."
msgstr "ไม่อนุญาตให้แก้ไขการลงคะแนนของผู้อื่น"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post_vote.py:0
msgid "It is not allowed to vote for its own post."
msgstr "ไม่อนุญาตให้ลงคะแนนสำหรับโพสต์ของตัวเอง"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
msgid "Karma Error"
msgstr "ข้อผิดพลาด Karma"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
msgid "Karma Gains"
msgstr "Karma เพิ่ม"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
msgid "Karma Related Rights"
msgstr "สิทธิเกี่ยวข้องกับ Karma"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_close
msgid "Karma to close"
msgstr "Karma เพื่อปิด"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_comment
msgid "Karma to comment"
msgstr "Karma เป็นความคิดเห็น"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_comment_convert
msgid "Karma to convert comment to answer"
msgstr "ใช้ Karma เพื่อแปลงความคิดเห็นเป็นคำตอบ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_edit
msgid "Karma to edit"
msgstr "Karma เพื่อแก้ไข"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_unlink
msgid "Karma to unlink"
msgstr "Karma เพื่อยกเลิกการเชื่อมโยง"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post
msgid "Last Activity"
msgstr "กิจกรรมล่าสุด"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__last_post_id
#: model_terms:ir.ui.view,arch_db:website_forum.snippet_options
msgid "Last Post"
msgstr "โพสต์สุดท้าย"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__default_order__last_activity_date_desc
msgid "Last Updated"
msgstr "อัปเดตครั้งสุดท้าย"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__write_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__write_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__write_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__write_uid
msgid "Last Updated by"
msgstr "อัปเดตครั้งล่าสุดโดย"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__write_date
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__write_date
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__write_date
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__write_date
msgid "Last Updated on"
msgstr "อัปเดตครั้งล่าสุดเมื่อ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__last_activity_date
msgid "Last activity on"
msgstr "กิจกรรมล่าสุดเมื่อ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.snippet_options
msgid "Layout"
msgstr "เลย์เอาต์"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_25
msgid "Left 10 answers with score of 10 or more"
msgstr "เหลือ 10 คำตอบกับ 10 หรือมากกว่า"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_url
msgid "Link to questions with the tag"
msgstr "ลิงก์ไปยังคำถามที่มีแท็ก"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid "LinkedIn"
msgstr "LinkedIn"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.snippet_options
msgid "List"
msgstr "รายการ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid "Mark as Best Answer"
msgstr "ทำเครื่องหมายว่าเป็นคำตอบที่ดีที่สุด"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.show_flag_validator
msgid "Mark as Offensive"
msgstr "ทำเครื่องหมายว่าไม่เหมาะสม"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.mark_as_offensive
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Mark as offensive"
msgstr "ทำเครื่องหมายว่าเป็นการละเมิด"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Mark as spam"
msgstr "ทำเครื่องหมายว่าเป็นแสปม"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all_oe_structure_forum_all_top
msgid "Meet our community members"
msgstr "พบกับสมาชิกคอมมูนิตี้ของเรา"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_has_error
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_has_error
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_has_error
msgid "Message Delivery error"
msgstr "เกิดข้อผิดพลาดในการส่งข้อความ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_ids
msgid "Messages"
msgstr "ข้อความ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__mode
msgid "Mode"
msgstr "โหมด"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_moderate
msgid "Moderate posts"
msgstr "กลั่นกรองโพสต์"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_body
msgid "Moderation tools"
msgstr "เครื่องมือกลั่นกรอง"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "More over:"
msgstr "นอกจากนี้:"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "Most Used Tags"
msgstr "แท็กที่ใช้มากที่สุด"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__default_order__vote_count_desc
msgid "Most Voted"
msgstr "ได้รับการโหวตมากที่สุด"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "Most used Tags"
msgstr "แท็กที่ใช้มากที่สุด"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__tag_most_used_ids
msgid "Most used tags"
msgstr "แท็กที่ใช้มากที่สุด"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "My Favorites"
msgstr "รายการโปรดของฉัน"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_body
msgid "My Posts"
msgstr "โพสต์ของฉัน"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__user_vote
msgid "My Vote"
msgstr "โหวตของฉัน"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_body
msgid "My forums"
msgstr "ฟอรั่มของฉัน"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_header
msgid "My profile"
msgstr "โปรไฟล์ของฉัน"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__name
msgid "Name"
msgstr "ชื่อ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_votes
msgid "Negative vote"
msgstr "โหวตด้านลบ"

#. module: website_forum
#: model:mail.message.subtype,description:website_forum.mt_answer_new
#: model:mail.message.subtype,name:website_forum.mt_answer_new
#: model:mail.message.subtype,name:website_forum.mt_forum_answer_new
msgid "New Answer"
msgstr "คำตอบใหม่"

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.forum_forum_action_add
msgid "New Forum"
msgstr "ฟอรั่มใหม่"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "New Post"
msgstr "โพสต์ใหม่"

#. module: website_forum
#: model:mail.message.subtype,description:website_forum.mt_question_new
#: model:mail.message.subtype,name:website_forum.mt_forum_question_new
#: model:mail.message.subtype,name:website_forum.mt_question_new
msgid "New Question"
msgstr "คำถามใหม่"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__default_order__create_date_desc
msgid "Newest"
msgstr "ใหม่ล่าสุด"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_2
#: model:gamification.challenge,name:website_forum.challenge_nice_answer
msgid "Nice Answer"
msgstr "คำตอบที่น่าสนใจ"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_nice_answer
msgid "Nice Answer (4)"
msgstr "คำตอบที่น่าสนใจ (4)"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_8
#: model:gamification.challenge,name:website_forum.challenge_nice_question
msgid "Nice Question"
msgstr "คำถามน่าสนใจ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all
msgid "No forum is available yet."
msgstr "ยังไม่มีฟอรั่ม"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all_all_entries
msgid "No posts yet"
msgstr "ยังไม่มีการโพสต์"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_dofollow
msgid "Nofollow links"
msgstr "ลิงก์ไม่ติดตาม"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_4
msgid "Not a real post"
msgstr "ไม่ใช่โพสต์จริง"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_6
msgid "Not relevant or out dated"
msgstr "ไม่มีความเกี่ยวข้องหรือล้าสมัย"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_2
#: model:gamification.challenge,name:website_forum.challenge_notable_question
msgid "Notable Question"
msgstr "คำถามที่โดดเด่น"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_needaction_counter
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_needaction_counter
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_needaction_counter
msgid "Number of Actions"
msgstr "จํานวนการดําเนินการ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__posts_count
msgid "Number of Posts"
msgstr "จำนวนของโพสต์"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_has_error_counter
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_has_error_counter
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_has_error_counter
msgid "Number of errors"
msgstr "จํานวนข้อผิดพลาด"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__count_flagged_posts
msgid "Number of flagged posts"
msgstr "จำนวนโพสต์ที่ตั้งสถานะ"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_needaction_counter
#: model:ir.model.fields,help:website_forum.field_forum_post__message_needaction_counter
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "จำนวนข้อความที่ต้องดำเนินการ"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_has_error_counter
#: model:ir.model.fields,help:website_forum.field_forum_post__message_has_error_counter
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "จํานวนข้อความที่มีข้อผิดพลาดในการส่ง"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__count_posts_waiting_validation
msgid "Number of posts waiting for validation"
msgstr "จำนวนโพสต์ที่รอการตรวจสอบ"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_2
msgid "Off-topic or not relevant"
msgstr "นอกประเด็นหรือไม่เกี่ยวข้อง"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post__state__offensive
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post_reason__reason_type__offensive
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
#: model_terms:ir.ui.view,arch_db:website_forum.mark_as_offensive
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Offensive"
msgstr "ละเมิด"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
msgid "Offensive Post"
msgstr "โพสต์ที่ละเมิด"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Offensive Posts"
msgstr "กระทู้ที่ไม่เหมาะสม"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
msgid "Oh no! Please <a href=\"%s\">sign in</a> to perform this action"
msgstr "โอ้ ไม่! กรุณา<a href=\"%s\">ลงชื่อเข้าใช้</a>เพื่อดำเนินการนี้"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
msgid "On average,"
msgstr "โดยเฉลี่ย"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "Oops!"
msgstr "อุ๊ปส์!"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
msgid "Options"
msgstr "ตัวเลือก"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
msgid "Order and Visibility"
msgstr "ลำดับและการมองเห็น"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_23
#: model:gamification.challenge,name:website_forum.challenge_peer_pressure
#: model:gamification.goal.definition,name:website_forum.definition_peer_pressure
msgid "Peer Pressure"
msgstr "แรงกดดันของขุนนาง"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__plain_content
msgid "Plain Content"
msgstr "เนื้อหาธรรมดา"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ""
"Please wait for a moderator to validate your previous post before "
"continuing."
msgstr "โปรดรอให้ผู้ดูแลตรวจสอบโพสต์ก่อนหน้าของคุณก่อนดำเนินการต่อ"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_1
#: model:gamification.challenge,name:website_forum.challenge_popular_question
msgid "Popular Question"
msgstr "คำถามยอดนิยม"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_popular_question
msgid "Popular Question (150)"
msgstr "คำถามยอดนิยม (150)"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_notable_question
msgid "Popular Question (250)"
msgstr "คำถามยอดนิยม (250)"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_famous_question
msgid "Popular Question (500)"
msgstr "คำถามยอดนิยม (500)"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_votes
msgid "Positive vote"
msgstr "โหวตด้านบวก"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__post_id
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_search
msgid "Post"
msgstr "โพสต์"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
msgid "Post Answer"
msgstr "โพสต์คำตอบ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__child_ids
msgid "Post Answers"
msgstr "โพสต์คำตอบ"

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.forum_post_reason_action
msgid "Post Close Reason"
msgstr "เหตุผลที่ปิดโพสต์"

#. module: website_forum
#: model:ir.model,name:website_forum.model_forum_post_reason
msgid "Post Closing Reason"
msgstr "โพสต์เหตุผลปิด"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.snippet_options
msgid "Post Count"
msgstr "จำนวนโพสต์"

#. module: website_forum
#: model:ir.model,name:website_forum.model_forum_post_vote
msgid "Post Vote"
msgstr "โพสต์โหวต"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Post Your Question"
msgstr "โพสต์คำถามของคุณ"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "Post is closed and marked as offensive content"
msgstr "โพสต์ถูกปิดและทำเครื่องหมายว่าเป็นเนื้อหาที่ไม่เหมาะสม"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "Post is closed and marked as spam"
msgstr "โพสต์ถูกปิดและทำเครื่องหมายว่าเป็นสแปม"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.mark_as_offensive
msgid "Post:"
msgstr "โพสต์:"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_p_2
msgid "Posted 10 comments"
msgstr "โพสต์ 10 ความคิดเห็น"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_p_4
msgid "Posted 100 comments"
msgstr "โพสต์ 100 ความคิดเห็น"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "Posting answer on a [Deleted] or [Closed] question is not possible."
msgstr "ไม่สามารถโพสต์คำตอบ [ลบ] หรือ [ปิด] ในคำถามได้"

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.forum_post_action_forum_main
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__post_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__post_ids
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_search
msgid "Posts"
msgstr "โพสต์"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__privacy
msgid "Privacy"
msgstr "ความเป็นส่วนตัว"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__privacy__public
msgid "Public"
msgstr "สาธารณะ"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__privacy
msgid ""
"Public: Forum is public\n"
"Signed In: Forum is visible for signed in users\n"
"Some users: Forum and their content are hidden for non members of selected group"
msgstr ""
"สาธารณะ: ฟอรั่มเป็นแบบสาธารณะ\n"
"เข้าสู่ระบบ: ฟอรั่มจะปรากฏสำหรับผู้ใช้ที่ลงชื่อเข้าใช้\n"
"ผู้ใช้บางคน: ฟอรัมและเนื้อหาถูกซ่อนสำหรับผู้ที่ไม่ใช่สมาชิกของกลุ่มที่เลือก"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_25
#: model:gamification.challenge,name:website_forum.challenge_pundit
#: model:gamification.goal.definition,name:website_forum.definition_pundit
msgid "Pundit"
msgstr "บัณฑิต"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
msgid "Put your answer here."
msgstr "ใส่คำตอบของคุณตรงนี้"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
msgid "Put your question here."
msgstr "ใส่คำถามของคุณที่นี้"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__parent_id
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Question"
msgstr "คำถาม"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post_vote.py:0
msgid "Question %s"
msgstr "คำถาม %s"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
#: model:mail.message.subtype,description:website_forum.mt_question_edit
#: model:mail.message.subtype,name:website_forum.mt_question_edit
msgid "Question Edited"
msgstr "แก้ไขคำถามแล้ว"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_question_downvote
msgid "Question downvoted"
msgstr "คำถามที่ถูกโหวตไม่เห็นด้วย"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.404
msgid "Question not found!"
msgstr "ไม่พบคำถาม!"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_4
msgid "Question set as favorite by 1 user"
msgstr "คำถามที่ถูกตั้งไว้เป็นที่ชื่นชอบโดยผู้ใช้ 1 ราย"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_6
msgid "Question set as favorite by 25 users"
msgstr "คำถามที่ถูกตั้งไว้เป็นที่ชื่นชอบโดยผู้ใช้ 25 ราย"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_5
msgid "Question set as favorite by 5 users"
msgstr "คำถามที่ถูกตั้งไว้เป็นรายการโปรดโดยผู้ใช้ 5 ราย"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/controllers/website_forum.py:0
msgid "Question should not be empty."
msgstr "คำถามต้องไม่เว้นว่าง"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_question_upvote
msgid "Question upvoted"
msgstr "คำถามถูกโหวตเห็นด้วย"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_10
msgid "Question voted up 15 times"
msgstr "คำถามถูกโหวตเห็นด้วย 15 ครั้ง"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_8
msgid "Question voted up 4 times"
msgstr "คำถามถูกโหวตเห็นด้วย 4 ครั้ง"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_9
msgid "Question voted up 6 times"
msgstr "คำถามถูกโหวตเห็นด้วย 6 ครั้ง"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Question:"
msgstr "คำถาม:"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Questions"
msgstr "คำถาม"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__mode__questions
msgid "Questions (1 answer)"
msgstr "คำถาม (1 คำตอบ)"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__mode
msgid ""
"Questions mode: only one answer allowed\n"
" Discussions mode: multiple answers allowed"
msgstr ""
"โหมดคำถาม: อนุญาติเพียงคำตอบเดียว\n"
"โหมดการสนทนา: อนุญาตให้ตอบได้หลายคำตอบ"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_15
msgid "Racist and hate speech"
msgstr "เหยียดเชื้อชาติและแสดงความเกลียดชัง"

#. module: website_forum
#: model:ir.ui.menu,name:website_forum.menu_forum_rank_global
msgid "Ranks"
msgstr "อันดับ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__rating_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_post__rating_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__rating_ids
msgid "Ratings"
msgstr "การให้คะแนน"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "Re: %s"
msgstr "ตอบกลับ: %s"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Read: #{question.name}"
msgstr "อ่าน: #{question.name}"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__closed_reason_id
msgid "Reason"
msgstr "เหตุผล"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__reason_type
msgid "Reason Type"
msgstr "ประเภทเหตุผล"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.mark_as_offensive
msgid "Reason:"
msgstr "เหตุผล:"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_reason_view_list
msgid "Reasons"
msgstr "เหตุผล"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_1
msgid "Received at least 3 upvote for an answer for the first time"
msgstr "ได้รับการโหวตเห็นด้วยอย่างน้อย 3 ครั้งสำหรับการตอบเป็นครั้งแรก"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Refuse"
msgstr "ปฏิเสธ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_related_posts
msgid "Related Posts"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__relevancy
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__default_order__relevancy_desc
msgid "Relevance"
msgstr "เกี่ยวข้อง"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
msgid "Relevance Computation"
msgstr "การคำนวณที่เกี่ยวข้อง"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "Remove validated answer"
msgstr "ลบคำตอบที่ถูกต้อง"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
#: model_terms:ir.ui.view,arch_db:website_forum.post_dropdown
msgid "Reopen"
msgstr "เปิดอีกครั้ง"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "Reopen a banned question"
msgstr "เปิดคำถามที่ถูกแบนอีกครั้ง"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
#: model_terms:ir.ui.view,arch_db:website_forum.forum_related_posts
#: model_terms:ir.ui.view,arch_db:website_forum.post_stats
msgid "Replies"
msgstr "ตอบกลับ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_stats
msgid "Reply"
msgstr "ตอบกลับ"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/controllers/website_forum.py:0
msgid "Reply should not be empty."
msgstr "การตอบกลับต้องไม่เว้นว่าง"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__self_reply
msgid "Reply to own question"
msgstr "ตอบคำถามของตัวเอง"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__website_id
#: model:ir.model.fields,help:website_forum.field_forum_post__website_id
msgid "Restrict to a specific website."
msgstr "จำกัดเฉพาะเว็บไซต์ใดเว็บไซต์หนึ่ง"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.profile_access_denied
msgid "Return to the forum"
msgstr "กลับไปที่ฟอรั่ม"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__moderator_id
msgid "Reviewed by"
msgstr "รีวิวโดย"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__is_seo_optimized
#: model:ir.model.fields,field_description:website_forum.field_forum_post__is_seo_optimized
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__is_seo_optimized
msgid "SEO optimized"
msgstr "ปรับ SEO ให้เหมาะสม"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_has_sms_error
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_has_sms_error
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_has_sms_error
msgid "SMS Delivery error"
msgstr "ข้อผิดพลาดในการส่ง SMS"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Save Changes"
msgstr "บันทึกการเปลี่ยนแปลง"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_26
#: model:gamification.challenge,name:website_forum.challenge_scholar
#: model:gamification.goal.definition,name:website_forum.definition_scholar
msgid "Scholar"
msgstr "นักวิชาการ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_search
msgid "Search in Post"
msgstr "ค้นหาในโพสต์"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Search..."
msgstr "ค้นหา..."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__relevancy_time_decay
msgid "Second Relevance Parameter"
msgstr "พารามิเตอร์ที่เกี่ยวข้องอันดับสอง"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/res_users.py:0
msgid "See our Forum"
msgstr "ดูฟอรั่มของเรา"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_answer
msgid "See post"
msgstr "ดูโพสต์"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_question
msgid "See question"
msgstr "ดูคำถาม"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Select All"
msgstr "เลือกทั้งหมด"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_8
#: model:gamification.challenge,name:website_forum.challenge_self_learner
#: model:gamification.goal.definition,name:website_forum.definition_self_learner
msgid "Self-Learner"
msgstr "ผู้เรียนรู้ด้วยตัวเอง"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__seo_name
#: model:ir.model.fields,field_description:website_forum.field_forum_post__seo_name
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__seo_name
msgid "Seo name"
msgstr "ชื่อ Seo"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__sequence
msgid "Sequence"
msgstr "ลำดับ"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_forum.py:0
#: model_terms:forum.forum,welcome_message:website_forum.forum_help
msgid ""
"Share and discuss the best content and new marketing ideas, build your "
"professional profile and become a better marketer together."
msgstr ""
"แบ่งปันและพูดคุยเกี่ยวกับเนื้อหาที่ดีที่สุดและไอเดียทางการตลาดใหม่ๆ "
"สร้างโปรไฟล์มืออาชีพของคุณและเป็นนักการตลาดที่ดีขึ้นไปพร้อมกัน"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
msgid ""
"Share this content to increase your chances to be featured on the front page"
" and attract more visitors."
msgstr ""
"แชร์เนื้อหานี้เพื่อเพิ่มโอกาสในการแสดงจุดเด่นต่าง ๆ "
"บนหน้าแรกและดึงดูดผู้เยี่ยมชมมากขึ้น"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__allow_share
msgid "Sharing Options"
msgstr "แชร์ตัวเลือก"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index_tags
msgid "Show Tags Starting with"
msgstr "แสดงแท็กที่เริ่มต้นด้วย"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "Showing results for"
msgstr "แสดงผลลัพธ์สำหรับ"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_forum.py:0
#: model_terms:forum.forum,welcome_message:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.forum_sign_up_cta
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_header
msgid "Sign up"
msgstr "ลงชื่อ"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__privacy__connected
msgid "Signed In"
msgstr "ลงชื่อเข้าใช้แล้ว"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "Solved"
msgstr "แก้แล้ว"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__privacy__private
msgid "Some users"
msgstr "ผู้ใช้บางราย"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.404
msgid "Sorry, this question is not available anymore."
msgstr "ขออภัย คำถามนี้ไม่มีอยู่แล้ว"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "Sorry, we could not find any <b>solved</b> results"
msgstr "ขออภัย เราไม่พบผลลัพธ์ที่<b>แก้ไขได้</b>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "Sorry, we could not find any <b>unanswered</b> results"
msgstr "ขออภัย เราไม่พบผลลัพธ์ที่ยัง<b>ไม่มีคำตอบ</b>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "Sorry, we could not find any <b>unsolved</b> results"
msgstr "ขออภัย เราไม่พบผลลัพธ์ที่ยัง<b>ไม่ได้รับการแก้ไข</b>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "Sorry, we could not find any results"
msgstr "ขออภัย เราไม่พบผลลัพธ์ใดๆ"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
msgid "Sorry, you cannot select your own posts as best answer"
msgstr "ขออภัย คุณไม่สามารถเลือกโพสต์ของคุณเองเป็นคำตอบที่ดีที่สุดได้"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
msgid "Sorry, you cannot vote for your own posts"
msgstr "ขออภัย คุณไม่สามารถลงคะแนนสำหรับโพสต์ของคุณเองได้"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Spam all post"
msgstr "แสปมโพสต์ทั้งหมด"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_8
msgid "Spam or advertising"
msgstr "สแปมหรือโฆษณา"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "Start by creating a post"
msgstr "เริ่มต้นด้วยการสร้างโพสต์"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__state
msgid "Status"
msgstr "สถานะ"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_6
#: model:gamification.challenge,name:website_forum.challenge_stellar_question_25
msgid "Stellar Question"
msgstr "คำถามแจ่ม ๆ "

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_7
#: model:gamification.challenge,name:website_forum.challenge_student
msgid "Student"
msgstr "นักเรียน"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.follow
msgid "Subscribe"
msgstr "ติดตาม"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_31
#: model:gamification.challenge,name:website_forum.challenge_supporter
#: model:gamification.goal.definition,name:website_forum.definition_supporter
msgid "Supporter"
msgstr "ผู้สนับสนุน"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_search
#: model_terms:ir.ui.view,arch_db:website_forum.forum_tag_view_form
msgid "Tag"
msgstr "แท็ก"

#. module: website_forum
#: model:ir.model.constraint,message:website_forum.constraint_forum_tag_name_uniq
msgid "Tag name already exists!"
msgstr "มีชื่อแท็กแล้ว!"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__tag_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_post__tag_ids
#: model:ir.ui.menu,name:website_forum.menu_forum_tag_global
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index_tags
#: model_terms:ir.ui.view,arch_db:website_forum.forum_tag_view_list
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_body
msgid "Tags"
msgstr "แท็ก"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all_oe_structure_forum_all_top
msgid ""
"Tap into the collective knowledge of our community by asking your questions "
"in our forums,<br/> where helpful members are ready to assist you."
msgstr ""
"ใช้ประโยชน์จากคลังข้อมูลโดยรวมของคอมมูนิตี้ของเราโดยถามคำถามของคุณในฟอรั่มของเรา"
" ซึ่งสมาชิกพร้อมที่จะช่วยเหลือคุณ"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_32
#: model:gamification.challenge,name:website_forum.challenge_taxonomist
#: model:gamification.goal.definition,name:website_forum.definition_taxonomist
msgid "Taxonomist"
msgstr "นักอนุกรมวิธาน"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_1
#: model:gamification.challenge,name:website_forum.challenge_teacher
#: model:gamification.goal.definition,name:website_forum.definition_teacher
msgid "Teacher"
msgstr "ครู"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__teaser
msgid "Teaser"
msgstr "ทีเซอร์"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
msgid "Thanks for posting!"
msgstr "ขอบคุณสำหรับโพสต์!"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "The accepted answer is deleted"
msgstr "คำตอบที่ยอมรับจะถูกลบออก"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid ""
"The goal of this site is create a relevant knowledge base that would answer "
"questions related to Odoo."
msgstr ""
"เป้าหมายของไซต์นี้คือการสร้างฐานความรู้ที่เกี่ยวข้อง "
"ซึ่งจะตอบคำถามที่เกี่ยวกับ Odoo"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "The question has been closed"
msgstr "คำถามนี้ถูกปิดแล้ว"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "There are no answers yet"
msgstr "ยังไม่มีคำตอบ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index_tags
msgid "There are no tags being used in this forum."
msgstr "ไม่มีแท็กที่ใช้ในฟอรั่มนี้"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index_tags
msgid "There are no tags matching the selected filter"
msgstr "ไม่มีแท็กที่ตรงกับตัวกรองที่เลือก"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index_tags
msgid "There are no tags matching the selected search."
msgstr "ไม่มีแท็กที่ตรงกับการค้นหาที่เลือก"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid ""
"Therefore questions and answers can be edited like wiki pages by experienced"
" users of this site in order to improve the overall quality of the knowledge"
" base content. Such privileges are granted based on user karma level: you "
"will be able to do the same once your karma gets high enough."
msgstr ""
"ดังนั้น คำถามและคำตอบสามารถแก้ไขได้เหมือนหน้า Wiki "
"โดยผู้ใช้ที่มีประสบการณ์ของไซต์นี้ "
"เพื่อปรับปรุงคุณภาพโดยรวมของเนื้อหาและความรู้ต่าง ๆ  สิทธิพิเศษต่าง ๆ "
"จะได้รับตามระดับ Karma ของผู้ใช้ คุณจะสามารถทำได้เช่นเดียวกันเมื่อคะแนน "
"Karma ของคุณสูงพอ"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"This community is for professional and enthusiast users, partners and "
"programmers. You can ask questions about:"
msgstr ""
"ชุมชนนี้มีไว้สำหรับผู้ใช้ พาร์ทเนอร์ "
"และนักพัฒนาโปรแกรมที่เป็นมืออาชีพและกระตือรือร้น คุณสามารถถามคำถามเกี่ยวกับ:"

#. module: website_forum
#: model:forum.forum,description:website_forum.forum_help
msgid ""
"This community is for professionals and enthusiasts of our products and "
"services. Share and discuss the best content and new marketing ideas, build "
"your professional profile and become a better marketer together."
msgstr ""
"ชุมชนนี้มีไว้สำหรับมืออาชีพและผู้ที่ชื่นชอบสินค้าและบริการของเรา "
"แชร์และพูดคุยเกี่ยวกับเนื้อหาที่ดีที่สุดและแนวคิดทางการตลาดใหม่ ๆ "
"สร้างโปรไฟล์มืออาชีพของคุณและกลายเป็นนักการตลาดที่ดียิ่งขึ้นไปด้วยกัน"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__relevancy_post_vote
msgid ""
"This formula is used in order to sort by relevance. The variable 'votes' "
"represents number of votes for a post, and 'days' is number of days since "
"the post creation"
msgstr ""
"สูตรนี้ใช้เพื่อจัดเรียงตามความเกี่ยวข้อง ตัวแปรการ 'โหวต' "
"หมายถึงจำนวนโหวตสำหรับโพสต์ และ 'วัน' คือจำนวนวันนับตั้งแต่สร้างโพสต์"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "This forum has been archived."
msgstr "ฟอรั่มนี้ถูกเก็บถาวรแล้ว"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
msgid "This post can not be flagged"
msgstr "โพสต์นี้ไม่สามารถตั้งค่าสถานะได้"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
msgid "This post is already flagged"
msgstr "โพสต์นี้ถูกตั้งค่าสถานะแล้ว"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "This post is awaiting validation"
msgstr "โพสต์นี้กำลังรอการตรวจสอบ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid ""
"This post is currently awaiting moderation and is not published yet.<br/>\n"
"                    As a moderator you can either <b>Accept</b> or <b>Reject</b> this post."
msgstr ""
"โพสต์นี้กำลังรอการตรวจสอบและยังไม่ได้เผยแพร่<br/>\n"
"                    ในฐานะผู้ดูแล คุณสามารถ<b>ยอมรับ</b>หรือ<b>ปฏิเสธ</b>โพสต์นี้ได้"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "This question has been flagged"
msgstr "คำถามนี้ถูกตั้งค่าสถานะ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "This user hasn't answered any questions yet. <br/>"
msgstr "ผู้ใช้รายนี้ยังไม่ได้ตอบคำถาม <br/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "This user hasn't posted any questions yet.<br/>"
msgstr "ผู้ใช้รายนี้ยังไม่ได้โพสต์คำถามใดๆ<br/>"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_14
msgid "Threatening language"
msgstr "ภาษาที่ใช้ข่มขู่"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__name
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Title"
msgstr "คำนำหน้าชื่อ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Title must not be empty"
msgstr "ชื่อเรื่องต้องไม่เว้นว่าง"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/controllers/website_forum.py:0
msgid "Title should not be empty."
msgstr "ชื่อเรื่องไม่ควรถูกเว้นว่าง"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__recipient_id
msgid "To"
msgstr "ถึง"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_body
msgid "To Validate"
msgstr "เพื่อตรวจสอบ"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"To prevent your question from being flagged and possibly removed, avoid "
"asking subjective questions where …"
msgstr ""
"เพื่อป้องกันไม่ให้คำถามของคุณถูกตั้งค่าสถานะและอาจถูกลบ "
"ให้หลีกเลี่ยงการถามคำถามที่มีความเป็นความคิดเห็นส่วนตัวซึ่ง ..."

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_9
msgid "Too localized"
msgstr "มีความเฉพาะถิ่นเกินไป"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_3
msgid "Too subjective and argumentative"
msgstr "เป็นความคิดเห็นส่วนตัวและมีข้อโต้แย้งมากเกินไป"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index_tags
msgid "Toolbar with button groups"
msgstr "แถบเครื่องมือที่มีกลุ่มปุ่ม"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_tree
msgid "Total Answers"
msgstr "คำตอบทั้งหมด"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_tree
msgid "Total Posts"
msgstr "โพสต์ทั้งหมด"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_tree
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_tree
msgid "Total Views"
msgstr "จำนวนยอดเข้าชมทั้งหมด"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__vote_count
msgid "Total Votes"
msgstr "คะแนนโหวตทั้งหมด"

#. module: website_forum
#: model:ir.model,name:website_forum.model_gamification_karma_tracking
msgid "Track Karma Changes"
msgstr "ติดตามความเปลี่ยนแปลงของ Karma"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid "Twitter"
msgstr "Twitter"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "Unanswered"
msgstr "ยกเลิกการตอบ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_dropdown
msgid "Undelete"
msgstr "ยกเลิกการลบ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.follow
msgid "Unfollow"
msgstr "เลิกติดตาม"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid "Unmark as Best Answer"
msgstr "ยกเลิกการทำเครื่องหมายเป็นคำตอบที่ดีที่สุด"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "Unsolved"
msgstr "ยังไม่แก้"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "Unused Tags"
msgstr "แท็กที่ไม่ได้ใช้"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__tag_unused_ids
msgid "Unused tags"
msgstr "แท็กที่ไม่ได้ใช้"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__write_uid
msgid "Updated by"
msgstr "อัปเดตโดย"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__write_date
msgid "Updated on"
msgstr "อัปเดตเมื่อ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_upvote
#: model_terms:ir.ui.view,arch_db:website_forum.vote
msgid "Upvote"
msgstr "โหวตเห็นด้วย"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_student
msgid "Upvoted question (1)"
msgstr "คำถามถูกโหวตเห็นด้วย (1)"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_great_question
msgid "Upvoted question (15)"
msgstr "คำถามถูกโหวตเห็นด้วย (15)"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_nice_question
msgid "Upvoted question (4)"
msgstr "คำถามถูกโหวตเห็นด้วย (4)"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_good_question
msgid "Upvoted question (6)"
msgstr "คำถามถูกโหวตเห็นด้วย (6)"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Use a clear, explicit and concise title"
msgstr "ใช้ชื่อเรื่องที่ชัดเจน ถูกต้อง และกระชับ"

#. module: website_forum
#: model:ir.model,name:website_forum.model_res_users
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__user_id
msgid "User"
msgstr "ผู้ใช้"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "User answer accepted"
msgstr "คำตอบของผู้ใช้ได้รับการยอมรับแล้ว"

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.forum_post_action_favorites
msgid "Users favorite posts"
msgstr "โพสต์โปรดของผู้ใช้"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
#: model_terms:ir.ui.view,arch_db:website_forum.post_dropdown
#: model_terms:ir.ui.view,arch_db:website_forum.show_flag_validator
msgid "Validate"
msgstr "ตรวจสอบ"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "Validate an answer"
msgstr "ตรวจสอบคำตอบ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_validation
msgid "Validate question"
msgstr "ตรวจสอบคำถาม"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_stats
msgid "View"
msgstr "ดู"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid "View my answer <i class=\"oi oi-arrow-right ms-1\"/>"
msgstr "ดูคำตอบของฉัน <i class=\"oi oi-arrow-right ms-1\"/>"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__views
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
#: model_terms:ir.ui.view,arch_db:website_forum.forum_related_posts
#: model_terms:ir.ui.view,arch_db:website_forum.post_stats
msgid "Views"
msgstr "มุมมอง"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_12
msgid "Violent language"
msgstr "ถ้อยคำที่รุนแรง"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__vote
msgid "Vote"
msgstr "โหวต"

#. module: website_forum
#: model:ir.model.constraint,message:website_forum.constraint_forum_post_vote_vote_uniq
msgid "Vote already exists!"
msgstr "โหวตแล้ว!"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__vote_ids
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Votes"
msgstr "โหวต"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post__state__pending
msgid "Waiting Validation"
msgstr "กำลังรอการตรวจสอบ"

#. module: website_forum
#: model:ir.model,name:website_forum.model_website
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_id
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_id
msgid "Website"
msgstr "เว็บไซต์"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__gamification_challenge__challenge_category__forum
msgid "Website / Forum"
msgstr "เว็บไซต์ / ฟอรั่ม"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_message_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_message_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_message_ids
msgid "Website Messages"
msgstr "ข้อความเว็บไซต์"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_url
msgid "Website URL"
msgstr "เว็บไซต์ URL"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__website_message_ids
#: model:ir.model.fields,help:website_forum.field_forum_post__website_message_ids
#: model:ir.model.fields,help:website_forum.field_forum_tag__website_message_ids
msgid "Website communication history"
msgstr "ประวัติการสื่อสารของเว็บไซต์"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_meta_description
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_meta_description
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_meta_description
msgid "Website meta description"
msgstr "คำอธิบายเนื้อหาเว็บไซต์"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_meta_keywords
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_meta_keywords
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_meta_keywords
msgid "Website meta keywords"
msgstr "คำสำคัญในคำอธิบายเนื้อหาเว็บไซต์"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_meta_title
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_meta_title
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_meta_title
msgid "Website meta title"
msgstr "ชื่อข้อมูลเว็บไซต์"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_meta_og_img
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_meta_og_img
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_meta_og_img
msgid "Website opengraph image"
msgstr "รูป Opengraph บนเว็บไซต์"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__welcome_message
msgid "Welcome Message"
msgstr "ข้อความต้อนรับ"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_forum.py:0
#: model_terms:forum.forum,welcome_message:website_forum.forum_help
msgid "Welcome!"
msgstr "ยินดีต้อนรับ!"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"When a question or answer is upvoted, the user who posted them will gain "
"some points, which are called \"karma points\". These points serve as a "
"rough measure of the community trust to him/her. Various moderation tasks "
"are gradually assigned to the users based on those points."
msgstr ""
"เมื่อมีการโหวตคำถามหรือคำตอบ ผู้ใช้ที่โพสต์จะได้รับคะแนนบางส่วน ซึ่งเรียกว่า"
" \"คะแนน Karma\" "
"ประเด็นเหล่านี้เป็นตัววัดความเชื่อถือของชุมชนที่มีต่อเขา/เธอ "
"งานการกลั่นกรองต่างๆ จะค่อยๆ มอบหมายให้กับผู้ใช้ตามคะแนนเหล่านั้น"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Write a clear, explicit and concise title"
msgstr "เขียนชื่อที่ชัดเจนและรัดกุม"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_header
msgid "XP"
msgstr "XP"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "You already have a pending post"
msgstr "คุณมีโพสต์ที่รอดำเนินการอยู่"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
msgid "You can share your question once it has been validated"
msgstr "คุณสามารถแชร์คำถามของคุณเมื่อได้รับการตรวจสอบแล้ว"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid "You can't vote for your own post"
msgstr "คุณไม่สามารถลงคะแนนให้กับโพสต์ของคุณเองได้"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "You cannot create recursive forum posts."
msgstr "คุณไม่สามารถสร้างโพสต์แบบเรียกซ้ำในฟอรั่มได้"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
msgid "You cannot post an empty answer"
msgstr "คุณไม่สามารถโพสต์คำตอบที่ว่างเปล่าได้"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid "You don't have enough karma"
msgstr "คุณมี karma ไม่เพียงพอ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "You have a pending post"
msgstr "คุณมีโพสต์ที่รอดำเนินการ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "You have not answered any questions yet. <br/>"
msgstr "คุณยังไม่ได้ตอบคำถาม <br/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "You have not posted any questions yet. <br/>"
msgstr "คุณยังไม่ได้โพสต์คำถาม <br/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_votes
msgid "You haven't given any votes yet."
msgstr "คุณยังไม่ได้โหวตเลย"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "You may now participate in our forums."
msgstr "คุณสามารถเข้าร่วมในฟอรั่มของเราได้แล้วตอนนี้"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "You need to have sufficient karma to edit tags"
msgstr "คุณต้องมีคะแนน Karma เพียงพอที่จะแก้ไขแท็ก"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"You should only ask practical, answerable questions based on actual problems"
" that you face. Chatty, open-ended questions diminish the usefulness of this"
" site and push other questions off the front page."
msgstr ""
"คุณควรถามคำถามที่ใช้งานได้จริงและตอบได้ โดยอิงจากปัญหาจริงที่คุณเผชิญ "
"คำถามชวนคุย ปลายเปิดลดทอนประโยชน์ของเว็บไซต์นี้และผลักคำถามอื่นๆ "
"ออกจากหน้าแรก"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "You're following this post"
msgstr "คุณกำลังติดตามโพสต์นี้"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "You've Completely Caught&amp;nbsp;Up!"
msgstr "คุณตามทันแล้ว!"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
msgid "Your Answer"
msgstr "คำตอบของคุณ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "Your favourite"
msgstr "รายการโปรดของคุณ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "accept any answer"
msgstr "ยอมรับคำตอบ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "and join this Forum"
msgstr "และเข้าร่วมฟอรั่มนี้"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index_tags
msgid "and search"
msgstr "และค้นหา"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.author_box
msgid "at"
msgstr "ที่"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "breadcrumb"
msgstr "เกล็ดขนมปัง"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "by"
msgstr "โดย"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "close any posts"
msgstr "ปิดโพสต์"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "delete any comment"
msgstr "ลบความคิดเห็น"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "delete any question or answer"
msgstr "ลบคำถามหรือคำตอบใด ๆ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "delete own comment"
msgstr "ลบความคิดเห็นของตัวเอง"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "downvote"
msgstr "โหวตไม่เห็นด้วย"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post_vote.py:0
msgid "downvoted"
msgstr "โหวตลง"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
msgid "e.g. Help"
msgstr "เช่น ช่วยเหลือ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form_add
msgid "e.g. Technical Assistance"
msgstr "เช่น ความช่วยเหลือด้านเทคนิค"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_form
msgid "e.g. When should I plant my tomatoes?"
msgstr "เช่น ฉันควรปลูกมะเขือเทศตอนช่วงไหน?"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "edit any post, view offensive flags"
msgstr "แก้ไขโพสต์ ดูการตั้งค่าสถานะที่ไม่เหมาะสม"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "every answer is equally valid: “What’s your favorite ______?”"
msgstr "ทุกคำตอบมีความถูกต้องเท่าเทียมกัน: “คุณชอบอะไร ______?”"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "flag offensive, close own questions"
msgstr "ตั้งค่าสถานะโพสต์เป็นการละเมิด และปิดคำถาม"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "for reason:"
msgstr "เนื่องจาก:"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_validation
msgid ""
"has been posted and require your validation. Click here to access the "
"question :"
msgstr ""
"ได้รับการโพสต์และต้องการการการตรวจสอบจากคุณ คลิกที่นี้เพื่อเข้าสู่คำถาม:"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_answer
msgid "has been posted. Click here to access the post :"
msgstr "ได้รับการโพสต์ คลิกที่นี่เพื่อเข้าสู่โพสต์:"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_question
msgid "has been posted. Click here to access the question :"
msgstr "ได้รับการโพสต์ คลิกที่นี้เพื่อเข้าสู่คำถาม:"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
msgid "here"
msgstr "ที่นี้"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "how to configure or customize Odoo to specific business needs,"
msgstr ""
"กำหนดค่าหรือปรับแต่ง Odoo ให้ตรงกับความต้องการทางธุรกิจเฉพาะได้อย่างไร"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "how to develop modules for your own need,"
msgstr "พัฒนาโมดูลตามความต้องการของคุณได้อย่างไร"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "how to install Odoo on a specific infrastructure,"
msgstr "ติดตั้ง Odoo บนโครงสร้างพื้นฐานเฉพาะได้อย่างไร"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
msgid ""
"if your\n"
"        answer is selected as the right one. See what you can do with karma"
msgstr ""
"ถ้า\n"
"        คำตอบของคุณถูกเลือกให้เป็นคำตอบที่ถูกต้อง ดูว่าคุณสามารถทำอย่างไรกับคะแนน Karma "

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "in your favourites"
msgstr "ในรายการโปรดของคุณ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "in your posts"
msgstr "ในโพสต์ของคุณ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "insert text link, upload files"
msgstr "แทรกลิงก์ข้อความ อัปโหลดไฟล์"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "instead."
msgstr "แทนที่"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "it is a rant disguised as a question: “______ sucks, am I right?”"
msgstr "เป็นการพูดจาหยาบคายในรูปแบบคำถาม: “______ ห่วย จริงไหม?”"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
msgid "karma is required to perform this action. "
msgstr "karma จะต้องดำเนินการนี้"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
msgid "karma points"
msgstr "คะแนน karma"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "matching \""
msgstr "ตรงกัน"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post_vote.py:0
msgid "no changes"
msgstr "ไม่มีการเปลี่ยนแปลง"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post_vote.py:0
msgid "no more downvoted"
msgstr "ไม่มีการโหวตลงอีกต่อไป"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post_vote.py:0
msgid "no more upvoted"
msgstr "ไม่มีการโหวตเพิ่มอีกต่อไป"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_question
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_validation
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "on"
msgstr "เมื่อ"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
msgid ""
"on social networks get an answer within\n"
"        5 hours. Questions shared on two social networks have"
msgstr ""
"บนเครือข่ายทางสังคมได้รับคำตอบภายใน\n"
"        5 ชั่วโมง คำถามที่แชร์ในสองช่องทางโซเชียลเน็ตเวิร์กมี"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all_all_entries
msgid "post"
msgstr "โพสต์"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all_all_entries
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "posts"
msgstr "โพสต์"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "specific questions about Odoo service offers, etc."
msgstr "คำถามเฉพาะเกี่ยวกับข้อเสนอและบริการต่าง ๆ ของ Odoo และอื่น ๆ "

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "tag"
msgstr "แท็ก"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index_tags
msgid "tags"
msgstr "แท็ก"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"there is no actual problem to be solved: “I’m curious if other people feel "
"like I do.”"
msgstr "ไม่มีปัญหาจริงที่ต้องแก้ไข: \"ฉันอยากรู้ว่าคนอื่นรู้สึกเหมือนฉันไหม\""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "upvote, add comments"
msgstr "โหวตออนไลน์และเพิ่มความคิดเห็น"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post_vote.py:0
msgid "upvoted"
msgstr "โหวตขึ้น"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "using the"
msgstr "ใช้"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"we are being asked an open-ended, hypothetical question: “What if ______ "
"happened?”"
msgstr ""
"เรากำลังถูกถามคำถามปลายเปิดและที่สมมติขึ้นว่า \"จะเกิดอะไรขึ้นถ้า ______ "
"เกิดขึ้น\""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "what's the best way to use Odoo for a specific business need,"
msgstr ""
"วิธีที่ดีที่สุดในการใช้ Odoo สำหรับความต้องการทางธุรกิจแบบเฉพาะคืออะไร"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.author_box
msgid "xp"
msgstr "xp"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"your answer is provided along with the question, and you expect more "
"answers: “I use ______ for ______, what do you use?”"
msgstr ""
"คำตอบของคุณมีให้พร้อมกับคำถาม และคุณคาดหวังคำตอบเพิ่มเติม: “ฉันใช้ ______ "
"สำหรับ ______ คุณล่ะใช้อะไร”"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "your biography can be seen as tooltip"
msgstr "ชีวประวัติของคุณสามารถมองเห็นเป็นทูลทิป"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.follow
msgid "your email..."
msgstr "อีเมลของคุณ..."
