# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_skills
# 
# Translators:
# <PERSON><PERSON><PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: hr_skills
#. odoo-python
#: code:addons/hr_skills/models/hr_skill_type.py:0
msgid "%s (copy)"
msgstr "%s (copia)"

#. module: hr_skills
#: model:ir.actions.report,print_report_name:hr_skills.action_report_employee_cv
msgid "'CV - %s' % (object.name)"
msgstr "'Curriculum Vitae - %s' % (object.name)"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_sidepanel
msgid "+1234567890"
msgstr "+1234567890"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_main_panel
msgid "2022"
msgstr "2022"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_main_panel
msgid "2023"
msgstr "2023"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_line_admin_7
msgid "A 2D/3D map generator for incremental games."
msgstr "Un generador de mapas 2D/3D para juegos incrementales."

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/skills_one2many/skills_one2many.xml:0
#: code:addons/hr_skills/static/src/xml/resume_templates.xml:0
msgid "ADD"
msgstr "AÑADIR"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_report__active
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_type__active
msgid "Active"
msgstr "Activo"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_adaptability
msgid "Adaptability"
msgstr "Adaptabilidad"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_agile_scrum
msgid "Agile and Scrum methodologies"
msgstr "Metodologías Agile y Scrum"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jog_allenkeller
msgid "Allen-Keller"
msgstr "Allen-Keller"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_line_admin_5
msgid ""
"Allows to encrypt/decrypt plain text or files. Available as a web app or as "
"an API."
msgstr ""
"Permite encriptar/desencriptar texto sin formato o archivos. Disponible como"
" aplicación web o como API."

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jth_goodman_inc
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_ngh_jackson_schwartz_and_aguirre
msgid "Analytical chemist"
msgstr "Analista químico"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_analytics
msgid "Analytics"
msgstr "Analytics"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_android
msgid "Android"
msgstr "Android"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_arabic
msgid "Arabic"
msgstr "Árabe"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_stw_green_ltd
msgid "Arboriculturist"
msgstr "Arboricultor"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jth_wilkinson_plc
msgid "Architectural technologist"
msgstr "Arquitecto técnico"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_type_view_form
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_skill_type_view_search
msgid "Archived"
msgstr "Archivado"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_ngh_armidale_city_public_school
msgid "Armidale City Public School"
msgstr "Armidale City Public School"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jve_arnoldcohen
msgid "Arnold-Cohen"
msgstr "Arnold-Cohen"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_niv_arroyo_ltd
msgid "Arroyo Ltd"
msgstr "Arroyo Ltd"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_chs_avoca_primary_school
msgid "Avoca Primary School"
msgstr "Avoca Primary School"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_al_bathurst_west_public_school
msgid "Bathurst West Public School"
msgstr "Bathurst West Public School"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_bengali
msgid "Bengali"
msgstr "Bengalí"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_big_data_technologies
msgid "Big data technologies (Hadoop,Spark)"
msgstr "Tecnologías de big data (Hadoop, Spark)"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_fme_russellwebster
msgid "Biochemist, clinical"
msgstr "Bioquímico, clínico"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_admin_blue_mountains_grammar_school
msgid "Blue Mountains Grammar School"
msgstr "Blue Mountains Grammar School"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_chs_boyd_wilson_and_moore
msgid "Boyd, Wilson and Moore"
msgstr "Boyd, Wilson and Moore"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_mit_burns_lester_and_cuevas
msgid "Burns, Lester and Cuevas"
msgstr "Burns, Lester and Cuevas"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_line_admin_3
msgid "Burtho Inc."
msgstr "Burtho Inc."

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_c
msgid "C/C++"
msgstr "C/C++"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_cms
msgid "CMS"
msgstr "CMS"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/xml/resume_templates.xml:0
msgid "CREATE A NEW ENTRY"
msgstr "CREAR UNA NUEVA ENTRADA"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_css
msgid "CSS"
msgstr "CSS"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__can_show_others
msgid "Can Show Others"
msgstr "Puede mostrar otros"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__can_show_skills
msgid "Can Show Skills"
msgstr "Puede mostrar habilidades"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jep_garcia_and_sons
msgid "Careers information officer"
msgstr "Responsable de información profesional"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_fpi_chavez_group
msgid "Chavez Group"
msgstr "Chavez Group"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_vad_christian_outreach_college
msgid "Christian Outreach College"
msgstr "Christian Outreach College"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_admin_lewisbailey
msgid "Civil Service fast streamer"
msgstr "Funcionario público en avance rápido"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jog_claremont_college
msgid "Claremont College"
msgstr "Claremont College"

#. module: hr_skills
#: model:ir.model.fields.selection,name:hr_skills.selection__hr_resume_line__display_type__classic
msgid "Classic"
msgstr "Clásico"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_cloud_computing
msgid "Cloud computing"
msgstr "Computación en la nube"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jep_cole_ltd
msgid "Cole Ltd"
msgstr "Cole Ltd"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__color
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill__color
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_type__color
msgid "Color"
msgstr "Color"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_cv_wizard_view_form
msgid "Colors"
msgstr "Colores"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_com
#: model:hr.skill,name:hr_skills.hr_skill_communication
msgid "Communication"
msgstr "Comunicación"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_report__company_id
msgid "Company"
msgstr "Compañía"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_fpi_hubbarddean
msgid "Conference centre manager"
msgstr "Gerente de centros de conferencias"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_conflict_management
msgid "Conflict Management"
msgstr "Solución de conflictos"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__show_contact
msgid "Contact Information"
msgstr "Información de contacto"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_stw_finley_rowe_and_adams
msgid "Copywriter, advertising"
msgstr "Redactor, publicidad"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_ngh_craigmore_south_junior_primary_school
msgid "Craigmore South Junior Primary School"
msgstr "Craigmore South Junior Primary School"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/resume_one2many/resume_one2many.xml:0
msgid "Create a new entry"
msgstr "Crear una nueva entrada"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/skills_one2many/skills_one2many.xml:0
msgid "Create new Skills"
msgstr "Crear nuevas habilidades"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__create_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__create_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_log__create_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__create_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line_type__create_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill__create_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_level__create_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_type__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__create_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__create_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_log__create_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__create_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line_type__create_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill__create_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_level__create_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_type__create_date
msgid "Created on"
msgstr "Creado el"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_creativity
msgid "Creativity"
msgstr "Creatividad"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_critical_thinking
msgid "Critical Thinking"
msgstr "Pensamiento crítico"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/resume_one2many/resume_one2many.xml:0
msgid "Current"
msgstr "Actual"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jep_davis_sanchez_and_miller
msgid "Customer service manager"
msgstr "Gerente de atención al cliente"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_cybersecurity
msgid "Cybersecurity"
msgstr "Ciberseguridad"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_hne_dandenong_north_primary_school
msgid "Dandenong North Primary School"
msgstr "Dandenong North Primary School"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_mit_darlington_primary_school
msgid "Darlington Primary School"
msgstr "Darlington Primary School"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_data_analysis
msgid "Data analysis/visualization"
msgstr "Análisis y visualización de datos"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_database_management
msgid "Database Management"
msgstr "Gestión de bases de datos"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_log__date
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_log_view_search
msgid "Date"
msgstr "Fecha"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__date_end
msgid "Date End"
msgstr "Fecha de finalización"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__date_start
msgid "Date Start"
msgstr "Fecha de inicio"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_chs_davis_plc
#: model:hr.resume.line,name:hr_skills.employee_resume_han_davis_plc
msgid "Davis PLC"
msgstr "Davis PLC"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jve_davis_and_sons
msgid "Davis and Sons"
msgstr "Davis and Sons"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jep_davis_sanchez_and_miller
msgid "Davis, Sanchez and Miller"
msgstr "Davis, Sanchez and Miller"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_decision_making
msgid "Decision-Making"
msgstr "Toma de decisiones"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_level__default_level
msgid "Default Level"
msgstr "Nivel por defecto"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_company
msgid "Demo Address"
msgstr "Dirección de demostración"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_company
msgid "Demo Company Name"
msgstr "Nombre de la empresa de demostración"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_log__department_id
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_report__department_id
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_report_view_search
msgid "Department"
msgstr "Departamento"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__description
#: model_terms:ir.ui.view,arch_db:hr_skills.resume_line_view_form
msgid "Description"
msgstr "Descripción"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_admin_white_inc
msgid "Designer, television/film set"
msgstr "Diseñador, plató de televisión/película"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_devops
msgid "DevOps"
msgstr "DevOps"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_digital_ad
msgid "Digital advertising"
msgstr "Publicidad digital"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_cv_wizard_view_form
msgid "Discard"
msgstr "Descartar"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_type_view_form
msgid "Display"
msgstr "Mostrar en pantalla"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__display_name
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__display_name
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_log__display_name
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__display_name
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line_type__display_name
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill__display_name
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_level__display_name
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_type__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__display_type
msgid "Display Type"
msgstr "Tipo de pantalla"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_django
msgid "Django"
msgstr "Django"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jog_douglas_thompson_and_conner
msgid "Douglas, Thompson and Conner"
msgstr "Douglas, Thompson and Conner"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.resume_line_view_form
msgid "Duration"
msgstr "Duración"

#. module: hr_skills
#: model:hr.resume.line.type,name:hr_skills.resume_type_education
msgid "Education"
msgstr "Educación"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jve_ellinbank_primary_school
msgid "Ellinbank Primary School"
msgstr "Ellinbank Primary School"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_han_elphinstone_primary_school
msgid "Elphinstone Primary School"
msgstr "Elphinstone Primary School"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_email
msgid "Email Marketing"
msgstr "Marketing por correo electrónico"

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_hr_employee
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__employee_ids
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__employee_id
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_log__employee_id
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_report__employee_id
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__employee_id
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_log_view_search
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_report_view_search
msgid "Employee"
msgstr "Empleado"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_report__display_name
msgid "Employee Name"
msgstr "Nombre del empleado"

#. module: hr_skills
#: model:ir.actions.report,name:hr_skills.action_report_employee_cv
#: model:ir.model,name:hr_skills.model_report_hr_skills_report_employee_cv
msgid "Employee Resume"
msgstr "Currículum del empleado"

#. module: hr_skills
#: model:ir.actions.act_window,name:hr_skills.hr_employee_skill_report_action
msgid "Employee Skills"
msgstr "Habilidades del empleado"

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_hr_employee_skill_report
msgid "Employee Skills Report"
msgstr "Informe de habilidades del empleado"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_report_view_search
msgid "Employees with Skills"
msgstr "Empleados con habilidades"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_report_view_search
msgid "Employees without Skills"
msgstr "Empleados sin habilidades"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_line_admin_5
msgid "Encryption/decryption"
msgstr "Encriptación/desencriptación"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_al_jones_ltd
msgid "Energy manager"
msgstr "Gerente de energía"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_qdp_hughes_parker_and_barber
msgid "Engineer, drilling"
msgstr "Ingeniero, perforación"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_admin_schultz_inc
msgid "Engineer, electrical"
msgstr "Ingeniero, eléctrico"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_fme_johnson_shaw_and_carroll
msgid "Engineer, mining"
msgstr "Ingeniero, minería"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_han_davis_plc
msgid "Engineer, production"
msgstr "Ingeniero, producción"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_english
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_main_panel
msgid "English"
msgstr "Inglés"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_line_admin_6
msgid ""
"Enter your finance data and the app tries to forecast what will be your "
"future incomes/expenses. The application uses machine learning to train "
"itself."
msgstr ""
"Introduzca sus datos financieros y la aplicación calculará cuáles serán sus "
"ingresos y gastos en el futuro. La aplicación utiliza el aprendizaje "
"automático para su funcionamiento."

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_qdp_evans_cooper_and_white
msgid "Evans, Cooper and White"
msgstr "Evans, Cooper and White"

#. module: hr_skills
#: model:hr.resume.line.type,name:hr_skills.resume_type_experience
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_main_panel
msgid "Experience"
msgstr "Experiencia"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jep_cole_ltd
msgid "Fast food restaurant manager"
msgstr "Gerente de restaurante de comida rápida"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_filipino
msgid "Filipino"
msgstr "Filipino"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_line_admin_6
msgid "Finance forecaster"
msgstr "Pronóstico financiero"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_stw_finley_rowe_and_adams
msgid "Finley, Rowe and Adams"
msgstr "Finley, Rowe and Adams"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_main_panel
msgid "Fluent"
msgstr "Fluido"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jgo_fox_and_sons
msgid "Fox and Sons"
msgstr "Fox and Sons"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_chs_freeman_williams_and_berger
msgid "Freeman, Williams and Berger"
msgstr "Freeman, Williams and Berger"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_french
msgid "French"
msgstr "Francés"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jgo_galilee_catholic_school
msgid "Galilee Catholic School"
msgstr "Galilee Catholic School"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_vad_gallegos_little_and_walters
msgid "Gallegos, Little and Walters"
msgstr "Gallegos, Little and Walters"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jep_garcia_and_sons
msgid "Garcia and Sons"
msgstr "Garcia and Sons"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_al_garcia_smith_and_king
msgid "Garcia, Smith and King"
msgstr "Garcia, Smith and King"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_chs_hanson_roach_and_jordan
msgid "Geographical information systems officer"
msgstr "Encargado de sistemas de información geográfica"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_han_perezmorgan
msgid "Geoscientist"
msgstr "Geocientífico"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_german
msgid "German"
msgstr "Alemán"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_lur_ramirez_inc
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_mit_hill_group
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_ngh_stanleymendez
msgid "Glass blower/designer"
msgstr "Soplador/diseñador de vidrio"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_go
msgid "Go"
msgstr "Go"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jth_goodman_inc
msgid "Goodman Inc"
msgstr "Goodman Inc"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_stw_green_ltd
msgid "Green Ltd"
msgstr "Green Ltd"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_admin_greeneorr
msgid "Greene-Orr"
msgstr "Greene-Orr"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_log_view_search
msgid "Group By"
msgstr "Agrupar por"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_skill_view_search
msgid "Group By..."
msgstr "Agrupar por..."

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_html
msgid "HTML"
msgstr "HTML"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_hadoop
msgid "Hadoop"
msgstr "Hadoop"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_chs_hanson_roach_and_jordan
msgid "Hanson, Roach and Jordan"
msgstr "Hanson, Roach and Jordan"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_admin_harrington_park_public_school
msgid "Harrington Park Public School"
msgstr "Harrington Park Public School"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_report__has_department_manager_access
msgid "Has Department Manager Access"
msgstr "Tiene acceso de gerente al departamento"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jve_davis_and_sons
msgid "Health physicist"
msgstr "Físico médico"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_mit_hill_group
msgid "Hill Group"
msgstr "Hill Group"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_hindi
msgid "Hindi"
msgstr "Hindi"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_lur_holy_family_primary_school
msgid "Holy Family Primary School"
msgstr "Holy Family Primary School"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_hne_nortonsilva
msgid "Horticulturist, commercial"
msgstr "Horticultor, comercial"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_fpi_hubbarddean
msgid "Hubbard-Dean"
msgstr "Hubbard-Dean"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_qdp_hughes_parker_and_barber
msgid "Hughes, Parker and Barber"
msgstr "Hughes, Parker and Barber"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_chs_freeman_williams_and_berger
msgid "Human resources officer"
msgstr "Responsable de recursos humanos"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__id
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__id
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_log__id
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_report__id
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__id
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line_type__id
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill__id
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_level__id
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_type__id
msgid "ID"
msgstr "ID"

#. module: hr_skills
#: model:hr.skill.type,name:hr_skills.hr_skill_type_it
msgid "IT"
msgstr "TI"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_it_governance_compliance
msgid "IT governance and compliance (GDPR,HIPAA,...)"
msgstr "Gobernanza y cumplimiento de las TI (GDPR, HIPAA...)"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_it_infrastructure_architecture
msgid "IT infrastructure and architecture"
msgstr "Infraestructura y arquitectura de TI"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jth_simmonswilcox
msgid "IT sales professional"
msgstr "Profesional de ventas de TI"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_it_service_management
msgid "IT service management (ITSM)"
msgstr "Gestión de servicios de TI (ITSM)"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_it_support
msgid "IT support"
msgstr "Soporte de TI"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jgo_martin_stanley_and_duncan
msgid "IT technical support officer"
msgstr "Técnico de soporte informático"

#. module: hr_skills
#: model:ir.model.fields,help:hr_skills.field_hr_skill_level__default_level
msgid ""
"If checked, this level will be the default one selected when choosing this "
"skill."
msgstr ""
"Si está marcado, este nivel será el seleccionado por defecto al elegir esta "
"habilidad."

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/skills_one2many/skills_one2many.xml:0
msgid "If skills are missing, they can be created by an HR officer."
msgstr "Si faltan habilidades, un gerente de RR. HH. puede crearlas."

#. module: hr_skills
#: model:ir.model.fields,help:hr_skills.field_hr_employee_skill_report__active
msgid ""
"If the active field is set to False, it will allow you to hide the resource "
"record without removing it."
msgstr ""
"Si el campo activo es False, le permitirá ocultar el registro del recurso "
"sin eliminarlo."

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_indonesian
msgid "Indonesian"
msgstr "Indonesio"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_niv_arroyo_ltd
msgid "Insurance risk surveyor"
msgstr "Perito de riesgos de seguros"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_iot_embedded_systems
msgid "IoT and embedded systems"
msgstr "IoT y sistemas integrados"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_ngh_jackson_schwartz_and_aguirre
msgid "Jackson, Schwartz and Aguirre"
msgstr "Jackson, Schwartz and Aguirre"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_japanese
msgid "Japanese"
msgstr "Japonés"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_java
msgid "Java"
msgstr "Java"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_javanese
msgid "Javanese"
msgstr "Javanés"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_js
msgid "Javascript"
msgstr "Javascript"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jve_saundersadkins
msgid "Jewellery designer"
msgstr "Diseñador de joyería"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_line_admin_4
msgid ""
"Job position: Development team leader\n"
"- Supported technical operations with investigating and correcting varied production support issues (Java, Perl, Shell scripts, SQL).\n"
"- Led quality assurance planning for multiple concurrent projects relative to overall system architecture or trading system changes/new developments.\n"
"- Configured and released business critical alpha and risk models using MATLAB and SQL with inputs from Portfolio Managers."
msgstr ""
"Puesto de trabajo: líder del equipo de desarrollo\n"
"- Soporte en operaciones técnicas; investigación y corrección de problemas de producción (Java, Perl, Shell scripts, SQL).\n"
"- Dirigir la planificación de la garantía de calidad para varios proyectos simultáneos relativos a la arquitectura general del sistema o a los cambios/nuevos desarrollos.\n"
"- Configuración y publicación de modelos critical Alpha y de riesgo utilizando MATLAB y SQL, con la ayuda de los directores de cartera."

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_line_admin_3
msgid ""
"Job position: Product manager\n"
"- Coordinated and managed software deployment across five system environments from development to production.\n"
"- Developed stored procedures to assist Java level programming efforts.\n"
"- Developed multiple renewable energy plant architectures, both commercial installations and defense-related."
msgstr ""
"Puesto de trabajo: gerente de producto\n"
"- Coordinación y gestión de la implementación de software en cinco entornos de sistema, desde el desarrollo hasta la producción.\n"
"- Desarrollo de procedimientos almacenados para asistir en la programación a nivel Java.\n"
"- Desarrollo de varias plantas arquitectónicas de energías renovables, tanto en instalaciones comerciales como en el sector de la defensa."

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_fme_johnson_shaw_and_carroll
msgid "Johnson, Shaw and Carroll"
msgstr "Johnson, Shaw and Carroll"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_al_jones_ltd
msgid "Jones Ltd"
msgstr "Jones Ltd"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_niv_kialla_west_primary_school
msgid "Kialla West Primary School"
msgstr "Kialla West Primary School"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_han_king_island_district_high_school
msgid "King Island District High School"
msgstr "King Island District High School"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_korean
msgid "Korean"
msgstr "Coreano"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_kotlin
msgid "Kotlin"
msgstr "Kotlin"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_qdp_rivera_shaw_and_hughes
msgid "Landscape architect"
msgstr "Arquitecto paisajista"

#. module: hr_skills
#: model:hr.skill.type,name:hr_skills.hr_skill_type_lang
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_main_panel
msgid "Languages"
msgstr "Idiomas"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__write_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__write_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_log__write_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__write_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line_type__write_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill__write_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_level__write_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_type__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__write_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__write_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_log__write_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__write_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line_type__write_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill__write_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_level__write_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_type__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jep_lawson_public_school
msgid "Lawson Public School"
msgstr "Lawson Public School"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_leadership
msgid "Leadership"
msgstr "Liderazgo"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_vad_gallegos_little_and_walters
msgid "Lecturer, higher education"
msgstr "Profesor, educación superior"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_fme_leinster_school
msgid "Leinster School"
msgstr "Leinster School"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_report__level_progress
msgid "Level Progress"
msgstr "Progreso de nivel"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_type__skill_level_ids
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_type_view_form
msgid "Levels"
msgstr "Niveles"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_fme_lewis_group
msgid "Lewis Group"
msgstr "Lewis Group"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_admin_lewisbailey
msgid "Lewis-Bailey"
msgstr "Lewis-Bailey"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jog_allenkeller
msgid "Lexicographer"
msgstr "Lexicógrafo"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_lur_lindenow_primary_school
msgid "Lindenow Primary School"
msgstr "Lindenow Primary School"

#. module: hr_skills
#: model:ir.ui.menu,name:hr_skills.hr_resume_line_type_menu
msgid "Line Types"
msgstr "Tipos de línea"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_vad_loganmartin
msgid "Logan-Martin"
msgstr "Logan-Martin"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_company
msgid "Logo"
msgstr "Logotipo"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_stw_lynchhodges
msgid "Lynch-Hodges"
msgstr "Lynch-Hodges"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_machine_learning
msgid "Machine Learning (AI)"
msgstr "Aprendizaje automático (IA)"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_admin_greeneorr
msgid "Magazine journalist"
msgstr "Periodista de revista"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_mandarin_chinese
msgid "Mandarin Chinese"
msgstr "Mandarín"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jog_mandurah_catholic_college
msgid "Mandurah Catholic College"
msgstr "Mandurah Catholic College"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_line_admin_7
msgid "Map Generator"
msgstr "Generador de mapa"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_marathi
msgid "Marathi"
msgstr "Maratí"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_sidepanel
msgid "Marc Demo"
msgstr "Marc Demo"

#. module: hr_skills
#: model:hr.skill.type,name:hr_skills.hr_skill_type_marketing
msgid "Marketing"
msgstr "Marketing"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jgo_martin_stanley_and_duncan
msgid "Martin, Stanley and Duncan"
msgstr "Martin, Stanley and Duncan"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_line_admin_1
msgid ""
"Master in Electrical engineering\n"
"            Master thesis: Better grid management and control through machine learning"
msgstr ""
"Máster en ingeniería eléctrica\n"
"            Tesis de máster: Mejor gestión y control de la capacidad informática de la red mediante aprendizaje automático"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_matlab
msgid "Matlab"
msgstr "Matlab"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jep_mcneil_rodriguez_and_warren
msgid "Mcneil, Rodriguez and Warren"
msgstr "Mcneil, Rodriguez and Warren"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_al_garcia_smith_and_king
msgid "Medical illustrator"
msgstr "Ilustrador médico"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_chs_boyd_wilson_and_moore
msgid "Medical physicist"
msgstr "Físico médico"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_fpi_chavez_group
msgid "Mental health nurse"
msgstr "Enfermera de salud mental"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jgo_fox_and_sons
msgid "Merchant navy officer"
msgstr "Oficial de la Marina Mercante"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_mobile_app_development
msgid "Mobile app development"
msgstr "Desarrollo de aplicaciones móviles"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jog_douglas_thompson_and_conner
msgid "Music therapist"
msgstr "Terapeuta musical"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__name
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line_type__name
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill__name
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_level__name
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_type__name
msgid "Name"
msgstr "Nombre"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jth_narellan_public_school
msgid "Narellan Public School"
msgstr "Narellan Public School"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_lur_narrogin_primary_school
msgid "Narrogin Primary School"
msgstr "Narrogin Primary School"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_network_administration
msgid "Network administration"
msgstr "Administración de redes"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/resume_one2many/resume_one2many.js:0
msgid "New Resume line"
msgstr "Nueva línea de currículum"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_nosql
msgid "NoSQL"
msgstr "NoSQL"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_stw_northern_bay_p12_college
msgid "Northern Bay P-12 College"
msgstr "Northern Bay P-12 College"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_hne_nortonsilva
msgid "Norton-Silva"
msgstr "Norton-Silva"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_line_admin_4
msgid "Odoo SA"
msgstr "Odoo SA"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_openness_to_criticism
msgid "Openness to criticism"
msgstr "Apertura a las críticas constructivas"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_organizational
msgid "Organizational"
msgstr "Organizacional"

#. module: hr_skills
#. odoo-javascript
#. odoo-python
#: code:addons/hr_skills/report/hr_employee_cv_report.py:0
#: code:addons/hr_skills/static/src/views/skills_list_renderer.js:0
msgid "Other"
msgstr "Otro"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__show_others
msgid "Others"
msgstr "Otros"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_fpi_our_lady_star_of_the_sea_school
msgid "Our Lady Star of the Sea School"
msgstr "Our Lady Star of the Sea School"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_php
msgid "PHP"
msgstr "PHP"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_admin_park_lake_state_school
msgid "Park Lake State School"
msgstr "Park Lake State School"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_qdp_parke_state_school
msgid "Parke State School"
msgstr "Parke State School"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_mit_parker_roberson_and_acosta
msgid "Parker, Roberson and Acosta"
msgstr "Parker, Roberson and Acosta"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_han_perezmorgan
msgid "Perez-Morgan"
msgstr "Perez-Morgan"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_perl
msgid "Perl"
msgstr "Perl"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jve_arnoldcohen
msgid "Personnel officer"
msgstr "Responsable del personal"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_persuasion
msgid "Persuasion"
msgstr "Poder de convicción"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_vad_loganmartin
msgid "Petroleum engineer"
msgstr "Ingeniero petrolero"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_qdp_phillips_jones_and_brown
msgid "Phillips, Jones and Brown"
msgstr "Phillips, Jones and Brown"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/skills_one2many/skills_one2many.xml:0
msgid "Pick a skill from the list"
msgstr "Elija una habilidad de la lista"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_mit_burns_lester_and_cuevas
msgid "Police officer"
msgstr "Agente de policía"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jog_port_curtis_road_state_school
msgid "Port Curtis Road State School"
msgstr "Port Curtis Road State School"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_portuguese
msgid "Portuguese"
msgstr "Portugués"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_main_panel
msgid "Present"
msgstr "Presente"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__color_primary
msgid "Primary Color"
msgstr "Color primario"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_cv_wizard_view_form
msgid "Print"
msgstr "Imprimir"

#. module: hr_skills
#. odoo-python
#: code:addons/hr_skills/wizard/hr_employee_cv_wizard.py:0
#: model:ir.actions.act_window,name:hr_skills.action_hr_employee_cv_wizard
#: model:ir.actions.server,name:hr_skills.action_print_employees_cv
#: model:ir.model,name:hr_skills.model_hr_employee_cv_wizard
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_cv_wizard_view_form
msgid "Print Resume"
msgstr "Imprimir currículum"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_problem_solving
msgid "Problem-Solving"
msgstr "Solución de problemas"

#. module: hr_skills
#: model:hr.skill.type,name:hr_skills.hr_skill_type_dev
msgid "Programming Languages"
msgstr "Lenguajes de programación"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__level_progress
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_log__level_progress
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_level__level_progress
msgid "Progress"
msgstr "Progreso"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.employee_skill_level_view_form
msgid "Progress (%)"
msgstr "Progreso (%)"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_main_panel
msgid "Progress bar"
msgstr "Barra de progreso"

#. module: hr_skills
#: model:ir.model.fields,help:hr_skills.field_hr_employee_skill__level_progress
#: model:ir.model.fields,help:hr_skills.field_hr_employee_skill_log__level_progress
#: model:ir.model.fields,help:hr_skills.field_hr_skill_level__level_progress
msgid "Progress from zero knowledge (0%) to fully mastered (100%)."
msgstr "Progreso desde cero (0 %) a dominio total (100 %)."

#. module: hr_skills
#: model:ir.model.constraint,message:hr_skills.constraint_hr_skill_level_check_level_progress
msgid "Progress should be a number between 0 and 100."
msgstr "El progreso debe ser un número entre 0 y 100"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_project_management
msgid "Project Management"
msgstr "Gestión de proyectos"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_mit_robinson_crawford_and_norman
msgid "Psychiatric nurse"
msgstr "Enfermero psiquiátrico"

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_hr_employee_public
msgid "Public Employee"
msgstr "Empleado público"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_public
msgid "Public Speaking"
msgstr "Oratoria"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_stw_lynchhodges
msgid "Publishing rights manager"
msgstr "Gestión de derechos de publicación"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_punjabi
msgid "Punjabi"
msgstr "Panyabí"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_python
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_main_panel
msgid "Python"
msgstr "Python"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_sql
msgid "RDMS"
msgstr "RDBMS"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_lur_ramirez_inc
msgid "Ramirez Inc"
msgstr "Ramirez Inc"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_react
msgid "React"
msgstr "React"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_resourcefulness
msgid "Resourcefulness"
msgstr "Ingeniosidad"

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_resource_resource
msgid "Resources"
msgstr "Recursos"

#. module: hr_skills
#: model:ir.ui.menu,name:hr_skills.menu_human_resources_configuration_resume
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_public_view_form_inherit
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_view_form
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_view_search
#: model_terms:ir.ui.view,arch_db:hr_skills.res_users_view_form
#: model_terms:ir.ui.view,arch_db:hr_skills.resume_line_view_form
msgid "Resume"
msgstr "Currículum"

#. module: hr_skills
#. odoo-python
#: code:addons/hr_skills/controllers/main.py:0
msgid "Resume %s"
msgstr "Currículum %s"

#. module: hr_skills
#: model:ir.actions.act_window,name:hr_skills.hr_resume_type_action
msgid "Resume Line Types"
msgstr "Tipos de líneas de currículum"

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_hr_resume_line
msgid "Resume line of an employee"
msgstr "Línea de currículum de un empleado"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee__resume_line_ids
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_public__resume_line_ids
#: model:ir.model.fields,field_description:hr_skills.field_res_users__resume_line_ids
msgid "Resume lines"
msgstr "Líneas de currículum"

#. module: hr_skills
#. odoo-python
#: code:addons/hr_skills/controllers/main.py:0
msgid "Resumes"
msgstr "Currículums"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_qdp_rivera_shaw_and_hughes
msgid "Rivera, Shaw and Hughes"
msgstr "Rivera, Shaw and Hughes"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_mit_robinson_crawford_and_norman
msgid "Robinson, Crawford and Norman"
msgstr "Robinson, Crawford and Norman"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_ruby
msgid "Ruby"
msgstr "Ruby"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_fme_russellwebster
msgid "Russell-Webster"
msgstr "Russell-Webster"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_russian
msgid "Russian"
msgstr "Ruso"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_rust
msgid "Rust"
msgstr "Rust"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_line_admin_2
msgid "Saint-Joseph School"
msgstr "Saint-Joseph School"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jve_saundersadkins
msgid "Saunders-Adkins"
msgstr "Saunders-Adkins"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_scala
msgid "Scala"
msgstr "Scala"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_admin_schultz_inc
msgid "Schultz Inc"
msgstr "Schultz Inc"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_line_admin_2
msgid "Science &amp; math"
msgstr "Ciencia y matemáticas"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_mit_parker_roberson_and_acosta
msgid "Science writer"
msgstr "Escritor científico"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_log_view_search
msgid "Search Logs"
msgstr "Buscar registros"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_skill_view_search
msgid "Search Skill"
msgstr "Buscar habilidad"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_skill_type_view_search
msgid "Search Skill Type"
msgstr "Buscar tipo de habilidad"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__color_secondary
msgid "Secondary Color"
msgstr "Color secundario"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_chs_davis_plc
msgid "Secretary, company"
msgstr "Secretario, empresa"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/form_view_one2many/form_view_one2many.xml:0
msgid "Select & Close"
msgstr "Seleccionar & cerrar"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/form_view_one2many/form_view_one2many.xml:0
msgid "Select & New"
msgstr "Seleccionar & Nuevo"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/skills_one2many/skills_one2many.js:0
msgid "Select Skills"
msgstr "Seleccionar habilidades"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line_type__sequence
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill__sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_mit_seymour_p12_college
msgid "Seymour P-12 College"
msgstr "Seymour P-12 College"

#. module: hr_skills
#: model:hr.resume.line.type,name:hr_skills.resume_type_side_projects
msgid "Side Projects"
msgstr "Proyectos secundarios"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jth_simmonswilcox
msgid "Simmons-Wilcox"
msgstr "Simmons-Wilcox"

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_hr_skill
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee__skill_ids
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__skill_id
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_log__skill_id
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_report__skill_id
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_log_view_search
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_skill_view_search
msgid "Skill"
msgstr "Habilidad"

#. module: hr_skills
#: model:ir.actions.act_window,name:hr_skills.action_hr_employee_skill_log_department
#: model:ir.actions.act_window,name:hr_skills.action_hr_employee_skill_log_employee
#: model:ir.actions.server,name:hr_skills.action_open_skills_log_department
msgid "Skill History Report"
msgstr "Informe de historial de habilidades"

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_hr_skill_level
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__skill_level_id
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_log__skill_level_id
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_report__skill_level
#: model_terms:ir.ui.view,arch_db:hr_skills.employee_skill_level_view_form
msgid "Skill Level"
msgstr "Nivel de habilidad"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.employee_skill_level_view_tree
#: model_terms:ir.ui.view,arch_db:hr_skills.employee_skill_view_tree
msgid "Skill Levels"
msgstr "Niveles de habilidad"

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_hr_skill_type
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__skill_type_id
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_log__skill_type_id
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_report__skill_type_id
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill__skill_type_id
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_level__skill_type_id
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_log_view_search
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_type_view_form
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_skill_view_search
msgid "Skill Type"
msgstr "Tipo de habilidad"

#. module: hr_skills
#: model:ir.actions.act_window,name:hr_skills.hr_skill_type_action
#: model:ir.ui.menu,name:hr_skills.hr_skill_type_menu
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_skill_type_view_search
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_skill_type_view_tree
msgid "Skill Types"
msgstr "Tipos de habilidad"

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_hr_employee_skill
msgid "Skill level for an employee"
msgstr "Nivel de habilidad para un empleado"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/components/avatar_card_resource/avatar_card_resource_popover.xml:0
#: code:addons/hr_skills/static/src/fields/skills_one2many/skills_one2many.xml:0
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee__employee_skill_ids
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__show_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_public__employee_skill_ids
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_type__skill_ids
#: model:ir.model.fields,field_description:hr_skills.field_res_users__employee_skill_ids
#: model:ir.model.fields,field_description:hr_skills.field_resource_resource__employee_skill_ids
#: model:ir.ui.menu,name:hr_skills.hr_employee_skill_report_menu
#: model_terms:ir.ui.view,arch_db:hr_skills.employee_skill_view_form
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_type_view_form
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_skill_view_form
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_main_panel
msgid "Skills"
msgstr "Habilidades"

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_hr_employee_skill_log
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_log_view_graph_department
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_log_view_graph_employee
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_log_view_tree
msgid "Skills History"
msgstr "Historial de habilidades"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/skills_one2many/skills_one2many.js:0
msgid "Skills Report"
msgstr "Informe de habilidades"

#. module: hr_skills
#: model:hr.resume.line.type,name:hr_skills.resume_type_social_media
msgid "Social Media"
msgstr "Redes sociales"

#. module: hr_skills
#: model:hr.skill.type,name:hr_skills.hr_skill_type_softskill
msgid "Soft Skills"
msgstr "Habilidades blandas"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_main_panel
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_sidepanel
msgid "Software Developer"
msgstr "Desarrollador de software"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_spanish
msgid "Spanish"
msgstr "Español"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_spark
msgid "Spark"
msgstr "Spark"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_lur_whitebell
msgid "Sports coach"
msgstr "Entrenador deportivo"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_fme_lewis_group
msgid "Sports development officer"
msgstr "Director de promoción deportiva"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_fme_st_michaels_primary_school
msgid "St Michael's Primary School"
msgstr "St Michael's Primary School"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_hne_st_peters_parish_primary_school
msgid "St Peter's Parish Primary School"
msgstr "St Peter's Parish Primary School"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_fpi_st_raphaels_primary_school
msgid "St Raphael's Primary School"
msgstr "St Raphael's Primary School"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_ngh_stanleymendez
msgid "Stanley-Mendez"
msgstr "Stanley-Mendez"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_stress_management
msgid "Stress management"
msgstr "Control del estrés"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jep_mcneil_rodriguez_and_warren
msgid "Sub"
msgstr "Substituto"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_mit_sutherland_dianella_primary_school
msgid "Sutherland Dianella Primary School"
msgstr "Sutherland Dianella Primary School"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_swift
msgid "Swift"
msgstr "Swift"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_system_administration
msgid "System Administration (Linux, Windows)"
msgstr "Administración de sistemas (Linux, Windows)"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jve_talbot_primary_school
msgid "Talbot Primary School"
msgstr "Talbot Primary School"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_qdp_phillips_jones_and_brown
msgid "Teacher, special educational needs"
msgstr "Profesor, necesidades educativas especiales"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_teamwork
msgid "Teamwork"
msgstr "Trabajo en equipo"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_telugu
msgid "Telugu"
msgstr "Télugu"

#. module: hr_skills
#. odoo-python
#: code:addons/hr_skills/models/hr_employee_skill.py:0
msgid "The skill %(name)s and skill type %(type)s doesn't match"
msgstr "La habilidad %(name)s y el tipo de habilidad %(type)s no coinciden"

#. module: hr_skills
#. odoo-python
#: code:addons/hr_skills/models/hr_employee_skill.py:0
msgid "The skill level %(level)s is not valid for skill type: %(type)s"
msgstr ""
"El nivel de habilidad %(level)s no es válido para el tipo de habilidad: "
"%(type)s"

#. module: hr_skills
#: model:ir.model.constraint,message:hr_skills.constraint_hr_resume_line_date_check
msgid "The start date must be anterior to the end date."
msgstr "La fecha de inicio debe ser anterior a la fecha de finalización."

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_qdp_evans_cooper_and_white
msgid "Therapist, speech and language"
msgstr "Terapeuta del lenguaje"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/resume_one2many/resume_one2many.xml:0
msgid ""
"There are no resume lines on this employee.\n"
"                        Why not add a new one?"
msgstr ""
"No hay líneas de currículum en este empleado.\n"
"                        ¿Por qué no añade una nueva?"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/skills_one2many/skills_one2many.xml:0
msgid "There are no skills defined in the library."
msgstr "No hay habilidades definidas en la biblioteca. "

#. module: hr_skills
#: model_terms:ir.actions.act_window,help:hr_skills.hr_employee_skill_report_action
msgid ""
"This report will give you an overview of the skills per Employee.\n"
"                Create them in configuration and add them on the Employee."
msgstr ""
"Este informe proporcione información general relacionada con las habilidades de cada empleado.\n"
"                Créelas en configuración y agréguelas al empleado."

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_vad_thomas_chirnside_primary_school
msgid "Thomas Chirnside Primary School"
msgstr "Thomas Chirnside Primary School"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_time_management
msgid "Time Management"
msgstr "Gestión del tiempo"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/skills_one2many/skills_one2many.xml:0
msgid "Timeline"
msgstr "Línea de tiempo"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.resume_line_view_form
msgid "Title"
msgstr "Título"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jgo_tottenham_central_school
msgid "Tottenham Central School"
msgstr "Tottenham Central School"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jod_wilson_ltd
msgid "Trade union research officer"
msgstr "Responsable de investigación sindical"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jep_trinity_college
msgid "Trinity College"
msgstr "Trinity College"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_turkish
msgid "Turkish"
msgstr "Turco"

#. module: hr_skills
#: model:ir.model.constraint,message:hr_skills.constraint_hr_employee_skill__unique_skill
msgid "Two levels for the same skill is not allowed"
msgstr "No se permiten dos niveles para la misma habilidad"

#. module: hr_skills
#: model:ir.model.constraint,message:hr_skills.constraint_hr_employee_skill_log__unique_skill_log
msgid "Two levels for the same skill on the same day is not allowed"
msgstr "No se permiten dos niveles para la misma habilidad en el mismo día"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_stw_tyndale_christian_school
msgid "Tyndale Christian School"
msgstr "Tyndale Christian School"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__line_type_id
msgid "Type"
msgstr "Tipo"

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_hr_resume_line_type
msgid "Type of a resume line"
msgstr "Tipo de una línea de currículum"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_typescript
msgid "TypeScript"
msgstr "TypeScript"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jod_umbakumba_school
msgid "Umbakumba School"
msgstr "Umbakumba School"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_line_admin_1
msgid "Université Libre de Bruxelles - Polytechnique"
msgstr "Université Libre de Bruxelles - Polytechnique"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_urdu
msgid "Urdu"
msgstr "Urdu"

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_res_users
msgid "User"
msgstr "Usuario"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_virtualization_containerization
msgid "Virtualization and Containerization"
msgstr "Virtualización y contenedorización"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_web_development
msgid "Web Development"
msgstr "Desarrollo web"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_admin_white_inc
msgid "White Inc"
msgstr "White Inc"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_lur_whitebell
msgid "White-Bell"
msgstr "White-Bell"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_stw_whitsunday_anglican_school
msgid "Whitsunday Anglican School"
msgstr "Whitsunday Anglican School"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/skills_one2many/skills_one2many.xml:0
msgid "Why not try adding some ?"
msgstr "¿Por qué no añade algunas?"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jth_wilkinson_plc
msgid "Wilkinson PLC"
msgstr "Wilkinson PLC"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_han_william_light_r12_school
msgid "William Light R-12 School"
msgstr "William Light R-12 School"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jod_wilson_ltd
msgid "Wilson Ltd"
msgstr "Wilson Ltd"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_fme_wodonga_primary_school
msgid "Wodonga Primary School"
msgstr "Wodonga Primary School"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jep_woodend_primary_school
msgid "Woodend Primary School"
msgstr "Woodend Primary School"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_fpi_woodridge_state_school
msgid "Woodridge State School"
msgstr "Woodridge State School"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_wu_chinese
msgid "Wu Chinese"
msgstr "Chino wu"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_vad_wycheproof_p12_college
msgid "Wycheproof P-12 College"
msgstr "Wycheproof P-12 College"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/skills_one2many/skills_one2many.xml:0
msgid "You can add skills from our library to the employee profile."
msgstr ""
"Puede añadir habilidades desde nuestra biblioteca al perfil del empleado"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_sidepanel
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_type_view_form
msgid "e.g. Languages"
msgstr "p. ej. Idiomas"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.resume_line_view_form
msgid "e.g. Odoo Inc."
msgstr "p. ej. Odoo Inc."

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_sidepanel
msgid "www.demo.com"
msgstr "www.demo.com"
