# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_stock
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON><PERSON> <arn<PERSON>@allegro.lv>, 2024
# ievaput<PERSON>a <<EMAIL>>, 2024
# Will Sensors, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-19 20:37+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Latvian (https://app.transifex.com/odoo/teams/41243/lv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lv\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n != 0 ? 1 : 2);\n"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_picking
msgid ""
".\n"
"                Manual actions may be needed."
msgstr ""

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "<i class=\"fa fa-fw fa-clock-o\"/> Awaiting arrival"
msgstr ""

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "<i class=\"fa fa-fw fa-clock-o\"/>Preparation"
msgstr ""

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "<i class=\"fa fa-fw fa-times\"/> Cancelled"
msgstr ""

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "<i class=\"fa fa-fw fa-times\"/>Cancelled"
msgstr ""

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "<i class=\"fa fa-fw fa-truck\"/> Received"
msgstr ""

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.stock_production_lot_view_form
msgid "<span class=\"o_stat_text\">Sales</span>"
msgstr "<span class=\"o_stat_text\">Pārdošana</span>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid ""
"<span class=\"small badge rounded-pill text-bg-success orders_label_text_align\">\n"
"                                        <i class=\"fa fa-fw fa-truck\"/> Shipped\n"
"                                    </span>"
msgstr ""

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_cancel_view_form_inherit
msgid ""
"<span invisible=\"not display_delivery_alert\">\n"
"                    Some deliveries are already done. Returns can be created from the Delivery Orders.\n"
"                </span>"
msgstr ""

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.report_delivery_document_inherit_sale_stock
msgid "<strong>Customer Reference</strong>"
msgstr ""

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.report_delivery_document_inherit_sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.report_saleorder_document_inherit_sale_stock
msgid "<strong>Incoterm</strong>"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order_line__is_storable
msgid "A storable product is a product for which you manage stock."
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order_line__qty_delivered_method
msgid ""
"According to product configuration, the delivered quantity can be automatically computed by mechanism:\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Analytic From expenses: the quantity is the quantity sum from posted expenses\n"
"  - Timesheet: the quantity is the sum of hours recorded on tasks linked to this sale line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""
"Atbilstoši produkta konfigurācijai piegādājamo daudzumu var automātiski aprēķināt ar mehānisma palīdzību:\n"
"  - Manuāli: daudzums tiek iestatīts manuāli uz līnijas\n"
"  - Analītisks No izdevumiem: daudzums ir iegrāmatoto izdevumu daudzuma summa\n"
"  - Laika grafiks: daudzums ir to stundu summa, kas reģistrētas uzdevumos, kuri saistīti ar šo pārdošanas pozīciju\n"
"  - Krājumu pārvietošana: daudzums tiek iegūts no apstiprinātās komplektācijas\n"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
msgid "All planned operations included"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_rules_report__so_route_ids
msgid "Apply specific routes"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__sale_order__picking_policy__direct
msgid "As soon as possible"
msgstr "Pēc iespējas ātrāk"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
msgid "Availability"
msgstr "Pieejamība"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
msgid "Available"
msgstr "Pieejams"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
msgid "Available in stock"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order__delivery_status
msgid ""
"Blue: Not Delivered/Started\n"
"            Orange: Partially Delivered\n"
"            Green: Fully Delivered"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_stock_rules_report__so_route_ids
msgid "Choose to apply SO lines specific routes."
msgstr ""

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_res_company
msgid "Companies"
msgstr "Uzņēmumi"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order__effective_date
msgid "Completion date of the first delivery order."
msgstr ""

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurācijas uzstādījumi"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.report_delivery_document_inherit_sale_stock
msgid "Customer reference"
msgstr ""

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "Date:"
msgstr "Datums:"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_users__property_warehouse_id
msgid "Default Warehouse"
msgstr ""

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.view_order_form_inherit_sale_stock
msgid "Delivery"
msgstr "Piegāde"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_cancel__display_delivery_alert
msgid "Delivery Alert"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__delivery_count
msgid "Delivery Orders"
msgstr "Preču Piegādes"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__delivery_status
msgid "Delivery Status"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order__expected_date
msgid ""
"Delivery date you can promise to the customer, computed from the minimum "
"lead time of the order lines in case of Service products. In case of "
"shipping, the shipping policy of the order will be taken into account to "
"either use the minimum or maximum lead time of the order lines."
msgstr ""
"Piegādes termiņš, ko varat apsolīt klientam, rēķinot no minimālā pasūtījuma "
"līniju izpildes termiņa, ja runa ir par servisa produktiem. Piegādes "
"gadījumā tiks ņemta vērā pasūtījuma piegādes politika, lai izmantotu "
"minimālo vai maksimālo pasūtījuma rindu izpildes termiņu."

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__display_qty_widget
msgid "Display Qty Widget"
msgstr ""

#. module: sale_stock
#. odoo-python
#: code:addons/sale_stock/models/sale_order.py:0
msgid ""
"Do not forget to change the partner on the following delivery orders: %s"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__effective_date
msgid "Effective Date"
msgstr "Izpildes Datums"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_picking
msgid "Exception(s) occurred on the picking:"
msgstr ""

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "Exception(s) occurred on the sale order(s):"
msgstr "Pārdošanas rīkojumā(-os) konstatēts(-i) izņēmums(-i):"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_picking
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "Exception(s):"
msgstr "Izņēmums(-i):"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__expected_date
msgid "Expected Date"
msgstr "Paredzamais datums"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
msgid "Expected Delivery"
msgstr ""

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.view_order_form_inherit_sale_stock
msgid "Expected:"
msgstr "Paredzams:"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__forecast_expected_date
msgid "Forecast Expected Date"
msgstr ""

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
msgid "Forecasted Stock"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__free_qty_today
msgid "Free Qty Today"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__sale_order__delivery_status__full
msgid "Fully Delivered"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__show_json_popover
msgid "Has late picking"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order__picking_policy
msgid ""
"If you deliver all products at once, the delivery order will be scheduled "
"based on the greatest product lead time. Otherwise, it will be based on the "
"shortest."
msgstr ""

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "Impacted Transfer(s):"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__incoterm
msgid "Incoterm"
msgstr "Incoterm"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__incoterm_location
msgid "Incoterm Location"
msgstr "Incoterm Atrašanās vieta"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.report_delivery_document_inherit_sale_stock
msgid "Incoterm details"
msgstr ""

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "Incoterm:"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order__incoterm
msgid ""
"International Commercial Terms are a series of predefined commercial terms "
"used in international transactions."
msgstr ""
"Starptautiskie tirdzniecības noteikumi ir iepriekš definētu komercterminu "
"virkne, ko izmanto starptautiskos darījumos."

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_users_view_form
msgid "Inventory"
msgstr "Inventārs"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_route
msgid "Inventory Routes"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__is_mto
msgid "Is Mto"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__json_popover
msgid "JSON data for the popover widget"
msgstr ""

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_account_move
msgid "Journal Entry"
msgstr "Grāmatojums"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_account_move_line
msgid "Journal Item"
msgstr "Žurnāla ieraksts"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "Last Delivery Orders"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__customer_lead
msgid "Lead Time"
msgstr "Piegādes laiks"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_lot
msgid "Lot/Serial"
msgstr "Partija/Sērija"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "Manual actions may be needed."
msgstr "Var būt nepieciešamas manuālas darbības."

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_res_config_settings__use_security_lead
msgid ""
"Margin of error for dates promised to customers. Products will be scheduled "
"for delivery that many days earlier than the actual promised date, to cope "
"with unexpected delays in the supply chain."
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_res_company__security_lead
#: model:ir.model.fields,help:sale_stock.field_res_config_settings__security_lead
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid ""
"Margin of error for dates promised to customers. Products will be scheduled "
"for procurement and delivery that many days earlier than the actual promised"
" date, to cope with unexpected delays in the supply chain."
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__qty_delivered_method
msgid "Method to update delivered qty"
msgstr "Piegādātā daudzuma atjaunināšanas metode"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid "Move forward expected delivery dates by"
msgstr ""

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_stock_help_message_template
msgid "No delivery yet! Automate them with sales orders."
msgstr ""

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
msgid "No future availability"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__sale_order__delivery_status__pending
msgid "Not Delivered"
msgstr ""

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
msgid "Not enough future availability"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order_line__customer_lead
msgid ""
"Number of days between the order confirmation and the shipping of the "
"products to the customer"
msgstr ""
"Number of days between the order confirmation and the shipping of the "
"products to the customer"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
msgid "On"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__sale_order__delivery_status__partial
msgid "Partially Delivered"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_config_settings__default_picking_policy
msgid "Picking Policy"
msgstr ""

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_procurement_group
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__procurement_group_id
msgid "Procurement Group"
msgstr "Apgādes grupa"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_product_template
msgid "Product"
msgstr "Prece"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__qty_available_today
msgid "Qty Available Today"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__qty_to_deliver
msgid "Qty To Deliver"
msgstr "Daudzums piegādei"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/sale_stock_forecasted/forecasted_details.xml:0
msgid "Quotations"
msgstr "Piedāvājumi"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "RETURN"
msgstr ""

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_stock_help_message_template
msgid ""
"Reduce back orders with reservations, locations management, smart\n"
"                replenishment propositions, shipping connectors, barcode scanner, etc."
msgstr ""

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
msgid "Remaining demand available at"
msgstr ""

#. module: sale_stock
#. odoo-python
#: code:addons/sale_stock/models/sale_order_line.py:0
msgid "Replenish on Order (MTO)"
msgstr ""

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
msgid "Reserved"
msgstr ""

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_return_picking
msgid "Return Picking"
msgstr "Preču Atgriešana"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "Returns"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__route_id
msgid "Route"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_move__sale_line_id
msgid "Sale Line"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_procurement_group__sale_id
msgid "Sale Order"
msgstr "Pārdošanas pasūtījums"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.stock_production_lot_view_form
msgid "Sale Orders"
msgstr "Pārdošanas pasūtījumi"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_lot__sale_order_count
msgid "Sale order count"
msgstr ""

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_sale_report
msgid "Sales Analysis Report"
msgstr "Pārdošanas analīzes atskaite"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_sale_order
#: model:ir.model.fields,field_description:sale_stock.field_stock_picking__sale_id
msgid "Sales Order"
msgstr "Pārdošanas pasūtījums"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_sale_order_cancel
msgid "Sales Order Cancel"
msgstr "Pārdošanas pasūtījums atcelts"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_sale_order_line
msgid "Sales Order Line"
msgstr "Pasūtījuma Rinda"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.stock_location_route_view_form_inherit_sale_stock
msgid "Sales Order Lines"
msgstr "Tirdzniecības Pasūtījuma pozīcijas"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_lot__sale_order_ids
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_stock_help_message_template
msgid "Sales Orders"
msgstr "Pārdošanas pasūtījumi"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_company__security_lead
msgid "Sales Safety Days"
msgstr ""

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid "Schedule deliveries earlier to avoid delays"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__scheduled_date
msgid "Scheduled Date"
msgstr "Plānotais datums"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_config_settings__security_lead
msgid "Security Lead Time"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_config_settings__use_security_lead
msgid "Security Lead Time for Sales"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_route__sale_selectable
msgid "Selectable on Sales Order Line"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__res_config_settings__default_picking_policy__one
msgid "Ship all products at once"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__res_config_settings__default_picking_policy__direct
msgid "Ship products as soon as available, with back orders"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__picking_policy
msgid "Shipping Policy"
msgstr "Piegādes noteikumi"

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__sale_order__delivery_status__started
msgid "Started"
msgstr ""

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_move
msgid "Stock Move"
msgstr "Preču kustība Noliktavā"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__move_ids
#: model:ir.model.fields.selection,name:sale_stock.selection__sale_order_line__qty_delivered_method__stock_move
msgid "Stock Moves"
msgstr "Preču kustība Noliktavā"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_forecasted_product_product
msgid "Stock Replenishment Report"
msgstr "Noliktavas papildināšanas pārskats"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_rule
msgid "Stock Rule"
msgstr "Noliktavas noteikums"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_rules_report
msgid "Stock Rules report"
msgstr ""

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_report_stock_report_stock_rule
msgid "Stock rule report"
msgstr "Pārskats par noliktavas noteikumiem"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/xml/delay_alert.xml:0
msgid "The delivery"
msgstr ""

#. module: sale_stock
#. odoo-python
#: code:addons/sale_stock/models/sale_order.py:0
msgid ""
"The delivery address has been changed on the Sales Order<br/>\n"
"                        From <strong>\"%(old_address)s\"</strong> to <strong>\"%(new_address)s\"</strong>,\n"
"                        You should probably update the partner on this document."
msgstr ""

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/product_catalog/sale_order_line/sale_order_line.js:0
msgid ""
"The ordered quantity cannot be decreased below the amount already delivered."
" Instead, create a return in your inventory."
msgstr ""

#. module: sale_stock
#. odoo-python
#: code:addons/sale_stock/models/sale_order_line.py:0
msgid ""
"The ordered quantity of a sale order line cannot be decreased below the "
"amount already delivered. Instead, create a return in your inventory."
msgstr ""

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
msgid "This product is replenished on demand."
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__is_storable
msgid "Track Inventory"
msgstr ""

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_picking
msgid "Transfer"
msgstr "Pārsūtījums"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__picking_ids
msgid "Transfers"
msgstr "Pārsūtījumi"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_res_users
msgid "User"
msgstr "Lietotājs"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
msgid "View Forecast"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__virtual_available_at_date
msgid "Virtual Available At Date"
msgstr ""

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_warehouse
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__warehouse_id
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__warehouse_id
#: model:ir.model.fields,field_description:sale_stock.field_sale_report__warehouse_id
msgid "Warehouse"
msgstr "Noliktava"

#. module: sale_stock
#. odoo-python
#: code:addons/sale_stock/models/sale_order.py:0
msgid "Warning!"
msgstr "Brīdinājums!"

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__sale_order__picking_policy__one
msgid "When all products are ready"
msgstr "Kad visa prece ir gatava"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid "When to start shipping"
msgstr ""

#. module: sale_stock
#. odoo-python
#: code:addons/sale_stock/models/sale_order.py:0
msgid ""
"You must have a warehouse for line using a delivery in different company."
msgstr ""

#. module: sale_stock
#. odoo-python
#: code:addons/sale_stock/models/sale_order.py:0
msgid "You must set a warehouse on your sale order to proceed."
msgstr ""

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "cancelled"
msgstr "atcelts"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid "days"
msgstr "dienas"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_picking
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "of"
msgstr "no"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "ordered instead of"
msgstr "pasūtīts šī vietā"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_picking
msgid "processed instead of"
msgstr ""

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/xml/delay_alert.xml:0
msgid "will be late."
msgstr ""
