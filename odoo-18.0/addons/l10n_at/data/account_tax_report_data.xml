<?xml version="1.0" encoding="utf-8"?>
<odoo auto_sequence="1">
    <record id="tax_report" model="account.report">
        <field name="name">Tax Report</field>
        <field name="root_report_id" ref="account.generic_tax_report"/>
        <field name="country_id" ref="base.at"/>
        <field name="filter_fiscal_position" eval="True"/>
        <field name="availability_condition">country</field>
        <field name="column_ids">
            <record id="tax_report_balance" model="account.report.column">
                <field name="name">Balance</field>
                <field name="expression_label">balance</field>
            </record>
        </field>
        <field name="line_ids">
            <record id="tax_report_line_l10n_at_non_tva_sale_report_title" model="account.report.line">
                <field name="name">3. Aufschlüsselung für die Zusammenfassende Meldung (ZM)</field>
                <field name="sequence" eval="5"/>
                <field name="aggregation_formula">(AT_ZM_IGL.balance + AT_ZM_IGL3.balance + AT_ZM_DL.balance)</field>
                <field name="children_ids">
                    <record id="tax_report_line_l10n_at_tva_line_3_zm_igl" model="account.report.line">
                        <field name="name">Innergemeinschaftliche Lieferungen</field>
                        <field name="code">AT_ZM_IGL</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_l10n_at_tva_line_3_zm_igl_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">AT_ZM_IGL</field>
                            </record>
                        </field>
                        <field name="sequence" eval="10"/>
                    </record>
                    <record id="tax_report_line_l10n_at_tva_line_3_zm_igl3" model="account.report.line">
                        <field name="name">Innergemeinschaftliche Lieferungen (Dreiecksgeschäfte)</field>
                        <field name="code">AT_ZM_IGL3</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_l10n_at_tva_line_3_zm_igl3_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">AT_ZM_IGL3</field>
                            </record>
                        </field>
                        <field name="sequence" eval="20"/>
                    </record>
                    <record id="tax_report_line_l10n_at_tva_line_3_zm_dl" model="account.report.line">
                        <field name="name">Grenzüberschreitende Dienstleistungen (Sonstige Leistungen)</field>
                        <field name="code">AT_ZM_DL</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_l10n_at_tva_line_3_zm_dl_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">AT_ZM_DL</field>
                            </record>
                        </field>
                        <field name="sequence" eval="30"/>
                    </record>
                </field>
            </record>
            <record id="tax_report_line_l10n_at_tva_sale_report_title" model="account.report.line">
                <field name="name">4. VAT Computation (U1/U30)</field>
                <field name="aggregation_formula">(AT_022_tax.balance + AT_029_tax.balance + AT_006_tax.balance + AT_037_tax.balance + AT_052_tax.balance + AT_007_tax.balance + AT_056.balance + AT_057.balance + AT_048.balance + AT_044.balance + AT_032.balance + AT_072_tax.balance + AT_073_tax.balance + AT_008_tax.balance + AT_088_tax.balance)</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="tax_report_line_l10n_at_tva_line_4_1" model="account.report.line">
                        <field name="name">4.1 Total amount of the taxable base for deliveries and other services [000]</field>
                        <field name="code">AT_000</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_l10n_at_tva_line_4_1_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">KZ 000</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_line_l10n_at_tva_line_4_2" model="account.report.line">
                        <field name="name">4.2 Plus own consumption (Section 1(1)(2), Section 3(2) and Section 3a(1a)) [001]</field>
                        <field name="code">AT_001</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_l10n_at_tva_line_4_2_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">KZ 001</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_line_l10n_at_tva_line_4_3" model="account.report.line">
                        <field name="name">4.3 Minus revenue for which the tax liability is the beneficiary according to Section 19(1) [021]</field>
                        <field name="code">AT_021</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_l10n_at_tva_line_4_3_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">KZ 021</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_line_l10n_at_tva_line_4_4" model="account.report.line">
                        <field name="name">4.4 Total</field>
                        <field name="aggregation_formula">AT_000.balance + AT_001.balance - AT_021.balance</field>
                    </record>
                    <record id="tax_report_line_l10n_at_tva_sale_01_report_title" model="account.report.line">
                        <field name="name">Tax-exempt WITH input tax deduction according to</field>
                        <field name="aggregation_formula">AT_011.balance + AT_012.balance + AT_015.balance + AT_017.balance + AT_018.balance</field>
                        <field name="children_ids">
                            <record id="tax_report_line_l10n_at_tva_line_4_5" model="account.report.line">
                                <field name="name">4.5 Section 6(1)(1) in conjunction with Section 7 (export deliveries) [011]</field>
                                <field name="code">AT_011</field>
                                <field name="expression_ids">
                                    <record id="tax_report_line_l10n_at_tva_line_4_5_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">KZ 011</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_line_l10n_at_tva_line_4_6" model="account.report.line">
                                <field name="name">4.6 Section 6(1)(1) in conjunction with Section 8 (contract processing) [012]</field>
                                <field name="code">AT_012</field>
                                <field name="expression_ids">
                                    <record id="tax_report_line_l10n_at_tva_line_4_6_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">KZ 012</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_line_l10n_at_tva_line_4_7" model="account.report.line">
                                <field name="name">4.7 Section 6(1)(2)-(6) and Section 23(5) (seafaring, aviation, ...) [015]</field>
                                <field name="code">AT_015</field>
                                <field name="expression_ids">
                                    <record id="tax_report_line_l10n_at_tva_line_4_7_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">KZ 015</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_line_l10n_at_tva_line_4_8" model="account.report.line">
                                <field name="name">4.8 Article 6(1) (Intra-Community deliveries excluding deliveries of vehicles to be specified separately below) [017]</field>
                                <field name="code">AT_017</field>
                                <field name="expression_ids">
                                    <record id="tax_report_line_l10n_at_tva_line_4_8_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">KZ 017</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_line_l10n_at_tva_line_4_9" model="account.report.line">
                                <field name="name">4.9 Article 6(1), if deliveries of new vehicles were made to customers without a VAT number or by vehicle suppliers pursuant to article. 2 [018]</field>
                                <field name="code">AT_018</field>
                                <field name="expression_ids">
                                    <record id="tax_report_line_l10n_at_tva_line_4_9_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">KZ 018</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_line_l10n_at_tva_sale_02_report_title" model="account.report.line">
                        <field name="name">Tax-exempt WITHOUT input tax deduction according to</field>
                        <field name="aggregation_formula">AT_019.balance + AT_016.balance + AT_020.balance</field>
                        <field name="children_ids">
                            <record id="tax_report_line_l10n_at_tva_line_4_10" model="account.report.line">
                                <field name="name">4.10 Section 6(1)(9)(a) (land sales) [019]</field>
                                <field name="code">AT_019</field>
                                <field name="expression_ids">
                                    <record id="tax_report_line_l10n_at_tva_line_4_10_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">KZ 019</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_line_l10n_at_tva_line_4_11" model="account.report.line">
                                <field name="name">4.11 Section 6(1)(27) (small business) [016]</field>
                                <field name="code">AT_016</field>
                                <field name="expression_ids">
                                    <record id="tax_report_line_l10n_at_tva_line_4_11_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">KZ 016</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_line_l10n_at_tva_line_4_12" model="account.report.line">
                                <field name="name">4.12 Section 6(1)(...) (other tax-exempt transactions without deduction of input tax) [020]</field>
                                <field name="code">AT_020</field>
                                <field name="expression_ids">
                                    <record id="tax_report_line_l10n_at_tva_line_4_12_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">KZ 020</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_line_l10n_at_tva_line_4_13" model="account.report.line">
                        <field name="name">4.13 Total amount of taxable deliveries, other services and own consumption (including taxable advance payments)</field>
                        <field name="aggregation_formula">(AT_000.balance + AT_001.balance - AT_021.balance) - AT_011.balance - AT_012.balance - AT_015.balance - AT_017.balance - AT_018.balance - AT_019.balance - AT_016.balance - AT_020.balance</field>
                    </record>
                    <record id="tax_report_line_at_base_title_umsatz_base_4_14_19" model="account.report.line">
                        <field name="name">Taxable base</field>
                        <field name="aggregation_formula">AT_022_base.balance + AT_029_base.balance + AT_006_base.balance + AT_037_base.balance + AT_052_base.balance + AT_007_base.balance</field>
                        <field name="children_ids">
                            <record id="tax_report_line_l10n_at_tva_line_4_14_base" model="account.report.line">
                                <field name="name">4.14 20% Standard rate [022]</field>
                                <field name="code">AT_022_base</field>
                                <field name="expression_ids">
                                    <record id="tax_report_line_l10n_at_tva_line_4_14_base_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">KZ 022 Bemessungsgrundlage</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_line_l10n_at_tva_line_4_15_base" model="account.report.line">
                                <field name="name">4.15 10% Reduced rate [029]</field>
                                <field name="code">AT_029_base</field>
                                <field name="expression_ids">
                                    <record id="tax_report_line_l10n_at_tva_line_4_15_base_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">KZ 029 Bemessungsgrundlage</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_line_l10n_at_tva_line_4_16_base" model="account.report.line">
                                <field name="name">4.16 13% Reduced rate [006]</field>
                                <field name="code">AT_006_base</field>
                                <field name="expression_ids">
                                    <record id="tax_report_line_l10n_at_tva_line_4_16_base_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">KZ 006 Bemessungsgrundlage</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_line_l10n_at_tva_line_4_17_base" model="account.report.line">
                                <field name="name">4.17 19% for Jungholz and Mittelberg [037]</field>
                                <field name="code">AT_037_base</field>
                                <field name="expression_ids">
                                    <record id="tax_report_line_l10n_at_tva_line_4_17_base_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">KZ 037 Bemessungsgrundlage</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_line_l10n_at_tva_line_4_18_base" model="account.report.line">
                                <field name="name">4.18 10% Additional tax for flat-rate agricultural and forestry holdings [052]</field>
                                <field name="code">AT_052_base</field>
                                <field name="expression_ids">
                                    <record id="tax_report_line_l10n_at_tva_line_4_18_base_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">KZ 052 Bemessungsgrundlage</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_line_l10n_at_tva_line_4_19_base" model="account.report.line">
                                <field name="name">4.19 7% Additional tax for flat-rate agricultural and forestry holdings [007]</field>
                                <field name="code">AT_007_base</field>
                                <field name="expression_ids">
                                    <record id="tax_report_line_l10n_at_tva_line_4_19_base_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">KZ 007 Bemessungsgrundlage</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_line_at_tax_title_4_14_19" model="account.report.line">
                        <field name="name">VAT</field>
                        <field name="aggregation_formula">AT_022_tax.balance + AT_029_tax.balance + AT_006_tax.balance + AT_037_tax.balance + AT_052_tax.balance + AT_007_tax.balance</field>
                        <field name="children_ids">
                            <record id="tax_report_line_l10n_at_tva_line_4_14_tax" model="account.report.line">
                                <field name="name">4.14 20% Standard rate</field>
                                <field name="code">AT_022_tax</field>
                                <field name="expression_ids">
                                    <record id="tax_report_line_l10n_at_tva_line_4_14_tax_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">KZ 022 Umsatzsteuer</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_line_l10n_at_tva_line_4_15_tax" model="account.report.line">
                                <field name="name">4.15 10% Reduced rate</field>
                                <field name="code">AT_029_tax</field>
                                <field name="expression_ids">
                                    <record id="tax_report_line_l10n_at_tva_line_4_15_tax_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">KZ 029 Umsatzsteuer</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_line_l10n_at_tva_line_4_16_tax" model="account.report.line">
                                <field name="name">4.16 13% Reduced rate</field>
                                <field name="code">AT_006_tax</field>
                                <field name="expression_ids">
                                    <record id="tax_report_line_l10n_at_tva_line_4_16_tax_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">KZ 006 Umsatzsteuer</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_line_l10n_at_tva_line_4_17_tax" model="account.report.line">
                                <field name="name">4.17 19% for Jungholz and Mittelberg</field>
                                <field name="code">AT_037_tax</field>
                                <field name="expression_ids">
                                    <record id="tax_report_line_l10n_at_tva_line_4_17_tax_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">KZ 037 Umsatzsteuer</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_line_l10n_at_tva_line_4_18_tax" model="account.report.line">
                                <field name="name">4.18 10% Additional tax for flat-rate agricultural and forestry holdings</field>
                                <field name="code">AT_052_tax</field>
                                <field name="expression_ids">
                                    <record id="tax_report_line_l10n_at_tva_line_4_18_tax_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">KZ 052 Umsatzsteuer</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_line_l10n_at_tva_line_4_19_tax" model="account.report.line">
                                <field name="name">4.19 7% Additional tax for flat-rate agricultural and forestry holdings</field>
                                <field name="code">AT_007_tax</field>
                                <field name="expression_ids">
                                    <record id="tax_report_line_l10n_at_tva_line_4_19_tax_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">KZ 007 Umsatzsteuer</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_line_l10n_at_tva_line_4_20" model="account.report.line">
                        <field name="name">4.20 Tax liability pursuant to Section 11(12,14), Section 16(2) and pursuant to Art. 7(4) [056]</field>
                        <field name="code">AT_056</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_l10n_at_tva_line_4_20_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">KZ 056</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_line_l10n_at_tva_line_4_21" model="account.report.line">
                        <field name="name">4.21 Tax liability pursuant to Section 19(1) second sentence, Section 19(1c,1e) and pursuant to Art. 25(5) [057]</field>
                        <field name="code">AT_057</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_l10n_at_tva_line_4_21_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">KZ 057</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_line_l10n_at_tva_line_4_22" model="account.report.line">
                        <field name="name">4.22 Tax liability according to Section 19(1a) (construction services) [048]</field>
                        <field name="code">AT_048</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_l10n_at_tva_line_4_22_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">KZ 048</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_line_l10n_at_tva_line_4_23" model="account.report.line">
                        <field name="name">4.23 Tax liability under Section 19(1b) (security property, ...) [044]</field>
                        <field name="code">AT_044</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_l10n_at_tva_line_4_23_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">KZ 044</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_line_l10n_at_tva_line_4_24" model="account.report.line">
                        <field name="name">4.24 Tax liability under Section 19(1d) (scrap and waste materials) [032]</field>
                        <field name="code">AT_032</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_l10n_at_tva_line_4_24_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">KZ 032</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_line_l10n_at_tva_sale_03_report_title" model="account.report.line">
                        <field name="name">Intra-Community acquisition</field>
                        <field name="aggregation_formula">AT_070.balance + AT_071.balance</field>
                        <field name="children_ids">
                            <record id="tax_report_line_l10n_at_tva_line_4_25" model="account.report.line">
                                <field name="name">4.25 Total amount of taxable amounts for intra-Community acquisitions [070]</field>
                                <field name="code">AT_070</field>
                                <field name="expression_ids">
                                    <record id="tax_report_line_l10n_at_tva_line_4_25_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">KZ 070</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_line_l10n_at_tva_line_4_26" model="account.report.line">
                                <field name="name">4.26 Tax-exempt under Art. 6(2) [071]</field>
                                <field name="code">AT_071</field>
                                <field name="expression_ids">
                                    <record id="tax_report_line_l10n_at_tva_line_4_26_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">KZ 071</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_line_l10n_at_tva_line_4_27" model="account.report.line">
                                <field name="name">4.27 Total amount of taxable intra-Community acquisitions</field>
                                <field name="aggregation_formula">AT_070.balance - AT_071.balance</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_line_l10n_at_tva_sale_04_report_title" model="account.report.line">
                        <field name="name">Of which taxable with</field> <!-- TODO: -->
                        <field name="aggregation_formula">AT_072_base.balance + AT_073_base.balance + AT_008_base.balance + AT_088_base.balance</field>
                        <field name="children_ids">
                            <record id="tax_report_line_at_base_title_umsatz_base_4_28_31" model="account.report.line">
                                <field name="name">Taxable base</field>
                                <field name="aggregation_formula">AT_072_base.balance + AT_073_base.balance + AT_008_base.balance + AT_088_base.balance</field>
                                <field name="children_ids">
                                    <record id="tax_report_line_l10n_at_tva_line_4_28_base" model="account.report.line">
                                        <field name="name">4.28 20% Standard rate [072]</field>
                                        <field name="code">AT_072_base</field>
                                        <field name="expression_ids">
                                            <record id="tax_report_line_l10n_at_tva_line_4_28_base_tag" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">tax_tags</field>
                                                <field name="formula">KZ 072 Bemessungsgrundlage</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="tax_report_line_l10n_at_tva_line_4_29_base" model="account.report.line">
                                        <field name="name">4.29 10% Reduced rate [073]</field>
                                        <field name="code">AT_073_base</field>
                                        <field name="expression_ids">
                                            <record id="tax_report_line_l10n_at_tva_line_4_29_base_tag" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">tax_tags</field>
                                                <field name="formula">KZ 073 Bemessungsgrundlage</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="tax_report_line_l10n_at_tva_line_4_30_base" model="account.report.line">
                                        <field name="name">4.30 13% Reduced rate [008]</field>
                                        <field name="code">AT_008_base</field>
                                        <field name="expression_ids">
                                            <record id="tax_report_line_l10n_at_tva_line_4_30_base_tag" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">tax_tags</field>
                                                <field name="formula">KZ 008 Bemessungsgrundlage</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="tax_report_line_l10n_at_tva_line_4_31_base" model="account.report.line">
                                        <field name="name">4.31 19% for Jungholz and Mittelberg [088]</field>
                                        <field name="code">AT_088_base</field>
                                        <field name="expression_ids">
                                            <record id="tax_report_line_l10n_at_tva_line_4_31_base_tag" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">tax_tags</field>
                                                <field name="formula">KZ 088 Bemessungsgrundlage</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_line_at_tax_title_4_28_31" model="account.report.line">
                                <field name="name">VAT</field>
                                <field name="aggregation_formula">AT_072_tax.balance + AT_073_tax.balance + AT_008_tax.balance + AT_088_tax.balance</field>
                                <field name="children_ids">
                                    <record id="tax_report_line_l10n_at_tva_line_4_28_tax" model="account.report.line">
                                        <field name="name">4.28 20% Standard rate</field>
                                        <field name="code">AT_072_tax</field>
                                        <field name="expression_ids">
                                            <record id="tax_report_line_l10n_at_tva_line_4_28_tax_tag" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">tax_tags</field>
                                                <field name="formula">KZ 072 Umsatzsteuer</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="tax_report_line_l10n_at_tva_line_4_29_tax" model="account.report.line">
                                        <field name="name">4.29 10% Reduced rate</field>
                                        <field name="code">AT_073_tax</field>
                                        <field name="expression_ids">
                                            <record id="tax_report_line_l10n_at_tva_line_4_29_tax_tag" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">tax_tags</field>
                                                <field name="formula">KZ 073 Umsatzsteuer</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="tax_report_line_l10n_at_tva_line_4_30_tax" model="account.report.line">
                                        <field name="name">4.30 13% Reduced rate</field>
                                        <field name="code">AT_008_tax</field>
                                        <field name="expression_ids">
                                            <record id="tax_report_line_l10n_at_tva_line_4_30_tax_tag" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">tax_tags</field>
                                                <field name="formula">KZ 008 Umsatzsteuer</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="tax_report_line_l10n_at_tva_line_4_31_tax" model="account.report.line">
                                        <field name="name">4.31 19% for Jungholz and Mittelberg</field>
                                        <field name="code">AT_088_tax</field>
                                        <field name="expression_ids">
                                            <record id="tax_report_line_l10n_at_tva_line_4_31_tax_tag" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">tax_tags</field>
                                                <field name="formula">KZ 088 Umsatzsteuer</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_line_l10n_at_tva_sale_05_report_title" model="account.report.line">
                        <field name="name">Non-taxable acquisitions</field>
                        <field name="aggregation_formula">AT_076.balance + AT_077.balance</field>
                        <field name="children_ids">
                            <record id="tax_report_line_l10n_at_tva_line_4_32" model="account.report.line">
                                <field name="name">4.32 Acquisitions pursuant to Art. 3(8), second sentence, that have been taxed in the Member State of destination [076]</field>
                                <field name="code">AT_076</field>
                                <field name="expression_ids">
                                    <record id="tax_report_line_l10n_at_tva_line_4_32_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">KZ 076</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_line_l10n_at_tva_line_4_33" model="account.report.line">
                                <field name="name">4.33 Acquisitions under Art. 3(8), second sentence, deemed to be taxed domestically under Art. 25(2) [077]</field>
                                <field name="code">AT_077</field>
                                <field name="expression_ids">
                                    <record id="tax_report_line_l10n_at_tva_line_4_33_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">KZ 077</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="tax_report_line_l10n_at_tva_purchase_report_title" model="account.report.line">
                <field name="name">5. Deductible input tax computation</field>
                <field name="aggregation_formula">AT_060.balance + AT_061.balance + AT_083.balance + AT_065.balance + AT_066.balance + AT_082.balance + AT_087.balance + AT_089.balance + AT_064.balance + AT_062.balance + AT_063.balance + AT_067.balance</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="tax_report_line_l10n_at_tva_line_5_1" model="account.report.line">
                        <field name="name">5.1 Total amount of input taxes (excluding amounts to be shown separately below) [060]</field>
                        <field name="code">AT_060</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_l10n_at_tva_line_5_1_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">KZ 060</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_line_l10n_at_tva_line_5_2" model="account.report.line">
                        <field name="name">5.2 Input tax relating to import turnover tax paid (Section 12(1)(2)(a)) [061]</field>
                        <field name="code">AT_061</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_l10n_at_tva_line_5_2_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">KZ 061</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_line_l10n_at_tva_line_5_3" model="account.report.line">
                        <field name="name">5.3 Input tax concerning the import turnover tax owed and entered in the tax account (Section 12(1)(2)(b)) [083]</field>
                        <field name="code">AT_083</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_l10n_at_tva_line_5_3_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">KZ 083</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_line_l10n_at_tva_line_5_4" model="account.report.line">
                        <field name="name">5.4 Input taxes from intra-Community acquisition [065]</field>
                        <field name="code">AT_065</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_l10n_at_tva_line_5_4_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">KZ 065</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_line_l10n_at_tva_line_5_5" model="account.report.line">
                        <field name="name">5.5 Input taxes concerning the tax liability pursuant to Section 19(1) second sentence, Section 19(1c,1e) and pursuant to Art. 25(5) [066]</field>
                        <field name="code">AT_066</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_l10n_at_tva_line_5_5_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">KZ 066</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_line_l10n_at_tva_line_5_6" model="account.report.line">
                        <field name="name">5.6 Input taxes relating to tax liability pursuant to Section 19(1a) (construction services) [082]</field>
                        <field name="code">AT_082</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_l10n_at_tva_line_5_6_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">KZ 082</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_line_l10n_at_tva_line_5_7" model="account.report.line">
                        <field name="name">5.7 Input taxes relating to tax liability under Section 19(1b) (security property, ...) [087]</field>
                        <field name="code">AT_087</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_l10n_at_tva_line_5_7_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">KZ 087</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_line_l10n_at_tva_line_5_8" model="account.report.line">
                        <field name="name">5.8 Input taxes relating to tax liability under Section 19(1d) (scrap and waste materials) [089]</field>
                        <field name="code">AT_089</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_l10n_at_tva_line_5_8_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">KZ 089</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_line_l10n_at_tva_line_5_9" model="account.report.line">
                        <field name="name">5.9 Input taxes for intra-Community deliveries of new vehicles by vehicle suppliers under Art. 2 [064]</field>
                        <field name="code">AT_064</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_l10n_at_tva_line_5_9_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">KZ 064</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_line_l10n_at_tva_line_5_10" model="account.report.line">
                        <field name="name">5.10 Not deductible under Section 12(3) in conjunction with Subsections 4 and 5 [062]</field>
                        <field name="code">AT_062</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_l10n_at_tva_line_5_10_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">KZ 062</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_line_l10n_at_tva_line_5_11" model="account.report.line">
                        <field name="name">5.11 Correction pursuant to Section 12(10,11) [063]</field>
                        <field name="code">AT_063</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_l10n_at_tva_line_5_11_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">KZ 063</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_line_l10n_at_tva_line_5_12" model="account.report.line">
                        <field name="name">5.12 Correction pursuant to Section 16 [067]</field>
                        <field name="code">AT_067</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_l10n_at_tva_line_5_12_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">KZ 067</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_line_l10n_at_tva_line_5_13" model="account.report.line">
                        <field name="name">5.13 Total amount of deductible input tax</field>
                        <field name="aggregation_formula">AT_060.balance + AT_061.balance + AT_083.balance + AT_065.balance + AT_066.balance + AT_082.balance + AT_087.balance + AT_089.balance + AT_064.balance + AT_062.balance + AT_063.balance + AT_067.balance</field>
                    </record>
                </field>
            </record>
            <record id="tax_report_line_l10n_at_tva_final_report_title" model="account.report.line">
                <field name="name">6. Other corrections</field>
                <field name="aggregation_formula">AT_090.balance</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="tax_report_line_l10n_at_tva_line_6" model="account.report.line">
                        <field name="name">Other corrections [090]</field>
                        <field name="code">AT_090</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_l10n_at_tva_line_6_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">KZ 090</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="tax_report_line_l10n_at_tva_line_7" model="account.report.line">
                <field name="name">7. Prepayment/debit (-) or credit/surplus (+) [095]</field>
                <field name="aggregation_formula">(AT_022_tax.balance + AT_029_tax.balance + AT_006_tax.balance + AT_037_tax.balance + AT_052_tax.balance + AT_007_tax.balance + AT_056.balance + AT_057.balance + AT_048.balance + AT_044.balance + AT_032.balance + AT_072_tax.balance + AT_073_tax.balance + AT_008_tax.balance + AT_088_tax.balance + AT_060.balance + AT_061.balance + AT_083.balance + AT_065.balance + AT_066.balance + AT_082.balance + AT_087.balance + AT_089.balance + AT_064.balance + AT_062.balance + AT_063.balance + AT_067.balance) + AT_090.balance</field>
                <field name="hierarchy_level">0</field>
            </record>
        </field>
    </record>
</odoo>
