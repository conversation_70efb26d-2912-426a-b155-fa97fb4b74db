# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_invoice_extract
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:26+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Ukrainian (https://app.transifex.com/odoo/teams/41243/uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != 11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || (n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__message_needaction
msgid "Action Needed"
msgstr "Необхідна дія"

#. module: account_invoice_extract
#: model:ir.model,name:account_invoice_extract.model_ir_attachment
msgid "Attachment"
msgstr "Прикріплення"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__message_attachment_count
msgid "Attachment Count"
msgstr "Підрахунок прикріплення"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_bank_statement_line__extract_can_show_banners
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__extract_can_show_banners
msgid "Can show the ocr banners"
msgstr "Може показувати банери OCR"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__extract_can_show_send_button
msgid "Can show the ocr send button"
msgstr "Може показувати надіслану кнопку ocr "

#. module: account_invoice_extract
#: model:ir.model,name:account_invoice_extract.model_res_company
msgid "Companies"
msgstr "Компанії"

#. module: account_invoice_extract
#: model:ir.model,name:account_invoice_extract.model_res_config_settings
msgid "Config Settings"
msgstr "Налаштування"

#. module: account_invoice_extract
#. odoo-python
#: code:addons/account_invoice_extract/models/account_invoice.py:0
msgid "Couldn't reload AI data."
msgstr "Неможливо повторно завантажити дані AI."

#. module: account_invoice_extract
#. odoo-javascript
#: code:addons/account_invoice_extract/static/src/js/invoice_extract_form_renderer.js:0
msgid "Create"
msgstr "Створити"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__create_uid
msgid "Created by"
msgstr "Створив"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__create_date
msgid "Created on"
msgstr "Створено"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_res_config_settings__extract_out_invoice_digitalization_mode
msgid "Customer Invoices"
msgstr "Рахунки клієнта"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_res_company__extract_out_invoice_digitalization_mode
msgid "Digitization mode on customer invoices"
msgstr "Режим оцифровки на рахунках клієнтів"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_res_company__extract_in_invoice_digitalization_mode
msgid "Digitization mode on vendor bills"
msgstr "Режим оцифровки на рахунках від постачальників"

#. module: account_invoice_extract
#: model:ir.model.fields.selection,name:account_invoice_extract.selection__res_company__extract_in_invoice_digitalization_mode__auto_send
#: model:ir.model.fields.selection,name:account_invoice_extract.selection__res_company__extract_out_invoice_digitalization_mode__auto_send
msgid "Digitize automatically"
msgstr "Оцифровувати автоматично"

#. module: account_invoice_extract
#: model_terms:ir.ui.view,arch_db:account_invoice_extract.view_move_form_inherit_ocr
msgid "Digitize document"
msgstr "Оцифрувати документ"

#. module: account_invoice_extract
#: model:ir.model.fields.selection,name:account_invoice_extract.selection__res_company__extract_in_invoice_digitalization_mode__manual_send
#: model:ir.model.fields.selection,name:account_invoice_extract.selection__res_company__extract_out_invoice_digitalization_mode__manual_send
msgid "Digitize on demand only"
msgstr "Оцифровувати лише на вимогу"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__display_name
msgid "Display Name"
msgstr "Назва для відображення"

#. module: account_invoice_extract
#: model:ir.model.fields.selection,name:account_invoice_extract.selection__res_company__extract_in_invoice_digitalization_mode__no_send
#: model:ir.model.fields.selection,name:account_invoice_extract.selection__res_company__extract_out_invoice_digitalization_mode__no_send
msgid "Do not digitize"
msgstr "Не оцифровувати"

#. module: account_invoice_extract
#: model_terms:ir.ui.view,arch_db:account_invoice_extract.res_config_settings_view_form
msgid "Enable to get only one invoice line per tax"
msgstr "Може отримати лише один рядок рахунку на податок"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__extract_error_message
msgid "Error message"
msgstr "Повідомлення про помилку"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_bank_statement_line__extract_attachment_id
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__extract_attachment_id
msgid "Extract Attachment"
msgstr "Витягти прикрпілення"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_bank_statement_line__extract_detected_layout
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__extract_detected_layout
msgid "Extract Detected Layout Id"
msgstr "Id шаблону виявленого вилучення"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_bank_statement_line__extract_partner_name
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__extract_partner_name
msgid "Extract Detected Partner Name"
msgstr "Вилучити виявлене ім’я партнера"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_bank_statement_line__extract_prefill_data
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__extract_prefill_data
msgid "Extract Prefill Data"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__extract_state_processed
msgid "Extract State Processed"
msgstr "Оброблений статус вилучення"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_bank_statement_line__extract_word_ids
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__extract_word_ids
msgid "Extract Word"
msgstr "Вилучити слово"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__extract_state
msgid "Extract state"
msgstr "Стан вилучення"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__extract_status
msgid "Extract status"
msgstr "Статус вилучення"

#. module: account_invoice_extract
#: model:ir.model,name:account_invoice_extract.model_account_invoice_extract_words
msgid "Extracted words from invoice scan"
msgstr "Вилучені слова зі скану рахунка"

#. module: account_invoice_extract
#: model_terms:ir.ui.view,arch_db:account_invoice_extract.view_move_form_inherit_ocr
msgid "Extraction Information"
msgstr "Інформація про вилучення"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__field
msgid "Field"
msgstr "Поле"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__message_follower_ids
msgid "Followers"
msgstr "Підписники"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__message_partner_ids
msgid "Followers (Partners)"
msgstr "Підписники (Партнери)"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__has_message
msgid "Has Message"
msgstr "Є повідомлення"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__id
msgid "ID"
msgstr "ID"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__extract_document_uuid
msgid "ID of the request to IAP-OCR"
msgstr "ID запиту на IAP-OCR"

#. module: account_invoice_extract
#: model:ir.model.fields,help:account_invoice_extract.field_account_move__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Якщо позначено, то нові повідомлення будуть потребувати вашої уваги."

#. module: account_invoice_extract
#: model:ir.model.fields,help:account_invoice_extract.field_account_move__message_has_error
#: model:ir.model.fields,help:account_invoice_extract.field_account_move__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Якщо позначено, деякі повідомлення мають помилку доставки."

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__invoice_id
msgid "Invoice"
msgstr "Рахунок"

#. module: account_invoice_extract
#: model:ir.actions.server,name:account_invoice_extract.ir_cron_update_ocr_status_ir_actions_server
msgid "Invoice OCR: Update All Status"
msgstr "Рахунок OCR: оновити усі статуси"

#. module: account_invoice_extract
#: model:ir.actions.server,name:account_invoice_extract.ir_cron_ocr_validate_ir_actions_server
msgid "Invoice OCR: Validate Invoices"
msgstr "OCR рахунку: підтвердити рахунки"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__message_is_follower
msgid "Is Follower"
msgstr "Стежить"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__is_in_extractable_state
msgid "Is In Extractable State"
msgstr "В статусі вилучення"

#. module: account_invoice_extract
#: model:ir.model,name:account_invoice_extract.model_account_move
msgid "Journal Entry"
msgstr "Запис у журналі"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__write_uid
msgid "Last Updated by"
msgstr "Востаннє оновив"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__write_date
msgid "Last Updated on"
msgstr "Останнє оновлення"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__message_main_attachment_id
msgid "Main Attachment"
msgstr "Основне прикріплення"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__message_has_error
msgid "Message Delivery error"
msgstr "Помилка доставлення повідомлення"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__message_ids
msgid "Messages"
msgstr "Повідомлення"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__message_needaction_counter
msgid "Number of Actions"
msgstr "Кількість дій"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__message_has_error_counter
msgid "Number of errors"
msgstr "Кількість помилок"

#. module: account_invoice_extract
#: model:ir.model.fields,help:account_invoice_extract.field_account_move__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Кількість повідомлень, які вимагають дії"

#. module: account_invoice_extract
#: model:ir.model.fields,help:account_invoice_extract.field_account_move__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Кількість повідомлень з помилковою дставкою"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__ocr_selected
msgid "Ocr Selected"
msgstr "Ocr вибрані"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__rating_ids
msgid "Ratings"
msgstr "Оцінювання"

#. module: account_invoice_extract
#: model_terms:ir.ui.view,arch_db:account_invoice_extract.view_move_form_inherit_ocr
msgid "Reload AI Data"
msgstr "Перезавантажити дані AI"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Помилка доставки SMS"

#. module: account_invoice_extract
#: model:ir.actions.server,name:account_invoice_extract.model_account_send_for_digitalization
msgid "Send Bills for digitization"
msgstr "Надіслати рахунки від постачальників для оцифрування"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_res_company__extract_single_line_per_tax
#: model:ir.model.fields,field_description:account_invoice_extract.field_res_config_settings__extract_single_line_per_tax
msgid "Single Invoice Line Per Tax"
msgstr "Єдиний рядок рахунку на податок"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__user_selected
msgid "User Selected"
msgstr "Вибрано користувача"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_res_config_settings__extract_in_invoice_digitalization_mode
msgid "Vendor Bills"
msgstr "Рахунки постачальників"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__website_message_ids
msgid "Website Messages"
msgstr "Повідомлення з веб-сайту"

#. module: account_invoice_extract
#: model:ir.model.fields,help:account_invoice_extract.field_account_move__website_message_ids
msgid "Website communication history"
msgstr "Історія бесіди на сайті"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__word_box_angle
msgid "Word Box Angle"
msgstr "Word Box Angle"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__word_box_height
msgid "Word Box Height"
msgstr "Word Box Height"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__word_box_midX
msgid "Word Box Midx"
msgstr "Word Box Midx"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__word_box_midY
msgid "Word Box Midy"
msgstr "Word Box Midy"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__word_box_width
msgid "Word Box Width"
msgstr "Word Box Width"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__word_page
msgid "Word Page"
msgstr "Word Page"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__word_text
msgid "Word Text"
msgstr "Word Text"

#. module: account_invoice_extract
#. odoo-python
#: code:addons/account_invoice_extract/models/account_invoice.py:0
msgid "You cannot send a expense that is not in draft state!"
msgstr "Ви не можете надіслати витрати, які не в проекті!"
