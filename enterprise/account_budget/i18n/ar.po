# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_budget
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 09:13+0000\n"
"PO-Revision-Date: 2024-09-25 09:43+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_analytic_kanban
msgid "<i class=\"fa fa-clock-o me-1\" role=\"img\" aria-label=\"Period\" title=\"Period\"/>"
msgstr ""
"<i class=\"fa fa-clock-o me-1\" role=\"img\" aria-label=\"Period\" "
"title=\"الفترة \"/> "

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.budget_split_wizard_form_view
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_analytic_form
msgid "<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"to\" title=\"to\"/>"
msgstr "<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"to\" title=\"إلى \"/>"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.budget_report_view_search
msgid "Account"
msgstr "الحساب "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_line__achieved_amount
#: model:ir.model.fields,field_description:account_budget.field_budget_report__achieved
#: model:ir.model.fields.selection,name:account_budget.selection__budget_report__line_type__achieved
#: model_terms:ir.ui.view,arch_db:account_budget.budget_report_view_search
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_analytic_form
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_line_pivot
msgid "Achieved"
msgstr "مؤرشف "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_line__achieved_percentage
msgid "Achieved (%)"
msgstr "ما تم تحقيقه (%) "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__message_needaction
msgid "Action Needed"
msgstr "إجراء مطلوب"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__activity_ids
msgid "Activities"
msgstr "الأنشطة"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "زخرفة استثناء النشاط"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__activity_state
msgid "Activity State"
msgstr "حالة النشاط"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__activity_type_icon
msgid "Activity Type Icon"
msgstr "أيقونة نوع النشاط"

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_budget_line__committed_amount
msgid "Already Billed amount + Confirmed purchase orders."
msgstr "المبلغ الذي تمت فوترته بالفعل + أوامر الشراء المؤكدة. "

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_budget_line__achieved_amount
msgid "Amount Billed/Invoiced."
msgstr "المبلغ المفوتر "

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_budget_line__theoritical_amount
msgid ""
"Amount supposed to be Billed/Invoiced, formula = (Budget Amount / Budget "
"Days) x Budget Days Completed"
msgstr ""

#. module: account_budget
#: model:ir.model,name:account_budget.model_account_analytic_account
#: model:ir.model.fields,field_description:account_budget.field_budget_line__auto_account_id
#: model:ir.model.fields,field_description:account_budget.field_budget_report__auto_account_id
msgid "Analytic Account"
msgstr "الحساب التحليلي"

#. module: account_budget
#: model:ir.ui.menu,name:account_budget.menu_act_budget_analytic_view
msgid "Analytic Budget"
msgstr "الميزانية التحليلية "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_purchase_order_line__analytic_json
msgid "Analytic JSON"
msgstr "JSON التحليلي "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_split_wizard__analytical_plan_ids
msgid "Analytic Plans"
msgstr "الخطط التحليلية "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__message_attachment_count
msgid "Attachment Count"
msgstr "عدد المرفقات"

#. module: account_budget
#: model:ir.model.fields.selection,name:account_budget.selection__budget_analytic__budget_type__both
msgid "Both"
msgstr "كلاهما"

#. module: account_budget
#: model:ir.model,name:account_budget.model_budget_analytic
#: model:ir.model.fields,field_description:account_budget.field_budget_report__budget
#: model:ir.model.fields.selection,name:account_budget.selection__budget_report__line_type__budget
#: model_terms:ir.ui.view,arch_db:account_budget.budget_report_view_search
#: model_terms:ir.ui.view,arch_db:account_budget.purchase_order_form_account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_analytic_form
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_analytic_search
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_analytic_tree
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_line_graph
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_line_pivot
msgid "Budget"
msgstr "الميزانية "

#. module: account_budget
#. odoo-python
#: code:addons/account_budget/wizards/budget_split_wizard.py:0
msgid "Budget %s"
msgstr "الميزانية %s "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_line__budget_analytic_id
#: model:ir.model.fields,field_description:account_budget.field_budget_report__budget_analytic_id
msgid "Budget Analytic"
msgstr "تحليل الميزانيات "

#. module: account_budget
#: model:ir.model,name:account_budget.model_budget_line
#: model:ir.model.fields,field_description:account_budget.field_account_analytic_account__budget_line_ids
#: model:ir.model.fields,field_description:account_budget.field_budget_report__budget_line_id
msgid "Budget Line"
msgstr "بند الميزانية "

#. module: account_budget
#. odoo-python
#: code:addons/account_budget/models/budget_analytic.py:0
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__budget_line_ids
#: model:ir.model.fields,field_description:account_budget.field_purchase_order_line__budget_line_ids
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_analytic_form
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_analytic_tree
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_line_form
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_line_graph
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_line_pivot
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_line_search
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_line_tree
msgid "Budget Lines"
msgstr "بنود الميزانية "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__name
#: model:ir.model.fields,field_description:account_budget.field_budget_line__name
msgid "Budget Name"
msgstr "اسم الميزانية "

#. module: account_budget
#: model:ir.actions.act_window,name:account_budget.budget_report_action
#: model:ir.model,name:account_budget.model_budget_report
#: model:ir.ui.menu,name:account_budget.budget_report_menu
#: model_terms:ir.ui.view,arch_db:account_budget.budget_report_view_graph
#: model_terms:ir.ui.view,arch_db:account_budget.budget_report_view_pivot
#: model_terms:ir.ui.view,arch_db:account_budget.budget_report_view_search
#: model_terms:ir.ui.view,arch_db:account_budget.budget_report_view_tree
msgid "Budget Report"
msgstr "تقرير الميزانية "

#. module: account_budget
#: model:ir.model,name:account_budget.model_budget_split_wizard
msgid "Budget Split Wizard"
msgstr "معالج تقسيم الميزانية "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_line__budget_analytic_state
msgid "Budget State"
msgstr "حالة الميزانية "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__budget_type
msgid "Budget Type"
msgstr "نوع الميزانية "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_line__budget_amount
msgid "Budgeted"
msgstr ""

#. module: account_budget
#. odoo-python
#: code:addons/account_budget/wizards/budget_split_wizard.py:0
#: model:ir.actions.act_window,name:account_budget.act_budget_analytic_view
#: model_terms:ir.ui.view,arch_db:account_budget.view_account_analytic_account_form_inherit_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_line_search
msgid "Budgets"
msgstr "الميزانيات "

#. module: account_budget
#: model:ir.actions.act_window,name:account_budget.act_budget_lines_view
msgid "Budgets Analysis"
msgstr "تحليل الميزانيات "

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.budget_split_wizard_form_view
msgid "Cancel"
msgstr "إلغاء "

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_analytic_form
msgid "Cancel Budget"
msgstr "إلغاء الميزانية "

#. module: account_budget
#: model:ir.model.fields.selection,name:account_budget.selection__budget_analytic__state__canceled
msgid "Canceled"
msgstr "تم الإلغاء "

#. module: account_budget
#: model_terms:ir.actions.act_window,help:account_budget.act_budget_analytic_view
msgid "Click to create a new budget."
msgstr "اضغط لإنشاء ميزانية جديدة. "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_line__committed_amount
#: model:ir.model.fields,field_description:account_budget.field_budget_report__committed
#: model:ir.model.fields.selection,name:account_budget.selection__budget_report__line_type__committed
#: model_terms:ir.ui.view,arch_db:account_budget.budget_report_view_search
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_analytic_form
msgid "Committed"
msgstr "ملتزم "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_line__committed_percentage
msgid "Committed (%)"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__company_id
#: model:ir.model.fields,field_description:account_budget.field_budget_line__company_id
#: model:ir.model.fields,field_description:account_budget.field_budget_report__company_id
msgid "Company"
msgstr "الشركة "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__create_uid
#: model:ir.model.fields,field_description:account_budget.field_budget_line__create_uid
#: model:ir.model.fields,field_description:account_budget.field_budget_split_wizard__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__create_date
#: model:ir.model.fields,field_description:account_budget.field_budget_line__create_date
#: model:ir.model.fields,field_description:account_budget.field_budget_split_wizard__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_line__currency_id
msgid "Currency"
msgstr "العملة"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_report__date
#: model_terms:ir.ui.view,arch_db:account_budget.budget_report_view_search
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_line_search
msgid "Date"
msgstr "التاريخ"

#. module: account_budget
#. odoo-python
#: code:addons/account_budget/models/budget_analytic.py:0
msgid "Deletion is only allowed in the Draft and Canceled stages."
msgstr "يمكنك الحذف فقط إذا كانت في حالة المسودة أو ملغية "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_report__description
msgid "Description"
msgstr "الوصف"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_analytic_form
msgid "Details..."
msgstr "التقاصيل... "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__display_name
#: model:ir.model.fields,field_description:account_budget.field_budget_line__display_name
#: model:ir.model.fields,field_description:account_budget.field_budget_report__display_name
#: model:ir.model.fields,field_description:account_budget.field_budget_split_wizard__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_report__res_id
msgid "Document"
msgstr "المستند "

#. module: account_budget
#: model:ir.model.fields.selection,name:account_budget.selection__budget_analytic__state__done
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_analytic_form
msgid "Done"
msgstr "منتهي "

#. module: account_budget
#: model:ir.model.fields.selection,name:account_budget.selection__budget_analytic__state__draft
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_analytic_search
msgid "Draft"
msgstr "مسودة"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_analytic_search
msgid "Draft Budgets"
msgstr "ميزانية بحالة المسودة "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__date_to
#: model:ir.model.fields,field_description:account_budget.field_budget_line__date_to
#: model:ir.model.fields,field_description:account_budget.field_budget_split_wizard__date_to
msgid "End Date"
msgstr "تاريخ الانتهاء"

#. module: account_budget
#: model:ir.model.fields.selection,name:account_budget.selection__budget_analytic__budget_type__expense
msgid "Expense"
msgstr "النفقة "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__message_follower_ids
msgid "Followers"
msgstr "المتابعين"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__message_partner_ids
msgid "Followers (Partners)"
msgstr "المتابعين (الشركاء) "

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_budget_analytic__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "أيقونة من Font awesome مثال: fa-tasks "

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.budget_split_wizard_form_view
msgid "From"
msgstr "من"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_analytic_search
msgid "Future Activities"
msgstr "الأنشطة المستقبلية"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_analytic_tree
msgid "Generate"
msgstr "إنشاء"

#. module: account_budget
#: model:ir.actions.act_window,name:account_budget.budget_split_wizard_action
#: model_terms:ir.ui.view,arch_db:account_budget.budget_split_wizard_form_view
msgid "Generate Budget"
msgstr "إنشاء ميزانية "

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.budget_report_view_search
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_line_search
msgid "Group By"
msgstr "تجميع حسب"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__has_message
msgid "Has Message"
msgstr "يحتوي على رسالة "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__id
#: model:ir.model.fields,field_description:account_budget.field_budget_line__id
#: model:ir.model.fields,field_description:account_budget.field_budget_report__id
#: model:ir.model.fields,field_description:account_budget.field_budget_split_wizard__id
msgid "ID"
msgstr "المُعرف"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__activity_exception_icon
msgid "Icon"
msgstr "الأيقونة"

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_budget_analytic__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "الأيقونة للإشارة إلى النشاط المستثنى. "

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_budget_analytic__message_needaction
msgid "If checked, new messages require your attention."
msgstr "إذا كان محددًا، فهناك رسائل جديدة عليك رؤيتها. "

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_budget_analytic__message_has_error
#: model:ir.model.fields,help:account_budget.field_budget_analytic__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "إذا كان محددًا، فقد حدث خطأ في تسليم بعض الرسائل."

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.budget_report_view_search
msgid "In a Budget"
msgstr "في ميزانية "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_line__is_above_budget
#: model:ir.model.fields,field_description:account_budget.field_purchase_order__is_above_budget
#: model:ir.model.fields,field_description:account_budget.field_purchase_order_line__is_above_budget
msgid "Is Above Budget"
msgstr "يتخطى الميزانية "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_purchase_order__is_analytic
msgid "Is Analytic"
msgstr "تحليلي "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__message_is_follower
msgid "Is Follower"
msgstr "متابع"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__write_uid
#: model:ir.model.fields,field_description:account_budget.field_budget_line__write_uid
#: model:ir.model.fields,field_description:account_budget.field_budget_split_wizard__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__write_date
#: model:ir.model.fields,field_description:account_budget.field_budget_line__write_date
#: model:ir.model.fields,field_description:account_budget.field_budget_split_wizard__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_analytic_search
msgid "Late Activities"
msgstr "الأنشطة المتأخرة"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__message_has_error
msgid "Message Delivery error"
msgstr "خطأ في تسليم الرسائل"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__message_ids
msgid "Messages"
msgstr "الرسائل"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_report__res_model
msgid "Model"
msgstr "النموذج "

#. module: account_budget
#: model:ir.model.fields.selection,name:account_budget.selection__budget_split_wizard__period__month
msgid "Month"
msgstr "الشهر"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "الموعد النهائي لنشاطاتي "

#. module: account_budget
#. odoo-python
#: code:addons/account_budget/models/budget_analytic.py:0
msgid "New revision"
msgstr "مراجعة جديدة "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "الفعالية التالية في تقويم الأنشطة "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "الموعد النهائي للنشاط التالي"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__activity_summary
msgid "Next Activity Summary"
msgstr "ملخص النشاط التالي"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__activity_type_id
msgid "Next Activity Type"
msgstr "نوع النشاط التالي"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_line_search
msgid "Not Cancelled"
msgstr "غير ملغي "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__message_needaction_counter
msgid "Number of Actions"
msgstr "عدد الإجراءات"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__message_has_error_counter
msgid "Number of errors"
msgstr "عدد الأخطاء "

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_budget_analytic__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "عدد الرسائل التي تتطلب اتخاذ إجراء"

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_budget_analytic__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "عدد الرسائل الحادث بها خطأ في التسليم"

#. module: account_budget
#: model:ir.model.fields.selection,name:account_budget.selection__budget_analytic__state__confirmed
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_analytic_form
msgid "Open"
msgstr "فتح "

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.budget_report_view_search
msgid "Open Budget"
msgstr "ميزانية مفتوحة "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_split_wizard__period
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_analytic_form
msgid "Period"
msgstr "الفترة"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_line_graph
msgid "Practical amount"
msgstr "المبلغ الفعلي"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_line__account_id
#: model:ir.model.fields,field_description:account_budget.field_budget_report__account_id
msgid "Project Account"
msgstr "حساب المشروع "

#. module: account_budget
#: model:ir.model,name:account_budget.model_purchase_order
msgid "Purchase Order"
msgstr "أمر شراء "

#. module: account_budget
#: model:ir.model,name:account_budget.model_purchase_order_line
msgid "Purchase Order Line"
msgstr "بند أمر الشراء"

#. module: account_budget
#: model:ir.model.fields.selection,name:account_budget.selection__budget_split_wizard__period__quarter
msgid "Quarter"
msgstr "ربع السنة"

#. module: account_budget
#. odoo-python
#: code:addons/account_budget/models/budget_analytic.py:0
msgid "REV %s"
msgstr "REV %s"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__rating_ids
msgid "Ratings"
msgstr "التقييمات "

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_analytic_form
msgid "Reset to Draft"
msgstr "إعادة التعيين كمسودة "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__user_id
msgid "Responsible"
msgstr "المسؤول "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__activity_user_id
msgid "Responsible User"
msgstr "المستخدم المسؤول"

#. module: account_budget
#: model:ir.model.fields.selection,name:account_budget.selection__budget_analytic__budget_type__revenue
msgid "Revenue"
msgstr "الإيرادات "

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_analytic_form
msgid "Revise"
msgstr "مراجعة "

#. module: account_budget
#: model:ir.model.fields.selection,name:account_budget.selection__budget_analytic__state__revised
msgid "Revised"
msgstr "تمت المراجعة "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__parent_id
msgid "Revision Of"
msgstr "مراجعة "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__children_ids
msgid "Revisions"
msgstr "مراجعات "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__message_has_sms_error
msgid "SMS Delivery error"
msgstr "خطأ في تسليم الرسائل النصية القصيرة "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_line__sequence
msgid "Sequence"
msgstr "تسلسل "

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_analytic_search
msgid "Show all records which has next action date is before today"
msgstr ""
"عرض كافة السجلات التي يسبق تاريخ الإجراء التالي فيها تاريخ اليوم الجاري "

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.budget_split_wizard_form_view
msgid "Split"
msgstr "تقسيم"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__date_from
#: model:ir.model.fields,field_description:account_budget.field_budget_line__date_from
#: model:ir.model.fields,field_description:account_budget.field_budget_split_wizard__date_from
msgid "Start Date"
msgstr "تاريخ البدء "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__state
msgid "Status"
msgstr "الحالة"

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_budget_analytic__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"الأنشطة المعتمدة على الحالة\n"
"المتأخرة: تاريخ الاستحقاق مر\n"
"اليوم: تاريخ النشاط هو اليوم\n"
"المخطط: الأنشطة المستقبلية."

#. module: account_budget
#. odoo-python
#: code:addons/account_budget/models/budget_line.py:0
msgid "The 'End Date' must be greater than or equal to 'Start Date'."
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_line__theoritical_amount
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_analytic_form
msgid "Theoretical"
msgstr "نظري "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_line__theoritical_percentage
msgid "Theoretical (%)"
msgstr "نظري (%) "

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_analytic_search
msgid "Today Activities"
msgstr "أنشطة اليوم "

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.budget_report_view_tree
msgid "Total Achieved"
msgstr "إجمالي ما تمت أرشفته "

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.budget_report_view_tree
msgid "Total Budget"
msgstr "إجمالي الميزانية "

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.budget_report_view_tree
msgid "Total Committed"
msgstr "إجمالي ما تم الإلتزام به "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_report__line_type
#: model_terms:ir.ui.view,arch_db:account_budget.budget_report_view_search
msgid "Type"
msgstr "النوع"

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_budget_analytic__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "نوع النشاط المستثنى في السجل. "

#. module: account_budget
#: model_terms:ir.actions.act_window,help:account_budget.act_budget_analytic_view
msgid "Use budgets to compare actual with expected revenues and costs"
msgstr ""
"استخدم الميزانيات لمقارنة الإيرادات الفعلية مع الإيرادات المتوقعة والتكاليف "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_report__user_id
#: model_terms:ir.ui.view,arch_db:account_budget.budget_report_view_search
msgid "User"
msgstr "المستخدم"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__website_message_ids
msgid "Website Messages"
msgstr "رسائل الموقع الإلكتروني "

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_budget_analytic__website_message_ids
msgid "Website communication history"
msgstr "سجل تواصل الموقع الإلكتروني "

#. module: account_budget
#: model:ir.model.fields.selection,name:account_budget.selection__budget_split_wizard__period__year
msgid "Year"
msgstr "السنة"

#. module: account_budget
#. odoo-python
#: code:addons/account_budget/models/budget_analytic.py:0
msgid "You cannot create recursive revision of budget."
msgstr "لا يمكنك إنشاء مراجعات متداخلة للميزانية "

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_analytic_form
msgid "e.g. Budget 2023"
msgstr "مثال: ميزانية 2023 "
