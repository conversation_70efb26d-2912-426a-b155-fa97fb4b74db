# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_booth
# 
# Translators:
# <PERSON><PERSON>, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration_details
msgid ""
"<i class=\"fa fa-exclamation-triangle me-2\" role=\"img\" aria-label=\"Error\" title=\"Error\"/>\n"
"                    <span class=\"o_wbooth_registration_error_message\"/>"
msgstr ""
"<i class=\"fa fa-exclamation-triangle me-2\" role=\"img\" aria-label=\"에러\" title=\"에러\"/>\n"
"                    <span class=\"o_wbooth_registration_error_message\"/>"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid ""
"<i class=\"fa fa-exclamation-triangle\"/>\n"
"                                        <span>Sorry, several booths are now sold out. Please change your choices before validating again.</span>"
msgstr ""
"<i class=\"fa fa-exclamation-triangle\"/>\n"
"                                        <span>죄송합니다. 현재 몇몇 부스가 매진되었습니다. 선택 사항을 변경 후 다시 승인해 주십시오.</span>"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid "<i class=\"fa fa-map-o me-1\"/>View Plan"
msgstr "<i class=\"fa fa-map-o me-1\"/>플랜 보기"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
msgid "<span class=\"fa fa-gear me-1\"/> Configure Booths"
msgstr "<span class=\"fa fa-gear me-1\"/> 부스 설정"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid "<span class=\"text-nowrap\">Sold Out</span>"
msgstr "<span class=\"text-nowrap\">매진</span>"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid "<span>Book my Booth<small>(s)</small></span>"
msgstr "<span>내 부스 예약<small></small></span>"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration_details
msgid "<span>Book my Booths</span>"
msgstr "<span>부스 예약하기</span>"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_order_progress
msgid ""
"<span>Booth Selection</span><span class=\"fa fa-angle-right d-inline-block "
"align-middle mx-2 mx-lg-3 opacity-75\"/>"
msgstr ""
"<span>부스 선택</span><span class=\"fa fa-angle-right d-inline-block align-"
"middle mx-2 mx-lg-3 opacity-75\"/>"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_order_progress
msgid "<span>Confirmed</span>"
msgstr "<span>확인함</span>"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration_details
msgid ""
"<span>Email</span>\n"
"                            <span> *</span>"
msgstr ""
"<span>이메일</span>\n"
"                            <span> *</span>"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration_details
msgid ""
"<span>Name</span>\n"
"                            <span> *</span>"
msgstr ""
"<span>이름</span>\n"
"                            <span> *</span>"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_type_view_form
msgid "Booth Menu Item"
msgstr "부스 메뉴 항목"

#. module: website_event_booth
#: model:ir.model.fields,field_description:website_event_booth.field_event_event__booth_menu
msgid "Booth Register"
msgstr "부스 등록"

#. module: website_event_booth
#. odoo-javascript
#: code:addons/website_event_booth/static/src/xml/event_booth_registration_templates.xml:0
msgid "Booth Registration completed!"
msgstr "부스 등록을 완료했습니다!"

#. module: website_event_booth
#. odoo-javascript
#: code:addons/website_event_booth/static/src/js/booth_register.js:0
msgid "Booth registration failed."
msgstr "부스 등록에 실패했습니다."

#. module: website_event_booth
#: model:ir.model.fields,field_description:website_event_booth.field_event_type__booth_menu
msgid "Booths on Website"
msgstr "웹사이트 부스"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
msgid "Check our"
msgstr "우리의"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid "Choose your type of booth"
msgstr "부스 유형 선택"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration_details
msgid "Contact Details"
msgstr "연락처 상세 내용"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_order_progress
msgid ""
"Contact Details<span class=\"fa fa-angle-right d-inline-block align-middle "
"mx-2 mx-lg-3 opacity-75\"/>"
msgstr ""
"연락처 정보<span class=\"fa fa-angle-right d-inline-block align-middle mx-2 mx-"
"lg-3 opacity-75\"/>"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid "Contact Us"
msgstr "문의하기"

#. module: website_event_booth
#: model:ir.model,name:website_event_booth.model_event_event
msgid "Event"
msgstr "행사"

#. module: website_event_booth
#: model:ir.model.fields.selection,name:website_event_booth.selection__website_event_menu__menu_type__booth
msgid "Event Booth Menus"
msgstr "행사 부스 메뉴"

#. module: website_event_booth
#: model:ir.model.fields,field_description:website_event_booth.field_event_event__booth_menu_ids
msgid "Event Booths Menus"
msgstr "행사 부스 메뉴"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
msgid "Event Finished"
msgstr "행사 완료됨"

#. module: website_event_booth
#: model:ir.model,name:website_event_booth.model_event_type
msgid "Event Template"
msgstr "행사 서식"

#. module: website_event_booth
#: model:ir.model.fields,field_description:website_event_booth.field_event_event__exhibition_map
msgid "Exhibition Map"
msgstr "전시회 지도"

#. module: website_event_booth
#. odoo-python
#: code:addons/website_event_booth/models/event_event.py:0
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration_details
msgid "Get A Booth"
msgstr "부스 예약하기"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration_details
msgid "Go back"
msgstr "돌아가기"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
msgid "It's no longer possible to book a booth."
msgstr "지금은 부스를 예약할 수 없습니다."

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
msgid "Link to list of future events"
msgstr "향후 행사 목록 링크"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
msgid "List of Future Events"
msgstr "향후 행사 목록 "

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid "Location"
msgstr "위치"

#. module: website_event_booth
#: model:ir.model.fields,field_description:website_event_booth.field_website_event_menu__menu_type
msgid "Menu Type"
msgstr "메뉴 유형"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration_details
msgid "Phone"
msgstr "전화번호"

#. module: website_event_booth
#. odoo-javascript
#: code:addons/website_event_booth/static/src/js/booth_register.js:0
msgid "Please fill out the form correctly."
msgstr "양식을 정확하게 작성해주세요."

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
msgid "Registration Not Open."
msgstr "등록이 시작되지 않았습니다."

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid "Sorry, all the booths are sold out."
msgstr "죄송합니다, 모든 부스가 매진되었습니다."

#. module: website_event_booth
#. odoo-javascript
#: code:addons/website_event_booth/static/src/js/booth_register.js:0
msgid "The booth category doesn't exist."
msgstr "부스 카테고리가 존재하지 않습니다."

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
msgid "This event is not open to exhibitors registration at this time."
msgstr "이 행사에는 현재 전시업체 등록이 불가합니다."

#. module: website_event_booth
#: model:ir.model,name:website_event_booth.model_website_event_menu
msgid "Website Event Menu"
msgstr "웹사이트 행사 메뉴"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid "if you have any question."
msgstr "문의 사항이 있으시면"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
msgid "list of future events"
msgstr "향후 예정된 행사 목록"
