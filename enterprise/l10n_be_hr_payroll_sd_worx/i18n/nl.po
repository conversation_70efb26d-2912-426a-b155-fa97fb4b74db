# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_be_hr_payroll_sd_worx
#
# Translators:
# <PERSON><PERSON><PERSON>, 2024
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-31 12:52+0000\n"
"PO-Revision-Date: 2024-01-30 08:20+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"Language: nl\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: l10n_be_hr_payroll_sd_worx
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll_sd_worx.res_config_settings_view_form
msgid "Allow to export Working Entries to your Social Secretariat"
msgstr "Werkboekingen exporteren naar je sociaal secretariaat toestaan"

#. module: l10n_be_hr_payroll_sd_worx
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll_sd_worx.selection__l10n_be_export_sdworx_leaves_wizard__reference_month__4
msgid "April"
msgstr "April"

#. module: l10n_be_hr_payroll_sd_worx
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll_sd_worx.selection__l10n_be_export_sdworx_leaves_wizard__reference_month__8
msgid "August"
msgstr "Augustus"

#. module: l10n_be_hr_payroll_sd_worx
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll_sd_worx.l10n_be_export_sdworx_leaves_wizard_view_form
msgid "CANCEL"
msgstr "ANNULEREN"

#. module: l10n_be_hr_payroll_sd_worx
#: model:ir.model,name:l10n_be_hr_payroll_sd_worx.model_res_company
msgid "Companies"
msgstr "Bedrijven"

#. module: l10n_be_hr_payroll_sd_worx
#: model:ir.model,name:l10n_be_hr_payroll_sd_worx.model_res_config_settings
msgid "Config Settings"
msgstr "Config-instellingen"

#. module: l10n_be_hr_payroll_sd_worx
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_sd_worx.field_l10n_be_export_sdworx_leaves_wizard__create_uid
msgid "Created by"
msgstr "Aangemaakt door"

#. module: l10n_be_hr_payroll_sd_worx
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_sd_worx.field_l10n_be_export_sdworx_leaves_wizard__create_date
msgid "Created on"
msgstr "Aangemaakt op"

#. module: l10n_be_hr_payroll_sd_worx
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll_sd_worx.selection__l10n_be_export_sdworx_leaves_wizard__reference_month__12
msgid "December"
msgstr "December"

#. module: l10n_be_hr_payroll_sd_worx
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_sd_worx.field_l10n_be_export_sdworx_leaves_wizard__display_name
msgid "Display Name"
msgstr "Schermnaam"

#. module: l10n_be_hr_payroll_sd_worx
#: model:ir.model,name:l10n_be_hr_payroll_sd_worx.model_hr_employee
msgid "Employee"
msgstr "Werknemer"

#. module: l10n_be_hr_payroll_sd_worx
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll_sd_worx.l10n_be_export_sdworx_leaves_wizard_view_form
msgid "Export Employee Leaves for SD Worx"
msgstr "Verlof werknemer exporteren naar SD Worx"

#. module: l10n_be_hr_payroll_sd_worx
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_sd_worx.field_l10n_be_export_sdworx_leaves_wizard__export_file
msgid "Export File"
msgstr "Exportbestand"

#. module: l10n_be_hr_payroll_sd_worx
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_sd_worx.field_l10n_be_export_sdworx_leaves_wizard__export_filename
msgid "Export Filename"
msgstr "Naam exportbestand"

#. module: l10n_be_hr_payroll_sd_worx
#: model:ir.model,name:l10n_be_hr_payroll_sd_worx.model_l10n_be_export_sdworx_leaves_wizard
msgid "Export Leaves to SDWorx"
msgstr "Verlof exporteren naar SDWorx"

#. module: l10n_be_hr_payroll_sd_worx
#. odoo-python
#: code:addons/l10n_be_hr_payroll_sd_worx/wizard/l10n_be_export_sdworx_leaves_wizard.py:0
#: model:ir.actions.act_window,name:l10n_be_hr_payroll_sd_worx.l10n_be_export_sdworx_leaves_wizard_action
#: model:ir.ui.menu,name:l10n_be_hr_payroll_sd_worx.menu_l10n_be_export_sdworx_leaves_wizard
msgid "Export Work Entries to SDWorx"
msgstr "Werkboekingen exporteren naar SDWorx"

#. module: l10n_be_hr_payroll_sd_worx
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll_sd_worx.selection__l10n_be_export_sdworx_leaves_wizard__reference_month__2
msgid "February"
msgstr "Februari"

#. module: l10n_be_hr_payroll_sd_worx
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll_sd_worx.l10n_be_export_sdworx_leaves_wizard_view_form
msgid "Generate Export File"
msgstr "Exportbestand genereren"

#. module: l10n_be_hr_payroll_sd_worx
#: model:ir.model,name:l10n_be_hr_payroll_sd_worx.model_hr_work_entry_type
msgid "HR Work Entry Type"
msgstr "Type HR-werkboeking"

#. module: l10n_be_hr_payroll_sd_worx
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_sd_worx.field_l10n_be_export_sdworx_leaves_wizard__id
msgid "ID"
msgstr "ID"

#. module: l10n_be_hr_payroll_sd_worx
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll_sd_worx.selection__l10n_be_export_sdworx_leaves_wizard__reference_month__1
msgid "January"
msgstr "Januari"

#. module: l10n_be_hr_payroll_sd_worx
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll_sd_worx.selection__l10n_be_export_sdworx_leaves_wizard__reference_month__7
msgid "July"
msgstr "Juli"

#. module: l10n_be_hr_payroll_sd_worx
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll_sd_worx.selection__l10n_be_export_sdworx_leaves_wizard__reference_month__6
msgid "June"
msgstr "Juni"

#. module: l10n_be_hr_payroll_sd_worx
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_sd_worx.field_l10n_be_export_sdworx_leaves_wizard__write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: l10n_be_hr_payroll_sd_worx
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_sd_worx.field_l10n_be_export_sdworx_leaves_wizard__write_date
msgid "Last Updated on"
msgstr "Laatst bijgewerkt op"

#. module: l10n_be_hr_payroll_sd_worx
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_sd_worx.field_l10n_be_export_sdworx_leaves_wizard__leave_ids
msgid "Leaves"
msgstr "Verlof"

#. module: l10n_be_hr_payroll_sd_worx
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll_sd_worx.selection__l10n_be_export_sdworx_leaves_wizard__reference_month__3
msgid "March"
msgstr "Maart"

#. module: l10n_be_hr_payroll_sd_worx
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll_sd_worx.selection__l10n_be_export_sdworx_leaves_wizard__reference_month__5
msgid "May"
msgstr "Mei"

#. module: l10n_be_hr_payroll_sd_worx
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll_sd_worx.selection__l10n_be_export_sdworx_leaves_wizard__reference_month__11
msgid "November"
msgstr "November"

#. module: l10n_be_hr_payroll_sd_worx
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll_sd_worx.selection__l10n_be_export_sdworx_leaves_wizard__reference_month__10
msgid "October"
msgstr "Oktober"

#. module: l10n_be_hr_payroll_sd_worx
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_sd_worx.field_l10n_be_export_sdworx_leaves_wizard__reference_month
msgid "Reference Month"
msgstr "Referentiemaand"

#. module: l10n_be_hr_payroll_sd_worx
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_sd_worx.field_l10n_be_export_sdworx_leaves_wizard__reference_year
msgid "Reference Year"
msgstr "Referentiejaar"

#. module: l10n_be_hr_payroll_sd_worx
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll_sd_worx.hr_employee_form_l10n_be_hr_payroll_sd_worx
msgid "SD Worx"
msgstr "SD Worx"

#. module: l10n_be_hr_payroll_sd_worx
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll_sd_worx.res_config_settings_view_form
msgid "SDWorx"
msgstr "SDWorx"

#. module: l10n_be_hr_payroll_sd_worx
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_sd_worx.field_hr_employee__sdworx_code
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_sd_worx.field_hr_work_entry_type__sdworx_code
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_sd_worx.field_res_company__sdworx_code
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_sd_worx.field_res_config_settings__sdworx_code
msgid "SDWorx code"
msgstr "SDWorx-code"

#. module: l10n_be_hr_payroll_sd_worx
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll_sd_worx.selection__l10n_be_export_sdworx_leaves_wizard__reference_month__9
msgid "September"
msgstr "September"

#. module: l10n_be_hr_payroll_sd_worx
#. odoo-python
#: code:addons/l10n_be_hr_payroll_sd_worx/models/hr_employee.py:0
msgid "The SDWorx code should have 7 characters or should be left empty!"
msgstr "De SDWorx-code moet uit 7 tekens bestaan of moet leeg blijven!"

#. module: l10n_be_hr_payroll_sd_worx
#. odoo-python
#: code:addons/l10n_be_hr_payroll_sd_worx/models/hr_work_entry_type.py:0
msgid "The code should have 4 characters!"
msgstr "De code moet uit 4 karakters bestaan!"

#. module: l10n_be_hr_payroll_sd_worx
#. odoo-python
#: code:addons/l10n_be_hr_payroll_sd_worx/models/res_company.py:0
msgid "The code should have 7 characters!"
msgstr "De code moet uit 7 karakters bestaan!"

#. module: l10n_be_hr_payroll_sd_worx
#. odoo-python
#: code:addons/l10n_be_hr_payroll_sd_worx/wizard/l10n_be_export_sdworx_leaves_wizard.py:0
msgid "There is no SDWorx code defined for the following employees:\n"
msgstr "Er is geen SDWorx-code bepaald voor de volgende werknemers:\n"

#. module: l10n_be_hr_payroll_sd_worx
#. odoo-python
#: code:addons/l10n_be_hr_payroll_sd_worx/wizard/l10n_be_export_sdworx_leaves_wizard.py:0
msgid "There is no SDWorx code defined for the following work entry types:\n"
msgstr "Er is geen SDWorx-code bepaald voor de volgende types werkboekingen:\n"

#. module: l10n_be_hr_payroll_sd_worx
#. odoo-python
#: code:addons/l10n_be_hr_payroll_sd_worx/wizard/l10n_be_export_sdworx_leaves_wizard.py:0
msgid ""
"There is no SDWorx code defined on the company. Please configure it on the "
"Payroll Settings"
msgstr ""
"Er is geen SDWorx-code bepaald op het bedrijf. Configureer deze in de "
"Loonadministratie-instellingen"

#. module: l10n_be_hr_payroll_sd_worx
#. odoo-python
#: code:addons/l10n_be_hr_payroll_sd_worx/wizard/l10n_be_export_sdworx_leaves_wizard.py:0
msgid "You must be logged in a Belgian company to use this feature"
msgstr ""
"Je moet ingelogd zijn in een Belgisch bedrijf om deze functie te kunnen "
"gebruiken"
