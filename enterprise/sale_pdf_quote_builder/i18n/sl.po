# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_pdf_quote_builder
# 
# Translators:
# <PERSON><PERSON>, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:40+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Slovenian (https://app.transifex.com/odoo/teams/41243/sl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sl\n"
"Plural-Forms: nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n%100==4 ? 2 : 3);\n"

#. module: sale_pdf_quote_builder
#: model:ir.actions.report,print_report_name:sale_pdf_quote_builder.action_report_saleorder_raw
msgid ""
"(object.state in ('draft', 'sent') and 'Quotation - %s' % (object.name)) or "
"'Order - %s' % (object.name)"
msgstr ""
"(object.state in ('draft', 'sent') and 'Ponudba - %s' % (object.name)) or "
"'Naročilo - %s' % (object.name)"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_kanban
msgid "<span class=\"pe-2\">Templates:</span>"
msgstr ""

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_kanban
msgid "<span>Document type:</span>"
msgstr ""

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/sale_pdf_form_field.py:0
msgid ""
"A form field set as used in product documents can't be linked to a quotation"
" document."
msgstr ""

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/sale_pdf_form_field.py:0
msgid ""
"A form field set as used in quotation documents can't be linked to a product"
" document."
msgstr ""

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__access_token
msgid "Access Token"
msgstr "Žeton vabila"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__active
msgid "Active"
msgstr "Aktivno"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_search_view
msgid "All"
msgstr "Vse"

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/controllers/quotation_document.py:0
msgid "All files uploaded"
msgstr "Vse datoteke so bile naložene"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,help:sale_pdf_quote_builder.field_product_document__attached_on_sale
msgid ""
"Allows you to share the document with your customers within a sale.\n"
"Leave it empty if you don't want to share this document with sales customer.\n"
"On quote: the document will be sent to and accessible by customers at any time.\n"
"e.g. this option can be useful to share Product description files.\n"
"On order confirmation: the document will be sent to and accessible by customers.\n"
"e.g. this option can be useful to share User Manual or digital content bought on ecommerce. \n"
"Inside quote: The document will be included in the pdf of the quotation and sale order between the header pages and the quote table. "
msgstr ""

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_kanban
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_search_view
msgid "Archived"
msgstr "Arhivirano"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_form
msgid "Attached To"
msgstr "Pripeto k"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__local_url
msgid "Attachment URL"
msgstr "URL priloge"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_order__available_product_document_ids
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_order_line__available_product_document_ids
msgid "Available Product Documents"
msgstr ""

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__checksum
msgid "Checksum/SHA1"
msgstr "Checksum/SHA1"

#. module: sale_pdf_quote_builder
#. odoo-javascript
#: code:addons/sale_pdf_quote_builder/static/src/js/custom_content_kanban_like_widget/custom_field_card/custom_field_card.js:0
msgid "Click to write content for the PDF quote..."
msgstr ""

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__company_id
msgid "Company"
msgstr "Podjetje"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.product_document_form
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_form
msgid "Configure dynamic fields"
msgstr ""

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.product_document_form
msgid "Configure the path to fill the form fields."
msgstr ""

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__create_uid
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_pdf_form_field__create_uid
msgid "Created by"
msgstr "Ustvaril"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__create_date
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_pdf_form_field__create_date
msgid "Created on"
msgstr "Ustvarjeno"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_form
msgid "Creation"
msgstr "Ustvarjeno"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_search
msgid "Customizable"
msgstr ""

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_order__customizable_pdf_form_fields
msgid "Customizable PDF Form Fields"
msgstr ""

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__db_datas
msgid "Database Data"
msgstr "Podatki podatkovne zbirke"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_kanban
msgid "Delete"
msgstr "Izbriši"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__description
msgid "Description"
msgstr "Opis"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__display_name
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_pdf_form_field__display_name
msgid "Display Name"
msgstr "Prikazani naziv"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__document_type
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_pdf_form_field__document_type
msgid "Document Type"
msgstr "Vrsta dokumenta"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_search_view
msgid "Document type"
msgstr ""

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_kanban
msgid "Download"
msgstr "Prenesi"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.sale_order_template_form
msgid "Download examples"
msgstr ""

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_kanban
msgid "Edit"
msgstr "Uredi"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__datas
msgid "File Content (base64)"
msgstr ""

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__raw
msgid "File Content (raw)"
msgstr ""

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__file_size
msgid "File Size"
msgstr "Velikost datoteke"

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/sale_order.py:0
#: model:ir.model.fields.selection,name:sale_pdf_quote_builder.selection__quotation_document__document_type__footer
msgid "Footer"
msgstr "Noga"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_pdf_form_field__name
msgid "Form Field Name"
msgstr ""

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/product_document.py:0
#: code:addons/sale_pdf_quote_builder/models/quotation_document.py:0
msgid "Form Fields"
msgstr ""

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_product_document__form_field_ids
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__form_field_ids
msgid "Form Fields Included"
msgstr ""

#. module: sale_pdf_quote_builder
#: model:ir.model.constraint,message:sale_pdf_quote_builder.constraint_sale_pdf_form_field_unique_name_per_doc_type
msgid "Form field name must be unique for a given document type."
msgstr ""

#. module: sale_pdf_quote_builder
#: model:ir.model,name:sale_pdf_quote_builder.model_sale_pdf_form_field
msgid "Form fields of inside quotation documents."
msgstr ""

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_search_view
msgid "Group By"
msgstr "Združi po"

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/sale_order.py:0
#: model:ir.model.fields.selection,name:sale_pdf_quote_builder.selection__quotation_document__document_type__header
msgid "Header"
msgstr "Glava"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields.selection,name:sale_pdf_quote_builder.selection__sale_pdf_form_field__document_type__quotation_document
msgid "Header/Footer"
msgstr ""

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_order_template__quotation_document_ids
msgid "Headers and footers"
msgstr ""

#. module: sale_pdf_quote_builder
#: model:ir.actions.act_window,name:sale_pdf_quote_builder.quotation_document_action
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_order__quotation_document_ids
#: model:ir.ui.menu,name:sale_pdf_quote_builder.sale_menu_quotation_document_action
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.res_config_settings_view_form
msgid "Headers/Footers"
msgstr ""

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_form
msgid "History"
msgstr "Zgodovina"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__id
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_pdf_form_field__id
msgid "ID"
msgstr "ID"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,help:sale_pdf_quote_builder.field_quotation_document__active
msgid ""
"If unchecked, it will allow you to hide the header or footer without "
"removing it."
msgstr ""

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__image_height
msgid "Image Height"
msgstr ""

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__image_src
msgid "Image Src"
msgstr ""

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__image_width
msgid "Image Width"
msgstr ""

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__index_content
msgid "Indexed Content"
msgstr "Indeksirana vsebina"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields.selection,name:sale_pdf_quote_builder.selection__product_document__attached_on_sale__inside
msgid "Inside quote pdf"
msgstr ""

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/sale_pdf_form_field.py:0
msgid ""
"Invalid form field name %(field_name)s. A form field name in a header or a "
"footer can not start with \"sol_id_\"."
msgstr ""

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/sale_pdf_form_field.py:0
msgid ""
"Invalid form field name %(field_name)s. It should only contain "
"alphanumerics, hyphens or underscores."
msgstr ""

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/sale_pdf_form_field.py:0
msgid ""
"Invalid path %(path)s. It should only contain alphanumerics, hyphens, "
"underscores or points."
msgstr ""

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_order__is_pdf_quote_builder_available
msgid "Is Pdf Quote Builder Available"
msgstr ""

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__public
msgid "Is public document"
msgstr "Je javni dokument"

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/utils.py:0
msgid ""
"It seems that we're not able to process this pdf inside a quotation. It is "
"either encrypted, or encoded in a format we do not support."
msgstr ""

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__key
msgid "Key"
msgstr "Ključ"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__write_uid
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_pdf_form_field__write_uid
msgid "Last Updated by"
msgstr "Zadnji posodobil"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__write_date
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_pdf_form_field__write_date
msgid "Last Updated on"
msgstr "Zadnjič posodobljeno"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_form
msgid "Mark fields as safe to fill in the quote."
msgstr ""

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__mimetype
msgid "Mime Type"
msgstr "MIME tip"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__name
msgid "Name"
msgstr "Naziv"

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/ir_actions_report.py:0
msgid "No"
msgstr "Ne"

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/product_document.py:0
msgid "Only PDF documents can be attached inside a quote."
msgstr ""

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/quotation_document.py:0
msgid "Only PDF documents can be used as header or footer."
msgstr ""

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__original_id
msgid "Original (unoptimized, unresized) attachment"
msgstr ""

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_pdf_form_field__path
msgid "Path"
msgstr "Pot"

#. module: sale_pdf_quote_builder
#: model_terms:ir.actions.act_window,help:sale_pdf_quote_builder.quotation_document_action
msgid ""
"Personalize your quotes with catchy header and footer pages\n"
"                <br>\n"
"                to boost your sales."
msgstr ""

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/sale_pdf_form_field.py:0
msgid "Please use only relational fields until the last value of your path."
msgstr ""

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/sale_order.py:0
msgid "Product"
msgstr "Izdelek"

#. module: sale_pdf_quote_builder
#: model:ir.model,name:sale_pdf_quote_builder.model_product_document
#: model:ir.model.fields.selection,name:sale_pdf_quote_builder.selection__sale_pdf_form_field__document_type__product_document
msgid "Product Document"
msgstr ""

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_order_line__product_document_ids
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_pdf_form_field__product_document_ids
msgid "Product Documents"
msgstr ""

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.sale_order_template_form
msgid ""
"Provide header pages and footer pages to compose an attractive quotation with more information\n"
"                        about your company, your products and you services. <br/>\n"
"                        The pdf of your quotes will be built by putting together header pages, product descriptions,\n"
"                        details of the quote and then the footer pages. <br/>\n"
"                        If empty, it will use those define in the company settings. <br/>"
msgstr ""

#. module: sale_pdf_quote_builder
#: model:ir.actions.report,name:sale_pdf_quote_builder.action_report_saleorder_raw
msgid "Quotation / Order"
msgstr "Ponudba / Naročilo"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_search_view
msgid "Quotation Document"
msgstr ""

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_pdf_form_field__quotation_document_ids
msgid "Quotation Documents"
msgstr ""

#. module: sale_pdf_quote_builder
#: model:ir.model,name:sale_pdf_quote_builder.model_sale_order_template
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_search_view
msgid "Quotation Template"
msgstr "Predloga ponudbe"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__quotation_template_ids
msgid "Quotation Templates"
msgstr "Predloge ponudb"

#. module: sale_pdf_quote_builder
#: model:ir.model,name:sale_pdf_quote_builder.model_quotation_document
msgid "Quotation's Headers & Footers"
msgstr ""

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.sale_order_form_inherit_sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.sale_order_template_form
msgid "Quote Builder"
msgstr ""

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__ir_attachment_id
msgid "Related attachment"
msgstr ""

#. module: sale_pdf_quote_builder
#: model:ir.model,name:sale_pdf_quote_builder.model_ir_actions_report
msgid "Report Action"
msgstr "Ukrep poročila"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__res_field
msgid "Resource Field"
msgstr "Polje vira"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__res_id
msgid "Resource ID"
msgstr "ID vira"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__res_model
msgid "Resource Model"
msgstr "Izvorni model"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__res_name
msgid "Resource Name"
msgstr "Naziv vira"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_product_document__attached_on_sale
msgid "Sale : Visible at"
msgstr ""

#. module: sale_pdf_quote_builder
#: model:ir.actions.server,name:sale_pdf_quote_builder.cron_post_upgrade_assign_missing_form_fields_ir_actions_server
msgid "Sale Pdf Quote Builder: assign form fields to documents post upgrade"
msgstr ""

#. module: sale_pdf_quote_builder
#: model:ir.model,name:sale_pdf_quote_builder.model_sale_order
msgid "Sales Order"
msgstr "Prodajni nalog"

#. module: sale_pdf_quote_builder
#: model:ir.model,name:sale_pdf_quote_builder.model_sale_order_line
msgid "Sales Order Line"
msgstr "Postavka naročila"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__sequence
msgid "Sequence"
msgstr "Zaporedje"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__store_fname
msgid "Stored Filename"
msgstr "Naziv shranjene datoteke"

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/sale_pdf_form_field.py:0
msgid "The field %(field_name)s doesn't exist on model %(model_name)s"
msgstr ""

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,help:sale_pdf_quote_builder.field_sale_pdf_form_field__name
msgid "The form field name as written in the PDF."
msgstr ""

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,help:sale_pdf_quote_builder.field_sale_pdf_form_field__path
msgid ""
"The path to follow to dynamically fill the form field. \n"
"Leave empty to be able to customized it in the quotation form."
msgstr ""

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,help:sale_pdf_quote_builder.field_sale_order_line__product_document_ids
msgid ""
"The product documents for this order line that will be merged in the PDF "
"quote."
msgstr ""

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__theme_template_id
msgid "Theme Template"
msgstr "Predloga teme"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_search
msgid "This document"
msgstr ""

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__type
msgid "Type"
msgstr "Tip"

#. module: sale_pdf_quote_builder
#: model_terms:ir.actions.act_window,help:sale_pdf_quote_builder.quotation_document_action
msgid "Upload quotation headers and footers"
msgstr ""

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__url
msgid "Url"
msgstr "Url"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_form
msgid "Visible to all"
msgstr ""

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__voice_ids
msgid "Voice"
msgstr ""

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__website_id
msgid "Website"
msgstr "Spletna stran"

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/product_document.py:0
msgid "When attached inside a quote, the document must be a file, not a URL."
msgstr ""

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/ir_actions_report.py:0
msgid "Yes"
msgstr "Da"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,help:sale_pdf_quote_builder.field_quotation_document__type
msgid ""
"You can either upload a file from your computer or copy/paste an internet "
"link to your file."
msgstr ""
"Datoteko lahko naložite iz svojega računalnika ali pa kopirate/prilepite "
"internetno povezavo do datoteke."

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_form
msgid "on"
msgstr "na"
