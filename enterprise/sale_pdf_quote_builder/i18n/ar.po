# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_pdf_quote_builder
# 
# Translators:
# <PERSON>, 2024
# Wil Odoo, 2024
# <PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:40+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: sale_pdf_quote_builder
#: model:ir.actions.report,print_report_name:sale_pdf_quote_builder.action_report_saleorder_raw
msgid ""
"(object.state in ('draft', 'sent') and 'Quotation - %s' % (object.name)) or "
"'Order - %s' % (object.name)"
msgstr ""
"(object.state in ('draft', 'sent') and 'Quotation - %s' % (object.name)) or "
"'Order - %s' % (object.name)"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_kanban
msgid "<span class=\"pe-2\">Templates:</span>"
msgstr "<span class=\"pe-2\">القوالب:</span> "

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_kanban
msgid "<span>Document type:</span>"
msgstr "<span>نوع المستند:</span> "

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/sale_pdf_form_field.py:0
msgid ""
"A form field set as used in product documents can't be linked to a quotation"
" document."
msgstr ""

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/sale_pdf_form_field.py:0
msgid ""
"A form field set as used in quotation documents can't be linked to a product"
" document."
msgstr ""

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__access_token
msgid "Access Token"
msgstr "رمز الوصول "

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__active
msgid "Active"
msgstr "نشط"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_search_view
msgid "All"
msgstr "الكل"

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/controllers/quotation_document.py:0
msgid "All files uploaded"
msgstr "تم رفع كافة الملفات "

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,help:sale_pdf_quote_builder.field_product_document__attached_on_sale
msgid ""
"Allows you to share the document with your customers within a sale.\n"
"Leave it empty if you don't want to share this document with sales customer.\n"
"On quote: the document will be sent to and accessible by customers at any time.\n"
"e.g. this option can be useful to share Product description files.\n"
"On order confirmation: the document will be sent to and accessible by customers.\n"
"e.g. this option can be useful to share User Manual or digital content bought on ecommerce. \n"
"Inside quote: The document will be included in the pdf of the quotation and sale order between the header pages and the quote table. "
msgstr ""

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_kanban
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_search_view
msgid "Archived"
msgstr "مؤرشف"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_form
msgid "Attached To"
msgstr "مرفق بـ"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__local_url
msgid "Attachment URL"
msgstr "رابط URL للمرفق "

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_order__available_product_document_ids
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_order_line__available_product_document_ids
msgid "Available Product Documents"
msgstr "مستندات المنتجات المتوفرة "

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__checksum
msgid "Checksum/SHA1"
msgstr "Checksum/SHA1"

#. module: sale_pdf_quote_builder
#. odoo-javascript
#: code:addons/sale_pdf_quote_builder/static/src/js/custom_content_kanban_like_widget/custom_field_card/custom_field_card.js:0
msgid "Click to write content for the PDF quote..."
msgstr "انقر لكتابة محتوى لعرض السعر بصيغة PDF... "

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__company_id
msgid "Company"
msgstr "الشركة "

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.product_document_form
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_form
msgid "Configure dynamic fields"
msgstr "تهيئة الحقول الديناميكية "

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.product_document_form
msgid "Configure the path to fill the form fields."
msgstr "قم بتهيئة المسار لتعبئة حقول الاستمارة. "

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__create_uid
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_pdf_form_field__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__create_date
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_pdf_form_field__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_form
msgid "Creation"
msgstr "إنشاء"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_search
msgid "Customizable"
msgstr "قابل للتخصيص "

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_order__customizable_pdf_form_fields
msgid "Customizable PDF Form Fields"
msgstr "حقول استمارة PDF القابلة للتخصيص "

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__db_datas
msgid "Database Data"
msgstr "بيانات قاعدة البيانات"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_kanban
msgid "Delete"
msgstr "حذف"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__description
msgid "Description"
msgstr "الوصف"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__display_name
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_pdf_form_field__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__document_type
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_pdf_form_field__document_type
msgid "Document Type"
msgstr "نوع المستند "

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_search_view
msgid "Document type"
msgstr "نوع المستند "

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_kanban
msgid "Download"
msgstr "تنزيل "

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.sale_order_template_form
msgid "Download examples"
msgstr "تنزيل الأمثلة "

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_kanban
msgid "Edit"
msgstr "تحرير"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__datas
msgid "File Content (base64)"
msgstr "محتوى الملف (base64) "

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__raw
msgid "File Content (raw)"
msgstr "محتوى الملف (أولي)"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__file_size
msgid "File Size"
msgstr "حجم الملف"

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/sale_order.py:0
#: model:ir.model.fields.selection,name:sale_pdf_quote_builder.selection__quotation_document__document_type__footer
msgid "Footer"
msgstr "التذييل"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_pdf_form_field__name
msgid "Form Field Name"
msgstr "اسم حقل الاستمارة "

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/product_document.py:0
#: code:addons/sale_pdf_quote_builder/models/quotation_document.py:0
msgid "Form Fields"
msgstr "حقول الاستمارة "

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_product_document__form_field_ids
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__form_field_ids
msgid "Form Fields Included"
msgstr "حقول الاستمارة مشمولة "

#. module: sale_pdf_quote_builder
#: model:ir.model.constraint,message:sale_pdf_quote_builder.constraint_sale_pdf_form_field_unique_name_per_doc_type
msgid "Form field name must be unique for a given document type."
msgstr ""

#. module: sale_pdf_quote_builder
#: model:ir.model,name:sale_pdf_quote_builder.model_sale_pdf_form_field
msgid "Form fields of inside quotation documents."
msgstr "حقول الاستمارات الموجودة داخل مستندات عروض الأسعار. "

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_search_view
msgid "Group By"
msgstr "التجميع حسب "

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/sale_order.py:0
#: model:ir.model.fields.selection,name:sale_pdf_quote_builder.selection__quotation_document__document_type__header
msgid "Header"
msgstr "الترويسة "

#. module: sale_pdf_quote_builder
#: model:ir.model.fields.selection,name:sale_pdf_quote_builder.selection__sale_pdf_form_field__document_type__quotation_document
msgid "Header/Footer"
msgstr "الترويسة/التذييل "

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_order_template__quotation_document_ids
msgid "Headers and footers"
msgstr "الترويسات والتذييلات "

#. module: sale_pdf_quote_builder
#: model:ir.actions.act_window,name:sale_pdf_quote_builder.quotation_document_action
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_order__quotation_document_ids
#: model:ir.ui.menu,name:sale_pdf_quote_builder.sale_menu_quotation_document_action
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.res_config_settings_view_form
msgid "Headers/Footers"
msgstr "الترويسات / التذييلات "

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_form
msgid "History"
msgstr "السجل"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__id
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_pdf_form_field__id
msgid "ID"
msgstr "المُعرف"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,help:sale_pdf_quote_builder.field_quotation_document__active
msgid ""
"If unchecked, it will allow you to hide the header or footer without "
"removing it."
msgstr ""

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__image_height
msgid "Image Height"
msgstr "ارتفاع الصورة "

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__image_src
msgid "Image Src"
msgstr "مصدر الصورة "

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__image_width
msgid "Image Width"
msgstr "عرض الصورة "

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__index_content
msgid "Indexed Content"
msgstr "المحتوى المفهرس"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields.selection,name:sale_pdf_quote_builder.selection__product_document__attached_on_sale__inside
msgid "Inside quote pdf"
msgstr "داخل عرض السعر PDF "

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/sale_pdf_form_field.py:0
msgid ""
"Invalid form field name %(field_name)s. A form field name in a header or a "
"footer can not start with \"sol_id_\"."
msgstr ""
"اسم حقل الاستمارة غير صالح %(field_name)s. لا يمكن أن يبدأ اسم حقل الاستمارة"
" الموجود في الترويسة أو التذييل بـ \"sol_id_\". "

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/sale_pdf_form_field.py:0
msgid ""
"Invalid form field name %(field_name)s. It should only contain "
"alphanumerics, hyphens or underscores."
msgstr ""
"اسم حقل الاستمارة غير صالح %(field_name)s. يجب أن يحتوي فقط على حروف وأرقام "
"أو شرطات أو شرطات سفلية. "

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/sale_pdf_form_field.py:0
msgid ""
"Invalid path %(path)s. It should only contain alphanumerics, hyphens, "
"underscores or points."
msgstr ""
"المسار غير صالح %(path)s. يجب أن يحتوي فقط على حروف وأرقام أو شرطات أو شرطات"
" سفلية أو نقاط. "

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_order__is_pdf_quote_builder_available
msgid "Is Pdf Quote Builder Available"
msgstr "منشئ عروض الأسعار بتنسيق Pdf متاح "

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__public
msgid "Is public document"
msgstr "مستند عام "

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/utils.py:0
msgid ""
"It seems that we're not able to process this pdf inside a quotation. It is "
"either encrypted, or encoded in a format we do not support."
msgstr ""
"لم نتمكن من معالجة ملف PDF الموجود داخل عرض السعر. قد يكون مشفراً أو تم "
"ترميزه بصيغة لا ندعمها. "

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__key
msgid "Key"
msgstr "المفتاح"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__write_uid
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_pdf_form_field__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__write_date
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_pdf_form_field__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_form
msgid "Mark fields as safe to fill in the quote."
msgstr ""

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__mimetype
msgid "Mime Type"
msgstr "نوع تنسيق الملف"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__name
msgid "Name"
msgstr "الاسم"

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/ir_actions_report.py:0
msgid "No"
msgstr "لا"

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/product_document.py:0
msgid "Only PDF documents can be attached inside a quote."
msgstr "يمكن فقط إرفاق الملفات بصيغة PDF داخل عرض السعر "

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/quotation_document.py:0
msgid "Only PDF documents can be used as header or footer."
msgstr ""

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__original_id
msgid "Original (unoptimized, unresized) attachment"
msgstr "مرفق أصلي (غير محسن، لم يتم تحجيمه) "

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_pdf_form_field__path
msgid "Path"
msgstr "المسار "

#. module: sale_pdf_quote_builder
#: model_terms:ir.actions.act_window,help:sale_pdf_quote_builder.quotation_document_action
msgid ""
"Personalize your quotes with catchy header and footer pages\n"
"                <br>\n"
"                to boost your sales."
msgstr ""

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/sale_pdf_form_field.py:0
msgid "Please use only relational fields until the last value of your path."
msgstr ""

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/sale_order.py:0
msgid "Product"
msgstr "المنتج"

#. module: sale_pdf_quote_builder
#: model:ir.model,name:sale_pdf_quote_builder.model_product_document
#: model:ir.model.fields.selection,name:sale_pdf_quote_builder.selection__sale_pdf_form_field__document_type__product_document
msgid "Product Document"
msgstr "مستند المنتج "

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_order_line__product_document_ids
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_pdf_form_field__product_document_ids
msgid "Product Documents"
msgstr "مستندات المنتج "

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.sale_order_template_form
msgid ""
"Provide header pages and footer pages to compose an attractive quotation with more information\n"
"                        about your company, your products and you services. <br/>\n"
"                        The pdf of your quotes will be built by putting together header pages, product descriptions,\n"
"                        details of the quote and then the footer pages. <br/>\n"
"                        If empty, it will use those define in the company settings. <br/>"
msgstr ""

#. module: sale_pdf_quote_builder
#: model:ir.actions.report,name:sale_pdf_quote_builder.action_report_saleorder_raw
msgid "Quotation / Order"
msgstr "عرض السعر / الطلب"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_search_view
msgid "Quotation Document"
msgstr "مستند عرض السعر "

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_pdf_form_field__quotation_document_ids
msgid "Quotation Documents"
msgstr "مستندات عرض السعر "

#. module: sale_pdf_quote_builder
#: model:ir.model,name:sale_pdf_quote_builder.model_sale_order_template
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_search_view
msgid "Quotation Template"
msgstr "قالب عرض السعر"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__quotation_template_ids
msgid "Quotation Templates"
msgstr "قوالب عرض السعر"

#. module: sale_pdf_quote_builder
#: model:ir.model,name:sale_pdf_quote_builder.model_quotation_document
msgid "Quotation's Headers & Footers"
msgstr "الترويسة والتذييل لعرض السعر "

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.sale_order_form_inherit_sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.sale_order_template_form
msgid "Quote Builder"
msgstr "أداة إنشاء عروض الأسعار "

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__ir_attachment_id
msgid "Related attachment"
msgstr "المرفق ذو الصلة "

#. module: sale_pdf_quote_builder
#: model:ir.model,name:sale_pdf_quote_builder.model_ir_actions_report
msgid "Report Action"
msgstr "إجراء التقرير"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__res_field
msgid "Resource Field"
msgstr "حقل المصدر"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__res_id
msgid "Resource ID"
msgstr "معرف المَورِد "

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__res_model
msgid "Resource Model"
msgstr "نموذج الموارد"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__res_name
msgid "Resource Name"
msgstr "اسم المَورِد "

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_product_document__attached_on_sale
msgid "Sale : Visible at"
msgstr "البيع: مرئي في "

#. module: sale_pdf_quote_builder
#: model:ir.actions.server,name:sale_pdf_quote_builder.cron_post_upgrade_assign_missing_form_fields_ir_actions_server
msgid "Sale Pdf Quote Builder: assign form fields to documents post upgrade"
msgstr ""

#. module: sale_pdf_quote_builder
#: model:ir.model,name:sale_pdf_quote_builder.model_sale_order
msgid "Sales Order"
msgstr "أمر البيع"

#. module: sale_pdf_quote_builder
#: model:ir.model,name:sale_pdf_quote_builder.model_sale_order_line
msgid "Sales Order Line"
msgstr "بند أمر المبيعات"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__sequence
msgid "Sequence"
msgstr "تسلسل "

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__store_fname
msgid "Stored Filename"
msgstr "اسم الملف المُخزّن "

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/sale_pdf_form_field.py:0
msgid "The field %(field_name)s doesn't exist on model %(model_name)s"
msgstr "الحقل %(field_name)s غير موجود في النموذج %(model_name)s "

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,help:sale_pdf_quote_builder.field_sale_pdf_form_field__name
msgid "The form field name as written in the PDF."
msgstr ""

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,help:sale_pdf_quote_builder.field_sale_pdf_form_field__path
msgid ""
"The path to follow to dynamically fill the form field. \n"
"Leave empty to be able to customized it in the quotation form."
msgstr ""

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,help:sale_pdf_quote_builder.field_sale_order_line__product_document_ids
msgid ""
"The product documents for this order line that will be merged in the PDF "
"quote."
msgstr ""

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__theme_template_id
msgid "Theme Template"
msgstr "قالب السمة "

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_search
msgid "This document"
msgstr "هذا المستند"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__type
msgid "Type"
msgstr "النوع"

#. module: sale_pdf_quote_builder
#: model_terms:ir.actions.act_window,help:sale_pdf_quote_builder.quotation_document_action
msgid "Upload quotation headers and footers"
msgstr "رفع ترويسات وتذييلات عروض الأسعار "

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__url
msgid "Url"
msgstr "رابط URL "

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_form
msgid "Visible to all"
msgstr "مرئي للكل "

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__voice_ids
msgid "Voice"
msgstr "صوت "

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_quotation_document__website_id
msgid "Website"
msgstr "الموقع الإلكتروني"

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/product_document.py:0
msgid "When attached inside a quote, the document must be a file, not a URL."
msgstr ""
"عندما يكون مرفقاً داخل عرض السعر، يجب أن يكون المستند عبارة عن ملف، وليس "
"رابط URL. "

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/ir_actions_report.py:0
msgid "Yes"
msgstr "نعم"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,help:sale_pdf_quote_builder.field_quotation_document__type
msgid ""
"You can either upload a file from your computer or copy/paste an internet "
"link to your file."
msgstr "يمكنك أن ترفع ملفًا من جهازك أو تنسخ وتلصق رابط الملف."

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.quotation_document_form
msgid "on"
msgstr "في"
