# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_commission
# 
# Translators:
# <PERSON> <<EMAIL>>, 2024
# Wil Odoo, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: sale_commission
#. odoo-python
#: code:addons/sale_commission/model/commission_plan.py:0
msgid "%s (copy)"
msgstr "%s (نسخة)"

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "<span class=\"o_stat_text\">Commissions</span>"
msgstr "<span class=\"o_stat_text\">العمولات</span> "

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement_report__achieved
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_report__achieved
msgid "Achieved"
msgstr "مؤرشف "

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_report__achieved_rate
msgid "Achieved Rate"
msgstr "النسبة التي تم تحقيقها "

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__achievement_ids
msgid "Achievement"
msgstr "إنجاز "

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_res_config_settings__group_commission_forecast
msgid "Achievement Forecast"
msgstr "النسبة المتوقع تحقيقها "

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_achievement_report_view_search
msgid "Achievement Report"
msgstr "تقرير الإنجازات "

#. module: sale_commission
#: model:ir.actions.act_window,name:sale_commission.sale_achievement_action_report
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_plan__type__achieve
#: model:ir.ui.menu,name:sale_commission.sale_commission_achievement_report_menu
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_report_view_list
msgid "Achievements"
msgstr "الإنجازات "

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__message_needaction
msgid "Action Needed"
msgstr "إجراء مطلوب"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__active
msgid "Active"
msgstr "نشط"

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_report_view_search
msgid "Active Plans"
msgstr "الخطط النشطة "

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_user_wizard
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "Add Multiple Salespersons"
msgstr "إضافة عدة مندوبي مبيعات "

#. module: sale_commission
#: model:ir.actions.act_window,name:sale_commission.sale_subscription_change_customer_wizard_action
msgid "Add Salespersons"
msgstr "إضافة مندوبي مبيعات "

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "Add a new Sales Person"
msgstr "إضافة مندوب مبيعات جديد "

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "Add a new achievement"
msgstr "إضافة إنجاز جديد "

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "Add a new commission level"
msgstr "إضافة مستوى عمولة جديد "

#. module: sale_commission
#. odoo-python
#: code:addons/sale_commission/model/commission_achievement.py:0
msgid "Adjustment %s"
msgstr "التعديل %s "

#. module: sale_commission
#. odoo-python
#: code:addons/sale_commission/model/commission_achievement.py:0
msgid "Adjustment: %s"
msgstr "التعديل: %s "

#. module: sale_commission
#: model:ir.actions.act_window,name:sale_commission.sale_commission_action_achievement
#: model:ir.ui.menu,name:sale_commission.sale_commission_achievement_menu
msgid "Adjustments"
msgstr "التعديلات"

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "All categories"
msgstr "كافة الفئات "

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "All products"
msgstr "كافة المنتجات "

#. module: sale_commission
#: model_terms:ir.actions.act_window,help:sale_commission.sale_commission_action_achievement
msgid ""
"Allows you to manually adjust a salesperson's achievements\n"
"                for a specific period and commission plan."
msgstr ""
"يُتيح لك تعديل إنجازات مندوب المبيعات يدوياً \n"
"                لفترة محددة ولخطة عمولة محددة. "

#. module: sale_commission
#: model_terms:ir.actions.act_window,help:sale_commission.sale_commission_action_forecast
msgid ""
"Allows you to manually set a salesperson's forecast\n"
"                for a specific period and commission plan."
msgstr ""
"يُتيح لك تعيين النِسَب المتوقَّعة من مندوب المبيعات يدوياً \n"
"                لفترة محددة ولخطة عمولة محددة. "

#. module: sale_commission
#: model_terms:ir.actions.act_window,help:sale_commission.sale_commission_action_my_forecast
msgid ""
"Allows you to manually set your sales' forecast\n"
"                for a specific period and commission plan."
msgstr ""
"يُتيح لك تعيين توقعات مبيعاتك يدوياً \n"
"                لفترة محددة ولخطة عمولة محددة. "

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement__amount
msgid "Amount"
msgstr "مبلغ"

#. module: sale_commission
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_achievement__type__amount_invoiced
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_plan_achievement__type__amount_invoiced
msgid "Amount Invoiced"
msgstr "المبلغ المفوتر "

#. module: sale_commission
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_achievement__type__amount_sold
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_plan_achievement__type__amount_sold
msgid "Amount Sold"
msgstr "ما تم تحقيقه من المبيعات "

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "Approve"
msgstr "موافقة"

#. module: sale_commission
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_plan__state__approved
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_search
msgid "Approved"
msgstr "تمت الموافقة "

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "Archived"
msgstr "مؤرشف"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__message_attachment_count
msgid "Attachment Count"
msgstr "عدد المرفقات"

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "Based on"
msgstr "بناءً على"

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_report_view_search
msgid "By Quarter"
msgstr "لكل ربع سنة "

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_user_wizard
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "Cancel"
msgstr "إلغاء"

#. module: sale_commission
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_plan__state__cancel
msgid "Cancelled"
msgstr "تم الإلغاء "

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_achievement__product_categ_id
msgid "Category"
msgstr "الفئة "

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_commission__amount
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_report__commission
msgid "Commission"
msgstr "العمولة "

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_achievement_view_search
msgid "Commission Achievement"
msgstr "العمولة التي تم تحقيقها "

#. module: sale_commission
#. odoo-python
#: code:addons/sale_commission/report/commission_report.py:0
msgid "Commission Detail: %(name)s"
msgstr "تفاصيل العمولة: %(name)s "

#. module: sale_commission
#: model:ir.model,name:sale_commission.model_sale_commission_plan
#: model:ir.model.fields,field_description:sale_commission.field_crm_team__commission_plan_ids
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement_report__plan_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_report__plan_id
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_achievement_report_view_search
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_search
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_report_view_search
msgid "Commission Plan"
msgstr "خطة العمولة "

#. module: sale_commission
#: model:ir.model,name:sale_commission.model_sale_commission_plan_achievement
msgid "Commission Plan Achievement"
msgstr "ما تم إنجازه من خطة العمولة "

#. module: sale_commission
#: model:ir.model,name:sale_commission.model_sale_commission_plan_target
msgid "Commission Plan Target"
msgstr "هدف خطة عمولة المبيعات "

#. module: sale_commission
#: model:ir.model,name:sale_commission.model_sale_commission_plan_target_commission
msgid "Commission Plan Target Commission"
msgstr "العمولة المستهدفة في خطة عمولة المبيعات "

#. module: sale_commission
#: model:ir.model,name:sale_commission.model_sale_commission_plan_target_forecast
msgid "Commission Plan Target Forecast"
msgstr "توقعات هدف خطة عمولة المبيعات "

#. module: sale_commission
#: model:ir.model,name:sale_commission.model_sale_commission_plan_user
msgid "Commission Plan User"
msgstr "المستخدِم في خطة العمولة "

#. module: sale_commission
#: model:ir.actions.act_window,name:sale_commission.sale_commission_action_plan
#: model:ir.ui.menu,name:sale_commission.sale_commission_plan_menu
msgid "Commission Plans"
msgstr "خطط العمولة "

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_report_view_search
msgid "Commission Report"
msgstr "تقرير عمولة المبيعات "

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_res_users__commission_plan_users_ids
msgid "Commission plans"
msgstr "خطط عمولة المبيعات "

#. module: sale_commission
#: model:ir.actions.act_window,name:sale_commission.sale_commission_action_report
#: model:ir.actions.act_window,name:sale_commission.sale_commission_action_report_sale
#: model:ir.ui.menu,name:sale_commission.menu_sale_commission
#: model:ir.ui.menu,name:sale_commission.sale_commission_plan_report_menu
#: model:ir.ui.menu,name:sale_commission.sale_commission_plan_report_menu_report
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "Commissions"
msgstr "العمولات "

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement__company_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement_report__company_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__company_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_report__company_id
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_achievement_report_view_search
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_search
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_report_view_search
msgid "Company"
msgstr "الشركة "

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_search
msgid "Completed"
msgstr "مكتملة "

#. module: sale_commission
#: model_terms:ir.actions.act_window,help:sale_commission.sale_commission_action_plan
msgid ""
"Compute commissions of your sales people based on their achievements and "
"targets"
msgstr "احسب عمولات موظفي المبيعات لديك بناءً على إنجازاتهم وأهدافهم "

#. module: sale_commission
#: model:ir.model,name:sale_commission.model_res_config_settings
msgid "Config Settings"
msgstr "تهيئة الإعدادات "

#. module: sale_commission
#: model_terms:ir.actions.act_window,help:sale_commission.sale_commission_action_plan
msgid "Create a new commission plan"
msgstr "إنشاء خطة عمولات جديدة "

#. module: sale_commission
#: model_terms:ir.actions.act_window,help:sale_commission.sale_commission_action_achievement
msgid "Create an adjustment"
msgstr "إجراء تعديل "

#. module: sale_commission
#: model_terms:ir.actions.act_window,help:sale_commission.sale_commission_action_forecast
#: model_terms:ir.actions.act_window,help:sale_commission.sale_commission_action_my_forecast
msgid "Create an forecast"
msgstr "تحديد التوقعات "

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement__create_uid
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__create_uid
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_achievement__create_uid
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target__create_uid
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_commission__create_uid
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_forecast__create_uid
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_user__create_uid
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_user_wizard__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement__create_date
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__create_date
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_achievement__create_date
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target__create_date
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_commission__create_date
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_forecast__create_date
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_user__create_date
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_user_wizard__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement__currency_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement_report__currency_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__currency_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target__currency_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_commission__currency_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_forecast__currency_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_report__currency_id
msgid "Currency"
msgstr "العملة"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement__currency_rate
msgid "Currency Rate"
msgstr "سعر صرف العملة"

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_achievement_report_view_search
msgid "Current Period"
msgstr "الفترة الحالية "

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement__date
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement_report__date
msgid "Date"
msgstr "التاريخ"

#. module: sale_commission
#: model:ir.model.fields,help:sale_commission.field_crm_team__commission_plan_ids
msgid "Default commission plan for team members."
msgstr "خطة العمولة الافتراضية لأعضاء الفريق. "

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_report_view_list
msgid "Details"
msgstr "التفاصيل"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement__display_name
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement_report__display_name
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__display_name
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_achievement__display_name
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target__display_name
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_commission__display_name
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_forecast__display_name
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_user__display_name
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_user_wizard__display_name
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_report__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: sale_commission
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_plan__state__done
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_search
msgid "Done"
msgstr "منتهي "

#. module: sale_commission
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_plan__state__draft
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_search
msgid "Draft"
msgstr "مسودة"

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "Effective Period"
msgstr "فترة السريان "

#. module: sale_commission
#: model:res.groups,name:sale_commission.group_commission_forecast
msgid "Enable Commission Forecast"
msgstr "تفعيل توقعات العمولة "

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_list
msgid "End Date"
msgstr "تاريخ الانتهاء"

#. module: sale_commission
#: model_terms:ir.actions.act_window,help:sale_commission.sale_commission_action_my_report
#: model_terms:ir.actions.act_window,help:sale_commission.sale_commission_action_report
#: model_terms:ir.actions.act_window,help:sale_commission.sale_commission_action_report_sale
msgid ""
"Ensure you are assigned to a commission plan and have made sales that "
"qualify for commissions"
msgstr ""
"تأكد من أنه قد تم تعيينك في خطة عمولة وقمت بتحقيق مبيعات مؤهلة للحصول على "
"عمولات "

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_res_users__filtered_commission_plan_users_ids
msgid "Filtered Commission Plan Users"
msgstr "مستخدمو خطة العمولة الذين تمت تصفيتهم "

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__message_follower_ids
msgid "Followers"
msgstr "المتابعين"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__message_partner_ids
msgid "Followers (Partners)"
msgstr "المتابعين (الشركاء) "

#. module: sale_commission
#: model:ir.actions.act_window,name:sale_commission.sale_commission_action_forecast
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_forecast__amount
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_report__forecast
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_target_forecast_view_graph
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_target_forecast_view_list
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_target_forecast_view_pivot
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_target_forecast_view_search
msgid "Forecast"
msgstr "المتوقع "

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__date_from
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target__date_from
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_user__date_from
msgid "From"
msgstr "من"

#. module: sale_commission
#. odoo-python
#: code:addons/sale_commission/model/commission_plan_user.py:0
msgid "From must be before To"
msgstr "يجب أن تكون قيمة \"من\" قبل قيمة \"إلى\" "

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_achievement_report_view_search
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_target_forecast_view_search
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_search
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_report_view_search
msgid "Group By"
msgstr "التجميع حسب "

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__has_message
msgid "Has Message"
msgstr "يحتوي على رسالة "

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement__id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement_report__id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_achievement__id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target__id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_commission__id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_forecast__id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_user__id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_user_wizard__id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_report__id
msgid "ID"
msgstr "المُعرف"

#. module: sale_commission
#: model:ir.model.fields,help:sale_commission.field_sale_commission_plan__message_needaction
msgid "If checked, new messages require your attention."
msgstr "إذا كان محددًا، فهناك رسائل جديدة عليك رؤيتها. "

#. module: sale_commission
#: model:ir.model.fields,help:sale_commission.field_sale_commission_plan__message_has_error
#: model:ir.model.fields,help:sale_commission.field_sale_commission_plan__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "إذا كان محددًا، فقد حدث خطأ في تسليم بعض الرسائل."

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__message_is_follower
msgid "Is Follower"
msgstr "متابع"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement__write_uid
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__write_uid
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_achievement__write_uid
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target__write_uid
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_commission__write_uid
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_forecast__write_uid
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_user__write_uid
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_user_wizard__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement__write_date
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__write_date
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_achievement__write_date
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target__write_date
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_commission__write_date
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_forecast__write_date
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_user__write_date
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_user_wizard__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: sale_commission
#: model:ir.model.fields,help:sale_commission.field_sale_commission_achievement__team_id
#: model:ir.model.fields,help:sale_commission.field_sale_commission_plan_target_forecast__team_id
msgid ""
"Main user sales team. Used notably for pipeline, or to set sales team in "
"invoicing or subscription."
msgstr ""
"فريق المبيعات للمستخدم الرئيسي. يُستخدم لمخطط سير العمل بشكل كبير أو لتعيين "
"فريق المبيعات في الفوترة أو الاشتراك. "

#. module: sale_commission
#: model:ir.model,name:sale_commission.model_sale_commission_achievement
msgid "Manual Commission Achievement"
msgstr "تحقيق العمولة يدوياً "

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "Mark as done"
msgstr "التعيين كمنتهي "

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__message_has_error
msgid "Message Delivery error"
msgstr "خطأ في تسليم الرسائل"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__message_ids
msgid "Messages"
msgstr "الرسائل"

#. module: sale_commission
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_plan__periodicity__month
msgid "Monthly"
msgstr "شهرياً"

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_achievement_report_view_search
msgid "My Achievements"
msgstr "إنجازاتي "

#. module: sale_commission
#: model:ir.actions.act_window,name:sale_commission.sale_commission_action_my_report
#: model:ir.ui.menu,name:sale_commission.sale_commission_my_menu
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_report_view_search
msgid "My Commissions"
msgstr "عمولاتي "

#. module: sale_commission
#: model:ir.actions.act_window,name:sale_commission.sale_commission_action_my_forecast
msgid "My Forecast"
msgstr "توقعاتي "

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__name
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_search
msgid "Name"
msgstr "الاسم"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement__note
msgid "Note"
msgstr "الملاحظات"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__message_needaction_counter
msgid "Number of Actions"
msgstr "عدد الإجراءات"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__message_has_error_counter
msgid "Number of errors"
msgstr "عدد الأخطاء "

#. module: sale_commission
#: model:ir.model.fields,help:sale_commission.field_sale_commission_plan__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "عدد الرسائل التي تتطلب اتخاذ إجراء"

#. module: sale_commission
#: model:ir.model.fields,help:sale_commission.field_sale_commission_plan__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "عدد الرسائل الحادث بها خطأ في التسليم"

#. module: sale_commission
#: model:ir.model.fields,help:sale_commission.field_sale_commission_plan__commission_amount
msgid "OTC"
msgstr "العمولة عند تحقيق الهدف "

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_commission__amount_rate
msgid "OTC %"
msgstr "نسبة العمولة عند تحقيق الهدف "

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__commission_amount
msgid "On Target Commission"
msgstr "العمولة عند تحقيق الهدف "

#. module: sale_commission
#: model:ir.model.fields,help:sale_commission.field_sale_commission_plan_target_commission__amount_rate
msgid "On Target Commission rate"
msgstr "نسبة العمولة عند تحقيق الهدف "

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_search
msgid "Ongoing"
msgstr "جاري"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_user__other_plans
msgid "Other plans"
msgstr "الخطط الأخرى "

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_report__payment_date
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_report_view_search
msgid "Payment Date"
msgstr "تاريخ الدفع "

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement_report__target_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target__name
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_forecast__target_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_report__target_id
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_achievement_report_view_search
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_target_forecast_view_search
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_report_view_search
msgid "Period"
msgstr "الفترة"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__periodicity
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_search
msgid "Periodicity"
msgstr "الوتيرة "

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "Periods"
msgstr "الفترات "

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_achievement__plan_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target__plan_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_commission__plan_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_forecast__plan_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_user__plan_id
msgid "Plan"
msgstr "الخطة "

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_achievement__product_id
msgid "Product"
msgstr "المنتج"

#. module: sale_commission
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_achievement__type__qty_invoiced
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_plan_achievement__type__qty_invoiced
msgid "Quantity Invoiced"
msgstr "الكمية التي تمت فوترتها "

#. module: sale_commission
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_achievement__type__qty_sold
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_plan_achievement__type__qty_sold
msgid "Quantity Sold"
msgstr "الكمية المباعة"

#. module: sale_commission
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_plan__periodicity__quarter
msgid "Quarterly"
msgstr "ربع سنوي"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_achievement__rate
msgid "Rate"
msgstr "المعدل "

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__rating_ids
msgid "Ratings"
msgstr "التقييمات "

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement_report__related_res_id
msgid "Related"
msgstr "ذو صلة "

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement_report__related_res_model
msgid "Related Res Model"
msgstr "نموذج Res ذو الصلة "

#. module: sale_commission
#. odoo-python
#: code:addons/sale_commission/model/commission_plan.py:0
msgid "Related commissions"
msgstr "العمولات ذات الصلة "

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "Reset to draft"
msgstr "إعادة التعيين كمسودة "

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__message_has_sms_error
msgid "SMS Delivery error"
msgstr "خطأ في تسليم الرسائل النصية القصيرة "

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__team_id
msgid "Sale team"
msgstr "فريق المبيعات "

#. module: sale_commission
#: model:ir.model,name:sale_commission.model_sale_commission_achievement_report
msgid "Sales Achievement Report"
msgstr "تقرير إنجازات المبيعات "

#. module: sale_commission
#: model:ir.model,name:sale_commission.model_sale_commission_report
msgid "Sales Commission Report"
msgstr "تقرير عمولة المبيعات "

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "Sales People"
msgstr "مندوبو المبيعات "

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement__user_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement_report__user_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_report__user_id
msgid "Sales Person"
msgstr "مندوب مبيعات"

#. module: sale_commission
#: model:ir.model,name:sale_commission.model_crm_team
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement_report__team_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_report__team_id
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_plan__user_type__team
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_achievement_report_view_search
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_target_forecast_view_search
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_report_view_search
msgid "Sales Team"
msgstr "فريق المبيعات"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_user__user_id
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_plan__user_type__person
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_achievement_report_view_search
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_target_forecast_view_search
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_report_view_search
msgid "Salesperson"
msgstr "مندوب المبيعات "

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_report_view_list
msgid "See associated achievements"
msgstr "ألقِ نظرة على الإنجازات ذات الصلة "

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_achievement_report_view_list
msgid "Source"
msgstr "المصدر"

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_list
msgid "Start Date"
msgstr "تاريخ البدء "

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__state
msgid "State"
msgstr "الحالة "

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_user_wizard
msgid "Submit"
msgstr "إرسال"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__target_ids
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target__amount
msgid "Target"
msgstr "الهدف"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_report__target_amount
msgid "Target Amount"
msgstr "المبلغ المُراد تحقيقه "

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__target_commission_ids
msgid "Target Commission"
msgstr "العمولة المُراد تحقيقها "

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__target_commission_graph
msgid "Target Commission Graph"
msgstr "الرسم البياني للعمولة المُراد تحقيقها "

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "Target Frequency"
msgstr "الوتيرة المستهدفة "

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_commission__target_rate
msgid "Target completion (%)"
msgstr "ما تم تحقيقه (%) "

#. module: sale_commission
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_plan__type__target
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "Targets"
msgstr "الأهداف المُراد تحقيقها "

#. module: sale_commission
#. odoo-python
#: code:addons/sale_commission/model/commission_plan.py:0
msgid ""
"The plan should have at least one target with an achievement rate of 0%"
msgstr "يجب أن تتضمن الخطة هدفاً واحداً على الأقل بمعدل إنجاز يبلغ 0% "

#. module: sale_commission
#. odoo-python
#: code:addons/sale_commission/model/commission_plan.py:0
msgid "The start date must be before the end date."
msgstr "يجب أن يكون تاريخ البدء قبل تاريخ الانتهاء."

#. module: sale_commission
#. odoo-python
#: code:addons/sale_commission/model/commission_plan.py:0
msgid "The team is required in team plan."
msgstr "الفريق مطلوب في خطة الفريق. "

#. module: sale_commission
#: model:ir.model.constraint,message:sale_commission.constraint_sale_commission_plan_user_user_uniq
msgid "The user is already present in the plan"
msgstr "المستخدم موجود بالفعل في الخطة "

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_target_forecast_view_search
msgid "This Month"
msgstr "هذا الشهر"

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_target_forecast_view_search
msgid "This Year"
msgstr "هذا العام "

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__date_to
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target__date_to
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_user__date_to
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_report__date_to
msgid "To"
msgstr "إلى"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement__type
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__type
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_achievement__type
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_search
msgid "Type"
msgstr "النوع"

#. module: sale_commission
#: model_terms:ir.actions.act_window,help:sale_commission.sale_commission_action_my_report
#: model_terms:ir.actions.act_window,help:sale_commission.sale_commission_action_report
#: model_terms:ir.actions.act_window,help:sale_commission.sale_commission_action_report_sale
msgid "Unfortunately, there are no commissions for you"
msgstr "لسوء الحظ، لا توجد عمولات لك "

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_search
msgid "Upcoming"
msgstr "القادم "

#. module: sale_commission
#: model:ir.model,name:sale_commission.model_res_users
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__user_ids
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_forecast__user_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_user_wizard__user_ids
msgid "User"
msgstr "المستخدم"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement__team_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_forecast__team_id
msgid "User Sales Team"
msgstr "فريق مبيعات المستخدم "

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__user_type
msgid "User Type"
msgstr "نوع المستخدم"

#. module: sale_commission
#. odoo-python
#: code:addons/sale_commission/model/commission_plan_user.py:0
msgid "User period cannot end after the plan."
msgstr "لا يمكن أن تنتهي فترة المستخدم بعد الخطة. "

#. module: sale_commission
#. odoo-python
#: code:addons/sale_commission/model/commission_plan_user.py:0
msgid "User period cannot start before the plan."
msgstr "لا يمكن أن تبدأ فترة المستخدم قبل الخطة. "

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__website_message_ids
msgid "Website Messages"
msgstr "رسائل الموقع الإلكتروني "

#. module: sale_commission
#: model:ir.model.fields,help:sale_commission.field_sale_commission_plan__website_message_ids
msgid "Website communication history"
msgstr "سجل تواصل الموقع الإلكتروني "

#. module: sale_commission
#: model:ir.model,name:sale_commission.model_sale_commission_plan_user_wizard
msgid "Wizard for selecting multiple users"
msgstr "معالج لتحديد عدة مستخدمين "

#. module: sale_commission
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_plan__periodicity__year
msgid "Yearly"
msgstr "سنويًا"

#. module: sale_commission
#. odoo-python
#: code:addons/sale_commission/model/commission_plan_target_forecast.py:0
msgid "You cannot create a forecast for an user that is not in the plan."
msgstr ""

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_report__forecast_id
msgid "fc"
msgstr "fc"

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "i.e. Commissions plan 2025"
msgstr "مثال: خطة العمولة لعام 2025 "

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "per"
msgstr "لكل "
