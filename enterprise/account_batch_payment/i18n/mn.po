# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_batch_payment
# 
# Translators:
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON><PERSON> <chee<PERSON><EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# Bayarkhuu Bataa, 2024
# Гэрэлтцог Цогтбаатар, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# Torbat Jargalsaikhan, 2024
# hish, 2024
# Martin Trigaux, 2024
# <AUTHOR> <EMAIL>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 09:13+0000\n"
"PO-Revision-Date: 2024-09-25 09:43+0000\n"
"Last-Translator: Baskhuu Lodoikhuu <<EMAIL>>, 2024\n"
"Language-Team: Mongolian (https://app.transifex.com/odoo/teams/41243/mn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: mn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
msgid "$1000.0"
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
msgid "2023-08-14"
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
msgid "2023-08-15"
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
msgid "*************"
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_account_move_kanban
msgid "<i class=\"fa fa-clock-o\" aria-label=\"Date\" role=\"img\" title=\"Date\"/>"
msgstr "<i class=\"fa fa-clock-o\" aria-label=\"Date\" role=\"img\" title=\"Date\"/>"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_account_payment_form_inherit_account_batch_payment
msgid "<span>Batch Payment</span>"
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.batch_error_wizard_form
msgid ""
"<strong invisible=\"not warning_line_ids\">The following warnings were also "
"raised; they do not impeach validation</strong>"
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.batch_error_wizard_form
msgid "<strong>Please first consider the following warnings</strong>"
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.batch_error_wizard_form
msgid "<strong>The following errors occurred</strong>"
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
msgid "ABC Holder Name"
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
msgid "ABC Suppliers"
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
msgid "Account Holder Name"
msgstr "Данс эзэмшигчийн нэр"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__message_needaction
msgid "Action Needed"
msgstr "Үйлдэл шаардлагатай"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__activity_ids
msgid "Activities"
msgstr "Ажилбар"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Ажилбарын тайлбар"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__activity_state
msgid "Activity State"
msgstr "Ажилбарын төлөв"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ажилбарын төрлийн зураг"

#. module: account_batch_payment
#. odoo-python
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
msgid "All payments in the batch must belong to the same company."
msgstr "Багцлах гэж буй төлбөрүүд нь бүгд ижил компаных байх ёстой."

#. module: account_batch_payment
#. odoo-python
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
msgid "All payments in the batch must share the same payment method."
msgstr "Багцлах гэж буй төлбөрүүд нь бүгд ижил төлбөрийн аргатай байх ёстой."

#. module: account_batch_payment
#. odoo-python
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
msgid "All payments must be posted to validate the batch."
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__amount
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Amount"
msgstr "Дүн"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__amount_residual
msgid "Amount Residual"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__amount_residual_currency
msgid "Amount Residual Currency"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_payment__amount_signed
msgid "Amount Signed"
msgstr "Гарын үсэг зурсан дүн"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__message_attachment_count
msgid "Attachment Count"
msgstr "Хавсралтын тоо"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__available_payment_method_ids
msgid "Available Payment Method"
msgstr "Боломжит төлбөрийн арга"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__journal_id
msgid "Bank"
msgstr "Банк"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_search
msgid "Bank Journal"
msgstr "Банкны журнал"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
msgid "Bank Transfer"
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Batch Content"
msgstr "Багцын агуулга"

#. module: account_batch_payment
#: model:account.payment.method,name:account_batch_payment.account_payment_method_batch_deposit
#: model_terms:ir.ui.view,arch_db:account_batch_payment.account_journal_dashboard_kanban_view_inherited
msgid "Batch Deposit"
msgstr "Багц төлбөр"

#. module: account_batch_payment
#: model:ir.model,name:account_batch_payment.model_report_account_batch_payment_print_batch_payment
msgid "Batch Deposit Report"
msgstr "Багц төлбөрийн тайлан"

#. module: account_batch_payment
#. odoo-python
#: code:addons/account_batch_payment/models/account_payment.py:0
#: model:ir.model,name:account_batch_payment.model_account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard__batch_payment_id
#: model:ir.model.fields,field_description:account_batch_payment.field_account_payment__batch_payment_id
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_search
msgid "Batch Payment"
msgstr "Багц төлбөр"

#. module: account_batch_payment
#: model:ir.ui.menu,name:account_batch_payment.menu_batch_payment_purchases
#: model:ir.ui.menu,name:account_batch_payment.menu_batch_payment_sales
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_account_payment_search_inherit_account_batch_payment
msgid "Batch Payments"
msgstr "Багц төлөлт"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__batch_type
msgid "Batch Type"
msgstr "Багцын төрөл"

#. module: account_batch_payment
#: model_terms:ir.actions.act_window,help:account_batch_payment.action_batch_payment_in
#: model_terms:ir.actions.act_window,help:account_batch_payment.action_batch_payment_out
msgid ""
"Batch payments allow you grouping different payments to ease\n"
"                    reconciliation. They are also useful when depositing checks\n"
"                    to the bank."
msgstr ""
"Багц төлөлт нь танд ирсэн төлбөрүүдийг хялбархан багцалж тулгалт хийх боломж олгоно.\n"
"                   Мөн энэ боломжыг ашиглан та өөрийн төлж буй төлбөрүүдийг багцлан банк руу явуулах боломжтой."

#. module: account_batch_payment
#: model:ir.model,name:account_batch_payment.model_account_batch_error_wizard
msgid "Batch payments error reporting wizard"
msgstr ""

#. module: account_batch_payment
#: model:ir.model,name:account_batch_payment.model_account_batch_error_wizard_line
msgid "Batch payments error reporting wizard line"
msgstr ""

#. module: account_batch_payment
#. odoo-python
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
msgid "Cannot validate an empty batch. Please add some payments to it first."
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.batch_error_wizard_form
msgid "Close"
msgstr "Хаах"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__payment_method_code
msgid "Code"
msgstr "Код"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__company_id
msgid "Company"
msgstr "Компани"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__company_currency_id
msgid "Company Currency"
msgstr "Компанийн валют"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__company_id
msgid "Company related to this journal"
msgstr "Энэ журналын харъяалагдах компани"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_account_payment_tree_inherit_account_batch_payment
msgid "Create Batch"
msgstr ""

#. module: account_batch_payment
#. odoo-python
#: code:addons/account_batch_payment/models/account_journal.py:0
msgid "Create Batch Payment"
msgstr "Багц төлбөр үүсгэх"

#. module: account_batch_payment
#: model_terms:ir.actions.act_window,help:account_batch_payment.action_batch_payment_in
msgid "Create a new customer batch payment"
msgstr "Захиалагчийн багц төлбөр шинээр үүсгэх"

#. module: account_batch_payment
#: model_terms:ir.actions.act_window,help:account_batch_payment.action_batch_payment_out
msgid "Create a new vendor batch payment"
msgstr "Нийлүүлэгчийн багц төлбөр шинээр үүсгэх"

#. module: account_batch_payment
#: model:ir.actions.server,name:account_batch_payment.action_account_create_batch_payment
msgid "Create batch payment"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard__create_uid
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__create_uid
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__create_uid
msgid "Created by"
msgstr "Үүсгэсэн этгээд"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard__create_date
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__create_date
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__create_date
msgid "Created on"
msgstr "Үүсгэсэн огноо"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__export_file_create_date
msgid "Creation date of the related export file."
msgstr "Экспорт файлын үүссэн огноо."

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__currency_id
msgid "Currency"
msgstr "Валют"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
msgid "Customer"
msgstr "Үйлчлүүлэгч"

#. module: account_batch_payment
#: model:ir.actions.act_window,name:account_batch_payment.action_batch_payment_in
msgid "Customer Batch Payments"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__date
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
msgid "Date"
msgstr "Огноо"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
msgid "Demo Ref"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__description
msgid "Description"
msgstr "Тайлбар"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard__display_name
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__display_name
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__display_name
msgid "Display Name"
msgstr "Дэлгэрэнгүй нэр"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard__error_line_ids
msgid "Error Line"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__error_wizard_id
msgid "Error Wizard"
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.batch_error_wizard_line_tree
msgid "Exclude Payments"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__export_file
msgid "Export file related to this batch"
msgstr "Энэ багцаас боловсруулагдсан файл"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__export_file
msgid "File"
msgstr "Файл"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__file_generation_enabled
msgid "File Generation Enabled"
msgstr "Файл үүсгэлт идэвхижсэн"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__export_filename
msgid "File Name"
msgstr "Файлын нэр"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__message_follower_ids
msgid "Followers"
msgstr "Дагагчид"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__message_partner_ids
msgid "Followers (Partners)"
msgstr "Дагагчид (Харилцагчид)"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesome icon ж.ш. fa-tasks"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__export_file_create_date
msgid "Generation Date"
msgstr "Боловсруулсан огоо"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_search
msgid "Group By"
msgstr "Бүлэглэлт"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__has_message
msgid "Has Message"
msgstr "Мессежтэй"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__help_message
msgid "Help"
msgstr "Тусламж"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard__id
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__id
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__id
msgid "ID"
msgstr "ID"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__activity_exception_icon
msgid "Icon"
msgstr "Дүрс"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ажилбар дээр сануулга гарсныг илэрхийлэх зураг."

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Хэрэв сонгогдсон бол, шинэ зурвасууд таны анхаарлыг шаардана."

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__message_has_error
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Үүнийг сонговол алдаа үүсэх үед зурвасууд ирнэ."

#. module: account_batch_payment
#: model:ir.model.fields.selection,name:account_batch_payment.selection__account_batch_payment__batch_type__inbound
msgid "Inbound"
msgstr "Ирж буй"

#. module: account_batch_payment
#. odoo-python
#: code:addons/account_batch_payment/models/account_journal.py:0
msgid "Inbound Batch Payments Sequence"
msgstr "Ирж буй багц төлбөрийн дугаарлалт"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__message_is_follower
msgid "Is Follower"
msgstr "Дагагч эсэх"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
msgid "Issuing bank account :"
msgstr ""

#. module: account_batch_payment
#: model:ir.model,name:account_batch_payment.model_account_journal
msgid "Journal"
msgstr "Журнал"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard__write_uid
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__write_uid
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__write_uid
msgid "Last Updated by"
msgstr "Сүүлд зассан этгээд"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard__write_date
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__write_date
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__write_date
msgid "Last Updated on"
msgstr "Сүүлд зассан огноо"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Leave empty to generate automatically..."
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
msgid "Memo"
msgstr "Санамж"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__message_has_error
msgid "Message Delivery error"
msgstr "Зурвас илгээх алдаа"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__message_ids
msgid "Messages"
msgstr "Зурвасууд"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
msgid "Monthly Payment"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Миний ажилбарын эцсийн огноо"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_payment__payment_method_name
msgid "Name"
msgstr "Нэр"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__export_filename
msgid "Name of the export file generated for this batch"
msgstr "Энэ багцаас боловсруулагдаж гарах файлын нэр"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_payment__amount_signed
msgid "Negative value of amount field if payment_type is outbound"
msgstr ""
"Хэрэв `payment_type` нь зарлагын (outbound) тохиолдолд, дүнгийн талбарыг "
"сөрөг утгаар заана."

#. module: account_batch_payment
#: model:ir.model.fields.selection,name:account_batch_payment.selection__account_batch_payment__state__draft
msgid "New"
msgstr "Шинэ"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Календар дээрх дараагийн Үйл ажиллагаа"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Дараагийн ажилбарын эцсийн огноо"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__activity_summary
msgid "Next Activity Summary"
msgstr "Дараагийн ажилбарын гарчиг"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__activity_type_id
msgid "Next Activity Type"
msgstr "Дараагийн ажилбарын төрөл"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_account_payment_search_inherit_account_batch_payment
msgid "Not Batch Payments"
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_account_payment_search_inherit_account_batch_payment
msgid "Not reconciled"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__message_needaction_counter
msgid "Number of Actions"
msgstr "Үйлдлийн тоо"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__message_has_error_counter
msgid "Number of errors"
msgstr "Алдааны тоо"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__message_needaction_counter
msgid "Number of messages requiring action"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Алдааны мэдэгдэл бүхий зурвасын тоо"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
msgid "Odoo Payments LLC"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields.selection,name:account_batch_payment.selection__account_batch_payment__batch_type__outbound
msgid "Outbound"
msgstr "Гарч буй"

#. module: account_batch_payment
#. odoo-python
#: code:addons/account_batch_payment/models/account_journal.py:0
msgid "Outbound Batch Payments Sequence"
msgstr "Гарч буй багц төлбөрийн дугаарлалт"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_account_payment_tree_inherit_account_batch_payment
msgid "Partner"
msgstr "Харилцагч"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__payment_method_id
msgid "Payment Method"
msgstr "Төлбөрийн арга"

#. module: account_batch_payment
#: model:ir.model,name:account_batch_payment.model_account_payment_method
msgid "Payment Methods"
msgstr "Төлбөрийн аргууд"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Payment method"
msgstr "Төлбөрийн хэлбэр"

#. module: account_batch_payment
#: model:ir.model,name:account_batch_payment.model_account_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__payment_ids
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__payment_ids
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Payments"
msgstr "Төлбөрүүд"

#. module: account_batch_payment
#. odoo-python
#: code:addons/account_batch_payment/wizard/batch_error.py:0
msgid "Payments in Error"
msgstr ""

#. module: account_batch_payment
#. odoo-python
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
msgid "Payments must be posted to be added to a batch."
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Print"
msgstr "Хэвлэх"

#. module: account_batch_payment
#: model:ir.actions.report,name:account_batch_payment.action_print_batch_payment
msgid "Print Batch Payment"
msgstr "Багц төлбөр хэвлэх"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.batch_error_wizard_form
msgid "Proceed with validation"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__rating_ids
msgid "Ratings"
msgstr "Үнэлгээнүүд"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Re-generate Export File"
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
msgid "Recipient Bank Account"
msgstr "Хүлээн авах банкны данс"

#. module: account_batch_payment
#: model:ir.model.fields.selection,name:account_batch_payment.selection__account_batch_payment__state__reconciled
msgid "Reconciled"
msgstr "Тулгагдсан"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__name
msgid "Reference"
msgstr "Холбогдол"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__activity_user_id
msgid "Responsible User"
msgstr "Эд хариуцагч"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS илгээлтийн алдаа"

#. module: account_batch_payment
#: model:ir.model.fields.selection,name:account_batch_payment.selection__account_batch_payment__state__sent
msgid "Sent"
msgstr "Илгээгдсэн"

#. module: account_batch_payment
#. odoo-python
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
msgid "Set payments state to \"In Process\"."
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.batch_error_wizard_line_tree
msgid "Show"
msgstr "Үзүүлэх"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__show_remove_button
msgid "Show Remove Button"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard__show_remove_options
msgid "Show Remove Options"
msgstr ""

#. module: account_batch_payment
#. odoo-python
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
msgid "Some payments have already been matched with a bank statement."
msgstr ""

#. module: account_batch_payment
#. odoo-python
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
msgid "Some payments have already been sent."
msgstr ""

#. module: account_batch_payment
#. odoo-python
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
msgid "Some recipient accounts do not allow out payments."
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__state
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_search
msgid "State"
msgstr "Төлөв"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Ажилбаруудын төлөв байдал\n"
"Хоцорсон: Гүйцэтгэх огноо нь аль хэдий нь өнгөрсөн\n"
"Өнөөдөр: Өнөөдөр гүйцэтгэх ёстой\n"
"Төлөвлөгдсөн: Ирээдүйд гүйцэтгэх ажилбарууд"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
msgid "TOTAL"
msgstr "НИЙТ"

#. module: account_batch_payment
#. odoo-python
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
msgid ""
"Target another recipient account or allow sending money to the current one."
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.batch_error_wizard_form
msgid "The batch cannot be validated."
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.batch_error_wizard_form
msgid "The batch could not be validated"
msgstr ""

#. module: account_batch_payment
#. odoo-python
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
msgid ""
"The batch must have the same payment method as the payments it contains."
msgstr ""
"Багцад агуулагдаж буй төлбөрүүд бүгд ижил төлбөрийн аргатай байх ёстой."

#. module: account_batch_payment
#. odoo-python
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
msgid "The batch must have the same type as the payments it contains."
msgstr ""
"Багцад агуулагдаж буй төлбөрүүд бүгд ижил төлбөрийн төрөлтэй байх ёстой."

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_error_wizard__batch_payment_id
msgid ""
"The batch payment generating the errors and warnings displayed in this "
"wizard."
msgstr ""

#. module: account_batch_payment
#. odoo-python
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
msgid ""
"The journal of the batch payment and of the payments it contains must be the"
" same."
msgstr "Багцад агуулагдаж буй төлбөрүүд бүгд ижил журналтай байх ёстой."

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__payment_method_id
msgid "The payment method used by the payments in this batch."
msgstr "Энэ багцын төлбөрүүдэд хэрэглэгдэх төлбөрийн арга."

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Total"
msgstr "Нийт дүн"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_error_wizard__show_remove_options
msgid ""
"True if and only if the options to remove the payments causing the errors or"
" warnings from the batch should be shown"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Бичлэг дээрх асуудал бүхий ажилбарын төрөл"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_search
msgid "Unreconciled"
msgstr "Тулгагдаагүй"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Validate"
msgstr "Зөвшөөрөх"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
msgid "Vendor"
msgstr "Нийлүүлэгч"

#. module: account_batch_payment
#: model:ir.actions.act_window,name:account_batch_payment.action_batch_payment_out
msgid "Vendor Batch Payments"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard__warning_line_ids
msgid "Warning Line"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__warning_wizard_id
msgid "Warning Wizard"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__website_message_ids
msgid "Website Messages"
msgstr "Вебсайтын зурвас"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__website_message_ids
msgid "Website communication history"
msgstr "Вебсайтын харилцааны түүх"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__file_generation_enabled
msgid ""
"Whether or not this batch payment should display the 'Generate File' button "
"instead of 'Print' in form view."
msgstr ""
"Энэ багцын дэлгэц дээр 'Хэвлэх' товчын оронд 'Файл боловсруулах' товч "
"харуулах эсэхийг илтгэнэ."

#. module: account_batch_payment
#. odoo-python
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
msgid "You cannot add payments that are already sent."
msgstr ""

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
msgid "You cannot add payments that are not posted."
msgstr ""

#. module: account_batch_payment
#. odoo-python
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
msgid "You cannot add payments with zero amount in a Batch Payment."
msgstr ""

#. module: account_batch_payment
#. odoo-python
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
msgid "You cannot add the same payment to multiple batches."
msgstr ""

#. module: account_batch_payment
#. odoo-python
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
msgid ""
"You cannot create a batch with payments that are already in another batch."
msgstr ""

#. module: account_batch_payment
#. odoo-python
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
msgid "You cannot create batches with overlapping payments."
msgstr ""
