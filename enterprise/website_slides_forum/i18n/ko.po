# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_slides_forum
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid "(The above section was adapted from Stackoverflow’s FAQ.)"
msgstr "(위의 섹션은 Stackoverflow의 FAQ에서 수정되었습니다.)"

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid ""
"<b>Answers should not add or expand questions</b>. Instead either edit the "
"question or add a question comment."
msgstr "<b>답변은 질문을 추가하거나 확장해서는 안됩니다</b>. 대신 질문을 편집하거나 질문 주석을 추가하십시오."

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid ""
"<b>Answers should not add or expand questions</b>. Instead, either edit the "
"question or add a comment."
msgstr "<b>답변에는 질문을 추가하거나 확장할 수 없습니다</b>. 대신 질문을 수정하거나 의견을 추가할 수 있습니다."

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid ""
"<b>Answers should not comment other answers</b>. Instead add a comment on "
"the other answers."
msgstr "<b>답변은 다른 답변을 언급해서는 안됩니다</b>. 대신 다른 답변에 의견을 추가하십시오."

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid ""
"<b>Answers should not start debates</b> This community Q&amp;A is not a "
"discussion group. Please avoid holding debates in your answers as they tend "
"to dilute the essence of questions and answers. For brief discussions please"
" use commenting facility."
msgstr ""
"<b>답변은 토론을 시작해서는 안됩니다.</b> 이 커뮤니티 Q&amp;A는 토론 그룹이 아닙니다. 질문과 답변의 본질을 희석시키는 "
"경향이 있으므로 답변에 토론을 포함시키지 마십시오. 간단한 토론을 하려면 주석 기능을 사용하십시오."

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid ""
"<b>Answers shouldn't just point to other Questions</b>. Instead add a "
"question comment indication \"Possible duplicate of...\". However, it's ok "
"to include links to other questions or answers providing relevant additional"
" information."
msgstr ""
"<b>답변이 다른 질문을 지목해서는 안됩니다</b>. 대신 \"가능한 복제 가능 항목...\"이라는 질문 주석 표시를 추가하십시오. "
"그러나 관련 추가 정보를 제공하는 다른 질문이나 답변에 대한 링크를 포함해도 됩니다."

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid ""
"<b>Answers shouldn't just point to other questions</b>.Instead add a comment"
" indicating <i>\"Possible duplicate of...\"</i>. However, it's fine to "
"include links to other questions or answers providing relevant additional "
"information."
msgstr ""
"<b>답변은 다른 질문을 지목해서는 안됩니다</b>. 대신 <i>\"가능한 복제 가능 항목...\"</i>이라는 질문 주석 표시를 "
"추가하십시오. 그러나 관련 추가 정보를 제공하는 다른 질문이나 답변에 대한 링크를 포함해도됩니다."

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid ""
"<b>Answers shouldn't just provide a link a solution</b>. Instead provide the"
" solution description text in your answer, even if it's just a copy/paste. "
"Links are welcome, but should be complementary to answer, referring sources "
"or additional reading."
msgstr ""
"<b>답변은 단지 솔루션 링크를 제공해서는 안됩니다</b>. 대신 복사/붙여넣기인 경우에도 답에 솔루션 설명 텍스트를 제공하십시오. "
"링크는 환영하지만 답변, 추천 소스 또는 추가 자료를 보완하려면 보완 적이어야합니다."

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid ""
"<b>Before you ask - please make sure to search for a similar question.</b> "
"You can search questions by their title or tags. It’s also OK to answer your"
" own question."
msgstr ""
"<b>문의하기 전에 유사한 질문을 검색하십시오.</b> 제목이나 태그별로 질문을 검색할 수 있습니다. 자신의 질문에 대답해도 됩니다."

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid ""
"<b>Please avoid asking questions that are too subjective and "
"argumentative</b> or not relevant to this community."
msgstr "이 커뮤니티와 관련이 없거나 <b>너무 주관적이고 논쟁적인 질문은 피하십시오.</b>"

#. module: website_slides_forum
#: model_terms:ir.ui.view,arch_db:website_slides_forum.slide_fullscreen
msgid ""
"<i class=\"fa fa-comments\"/><span class=\"ms-1 d-none d-md-inline-"
"block\">Forum</span>"
msgstr ""
"<i class=\"fa fa-comments\"/><span class=\"ms-1 d-none d-md-inline-"
"block\">게시판</span>"

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid "<span class=\"flex-grow-1\">What kind of questions can I ask here?</span>"
msgstr "<span class=\"flex-grow-1\">여기에서 어떤 질문을 할 수 있나요?</span>"

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid "<span class=\"flex-grow-1\">What should I avoid in my answers?</span>"
msgstr "<span class=\"flex-grow-1\">답변에서 피해야 할 사항은 무엇인가요?</span>"

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid "<span class=\"flex-grow-1\">What should I avoid in my questions?</span>"
msgstr "<span class=\"flex-grow-1\">질문에서 피해야 할 사항은 무엇인가요?</span>"

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid ""
"<span class=\"flex-grow-1\">Why can other people edit my "
"questions/answers?</span>"
msgstr "<span class=\"flex-grow-1\">다른 사람이 내 질문/답변을 수정할 수 있는 이유는 무엇인가요?</span>"

#. module: website_slides_forum
#: model_terms:ir.ui.view,arch_db:website_slides_forum.website_slides_forum_breadcrumb
msgid "<strong>Forum</strong>"
msgstr "<strong>게시판</strong>"

#. module: website_slides_forum
#: model:forum.forum,name:website_slides_forum.forum_forum_demo_channel_0
msgid "Basics of Gardening"
msgstr "원예의 기초"

#. module: website_slides_forum
#: model_terms:ir.ui.view,arch_db:website_slides_forum.forum_all_all_entries
msgid "Check out our courses <i class=\"fa fa-long-arrow-right\"/>"
msgstr "<i class=\"fa fa-long-arrow-right\"/> 강좌 확인하기"

#. module: website_slides_forum
#: model_terms:ir.actions.act_window,help:website_slides_forum.forum_post_action_channel
msgid "Come back later to monitor and moderate what is posted on your Forums."
msgstr " 나중에 다시 게시판에 올라온 내용을 확인하고 관리해주세요."

#. module: website_slides_forum
#: model:ir.model,name:website_slides_forum.model_slide_channel
#: model:ir.model.fields,field_description:website_slides_forum.field_forum_forum__slide_channel_id
#: model_terms:ir.ui.view,arch_db:website_slides_forum.forum_all_all_entries
msgid "Course"
msgstr "강좌"

#. module: website_slides_forum
#: model:ir.model.fields,field_description:website_slides_forum.field_slide_channel__forum_id
msgid "Course Forum"
msgstr "강좌 게시판"

#. module: website_slides_forum
#: model:ir.model.fields,field_description:website_slides_forum.field_forum_forum__slide_channel_ids
msgid "Courses"
msgstr "강좌"

#. module: website_slides_forum
#: model_terms:ir.actions.act_window,help:website_slides_forum.forum_forum_action_channel
msgid "Create a Forum"
msgstr "게시판 만들기"

#. module: website_slides_forum
#: model_terms:forum.forum,welcome_message:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,welcome_message:website_slides_forum.forum_forum_demo_channel_2
msgid "Dismiss"
msgstr "해산"

#. module: website_slides_forum
#: model_terms:forum.forum,welcome_message:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,welcome_message:website_slides_forum.forum_forum_demo_channel_2
msgid "Dismiss message"
msgstr "메시지 삭제"

#. module: website_slides_forum
#: model:ir.model.fields,help:website_slides_forum.field_forum_forum__slide_channel_ids
msgid "Edit the course linked to this forum on the course form."
msgstr "강좌 서식에서 이 게시판에 연결된 강좌를 수정합니다"

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid ""
"For example, if you ask an interesting question or give a helpful answer, "
"your input will be upvoted. On the other hand if the answer is misleading - "
"it will be downvoted. Each vote in favor will generate 10 points, each vote "
"against will subtract 2 points. There is a limit of 200 points that can be "
"accumulated for a question or answer per day. The table given at the end "
"explains reputation point requirements for each type of moderation task."
msgstr ""
"예를 들어, 흥미로운 질문이나 유용한 답변을 제공하면 해당 의견은 찬성투표를 받게 됩니다. 반면 답변에 오해의 소지가 있는 경우, 해당 "
"답변은 반대투표를 받게 됩니다. 찬성투표를 받을 때마다 10점을 획득하게 되며, 반대투표를 받을 때마다 2점이 차감됩니다. 각각의 질문 "
"또는 답변에 대해 누적되는 포인트는 하루 총 200점으로 제한되어 있습니다. 마지막에 제시된 표에서 각 유형에 대한 점수 요건을 확인할 "
"수 있습니다."

#. module: website_slides_forum
#: model:ir.model,name:website_slides_forum.model_forum_forum
#: model:ir.ui.menu,name:website_slides_forum.website_slides_menu_forum
#: model_terms:ir.ui.view,arch_db:website_slides_forum.course_main
#: model_terms:ir.ui.view,arch_db:website_slides_forum.slide_fullscreen
#: model_terms:ir.ui.view,arch_db:website_slides_forum.website_slides_forum_breadcrumb
#: model_terms:ir.ui.view,arch_db:website_slides_forum.website_slides_forum_channel_inherit_view_form
msgid "Forum"
msgstr "게시판"

#. module: website_slides_forum
#: model_terms:ir.ui.view,arch_db:website_slides_forum.snippet_options
msgid "Forum Page"
msgstr "게시판 페이지"

#. module: website_slides_forum
#: model:ir.actions.act_window,name:website_slides_forum.forum_post_action_channel
#: model_terms:ir.ui.view,arch_db:website_slides_forum.website_slides_forum_channel_inherit_view_form
msgid "Forum Posts"
msgstr "게시물"

#. module: website_slides_forum
#: model:ir.model.fields,help:website_slides_forum.field_forum_forum__visibility
msgid ""
"Forum linked to a Course, the visibility is the one applied on the course."
msgstr "강좌와 연결된 게시판은 강좌에 적용된 가시성과 동일하게 적용됩니다."

#. module: website_slides_forum
#: model:ir.actions.act_window,name:website_slides_forum.forum_forum_action_channel
#: model:ir.ui.menu,name:website_slides_forum.website_slides_menu_forum_forum
msgid "Forums"
msgstr "게시판"

#. module: website_slides_forum
#: model_terms:ir.actions.act_window,help:website_slides_forum.forum_forum_action_channel
msgid "Forums allow your attendees to ask questions to your community."
msgstr "참석자들은 게시판을 활용하여 커뮤니티에 질문을 남길 수 있습니다."

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid "Here a table with the privileges and the karma level"
msgstr "다음은 권한과 카르마 레벨이 표시된 표입니다"

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid "If this approach is not for you, please respect the community."
msgstr "이 방법이 적합하지 않은 경우에도 커뮤니티를 존중해 주세요."

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid ""
"If you fit in one of these example or if your motivation for asking the "
"question is “I would like to participate in a discussion about ______”, then"
" you should not be asking here but on our mailing lists. However, if your "
"motivation is “I would like others to explain ______ to me”, then you are "
"probably OK."
msgstr ""
"만약 당신이 이러한 예시들 중 하나에 들어맞거나 질문을 한 동기가 “저는 ______에 대한 토론에 참여하고 싶습니다”라면, 당신은 "
"여기서가 아니라 우리의 메일링 리스트에 있어야 합니다. 하지만, 만약 여러분의 동기가 “다른 사람들이 나에게 ______를 설명해주었으면"
" 좋겠어요”라면, 여러분은 아마 괜찮을 거예요."

#. module: website_slides_forum
#: model:ir.model.fields,field_description:website_slides_forum.field_forum_forum__image_1920
msgid "Image"
msgstr "이미지"

#. module: website_slides_forum
#: model_terms:ir.ui.view,arch_db:website_slides_forum.res_config_settings_view_form
msgid "Manage Forums"
msgstr "게시판 관리"

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid "More over:"
msgstr "추가 :"

#. module: website_slides_forum
#: model_terms:ir.actions.act_window,help:website_slides_forum.forum_post_action_channel
msgid "No Forum Post yet!"
msgstr "아직 게시판에 게시물이 없습니다!"

#. module: website_slides_forum
#: model:ir.model.fields,field_description:website_slides_forum.field_slide_channel__forum_total_posts
msgid "Number of active forum posts"
msgstr "활성 게시물 수"

#. module: website_slides_forum
#: model:ir.model.constraint,message:website_slides_forum.constraint_slide_channel_forum_uniq
msgid "Only one course per forum!"
msgstr "게시판당 하나의 강좌만 연결할 수 있습니다!"

#. module: website_slides_forum
#: model:ir.ui.menu,name:website_slides_forum.website_slides_menu_forum_post
msgid "Posts"
msgstr "게시물"

#. module: website_slides_forum
#: model_terms:ir.ui.view,arch_db:website_slides_forum.snippet_options
msgid "Separate Courses"
msgstr "개별 강좌"

#. module: website_slides_forum
#: model_terms:forum.forum,welcome_message:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,welcome_message:website_slides_forum.forum_forum_demo_channel_2
msgid ""
"Share and discuss the best content and new marketing ideas, build your "
"professional profile and become a better marketer together."
msgstr ""
"최고의 콘텐츠와 새로운 마케팅 아이디어를 공유하고 토론하며 전문적인 프로필을 쌓아 더 나은 마케터가 될 수 있도록 함께 노력할 수 "
"있습니다."

#. module: website_slides_forum
#: model:ir.model.fields,field_description:website_slides_forum.field_forum_forum__visibility
msgid "Show Course To"
msgstr "강좌 보기 대상"

#. module: website_slides_forum
#: model_terms:forum.forum,welcome_message:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,welcome_message:website_slides_forum.forum_forum_demo_channel_2
msgid "Sign up"
msgstr "가입"

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid ""
"The goal of this site is create a relevant knowledge base that would answer "
"questions related to Odoo."
msgstr "이 사이트의 목표는 Odoo와 관련된 질문에 답변 할 수 있는 관련 기술 자료를 만드는 것입니다."

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid ""
"Therefore questions and answers can be edited like wiki pages by experienced"
" users of this site in order to improve the overall quality of the knowledge"
" base content. Such privileges are granted based on user karma level: you "
"will be able to do the same once your karma gets high enough."
msgstr ""
"따라서 본 사이트의 숙련된 사용자가 질문과 답변을 위키 페이지처럼 편집하여 전반적인 기술 자료 내용을 개선할 수 있습니다. 이러한 권한은"
" 사용자의 명성 수준에 따라 부여됩니다. 명성이 충분히 높아지면 동일한 작업을 수행할 수 있습니다."

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid ""
"This community is for professional and enthusiast users, partners and "
"programmers. You can ask questions about:"
msgstr ""
"이 커뮤니티는 전문적이며 열정적인 사용자들과 협력사, 그리고 프로그래머들을 위해 마련되었습니다. 다음 내용에 대해 질문이 가능합니다:"

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid ""
"To prevent your question from being flagged and possibly removed, avoid "
"asking subjective questions where …"
msgstr "질문이 신고되거나 제거되지 않도록 하려면 여기에선 주관적인 질문을 하지 마십시오..."

#. module: website_slides_forum
#: model:forum.forum,name:website_slides_forum.forum_forum_demo_channel_2
msgid "Trees, Wood and Gardens"
msgstr "나무, 숲 그리고 정원"

#. module: website_slides_forum
#: model_terms:forum.forum,welcome_message:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,welcome_message:website_slides_forum.forum_forum_demo_channel_2
msgid "Welcome!"
msgstr "환영합니다!"

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid ""
"When a question or answer is upvoted, the user who posted them will gain "
"some points, which are called \"karma points\". These points serve as a "
"rough measure of the community trust to him/her. Various moderation tasks "
"are gradually assigned to the users based on those points."
msgstr ""
"질문이나 답변이 상향 조정되면 이를 게시한 사용자가 어느 정도 점수를 얻게 되는데, 이를 \"명성 포인트\"라고 합니다. 이러한 점들은 "
"그/그녀에 대한 지역사회의 신뢰의 대략적인 척도로 작용합니다. 이러한 점에 따라 다양한 조정 작업이 점차 사용자에게 할당됩니다."

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid ""
"You should only ask practical, answerable questions based on actual problems"
" that you face. Chatty, open-ended questions diminish the usefulness of this"
" site and push other questions off the front page."
msgstr ""
"여러분은 여러분이 직면하고 있는 실제 문제들에 근거해서만 실용적이고 대답 가능한 질문을 해야 합니다. 수다스럽고 열린 질문은 이 사이트의"
" 유용성을 떨어뜨리고 다른 질문들을 첫페이지에서 밀어냅니다."

#. module: website_slides_forum
#: model_terms:ir.ui.view,arch_db:website_slides_forum.forum_forum_view_form
msgid "eLearning"
msgstr "온라인 학습 관리"

#. module: website_slides_forum
#: model_terms:ir.ui.view,arch_db:website_slides_forum.forum_post_view_graph_slides
msgid "eLearning Forum Posts"
msgstr "온라인 학습 게시물"

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid "every answer is equally valid: “What’s your favorite ______?”"
msgstr "모든 대답은 똑같이 유효합니다 : “가장 좋아하는 ______?”"

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid "how to configure or customize Odoo to specific business needs,"
msgstr "특정 비즈니스 요구에 맞게 Odoo를 구성하거나 사용자 정의하는 방법"

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid "how to develop modules for your own need,"
msgstr "자신의 필요에 맞는 모듈을 개발하는 방법"

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid "how to install Odoo on a specific infrastructure,"
msgstr "특정 인프라에 Odoo를 설치하는 방법"

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid "it is a rant disguised as a question: “______ sucks, am I right?”"
msgstr "그것은 질문으로 위장한 소리입니다 : “______ 정말 형편없죠?”"

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid "specific questions about Odoo service offers, etc."
msgstr "Odoo 서비스 제공 등에 대한 특정 질문 등."

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid ""
"there is no actual problem to be solved: “I’m curious if other people feel "
"like I do.”"
msgstr "실제로 해결해야 할 문제는 없습니다 : “다른 사람들이 저처럼 느끼는 지 궁금해요.”"

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid ""
"we are being asked an open-ended, hypothetical question: “What if ______ "
"happened?”"
msgstr "우리는 개방형의 가상의 질문을 받습니다 : “______가 발생하면 어떻게 됩니까?”"

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid "what's the best way to use Odoo for a specific business need,"
msgstr "특정 비즈니스 요구에 Odoo를 사용하는 가장 좋은 방법은 무엇입니까?"

#. module: website_slides_forum
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_0
#: model_terms:forum.forum,faq:website_slides_forum.forum_forum_demo_channel_2
msgid ""
"your answer is provided along with the question, and you expect more "
"answers: “I use ______ for ______, what do you use?”"
msgstr ""
"귀하의 답변은 질문과 함께 제공되며 더 많은 답변을 기대합니다. : “______에는 ______를 사용합니다. 무엇을 사용하십니까?”"
