# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_bank_statement_import_camt
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:26+0000\n"
"PO-Revision-Date: 2024-09-25 09:43+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Azerbaijani (https://app.transifex.com/odoo/teams/41243/az/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: az\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "ACH Concentration"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "ACH Corporate Trade"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "ACH Credit"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "ACH Debit"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "ACH Pre-Authorised"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "ACH Return"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "ACH Reversal"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "ACH Settlement"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "ACH Transaction"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "ARP Debit"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Account Balancing"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Account Closing"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Account Management"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Account Opening"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Account Transfer"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/models/account_journal.py:0
msgid "Additional Info: %s"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Additional Miscellaneous Credit Operations"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Additional Miscellaneous Debit Operations"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/models/account_journal.py:0
msgid ""
"Address:\n"
"%s"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Adjustments"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Automatic Transfer"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Back Value"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Bank Cheque"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Bank Fees"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Blocked Transactions"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Bonus Issue/Capitalisation Issue"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Borrowing fee"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Branch Account Transfer"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Branch Deposit"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Branch Withdrawal"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Brokerage fee"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Buy Sell Back"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "CSD Blocked Transactions"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Call on intermediate securities"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Capital Gains Distribution"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Cash Deposit"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Cash Dividend"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Cash Letter"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Cash Letter Adjustment"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Cash Management"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Cash Pooling"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Cash Withdrawal"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Cash in lieu"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Certified Customer Cheque"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Charge/fees"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Charges"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/models/account_journal.py:0
msgid "Check Number: %s"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Cheque"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Cheque Deposit"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Cheque Reversal"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Cheque Under Reserve"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Circular Cheque"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Clean Collection"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Client Owned Collateral"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Collateral Management"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Commission"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Commission excluding taxes"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Commission including taxes"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Commodities"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Compensation/Claims"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Consumer Loans"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Controlled Disbursement"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Conversion"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Corporate Action"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Corporate Own Account Transfer"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Corporate Rebate"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Corporate mark broker owned"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Corporate mark client owned"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/models/account_journal.py:0
msgid "Counter Party: %(partner)s"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Counter Transactions"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Credit Adjustment"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Credit Adjustments"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Credit Card Payment"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Credit Transfer with agreed Commercial Information"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Cross Trade"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Cross-Border"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Cross-Border Cash Withdrawal"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Cross-Border Credit Card Payment"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Cross-Border Credit Transfer"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Cross-Border Direct Debit"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Cross-Border Intra Company Transfer"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Cross-Border Payroll/Salary Payment"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Cross-Border Standing Order"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Crossed Cheque"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Custody"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Custody Collection"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Customer Card Transactions"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Debit"
msgstr "Debet"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Debit Adjustments"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Decrease in Value"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Delivery"
msgstr "Təslimat"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Deposit"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Deposit/Contribution"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Depositary Receipt Issue"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Derivatives"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Direct Debit"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Direct Debit Payment"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Direct Debit under reserve"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Discounted Draft"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Dishonoured/Unpaid Draft"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Dividend Option"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Dividend Reinvestment"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Documentary Collection"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Documentary Credit"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Domestic Credit Transfer"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Draft Maturity Change"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Drafts/BillOfOrders"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Drawdown"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Drawing"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Dutch Auction"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/models/account_journal.py:0
msgid "End to end ID: %s"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/models/account_journal.py:0
msgid "Entry Info: %s"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Equity Premium Reserve"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Equity mark broker owned"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Equity mark client owned"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Exchange"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Exchange Rate Adjustment"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Exchange Traded"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Exchange Traded CCP"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Exchange Traded Non-CCP"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Extended Domain"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "External Account Transfer"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Factor Update"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Fees"
msgstr "Ödənişlər"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Fees, Commission , Taxes, Charges and Interest"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Final Maturity"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Final Payment"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Financial Institution Credit Transfer"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Financial Institution Direct Debit Payment"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Financial Institution Own Account Transfer"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Fixed Deposit Interest Amount"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Fixed Term Deposits"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Fixed Term Loans"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Float adjustment"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Foreign Cheque"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Foreign Cheque Under Reserve"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Foreign Currency Deposit"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Foreign Currency Withdrawal"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Foreign Exchange"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Forwards"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Forwards broker owned collateral"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Forwards client owned collateral"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Freeze of funds"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Full Call / Early Redemption"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Future Variation Margin"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Futures"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Futures Commission"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Futures Residual Amount"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Guarantees"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Inspeci/Share Exchange"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/models/account_journal.py:0
msgid "Instruction ID: %s"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Interest"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Interest Payment"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Interest Payment with Principle"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Internal Account Transfer"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Internal Book Transfer"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Intra Company Transfer"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Invoice Accepted with Differed Due Date"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Issued Cash Concentration Transactions"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Issued Cheques"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Issued Credit Transfers"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Issued Direct Debits"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Issued Real Time Credit Transfer"
msgstr ""

#. module: account_bank_statement_import_camt
#: model:ir.model,name:account_bank_statement_import_camt.model_account_journal
msgid "Journal"
msgstr "Jurnal"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Lack"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Lending Broker Owned Cash Collateral"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Lending Client Owned Cash Collateral"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Lending income"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Liquidation Dividend / Liquidation Payment"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Listed Derivatives – Futures"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Listed Derivatives – Options"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Loans, Deposits & Syndications"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Lockbox Transactions"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Management Fees"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/models/account_journal.py:0
msgid "Mandate ID: %s"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Margin Payments"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Margin client owned cash collateral"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Merchant Card Transactions"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Merger"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Miscellaneous Credit Operations"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Miscellaneous Debit Operations"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Miscellaneous Deposit"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Miscellaneous Securities Operations"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Mixed Deposit"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Mortgage Loans"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Netting"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid ""
"No exchange rate was found to convert an amount into the currency of the "
"journal"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Non Deliverable"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Non Settled"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Non Syndicated"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Non Taxable commissions"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Non-Presented Circular Cheque"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Not available"
msgstr "Mövcud deyil"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Notice Deposits"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Notice Loans"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "OTC"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "OTC CCP"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "OTC Derivatives – Bonds"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "OTC Derivatives – Credit Derivatives"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "OTC Derivatives – Equity"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "OTC Derivatives – Interest Rates"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "OTC Derivatives – Structured Exotic Derivatives"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "OTC Derivatives – Swaps"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "OTC Non-CCP"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Odd Lot Sale/Purchase"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "One-Off Direct Debit"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Open Cheque"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Opening & Closing"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Option broker owned collateral"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Option client owned collateral"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Options"
msgstr "Opsionlar"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Order Cheque"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Other"
msgstr "Digər"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Overdraft"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Overdraft Charge"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Pair-Off"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Partial Payment"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Partial Redemption Without Reduction of Nominal Value"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Partial Redemption with reduction of nominal value"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Payments"
msgstr "Ödənişlər"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Payroll/Salary Payment"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Placement"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/models/account_journal.py:0
msgid ""
"Please check the currency on your bank journal.\n"
"No statements in currency %s were found in this CAMT file."
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/models/account_journal.py:0
msgid ""
"Please set the IBAN account on your bank journal.\n"
"\n"
"This CAMT file is targeting several IBAN accounts but none match the current journal."
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Point-of-Sale (POS) Payment"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Point-of-Sale (POS) Payment - Debit Card"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Portfolio Move"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Posting Error"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Pre-Authorised Direct Debit"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Precious Metal"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Principal Pay-down/pay-up"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Principal Payment"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Priority Credit Transfer"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Priority Issue"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Put Redemption"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Received Cash Concentration Transactions"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Received Cheques"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Received Credit Transfers"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Received Direct Debits"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Received Real Time Credit Transfer"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Redemption"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Redemption Asset Allocation"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Redemption Withdrawing Plan"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Reimbursements"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Renewal"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Repayment"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Repo"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Repurchase offer/Issuer Bid/Reverse Rights."
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Reset Payment"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Reversal due to Payment Cancellation Request"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Reversal due to Payment Return/reimbursement of a Credit Transfer"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Reversal due to Payment Reversal"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Reversal due to Return/Unpaid Direct Debit"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Reversal due to a Payment Cancellation Request"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Reverse Repo"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Rights Issue/Subscription Rights/Rights Offer"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "SEPA B2B Direct Debit"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "SEPA Core Direct Debit"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "SEPA Credit Transfer"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Same Day Value Credit Transfer"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Securities"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Securities Borrowing"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Securities Lending"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Sell Buy Back"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Settlement"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Settlement after collection"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Settlement against bank guarantee"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Settlement at Maturity"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Settlement of Sight Export document"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Settlement of Sight Import document"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Settlement under reserve"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Smart-Card Payment"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Spots"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Stamp duty"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Stand-By Letter Of Credit"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Standing Order"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Subscription"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Subscription Asset Allocation"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Subscription Savings Plan"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Swap Payment"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Swap broker owned collateral"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Swaps"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Sweep"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Sweeping"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Switch"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Syndicated"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Syndications"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "TBA closing"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Tax Reclaim"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Taxes"
msgstr "Vergilər"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Tender"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Topping"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Trade"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Trade Services"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Trade, Clearing and Settlement"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Transaction Fees"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/models/account_journal.py:0
msgid "Transaction ID: %s"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Transfer In"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Transfer Out"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Travellers Cheques Deposit"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Travellers Cheques Withdrawal"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Treasury Tax And Loan Service"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Triparty Repo"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Triparty Reverse Repo"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Turnaround"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Underwriting Commission"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Unpaid Card Transaction"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Unpaid Cheque"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Unpaid Foreign Cheque"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Upfront Payment"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Value Date"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Warrant Exercise/Warrant Conversion"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Withdrawal/distribution"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Withholding Tax"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "YTD Adjustment"
msgstr ""

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Zero Balancing"
msgstr ""
