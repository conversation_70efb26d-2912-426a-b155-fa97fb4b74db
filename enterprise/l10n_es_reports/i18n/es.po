# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_es_reports
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~17.1+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-03-20 14:49+0000\n"
"PO-Revision-Date: 2023-03-20 14:49+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_es_reports
#: model:ir.model.fields,help:l10n_es_reports.field_res_company__l10n_es_reports_iae_group
msgid ""
"        This field corresponds to the activity to which the entry refers in "
"7 alphanumeric characters.\n"
"\n"
"        For example, in the operations of a hardware store, 'A036533' will "
"be entered, which indicates an operation        carried out by a business "
"activity of a commercial nature subject to the IAE for 'retail trade in "
"household        items, hardware, ornaments.'"
msgstr ""
"        Este campo corresponde a la actividad a la que se refiere la entrada "
"en 7 caracteres alfanuméricos.\n"
"\n"
"        Por ejemplo, en las operaciones de una ferretería, se introducirá "
"'A036533' lo que indica una operación        realizada por una actividad "
"empresarial de carácter comercial sujeta al IAE de 'comercio al por menor de "
"artículos para el hogar, ferretería, adornos.'"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_31210
#: model:account.report.line,name:l10n_es_reports.balance_full_line_32310
msgid "1. Bonds and other marketable securities"
msgstr "1. Obligaciones y otros valores negociables"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_12710
msgid "1. Cash"
msgstr "1. Tesorería"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_assoc_line_31210
#: model:account.report.line,name:l10n_es_reports.balance_assoc_line_32310
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_31220
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_32320
msgid "1. Debt with financial institutions"
msgstr "1. Deudas con entidades de crédito"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_11110
msgid "1. Development"
msgstr "1. Desarrollo"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_11410
#: model:account.report.line,name:l10n_es_reports.balance_full_line_11510
#: model:account.report.line,name:l10n_es_reports.balance_full_line_12410
#: model:account.report.line,name:l10n_es_reports.balance_full_line_12510
msgid "1. Equity instruments"
msgstr "1. Instrumentos de patrimonio"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_assoc_line_21110
msgid "1. Foundation endowment/Social fund"
msgstr "1. Dotacón fundacional/Fondo social"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_12210
msgid "1. Goods for resale"
msgstr "1. Comerciales"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_11210
msgid "1. Land and buildings"
msgstr "1. Terrenos y construcciones"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_11310
msgid "1. Lands"
msgstr "1. Terrenos"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_21310
msgid "1. Legal and statutory reserves"
msgstr "1. Legal y estatutarias"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_31110
msgid "1. Long-term employee benefits"
msgstr "1. Obligaciones por prestaciones a largo plazo al personal"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_21110
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_21110
msgid "1. Registered capital"
msgstr "1. Capital escriturado"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.es_profit_and_loss_line_1
msgid "1. Revenue"
msgstr "1. Ingreso"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_assoc_line_32510
#: model:account.report.line,name:l10n_es_reports.balance_full_line_32510
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_32510
msgid "1. Suppliers"
msgstr "1. Proveedores"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_12310
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_12310
msgid "1. Trade receivables"
msgstr "1. Clientes por ventas y prestaciones de servicios"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.es_profit_and_loss_line_10
msgid "10. Provision surpluses"
msgstr "10. Excesos de provisiones"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.es_profit_and_loss_line_11
msgid "11. Impairment and gains/losses on disposal of fixed assets"
msgstr "11. Deterioro y resultado por enajenaciones del inmovilizado"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.es_profit_and_loss_line_12
msgid "12. Other gains/losses"
msgstr "12. Otros resultados"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.es_profit_and_loss_line_13
msgid "13. Finance income"
msgstr "13. Ingresos financieros"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.es_profit_and_loss_line_14
msgid "14. Finance expenses"
msgstr "14. Gastos financieros"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.es_profit_and_loss_line_15
msgid "15. Variation of fair value of financial instruments"
msgstr "15. Variación de valor razonable en instrumentos financieros"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.es_profit_and_loss_line_16
msgid "16. Exchange gains/losses"
msgstr "16. Diferencias de cambio"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.es_profit_and_loss_line_17
msgid "17. Impairment and gains/losses on disposal of financial instruments"
msgstr ""
"17. Deterioro y resultado por enajenaciones de instrumentos financieros."

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.es_profit_and_loss_line_18
msgid "18. Income tax expense"
msgstr "18. Impuestos sobre beneficios."

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_21120
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_21120
msgid "2. (Uncalled capital)"
msgstr "2. (Capital no exigido)"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_assoc_line_21120
msgid "2. (Uncalled foundation endowment/social fund)"
msgstr "2. (Dotación fundacional no exigida/Fondo social no exigido)"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_11320
msgid "2. Buildings"
msgstr "2. Construcciones"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_12720
msgid "2. Cash equivalents"
msgstr "2. Otros activos líquidos equivalentes"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.es_profit_and_loss_line_2
msgid "2. Changes in inventories of finished goods and work in progress"
msgstr ""
"2. Variación de existencias de productos terminados y en curso de fabricación"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_11120
msgid "2. Concessions"
msgstr "2. Concesiones"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_31220
#: model:account.report.line,name:l10n_es_reports.balance_full_line_32320
msgid "2. Debt with financial institutions"
msgstr "2. Deudas con entidades de crédito"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_31120
msgid "2. Environmental actions"
msgstr "2. Actuaciones medioambientales"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_assoc_line_31220
#: model:account.report.line,name:l10n_es_reports.balance_assoc_line_32220
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_31230
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_32330
msgid "2. Finance lease payables"
msgstr "2. Acreedores por arrendamiento financiero"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_11420
#: model:account.report.line,name:l10n_es_reports.balance_full_line_12420
#: model:account.report.line,name:l10n_es_reports.balance_full_line_12520
msgid "2. Loans to companies"
msgstr "2. Créditos a empresas"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_11520
msgid "2. Loans to third parties"
msgstr "2. Créditos a terceros"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_assoc_line_32580
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_32580
msgid "2. Other payables"
msgstr "2. Otros acreedores"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_21320
msgid "2. Other reserves"
msgstr "2. Otras reservas"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_12220
msgid "2. Raw materials and other supplies"
msgstr "2. Materias primas y otros aprovisionamientos"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_12370
msgid "2. Receivable on called-up share capital"
msgstr "2. Accionistas (socios) por desembolsos exigidos"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_32520
msgid "2. Suppliers, group companies and associates"
msgstr "2. Proveedores, empresas del grupo y asociadas"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_11220
msgid "2. Technical installations and other items"
msgstr "2. Instalaciones técnicas y otro inmovilizado material"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_12320
msgid "2. Trade receivables from group companies and associates"
msgstr "2. Clientes empresas del grupo y asociadas"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_11430
#: model:account.report.line,name:l10n_es_reports.balance_full_line_11530
#: model:account.report.line,name:l10n_es_reports.balance_full_line_12430
#: model:account.report.line,name:l10n_es_reports.balance_full_line_12530
msgid "3. Debt securities"
msgstr "3. Valores representativos de deuda"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_31230
#: model:account.report.line,name:l10n_es_reports.balance_full_line_32330
msgid "3. Finance lease payables"
msgstr "3. Acreedores por arrendamiento financiero"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_assoc_line_32230
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_32390
msgid "3. Other current payables"
msgstr "3. Otras deudas a corto plazo"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_assoc_line_31230
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_31290
msgid "3. Other non-current payables"
msgstr "3. Otras deudas a largo plazo"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_32530
msgid "3. Other payables"
msgstr "3. Acreedores varios"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_12330
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_12390
msgid "3. Other receivables"
msgstr "3. Deudores varios"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_11130
msgid "3. Patents, licences, trademarks and similar rights"
msgstr "3. Patentes, licencias, marcas y similares"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_31130
msgid "3. Restructuring costs"
msgstr "3. Provisiones por reestructuración"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_11230
msgid "3. Under construction and advances"
msgstr "3. Inmovilizado en curso y anticipos"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.es_profit_and_loss_line_3
msgid "3. Work carried out by the company for assets"
msgstr "3. Trabajos realizados por la empresa para su activo."

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_12230
msgid "3. Work in progress"
msgstr "3. Productos en curso"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_11440
#: model:account.report.line,name:l10n_es_reports.balance_full_line_11540
#: model:account.report.line,name:l10n_es_reports.balance_full_line_12440
#: model:account.report.line,name:l10n_es_reports.balance_full_line_12540
#: model:account.report.line,name:l10n_es_reports.balance_full_line_31240
#: model:account.report.line,name:l10n_es_reports.balance_full_line_32340
msgid "4. Derivatives"
msgstr "4. Derivados"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_12240
msgid "4. Finished goods"
msgstr "4. Productos terminados"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_11140
msgid "4. Goodwill"
msgstr "4. Fondo de comercio"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_31140
msgid "4. Other provisions"
msgstr "4. Otras provisiones"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_12340
msgid "4. Personnel"
msgstr "4. Personal"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_32540
msgid "4. Personnel (salaries payables)"
msgstr "4. Personal (remuneraciones pendientes de pago)"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.es_profit_and_loss_line_4
msgid "4. Supplies"
msgstr "4. Aprovisionamientos"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_12250
msgid "5. By-products, waste and recovered materials"
msgstr "5. Subproductos, residuos y materiales recuperados"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_11150
msgid "5. Computer software"
msgstr "5. Aplicaciones informáticas"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_12350
msgid "5. Current tax assets"
msgstr "5. Activos por impuesto corriente"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_32550
msgid "5. Current tax liabilities"
msgstr "5. Pasivos por impuesto corriente"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_11450
#: model:account.report.line,name:l10n_es_reports.balance_full_line_11550
#: model:account.report.line,name:l10n_es_reports.balance_full_line_12450
#: model:account.report.line,name:l10n_es_reports.balance_full_line_12550
msgid "5. Other financial assets"
msgstr "5. Otros activos financieros"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_31250
#: model:account.report.line,name:l10n_es_reports.balance_full_line_32350
msgid "5. Other financial liabilities"
msgstr "5. Otros pasivos financieros"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.es_profit_and_loss_line_5
msgid "5. Other operating income"
msgstr "5. Otros ingresos de explotación"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_12260
msgid "6. Advances to suppliers"
msgstr "6. Anticipos a proveedores"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_11460
#: model:account.report.line,name:l10n_es_reports.balance_full_line_11560
#: model:account.report.line,name:l10n_es_reports.balance_full_line_12460
#: model:account.report.line,name:l10n_es_reports.balance_full_line_12560
msgid "6. Other investments"
msgstr "6. Otras inversiones"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.es_profit_and_loss_line_6
msgid "6. Personnel expenses"
msgstr "6. Gastos de personal"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_12360
msgid "6. Public entities, other"
msgstr "6. Otros créditos con las Administraciones Públicas"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_32560
msgid "6. Public entities, other current liabilities"
msgstr "6. Otras deudas con las Administraciones Públicas"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_11160
msgid "6. Research"
msgstr "6. Investigación"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_32570
msgid "7. Advances from customers"
msgstr "7. Anticipos de clientes"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_11170
msgid "7. Other intangible assets"
msgstr "7. Otro inmovilizado intangible"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.es_profit_and_loss_line_7
msgid "7. Other operating expenses"
msgstr "7. Otros gastos de explotación"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_12370
msgid "7. Receivable on called-up capital"
msgstr "7. Accionistas (socios) por desembolsos exigidos"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.es_profit_and_loss_line_8
msgid "8. Amortisation and depreciation"
msgstr "8. Amortización del inmovilizado"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.es_profit_and_loss_line_9
msgid "9. Non-financial and other capital grants"
msgstr "9. Imputación de subvenciones de inmovilizado no financiero y otras"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/account_move.py:0
msgid "A - Acquisition"
msgstr "A - Adquisición"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.mod_347_operations_regular_bought
msgid "A - Purchases of goods and services greater than 3.005,06 €"
msgstr "A - Adquisiciones de bienes y servicios superiores a 3 005,06 €"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_assoc_line_20000
#: model:account.report.line,name:l10n_es_reports.balance_full_line_20000
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_20000
msgid "A) EQUITY"
msgstr "A) PATRIMONIO NETO"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_assoc_line_11000
#: model:account.report.line,name:l10n_es_reports.balance_full_line_11000
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_11000
msgid "A) NON-CURRENT ASSETS"
msgstr "A) ACTIVO NO CORRIENTE"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.es_profit_and_loss_line_sum_a
msgid ""
"A) RESULTS FROM OPERATING ACTIVITIES (1 + 2 + 3 + 4 + 5 + 6 + 7 + 8 + 9 + 10 "
"+ 11 + 12)"
msgstr ""
"A) RESULTADO DE EXPLOTACIÓN (1 + 2 + 3 + 4 + 5 + 6 + 7 + 8 + 9 + 10 + 11 + "
"12)"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_assoc_line_21000
#: model:account.report.line,name:l10n_es_reports.balance_full_line_21000
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_21000
msgid "A-1) Capital and reserves without valuation adjustments"
msgstr "A-1) Fondos propios"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_assoc_line_21500
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_22000
msgid "A-2) Adjustments in equity"
msgstr "A-2) Ajustes en patrimonio neto"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_22000
msgid "A-2) Valuation adjustments"
msgstr "A-2) Ajustes por cambios de valor"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_assoc_line_22000
#: model:account.report.line,name:l10n_es_reports.balance_full_line_23000
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_23000
msgid "A-3) Grants, donations and bequests received"
msgstr "A-3) Subvenciones, donaciones y legados recibidos"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.mod_349_acquisitions
#: model:account.report.line,name:l10n_es_reports.mod_349_acquisitions_refunds
msgid "A. Intra-community purchases subject to taxes"
msgstr "A. Adquisiciones intracomunitarias sujetas"

#. module: l10n_es_reports
#: model_terms:ir.ui.view,arch_db:l10n_es_reports.invoice_form_inherit
msgid "AEAT data"
msgstr "Datos de la AEAT"

#. module: l10n_es_reports
#: model:account.report,name:l10n_es_reports.financial_report_balance_sheet_assoc
msgid "Abbreviated Balance Sheet for Associations"
msgstr "Balance abreviado para asociaciones"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/libro_diario_export.py:0
msgid "Account Code"
msgstr "Código de cuenta"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/libro_diario_export.py:0
msgid "Account Name"
msgstr "Nombre de cuenta"

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_account_report
msgid "Accounting Report"
msgstr "Informe contable"

#. module: l10n_es_reports
#: model:account.account.tag,name:l10n_es_reports.balance_sheet_31600_account
msgid "Acreedores comerciales no corrientes"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_mod347_manual_partner_data__operation_key__a
msgid "Adquisiciones de bienes y servicios"
msgstr ""

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.mod_349_statistics_refunds_total_amount
msgid "Amount of intra-community refund operations"
msgstr "Importe de las operaciones intracomunitarias con rectificaciones"

#. module: l10n_es_reports
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_mod347_manual_partner_data__operation_class__local_negocio
msgid "Arrendamiento Local Negocio"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_bank_statement_line__l10n_es_reports_mod347_available
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_move__l10n_es_reports_mod347_available
msgid "Available for Mod347"
msgstr "Disponible para Mod347"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_bank_statement_line__l10n_es_reports_mod349_available
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_move__l10n_es_reports_mod349_available
msgid "Available for Mod349"
msgstr "Disponible para Mod349"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.mod_347_operations_insurance_bought
#: model:account.report.line,name:l10n_es_reports.mod_347_operations_regular_sold
msgid "B - Sales of goods and services greater than 3.005,06 €"
msgstr ""
"B - Entregas de bienes y prestaciones de servicios superiores a 3 005,06 €"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_assoc_line_12000
#: model:account.report.line,name:l10n_es_reports.balance_full_line_12000
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_12000
msgid "B) CURRENT ASSETS"
msgstr "B) ACTIVO CORRIENTE"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.es_profit_and_loss_line_sum_b
msgid "B) FINANCIAL RESULTS (13 + 14 + 15 + 16 + 17)"
msgstr "B) RESULTADO FINANCIERO (13 + 14 + 15 + 16 + 17)"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_assoc_line_31000
#: model:account.report.line,name:l10n_es_reports.balance_full_line_31000
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_31000
msgid "B) NON-CURRENT LIABILITIES"
msgstr "B) PASIVO NO CORRIENTE"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/aeat_tax_reports.py:0
msgid "BOE"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_reports_aeat_boe_export_wizard
msgid "BOE Export Wizard"
msgstr "Asistente para la exportación del BOE"

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_reports_aeat_boe_mod111_export_wizard
msgid "BOE Export Wizard for (mod111)"
msgstr "Asistente para la exportación del BOE para (mod111)"

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard
msgid "BOE Export Wizard for (mod111, mod115 & 303)"
msgstr "Asistente para la exportación del BOE para (mod111, mod115 & 303)"

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_reports_aeat_boe_mod115_export_wizard
msgid "BOE Export Wizard for (mod115)"
msgstr "Asistente para la exportación del BOE para (mod115)"

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_reports_aeat_boe_mod303_export_wizard
msgid "BOE Export Wizard for (mod303)"
msgstr "Asistente para la exportación del BOE para (mod303)"

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_reports_aeat_boe_mod347and349_export_wizard
msgid "BOE Export Wizard for (mod347 & mod349)"
msgstr "Asistente para la exportación del BOE para (mod347 & mod349)"

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_reports_aeat_boe_mod347_export_wizard
msgid "BOE Export Wizard for (mod347)"
msgstr "Asistente para la exportación del BOE para (mod347)"

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_reports_aeat_boe_mod349_export_wizard
msgid "BOE Export Wizard for (mod349)"
msgstr "Asistente para la exportación del BOE para (mod349)"

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_reports_aeat_boe_mod390_export_wizard
msgid "BOE Export Wizard for (mod390)"
msgstr "Asistente para la exportación del BOE para (mod390)"

#. module: l10n_es_reports
#: model:account.report.column,name:l10n_es_reports.financial_report_balance_sheet_assoc_column
#: model:account.report.column,name:l10n_es_reports.financial_report_balance_sheet_full_column
#: model:account.report.column,name:l10n_es_reports.financial_report_balance_sheet_pymes_column
#: model:account.report.column,name:l10n_es_reports.financial_report_es_profit_and_loss_column
#: model:account.report.column,name:l10n_es_reports.mod_347_column
#: model:account.report.column,name:l10n_es_reports.mod_349_column
msgid "Balance"
msgstr "Saldo"

#. module: l10n_es_reports
#: model:account.report,name:l10n_es_reports.financial_report_balance_sheet_pymes
msgid "Balance Sheet - SMEs"
msgstr "Balance - PyMEs"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__special_cash_basis_beneficiary
msgid "Beneficiary of the special cash regime"
msgstr "Beneficiario del régimen especial de efectivo"

#. module: l10n_es_reports
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod303_export_wizard__declaration_type__c
msgid "C - Compensation Request"
msgstr "C - Solicitud de indemnización"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/account_move.py:0
#: code:addons/l10n_es_reports/models/account_move.py:0
#, python-format
msgid "C - Replacements of goods"
msgstr "C - Sustitución de bienes"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_assoc_line_32000
#: model:account.report.line,name:l10n_es_reports.balance_full_line_32000
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_32000
msgid "C) CURRENT LIABILITIES"
msgstr "C) PASIVO CORRIENTE"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.es_profit_and_loss_line_sum_c
msgid "C) PROFIT/LOSS BEFORE INCOME TAX (A + B)"
msgstr "C) RESULTADO ANTES DE IMPUESTOS (A + B)"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.mod_349_substitutions_refunds
msgid "C. Rectifications for replacement of goods"
msgstr "C. Rectificaciones por sustitución de bienes"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.mod_349_substitutions
msgid "C. Replacements of goods"
msgstr "C. Sustitución de bienes"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_export_wizard__calling_export_wizard_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111_export_wizard__calling_export_wizard_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__calling_export_wizard_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod115_export_wizard__calling_export_wizard_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__calling_export_wizard_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__calling_export_wizard_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__calling_export_wizard_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__calling_export_wizard_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__calling_export_wizard_id
msgid "Calling Export Wizard"
msgstr "Llamada al asistente de exportación"

#. module: l10n_es_reports
#: model_terms:ir.ui.view,arch_db:l10n_es_reports.mod111_boe_wizard
#: model_terms:ir.ui.view,arch_db:l10n_es_reports.mod115_boe_wizard
#: model_terms:ir.ui.view,arch_db:l10n_es_reports.mod303_boe_wizard
#: model_terms:ir.ui.view,arch_db:l10n_es_reports.mod347_boe_wizard
#: model_terms:ir.ui.view,arch_db:l10n_es_reports.mod349_boe_wizard
#: model_terms:ir.ui.view,arch_db:l10n_es_reports.mod390_boe_wizard
msgid "Cancel"
msgstr "Cancelar"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/aeat_tax_reports.py:0
msgid "Cannot generate a BOE file for two different years"
msgstr "No se puede generar un archivo BOE para dos años diferentes"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__cash_basis_mod347_data
msgid "Cash Basis Data"
msgstr "Datos de base de efectivo"

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_res_company
msgid "Companies"
msgstr "Compañías"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111_export_wizard__company_partner_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__company_partner_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod115_export_wizard__company_partner_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__company_partner_id
msgid "Company Partner"
msgstr "Contacto de la compañía"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111_export_wizard__complementary_declaration
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__complementary_declaration
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod115_export_wizard__complementary_declaration
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__complementary_declaration
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__complementary_declaration
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__complementary_declaration
msgid "Complementary Declaration"
msgstr "Declaración complementaria"

#. module: l10n_es_reports
#: model:account.report,name:l10n_es_reports.financial_report_balance_sheet_full
msgid "Complete Balance Sheet"
msgstr "Balance completo"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__contact_person_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__contact_person_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__contact_person_name
msgid "Contact person"
msgstr "Persona de contacto"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__contact_person_phone
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__contact_person_phone
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__contact_person_phone
msgid "Contact phone number"
msgstr "Número de teléfono de contacto"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__complementary_declaration
msgid "Corrective Self-Assessment"
msgstr "Autoliquidación rectificativa"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_export_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111_export_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod115_export_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_mod347_manual_partner_data__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_export_wizard__create_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111_export_wizard__create_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__create_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod115_export_wizard__create_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__create_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__create_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__create_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__create_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__create_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_mod347_manual_partner_data__create_date
msgid "Created on"
msgstr "Creado el"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/libro_diario_export.py:0
msgid "Credit"
msgstr "Crédito"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_mod347_manual_partner_data__currency_id
msgid "Currency"
msgstr "Moneda"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/aeat_tax_reports.py:0
msgid ""
"Currency %s, used for a threshold in this report, is either nonexistent or "
"inactive. Please create or activate it."
msgstr ""
"La moneda %s, utilizada para un umbral en este informe, no existe o está "
"inactiva. Por favor, créala o actívala."

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111_export_wizard__company_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__company_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod115_export_wizard__company_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__company_id
msgid "Current Company"
msgstr "Compañía actual"

#. module: l10n_es_reports
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod303_export_wizard__declaration_type__d
msgid "D - Return"
msgstr "D - Devolución"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/account_move.py:0
#, python-format
msgid "D - Returns of goods previously sent from the TAI"
msgstr "D - Devoluciones de mercancías previamente enviadas desde el TAI"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.es_profit_and_loss_line_sum_d
msgid "D) PROFIT/LOSS FOR THE PERIOD (C + 18)"
msgstr "D) RESULTADO DEL EJERCICIO (C + 18)"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.mod_349_returns_goods_consignment
msgid "D. Returns of goods previously sent from the TAI"
msgstr "D. Devoluciones de mercancías previamente enviadas desde el TAI"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/libro_diario_export.py:0
msgid "Date"
msgstr "Fecha"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/libro_diario_export.py:0
msgid "Debit"
msgstr "Débito"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.mod_349_returns_goods_consignment_refunds
msgid "D. Rectifications of returned goods previously sent from the TAI"
msgstr ""
"D. Rectificaciones de devoluciones de mercancías previamente enviadas desde "
"el TAI"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111_export_wizard__declaration_type
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__declaration_type
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod115_export_wizard__declaration_type
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__declaration_type
msgid "Declaration Type"
msgstr "Tipo de declaración"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.mod_347_operations_title
msgid "Declared basis"
msgstr "Relación de declarados"

#. module: l10n_es_reports
#: model:ir.model.fields,help:l10n_es_reports.field_account_bank_statement_line__l10n_es_reports_mod347_invoice_type
#: model:ir.model.fields,help:l10n_es_reports.field_account_move__l10n_es_reports_mod347_invoice_type
msgid "Defines the category into which this invoice falls for mod 347 report."
msgstr ""
"Define la categoría en la que se incluye esta factura para el informe mod 347"

#. module: l10n_es_reports
#: model:ir.model.fields,help:l10n_es_reports.field_account_bank_statement_line__l10n_es_reports_mod349_invoice_type
#: model:ir.model.fields,help:l10n_es_reports.field_account_move__l10n_es_reports_mod349_invoice_type
msgid "Defines the category into which this invoice falls for mod 349 report"
msgstr ""
"Define la categoría en la que se incluye esta factura para el informe mod 349"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/libro_diario_export.py:0
msgid "Description"
msgstr "Descripción"

#. module: l10n_es_reports
#: model:account.account.tag,name:l10n_es_reports.balance_sheet_11700_account
msgid "Deudores comerciales no corrientes"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111_export_wizard__partner_bank_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__partner_bank_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod115_export_wizard__partner_bank_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__partner_bank_id
msgid "Direct Debit Account"
msgstr "Cuenta de domiciliación bancaria"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_export_wizard__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111_export_wizard__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod115_export_wizard__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__display_name
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_mod347_manual_partner_data__display_name
msgid "Display Name"
msgstr "Nombre de pantalla"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/libro_diario_export.py:0
msgid "Document"
msgstr "Documento"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/account_move.py:0
msgid "E - Supply"
msgstr "E - Suministro"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.mod_349_supplies
msgid "E. Intra-community sales"
msgstr "E. Entregas intracomunitarias"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.mod_349_supplies_refunds
msgid "E. Intra-community sales refunds"
msgstr "E. Entregas intracomunitarias exentas"

#. module: l10n_es_reports
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_mod347_manual_partner_data__operation_key__b
msgid "Entregas de bienes y prestaciones de servicios"
msgstr ""

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/libro_diario_export.py:0
msgid "Entry"
msgstr "Entrada"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__exempted_from_mod_390_available
msgid "Exempted From Mod 390 Available"
msgstr "Exento del Mod 390 disponible"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__exempted_from_mod_390
msgid "Exempted From Modelo 390"
msgstr "Exento del Modelo 390 disponible"

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_account_reports_export_wizard_format
msgid "Export format for accounting's reports"
msgstr "Formato de exportación para informes contables"

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_account_reports_export_wizard
msgid "Export wizard for accounting's reports"
msgstr "Asistente de exportación para informes contables"

#. module: l10n_es_reports
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod111_export_wizard__declaration_type__g
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__declaration_type__g
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod115_export_wizard__declaration_type__g
msgid "G - Income to enter on CCT"
msgstr "G - Ingresos a ingresar en el CCT"

#. module: l10n_es_reports
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod303_export_wizard__declaration_type__g
msgid "G - Tributaria Current Account - Income"
msgstr "G - Tributaria Cuenta corriente - Ingresos"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/libro_diario_export.py:0
msgid "General Ledger"
msgstr "Libro Diario"

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_account_general_ledger_report_handler
msgid "General Ledger Custom Handler"
msgstr ""

#. module: l10n_es_reports
#: model_terms:ir.ui.view,arch_db:l10n_es_reports.mod111_boe_wizard
#: model_terms:ir.ui.view,arch_db:l10n_es_reports.mod115_boe_wizard
#: model_terms:ir.ui.view,arch_db:l10n_es_reports.mod303_boe_wizard
#: model_terms:ir.ui.view,arch_db:l10n_es_reports.mod347_boe_wizard
#: model_terms:ir.ui.view,arch_db:l10n_es_reports.mod349_boe_wizard
#: model_terms:ir.ui.view,arch_db:l10n_es_reports.mod390_boe_wizard
msgid "Generate BOE"
msgstr "Generar BOE"

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_account_generic_tax_report_handler
msgid "Generic Tax Report Custom Handler"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__group_number
msgid "Group of entities - Group Number"
msgstr "Grupo de entidades - Número de grupo"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/account_move.py:0
msgid "H - Supply without taxes delivered by a legal representative"
msgstr "H - Suministro sin impuestos entregado por un representante legal"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.mod_349_supplies_without_taxes_legal_representative
#: model:account.report.line,name:l10n_es_reports.mod_349_supplies_without_taxes_legal_representative_refunds
msgid ""
"H. Intra-community sales of goods after an import exempted of taxes made for "
"the fiscal representative"
msgstr ""
"H. Entregas intracomunitarias de bienes posteriores a una importación exenta "
"efectuadas por el representante fiscal"

#. module: l10n_es_reports
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod111_export_wizard__declaration_type__i
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__declaration_type__i
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod115_export_wizard__declaration_type__i
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod303_export_wizard__declaration_type__i
msgid "I - Income"
msgstr "I - Ingreso"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/account_move.py:0
msgid "I - Services acquisition"
msgstr "I - Adquisición de servicios"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_22100
msgid "I. Available-for-sale financial assets"
msgstr "I. Activos financieros disponibles para la venta"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_21100
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_21100
msgid "I. Capital"
msgstr ""

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_assoc_line_32100
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_32200
msgid "I. Current provisions"
msgstr "I. Provisiones a corto plazo"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_assoc_line_21100
msgid "I. Foundation endowment/Social fund"
msgstr "I. Dotación fundacional/Fondo social"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_assoc_line_11100
#: model:account.report.line,name:l10n_es_reports.balance_full_line_11100
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_11100
msgid "I. Intangible assets"
msgstr "I. Inmovilizado intangible"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.mod_349_services_acquired
#: model:account.report.line,name:l10n_es_reports.mod_349_services_acquired_refunds
msgid "I. Intra-community purchases of services"
msgstr "I. Adquisiciones intracomunitarias de servicios"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_assoc_line_12100
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_12200
msgid "I. Inventories"
msgstr "I. Existencias"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_32100
msgid "I. Liabilities associated with non-current assets held for sale"
msgstr ""
"I. Pasivos vinculados con activos no corrientes mantenidos para la venta"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_12100
msgid "I. Non-current assets held for sale"
msgstr "I. Activos no corrientes mantenidos para la venta"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_assoc_line_31100
#: model:account.report.line,name:l10n_es_reports.balance_full_line_31100
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_31100
msgid "I. Non-current provisions"
msgstr "I. Provisiones a largo plazo"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_res_company__l10n_es_reports_iae_group
msgid "IAE Group or Heading"
msgstr "Grupo o rúbrica IAE"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_export_wizard__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111_export_wizard__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod115_export_wizard__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_mod347_manual_partner_data__id
msgid "ID"
msgstr ""

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_assoc_line_32200
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_32300
msgid "II. Current payables"
msgstr "II. Deudas a corto plazo"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_assoc_line_31200
#: model:account.report.line,name:l10n_es_reports.balance_full_line_31200
#: model:account.report.line,name:l10n_es_reports.balance_full_line_32200
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_31200
msgid "II. Current provisions"
msgstr "II. Deudas a largo plazo"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_22200
msgid "II. Hedging transactions"
msgstr "II. Operaciones de cobertura"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_assoc_line_11200
msgid "II. Historical heritage assets"
msgstr "II. Bienes del Patrimonio Histórico"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_12200
msgid "II. Inventories"
msgstr "II. Existencias"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_11200
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_11200
msgid "II. Property, plant and equipment"
msgstr "II. Inmovilizado material"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_assoc_line_21200
msgid "II. Reserves"
msgstr "II. Reservas"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_21200
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_21200
msgid "II. Share premium"
msgstr "II. Prima de emisión"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_12300
msgid "II. Trade and other receivables"
msgstr "II. Deudores comerciales y otras cuentas a cobrar"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_assoc_line_12200
msgid "II. Usables and other receivables of the own activity"
msgstr "II. Usuarios y otros deudores de la actividad propia"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_assoc_line_21250
msgid "III. (Own shares and equity interests)"
msgstr "III. (Acciones y participaciones en patrimonio propias)"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_12400
msgid "III. Current investments in group companies and associates"
msgstr "III. Inversiones en empresas del grupo y asociadas a corto plazo"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_32300
msgid "III. Current payables"
msgstr "III. Deudas a corto plazo"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_31300
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_31300
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_32400
msgid "III. Group companies and associates, non-current liabilities"
msgstr "III. Deudas con empresas del grupo y asociadas a largo plazo"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_11300
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_11300
msgid "III. Investment property"
msgstr "III. Inversiones inmobiliarias"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_22300
msgid "III. Non-current assets and related liabilities held for sale"
msgstr ""
"III. Activos no corrientes y pasivos vinculados, mantenidos para la venta"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_assoc_line_31300
#: model:account.report.line,name:l10n_es_reports.balance_assoc_line_32300
msgid "III. Non-current debts with group companies and associates"
msgstr "III. Deudas con entidades del grupo y asociadas a largo plazo"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_assoc_line_11300
msgid "III. Property, plant and equipment"
msgstr "III. Inmovilizado material"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_21300
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_21300
msgid "III. Reserves"
msgstr "III. Reservas"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_assoc_line_12300
#: model:account.report.line,name:l10n_es_reports.balance_full_line_12300
msgid "III. Trade and other receivables"
msgstr "III. Deudores comerciales y otras cuentas a cobrar"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_21400
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_21400
msgid "IV. (Own shares and equity holdings)"
msgstr "IV. (Acciones y participaciones en patrimonio propias)"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_assoc_line_32400
msgid "IV. Beneficiaries-Creditors"
msgstr "IV. Beneficiarios-Acreedores"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_22400
msgid "IV. Conversion difference"
msgstr "IV. Diferencia de conversión"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_12500
msgid "IV. Current financial investments"
msgstr "IV. Inversiones financieras a corto plazo"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_assoc_line_12400
#: model:account.report.line,name:l10n_es_reports.balance_full_line_12400
msgid "IV. Current investments in group companies and associates"
msgstr "IV. Inversiones en entidades del grupo y asociadas a corto plazo"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_assoc_line_31400
#: model:account.report.line,name:l10n_es_reports.balance_full_line_31400
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_31400
msgid "IV. Deferred tax liabilities"
msgstr "IV. Pasivos por impuesto diferido"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_32400
msgid "IV. Group companies and associates, current liabilities"
msgstr "IV. Deudas con empresas del grupo y asociadas a corto plazo"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_assoc_line_11400
msgid "IV. Investment property"
msgstr "IV. Inversiones inmobiliarias"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_11400
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_11400
msgid "IV. Non-current investments in group companies and associates"
msgstr "IV. Inversiones en empresas del grupo y asociadas a largo plazo"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_assoc_line_21300
msgid "IV. Surplus from previous period"
msgstr "IV. Excedentes de ejercicios anteriores"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_32500
msgid "IV. Trade and other payables"
msgstr "IV. Acreedores comerciales y otras cuentas a pagar"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_21900
msgid "IX. Other equity instruments"
msgstr "IX. Otros instrumentos de patrimonio neto"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__monthly_return
msgid "In Monthly Return Register"
msgstr "En el registro de devolución mensual"

#. module: l10n_es_reports
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__account_move__l10n_es_reports_mod347_invoice_type__insurance
msgid "Insurance operation"
msgstr "Operación de seguro"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.mod_347_operations_insurance_title
msgid "Insurance operations"
msgstr "Operaciones de seguros"

#. module: l10n_es_reports
#: model:account.account.tag,name:l10n_es_reports.balance_sheet_12440_account
msgid "Inversiones en empresas del grupo y asociadas a corto plazo - Derivados"
msgstr ""

#. module: l10n_es_reports
#: model:account.account.tag,name:l10n_es_reports.balance_sheet_12460_account
msgid ""
"Inversiones en empresas del grupo y asociadas a corto plazo - Otras "
"inversiones"
msgstr ""

#. module: l10n_es_reports
#: model:account.account.tag,name:l10n_es_reports.balance_sheet_11440_account
msgid "Inversiones en empresas del grupo y asociadas a largo plazo - Derivados"
msgstr ""

#. module: l10n_es_reports
#: model:account.account.tag,name:l10n_es_reports.balance_sheet_11460_account
msgid ""
"Inversiones en empresas del grupo y asociadas a largo plazo - Otras "
"inversiones"
msgstr ""

#. module: l10n_es_reports
#: model:account.account.tag,name:l10n_es_reports.balance_sheet_11450_account
msgid ""
"Inversiones en empresas del grupo y asociadas a largo plazo - Otros activos "
"financieros"
msgstr ""

#. module: l10n_es_reports
#: model:account.account.tag,name:l10n_es_reports.balance_sheet_12560_account
msgid "Inversiones financieras a corto plazo - Otras inversiones"
msgstr ""

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.mod_349_invoices_title
msgid "Invoices"
msgstr "Facturas"

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_account_move
msgid "Journal Entry"
msgstr "Asiento contable"

#. module: l10n_es_reports
#: model_terms:ir.ui.view,arch_db:l10n_es_reports.mod390_boe_wizard
msgid "Judicial person"
msgstr "Persona jurídica"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_reports_export_wizard__l10n_es_reports_boe_wizard_id
msgid "L10N Es Reports Boe Wizard"
msgstr "L10N Es Asistente de informes BOE"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_reports_export_wizard__l10n_es_reports_boe_wizard_model
msgid "L10N Es Reports Boe Wizard Model"
msgstr "L10N Es Modelo de asistente de informes BOE"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_export_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111_export_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod115_export_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_mod347_manual_partner_data__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_export_wizard__write_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111_export_wizard__write_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__write_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod115_export_wizard__write_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__write_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__write_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__write_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__write_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__write_date
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_mod347_manual_partner_data__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/libro_diario_export.py:0
msgid "Libro Diario XLSX"
msgstr ""

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/libro_diario_export.py:0
msgid "Line"
msgstr "Línea"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/account_move.py:0
msgid "M - Supply without taxes"
msgstr "M - Suministro sin impuestos"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.mod_349_supplies_without_taxes
#: model:account.report.line,name:l10n_es_reports.mod_349_supplies_without_taxes_refunds
msgid ""
"M. Intra-community sales of goods after an importation exempted of taxes"
msgstr ""
"M. Entregas intracomunitarias de bienes posteriores a una importación exenta"

#. module: l10n_es_reports
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__cash_basis_mod347_data
msgid ""
"Manual entries containing the amounts perceived for the partners with cash "
"basis criterion during this year. Leave empty for partners for which this "
"criterion does not apply."
msgstr ""
"Entradas manuales que contienen los importes percibidos por los contactos "
"con criterio de base de efectivo durante este año. Dejar vacío para los "
"contactos a los que no se aplica este criterio."

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_reports_aeat_mod347_manual_partner_data
msgid "Manually Entered Data for Mod 347 Report"
msgstr "Datos introducidos manualmente para el informe Mod 347"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__monthly_return
msgid "Monthly return record in some period of the fiscal year"
msgstr "Registro de devolución mensual en algún periodo del año fiscal"

#. module: l10n_es_reports
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod303_export_wizard__declaration_type__n
msgid "N - No Activity / Zero Result"
msgstr "N - Sin actividad / Resultado cero"

#. module: l10n_es_reports
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod111_export_wizard__declaration_type__n
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__declaration_type__n
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod115_export_wizard__declaration_type__n
msgid "N - To return"
msgstr "N - A devolver"

#. module: l10n_es_reports
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__contact_person_name
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__contact_person_name
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__contact_person_name
msgid "Name of the contact person fot this BOE file's submission"
msgstr ""
"Nombre de la persona de contacto para la presentación de este archivo BOE"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__physical_person_name
msgid "Natural Person - Name"
msgstr "Personal natural - Nombre"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/aeat_tax_reports.py:0
msgid "No TIN set for partner %(name)s (id %(id)d). Please define one."
msgstr ""
"No se ha definido un NIF para el partner %(name)s (id %(id)d). Por favor, "
"defina uno."

#. module: l10n_es_reports
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__previous_report_number
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__previous_report_number
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__previous_report_number
msgid ""
"Number of the previous report, corrected or replaced by this one, if any"
msgstr ""
"Número del informe anterior, corregido o sustituido por éste, en su caso"

#. module: l10n_es_reports
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111_export_wizard__previous_report_number
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__previous_report_number
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod115_export_wizard__previous_report_number
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__previous_report_number
msgid "Number of the report previously submitted"
msgstr "Número del informe presentado anteriormente"

#. module: l10n_es_reports
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_mod347_manual_partner_data__operation_class__seguros
msgid "Operaciones de Seguros"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_mod347_manual_partner_data__operation_class
msgid "Operation Class"
msgstr "Clase de operación"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_mod347_manual_partner_data__operation_key
msgid "Operation Key"
msgstr "Clave de operación"

#. module: l10n_es_reports
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_export_wizard__calling_export_wizard_id
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111_export_wizard__calling_export_wizard_id
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__calling_export_wizard_id
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod115_export_wizard__calling_export_wizard_id
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__calling_export_wizard_id
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__calling_export_wizard_id
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__calling_export_wizard_id
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__calling_export_wizard_id
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__calling_export_wizard_id
msgid ""
"Optional field containing the report export wizard calling this BOE wizard, "
"if there is one."
msgstr ""
"Campo opcional que contiene el asistente de exortación de informes que llama "
"a este asistente BOE, si existe"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.mod_347_operations_regular_title
msgid "Other operations"
msgstr "Otras operaciones"

#. module: l10n_es_reports
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_mod347_manual_partner_data__operation_class__otras
msgid "Otras operaciones"
msgstr ""

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_mod347_manual_partner_data__parent_wizard_id
msgid "Parent Wizard"
msgstr "Asistente principal"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__is_in_tax_unit
msgid "Part of a tax unit"
msgstr "Parte de una unidad fiscal"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_mod347_manual_partner_data__partner_id
msgid "Partner"
msgstr "Contacto"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/aeat_tax_reports.py:0
msgid ""
"Partner %(name)s (id %(id)d) is located in Spain but does not have any "
"province. Please set one."
msgstr ""
"El contacto %(name)s (id %(id)d) se encuentra en España pero no tiene "
"ninguna provincia. Por favor, establezca una."

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/aeat_tax_reports.py:0
msgid ""
"Partner %(name)s (id %(id)d) is not associated to any Spanish province, and "
"should hence have a country code. For this, fill in its 'country' field."
msgstr ""
"El contacto %(name)s (id %(id)d) no está asociado a ninguna provincia "
"española, y debería tener un código de país. Para ello, rellene su campo "
"'país'."

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_mod347_manual_partner_data__perceived_amount
msgid "Perceived Amount"
msgstr "Importe percibido"

#. module: l10n_es_reports
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__contact_person_phone
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__contact_person_phone
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__contact_person_phone
msgid "Phone number where to join the contact person"
msgstr "Número de teléfono de la persona de contacto"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/wizard/aeat_boe_export_wizards.py:0
msgid "Please first assign a BIC number to the bank related to this account."
msgstr ""
"Por favor, asigne primero un número BIC al banco relacionado con esta cuenta."

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/wizard/aeat_boe_export_wizards.py:0
msgid "Please first set the TIN of your company."
msgstr "Por favor, introduzca primero el NIF de su empresa"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/account_move.py:0
msgid "Please select a Spanish invoice type for this invoice."
msgstr "Por favor, seleccione un tipo de factura española para esta factura."

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/wizard/aeat_boe_export_wizards.py:0
msgid "Please select an IBAN account."
msgstr "Por favor, seleccione una cuenta IBAN"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111_export_wizard__previous_report_number
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__previous_report_number
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod115_export_wizard__previous_report_number
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__previous_report_number
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__previous_report_number
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__previous_report_number
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__previous_report_number
msgid "Previous Report Number"
msgstr "Número de informe anterior"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__previous_decl_number
msgid "Previous declaration no."
msgstr "N° de declaración anterior"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__principal_activity
#: model_terms:ir.ui.view,arch_db:l10n_es_reports.mod390_boe_wizard
msgid "Principal activity"
msgstr "Actividad principal"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__principal_code_activity
msgid "Principal activity - Activity Code"
msgstr "Actividad principal - Código de actividad"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__principal_iae_epigrafe
msgid "Principal activity - Epígrafe"
msgstr "Actividad principal - Epígrafe"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/aeat_tax_reports.py:0
msgid "Print BOE"
msgstr "Imprimir BOE"

#. module: l10n_es_reports
#: model:account.report,name:l10n_es_reports.financial_report_es_profit_and_loss
msgid "Profit and Loss"
msgstr "Pérdidas y Ganancias"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/account_move.py:0
#, python-format
msgid "R - Transfers of goods made under consignment sales agreements"
msgstr ""
"R - Transferencias de bienes realizadas en virtud de contratos de venta en "
"consignación"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.mod_349_consignment_sales_agreements_refunds
msgid ""
"R. Rectifications of transfers of goods made under consignment "
"sale contracts."
msgstr ""
"R. Rectificaciones de las transferencias de bienes realizadas en virtud de "
"contratos de venta en consignación"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.mod_349_consignment_sales_agreements
msgid "R. Transfers of goods made under consignment sales contracts."
msgstr ""
"R. Transferencias de bienes realizadas en virtud de contratos de venta en "
"consignación"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/aeat_tax_reports.py:0
msgid "Reading a non-Spanish TIN as a Spanish TIN."
msgstr "Lectura de un NIF no español como NIF español"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/aeat_tax_reports.py:0
msgid ""
"Refund Invoice %s was created without a link to the original invoice that "
"was credited, while we need that information for this report. "
msgstr ""
"La factura de reembolso %s se creó sin un enlace a la factura original que "
"se abonó, mientras que necesitamos esa información para este informe."

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.mod_349_refunds_title
msgid "Refunds"
msgstr "Reembolsos"

#. module: l10n_es_reports
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__account_move__l10n_es_reports_mod347_invoice_type__regular
msgid "Regular operation"
msgstr "Operación regular"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_export_wizard__report_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111_export_wizard__report_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__report_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod115_export_wizard__report_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__report_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__report_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__report_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__report_id
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__report_id
msgid "Report"
msgstr "Informe"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__judicial_person_nif
msgid "Representative - NIF"
msgstr "Representante - NIF"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__judicial_person_name
msgid "Representative - Name and Surname"
msgstr "Representante - Nombre y apellidos"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__judicial_person_notary
msgid "Representative - Notary"
msgstr "Representante - Notario"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__judicial_person_procuration_date
msgid "Representative - Power of Attorney Date"
msgstr "Representante - Fecha Poder notarial"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/account_move.py:0
msgid "S - Services sale"
msgstr "S - Venta de servicios"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.mod_349_services_sold
#: model:account.report.line,name:l10n_es_reports.mod_349_services_sold_refunds
msgid "S. Intra-community sales of services carried out by the declarant"
msgstr ""
"S. Prestaciones intracomunitarias de servicios realizadas por el declarante"

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_libros_registro_export_handler
msgid "Spanish Libros Registro de IVA"
msgstr "Libros en español Registro de IVA"

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_tax_report_handler
msgid "Spanish Tax Report Custom Handler"
msgstr "Gestor personalizado de informe de impuestos españoles"

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_mod111_tax_report_handler
msgid "Spanish Tax Report Custom Handler (Mod111)"
msgstr "Gestor personalizado de informe de impuestos españoles (Mod111)"

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_mod115_tax_report_handler
msgid "Spanish Tax Report Custom Handler (Mod115)"
msgstr "Gestor personalizado de informe de impuestos españoles (Mod115)"

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_mod303_tax_report_handler
msgid "Spanish Tax Report Custom Handler (Mod303)"
msgstr "Gestor personalizado de informe de impuestos españoles (Mod303)"

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_mod347_tax_report_handler
msgid "Spanish Tax Report Custom Handler (Mod347)"
msgstr "Gestor personalizado de informe de impuestos españoles (Mod347)"

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_mod349_tax_report_handler
msgid "Spanish Tax Report Custom Handler (Mod349)"
msgstr "Gestor personalizado de informe de impuestos españoles (Mod349)"

#. module: l10n_es_reports
#: model:ir.model,name:l10n_es_reports.model_l10n_es_mod390_tax_report_handler
msgid "Spanish Tax Report Custom Handler (Mod390)"
msgstr "Gestor personalizado de informe de impuestos españoles (Mod390)"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__special_regime_applicable_163
msgid "Special Regime Art. 163 is applicable"
msgstr "Régimen especial El Art. 163 es aplicable"

#. module: l10n_es_reports
#: model_terms:ir.ui.view,arch_db:l10n_es_reports.mod390_boe_wizard
msgid "Special Regimen"
msgstr "Régimen especial"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__special_cash_basis
msgid "Special cash basis regime"
msgstr "Régimen especial de base de efectivo"

#. module: l10n_es_reports
#: model_terms:ir.ui.view,arch_db:l10n_es_reports.mod390_boe_wizard
msgid "Substitute declaration"
msgstr "Sustituir declaración"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__substitutive_declaration
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__substitutive_declaration
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__substitutive_declaration
msgid "Substitutive Declaration"
msgstr "Declaración sustitutiva"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__is_substitute_decl_by_rectif_of_quotas
msgid "Substitutive declaration for correction of quotas"
msgstr "Declaración sustitutiva para correción de cuotas"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod390_export_wizard__is_substitute_declaration
msgid "Substitutive declaration?"
msgstr "Declaración sustitutiva?"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.mod_347_summary_title
#: model:account.report.line,name:l10n_es_reports.mod_349_summary_title
msgid "Summary"
msgstr "Resumen"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/account_move.py:0
msgid "T - Triangular Operation"
msgstr "T - Operación triangular"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.mod_349_triangular
#: model:account.report.line,name:l10n_es_reports.mod_349_triangular_refunds
msgid ""
"T. Sales to other member states exempted of intra-community taxes in case of "
"triangular operations"
msgstr ""
"T. Entregas a otros estados miembros subsiguientes a adquisiciones "
"intracomunitarias exentas en el marco de operaciones triangulares"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_assoc_line_10000
#: model:account.report.line,name:l10n_es_reports.balance_full_line_10000
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_10000
msgid "TOTAL ASSETS (A + B)"
msgstr "TOTAL ACTIVO (A + B)"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_assoc_line_30000
#: model:account.report.line,name:l10n_es_reports.balance_full_line_30000
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_30000
msgid "TOTAL EQUITY AND LIABILITIES (A + B + C)"
msgstr "TOTAL PATRIMONIO NETO Y PASIVO (A + B + C)"

#. module: l10n_es_reports
#: model:account.report,name:l10n_es_reports.mod_347
msgid "Tax Report (Mod 347)"
msgstr "Informe de impuestos (Mod 347)"

#. module: l10n_es_reports
#: model:account.report,name:l10n_es_reports.mod_349
msgid "Tax Report (Mod 349)"
msgstr "Informe de impuestos (Mod 349)"

#. module: l10n_es_reports
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__exempted_from_mod_390_available
msgid ""
"Technical field used to only make exempted_from_mod_390 avilable in the last "
"period (12 or 4T)"
msgstr ""
"Campo técnico utilizado para que exempted_from_mod_390 sólo esté disponible "
"en el último periodo (12 ó 4T)"

#. module: l10n_es_reports
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111_export_wizard__partner_bank_id
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__partner_bank_id
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod115_export_wizard__partner_bank_id
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__partner_bank_id
msgid ""
"The IBAN account number to use for direct debit. Leave blank if you don't "
"use direct debit."
msgstr ""
"El número de cuenta IBAN a utilizar para la domiciliación bancaria. Déjelo "
"vacío si no utiliza la domiciliación bancaria."

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/libro_diario_export.py:0
msgid "This report export is only available for ES companies."
msgstr "Esta exportación de informes sólo está disponible para las empresas ES."

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.mod_349_statistics_invoices_total_amount
msgid "Total amount of intra-community operations"
msgstr "Importe de las operaciones intracomunitarias"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.mod_349_statistics_invoices_partners_count
msgid "Total number of intra-community operations"
msgstr "Número total de operadores intracomunitarios"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.mod_349_statistics_refunds_partners_count
msgid "Total number of intra-community refund operations"
msgstr "Número total de operadores intracomunitarios con rectificaciones"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.mod_347_statistics_operations_count
msgid "Total number of persons and entities"
msgstr "Número total de personas y entitades"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__trimester_2months_report
msgid "Trimester monthly report"
msgstr "Informe mensual del trimestre"

#. module: l10n_es_reports
#: model:ir.model.fields,help:l10n_es_reports.field_account_bank_statement_line__l10n_es_reports_mod347_available
#: model:ir.model.fields,help:l10n_es_reports.field_account_move__l10n_es_reports_mod347_available
msgid ""
"True if and only if the invoice MIGHT need to be reported on mod 347, i.e. "
"it concerns an operation from a Spanish headquarter."
msgstr ""
"True si y sólo si la factura PODRÍA tener que ser declarada en el mod 347, "
"es decir, si se refiere a una operación de una sede española."

#. module: l10n_es_reports
#: model:ir.model.fields,help:l10n_es_reports.field_account_bank_statement_line__l10n_es_reports_mod349_available
#: model:ir.model.fields,help:l10n_es_reports.field_account_move__l10n_es_reports_mod349_available
msgid ""
"True if and only if the invoice must be reported on mod 349 report, i.e. it "
"concerns an intracommunitary operation."
msgstr ""
"True si y sólo si la factura tiene que ser declarada en el mod 349, es "
"decir, si se trata de una operación intracomunitaria."

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_bank_statement_line__l10n_es_reports_mod347_invoice_type
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_move__l10n_es_reports_mod347_invoice_type
msgid "Type for mod 347"
msgstr "Tipo para mod 347"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_bank_statement_line__l10n_es_reports_mod349_invoice_type
#: model:ir.model.fields,field_description:l10n_es_reports.field_account_move__l10n_es_reports_mod349_invoice_type
msgid "Type for mod 349"
msgstr "Tipo para mod 349"

#. module: l10n_es_reports
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod303_export_wizard__declaration_type__u
msgid "U - Direct Debit of the Income in CCC"
msgstr "U - Domiciliación de los ingresos en CCC"

#. module: l10n_es_reports
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod111_export_wizard__declaration_type__u
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__declaration_type__u
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod115_export_wizard__declaration_type__u
msgid "U - Direct debit"
msgstr "U - Domiciliación bancaria"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/libros_export.py:0
msgid "Unable to find matching surcharge tax in %s"
msgstr "No se ha podido encontrar el impuesto de recargo correspondiente en %s"

#. module: l10n_es_reports
#: model:ir.model.fields,field_description:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__using_sii
msgid "Using SII Voluntarily"
msgstr "Uso voluntario de SII"

#. module: l10n_es_reports
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod303_export_wizard__declaration_type__v
msgid "V - Tributaria Current Account - Return"
msgstr "V - Tributaria Cuenta corriente - Devolución"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_assoc_line_12500
#: model:account.report.line,name:l10n_es_reports.balance_full_line_12500
msgid "V. Current financial investments"
msgstr "V. Inversiones financieras a corto plazo"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_assoc_line_31500
#: model:account.report.line,name:l10n_es_reports.balance_full_line_31500
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_31500
msgid "V. Non-current accruals"
msgstr "V. Periodificaciones a largo plazo"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_11500
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_11500
msgid "V. Non-current investments"
msgstr "V. Inversiones financieras a largo plazo"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_assoc_line_11500
msgid "V. Non-current investments in group companies and associates"
msgstr "V. Inversiones en entidades del grupo y asociadas a largo plazo"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_22500
msgid "V. Other"
msgstr "V. Otros"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_12600
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_32600
msgid "V. Prepayments for current assets"
msgstr "V. Periodificaciones a corto plazo"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_21500
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_21500
msgid "V. Prior periods' profit and loss"
msgstr "V. Resultados de ejercicios anteriores"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_assoc_line_21400
msgid "V. Surplus of the year"
msgstr "V. Excedente del ejercicio"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_assoc_line_32500
#: model:account.report.line,name:l10n_es_reports.balance_full_line_32500
msgid "V. Trade and other payables"
msgstr "V. Acreedores comerciales y otras cuentas a pagar"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/libros_export.py:0
msgid "VAT Record Books (XLSX)"
msgstr "Libros de registro de IVA (XLSX)"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_12700
msgid "VI. Cash and cash equivalents"
msgstr "VI. Efectivo y otros activos líquidos equivalentes"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_32700
msgid "VI. Current debt with special characteristics"
msgstr "VI. Deuda con características especiales a corto plazo"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_11600
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_11600
msgid "VI. Deferred tax assets"
msgstr "VI. Activos por impuesto diferido"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_assoc_line_11600
msgid "VI. Non-current investments"
msgstr "VI. Inversiones financieras a largo plazo"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_31600
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_31600
msgid "VI. Non-current trade payables"
msgstr "VI. Acreedores comerciales no corrientes"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_21600
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_21600
msgid "VI. Other equity holder contributions"
msgstr "VI. Otras aportaciones de socios"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_assoc_line_12600
#: model:account.report.line,name:l10n_es_reports.balance_assoc_line_32600
#: model:account.report.line,name:l10n_es_reports.balance_full_line_12600
#: model:account.report.line,name:l10n_es_reports.balance_full_line_32600
msgid "VI. Prepayments for current assets"
msgstr "VI. Periodificaciones a corto plazo"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_assoc_line_12700
#: model:account.report.line,name:l10n_es_reports.balance_full_line_12700
msgid "VII. Cash and cash equivalents"
msgstr "VII. Efectivo y otros activos líquidos equivalentes"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_assoc_line_32700
#: model:account.report.line,name:l10n_es_reports.balance_full_line_32700
msgid "VII. Current debts with special characteristics"
msgstr "VII. Deuda con características especiales a corto plazo"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_assoc_line_11700
msgid "VII. Deferred tax assets"
msgstr "VII. Activos por impuesto diferido"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_11700
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_11700
msgid "VII. Non-current commercial debt"
msgstr "VII. Deudores comerciales no corrientes"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_31700
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_31700
#: model:account.report.line,name:l10n_es_reports.l10n_es_assoc_line_31600
msgid "VII. Non-current debt with special characteristics"
msgstr "VII. Deuda con características especiales a largo plazo"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_21700
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_21700
msgid "VII. Profit/loss for the period"
msgstr "VII. Resultado del ejercicio"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.balance_full_line_21800
#: model:account.report.line,name:l10n_es_reports.balance_pymes_line_21800
msgid "VIII. (Interim dividend)"
msgstr "VIII. (Dividendo a cuenta)"

#. module: l10n_es_reports
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__complementary_declaration
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__complementary_declaration
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__complementary_declaration
msgid "Whether or not this BOE file corresponds to a complementary declaration"
msgstr "Si este archivo BOE corresponde o no a una declaración complementaria"

#. module: l10n_es_reports
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347_export_wizard__substitutive_declaration
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod347and349_export_wizard__substitutive_declaration
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__substitutive_declaration
msgid "Whether or not this BOE file corresponds to a substitutive declaration"
msgstr "Si este archivo BOE corresponde o no a una declaración sustitutiva"

#. module: l10n_es_reports
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111_export_wizard__complementary_declaration
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod111and115and303_export_wizard__complementary_declaration
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod115_export_wizard__complementary_declaration
msgid "Whether or not this BOE file is a complementary declaration."
msgstr "Si este archivo BOE es o no una declaración complementaria."

#. module: l10n_es_reports
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod303_export_wizard__complementary_declaration
msgid "Whether or not this BOE file is a corrective self-assessment."
msgstr "Si este archivo BOE es o no una autoliquidación rectificativa."

#. module: l10n_es_reports
#: model:ir.model.fields,help:l10n_es_reports.field_l10n_es_reports_aeat_boe_mod349_export_wizard__trimester_2months_report
msgid ""
"Whether or not this BOE file must be generated with the data of the first "
"two months of the trimester (in case its total amount of operation is above "
"the threshold fixed by the law)"
msgstr ""
"Si este archivo BOE debe o no generarse con los datos de los dos primeros "
"meses del trimestre (en caso de que su importe total de operación supere el "
"umbral fijado por la ley)."

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/aeat_tax_reports.py:0
msgid ""
"Wrong report dates for BOE generation : please select a range of a year."
msgstr ""
"Fechas de informe erróneas para la generación de BOE: seleccione un "
"intervalo de un año"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/aeat_tax_reports.py:0
msgid ""
"Wrong report dates for BOE generation : please select a range of one month "
"or a trimester."
msgstr ""
"Fechas de informe erróneas para la generación del BOE: seleccione un rango "
"de un mes o un trimestre."

#. module: l10n_es_reports
#: model:ir.model.fields.selection,name:l10n_es_reports.selection__l10n_es_reports_aeat_boe_mod303_export_wizard__declaration_type__x
msgid "X - Return by Transfer Abroad (Only for Periods 3T, 4T and 07 to 12)"
msgstr ""
"X - Devolución por traslado al extranjero (sólo para los periodos 3T, 4T y "
"07 a 12)"

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/libros_export.py:0
msgid "XLSX"
msgstr ""

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/aeat_tax_reports.py:0
msgid ""
"You cannot generate a BOE file for the first two months of a trimester if "
"only one month is selected!"
msgstr ""
"No se puede generar un archivo BOE para los dos primeros meses de un "
"trimestre si sólo se selecciona un mes."

#. module: l10n_es_reports
#. odoo-python
#: code:addons/l10n_es_reports/models/aeat_tax_reports.py:0
msgid ""
"Your date range does not cover entire months, please use a start and end "
"date matching respectively the first and last day of a month."
msgstr ""
"Su intervalo de fechas no cubre meses enteros, por favor utilice una fecha "
"de inicio y finalización que coincidan respectivamente con el primer y "
"último día de un mes."

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.es_profit_and_loss_line_13a
msgid "a) Allocation of subsidies, donations and legacies of financial nature"
msgstr ""
"a) Imputación de subvenciones, donaciones y legados de carácter financiero"

#. module: l10n_es_reports
#: model:account.report.line,name:l10n_es_reports.es_profit_and_loss_line_13b
msgid "b) Other finance expenses"
msgstr "b) Otros ingresos financieros"
