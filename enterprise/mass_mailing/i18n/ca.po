# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mass_mailing
# 
# Translators:
# jabiri7, 2024
# <PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON><PERSON>, 2024
# <PERSON> Bochaca <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# marc<PERSON>, 2024
# eriiikgt, 2024
# <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <car<PERSON><PERSON><PERSON>@hotmail.com>, 2024
# Josep <PERSON> Belchi, 2024
# martioodo hola, 2024
# <AUTHOR> <EMAIL>, 2024
# Jonatan Gk, 2024
# Arnau Ros, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Arnau Ros, 2024\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
msgid " %(subject)s (final)"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_import_view_form
msgid ""
"\"Damien Roberts\" <<EMAIL>>\n"
"\"Rick Sanchez\" <<EMAIL>>\n"
"<EMAIL>"
msgstr ""
"\"Damien Roberts\" <<EMAIL>>\n"
"\"Rick Sanchez\" <<EMAIL>>\n"
"<EMAIL>"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mailing_filter_count
msgid "# Favorite Filters"
msgstr "# Filtres preferits"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
msgid "$18"
msgstr "$18"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form
msgid "% Blacklist"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form
msgid "% Bounce"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form
msgid "% Opt-out"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "% of recipients"
msgstr "% dels destinataris"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing_list.py:0
msgid "%(contact_name)s subscribed to the following mailing list(s)"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing_list.py:0
msgid "%(contact_name)s unsubscribed from the following mailing list(s)"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mass_mailing_kpi_link_trackers
msgid "%Click (Total)"
msgstr "%Click (Total)"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing_list.py:0
msgid "%s (copy)"
msgstr "%s (còpia)"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/wizard/mailing_contact_to_list.py:0
msgid "%s Mailing Contacts have been added. "
msgstr "S'han afegit %s Contactes de correu. "

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.social_links
msgid "&amp;nbsp;&amp;nbsp;"
msgstr "&amp;nbsp;&amp;nbsp;"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/ir_mail_server.py:0
msgid "(scheduled for %s)"
msgstr "(planificat per a %s)"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_hr_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "100%"
msgstr "100%"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_res_config_settings__mass_mailing_reports
msgid "24H Stat Mailing Reports"
msgstr "Informes de correu d'estat 24H"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
msgid "24H Stats of %(mailing_type)s \"%(mailing_name)s\""
msgstr "24H d'estadístiques de %(mailing_type)s \"%(mailing_name)s\""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_hr_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "25%"
msgstr "25%"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "400px"
msgstr "400px"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_hr_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "50%"
msgstr "50%"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_hr_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "75%"
msgstr "75%"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "800px"
msgstr "800px"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_blockquote
msgid "<b>John DOE</b> • CEO of MyCompany"
msgstr "<b>John DOE</b> • CEO de MyCompany"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_kanban
msgid ""
"<br/>\n"
"                                    <span class=\"text-muted\">\n"
"                                        <i class=\"fa fa-envelope-o\"/> Contacts\n"
"                                    </span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_kanban
msgid ""
"<br/>\n"
"                                    <span class=\"text-muted\">Blacklist</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_kanban
msgid ""
"<br/>\n"
"                                    <span class=\"text-muted\">Bounce</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_kanban
msgid ""
"<br/>\n"
"                                    <span class=\"text-muted\">Mailings</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_kanban
msgid ""
"<br/>\n"
"                                    <span class=\"text-muted\">Opt-Out</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.theme_default_template
msgid ""
"<br/>We want to take this opportunity to welcome you to our ever-growing community!\n"
"                <br/>Your platform is ready for work, it will help you reduce the costs of digital signatures, attract new customers and increase sales."
msgstr ""
"<br/>Volem aprofitar aquesta oportunitat per donar-vos la benvinguda a la nostra comunitat en creixement constant!\n"
"                <br/>La vostra plataforma està llesta per treballar, us ajudarà a reduir els costos de les signatures digitals, atraure nous clients i augmentar les vendes."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_event
msgid "<font class=\"text-o-color-1\">25 September 2022 - 4:30 PM</font>"
msgstr "<font class=\"text-o-color-1\">25 de setembre de 2022 - 4:30 PM</font>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_event
msgid "<font class=\"text-o-color-1\">26 September 2022 - 1:30 PM</font>"
msgstr "<font class=\"text-o-color-1\">26 de setembre de 2022 - 1:30 PM</font>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_discount1
msgid ""
"<font class=\"text-o-color-2\"><span style=\"font-"
"weight:bolder;\">-20%</span></font>"
msgstr ""
"<font class=\"text-o-color-2\"><span style=\"font-"
"weight:bolder;\">-20%</span></font>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_alert
msgid ""
"<font style=\"color: rgb(12 84 96);\">Don't write about products or services"
" here, write about solutions.</font>"
msgstr ""
"<font style=\"color: rgb(12 84 96);\">No escrius sobre productes o serveis "
"aquí, escriu sobre solucions.</font>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_picture
msgid ""
"<font style=\"font-size: 12px;\">Add a caption to enhance the meaning of "
"this image.</font>"
msgstr ""
"<font style=\"font-size: 12px;\">Afegeix una llegenda per millorar el "
"significat d'aquesta imatge.</font>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_event
msgid "<font style=\"font-size: 18px\">Event Two</font>"
msgstr "<font style=\"font-size: 18px\">Esdeveniment dos</font>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_event
msgid "<font style=\"font-size: 18px;\">Event One</font>"
msgstr "<font style=\"font-size: 18px;\">Esdeveniment u</font>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_picture
msgid "<font style=\"font-size: 48px;\">A Punchy Headline</font>"
msgstr "<font style=\"font-size: 48px;\">Un Punchy Headline</font>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_cover
msgid "<font style=\"font-size: 62px; font-weight: bold;\">Catchy Headline</font>"
msgstr ""
"<font style=\"font-size: 62px; font-weight: bold;\">Titular Enganxós</font>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_subscription_view_form
msgid ""
"<i class=\"fa fa-ban text-danger\" role=\"img\" title=\"This email is "
"blacklisted for mass mailings\" aria-label=\"Blacklisted\" invisible=\"not "
"is_blacklisted\" groups=\"base.group_user\"/>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "<i class=\"fa fa-bar-chart\"/> Compare Version"
msgstr "<i class=\"fa fa-bar-chart\"/> Compara versió"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "<i class=\"fa fa-bullseye me-2\" role=\"img\" aria-label=\"Lead/Opportunity\"/>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "<i class=\"fa fa-copy\"/> Create an Alternative"
msgstr "<i class=\"fa fa-copy\"/> Crea una alternativa"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "<i class=\"fa fa-envelope\"/> Send this as winner"
msgstr "<i class=\"fa fa-envelope\"/> Envia això com a guanyador"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid ""
"<i class=\"fa fa-envelope\"/><span name=\"ab_test_auto\">\n"
"                                                    Send Winner Now\n"
"                                                </span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid ""
"<i class=\"fa fa-exclamation-triangle text-danger\" aria-hidden=\"true\"/>\n"
"                        The sum of all percentages for this A/B campaign totals"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_kanban
msgid ""
"<i class=\"fa fa-exclamation-triangle\" role=\"img\" aria-label=\"Warning\" "
"title=\"Warning\"/>"
msgstr ""
"<i class=\"fa fa-exclamation-triangle\" role=\"img\" aria-label=\"Warning\" "
"title=\"Warning\"/>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "<i class=\"fa fa-fw fa-circle\"/> Circles"
msgstr "<i class=\"fa fa-fw fa-circle\"/> Cercles"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "<i class=\"fa fa-fw fa-heart\"/> Hearts"
msgstr "<i class=\"fa fa-fw fa-heart\"/> Cors"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "<i class=\"fa fa-fw fa-refresh me-1\"/> Replace Icon"
msgstr "Icona de substitució <i class=\"fa fa-fw fa-refresh me-1\"/>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "<i class=\"fa fa-fw fa-square\"/> Squares"
msgstr "<i class=\"fa fa-fw fa-square\"/> Quadrats"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "<i class=\"fa fa-fw fa-star\"/> Stars"
msgstr "<i class=\"fa fa-fw fa-star\"/> Estrelles"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "<i class=\"fa fa-fw fa-thumbs-up\"/> Thumbs"
msgstr "<i class=\"fa fa-fw fa-thumbs-up\"/> Colzes"

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.mailing_subscription_action_report_optout
msgid "<i class=\"oi oi-arrow-right\"></i> Configure Opt-out Reasons"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_blockquote
msgid ""
"<i>Write a quote here from one of your customers. Quotes are a great way to "
"build confidence in your products or services.</i>"
msgstr ""
"<i>Escriviu aquí un pressupost d'un dels vostres clients. Les cotitzacions "
"són una bona manera de generar confiança en els vostres productes o "
"serveis.</i>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid ""
"<small class=\"oe_edit_only text-muted mb-2\" style=\"font-size:74%\" invisible=\"reply_to_mode == 'update' or mailing_model_name in ['mailing.contact', 'res.partner', 'mailing.list']\">\n"
"                                                    To track replies, this address must belong to this database.\n"
"                                                </small>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid ""
"<span class=\"fa fa-hourglass-half me-2 small my-auto\" aria-"
"label=\"Scheduled date\"/>"
msgstr ""
"<span class=\"fa fa-hourglass-half me-2 small my-auto\" aria-"
"label=\"Scheduled date\"/>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid ""
"<span class=\"fa fa-hourglass-o me-2 small my-auto\" aria-label=\"Scheduled date\"/>\n"
"                                        <span>Next Batch</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid ""
"<span class=\"fa fa-paper-plane me-2 small my-auto\" aria-label=\"Sent "
"date\"/>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "<span class=\"mx-2\">/</span>"
msgstr "<span class=\"mx-2\">/</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_form
msgid "<span class=\"o_stat_text\">Open Recipient</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_form_split_name
msgid "<span class=\"oe_grey\" invisible=\"name\">e.g. \"John Smith\"</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_kanban
msgid "<span class=\"text-muted\">Blacklist</span>"
msgstr "<span class=\"text-muted\">Llista negra</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_kanban
msgid "<span class=\"text-muted\">Bounce</span>"
msgstr "<span class=\"text-muted\">Rebot</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_kanban
msgid "<span class=\"text-muted\">Mailings</span>"
msgstr "<span class=\"text-muted\">Enviaments</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_kanban
msgid "<span class=\"text-muted\">Opt-out</span>"
msgstr "<span class=\"text-muted\">Baixes</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "<span invisible=\"not mailing_on_mailing_list\">Mailing Contact</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid ""
"<span name=\"canceled_text\">emails have been cancelled and will not be "
"sent.</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "<span name=\"failed_text\">emails could not be sent.</span>"
msgstr ""
"<span name=\"failed_text\">no s'han pogut enviar correus electrònics.</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid ""
"<span name=\"mailing_schedule_type_now_text\">This mailing will be sent as "
"soon as possible.</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "<span name=\"next_departure_text\">This mailing is scheduled for </span>"
msgstr ""
"<span name=\"next_departure_text\">Aquest correu està programat per </span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "<span name=\"process_text\">emails are being processed.</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid ""
"<span name=\"refresh_text\">This mailing will be sent as soon as "
"possible.</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid ""
"<span name=\"scheduled_text\">emails are in queue and will be sent "
"soon.</span>"
msgstr ""
"<span name=\"scheduled_text\">Els correus electrònics estan a la cua i "
"s'enviaran aviat.</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "<span name=\"sent\">emails have been sent.</span>"
msgstr "<span name=\"sent\">s'han enviat els correus electrònics.</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.digest_mail_main
msgid "<span style=\"color: #878d97;\">Turn off Mailing Reports</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_alert
msgid ""
"<span style=\"color: rgb(12 84 96); font-size: 16px; font-weight: "
"bolder;\">Explain the benefits you offer</span>"
msgstr ""
"<span style=\"color: rgb(12 84 96); font-size: 16px; font-weight: "
"bolder;\">Explica els beneficis que ofereixes</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
msgid "<span style=\"font-size: 11px\">user / month (billed annually)</span>"
msgstr "<span style=\"font-size: 11px\">usuari / mes (bil·lat anualment)</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_numbers
msgid "<span style=\"font-size: 48px;\">12</span>"
msgstr "<span style=\"font-size: 48px;\">12</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_numbers
msgid "<span style=\"font-size: 48px;\">45</span>"
msgstr "<span style=\"font-size: 48px;\">45</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_numbers
msgid "<span style=\"font-size: 48px;\">8</span>"
msgstr "<span style=\"font-size: 48px;\">8</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_call_to_action
msgid "<span style=\"font-weight: bolder;\">50,000+ companies</span> run Odoo."
msgstr ""
"<span style=\"font-weight: bolder;\">50,000 + empreses</span> dirigeixen "
"Odoo."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
msgid "<span style=\"font-weight: bolder;\">DEFAULT</span>"
msgstr "<span style=\"font-weight: bolder;\">PER DEFECTE</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_coupon_code
msgid "<span style=\"font-weight: bolder;\">GET $20 OFF</span>"
msgstr "<span style=\"font-weight: bolder;\">OBTENIU 20 DÒLARS DE</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
msgid "<span style=\"font-weight: bolder;\">PRO</span>"
msgstr "<span style=\"font-weight: bolder;\">PRO</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
msgid "<span style=\"font-weight:bolder\">24/7 Support</span>"
msgstr "<span style=\"font-weight:bolder\">24/7 Suport</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
msgid ""
"<span style=\"font-weight:bolder\">Advanced</span>\n"
"                                    features"
msgstr ""
"<span style=\"font-weight:bolder\">Avançat</span>\n"
"                                    característiques"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
msgid "<span style=\"font-weight:bolder\">Fully customizable</span>"
msgstr ""
"<span style=\"font-weight:bolder\">Es pot personalitzar completament</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
msgid ""
"<span style=\"font-weight:bolder\">Total</span>\n"
"                                    management"
msgstr ""
"<span style=\"font-weight:bolder\">Total</span>\n"
"                                    gestió"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_kanban
msgid "<span>Contacts</span>"
msgstr "<span>Contactes</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "<span>There wasn't enough recipients left for this mailing</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_kanban
msgid "<span>Valid Email Recipients</span>"
msgstr "<span>Destinataris de correu vàlids</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "<span>​</span>"
msgstr "<span></span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid ""
"<strong class=\"d-block\" invisible=\"mailing_type == 'mail' or not ab_testing_enabled or state != 'done' or sent != 0 or failed != 0 or canceled != 0\">\n"
"                                <span name=\"ab_test_text\">There wasn't enough recipients given to this mailing. </span>\n"
"                            </strong>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "<u>Refresh <i class=\"fa fa-refresh ms-1\"/></u>"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
msgid "A campaign should be set when A/B test is enabled"
msgstr "S' ha d' establir una campanya quan s' habilita la prova A/BName"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_color_blocks_2
msgid "A color block"
msgstr "Un bloc de color"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_alternation_image_text_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_alternation_text_image_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_alternation_text_image_text_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_alternation_text_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_default_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_image_texts_image_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_mosaic_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_reversed_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_texts_image_texts_template
msgid "A great title"
msgstr "Un gran títol"

#. module: mass_mailing
#: model:ir.model.constraint,message:mass_mailing.constraint_mailing_subscription_unique_contact_list
msgid ""
"A mailing contact cannot subscribe to the same mailing list multiple times."
msgstr ""
"Un contacte de correu no pot subscriure' s a la mateixa llista de correu "
"diverses vegades."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "A sample of"
msgstr "Una mostra de"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_showcase
msgid "A short description of this great feature."
msgstr "Una breu descripció d'aquesta gran característica."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features
msgid "A small explanation of this great feature, in clear words."
msgstr ""
"Una petita explicació d'aquesta gran característica, en paraules clares."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_text_image
msgid "A unique value"
msgstr "Un valor únic"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__ab_testing_winner_mailing_id
msgid "A/B Campaign Winner Mailing"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "A/B Test"
msgstr "Prova A/B"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__ab_testing_mailings_count
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__ab_testing_mailings_count
msgid "A/B Test Mailings #"
msgstr "Comprovació de correu A/B #"

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_ab_testing_open_winner_mailing
msgid "A/B Test Winner"
msgstr "Guanyador de proves A/B"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
msgid "A/B Test: %s"
msgstr "Prova A/B: %s"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__ab_testing_completed
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__ab_testing_completed
msgid "A/B Testing Campaign Finished"
msgstr "La campanya de proves A/B ha finalitzat"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__ab_testing_description
msgid "A/B Testing Description"
msgstr "Descripció de prova A/B"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__ab_testing_pc
msgid "A/B Testing percentage"
msgstr "Percentatge de proves A/B"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "A/B Tests"
msgstr "Proves A/B"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "A/B Tests to review"
msgstr "Proves A/B per revisar"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
msgid "A/B test option has not been enabled"
msgstr "L' opció de prova A/ B no s' ha habilitat"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_needaction
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_needaction
msgid "Action Needed"
msgstr "Acció necessària"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__active
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__active
msgid "Active"
msgstr "Actiu"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_ir_mail_server__active_mailing_ids
msgid "Active mailing using this mail server"
msgstr "Enviament de correu actiu usant aquest servidor de correu"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__activity_ids
msgid "Activities"
msgstr "Activitats"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Activitat d'excepció de decoració"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__activity_state
msgid "Activity State"
msgstr "Estat de l'activitat"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icona de tipus d'activitat"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_three_columns
msgid ""
"Adapt these three columns to fit your design need. To duplicate, delete or "
"move columns, select the column and use the top icons to perform your "
"action."
msgstr ""
"Adapeu aquestes tres columnes per ajustar el vostre disseny. Per a duplicar,"
" esborrar o moure columnes, seleccioneu la columna i useu les icones "
"superiors per a realitzar l' acció."

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_filter_widget.xml:0
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_to_list_view_form
msgid "Add"
msgstr "Afegir"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_contact_to_list
msgid "Add Contacts to Mailing List"
msgstr "Afegeix contactes a la llista de correu"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Add Mailing Contacts"
msgstr "Afegeix els contactes de correu"

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.mailing_contact_to_list_action
msgid "Add Selected Contacts to a Mailing List"
msgstr "Afegeix els contactes seleccionats a una llista de correu"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "Add a great slogan."
msgstr "Afegeix un gran eslògan "

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_to_list_view_form
msgid "Add and Send Mailing"
msgstr "Afegeix i envia correu"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_tree
msgid "Add to List"
msgstr "Afegeix a la llista"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Add to Templates"
msgstr "Afegeix a les plantilles"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_filter_widget.xml:0
msgid "Add to favorite filters"
msgstr "Afegir als filtres favorits"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Advanced"
msgstr "Avançat"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Alert"
msgstr "Alerta"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Align Bottom"
msgstr "Alinea a baix"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Align Center"
msgstr "Alinear al centre"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Align Left"
msgstr "Alinear a l'esquerre"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Align Middle"
msgstr "Alinea al mig"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Align Right"
msgstr "Alinear a la dreta"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Align Top"
msgstr "Alinea a dalt"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_hr_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Alignment"
msgstr "Alineament"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_company_team
msgid "Aline Turner, CTO"
msgstr "Aline Turner, CTO"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_company_team
msgid ""
"Aline is one of the iconic people in life who can say they love what they "
"do. She mentors 100+ in-house developers and looks after the community of "
"thousands of developers."
msgstr ""
"Una línia és una de les persones icràctiques de la vida que poden dir que "
"els encanta el que fan. Els mentors de 100+ a l'equip de casa tenen cura de "
"la comunitat de milers de desenvolupadors."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_footer_social
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_footer_social_left
msgid "All Rights Reserved"
msgstr "S' han reservat tots els drets"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "All these icons are completely free for commercial use."
msgstr ""
"Totes aquestes icones són completament gratuïtes per a ús comercial.  "

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__ab_testing_enabled
msgid "Allow A/B Testing"
msgstr "Permet la prova A/B"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_subscription_optout_view_tree
msgid "Allow Feedback"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid "Allow recipients to blacklist themselves"
msgstr "Permet als destinataris a la llista negra"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_res_config_settings__show_blacklist_buttons
msgid ""
"Allow the recipient to manage themselves their state in the blacklist via "
"the unsubscription page."
msgstr ""
"Permetre al destinatari gestionar el seu estat a la llista negra a través de"
" la pàgina de baixa."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid ""
"Allow the recipient to manage themselves their state in the blacklist via "
"the unsubscription page. If the option is active, the 'Blacklist Me' button "
"is hidden on the unsubscription page. The 'come Back' button will always be "
"visible in any case to allow leads and partners to re-subscribe."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_options
msgid "Alternate Image Text"
msgstr "Text alternatiu d' imatge"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_options
msgid "Alternate Text"
msgstr "Text alternatiu"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_options
msgid "Alternate Text Image"
msgstr "Imatge de text alternatiu"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_options
msgid "Alternate Text Image Text"
msgstr "Text d' imatge alternatiu"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_numbers
msgid "Amazing pages"
msgstr "Pàgines increïbles"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_portal_subscription_feedback.xml:0
msgid "An error occurred. Please retry later or contact us."
msgstr ""

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_portal_subscription_blocklist.xml:0
#: code:addons/mass_mailing/static/src/xml/mailing_portal_subscription_form.xml:0
msgid "An error occurred. Please retry later."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_alternation_image_text_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_alternation_text_image_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_alternation_text_image_text_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_alternation_text_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_default_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_image_texts_image_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_mosaic_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_reversed_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_texts_image_texts_template
msgid "And a great subtitle"
msgstr "I un gran subtítols"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_color_blocks_2
msgid "Another color block"
msgstr "Un altre bloc de color"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_showcase
msgid "Another feature"
msgstr "Una altra característica"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe_form
msgid "Apply changes"
msgstr "Aplicar canvis"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "Archive"
msgstr "Arxivar"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge__archive_src_lists
msgid "Archive source mailing lists"
msgstr "Arxiva les llistes de correu font"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "Archived"
msgstr "Arxivat"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription_optout__is_feedback
msgid "Ask For Feedback"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing_list.py:0
msgid ""
"At least one of the mailing list you are trying to archive is used in an "
"ongoing mailing campaign."
msgstr ""
"Com a mínim s' usa una de la llista de correu que esteu intentant arxivar en"
" una campanya de correu en curs."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Attach a file"
msgstr "Adjunta un arxiu"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_attachment_count
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_attachment_count
msgid "Attachment Count"
msgstr "Nombre d'adjunts"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__attachment_ids
msgid "Attachments"
msgstr "Adjunts"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Auto"
msgstr "Auto"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "Average"
msgstr "Mitjana"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
msgid "Average of Bounced"
msgstr "Mitjana de rebotats"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
msgid "Average of Clicked"
msgstr "Mitjana de clicats"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
msgid "Average of Delivered"
msgstr "Mitjana de lliurament"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
msgid "Average of Opened"
msgstr "Mitjana d'obertes"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
msgid "Average of Replied"
msgstr "Mitjana de respostes"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_alert_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Background Color"
msgstr "Color de fons"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mailing_on_mailing_list
msgid "Based on Mailing Lists"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
msgid "Basic features"
msgstr "Característiques bàsiques"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
msgid "Basic management"
msgstr "Gestió bàsica"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_numbers
msgid "Beautiful snippets"
msgstr "Retalls bonics"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Big Boxes"
msgstr "Caixes Grans"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__is_blacklisted
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription__is_blacklisted
msgid "Blacklist"
msgstr "Llista negra"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_tree
msgid "Blacklist (%s)"
msgstr "Llista negra (%s)"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_res_config_settings__show_blacklist_buttons
msgid "Blacklist Option when Unsubscribing"
msgstr "Opció de llista negra en cancel· lar l' UNSUBS"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_search
msgid "Blacklisted"
msgstr "A la llista negra"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__failure_type__mail_bl
msgid "Blacklisted Address"
msgstr "Adreça de la llista negra"

#. module: mass_mailing
#: model:ir.ui.menu,name:mass_mailing.mail_blacklist_mm_menu
msgid "Blacklisted Email Addresses"
msgstr "Adreces electròniques a la llista negra"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/controllers/main.py:0
msgid "Blocklist removal request from portal"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/controllers/main.py:0
msgid ""
"Blocklist removal request from portal of mailing %(mailing_link)s (document "
"%(record_link)s)"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/controllers/main.py:0
msgid "Blocklist request from portal"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/controllers/main.py:0
msgid ""
"Blocklist request from portal of mailing %(mailing_link)s (document "
"%(record_link)s)"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/controllers/main.py:0
msgid ""
"Blocklist request from unsubscribe link of mailing %(mailing_link)s (direct "
"link usage)"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/controllers/main.py:0
msgid ""
"Blocklist request from unsubscribe link of mailing %(mailing_link)s "
"(document %(record_link)s)"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Blockquote"
msgstr "Cita en bloc"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__body_arch
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Body"
msgstr "Cos del missatge"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Body Width"
msgstr "Amplada del cos"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__body_html
msgid "Body converted to be sent by mail"
msgstr "Cos convertit a ser enviat per correu"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Bold"
msgstr "Negreta"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_product_list
msgid "Books"
msgstr "Llibres"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_hr_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options_border_widgets
msgid "Border"
msgstr "Vora"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_bounce
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription__message_bounce
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__failure_type__mail_bounce
msgid "Bounce"
msgstr "Rebot"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_tree
msgid "Bounce (%)"
msgstr "Rebot (%)"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
msgid ""
"Bounce happens when a mailing cannot be delivered (fake address, server "
"issues, ...). Check each record to see what went wrong."
msgstr ""
"El retorn ocorre quan no es pot lliurar un correu (adreça falsa, problemes "
"del servidor, ...). Comproveu cada registre per veure què ha anat malament."

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__bounced
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__bounced
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__trace_status__bounce
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Bounced"
msgstr "Rebotat"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "Bounced (%)"
msgstr "Rebotat (%)"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__bounced_ratio
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__bounced_ratio
msgid "Bounced Ratio"
msgstr "Ràtio de rebots"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
msgid "Business Benefits on %(expected)i %(mailing_type)s Sent"
msgstr "Beneficis empresarials a %(expected)i %(mailing_type)s enviats"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mass_mailing_kpi_link_trackers
msgid "Button Label"
msgstr "Etiqueta de botó"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
msgid "By using the <b>Breadcrumb</b>, you can navigate back to the overview."
msgstr ""
"Utilitzant <b>Fil d'Ariadna</b>, pots tornar a navegar a la vista general."

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__calendar_date
msgid "Calendar Date"
msgstr "Data del calendari"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Call to Action"
msgstr "Anomenada a l'acció"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__campaign_id
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "Campaign"
msgstr "Campanya"

#. module: mass_mailing
#: model:ir.ui.menu,name:mass_mailing.menu_view_mass_mailing_stages
msgid "Campaign Stages"
msgstr "Etapes de la campanya"

#. module: mass_mailing
#: model:ir.ui.menu,name:mass_mailing.mass_mailing_tag_menu
msgid "Campaign Tags"
msgstr "Etiquetes de la campanya"

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_view_utm_campaigns
#: model:ir.ui.menu,name:mass_mailing.menu_email_campaigns
msgid "Campaigns"
msgstr "Campanyes"

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_utm_campaigns
msgid ""
"Campaigns are the perfect tool to track results across multiple mailings."
msgstr ""
"Les campanyes són l'eina ideal per seguir els resultats de múltiples "
"enviaments de correus."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_to_list_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_merge_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_test_form
msgid "Cancel"
msgstr "Cancel·la"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__canceled
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__canceled
msgid "Canceled"
msgstr "Cancel·lat"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__trace_status__cancel
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
msgid "Cancelled"
msgstr "Cancel·lat"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing_test__email_to
msgid "Carriage-return-separated list of email addresses."
msgstr "Llista d' adreces de correu electrònic separada per carro."

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__preview
msgid ""
"Catchy preview sentence that encourages recipients to open this email.\n"
"In most inboxes, this is displayed next to the subject.\n"
"Keep it empty if you prefer the first characters of your email content to appear instead."
msgstr ""
"Frase de vista prèvia Capturada que anima els destinataris a obrir aquest correu electrònic.\n"
"En la majoria de les safates d'entrada, es mostra al costat del subjecte.\n"
"Mantén- lo buit si preferiu que apareguin els primers caràcters del vostre contingut de correu electrònic."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_hr_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Center"
msgstr "Centrar"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Centered Logo"
msgstr "Logotip centrat"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "Change Icons"
msgstr "Canviar icones"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid "Check how well your mailing is doing a day after it has been sent"
msgstr ""
"Comproveu com de bé està funcionant el vostre enviament un dia després "
"d'haver-lo enviat"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_res_config_settings__mass_mailing_reports
msgid "Check how well your mailing is doing a day after it has been sent."
msgstr ""
"Comproveu com de bé està funcionant el vostre enviament un dia després "
"d'haver-lo enviat."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_product_list
msgid "Check out all our books"
msgstr "Mira tots els nostres llibres."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_product_list
msgid "Check out all our clothes"
msgstr "Mira tota la nostra roba."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_product_list
msgid "Check out all our furniture"
msgstr "Mira tots els nostres mobles."

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
msgid "Check the email address and click send."
msgstr "Comproveu l'adreça de correu electrònic i feu clic a enviar."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_schedule_date_view_form
msgid "Choose a date"
msgstr ""

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
msgid "Choose this <b>theme</b>."
msgstr "Trieu això <b>tema</b>."

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_portal_subscription_form.xml:0
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe_form
msgid "Choose your mailing subscriptions"
msgstr "Escolliu les subscripcions de correu"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
msgid "Click on this button to add this mailing to your templates."
msgstr ""
"Feu clic en aquest botó per a afegir aquest enviament a les vostres "
"plantilles."

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
msgid "Click on this paragraph to edit it."
msgstr "Feu clic a aquest paràgraf per modificar-ho."

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__clicked
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__clicked
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Clicked"
msgstr "Clicat"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "Clicked (%)"
msgstr "Clicat (%)"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__links_click_datetime
msgid "Clicked On"
msgstr "Clicat en"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mass_mailing_mobile_preview.xml:0
msgid "Close"
msgstr "Tancar"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_product_list
msgid "Clothes"
msgstr "Roba"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__color
msgid "Color Index"
msgstr "Índex de color"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_color_blocks_2
msgid ""
"Color blocks are a simple and effective way to <b>present and highlight your"
" content</b>. Choose an image or a color for the background. You can even "
"resize and duplicate the blocks to create your own layout. Add images or "
"icons to customize the blocks."
msgstr ""
"Els blocs de color són una manera senzilla i eficaç de <b>presentar i "
"ressaltar el vostre contingut</b>. Trieu una imatge o un color per al fons. "
"Fins i tot podeu canviar la mida i duplicar els blocs per crear el vostre "
"propi disseny. Afegiu imatges o icones per personalitzar els blocs."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Columns"
msgstr "Columnes"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe_form
msgid "Come Back"
msgstr "Tornar"

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.mailing_subscription_action_report_optout
msgid "Come back later to discover why contacts unsubscribe.<br>"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
msgid ""
"Come back once your mailing has been sent to track who clicked on the "
"embedded links."
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
msgid ""
"Come back once your mailing has been sent to track who opened your mailing."
msgstr ""
"Torna un cop s'hagi enviat el vostre correu per a veure qui l'ha obert."

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_res_company
msgid "Companies"
msgstr "Empreses"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__company_name
msgid "Company Name"
msgstr "Nom d'empresa"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Comparisons"
msgstr "Comparacions"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_res_config_settings
msgid "Config Settings"
msgstr "Paràmetres de configuració"

#. module: mass_mailing
#: model:ir.ui.menu,name:mass_mailing.mass_mailing_configuration
msgid "Configuration"
msgstr "Configuració"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid "Configure Outgoing Mail Servers"
msgstr "Configura els servidors de correu de sortida"

#. module: mass_mailing
#: model_terms:web_tour.tour,rainbow_man_message:mass_mailing.mass_mailing_tour
msgid "Congratulations, I love your first mailing. :)"
msgstr "Felicitats, m'encanta el teu primer correu. :)"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__failure_type__mail_smtp
msgid "Connection failed (outgoing mail server problem)"
msgstr "Connexió fallida (problema amb el servidor de sortida de mail)"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_res_partner
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription__contact_id
msgid "Contact"
msgstr "Contacte"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_import__contact_list
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_import_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form_simplified
msgid "Contact List"
msgstr "Llista de contactes"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_form
msgid "Contact Name"
msgstr "Nom del contacte"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid "Contact Naming"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact_import__contact_list
msgid "Contact list that will be imported, one contact per line"
msgstr "Llista de contactes que s'importaran, un contacte per línia"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_call_to_action
msgid "Contact us"
msgstr "Contacta amb nosaltres"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_to_list__contact_ids
msgid "Contacts"
msgstr "Contactes"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/wizard/mailing_contact_import.py:0
msgid ""
"Contacts successfully imported. Number of contacts imported: "
"%(imported_count)s"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/wizard/mailing_contact_import.py:0
msgid ""
"Contacts successfully imported. Number of contacts imported: "
"%(imported_count)s. Number of duplicates ignored: %(duplicate_count)s"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Content Background"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_media_list
msgid "Continue reading <i class=\"fa fa-long-arrow-right align-middle ms-1\"/>"
msgstr "Continua llegint <i class=\"fa fa-long-arrow-right align-middle ms-1\"/>"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
msgid "Could not retrieve URL: %s"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact__message_bounce
#: model:ir.model.fields,help:mass_mailing.field_mailing_subscription__message_bounce
msgid "Counter of the number of bounced emails for this contact"
msgstr ""
"Comptador del nombre de correus electrònics rebotats d'aquest contacte"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__country_id
msgid "Country"
msgstr "País"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Cover"
msgstr "Coberta"

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.mailing_mailing_action_mail
msgid "Create a Mailing"
msgstr "Crea una correu"

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mass_mailing_lists
msgid "Create a Mailing List"
msgstr "Crea una llista de correu"

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_utm_campaigns
msgid "Create a mailing campaign"
msgstr "Crea una campanya de correu"

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mass_mailing_contacts
msgid "Create a mailing contact"
msgstr "Crea un contacte de correu electrònic"

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_create_mass_mailings_from_campaign
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mass_mailings_from_campaign
msgid "Create a new mailing"
msgstr "Creeu un correu nou"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Create an Alternative Version"
msgstr "Crea una versió alternativa"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_import__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_to_list__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_schedule_date__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_test__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription_optout__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__create_uid
msgid "Created by"
msgstr "Creat per"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_import__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_to_list__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_filter__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_schedule_date__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_test__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription_optout__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__create_date
msgid "Created on"
msgstr "Creat el"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_search
msgid "Creation Date"
msgstr "Data de creació"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_search
msgid "Creation Period"
msgstr "Període de creació"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Custom"
msgstr "Personalitzat"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/mass_mailing_html_field.js:0
msgid "DRAG BUILDING BLOCKS HERE"
msgstr "ARROSSEGA ELS BLOCS DE CONSTRUCCIÓ AQUÍ"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options_border_line_widgets
msgid "Dashed"
msgstr "Ratllat"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "Date"
msgstr "Data"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__calendar_date
msgid "Date at which the mailing was or will be sent."
msgstr "Data en què es va enviar o s'enviarà el correu."

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__ab_testing_schedule_datetime
#: model:ir.model.fields,help:mass_mailing.field_utm_campaign__ab_testing_schedule_datetime
msgid ""
"Date that will be used to know when to determine and send the winner mailing"
msgstr ""
"Data que s' usarà per saber quan determinar i enviar les correu guanyadores"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_res_config_settings__mass_mailing_outgoing_mail_server
msgid "Dedicated Server"
msgstr "Servidor Dèdicat"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_options
msgid "Default"
msgstr "Per defecte"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_options
msgid "Default Reversed"
msgstr "Per defecte invertit"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid "Default Server"
msgstr "Servidor predeterminat"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "Delete"
msgstr "Eliminar"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "Delete Blocks"
msgstr "Eliminar Blocs"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_three_columns
msgid ""
"Delete the above image or replace it with a picture that illustrates your "
"message. Click on the picture to change its <em>rounded corner</em> style."
msgstr ""
"Suprimeix la imatge anterior o substitueix-la per una imatge que il·lustre "
"el teu missatge. Feu clic a la imatge per canviar-ne l'estil de "
"la<em>cantonada arrodonida</em>."

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__delivered
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__delivered
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__trace_status__sent
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
msgid "Delivered"
msgstr "Lliurat"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "Delivered (%)"
msgstr "S' ha lliurat (%)"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mass_mailing.editor.xml:0
msgid "Design"
msgstr "Disseny"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/snippets.editor.js:0
msgid "Design Options"
msgstr "Opcions de disseny"

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.mailing_mailing_action_mail
msgid "Design a striking email, define recipients and track its results."
msgstr ""
"Dissenyeu un correu electrònic sorprenent, defineixen destinataris i segueix"
" els resultats."

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
msgid "Design added to the %s Templates!"
msgstr "Disseny afegit a les plantilles %s!"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
msgid "Design removed from the %s Templates!"
msgstr "S'ha suprimit el disseny de les plantilles %s!"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge__dest_list_id
msgid "Destination Mailing List"
msgstr "Llista de correu de destí"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_import_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_schedule_date_view_form
msgid "Discard"
msgstr "Descartar"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Discount Offer"
msgstr "Descompte oferit"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_media_list
msgid "Discover"
msgstr "Descobri"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_showcase
msgid "Discover all the features"
msgstr "Descobreix totes les característiques"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_import__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_to_list__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_filter__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_schedule_date__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_test__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription_optout__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__display_name
msgid "Display Name"
msgstr "Nom mostrat"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__res_id
msgid "Document ID"
msgstr "ID del document"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__model
msgid "Document model"
msgstr "Model de Document"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mailing_domain
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_filter_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_filter_view_tree
msgid "Domain"
msgstr "Domini "

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/mailing_m2o_filter.js:0
msgid "Domain field"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "Don't forget to send your preferred version"
msgstr "No oblideu enviar la vostra versió preferida"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
msgid "Don't worry, the mailing contact we created is an internal user."
msgstr ""
"No et preocupis, el contacte de correu que hem creat és un usuari intern."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options_border_line_widgets
msgid "Dotted"
msgstr "Puntejat"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options_border_line_widgets
msgid "Double"
msgstr "Doble"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "Double click an icon to replace it with one of your choice."
msgstr "Fes doble clic en una icona per canviar-la per una a la teva elecció"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_mailing__state__draft
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace_report__state__draft
msgid "Draft"
msgstr "Esborrany"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Duplicate"
msgstr "Duplicar"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "Duplicate blocks and columns to add more features."
msgstr "Duplicar els blocs i columnes per afegir més característiques. "

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__failure_type__mail_dup
msgid "Duplicated Email"
msgstr "Correu duplicat"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_coupon_code
msgid "ENDOFSUMMER20"
msgstr "ENDOFSUMMER20"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "Edit Styles"
msgstr "Modificar  estils"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/mass_mailing_html_field.js:0
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__email
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__email
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_mailing__mailing_type__mail
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__trace_type__mail
msgid "Email"
msgstr "Correu electrònic"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_tree
msgid "Email Blacklisted"
msgstr "Llista negra de correu- e"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Email Content"
msgstr "Contingut de correu electrònic"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/res_users.py:0
#: model:ir.ui.menu,name:mass_mailing.mass_mailing_menu_root
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid "Email Marketing"
msgstr "Màrqueting per correu"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/ir_mail_server.py:0
msgid ""
"Email Marketing uses it as its default mail server to send mass mailings"
msgstr ""
"El màrqueting per correu el fa servir com a servidor de correu per defecte "
"per enviar correus massius"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mail_thread
msgid "Email Thread"
msgstr "Fil de correus"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_portal_subscription_blocklist.xml:0
msgid "Email added to our blocklist"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mail_compose_message
msgid "Email composition wizard"
msgstr "Assistent de redacció d'emails"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_portal_subscription_blocklist.xml:0
msgid "Email removed from our blocklist"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
msgid "Emails"
msgstr "Correus electrònics"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mailing_trace_ids
msgid "Emails Statistics"
msgstr "Estadístiques de correus electrònics"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
msgid "Engagement on %(expected)i %(mailing_type)s Sent"
msgstr "Compromís en %(expected)i %(mailing_type)s enviat(s)"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__error
msgid "Error"
msgstr "Error"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Event"
msgstr "Esdeveniment"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_media_list
msgid "Event heading"
msgstr "Encapçalament de l'esdeveniment"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__trace_status__error
msgid "Exception"
msgstr "Excepció"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_search
msgid "Exclude Blacklisted Emails"
msgstr "Exclou els correus de la llista negra"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe_form
msgid "Exclude Me"
msgstr "Exclou-me"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_search
msgid "Exclude Opt Out"
msgstr "Excloure els que han refusat enviaments"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__expected
msgid "Expected"
msgstr "Previst"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_search
msgid "Extended Filters..."
msgstr "Filtres estesos..."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.social_links
msgid "Facebook"
msgstr "Facebook"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__failed
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
msgid "Failed"
msgstr "Fallits"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__failure_reason
msgid "Failure reason"
msgstr "Motiu erroni"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__failure_type
msgid "Failure type"
msgstr "Tipus de error"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__favorite
msgid "Favorite"
msgstr "Favorit"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__favorite_date
msgid "Favorite Date"
msgstr "Data preferida"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mailing_filter_id
msgid "Favorite Filter"
msgstr "Filtre preferit"

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.mailing_filter_action
#: model:ir.ui.menu,name:mass_mailing.mailing_filter_menu_action
msgid "Favorite Filters"
msgstr "Filtres preferits"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mailing_filter_domain
msgid "Favorite filter domain"
msgstr "Domini del filtre preferit"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_three_columns
msgid "Feature One"
msgstr "Característica u"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_three_columns
msgid "Feature Three"
msgstr "Característica tres"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_three_columns
msgid "Feature Two"
msgstr "Característica dos"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Features"
msgstr "Característiques"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Features Grid"
msgstr "Característiques de la graella"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/controllers/main.py:0
msgid "Feedback from %(author_name)s"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
msgid "File size exceeds configured maximum (%s bytes)"
msgstr "La grandària de l'arxiu supera el màxim configurat (%s bytes)"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_filter__mailing_domain
msgid "Filter Domain"
msgstr "Filtrar dominis"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_filter__name
msgid "Filter Name"
msgstr "Nom del filtre"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/mass_mailing_html_field.js:0
msgid "Filter templates"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_filter_view_search
msgid "Filters saved by me"
msgstr "Filtres desats per mi"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features
msgid "First Feature"
msgstr "Primera característica"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__first_name
msgid "First Name"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_showcase
msgid "First feature"
msgstr "Primera característica"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "First list of Features"
msgstr "Primera llista de característiques "

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Fit content"
msgstr "Ajusta al contingut"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_follower_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_follower_ids
msgid "Followers"
msgstr "Seguidors"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_partner_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidors (Partners)"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Font Family"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icona Font Awesome p.e. fa-tasks"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Footer Center"
msgstr "Centre del peu de pàgina"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Footer Left"
msgstr "Peu de pàgina esquerre"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Footers"
msgstr "Peus"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_company_team
msgid ""
"Founder and chief visionary, Tony is the driving force behind the company. "
"He loves to keep his hands full by participating in the development of the "
"software, marketing, and customer experience strategies."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__email_from
msgid "From"
msgstr "Des de"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Full"
msgstr "Complet"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mass_mailing.editor.xml:0
msgid "Fullscreen"
msgstr "Pantalla Complerta"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_product_list
msgid "Furniture"
msgstr "FunituraName"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_image_text
msgid ""
"Get your inside sales (CRM) fully integrated with online sales (eCommerce), "
"in-store sales (Point of Sale) and marketplaces like Amazon."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "Great Value"
msgstr "Gran valor"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mail_blacklist_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_filter_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_subscription_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "Group By"
msgstr "Agrupar per"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_search
msgid "Group By..."
msgstr "Agrupa per..."

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_ir_http
msgid "HTTP Routing"
msgstr "Enrutament HTTP"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__has_message
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__has_message
msgid "Has Message"
msgstr "Té un missatge"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Headers"
msgstr "Capçaleres"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Heading 1"
msgstr "Encapçalament 1"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Heading 2"
msgstr "Encapçalament 2"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Heading 3"
msgstr "Encapçalament 3"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Height"
msgstr "Alçada"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_coupon_code
msgid "Here's your coupon code - but hurry! Ends 9/28"
msgstr "Aquí està el seu codi d'estat, però de pressa! Fis 9/28"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__utm_campaign__ab_testing_winner_selection__clicks_ratio
msgid "Highest Click Rate"
msgstr "Taxa de clic més alta"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__utm_campaign__ab_testing_winner_selection__opened_ratio
msgid "Highest Open Rate"
msgstr "Taxa d' obertura més alta"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__utm_campaign__ab_testing_winner_selection__replied_ratio
msgid "Highest Reply Rate"
msgstr "Taxa de respostes més alta"

#. module: mass_mailing
#: model:mailing.subscription.optout,name:mass_mailing.mailing_subscription_optout_data_1
msgid "I changed my mind"
msgstr ""

#. module: mass_mailing
#: model:mailing.subscription.optout,name:mass_mailing.mailing_subscription_optout_data_0
msgid "I never subscribed to this list"
msgstr ""

#. module: mass_mailing
#: model:mailing.subscription.optout,name:mass_mailing.mailing_subscription_optout_data_2
msgid "I receive too many emails from this list"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_import__id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_to_list__id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_filter__id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge__id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_schedule_date__id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_test__id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription__id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription_optout__id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__id
msgid "ID"
msgstr "ID"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_trace__mail_mail_id_int
msgid ""
"ID of the related mail_mail. This field is an integer field because the "
"related mail_mail can be deleted separately from its statistics. However the"
" ID is needed for several action and controllers."
msgstr ""
"ID del correu electrònic relacionat. Aquest camp és un camp enter perquè el "
"correu electrònic corresponent es pot eliminar separadament de les seves "
"estadístiques. No obstant això, es necessita l' ID per a diverses accions i "
"controladors."

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__activity_exception_icon
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "Icon"
msgstr "Icona"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icona que indica una activitat d'excepció."

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact__message_needaction
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""
"Si està marcat, hi ha nous missatges que requereixen la vostra atenció."

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__ab_testing_enabled
msgid ""
"If checked, recipients will be mailed only once for the whole campaign. This"
" lets you send different mailings to randomly selected recipients and test "
"the effectiveness of the mailings, without causing duplicate messages."
msgstr ""
"Si està marcat, els destinataris només seran enviats una vegada per a tota "
"la campanya. Això us permet enviar diferents correus a destinataris "
"seleccionats aleatòriament i provar l' eficàcia de les llistes de correu, "
"sense causar missatges duplicats."

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact__message_has_error
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact__message_has_sms_error
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__message_has_error
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Si està marcat, alguns missatges tenen un error d'entrega."

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mail_compose_message__mass_mailing_name
msgid ""
"If set, a mass mailing will be created so that you can track its results in "
"the Email Marketing app."
msgstr ""
"Si s'estableix, es crearà un enviament massiu perquè pugueu fer un seguiment"
" dels seus resultats a l'aplicació de màrqueting de correu electrònic."

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact__is_blacklisted
#: model:ir.model.fields,help:mass_mailing.field_mailing_subscription__is_blacklisted
msgid ""
"If the email address is on the blacklist, the contact won't receive mass "
"mailing anymore, from any list"
msgstr ""
"Si l' adreça de correu electrònic està a la llista negra, el contacte ja no "
"rebrà cap correu massa, des de qualsevol llista"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Image - Text"
msgstr "Imatge - Text"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_options
msgid "Image Text Image"
msgstr "Imatge de text"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
msgid ""
"Image size excessive, imported images must be smaller than 42 million pixel"
msgstr ""
"La mida de la imatge és excessiva, les imatges penjades han de ser inferiors"
" a 42 milions de píxels."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_options
msgid "Images"
msgstr "Imatges"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_import_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_kanban
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_tree
msgid "Import"
msgstr "Importar"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_kanban
msgid "Import Contacts"
msgstr "Importa contactes"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/wizard/mailing_contact_import.py:0
#: model:ir.actions.act_window,name:mass_mailing.mailing_contact_import_action
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_import_view_form
msgid "Import Mailing Contacts"
msgstr "Importar els contactes de correu"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing_contact.py:0
msgid "Import Template for Mailing List Contacts"
msgstr "Importa una plantilla per als contactes de la llista de correu"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_import_view_form
msgid "Import contacts in"
msgstr "Importa contactes a"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_mailing__state__in_queue
msgid "In Queue"
msgstr "A la cua"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/mass_mailing_html_field.js:0
msgid "Inline field"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Inner Content"
msgstr "Contingut interior"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.social_links
msgid "Instagram"
msgstr "Instagram"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__failure_type__mail_email_invalid
msgid "Invalid email address"
msgstr "Adreça de correu electrònic no vàlida"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__failure_type__mail_from_invalid
msgid "Invalid from address"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_company_team
msgid "Iris Joe, CFO"
msgstr "Iris Joe, CFO"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_company_team
msgid ""
"Iris, with her international experience, helps us easily understand the "
"numbers and improves them. She is determined to drive success and delivers "
"her professional acumen to bring the company to the next level."
msgstr ""
"L'Iris, amb la seva experiència internacional, ens ajuda a entendre "
"fàcilment els números i millorar-los. Està decidit a conduir l'èxit i "
"entregar els seus acumen professionals per portar l'empresa al següent "
"nivell."

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__is_ab_test_sent
msgid "Is Ab Test Sent"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__is_body_empty
msgid "Is Body Empty"
msgstr "El cos està buit"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_is_follower
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_is_follower
msgid "Is Follower"
msgstr "És un seguidor"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__is_mailing_campaign_activated
msgid "Is Mailing Campaign Activated"
msgstr "És una campanya d'enviaments activa"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__ab_testing_is_winner_mailing
msgid "Is the Winner of its Campaign"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Italic"
msgstr "Cursiva"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Items"
msgstr "Articles"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_call_to_action
msgid "Join us and make your company a better place."
msgstr "Uneix-te a nosaltres per fer de la companyia un lloc millor."

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__kpi_mail_required
msgid "KPI mail required"
msgstr "Cal correu KPI"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__keep_archives
msgid "Keep Archives"
msgstr "Conservar arxius"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.theme_default_template
msgid "LOGIN"
msgstr "INICIAR SESSIÓ"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__lang
msgid "Language"
msgstr "Idioma"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_alert_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Large"
msgstr "Gran"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_showcase
msgid "Last Feature"
msgstr "Última funcionalitat"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__last_name
msgid "Last Name"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
msgid "Last State Update"
msgstr "Darrera actualització de l' estat"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_import__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_to_list__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_filter__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_schedule_date__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_test__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription_optout__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__write_uid
msgid "Last Updated by"
msgstr "Última actualització per"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_import__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_to_list__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_filter__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_schedule_date__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_test__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription_optout__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__write_date
msgid "Last Updated on"
msgstr "Última actualització el"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_hr_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Left"
msgstr "Esquerra"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Left Logo"
msgstr "Logotip esquerre"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Left Text"
msgstr "Text esquerre"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
msgid "Let's try the Email Marketing app."
msgstr "Intentem l'aplicació de mercat per correu electrònic."

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_link_tracker
#: model:ir.ui.menu,name:mass_mailing.link_tracker_menu_mass_mailing
msgid "Link Tracker"
msgstr "Rastrejador d'enllaços"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_link_tracker_click
msgid "Link Tracker Click"
msgstr "Clic al seguidor d' enllaç"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Link Trackers"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__link_trackers_count
msgid "Link Trackers Count"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
msgid ""
"Link Trackers will measure how many times each link is clicked as well as "
"the proportion of %s who clicked at least once in your mailing."
msgstr ""
"Els rastrejadors d'enllaços mesuraran quantes vegades es fa clic a cada "
"enllaç, així com la proporció de %s que han fet clic almenys una vegada en "
"el vostre correu."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.social_links
msgid "LinkedIn"
msgstr "LinkedIn"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Links"
msgstr "Enllaços"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__links_click_ids
msgid "Links click"
msgstr "Clic als enllaços"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_import__mailing_list_ids
msgid "Lists"
msgstr "Llistes"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_event
msgid "London, United Kingdom"
msgstr "Londres, Regne Unit"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__mail_mail_id
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace_report__mailing_type__mail
msgid "Mail"
msgstr "Correu"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mail_blacklist
msgid "Mail Blacklist"
msgstr "Envia la llista negra de correu"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Mail Body"
msgstr "Cos del correu"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_form
msgid "Mail ID"
msgstr "ID del correu"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__mail_mail_id_int
msgid "Mail ID (tech)"
msgstr "ID del correu (tech)"

#. module: mass_mailing
#: model:ir.actions.server,name:mass_mailing.ir_cron_mass_mailing_ab_testing_ir_actions_server
msgid "Mail Marketing: A/B Testing"
msgstr "Enviament de correu: provació A/B"

#. module: mass_mailing
#: model:ir.actions.server,name:mass_mailing.ir_cron_mass_mailing_queue_ir_actions_server
msgid "Mail Marketing: Process queue"
msgstr "Mail Marketing: cua de procès"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mail_render_mixin
msgid "Mail Render Mixin"
msgstr "Bloc de correu"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_ir_mail_server
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mail_server_id
#: model:ir.model.fields,field_description:mass_mailing.field_res_config_settings__mass_mailing_mail_server_id
msgid "Mail Server"
msgstr "Servidor de correu"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mail_server_available
msgid "Mail Server Available"
msgstr "Servidor de correu disponible"

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_view_mail_mail_statistics_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_link_tracker_click__mailing_trace_id
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mail_statistics_graph
msgid "Mail Statistics"
msgstr "Estadístiques de correu"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_tree_mail
msgid "Mail Traces"
msgstr "Traces de correu"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_test__mass_mailing_id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__mass_mailing_id
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "Mailing"
msgstr "Enviament correu"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Mailing Background"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__campaign
msgid "Mailing Campaign"
msgstr "Campanya de correu electrònic"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_res_config_settings__group_mass_mailing_campaign
msgid "Mailing Campaigns"
msgstr "Campanyes de correu"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing_subscription.py:0
#: model:ir.model,name:mass_mailing.model_mailing_contact
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_subscription_view_tree
msgid "Mailing Contact"
msgstr "Contacte de correu"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_contact_import
msgid "Mailing Contact Import"
msgstr "Importació de contactes de correu"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing_subscription.py:0
msgid "Mailing Contacts"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_ir_model__is_mailing_enabled
msgid "Mailing Enabled"
msgstr "S' ha habilitat la correu"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_filter
msgid "Mailing Favorite Filters"
msgstr "Filtres preferits dels correus"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_filter_view_search
msgid "Mailing Filters"
msgstr "Filtres de correus"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_list
#: model:ir.model.fields,field_description:mass_mailing.field_mail_compose_message__mailing_list_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_to_list__mailing_list_id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription__list_id
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_subscription_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "Mailing List"
msgstr "Llista d'enviament"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe_form
msgid "Mailing List #"
msgstr ""

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_view_mass_mailing_contacts
#: model:ir.ui.menu,name:mass_mailing.menu_email_mass_mailing_contacts
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_graph
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_pivot
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_tree
msgid "Mailing List Contacts"
msgstr "Contactes de llista de correu"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_subscription
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_subscription_view_form
msgid "Mailing List Subscription"
msgstr "Subscripció de llista de correu"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_subscription_view_graph
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_subscription_view_pivot
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_subscription_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_subscription_view_tree
msgid "Mailing List Subscriptions"
msgstr "Subscripció de llista de correu"

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_view_mass_mailing_lists
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__list_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__contact_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge__src_list_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__contact_list_ids
#: model:ir.ui.menu,name:mass_mailing.mass_mailing_mailing_list_menu
#: model:ir.ui.menu,name:mass_mailing.menu_email_mass_mailing_lists
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_tree
msgid "Mailing Lists"
msgstr "Llistes de correu"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Mailing Lists:"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_report_deactivated
msgid "Mailing Reports Turned Off"
msgstr "Informes de correu desactivats"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_report_deactivated
msgid ""
"Mailing Reports have been turned off for all users. <br/>\n"
"                                If needed, they can be turned back on from the"
msgstr ""
"S'han desactivat els informes de correu per a tots els usuaris. <br/>\n"
"                                Si cal, es poden tornar a activar des de"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_trace
msgid "Mailing Statistics"
msgstr "Estadístiques de correu"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_subscription_optout
msgid "Mailing Subscription Reason"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe_form
msgid "Mailing Subscriptions"
msgstr "Subscripcions de correu"

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_mail_mass_mailing_test
msgid "Mailing Test"
msgstr "Prova de correu"

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.mailing_trace_action
#: model:ir.ui.menu,name:mass_mailing.menu_email_statistics
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_tree
msgid "Mailing Traces"
msgstr "Traces de correu"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mailing_type
msgid "Mailing Type"
msgstr "Tipus de correu"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mailing_type_description
msgid "Mailing Type Description"
msgstr "Descripció del tipus de correu"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/wizard/mailing_mailing_test.py:0
msgid "Mailing addresses incorrect: %s"
msgstr "Adreces de correu incorrectes: %s"

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mass_mailing_contacts
msgid ""
"Mailing contacts allow you to separate your marketing audience from your "
"business contact directory."
msgstr ""
"Els contactes de correu us permeten separar la vostra audiència de "
"màrqueting del vostre directori de contactes de l' empresa."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_filter_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_filter_view_tree
msgid "Mailing filters"
msgstr "Filtres d'envio"

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_create_mass_mailings_from_campaign
#: model:ir.actions.act_window,name:mass_mailing.action_view_mass_mailings_from_campaign
#: model:ir.actions.act_window,name:mass_mailing.mailing_mailing_action_mail
#: model:ir.ui.menu,name:mass_mailing.mass_mailing_menu
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_tree
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_view_calendar
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_kanban
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "Mailings"
msgstr "Enviaments"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "Mailings that are assigned to me"
msgstr "Llistes de correu que se m' han assignat"

#. module: mass_mailing
#: model:res.groups,name:mass_mailing.group_mass_mailing_campaign
msgid "Manage Mass Mailing Campaigns"
msgstr "Gestionar les campanyes de correu massiu"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid "Manage mass mailing campaigns"
msgstr "Gestionar les campanyes de correu massiu"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__utm_campaign__ab_testing_winner_selection__manual
msgid "Manual"
msgstr "Manual"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_form
msgid "Marketing"
msgstr "Màrqueting"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Marketing Content"
msgstr "Contingut de mercat"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Masonry"
msgstr "Paleta"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__name
msgid "Mass Mail"
msgstr "Correu de massa"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_link_tracker__mass_mailing_id
#: model:ir.model.fields,field_description:mass_mailing.field_link_tracker_click__mass_mailing_id
#: model:ir.model.fields,field_description:mass_mailing.field_mail_compose_message__mass_mailing_id
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail__mailing_id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_schedule_date__mass_mailing_id
#: model:ir.ui.menu,name:mass_mailing.mailing_mailing_menu_technical
#: model_terms:ir.ui.view,arch_db:mass_mailing.link_tracker_click_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.link_tracker_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
msgid "Mass Mailing"
msgstr "Enviament massiu de correus "

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/ir_mail_server.py:0
msgid "Mass Mailing \"%s\""
msgstr "Correu massiu «%s»"

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.mailing_trace_report_action_mail
#: model:ir.ui.menu,name:mass_mailing.mailing_menu_report_mailing
msgid "Mass Mailing Analysis"
msgstr "Anàlisi de correu massiu"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_compose_message__campaign_id
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_search
msgid "Mass Mailing Campaign"
msgstr "Campanya de correu massiu"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_compose_message__mass_mailing_name
msgid "Mass Mailing Name"
msgstr "Nom de correu massiu"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_trace_report
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_graph
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_pivot
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_tree
msgid "Mass Mailing Statistics"
msgstr "Estadístiques de correu massiu"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__mailing_ids
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__mailing_mail_ids
msgid "Mass Mailings"
msgstr "Mails de massa"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Media List"
msgstr "Contingut Multimèdia"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_media_list
msgid "Media heading"
msgstr "Encapçalament de contingut multimèdia"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__medium_id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__medium_id
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_alert_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Medium"
msgstr "Mitjà"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_portal_subscription_form.xml:0
msgid "Membership updated"
msgstr ""

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.mailing_list_merge_action
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_merge_view_form
msgid "Merge"
msgstr "Combina"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_list_merge
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_merge_view_form
msgid "Merge Mass Mailing List"
msgstr "Fusiona la llista de correu de masses"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge__merge_options
msgid "Merge Option"
msgstr "Opció de fusió"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_list_merge__merge_options__new
msgid "Merge into a new mailing list"
msgstr "Fusiona en una nova llista de correu"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_list_merge__merge_options__existing
msgid "Merge into an existing mailing list"
msgstr "Fusiona en una llista de correu existent"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_has_error
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_has_error
msgid "Message Delivery error"
msgstr "Error d'entrega del missatge"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__message_id
msgid "Message-ID"
msgstr "ID-Missatge"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_ids
msgid "Messages"
msgstr "Missatges"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_company_team
msgid "Mich Stark, COO"
msgstr "Mich Stark, COO"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_company_team
msgid ""
"Mich loves taking on challenges. With his multi-year experience as "
"Commercial Director in the software industry, Mich has helped the company to"
" get where it is today. Mich is among the best minds."
msgstr ""
"A Mich li encanten els reptes. Amb la seva experiència de diversos anys com "
"a director comercial de la indústria de programari, Mich ha ajudat a "
"l'empresa a arribar on és avui. Mich està entre les millors ments."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.theme_default_template
msgid ""
"Michael Fletcher<br/>\n"
"                   <span style=\"font-size: 12px; font-weight: bolder;\">Customer Service</span>"
msgstr ""
"Michael Fletcher<br/>\n"
"                   <span style=\"font-size: 12px; font-weight: bolder;\">Servei de client</span>"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__failure_type__mail_email_missing
msgid "Missing email address"
msgstr "Falta l'adreça de correu electrònic"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__failure_type__mail_from_missing
msgid "Missing from address"
msgstr ""

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/snippets.editor.js:0
msgid "Mobile Preview"
msgstr "Vista prèvia adaptativa"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/mailing_m2o_filter.js:0
msgid "Model field"
msgstr "Camp model"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_ir_model
msgid "Models"
msgstr "Models"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
msgid "More"
msgstr "Més"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_color_blocks_2
msgid "More Details"
msgstr "Més detalls"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
msgid "More Info"
msgstr "Més informació"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_options
msgid "Mosaic"
msgstr "Mosaic"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Venciment de l'activitat"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_header_text_social
msgid "My Company"
msgstr "La meva empresa"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_filter_view_search
msgid "My Filters"
msgstr "Els meus filtres"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "My Mailings"
msgstr "Els meus enviaments"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__name
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Name"
msgstr "Nom"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_search
msgid "Name / Email"
msgstr "Nom / Correu electrònic"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge__new_list_name
msgid "New Mailing List Name"
msgstr "Nou nom de llista de correu"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/wizard/mailing_contact_import.py:0
msgid "New contacts imported"
msgstr "S'han importat contactes nous"

#. module: mass_mailing
#: model:utm.campaign,title:mass_mailing.mass_mail_campaign_1
msgid "Newsletter"
msgstr "Butlletí de notícies"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Proper esdeveniment del calendari d'activitats"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Data límit de la següent activitat"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__activity_summary
msgid "Next Activity Summary"
msgstr "Resum de la següent activitat"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__activity_type_id
msgid "Next Activity Type"
msgstr "Tipus de la següent activitat"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__next_departure_is_past
msgid "Next Departure Is Past"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
msgid "No %s address bounced yet!"
msgstr "Encara no s'ha rebut cap adreça %s!"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
msgid "No %s clicked your mailing yet!"
msgstr "Encara no s'ha fet clic a %s al vostre correu!"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
msgid "No %s opened your mailing yet!"
msgstr "No hi ha %s que hagi obert el vostre correu!"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
msgid "No %s received your mailing yet!"
msgstr "Cap %s d'ells ha rebut encara el vostre mail!"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
msgid "No %s replied to your mailing yet!"
msgstr "Cap %s d'ells ha respost encara el vostre mail!"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
msgid "No Link Tracker for that mailing!"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.mailing_trace_report_action_mail
msgid "No Mailing Data yet!"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/wizard/mailing_contact_import.py:0
msgid ""
"No contacts were imported. All email addresses are already in the mailing "
"list."
msgstr ""
"No s'ha importat cap contacte. Totes les adreces de correu electrònic ja es "
"troben a la llista de correu."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
msgid "No customization"
msgstr "Sense personalització"

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mail_mail_statistics_mailing
msgid "No data yet!"
msgstr "Encara no hi han dades!"

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.mailing_subscription_action_report_optout
#: model_terms:ir.actions.act_window,help:mass_mailing.mailing_subscription_optout_action
msgid "No data yet."
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
msgid "No mailing campaign has been found"
msgstr "No s' ha trobat cap campanya de correu"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
msgid ""
"No mailing for this A/B testing campaign has been sent yet! Send one first "
"and try again later."
msgstr ""
"Encara no s'ha enviat cap correu per a aquesta campanya de proves A/B! "
"Envia'n un primer i torna-ho a provar més tard."

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mass_mailing_lists
msgid ""
"No need to import mailing lists, you can send mailings to contacts saved in "
"other Odoo apps."
msgstr ""
"No cal importar llistes de correu, podeu enviar llistes de correu als "
"contactes desats en d' altres aplicacions Odoo."

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.mailing_filter_action
msgid "No saved filter yet!"
msgstr "Encara no hi ha cap filtre desat!"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
msgid "No support"
msgstr "No soportat"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/wizard/mailing_contact_import.py:0
msgid "No valid email address found."
msgstr "No s'ha trobat cap adreça electrònica vàlida."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "None"
msgstr "Cap"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__email_normalized
msgid "Normalized Email"
msgstr "Correu normalitzat"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_trace__email
msgid "Normalized email address"
msgstr "Adreça de correu normalitzat"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_portal_subscription_form.xml:0
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe_form
msgid "Not subscribed"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_view_generic
msgid "Nothing to see yet!"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_needaction_counter
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_needaction_counter
msgid "Number of Actions"
msgstr "Nombre d'accions"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__contact_count_blacklisted
msgid "Number of Blacklisted"
msgstr "Nombre en llista negra"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__clicks_ratio
msgid "Number of Clicks"
msgstr "Nombre de clics"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__contact_count
msgid "Number of Contacts"
msgstr "Nombre de contactes"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__contact_count_email
msgid "Number of Emails"
msgstr "Nombre de missatges"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__mailing_count
msgid "Number of Mailing"
msgstr "Nombre de correu"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__mailing_mail_count
msgid "Number of Mass Mailing"
msgstr "Nombre de correu de massa"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__contact_count_opt_out
msgid "Number of Opted-out"
msgstr "Nombre de baixes"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_merge_view_form
msgid "Number of Recipients"
msgstr "Nombre de destinataris"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_kanban
msgid "Number of bounced email."
msgstr "Nombre de correus electrònics rebotats."

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_has_error_counter
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_has_error_counter
msgid "Number of errors"
msgstr "Nombre d'errors"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact__message_needaction_counter
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Nombre de missatges que requereixen una acció"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact__message_has_error_counter
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Nombre de missatges amb error d'entrega"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Numbers"
msgstr "Números"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_discount1
msgid "ON YOUR NEXT ORDER!"
msgstr "En el seu proper episodi!"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
msgid "OPENED (%i)"
msgstr "OBERTS (%i)"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_image_text
msgid "Omnichannel sales"
msgstr "Vendes omnicanal"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid ""
"Once the best version is identified, we will send the best one to the "
"remaining recipients."
msgstr ""
"Una vegada s' identifica la millor versió, enviarem el millor dels "
"destinataris que queden."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid ""
"Once you send these emails, they'll be making a grand entrance in all the "
"inboxes, creating quite the buzz!"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
msgid "Open Date"
msgstr "Data d'inici"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_tree
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_tree_mail
msgid "Open Recipient"
msgstr "Obre el destinatari"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__opened
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__opened
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__trace_status__open
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Opened"
msgstr "Obert"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "Opened (%)"
msgstr "S' ha obert (%)"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__open_datetime
msgid "Opened On"
msgstr "S' ha obert el"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__opened_ratio
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__opened_ratio
msgid "Opened Ratio"
msgstr "Relació oberta"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__opt_out
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription__opt_out
msgid "Opt Out"
msgstr "Desinscripció"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact__opt_out
msgid ""
"Opt out flag for a specific mailing list. This field should not be used in a"
" view without a unique and active mailing list context."
msgstr ""
"Indicador de sortida per a una llista de correu específica. Aquest camp no "
"s'ha d'utilitzar en una vista sense un context únic i actiu de llista de "
"correu."

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.mailing_subscription_action_report_optout
#: model:ir.ui.menu,name:mass_mailing.mailing_menu_report_subscribe_reason
msgid "Opt-Out Report"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_tree
msgid "Opt-out (%)"
msgstr "Baixes (%)"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_blacklist__opt_out_reason_id
msgid "Opt-out Reason"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__failure_type__mail_optout
msgid "Opted Out"
msgstr "Optat per sortir"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_search
msgid "Opted-out"
msgstr "Optat per sortir"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"Idioma opcional de traducció (codi ISO) per a seleccionar en enviar un "
"correu electrònic. Si no està establert, s'usarà la versió anglesa. Això "
"normalment hauria de ser una expressió de substitució que proveeixi l'idioma"
" apropiat, p. ex. {{ object.partner_id.lang }}."

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.mailing_subscription_optout_action
#: model:ir.ui.menu,name:mass_mailing.mailing_subscription_optout_menu
msgid "Optout Reasons"
msgstr ""

#. module: mass_mailing
#: model:mailing.subscription.optout,name:mass_mailing.mailing_subscription_optout_data_4
msgid "Other"
msgstr "Altres"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_references
msgid "Our References"
msgstr "Les Nostres Referències"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__trace_status__outgoing
msgid "Outgoing"
msgstr "Sortida"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mail_mail
msgid "Outgoing Mails"
msgstr "Correu de sortida"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Padding ↔"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Padding ↕"
msgstr "Farciment ↕"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__pending
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__pending
msgid "Pending"
msgstr "Pendent"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__contact_pct_blacklisted
msgid "Percentage of Blacklisted"
msgstr "Percentatge en llista negra"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__contact_pct_bounce
msgid "Percentage of Bouncing"
msgstr "Percentatge de rebots"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__contact_pct_opt_out
msgid "Percentage of Opted-out"
msgstr "Percentatge de baixes"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__ab_testing_pc
msgid ""
"Percentage of the contacts that will be mailed. Recipients will be chosen "
"randomly."
msgstr ""
"Percentatge dels contactes que seran enviats. Els destinataris s' escolliran"
" aleatòriament."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid "Pick a dedicated outgoing mail server for your mass mailings"
msgstr ""
"Trieu un servidor de correu de sortida dedicat per als vostres enviaments "
"massius"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
msgid "Pick the <b>email subject</b>."
msgstr "Trieu <b>l'assumpte del correu electrònic</b>."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Picture"
msgstr "Imatge"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_themes
msgid "Plain Text"
msgstr "Text net"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/mailing_portal_subscription_feedback.js:0
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe_form
msgid "Please let us know why you updated your subscription."
msgstr ""

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/mailing_portal_subscription_feedback.js:0
msgid "Please let us know why you want to be in our block list."
msgstr ""

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/mailing_m2o_filter.js:0
msgid "Please provide a name for the filter"
msgstr "Introduïu un nom per al filtre"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_media_list
msgid "Post heading"
msgstr "Encapçalament de la publicació"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__reply_to
msgid "Preferred Reply-To Address"
msgstr "Adreça de Respon preferida a"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__preview
msgid "Preview"
msgstr "Vista prèvia"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Preview Text"
msgstr "Vista prèvia de text"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Primary Buttons"
msgstr "Botons primaris"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__process
msgid "Process"
msgstr "Procés"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__processing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__trace_status__process
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
msgid "Processing"
msgstr "Processant"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Promo Code"
msgstr "Codi de promoció"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_text_highlight
msgid "Put the focus on what you have to say!"
msgstr "Concentra't en el que has de dir!"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating
msgid "Quality"
msgstr "Qualitat"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
msgid "RECEIVED (%i)"
msgstr "REBUTS (%i)"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
msgid "REPLIED (%i)"
msgstr "RESPOST (%i)"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Rating"
msgstr "Valoració"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__rating_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__rating_ids
msgid "Ratings"
msgstr "Valoracions"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_image_text
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_text_image
msgid "Read More"
msgstr "Llegeix més"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
msgid "Ready for take-off!"
msgstr "Preparats per marxar!"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription__opt_out_reason_id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription_optout__name
#: model_terms:ir.ui.view,arch_db:mass_mailing.mail_blacklist_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_subscription_view_search
msgid "Reason"
msgstr "Raó"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Received"
msgstr "Rebuda"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__received_ratio
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__received_ratio
msgid "Received Ratio"
msgstr "Proporció rebuda"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_view_calendar
msgid "Recipient"
msgstr "Destinatari"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_form
msgid "Recipient Address"
msgstr "Adreça del destinatari"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_mailing__reply_to_mode__update
msgid "Recipient Followers"
msgstr "Segueix el destinatari"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_test__email_to
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_filter_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_filter_view_tree
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_tree
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "Recipients"
msgstr "Destinataris"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_filter__mailing_model_id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mailing_model_id
msgid "Recipients Model"
msgstr "Model de destinataris"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_filter__mailing_model_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mailing_model_name
msgid "Recipients Model Name"
msgstr "Nom del model dels destinataris"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mailing_model_real
msgid "Recipients Real Model"
msgstr "Model real dels destinataris"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_discount1
msgid "Redeem Discount!"
msgstr "Comptador de dispars del Redeem!"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "References"
msgstr "Referències"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_event
msgid "Register Now"
msgstr "Registrar ara"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Regular"
msgstr "Regular"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Reload a favorite filter"
msgstr "Torna a carregar un filtre preferit"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_filter_widget.xml:0
msgid "Remove from Favorites"
msgstr "Eliminar de favorits"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mass_mailing.xml:0
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Remove from Templates"
msgstr "Elimina de les plantilles"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__render_model
msgid "Rendering Model"
msgstr "Model de renderització"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__replied
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__replied
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__trace_status__reply
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Replied"
msgstr "Respost"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "Replied (%)"
msgstr "Respost (%)"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__reply_datetime
msgid "Replied On"
msgstr "Replied On"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__replied_ratio
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__replied_ratio
msgid "Replied Ratio"
msgstr "Relació referenciada"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
msgid "Reply Date"
msgstr "Data de resposta"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__reply_to
msgid "Reply To"
msgstr "Respondre a"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__reply_to_mode
msgid "Reply-To Mode"
msgstr "Mode Respon- a"

#. module: mass_mailing
#: model:ir.ui.menu,name:mass_mailing.menu_mass_mailing_report
msgid "Reporting"
msgstr "Informes"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__user_id
msgid "Responsible"
msgstr "Responsable"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__activity_user_id
msgid "Responsible User"
msgstr "Usuari responsable"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "Restore"
msgstr "Restaurar"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Retry"
msgstr "Reintentar"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_hr_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Right"
msgstr "Dreta"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options_border_widgets
msgid "Round Corners"
msgstr "Cantonades arrodonides"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_has_sms_error
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Error de lliurament SMS"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "Sample Icons"
msgstr "Icones de mostra"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_mailing_test
msgid "Sample Mail Wizard"
msgstr "Assistent de correu mostra"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_filter_widget.xml:0
msgid "Save as Favorite Filter"
msgstr "Desar com a filtre preferit"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_filter__create_uid
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_filter_view_tree
msgid "Saved by"
msgstr "Desat per"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__schedule_type
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_schedule_date_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Schedule"
msgstr "Planificació"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__scheduled
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__scheduled
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
msgid "Scheduled"
msgstr "Planificat"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__scheduled_date
msgid "Scheduled Date"
msgstr "Data programada"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_tree
msgid "Scheduled On"
msgstr "Planificat"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_search
msgid "Scheduled Period"
msgstr "Període programat"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__next_departure
msgid "Scheduled date"
msgstr "Data planificada"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__schedule_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_schedule_date__schedule_date
msgid "Scheduled for"
msgstr "Programat per"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "Scheduled on #{record.next_departure.value}"
msgstr "Programat en # {record. next_ departure. valor}"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "Scheduled on #{record.schedule_date.value}"
msgstr "Programat per # {record. Eplant_ data. valor}"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "Score"
msgstr "Puntuació"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/ir_model.py:0
msgid ""
"Searching Mailing Enabled models supports only direct search using '='' or "
"'!='."
msgstr ""
"La cerca de models de correu habilitat només permeten la cerca directa usant"
" '=' o '!='."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features
msgid "Second Feature"
msgstr "Segona característica"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_showcase
msgid "Second feature"
msgstr "Segona característica"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "Second list of Features"
msgstr "Segona llista de característiques "

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Secondary Buttons"
msgstr "Botons secundaris"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_filter_view_form
msgid "Select a Target Model..."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "Select and delete blocks to remove features."
msgstr "Seleccioneu i suprimiu blocs per eliminar característiques."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_import_view_form
msgid "Select mailing lists"
msgstr "Seleccioneu les llistes de correu"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Select recipients..."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__ab_testing_winner_selection
#: model:ir.model.fields,help:mass_mailing.field_utm_campaign__ab_testing_winner_selection
msgid "Selection to determine the winner mailing that will be sent."
msgstr "Selecció per a determinar la correu guanyadora que s' enviarà."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Send"
msgstr "Enviar"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__ab_testing_schedule_datetime
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__ab_testing_schedule_datetime
msgid "Send Final On"
msgstr "Envia el final al"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__email_from
msgid "Send From"
msgstr "Envia des de"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_kanban
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
msgid "Send Mailing"
msgstr "Executa l'envio"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_compose_form_mass_mailing
msgid "Send Mass Mailing"
msgstr "Enviar correu massiu"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_to_list_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_test_form
msgid "Send a Sample Mail"
msgstr "Enviar un correu electrònic d’exemple"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid ""
"Send a report to the mailing responsible one day after the mailing has been "
"sent."
msgstr ""
"Envia un informe al responsable del enviament un dia després d'enviar els "
"correus."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_test_form
msgid "Send a sample mailing for testing purpose to the address below."
msgstr ""
"Envia un correu de mostra per a provar el propòsit de l' adreça de sota."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_view_generic
msgid "Send a test version of your mailing to preview its design"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_mailing__schedule_type__now
msgid "Send now"
msgstr "Envia ara"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_mailing__schedule_type__scheduled
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_schedule_date_view_form
msgid "Send on"
msgstr "Envia a"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_test_form
msgid "Send test"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_mailing__state__sending
msgid "Sending"
msgstr "S'està enviant"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__sent
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__sent
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_mailing__state__done
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__trace_status__pending
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace_report__state__done
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
msgid "Sent"
msgstr "Enviat"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "Sent By"
msgstr "Enviat per"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__sent_date
msgid "Sent Date"
msgstr "Data d’enviament"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
msgid "Sent Mailings"
msgstr "Llistes de correu enviat"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__sent_datetime
msgid "Sent On"
msgstr "Enviat en"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "Sent Period"
msgstr "Envieu període"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "Sent on #{record.sent_date.value}"
msgstr "Enviat a # {record. sent_ data. valor}"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_portal_subscription_feedback.xml:0
msgid "Sent. Thanks you for your feedback!"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_res_config_settings__mass_mailing_split_contact_name
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid "Separate Mailing Contact Names into two fields"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Separator"
msgstr "Separador"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Separators"
msgstr "Separadors"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription_optout__sequence
msgid "Sequence"
msgstr "Seqüència"

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_mass_mailing_configuration
#: model:ir.ui.menu,name:mass_mailing.menu_mass_mailing_global_settings
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Settings"
msgstr "Configuració"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_report_deactivated
msgid "Settings Menu."
msgstr "El menú Arranjament."

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__is_public
msgid "Show In Preferences"
msgstr "Mostra a les preferències"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Showcase"
msgstr "Presentació"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.theme_default_template
msgid "Signature"
msgstr "Signatura"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid ""
"Since the date and time for this test has not been scheduled, don't forget "
"to manually send your preferred version."
msgstr ""
"Des de la data i hora d' aquesta prova no s' ha planificat, no oblideu d' "
"enviar manualment la vostra versió preferida."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_alert_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Size"
msgstr "Mida"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_alert_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Small"
msgstr "Petit"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options_border_line_widgets
msgid "Solid"
msgstr "Sòlid"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__source_id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__source_id
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Source"
msgstr "Font"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_media_list
msgid ""
"Speakers from all over the world will join our experts to give inspiring "
"talks on various topics. Stay on top of the latest business management "
"trends &amp; technologies"
msgstr ""
"Els presidents de tot el món s'uniran als nostres experts per inspirar "
"converses sobre diversos temes. Quedeu-vos a dalt de les últimes tecnologies"
" de gestió empresarial"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_mailing__reply_to_mode__new
msgid "Specified Email Address"
msgstr "Adreça de correu especificada"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_res_config_settings__mass_mailing_split_contact_name
msgid "Split First and Last Name"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_themes
msgid "Start From Scratch"
msgstr "Comença des de Scratch"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
msgid "Start by creating your first <b>Mailing</b>."
msgstr "Comenceu creant el primer <b> Mailant </b>."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
msgid "State"
msgstr "Estat"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail__mailing_trace_ids
msgid "Statistics"
msgstr "Estadístiques"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__state
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__trace_status
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__state
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "Status"
msgstr "Estat"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Estat basat en activitats\n"
"Vençuda: La data límit ja ha passat\n"
"Avui: La data de l'activitat és avui\n"
"Planificat: Activitats futures."

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_trace__links_click_datetime
msgid "Stores last click datetime in case of multi clicks."
msgstr ""
"Emmagatzema l' últim clic en temps de data en cas de fer clics múltiples."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Stretch to Equal Height"
msgstr "Amplia fins a l' alçada igual"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__subject
msgid "Subject"
msgstr "Assumpte"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_portal_subscription_form.xml:0
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe_form
msgid "Subscribed"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_subscription_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_subscription_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_subscription_view_tree
msgid "Subscription Date"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__subscription_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__subscription_ids
msgid "Subscription Information"
msgstr "Informació de subscripció"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe_form
msgid "Successfully Unsubscribed"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__tag_ids
msgid "Tags"
msgstr "Etiquetes"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_schedule_date_view_form
msgid "Take Future Schedule Date"
msgstr "Agafa la data de planificació futura"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Team"
msgstr "Equip"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__mail_server_available
msgid ""
"Technical field used to know if the user has activated the outgoing mail "
"server option in the settings"
msgstr ""
"Camp tècnic usat per saber si l' usuari ha activat l' opció del servidor de "
"correu sortint a l' arranjament"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features
msgid "Tell what's the value for the customer for this feature."
msgstr "Indica quin és el valor per al client d'aquesta funció."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_options
msgid "Template"
msgstr "Plantilla"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Test"
msgstr "Test"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
msgid "Test Mailing"
msgstr "Prova de correu"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/wizard/mailing_mailing_test.py:0
msgid "Test mailing could not be sent to %s:"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/wizard/mailing_mailing_test.py:0
msgid "Test mailing successfully sent to %s"
msgstr "El correu de prova s'ha enviat amb èxit a %s"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
msgid "Test this mailing by sending a copy to yourself."
msgstr "Proveu aquesta correu enviant una còpia a vós mateix."

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace_report__state__test
msgid "Tested"
msgstr "Provat"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Text"
msgstr "Text"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Text - Image"
msgstr "Text - Imatge"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_text_highlight
msgid "Text Highlight"
msgstr "Ressaltat de text"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_options
msgid "Text Image Text"
msgstr "Text d' imatge"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.theme_default_template
msgid "Thank you for joining us!"
msgstr "Gràcies per acompanyar-nos!"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_text_block
msgid "That way, Odoo evolves much faster than any other solution."
msgstr "Així, Odoo evoluciona molt més ràpid que qualsevol altra solució."

#. module: mass_mailing
#: model:ir.model.constraint,message:mass_mailing.constraint_mailing_mailing_percentage_valid
msgid "The A/B Testing Percentage needs to be between 0 and 100%"
msgstr "El percentatge de proves A/B ha d' estar entre 0 i 100%"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_subscription__opt_out
msgid "The contact has chosen not to receive mails anymore from this list"
msgstr "El contacte ha escollit no rebre més correus d' aquesta llista"

#. module: mass_mailing
#: model:mailing.subscription.optout,name:mass_mailing.mailing_subscription_optout_data_3
msgid "The content of these emails is not relevant to me"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing_filter.py:0
msgid "The filter domain is not valid for this recipients."
msgstr "El domini del filtre no és vàlid per a aquests destinataris."

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_list__is_public
msgid ""
"The mailing list can be accessible by recipients in the subscription "
"management page to allow them to update their preferences."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_text_block
msgid ""
"The open source model of Odoo has allowed us to leverage thousands of developers and\n"
"                    business experts to build hundreds of apps in just a few years."
msgstr ""
"El model de codi font obert de Odoo ens ha permès aprofitar milers de desenvolupadors i\n"
"                    Els experts en negocis per construir centenars d'aplicacions en només uns anys."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_text_image
msgid ""
"The open source model of Odoo has allowed us to leverage thousands of "
"developers and business experts to build hundreds of apps in just a few "
"years."
msgstr ""
"El model de codi font obert de Odoo ens ha permès aprofitar milers de "
"desenvolupadors i experts en negocis per construir centenars d'aplicacions "
"en només uns anys."

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
msgid ""
"The saved filter targets different recipients and is incompatible with this "
"mailing."
msgstr ""
"El filtre desat s'adreça a diferents destinataris i és incompatible amb "
"aquest envio."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid ""
"The winner has already been sent. Use <b>Compare Version</b> to get an "
"overview of this A/B testing campaign."
msgstr ""
"El guanyador ja ha estat enviat. Useu <b>Compare Versió </b> per obtenir una"
" visió general d' aquesta campanya de proves A/B."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "Then on"
msgstr "Després, el"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
msgid "There are no recipients selected."
msgstr "No hi ha destinataris seleccionats."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features
msgid "Third Feature"
msgstr "Tercera característica"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "This"
msgstr "Aquest"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
msgid ""
"This email from can not be used with this mail server.\n"
"Your emails might be marked as spam on the mail clients."
msgstr ""
"Aquest correu no es pot utilitzar amb aquest servidor de correu.\n"
"Els vostres correus electrònics podrien ser marcats com a correu brossa als clients de correu."

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mail_thread.py:0
msgid ""
"This email has been automatically added in blocklist because of too much "
"bounced."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_form
msgid "This email is blacklisted for mass mailings. Click to unblacklist."
msgstr ""
"Aquest correu electrònic està a la llista negra per a enviaments massius. "
"Feu clic per treure'l."

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact__email_normalized
msgid ""
"This field is used to search on email address as the primary email field can"
" contain more than strictly an email address."
msgstr ""
"Aquest camp s' usa per a cercar l' adreça de correu electrònic com a camp "
"primari pot contenir més que una adreça de correu electrònic estrictament."

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_res_config_settings__group_mass_mailing_campaign
msgid ""
"This is useful if your marketing campaigns are composed of several emails"
msgstr ""
"Això és útil si les vostres campanyes de màrqueting estan dissenyades de "
"diversos correus electrònics"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "This mailing has no selected design (yet!)."
msgstr "Aquest envio no té cap disseny seleccionat (encara!)."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid ""
"This tool is advised if your marketing campaign is composed of several "
"emails."
msgstr ""
"Aquesta eina s' ha avisat si la vostra campanya de màrqueting està composta "
"per diversos correus electrònics."

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__reply_to_mode
msgid ""
"Thread: replies go to target document. Email: replies are routed to a given "
"email."
msgstr ""
"Fil: les respostes van al document objectiu. Correu- e: Les respostes s' "
"envien a un correu electrònic donat."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.social_links
msgid "TikTok"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__title_id
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Title"
msgstr "Títol"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "Title Position"
msgstr "Posició del títol"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_three_columns
msgid ""
"To add a fourth column, reduce the size of these three columns using the "
"right icon of each block. Then, duplicate one of the columns to create a new"
" one as a copy."
msgstr ""
"Per afegir una quarta columna, redueix la mida d' aquestes tres columnes "
"usant la icona dreta de cada bloc. Llavors, duplicat una de les columnes per"
" a crear- ne una de nova com a còpia."

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
msgid ""
"To send the winner mailing the campaign should not have been completed."
msgstr ""
"Per enviar el guanyador de correu de la campanya no hauria d'haver acabat."

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
msgid ""
"To send the winner mailing the same campaign should be used by the mailings"
msgstr ""
"Per enviar el guanyador de correu la mateixa campanya hauria d' usar- se per"
" les correu electrònic"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
msgid ""
"To track how many replies this mailing gets, make sure its reply-to address "
"belongs to this database."
msgstr ""
"Per fer un seguiment de quantes respostes rep aquest envio, assegureu-vos "
"que la seva adreça de resposta pertanyi a aquesta base de dades."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_company_team
msgid "Tony Fred, CEO"
msgstr "Tony Fred, CEO"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "Top"
msgstr "Superior"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__total
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_subscription_view_tree
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "Total"
msgstr "Total"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_kanban
msgid "Total <br/>Contacts"
msgstr "Total de <br/>contactes"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_tree
msgid "Total Bounces"
msgstr "Rebotes totals"

#. module: mass_mailing
#: model:ir.model.constraint,message:mass_mailing.constraint_mailing_trace_check_res_id_is_set
msgid "Traces have to be linked to records with a not null res_id."
msgstr "Els rastres s'han d'enllaçar a registres amb un resid no nul."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Tracking"
msgstr "Seguiment"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "Try different variations in the campaign to compare their"
msgstr "Proveu diferents variacions en la campanya per comparar-ne les seves"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "Turn every feature into a benefit for your reader."
msgstr "Converteix cada característica en un benefici per al lector"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__trace_type
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__mailing_type
msgid "Type"
msgstr "Tipus"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipus d'activitat d'excepció registrada."

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_utm_campaign
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__campaign_id
msgid "UTM Campaign"
msgstr "Campanya UTM"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_utm_medium
msgid "UTM Medium"
msgstr "UTM Mitjà"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__medium_id
#: model:ir.model.fields,help:mass_mailing.field_mailing_trace__medium_id
msgid "UTM Medium: delivery method (email, sms, ...)"
msgstr "UTM Mitjà: Mètode de lliurament (correu, sms,...)"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_utm_source
msgid "UTM Source"
msgstr "Font UTM"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Underline"
msgstr "Subratllar"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__failure_type__unknown
msgid "Unknown error"
msgstr "Error desconegut"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_footer_social
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_footer_social_left
#: model_terms:ir.ui.view,arch_db:mass_mailing.theme_basic_template
msgid "Unsubscribe"
msgstr "Donar-se de baixa"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_subscription__opt_out_datetime
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_subscription_view_search
msgid "Unsubscription Date"
msgstr "Data de cancel· lació de l' UNSUB"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_import_view_form
msgid "Upload a file"
msgstr "Puja un fitxer"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_text_block
msgid ""
"Usability improvements made on Odoo will automatically apply to all\n"
"                    of our fully integrated apps."
msgstr ""
"Millores d' usabilitat fetes a Odoo s' aplicarà automàticament a tots\n"
"                    De les nostres aplicacions integrades del tot."

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__mail_server_id
#: model:ir.model.fields,help:mass_mailing.field_res_config_settings__mass_mailing_outgoing_mail_server
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid ""
"Use a specific mail server in priority. Otherwise Odoo relies on the first "
"outgoing mail server available (based on their sequencing) as it does for "
"normal mails."
msgstr ""
"Usa un servidor de correu específic en prioritat. En cas contrari, Odoo "
"depèn del primer servidor de correu sortint disponible (basat en la seva "
"seqüència) com fa pels correus normals."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "Use alternative versions to be able to select the winner."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_coupon_code
msgid "Use now"
msgstr "Usa ara"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_media_list
msgid ""
"Use this component for creating a list of featured elements to which you "
"want to bring attention."
msgstr ""
"Useu aquest component per a crear una llista d' elements llistats als que "
"voleu cridar l' atenció."

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.mailing_trace_report_action_mail
msgid ""
"Use this menu to keep track of the results of your mailings.<br>\n"
"                    From here, you'll be able to overview the rate of replies, clicks, bounces..."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_media_list
msgid ""
"Use this snippet to build various types of components that feature a left- "
"or right-aligned image alongside textual content. Duplicate the element to "
"create a list that fits your needs."
msgstr ""
"Useu aquest retall per a construir diversos tipus de components que "
"característiques d' una imatge situada a l' esquerra o dreta junt amb el "
"contingut textual. Duplica l' element per a crear una llista que encaixi en "
"les vostres necessitats."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_numbers
msgid "Useful options"
msgstr "Opcions útils"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_res_users
#: model:res.groups,name:mass_mailing.group_mass_mailing_user
msgid "User"
msgstr "Usuari"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_search
msgid "Valid Email Recipients"
msgstr "Destinataris vàlids de correu"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Vert. Alignment"
msgstr "Vert. Alineament"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Vertical Alignment"
msgstr "Alineament vertical"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_header_view
msgid "View Online"
msgstr "Visualitza En línia"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing.py:0
msgid ""
"Wait until your mailing has been sent to check how many recipients you "
"managed to reach."
msgstr ""
"Espereu fins que s'hagi enviat el vostre envio per a comprovar a quants "
"destinataris heu aconseguit arribar."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_import_view_form
msgid "Want to import country, company name and more?"
msgstr "Voleu importar el país, el nom de l'empresa i més?"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__warning_message
msgid "Warning Message"
msgstr "Missatge d'advertència"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__warning_message
msgid "Warning message displayed in the mailing form view"
msgstr "Missatge d' avís mostrat a la vista de formulari de correu"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_discount1
msgid ""
"We are continuing to grow and we miss seeing you be a part of it! We've "
"increased store hours and have lot's of new brands available. To welcome you"
" back please accept this 20% discount on you next purchase by clicking the "
"button."
msgstr ""
"Continuem creixent i perdem veure't formar part d'això! Hem augmentat hores "
"de botigues i tenim moltes marques noves disponibles. Per a donar-vos la "
"benvinguda si us plau, accepteu aquest descompte del 20% en la propera "
"compra clicant el botó."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_references
msgid "We are in good company."
msgstr "Estem en una bona companyia."

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__website_message_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__website_message_ids
msgid "Website Messages"
msgstr "Missatges del lloc web"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact__website_message_ids
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__website_message_ids
msgid "Website communication history"
msgstr "Historial de comunicacions del lloc web"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_themes
msgid "Welcome Message"
msgstr "Missatge de benvinguda"

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.mailing_mailing_schedule_date_action
msgid "When do you want to send your mailing?"
msgstr "Quan vols enviar el correu?"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__favorite_date
msgid "When this mailing was added in the favorites"
msgstr "Quan s'ha afegit aquest envio als preferits"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_ir_model__is_mailing_enabled
msgid ""
"Whether this model supports marketing mailing capabilities (notably email "
"and SMS)."
msgstr ""
"Si aquest model accepta les capacitats de correu de màrqueting (especialment"
" correu electrònic i SMS)."

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.mailing_filter_action
msgid ""
"While designing the mailing, you can define the rules to filter recipients.\n"
"                To save the same criteria for future use, you can add it to the favorite list\n"
"                by clicking on <i class=\"fa fa-floppy-o text-warning\"></i> icon next to \"Recipients\"."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_hr_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Width"
msgstr "Amplada"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__ab_testing_winner_selection
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__ab_testing_winner_selection
msgid "Winner Selection"
msgstr "Selecció guanyadora"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_text_block
msgid ""
"With strong technical foundations, Odoo's framework is unique.\n"
"                    It provides top notch usability that scales across all apps."
msgstr ""
"Amb grans fundacions tècniques, el marc de treball de Odo és únic.\n"
"                    No proveeix d' usabilitat màxima noch que s' escala a través de totes les aplicacions."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_picture
msgid ""
"With strong technical foundations, Odoo's framework is unique. It provides "
"<span style=\"font-weight: bolder;\">top notch usability that scales across "
"all apps</span>."
msgstr ""
"Amb bases tècniques sòlides, el marc d'Odoo és únic. Proporciona una "
"usabilitat de <span style=\"font-weight: bolder;\">notch superior que escala"
" a totes les aplicacions</span>."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_cover
msgid ""
"Write one or two paragraphs describing your product, services or a specific "
"feature.<br/> To be successful your content needs to be useful to your "
"readers."
msgstr ""
"Escriu un o dos paràgrafs on descriguis els teus productes, serveis o una "
"característica específica. <br/>  Per a tenir èxit, el contingut ha de ser "
"útil per als lectors."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_import_view_form
msgid ""
"Write or paste email addresses in the field below.\n"
"                    Each line will be imported as a mailing list contact."
msgstr ""
"Escriviu o enganxeu adreces de correu electrònic al camp següent.\n"
"                    Cada línia s'importarà com a contacte de la llista de correu."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features
msgid "Write what the customer would like to know, not what you want to show."
msgstr "Escriu el que el client vulgui saber, no el que vulguis mostrar."

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/controllers/main.py:0
msgid "You are no longer part of our mailing list(s)."
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/controllers/main.py:0
msgid ""
"You are no longer part of our services and will not be contacted again."
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/controllers/main.py:0
msgid "You are no longer part of the %(mailing_name)s mailing list."
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/controllers/main.py:0
msgid "You are no longer part of the %(mailing_names)s mailing list."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe_form
msgid "You are not subscribed to any of our mailing list."
msgstr "No esteu subscrits a cap de la nostra llista de correu."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "You can edit colors and backgrounds to highlight features."
msgstr "Podeu modificar colors i fons per ressaltar les característiques."

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/wizard/mailing_list_merge.py:0
msgid "You can only apply this action from Mailing Lists."
msgstr "Només podeu aplicar aquesta acció a les llistes de correu."

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/utm_medium.py:0
msgid ""
"You cannot delete these UTM Mediums as they are linked to the following mailings in Mass Mailing:\n"
"%(mailing_names)s"
msgstr ""
"No podeu suprimir aquests suports UTM perquè estan enllaçats als següents enviaments de Marketing per correu electrònic :\n"
"%(mailing_names)s"

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/utm_source.py:0
msgid ""
"You cannot delete these UTM Sources as they are linked to the following mailings in Mass Mailing:\n"
"%(mailing_names)s"
msgstr ""
"No podeu suprimir aquestes fonts UTM, ja que estan enllaçades als següents enviaments de Marketing per correu electrònic:\n"
"%(mailing_names)s"

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_create_mass_mailings_from_campaign
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mass_mailings_from_campaign
msgid ""
"You don't need to import your mailing lists, you can easily\n"
"                send emails<br> to any contact saved in other Odoo apps."
msgstr ""
"No necessiteu importar les vostres llistes de correu, podeu fàcilment\n"
"                Envieu correus electrònics <br> a qualsevol contacte desat en altres aplicacions Odoo."

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/wizard/mailing_contact_import.py:0
msgid "You have to much emails, please upload a file."
msgstr "Teniu molts correus electrònics, carregueu un fitxer."

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_portal_subscription_form.xml:0
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe_form
msgid "You may also be interested in"
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/models/mailing_contact.py:0
msgid ""
"You should give either list_ids, either subscription_ids to create new "
"contacts."
msgstr ""

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_portal_subscription_form.xml:0
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe_form
msgid "You will not hear from us anymore."
msgstr ""

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_portal_subscription_form.xml:0
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe_form
msgid ""
"You will not receive any news from those mailing lists you are a member of:"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_title
msgid "Your Title"
msgstr "El vostre títol"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_portal_subscription_form.xml:0
msgid "Your email is currently"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe_form
msgid "Your email is currently <strong>in our block list</strong>."
msgstr ""

#. module: mass_mailing
#. odoo-python
#: code:addons/mass_mailing/wizard/mailing_mailing_test.py:0
msgid "[TEST] %(mailing_subject)s"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_filter_view_form
msgid "e.g. \"B2B Canadian Customers\""
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_form_split_name
msgid "e.g. \"John\""
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_form_split_name
msgid "e.g. \"Smith\""
msgstr ""

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_filter_widget.xml:0
msgid "e.g. \"VIP Customers\""
msgstr "p. ex. \"Clients VIP\""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_form
msgid "e.g. \"VIP\", \"Roadshow\", ..."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "e.g. Check it out before it's too late!"
msgstr "p.g. Mira-ho abans no sigui massa tard!"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form_simplified
msgid "e.g. Consumer Newsletter"
msgstr "p.g. Joc de notícies per a clients"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mail_blacklist_view_form
msgid "e.g. I received too many emails"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_form
msgid "e.g. John Smith"
msgstr "p.g. John Smith"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "e.g. New Sale on all T-shirts"
msgstr "p.g. Nova Sale en totes les samarretes"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_test_form
msgid ""
"<EMAIL>\n"
"<EMAIL>"
msgstr ""
"<EMAIL>\n"
"<EMAIL>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "having the"
msgstr "Agafant la"

#. module: mass_mailing
#. odoo-javascript
#: code:addons/mass_mailing/static/src/xml/mailing_portal_subscription_form.xml:0
msgid "in our block list"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid ""
"is the winner of the A/B testing campaign and has been sent to all remaining"
" recipients."
msgstr ""
"és el guanyador de la campanya de proves A/B i s' ha enviat a tots els "
"destinataris restants."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid ""
"of all potential recipients.<br/>\n"
"                        <b class=\"text-danger\">Some of the mailings will not be sent</b>, as only 1 email will be sent for each unique recipient in this campaign."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "on"
msgstr "en"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_mailing_schedule_date
msgid "schedule a mailing"
msgstr "Programar un correu"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "the"
msgstr "la"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "to the remaining recipients."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "will be sent"
msgstr "S' enviarà"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "will receive this version."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "will receive this version.<br/>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "⌙ Active"
msgstr "bibliography actiu"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "⌙ Inactive"
msgstr "inactives inactives"
