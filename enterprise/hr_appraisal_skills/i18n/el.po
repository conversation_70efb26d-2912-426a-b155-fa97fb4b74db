# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_appraisal_skills
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:29+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Greek (https://app.transifex.com/odoo/teams/41243/el/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: el\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_appraisal_skills
#. odoo-javascript
#: code:addons/hr_appraisal_skills/static/src/js/appraisal_skills_one2many.xml:0
msgid "Add new skills"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__appraisal_id
msgid "Appraisal"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.actions.act_window,name:hr_appraisal_skills.hr_appraisal_skill_report_action
#: model:ir.model,name:hr_appraisal_skills.model_hr_appraisal_skill_report
msgid "Appraisal Skills Report"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill_report__company_id
msgid "Company"
msgstr "Εταιρία"

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill_report__create_date
#: model_terms:ir.ui.view,arch_db:hr_appraisal_skills.hr_appraisal_skill_report_view_search
msgid "Create Date"
msgstr "Ημερομηνία Δημιουργίας"

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__create_uid
msgid "Created by"
msgstr "Δημιουργήθηκε από"

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__create_date
msgid "Created on"
msgstr "Δημιουργήθηκε στις"

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill_report__current_skill_level_id
msgid "Current Level"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill_report__current_level_progress
msgid "Current Progress"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields.selection,name:hr_appraisal_skills.selection__hr_appraisal_skill_report__evolution__decline
msgid "Decline"
msgstr "Απόρριψη"

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill_report__department_id
#: model_terms:ir.ui.view,arch_db:hr_appraisal_skills.hr_appraisal_skill_report_view_search
msgid "Department"
msgstr "Τμήμα"

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__display_name
msgid "Display Name"
msgstr "Εμφάνιση Ονόματος"

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__employee_id
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill_report__employee_id
#: model_terms:ir.ui.view,arch_db:hr_appraisal_skills.hr_appraisal_skill_report_view_search
msgid "Employee"
msgstr "Υπάλληλος"

#. module: hr_appraisal_skills
#: model:ir.model,name:hr_appraisal_skills.model_hr_appraisal
msgid "Employee Appraisal"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill_report__display_name
msgid "Employee Name"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__employee_skill_id
msgid "Employee Skill"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model,name:hr_appraisal_skills.model_hr_appraisal_skill
msgid "Employee Skills"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill_report__evolution
#: model_terms:ir.ui.view,arch_db:hr_appraisal_skills.hr_appraisal_skill_report_view_search
msgid "Evolution"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill_report__evolution_sequence
msgid "Evolution Sequence"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__id
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill_report__id
msgid "ID"
msgstr "Κωδικός"

#. module: hr_appraisal_skills
#. odoo-javascript
#: code:addons/hr_appraisal_skills/static/src/js/appraisal_skills_one2many.xml:0
msgid "If skills are missing, they can be created by an HR officer."
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields.selection,name:hr_appraisal_skills.selection__hr_appraisal_skill_report__evolution__improvement
#: model_terms:ir.ui.view,arch_db:hr_appraisal_skills.hr_appraisal_skill_report_view_search
msgid "Improvement"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields.selection,name:hr_appraisal_skills.selection__hr_appraisal_skill_report__evolution__just_added
msgid "Just added"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__justification
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill_report__justification
msgid "Justification"
msgstr "Αιτιολόγηση"

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__write_uid
msgid "Last Updated by"
msgstr "Τελευταία Ενημέρωση από"

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__write_date
msgid "Last Updated on"
msgstr "Τελευταία Ενημέρωση στις"

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__manager_ids
msgid "Manager"
msgstr "Διευθυντής"

#. module: hr_appraisal_skills
#: model_terms:ir.ui.view,arch_db:hr_appraisal_skills.hr_appraisal_skill_report_view_search
msgid "No Change"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill_report__previous_skill_level_id
msgid "Previous Level"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill_report__previous_level_progress
msgid "Previous Progress"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__previous_skill_level_id
msgid "Previous Skill Level"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__level_progress
msgid "Progress"
msgstr "Εξέλιξη"

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill_report__progress_evolution
msgid "Progress Evolution"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields,help:hr_appraisal_skills.field_hr_appraisal_skill__level_progress
msgid "Progress from zero knowledge (0%) to fully mastered (100%)."
msgstr ""

#. module: hr_appraisal_skills
#: model_terms:ir.ui.view,arch_db:hr_appraisal_skills.hr_appraisal_skill_report_view_search
msgid "Regression"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields.selection,name:hr_appraisal_skills.selection__hr_appraisal_skill_report__evolution__same
msgid "Same"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__skill_id
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill_report__skill_id
msgid "Skill"
msgstr ""

#. module: hr_appraisal_skills
#: model_terms:ir.ui.view,arch_db:hr_appraisal_skills.hr_appraisal_skill_report_view_graph
#: model_terms:ir.ui.view,arch_db:hr_appraisal_skills.hr_appraisal_skill_report_view_pivot
msgid "Skill Analysis"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__skill_level_id
msgid "Skill Level"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__skill_type_id
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill_report__skill_type_id
#: model_terms:ir.ui.view,arch_db:hr_appraisal_skills.hr_appraisal_skill_report_view_search
msgid "Skill Type"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal__skill_ids
#: model_terms:ir.ui.view,arch_db:hr_appraisal_skills.view_hr_appraisal_form
msgid "Skills"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.ui.menu,name:hr_appraisal_skills.menu_appraisal_skills_report
#: model_terms:ir.actions.act_window,help:hr_appraisal_skills.hr_appraisal_skill_report_action
msgid "Skills Evolution"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.actions.act_window,name:hr_appraisal_skills.hr_skill_type_action_appraisal
#: model:ir.ui.menu,name:hr_appraisal_skills.menu_hr_appraisal_surveys
msgid "Skills Type"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.constraint,message:hr_appraisal_skills.constraint_hr_appraisal_skill__unique_skill
msgid "Two levels for the same skill is not allowed"
msgstr ""

#. module: hr_appraisal_skills
#: model_terms:ir.actions.act_window,help:hr_appraisal_skills.hr_appraisal_skill_report_action
msgid ""
"View the evolution of employee's skill between the two latest appraisals."
msgstr ""

#. module: hr_appraisal_skills
#. odoo-javascript
#: code:addons/hr_appraisal_skills/static/src/js/appraisal_skills_one2many.xml:0
msgid "You can add skills from our library to the employee profile."
msgstr ""

#. module: hr_appraisal_skills
#: model_terms:ir.ui.view,arch_db:hr_appraisal_skills.appraisal_skills_update_template
msgid "has been added."
msgstr ""

#. module: hr_appraisal_skills
#: model_terms:ir.ui.view,arch_db:hr_appraisal_skills.appraisal_skills_update_template
msgid "has been deleted."
msgstr ""
