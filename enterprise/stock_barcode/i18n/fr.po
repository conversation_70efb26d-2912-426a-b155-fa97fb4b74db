# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_barcode
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 09:12+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "%s can't be inventoried. Only storable products can be inventoried."
msgstr ""
"%s ne peut pas être inventorié. Seuls les produits stockables peuvent être "
"inventoriés."

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
msgid "<i class=\"fa fa-fw fa-lg fa-barcode me-3\" title=\"Serial/Lot Number\"/>"
msgstr "<i class=\"fa fa-fw fa-lg fa-barcode me-3\" title=\"Numéro de série/lot\"/>"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
msgid "<i class=\"fa fa-fw fa-lg fa-cube me-3\" title=\"Quantity\"/>"
msgstr "<i class=\"fa fa-fw fa-lg fa-cube me-3\" title=\"Quantité\"/>"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
msgid ""
"<i class=\"fa fa-fw fa-lg fa-long-arrow-right me-3\" title=\"Scrap "
"Location\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-lg fa-long-arrow-right me-3\" title=\"Emplacement de "
"rebut\"/>"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
msgid "<i class=\"fa fa-fw fa-lg fa-tags me-3\" title=\"Product\"/>"
msgstr "<i class=\"fa fa-fw fa-lg fa-tags me-3\" title=\"Produit\"/>"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode_kanban_2
msgid "<i class=\"fa fa-lg fa-archive\" title=\"Package\"/>"
msgstr "<i class=\"fa fa-lg fa-archive\" title=\"Colis\"/>"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode_kanban_2
msgid "<i class=\"fa fa-lg fa-user-o\" title=\"Owner\"/>"
msgstr "<i class=\"fa fa-lg fa-user-o\" title=\"Propriétaire\"/>"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "<i class=\"fa fa-print\"/> Print barcode commands and operation types"
msgstr ""
"<i class=\"fa fa-print\"/> Imprimer les codes-barres des commandes et des "
"types d'opération"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "<i class=\"fa fa-print\"/> Print barcode demo sheet"
msgstr "<i class=\"fa fa-print\"/> Imprimer les commandes de codes-barres"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "<i class=\"fa fa-print\"/> Print storage locations"
msgstr "<i class=\"fa fa-print\"/> Imprimer les emplacements de stockage"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "<i class=\"fa fs-4 fa-tags me-1\" title=\"Product\"/>"
msgstr "<i class=\"fa fs-4 fa-tags me-1\" title=\"Produit\"/>"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
msgid "<span>Replenish Quantities</span>"
msgstr "<span>Réapprovisionner les quantités</span>"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
msgid ""
"A product tracked by serial numbers can't have multiple quantities for the "
"same serial number."
msgstr ""
"Un produit tracké par numéros de série ne peut avoir des quantités multiples"
" pour le même numéro de série"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.js:0
msgid "Add Product"
msgstr "Ajouter un produit"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Add extra product?"
msgstr "Ajouter un produit supplémentaire ?"

#. module: stock_barcode
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_put_in_pack__mandatory
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_scan_dest_location__mandatory
msgid "After each product"
msgstr "Après chaque produit"

#. module: stock_barcode
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_put_in_pack__optional
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_scan_dest_location__optional
msgid "After group of Products"
msgstr "Après groupe de produits"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "All products need to be packed"
msgstr "Tous les produits doivent être emballés"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_barcode
msgid "Allocation"
msgstr "Allocation"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__barcode_allow_extra_product
msgid "Allow extra products"
msgstr "Autoriser des produits supplémentaires"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__barcode_validation_full
msgid "Allow full picking validation"
msgstr "Autoriser la validation complète du transfert"

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_picking_type__barcode_validation_full
msgid ""
"Allow to validate a picking even if nothing was scanned yet (and so, do an "
"immediate transfert)"
msgstr ""
"Autoriser la validation d'un transfert même si rien n'a encore été scanné "
"(et donc, faire un transfert immédiat)"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid ""
"Allow users to see the expected quantity of a product to count in a location"
msgstr ""
"Permettre aux utilisateurs de voir la quantité attendue d'un produit à "
"compter dans un emplacement"

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_picking_type__show_reserved_sns
msgid ""
"Allows to display reserved lots/serial numbers. When non active, it is clear"
" for the picker that they can pick the lots/serials they want."
msgstr ""
"Permet d'afficher les lots/numéro de série réservés. Si cette option n'est "
"pas activée, le sélecteur sait qu'il peut choisir les lots/séries souhaités."

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
msgid ""
"An unexisting package type was scanned. This part of the barcode can't be "
"processed."
msgstr ""
"Un type de colis non existant à été scanné. Cette information ne sera pas "
"interprétée."

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.xml:0
#: code:addons/stock_barcode/static/src/components/manual_barcode.xml:0
msgid "Apply"
msgstr "Appliquer"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_cancel_operation_view
msgid "Are you sure you want to cancel this operation?"
msgstr "Êtes-vous sûr de vouloir annuler cette opération ?"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/backorder_dialog.xml:0
msgid "Backorder"
msgstr "Reliquat"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_backorder_confirmation
msgid "Backorder Confirmation"
msgstr "Confirmation de reliquat"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
#: model:ir.actions.client,name:stock_barcode.stock_barcode_action_main_menu
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__product_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_scrap__product_barcode
#: model:ir.ui.menu,name:stock_barcode.stock_barcode_menu
msgid "Barcode"
msgstr "Code-barres"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_view_picking_type_form
msgid "Barcode App"
msgstr "App Code-barres"

#. module: stock_barcode
#: model:ir.actions.client,name:stock_barcode.stock_barcode_inventory_client_action
msgid "Barcode Inventory Client Action"
msgstr "Barcode Inventory Client Action"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "Barcode Nomenclature"
msgstr "Nomenclature des codes-barres"

#. module: stock_barcode
#: model:ir.actions.client,name:stock_barcode.stock_barcode_picking_client_action
msgid "Barcode Picking Client Action"
msgstr "Action client de la préparation par code-barres"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_scrap___barcode_scanned
msgid "Barcode Scanned"
msgstr "Code barre scanné"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "Barcode Scanner"
msgstr "Lecteur de codes-barres"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
msgid "Barcode scan is ambiguous with several model: %s. Use the most likely."
msgstr ""
"Scan de code-barres ambigu avec plusieurs modèles:%s. Utilisez le plus "
"probable."

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/controllers/stock_barcode.py:0
msgid "Barcodes are not available."
msgstr "Les codes-barres ne sont pas disponibles."

#. module: stock_barcode
#: model:product.template,name:stock_barcode.product_cable_management_box_2_product_template
msgid "Cable Management Box"
msgstr "Boîtier pour cables"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_cancel_operation_view
msgid "Cancel"
msgstr "Annuler"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_barcode_cancel_operation
msgid "Cancel Operation"
msgstr "Annuler le transfert"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Cancel Transfer"
msgstr "Annuler le transfert"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_cancel_operation_view
msgid "Cancel operation"
msgstr "Annuler l'opération"

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/models/stock_picking.py:0
msgid "Cancel this operation?"
msgstr "Annuler cette opération ?"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/count_screen_rfid.xml:0
#: code:addons/stock_barcode/static/src/components/main.xml:0
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
#: code:addons/stock_barcode/static/src/widgets/image_preview.xml:0
msgid "Close"
msgstr "Fermer"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_res_config_settings
msgid "Config Settings"
msgstr "Paramètres de configuration"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "Configure Product Barcodes"
msgstr "Configurer les codes-barres des produits"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Confirm"
msgstr "Confirmer"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_res_partner
msgid "Contact"
msgstr "Contact"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__group_barcode_count_entire_location
#: model:res.groups,name:stock_barcode.group_barcode_count_entire_location
msgid "Count Entire Locations"
msgstr "Compter parmi les emplacements entiers"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Counted Quantity"
msgstr "Quantité comptée"

#. module: stock_barcode
#: model_terms:ir.actions.act_window,help:stock_barcode.stock_picking_action_kanban
msgid "Create a new transfer"
msgstr "Créer un nouveau transfert"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__create_date
msgid "Created on"
msgstr "Créé le"

#. module: stock_barcode
#: model:product.template,name:stock_barcode.product_custom_cabinet_metric_product_template
msgid "Customized Cabinet (Metric)"
msgstr "Armoire sur mesure (Métrique)"

#. module: stock_barcode
#: model:product.template,name:stock_barcode.product_custom_cabinet_usa_product_template
msgid "Customized Cabinet (USA)"
msgstr "Armoire sur mesure (États-Unis)"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.xml:0
msgid "Delete"
msgstr "Supprimer"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__stock_barcode_demo_active
msgid "Demo Data Active"
msgstr "Données de démonstration activées"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/line.xml:0
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Destination Location"
msgstr "Emplacement de destination"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Destination Package"
msgstr "Colis de destination"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Destination location must be scanned"
msgstr "L'emplacement de destination doit être scanné"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "Disable sound effect while scanning a barcode."
msgstr "Désactiver l'effet sonore lors du scan d'un code-barres."

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/apply_quant_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Discard"
msgstr "Ignorer"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__display_name
msgid "Display Name"
msgstr "Nom d'affichage"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.js:0
msgid ""
"Do you want to permanently remove this message ? It won't appear anymore, so"
" make sure you don't need the barcodes sheet or you have a copy."
msgstr ""
"Voulez-vous supprimer ce message définitivement ? Il n'apparaîtra plus, donc"
" soyez sûr de ne pas avoir besoin de la feuille de codes-barres ou d'en "
"garder une copie."

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_picking_type__restrict_put_in_pack
msgid ""
"Does the picker have to put in a package the scanned products? If yes, at "
"which rate?"
msgstr ""
"Le préparateur doit-il mettre dans un colis les produits scannés ? Si oui, à"
" quelle fréquence ?"

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_picking_type__restrict_scan_dest_location
msgid "Does the picker have to scan the destination? If yes, at which rate?"
msgstr ""
"Le préparateur doit-il scanner la destination ? Si oui, à quelle fréquence ?"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_cancel_operation_view
msgid "Don't cancel"
msgstr "Ne pas annuler"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.js:0
msgid "Don't show this message again"
msgstr "Ne plus montrer ce message"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/backorder_dialog.xml:0
msgid "Done /"
msgstr "Terminé /"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "Download demo data sheet"
msgstr "Télécharger une feuille de données de démonstration"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "Download operation barcodes"
msgstr "Télécharger les codes-barres des opérations"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__dummy_id
#: model:ir.model.fields,field_description:stock_barcode.field_stock_quant__dummy_id
msgid "Dummy"
msgstr "Factice"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_backorder_confirmation__empty_move_count
msgid "Empty Move Count"
msgstr "Nombre de mouvements vides"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/manual_barcode.xml:0
msgid "Enter a barcode..."
msgstr "Saisir un code-barres..."

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_view_picking_type_form
msgid "Final Validation"
msgstr "Validation finale"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Following backorder was created:"
msgstr "Le reliquat suivant est créé :"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Following backorders were created:"
msgstr "Les reliquats suivants sont créés :"

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_picking_type__barcode_allow_extra_product
msgid "For planned transfers, allow to add non-reserved products"
msgstr ""
"Permettre l'ajout de produits non réservés pour les transferts planifiés"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__restrict_scan_dest_location
msgid "Force Destination Location scan?"
msgstr "Forcer le scan de l'emplacement destination ?"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__restrict_scan_tracking_number
msgid "Force Lot/Serial scan?"
msgstr "Forcer le scan du numéro de lot/de série ?"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__restrict_scan_product
msgid "Force Product scan?"
msgstr "Forcer le scan du produit ?"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__restrict_scan_source_location
msgid "Force Source Location scan?"
msgstr "Forcer le scan de l'emplacement d'origine ?"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__barcode_validation_after_dest_location
msgid "Force a destination on all products"
msgstr "Forcer une destination sur tous les produits"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__barcode_validation_all_product_packed
msgid "Force all products to be packed"
msgstr "Forcer le colisage de tous les produits"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__restrict_put_in_pack
msgid "Force put in pack?"
msgstr "Forcer le colisage ?"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__formatted_product_barcode
msgid "Formatted Product Barcode"
msgstr "Code-barres produit formaté"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_product_product__has_image
msgid "Has Image"
msgstr "A une image"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__hide_lot
msgid "Hide Lot"
msgstr "Masquer le lot"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__hide_lot_name
msgid "Hide Lot Name"
msgstr "Masquer le nom du lot"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__id
msgid "ID"
msgstr "ID"

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/models/stock_picking_type.py:0
msgid ""
"If the source location must be scanned, then the destination location must "
"either be scanned after each product or not scanned at all."
msgstr ""
"Si l'emplacement d'origine doit être scanné, l'emplacement de destination "
"doit être scanné après chaque produit ou pas scanné du tout."

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/backorder_dialog.xml:0
msgid ""
"If you validate now, the remaining products will be added to a backorder."
msgstr ""
"Si vous validez maintenant, les produits restants seront ajoutés à un "
"reliquat."

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__image_1920
#: model:ir.model.fields,field_description:stock_barcode.field_stock_quant__image_1920
msgid "Image"
msgstr "Image"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/backorder_dialog.xml:0
msgid "Incomplete Transfer"
msgstr "Transfert incomplet"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "Install"
msgstr "Installer"

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_move_line__product_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_scrap__product_barcode
msgid "International Article Number used for product identification."
msgstr ""
"Numéro d'article international (IAN) utilisé pour identifier cet article."

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "Inventory Adjustment"
msgstr "Ajustement d'inventaire"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_location
msgid "Inventory Locations"
msgstr "Emplacements de l'inventaire"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "Inventory count"
msgstr "Ajustement d'inventaire"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__is_barcode_picking_type
msgid "Is Barcode Picking Type"
msgstr "Code-barres est type de transfert"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__write_uid
msgid "Last Updated by"
msgstr "Mis à jour par"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__write_date
msgid "Last Updated on"
msgstr "Mis à jour le"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.js:0
msgid "Leave it"
msgstr "Laisser"

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_picking_type__restrict_scan_product
msgid "Line's product must be scanned before the line can be edited"
msgstr ""
"Le produit de la ligne doit être scanné avant que la ligne puisse être "
"modifiée"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Location"
msgstr "Emplacement"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__location_processed
msgid "Location Processed"
msgstr "Emplacement traité"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "Log out"
msgstr "Se déconnecter"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_lot
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_view_picking_type_form
msgid "Lot/Serial"
msgstr "Lot/série"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Mandatory Destination Location"
msgstr "Emplacement de destination obligatoire"

#. module: stock_barcode
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_scan_source_location__mandatory
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_scan_tracking_number__mandatory
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_view_picking_type_form
msgid "Mandatory Scan"
msgstr "Scan obligatoire"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Mandatory Source Location"
msgstr "Emplacement d'origine obligatoire"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__barcode_max_time_between_keys_in_ms
msgid "Max time between each key"
msgstr "Temps maximal entre chaque clé"

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_res_config_settings__barcode_max_time_between_keys_in_ms
msgid "Maximum delay between each key in ms (100 ms by default)"
msgstr "Délai maximum entre chaque clé en ms (100 ms par défaut)"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__barcode_separator_regex
msgid "Multiscan Separator"
msgstr "Séparateur multiscan"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__stock_barcode_mute_sound_notifications
msgid "Mute Barcode application sounds"
msgstr "Couper les sons de l'application de code-barres"

#. module: stock_barcode
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_put_in_pack__no
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_scan_dest_location__no
msgid "No"
msgstr "Non"

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/models/stock_picking.py:0
msgid "No %(picking_type)s ready for this %(barcode_type)s"
msgstr "Aucun %(picking_type)s prêt(s) pour ce %(barcode_type)s"

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/models/stock_move_line.py:0
msgid "No Barcode"
msgstr "Pas de code-barres"

#. module: stock_barcode
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_scan_source_location__no
msgid "No Scan"
msgstr "Aucun scan"

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/controllers/stock_barcode.py:0
msgid ""
"No internal operation type. Please configure one in warehouse settings."
msgstr ""
"Pas de type d'opération interne. Configurez-en un dans les paramètres de "
"l'entrepôt."

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/controllers/stock_barcode.py:0
msgid "No picking or location or product corresponding to barcode %(barcode)s"
msgstr ""
"Aucun transfert, emplacement ou produit correspondant à ce code-barres "
"%(barcode)s"

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/controllers/stock_barcode.py:0
msgid "No picking or product corresponding to barcode %(barcode)s"
msgstr "Aucun transfert ou produit correspondant à ce code-barres %(barcode)s"

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/models/stock_picking.py:0
msgid "No product, lot or package found for barcode %s"
msgstr "Aucun produit, lot ou colis n'a été trouvé pour le code-barres %s"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/apply_quant_dialog.xml:0
msgid "No, I'll count them later"
msgstr "Non, je les compterai plus tard"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__barcode_nomenclature_id
msgid "Nomenclature"
msgstr "Nomenclature"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Not the expected scan"
msgstr "Pas le scan attendu"

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/models/stock_picking.py:0
#: model:ir.actions.act_window,name:stock_barcode.open_picking
msgid "Open picking form"
msgstr "Ouvrir un formulaire de transfert"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.xml:0
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
#: model:ir.actions.act_window,name:stock_barcode.stock_picking_action_kanban
#: model:ir.actions.act_window,name:stock_barcode.stock_picking_type_action_kanban
msgid "Operations"
msgstr "Opérations"

#. module: stock_barcode
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_scan_tracking_number__optional
msgid "Optional Scan"
msgstr "Scan optionnel"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_view_picking_type_form
msgid "Options"
msgstr "Options"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Owner"
msgstr "Détenteur"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Package"
msgstr "Colis"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "Package type %(type)s applied to the package %(package)s"
msgstr "Type d'emballage %(type)s appliqué aucolis %(package)s"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_quant_package
msgid "Packages"
msgstr "Colis"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__product_packaging_id
msgid "Packaging"
msgstr "Conditionnement"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__product_packaging_uom_qty
msgid "Packaging Quantity"
msgstr "Qté de conditionnements"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__parent_location_id
msgid "Parent Location"
msgstr "Emplacement parent"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__parent_location_dest_id
msgid "Parent Location Dest"
msgstr "Emplacement parent de destination"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_backorder_confirmation__partial_move_count
msgid "Partial Move Count"
msgstr "Nombre de mouvements partiels"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_barcode
msgid "Picking Details"
msgstr "Détails du transfert"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_picking_type
msgid "Picking Type"
msgstr "Type de transfert"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Please scan destination location for %s before scanning other product"
msgstr ""
"Veuillez scanner l'emplacement de destination pour %s avant de scanner un "
"autre produit"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.js:0
msgid "Please, Scan again!"
msgstr "Veuillez scanner à nouveau !"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Press Validate or scan another product"
msgstr "Appuyez sur Valider ou scannez un autre produit"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.xml:0
msgid "Print"
msgstr "Imprimer"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Print Barcodes"
msgstr "Imprimer les codes-barres"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Print Delivery Slip"
msgstr "Imprimer le bon de livraison"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "Print Inventory"
msgstr "Imprimer inventaire"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Print Packages"
msgstr "Imprimer des colis"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Print Picking Operations"
msgstr "Imprimer les opérations de transfert"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "Print the"
msgstr "Imprimer le/la/les"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.js:0
msgid "Processing %(processed)s/%(toProcess)s barcodes"
msgstr "Traitement %(processed)s/%(toProcess)s des codes-barres"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/backorder_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Product"
msgstr "Produit"

#. module: stock_barcode
#: model:ir.actions.act_window,name:stock_barcode.product_action_barcodes
#: model_terms:ir.ui.view,arch_db:stock_barcode.product_view_list_barcodes
msgid "Product Barcodes"
msgstr "Codes-barres de produits"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "Mouvements de produit (Ligne de mouvement de stock)"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_product_packaging
msgid "Product Packaging"
msgstr "Conditionnement des produits"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__product_reference_code
#: model:ir.model.fields,field_description:stock_barcode.field_stock_quant__product_reference_code
msgid "Product Reference Code"
msgstr "Code de référence du produit"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__product_stock_quant_ids
msgid "Product Stock Quant"
msgstr "Quantité du produit en stock"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_uom_uom
msgid "Product Unit of Measure"
msgstr "Unité de mesure du produit"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_product_product
msgid "Product Variant"
msgstr "Variante de produit"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.xml:0
msgid "Put In Pack"
msgstr "Mettre en colis"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_view_picking_type_form
msgid "Put in Pack"
msgstr "Mettre en colis"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__qty_done
msgid "Qty Done"
msgstr "Qté faite"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Quantity"
msgstr "Quantité"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode_kanban
msgid "Quantity Done"
msgstr "Quantité faite"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Quantity in stock"
msgstr "Quantité en stock"

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_move_line__product_packaging_uom_qty
msgid "Quantity of the Packaging in the UoM of the Stock Move Line."
msgstr ""
"Quantité de conditionnements dans la nomenclature de la ligne de mouvement "
"de stock."

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_quant
msgid "Quants"
msgstr "Quantités"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__barcode_rfid_batch_time
msgid "RFID Timer"
msgstr "Minuteur RFID"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/count_screen_rfid.xml:0
msgid "Read Rate"
msgstr "Taux de lecture"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/count_screen_rfid.xml:0
msgid "Read Time"
msgstr "Temps de lecture"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.js:0
msgid "Remove it"
msgstr "Retirer"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.xml:0
msgid "Return Products"
msgstr "Retourner les produits"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/kanban/stock_barcode_kanban_renderer.xml:0
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "Scan a"
msgstr "Scanner un(e)"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "Scan a location"
msgstr "Scanner un emplacement"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a lot number"
msgstr "Scanner un numéro de lot"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a lot number or a packages then the destination location"
msgstr ""
"Scanner un numéro de lot ou un colis puis l'emplacement de destination"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a lot number then the destination location"
msgstr "Scanner un numéro de lot puis l'emplacement de destination"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a package"
msgstr "Scanner un colis"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a package or put in pack"
msgstr "Scanner un colis ou mettre en colis"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a package or the destination location"
msgstr "Scanner un colis ou l'emplacement de destination"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a package, the destination location or another product"
msgstr "Scanner un colis, l'emplacement de destination ou un autre produit"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "Scan a product"
msgstr "Scanner un produit"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a product before scanning a tracking number"
msgstr "Scanner un produit avant de scanner un numéro de tracking"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a product from %s"
msgstr "Scanner un produit de %s"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "Scan a product in %s or scan another location"
msgstr "Scanner un produit dans %s ou scanner un autre emplacement"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a product or a package"
msgstr "Scanner un produit ou un colis"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a product or another package"
msgstr "Scanner un produit ou un autre colis"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a product or the destination location."
msgstr "Scanner un produit ou l'emplacement de destination."

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a product then the destination location"
msgstr "Scanner un produit puis l'emplacement de destination"

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/models/stock_picking.py:0
msgid ""
"Scan a product, a lot/serial number or a package to filter the transfers."
msgstr ""
"Scanner un produit, un numéro de série/lot ou un colis pour filtrer les "
"transferts."

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a product, a package or the destination location"
msgstr "Scanner un produit, un colis ou l'emplacement de destination"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a product, a package or the destination location."
msgstr "Scanner un produit, un colis ou l'emplacement de destination."

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a serial number"
msgstr "Scanner un numéro de série"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a serial number or a package"
msgstr "Scanner un numéro de série ou un colis"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a serial number or a package then the destination location"
msgstr ""
"Scanner un numéro de série ou un colis puis l'emplacement de destination"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a serial number then the destination location"
msgstr "Scanner un numéro de série puis l'emplacement de destination"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "Scan an"
msgstr "Scanner un"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan another serial number"
msgstr "Scanner un autre numéro de série"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "Scan lot numbers for product %s to change their quantity"
msgstr ""
"Scanner les numéros de lot des produits %s pour modifier leur quantité"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan more lot numbers"
msgstr "Scanner plus de numéros de lot"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan more lot numbers or a package"
msgstr "Scanner plus de numéros de lot ou un colis"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "Scan more products in %s or scan another location"
msgstr "Scanner plus de produits dans %s ou scanner un autre emplacement"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan more products or a package"
msgstr "Scanner plus de produits ou un colis"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan more products, or scan a new source location"
msgstr "Scanner plus de produits, ou scanner un nouvel emplacement d'origine"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "Scan or tap"
msgstr "Scanner ou toucher"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "Scan serial numbers for product %s to change their quantity"
msgstr ""
"Scanner les numéros de série du produit %s pour modifier leur quantité"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan the destination location"
msgstr "Scanner l'emplacement de destination"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan the package %s"
msgstr "Scanner le colis %s"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan the source location"
msgstr "Scanner l'emplacement d'origine"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan the source location or a package"
msgstr "Scanner l'emplacement d'origine ou un colis"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid ""
"Scanned product %s is not reserved for this transfer. Are you sure you want "
"to add it?"
msgstr ""
"Le produit scanné %s n'est pas réservé à ce transfert. Voulez-vous vraiment "
"l'ajouter ?"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid ""
"Scanned quantity uses %(unit)s as its Unit of Measure (UoM), but it is not "
"compatible with the line's UoM (%(lineUnit)s)."
msgstr ""
"La quantité scannée utilise %(unit)s comme unité de mesure, mais cette unité"
" de mesure n'est pas compatible avec celle de la ligne (%(lineUnit)s)."

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid ""
"Scanned quantity uses %(unit)s as its Unit of Measure (UoM), but it is not "
"compatible with the product's UoM (%(productUnit)s)."
msgstr ""
"La quantité scannée utilise %(unit)s comme unité de mesure, mais cette unité"
" de mesure n'est pas compatible avec celle du produit (%(productUnit)s)."

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scanning package"
msgstr "Scannez le colis"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.xml:0
#: model:ir.model,name:stock_barcode.model_stock_scrap
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
msgid "Scrap"
msgstr "Rebut"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
msgid "Scrap Location"
msgstr "Emplacement de rebut"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Select a Product"
msgstr "Sélectionner un produit"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Serial/Lot Number"
msgstr "Numéro de série / lot"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "Set the maximum delay between each key in ms (100 ms by default.)"
msgstr "Définir le délai maximum entre chaque clé en ms (100 ms par défaut.)"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__show_barcode_nomenclature
msgid "Show Barcode Nomenclature"
msgstr "Montrer la nomenclature des codes-barres"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__show_barcode_validation
msgid "Show Barcode Validation"
msgstr "Afficher la validation des codes-barres"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__group_barcode_show_quantity_count
#: model:res.groups,name:stock_barcode.group_barcode_show_quantity_count
msgid "Show Quantity to Count"
msgstr "Afficher la quantité à compter"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__show_reserved_sns
msgid "Show reserved lots/SN"
msgstr "Afficher les numéros de série/lot réservés"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.xml:0
msgid "Sign"
msgstr "Signer"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/apply_quant_dialog.xml:0
msgid "Some serials where not counted, set them as missing?"
msgstr ""
"Certains numéros de série n'ont pas été comptés, les définir comme manquants"
" ?"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Source Location"
msgstr "Emplacement d'origine"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Source Package"
msgstr "Colis d'origine"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/backorder_dialog.xml:0
msgid "Stay on transfer"
msgstr "Rester sur le transfert"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_move
msgid "Stock Move"
msgstr "Mouvement de stock"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_request_count
msgid "Stock Request an Inventory Count"
msgstr "Le stock requière un inventaire"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_package_type
msgid "Stock package type"
msgstr "Type de colis de stock"

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_picking_type__is_barcode_picking_type
msgid ""
"Technical field indicating if should be used in barcode app and used to "
"control visibility in the related UI."
msgstr ""
"Champ technique indiquant s'il doit être utilisé dans l'application Code-"
"barres et permettant de contrôler la visibilité dans l'interface utilisateur"
" correspondante."

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_picking_type__show_barcode_validation
msgid ""
"Technical field used to compute whether the \"Final Validation\" group "
"should be displayed, solving combined groups/invisible complexity."
msgstr ""
"Champ technique utilisé pour calculer si le groupe \"Validation finale\" "
"doit être affiché, résolvant les groupes combinés/complexité invisible."

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "The inventory adjustment has been validated"
msgstr "L'ajustement d'inventaire a été validé"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "The product %s should not be picked in this operation."
msgstr ""
"Le produit %s ne peut pas être sélectionné dans le cadre de cette opération."

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "The scanned location doesn't belong to this operation's destination"
msgstr ""
"L'emplacement scanné n'appartient pas à la destination de cette opération"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "The scanned location doesn't belong to this operation's location"
msgstr ""
"L'emplacement scanné n'appartient pas à l'emplacement de cette opération"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
msgid "The scanned serial number %s is already used."
msgstr "Le numéro de série scanné %s est déjà utilisé."

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "The transfer has been cancelled"
msgstr "Le transfert à été annulé"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "The transfer has been validated"
msgstr "Le transfert a été validé"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "There is nothing to apply in this page."
msgstr "Il n'y a rien à appliquer sur cette page."

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "There is nothing to print in this page."
msgstr "Il n'y a rien à imprimer sur cette page."

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "This package is already scanned."
msgstr "Ce colis est déjà scanné."

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "This picking is already done"
msgstr "Ce transfert est déjà effectué"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "This picking is cancelled"
msgstr "Ce transfert est annulé"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
msgid "This product doesn't exist."
msgstr "Ce produit n'existe pas."

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_res_config_settings__barcode_separator_regex
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid ""
"This regex is used in the Barcode application to separate individual "
"barcodes when an aggregate barcode (i.e. single barcode consisting of "
"multiple barcode encodings) is scanned."
msgstr ""
"Cette expression régulière est utilisée dans l'application Code-barres pour "
"séparer les codes-barres individuels lorsqu'un code-barres agrégé (c'est-à-"
"dire un code-barres unique composé de plusieurs encodages de codes-barres) "
"est scanné."

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
msgid "This serial number is already used."
msgstr "Ce numéro de série est déjà utilisé."

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid ""
"Time before processing a batch. Used to group communication to the server "
"and improve global performance at the cost of reactivity."
msgstr ""
"Temps avant de traiter un lot. Utilisé pour regrouper les communications "
"avec le serveur et améliorer les performances globales au détriment de la "
"réactivité."

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/backorder_dialog.xml:0
msgid "To do"
msgstr "À faire"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "To use packages, enable 'Packages' in the settings"
msgstr "Pour utiliser les colis, activez 'Colis' dans la configuration"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/count_screen_rfid.xml:0
msgid "Total Reads"
msgstr "Total des lectures"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_picking
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__picking_id
msgid "Transfer"
msgstr "Transfert"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__picking_name
msgid "Transfer Name"
msgstr "Nom du transfert"

#. module: stock_barcode
#: model_terms:ir.actions.act_window,help:stock_barcode.stock_picking_action_kanban
msgid "Transfers allow you to move products from one location to another."
msgstr ""
"Les transferts vous permettent de déplacer des produits d'un emplacement à "
"un autre."

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/count_screen_rfid.xml:0
msgid "Unique Tags"
msgstr "Étiquettes uniques"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode_kanban
msgid "Unit of Measure"
msgstr "Unité de mesure"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_stock_quant_tree
msgid "UoM"
msgstr "UdM"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/backorder_dialog.xml:0
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
msgid "Validate"
msgstr "Valider"

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_scrap___barcode_scanned
msgid "Value of the last barcode scanned."
msgstr "Valeur du dernier code-barres scanné."

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/widgets/image_preview.xml:0
msgid "Viewer"
msgstr "Visionneur"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_warehouse
msgid "Warehouse"
msgstr "Entrepôt"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid ""
"When scanning a location in inventory adjustments, you will be assigned to "
"count all products from this location"
msgstr ""
"Lorsque vous scannez un emplacement dans le cadre d'ajustements "
"d'inventaire, vous êtes chargé de compter tous les produits de cet "
"emplacement."

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "Wrong Unit of Measure"
msgstr "Mauvaise unité de mesure"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/apply_quant_dialog.xml:0
msgid "Yes"
msgstr "Oui"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid ""
"You are about to take the product %(productName)s from the location %(locationName)s but this product isn't reserved in this location.\n"
"Scan the current location to confirm that."
msgstr ""
"Vous êtes sur le point de retirer le produit %(productName)s de l'emplacement %(locationName)s, mais ce produit n'est pas réservé dans cet emplacement. \n"
"Scannez l'emplacement actuel pour confirmer."

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
msgid ""
"You are expected to scan one or more products or a package available at the "
"picking location"
msgstr "Vous êtes censé scanner un ou plusieurs produits/colis disponibles "

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "You are supposed to scan %s or another source location"
msgstr "Vous êtes censé scanner %s ou un autre emplacement d'origine"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "You can't apply a package type. First, scan product or select a line"
msgstr ""
"Vous ne pouvez pas appliquer de type de colis. Scannez d'abord un produit ou"
" sélectionnez une ligne."

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "You can't register scrap at this state of the operation"
msgstr "Vous ne pouvez pas enregistrer des rebuts à ce stade de l'opération"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid ""
"You have already scanned %s items of this package. Do you want to scan the "
"whole package?"
msgstr ""
"Vous avez déjà scanné %s articles de ce colis. Voulez-vous scanner "
"l'ensemble du colis ?"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_backorder_confirmation
msgid "You have processed less products than the initial demand:"
msgstr "Vous avez traité moins de produits que la demande initiale :"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "You must scan a package or put in pack"
msgstr "Vous devez scanner un colis ou mettre en colis"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "You must scan a product"
msgstr "Vous devez scanner un produit"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "barcodes"
msgstr "codes-barres"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "demo data sheet"
msgstr "feuille de données de démonstration"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "for operations."
msgstr "pour les opérations."

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "location"
msgstr "emplacement "

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/kanban/stock_barcode_kanban_renderer.xml:0
msgid "lot"
msgstr "lot"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "operation type"
msgstr "type d'opération"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/kanban/stock_barcode_kanban_renderer.xml:0
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "or a"
msgstr "ou une"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/kanban/stock_barcode_kanban_renderer.xml:0
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "package"
msgstr "colis"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "picking"
msgstr "préparation"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/kanban/stock_barcode_kanban_renderer.xml:0
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "product"
msgstr "produit"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_backorder_confirmation
msgid "products were not processed at all."
msgstr "produits n'ont pas du tout été traités."

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_backorder_confirmation
msgid "products were partially processed."
msgstr "produits ont été partiellement traités."

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/kanban/stock_barcode_kanban_renderer.xml:0
msgid "to filter your records"
msgstr "pour filtrer vos enregistrements"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "to initiate a transfer"
msgstr "pour lancer un transfert"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "to locate it"
msgstr "pour le localiser"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "to open it"
msgstr "pour l'ouvrir"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "to start it"
msgstr "pour démarrer"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "to test or"
msgstr "pour tester ou"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "tracking number"
msgstr "numéro de suivi"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/kanban/stock_barcode_kanban_renderer.xml:0
msgid "transfer"
msgstr "transfert"
