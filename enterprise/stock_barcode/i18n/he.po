# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_barcode
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2024
# NoaFarkash, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON>r, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# yael terner, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 09:12+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=3; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: 2;\n"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "%s can't be inventoried. Only storable products can be inventoried."
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
msgid "<i class=\"fa fa-fw fa-lg fa-barcode me-3\" title=\"Serial/Lot Number\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
msgid "<i class=\"fa fa-fw fa-lg fa-cube me-3\" title=\"Quantity\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
msgid ""
"<i class=\"fa fa-fw fa-lg fa-long-arrow-right me-3\" title=\"Scrap "
"Location\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
msgid "<i class=\"fa fa-fw fa-lg fa-tags me-3\" title=\"Product\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode_kanban_2
msgid "<i class=\"fa fa-lg fa-archive\" title=\"Package\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode_kanban_2
msgid "<i class=\"fa fa-lg fa-user-o\" title=\"Owner\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "<i class=\"fa fa-print\"/> Print barcode commands and operation types"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "<i class=\"fa fa-print\"/> Print barcode demo sheet"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "<i class=\"fa fa-print\"/> Print storage locations"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "<i class=\"fa fs-4 fa-tags me-1\" title=\"Product\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
msgid "<span>Replenish Quantities</span>"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
msgid ""
"A product tracked by serial numbers can't have multiple quantities for the "
"same serial number."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.js:0
msgid "Add Product"
msgstr "הוסף מוצר"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Add extra product?"
msgstr "האם להוסיף מוצר חדש?"

#. module: stock_barcode
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_put_in_pack__mandatory
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_scan_dest_location__mandatory
msgid "After each product"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_put_in_pack__optional
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_scan_dest_location__optional
msgid "After group of Products"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "All products need to be packed"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_barcode
msgid "Allocation"
msgstr "Allocation"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__barcode_allow_extra_product
msgid "Allow extra products"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__barcode_validation_full
msgid "Allow full picking validation"
msgstr "אפשר תיקוף מלא של פעולות ליקוט"

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_picking_type__barcode_validation_full
msgid ""
"Allow to validate a picking even if nothing was scanned yet (and so, do an "
"immediate transfert)"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid ""
"Allow users to see the expected quantity of a product to count in a location"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_picking_type__show_reserved_sns
msgid ""
"Allows to display reserved lots/serial numbers. When non active, it is clear"
" for the picker that they can pick the lots/serials they want."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
msgid ""
"An unexisting package type was scanned. This part of the barcode can't be "
"processed."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.xml:0
#: code:addons/stock_barcode/static/src/components/manual_barcode.xml:0
msgid "Apply"
msgstr "החל"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_cancel_operation_view
msgid "Are you sure you want to cancel this operation?"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/backorder_dialog.xml:0
msgid "Backorder"
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_backorder_confirmation
msgid "Backorder Confirmation"
msgstr "אישור הזמנה חוזרת"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
#: model:ir.actions.client,name:stock_barcode.stock_barcode_action_main_menu
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__product_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_scrap__product_barcode
#: model:ir.ui.menu,name:stock_barcode.stock_barcode_menu
msgid "Barcode"
msgstr "ברקוד"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_view_picking_type_form
msgid "Barcode App"
msgstr "אפליקציית ברקוד"

#. module: stock_barcode
#: model:ir.actions.client,name:stock_barcode.stock_barcode_inventory_client_action
msgid "Barcode Inventory Client Action"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "Barcode Nomenclature"
msgstr "מונחי ברקוד"

#. module: stock_barcode
#: model:ir.actions.client,name:stock_barcode.stock_barcode_picking_client_action
msgid "Barcode Picking Client Action"
msgstr "ליקוט ע\"י ברקוד פעולת משתמש קצה"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_scrap___barcode_scanned
msgid "Barcode Scanned"
msgstr "ברקוד נסרק"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "Barcode Scanner"
msgstr "סורק ברקוד"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
msgid "Barcode scan is ambiguous with several model: %s. Use the most likely."
msgstr ""

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/controllers/stock_barcode.py:0
msgid "Barcodes are not available."
msgstr ""

#. module: stock_barcode
#: model:product.template,name:stock_barcode.product_cable_management_box_2_product_template
msgid "Cable Management Box"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_cancel_operation_view
msgid "Cancel"
msgstr "בטל"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_barcode_cancel_operation
msgid "Cancel Operation"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Cancel Transfer"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_cancel_operation_view
msgid "Cancel operation"
msgstr ""

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/models/stock_picking.py:0
msgid "Cancel this operation?"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/count_screen_rfid.xml:0
#: code:addons/stock_barcode/static/src/components/main.xml:0
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
#: code:addons/stock_barcode/static/src/widgets/image_preview.xml:0
msgid "Close"
msgstr "סגור"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_res_config_settings
msgid "Config Settings"
msgstr "הגדרות תצורה"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "Configure Product Barcodes"
msgstr "הגדר ברקודי מוצר"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Confirm"
msgstr "אשר"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_res_partner
msgid "Contact"
msgstr "איש קשר"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__group_barcode_count_entire_location
#: model:res.groups,name:stock_barcode.group_barcode_count_entire_location
msgid "Count Entire Locations"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Counted Quantity"
msgstr "כמות שנספרה"

#. module: stock_barcode
#: model_terms:ir.actions.act_window,help:stock_barcode.stock_picking_action_kanban
msgid "Create a new transfer"
msgstr "צור העברה חדשה"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__create_uid
msgid "Created by"
msgstr "נוצר על-ידי"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__create_date
msgid "Created on"
msgstr "נוצר ב-"

#. module: stock_barcode
#: model:product.template,name:stock_barcode.product_custom_cabinet_metric_product_template
msgid "Customized Cabinet (Metric)"
msgstr ""

#. module: stock_barcode
#: model:product.template,name:stock_barcode.product_custom_cabinet_usa_product_template
msgid "Customized Cabinet (USA)"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.xml:0
msgid "Delete"
msgstr "מחיקה"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__stock_barcode_demo_active
msgid "Demo Data Active"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/line.xml:0
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Destination Location"
msgstr "מיקום יעד"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Destination Package"
msgstr "יעד חבילה"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Destination location must be scanned"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "Disable sound effect while scanning a barcode."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/apply_quant_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Discard"
msgstr "בטל"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__display_name
msgid "Display Name"
msgstr "שם לתצוגה"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.js:0
msgid ""
"Do you want to permanently remove this message ? It won't appear anymore, so"
" make sure you don't need the barcodes sheet or you have a copy."
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_picking_type__restrict_put_in_pack
msgid ""
"Does the picker have to put in a package the scanned products? If yes, at "
"which rate?"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_picking_type__restrict_scan_dest_location
msgid "Does the picker have to scan the destination? If yes, at which rate?"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_cancel_operation_view
msgid "Don't cancel"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.js:0
msgid "Don't show this message again"
msgstr "אל תציג הודעה זו שוב"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/backorder_dialog.xml:0
msgid "Done /"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "Download demo data sheet"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "Download operation barcodes"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__dummy_id
#: model:ir.model.fields,field_description:stock_barcode.field_stock_quant__dummy_id
msgid "Dummy"
msgstr "דמה"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_backorder_confirmation__empty_move_count
msgid "Empty Move Count"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/manual_barcode.xml:0
msgid "Enter a barcode..."
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_view_picking_type_form
msgid "Final Validation"
msgstr "אימות סופי"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Following backorder was created:"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Following backorders were created:"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_picking_type__barcode_allow_extra_product
msgid "For planned transfers, allow to add non-reserved products"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__restrict_scan_dest_location
msgid "Force Destination Location scan?"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__restrict_scan_tracking_number
msgid "Force Lot/Serial scan?"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__restrict_scan_product
msgid "Force Product scan?"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__restrict_scan_source_location
msgid "Force Source Location scan?"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__barcode_validation_after_dest_location
msgid "Force a destination on all products"
msgstr "חייב איתור יעד לכל המוצרים"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__barcode_validation_all_product_packed
msgid "Force all products to be packed"
msgstr "חייב שכל המוצרים יהיו ארוזים"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__restrict_put_in_pack
msgid "Force put in pack?"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__formatted_product_barcode
msgid "Formatted Product Barcode"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_product_product__has_image
msgid "Has Image"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__hide_lot
msgid "Hide Lot"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__hide_lot_name
msgid "Hide Lot Name"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__id
msgid "ID"
msgstr "מזהה"

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/models/stock_picking_type.py:0
msgid ""
"If the source location must be scanned, then the destination location must "
"either be scanned after each product or not scanned at all."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/backorder_dialog.xml:0
msgid ""
"If you validate now, the remaining products will be added to a backorder."
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__image_1920
#: model:ir.model.fields,field_description:stock_barcode.field_stock_quant__image_1920
msgid "Image"
msgstr "תמונה"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/backorder_dialog.xml:0
msgid "Incomplete Transfer"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "Install"
msgstr "להתקין"

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_move_line__product_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_scrap__product_barcode
msgid "International Article Number used for product identification."
msgstr "מספר זיהוי בינלאומי המשמש לזיהוי המוצר."

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "Inventory Adjustment"
msgstr "התאמת מלאי"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_location
msgid "Inventory Locations"
msgstr "איתורי מלאי"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "Inventory count"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__is_barcode_picking_type
msgid "Is Barcode Picking Type"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__write_uid
msgid "Last Updated by"
msgstr "עודכן לאחרונה על-ידי"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__write_date
msgid "Last Updated on"
msgstr "עדכון אחרון ב"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.js:0
msgid "Leave it"
msgstr "עזוב את זה"

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_picking_type__restrict_scan_product
msgid "Line's product must be scanned before the line can be edited"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Location"
msgstr "מיקום"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__location_processed
msgid "Location Processed"
msgstr "עיבוד המיקום "

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "Log out"
msgstr "התנתק"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_lot
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_view_picking_type_form
msgid "Lot/Serial"
msgstr "מספר סידורי/אצווה"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Mandatory Destination Location"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_scan_source_location__mandatory
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_scan_tracking_number__mandatory
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_view_picking_type_form
msgid "Mandatory Scan"
msgstr "חובת סריקה"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Mandatory Source Location"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__barcode_max_time_between_keys_in_ms
msgid "Max time between each key"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_res_config_settings__barcode_max_time_between_keys_in_ms
msgid "Maximum delay between each key in ms (100 ms by default)"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__barcode_separator_regex
msgid "Multiscan Separator"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__stock_barcode_mute_sound_notifications
msgid "Mute Barcode application sounds"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_put_in_pack__no
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_scan_dest_location__no
msgid "No"
msgstr "לא"

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/models/stock_picking.py:0
msgid "No %(picking_type)s ready for this %(barcode_type)s"
msgstr ""

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/models/stock_move_line.py:0
msgid "No Barcode"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_scan_source_location__no
msgid "No Scan"
msgstr ""

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/controllers/stock_barcode.py:0
msgid ""
"No internal operation type. Please configure one in warehouse settings."
msgstr "אין סוג פעולה פנימי. נא הגדר אחד בהגדרות המחסן."

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/controllers/stock_barcode.py:0
msgid "No picking or location or product corresponding to barcode %(barcode)s"
msgstr ""

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/controllers/stock_barcode.py:0
msgid "No picking or product corresponding to barcode %(barcode)s"
msgstr ""

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/models/stock_picking.py:0
msgid "No product, lot or package found for barcode %s"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/apply_quant_dialog.xml:0
msgid "No, I'll count them later"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__barcode_nomenclature_id
msgid "Nomenclature"
msgstr "מונחים"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Not the expected scan"
msgstr ""

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/models/stock_picking.py:0
#: model:ir.actions.act_window,name:stock_barcode.open_picking
msgid "Open picking form"
msgstr "פתח טופס ליקוט"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.xml:0
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
#: model:ir.actions.act_window,name:stock_barcode.stock_picking_action_kanban
#: model:ir.actions.act_window,name:stock_barcode.stock_picking_type_action_kanban
msgid "Operations"
msgstr "פעולות"

#. module: stock_barcode
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_scan_tracking_number__optional
msgid "Optional Scan"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_view_picking_type_form
msgid "Options"
msgstr "מוצרים אופציונליים"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Owner"
msgstr "אחראי"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Package"
msgstr "חבילה"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "Package type %(type)s applied to the package %(package)s"
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_quant_package
msgid "Packages"
msgstr "חבילות"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__product_packaging_id
msgid "Packaging"
msgstr "אריזה"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__product_packaging_uom_qty
msgid "Packaging Quantity"
msgstr "כמות אריזה"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__parent_location_id
msgid "Parent Location"
msgstr "מיקום אב"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__parent_location_dest_id
msgid "Parent Location Dest"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_backorder_confirmation__partial_move_count
msgid "Partial Move Count"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_barcode
msgid "Picking Details"
msgstr "פרטי ליקוט"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_picking_type
msgid "Picking Type"
msgstr "סוג ליקוט"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Please scan destination location for %s before scanning other product"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.js:0
msgid "Please, Scan again!"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Press Validate or scan another product"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.xml:0
msgid "Print"
msgstr "הדפס"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Print Barcodes"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Print Delivery Slip"
msgstr "הדפסת תעודת משלוח"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "Print Inventory"
msgstr "הדפס מלאי"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Print Packages"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Print Picking Operations"
msgstr "הדפס פעולות ליקוט"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "Print the"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.js:0
msgid "Processing %(processed)s/%(toProcess)s barcodes"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/backorder_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Product"
msgstr "מוצר"

#. module: stock_barcode
#: model:ir.actions.act_window,name:stock_barcode.product_action_barcodes
#: model_terms:ir.ui.view,arch_db:stock_barcode.product_view_list_barcodes
msgid "Product Barcodes"
msgstr "ברקודי מוצר "

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "תנועות המוצר (תנועת שורת מלאי)"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_product_packaging
msgid "Product Packaging"
msgstr "אריזת מוצר"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__product_reference_code
#: model:ir.model.fields,field_description:stock_barcode.field_stock_quant__product_reference_code
msgid "Product Reference Code"
msgstr "קוד מזהה מוצר"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__product_stock_quant_ids
msgid "Product Stock Quant"
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_uom_uom
msgid "Product Unit of Measure"
msgstr "יחידת מידה של מוצר"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_product_product
msgid "Product Variant"
msgstr "וריאנט מוצר"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.xml:0
msgid "Put In Pack"
msgstr "הכנס לחבילה"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_view_picking_type_form
msgid "Put in Pack"
msgstr "הכנס לחבילה "

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__qty_done
msgid "Qty Done"
msgstr "כמות שהושלמה"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Quantity"
msgstr "כמות"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode_kanban
msgid "Quantity Done"
msgstr "כמות שבוצעה"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Quantity in stock"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_move_line__product_packaging_uom_qty
msgid "Quantity of the Packaging in the UoM of the Stock Move Line."
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_quant
msgid "Quants"
msgstr "אנליסטים כמותיים"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__barcode_rfid_batch_time
msgid "RFID Timer"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/count_screen_rfid.xml:0
msgid "Read Rate"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/count_screen_rfid.xml:0
msgid "Read Time"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.js:0
msgid "Remove it"
msgstr "הסר זאת"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.xml:0
msgid "Return Products"
msgstr "החזר מוצרים"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/kanban/stock_barcode_kanban_renderer.xml:0
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "Scan a"
msgstr "סרוק "

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "Scan a location"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a lot number"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a lot number or a packages then the destination location"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a lot number then the destination location"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a package"
msgstr "סרוק את החבילה"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a package or put in pack"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a package or the destination location"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a package, the destination location or another product"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "Scan a product"
msgstr "סרוק מוצר"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a product before scanning a tracking number"
msgstr "סרוק מוצר לפני סריקת מספר מזהה מוצר"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a product from %s"
msgstr "סרוק מוצר מ%s"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "Scan a product in %s or scan another location"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a product or a package"
msgstr "סרוק מוצר או חבילה"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a product or another package"
msgstr "סרוק מוצר או חבילה אחרת"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a product or the destination location."
msgstr "סרוק מוצר או את מיקום היעד."

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a product then the destination location"
msgstr "סרוק מוצר ולאחר מכן את מיקום היעד"

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/models/stock_picking.py:0
msgid ""
"Scan a product, a lot/serial number or a package to filter the transfers."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a product, a package or the destination location"
msgstr "סרוק מוצר, חבילה או מיקום היעד"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a product, a package or the destination location."
msgstr "סרוק מוצר, חבילה או מיקום היעד"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a serial number"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a serial number or a package"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a serial number or a package then the destination location"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a serial number then the destination location"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "Scan an"
msgstr "סרוק "

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan another serial number"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "Scan lot numbers for product %s to change their quantity"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan more lot numbers"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan more lot numbers or a package"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "Scan more products in %s or scan another location"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan more products or a package"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan more products, or scan a new source location"
msgstr "סרוק מוצרים נוספים או סרוק מיקום  מוצא חדש"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "Scan or tap"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "Scan serial numbers for product %s to change their quantity"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan the destination location"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan the package %s"
msgstr "סרוק את החבילה%s"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan the source location"
msgstr "סרוק את איתור המקור"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan the source location or a package"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid ""
"Scanned product %s is not reserved for this transfer. Are you sure you want "
"to add it?"
msgstr ""
"המוצר הנסרק %s לא שמור עבור פעולה זו. האם אתה בטוח שאתה רוצה להוסיף אותו?"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid ""
"Scanned quantity uses %(unit)s as its Unit of Measure (UoM), but it is not "
"compatible with the line's UoM (%(lineUnit)s)."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid ""
"Scanned quantity uses %(unit)s as its Unit of Measure (UoM), but it is not "
"compatible with the product's UoM (%(productUnit)s)."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scanning package"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.xml:0
#: model:ir.model,name:stock_barcode.model_stock_scrap
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
msgid "Scrap"
msgstr "פסול"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
msgid "Scrap Location"
msgstr "מיקום פסולים"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Select a Product"
msgstr "בחר מוצר "

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Serial/Lot Number"
msgstr "מספר סידורי/ אצווה"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "Set the maximum delay between each key in ms (100 ms by default.)"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__show_barcode_nomenclature
msgid "Show Barcode Nomenclature"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__show_barcode_validation
msgid "Show Barcode Validation"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__group_barcode_show_quantity_count
#: model:res.groups,name:stock_barcode.group_barcode_show_quantity_count
msgid "Show Quantity to Count"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__show_reserved_sns
msgid "Show reserved lots/SN"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.xml:0
msgid "Sign"
msgstr "חתום"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/apply_quant_dialog.xml:0
msgid "Some serials where not counted, set them as missing?"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Source Location"
msgstr "מיקום מוצא"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Source Package"
msgstr "חבילת מקור"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/backorder_dialog.xml:0
msgid "Stay on transfer"
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_move
msgid "Stock Move"
msgstr "תנועת מלאי"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_request_count
msgid "Stock Request an Inventory Count"
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_package_type
msgid "Stock package type"
msgstr "סוג חבילת מלאי"

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_picking_type__is_barcode_picking_type
msgid ""
"Technical field indicating if should be used in barcode app and used to "
"control visibility in the related UI."
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_picking_type__show_barcode_validation
msgid ""
"Technical field used to compute whether the \"Final Validation\" group "
"should be displayed, solving combined groups/invisible complexity."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "The inventory adjustment has been validated"
msgstr "התאמת המלאי אושרה"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "The product %s should not be picked in this operation."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "The scanned location doesn't belong to this operation's destination"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "The scanned location doesn't belong to this operation's location"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
msgid "The scanned serial number %s is already used."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "The transfer has been cancelled"
msgstr "ההעברה בוטלה"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "The transfer has been validated"
msgstr "ההעברה אושרה"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "There is nothing to apply in this page."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "There is nothing to print in this page."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "This package is already scanned."
msgstr "חבילה זו כבר סרוקה."

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "This picking is already done"
msgstr "ליקוט זה כבר בוצע"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "This picking is cancelled"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
msgid "This product doesn't exist."
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_res_config_settings__barcode_separator_regex
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid ""
"This regex is used in the Barcode application to separate individual "
"barcodes when an aggregate barcode (i.e. single barcode consisting of "
"multiple barcode encodings) is scanned."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
msgid "This serial number is already used."
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid ""
"Time before processing a batch. Used to group communication to the server "
"and improve global performance at the cost of reactivity."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/backorder_dialog.xml:0
msgid "To do"
msgstr "לביצוע"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "To use packages, enable 'Packages' in the settings"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/count_screen_rfid.xml:0
msgid "Total Reads"
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_picking
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__picking_id
msgid "Transfer"
msgstr "העברה"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__picking_name
msgid "Transfer Name"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.actions.act_window,help:stock_barcode.stock_picking_action_kanban
msgid "Transfers allow you to move products from one location to another."
msgstr "העברות מאפשרות לך להעביר מוצרים ממקום למקום."

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/count_screen_rfid.xml:0
msgid "Unique Tags"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode_kanban
msgid "Unit of Measure"
msgstr "יחידת מידה"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_stock_quant_tree
msgid "UoM"
msgstr "יחידת מידה"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/backorder_dialog.xml:0
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
msgid "Validate"
msgstr "אשר"

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_scrap___barcode_scanned
msgid "Value of the last barcode scanned."
msgstr "הערך של הברקוד האחרון שנסרק."

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/widgets/image_preview.xml:0
msgid "Viewer"
msgstr "צופה"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_warehouse
msgid "Warehouse"
msgstr "מחסן"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid ""
"When scanning a location in inventory adjustments, you will be assigned to "
"count all products from this location"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "Wrong Unit of Measure"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/apply_quant_dialog.xml:0
msgid "Yes"
msgstr "כן"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid ""
"You are about to take the product %(productName)s from the location %(locationName)s but this product isn't reserved in this location.\n"
"Scan the current location to confirm that."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
msgid ""
"You are expected to scan one or more products or a package available at the "
"picking location"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "You are supposed to scan %s or another source location"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "You can't apply a package type. First, scan product or select a line"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "You can't register scrap at this state of the operation"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid ""
"You have already scanned %s items of this package. Do you want to scan the "
"whole package?"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_backorder_confirmation
msgid "You have processed less products than the initial demand:"
msgstr "סרקת פחות מוצרים מהביקוש הראשוני."

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "You must scan a package or put in pack"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "You must scan a product"
msgstr "אתה חייב לסרוק' מוצר"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "barcodes"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "demo data sheet"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "for operations."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "location"
msgstr "מיקום"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/kanban/stock_barcode_kanban_renderer.xml:0
msgid "lot"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "operation type"
msgstr "סוג פעולה "

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/kanban/stock_barcode_kanban_renderer.xml:0
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "or a"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/kanban/stock_barcode_kanban_renderer.xml:0
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "package"
msgstr "חבילה"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "picking"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/kanban/stock_barcode_kanban_renderer.xml:0
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "product"
msgstr "מוצר"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_backorder_confirmation
msgid "products were not processed at all."
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_backorder_confirmation
msgid "products were partially processed."
msgstr "מוצרים נסרקו חלקית"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/kanban/stock_barcode_kanban_renderer.xml:0
msgid "to filter your records"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "to initiate a transfer"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "to locate it"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "to open it"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "to start it"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "to test or"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "tracking number"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/kanban/stock_barcode_kanban_renderer.xml:0
msgid "transfer"
msgstr ""
