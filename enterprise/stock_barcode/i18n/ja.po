# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_barcode
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 09:12+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Jun<PERSON>, 2024\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "%s can't be inventoried. Only storable products can be inventoried."
msgstr "%sは棚卸できません。在庫可能品のみ棚卸することができます。"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
msgid "<i class=\"fa fa-fw fa-lg fa-barcode me-3\" title=\"Serial/Lot Number\"/>"
msgstr "<i class=\"fa fa-fw fa-lg fa-barcode me-3\" title=\"Serial/Lot Number\"/>"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
msgid "<i class=\"fa fa-fw fa-lg fa-cube me-3\" title=\"Quantity\"/>"
msgstr "<i class=\"fa fa-fw fa-lg fa-cube me-3\" title=\"Quantity\"/>"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
msgid ""
"<i class=\"fa fa-fw fa-lg fa-long-arrow-right me-3\" title=\"Scrap "
"Location\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-lg fa-long-arrow-right me-3\" title=\"Scrap "
"Location\"/>"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
msgid "<i class=\"fa fa-fw fa-lg fa-tags me-3\" title=\"Product\"/>"
msgstr "<i class=\"fa fa-fw fa-lg fa-tags me-3\" title=\"Product\"/>"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode_kanban_2
msgid "<i class=\"fa fa-lg fa-archive\" title=\"Package\"/>"
msgstr "<i class=\"fa fa-lg fa-archive\" title=\"Package\"/>"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode_kanban_2
msgid "<i class=\"fa fa-lg fa-user-o\" title=\"Owner\"/>"
msgstr "<i class=\"fa fa-lg fa-user-o\" title=\"Owner\"/>"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "<i class=\"fa fa-print\"/> Print barcode commands and operation types"
msgstr "<i class=\"fa fa-print\"/> バーコードコマンドとオペレーションタイプを印刷"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "<i class=\"fa fa-print\"/> Print barcode demo sheet"
msgstr "<i class=\"fa fa-print\"/> バーコード印刷のデモシート"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "<i class=\"fa fa-print\"/> Print storage locations"
msgstr "<i class=\"fa fa-print\"/>倉庫ロケーションを印刷 "

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "<i class=\"fa fs-4 fa-tags me-1\" title=\"Product\"/>"
msgstr "<i class=\"fa fs-4 fa-tags me-1\" title=\"Product\"/>"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
msgid "<span>Replenish Quantities</span>"
msgstr "<span>補充数量</span>"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
msgid ""
"A product tracked by serial numbers can't have multiple quantities for the "
"same serial number."
msgstr "シリアル番号で追跡されるプロダクトは、同じシリアル番号で複数の数量を持つことはできません。"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.js:0
msgid "Add Product"
msgstr "プロダクト追加"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Add extra product?"
msgstr "プロダクトを追加しますか？"

#. module: stock_barcode
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_put_in_pack__mandatory
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_scan_dest_location__mandatory
msgid "After each product"
msgstr "各プロダクトの後に"

#. module: stock_barcode
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_put_in_pack__optional
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_scan_dest_location__optional
msgid "After group of Products"
msgstr "プロダクトのグループの後に"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "All products need to be packed"
msgstr "全てのプロダクトは梱包される必要があります"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_barcode
msgid "Allocation"
msgstr "割当"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__barcode_allow_extra_product
msgid "Allow extra products"
msgstr "追加プロダクトを許可"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__barcode_validation_full
msgid "Allow full picking validation"
msgstr "フルピッキングの検証を許可"

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_picking_type__barcode_validation_full
msgid ""
"Allow to validate a picking even if nothing was scanned yet (and so, do an "
"immediate transfert)"
msgstr "まだ何もスキャンされていなくても、ピッキングを検証できるようにします。(よってすぐに転送して下さい)"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid ""
"Allow users to see the expected quantity of a product to count in a location"
msgstr "ユーザが、あるロケーションのプロダクトの予想棚卸数量を確認できるようにします。"

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_picking_type__show_reserved_sns
msgid ""
"Allows to display reserved lots/serial numbers. When non active, it is clear"
" for the picker that they can pick the lots/serials they want."
msgstr "引当済ロット/シリアル番号の表示を許可します。有効化されていない場合、ピッカーは希望のロット/シリアルをピッキングできます。"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
msgid ""
"An unexisting package type was scanned. This part of the barcode can't be "
"processed."
msgstr "存在しない梱包タイプがスキャンされました。バーコードのこの部分は処理できません。"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.xml:0
#: code:addons/stock_barcode/static/src/components/manual_barcode.xml:0
msgid "Apply"
msgstr "適用"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_cancel_operation_view
msgid "Are you sure you want to cancel this operation?"
msgstr "このオペレーションを本当にキャンセルしますか？"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/backorder_dialog.xml:0
msgid "Backorder"
msgstr "バックオーダ"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_backorder_confirmation
msgid "Backorder Confirmation"
msgstr "バックオーダ確認"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
#: model:ir.actions.client,name:stock_barcode.stock_barcode_action_main_menu
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__product_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_scrap__product_barcode
#: model:ir.ui.menu,name:stock_barcode.stock_barcode_menu
msgid "Barcode"
msgstr "バーコード"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_view_picking_type_form
msgid "Barcode App"
msgstr "バーコードアプリ"

#. module: stock_barcode
#: model:ir.actions.client,name:stock_barcode.stock_barcode_inventory_client_action
msgid "Barcode Inventory Client Action"
msgstr "バーコード在庫顧客アクション"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "Barcode Nomenclature"
msgstr "バーコード表現規則"

#. module: stock_barcode
#: model:ir.actions.client,name:stock_barcode.stock_barcode_picking_client_action
msgid "Barcode Picking Client Action"
msgstr "バーコードピッキングクライアントアクション"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_scrap___barcode_scanned
msgid "Barcode Scanned"
msgstr "スキャンされたバーコード"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "Barcode Scanner"
msgstr "バーコードスキャナ"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
msgid "Barcode scan is ambiguous with several model: %s. Use the most likely."
msgstr "バーコードスキャンは、いくつかのモデルであいまいです%s： 最も可能性の高いものを使用して下さい。"

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/controllers/stock_barcode.py:0
msgid "Barcodes are not available."
msgstr "バーコードが利用できません。"

#. module: stock_barcode
#: model:product.template,name:stock_barcode.product_cable_management_box_2_product_template
msgid "Cable Management Box"
msgstr "ケーブル管理ボックス"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_cancel_operation_view
msgid "Cancel"
msgstr "キャンセル"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_barcode_cancel_operation
msgid "Cancel Operation"
msgstr "オペレーションをキャンセル"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Cancel Transfer"
msgstr "転送をキャンセル"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_cancel_operation_view
msgid "Cancel operation"
msgstr "オペレーションをキャンセル"

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/models/stock_picking.py:0
msgid "Cancel this operation?"
msgstr "このオペレーションをキャンセルしますか？"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/count_screen_rfid.xml:0
#: code:addons/stock_barcode/static/src/components/main.xml:0
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
#: code:addons/stock_barcode/static/src/widgets/image_preview.xml:0
msgid "Close"
msgstr "閉じる"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_res_config_settings
msgid "Config Settings"
msgstr "コンフィグ設定"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "Configure Product Barcodes"
msgstr "製品バーコードを設定"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Confirm"
msgstr "確認"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_res_partner
msgid "Contact"
msgstr "連絡先"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__group_barcode_count_entire_location
#: model:res.groups,name:stock_barcode.group_barcode_count_entire_location
msgid "Count Entire Locations"
msgstr "全ロケーションをカウント"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Counted Quantity"
msgstr "棚卸数量"

#. module: stock_barcode
#: model_terms:ir.actions.act_window,help:stock_barcode.stock_picking_action_kanban
msgid "Create a new transfer"
msgstr "新しい転送を作成する"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__create_uid
msgid "Created by"
msgstr "作成者"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__create_date
msgid "Created on"
msgstr "作成日"

#. module: stock_barcode
#: model:product.template,name:stock_barcode.product_custom_cabinet_metric_product_template
msgid "Customized Cabinet (Metric)"
msgstr "カスタマイズキャビネット (メートル)"

#. module: stock_barcode
#: model:product.template,name:stock_barcode.product_custom_cabinet_usa_product_template
msgid "Customized Cabinet (USA)"
msgstr "カスタマイズキャビネット (USA)"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.xml:0
msgid "Delete"
msgstr "削除"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__stock_barcode_demo_active
msgid "Demo Data Active"
msgstr "アクティブなデモデータ"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/line.xml:0
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Destination Location"
msgstr "移動先ロケーション"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Destination Package"
msgstr "先梱包"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Destination location must be scanned"
msgstr "移動先ロケーションをスキャンして下さい。"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "Disable sound effect while scanning a barcode."
msgstr "バーコードをスキャンしている間、効果音を無効にします。"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/apply_quant_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Discard"
msgstr "破棄"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__display_name
msgid "Display Name"
msgstr "表示名"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.js:0
msgid ""
"Do you want to permanently remove this message ? It won't appear anymore, so"
" make sure you don't need the barcodes sheet or you have a copy."
msgstr ""
"このメッセージを永久に削除しますか？このメッセージは表示されなくなりますので、バーコードシートが不要か、コピーを持っていることを確認して下さい。"

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_picking_type__restrict_put_in_pack
msgid ""
"Does the picker have to put in a package the scanned products? If yes, at "
"which rate?"
msgstr "ピッカーはスキャンしたプロダクトを梱包に入れる必要がありますか？その場合、どれくらいの割合で行いますか？"

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_picking_type__restrict_scan_dest_location
msgid "Does the picker have to scan the destination? If yes, at which rate?"
msgstr "ピッカーは移動先ロケーションをスキャンする必要がありますか？その場合、どれくらいの割合で行いますか？"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_cancel_operation_view
msgid "Don't cancel"
msgstr "取消しない"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.js:0
msgid "Don't show this message again"
msgstr "このメッセージを二度と表示しないでください"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/backorder_dialog.xml:0
msgid "Done /"
msgstr "完了 /"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "Download demo data sheet"
msgstr "デモデータシートをダウンロード"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "Download operation barcodes"
msgstr "オペレーションバーコードをダウンロード"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__dummy_id
#: model:ir.model.fields,field_description:stock_barcode.field_stock_quant__dummy_id
msgid "Dummy"
msgstr "ダミー"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_backorder_confirmation__empty_move_count
msgid "Empty Move Count"
msgstr "空の移動数"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/manual_barcode.xml:0
msgid "Enter a barcode..."
msgstr "バーコードを記入..."

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_view_picking_type_form
msgid "Final Validation"
msgstr "最終検証"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Following backorder was created:"
msgstr "以下のバックオーダが作成されました:"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Following backorders were created:"
msgstr "以下のバックオーダが作成されました:"

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_picking_type__barcode_allow_extra_product
msgid "For planned transfers, allow to add non-reserved products"
msgstr "計画的な移動の場合、非引当プロダクトの追加を許可する"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__restrict_scan_dest_location
msgid "Force Destination Location scan?"
msgstr "宛先スキャンを強制しますか？"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__restrict_scan_tracking_number
msgid "Force Lot/Serial scan?"
msgstr "ロット/シリアルスキャンを強制しますか？ "

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__restrict_scan_product
msgid "Force Product scan?"
msgstr "プロダクトスキャンを強制しますか？"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__restrict_scan_source_location
msgid "Force Source Location scan?"
msgstr "移動元ロケーションスキャンを強制しますか？"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__barcode_validation_after_dest_location
msgid "Force a destination on all products"
msgstr "全てのプロダクトに宛先を強制する"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__barcode_validation_all_product_packed
msgid "Force all products to be packed"
msgstr "全てのプロダクトの梱包を強制する"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__restrict_put_in_pack
msgid "Force put in pack?"
msgstr "梱包材に入れることを強制しますか？"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__formatted_product_barcode
msgid "Formatted Product Barcode"
msgstr "フォーマット済プロダクトバーコード"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_product_product__has_image
msgid "Has Image"
msgstr "画像あり"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__hide_lot
msgid "Hide Lot"
msgstr "ロットを非表示"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__hide_lot_name
msgid "Hide Lot Name"
msgstr "ロット名を非表示"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__id
msgid "ID"
msgstr "ID"

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/models/stock_picking_type.py:0
msgid ""
"If the source location must be scanned, then the destination location must "
"either be scanned after each product or not scanned at all."
msgstr ""
"移動元ロケーションをスキャンしなければならない場合、移動先ロケーションは、各プロダクトの後にスキャンされるか、全くスキャンされないかのどちらかである必要があります。"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/backorder_dialog.xml:0
msgid ""
"If you validate now, the remaining products will be added to a backorder."
msgstr "今すぐ検証すると、残りのプロダクトはバックオーダに追加されます。"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__image_1920
#: model:ir.model.fields,field_description:stock_barcode.field_stock_quant__image_1920
msgid "Image"
msgstr "画像"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/backorder_dialog.xml:0
msgid "Incomplete Transfer"
msgstr "転送未完了"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "Install"
msgstr "インストール"

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_move_line__product_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_scrap__product_barcode
msgid "International Article Number used for product identification."
msgstr "製品識別に使用する国際物品番号。"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "Inventory Adjustment"
msgstr "在庫調整"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_location
msgid "Inventory Locations"
msgstr "在庫ロケーション"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "Inventory count"
msgstr "在庫数"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__is_barcode_picking_type
msgid "Is Barcode Picking Type"
msgstr "バーコードピッキングタイプ"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__write_uid
msgid "Last Updated by"
msgstr "最終更新者"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__write_date
msgid "Last Updated on"
msgstr "最終更新日"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.js:0
msgid "Leave it"
msgstr "そのままにしておきます"

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_picking_type__restrict_scan_product
msgid "Line's product must be scanned before the line can be edited"
msgstr "明細を編集する前に、明細のプロダクトをスキャンして下さい。"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Location"
msgstr "ロケーション"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__location_processed
msgid "Location Processed"
msgstr "処理された場所"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "Log out"
msgstr "ログアウト"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_lot
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_view_picking_type_form
msgid "Lot/Serial"
msgstr "ロット/シリアル"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Mandatory Destination Location"
msgstr "必須移動先ロケーション"

#. module: stock_barcode
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_scan_source_location__mandatory
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_scan_tracking_number__mandatory
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_view_picking_type_form
msgid "Mandatory Scan"
msgstr "必須スキャン"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Mandatory Source Location"
msgstr "必須移動元ロケーション"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__barcode_max_time_between_keys_in_ms
msgid "Max time between each key"
msgstr "各キー間の最大時間"

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_res_config_settings__barcode_max_time_between_keys_in_ms
msgid "Maximum delay between each key in ms (100 ms by default)"
msgstr "各キー間の最大遅延時間(分) (デフォルト100分)"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__barcode_separator_regex
msgid "Multiscan Separator"
msgstr "マルチスキャンセパレータ"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__stock_barcode_mute_sound_notifications
msgid "Mute Barcode application sounds"
msgstr "バーコードアプリオンをミュート"

#. module: stock_barcode
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_put_in_pack__no
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_scan_dest_location__no
msgid "No"
msgstr "いいえ"

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/models/stock_picking.py:0
msgid "No %(picking_type)s ready for this %(barcode_type)s"
msgstr " %(picking_type)sは一切、この%(barcode_type)s用に準備完了していません。"

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/models/stock_move_line.py:0
msgid "No Barcode"
msgstr "バーコードなし"

#. module: stock_barcode
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_scan_source_location__no
msgid "No Scan"
msgstr "スキャンなし"

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/controllers/stock_barcode.py:0
msgid ""
"No internal operation type. Please configure one in warehouse settings."
msgstr "内部オペレーションタイプはありません。倉庫設定で構成してください。"

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/controllers/stock_barcode.py:0
msgid "No picking or location or product corresponding to barcode %(barcode)s"
msgstr "バーコード%(barcode)sに対応するピッキングまたはロケーションまたはプロダクトはありません。"

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/controllers/stock_barcode.py:0
msgid "No picking or product corresponding to barcode %(barcode)s"
msgstr "バーコード%(barcode)sに対応するピッキングまたはプロダクトはありません。"

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/models/stock_picking.py:0
msgid "No product, lot or package found for barcode %s"
msgstr "バーコード%s用にプロダクト、ロットおよび梱包が見つかりません。"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/apply_quant_dialog.xml:0
msgid "No, I'll count them later"
msgstr "いいえ、後でカウントします"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__barcode_nomenclature_id
msgid "Nomenclature"
msgstr "体系"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Not the expected scan"
msgstr "予測されたスキャンではありません"

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/models/stock_picking.py:0
#: model:ir.actions.act_window,name:stock_barcode.open_picking
msgid "Open picking form"
msgstr "ピッキングフォームを開く"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.xml:0
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
#: model:ir.actions.act_window,name:stock_barcode.stock_picking_action_kanban
#: model:ir.actions.act_window,name:stock_barcode.stock_picking_type_action_kanban
msgid "Operations"
msgstr "オペレーション"

#. module: stock_barcode
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_scan_tracking_number__optional
msgid "Optional Scan"
msgstr "任意のスキャン"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_view_picking_type_form
msgid "Options"
msgstr "オプション"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Owner"
msgstr "オーナー"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Package"
msgstr "梱包"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "Package type %(type)s applied to the package %(package)s"
msgstr "梱包タイプ %(type)sが梱包%(package)sに適用されました"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_quant_package
msgid "Packages"
msgstr "梱包"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__product_packaging_id
msgid "Packaging"
msgstr "パッケージング"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__product_packaging_uom_qty
msgid "Packaging Quantity"
msgstr "パッケージング数量"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__parent_location_id
msgid "Parent Location"
msgstr "親ロケーション"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__parent_location_dest_id
msgid "Parent Location Dest"
msgstr "移動先親ロケーション"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_backorder_confirmation__partial_move_count
msgid "Partial Move Count"
msgstr "部分移動数"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_barcode
msgid "Picking Details"
msgstr "ピッキングの詳細"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_picking_type
msgid "Picking Type"
msgstr "ピッキングタイプ"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Please scan destination location for %s before scanning other product"
msgstr "他のプロダクトをスキャンする前に%s用の移動先ロケーションをスキャンして下さい。"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.js:0
msgid "Please, Scan again!"
msgstr "もう一度スキャンして下さい。"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Press Validate or scan another product"
msgstr "検証を押すか、他のプロダクトをスキャン"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.xml:0
msgid "Print"
msgstr "印刷"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Print Barcodes"
msgstr "バーコード印刷"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Print Delivery Slip"
msgstr "配達伝票を印刷する"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "Print Inventory"
msgstr "在庫の印刷"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Print Packages"
msgstr "パッケージを印刷"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Print Picking Operations"
msgstr "印刷ピッキング操作"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "Print the"
msgstr "印刷:"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.js:0
msgid "Processing %(processed)s/%(toProcess)s barcodes"
msgstr "処理中 %(processed)s/%(toProcess)s バーコード"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/backorder_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Product"
msgstr "プロダクト"

#. module: stock_barcode
#: model:ir.actions.act_window,name:stock_barcode.product_action_barcodes
#: model_terms:ir.ui.view,arch_db:stock_barcode.product_view_list_barcodes
msgid "Product Barcodes"
msgstr "製品バーコード"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "プロダクトの移動(在庫移動ライン)"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_product_packaging
msgid "Product Packaging"
msgstr "プロダクトパッケージング"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__product_reference_code
#: model:ir.model.fields,field_description:stock_barcode.field_stock_quant__product_reference_code
msgid "Product Reference Code"
msgstr "プロダクト参照コード"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__product_stock_quant_ids
msgid "Product Stock Quant"
msgstr "プロダクト在庫数"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_uom_uom
msgid "Product Unit of Measure"
msgstr "プロダクト単位"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_product_product
msgid "Product Variant"
msgstr "プロダクトバリアント"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.xml:0
msgid "Put In Pack"
msgstr "梱包する"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_view_picking_type_form
msgid "Put in Pack"
msgstr "梱包する"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__qty_done
msgid "Qty Done"
msgstr "数量完了"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Quantity"
msgstr "数量"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode_kanban
msgid "Quantity Done"
msgstr "完了済数量"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Quantity in stock"
msgstr "在庫数量"

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_move_line__product_packaging_uom_qty
msgid "Quantity of the Packaging in the UoM of the Stock Move Line."
msgstr "在庫移動明細の単位のパッケージング数量"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_quant
msgid "Quants"
msgstr "保管ロット"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__barcode_rfid_batch_time
msgid "RFID Timer"
msgstr "RFIDタイマー"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/count_screen_rfid.xml:0
msgid "Read Rate"
msgstr "読取速度"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/count_screen_rfid.xml:0
msgid "Read Time"
msgstr "読取時間"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.js:0
msgid "Remove it"
msgstr "それを除く"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.xml:0
msgid "Return Products"
msgstr "プロダクト返品"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/kanban/stock_barcode_kanban_renderer.xml:0
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "Scan a"
msgstr "スキャン"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "Scan a location"
msgstr "ロケーションをスキャン"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a lot number"
msgstr "ロット番号をスキャン"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a lot number or a packages then the destination location"
msgstr "ロット番号または梱包をスキャンし、次に移動先をスキャン"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a lot number then the destination location"
msgstr "ロット番号、次に移動先をスキャン"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a package"
msgstr "梱包をスキャン"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a package or put in pack"
msgstr "梱包をスキャンまたは梱包に入れる"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a package or the destination location"
msgstr "梱包または移動先ロケーションをスキャン"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a package, the destination location or another product"
msgstr "梱包または移動先ロケーションまたは他のプロダクトをスキャン"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "Scan a product"
msgstr "プロダクトをスキャン"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a product before scanning a tracking number"
msgstr "追跡番号をスキャンする前にプロダクトをスキャン"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a product from %s"
msgstr " %sからのプロダクトをスキャン"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "Scan a product in %s or scan another location"
msgstr "%s のプロダクトをスキャンするか、または、別のロケーションをスキャン"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a product or a package"
msgstr "プロダクトまたは梱包をスキャン"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a product or another package"
msgstr "プロダクトまたは他の梱包をスキャン"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a product or the destination location."
msgstr "プロダクトまたは移動先ロケーションをスキャン"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a product then the destination location"
msgstr "プロダクトをスキャン、次に移動先をスキャン"

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/models/stock_picking.py:0
msgid ""
"Scan a product, a lot/serial number or a package to filter the transfers."
msgstr "プロダクト、ロット/シリアル番号、または梱包をスキャンして転送をフィルタ"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a product, a package or the destination location"
msgstr "プロダクト、梱包または移動先ロケーションをスキャン"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a product, a package or the destination location."
msgstr "プロダクト、梱包または移動先ロケーションをスキャン"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a serial number"
msgstr "シリアル番号をスキャン"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a serial number or a package"
msgstr "シリアル番号または梱包をスキャン"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a serial number or a package then the destination location"
msgstr "シリアル番号または梱包をスキャンし、次に移動先ロケーションをスキャン"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a serial number then the destination location"
msgstr "シリアル番号をスキャン、次に移動先ロケーションをスキャン"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "Scan an"
msgstr "スキャン："

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan another serial number"
msgstr "他のシリアル番号をスキャン"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "Scan lot numbers for product %s to change their quantity"
msgstr "プロダクト%s用のロット番号をスキャンして数量を変更"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan more lot numbers"
msgstr "追加でロット番号をスキャン"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan more lot numbers or a package"
msgstr "追加でロット番号または梱包をスキャン"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "Scan more products in %s or scan another location"
msgstr "%s のプロダクトを追加スキャンするか、または、別のロケーションをスキャン"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan more products or a package"
msgstr "他の商品または梱包をスキャン"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan more products, or scan a new source location"
msgstr "追加でプロダクトをスキャンするか、新しい移動元ロケーションををスキャン"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "Scan or tap"
msgstr "スキャンまたはタップ"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "Scan serial numbers for product %s to change their quantity"
msgstr "プロダクト %s用のシリアル番号をスキャンして数量を変更"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan the destination location"
msgstr "移動先ロケーションをスキャン"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan the package %s"
msgstr "梱包%sをスキャン"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan the source location"
msgstr "移動元ロケーションをスキャン"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan the source location or a package"
msgstr "移動元ロケーションまたは梱包をスキャン"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid ""
"Scanned product %s is not reserved for this transfer. Are you sure you want "
"to add it?"
msgstr "スキャンされたプロダクト%sはこの転送に引当されていません。本当に追加しますか？"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid ""
"Scanned quantity uses %(unit)s as its Unit of Measure (UoM), but it is not "
"compatible with the line's UoM (%(lineUnit)s)."
msgstr "スキャンされた数量は%(unit)sを単位として使用していますが、この単位は明細の単位 (%(lineUnit)s)と互換性がありません。"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid ""
"Scanned quantity uses %(unit)s as its Unit of Measure (UoM), but it is not "
"compatible with the product's UoM (%(productUnit)s)."
msgstr ""
"スキャンされた数量は%(unit)sを単位として使用していますが、この単位はプロダクトの単位 (%(productUnit)s)と互換性がありません。"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scanning package"
msgstr "梱包のスキャン"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.xml:0
#: model:ir.model,name:stock_barcode.model_stock_scrap
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
msgid "Scrap"
msgstr "廃棄"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
msgid "Scrap Location"
msgstr "廃棄ロケーション"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Select a Product"
msgstr "プロダクトを選択してください"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Serial/Lot Number"
msgstr "シリアル/ロット番号"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "Set the maximum delay between each key in ms (100 ms by default.)"
msgstr "各キー間の最大遅延時間(分) (デフォルト100分)を設定します。"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__show_barcode_nomenclature
msgid "Show Barcode Nomenclature"
msgstr "バーコード命名法を表示"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__show_barcode_validation
msgid "Show Barcode Validation"
msgstr "バーコード検証を表示"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__group_barcode_show_quantity_count
#: model:res.groups,name:stock_barcode.group_barcode_show_quantity_count
msgid "Show Quantity to Count"
msgstr "カウントする数量を表示"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__show_reserved_sns
msgid "Show reserved lots/SN"
msgstr "引当ロット/シリアル番号を表示"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.xml:0
msgid "Sign"
msgstr "署名"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/apply_quant_dialog.xml:0
msgid "Some serials where not counted, set them as missing?"
msgstr "カウントされないシリアル番号がありました。不足として設定しますか?"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Source Location"
msgstr "移動元ロケーション"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Source Package"
msgstr "元梱包"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/backorder_dialog.xml:0
msgid "Stay on transfer"
msgstr "転送に留まる"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_move
msgid "Stock Move"
msgstr "在庫移動"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_request_count
msgid "Stock Request an Inventory Count"
msgstr "棚卸数在庫要求"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_package_type
msgid "Stock package type"
msgstr "在庫梱包タイプ"

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_picking_type__is_barcode_picking_type
msgid ""
"Technical field indicating if should be used in barcode app and used to "
"control visibility in the related UI."
msgstr "バーコードアプリで使用されるべきかどうかを示す技術的なフィールドで、関連するUIで可視性を制御するために使用されます。"

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_picking_type__show_barcode_validation
msgid ""
"Technical field used to compute whether the \"Final Validation\" group "
"should be displayed, solving combined groups/invisible complexity."
msgstr "\"最終検証\"グループを表示すべきかどうかを計算するために使用される技術的なフィールド。複合グループ／不可視の複雑性を解決します。"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "The inventory adjustment has been validated"
msgstr "在庫調整が検証されました"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "The product %s should not be picked in this operation."
msgstr "プロダクト%sはこのオペレーションではピッキングしてはいけません。"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "The scanned location doesn't belong to this operation's destination"
msgstr "スキャンされたロケーションは、このオペレーションの移動先に属していません。"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "The scanned location doesn't belong to this operation's location"
msgstr "スキャンされた場所は、このオペレーションのロケーションに属していません。"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
msgid "The scanned serial number %s is already used."
msgstr "スキャンしたシリアル番号%sはすでに使用されています。"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "The transfer has been cancelled"
msgstr "転送はキャンセルされました"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "The transfer has been validated"
msgstr "転送が検証されました"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "There is nothing to apply in this page."
msgstr "このページに適用するものはありません。"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "There is nothing to print in this page."
msgstr "このページで印刷するものはありません。"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "This package is already scanned."
msgstr "この梱包はすでにスキャンされています。"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "This picking is already done"
msgstr "このピッキングはすでに行われています"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "This picking is cancelled"
msgstr "このピッキングはキャンセルされました。"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
msgid "This product doesn't exist."
msgstr "このプロダクトは存在しません。"

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_res_config_settings__barcode_separator_regex
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid ""
"This regex is used in the Barcode application to separate individual "
"barcodes when an aggregate barcode (i.e. single barcode consisting of "
"multiple barcode encodings) is scanned."
msgstr ""
"この正規表現は、バーコードアプリケーションで、アグリゲートバーコード(つまり、複数のバーコードエンコーディングで構成される単一のバーコード)がスキャンされたときに、個々のバーコードを分離するために使用されます。"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
msgid "This serial number is already used."
msgstr "このシリアル番号はすでに使用済です。"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid ""
"Time before processing a batch. Used to group communication to the server "
"and improve global performance at the cost of reactivity."
msgstr "バッチ処理前の時間。サーバへの通信をグループ化し、反応性をよりも全体パフォーマンスを向上させるために使用します。"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/backorder_dialog.xml:0
msgid "To do"
msgstr "予定"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "To use packages, enable 'Packages' in the settings"
msgstr "梱包を使用するには設定で '梱包' を有効にして下さい。"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/count_screen_rfid.xml:0
msgid "Total Reads"
msgstr "読取合計"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_picking
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__picking_id
msgid "Transfer"
msgstr "転送"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__picking_name
msgid "Transfer Name"
msgstr "転送名"

#. module: stock_barcode
#: model_terms:ir.actions.act_window,help:stock_barcode.stock_picking_action_kanban
msgid "Transfers allow you to move products from one location to another."
msgstr "転送を使用すると、プロダクトをある場所から別の場所に移動できます。"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/count_screen_rfid.xml:0
msgid "Unique Tags"
msgstr "一意タグ"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode_kanban
msgid "Unit of Measure"
msgstr "単位"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_stock_quant_tree
msgid "UoM"
msgstr "測定単位"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/backorder_dialog.xml:0
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
msgid "Validate"
msgstr "検証"

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_scrap___barcode_scanned
msgid "Value of the last barcode scanned."
msgstr "最後にスキャンされたバーコードの値。"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/widgets/image_preview.xml:0
msgid "Viewer"
msgstr "ビューア"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_warehouse
msgid "Warehouse"
msgstr "倉庫"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid ""
"When scanning a location in inventory adjustments, you will be assigned to "
"count all products from this location"
msgstr "在庫調整でロケーションをスキャンすると、そのロケーションの全てのプロダクトをカウントするように割当てられます。"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "Wrong Unit of Measure"
msgstr "誤った単位"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/apply_quant_dialog.xml:0
msgid "Yes"
msgstr "はい"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid ""
"You are about to take the product %(productName)s from the location %(locationName)s but this product isn't reserved in this location.\n"
"Scan the current location to confirm that."
msgstr ""
"プロダクト%(productName)sをロケーション%(locationName)sから持ち出そうとしていますが、このプロダクトはこの場所に予約されていません。\n"
"確認するには現在のロケーションをスキャンして下さい。"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
msgid ""
"You are expected to scan one or more products or a package available at the "
"picking location"
msgstr "ピッキングロケーションにある1つ以上のプロダクトまたは梱包をスキャンして下さい。"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "You are supposed to scan %s or another source location"
msgstr "%sまたは他の移動元ロケーションをスキャンして下さい。"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "You can't apply a package type. First, scan product or select a line"
msgstr "梱包タイプを適用できません。最初にプロダクトまたは明細をスキャンして下さい。"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "You can't register scrap at this state of the operation"
msgstr "オペレーションのこの段階で廃棄を登録することはできません。"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid ""
"You have already scanned %s items of this package. Do you want to scan the "
"whole package?"
msgstr "この梱包の%sアイテムはすでにスキャン済です。梱包全体をスキャンしますか？"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_backorder_confirmation
msgid "You have processed less products than the initial demand:"
msgstr "最初の要求より少ないプロダクトを処理しました："

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "You must scan a package or put in pack"
msgstr "梱包をスキャンするか梱包に入れて下さい。"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "You must scan a product"
msgstr "プロダクトをスキャンして下さい。"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "barcodes"
msgstr "バーコード"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "demo data sheet"
msgstr "デモデータシート"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "for operations."
msgstr "操作用。"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "location"
msgstr "場所"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/kanban/stock_barcode_kanban_renderer.xml:0
msgid "lot"
msgstr "ロット"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "operation type"
msgstr "オペレーションタイプ"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/kanban/stock_barcode_kanban_renderer.xml:0
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "or a"
msgstr "または"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/kanban/stock_barcode_kanban_renderer.xml:0
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "package"
msgstr "梱包"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "picking"
msgstr "ピッキング"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/kanban/stock_barcode_kanban_renderer.xml:0
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "product"
msgstr "プロダクト"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_backorder_confirmation
msgid "products were not processed at all."
msgstr "のプロダクトは処理されていません。"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_backorder_confirmation
msgid "products were partially processed."
msgstr "プロダクトは部分的に処理されました。"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/kanban/stock_barcode_kanban_renderer.xml:0
msgid "to filter your records"
msgstr "レコードをフィルタ"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "to initiate a transfer"
msgstr "運送を開始します"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "to locate it"
msgstr "そのロケーションを確定します"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "to open it"
msgstr "それを開きます"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "to start it"
msgstr "それを開始します"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "to test or"
msgstr "テストまたは"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "tracking number"
msgstr "追跡番号"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/kanban/stock_barcode_kanban_renderer.xml:0
msgid "transfer"
msgstr "運送"
