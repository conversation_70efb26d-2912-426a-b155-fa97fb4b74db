# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_barcode
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 09:12+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Danish (https://app.transifex.com/odoo/teams/41243/da/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: da\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "%s can't be inventoried. Only storable products can be inventoried."
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
msgid "<i class=\"fa fa-fw fa-lg fa-barcode me-3\" title=\"Serial/Lot Number\"/>"
msgstr "<i class=\"fa fa-fw fa-lg fa-barcode me-3\" title=\"Serial/Lot Number\"/>"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
msgid "<i class=\"fa fa-fw fa-lg fa-cube me-3\" title=\"Quantity\"/>"
msgstr "<i class=\"fa fa-fw fa-lg fa-cube me-3\" title=\"Quantity\"/>"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
msgid ""
"<i class=\"fa fa-fw fa-lg fa-long-arrow-right me-3\" title=\"Scrap "
"Location\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
msgid "<i class=\"fa fa-fw fa-lg fa-tags me-3\" title=\"Product\"/>"
msgstr "<i class=\"fa fa-fw fa-lg fa-tags me-3\" title=\"Product\"/>"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode_kanban_2
msgid "<i class=\"fa fa-lg fa-archive\" title=\"Package\"/>"
msgstr "<i class=\"fa fa-lg fa-archive\" title=\"Package\"/>"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode_kanban_2
msgid "<i class=\"fa fa-lg fa-user-o\" title=\"Owner\"/>"
msgstr "<i class=\"fa fa-lg fa-user-o\" title=\"Owner\"/>"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "<i class=\"fa fa-print\"/> Print barcode commands and operation types"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "<i class=\"fa fa-print\"/> Print barcode demo sheet"
msgstr "<i class=\"fa fa-print\"/> Print stregkode eksempel ark"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "<i class=\"fa fa-print\"/> Print storage locations"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "<i class=\"fa fs-4 fa-tags me-1\" title=\"Product\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
msgid "<span>Replenish Quantities</span>"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
msgid ""
"A product tracked by serial numbers can't have multiple quantities for the "
"same serial number."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.js:0
msgid "Add Product"
msgstr "Tilføj produkt"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Add extra product?"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_put_in_pack__mandatory
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_scan_dest_location__mandatory
msgid "After each product"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_put_in_pack__optional
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_scan_dest_location__optional
msgid "After group of Products"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "All products need to be packed"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_barcode
msgid "Allocation"
msgstr "Tildeling"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__barcode_allow_extra_product
msgid "Allow extra products"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__barcode_validation_full
msgid "Allow full picking validation"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_picking_type__barcode_validation_full
msgid ""
"Allow to validate a picking even if nothing was scanned yet (and so, do an "
"immediate transfert)"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid ""
"Allow users to see the expected quantity of a product to count in a location"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_picking_type__show_reserved_sns
msgid ""
"Allows to display reserved lots/serial numbers. When non active, it is clear"
" for the picker that they can pick the lots/serials they want."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
msgid ""
"An unexisting package type was scanned. This part of the barcode can't be "
"processed."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.xml:0
#: code:addons/stock_barcode/static/src/components/manual_barcode.xml:0
msgid "Apply"
msgstr "Anvend"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_cancel_operation_view
msgid "Are you sure you want to cancel this operation?"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/backorder_dialog.xml:0
msgid "Backorder"
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_backorder_confirmation
msgid "Backorder Confirmation"
msgstr "Restordre bekræftelse"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
#: model:ir.actions.client,name:stock_barcode.stock_barcode_action_main_menu
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__product_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_scrap__product_barcode
#: model:ir.ui.menu,name:stock_barcode.stock_barcode_menu
msgid "Barcode"
msgstr "Stregkode"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_view_picking_type_form
msgid "Barcode App"
msgstr ""

#. module: stock_barcode
#: model:ir.actions.client,name:stock_barcode.stock_barcode_inventory_client_action
msgid "Barcode Inventory Client Action"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "Barcode Nomenclature"
msgstr "Stregkode plan"

#. module: stock_barcode
#: model:ir.actions.client,name:stock_barcode.stock_barcode_picking_client_action
msgid "Barcode Picking Client Action"
msgstr "Strejkode pluk klient handling"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_scrap___barcode_scanned
msgid "Barcode Scanned"
msgstr "Scannet stregkode"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "Barcode Scanner"
msgstr "Stregkode scanner"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
msgid "Barcode scan is ambiguous with several model: %s. Use the most likely."
msgstr ""

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/controllers/stock_barcode.py:0
msgid "Barcodes are not available."
msgstr ""

#. module: stock_barcode
#: model:product.template,name:stock_barcode.product_cable_management_box_2_product_template
msgid "Cable Management Box"
msgstr "Kabelføring boks"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_cancel_operation_view
msgid "Cancel"
msgstr "Annullér"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_barcode_cancel_operation
msgid "Cancel Operation"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Cancel Transfer"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_cancel_operation_view
msgid "Cancel operation"
msgstr ""

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/models/stock_picking.py:0
msgid "Cancel this operation?"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/count_screen_rfid.xml:0
#: code:addons/stock_barcode/static/src/components/main.xml:0
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
#: code:addons/stock_barcode/static/src/widgets/image_preview.xml:0
msgid "Close"
msgstr "Luk"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurer opsætning"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "Configure Product Barcodes"
msgstr "Konfigurér produktstregkoder"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Confirm"
msgstr "Bekræft"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__group_barcode_count_entire_location
#: model:res.groups,name:stock_barcode.group_barcode_count_entire_location
msgid "Count Entire Locations"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Counted Quantity"
msgstr "Talt kvantitet"

#. module: stock_barcode
#: model_terms:ir.actions.act_window,help:stock_barcode.stock_picking_action_kanban
msgid "Create a new transfer"
msgstr "Opret en ny overførsel"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__create_uid
msgid "Created by"
msgstr "Oprettet af"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__create_date
msgid "Created on"
msgstr "Oprettet den"

#. module: stock_barcode
#: model:product.template,name:stock_barcode.product_custom_cabinet_metric_product_template
msgid "Customized Cabinet (Metric)"
msgstr ""

#. module: stock_barcode
#: model:product.template,name:stock_barcode.product_custom_cabinet_usa_product_template
msgid "Customized Cabinet (USA)"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.xml:0
msgid "Delete"
msgstr "Slet"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__stock_barcode_demo_active
msgid "Demo Data Active"
msgstr "Demo Data Aktiv"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/line.xml:0
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Destination Location"
msgstr "Destinations lokation"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Destination Package"
msgstr "Destinations pakke"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Destination location must be scanned"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "Disable sound effect while scanning a barcode."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/apply_quant_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Discard"
msgstr "Kassér"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__display_name
msgid "Display Name"
msgstr "Vis navn"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.js:0
msgid ""
"Do you want to permanently remove this message ? It won't appear anymore, so"
" make sure you don't need the barcodes sheet or you have a copy."
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_picking_type__restrict_put_in_pack
msgid ""
"Does the picker have to put in a package the scanned products? If yes, at "
"which rate?"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_picking_type__restrict_scan_dest_location
msgid "Does the picker have to scan the destination? If yes, at which rate?"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_cancel_operation_view
msgid "Don't cancel"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.js:0
msgid "Don't show this message again"
msgstr "Vis ikke denne besked igen"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/backorder_dialog.xml:0
msgid "Done /"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "Download demo data sheet"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "Download operation barcodes"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__dummy_id
#: model:ir.model.fields,field_description:stock_barcode.field_stock_quant__dummy_id
msgid "Dummy"
msgstr "Attrap"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_backorder_confirmation__empty_move_count
msgid "Empty Move Count"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/manual_barcode.xml:0
msgid "Enter a barcode..."
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_view_picking_type_form
msgid "Final Validation"
msgstr "Endelig bekræftelse"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Following backorder was created:"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Following backorders were created:"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_picking_type__barcode_allow_extra_product
msgid "For planned transfers, allow to add non-reserved products"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__restrict_scan_dest_location
msgid "Force Destination Location scan?"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__restrict_scan_tracking_number
msgid "Force Lot/Serial scan?"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__restrict_scan_product
msgid "Force Product scan?"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__restrict_scan_source_location
msgid "Force Source Location scan?"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__barcode_validation_after_dest_location
msgid "Force a destination on all products"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__barcode_validation_all_product_packed
msgid "Force all products to be packed"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__restrict_put_in_pack
msgid "Force put in pack?"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__formatted_product_barcode
msgid "Formatted Product Barcode"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_product_product__has_image
msgid "Has Image"
msgstr "Har billede"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__hide_lot
msgid "Hide Lot"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__hide_lot_name
msgid "Hide Lot Name"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__id
msgid "ID"
msgstr "ID"

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/models/stock_picking_type.py:0
msgid ""
"If the source location must be scanned, then the destination location must "
"either be scanned after each product or not scanned at all."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/backorder_dialog.xml:0
msgid ""
"If you validate now, the remaining products will be added to a backorder."
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__image_1920
#: model:ir.model.fields,field_description:stock_barcode.field_stock_quant__image_1920
msgid "Image"
msgstr "Billede"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/backorder_dialog.xml:0
msgid "Incomplete Transfer"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "Install"
msgstr "Installer"

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_move_line__product_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_scrap__product_barcode
msgid "International Article Number used for product identification."
msgstr "Internationalt varenummer til brug ved vareidentifikation"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "Inventory Adjustment"
msgstr "Lagerjustering"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_location
msgid "Inventory Locations"
msgstr "Lager lokationer"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "Inventory count"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__is_barcode_picking_type
msgid "Is Barcode Picking Type"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__write_uid
msgid "Last Updated by"
msgstr "Sidst opdateret af"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__write_date
msgid "Last Updated on"
msgstr "Sidst opdateret den"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.js:0
msgid "Leave it"
msgstr "Efterlad den"

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_picking_type__restrict_scan_product
msgid "Line's product must be scanned before the line can be edited"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Location"
msgstr "Adresse"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__location_processed
msgid "Location Processed"
msgstr "Lokation behandlet"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "Log out"
msgstr "Log af"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_lot
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_view_picking_type_form
msgid "Lot/Serial"
msgstr "Lot/serienummer"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Mandatory Destination Location"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_scan_source_location__mandatory
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_scan_tracking_number__mandatory
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_view_picking_type_form
msgid "Mandatory Scan"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Mandatory Source Location"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__barcode_max_time_between_keys_in_ms
msgid "Max time between each key"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_res_config_settings__barcode_max_time_between_keys_in_ms
msgid "Maximum delay between each key in ms (100 ms by default)"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__barcode_separator_regex
msgid "Multiscan Separator"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__stock_barcode_mute_sound_notifications
msgid "Mute Barcode application sounds"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_put_in_pack__no
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_scan_dest_location__no
msgid "No"
msgstr "Nej"

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/models/stock_picking.py:0
msgid "No %(picking_type)s ready for this %(barcode_type)s"
msgstr ""

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/models/stock_move_line.py:0
msgid "No Barcode"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_scan_source_location__no
msgid "No Scan"
msgstr ""

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/controllers/stock_barcode.py:0
msgid ""
"No internal operation type. Please configure one in warehouse settings."
msgstr ""
"Ingen interne operations typer. Vær venlig at konfigurere en i varehus "
"indstillinger."

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/controllers/stock_barcode.py:0
msgid "No picking or location or product corresponding to barcode %(barcode)s"
msgstr ""

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/controllers/stock_barcode.py:0
msgid "No picking or product corresponding to barcode %(barcode)s"
msgstr ""

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/models/stock_picking.py:0
msgid "No product, lot or package found for barcode %s"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/apply_quant_dialog.xml:0
msgid "No, I'll count them later"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__barcode_nomenclature_id
msgid "Nomenclature"
msgstr "Nomenklature"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Not the expected scan"
msgstr ""

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/models/stock_picking.py:0
#: model:ir.actions.act_window,name:stock_barcode.open_picking
msgid "Open picking form"
msgstr "Åbn pluk fra"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.xml:0
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
#: model:ir.actions.act_window,name:stock_barcode.stock_picking_action_kanban
#: model:ir.actions.act_window,name:stock_barcode.stock_picking_type_action_kanban
msgid "Operations"
msgstr "Operationer"

#. module: stock_barcode
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_scan_tracking_number__optional
msgid "Optional Scan"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_view_picking_type_form
msgid "Options"
msgstr "Valgmuligheder"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Owner"
msgstr "Ejer"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Package"
msgstr "Pakke"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "Package type %(type)s applied to the package %(package)s"
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_quant_package
msgid "Packages"
msgstr "Pakker"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__product_packaging_id
msgid "Packaging"
msgstr "Emballering"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__product_packaging_uom_qty
msgid "Packaging Quantity"
msgstr "Pakke Antal"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__parent_location_id
msgid "Parent Location"
msgstr "Hoved lokation"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__parent_location_dest_id
msgid "Parent Location Dest"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_backorder_confirmation__partial_move_count
msgid "Partial Move Count"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_barcode
msgid "Picking Details"
msgstr "Pluk detaljer"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_picking_type
msgid "Picking Type"
msgstr "Pluk type"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Please scan destination location for %s before scanning other product"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.js:0
msgid "Please, Scan again!"
msgstr "Venligst, scan igen!"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Press Validate or scan another product"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.xml:0
msgid "Print"
msgstr "Udskriv"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Print Barcodes"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Print Delivery Slip"
msgstr "Udprint leverings seddel"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "Print Inventory"
msgstr "Udprint inventar"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Print Packages"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Print Picking Operations"
msgstr "Udprint pluk"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "Print the"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.js:0
msgid "Processing %(processed)s/%(toProcess)s barcodes"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/backorder_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Product"
msgstr "Produkt"

#. module: stock_barcode
#: model:ir.actions.act_window,name:stock_barcode.product_action_barcodes
#: model_terms:ir.ui.view,arch_db:stock_barcode.product_view_list_barcodes
msgid "Product Barcodes"
msgstr "Produkt stregkoder"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "Produkt bevægelser (Lagerbevægelse linje)"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_product_packaging
msgid "Product Packaging"
msgstr "Produkt indpakning"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__product_reference_code
#: model:ir.model.fields,field_description:stock_barcode.field_stock_quant__product_reference_code
msgid "Product Reference Code"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__product_stock_quant_ids
msgid "Product Stock Quant"
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_uom_uom
msgid "Product Unit of Measure"
msgstr "Vareenhed"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_product_product
msgid "Product Variant"
msgstr "Varevariant"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.xml:0
msgid "Put In Pack"
msgstr "Læg i pakke"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_view_picking_type_form
msgid "Put in Pack"
msgstr "Put i pakke"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__qty_done
msgid "Qty Done"
msgstr "Kvantitet fuldført"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Quantity"
msgstr "Antal"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode_kanban
msgid "Quantity Done"
msgstr "Kvantitet færdig"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Quantity in stock"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_move_line__product_packaging_uom_qty
msgid "Quantity of the Packaging in the UoM of the Stock Move Line."
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_quant
msgid "Quants"
msgstr "Kvantum"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__barcode_rfid_batch_time
msgid "RFID Timer"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/count_screen_rfid.xml:0
msgid "Read Rate"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/count_screen_rfid.xml:0
msgid "Read Time"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.js:0
msgid "Remove it"
msgstr "Fjern den"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.xml:0
msgid "Return Products"
msgstr "Returner produkter"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/kanban/stock_barcode_kanban_renderer.xml:0
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "Scan a"
msgstr "Scan en"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "Scan a location"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a lot number"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a lot number or a packages then the destination location"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a lot number then the destination location"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a package"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a package or put in pack"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a package or the destination location"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a package, the destination location or another product"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "Scan a product"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a product before scanning a tracking number"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a product from %s"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "Scan a product in %s or scan another location"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a product or a package"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a product or another package"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a product or the destination location."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a product then the destination location"
msgstr ""

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/models/stock_picking.py:0
msgid ""
"Scan a product, a lot/serial number or a package to filter the transfers."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a product, a package or the destination location"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a product, a package or the destination location."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a serial number"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a serial number or a package"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a serial number or a package then the destination location"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a serial number then the destination location"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "Scan an"
msgstr "Scan en"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan another serial number"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "Scan lot numbers for product %s to change their quantity"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan more lot numbers"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan more lot numbers or a package"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "Scan more products in %s or scan another location"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan more products or a package"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan more products, or scan a new source location"
msgstr "Scan flere produkter, eller scan en ny kilde lokation"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "Scan or tap"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "Scan serial numbers for product %s to change their quantity"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan the destination location"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan the package %s"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan the source location"
msgstr "Scan kilde lokationen"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan the source location or a package"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid ""
"Scanned product %s is not reserved for this transfer. Are you sure you want "
"to add it?"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid ""
"Scanned quantity uses %(unit)s as its Unit of Measure (UoM), but it is not "
"compatible with the line's UoM (%(lineUnit)s)."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid ""
"Scanned quantity uses %(unit)s as its Unit of Measure (UoM), but it is not "
"compatible with the product's UoM (%(productUnit)s)."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scanning package"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.xml:0
#: model:ir.model,name:stock_barcode.model_stock_scrap
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
msgid "Scrap"
msgstr "Skrot"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
msgid "Scrap Location"
msgstr "Kassationslokation"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Select a Product"
msgstr "Vælg et produkt"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Serial/Lot Number"
msgstr "Serie/lot nummer"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "Set the maximum delay between each key in ms (100 ms by default.)"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__show_barcode_nomenclature
msgid "Show Barcode Nomenclature"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__show_barcode_validation
msgid "Show Barcode Validation"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__group_barcode_show_quantity_count
#: model:res.groups,name:stock_barcode.group_barcode_show_quantity_count
msgid "Show Quantity to Count"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__show_reserved_sns
msgid "Show reserved lots/SN"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.xml:0
msgid "Sign"
msgstr "Underskriv"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/apply_quant_dialog.xml:0
msgid "Some serials where not counted, set them as missing?"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Source Location"
msgstr "Kilde placering"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Source Package"
msgstr "Kildeforpakning"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/backorder_dialog.xml:0
msgid "Stay on transfer"
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_move
msgid "Stock Move"
msgstr "Lagerflytning"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_request_count
msgid "Stock Request an Inventory Count"
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_package_type
msgid "Stock package type"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_picking_type__is_barcode_picking_type
msgid ""
"Technical field indicating if should be used in barcode app and used to "
"control visibility in the related UI."
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_picking_type__show_barcode_validation
msgid ""
"Technical field used to compute whether the \"Final Validation\" group "
"should be displayed, solving combined groups/invisible complexity."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "The inventory adjustment has been validated"
msgstr "Inventar justeringen er blevet godkendt"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "The product %s should not be picked in this operation."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "The scanned location doesn't belong to this operation's destination"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "The scanned location doesn't belong to this operation's location"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
msgid "The scanned serial number %s is already used."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "The transfer has been cancelled"
msgstr "Overførslen er blevet annulleret"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "The transfer has been validated"
msgstr "Overførslen er blevet godkendt"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "There is nothing to apply in this page."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "There is nothing to print in this page."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "This package is already scanned."
msgstr "Denne pakning er allerede scannet."

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "This picking is already done"
msgstr "Dette pluk er allerede fuldført"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "This picking is cancelled"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
msgid "This product doesn't exist."
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_res_config_settings__barcode_separator_regex
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid ""
"This regex is used in the Barcode application to separate individual "
"barcodes when an aggregate barcode (i.e. single barcode consisting of "
"multiple barcode encodings) is scanned."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
msgid "This serial number is already used."
msgstr "Dette serienummer bruges allerede."

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid ""
"Time before processing a batch. Used to group communication to the server "
"and improve global performance at the cost of reactivity."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/backorder_dialog.xml:0
msgid "To do"
msgstr "To do"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "To use packages, enable 'Packages' in the settings"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/count_screen_rfid.xml:0
msgid "Total Reads"
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_picking
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__picking_id
msgid "Transfer"
msgstr "Overfør"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__picking_name
msgid "Transfer Name"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.actions.act_window,help:stock_barcode.stock_picking_action_kanban
msgid "Transfers allow you to move products from one location to another."
msgstr ""
"Overførsler gør det muligt for dig at flytte produkter fra en lokation til "
"en anden."

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/count_screen_rfid.xml:0
msgid "Unique Tags"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode_kanban
msgid "Unit of Measure"
msgstr "Enhed"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_stock_quant_tree
msgid "UoM"
msgstr "Enhed"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/backorder_dialog.xml:0
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
msgid "Validate"
msgstr "Validér"

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_scrap___barcode_scanned
msgid "Value of the last barcode scanned."
msgstr "Værdi af den sidst scannede stregkode."

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/widgets/image_preview.xml:0
msgid "Viewer"
msgstr "Viser"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_warehouse
msgid "Warehouse"
msgstr "Lagerstyring"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid ""
"When scanning a location in inventory adjustments, you will be assigned to "
"count all products from this location"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "Wrong Unit of Measure"
msgstr "Forkert måleenhed"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/apply_quant_dialog.xml:0
msgid "Yes"
msgstr "Ja"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid ""
"You are about to take the product %(productName)s from the location %(locationName)s but this product isn't reserved in this location.\n"
"Scan the current location to confirm that."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
msgid ""
"You are expected to scan one or more products or a package available at the "
"picking location"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "You are supposed to scan %s or another source location"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "You can't apply a package type. First, scan product or select a line"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "You can't register scrap at this state of the operation"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid ""
"You have already scanned %s items of this package. Do you want to scan the "
"whole package?"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_backorder_confirmation
msgid "You have processed less products than the initial demand:"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "You must scan a package or put in pack"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "You must scan a product"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "barcodes"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "demo data sheet"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "for operations."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "location"
msgstr "lokation"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/kanban/stock_barcode_kanban_renderer.xml:0
msgid "lot"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "operation type"
msgstr "operationstype"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/kanban/stock_barcode_kanban_renderer.xml:0
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "or a"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/kanban/stock_barcode_kanban_renderer.xml:0
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "package"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "picking"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/kanban/stock_barcode_kanban_renderer.xml:0
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "product"
msgstr "produkt"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_backorder_confirmation
msgid "products were not processed at all."
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_backorder_confirmation
msgid "products were partially processed."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/kanban/stock_barcode_kanban_renderer.xml:0
msgid "to filter your records"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "to initiate a transfer"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "to locate it"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "to open it"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "to start it"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "to test or"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "tracking number"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/kanban/stock_barcode_kanban_renderer.xml:0
msgid "transfer"
msgstr ""
