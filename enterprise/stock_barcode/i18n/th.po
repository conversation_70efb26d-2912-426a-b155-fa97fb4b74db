# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_barcode
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON><PERSON><PERSON>pp<PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 09:12+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Rasareeyar Lappiam, 2024\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "%s can't be inventoried. Only storable products can be inventoried."
msgstr ""
"%s ไม่สามารถจัดเก็บในสินค้าคงคลังได้ "
"สามารถจัดเก็บได้เฉพาะสินค้าที่จัดเก็บได้เท่านั้น"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
msgid "<i class=\"fa fa-fw fa-lg fa-barcode me-3\" title=\"Serial/Lot Number\"/>"
msgstr "<i class=\"fa fa-fw fa-lg fa-barcode me-3\" title=\"หมายเลขซีเรียล/ล็อต\"/>"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
msgid "<i class=\"fa fa-fw fa-lg fa-cube me-3\" title=\"Quantity\"/>"
msgstr "<i class=\"fa fa-fw fa-lg fa-cube me-3\" title=\"จำนวน\"/>"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
msgid ""
"<i class=\"fa fa-fw fa-lg fa-long-arrow-right me-3\" title=\"Scrap "
"Location\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-lg fa-long-arrow-right me-3\" "
"title=\"ตำแหน่งเศษเหล็ก\"/>"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
msgid "<i class=\"fa fa-fw fa-lg fa-tags me-3\" title=\"Product\"/>"
msgstr "<i class=\"fa fa-fw fa-lg fa-tags me-3\" title=\"สินค้า\"/>"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode_kanban_2
msgid "<i class=\"fa fa-lg fa-archive\" title=\"Package\"/>"
msgstr "<i class=\"fa fa-lg fa-archive\" title=\"บรรจุภัณฑ์\"/>"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode_kanban_2
msgid "<i class=\"fa fa-lg fa-user-o\" title=\"Owner\"/>"
msgstr "<i class=\"fa fa-lg fa-user-o\" title=\"เจ้าของ\"/>"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "<i class=\"fa fa-print\"/> Print barcode commands and operation types"
msgstr "<i class=\"fa fa-print\"/> พิมพ์คำสั่งบาร์โค้ดและประเภทการดำเนินการ"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "<i class=\"fa fa-print\"/> Print barcode demo sheet"
msgstr "<i class=\"fa fa-print\"/> พิมพ์บาร์โค้ดใบการสาธิต"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "<i class=\"fa fa-print\"/> Print storage locations"
msgstr "<i class=\"fa fa-print\"/> พิมพ์สถานที่จัดเก็บ"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "<i class=\"fa fs-4 fa-tags me-1\" title=\"Product\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
msgid "<span>Replenish Quantities</span>"
msgstr "<span>เติมปริมาณ</span>"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
msgid ""
"A product tracked by serial numbers can't have multiple quantities for the "
"same serial number."
msgstr ""
"สินค้าที่ติดตามด้วยหมายเลขซีเรียลไม่สามารถมีหมายเลขซีเรียลเดียวกันหลายจำนวนได้"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.js:0
msgid "Add Product"
msgstr "เพิ่มสินค้า"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Add extra product?"
msgstr "เพิ่มสินค้า?"

#. module: stock_barcode
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_put_in_pack__mandatory
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_scan_dest_location__mandatory
msgid "After each product"
msgstr "หลังสินค้าแต่ละชิ้น"

#. module: stock_barcode
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_put_in_pack__optional
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_scan_dest_location__optional
msgid "After group of Products"
msgstr "หลังกลุ่มสินค้า"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "All products need to be packed"
msgstr "สินค้าทั้งหมดจะต้องได้รับการบรรจุ"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_barcode
msgid "Allocation"
msgstr "การจัดสรร"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__barcode_allow_extra_product
msgid "Allow extra products"
msgstr "อนุญาตให้มีผลิตภัณฑ์พิเศษ"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__barcode_validation_full
msgid "Allow full picking validation"
msgstr "อนุญาตให้มีการตรวจสอบความถูกต้องของการเบิกสินค้าแบบสมบูรณ์"

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_picking_type__barcode_validation_full
msgid ""
"Allow to validate a picking even if nothing was scanned yet (and so, do an "
"immediate transfert)"
msgstr ""
"อนุญาตให้ตรวจสอบความถูกต้องของการเบิกสินค้า แม้ว่าจะยังไม่ได้สแกน "
"(และทำการโอนย้ายทันที)"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid ""
"Allow users to see the expected quantity of a product to count in a location"
msgstr "อนุญาตให้ผู้ใช้ดูปริมาณที่คาดหวังของผลิตภัณฑ์ที่จะนับในสถานที่ตั้ง"

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_picking_type__show_reserved_sns
msgid ""
"Allows to display reserved lots/serial numbers. When non active, it is clear"
" for the picker that they can pick the lots/serials they want."
msgstr ""
"ช่วยให้สามารถแสดงล็อตที่จองไว้/หมายเลขซีเรียลได้ เมื่อไม่ได้ใช้งานอยู่ "
"ผู้เลือกจะสามารถเลือกล็อต/ซีเรียลที่ต้องการได้อย่างชัดเจน"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
msgid ""
"An unexisting package type was scanned. This part of the barcode can't be "
"processed."
msgstr ""
"สแกนประเภทบรรรจุภัณฑ์ที่ไม่มีอยู่แล้ว "
"ซึ่งไม่สามารถประมวลผลบาร์โค้ดส่วนนี้ได้"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.xml:0
#: code:addons/stock_barcode/static/src/components/manual_barcode.xml:0
msgid "Apply"
msgstr "นำไปใช้"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_cancel_operation_view
msgid "Are you sure you want to cancel this operation?"
msgstr "คุณแน่ใจหรือไม่ว่าต้องการยกเลิกการดำเนินการนี้"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/backorder_dialog.xml:0
msgid "Backorder"
msgstr "รายการสั่งซื้อล่วงหน้า"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_backorder_confirmation
msgid "Backorder Confirmation"
msgstr "ยืนยันสั่งซื้อล่วงหน้า"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
#: model:ir.actions.client,name:stock_barcode.stock_barcode_action_main_menu
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__product_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_scrap__product_barcode
#: model:ir.ui.menu,name:stock_barcode.stock_barcode_menu
msgid "Barcode"
msgstr "บาร์โค้ด"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_view_picking_type_form
msgid "Barcode App"
msgstr "แอปบาร์โค้ด"

#. module: stock_barcode
#: model:ir.actions.client,name:stock_barcode.stock_barcode_inventory_client_action
msgid "Barcode Inventory Client Action"
msgstr "การดำเนินการของลูกค้าในบาร์โค้ดสินค้าคงคลัง"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "Barcode Nomenclature"
msgstr "การตีความบาร์โค้ด"

#. module: stock_barcode
#: model:ir.actions.client,name:stock_barcode.stock_barcode_picking_client_action
msgid "Barcode Picking Client Action"
msgstr "การดำเนินการของลูกค้าในการเลือกบาร์โค้ด"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_scrap___barcode_scanned
msgid "Barcode Scanned"
msgstr "สแกนบาร์โค้ดแล้ว"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "Barcode Scanner"
msgstr "เครื่องสแกนบาร์โค้ด"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
msgid "Barcode scan is ambiguous with several model: %s. Use the most likely."
msgstr ""
"การสแกนบาร์โค้ดมีความคลุมเครือในหลายรุ่น: %s "
"ใช้อันที่มีความเป็นไปได้มากที่สุด"

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/controllers/stock_barcode.py:0
msgid "Barcodes are not available."
msgstr "บาร์โค้ดไม่พร้อมใช้งาน"

#. module: stock_barcode
#: model:product.template,name:stock_barcode.product_cable_management_box_2_product_template
msgid "Cable Management Box"
msgstr "กล่องการจัดการสายเคเบิล"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_cancel_operation_view
msgid "Cancel"
msgstr "ยกเลิก"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_barcode_cancel_operation
msgid "Cancel Operation"
msgstr "ยกเลิกการดำเนินการ"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Cancel Transfer"
msgstr "ยกเลิกการโอนย้าย"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_cancel_operation_view
msgid "Cancel operation"
msgstr "ยกเลิกการดำเนินการ"

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/models/stock_picking.py:0
msgid "Cancel this operation?"
msgstr "ยกเลิกการดำเนินการนี้หรือไม่?"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/count_screen_rfid.xml:0
#: code:addons/stock_barcode/static/src/components/main.xml:0
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
#: code:addons/stock_barcode/static/src/widgets/image_preview.xml:0
msgid "Close"
msgstr "ปิด"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_res_config_settings
msgid "Config Settings"
msgstr "ตั้งค่าการกำหนดค่า"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "Configure Product Barcodes"
msgstr "กำหนดค่าบาร์โค้ดของสินค้า"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Confirm"
msgstr "ยืนยัน"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_res_partner
msgid "Contact"
msgstr "ติดต่อ"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__group_barcode_count_entire_location
#: model:res.groups,name:stock_barcode.group_barcode_count_entire_location
msgid "Count Entire Locations"
msgstr "นับสถานที่ทั้งหมด"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Counted Quantity"
msgstr "จำนวนที่นับแล้ว"

#. module: stock_barcode
#: model_terms:ir.actions.act_window,help:stock_barcode.stock_picking_action_kanban
msgid "Create a new transfer"
msgstr "สร้างการจัดส่งใหม่"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: stock_barcode
#: model:product.template,name:stock_barcode.product_custom_cabinet_metric_product_template
msgid "Customized Cabinet (Metric)"
msgstr "ตู้สั่งทำพิเศษ (เมตริก)"

#. module: stock_barcode
#: model:product.template,name:stock_barcode.product_custom_cabinet_usa_product_template
msgid "Customized Cabinet (USA)"
msgstr "ตู้สั่งทำพิเศษ (สหรัฐอเมริกา)"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.xml:0
msgid "Delete"
msgstr "ลบ"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__stock_barcode_demo_active
msgid "Demo Data Active"
msgstr "ข้อมูลสาธิตที่ใช้งานอยู่"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/line.xml:0
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Destination Location"
msgstr "ตำแหน่งปลายทาง"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Destination Package"
msgstr "แพ็คเกจปลายทาง"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Destination location must be scanned"
msgstr "ต้องสแกนตำแหน่งปลายทาง"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "Disable sound effect while scanning a barcode."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/apply_quant_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Discard"
msgstr "ละทิ้ง"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__display_name
msgid "Display Name"
msgstr "แสดงชื่อ"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.js:0
msgid ""
"Do you want to permanently remove this message ? It won't appear anymore, so"
" make sure you don't need the barcodes sheet or you have a copy."
msgstr ""
"คุณต้องการลบข้อความนี้อย่างถาวรหรือไม่ มันจะไม่แสดงอีกต่อไป "
"ดังนั้นตรวจสอบให้แน่ใจว่าคุณไม่จำเป็นต้องใช้แผ่นบาร์โค้ดหรือคุณมีสำเนาแล้ว"

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_picking_type__restrict_put_in_pack
msgid ""
"Does the picker have to put in a package the scanned products? If yes, at "
"which rate?"
msgstr ""
"ผู้เบิกต้องใส่สินค้าที่สแกนลงในบรรจุภัณฑ์หรือไม่? ถ้าใช่ ต้องการใช้อัตราไหน?"

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_picking_type__restrict_scan_dest_location
msgid "Does the picker have to scan the destination? If yes, at which rate?"
msgstr "ผู้เบิกต้องสแกนปลายทางหรือไม่? ถ้าใช่ ต้องการใช้อัตราไหน?"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_cancel_operation_view
msgid "Don't cancel"
msgstr "ไม่ยกเลิก"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.js:0
msgid "Don't show this message again"
msgstr "ไม่ต้องแสดงข้อความนี้อีก"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/backorder_dialog.xml:0
msgid "Done /"
msgstr "เสร็จสิ้น /"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "Download demo data sheet"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "Download operation barcodes"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__dummy_id
#: model:ir.model.fields,field_description:stock_barcode.field_stock_quant__dummy_id
msgid "Dummy"
msgstr "ตัวจำลอง"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_backorder_confirmation__empty_move_count
msgid "Empty Move Count"
msgstr "จำนวนการย้ายที่ว่างเปล่า"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/manual_barcode.xml:0
msgid "Enter a barcode..."
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_view_picking_type_form
msgid "Final Validation"
msgstr "การตรวจสอบความถูกต้องครั้งสุดท้าย"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Following backorder was created:"
msgstr "มีการสร้างการสั่งซื้อล่วงหน้าต่อไปนี้แล้ว:"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Following backorders were created:"
msgstr "มีการสร้างการสั่งซื้อล่วงหน้าต่อไปนี้แล้ว:"

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_picking_type__barcode_allow_extra_product
msgid "For planned transfers, allow to add non-reserved products"
msgstr "สำหรับการโอนตามแผน อนุญาตให้เพิ่มผลิตภัณฑ์ที่ไม่ได้จองไว้"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__restrict_scan_dest_location
msgid "Force Destination Location scan?"
msgstr "บังคับให้สแกนจากตำแหน่งปลายทางหรือไม่"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__restrict_scan_tracking_number
msgid "Force Lot/Serial scan?"
msgstr "บังคับให้สแกนล็อต/ซีเรียล?"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__restrict_scan_product
msgid "Force Product scan?"
msgstr "บังคับสแกนสินค้า?"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__restrict_scan_source_location
msgid "Force Source Location scan?"
msgstr "บังคับการสแกนจากตำแหน่งต้นทางหรือไม่"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__barcode_validation_after_dest_location
msgid "Force a destination on all products"
msgstr "บังคับปลายทางกับสินค้าทั้งหมด"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__barcode_validation_all_product_packed
msgid "Force all products to be packed"
msgstr "บังคับให้สินค้าทั้งหมดถูกบรรจุ"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__restrict_put_in_pack
msgid "Force put in pack?"
msgstr "บังคับให้ใส่ในแพ็ค?"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__formatted_product_barcode
msgid "Formatted Product Barcode"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_product_product__has_image
msgid "Has Image"
msgstr "มีรูปภาพ"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__hide_lot
msgid "Hide Lot"
msgstr "ซ่อนล็อต"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__hide_lot_name
msgid "Hide Lot Name"
msgstr "ซ่อนชื่อล็อต"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__id
msgid "ID"
msgstr "ไอดี"

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/models/stock_picking_type.py:0
msgid ""
"If the source location must be scanned, then the destination location must "
"either be scanned after each product or not scanned at all."
msgstr ""
"หากจำเป็นต้องสแกนตำแหน่งต้นทาง "
"สินค้าในตำแหน่งปลายทางจะต้องถูกสแกนหรือไม่ถูกสแกนเลย"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/backorder_dialog.xml:0
msgid ""
"If you validate now, the remaining products will be added to a backorder."
msgstr "หากคุณตรวจสอบตอนนี้ สินค้าที่เหลือจะถูกเพิ่มไปยังคำสั่งซื้อล่วงหน้า"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__image_1920
#: model:ir.model.fields,field_description:stock_barcode.field_stock_quant__image_1920
msgid "Image"
msgstr "รูปภาพ"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/backorder_dialog.xml:0
msgid "Incomplete Transfer"
msgstr "การโอนไม่สมบูรณ์"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "Install"
msgstr "ติดตั้ง"

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_move_line__product_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_scrap__product_barcode
msgid "International Article Number used for product identification."
msgstr "หมายเลขบทความสากลที่ใช้เพื่อระบุสินค้า"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "Inventory Adjustment"
msgstr "การปรับปรุงสินค้าคงคลัง"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_location
msgid "Inventory Locations"
msgstr "ตำแหน่งคลังสินค้า"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "Inventory count"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__is_barcode_picking_type
msgid "Is Barcode Picking Type"
msgstr "เป็นประเภทเบิกบาร์โค้ด"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__write_uid
msgid "Last Updated by"
msgstr "อัปเดตครั้งล่าสุดโดย"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__write_date
msgid "Last Updated on"
msgstr "อัปเดตครั้งล่าสุดเมื่อ"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.js:0
msgid "Leave it"
msgstr "ทิ้งไว้"

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_picking_type__restrict_scan_product
msgid "Line's product must be scanned before the line can be edited"
msgstr "ต้องสแกนสินค้าของรายการก่อนจึงจะแก้ไขรายการได้"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Location"
msgstr "สถานที่"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__location_processed
msgid "Location Processed"
msgstr "ประมวลผลตำแหน่งแล้ว"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "Log out"
msgstr "ออกระบบ"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_lot
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_view_picking_type_form
msgid "Lot/Serial"
msgstr "ล็อต/ซีเรียล"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Mandatory Destination Location"
msgstr "ตำแหน่งปลายทางที่จำเป็น"

#. module: stock_barcode
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_scan_source_location__mandatory
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_scan_tracking_number__mandatory
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_view_picking_type_form
msgid "Mandatory Scan"
msgstr "การสแกนที่จำเป็น"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Mandatory Source Location"
msgstr "ตำแหน่งต้นทางบังคับ"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__barcode_max_time_between_keys_in_ms
msgid "Max time between each key"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_res_config_settings__barcode_max_time_between_keys_in_ms
msgid "Maximum delay between each key in ms (100 ms by default)"
msgstr ""
"ความล่าช้าสูงสุดระหว่างแต่ละคีย์เป็นมิลลิวินาที (ค่าเริ่มต้นคือ 100 "
"มิลลิวินาที)"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__barcode_separator_regex
msgid "Multiscan Separator"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__stock_barcode_mute_sound_notifications
msgid "Mute Barcode application sounds"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_put_in_pack__no
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_scan_dest_location__no
msgid "No"
msgstr "ไม่"

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/models/stock_picking.py:0
msgid "No %(picking_type)s ready for this %(barcode_type)s"
msgstr "เลขที่ %(picking_type)s พร้อมสำหรับ %(barcode_type)s"

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/models/stock_move_line.py:0
msgid "No Barcode"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_scan_source_location__no
msgid "No Scan"
msgstr "ไม่มีการสแกน"

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/controllers/stock_barcode.py:0
msgid ""
"No internal operation type. Please configure one in warehouse settings."
msgstr "ไม่มีประเภทการดำเนินการภายใน โปรดกำหนดค่าหนึ่งในการตั้งค่าคลังสินค้า"

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/controllers/stock_barcode.py:0
msgid "No picking or location or product corresponding to barcode %(barcode)s"
msgstr "ไม่มีการเบิก ตำแหน่ง หรือสินค้าที่สอดคล้องกับบาร์โค้ด %(barcode)s"

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/controllers/stock_barcode.py:0
msgid "No picking or product corresponding to barcode %(barcode)s"
msgstr "ไม่มีการเบิกหรือสินค้าที่สอดคล้องกับบาร์โค้ด %(barcode)s"

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/models/stock_picking.py:0
msgid "No product, lot or package found for barcode %s"
msgstr "ไม่พบผลิตภัณฑ์ ล็อต หรือบรรจุภัณฑ์สำหรับบาร์โค้ด %s"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/apply_quant_dialog.xml:0
msgid "No, I'll count them later"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__barcode_nomenclature_id
msgid "Nomenclature"
msgstr "การตีความ"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Not the expected scan"
msgstr "ไม่ใช่การสแกนที่คาดไว้"

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/models/stock_picking.py:0
#: model:ir.actions.act_window,name:stock_barcode.open_picking
msgid "Open picking form"
msgstr "เปิดแบบฟอร์มการเบิกสินค้า"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.xml:0
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
#: model:ir.actions.act_window,name:stock_barcode.stock_picking_action_kanban
#: model:ir.actions.act_window,name:stock_barcode.stock_picking_type_action_kanban
msgid "Operations"
msgstr "การปฏิบัติการ"

#. module: stock_barcode
#: model:ir.model.fields.selection,name:stock_barcode.selection__stock_picking_type__restrict_scan_tracking_number__optional
msgid "Optional Scan"
msgstr "ตัวเลือกสแกน"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_view_picking_type_form
msgid "Options"
msgstr "ตัวเลือก"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Owner"
msgstr "เจ้าของ"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Package"
msgstr "แพ็คเกจ"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "Package type %(type)s applied to the package %(package)s"
msgstr "ประเภทแพ็คเกจ %(type)s นำไปใช้กับแพ็คเกจ %(package)s"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_quant_package
msgid "Packages"
msgstr "แพ็คเกจ"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__product_packaging_id
msgid "Packaging"
msgstr "การบรรจุหีบห่อ"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__product_packaging_uom_qty
msgid "Packaging Quantity"
msgstr "จำนวนบรรจุภัณฑ์"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__parent_location_id
msgid "Parent Location"
msgstr "ตำแหน่งแม่"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__parent_location_dest_id
msgid "Parent Location Dest"
msgstr "สถานที่ตั้งหลักปลายทาง"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_backorder_confirmation__partial_move_count
msgid "Partial Move Count"
msgstr "จำนวนการย้ายบางส่วน"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_barcode
msgid "Picking Details"
msgstr "รายละเอียดการเบิกสินค้า"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_picking_type
msgid "Picking Type"
msgstr "ประเภทการรับ"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Please scan destination location for %s before scanning other product"
msgstr "โปรดสแกนตำแหน่งปลายทางสำหรับ %s ก่อนที่จะสแกนสินค้าอื่น"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.js:0
msgid "Please, Scan again!"
msgstr "กรุณาสแกนอีกครั้ง!"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Press Validate or scan another product"
msgstr "กดตรวจสอบหรือสแกนสินค้าอื่น"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.xml:0
msgid "Print"
msgstr "พิมพ์"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Print Barcodes"
msgstr "พิมพ์บาร์โค้ด"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Print Delivery Slip"
msgstr "พิมพ์ใบส่งสินค้า"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "Print Inventory"
msgstr "พิมพ์สินค้าคงคลัง"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Print Packages"
msgstr "พิมพ์แพ็คเกจ"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Print Picking Operations"
msgstr "พิมพ์การดำเนินการเบิกสินค้า"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "Print the"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.js:0
msgid "Processing %(processed)s/%(toProcess)s barcodes"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/backorder_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Product"
msgstr "สินค้า"

#. module: stock_barcode
#: model:ir.actions.act_window,name:stock_barcode.product_action_barcodes
#: model_terms:ir.ui.view,arch_db:stock_barcode.product_view_list_barcodes
msgid "Product Barcodes"
msgstr "บาร์โค้ดของสินค้า"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "ย้ายสินค้า (รายการเคลื่อนย้ายสต็อก)"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_product_packaging
msgid "Product Packaging"
msgstr "บรรจุภัณฑ์ของสินค้า"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__product_reference_code
#: model:ir.model.fields,field_description:stock_barcode.field_stock_quant__product_reference_code
msgid "Product Reference Code"
msgstr "รหัสอ้างอิงสินค้า"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__product_stock_quant_ids
msgid "Product Stock Quant"
msgstr "ปริมาณสต็อกสินค้า"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_uom_uom
msgid "Product Unit of Measure"
msgstr "หน่วยวัดสินค้า"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_product_product
msgid "Product Variant"
msgstr "ตัวแปรสินค้า"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.xml:0
msgid "Put In Pack"
msgstr "ใส่แพ็ค"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_view_picking_type_form
msgid "Put in Pack"
msgstr "ใส่ในแพ็ค"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__qty_done
msgid "Qty Done"
msgstr "จำนวนที่เสร็จแล้ว"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Quantity"
msgstr "ปริมาณ"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode_kanban
msgid "Quantity Done"
msgstr "ปริมาณที่ทำเสร็จแล้ว"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Quantity in stock"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_move_line__product_packaging_uom_qty
msgid "Quantity of the Packaging in the UoM of the Stock Move Line."
msgstr "ปริมาณบรรจุภัณฑ์ใน UoM ของรายการย้ายสต็อก"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_quant
msgid "Quants"
msgstr "วิเคราะห์เชิงปริมาณ"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__barcode_rfid_batch_time
msgid "RFID Timer"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/count_screen_rfid.xml:0
msgid "Read Rate"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/count_screen_rfid.xml:0
msgid "Read Time"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.js:0
msgid "Remove it"
msgstr "ลบออก"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.xml:0
msgid "Return Products"
msgstr "คืนสินค้า"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/kanban/stock_barcode_kanban_renderer.xml:0
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "Scan a"
msgstr "สแกน"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "Scan a location"
msgstr "สแกนตำแหน่ง"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a lot number"
msgstr "สแกนเลขล็อต"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a lot number or a packages then the destination location"
msgstr "สแกนหมายเลขล็อตหรือแพ็คเกจแล้วระบุตำแหน่งปลายทาง"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a lot number then the destination location"
msgstr "สแกนเลขล็อตแล้วระบุตำแหน่งปลายทาง"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a package"
msgstr "สแกนแพ็คเกจ"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a package or put in pack"
msgstr "สแกนแพ็คเกจหรือใส่แพ็ค"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a package or the destination location"
msgstr "สแกนแพ็คเกจหรือตำแหน่งปลายทาง"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a package, the destination location or another product"
msgstr "สแกนแพ็คเกจ สถานที่ปลายทาง หรือสินค้าอื่น"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "Scan a product"
msgstr "สแกนสินค้า"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a product before scanning a tracking number"
msgstr "สแกนสินค้าก่อนที่จะสแกนหมายเลขติดตาม"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a product from %s"
msgstr "สแกนสินค้าจาก %s"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "Scan a product in %s or scan another location"
msgstr "สแกนสินค้าใน %s หรือสแกนตำแหน่งอื่น"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a product or a package"
msgstr "สแกนสินค้าหรือแพ็คเกจ"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a product or another package"
msgstr "สแกนสินค้าหรือแพ็คเกจอื่น"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a product or the destination location."
msgstr "สแกนสินค้าหรือตำแหน่งปลายทาง"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a product then the destination location"
msgstr "สแกนสินค้าแล้วระบุตำแหน่งปลายทาง"

#. module: stock_barcode
#. odoo-python
#: code:addons/stock_barcode/models/stock_picking.py:0
msgid ""
"Scan a product, a lot/serial number or a package to filter the transfers."
msgstr "สแกนสินค้า ล็อต/ซีเรียลนัมเบอร์ หรือบรรจุภัณฑ์ เพื่อกรองการโอนเงิน"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a product, a package or the destination location"
msgstr "สแกนสินค้า แพ็คเกจ หรือตำแหน่งปลายทาง"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a product, a package or the destination location."
msgstr "สแกนสินค้า แพ็คเกจ หรือตำแหน่งปลายทาง"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a serial number"
msgstr "สแกนหมายเลขซีเรียล"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a serial number or a package"
msgstr "สแกนหมายเลขซีเรียลหรือแพ็คเกจ"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a serial number or a package then the destination location"
msgstr "สแกนหมายเลขซีเรียลหรือแพ็คเกจแล้วระบุตำแหน่งปลายทาง"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan a serial number then the destination location"
msgstr "สแกนหมายเลขซีเรียลแล้วระบุตำแหน่งปลายทาง"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "Scan an"
msgstr "สแกน"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan another serial number"
msgstr "สแกนหมายเลขซีเรียลอื่น"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "Scan lot numbers for product %s to change their quantity"
msgstr "สแกนหมายเลขล็อตสำหรับสินค้า %s เพื่อเปลี่ยนปริมาณ"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan more lot numbers"
msgstr "สแกนเลขล็อตเพิ่มเติม"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan more lot numbers or a package"
msgstr "สแกนหมายเลขล็อตหรือแพ็คเกจเพิ่มเติม"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "Scan more products in %s or scan another location"
msgstr "สแกนสินค้าเพิ่มเติมใน %s หรือสแกนตำแหน่งอื่น"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan more products or a package"
msgstr "สแกนสินค้าหรือแพ็คเกจเพิ่มเติม"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan more products, or scan a new source location"
msgstr "สแกนสินค้าเพิ่มเติมหรือสแกนตำแหน่งต้นทางใหม่"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "Scan or tap"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "Scan serial numbers for product %s to change their quantity"
msgstr "สแกนหมายเลขซีเรียลของสินค้า %s เพื่อเปลี่ยนปริมาณ"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan the destination location"
msgstr "สแกนตำแหน่งปลายทาง"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan the package %s"
msgstr "สแกนแพ็คเกจ %s"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan the source location"
msgstr "สแกนตำแหน่งต้นทาง"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scan the source location or a package"
msgstr "สแกนตำแหน่งต้นทางหรือบรรจุภัณฑ์"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid ""
"Scanned product %s is not reserved for this transfer. Are you sure you want "
"to add it?"
msgstr ""
"สแกนสินค้า %s ไม่ได้สำรองไว้สำหรับการโอนย้ายนี้ คุณแน่ใจว่าต้องการเพิ่ม?"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid ""
"Scanned quantity uses %(unit)s as its Unit of Measure (UoM), but it is not "
"compatible with the line's UoM (%(lineUnit)s)."
msgstr ""
"ปริมาณที่สแกนใช้ %(unit)s เป็นหน่วยวัด (UoM) แต่เข้ากันไม่ได้กับ UoM "
"ของบรรทัด (%(lineUnit)s)"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid ""
"Scanned quantity uses %(unit)s as its Unit of Measure (UoM), but it is not "
"compatible with the product's UoM (%(productUnit)s)."
msgstr ""
"ปริมาณที่สแกนใช้ %(unit)s เป็นหน่วยวัด (UoM) แต่เข้ากันไม่ได้กับ UoM "
"(%(productUnit)s) ของผลิตภัณฑ์"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "Scanning package"
msgstr "กำลังสแกนแพ็คเกจ"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.xml:0
#: model:ir.model,name:stock_barcode.model_stock_scrap
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
msgid "Scrap"
msgstr "เศษสินค้า"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
msgid "Scrap Location"
msgstr "ตำแหน่งของเศษสินค้า"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Select a Product"
msgstr "เลือกสินค้า"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Serial/Lot Number"
msgstr "หมายเลขซีเรียล/ล็อต"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "Set the maximum delay between each key in ms (100 ms by default.)"
msgstr ""
"ตั้งค่าความล่าช้าสูงสุดระหว่างแต่ละคีย์เป็นมิลลิวินาที (ค่าเริ่มต้นคือ 100 "
"มิลลิวินาที)"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__show_barcode_nomenclature
msgid "Show Barcode Nomenclature"
msgstr "แสดงระบบการตั้งชื่อบาร์โค้ด"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__show_barcode_validation
msgid "Show Barcode Validation"
msgstr "แสดงการตรวจสอบบาร์โค้ด"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__group_barcode_show_quantity_count
#: model:res.groups,name:stock_barcode.group_barcode_show_quantity_count
msgid "Show Quantity to Count"
msgstr "แสดงปริมาณที่จะนับ"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking_type__show_reserved_sns
msgid "Show reserved lots/SN"
msgstr "แสดง ล็อต/SN ที่จองไว้"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/main.xml:0
msgid "Sign"
msgstr "ลายเซ็น"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/apply_quant_dialog.xml:0
msgid "Some serials where not counted, set them as missing?"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Source Location"
msgstr "ตำแหน่งต้นทาง"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Source Package"
msgstr "ตำแหน่งบรรจุภัณฑ์"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/backorder_dialog.xml:0
msgid "Stay on transfer"
msgstr "อยู่ในการโอนย้าย"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_move
msgid "Stock Move"
msgstr "ย้ายสต็อก"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_request_count
msgid "Stock Request an Inventory Count"
msgstr "แจ้งขอนับสินค้าคงคลัง"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_package_type
msgid "Stock package type"
msgstr "ประเภทบรรจุภัณฑ์ของสต็อก"

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_picking_type__is_barcode_picking_type
msgid ""
"Technical field indicating if should be used in barcode app and used to "
"control visibility in the related UI."
msgstr ""
"ข้อมูลทางเทคนิคที่ระบุว่าควรใช้ในแอปบาร์โค้ดหรือไม่ "
"และใช้เพื่อควบคุมการมองเห็นใน UI ที่เกี่ยวข้อง"

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_picking_type__show_barcode_validation
msgid ""
"Technical field used to compute whether the \"Final Validation\" group "
"should be displayed, solving combined groups/invisible complexity."
msgstr ""
"ฟิลด์เทคนิคที่ใช้ในการคำนวณว่าควรแสดงกลุ่ม \"การตรวจสอบขั้นสุดท้าย\" หรือไม่"
" เพื่อแก้ไขกลุ่มที่รวมกัน/ความซับซ้อนที่มองไม่เห็น"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "The inventory adjustment has been validated"
msgstr "การปรับสินค้าคงคลังได้รับการตรวจสอบแล้ว"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "The product %s should not be picked in this operation."
msgstr "ไม่ควรเลือกผลิตภัณฑ์ %s ในการดำเนินการนี้"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "The scanned location doesn't belong to this operation's destination"
msgstr "ตำแหน่งที่สแกนไม่ได้เป็นของปลายทางของการดำเนินการนี้"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "The scanned location doesn't belong to this operation's location"
msgstr "ตำแหน่งที่สแกนไม่ได้เป็นของตำแหน่งของการดำเนินการนี้"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
msgid "The scanned serial number %s is already used."
msgstr "หมายเลขซีเรียลที่สแกน %s ถูกใช้ไปแล้ว"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "The transfer has been cancelled"
msgstr "การโอนย้ายถูกยกเลิก"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "The transfer has been validated"
msgstr "การโอนย้ายได้รับการตรวจสอบแล้ว"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "There is nothing to apply in this page."
msgstr "ไม่มีอะไรที่จะใช้ในหน้านี้"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "There is nothing to print in this page."
msgstr "ไม่มีอะไรจะพิมพ์ในหน้านี้"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "This package is already scanned."
msgstr "แพ็คเกจนี้ถูกสแกนแล้ว"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "This picking is already done"
msgstr "การเบิกสินค้านี้เสร็จสิ้นแล้ว"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "This picking is cancelled"
msgstr "การเบิกสินค้านี้ถูกยกเลิกแล้ว"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
msgid "This product doesn't exist."
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_res_config_settings__barcode_separator_regex
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid ""
"This regex is used in the Barcode application to separate individual "
"barcodes when an aggregate barcode (i.e. single barcode consisting of "
"multiple barcode encodings) is scanned."
msgstr ""
"regex "
"นี้ใช้ในแอปพลิเคชันบาร์โค้ดเพื่อแยกบาร์โค้ดแต่ละรายการเมื่อมีการสแกนบาร์โค้ดรวม"
" (เช่น บาร์โค้ดเดียวที่ประกอบด้วยการเข้ารหัสบาร์โค้ดหลายรายการ)"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
msgid "This serial number is already used."
msgstr "หมายเลขซีเรียลนี้ถูกใช้ไปแล้ว"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid ""
"Time before processing a batch. Used to group communication to the server "
"and improve global performance at the cost of reactivity."
msgstr ""
"เวลาที่ใช้ก่อนการประมวลผลชุดข้อมูล "
"ใช้เพื่อจัดกลุ่มการสื่อสารไปยังเซิร์ฟเวอร์และปรับปรุงประสิทธิภาพโดยรวมโดยแลกกับการตอบสนองที่รวดเร็ว"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/backorder_dialog.xml:0
msgid "To do"
msgstr "To do"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "To use packages, enable 'Packages' in the settings"
msgstr "หากต้องการใช้แพ็คเกจ ให้เปิดใช้งาน 'แพ็คเกจ' ในการตั้งค่า"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/count_screen_rfid.xml:0
msgid "Total Reads"
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_picking
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__picking_id
msgid "Transfer"
msgstr "โอน"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__picking_name
msgid "Transfer Name"
msgstr "ชื่อการโอนย้าย"

#. module: stock_barcode
#: model_terms:ir.actions.act_window,help:stock_barcode.stock_picking_action_kanban
msgid "Transfers allow you to move products from one location to another."
msgstr "การโอนย้ายทำให้คุณสามารถย้ายสินค้าจากที่หนึ่งไปยังอีกที่หนึ่งได้"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/count_screen_rfid.xml:0
msgid "Unique Tags"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.scrap_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode_kanban
msgid "Unit of Measure"
msgstr "หน่วยวัด"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_stock_quant_tree
msgid "UoM"
msgstr "หน่วยวัด"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/backorder_dialog.xml:0
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
msgid "Validate"
msgstr "ตรวจสอบ"

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_scrap___barcode_scanned
msgid "Value of the last barcode scanned."
msgstr "ค่าของบาร์โค้ดล่าสุดที่ถูกสแกน"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/widgets/image_preview.xml:0
msgid "Viewer"
msgstr "ผู้ดู"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_warehouse
msgid "Warehouse"
msgstr "โกดังสินค้า"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid ""
"When scanning a location in inventory adjustments, you will be assigned to "
"count all products from this location"
msgstr ""
"เมื่อสแกนสถานที่ในการปรับสินค้าคงคลัง "
"คุณจะได้รับมอบหมายให้นับผลิตภัณฑ์ทั้งหมดจากสถานที่นี้"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
msgid "Wrong Unit of Measure"
msgstr "หน่วยวัดที่ไม่ถูกต้อง"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/components/apply_quant_dialog.xml:0
msgid "Yes"
msgstr "ใช่"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid ""
"You are about to take the product %(productName)s from the location %(locationName)s but this product isn't reserved in this location.\n"
"Scan the current location to confirm that."
msgstr ""
"คุณกำลังจะนำสินค้า %(productName)s จากสถานที่ %(locationName)s แต่สินค้านี้ไม่ได้ถูกจองไว้ในสถานที่นี้\n"
"สแกนตำแหน่งปัจจุบันเพื่อยืนยัน"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
msgid ""
"You are expected to scan one or more products or a package available at the "
"picking location"
msgstr ""
"คุณจะต้องสแกนสินค้าหรือแพ็คเกจตั้งแต่หนึ่งรายการขึ้นไปที่ตำแหน่งเบิกสินค้า"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "You are supposed to scan %s or another source location"
msgstr "คุณควรจะสแกน %s หรือตำแหน่งต้นทางอื่น"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "You can't apply a package type. First, scan product or select a line"
msgstr ""
"คุณไม่สามารถใช้ประเภทแพ็คเกจได้ ขั้นแรกให้สแกนสินค้าหรือเลือกรายการก่อน"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "You can't register scrap at this state of the operation"
msgstr "คุณไม่สามารถลงทะเบียนเศษสินค้าในสถานะการดำเนินการนี้ได้"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid ""
"You have already scanned %s items of this package. Do you want to scan the "
"whole package?"
msgstr ""
"คุณได้สแกน %s รายการของแพ็คเกจนี้แล้ว คุณต้องการสแกนแพ็คเกจทั้งหมดหรือไม่"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_backorder_confirmation
msgid "You have processed less products than the initial demand:"
msgstr "คุณได้ดำเนินการสินค้าน้อยกว่าความต้องการในตอนแรก:"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "You must scan a package or put in pack"
msgstr "คุณต้องสแกนแพ็คเกจหรือใส่แพ็ค"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
msgid "You must scan a product"
msgstr "คุณต้องสแกนสินค้า"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "barcodes"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "demo data sheet"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "for operations."
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "location"
msgstr "ตำแหน่ง"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/kanban/stock_barcode_kanban_renderer.xml:0
msgid "lot"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "operation type"
msgstr "ประเภทการดำเนินงาน"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/kanban/stock_barcode_kanban_renderer.xml:0
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "or a"
msgstr "หรือ"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/kanban/stock_barcode_kanban_renderer.xml:0
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "package"
msgstr "แพ็คเกจ"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "picking"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/kanban/stock_barcode_kanban_renderer.xml:0
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "product"
msgstr "สินค้า"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_backorder_confirmation
msgid "products were not processed at all."
msgstr "สินค้าไม่ได้รับการดำเนินการเลย"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_backorder_confirmation
msgid "products were partially processed."
msgstr "สินค้าได้รับการดำเนินการบางส่วน"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/kanban/stock_barcode_kanban_renderer.xml:0
msgid "to filter your records"
msgstr "เพื่อกรองบันทึกของคุณ"

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "to initiate a transfer"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "to locate it"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "to open it"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "to start it"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "to test or"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/main_menu/main_menu.xml:0
msgid "tracking number"
msgstr ""

#. module: stock_barcode
#. odoo-javascript
#: code:addons/stock_barcode/static/src/kanban/stock_barcode_kanban_renderer.xml:0
msgid "transfer"
msgstr ""
