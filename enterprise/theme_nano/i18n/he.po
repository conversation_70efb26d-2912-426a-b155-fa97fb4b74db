# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* theme_nano
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-07 13:30+0000\n"
"PO-Revision-Date: 2024-09-25 18:03+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=3; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: 2;\n"

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_key_images
msgid "2019"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_key_images
msgid "2021"
msgstr "2021"

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_key_images
msgid "2022"
msgstr "2022"

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_key_images
msgid "2023"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_big_number
msgid "250+"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_empowerment
msgid ""
"<br/>Transforming ideas into tangible creations with precision and "
"flair.<br/><br/>"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_key_images
msgid "<font class=\"text-o-color-1\">LunaTech Rebrand</font>"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_key_images
msgid "<font class=\"text-o-color-1\">Nova App Launch</font>"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_key_images
msgid "<font class=\"text-o-color-1\">Pulse Website Redesign</font>"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_key_images
msgid "<font class=\"text-o-color-1\">Skyline Brand Strategy</font>"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.configurator_s_discovery
msgid ""
"<i class=\"fa fa-circle text-success\" aria-hidden=\"true\"/>  AVAILABLE FOR"
" NEW PROJECTS"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_discovery
msgid ""
"<i class=\"fa fa-fw fa-circle text-success o_not-animable\" aria-"
"hidden=\"true\"/><span class=\"o_small-fs\">  AVAILABLE FOR NEW "
"PROJECTS</span>"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_empowerment
msgid ""
"<i class=\"fa fa-fw fa-info-circle o_not-animable\" role=\"img\"/>  Creative"
" solutions"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_text_block
msgid ""
"<span class=\"h3-fs\">Our approach is rooted in creativity and innovation. "
"What started as a small team has evolved into an agency dedicated to "
"delivering results through strategic thinking and fostering long-term client"
" partnerships.</span>"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_numbers
msgid ""
"<span class=\"s_number display-1-fs\">4.5</span><br/>\n"
"                <span class=\"h5-fs\">Review Rate</span>"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_numbers
msgid ""
"<span class=\"s_number display-1-fs\">40h</span><br/>\n"
"                <span class=\"h5-fs\">Average shipping time</span>"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_numbers
msgid ""
"<span class=\"s_number display-1-fs\">50+</span><br/>\n"
"                <span class=\"h5-fs\">Happy customers</span>"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_text_block
msgid "<strong><font class=\"text-o-color-1\">OUR APPROACH</font></strong>"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_discovery
msgid "<strong>We are CreativeAgency.</strong>"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.new_page_template_about_map_s_text_block_h1
msgid "About Us"
msgstr "אודותינו"

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_company_team_basic
msgid "Account Manager"
msgstr "ניהול חשבון"

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_features
msgid "Adaptable"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_references_social
msgid "Barbershop"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_company_team_basic
msgid "Ben Cole"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_unveil
msgid ""
"Bring your ideas to life with our innovative design and creative expertise."
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_pricelist_boxed
msgid "Business Strategy Consulting"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_pricelist_boxed
msgid "Cloud Computing Solutions"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_pricelist_boxed
msgid ""
"Comprehensive financial planning, including risk assessment, investment "
"strategy, and tax optimization for your business."
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_quadrant
msgid "Contact Us"
msgstr "צור קשר"

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.configurator_s_discovery
msgid "Contact us  <i class=\"fa fa-long-arrow-right\" aria-hidden=\"true\"/>"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_empowerment
msgid "Crafting excellence with<br/>innovative design and fabrication"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_quadrant
msgid "Creative Agency"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_company_team_basic
msgid "Creative Director"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_striped_center_top
msgid "Creative Solutions for Agencies"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_freegrid
msgid "Creative Solutions for Modern Agencies"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_cover
msgid "Creative Studio"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_card_offset
msgid "Creativity Meets Cutting-Edge Technology"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_striped_center_top
msgid "Creativity that drives success"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_key_benefits
msgid "Custom Creations"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_pricelist_boxed
msgid ""
"Customized HR solutions, including talent acquisition, employee training, "
"and performance management to boost productivity."
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_three_columns
msgid "Details"
msgstr "פרטים"

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_pricelist_boxed
msgid "Digital Marketing Services"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_striped_center_top
msgid "Discover Our Services"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_features
msgid "Durable"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_pricelist_boxed
msgid ""
"Enhance your business operations with our comprehensive suite of "
"professional services, tailored to meet your corporate needs."
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_company_team_basic
msgid "Evan Ray"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_key_benefits
msgid "Exclusive Designs"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_pricelist_boxed
msgid ""
"Expert advice on business planning, market analysis, and competitive "
"strategy to help your company achieve its goals."
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_key_benefits
msgid ""
"Explore one-of-a-kind designs and limited-edition pieces that showcase "
"exceptional craftsmanship and creativity."
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_pricelist_boxed
msgid "Financial Advisory Services"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_discovery
msgid "Get Started"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_empowerment
msgid "Get a quote"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_three_columns
msgid "Handcrafted"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_references_social
msgid "Hosoren"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_pricelist_boxed
msgid "Human Resources Management"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_pricelist_boxed
msgid "IT Support Services"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_three_columns
msgid "Innovative"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_cta_box
msgid "Interested in our services?"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_features
msgid "Intuitive"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_company_team_basic
msgid "Jane Ford"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_carousel_intro
msgid "Leading the future with innovation"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_cta_box
msgid "Let's discuss how we can help your business<br/><br/>"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_card_offset
msgid "Let’s innovate together."
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_three_columns
msgid "Made in Santa's Workshop. High quality fabric &amp; exclusive designs."
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_company_team_basic
msgid "Marketing Analyst"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_company_team_basic
msgid "Media Strategist"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_company_team_basic
msgid ""
"Meet the masterminds behind our agency. With them, your project is in good "
"hands for sure."
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_company_team_basic
msgid "Mia Lee"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_references_social
msgid "Mountain"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_references_social
msgid "Oceandor"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_pricelist_boxed
msgid "Our Corporate Services"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_key_images
msgid "Our case studies"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_empowerment
msgid "Our projects"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_image_text_overlap
msgid ""
"Our story began with a passion for creativity and innovation. From a small "
"team, we've grown into an agency focused on delivering results and building "
"lasting client partnerships."
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_company_team_basic
msgid "Our talented crew"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_quadrant
msgid ""
"Partner with our creative agency for cutting-edge design and IT services. We"
" bring your vision to life with innovative solutions tailored to your "
"needs.<br/><br/> Let's create something extraordinary together."
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_striped_center_top
msgid ""
"Partner with us for innovative design and IT services that bring your "
"creative visions to life in the digital world."
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_freegrid
msgid ""
"Partner with us for innovative design and IT services that bring your "
"creative visions to life in the digital world. We blend creativity and "
"technology to deliver exceptional results."
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_three_columns
msgid ""
"People should love the things they own. Things should be built to last."
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_key_benefits
msgid "Personalized Support"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_key_benefits
msgid ""
"Receive hands-on assistance throughout the creation process, from concept to"
" completion, to ensure your vision comes to life."
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_pricelist_boxed
msgid ""
"Reliable IT support, network management, and cybersecurity solutions to "
"safeguard your business operations and data integrity."
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_pricelist_boxed
msgid ""
"Scalable cloud services to enhance your business’s flexibility and data "
"accessibility, supporting remote work and collaboration."
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_cta_box
msgid "Schedule a call"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_empowerment
msgid "See our work   <i class=\"fa fa-long-arrow-right\" role=\"img\"/>"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_key_images
msgid "Some of the latest projects we had the pleasure to work on"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_pricelist_boxed
msgid "Starting at $1,500"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_pricelist_boxed
msgid "Starting at $1,800"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_pricelist_boxed
msgid "Starting at $2,000"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_pricelist_boxed
msgid "Starting at $2,500"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_pricelist_boxed
msgid "Starting at $3,000"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_pricelist_boxed
msgid ""
"Strategic online marketing, SEO optimization, and social media management to"
" boost your brand visibility and customer engagement."
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_three_columns
msgid "Tailored"
msgstr ""

#. module: theme_nano
#: model:ir.model,name:theme_nano.model_theme_utils
msgid "Theme Utils"
msgstr "ערכות נושא"

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_three_columns
msgid "Transforming traditional crafts — with a shot of modern savvy!"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_references
msgid "Trusted by industry leaders"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_card_offset
msgid ""
"Unlock your creative potential with our innovative design and IT services. "
"We offer a blend of creativity and technology that brings your vision to "
"life in new and exciting ways."
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_features
msgid "We build modular solutions."
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_cover
msgid ""
"We combine smart design with rich technology <br/>to craft innovative "
"products."
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_features
msgid "We craft long-lasting goods."
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_features
msgid "We create easy-to-use products."
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_key_benefits
msgid ""
"We design and build bespoke items tailored to your specifications, ensuring "
"each piece is unique and meets your exact needs."
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_unveil
msgid "Where ideas take flight"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_freegrid
msgid "Work With Us"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_big_number
msgid "satisfied clients"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_pricelist_boxed
msgid "✽  Consulting Services"
msgstr ""

#. module: theme_nano
#: model_terms:theme.ir.ui.view,arch:theme_nano.s_pricelist_boxed
msgid "✽  Technology Solutions"
msgstr ""
