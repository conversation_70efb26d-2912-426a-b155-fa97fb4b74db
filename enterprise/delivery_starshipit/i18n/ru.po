# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* delivery_starshipit
# 
# Translators:
# Wil O<PERSON>, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:24+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid " after deleting the order on Starshipit"
msgstr " после удаления заказа на Starshipit"

#. module: delivery_starshipit
#: model_terms:ir.ui.view,arch_db:delivery_starshipit.view_delivery_carrier_form
msgid ""
"<i class=\"fa fa-exclamation-triangle\"/> Do not forget to select a service "
"for a valid configuration."
msgstr ""
"<i class=\"fa fa-exclamation-triangle\"/> Не забудьте выбрать службу для "
"действующей конфигурации."

#. module: delivery_starshipit
#: model_terms:ir.ui.view,arch_db:delivery_starshipit.view_delivery_carrier_form
msgid ""
"<i class=\"fa fa-exclamation-triangle\"/> In <b>Test Environment</b>, your shippings are automatically <b>archived</b> after the label creation. <br/>\n"
"                        Please note that charges can still occur for label creations and some shipping carriers may automatically validate the shipment when printing labels."
msgstr ""
"<i class=\"fa fa-exclamation-triangle\"/> В <b>тестовой среде</b> отправления автоматически <b>архивируются</b> после создания этикетки. <br/>\n"
"                        Обратите внимание, что за создание этикеток по-прежнему могут взиматься сборы, а некоторые перевозчики могут автоматически подтверждать отправление при печати этикеток."

#. module: delivery_starshipit
#: model_terms:ir.ui.view,arch_db:delivery_starshipit.view_shipping_method_starshipit
msgid ""
"<i class=\"fa fa-info-circle\"/> Available shipping services depend on "
"enabled carriers in your Starshipit account."
msgstr ""
"<i class=\"fa fa-info-circle\"/> Доступные службы доставки зависят от "
"включенных перевозчиков в вашем аккаунте Starshipit."

#. module: delivery_starshipit
#: model_terms:ir.ui.view,arch_db:delivery_starshipit.view_shipping_method_starshipit
msgid "Cancel"
msgstr "Отменить"

#. module: delivery_starshipit
#: model:ir.model.fields,field_description:delivery_starshipit.field_stock_package_type__package_carrier_type
msgid "Carrier"
msgstr "Перевозчик"

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid "Choose Starshipit Shipping Service"
msgstr "Выберите службу доставки Starshipit"

#. module: delivery_starshipit
#: model:ir.model,name:delivery_starshipit.model_starshipit_shipping_wizard
msgid "Choose from the available starshipit shipping methods"
msgstr "Выберите один из доступных способов доставки звездолета"

#. module: delivery_starshipit
#: model_terms:ir.ui.view,arch_db:delivery_starshipit.view_shipping_method_starshipit
msgid "Confirm"
msgstr "Подтвердить"

#. module: delivery_starshipit
#: model:ir.model.fields,help:delivery_starshipit.field_starshipit_shipping_wizard__available_services
msgid ""
"Contains the list of available services for the starshipit account to select"
" from."
msgstr ""
"Содержит список доступных услуг, которые можно выбрать для учетной записи "
"starshipit."

#. module: delivery_starshipit
#: model:ir.model.fields,field_description:delivery_starshipit.field_starshipit_shipping_wizard__create_uid
msgid "Created by"
msgstr "Создано"

#. module: delivery_starshipit
#: model:ir.model.fields,field_description:delivery_starshipit.field_starshipit_shipping_wizard__create_date
msgid "Created on"
msgstr "Создано"

#. module: delivery_starshipit
#: model_terms:ir.ui.view,arch_db:delivery_starshipit.view_delivery_carrier_form
msgid "Default Package Type"
msgstr "Тип пакета по умолчанию"

#. module: delivery_starshipit
#: model:ir.model.fields,field_description:delivery_starshipit.field_delivery_carrier__starshipit_default_package_type_id
msgid "Default Package Type for Starshipit"
msgstr "Тип пакета по умолчанию для Starshipit"

#. module: delivery_starshipit
#: model:ir.model.fields,field_description:delivery_starshipit.field_starshipit_shipping_wizard__carrier_id
msgid "Delivery"
msgstr "Доставка"

#. module: delivery_starshipit
#: model_terms:ir.ui.view,arch_db:delivery_starshipit.view_shipping_method_starshipit
msgid "Delivery Service"
msgstr "Служба доставки"

#. module: delivery_starshipit
#: model:ir.model.fields,field_description:delivery_starshipit.field_starshipit_shipping_wizard__display_name
msgid "Display Name"
msgstr "Отображаемое имя"

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid ""
"Error: %(file_name)s file could not be obtained for order %(order_name)s."
msgstr ""
"Ошибка: не удалось получить файл %(file_name)s для заказа %(order_name)s."

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid "Error: this delivery method is not available for this order."
msgstr "Ошибка: этот способ доставки недоступен для данного заказа."

#. module: delivery_starshipit
#: model:ir.model.fields,field_description:delivery_starshipit.field_starshipit_shipping_wizard__id
msgid "ID"
msgstr "ID"

#. module: delivery_starshipit
#: model_terms:ir.ui.view,arch_db:delivery_starshipit.view_delivery_carrier_form
msgid "Integration"
msgstr "Интеграция"

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/starshipit_service.py:0
msgid "Invalid Starshipit credentials."
msgstr "Неверные учетные данные Starshipit."

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid "Labels were generated for the order %s"
msgstr "Этикетки были созданы для заказа %s"

#. module: delivery_starshipit
#: model:ir.model.fields,field_description:delivery_starshipit.field_starshipit_shipping_wizard__write_uid
msgid "Last Updated by"
msgstr "Последнее обновление"

#. module: delivery_starshipit
#: model:ir.model.fields,field_description:delivery_starshipit.field_starshipit_shipping_wizard__write_date
msgid "Last Updated on"
msgstr "Последнее обновление"

#. module: delivery_starshipit
#: model_terms:ir.ui.view,arch_db:delivery_starshipit.view_delivery_carrier_form
msgid "Options"
msgstr "Опции"

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid ""
"Order %(order)s was already sent to the carrier during label creation.\n"
"Manifest number: %(manifest_number)s"
msgstr ""

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid ""
"Order %(order)s was sent to the carrier during label creation.As you are in a test environment, please make sure to cancel the order with your carrier directly.\n"
"Manifest number: %(manifest_number)s"
msgstr ""

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid "Order %s was archived."
msgstr "Заказ %s был отправлен в архив."

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid "Order %s was sent to the carrier."
msgstr "Заказ %s был отправлен перевозчику."

#. module: delivery_starshipit
#: model:ir.model.fields,field_description:delivery_starshipit.field_delivery_carrier__starshipit_origin_address
msgid "Origin Address"
msgstr "Адрес Отправителя"

#. module: delivery_starshipit
#: model:ir.model.fields,help:delivery_starshipit.field_delivery_carrier__starshipit_default_package_type_id
msgid ""
"Package dimensions are required to get more accurate rates. You can define "
"these in a package type that you set as default"
msgstr ""
"Размеры пакета необходимы для получения более точных тарифов. Их можно "
"определить в типе упаковки, который задается по умолчанию"

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid "Please delete the order on Starshipit then try again."
msgstr "Пожалуйста, удалите заказ на Starshipit и повторите попытку."

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/starshipit_service.py:0
msgid "Please fill in the field %(field)s on the %(partner)s partner."
msgstr ""

#. module: delivery_starshipit
#: model:ir.model.fields,field_description:delivery_starshipit.field_delivery_carrier__delivery_type
msgid "Provider"
msgstr "Провайдер"

#. module: delivery_starshipit
#: model:ir.model,name:delivery_starshipit.model_stock_return_picking
msgid "Return Picking"
msgstr "Возвратить комплектование"

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid "Return labels were generated for the order %s"
msgstr "Для заказа были созданы возвратные этикетки %s"

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid ""
"Return order %(order)s was already sent to the carrier during label creation.\n"
"Manifest number: %(manifest_number)s"
msgstr ""

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid ""
"Return order %(order)s was sent to the carrier during label creation.As you are in a test environment, please make sure to cancel the order with your carrier directly.\n"
"Manifest number: %(manifest_number)s"
msgstr ""

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid "Return order %s was archived."
msgstr "Заказ на возврат %s был отправлен в архив."

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid "Return order %s was sent to the carrier."
msgstr "Заказ на возврат %s был отправлен перевозчику."

#. module: delivery_starshipit
#: model_terms:ir.ui.view,arch_db:delivery_starshipit.view_delivery_carrier_form
msgid "Select a service linked to your starshipit account"
msgstr "Выберите услугу, связанную с вашим аккаунтом в starshipit"

#. module: delivery_starshipit
#: model:ir.model.fields,field_description:delivery_starshipit.field_starshipit_shipping_wizard__selected_service_code
msgid "Selected Service"
msgstr "Избранный сервис"

#. module: delivery_starshipit
#: model:ir.model.fields,field_description:delivery_starshipit.field_delivery_carrier__starshipit_service_code
msgid "Service Code"
msgstr "Код услуги"

#. module: delivery_starshipit
#: model:ir.model.fields,field_description:delivery_starshipit.field_delivery_carrier__starshipit_service_name
msgid "Service Name"
msgstr "Название услуги"

#. module: delivery_starshipit
#: model:ir.model,name:delivery_starshipit.model_delivery_carrier
msgid "Shipping Methods"
msgstr "Методы доставки"

#. module: delivery_starshipit
#: model_terms:ir.ui.view,arch_db:delivery_starshipit.view_shipping_method_starshipit
msgid "Shipping Product"
msgstr "Доставка товара"

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/starshipit_service.py:0
msgid "Something went wrong, please try again later: %s"
msgstr "Что-то пошло не так, пожалуйста, повторите попытку позже: %s"

#. module: delivery_starshipit
#: model:ir.model.fields.selection,name:delivery_starshipit.selection__delivery_carrier__delivery_type__starshipit
#: model:ir.model.fields.selection,name:delivery_starshipit.selection__stock_package_type__package_carrier_type__starshipit
msgid "Starshipit"
msgstr "Звездочет"

#. module: delivery_starshipit
#: model:ir.model.fields,help:delivery_starshipit.field_delivery_carrier__starshipit_api_key
msgid "Starshipit API Integration key"
msgstr "Ключ интеграции с API Starshipit"

#. module: delivery_starshipit
#: model:ir.model.fields,help:delivery_starshipit.field_delivery_carrier__starshipit_subscription_key
msgid "Starshipit API Subscription key"
msgstr "Ключ подписки на API Starshipit"

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/starshipit_service.py:0
msgid "Starshipit API rate exceeded. Please try again later."
msgstr "Превышен тариф API Starshipit. Пожалуйста, повторите попытку позже."

#. module: delivery_starshipit
#: model:ir.model.fields,field_description:delivery_starshipit.field_delivery_carrier__starshipit_api_key
msgid "Starshipit Api Key"
msgstr "Starshipit Api Key"

#. module: delivery_starshipit
#: model_terms:ir.ui.view,arch_db:delivery_starshipit.view_delivery_carrier_form
msgid "Starshipit Configuration"
msgstr "Конфигурация звездолета"

#. module: delivery_starshipit
#: model:ir.model.fields,field_description:delivery_starshipit.field_stock_picking__starshipit_parcel_reference
msgid "Starshipit Parcel Reference"
msgstr "Ссылка на посылку Starshipit"

#. module: delivery_starshipit
#: model:ir.model.fields,field_description:delivery_starshipit.field_stock_picking__starshipit_return_parcel_reference
msgid "Starshipit Return Parcel Reference"
msgstr "Ссылка на обратную посылку Starshipit"

#. module: delivery_starshipit
#: model_terms:ir.ui.view,arch_db:delivery_starshipit.res_config_settings_view_form_sale_starshipit
#: model_terms:ir.ui.view,arch_db:delivery_starshipit.res_config_settings_view_form_stock_starshipit
msgid "Starshipit Shipping Methods"
msgstr "Способы доставки Starshipit"

#. module: delivery_starshipit
#: model:ir.model.fields,field_description:delivery_starshipit.field_delivery_carrier__starshipit_subscription_key
msgid "Starshipit Subscription Key"
msgstr "Ключ подписки Starshipit"

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid ""
"Starshipit cannot generate returns for the carrier %s. Please handle this "
"return with the carrier directly."
msgstr ""
"Starshipit не может генерировать возвраты для перевозчика %s. Пожалуйста, "
"обратитесь с этим возвратом непосредственно к перевозчику."

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/starshipit_service.py:0
msgid "Starshipit returned an error: %(message)s"
msgstr "Starshipit выдал ошибку: %(message)s"

#. module: delivery_starshipit
#: model:ir.model,name:delivery_starshipit.model_stock_package_type
msgid "Stock package type"
msgstr "Тип комплекта поставки"

#. module: delivery_starshipit
#: model:ir.model.fields,help:delivery_starshipit.field_delivery_carrier__starshipit_carrier_code
msgid ""
"The carrier on starshipit used by this carrier. The service code belongs to "
"it."
msgstr ""
"Носитель на звездолете используется этим носителем. Код службы принадлежит "
"ему."

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/starshipit_service.py:0
msgid ""
"The picking %(picking_name)s sequence is too long for Starshipit. Please "
"update your pickings sequence in order to use at most 50 characters."
msgstr ""
"Последовательность подбора %(picking_name)s слишком длинная для Starshipit. "
"Пожалуйста, обновите последовательность пикировок, чтобы использовать не "
"более 50 символов."

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid "The return label creation failed."
msgstr "Не удалось создать возвратную этикетку."

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/starshipit_service.py:0
msgid ""
"The service code %(service_code)s is too long for Starshipit. Please update "
"the code inside starshipit to be at most 100 characters, then update your "
"shipping carrier %(shipping_carrier)s to the new code."
msgstr ""
"Код услуги %(service_code)s слишком длинный для Starshipit. Пожалуйста, "
"обновите код в Starshipit, чтобы он состоял не более чем из 100 символов, а "
"затем обновите код %(shipping_carrier)s вашего перевозчика на новый код."

#. module: delivery_starshipit
#: model:ir.model.fields,help:delivery_starshipit.field_delivery_carrier__starshipit_service_code
#: model:ir.model.fields,help:delivery_starshipit.field_delivery_carrier__starshipit_service_name
msgid ""
"The service that will be used for this carrier. This is set when you select "
"a carrier from the wizard."
msgstr ""
"Услуга, которая будет использоваться для данного перевозчика. Этот параметр "
"задается при выборе перевозчика в мастере."

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid ""
"The shipping label creation failed with the following error:\n"
"%(error)s"
msgstr ""
"Создание транспортной этикетки завершилось со следующей ошибкой:\n"
"%(error)s"

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid ""
"There are no shipping services available, please verify the shipping address"
" or activate suitable carriers in your starshipit account."
msgstr ""
"Нет доступных служб доставки, пожалуйста, проверьте адрес доставки или "
"активируйте подходящих перевозчиков в вашем аккаунте на starshipit."

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid "There was an issue when creating the order, please try again"
msgstr "При создании заказа возникла проблема, пожалуйста, попробуйте еще раз"

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid "This action requires a Starshipit carrier."
msgstr "Для этого действия требуется носитель Starshipit."

#. module: delivery_starshipit
#: model:ir.model.fields,help:delivery_starshipit.field_delivery_carrier__starshipit_origin_address
msgid ""
"This address will be used when fetching the available services from "
"starshipit."
msgstr ""
"Этот адрес будет использоваться при получении доступных услуг от starshipit."

#. module: delivery_starshipit
#: model:ir.model,name:delivery_starshipit.model_stock_picking
msgid "Transfer"
msgstr "Перевод"
