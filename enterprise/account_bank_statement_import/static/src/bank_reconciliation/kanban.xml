<templates id="template" xml:space="preserve">
    <t t-name="account.BankRecKanbanButtons">
        <xpath expr="//div[hasclass('o_cp_buttons')]" position="inside">
            <t t-call="account.AccountViewUploadButton"/>
        </xpath>
    </t>

    <t t-name="account.BankRecKanbanUploadRenderer" t-inherit="account.BankRecKanbanRenderer" t-inherit-mode="primary">
        <xpath expr="//div[@t-ref='root']" position="before">
            <UploadDropZone
                visible="dropzoneState.visible"
                hideZone="() => dropzoneState.visible = false"/>
        </xpath>
        <xpath expr="//div[@t-ref='root']" position="attributes">
            <attribute name="t-on-dragenter.stop.prevent">() => dropzoneState.visible = true</attribute>
        </xpath>
    </t>

</templates>
