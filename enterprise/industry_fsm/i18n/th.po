# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* industry_fsm
# 
# Translators:
# <PERSON>il <PERSON>, 2024
# <PERSON><PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:51+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON>ppiam, 2024\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__nbr
msgid "# of Tasks"
msgstr "# ของงาน"

#. module: industry_fsm
#. odoo-python
#: code:addons/industry_fsm/models/project_task.py:0
msgid "%(partner)s has %(number)s tasks at the same time."
msgstr "%(partner)s มี %(number)s งานในเวลาเดียวกัน"

#. module: industry_fsm
#: model:ir.actions.report,print_report_name:industry_fsm.task_custom_report
msgid ""
"'Field Service Report - %s - %s' % (object.name, object.partner_id.name)"
msgstr ""
"'รายงานการบริการภาคสนาม - %s - %s' % (object.name, object.partner_id.name)"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.worksheet_custom_page
msgid "10 days"
msgstr "10 วัน"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.worksheet_custom_page
msgid "10:00"
msgstr "10:00"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.worksheet_custom_page
msgid "2023-01-01"
msgstr "2023-01-01"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.worksheet_custom_page
msgid "5 days"
msgstr "5 วัน"

#. module: industry_fsm
#: model:mail.template,body_html:industry_fsm.mail_template_data_intervention_details
msgid ""
"<div>\n"
"    <t t-set=\"date_begin\" t-value=\"format_datetime(object.planned_date_begin, tz=object.partner_id.tz, lang_code=object.partner_id.lang)\"/>\n"
"\n"
"    <t t-set=\"date_end\" t-value=\"format_datetime(object.date_deadline, tz=object.partner_id.tz, lang_code=object.partner_id.lang)\"/>\n"
"\n"
"    Dear <t t-out=\"object.partner_id.name or 'customer'\">customer</t>,<br/><br/>\n"
"    <t t-if=\"date_begin and date_end\">\n"
"        Your <t t-out=\"object.name or ''\">Boiler maintenance</t> intervention is scheduled from the <t t-out=\"date_begin or ''\">05/31/2021 12:30:00</t> to the <t t-out=\"date_end or ''\">05/31/2021 14:30:00</t>.\n"
"    </t>\n"
"    <t t-else=\"\">\n"
"        Your <t t-out=\"object.name or ''\">Boiler maintenance</t> intervention is scheduled.\n"
"    </t>\n"
"    <br/><br/>\n"
"    Best regards,\n"
"    <t t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"        <br/>\n"
"        <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"        "
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.portal_task_sign_button
msgid "<i class=\"fa fa-check me-1\"/>Sign Report"
msgstr "<i class=\"fa fa-check me-1\"/>ลงนามรายงาน"

#. module: industry_fsm
#: model:mail.template,body_html:industry_fsm.mail_template_data_task_report
msgid ""
"<p>\n"
"                Dear <t t-out=\"object.partner_id.name or 'Customer'\">Customer</t>,<br/><br/>\n"
"                Please find attached the field service report for our onsite operation. <br/><br/>\n"
"                Feel free to contact us if you have any questions.<br/><br/>\n"
"                Best regards,<br/><br/>\n"
"            </p>\n"
"        "
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
msgid ""
"<span class=\"fa fa-exclamation-triangle text-warning oe_edit_only\" "
"title=\"By saving this change, the customer contact number will also be "
"updated.\" invisible=\"not partner_id or not is_task_phone_update\"/>"
msgstr ""
"<span class=\"fa fa-exclamation-triangle text-warning oe_edit_only\" "
"title=\"เมื่อบันทึกการเปลี่ยนแปลงนี้ "
"หมายเลขติดต่อของลูกค้าก็จะได้รับการอัปเดตด้วย\" invisible=\"not partner_id "
"or not is_task_phone_update\"/>"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.worksheet_custom_page
msgid ""
"<span style=\"                             font-size: 10px;                             color: #fff;                             text-transform: uppercase;                             text-align: center;                             font-weight: bold; line-height: 20px;                             transform: rotate(45deg);                             width: 100px; height: auto; display: block;                             background: green;                             position: absolute;                             top: 19px; right: -21px; left: auto;                             padding: 0;\">\n"
"                            Signed\n"
"                        </span>"
msgstr ""
"<span style=\"                             font-size: 10px;                             color: #fff;                             text-transform: uppercase;                             text-align: center;                             font-weight: bold; line-height: 20px;                             transform: rotate(45deg);                             width: 100px; height: auto; display: block;                             background: green;                             position: absolute;                             top: 19px; right: -21px; left: auto;                             padding: 0;\">\n"
"                            ลงนามแล้ว\n"
"                        </span>"

#. module: industry_fsm
#: model_terms:web_tour.tour,rainbow_man_message:industry_fsm.industry_fsm_tour
msgid "<span><b>Good job!</b> You went through all steps of this tour.</span>"
msgstr "<span><b>ทำได้ดีมาก!</b> คุณได้ผ่านทุกขั้นตอนของทัวร์นี้แล้ว</span>"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.worksheet_custom_page
msgid "<strong class=\"me-2\">Total</strong>"
msgstr "<strong class=\"me-2\">ทั้งหมด</strong>"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_gantt_fsm
msgid "<strong>Contact Number — </strong>"
msgstr "<strong>เบอร์ติดต่อ — </strong>"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.worksheet_custom_page
msgid "<strong>Customer: </strong>"
msgstr "<strong>ลูกค้า:</strong>"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.worksheet_custom_page
msgid "<strong>Worker: </strong>"
msgstr "<strong>คนงาน: </strong>"

#. module: industry_fsm
#: model:ir.model.constraint,message:industry_fsm.constraint_project_project_company_id_required_for_fsm_project
msgid "A fsm project must be company restricted"
msgstr "โปรเจ็กต์ fsm จะต้องถูกจำกัดโดยบริษัท"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__active
msgid "Active"
msgstr "เปิดใช้งาน"

#. module: industry_fsm
#: model:ir.ui.menu,name:industry_fsm.mail_activity_plan_menu_config_task
msgid "Activity Plans"
msgstr "แผนกิจกรรม"

#. module: industry_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_config_activity_type
msgid "Activity Types"
msgstr "ประเภทกิจกรรม"

#. module: industry_fsm
#: model:res.groups,name:industry_fsm.group_fsm_manager
msgid "Administrator"
msgstr "ผู้ดูแลระบบ"

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_action_all_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_action_all_fsm2
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_all_tasks_root
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_all_tasks_todo
msgid "All Tasks"
msgstr "งานทั้งหมด"

#. module: industry_fsm
#: model:ir.actions.server,name:industry_fsm.project_task_all_fsm_server_action
msgid "All Tasks Server Action"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__allocated_hours
msgid "Allocated Time"
msgstr "เวลาที่ถูกจัดสรร"

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_user_action_report_fsm
msgid ""
"Analyze the progress of your tasks and the performance of your workers."
msgstr "วิเคราะห์ความคืบหน้าของงานและประสิทธิภาพของพนักงานของคุณ"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__user_ids
msgid "Assignees"
msgstr "ผู้รับมอบหมาย"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__date_assign
msgid "Assignment Date"
msgstr "วันที่มอบหมาย"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__rating_avg
msgid "Average Rating (1-5)"
msgstr "คะแนนเฉลี่ย (1-5)"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__dependent_ids
msgid "Block"
msgstr "บล็อก"

#. module: industry_fsm
#: model:ir.ui.menu,name:industry_fsm.project_task_menu_planning_by_location_fsm
msgid "By Location"
msgstr "ตามสถานที่ตั้ง"

#. module: industry_fsm
#: model:ir.ui.menu,name:industry_fsm.project_task_menu_planning_by_project_fsm
msgid "By Project"
msgstr "ตามโปรเจ็กต์"

#. module: industry_fsm
#: model:ir.ui.menu,name:industry_fsm.project_task_menu_planning_by_user_fsm
msgid "By User"
msgstr "โดยผู้ใช้"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__partner_city
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__partner_city
msgid "City"
msgstr "เมือง"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.portal_task_sign_modal
msgid "Close"
msgstr "ปิด"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__is_closed
msgid "Closed state"
msgstr "สถานะปิด"

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_res_company
msgid "Companies"
msgstr "บริษัท"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__company_id
msgid "Company"
msgstr "บริษัท"

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_res_config_settings
msgid "Config Settings"
msgstr "ตั้งค่าการกำหนดค่า"

#. module: industry_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_settings
msgid "Configuration"
msgstr "การกำหนดค่า"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_stop_timer_wizard_form
msgid "Confirm"
msgstr "ยืนยัน"

#. module: industry_fsm
#. odoo-javascript
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
msgid ""
"Confirm the <b>time spent</b> on your task. <i>Tip: note that the duration "
"has automatically been rounded to 15 minutes.</i>"
msgstr ""
"<b>ยืนยันเวลาที่ใช้ในงานของคุณ <i>เคล็ดลับ: "
"โปรดทราบว่าระยะเวลาจะถูกปัดเศษเป็น 15 นาทีโดยอัตโนมัติ</i>"

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_res_partner
msgid "Contact"
msgstr "ติดต่อ"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__partner_phone
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_map_view_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_mobile_form
msgid "Contact Number"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__partner_country_id
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__partner_country_id
msgid "Country"
msgstr "ประเทศ"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_project_view_form_simplified_footer_fsm
msgid "Create"
msgstr "สร้าง"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__create_date
msgid "Create Date"
msgstr "วันที่สร้าง"

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_project_task_create_timesheet
msgid "Create Timesheet from task"
msgstr "สร้างใบบันทึกเวลาจากงาน"

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.open_create_project_fsm
msgid "Create a Project"
msgstr "สร้างโปรเจ็กต์"

#. module: industry_fsm
#: model:res.groups,name:industry_fsm.group_fsm_quotation_from_task
msgid "Create new quotations directly from the tasks"
msgstr "สร้างใบเสนอราคาใหม่ได้โดยตรงจากงาน"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.res_config_settings_view_form
msgid "Create new quotations directly from your tasks"
msgstr "สร้างใบเสนอราคาใหม่ได้โดยตรงจากงานของคุณ"

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_project_action_only_fsm
msgid ""
"Create projects to organize your tasks and define a different workflow for "
"each project."
msgstr ""
"สร้างโปรเจ็กต์เพื่อจัดระเบียบงานของคุณและกำหนดขั้นตอนการทำงานที่แตกต่างกันสำหรับแต่ละโปรเจ็กต์"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard__create_uid
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard_line__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard__create_date
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard_line__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: industry_fsm
#. odoo-python
#: code:addons/industry_fsm/models/project_task.py:0
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__partner_id
msgid "Customer"
msgstr "ลูกค้า"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
msgid "Customer Preview"
msgstr "ตัวอย่างลูกค้า"

#. module: industry_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_reporting_customer_ratings
msgid "Customer Ratings"
msgstr "การให้คะแนนลูกค้า"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__partner_state_id
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__partner_state_id
msgid "Customer State"
msgstr "สถานะลูกค้า"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.worksheet_custom_page
msgid "Date"
msgstr "วันที่"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__delay_endings_days
msgid "Days to Deadline"
msgstr "วันถึงกำหนด"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__date_deadline
msgid "Deadline"
msgstr "วันครบกำหนด"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__description
#: model_terms:ir.ui.view,arch_db:industry_fsm.worksheet_custom_page
msgid "Description"
msgstr "คำอธิบาย"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.res_config_settings_view_form
msgid ""
"Design tailored worksheet templates for various interventions, and analyze "
"collected intervention data."
msgstr ""
"ออกแบบเทมเพลตแผ่นงานที่เหมาะสมสำหรับการแทรกแซงต่างๆ "
"และวิเคราะห์ข้อมูลการแทรกแซงที่รวบรวมไว้"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_project_view_form_simplified_footer_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_partner_address_form_industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_stop_timer_wizard_form
msgid "Discard"
msgstr "ละทิ้ง"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard__display_name
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard_line__display_name
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__display_name
msgid "Display Name"
msgstr "แสดงชื่อ"

#. module: industry_fsm
#: model:ir.model.fields,help:industry_fsm.field_project_project__is_fsm
#: model:ir.model.fields,help:industry_fsm.field_project_task__is_fsm
msgid ""
"Display tasks in the Field Service module and allow planning with start/end "
"dates."
msgstr ""
"แสดงงานในโมดูลบริการภาคสนาม และอนุญาตการวางแผนด้วยวันที่เริ่มต้น/สิ้นสุด"

#. module: industry_fsm
#. odoo-python
#: code:addons/industry_fsm/models/project_task.py:0
msgid "Do you want to stop the running timers?"
msgstr "คุณต้องการหยุดตัวจับเวลาที่ทำงานอยู่หรือไม่?"

#. module: industry_fsm
#: model:mail.template,description:industry_fsm.mail_template_data_task_report
msgid "Email sent when clicking on \"send report\" in a task"
msgstr "ส่งอีเมลเมื่อคลิกที่ \"ส่งรายงาน\" ในงาน"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__date_end
msgid "Ending Date"
msgstr "วันที่สิ้นสุด"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_res_config_settings__group_industry_fsm_quotations
msgid "Extra Quotations"
msgstr "ใบเสนอราคาเพิ่มเติม"

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_report_project_task_user_fsm
msgid "FSM Tasks Analysis"
msgstr "การวิเคราะห์งาน FSM"

#. module: industry_fsm
#. odoo-python
#: code:addons/industry_fsm/models/project_project.py:0
#: code:addons/industry_fsm/models/res_company.py:0
#: model:ir.model.fields,field_description:industry_fsm.field_project_project__is_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__is_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_root
#: model:project.project,name:industry_fsm.fsm_project
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_view_form_inherit
#: model_terms:ir.ui.view,arch_db:industry_fsm.res_config_settings_view_form
msgid "Field Service"
msgstr "การบริการภาคสนาม"

#. module: industry_fsm
#: model:ir.actions.report,name:industry_fsm.task_custom_report
msgid "Field Service Report"
msgstr "รายงานการบริการภาคสนาม"

#. module: industry_fsm
#: model:mail.template,subject:industry_fsm.mail_template_data_task_report
msgid "Field Service Report - {{ object.name }}"
msgstr "รายงานการบริการภาคสนาม - {{ object.name }}"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.worksheet_custom_page
msgid "Field Service Report:"
msgstr "รายงานการบริการภาคสนาม:"

#. module: industry_fsm
#: model:mail.template,name:industry_fsm.mail_template_data_task_report
msgid "Field Service: Field Service Report"
msgstr "บริการภาคสนาม: รายงานบริการภาคสนาม"

#. module: industry_fsm
#: model:mail.template,name:industry_fsm.mail_template_data_intervention_details
msgid "Field Service: Intervention Scheduled"
msgstr "บริการภาคสนาม: กำหนดการดำเนินการแล้ว"

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm_map
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm_map2
msgid "Find here your itinerary for today's tasks."
msgstr "ค้นหาแผนการเดินทางของคุณสำหรับงานวันนี้ได้ที่นี่"

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm2
msgid "Find here your upcoming tasks for the next few days."
msgstr "ค้นหางานที่จะเกิดขึ้นในอีกไม่กี่วันข้างหน้าของคุณที่นี่"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Future"
msgstr "อนาคต"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Future Activities"
msgstr "กิจกรรมในอนาคต"

#. module: industry_fsm
#. odoo-javascript
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
msgid ""
"Give it a <b>title</b> <i>(e.g. Boiler maintenance, Air-conditioning "
"installation, etc.).</i>"
msgstr ""
"ตั้งชื่อ <b>หัวเรื่อง</b> <i>(เช่น การซ่อมบำรุงหม้อไอน้ำ "
"การติดตั้งเครื่องปรับอากาศ ฯลฯ)</i>"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__total_hours_spent
msgid "Hours By Task (Including Subtasks)"
msgstr "ชั่วโมงตามงาน (รวมถึงงานย่อย)"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard__id
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard_line__id
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__id
msgid "ID"
msgstr "ไอดี"

#. module: industry_fsm
#: model:project.task.type,name:industry_fsm.planning_project_stage_2
msgid "In Progress"
msgstr "กำลังดำเนินการ"

#. module: industry_fsm
#. odoo-python
#: code:addons/industry_fsm/controllers/portal.py:0
msgid "Invalid Task."
msgstr "งานไม่ถูกต้อง"

#. module: industry_fsm
#. odoo-python
#: code:addons/industry_fsm/controllers/portal.py:0
msgid "Invalid signature data."
msgstr "ข้อมูลลายเซ็นไม่ถูกต้อง"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__message_is_follower
msgid "Is Follower"
msgstr "เป็นผู้ติดตาม"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.worksheet_custom_page
msgid "Jane Worker"
msgstr "Jane Worker"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.worksheet_custom_page
msgid "Jane smith"
msgstr "Jane smith"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.res_config_settings_view_form
msgid ""
"Keep track of the products used for your tasks, and invoice your time and "
"material to your customers"
msgstr ""
"ติดตามสินค้าที่ใช้สำหรับงานของคุณ "
"และออกใบแจ้งหนี้เวลาและวัสดุของคุณให้กับลูกค้าของคุณ"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__rating_last_value
msgid "Last Rating (1-5)"
msgstr "คะแนนล่าสุด (1-5)"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__date_last_stage_update
msgid "Last Stage Update"
msgstr "อัปเดตขั้นตอนสุดท้าย"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard__write_uid
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard_line__write_uid
msgid "Last Updated by"
msgstr "อัปเดตครั้งล่าสุดโดย"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard__write_date
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard_line__write_date
msgid "Last Updated on"
msgstr "อัปเดตครั้งล่าสุดเมื่อ"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Late Activities"
msgstr "กิจกรรมล่าสุด"

#. module: industry_fsm
#. odoo-javascript
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
msgid "Launch the timer to <b>track the time spent</b> on your task."
msgstr "เปิดตัวจับเวลาเพื่อ <b>ติดตามเวลาที่ใช้</b> ในงานของคุณ "

#. module: industry_fsm
#. odoo-javascript
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
msgid ""
"Let's <b>mark your task as done!</b> <i>Tip: when doing so, your stock will "
"automatically be updated, and your task will be closed.</i>"
msgstr ""
"<b>มาทำเครื่องหมายงานของคุณว่าเสร็จแล้ว!</b> <i>เคล็ดลับ: เมื่อดำเนินการ "
"สต็อกของคุณจะได้รับการอัปเดตโดยอัตโนมัติ และงานของคุณจะถูกปิด</i>"

#. module: industry_fsm
#. odoo-javascript
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
msgid "Let's create your first <b>task</b>."
msgstr "มาสร้าง <b>งานแรก</b> ของคุณไปด้วยกัน"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard__line_ids
msgid "Line"
msgstr "รายการ"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.worksheet_custom_page
msgid "Manage SME"
msgstr "การจัดการ SME"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_view_form_inherit
msgid "Manage tasks in the Field Service module"
msgstr "จัดการงานในโมดูลบริการภาคสนาม"

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_action_fsm_map
#: model:ir.actions.act_window,name:industry_fsm.project_task_action_fsm_map2
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_tasks_map
msgid "Map"
msgstr "แผนที่"

#. module: industry_fsm
#: model:ir.actions.server,name:industry_fsm.project_task_fsm_map_server_action
msgid "Map Server Action"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.worksheet_custom_page
msgid "Marc Demo"
msgstr "Marc Demo"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
msgid "Mark as done"
msgstr "ทำเครื่องหมายว่าเสร็จสิ้น"

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_ir_ui_menu
msgid "Menu"
msgstr "เมนู"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__milestone_id
msgid "Milestone"
msgstr "ไมล์สโตน"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_project__allow_milestones
msgid "Milestones"
msgstr "ไมล์สโตน"

#. module: industry_fsm
#. odoo-python
#: code:addons/industry_fsm/controllers/webmanifest.py:0
msgid "My Calendar"
msgstr ""

#. module: industry_fsm
#. odoo-python
#: code:addons/industry_fsm/controllers/webmanifest.py:0
#: model:ir.actions.act_window,name:industry_fsm.project_task_action_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_action_fsm2
#: model:ir.ui.menu,name:industry_fsm.fsm_tasks_menu
msgid "My Tasks"
msgstr "งานของฉัน"

#. module: industry_fsm
#: model:ir.actions.server,name:industry_fsm.project_task_fsm_server_action
msgid "My Tasks Server Action"
msgstr ""

#. module: industry_fsm
#: model:ir.actions.server,name:industry_fsm.project_task_fsm_mobile_server_action
msgid "My Tasks in mobile Server Action"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_partner_address_form_industry_fsm
msgid "Navigate To"
msgstr "นำไปยัง"

#. module: industry_fsm
#. odoo-python
#: code:addons/industry_fsm/controllers/webmanifest.py:0
msgid "New task"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_user_action_report_fsm
msgid "No data yet!"
msgstr "ยังไม่มีข้อมูล!"

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_project_action_only_fsm
msgid "No projects found. Let's create one!"
msgstr "ไม่พบโปรเจ็กต์ มาสร้างกันเถอะ!"

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_type_action_fsm
msgid "No stages found. Let's create one!"
msgstr "ไม่พบขั้นตอน มาสร้างกันเถอะ!"

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_all_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_all_fsm2
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm2
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm_map
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm_map2
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm_planning_groupby_location
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm_planning_groupby_location2
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm_planning_groupby_project
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm_planning_groupby_project2
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm_planning_groupby_user
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm_planning_groupby_user2
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_to_schedule_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_to_schedule_fsm2
#: model_terms:ir.actions.act_window,help:industry_fsm.project_tasks_action_fsm
msgid "No tasks found. Let's create one!"
msgstr "ไม่พบงาน มาสร้างกันเถอะ!"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__parent_id
msgid "Parent Task"
msgstr "งานหลัก"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__personal_stage_type_ids
msgid "Personal Stage"
msgstr "ขั้นตอนส่วนตัว"

#. module: industry_fsm
#: model:project.task.type,name:industry_fsm.planning_project_stage_1
msgid "Planned"
msgstr "วางแผน"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_list_fsm
msgid "Planned Date"
msgstr "วันที่ตามแผน"

#. module: industry_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_planning
msgid "Planning"
msgstr "การวางแผน"

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_action_fsm_planning_groupby_location
#: model:ir.actions.act_window,name:industry_fsm.project_task_action_fsm_planning_groupby_location2
msgid "Planning by Location"
msgstr "การวางแผนตามสถานที่ตั้ง"

#. module: industry_fsm
#: model:ir.actions.server,name:industry_fsm.project_task_fsm_planning_groupby_location_server_action
msgid "Planning by Location Server Action"
msgstr ""

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_action_fsm_planning_groupby_project
#: model:ir.actions.act_window,name:industry_fsm.project_task_action_fsm_planning_groupby_project2
msgid "Planning by Project"
msgstr "การวางแผนตามโปรเจ็กต์"

#. module: industry_fsm
#: model:ir.actions.server,name:industry_fsm.project_task_fsm_planning_groupby_project_server_action
msgid "Planning by Project Server Action"
msgstr ""

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_action_fsm_planning_groupby_user
#: model:ir.actions.act_window,name:industry_fsm.project_task_action_fsm_planning_groupby_user2
msgid "Planning by User"
msgstr "การวางแผนโดยผู้ใช้"

#. module: industry_fsm
#: model:ir.actions.server,name:industry_fsm.project_task_fsm_planning_groupby_user_server_action
msgid "Planning by User Action"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__priority
msgid "Priority"
msgstr "ระดับความสำคัญ"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__progress
msgid "Progress"
msgstr "ความคืบหน้า"

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_project_project
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__project_id
msgid "Project"
msgstr "โปรเจ็กต์"

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_project_action_only_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_settings_project
msgid "Projects"
msgstr "โปรเจ็กต์"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Properties"
msgstr "คุณสมบัติ"

#. module: industry_fsm
#. odoo-javascript
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
msgid ""
"Ready to <b>manage your onsite interventions</b>? <i>Click Field Service to "
"start.</i>"
msgstr ""
"พร้อมที่จะ <b>จัดการการดำเนินการนอกสถานที่ของคุณแล้วหรือยัง</b>? <i>คลิก "
"บริการภาคสนาม เพื่อเริ่ม</i>"

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_ir_actions_report
msgid "Report Action"
msgstr "การดําเนินการรายงาน"

#. module: industry_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_reporting
msgid "Reporting"
msgstr "การรายงาน"

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm_planning_groupby_location
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm_planning_groupby_location2
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm_planning_groupby_project
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm_planning_groupby_project2
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm_planning_groupby_user
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm_planning_groupby_user2
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_to_schedule_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_to_schedule_fsm2
msgid "Schedule your tasks and assign them to your workers."
msgstr "กำหนดเวลางานและมอบหมายให้กับพนักงานของคุณ"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_search
msgid "Search Planning"
msgstr "ค้นหาการวางแผน"

#. module: industry_fsm
#. odoo-javascript
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
msgid "Select the <b>customer</b> for your task."
msgstr "เลือก<b>ลูกค้า</b>สำหรับงานของคุณ"

#. module: industry_fsm
#. odoo-python
#: code:addons/industry_fsm/models/project_task.py:0
msgid "Send Field Service Report"
msgstr "ส่งรายงานการบริการภาคสนาม"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
msgid "Send Report"
msgstr "ส่งรายงาน"

#. module: industry_fsm
#: model:mail.template,description:industry_fsm.mail_template_data_intervention_details
msgid ""
"Set this template on a project's stage to automate email when tasks reach "
"stages"
msgstr ""
"ตั้งค่าเทมเพลตนี้บนขั้นตอนของโปรเจ็กต์เพื่อส่งอีเมลอัตโนมัติเมื่องานถึงขั้นตอนนั้น"

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.res_config_settings_action_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_settings_res_config
msgid "Settings"
msgstr "การตั้งค่า"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Show all records which has next action date is before today"
msgstr "แสดงระเบียนทั้งหมดที่มีวันที่ดำเนินการถัดไปคือก่อนวันนี้"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.portal_task_sign_modal
msgid "Sign Field Service Report"
msgstr "ลงชื่อรายงานการบริการภาคสนาม"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.portal_task_sign_modal
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
msgid "Sign Report"
msgstr "ลงชื่อรายงาน"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__worksheet_signature
#: model_terms:ir.ui.view,arch_db:industry_fsm.portal_my_task
#: model_terms:ir.ui.view,arch_db:industry_fsm.worksheet_custom_page
msgid "Signature"
msgstr "ลายเซ็น"

#. module: industry_fsm
#. odoo-python
#: code:addons/industry_fsm/controllers/portal.py:0
msgid "Signature is missing."
msgstr "ลายเซ็นหายไป"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__worksheet_signed_by
msgid "Signed By"
msgstr "เซ็นโดย"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__user_skill_ids
msgid "Skills"
msgstr "ทักษะ"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_stop_timer_wizard_form
msgid ""
"Some employees are still running their timer for this task. Are you sure you"
" want to continue?"
msgstr ""
"พนักงานบางคนยังคงจับเวลาสำหรับงานนี้ คุณแน่ใจหรือไม่ว่าต้องการดำเนินการต่อ?"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__stage_id
msgid "Stage"
msgstr "ขั้นตอน"

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_type_action_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_settings_stage
msgid "Stages"
msgstr "สถานะ"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Start Date"
msgstr "วันที่เริ่ม"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__planned_date_begin
msgid "Start date"
msgstr "วันเริ่มต้น"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__state
msgid "State"
msgstr "รัฐ"

#. module: industry_fsm
#. odoo-javascript
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
msgid "Stop the <b>timer</b> when you are done."
msgstr "หยุด<b>จับเวลา</b>เมื่อคุณทำเสร็จแล้ว"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__partner_street
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__partner_street
msgid "Street"
msgstr "ถนน"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__partner_street2
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__partner_street2
msgid "Street2"
msgstr "ถนน2"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_tree
msgid "Sum of Effective Hours"
msgstr "ผลรวมของชั่วโมงที่มีผลบังคับใช้"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__tag_ids
#: model:ir.ui.menu,name:industry_fsm.menu_project_tags_act
msgid "Tags"
msgstr "แท็ก"

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_project_task
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard_line__task_id
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__name
msgid "Task"
msgstr "งาน"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_project__allow_task_dependencies
msgid "Task Dependencies"
msgstr "การขึ้นต่อกันของงาน"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__fsm_done
msgid "Task Done"
msgstr "งานเสร็จสิ้น"

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_project_task_recurrence
msgid "Task Recurrence"
msgstr "การเกิดซ้ำของงาน"

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_project_task_type
msgid "Task Stage"
msgstr "ขั้นตอนงาน"

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_report_industry_fsm_worksheet_custom
msgid "Task Worksheet Custom Report"
msgstr "รายงานที่กำหนดเองของแผ่นงานงาน"

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_project_task_stop_timers_wizard
msgid "Task stop running timers confirmation wizard"
msgstr "โปรแกรมยืนยันการหยุดการทำงานของตัวจับเวลา"

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_project_task_stop_timers_wizard_line
msgid "Task stop running timers confirmation wizard line"
msgstr "รายการงานจะหยุดทำงานและรายการโปรแกรมยืนยันตัวจับเวลา"

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_tasks_action_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__task_id
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_tasks_kanban
#: model:project.project,label_tasks:industry_fsm.fsm_project
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_calendar_fsm
msgid "Tasks"
msgstr "งาน"

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_user_action_report_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_reporting_task_analysis
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_tree
msgid "Tasks Analysis"
msgstr "วิเคราะห์งาน"

#. module: industry_fsm
#. odoo-python
#: code:addons/industry_fsm/controllers/portal.py:0
msgid "The field service report has been signed by the customer."
msgstr "รายงานการบริการภาคสนามได้รับการลงนามโดยลูกค้า"

#. module: industry_fsm
#. odoo-python
#: code:addons/industry_fsm/models/ir_actions_report.py:0
msgid ""
"The field service report is unavailable for the selected tasks as they do "
"not contain any timesheets, products, or worksheets."
msgstr ""
"รายงานการบริการภาคสนามไม่พร้อมใช้งานสำหรับงานที่เลือก "
"เนื่องจากไม่มีใบบันทึกเวลา ผลิตภัณฑ์ หรือแผ่นงาน"

#. module: industry_fsm
#. odoo-python
#: code:addons/industry_fsm/controllers/portal.py:0
msgid "The worksheet is not in a state requiring customer signature."
msgstr "แผ่นงานไม่อยู่ในสถานะที่จำเป็นต้องมีลายเซ็นจากลูกค้า"

#. module: industry_fsm
#. odoo-python
#: code:addons/industry_fsm/models/project_task.py:0
msgid "There are no reports to send."
msgstr "ไม่มีรายงานที่จะส่ง"

#. module: industry_fsm
#. odoo-python
#: code:addons/industry_fsm/models/project_task.py:0
msgid "Time"
msgstr "เวลา"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__remaining_hours
msgid "Time Remaining"
msgstr "เวลาคงเหลือ"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__remaining_hours_percentage
msgid "Time Remaining Percentage"
msgstr "เปอร์เซ็นต์เวลาที่เหลือ"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__effective_hours
#: model_terms:ir.ui.view,arch_db:industry_fsm.timesheet_view_form
#: model_terms:ir.ui.view,arch_db:industry_fsm.worksheet_custom_page
msgid "Time Spent"
msgstr "เวลาที่ใช้ไป"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__subtask_effective_hours
msgid "Time Spent on Sub-Tasks"
msgstr "เวลาที่ใช้ในงานย่อย"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_res_config_settings__module_industry_fsm_sale
msgid "Time and Material Invoicing"
msgstr "การออกใบแจ้งหนี้เวลาและวัสดุ"

#. module: industry_fsm
#: model:ir.model.fields,help:industry_fsm.field_report_project_task_user_fsm__subtask_effective_hours
msgid "Time spent on the sub-tasks (and their own sub-tasks) of this task."
msgstr "เวลาที่ใช้ในงานย่อย (และงานย่อยของตัวเอง) ของงานนี้"

#. module: industry_fsm
#: model:ir.model.fields,help:industry_fsm.field_report_project_task_user_fsm__total_hours_spent
msgid "Time spent on this task, including its sub-tasks."
msgstr "เวลาที่ใช้ในงานนี้ รวมถึงงานย่อย"

#. module: industry_fsm
#. odoo-python
#: code:addons/industry_fsm/models/project_task.py:0
msgid "Timer started at: %(date)s %(time)s"
msgstr "ตัวจับเวลาเริ่มต้นที่: %(date)s %(time)s"

#. module: industry_fsm
#. odoo-python
#: code:addons/industry_fsm/wizard/project_task_create_timesheet.py:0
msgid "Timer stopped at: %(date)s %(time)s"
msgstr "ตัวจับเวลาหยุดที่: %(date)s %(time)s"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.timesheet_view_form
msgid "Timesheet"
msgstr "ใบบันทึกเวลา"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.worksheet_custom_page
msgid "Timesheets"
msgstr "ใบบันทึกเวลา"

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_action_to_schedule_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_action_to_schedule_fsm2
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_all_tasks_schedule
msgid "To Schedule"
msgstr "กำหนดเวลา"

#. module: industry_fsm
#: model:ir.actions.server,name:industry_fsm.project_task_to_schedule_fsm_server_action
msgid "To Schedule Server Action"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "To Schedule/Assign"
msgstr "เพื่อกำหนดเวลา/มอบหมาย"

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_all_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_all_fsm2
msgid ""
"To get things done, plan activities and use the task status.<br>\n"
"                Collaborate efficiently by chatting in real-time or via email."
msgstr ""
"หากต้องการทำงานให้เสร็จสิ้น ให้วางแผนกิจกรรมและใช้สถานะงาน<br>\n"
"                ทำงานร่วมกันอย่างมีประสิทธิภาพด้วยการแชทแบบเรียลไทม์หรือทางอีเมล"

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_tasks_action_fsm
msgid ""
"To get things done, use activities and status on tasks.<br>\n"
"                Chat in real-time or by email to collaborate efficiently."
msgstr ""
"หากต้องการทำงานให้เสร็จสิ้น ให้ใช้กิจกรรมและสถานะกับงาน<br>\n"
"                แชทแบบเรียลไทม์หรือทางอีเมลเพื่อทำงานร่วมกันอย่างมีประสิทธิภาพ"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Today"
msgstr "วันนี้"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Today Activities"
msgstr "กิจกรรมวันนี้"

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_type_action_fsm
msgid "Track the progress of your tasks from their creation to their closing."
msgstr "ติดตามความคืบหน้าของงานของคุณตั้งแต่การสร้างไปจนถึงการปิดงาน"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Unread Messages"
msgstr "ข้อความที่ยังไม่ได้อ่าน"

#. module: industry_fsm
#: model:res.groups,name:industry_fsm.group_fsm_user
msgid "User"
msgstr "ผู้ใช้"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
msgid "View Itinerary"
msgstr "ดูกำหนดการเดินทาง"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task_stop_timers_wizard_line__wizard_id
msgid "Wizard"
msgstr "ตัวช่วย"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.worksheet_custom_page
msgid "Worker"
msgstr "คนงาน"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__working_days_open
msgid "Working Days to Assign"
msgstr "วันทำการที่จะมอบหมาย"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__working_days_close
msgid "Working Days to Close"
msgstr "วันทำการเพื่อปิด"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__working_hours_open
msgid "Working Hours to Assign"
msgstr "ชั่วโมงการทำงานที่จะกำหนด"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__working_hours_close
msgid "Working Hours to Close"
msgstr "ชั่วโมงการทำงานเพื่อปิด"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_res_config_settings__module_industry_fsm_report
msgid "Worksheet Templates"
msgstr "เทมเพลตแผ่นงาน"

#. module: industry_fsm
#: model:mail.template,subject:industry_fsm.mail_template_data_intervention_details
msgid ""
"Your intervention is scheduled {{ object.planned_date_begin and "
"object.date_deadline and 'from the ' + "
"format_datetime(object.planned_date_begin, tz=object.partner_id.tz, "
"lang_code=object.partner_id.lang) + ' to the ' + "
"format_datetime(object.date_deadline, tz=object.partner_id.tz, "
"lang_code=object.partner_id.lang) or '' }}"
msgstr ""
"การดำเนินการของคุณถูกกำหนดไว้ตั้งแต่ {{ object.planned_date_begin and "
"object.date_deadline and 'from the ' + "
"format_datetime(object.planned_date_begin, tz=object.partner_id.tz, "
"lang_code=object.partner_id.lang) + ' to the ' + "
"format_datetime(object.date_deadline, tz=object.partner_id.tz, "
"lang_code=object.partner_id.lang) or '' }}"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__partner_zip
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__partner_zip
msgid "ZIP"
msgstr "รหัสไปรษณีย์"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.quick_create_task_form_fsm_inherited
msgid "e.g. Boiler replacement"
msgstr "เช่น การเปลี่ยนหม้อไอน้ำ"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
msgid "is FSM?"
msgstr "เป็น FSM?"

#. module: industry_fsm
#: model:ir.actions.server,name:industry_fsm.fsm_customer_ratings_server_action
msgid "project.project.fsm"
msgstr "project.project.fsm"
