# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* marketing_automation
# 
# Translators:
# ka<PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# <PERSON>, 2024
# <PERSON> <igor.shelu<PERSON><PERSON>@gmail.com>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON> <albena_viche<PERSON>@abv.bg>, 2024
# <PERSON> <<EMAIL>>, 2024
# Ивайло <PERSON>алин<PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON><PERSON>, 2024
# KeyVillage, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:51+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON>rastev, 2024\n"
"Language-Team: Bulgarian (https://app.transifex.com/odoo/teams/41243/bg/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bg\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_summary_template
msgid "\" <strong>bounced</strong>,"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_summary_template
msgid "\" did <strong>not receive a reply</strong>,"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_summary_template
msgid "\" gets clicked,"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_summary_template
msgid "\" was <strong>not opened</strong>,"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__link_tracker_click_count
msgid "# Clicks"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__mailing_filter_count
msgid "# Favorite Filters"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__mass_mailing_count
msgid "# Mailings"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__total_participant_count
msgid "# of active and completed participants"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__running_participant_count
msgid "# of active participants"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__completed_participant_count
msgid "# of completed participants"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__test_participant_count
msgid "# of test participants"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_summary_template
msgid "<br/>This activity will be"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "<i class=\"fa fa-check-circle\"/> Bounced"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "<i class=\"fa fa-check-circle\"/> Clicked"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "<i class=\"fa fa-check-circle\"/> Opened"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "<i class=\"fa fa-check-circle\"/> Replied"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid ""
"<i class=\"fa fa-clock-o pe-1\" role=\"img\" aria-label=\"Select time\" "
"title=\"Select time\"/>"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "<i class=\"fa fa-envelope-open-o\"/> Not opened within"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "<i class=\"fa fa-envelope-open-o\"/> Opened after"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "<i class=\"fa fa-exclamation-circle\"/> Bounced after"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "<i class=\"fa fa-hand-pointer-o\"/> Clicked after"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "<i class=\"fa fa-hand-pointer-o\"/> Not clicked within"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "<i class=\"fa fa-pie-chart\"/> Details"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "<i class=\"fa fa-plus-circle\"/> Add child activity"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "<i class=\"fa fa-reply\"/> Not replied within"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "<i class=\"fa fa-reply\"/> Replied after"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid ""
"<i data-trigger-type=\"activity\" class=\"fa fa-code-fork fa-rotate-180 fa-"
"flip-vertical o_ma_text_processed o_add_child_activity text-success\" "
"title=\"Add Another Activity\" role=\"img\" aria-label=\"Add Another "
"Activity\"/>"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_view_form
msgid "<span class=\"d-inline-block w-25\">after</span>"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "<span class=\"o_form_label\">Record</span>"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid ""
"<span role=\"img\" title=\"Graph\" aria-label=\"Graph\" class=\"o_ma_activity_tab active\" data-tab-type=\"graph\">\n"
"                                                            <i class=\"fa fa-pie-chart\"/>\n"
"                                                        </span>\n"
"                                                        <span title=\"Filter\" role=\"img\" aria-label=\"Filter\" class=\"o_ma_activity_tab\" data-tab-type=\"filter\">\n"
"                                                            <i class=\"fa fa-filter\"/>\n"
"                                                        </span>"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_kanban
msgid "<span>Completed</span>"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_kanban
msgid "<span>Running</span>"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_kanban
msgid "<span>Total</span>"
msgstr "<span>Общо</span>"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_summary_template
msgid ""
"<strong>Summary</strong><br/>\n"
"            Starting from"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "<strong>The workflow has been modified!</strong>"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__ab_testing_winner_mailing_id
msgid "A/B Campaign Winner Mailing"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__ab_testing_mailings_count
msgid "A/B Test Mailings #"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__ab_testing_completed
msgid "A/B Testing Campaign Finished"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__trace_ids
msgid "Actions"
msgstr "Действия"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__active
msgid "Active"
msgstr "Активно"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__marketing_activity_ids
#: model_terms:ir.ui.view,arch_db:marketing_automation.mailing_mailing_view_form_marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.mailing_mailing_view_tree_marketing_automation
msgid "Activities"
msgstr "Дейности"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__parent_id
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__activity_id
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_view_form
msgid "Activity"
msgstr "Дейност"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__activity_domain
msgid "Activity Filter"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_view_form
msgid "Activity Name"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__activity_summary
msgid "Activity Summary"
msgstr "Обобщение на Дейност"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__activity_type
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__activity_type
msgid "Activity Type"
msgstr "Вид дейност"

#. module: marketing_automation
#: model:ir.model.fields,help:marketing_automation.field_marketing_activity__domain
msgid ""
"Activity will only be performed if record satisfies this domain, obtained "
"from the combination of the activity filter and its inherited filter"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Add Hot Category"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Add Tag"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Add To Confirmed List"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Add new activity"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.mailing_mailing_view_form_marketing_activity
msgid "Add to Templates"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Add to list"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.mailing_mailing_view_form_marketing_activity
msgid "Advanced"
msgstr "Разширени"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "After 7 days"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,help:marketing_automation.field_marketing_activity__allowed_parent_ids
msgid "All activities which can be the parent of this one"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__social_post_ids
msgid "All related social media posts"
msgstr "Всички свързани публикации в социалните медии"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__allowed_parent_ids
msgid "Allowed parents"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,help:marketing_automation.field_marketing_campaign__is_auto_campaign
msgid "Allows us to filter relevant Campaigns"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__domain
msgid "Applied Filter"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_search
msgid "Archived"
msgstr "Архивиран"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid ""
"Are you sure you want to create a new participant for each matching record "
"that has not been used yet?"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.mailing_mailing_view_form_marketing_activity
msgid "Attach a file"
msgstr "Прикачи файл"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__is_auto_campaign
msgid "Automatically Generated Campaign"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid ""
"Be aware that participants that had no more activities could be reintroduced"
" into the campaign and new traces could be created for them."
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Blacklist Bounces"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Blacklist record"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Bounced"
msgstr "Отхвърлен"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__bounced_ratio
msgid "Bounced Ratio"
msgstr "Съотношение на отхвърлени спрямо неотхвърлени"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__campaign_id
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign_test__campaign_id
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__campaign_id
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_search
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_tree
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_search
msgid "Campaign"
msgstr "Кампании"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__name
msgid "Campaign Identifier"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__title
msgid "Campaign Name"
msgstr "Име на кампания"

#. module: marketing_automation
#: model:ir.actions.act_window,name:marketing_automation.marketing_campaign_action
#: model:ir.ui.menu,name:marketing_automation.marketing_campaign_menu
msgid "Campaigns"
msgstr "Кампании"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_test_view_form
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "Cancel"
msgstr "Отказ"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_view_form
msgid "Cancel after"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_trace__state__canceled
msgid "Cancelled"
msgstr "Отказан"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Check Bounce Contact"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Check Email Address"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,help:marketing_automation.field_marketing_activity__validity_duration
msgid ""
"Check this to make sure your actions are not executed after a specific "
"amount of time after the scheduled date. (e.g. Time-limited offer, Upcoming "
"event, …)"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__child_ids
msgid "Child Activities"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Clicked"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_tree
msgid "Clicks"
msgstr "Натискания на бутон"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__color
msgid "Color Index"
msgstr "Цветови индекс"

#. module: marketing_automation
#: model_terms:ir.actions.act_window,help:marketing_automation.marketing_participants_action_reporting
msgid ""
"Come back later once your campaigns are running to overview your "
"participants."
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Commercial prospection"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_participant__state__completed
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_search
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_trace_view_search
msgid "Completed"
msgstr "Завършен"

#. module: marketing_automation
#: model:ir.ui.menu,name:marketing_automation.marketing_automation_menu_configuration
msgid "Configuration"
msgstr "Конфигурация"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Confirmation"
msgstr "Потвърждение"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Confirmed contacts"
msgstr ""

#. module: marketing_automation
#. odoo-javascript
#: code:addons/marketing_automation/static/src/components/campaign_template_picker_dialog/campaign_template_picker_dialog.xml:0
msgid "Create Campaign"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.actions.act_window,help:marketing_automation.marketing_campaign_action
msgid "Create a Campaign"
msgstr "Създайте кампания"

#. module: marketing_automation
#. odoo-javascript
#: code:addons/marketing_automation/static/src/components/campaign_template_picker_dialog/campaign_template_picker_dialog.xml:0
msgid "Create a Marketing Automation Campaign"
msgstr ""

#. module: marketing_automation
#. odoo-javascript
#: code:addons/marketing_automation/static/src/components/campaign_template_picker_dialog/campaign_template_picker_dialog.xml:0
msgid "Create one or load a template prepared by our experts."
msgstr ""

#. module: marketing_automation
#: model_terms:ir.actions.act_window,help:marketing_automation.marketing_campaign_action
msgid ""
"Create one or load a template prepared by our experts.<br>\n"
"                            Then sit down and let Odoo handle the rest!"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__create_uid
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__create_uid
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign_test__create_uid
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__create_uid
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__create_uid
msgid "Created by"
msgstr "Създаден от"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__create_date
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__create_date
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign_test__create_date
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__create_date
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__create_date
msgid "Created on"
msgstr "Създадено на"

#. module: marketing_automation
#: model:ir.model.fields,help:marketing_automation.field_marketing_campaign__ab_testing_schedule_datetime
msgid ""
"Date that will be used to know when to determine and send the winner mailing"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__interval_type__days
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__validity_duration_type__days
msgid "Days"
msgstr "Дни"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__interval_type
msgid "Delay Type"
msgstr "Тип на забавяне"

#. module: marketing_automation
#. odoo-javascript
#: code:addons/marketing_automation/static/src/js/marketing_automation_one2many.js:0
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Delete"
msgstr "Изтрий"

#. module: marketing_automation
#. odoo-javascript
#: code:addons/marketing_automation/static/src/js/marketing_automation_one2many.js:0
msgid ""
"Deleting this activity will delete ALL its children activities. Are you "
"sure?"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Design your own marketing campaign from the ground up."
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__child_ids
msgid "Direct child traces"
msgstr ""

#. module: marketing_automation
#. odoo-javascript
#: code:addons/marketing_automation/static/src/components/campaign_template_picker_dialog/campaign_template_picker_dialog.xml:0
msgid "Discard"
msgstr "Отхвърлете"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__display_name
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__display_name
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign_test__display_name
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__display_name
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__display_name
msgid "Display Name"
msgstr "Име за показване"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__res_id
msgid "Document ID"
msgstr "ИН на документ"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_view_form
msgid "Domain"
msgstr "Домейн"

#. module: marketing_automation
#: model:ir.model.fields,help:marketing_automation.field_marketing_activity__activity_domain
msgid "Domain that applies to this activity and its child activities"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Don't update"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Double Opt-in"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__activity_type__email
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__mass_mailing_id_mailing_type__mail
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "Email"
msgstr "Имейл"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_activity.py:0
msgid "Email cancelled"
msgstr ""

#. module: marketing_automation
#: model:ir.model,name:marketing_automation.model_mail_compose_message
msgid "Email composition wizard"
msgstr "Помощник за създаване на имейли"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_activity.py:0
msgid "Email failed"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_trace__state__error
msgid "Error"
msgstr "Грешка"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__state_msg
msgid "Error message"
msgstr "Съобщение за грешка"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_activity.py:0
msgid "Error! You can't create recursive hierarchy of Activity."
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_activity.py:0
msgid "Exception in mass mailing: %s"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_activity.py:0
msgid "Exception in server action: %s"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_search
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_trace_view_search
msgid "Exclude Test"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_view_form
msgid "Expiry Duration"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__mailing_filter_id
msgid "Favorite Filter"
msgstr ""

#. module: marketing_automation
#: model:ir.ui.menu,name:marketing_automation.mailing_filter_menu_action_marketing_automaion
msgid "Favorite Filters"
msgstr "Любими филтри"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__mailing_filter_domain
msgid "Favorite filter domain"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__domain
msgid "Filter"
msgstr "Филтър"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Generate participants"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Get 10% OFF"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.actions.act_window,help:marketing_automation.marketing_trace_action
msgid ""
"Here you will be able to check the results of your mailings from all "
"Marketing Automation Campaigns."
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Hot"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__interval_type__hours
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__validity_duration_type__hours
msgid "Hours"
msgstr "Часове"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__id
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__id
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign_test__id
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__id
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__id
msgid "ID"
msgstr "ID"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__is_mailing_campaign_activated
msgid "Is Mailing Campaign Activated"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "It will be generated automatically once you save this record."
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Join partnership!"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__write_uid
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__write_uid
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign_test__write_uid
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__write_uid
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__write_uid
msgid "Last Updated by"
msgstr "Последно актуализирано от"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__write_date
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__write_date
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign_test__write_date
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__write_date
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__write_date
msgid "Last Updated on"
msgstr "Последно актуализирано на"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__last_sync_date
msgid "Last activities synchronization"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_test_view_form
msgid "Launch"
msgstr "Лансирайте"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/wizard/marketing_campaign_test.py:0
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Launch a Test"
msgstr ""

#. module: marketing_automation
#: model:ir.actions.act_window,name:marketing_automation.marketing_campaign_test_action
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_test_view_form
msgid "Launch a test"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__crm_lead_count
msgid "Leads/Opportunities count"
msgstr ""

#. module: marketing_automation
#: model:ir.actions.act_window,name:marketing_automation.link_tracker_action_marketing_campaign
msgid "Link Statistics"
msgstr ""

#. module: marketing_automation
#: model:ir.ui.menu,name:marketing_automation.link_tracker_menu_reporting_marketing_automation
msgid "Link Tracker"
msgstr "Link Tracker"

#. module: marketing_automation
#: model_terms:ir.actions.act_window,help:marketing_automation.link_tracker_action_marketing_campaign
msgid ""
"Link Trackers are created when mailings with links are sent to track how "
"many clicks they get."
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__links_click_datetime
msgid "Links Click Datetime"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__trigger_category__email
msgid "Mail"
msgstr "Поща"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.mailing_mailing_view_form_marketing_activity
#: model_terms:ir.ui.view,arch_db:marketing_automation.mailing_mailing_view_form_marketing_automation
msgid "Mail Body"
msgstr "Тяло на мейлинг"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.mailing_mailing_view_form_marketing_activity
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_view_form
msgid "Mail Template"
msgstr "шаблон за имейл"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__trigger_type__mail_bounce
msgid "Mail: bounced"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__trigger_type__mail_click
msgid "Mail: clicked"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__trigger_type__mail_not_click
msgid "Mail: not clicked"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__trigger_type__mail_not_open
msgid "Mail: not opened"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__trigger_type__mail_not_reply
msgid "Mail: not replied"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__trigger_type__mail_open
msgid "Mail: opened"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__trigger_type__mail_reply
msgid "Mail: replied"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.mailing_mailing_view_form_marketing_activity
msgid "Mailing"
msgstr "Мейлинг"

#. module: marketing_automation
#: model:ir.model,name:marketing_automation.model_mailing_trace
msgid "Mailing Statistics"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__mass_mailing_id_mailing_type
msgid "Mailing Type"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_tree
msgid "Mailings"
msgstr "Мейлинги"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/mailing_mailing.py:0
msgid ""
"Mailings %(mailing_names)s are used in marketing campaigns. You should take "
"care of this before unlinking the mailings."
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Mails sent and not bounced"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_trace.py:0
msgid "Manually"
msgstr "Механично"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_participant.py:0
msgid "Marked as completed"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Marketing"
msgstr "Маркетинг"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_mailing_mailing__marketing_activity_ids
msgid "Marketing Activities"
msgstr ""

#. module: marketing_automation
#: model:ir.model,name:marketing_automation.model_marketing_activity
#: model:ir.model.fields,field_description:marketing_automation.field_mail_compose_message__marketing_activity_id
msgid "Marketing Activity"
msgstr ""

#. module: marketing_automation
#: model:ir.module.category,name:marketing_automation.module_marketing_automation_category
#: model:ir.ui.menu,name:marketing_automation.marketing_automation_menu
msgid "Marketing Automation"
msgstr ""

#. module: marketing_automation
#: model:ir.actions.act_window,name:marketing_automation.mail_mass_mailing_action_marketing_automation
msgid "Marketing Automation Mailings"
msgstr ""

#. module: marketing_automation
#: model:ir.actions.server,name:marketing_automation.ir_cron_campaign_execute_activities_ir_actions_server
msgid "Marketing Automation: execute activities"
msgstr ""

#. module: marketing_automation
#: model:ir.actions.server,name:marketing_automation.ir_cron_campaign_sync_participants_ir_actions_server
msgid "Marketing Automation: sync participants"
msgstr ""

#. module: marketing_automation
#: model:ir.model,name:marketing_automation.model_marketing_campaign
msgid "Marketing Campaign"
msgstr "Маркетингова кампания"

#. module: marketing_automation
#: model:ir.model,name:marketing_automation.model_marketing_campaign_test
msgid "Marketing Campaign: Launch a Test"
msgstr ""

#. module: marketing_automation
#: model:ir.model,name:marketing_automation.model_marketing_participant
msgid "Marketing Participant"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__mass_mailing_id
msgid "Marketing Template"
msgstr ""

#. module: marketing_automation
#: model:ir.model,name:marketing_automation.model_marketing_trace
#: model:ir.model.fields,field_description:marketing_automation.field_mailing_trace__marketing_trace_id
msgid "Marketing Trace"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,help:marketing_automation.field_mailing_mailing__use_in_marketing_automation
msgid ""
"Marketing campaigns use mass mailings with some specific behavior; this "
"field is used to indicate its statistics may be suspicious."
msgstr ""

#. module: marketing_automation
#: model:ir.model,name:marketing_automation.model_mailing_mailing
msgid "Mass Mailing"
msgstr "Масов мейлинг"

#. module: marketing_automation
#: model:ir.model,name:marketing_automation.model_mailing_trace_report
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_trace_view_form
msgid "Mass Mailing Statistics"
msgstr "Статистика за масови мейлинги"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__mailing_mail_ids
msgid "Mass Mailings"
msgstr "Масови мейлинги"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__mailing_trace_ids
msgid "Mass mailing statistics"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Message for sales person"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Misc"
msgstr "Разни"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__model_id
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__model_id
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign_test__model_id
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__model_id
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_search
msgid "Model"
msgstr "Модел"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.ir_model_view_tree_marketing
msgid "Model Description"
msgstr "Описание на модела"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__model_name
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__model_name
msgid "Model Name"
msgstr "Име на модела"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__interval_type__months
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__validity_duration_type__months
msgid "Months"
msgstr "Месеци"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__name
msgid "Name"
msgstr "Име"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_campaign__state__draft
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_search
msgid "New"
msgstr "Нов"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Next activity: Check Email Address"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "No activity"
msgstr "Няма дейност"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "No activity for this campaign."
msgstr ""

#. module: marketing_automation
#: model_terms:ir.actions.act_window,help:marketing_automation.link_tracker_action_marketing_campaign
#: model_terms:ir.actions.act_window,help:marketing_automation.marketing_participants_action_reporting
#: model_terms:ir.actions.act_window,help:marketing_automation.marketing_trace_action
msgid "No data yet!"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Not Clicked"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Not Opened"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Not Replied"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "Not bounced yet"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "Not clicked yet"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "Not opened yet"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "Not replied yet"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__mailing_mail_count
msgid "Number of Mass Mailing"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__click_count
msgid "Number of clicks generated by the campaign"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__social_engagement
msgid ""
"Number of interactions (likes, shares, comments ...) with the social posts"
msgstr ""
"Брой взаимодействия (харесвания, споделяния, коментари ...) с публикациите в"
" социалните медии"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Offer free catalog"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Opened"
msgstr "Отворен"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__opened_ratio
msgid "Opened Ratio"
msgstr "Отворено съотношение"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.mailing_mailing_view_form_marketing_activity
msgid "Options"
msgstr "Настройки"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "Other activity"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__parent_id
msgid "Parent"
msgstr "Основен"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_trace.py:0
msgid "Parent activity mail bounced"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_trace.py:0
msgid "Parent activity mail clicked"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_trace.py:0
msgid "Parent activity mail opened"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_trace.py:0
msgid "Parent activity mail replied"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__participant_id
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_tree
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_trace_view_form
msgid "Participant"
msgstr "Участник"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_tree
msgid "Participant Name"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_graph
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_pivot
msgid "Participant summary"
msgstr ""

#. module: marketing_automation
#: model:ir.actions.act_window,name:marketing_automation.marketing_participant_action_campaign
#: model:ir.actions.act_window,name:marketing_automation.marketing_participant_action_campaign_test
#: model:ir.actions.act_window,name:marketing_automation.marketing_participants_action_mail
#: model:ir.actions.act_window,name:marketing_automation.marketing_participants_action_reporting
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__participant_ids
#: model:ir.ui.menu,name:marketing_automation.marketing_participants_menu
#: model_terms:ir.ui.view,arch_db:marketing_automation.mailing_mailing_view_form_marketing_activity
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_tree
msgid "Participants"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_activity.py:0
msgid "Participants of %(activity)s (%(filter)s)"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_view_form
msgid "Pick a Server Action..."
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "Pick a record..."
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_view_form
msgid "Pick or Create a Template..."
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_test_view_form
msgid "Pick or create a/an"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__processed
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_trace__state__processed
msgid "Processed"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "REJECTED"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__received_ratio
msgid "Received Ratio"
msgstr "Получено съотношение"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign_test__resource_ref
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__resource_ref
msgid "Record"
msgstr "Запис"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign_test__res_id
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__res_id
msgid "Record ID"
msgstr "ИН на запис"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_participant.py:0
msgid "Record deleted"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign_test__model_name
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__model_name
msgid "Record model"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_activity.py:0
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__rejected
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_trace__state__rejected
msgid "Rejected"
msgstr "Отхвърлен"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_activity.py:0
msgid "Rejected by activity filter or record deleted / archived"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Reload a favorite filter"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.mailing_mailing_view_form_marketing_activity
msgid "Remove from Templates"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_participant__state__unlinked
msgid "Removed"
msgstr "Премахнато"

#. module: marketing_automation
#: model:ir.model.fields,help:marketing_automation.field_marketing_participant__state
msgid "Removed means the related record does not exist anymore."
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Replied"
msgstr "Отговорени"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__replied_ratio
msgid "Replied Ratio"
msgstr "Съотношение на отговорените спрямо неотговорените"

#. module: marketing_automation
#: model:ir.ui.menu,name:marketing_automation.marketing_automation_reporting_menu
msgid "Reporting"
msgstr "Отчитане"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__require_sync
msgid "Require trace sync"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "Resource ID"
msgstr "ИН на ресурс"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_search
msgid "Resource Name"
msgstr "Име на ресурс"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__user_id
msgid "Responsible"
msgstr "Отговорен"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "Run"
msgstr "Стартирайте"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Run the next scheduled activity for each participant of this campaign?"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_campaign__state__running
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_participant__state__running
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_search
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_search
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_trace_view_search
msgid "Running"
msgstr "Работещ"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "SUCCESS"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__schedule_date
msgid "Schedule Date"
msgstr "Назначете дата"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_trace__state__scheduled
msgid "Scheduled"
msgstr "Насрочен"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_search
msgid "Search Campaign"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_search
msgid "Search Participant"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_trace_view_search
msgid "Search Traces"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,help:marketing_automation.field_marketing_campaign__ab_testing_winner_selection
msgid "Selection to determine the winner mailing that will be sent."
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Send 10% Welcome Discount"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__ab_testing_schedule_datetime
msgid "Send Final On"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Send Welcome Email"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Send a free catalog and follow-up according to reactions."
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Send a welcome email to contacts and tag them if they click in it."
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid ""
"Send a welcome email to new subscribers, remove the addresses that bounced."
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__interval_number
msgid "Send after"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__interval_standardized
msgid "Send after (in hours)"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Send an email to new recipients to confirm their consent."
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Sent"
msgstr "Изпратен"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__server_action_id
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__activity_type__action
msgid "Server Action"
msgstr "Действие на сървър"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__social_posts_count
msgid "Social Media Posts"
msgstr "Публикации в социалните медии"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid ""
"Some participants are already running on this campaign. Click on 'Update' to"
" apply the modifications you've just made."
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__source_id
msgid "Source"
msgstr "Източник"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_mailing_mailing__use_in_marketing_automation
msgid "Specific mailing used in marketing campaign"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__stage_id
msgid "Stage"
msgstr "Стадий"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Start"
msgstr "Стартирайте"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Start from scratch"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__state
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__state
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__state
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_search
msgid "State"
msgstr "Област"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "State: #{record.state.raw_value}"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__statistics_graph_data
msgid "Statistics Graph Data"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__mailing_trace_status
msgid "Status"
msgstr "Състояние"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "Stop"
msgstr "Край"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_campaign__state__stopped
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_search
msgid "Stopped"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_activity.py:0
msgid "Success"
msgstr "Успех"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid ""
"Switching Target Model invalidates the existing activities. Either update "
"your activity actions to match the new Target Model or delete them."
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__require_sync
msgid "Sync of participants is required"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Tag Hot Contacts"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__tag_ids
msgid "Tags"
msgstr "Маркери"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_search
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_tree
msgid "Target"
msgstr "Цел"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_tree
msgid "Target Model"
msgstr "Целеви модел"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Templates"
msgstr "Шаблони"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "Test"
msgstr "Тест"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__is_test
msgid "Test Record"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__is_test
msgid "Test Trace"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Tests"
msgstr "Тестове"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid ""
"The saved filter targets different model and is incompatible with this "
"campaign."
msgstr ""

#. module: marketing_automation
#. odoo-javascript
#: code:addons/marketing_automation/static/src/components/campaign_template_picker_dialog/campaign_template_picker_dialog.xml:0
msgid "Then sit down and let Odoo handle the rest!"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_activity.py:0
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid ""
"To use this feature you should be an administrator or belong to the "
"marketing automation group."
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__total_bounce
msgid "Total Bounce"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__total_click
msgid "Total Click"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__total_open
msgid "Total Open"
msgstr "Общо отворени"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__total_reply
msgid "Total Reply"
msgstr "Общо отговори"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__total_sent
msgid "Total Sent"
msgstr "Общо изпратени"

#. module: marketing_automation
#: model:ir.actions.act_window,name:marketing_automation.marketing_trace_action
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__trace_ids
#: model:ir.ui.menu,name:marketing_automation.marketing_trace_menu
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_trace_view_tree
msgid "Traces"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_view_form
msgid "Trigger"
msgstr "Тригер"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__trigger_category
msgid "Trigger Category"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__trigger_type
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__trigger_type
msgid "Trigger Type"
msgstr ""

#. module: marketing_automation
#: model:ir.model,name:marketing_automation.model_utm_campaign
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__utm_campaign_id
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__utm_campaign_id
msgid "UTM Campaign"
msgstr "UTM кампания"

#. module: marketing_automation
#: model:ir.model,name:marketing_automation.model_utm_source
msgid "UTM Source"
msgstr "UTM източник"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Unicity based on"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__unique_field_id
msgid "Unique Field"
msgstr "Уникално поле"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Update"
msgstr "Актуализирайте"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__use_leads
msgid "Use Leads"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,help:marketing_automation.field_marketing_campaign__unique_field_id
msgid ""
"Used to avoid duplicates based on model field.\n"
"e.g.\n"
"                For model 'Customers', select email field here if you don't\n"
"                want to process records which have the same email address"
msgstr ""

#. module: marketing_automation
#: model:res.groups,name:marketing_automation.group_marketing_automation_user
msgid "User"
msgstr "Потребител"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__validity_duration_number
msgid "Valid during"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__validity_duration
msgid "Validity Duration"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__validity_duration_type
msgid "Validity Duration Type"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Warning"
msgstr "Внимание"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__interval_type__weeks
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__validity_duration_type__weeks
msgid "Weeks"
msgstr "Седмици"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Welcome Flow"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Welcome!"
msgstr "Добре дошли!"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__ab_testing_winner_selection
msgid "Winner Selection"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "Workflow"
msgstr "Работен процес"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "Workflow Started On"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_activity.py:0
msgid ""
"You are trying to set the activity \"%(parent_activity)s\" as \"%(parent_type)s\" while its child \"%(activity)s\" has the trigger type \"%(trigger_type)s\"\n"
"Please modify one of those activities before saving."
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/utm_campaign.py:0
msgid ""
"You cannot delete these UTM Campaigns as they are linked to the following marketing campaigns in Marketing Automation:\n"
"%(campaign_names)s"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/utm_source.py:0
msgid ""
"You cannot delete these UTM Sources as they are linked to the following marketing activities in Marketing Automation:\n"
"%(activities_names)s"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "You must set up at least one activity to start this campaign."
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_summary_template
msgid "after the <strong>Mailing</strong> sent by the Activity \""
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_summary_template
msgid "after the <strong>beginning of the workflow</strong>,<br/>the"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_summary_template
msgid ""
"after the Participant <strong>clicked</strong>,<br/>on any link included in "
"the <strong>Mailing</strong> sent by the Activity \""
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_summary_template
msgid ""
"after the Participant <strong>opened</strong> the <strong>Mailing</strong> "
"sent by the Activity \""
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_summary_template
msgid ""
"after the Participant <strong>replied</strong> to the "
"<strong>Mailing</strong> sent by the Activity \""
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_summary_template
msgid "after the execution of the Activity \""
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__trigger_type__activity
msgid "another activity"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__trigger_type__begin
msgid "beginning of workflow"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_summary_template
msgid "cancelled, if"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_test_view_form
msgid "e.g. \"Brandon Freeman\""
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_view_form
msgid "e.g. eCommerce Offers"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "e.g. eCommerce Offers Plan"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_summary_template
msgid "have passed since the scheduled date."
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_summary_template
msgid ""
"if no link included in the <strong>Mailing</strong> sent by the Activity \""
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_summary_template
msgid "if the <strong>Mailing</strong> sent by the Activity \""
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_summary_template
msgid "run"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_summary_template
msgid "sent"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_summary_template
msgid "the"
msgstr "-"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_test_view_form
msgid "to generate a Test Participant"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_summary_template
msgid "will be"
msgstr ""

#. module: marketing_automation
#. odoo-javascript
#: code:addons/marketing_automation/static/src/components/campaign_template_picker_dialog/campaign_template_picker_dialog.xml:0
msgid "{{template_value.title}}"
msgstr ""
