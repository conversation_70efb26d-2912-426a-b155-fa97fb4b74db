# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* spreadsheet_dashboard_stock
# 
# Translators:
# Wil Odoo, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 09:15+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Czech (https://app.transifex.com/odoo/teams/41243/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n <= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "All / Rental"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "All / Saleable"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "All / Saleable / Office Furniture"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Amazon"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Apple Pie"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Avg  Delay"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Avg Cycle Time"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Bagel"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "BlueWave Solar"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "Bottom"
msgstr "Spodek"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Cabinet with Doors"
msgstr "Skříň s dveřmi"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "Categories"
msgstr "Kategorie"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_dashboard.json:0
msgid "Contact"
msgstr "Kontakt"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "Count"
msgstr "Počet"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Count orders on time"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Current"
msgstr "Aktuální"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "Current period"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "Current period reworked"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Customer"
msgstr "Zákazník"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Cycle Time (Days)"
msgstr "Čas cyklu (dny)"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "Cycle time Current period"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Delay (Days)"
msgstr "Zpoždění (dny)"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Deliveries next 7 days"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Delivery Orders"
msgstr "Příkaz dodání"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Demand"
msgstr "Poptávka"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Departmental Transfer"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_dashboard.json:0
msgid "Destination locations"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Drawer"
msgstr "Šuplík"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "Dynamic fields for filters fill rate"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "Dynamic fields for on time"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Elisabeth Wild"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Fill rate"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "Fill rate sort by "
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Fill rate sort by Top Demand"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "Filter"
msgstr "Filtr"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Internal Transfers"
msgstr "Interní převody"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Internal lead time"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "John Stones"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Julia Denver"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "KPI"
msgstr "KPI"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Late deliveries"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Late internal transfer"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Late internal transfers"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Late receptions"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Location Transfer"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Manufacturing"
msgstr "Výroba"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Moves lines count by operation"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Multigrain Bread"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Nvidia"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Office Chair Black"
msgstr "Kancelářská židle černá"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "On Time"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "On Time delivery"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "On time"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "On time deliveries"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "On time rate"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "On time rate sort by "
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "On time rate sort by Top Demand"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Open deliveries to date"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Open internal transfers to date"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Open late deliveries"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Open late internal transfers"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Open late receipts"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Open receptions to date"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Open transfers to date"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "OpenAI"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "Operation"
msgstr "Úkon"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_dashboard.json:0
msgid "Operation Type"
msgstr "Typ operace"

#. module: spreadsheet_dashboard_stock
#: model:spreadsheet.dashboard,name:spreadsheet_dashboard_stock.spreadsheet_dashboard_inventory_flow
msgid "Operation analysis"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "Operation name"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "Operations"
msgstr "Operace"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "Orders count"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Pack"
msgstr "Sada"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Patagonia"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "Period"
msgstr "Období"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Pick"
msgstr "Dodání"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "Pivot to choose"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "PoS Orders"
msgstr "Objednávky prod. místa"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Previous"
msgstr "Předchozí"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "Previous period"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "Previous period reworked"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_dashboard.json:0
msgid "Product"
msgstr "Produkt"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "Product Category"
msgstr "Produktová kategorie"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Qty scrapped product by category"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Quantity"
msgstr "Množství"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Quantity of stock adjustments by category"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Receipts"
msgstr "Příjemky"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Receptions next 7 days"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Repairs"
msgstr "Opravy"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Responsible"
msgstr "Odpovědný"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Rivian"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "Row"
msgstr "Řádek"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Scheduled on"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "Selected filter"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Slack Technologies"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "Sort graph by"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_dashboard.json:0
msgid "Source location"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Storage"
msgstr "Úložiště"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Test Batch Product"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "Top"
msgstr "Nahoru"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "Top/Bottoms values"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "Total count"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Total orders"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Transfer"
msgstr "Převod"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Transfer to be assigned"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Transfers count by responsible and operation"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Vendor"
msgstr "Dodavatel"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "Warehouse"
msgstr "Sklad"

#. module: spreadsheet_dashboard_stock
#: model:spreadsheet.dashboard,name:spreadsheet_dashboard_stock.spreadsheet_dashboard_warehouse_operations
msgid "Warehouse Daily Operations"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Warehouse-to-Warehouse Transfer"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Weekly Stock Moves Lines by operation"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "[E-COM11] Cabinet with Doors"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "[FURN_0269] Office Chair Black"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "[FURN_8855] Drawer"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "last period"
msgstr ""
