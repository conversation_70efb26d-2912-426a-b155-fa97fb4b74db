# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* spreadsheet_dashboard_stock
# 
# Translators:
# Wil Odoo, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 09:15+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Spanish (Latin America) (https://app.transifex.com/odoo/teams/41243/es_419/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_419\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "All / Rental"
msgstr "Todos / Alquiler"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "All / Saleable"
msgstr "Todos / Se puede vender"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "All / Saleable / Office Furniture"
msgstr "Todos / Se puede vender / Muebles de oficina"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Amazon"
msgstr "Amazon"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Apple Pie"
msgstr "Tarta de manzana"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Avg  Delay"
msgstr "Atrasos promedios"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Avg Cycle Time"
msgstr "Tiempo del ciclo promedio"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Bagel"
msgstr "Bagel"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "BlueWave Solar"
msgstr "Panel solar BlueWave"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "Bottom"
msgstr "Inferior"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Cabinet with Doors"
msgstr "Gabinete con puertas"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "Categories"
msgstr "Categorías"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_dashboard.json:0
msgid "Contact"
msgstr "Contacto"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "Count"
msgstr "Número"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Count orders on time"
msgstr "Conteo de órdenes a tiempo"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Current"
msgstr "Actual"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "Current period"
msgstr "Periodo actual"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "Current period reworked"
msgstr "Periodo actual rediseñado"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Customer"
msgstr "Cliente"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Cycle Time (Days)"
msgstr "Tiempo del ciclo (días) "

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "Cycle time Current period"
msgstr "Tiempo del ciclo del periodo en curso"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Delay (Days)"
msgstr "Retraso (días)"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Deliveries next 7 days"
msgstr "Entregas en los siguientes 7 días"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Delivery Orders"
msgstr "Órdenes de entrega"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Demand"
msgstr "Demanda"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Departmental Transfer"
msgstr "Transferencia departamental"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_dashboard.json:0
msgid "Destination locations"
msgstr "Ubicación de destino"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Drawer"
msgstr "Cajón"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "Dynamic fields for filters fill rate"
msgstr "Campos dinámicos para la tasa de llenado de filtros"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "Dynamic fields for on time"
msgstr "Campos dinámicos por a tiempo"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Elisabeth Wild"
msgstr "Elisabeth Wild"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Fill rate"
msgstr "Tasa de cumplimiento"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "Fill rate sort by "
msgstr "Ordenar por tasa de cumplimiento"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Fill rate sort by Top Demand"
msgstr "Ordenar tasa de cumplimiento por demanda principal"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "Filter"
msgstr "Filtro"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Internal Transfers"
msgstr "Traslados internos"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Internal lead time"
msgstr "Plazo de entrega interno"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "John Stones"
msgstr "John Stones"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Julia Denver"
msgstr "Julia Denver"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "KPI"
msgstr "KPI"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Late deliveries"
msgstr "Entregas tardías"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Late internal transfer"
msgstr "Transferencia interna tardía"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Late internal transfers"
msgstr "Transferencias internas tardías"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Late receptions"
msgstr "Recepciones tardías"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Location Transfer"
msgstr "Transferencia de ubicación"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Manufacturing"
msgstr "Fabricación"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Moves lines count by operation"
msgstr "Conteo de líneas de movimiento por operación"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Multigrain Bread"
msgstr "Pan multigrano"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Nvidia"
msgstr "Nvidia"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Office Chair Black"
msgstr "Silla de oficina negra"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "On Time"
msgstr "A tiempo"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "On Time delivery"
msgstr "Entrega a tiempo"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "On time"
msgstr "A tiempo"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "On time deliveries"
msgstr "Entregas a tiempo"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "On time rate"
msgstr "Tasa de entregar a tiempo"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "On time rate sort by "
msgstr "Ordenar por tasa de entregas a tiempo"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "On time rate sort by Top Demand"
msgstr "Ordenar la tasa de entregas a tiempo por demanda principal"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Open deliveries to date"
msgstr "Abrir entregas a la fecha"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Open internal transfers to date"
msgstr "Abrir transferencias internas a la fecha"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Open late deliveries"
msgstr "Abrir entregas tardías"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Open late internal transfers"
msgstr "Abrir transferencias internas tardías"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Open late receipts"
msgstr "Abrir recepciones tardías"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Open receptions to date"
msgstr "Abrir recepciones a la fecha"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Open transfers to date"
msgstr "Abrir transferencias a la fecha"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "OpenAI"
msgstr "OpenAI"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "Operation"
msgstr "Operación"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_dashboard.json:0
msgid "Operation Type"
msgstr "Tipo de operación"

#. module: spreadsheet_dashboard_stock
#: model:spreadsheet.dashboard,name:spreadsheet_dashboard_stock.spreadsheet_dashboard_inventory_flow
msgid "Operation analysis"
msgstr "Análisis de operación"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "Operation name"
msgstr "Nombre de la operación"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "Operations"
msgstr "Operaciones"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "Orders count"
msgstr "Conteo de las órdenes"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Pack"
msgstr "Empaquetar"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Patagonia"
msgstr "Patagonia"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "Period"
msgstr "Periodo"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Pick"
msgstr "Recolectar"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "Pivot to choose"
msgstr "Tabla dinámica por elegir"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "PoS Orders"
msgstr "Órdenes de PdV"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Previous"
msgstr "Anterior"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "Previous period"
msgstr "Periodo anterior"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "Previous period reworked"
msgstr "Periodo previo rediseñado"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_dashboard.json:0
msgid "Product"
msgstr "Producto"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "Product Category"
msgstr "Categoría del producto"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Qty scrapped product by category"
msgstr "Cantidad de productos desechados por categoria"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Quantity"
msgstr "Cantidad"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Quantity of stock adjustments by category"
msgstr "Cantidad de ajustes de inventario por categoría"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Receipts"
msgstr "Recepciones"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Receptions next 7 days"
msgstr "Recepciones en los próximos 7 días"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Repairs"
msgstr "Reparación"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Responsible"
msgstr "Responsable"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Rivian"
msgstr "Rivian"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "Row"
msgstr "Fila"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Scheduled on"
msgstr "Programado el"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "Selected filter"
msgstr "Filtro seleccionado"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Slack Technologies"
msgstr "Slack Technologies"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "Sort graph by"
msgstr "Ordenar gráfico por"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_dashboard.json:0
msgid "Source location"
msgstr "Ubicación origen"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Storage"
msgstr "Almacenamiento"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Test Batch Product"
msgstr "Prueba por lotes de productos"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "Top"
msgstr "Arriba"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "Top/Bottoms values"
msgstr "Valores superiores/inferiores"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "Total count"
msgstr "Conteo total"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Total orders"
msgstr "Órdenes principales"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Transfer"
msgstr "Transferir "

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Transfer to be assigned"
msgstr "Transferencias por asignar"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Transfers count by responsible and operation"
msgstr "Conteo de transferencias por responsable y operación"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Vendor"
msgstr "Proveedor"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
msgid "Warehouse"
msgstr "Almacén"

#. module: spreadsheet_dashboard_stock
#: model:spreadsheet.dashboard,name:spreadsheet_dashboard_stock.spreadsheet_dashboard_warehouse_operations
msgid "Warehouse Daily Operations"
msgstr "Operaciones diarias del almacén"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/warehouse_operations_sample_dashboard.json:0
msgid "Warehouse-to-Warehouse Transfer"
msgstr "Transferencia de almacén a almacén"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "Weekly Stock Moves Lines by operation"
msgstr "Líneas de movimientos semanales de Stock por operación"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "[E-COM11] Cabinet with Doors"
msgstr "[E-COM11] Gabinete con puertas"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "[FURN_0269] Office Chair Black"
msgstr "[FURN_0269] Silla de oficina negra"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "[FURN_8855] Drawer"
msgstr "[FURN_8855] Cajón"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/operation_analysis_sample_dashboard.json:0
msgid "last period"
msgstr "último periodo"
