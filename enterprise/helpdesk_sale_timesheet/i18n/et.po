# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* helpdesk_sale_timesheet
# 
# Translators:
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:27+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Anna, 2024\n"
"Language-Team: Estonian (https://app.transifex.com/odoo/teams/41243/et/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: et\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: helpdesk_sale_timesheet
#: model:helpdesk.sla,name:helpdesk_sale_timesheet.helpdesk_sla_4
msgid "4 hours to finish"
msgstr "4 tundi lõpetamiseni"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_sale_timesheet
msgid ""
"<span class=\"fa fa-exclamation-triangle text-warning\" title=\"The sales "
"order associated with this ticket has been cancelled. We recommend either "
"updating the sales order item or cancelling this ticket in alignment with "
"the cancellation of the sales order.\" invisible=\"sale_order_state != "
"'cancel'\"/>"
msgstr ""

#. module: helpdesk_sale_timesheet
#: model:ir.model,name:helpdesk_sale_timesheet.model_account_analytic_line
msgid "Analytic Line"
msgstr "Analüütiline rida"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_sale_order_line__color
msgid "Color"
msgstr "Värv"

#. module: helpdesk_sale_timesheet
#: model:ir.model,name:helpdesk_sale_timesheet.model_helpdesk_ticket_convert_wizard
msgid "Convert Helpdesk Tickets to Tasks"
msgstr "Muuda kasutajatoe piletid ülesanneteks"

#. module: helpdesk_sale_timesheet
#: model:ir.model,name:helpdesk_sale_timesheet.model_project_task_convert_wizard
msgid "Convert Project Tasks to Tickets"
msgstr "Muuda projekti ülesanded piletiteks"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.tickets_followup_timesheet
msgid "Date"
msgstr "Kuupäev"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.tickets_followup_timesheet
msgid "Description"
msgstr "Kirjeldus"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.tickets_followup_timesheet
msgid "Employee"
msgstr "Töötaja"

#. module: helpdesk_sale_timesheet
#: model:ir.model,name:helpdesk_sale_timesheet.model_helpdesk_sla
msgid "Helpdesk SLA Policies"
msgstr "Kasutajatoe lubatud teenuse tingimused"

#. module: helpdesk_sale_timesheet
#: model:ir.model,name:helpdesk_sale_timesheet.model_helpdesk_team
msgid "Helpdesk Team"
msgstr "Kasutajatoe meeskond"

#. module: helpdesk_sale_timesheet
#: model:ir.model,name:helpdesk_sale_timesheet.model_helpdesk_ticket
msgid "Helpdesk Ticket"
msgstr "Kasutajatoe pilet"

#. module: helpdesk_sale_timesheet
#. odoo-python
#: code:addons/helpdesk_sale_timesheet/models/helpdesk_ticket.py:0
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_sale_timesheet
msgid "Invoices"
msgstr "Arved"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.portal_my_timesheets_inherit
msgid "No Ticket"
msgstr "Pilet puudub"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_sale_timesheet
msgid "Non-billable"
msgstr "Mittearveldatav"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.portal_helpdesk_ticket_inherit
msgid "Not Billed"
msgstr "Arveldamata"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.helpdesk_sla_view_form_inherit_sale_timesheet
msgid "OR"
msgstr "või"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_team__project_id
msgid "Project"
msgstr "Projektid"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,help:helpdesk_sale_timesheet.field_helpdesk_team__project_id
msgid ""
"Project to which the timesheets of this helpdesk team's tickets will be "
"linked."
msgstr "Projekt, millega seotakse tööaja arvestusleht kasutajatoe piletilt."

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_ticket__sale_order_id
msgid "Ref. Sales Order"
msgstr "Müügitellimuse viide"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_ticket__use_helpdesk_sale_timesheet
msgid "Reinvoicing Timesheet activated on Team"
msgstr "Ajaarvestuse arveldamine on meeskonnas aktiveeritud."

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_sla_report_analysis__remaining_hours_so
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_ticket_report_analysis__remaining_hours_so
msgid "Remaining Hours on SO"
msgstr "Alles jäänud tunde müügitellimusel"

#. module: helpdesk_sale_timesheet
#: model:ir.model,name:helpdesk_sale_timesheet.model_helpdesk_sla_report_analysis
msgid "SLA Status Analysis"
msgstr "Teenuses lubatu täitmise analüüs"

#. module: helpdesk_sale_timesheet
#. odoo-python
#: code:addons/helpdesk_sale_timesheet/controllers/portal.py:0
#: code:addons/helpdesk_sale_timesheet/models/helpdesk_ticket.py:0
#: model:ir.model,name:helpdesk_sale_timesheet.model_sale_order
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_sale_timesheet
msgid "Sales Order"
msgstr "Müügitellimus"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_sla_report_analysis__sale_line_id
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_ticket__sale_line_id
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_ticket_report_analysis__sale_line_id
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.tickets_followup_timesheet
msgid "Sales Order Item"
msgstr "Müügiartikkel"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,help:helpdesk_sale_timesheet.field_helpdesk_ticket__sale_line_id
msgid ""
"Sales Order Item to which the time spent on this ticket will be added in order to be invoiced to your customer.\n"
"By default the last prepaid sales order item that has time remaining will be selected.\n"
"Remove the sales order item in order to make this ticket non-billable.\n"
"You can also change or remove the sales order item of each timesheet entry individually."
msgstr ""
"Müügitellimuse artikkel, millele lisatakse sellele piletile kulunud aeg, et kliendile arveldada.\n"
"Vaikimisi valitakse viimane ettemakstud müügitellimuse artikkel, millel on aega järele jäänud.\n"
"Selle pileti mittearveldamiseks eemaldage müügitellimuse artikkel.\n"
"Samuti saate muuta või eemaldada iga tööajaarvestuse kirje müügitellimuse artiklit eraldi."

#. module: helpdesk_sale_timesheet
#: model:ir.model,name:helpdesk_sale_timesheet.model_sale_order_line
msgid "Sales Order Line"
msgstr "Müügitellimuse rida"

#. module: helpdesk_sale_timesheet
#. odoo-python
#: code:addons/helpdesk_sale_timesheet/controllers/portal.py:0
msgid "Search in Sales Order"
msgstr "Otsi müügitellimusest"

#. module: helpdesk_sale_timesheet
#. odoo-python
#: code:addons/helpdesk_sale_timesheet/controllers/portal.py:0
msgid "Search in Ticket"
msgstr "Otsi piletist"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_sla__product_ids
msgid "Services"
msgstr "Teenused"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.portal_my_timesheets_inherit
msgid "Task/Ticket"
msgstr "Ülesanne/Pilet"

#. module: helpdesk_sale_timesheet
#. odoo-python
#: code:addons/helpdesk_sale_timesheet/controllers/portal.py:0
#: code:addons/helpdesk_sale_timesheet/models/sale_order.py:0
msgid "Ticket"
msgstr "Pilet"

#. module: helpdesk_sale_timesheet
#: model:ir.model,name:helpdesk_sale_timesheet.model_helpdesk_ticket_report_analysis
msgid "Ticket Analysis"
msgstr "Piletite analüüs"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_sale_order__ticket_count
msgid "Ticket Count"
msgstr "Kasutajatoe piletite arv"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.sale_order_form_inherit_helpdesk_sale
msgid "Tickets"
msgstr "Piletid"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_sla__use_helpdesk_sale_timesheet
msgid "Time Billing"
msgstr "Aja arveldamine"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_ticket__remaining_hours_so
msgid "Time Remaining on SO"
msgstr "Järelejäänud aeg müügitellimusel"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.tickets_followup_timesheet
msgid "Time Remaining on SO:"
msgstr ""

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.portal_helpdesk_ticket_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.tickets_followup_timesheet
msgid "Time Spent"
msgstr "Kulutatud aeg"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.tickets_followup_timesheet
msgid "Timesheets"
msgstr "Tööaja arvestusleht"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.tickets_followup_timesheet
msgid "Total Time Spent:"
msgstr "Kokku kulutatud aeg"

#. module: helpdesk_sale_timesheet
#. odoo-python
#: code:addons/helpdesk_sale_timesheet/models/helpdesk_ticket.py:0
msgid ""
"You cannot link order item %(order_id)s - %(product_id)s to this ticket "
"because it is not a service product."
msgstr ""
