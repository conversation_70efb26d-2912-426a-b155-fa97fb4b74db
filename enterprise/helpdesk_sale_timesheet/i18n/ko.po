# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* helpdesk_sale_timesheet
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:27+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: helpdesk_sale_timesheet
#: model:helpdesk.sla,name:helpdesk_sale_timesheet.helpdesk_sla_4
msgid "4 hours to finish"
msgstr "완료까지 4시간"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_sale_timesheet
msgid ""
"<span class=\"fa fa-exclamation-triangle text-warning\" title=\"The sales "
"order associated with this ticket has been cancelled. We recommend either "
"updating the sales order item or cancelling this ticket in alignment with "
"the cancellation of the sales order.\" invisible=\"sale_order_state != "
"'cancel'\"/>"
msgstr ""
"<span class=\"fa fa-exclamation-triangle text-warning\" title=\"이 티켓과 관련된 판매"
" 주문이 취소되었습니다. 판매 주문 항목을 업데이트하거나 판매 주문 취소에 맞춰 이 티켓을 취소하는 것이 좋습니다.\" "
"invisible=\"sale_order_state != 'cancel'\"/>"

#. module: helpdesk_sale_timesheet
#: model:ir.model,name:helpdesk_sale_timesheet.model_account_analytic_line
msgid "Analytic Line"
msgstr "분석 라인"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_sale_order_line__color
msgid "Color"
msgstr "색상"

#. module: helpdesk_sale_timesheet
#: model:ir.model,name:helpdesk_sale_timesheet.model_helpdesk_ticket_convert_wizard
msgid "Convert Helpdesk Tickets to Tasks"
msgstr "헬프데스크 티켓을 작업으로 전환"

#. module: helpdesk_sale_timesheet
#: model:ir.model,name:helpdesk_sale_timesheet.model_project_task_convert_wizard
msgid "Convert Project Tasks to Tickets"
msgstr "프로젝트 작업을 티켓으로 전환"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.tickets_followup_timesheet
msgid "Date"
msgstr "날짜"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.tickets_followup_timesheet
msgid "Description"
msgstr "설명"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.tickets_followup_timesheet
msgid "Employee"
msgstr "임직원"

#. module: helpdesk_sale_timesheet
#: model:ir.model,name:helpdesk_sale_timesheet.model_helpdesk_sla
msgid "Helpdesk SLA Policies"
msgstr "고객센터 SLA 정책"

#. module: helpdesk_sale_timesheet
#: model:ir.model,name:helpdesk_sale_timesheet.model_helpdesk_team
msgid "Helpdesk Team"
msgstr "고객센터 팀"

#. module: helpdesk_sale_timesheet
#: model:ir.model,name:helpdesk_sale_timesheet.model_helpdesk_ticket
msgid "Helpdesk Ticket"
msgstr "고객센터 티켓"

#. module: helpdesk_sale_timesheet
#. odoo-python
#: code:addons/helpdesk_sale_timesheet/models/helpdesk_ticket.py:0
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_sale_timesheet
msgid "Invoices"
msgstr "청구서"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.portal_my_timesheets_inherit
msgid "No Ticket"
msgstr "티켓이 없습니다"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_sale_timesheet
msgid "Non-billable"
msgstr "청구 불가"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.portal_helpdesk_ticket_inherit
msgid "Not Billed"
msgstr "청구되지 않음"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.helpdesk_sla_view_form_inherit_sale_timesheet
msgid "OR"
msgstr "또는"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_team__project_id
msgid "Project"
msgstr "프로젝트"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,help:helpdesk_sale_timesheet.field_helpdesk_team__project_id
msgid ""
"Project to which the timesheets of this helpdesk team's tickets will be "
"linked."
msgstr "해당 헬프데스크 팀 티켓의 작업 기록이 연결될 프로젝트입니다."

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_ticket__sale_order_id
msgid "Ref. Sales Order"
msgstr "판매 주문서 참조"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_ticket__use_helpdesk_sale_timesheet
msgid "Reinvoicing Timesheet activated on Team"
msgstr "팀에서 활성화된 작업 기록 재청구"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_sla_report_analysis__remaining_hours_so
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_ticket_report_analysis__remaining_hours_so
msgid "Remaining Hours on SO"
msgstr "판매 주문서의 잔여 시간"

#. module: helpdesk_sale_timesheet
#: model:ir.model,name:helpdesk_sale_timesheet.model_helpdesk_sla_report_analysis
msgid "SLA Status Analysis"
msgstr "SLA 상태 분석"

#. module: helpdesk_sale_timesheet
#. odoo-python
#: code:addons/helpdesk_sale_timesheet/controllers/portal.py:0
#: code:addons/helpdesk_sale_timesheet/models/helpdesk_ticket.py:0
#: model:ir.model,name:helpdesk_sale_timesheet.model_sale_order
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_sale_timesheet
msgid "Sales Order"
msgstr "판매 주문"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_sla_report_analysis__sale_line_id
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_ticket__sale_line_id
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_ticket_report_analysis__sale_line_id
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.tickets_followup_timesheet
msgid "Sales Order Item"
msgstr "판매 주문 항목"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,help:helpdesk_sale_timesheet.field_helpdesk_ticket__sale_line_id
msgid ""
"Sales Order Item to which the time spent on this ticket will be added in order to be invoiced to your customer.\n"
"By default the last prepaid sales order item that has time remaining will be selected.\n"
"Remove the sales order item in order to make this ticket non-billable.\n"
"You can also change or remove the sales order item of each timesheet entry individually."
msgstr ""
"이 티켓에 소요된 시간을 추가하여 고객에게 청구서를 발행하기 위한 판매 주문 항목입니다.\n"
"잔여 시간이 남아있는 가장 최근의 가장 최근의 선불 판매 주문 항목이 기본값으로 선택됩니다.\n"
"이 티켓을 청구할 수 없도록 하려면 판매 주문 항목을 제거하십시오. \n"
"각 작업 기록의 판매 주문 항목을 개별적으로 변경하거나 제거할 수도 있습니다."

#. module: helpdesk_sale_timesheet
#: model:ir.model,name:helpdesk_sale_timesheet.model_sale_order_line
msgid "Sales Order Line"
msgstr "판매 주문 내역"

#. module: helpdesk_sale_timesheet
#. odoo-python
#: code:addons/helpdesk_sale_timesheet/controllers/portal.py:0
msgid "Search in Sales Order"
msgstr "판매 주문에서 검색"

#. module: helpdesk_sale_timesheet
#. odoo-python
#: code:addons/helpdesk_sale_timesheet/controllers/portal.py:0
msgid "Search in Ticket"
msgstr "티켓에서 검색"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_sla__product_ids
msgid "Services"
msgstr "서비스"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.portal_my_timesheets_inherit
msgid "Task/Ticket"
msgstr "작업/티켓"

#. module: helpdesk_sale_timesheet
#. odoo-python
#: code:addons/helpdesk_sale_timesheet/controllers/portal.py:0
#: code:addons/helpdesk_sale_timesheet/models/sale_order.py:0
msgid "Ticket"
msgstr "티켓"

#. module: helpdesk_sale_timesheet
#: model:ir.model,name:helpdesk_sale_timesheet.model_helpdesk_ticket_report_analysis
msgid "Ticket Analysis"
msgstr "티켓 분석"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_sale_order__ticket_count
msgid "Ticket Count"
msgstr "티켓 수"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.sale_order_form_inherit_helpdesk_sale
msgid "Tickets"
msgstr "티켓"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_sla__use_helpdesk_sale_timesheet
msgid "Time Billing"
msgstr "시간 청구"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_ticket__remaining_hours_so
msgid "Time Remaining on SO"
msgstr "SO 잔여 시간"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.tickets_followup_timesheet
msgid "Time Remaining on SO:"
msgstr "SO에 남은 시간:"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.portal_helpdesk_ticket_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.tickets_followup_timesheet
msgid "Time Spent"
msgstr "소요 시간"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.tickets_followup_timesheet
msgid "Timesheets"
msgstr "작업 기록"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.tickets_followup_timesheet
msgid "Total Time Spent:"
msgstr "총 소요 시간"

#. module: helpdesk_sale_timesheet
#. odoo-python
#: code:addons/helpdesk_sale_timesheet/models/helpdesk_ticket.py:0
msgid ""
"You cannot link order item %(order_id)s - %(product_id)s to this ticket "
"because it is not a service product."
msgstr "주문 항목 %(order_id)s-%(product_id)s는 서비스 제품이 아니므로 이 티켓에 연결할 수 없습니다."
