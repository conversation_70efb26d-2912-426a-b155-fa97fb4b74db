# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* helpdesk_sale_timesheet
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:27+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: helpdesk_sale_timesheet
#: model:helpdesk.sla,name:helpdesk_sale_timesheet.helpdesk_sla_4
msgid "4 hours to finish"
msgstr "4時間で終了"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_sale_timesheet
msgid ""
"<span class=\"fa fa-exclamation-triangle text-warning\" title=\"The sales "
"order associated with this ticket has been cancelled. We recommend either "
"updating the sales order item or cancelling this ticket in alignment with "
"the cancellation of the sales order.\" invisible=\"sale_order_state != "
"'cancel'\"/>"
msgstr ""
"<span class=\"fa fa-exclamation-triangle text-warning\" title=\"The sales "
"order associated with this ticket has been cancelled. We recommend either "
"updating the sales order item or cancelling this ticket in alignment with "
"the cancellation of the sales order.\" invisible=\"sale_order_state != "
"'cancel'\"/>"

#. module: helpdesk_sale_timesheet
#: model:ir.model,name:helpdesk_sale_timesheet.model_account_analytic_line
msgid "Analytic Line"
msgstr "分析行"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_sale_order_line__color
msgid "Color"
msgstr "色"

#. module: helpdesk_sale_timesheet
#: model:ir.model,name:helpdesk_sale_timesheet.model_helpdesk_ticket_convert_wizard
msgid "Convert Helpdesk Tickets to Tasks"
msgstr "ヘルプデスクチケットをタスクに変換"

#. module: helpdesk_sale_timesheet
#: model:ir.model,name:helpdesk_sale_timesheet.model_project_task_convert_wizard
msgid "Convert Project Tasks to Tickets"
msgstr "プロジェクトタスクをチケットに変換"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.tickets_followup_timesheet
msgid "Date"
msgstr "日付"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.tickets_followup_timesheet
msgid "Description"
msgstr "説明"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.tickets_followup_timesheet
msgid "Employee"
msgstr "従業員"

#. module: helpdesk_sale_timesheet
#: model:ir.model,name:helpdesk_sale_timesheet.model_helpdesk_sla
msgid "Helpdesk SLA Policies"
msgstr "ヘルプデスクのSLAポリシー"

#. module: helpdesk_sale_timesheet
#: model:ir.model,name:helpdesk_sale_timesheet.model_helpdesk_team
msgid "Helpdesk Team"
msgstr "ヘルプデスクチーム"

#. module: helpdesk_sale_timesheet
#: model:ir.model,name:helpdesk_sale_timesheet.model_helpdesk_ticket
msgid "Helpdesk Ticket"
msgstr "ヘルプデスクチケット"

#. module: helpdesk_sale_timesheet
#. odoo-python
#: code:addons/helpdesk_sale_timesheet/models/helpdesk_ticket.py:0
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_sale_timesheet
msgid "Invoices"
msgstr "請求書"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.portal_my_timesheets_inherit
msgid "No Ticket"
msgstr "チケットなし"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_sale_timesheet
msgid "Non-billable"
msgstr "請求不可"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.portal_helpdesk_ticket_inherit
msgid "Not Billed"
msgstr "未請求"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.helpdesk_sla_view_form_inherit_sale_timesheet
msgid "OR"
msgstr "又は"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_team__project_id
msgid "Project"
msgstr "プロジェクト"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,help:helpdesk_sale_timesheet.field_helpdesk_team__project_id
msgid ""
"Project to which the timesheets of this helpdesk team's tickets will be "
"linked."
msgstr "このヘルプデスクチームのチケットのタイムシートがリンクされるプロジェクト"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_ticket__sale_order_id
msgid "Ref. Sales Order"
msgstr "参照。販売注文"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_ticket__use_helpdesk_sale_timesheet
msgid "Reinvoicing Timesheet activated on Team"
msgstr "チームで有効化されたタイムシートの再請求"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_sla_report_analysis__remaining_hours_so
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_ticket_report_analysis__remaining_hours_so
msgid "Remaining Hours on SO"
msgstr "販売注文の残存時間"

#. module: helpdesk_sale_timesheet
#: model:ir.model,name:helpdesk_sale_timesheet.model_helpdesk_sla_report_analysis
msgid "SLA Status Analysis"
msgstr "SLAステータス分析"

#. module: helpdesk_sale_timesheet
#. odoo-python
#: code:addons/helpdesk_sale_timesheet/controllers/portal.py:0
#: code:addons/helpdesk_sale_timesheet/models/helpdesk_ticket.py:0
#: model:ir.model,name:helpdesk_sale_timesheet.model_sale_order
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_sale_timesheet
msgid "Sales Order"
msgstr "販売オーダ"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_sla_report_analysis__sale_line_id
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_ticket__sale_line_id
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_ticket_report_analysis__sale_line_id
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.tickets_followup_timesheet
msgid "Sales Order Item"
msgstr "販売オーダ項目"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,help:helpdesk_sale_timesheet.field_helpdesk_ticket__sale_line_id
msgid ""
"Sales Order Item to which the time spent on this ticket will be added in order to be invoiced to your customer.\n"
"By default the last prepaid sales order item that has time remaining will be selected.\n"
"Remove the sales order item in order to make this ticket non-billable.\n"
"You can also change or remove the sales order item of each timesheet entry individually."
msgstr ""
"顧客への請求のために、このチケットに費やされた時間が追加される販売オーダ項目。\n"
"デフォルトでは、残り時間がある最終の前払販売オーダ項目が選択されます。\n"
"このチケットを請求不可にするには、販売オーダ項目を削除します。\n"
"各タイムシートエントリーの販売オーダ項目を個別に変更または削除することもできます。"

#. module: helpdesk_sale_timesheet
#: model:ir.model,name:helpdesk_sale_timesheet.model_sale_order_line
msgid "Sales Order Line"
msgstr "販売オーダ明細"

#. module: helpdesk_sale_timesheet
#. odoo-python
#: code:addons/helpdesk_sale_timesheet/controllers/portal.py:0
msgid "Search in Sales Order"
msgstr "販売オーダ検索"

#. module: helpdesk_sale_timesheet
#. odoo-python
#: code:addons/helpdesk_sale_timesheet/controllers/portal.py:0
msgid "Search in Ticket"
msgstr "チケット検索"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_sla__product_ids
msgid "Services"
msgstr "サービス"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.portal_my_timesheets_inherit
msgid "Task/Ticket"
msgstr "タスク/チケット"

#. module: helpdesk_sale_timesheet
#. odoo-python
#: code:addons/helpdesk_sale_timesheet/controllers/portal.py:0
#: code:addons/helpdesk_sale_timesheet/models/sale_order.py:0
msgid "Ticket"
msgstr "チケット"

#. module: helpdesk_sale_timesheet
#: model:ir.model,name:helpdesk_sale_timesheet.model_helpdesk_ticket_report_analysis
msgid "Ticket Analysis"
msgstr "チケット分析"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_sale_order__ticket_count
msgid "Ticket Count"
msgstr "チケット数"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.sale_order_form_inherit_helpdesk_sale
msgid "Tickets"
msgstr "チケット"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_sla__use_helpdesk_sale_timesheet
msgid "Time Billing"
msgstr "時間請求"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_ticket__remaining_hours_so
msgid "Time Remaining on SO"
msgstr "販売オーダ残時間"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.tickets_followup_timesheet
msgid "Time Remaining on SO:"
msgstr "販売オーダ残時間:"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.portal_helpdesk_ticket_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.tickets_followup_timesheet
msgid "Time Spent"
msgstr "消費時間"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.tickets_followup_timesheet
msgid "Timesheets"
msgstr "タイムシート"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.tickets_followup_timesheet
msgid "Total Time Spent:"
msgstr "消費時間合計:"

#. module: helpdesk_sale_timesheet
#. odoo-python
#: code:addons/helpdesk_sale_timesheet/models/helpdesk_ticket.py:0
msgid ""
"You cannot link order item %(order_id)s - %(product_id)s to this ticket "
"because it is not a service product."
msgstr ""
"オーダ項目 %(order_id)s - %(product_id)s をこのチケットにリンクすることはできません。サービスプロダクトではないためです。"
