# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_bank_statement_extract
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:26+0000\n"
"PO-Revision-Date: 2024-09-25 09:43+0000\n"
"Last-Translator: Milan Tribuson <<EMAIL>>, 2024\n"
"Language-Team: Croatian (https://app.transifex.com/odoo/teams/41243/hr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hr\n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"

#. module: account_bank_statement_extract
#: model:ir.model.fields,field_description:account_bank_statement_extract.field_account_bank_statement__message_needaction
msgid "Action Needed"
msgstr "Potrebna radnja"

#. module: account_bank_statement_extract
#: model:ir.model.fields,field_description:account_bank_statement_extract.field_account_bank_statement__message_attachment_count
msgid "Attachment Count"
msgstr "Broj priloga"

#. module: account_bank_statement_extract
#: model:ir.model,name:account_bank_statement_extract.model_account_bank_statement
msgid "Bank Statement"
msgstr "Izvod banke"

#. module: account_bank_statement_extract
#: model:ir.model.fields,field_description:account_bank_statement_extract.field_res_config_settings__extract_bank_statement_digitalization_mode
msgid "Bank Statements"
msgstr "Izvodi banke"

#. module: account_bank_statement_extract
#: model:ir.model.fields,field_description:account_bank_statement_extract.field_account_bank_statement__extract_can_show_send_button
msgid "Can show the ocr send button"
msgstr ""

#. module: account_bank_statement_extract
#: model:ir.model,name:account_bank_statement_extract.model_res_company
msgid "Companies"
msgstr "Tvrtke"

#. module: account_bank_statement_extract
#: model:ir.model,name:account_bank_statement_extract.model_res_config_settings
msgid "Config Settings"
msgstr "Konfiguracijske postavke"

#. module: account_bank_statement_extract
#: model:ir.model.fields,field_description:account_bank_statement_extract.field_res_company__extract_bank_statement_digitalization_mode
msgid "Digitization mode on bank statements"
msgstr ""

#. module: account_bank_statement_extract
#: model:ir.model.fields.selection,name:account_bank_statement_extract.selection__res_company__extract_bank_statement_digitalization_mode__auto_send
msgid "Digitize automatically"
msgstr ""

#. module: account_bank_statement_extract
#: model:ir.model.fields.selection,name:account_bank_statement_extract.selection__res_company__extract_bank_statement_digitalization_mode__no_send
msgid "Do not digitize"
msgstr ""

#. module: account_bank_statement_extract
#: model:ir.model.fields,field_description:account_bank_statement_extract.field_account_bank_statement__extract_error_message
msgid "Error message"
msgstr "Error message"

#. module: account_bank_statement_extract
#: model:ir.model.fields,field_description:account_bank_statement_extract.field_account_bank_statement__extract_state_processed
msgid "Extract State Processed"
msgstr ""

#. module: account_bank_statement_extract
#: model:ir.model.fields,field_description:account_bank_statement_extract.field_account_bank_statement__extract_state
msgid "Extract state"
msgstr ""

#. module: account_bank_statement_extract
#: model:ir.model.fields,field_description:account_bank_statement_extract.field_account_bank_statement__extract_status
msgid "Extract status"
msgstr ""

#. module: account_bank_statement_extract
#: model:ir.model.fields,field_description:account_bank_statement_extract.field_account_bank_statement__message_follower_ids
msgid "Followers"
msgstr "Pratitelji"

#. module: account_bank_statement_extract
#: model:ir.model.fields,field_description:account_bank_statement_extract.field_account_bank_statement__message_partner_ids
msgid "Followers (Partners)"
msgstr "Pratitelji (partneri)"

#. module: account_bank_statement_extract
#. odoo-python
#: code:addons/account_bank_statement_extract/models/account_journal.py:0
msgid "Generated Bank Statements"
msgstr ""

#. module: account_bank_statement_extract
#: model:ir.model.fields,field_description:account_bank_statement_extract.field_account_bank_statement__has_message
msgid "Has Message"
msgstr "Ima poruku"

#. module: account_bank_statement_extract
#: model:ir.model.fields,field_description:account_bank_statement_extract.field_account_bank_statement__extract_document_uuid
msgid "ID of the request to IAP-OCR"
msgstr ""

#. module: account_bank_statement_extract
#: model:ir.model.fields,help:account_bank_statement_extract.field_account_bank_statement__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Ako je označeno, nove poruke zahtijevaju Vašu pažnju."

#. module: account_bank_statement_extract
#: model:ir.model.fields,help:account_bank_statement_extract.field_account_bank_statement__message_has_error
#: model:ir.model.fields,help:account_bank_statement_extract.field_account_bank_statement__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Ako je označeno neke poruke mogu imati grešku u dostavi."

#. module: account_bank_statement_extract
#: model:ir.model.fields,field_description:account_bank_statement_extract.field_account_bank_statement__message_is_follower
msgid "Is Follower"
msgstr "Je li pratitelj"

#. module: account_bank_statement_extract
#: model:ir.model.fields,field_description:account_bank_statement_extract.field_account_bank_statement__is_in_extractable_state
msgid "Is In Extractable State"
msgstr ""

#. module: account_bank_statement_extract
#: model:ir.model,name:account_bank_statement_extract.model_account_journal
msgid "Journal"
msgstr "Dnevnik"

#. module: account_bank_statement_extract
#: model:ir.model.fields,field_description:account_bank_statement_extract.field_account_bank_statement__message_main_attachment_id
msgid "Main Attachment"
msgstr "Glavni prilog"

#. module: account_bank_statement_extract
#: model:ir.model.fields,field_description:account_bank_statement_extract.field_account_bank_statement__message_has_error
msgid "Message Delivery error"
msgstr "Greška pri isporuci poruke"

#. module: account_bank_statement_extract
#: model:ir.model.fields,field_description:account_bank_statement_extract.field_account_bank_statement__message_ids
msgid "Messages"
msgstr "Poruke"

#. module: account_bank_statement_extract
#. odoo-python
#: code:addons/account_bank_statement_extract/models/account_journal.py:0
msgid "Mixing PDF/Image files with other file types is not allowed."
msgstr ""

#. module: account_bank_statement_extract
#: model:ir.model.fields,field_description:account_bank_statement_extract.field_account_bank_statement__message_needaction_counter
msgid "Number of Actions"
msgstr "Broj akcija"

#. module: account_bank_statement_extract
#: model:ir.model.fields,field_description:account_bank_statement_extract.field_account_bank_statement__message_has_error_counter
msgid "Number of errors"
msgstr "Broj grešaka"

#. module: account_bank_statement_extract
#: model:ir.model.fields,help:account_bank_statement_extract.field_account_bank_statement__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Broj poruka koje zahtijevaju aktivnost"

#. module: account_bank_statement_extract
#: model:ir.model.fields,help:account_bank_statement_extract.field_account_bank_statement__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Broj poruka sa greškama pri isporuci"

#. module: account_bank_statement_extract
#: model:ir.model.fields,field_description:account_bank_statement_extract.field_account_bank_statement__rating_ids
msgid "Ratings"
msgstr "Ocjene"

#. module: account_bank_statement_extract
#: model:ir.model.fields,field_description:account_bank_statement_extract.field_account_bank_statement__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Greška u slanju SMSa"

#. module: account_bank_statement_extract
#. odoo-python
#: code:addons/account_bank_statement_extract/models/account_bank_statement.py:0
msgid "Statement %s"
msgstr ""

#. module: account_bank_statement_extract
#. odoo-python
#: code:addons/account_bank_statement_extract/models/account_bank_statement.py:0
msgid ""
"Statement and transactions have been updated using Artificial Intelligence."
msgstr ""

#. module: account_bank_statement_extract
#: model:ir.model.fields,field_description:account_bank_statement_extract.field_account_bank_statement__website_message_ids
msgid "Website Messages"
msgstr "Poruke webstranica"

#. module: account_bank_statement_extract
#: model:ir.model.fields,help:account_bank_statement_extract.field_account_bank_statement__website_message_ids
msgid "Website communication history"
msgstr "Povijest komunikacije Web stranice"
