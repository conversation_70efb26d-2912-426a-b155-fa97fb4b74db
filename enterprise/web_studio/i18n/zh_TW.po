# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_studio
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:51+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    This is your new action.\n"
"                </p>\n"
"                <p>By default, it contains a list and a form view and possibly\n"
"                    other view types depending on the options you chose for your model.\n"
"                </p>\n"
"                <p>\n"
"                    You can start customizing these screens by clicking on the Studio icon on the\n"
"                    top right corner (you can also customize this help message there).\n"
"                </p>\n"
"            "
msgstr ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    這是你的新動作。\n"
"                </p>\n"
"                <p>它預設包括一個列表檢視及一個表單檢視畫面，\n"
"                    也可能有其他檢視方式，視乎你為模型選擇了甚麼選項。\n"
"                </p>\n"
"                <p>\n"
"                    你可按右上角的 Studio 圖示，開始自訂這些畫面。\n"
"                    該處亦可自訂此幫助訊息。\n"
"                </p>\n"
"            "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor.js:0
msgid ""
"\n"
"    There are no many2one fields related to the current model.\n"
"    To create a one2many field on the current model, you must first create its many2one counterpart on the model you want to relate to.\n"
msgstr ""
"\n"
"    沒有與目前模型相關聯的多對一（many2one）欄位。\n"
"    要在目前模型建立一對多（one2many）欄位，必須先在想要關聯的模型上，建立對應的多對一（many2one）欄位。\n"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid ""
" <p class=\"o_view_nocontent_empty_report\">\n"
"                Add a new report\n"
"            </p>\n"
"            "
msgstr ""
" <p class=\"o_view_nocontent_empty_report\">\n"
"                 建立新報表\n"
"            </p>\n"
"            "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid ""
" <p class=\"o_view_nocontent_smiling_face\">\n"
"                Add a new filter\n"
"            </p>\n"
"            "
msgstr ""
" <p class=\"o_view_nocontent_smiling_face\">\n"
"                加入新篩選器\n"
"            </p>\n"
"            "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid " until %s"
msgstr "直至 %s"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid "\" failed."
msgstr "\" 失敗。"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "%(user_name)s delegated approval rights to %(delegate_to)s"
msgstr "%(user_name)s 已委派批核權限至 %(delegate_to)s"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid ""
"%(user_name)s has set approval rights from %(previous_approvers)s to "
"%(next_approvers)s"
msgstr "%(user_name)s 已將批核權限設定，從 %(previous_approvers)s 改為 %(next_approvers)s"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "%(user_name)s revoked their delegation to %(revoked_users)s"
msgstr "%(user_name)s 已撤銷委派至 %(revoked_users)s"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/report.py:0
msgid "%s Report"
msgstr "%s 報告"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_export_model.py:0
msgid "%s record(s)"
msgstr "%s 項記錄"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_kanban_view
msgid "<i role=\"img\" class=\"fa fa-user-times\" title=\"Exclusive approval\"/>"
msgstr "<i role=\"img\" class=\"fa fa-user-times\" title=\"獨家認可\"/>"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                Add a new access control list\n"
"            </p>\n"
"            "
msgstr ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                新增存取控制列表\n"
"            </p>\n"
"            "

#. module: web_studio
#: model_terms:web_tour.tour,rainbow_man_message:web_studio.web_studio_new_app_tour
msgid "<span><b>Good job!</b> You went through all steps of this tour.</span>"
msgstr "<span><b>做得好！</b>你已完成本次導覽的所有步驟。</span>"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor_model.js:0
msgid "A field with the same name already exists."
msgstr "已有同名欄位。"

#. module: web_studio
#: model:ir.model.constraint,message:web_studio.constraint_studio_approval_entry_uniq_combination
msgid "A rule can only be approved/rejected once per record."
msgstr "在個別記錄上，一項規則只准批核/拒絕一次。"

#. module: web_studio
#: model:ir.model.constraint,message:web_studio.constraint_studio_approval_rule_method_or_action_together
msgid "A rule must apply to an action or a method (but not both)."
msgstr "規則必須套用至一項操作或方法（但不能同時套用至兩者）。"

#. module: web_studio
#: model:ir.model.constraint,message:web_studio.constraint_studio_approval_rule_method_or_action_not_null
msgid "A rule must apply to an action or a method."
msgstr "規則必須套用至操作或方法。"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_ir_model__abstract
msgid "Abstract"
msgstr "提要"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
msgid "Access Control"
msgstr "存取控制"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "Access Control Lists"
msgstr "存取控制列表"

#. module: web_studio
#: model:ir.model,name:web_studio.model_res_groups
msgid "Access Groups"
msgstr "存取組"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/pivot/pivot_editor_sidebar.xml:0
msgid "Access records from cell"
msgstr "從儲存格存取記錄"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor_sidebar.xml:0
msgid "Access records from graph"
msgstr "從圖表中存取記錄"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_mail_activity_type__category
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__action_id
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__action_id
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_search_view
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_search_view
msgid "Action"
msgstr "動作"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message_needaction
msgid "Action Needed"
msgstr "需要採取行動"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_actions_act_window
msgid "Action Window"
msgstr "動作檢視"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_actions_act_window_view
msgid "Action Window View"
msgstr "動作窗檢視"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
msgid "Action to approve:"
msgstr "待批核動作："

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
msgid "Action to run"
msgstr "待運行的動作"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
msgid "Action's title"
msgstr "動作的標題"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Action: %s"
msgstr "動作：%s"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_actions_actions
msgid "Actions"
msgstr "動作"

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_mail_activity_type__category
msgid ""
"Actions may trigger specific behavior like opening calendar view or "
"automatically mark as done when a document is uploaded"
msgstr "操作可能會觸發特定行為，如打開日曆視圖或自動標記為上載文檔時執行的操作"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/map_new_view_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/new_view_dialog.xml:0
msgid "Activate View"
msgstr "啟動檢視畫面"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__active
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_search_view
msgid "Active"
msgstr "啟用"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
#: model:ir.model,name:web_studio.model_mail_activity
msgid "Activity"
msgstr "活動"

#. module: web_studio
#: model:ir.model,name:web_studio.model_mail_activity_type
msgid "Activity Type"
msgstr "活動類型"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.js:0
msgid "Activity view unavailable on this model"
msgstr "此模型沒有活動檢視畫面"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor_sidebar.js:0
msgid "Add"
msgstr "加入"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/chatter_container.xml:0
msgid "Add Chatter Widget"
msgstr "加入聊天小工具"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.js:0
msgid "Add Picture"
msgstr "加入圖片"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.xml:0
msgid "Add a Button"
msgstr "新增按鈕"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
msgid "Add a button"
msgstr "加入按鈕。"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.js:0
msgid "Add a pipeline status bar"
msgstr "加入管道狀態列"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_compiler_legacy.js:0
msgid "Add a priority"
msgstr "新增優先等級"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
msgid "Add an approval step"
msgstr "加入一個批核步驟"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_compiler_legacy.js:0
msgid "Add an avatar"
msgstr "加入頭像"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Add details to your records with an embedded list view"
msgstr "使用嵌入的列表檢視畫面，為記錄加入詳細資訊"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
msgid "Add new value"
msgstr "添加新的值"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.js:0
msgid "Add record at the bottom"
msgstr "新增記錄至最底"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.js:0
msgid "Add record on top"
msgstr "新增記錄至最頂"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_compiler_legacy.js:0
msgid "Add tags"
msgstr "加入標籤"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard__additional_export_data
msgid "Additional Export Data"
msgstr "額外匯出數據"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/map/map_editor_sidebar.xml:0
msgid "Additional Fields"
msgstr "額外欄位"

#. module: web_studio
#: model_terms:ir.actions.act_window,help:web_studio.action_models_to_export
msgid "Additional Studio Exports"
msgstr "額外的 Studio 匯出"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard__additional_models
msgid "Additional models to export"
msgstr "要匯出的額外模型"

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_export_wizard__additional_models
msgid ""
"Additional models you may choose to export in addition to the Studio "
"customizations"
msgstr "除了 Studio 自訂功能外，你還可選擇匯出的其他模型"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/type_widget_properties/type_specific_and_computed_properties.js:0
msgid "Aggregate"
msgstr "綜合計算"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
msgid "All Day"
msgstr "全天"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid ""
"All changes done to the report's structure will be discarded and the report "
"will be reset to its factory settings."
msgstr "對報告結構所做的所有變更，都將會捨棄，而報告會重設為出廠設定。"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid ""
"All set? You are just one click away from <b>generating your first app</b>."
msgstr "一切都準備好了嗎？現在只需簡單按一下，即可<b>產生你的第一個應用程式</b>。"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/map/map_editor_sidebar.xml:0
msgid "Allow Resequencing"
msgstr "允許重新排序"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/limit_group_visibility/limit_group_visibility.xml:0
msgid "Allow visibility to groups"
msgstr "允許向群組顯示"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
#: code:addons/web_studio/static/src/approval/approval_hook.js:0
msgid "An approval is missing"
msgstr "缺漏一項批核"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_delegate_approvers
msgid "Apply"
msgstr "套用"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
msgid "Approval"
msgstr "核准方式"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_search_view
msgid "Approval Entries"
msgstr "批核記項"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__approval_group_id
msgid "Approval Group"
msgstr "批核組別"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
msgid "Approval Order"
msgstr "批核單"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__rule_id
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__rule_id
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_delegate__approval_rule_id
msgid "Approval Rule"
msgstr "批核規則"

#. module: web_studio
#: model:ir.model,name:web_studio.model_studio_approval_rule_approver
msgid "Approval Rule Approvers Enriched"
msgstr "批核規則批核者已豐富"

#. module: web_studio
#: model:ir.model,name:web_studio.model_studio_approval_rule_delegate
msgid "Approval Rule Delegate"
msgstr "批核規則委派"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_button_configuration_search_view
msgid "Approval Rules"
msgstr "批核規則"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
msgid "Approvals"
msgstr "審批"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Approvals %(model_name)s"
msgstr "批核 %(model_name)s"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Approvals can only be done on a method or an action, not both."
msgstr "批准只可在一項方法或操作上進行，不能同時對兩者進行。"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Approvals missing"
msgstr "缺漏批核"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
msgid "Approve"
msgstr "審批"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__approved
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_search_view
msgid "Approved"
msgstr "已審批"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.notify_approval
msgid "Approved <i class=\"fa fa-thumbs-up text-success\"/>"
msgstr "已批准 <i class=\"fa fa-thumbs-up text-success\"/>"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
msgid "Approved on"
msgstr "批核日期"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__user_id
msgid "Approved/rejected by"
msgstr "批核人/拒絕的使用者"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
msgid "Approver Group"
msgstr "批核者組別"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__approver_log_ids
msgid "Approver Log"
msgstr "批核人記錄"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__approver_ids
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_delegate__approver_ids
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_delegate_approvers
msgid "Approvers"
msgstr "流程審批管理人"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Archive deprecated records"
msgstr "封存已棄用的記錄"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_button_configuration_search_view
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_form_view
msgid "Archived"
msgstr "已封存"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Archiving"
msgstr "正在封存"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.js:0
msgid "Are you sure you want to remove the selection values?"
msgstr "確定要移除該些選取值？"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/sidebar_properties_toolbox/sidebar_properties_toolbox.js:0
msgid "Are you sure you want to remove this %s from the view?"
msgstr "確定要移除檢視畫面的這個 %s 嗎？"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/home_menu_customizer/home_menu_customizer.js:0
msgid "Are you sure you want to reset the background image?"
msgstr "確定要重設背景圖片？"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/edition_flow.js:0
msgid ""
"Are you sure you want to restore the default view?\r\n"
"All customization done with studio on this view will be lost."
msgstr ""
"確定要恢復預設檢視畫面嗎？\r\n"
"以 Studio 對此檢視畫面所做的全部自訂，將會遺失。"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__is_demo_data
msgid "As Demo"
msgstr "作為模擬數據"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_sidebar_legacy/kanban_editor_sidebar_legacy.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.js:0
msgid "Ascending"
msgstr "升序"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Assign a responsible to each record"
msgstr "為每項記錄分配一名負責人"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Assign dates and visualize records in a calendar"
msgstr "指定相關日期，並在日曆中視覺化呈現記錄"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Attach a picture to a record"
msgstr "加入圖片至記錄中"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message_attachment_count
msgid "Attachment Count"
msgstr "附件數目"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__include_attachment
msgid "Attachments"
msgstr "附件"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.xml:0
msgid "Autocompletion Fields"
msgstr "自動完成欄位"

#. module: web_studio
#: model:ir.model,name:web_studio.model_base_automation
msgid "Automation Rule"
msgstr "自動化規則"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
msgid "Automations"
msgstr "自動化"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/type_widget_properties/type_specific_and_computed_properties.js:0
msgid "Average"
msgstr "平均"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.js:0
msgid "Average of %s"
msgstr "平均 %s"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
msgid "Awaiting approval"
msgstr "等待批核"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
msgid "Backwards"
msgstr "向後"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor.js:0
msgid "Bar"
msgstr "條"

#. module: web_studio
#: model:ir.model,name:web_studio.model_base
msgid "Base"
msgstr "計稅基數"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/views/kanban_report/new_report_dialog.js:0
msgid "Blank"
msgstr "空白"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Blocked"
msgstr "已封鎖"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
msgid "Bold"
msgstr "粗線"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/views/kanban_report/new_report_dialog.js:0
msgid "Business header/footer"
msgstr "商業頁首/頁尾"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/properties/kanban_button_properties/kanban_button_properties.js:0
msgid "Button"
msgstr "按鈕"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
msgid "Buttons Properties"
msgstr "按鈕屬性"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/properties/aside_properties/aside_properties.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/properties/div_properties/div_properties.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/properties/footer_properties/footer_properties.xml:0
msgid "CSS style"
msgstr "CSS 樣式"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "Calendar"
msgstr "日曆"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
msgid "Call a method"
msgstr "召用一個方法"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/form_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_sidebar_legacy/kanban_editor_sidebar_legacy.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "Can Create"
msgstr "可以建立"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/form_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "Can Delete"
msgstr "可刪除"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/form_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "Can Edit"
msgstr "能編輯"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__can_validate
msgid "Can be approved"
msgstr "可被批核"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Can't patch 'create', 'write' and 'unlink'."
msgstr "未能修補\"建立\"、\"寫入\"及\"取消連結\"。"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Can't patch private methods."
msgstr "無法修補私密方法。"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
#: code:addons/web_studio/static/src/client_action/editor/new_model_item/new_model_item.xml:0
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/map_new_view_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/new_view_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.xml:0
#: code:addons/web_studio/static/src/client_action/studio_home_menu/icon_creator_dialog/icon_creator_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/field_selector_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/field_configuration.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:web_studio.view_studio_export_wizard
msgid "Cancel"
msgstr "取消"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Categorize records with custom tags"
msgstr "使用自訂標籤，將記錄分類"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/home_menu_customizer/home_menu_customizer.xml:0
msgid "Change Background"
msgstr "更改背景"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.xml:0
msgid "Change Image"
msgstr "更換圖片"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Chatter"
msgstr "聊天視窗"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "CheckBox"
msgstr "勾選方格"

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_ir_model_data__studio
msgid "Checked if it has been edited with Studio."
msgstr "若曾使用 Studio 編輯，會勾選。"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
msgid "Choose an app name"
msgstr "選擇一個應用程式名稱"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.xml:0
msgid "Choose the name of the menu"
msgstr "選擇選單名稱"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
msgid "Churn"
msgstr "流失"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/class_attribute/class_attribute.xml:0
msgid "Class"
msgstr "類"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid "Click to edit messaging features on your model."
msgstr "按一下以編輯模型上的訊息功能。"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/studio_approval.xml:0
msgid "Click to see all approval rules."
msgstr "按一下查看所有批核規則。"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
#: code:addons/web_studio/static/src/client_action/navbar/navbar.xml:0
#: code:addons/web_studio/static/src/client_action/xml_resource_editor/xml_resource_editor.xml:0
msgid "Close"
msgstr "關閉"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "Cohort"
msgstr "隊列"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Color"
msgstr "顏色"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.js:0
msgid "Color Picker"
msgstr "顏色選擇器"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/form_editor_sidebar.js:0
msgid "Column"
msgstr "列"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/pivot/pivot_editor_sidebar.xml:0
msgid "Column grouping"
msgstr "欄分組"

#. module: web_studio
#: model:ir.model,name:web_studio.model_res_company
msgid "Companies"
msgstr "公司"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Company"
msgstr "公司"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_structures.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.xml:0
msgid "Components"
msgstr "配件"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/modifiers/modifiers_properties.xml:0
msgid "Conditional"
msgstr "條件化"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__conditional
msgid "Conditional Rule"
msgstr "條件規則"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
#: code:addons/web_studio/models/ir_ui_menu.py:0
msgid "Configuration"
msgstr "配置"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_model_item/new_model_item.xml:0
msgid "Configure Model"
msgstr "配置模型"

#. module: web_studio
#: model_terms:ir.actions.act_window,help:web_studio.action_models_to_export
msgid ""
"Configure additional models to export with Studio, such as records that hold"
" configuration information or demo data."
msgstr "配置額外的模型，以使用 Studio 進行匯出，例如匯出載有配置資料或模擬數據的記錄。"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.view_studio_export_wizard
msgid "Configure data and demo data to export"
msgstr "配置要匯出的資料及模擬數據"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.xml:0
msgid "Configure model"
msgstr "配置模型"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.xml:0
#: code:addons/web_studio/static/src/client_action/studio_home_menu/icon_creator_dialog/icon_creator_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/field_selector_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/field_configuration.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
msgid "Confirm"
msgstr "確認"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/home_menu_customizer/home_menu_customizer.js:0
msgid "Confirmation"
msgstr "確認"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Contact"
msgstr "聯絡人"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/map_new_view_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/map/map_editor_sidebar.xml:0
msgid "Contact Field"
msgstr "聯絡人欄位"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/map_new_view_dialog.js:0
msgid "Contact Field Required"
msgstr "聯絡方式欄位屬必填"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Contact details"
msgstr "聯絡方法"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/type_widget_properties/type_specific_and_computed_properties.js:0
msgid "Context"
msgstr "上下文"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid ""
"Continue to configure some typical behaviors for your new type of object."
msgstr "繼續為新類型的物件，配置一些常見行為。"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/home_menu_customizer/home_menu_customizer.js:0
msgid "Could not change the background"
msgstr "未能更改背景"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.js:0
msgid "Create Menu"
msgstr "新增功能表"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_model_item/new_model_item.js:0
msgid "Create Model"
msgstr "建立模型"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_model_item/new_model_item.xml:0
msgid "Create a new Model"
msgstr "建立一個新模型"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid ""
"Create your <b>selection values</b> (e.g.: Romance, Polar, Fantasy, etc.)"
msgstr "建立你的<b>選項值</b>（例如：愛情、極地、奇幻等）"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
msgid "Create your App"
msgstr "創製你的應用程式"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
msgid "Create your app"
msgstr "創製你的應用程式"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
msgid "Create your first menu"
msgstr "建立你的第一個選單"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.xml:0
msgid "Create your menu"
msgstr "創製你的選單"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__create_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__create_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__create_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_approver__create_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_delegate__create_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__create_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard__create_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__create_uid
msgid "Created by"
msgstr "建立人員"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__create_date
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__create_date
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__create_date
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_approver__create_date
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_delegate__create_date
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__create_date
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard__create_date
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__create_date
msgid "Created on"
msgstr "建立於"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.js:0
msgid "Creating this type of view is not currently supported in Studio."
msgstr "Studio 目前不支援建立此類型的檢視畫面。"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Currency"
msgstr "貨幣"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid "Current model:"
msgstr "目前模型："

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_menu.py:0
msgid "Custom Configuration"
msgstr "自訂配置"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_base_module_uninstall__custom_fields
msgid "Custom Fields"
msgstr "自訂欄位"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_base_module_uninstall__custom_models
msgid "Custom Models"
msgstr "自訂模型"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_base_module_uninstall__custom_reports
msgid "Custom Reports"
msgstr "自訂報告"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Custom Sorting"
msgstr "自訂排序"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_base_module_uninstall__custom_views
msgid "Custom Views"
msgstr "自訂檢視畫面"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Custom field names cannot contain double underscores."
msgstr "自訂欄位名稱不可包含連續兩個底線（_）字元。"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.view_base_module_uninstall_studio
msgid "Custom fields:"
msgstr "自訂欄位："

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.view_base_module_uninstall_studio
msgid "Custom models:"
msgstr "自訂模型："

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.view_base_module_uninstall_studio
msgid "Custom reports:"
msgstr "自訂報告："

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.view_base_module_uninstall_studio
msgid "Custom views:"
msgstr "自訂檢視畫面："

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.view_base_module_uninstall_studio
msgid "Customization made with Studio will be permanently lost"
msgstr "使用 Studio 所做的自訂將永久遺失"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_xml/report_editor_xml.xml:0
msgid "DIFF"
msgstr "diff 比對"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/models/ir_ui_view.py:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Date"
msgstr "日期"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Date & Calendar"
msgstr "日期及日曆"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_approver__date_to
msgid "Date To"
msgstr "結束日期"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Date range & Gantt"
msgstr "日期範圍及甘特圖"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Datetime"
msgstr "日期時間"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Day"
msgstr "日"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Day Precision"
msgstr "天數精確度"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Decimal"
msgstr "小數"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
msgid "Default Display Mode"
msgstr "預設顯示模式"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard__default_export_data
msgid "Default Export Data"
msgstr "預設匯出數據"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_sidebar_legacy/kanban_editor_sidebar_legacy.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "Default Group By"
msgstr "預設分組依據"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Default Group by"
msgstr "預設分組依據"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Default Scale"
msgstr "預設範圍"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/sidebar_view_toolbox/sidebar_view_toolbox.xml:0
#: model:ir.model,name:web_studio.model_ir_default
msgid "Default Values"
msgstr "預設值"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
msgid "Default value"
msgstr "預設值"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
msgid "Default view"
msgstr "預設檢視畫面"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Define start/end dates and visualize records in a Gantt chart"
msgstr "定義開始/結束日期，並在甘特圖中視覺化呈現記錄"

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_export_model__updatable
msgid "Defines if the records would be updated during a module update."
msgstr "定義在模組更新期間，是否更新記錄。"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__notification_order
msgid "Defines the sequential order in which the approvals are requested."
msgstr "定義請求批准的順序。"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
msgid "Delay Field"
msgstr "延遲欄位"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_form_view
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_kanban_view
msgid "Delegate"
msgstr "委派"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Delegate to"
msgstr "委派至"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/qweb_table_plugin.js:0
#: model_terms:ir.ui.view,arch_db:web_studio.act_report_xml_view_kanban
msgid "Delete"
msgstr "刪除"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__is_demo_data
msgid "Demo"
msgstr "範例"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Dense"
msgstr "緊密"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_sidebar_legacy/kanban_editor_sidebar_legacy.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.js:0
msgid "Descending"
msgstr "降序"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message
msgid "Description"
msgstr "說明"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/icon_creator/icon_creator.xml:0
msgid "Design your Icon"
msgstr "設計你的圖示"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
msgid "Disable View"
msgstr "停用檢視畫面"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_snackbar.xml:0
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_delegate_approvers
msgid "Discard"
msgstr "捨棄"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
msgid "Discard changes"
msgstr "捨棄變更"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Display Mode"
msgstr "顯示模式"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__display_name
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__display_name
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__display_name
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_approver__display_name
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_delegate__display_name
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__display_name
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard__display_name
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Display Total row"
msgstr "顯示總列數"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Display Unavailability"
msgstr "顯示不可用"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/type_widget_properties/type_specific_and_computed_properties.js:0
msgid "Displays a textual hint that helps the user when the field is empty."
msgstr "欄位空白時，顯示文字提示幫助使用者。"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_record_legacy.js:0
msgid "Do you want to add a dropdown with colors?"
msgstr "你想加入有不同顏色的下拉式選單嗎？"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/type_widget_properties/type_specific_and_computed_properties.js:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__domain
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__domain
msgid "Domain"
msgstr "篩選範圍"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Done"
msgstr "完成"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid ""
"Drag & drop <b>another field</b>. Let's try with a <i>selection field</i>."
msgstr "拖放<b>另一個欄位</b>。試試拖放<b>選項欄位</b>吧。<i></i>"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
msgid "Drag a menu to the right to create a sub-menu"
msgstr "將選單向右拖曳，以建立子選單"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_sidebar_legacy/properties/kanban_cover_properties/kanban_cover_properties.js:0
msgid "Dropdown"
msgstr "下拉式選單"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.act_report_xml_view_kanban
msgid "Duplicate"
msgstr "複製"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
msgid "Dynamic Table"
msgstr "動態表格"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Edit"
msgstr "編輯"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/field_content_overlay.js:0
msgid "Edit %s view"
msgstr "編輯 %s 檢視畫面"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/studio_home_menu/icon_creator_dialog/icon_creator_dialog.xml:0
msgid "Edit Application Icon"
msgstr "編輯應用程式圖示"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/properties/menu_properties/menu_properties.xml:0
msgid "Edit Color Picker"
msgstr "編輯顏色選擇器"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
msgid "Edit Menu"
msgstr "編輯選單"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
msgid "Edit Values"
msgstr "編輯值"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
msgid "Edit selection"
msgstr "編輯選取項"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid "Edit sources"
msgstr "編輯來源"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/xml_resource_editor/xml_resource_editor.js:0
msgid ""
"Editing a built-in file through this editor is not advised, as it will "
"prevent it from being updated during future App upgrades."
msgstr "不建議通過此編輯器編輯內置文件，因為它會阻止在以後的 App 升級期間更新。"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
msgid "Editing item:"
msgstr "正在編輯項目："

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Email"
msgstr "電郵"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/chatter_properties/chatter_properties.xml:0
msgid "Email Alias"
msgstr "郵箱別名"

#. module: web_studio
#: model:ir.model,name:web_studio.model_mail_template
msgid "Email Templates"
msgstr "電郵範本"

#. module: web_studio
#: model:ir.model,name:web_studio.model_mail_thread
msgid "Email Thread"
msgstr "電郵對話串"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
msgid "Empty List Message"
msgstr "清單空白時的訊息"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "Enable Mass Editing"
msgstr "啟用大批編輯"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/map/map_editor_sidebar.xml:0
msgid "Enable Routing"
msgstr "啟用路線功能"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "End Date"
msgstr "結束日期"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__entry_ids
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_form_view
msgid "Entries"
msgstr "分錄"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/studio_view.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor_model.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor_snackbar.xml:0
msgid "Error"
msgstr "錯誤"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid "Error message:"
msgstr "錯誤訊息："

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid "Error name:"
msgstr "錯誤名稱："

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__exclusive_user
msgid "Exclusive Approval"
msgstr "專有批核權"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.xml:0
msgid "Existing Fields"
msgstr "現有欄位"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.js:0
msgid "Existing Model"
msgstr "現存模型"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/home_menu_customizer/home_menu_customizer.xml:0
#: model_terms:ir.ui.view,arch_db:web_studio.models_to_export_list
#: model_terms:ir.ui.view,arch_db:web_studio.view_studio_export_wizard
msgid "Export"
msgstr "匯出"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/views/kanban_report/new_report_dialog.js:0
msgid "External"
msgstr "外部"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__action_xmlid
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__xmlid
msgid "External ID"
msgstr "外部識別碼"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.xml:0
msgid "Fadeout Speed"
msgstr "淡出速度"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.js:0
msgid "Fast"
msgstr "快速"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.xml:0
msgid "Favourites"
msgstr "最愛"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/field_selector_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.xml:0
msgid "Field"
msgstr "欄位"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
msgid "Field Properties"
msgstr "欄位屬性"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/field_configuration.js:0
msgid "Field properties: %s"
msgstr "欄位屬性：%s"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_model_fields
msgid "Fields"
msgstr "欄位"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__excluded_fields
#: model_terms:ir.ui.view,arch_db:web_studio.models_to_export_form_view
msgid "Fields to exclude"
msgstr "要排除的欄位"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
#: model_terms:ir.ui.view,arch_db:web_studio.act_report_xml_view_kanban
msgid "File"
msgstr "文件"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "Filename for %s"
msgstr "%s 的檔案名稱"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.js:0
msgid "Filter"
msgstr "篩選"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
msgid "Filter Rules"
msgstr "篩選規則"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/field_configuration.xml:0
msgid "Filter label"
msgstr "篩選標籤"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.xml:0
#: model:ir.model,name:web_studio.model_ir_filters
msgid "Filters"
msgstr "篩選"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.js:0
msgid "First Status"
msgstr "首個狀態"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor_sidebar.xml:0
msgid "First dimension"
msgstr "第一個維度"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message_follower_ids
msgid "Followers"
msgstr "關注人"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message_partner_ids
msgid "Followers (Partners)"
msgstr "關注人（業務夥伴）"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.js:0
msgid "Footer"
msgstr "頁尾"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid ""
"For compatibility purpose with base_automation,approvals on 'create', "
"'write' and 'unlink' methods are forbidden."
msgstr "為了與 base_automation 兼容，已禁用對「建立」、「寫入」及「取消連結」方法進行的批核。"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/limit_group_visibility/limit_group_visibility.xml:0
msgid "Forbid visibility to groups"
msgstr "禁止向群組顯示"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_delegate_approvers
msgid "Forever"
msgstr "永遠"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "Form"
msgstr "表單"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/xml_resource_editor/xml_resource_editor.xml:0
msgid "Format"
msgstr "文字格式"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
msgid "Forward"
msgstr "向前"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "Gantt"
msgstr "甘特圖"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.js:0
msgid "General views"
msgstr "一般檢視畫面"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/new_view_dialog.js:0
msgid "Generate %s View"
msgstr "生成 %s 檢視畫面"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Get contact, phone and email fields on records"
msgstr "取得記錄的聯絡人、電話及電郵地址欄位"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
msgid "Go back"
msgstr "返回"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid "Go on, you are almost done!"
msgstr "繼續，就快完成了！"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid "Good job! To add more <b>fields</b>, come back to the <i>Add tab</i>."
msgstr "做得好！要加入更多<b>欄位</b>，可返回此處並選擇「加入標籤」。<i></i>"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
#: model:ir.model.fields.selection,name:web_studio.selection__mail_activity_type__category__grant_approval
#: model:mail.activity.type,name:web_studio.mail_activity_data_approve
msgid "Grant Approval"
msgstr "批出准許"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "Graph"
msgstr "圖形"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.xml:0
msgid "Great !"
msgstr "很好！"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_renderer.xml:0
msgid "Group"
msgstr "組"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Group By"
msgstr "分組依據"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.xml:0
msgid "Group by"
msgstr "分組"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "Grouping is not applied while in Studio to allow editing."
msgstr "Studio 不會套用分組，以允許編輯。"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "HTML"
msgstr "HTML"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP 路由"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Half Day"
msgstr "半天"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Half Hour"
msgstr "半小時"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__has_message
msgid "Has Message"
msgstr "有訊息"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid ""
"Header and footer are shared with other reports which may be impacted by the"
" reset."
msgstr "頁首及頁尾會共享，給其他可能受重設影響的報告使用。"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
msgid "Help Tooltip"
msgstr "幫助提示"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid ""
"Here, you can <b>name</b> your field (e.g. Book reference, ISBN, Internal "
"Note, etc.)."
msgstr "在此處，你可為欄位<b>命名</b>（例如：書籍參考、ISBN、內部備註等）。"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/map/map_editor_sidebar.xml:0
msgid "Hide Address"
msgstr "隱藏地址"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/map/map_editor_sidebar.xml:0
msgid "Hide Name"
msgstr "隱藏名稱"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.js:0
msgid "Hide by default"
msgstr "預設隱藏"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "High Priority"
msgstr "高優先級別"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.xml:0
msgid "Highlight Color Field"
msgstr "突出顏色欄位"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_res_company__background_image
msgid "Home Menu Background Image"
msgstr "主頁選單背景圖片"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Hour"
msgstr "小時"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid "How do you want to <b>name</b> your app? Library, Academy, …?"
msgstr "你想如何<b>命名</b>你的應用程式？例如：圖書館、學院⋯⋯等等？"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid "How do you want to name your first <b>menu</b>? My books, My courses?"
msgstr "你想如何命名你的首個<b>選單</b>？例如：我的書籍、我的課程⋯⋯等等？"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid ""
"I bet you can <b>build an app</b> in 5 minutes. Ready for the challenge?"
msgstr "我猜你能在 5 分鐘內<b>構建一個應用程式</b>。準備好接受挑戰嗎？"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__id
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__id
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__id
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_approver__id
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_delegate__id
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__id
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard__id
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__id
msgid "ID"
msgstr "識別號"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid "IDs"
msgstr "識別碼"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.xml:0
msgid "Icon"
msgstr "圖示"

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__message_needaction
msgid "If checked, new messages require your attention."
msgstr "勾選代表有新訊息需要您留意。"

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__message_has_error
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "勾選代表有訊息發生傳送錯誤。"

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_export_model__include_attachment
msgid ""
"If set, the attachments related to the exported records will be included in "
"the export."
msgstr "如果設定，與匯出記錄相關的附件，會一併包含在匯出中。"

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_export_model__is_demo_data
msgid ""
"If set, the exported records will be considered as demo data during the "
"import."
msgstr "如果設定，匯出的記錄會在匯入時視為模擬數據。"

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__domain
msgid "If set, the rule will only apply on records that match the domain."
msgstr "如果設置，該規則將只會適用於與該網域相符的記錄。"

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__exclusive_user
msgid ""
"If set, the user who approves this rule will not be able to approve other "
"rules for the same record"
msgstr "如果設置，批核此規則的使用者將無法批核同一記錄的其他規則"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
msgid ""
"If you discard the current edits, all unsaved changes will be lost. You can "
"cancel to return to edit mode."
msgstr "若你放棄目前的編輯，所有未儲存的修改將會消失。你可以按「取消」返回編輯模式。"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid ""
"If you don't want to create a new model, an existing model should be "
"selected."
msgstr "若不想建立新模型，便應該選擇現有模型。"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Image"
msgstr "圖片"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/home_menu_customizer/home_menu_customizer.xml:0
msgid "Import"
msgstr "匯入"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "In Progress"
msgstr "進行中"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard__include_additional_data
msgid "Include Data"
msgstr "包含數據"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard__include_demo_data
msgid "Include Demo Data"
msgstr "包含模擬數據"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid "Include header and footer"
msgstr "包括頁首及頁尾"

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_ir_ui_menu__is_studio_configuration
msgid ""
"Indicates that this menu was created by Studio to hold configuration sub-"
"menus"
msgstr "表示此選單是在Studio創建的，用於裝載配置子選單"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
msgid "Insert a field"
msgstr "插入欄位"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
msgid "Insert a field..."
msgstr "插入欄位⋯"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
msgid "Insert a table based on a relational field."
msgstr "插入表格，資料基於關係欄位。"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/qweb_table_plugin.js:0
msgid "Insert left"
msgstr "向左插入"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/qweb_table_plugin.js:0
msgid "Insert right"
msgstr "向右插入"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Integer"
msgstr "整數"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/views/kanban_report/new_report_dialog.js:0
msgid "Internal"
msgstr "內部"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor_sidebar.xml:0
msgid "Interval"
msgstr "間隔"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Invalid studio_approval %s in button"
msgstr "按鈕內的 studio_approval %s 無效"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/modifiers/modifiers_properties.xml:0
msgid "Invisible"
msgstr "隱藏"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_approver__is_delegation
msgid "Is Delegation"
msgstr "是委派"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message_is_follower
msgid "Is Follower"
msgstr "是關注者"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_base_module_uninstall__is_studio
msgid "Is Studio"
msgstr "是 Studio"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
msgid "Is default view"
msgstr "是預設檢視畫面"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "It lacks a method to check."
msgstr "它缺乏一種檢查方法。"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "Kanban"
msgstr "看板"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__kanban_color
msgid "Kanban Color"
msgstr "看板顏色"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Kanban State"
msgstr "看板狀態"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/group_properties/group_properties.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/page_properties/page_properties.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
msgid "Label"
msgstr "標籤"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
msgid "Label of the button"
msgstr "按鈕標籤"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__write_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__write_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__write_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_approver__write_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_delegate__write_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__write_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard__write_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__write_uid
msgid "Last Updated by"
msgstr "最後更新者"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__write_date
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__write_date
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__write_date
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_approver__write_date
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_delegate__write_date
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__write_date
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard__write_date
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__write_date
msgid "Last Updated on"
msgstr "最後更新於"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_xml/report_editor_xml.xml:0
msgid "Leave DIFF"
msgstr "離開 diff 比對"

#. module: web_studio
#: model:ir.actions.client,name:web_studio.action_web_studio_leave_with
msgid "Leave Studio with another action"
msgstr "離開 Studio 使用其他操作"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid ""
"Let's check the result. Close Odoo Studio to get an <b>overview of your "
"app</b>."
msgstr "我們一起看看結果吧。請關閉 Odoo Studio，以查看<b>應用程式概覽</b>。"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid "Limit visibility to groups"
msgstr "限制群組可見性"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor.js:0
msgid "Line"
msgstr "明細"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Lines"
msgstr "明細列表"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__mail_activity_id
msgid "Linked Activity"
msgstr "Linked Activity"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "List"
msgstr "清單"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Manually sort records in the list view"
msgstr "在列表檢視畫面中，對記錄手動排序"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Many2Many"
msgstr "Many2Many(多對多)"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Many2One"
msgstr "多對一"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "Map"
msgstr "地圖"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/map_new_view_dialog.js:0
msgid ""
"Map views are based on the address of a linked Contact. You need to have a "
"Many2one field linked to the res.partner model in order to create a map "
"view."
msgstr "地圖檢視畫面是基於已連結聯絡人的地址。你需要將一個 many2one 欄位連結至 res.partner 模型，才可建立地圖檢視畫面。"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor_sidebar.xml:0
msgid "Measure"
msgstr "測量"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor_sidebar.xml:0
msgid "Measure Field"
msgstr "量度欄位"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/pivot/pivot_editor_sidebar.xml:0
msgid "Measures"
msgstr "量度"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.js:0
msgid "Medium"
msgstr "媒體"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.js:0
#: model:ir.model,name:web_studio.model_ir_ui_menu
msgid "Menu"
msgstr "功能表"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.xml:0
msgid "Message"
msgstr "消息"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message_has_error
msgid "Message Delivery error"
msgstr "訊息遞送錯誤"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message_ids
msgid "Messages"
msgstr "訊息"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/properties/kanban_button_properties/kanban_button_properties.xml:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__method
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__method
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_search_view
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_search_view
msgid "Method"
msgstr "方法"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
msgid "Method to run"
msgstr "待運行方法"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Method: %s"
msgstr "方法：%s"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/views/kanban_report/new_report_dialog.js:0
msgid "Minimal header/footer"
msgstr "簡約頁首/頁尾"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor_sidebar.xml:0
msgid "Mode"
msgstr "模式"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.xml:0
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__model_id
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__model_id
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__model
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_search_view
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_search_view
msgid "Model"
msgstr "模型"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/wizard/studio_export_wizard.py:0
msgid "Model '%s' should not contain records with the same ID."
msgstr "模型「%s」不應包含擁有相同識別碼的記錄。"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_model_access
msgid "Model Access"
msgstr "模型存取"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_model_data
msgid "Model Data"
msgstr "模型資料"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__model_name
msgid "Model Description"
msgstr "模型說明"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__model
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__model_name
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__model_name
msgid "Model Name"
msgstr "模型名稱"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_model_item/new_model_item.xml:0
msgid "Model name"
msgstr "型號名稱"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_model
msgid "Models"
msgstr "型號"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/xml_resource_editor/xml_resource_editor.xml:0
msgid "Modified by:"
msgstr "修改者："

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_module_module
msgid "Module"
msgstr "模組"

#. module: web_studio
#: model:ir.model,name:web_studio.model_base_module_uninstall
msgid "Module Uninstall"
msgstr "模組卸載"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Monetary"
msgstr "貨幣的"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Monetary value"
msgstr "金錢價值"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Month"
msgstr "月"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Month (expanded)"
msgstr "月（展開）"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Month Precision"
msgstr "月份精確度"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/sidebar_properties_toolbox/sidebar_properties_toolbox.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/sidebar_view_toolbox/sidebar_view_toolbox.xml:0
msgid "More"
msgstr "更多"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Multine Text"
msgstr "多行文字"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.js:0
msgid "Multiple records views"
msgstr "多記錄檢視畫面"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "My %s"
msgstr "我的 %s"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.xml:0
msgid "My Button"
msgstr "我的按鈕"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_search_view
msgid "My entries"
msgstr "我的記項"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid "N/A"
msgstr "不適用"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__name
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__name
msgid "Name"
msgstr "名稱"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "New"
msgstr "新增"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor.js:0
msgid "New %s"
msgstr "新%s"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/studio_home_menu/studio_home_menu.js:0
msgid "New App"
msgstr "新應用程式"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "New Approval Entry"
msgstr "新批核記項"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/field_selector_dialog.xml:0
msgid "New Field"
msgstr "新欄位"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.xml:0
msgid "New Fields"
msgstr "新欄位"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.js:0
msgid "New Filter"
msgstr "新篩選器"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "New Lines"
msgstr "新行"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
msgid "New Menu"
msgstr "新選單"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_model_item/new_model_item.xml:0
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.js:0
msgid "New Model"
msgstr "新模型"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.js:0
msgid "New Page"
msgstr "新網頁"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.js:0
msgid "New button"
msgstr "新按鈕"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.xml:0
msgid "Next"
msgstr "下一頁"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid ""
"Nicely done! Let's build your screen now; <b>drag</b> a <i>text field</i> "
"and <b>drop</b> it in your view, on the right."
msgstr "做得好！現在讓我們構建顯示畫面。<b>拖曳</b>一個文字欄位<i></i>，將它<b>放置</b>在檢視畫面右方。"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/type_widget_properties/type_specific_and_computed_properties.js:0
msgid "No aggregation"
msgstr "不作匯合"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "No approval found for this rule, record and user combination."
msgstr "就此規則、記錄及使用者組合，找不到相符的批准。"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/views/kanban_report/new_report_dialog.js:0
msgid "No header/footer"
msgstr "無頁首/頁尾"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor.js:0
msgid "No related many2one fields found"
msgstr "找不到關聯的many2one欄位"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.js:0
msgid "None"
msgstr "無"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Notes"
msgstr "備註"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_delegate__users_to_notify
msgid "Notify to"
msgstr "通知至"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid "Now you're on your own. Enjoy your <b>super power</b>."
msgstr "現在你可自己操作了。盡情發揮你的<b>超能力</b>吧！"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid "Now, customize your icon. Make it yours."
msgstr "現在，你可隨你所好，自訂你專屬的圖示。"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message_needaction_counter
msgid "Number of Actions"
msgstr "操作數目"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__entries_count
msgid "Number of Entries"
msgstr "折舊次數"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message_has_error_counter
msgid "Number of errors"
msgstr "錯誤數量"

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "需要採取行動的訊息數目"

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "有發送錯誤的郵件數量"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
msgid "Odoo Studio"
msgstr "Odoo Studio"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid "On view (ir.ui.view):"
msgstr "顯示時(ir.ui.view):"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "One2Many"
msgstr "One2Many(一對多)"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid ""
"Only groups with an external ID can be used here. Please choose another "
"group or assign manually an external ID to this group."
msgstr "此處只可使用擁有外部識別碼的群組。請選擇另一個群組，或手動編配一個外部識別碼給此群組。"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.xml:0
msgid "Open Record On Click"
msgstr "按下時開啟記錄"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.js:0
msgid "Open form view"
msgstr "開啟表單檢視畫面"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
msgid "Open kanban view of approvals"
msgstr "開啟批核看板檢視畫面"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
msgid "Optional"
msgstr "選填"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_sidebar_legacy/kanban_editor_sidebar_legacy.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "Order"
msgstr "訂單"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.act_report_xml_view_kanban
msgid "PDF file"
msgstr "PDF"

#. module: web_studio
#: model:ir.model,name:web_studio.model_report_paperformat
msgid "Paper Format Config"
msgstr "紙張格式設定"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid "Paper format"
msgstr "紙張格式"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.js:0
msgid "Parent Menu"
msgstr "上層功能表"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid "Parent View (inherit_id)"
msgstr "母項檢視畫面(inherit_id)"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Partner"
msgstr "業務夥伴"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Phone"
msgstr "電話"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Picture"
msgstr "圖片"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor.js:0
msgid "Pie"
msgstr "Pie"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Pipeline stages"
msgstr "管道階段"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.js:0
msgid "Pipeline status bar"
msgstr "管道狀態列"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "Pivot"
msgstr "樞紐分析表"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/type_widget_properties/type_specific_and_computed_properties.js:0
msgid "Placeholder"
msgstr "提示文字"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "Please specify a field."
msgstr "請指定一個欄位。"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__post
msgid "Post"
msgstr "過賬"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__pre
msgid "Pre"
msgstr "前"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.models_to_export_list
msgid "Preset"
msgstr "預設組合"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_record.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_record_legacy.xml:0
msgid "Preview is not available"
msgstr "沒有可用預覽"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.xml:0
msgid "Previous"
msgstr "上一頁"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid "Print preview"
msgstr "列印預覽"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Priority"
msgstr "優先級"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid ""
"Private methods cannot be restricted (since they cannot be called remotely, "
"this would be useless)."
msgstr "私密方法不能被限制（因為它們不能由遠端呼叫召用，這樣做是沒有用的）。"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor_sidebar.js:0
msgid "Properties"
msgstr "屬性"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Quarter Hour"
msgstr "一刻鐘（15 分鐘）"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_sidebar_legacy/kanban_editor_sidebar_legacy.xml:0
msgid "Quick Create"
msgstr "快速建立"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
msgid "Quick Create Field"
msgstr "快速建立欄位"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_qweb
msgid "Qweb"
msgstr "Qweb"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.xml:0
msgid "Rainbow Effect"
msgstr "彩虹效果"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.xml:0
msgid "Rainbow Man"
msgstr "彩虹小精靈"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__rating_ids
msgid "Ratings"
msgstr "評分"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/modifiers/modifiers_properties.xml:0
msgid "Readonly"
msgstr "唯讀"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Ready"
msgstr "準備好"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_form_view
msgid "Record"
msgstr "記錄"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__res_id
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__res_id
msgid "Record ID"
msgstr "記錄 ID"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__name
msgid "Record Name"
msgstr "記錄名稱"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__records_count
msgid "Records"
msgstr "記錄"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor_snackbar.xml:0
msgid "Redo"
msgstr "取消復原"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__reference
msgid "Reference"
msgstr "編號"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
msgid "Reject"
msgstr "拒絕"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_search_view
msgid "Rejected"
msgstr "已拒絕"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.notify_approval
msgid "Rejected <i class=\"fa fa-thumbs-down text-danger\"/>"
msgstr "已拒絕 <i class=\"fa fa-thumbs-down text-danger\"/>"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
msgid "Rejected on"
msgstr "拒絕時間"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Related Field"
msgstr "關聯欄位"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid "Reload from attachment"
msgstr "從附件重新載入"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/sidebar_properties_toolbox/sidebar_properties_toolbox.xml:0
msgid "Remove from View"
msgstr "從檢視畫面移除"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
msgid "Remove rule"
msgstr "移除規則"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
msgid "Remove selection"
msgstr "刪除選擇"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_actions_report
msgid "Report Action"
msgstr "報表動作"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
msgid "Report Tools"
msgstr "報告工具"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_model.js:0
msgid "Report edition failed"
msgstr "報告編輯失敗"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid "Report name"
msgstr "報告名稱"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
msgid "Report preview not available"
msgstr "沒有可用報告預覽"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.js:0
msgid "Reporting views"
msgstr "報告檢視畫面"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
msgid "Reports"
msgstr "報表"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/modifiers/modifiers_properties.xml:0
msgid "Required"
msgstr "必填項"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__res_id
msgid "Res"
msgstr "Res"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/home_menu_customizer/home_menu_customizer.xml:0
msgid "Reset Default Background"
msgstr "重設預設背景"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.xml:0
msgid "Reset Image"
msgstr "重設圖片"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.reset_view_arch_wizard_view
msgid "Reset View"
msgstr "重置檢視"

#. module: web_studio
#: model:ir.model,name:web_studio.model_reset_view_arch_wizard
msgid "Reset View Architecture Wizard"
msgstr "重設檢視畫面架構精靈"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid "Reset report"
msgstr "重設報告"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Responsible"
msgstr "負責人"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/sidebar_view_toolbox/sidebar_view_toolbox.xml:0
msgid "Restore Default View"
msgstr "恢復預設檢視畫面"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Restrict a record to a specific company"
msgstr "將記錄限制至特定公司"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
msgid "Retention"
msgstr "保留"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
msgid "Revoke"
msgstr "取消"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.js:0
msgid "Ribbon"
msgstr "絲帶"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/pivot/pivot_editor_sidebar.xml:0
msgid "Row grouping - First level"
msgstr "列分組 - 第一層級"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/pivot/pivot_editor_sidebar.xml:0
msgid "Row grouping - Second level"
msgstr "列分組 - 第二層級"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_rule
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_approver__rule_id
msgid "Rule"
msgstr "規則"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid ""
"Rules with existing entries cannot be deleted since it would delete existing"
" approval entries. You should archive the rule instead."
msgstr "擁有現存記項的規則不可刪除，因為會刪除現存的批核記項。應該改為將規則封存。"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid ""
"Rules with existing entries cannot be modified since it would break existing"
" approval entries. You should archive the rule and create a new one instead."
msgstr "擁有現存記項的規則不可修改，因為會破壞現存的批核記項。應該先將規則封存，然後建立一個新規則。"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
msgid "Run a Server Action"
msgstr "運行伺服器操作"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message_has_sms_error
msgid "SMS Delivery error"
msgstr "簡訊發送錯誤"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_snackbar.xml:0
#: code:addons/web_studio/static/src/client_action/xml_resource_editor/xml_resource_editor.xml:0
msgid "Save"
msgstr "儲存"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid "Save."
msgstr "儲存。"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor_snackbar.xml:0
msgid "Saved"
msgstr "已儲存"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor_snackbar.xml:0
msgid "Saving"
msgstr "儲存中⋯"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_model.js:0
msgid "Saving both some report's parts and full xml is not permitted."
msgstr "不允許同時儲存某些報告的部份及完整xml。"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid "Saving the report \""
msgstr "正在儲存報告 \""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "Search"
msgstr "搜尋"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.xml:0
msgid "Search..."
msgstr "搜尋⋯"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.js:0
msgid "Second Status"
msgstr "第二個狀態"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor_sidebar.xml:0
msgid "Second dimension"
msgstr "第二個維度"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_xml/report_editor_xml.xml:0
msgid "See what changes have been made to this view"
msgstr "看看此檢視畫面有哪些變更"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/field_selector_dialog.xml:0
msgid "Select a Field"
msgstr "選擇一個欄位"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_form_view_quick_create
msgid "Select a group..."
msgstr "選擇一個組別⋯"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.xml:0
msgid "Select a related field"
msgstr "選擇一個相關聯欄位"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.js:0
msgid "Select a related field."
msgstr "選擇一個相關聯欄位。"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_form_view
msgid "Select group"
msgstr "選擇組別"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/map_new_view_dialog.xml:0
msgid ""
"Select the contact field to use to get the coordinates of your records."
msgstr "選擇用作取得記錄座標的聯絡人欄位。"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/field_configuration.xml:0
msgid "Select the model in relation to this one"
msgstr "選擇與此模型相關聯的模型"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/field_configuration.xml:0
msgid "Select the reciprocal ManyToOne field"
msgstr "選擇相對應的ManyToOne欄位"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_form_view
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_form_view_quick_create
msgid "Select users..."
msgstr "選擇使用者⋯"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Selection"
msgstr "選單"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/chatter_properties/chatter_properties.xml:0
msgid "Send a"
msgstr "發送一個"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Send messages, log notes and schedule activities"
msgstr "發送訊息、記錄備註，及安排活動"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.js:0
msgid "Separator"
msgstr "分隔線"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__sequence
msgid "Sequence"
msgstr "序列號"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_actions_server
msgid "Server Action"
msgstr "伺服器動作"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
msgid "Set As Default"
msgstr "設為預設"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_sidebar_legacy/properties/kanban_cover_properties/kanban_cover_properties.xml:0
msgid "Set Cover Image"
msgstr "設定封面圖像"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Set a price or cost on records"
msgstr "設定記錄的價格或成本"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid ""
"Set an <b>email alias</b>. Then, try to send an email to this address; it "
"will create a document automatically for you. Pretty cool, huh?"
msgstr "設定一個<b>電郵別名</b>，然後嘗試向此地址傳送電子郵件。系統會自動為你建立一個文件。很酷吧？"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/sidebar_view_toolbox/sidebar_view_toolbox.xml:0
msgid "Show Invisible Elements"
msgstr "顯示隱藏項目"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid "Show Traceback"
msgstr "顯示追溯"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.js:0
msgid "Show by default"
msgstr "預設顯示"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid "Show in print menu"
msgstr "在列印選單顯示"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "Show link to record"
msgstr "顯示記錄連結"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.js:0
msgid "Side panel"
msgstr "側邊面板"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Signature"
msgstr "簽名"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.js:0
msgid "Slow"
msgstr "慢"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
#: code:addons/web_studio/static/src/approval/approval_hook.js:0
msgid "Some approvals are missing"
msgstr "缺漏一些批核"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid ""
"Some records were skipped because approvals were missing to"
"                                    proceed with your request: "
msgstr "由於缺漏繼續處理你的請求所需的批准，因此已跳過部份記錄："

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_sidebar_legacy/kanban_editor_sidebar_legacy.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "Sort By"
msgstr "排序方式"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/map/map_editor_sidebar.xml:0
msgid "Sort by"
msgstr "排序"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor_sidebar.xml:0
msgid "Sorting"
msgstr "排序"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Sparse"
msgstr "稀疏"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
msgid "Specify all possible values"
msgstr "指定所有可能的值"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor_sidebar.xml:0
msgid "Stacked graph"
msgstr "堆疊圖表"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Stage"
msgstr "階段"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Stage Name"
msgstr "階段名稱"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Stage and visualize records in a custom pipeline"
msgstr "在自訂管道中暫存及視覺化呈現記錄"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Start Date"
msgstr "開始日期"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/new_view_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Start Date Field"
msgstr "開始日期欄位"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__notification_order
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_button_configuration_search_view
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_kanban_view
msgid "Step"
msgstr "跳號"

#. module: web_studio
#: model:ir.model.fields.selection,name:web_studio.selection__studio_approval_rule__notification_order__1
msgid "Step 1"
msgstr "步驟 1"

#. module: web_studio
#: model:ir.model.fields.selection,name:web_studio.selection__studio_approval_rule__notification_order__2
msgid "Step 2"
msgstr "步驟 2"

#. module: web_studio
#: model:ir.model.fields.selection,name:web_studio.selection__studio_approval_rule__notification_order__3
msgid "Step 3"
msgstr "步驟 3"

#. module: web_studio
#: model:ir.model.fields.selection,name:web_studio.selection__studio_approval_rule__notification_order__4
msgid "Step 4"
msgstr "步驟 4"

#. module: web_studio
#: model:ir.model.fields.selection,name:web_studio.selection__studio_approval_rule__notification_order__5
msgid "Step 5"
msgstr "步驟 5"

#. module: web_studio
#: model:ir.model.fields.selection,name:web_studio.selection__studio_approval_rule__notification_order__6
msgid "Step 6"
msgstr "步驟 6"

#. module: web_studio
#: model:ir.model.fields.selection,name:web_studio.selection__studio_approval_rule__notification_order__7
msgid "Step 7"
msgstr "步驟 7"

#. module: web_studio
#: model:ir.model.fields.selection,name:web_studio.selection__studio_approval_rule__notification_order__8
msgid "Step 8"
msgstr "步驟 8"

#. module: web_studio
#: model:ir.model.fields.selection,name:web_studio.selection__studio_approval_rule__notification_order__9
msgid "Step 9"
msgstr "步驟 9"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/new_view_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Stop Date Field"
msgstr "結束日期欄位"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_ir_model_data__studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__studio
msgid "Studio"
msgstr "工作室"

#. module: web_studio
#: model:ir.actions.client,name:web_studio.action_web_studio_app_creator
msgid "Studio App Creator"
msgstr "Studio 應用程式創建者"

#. module: web_studio
#: model:ir.actions.act_window,name:web_studio.studio_approval_entry_action
#: model:ir.ui.menu,name:web_studio.menu_studio_approval_entry
msgid "Studio Approval Entries"
msgstr "Studio 批核記項"

#. module: web_studio
#: model:ir.model,name:web_studio.model_studio_approval_entry
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_form_view
msgid "Studio Approval Entry"
msgstr "Studio 批核記項"

#. module: web_studio
#: model:ir.model,name:web_studio.model_studio_approval_request
msgid "Studio Approval Request"
msgstr "Studio 批核請求"

#. module: web_studio
#: model:ir.model,name:web_studio.model_studio_approval_rule
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_form_view
msgid "Studio Approval Rule"
msgstr "Studio 批核規則"

#. module: web_studio
#: model:ir.actions.act_window,name:web_studio.studio_approval_rule_action
#: model:ir.ui.menu,name:web_studio.menu_studio_approval_rule
msgid "Studio Approval Rules"
msgstr "Studio 批核規則"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_ir_ui_menu__is_studio_configuration
msgid "Studio Configuration Menu"
msgstr "Studio 配置選單"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_customizations_filter
msgid "Studio Customizations"
msgstr "Studio 自訂功能"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.view_studio_export_wizard
msgid "Studio Customizations will be exported"
msgstr "Studio 自訂功能將會匯出"

#. module: web_studio
#: model:ir.actions.act_window,name:web_studio.action_models_to_export
#: model:ir.actions.act_window,name:web_studio.action_studio_export_wizard
#: model:ir.ui.menu,name:web_studio.menu_models_to_export
#: model_terms:ir.ui.view,arch_db:web_studio.models_to_export_form_view
#: model_terms:ir.ui.view,arch_db:web_studio.models_to_export_list
msgid "Studio Export"
msgstr "Studio 匯出"

#. module: web_studio
#: model:ir.actions.client,name:web_studio.studio_export_action
msgid "Studio Export Action"
msgstr "Studio 匯出操作"

#. module: web_studio
#: model:ir.model,name:web_studio.model_studio_export_wizard_data
msgid "Studio Export Data"
msgstr "Studio 匯出數據"

#. module: web_studio
#: model:ir.model,name:web_studio.model_studio_export_model
msgid "Studio Export Models"
msgstr "Studio 匯出模型"

#. module: web_studio
#: model:ir.model,name:web_studio.model_studio_export_wizard
msgid "Studio Export Wizard"
msgstr "Studio 匯出精靈"

#. module: web_studio
#: model:ir.model,name:web_studio.model_studio_mixin
msgid "Studio Mixin"
msgstr "Studio 混入程式"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.xml:0
msgid "Suggested features for your new model"
msgstr "為你的新模型推薦的功能"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/type_widget_properties/type_specific_and_computed_properties.js:0
msgid "Sum"
msgstr "總數"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.js:0
msgid "Sum of %s"
msgstr "%s 總計"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/form_editor_sidebar.js:0
msgid "Tabs"
msgstr "分頁"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Tags"
msgstr "標籤"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
msgid "Technical Name"
msgstr "技術名稱"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Template '%s' not found"
msgstr "找不到範本「%s」"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Text"
msgstr "文字"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
msgid "The fastest way to create a web application."
msgstr "創製網絡應用程式的最快方式。"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "The field %s does not exist."
msgstr "欄位 %s 不存在。"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.xml:0
msgid "The following fields are currently not in the view."
msgstr "以下欄位目前未加入至檢視畫面。"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "The icon has not a correct format"
msgstr "圖示格式不正確"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "The icon is not linked to an attachment"
msgstr "該圖示未連結至附件"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "The method %(method)s does not exist on the model %(model)s."
msgstr "方法 %(method)s 並不存在於模型 %(model)s 上。"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.js:0
msgid "The method %s is private."
msgstr "方法 %s 是私密方法。"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "The model %s does not exist."
msgstr "模型 %s 不存在。"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "The model %s doesn't exist."
msgstr "模型 %s 不存在。"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "The model %s doesn't support adding fields."
msgstr "%s 模型不支援新增欄位。"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "The operation  type \"%s\" is not supported"
msgstr "不支援「%s」操作類型"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "The related field of a button has to be a many2one to %s."
msgstr "按鈕的相關聯欄位，必須是目標為 %s 的many2one。"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid ""
"The report could not be loaded as some error occured. Usually it means that "
"some view inherits from another but targets a node that doesn't exist. It "
"might be due to the mutations of the base views during the upgrade process."
msgstr ""
"由於發生某些錯誤，未能載入該報告。通常這意味某個檢視畫面繼承自另一個檢視畫面，但目標節點不存在。這可能是由於升級過程中，基本檢視畫面突變而造成。"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_model.js:0
msgid "The report is in error. Only editing the XML sources is permitted"
msgstr "報告有錯誤。只允許編輯 XML 來源"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/studio_view.js:0
msgid ""
"The requested change caused an error in the view. It could be because a "
"field was deleted, but still used somewhere else."
msgstr "要求的變更引致檢視畫面中出現錯誤。可能是因為某個欄位已被刪除，但其他地方仍使用它。"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__message
msgid ""
"The step description will be displayed on the button on which an approval is"
" requested."
msgstr "步驟描述將顯示在請求批准的按鈕上。"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
msgid ""
"The user who approves this step will not be able to approve other steps for "
"the same record."
msgstr "批准此步驟的使用者，將不可批准同一記錄的其他步驟。"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__approval_group_id
msgid "The users in this group are able to approve or reject the step."
msgstr "此群組的使用者，可批准或拒絕該步驟。"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid ""
"There are %s records using selection values not listed in those you are trying to save.\n"
"Are you sure you want to remove the selection values of those records?"
msgstr ""
"有 %s 記錄使用的選取值，未包含在你嘗試儲存的記錄中。\n"
"確定要移除該些記錄的選取值？"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid ""
"There is no method %(method)s on the model %(model_name)s (%(model_id)s)"
msgstr "沒有方法 %(method)s 存在於模型 %(model_name)s（%(model_id)s）上。"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
msgid ""
"There is no record on which this report can be previewed. Create at least "
"one record to preview the report."
msgstr "沒有可以用此報告預覽的記錄。請建立最少一項記錄，以預覽報告。"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid "There is no record to preview"
msgstr "沒有記錄可以預覽"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__approver_ids
msgid ""
"These users are able to approve or reject the step and will be assigned to "
"an activity when their approval is requested."
msgstr "這些使用者可批准或拒絕該步驟，並在他們請求批准時，獲指派至一項活動。"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__users_to_notify
msgid ""
"These users will receive a notification via internal note when the step is "
"approved or rejected"
msgstr "步驟獲批准或被拒絕時，這些使用者將透過內部備註收到通知"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.js:0
msgid "Third Status"
msgstr "第三個狀態"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/navbar.js:0
#: code:addons/web_studio/static/src/client_action/studio_home_menu/studio_home_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "This action is not editable by Studio"
msgstr "Studio 無法編輯此操作"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid ""
"This approval or the one you already submitted limits you to a single approval on this action.\n"
"Another user is required to further approve this action."
msgstr ""
"此批核或你已提交的批核，限制你只可對此操作進行一次批准。\n"
"若要進一步批核此操作，需要另一位用戶。"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid ""
"This could also be due to the absence of a real record to render the report "
"with."
msgstr "也可能是由於缺乏用作繪製報告的真實記錄。"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid "This may be due to an incorrect syntax in the edited parts."
msgstr "可能是因為已編輯部份內，出現不正確的語法。"

#. module: web_studio
#: model:ir.model.constraint,message:web_studio.constraint_studio_export_model_unique_model
msgid "This model is already being exported."
msgstr "此模型已經正在匯出。"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor_model.js:0
msgid "This operation caused an error, probably because a xpath was broken"
msgstr "此操作引致錯誤，可能是因為 xpath 損壞"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "This rule has already been approved/rejected."
msgstr "此規則已被批准/拒絕。"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
msgid "This rule limits this user to a single approval for this action."
msgstr "此規則限制此使用者只准對此操作進行一次批核。"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor_sidebar.xml:0
msgid "Timeline"
msgstr "時間軸"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.js:0
msgid "Timeline views"
msgstr "時間線檢視畫面"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid "To <b>customize a field</b>, click on its <i>label</i>."
msgstr "若想<b>對欄位進行自訂</b>，請按下其<b>標籤</b>。"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/systray_item/systray_item.xml:0
msgid "Toggle Studio"
msgstr "切換開關 Studio"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Total"
msgstr "總計"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor_sidebar.xml:0
msgid "Type"
msgstr "類型"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Type down your notes here..."
msgstr "此處輸入備註⋯"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor_snackbar.xml:0
msgid "Undo"
msgstr "復原"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Unsupported operator '%s' to search action_xmlid"
msgstr "不支援以運算符「%s」搜尋 action_xmlid"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_delegate__date_to
msgid "Until"
msgstr "直至"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Until %s"
msgstr "直至 %s"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__updatable
msgid "Updatable"
msgstr "可更新"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/class_attribute/class_attribute.js:0
msgid ""
"Use Bootstrap or any other custom classes to customize the style and the "
"display of the element."
msgstr "使用 Bootstrap 或任何其他自訂類（class），自訂網頁元素的樣式及顯示方式。"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_approver__user_id
msgid "User"
msgstr "使用者"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "User assignment"
msgstr "使用者分配"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/studio_approval.xml:0
msgid "User avatar placeholder"
msgstr "使用者頭像佔位符"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__users_to_notify
msgid "Users to Notify"
msgstr "要通知的使用者"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/xml_resource_editor/xml_resource_editor.xml:0
msgid "Uses:"
msgstr "使用："

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
msgid "Value"
msgstr "值"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
msgid "Value of list"
msgstr "列表值"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor_sidebar.js:0
#: model:ir.model,name:web_studio.model_ir_ui_view
msgid "View"
msgstr "檢視"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor.js:0
msgid "View Editor"
msgstr "查看編輯器"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid "View in Error:"
msgstr "在錯誤中檢視："

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
msgid "Views"
msgstr "視圖"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/studio_approval.xml:0
msgid "Waiting for approval"
msgstr "等待批核"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid "Want more fun? Let's create more <b>views</b>."
msgstr "想要更多樂趣嗎？讓我們建立其他<b>檢視畫面</b>。"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "Webhook Automations"
msgstr "網絡鈎子自動化操作"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
msgid "Webhooks"
msgstr "網絡鈎子"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__website_message_ids
msgid "Website Messages"
msgstr "網站資訊"

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__website_message_ids
msgid "Website communication history"
msgstr "網站溝通記錄"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Week"
msgstr "星期"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Week (expanded)"
msgstr "星期（展開）"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Week Precision"
msgstr "星期精確度"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
msgid "Welcome to"
msgstr "歡迎來到"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid "What about a <b>Kanban view</b>?"
msgstr "不如試試<b>看板檢視畫面</b>？"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
msgid "What should the button do"
msgstr "按鈕應該做的行為"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "When Creating Record"
msgstr "建立記錄時"

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__can_validate
msgid "Whether the rule can be approved by the current user"
msgstr "目前使用者是否可以批准該規則"

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_ir_model__abstract
msgid "Whether this model is abstract"
msgstr "此模型是否抽象"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/views/kanban_report/new_report_dialog.xml:0
msgid "Which type of report do you want to create?"
msgstr "你想建立哪種類型的報表？"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/type_widget_properties/type_widget_properties.xml:0
msgid "Widget"
msgstr "小工具"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid ""
"Wow, nice! And I'm sure you can make it even better! Use this icon to open "
"<b>Odoo Studio</b> and customize any screen."
msgstr "嘩，做得很好！相信你可以令它的功能再提升！請使用此圖示開啟 <b>Odoo Studio</b>，以自訂任何畫面。"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Write additional notes or comments"
msgstr "輸入額外備註或留言"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/sidebar_view_toolbox/sidebar_view_toolbox.xml:0
msgid "XML"
msgstr "XML"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Year"
msgstr "年"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "You can not approve this rule."
msgstr "你沒有權限批准此規則。"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid ""
"You cannot cancel an approval you didn't set yourself or you don't belong to"
" an higher level rule's approvers."
msgstr "若批核不是由你自己設定，或你不屬於較高層級的規則批核人，你便不可取消該項批核。"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.js:0
msgid "You cannot deactivate this view as it is the last one active."
msgstr "不可停用此檢視畫面，因為它是最後一個生效的檢視畫面。"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_record_legacy.js:0
msgid "You first need to create a many2many field in the form view."
msgstr "你需要先在表單檢視畫面中，建立一個many2many欄位。"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "You just like to break things, don't you?"
msgstr "You just like to break things, don't you?"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
msgid "action"
msgstr "動作"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "delegates to %s."
msgstr "委派至 %s。"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/chatter_properties/chatter_properties.xml:0
msgid "domain not defined"
msgstr "未定義網域"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.xml:0
msgid "e.g. Properties"
msgstr "例：屬性"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
msgid "e.g. Real Estate"
msgstr "例：房地產"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "no one"
msgstr "沒有人"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
msgid "object"
msgstr "物件"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/icon_creator/icon_creator.xml:0
msgid "or"
msgstr "或"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "studio_approval attribute can only be set in form views"
msgstr "studio_approval 屬性只可在表單檢視畫面設定"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/chatter_properties/chatter_properties.xml:0
msgid "test email"
msgstr "測試電郵"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "to %s."
msgstr "至 %s。"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/icon_creator/icon_creator.xml:0
msgid "upload it"
msgstr "上載它"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/icon_creator/icon_creator.xml:0
msgid "upload one"
msgstr "上載一個"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
msgid "x_studio_"
msgstr "x_studio_"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
msgid "{{ item.isInEdition ? 'Add selection' : 'Edit selection' }}"
msgstr "{{ item.isInEdition ? '加入選取項' : '編輯選取項' }}"
