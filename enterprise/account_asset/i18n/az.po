# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_asset
# 
# Translators:
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:25+0000\n"
"PO-Revision-Date: 2024-09-25 09:43+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Azerbaijani (https://app.transifex.com/odoo/teams/41243/az/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: az\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__total_depreciation_entries_count
msgid "# Depreciation Entries"
msgstr "# Amortizasiya Qeydləri"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__gross_increase_count
msgid "# Gross Increases"
msgstr "# Ümumi Artımlar"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__depreciation_entries_count
msgid "# Posted Depreciation Entries"
msgstr "# Paylaşılmış Amortizasiya Qeydləri"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "%(asset)s: Disposal"
msgstr ""

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "%(asset)s: Sale"
msgstr ""

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_assets_report.py:0
msgid "%(months)s m"
msgstr ""

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_move.py:0
msgid "%(move_line)s (%(current)s of %(total)s)"
msgstr ""

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_assets_report.py:0
msgid "%(years)s y"
msgstr ""

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "%s (copy)"
msgstr "%s (surət)"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid ""
"%s Future entries will be recomputed to depreciate the asset following the "
"changes."
msgstr ""

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_move.py:0
msgid "%s: Depreciation"
msgstr ""

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_assets_report.py:0
msgid "(No %s)"
msgstr ""

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "(incl."
msgstr ""

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid ""
"A depreciation entry will be posted on and including the date %(date)s. "
"<br/> %(extra_text)s Future entries will be recomputed to depreciate the "
"asset following the changes."
msgstr ""

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid ""
"A depreciation entry will be posted on and including the date %(date)s.<br/>"
" A disposal entry will be posted on the %(account_type)s account "
"<b>%(account)s</b>."
msgstr ""

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid ""
"A depreciation entry will be posted on and including the date %(date)s.<br/>"
" A second entry will neutralize the original income and post the  outcome of"
" this sale on account <b>%(account)s</b>."
msgstr ""

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "A depreciation entry will be posted on and including the date %s."
msgstr ""

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "A document linked to %(move_line_name)s has been deleted: %(link)s"
msgstr ""

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "A document linked to this move has been deleted: %s"
msgstr ""

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "A gross increase has been created: %(link)s"
msgstr ""

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid ""
"A non deductible tax value of %(tax_value)s was added to %(name)s's initial "
"value of %(purchase_value)s"
msgstr ""

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_account
msgid "Account"
msgstr "Hesab"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__account_type
msgid ""
"Account Type is used for information purpose, to generate country-specific "
"legal reports, and set the rules to close a fiscal year and generate opening"
" entries."
msgstr ""
"Hesab növü məlumatlandırma məqsədilə, ölkə üçün nəzərdə tutulan hüquqi "
"hesabatların və maliyyə ilinin bağlanması və açılış qeydlərinin yaradılması "
"üçün qaydaların yaradılması üçün istifadə olunur."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__account_depreciation_id
msgid "Account used in the depreciation entries, to decrease the asset value."
msgstr ""
"Dəyərin düşməsi qeydində aktivin dəyərini azaltmaq üçün istifadə olunan "
"hesab."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__account_depreciation_expense_id
msgid ""
"Account used in the periodical entries, to record a part of the asset as "
"expense."
msgstr ""
"Aktivin bir hissəsini xərc kimi qeyd etmək üçün dövri qeydlərdə istifadə "
"olunan hesab."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__account_asset_id
msgid ""
"Account used to record the purchase of the asset at its original price."
msgstr "Aktivin ilkin qiyməti ilə alınması qeydi üçün istifadə olunan hesab."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_asset_modify__gain_account_id
msgid "Account used to write the journal item in case of gain"
msgstr ""
"Mənfəət əldə edildikdə jurnalın bəndini yazmaq üçün istifadə olunan hesab"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_res_company__gain_account_id
msgid ""
"Account used to write the journal item in case of gain while selling an "
"asset"
msgstr ""
"Aktivin satışı zamanı mənfəət əldə edildikdə jurnal bəndini yazmaq üçün "
"istifadə olunan hesab"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_asset_modify__loss_account_id
msgid "Account used to write the journal item in case of loss"
msgstr "Zərər zamanı jurnal bəndini yazmaq üçün istifadə olunan hesab"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_res_company__loss_account_id
msgid ""
"Account used to write the journal item in case of loss while selling an "
"asset"
msgstr ""
"Aktivin satışı zamanı zərər dəyibsə, jurnalın bəndini yazmaq üçün istifadə "
"olunan hesab"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Accounting"
msgstr "Mühasibat Uçotu"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_report
msgid "Accounting Report"
msgstr "Muhasibat hesabatı"

#. module: account_asset
#: model:account.report.column,name:account_asset.assets_report_acquisition_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset__acquisition_date
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_kanban
msgid "Acquisition Date"
msgstr "Qəbul Tarixi"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__modify_action
msgid "Action"
msgstr "Əməliyyat"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_needaction
msgid "Action Needed"
msgstr "Gərəkli Əməliyyat"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__active
msgid "Active"
msgstr "Aktiv"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_ids
msgid "Activities"
msgstr "Fəaliyyətlər"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Faəliyyət istisnaetmə İşarəsi"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_state
msgid "Activity State"
msgstr "Fəaliyyət Statusu"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_type_icon
msgid "Activity Type Icon"
msgstr "Fəaliyyət Növü ikonu"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Add an internal note"
msgstr ""

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
#: code:addons/account_asset/models/account_move.py:0
msgid "All the lines should be from the same account"
msgstr "Bütün sətirlər eyni hesabdan olmalıdılar"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_move.py:0
msgid "All the lines should be from the same company"
msgstr ""

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
#: code:addons/account_asset/models/account_move.py:0
msgid "All the lines should be posted"
msgstr "Bütün sətirlər yerləşdirilməlidir"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__already_depreciated_amount_import
msgid "Already Depreciated Amount Import"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__parent_id
msgid "An asset has a parent when it is the result of gaining value"
msgstr "Bu dəyəri əldə etdiyiniz zaman aktivlərin əsası olur"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "An asset has been created for this move:"
msgstr "Bu hərəkət üçün aktiv yaradıldı:"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_account__asset_model_ids
msgid ""
"An asset wil be created for each asset model when this account is used on a "
"vendor bill or a refund"
msgstr ""

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "An asset will be created for the value increase of the asset. <br/>"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__analytic_distribution
msgid "Analytic Distribution"
msgstr "Analitik bölüşdürmə"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__analytic_precision
msgid "Analytic Precision"
msgstr "Analitik Dəqiqlik"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Archived"
msgstr "Arxivləndi"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
#: code:addons/account_asset/models/account_move.py:0
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_id
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_id
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__asset_id
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Asset"
msgstr "Aktiv"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Asset Account"
msgstr "Aktivin Hesabı"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "Asset Cancelled"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__account_asset_counterpart_id
msgid "Asset Counterpart Account"
msgstr ""

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_asset_group
#: model:ir.model.fields,field_description:account_asset.field_account_asset__asset_group_id
#: model_terms:ir.ui.view,arch_db:account_asset.asset_group_form_view
#: model_terms:ir.ui.view,arch_db:account_asset.asset_group_list_view
msgid "Asset Group"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_id_display_name
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_id_display_name
msgid "Asset Id Display Name"
msgstr "Aktivin ŞV Göstərilən Adı"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__asset_lifetime_days
msgid "Asset Lifetime Days"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_account__asset_model_ids
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Asset Model"
msgstr "Aktiv Modeli"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Asset Model name"
msgstr "Aktiv Modelinin adı"

#. module: account_asset
#: model:ir.actions.act_window,name:account_asset.action_account_asset_model_form
#: model:ir.ui.menu,name:account_asset.menu_action_account_asset_model_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_tree
msgid "Asset Models"
msgstr "Aktiv Modelləri"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_move_type
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_move_type
msgid "Asset Move Type"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__name
msgid "Asset Name"
msgstr "Aktivin Adı"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__asset_paused_days
msgid "Asset Paused Days"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_value_change
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_value_change
msgid "Asset Value Change"
msgstr "Aktivlərin Dəyərinin Dəyişikliyi"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Asset Values"
msgstr "Aktivlərin Dəyərləri"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "Asset created"
msgstr ""

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_move.py:0
msgid "Asset created from invoice: %s"
msgstr ""

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "Asset disposed. %s"
msgstr ""

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "Asset paused. %s"
msgstr ""

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "Asset sold. %s"
msgstr ""

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "Asset unpaused. %s"
msgstr ""

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.asset_group_form_view
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_move_form_asset_inherit
msgid "Asset(s)"
msgstr "Aktiv(lər)"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_asset
msgid "Asset/Revenue Recognition"
msgstr "Aktivin/Gəlirin Tanınması"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_assets_report.py:0
#: model:ir.actions.act_window,name:account_asset.action_account_asset_form
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_ids
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_ids
#: model:ir.ui.menu,name:account_asset.menu_action_account_asset_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_tree
msgid "Assets"
msgstr "Əsas Vəsaitlər"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_asset_report_handler
msgid "Assets Report Custom Handler"
msgstr ""

#. module: account_asset
#: model:ir.ui.menu,name:account_asset.menu_finance_config_assets
msgid "Assets and Revenues"
msgstr "Aktivlər və Gəlirlər"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Assets in closed state"
msgstr "Qapalı vəziyyətdə aktivlər"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Assets in draft and open states"
msgstr "Qaralama və açıq vəziyyətdə olan aktivlər"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid ""
"Atleast one asset (%s) couldn't be set as running because it lacks any "
"required information"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_attachment_count
msgid "Attachment Count"
msgstr "Qoşma Sayı"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Automate Asset"
msgstr "Aktivi Avtomatlaşdırın"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Automation"
msgstr "Avtomatlaşdırma"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__prorata_computation_type__daily_computation
msgid "Based on days per period"
msgstr ""

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Bills"
msgstr "Fakturalar"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_assets_report.py:0
#: model:ir.model.fields,field_description:account_asset.field_account_asset__book_value
msgid "Book Value"
msgstr "Mühasibat uçotu üzrə müəyyən edilmiş aktivlərinin məbləği"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_account__can_create_asset
msgid "Can Create Asset"
msgstr "Aktiv yarada bilər"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Cancel"
msgstr "Ləğv edin"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Cancel Asset"
msgstr ""

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__state__cancelled
msgid "Cancelled"
msgstr "Ləğv olundu"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_assets_report.py:0
msgid "Characteristics"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__children_ids
msgid "Children"
msgstr "Alt Qruplar"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__method
msgid ""
"Choose the method to use to compute the amount of depreciation lines.\n"
"  * Straight Line: Calculated on basis of: Gross Value / Duration\n"
"  * Declining: Calculated on basis of: Residual Value * Declining Factor\n"
"  * Declining then Straight Line: Like Declining but with a minimum depreciation value equal to the straight line value."
msgstr ""

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__state__close
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Closed"
msgstr "Bağlandı"

#. module: account_asset
#: model:ir.model,name:account_asset.model_res_company
msgid "Companies"
msgstr "Şirkətlər"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__company_id
#: model:ir.model.fields,field_description:account_asset.field_account_asset_group__company_id
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__company_id
msgid "Company"
msgstr "Şirkət"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__prorata_computation_type
msgid "Computation"
msgstr "Hesablama"

#. module: account_asset
#: model:ir.actions.server,name:account_asset.action_account_asset_compute_depreciations
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Compute Depreciation"
msgstr "Dəyərin Düşməsinin Hesablanması"

#. module: account_asset
#: model:ir.actions.server,name:account_asset.action_account_asset_run
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Confirm"
msgstr "Təsdiq edin"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__prorata_computation_type__constant_periods
msgid "Constant Periods"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__count_asset
#: model:ir.model.fields,field_description:account_asset.field_account_move__count_asset
msgid "Count Asset"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__count_linked_asset
msgid "Count Linked Asset"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_group__count_linked_assets
msgid "Count Linked Assets"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__country_code
msgid "Country Code"
msgstr "Ölkə Kodu"

#. module: account_asset
#: model:ir.actions.server,name:account_asset.action_account_aml_to_asset
#: model:ir.model.fields,field_description:account_asset.field_account_account__create_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_move_line_tree
msgid "Create Asset"
msgstr "Aktiv yarat"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_account__create_asset__validate
msgid "Create and validate"
msgstr "Yarat və təsdiq et"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_account__create_asset__draft
msgid "Create in draft"
msgstr "Layihədə yarat"

#. module: account_asset
#: model_terms:ir.actions.act_window,help:account_asset.action_account_asset_form
msgid "Create new asset"
msgstr ""

#. module: account_asset
#: model_terms:ir.actions.act_window,help:account_asset.action_account_asset_model_form
msgid "Create new asset model"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__create_uid
#: model:ir.model.fields,field_description:account_asset.field_account_asset_group__create_uid
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__create_uid
msgid "Created by"
msgstr "Tərəfindən yaradılıb"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__create_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset_group__create_date
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__create_date
msgid "Created on"
msgstr "Tarixdə yaradıldı"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_depreciated_value
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_depreciated_value
msgid "Cumulative Depreciation"
msgstr "Proqressiv Amortizasiya"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__currency_id
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__currency_id
msgid "Currency"
msgstr "Valyuta"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Current"
msgstr "Cari"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Current Values"
msgstr "Cari Dəyərlər"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__invoice_ids
msgid "Customer Invoice"
msgstr "Müştəri Qaiməsi"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__date
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Date"
msgstr "Tarix"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_depreciation_beginning_date
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_depreciation_beginning_date
msgid "Date of the beginning of the depreciation"
msgstr ""

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_assets_report.py:0
msgid "Dec. then Straight"
msgstr ""

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_assets_report.py:0
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__method__degressive
msgid "Declining"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__method_progress_factor
msgid "Declining Factor"
msgstr ""

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__method__degressive_then_linear
msgid "Declining then Straight Line"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__value_residual
msgid "Depreciable Amount"
msgstr "Amortizasiya Olunan Məbləğ"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__value_residual
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_remaining_value
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_remaining_value
msgid "Depreciable Value"
msgstr "Amortizasiya Olunan Dəyər"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Depreciated Amount"
msgstr ""

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_assets_report.py:0
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__depreciation_value
#: model:ir.model.fields,field_description:account_asset.field_account_move__depreciation_value
#: model:ir.model.fields.selection,name:account_asset.selection__account_move__asset_move_type__depreciation
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Depreciation"
msgstr "Dəyərin düşməsi"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__account_depreciation_id
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__account_depreciation_id
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
msgid "Depreciation Account"
msgstr "Amortizasiya Hesabı"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Depreciation Board"
msgstr "Amortizasiya Komissiyası"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Depreciation Date"
msgstr "Amortizasiya Tarixi"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__depreciation_move_ids
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Depreciation Lines"
msgstr "Amortizasiya Xətləri"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Depreciation Method"
msgstr "Amortizasiya Üsulu"

#. module: account_asset
#: model:account.report,name:account_asset.assets_report
#: model:ir.actions.client,name:account_asset.action_account_report_assets
#: model:ir.ui.menu,name:account_asset.menu_action_account_report_assets
msgid "Depreciation Schedule"
msgstr ""

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "Depreciation board modified %s"
msgstr ""

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_move.py:0
msgid "Depreciation entry %(name)s posted (%(value)s)"
msgstr ""

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_move.py:0
msgid "Depreciation entry %(name)s reversed (%(value)s)"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__display_account_asset_id
msgid "Display Account Asset"
msgstr "Hesab Aktivini Göstərin"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__display_name
#: model:ir.model.fields,field_description:account_asset.field_account_asset_group__display_name
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__display_name
msgid "Display Name"
msgstr "Göstəriləcək Ad"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_move__asset_move_type__disposal
msgid "Disposal"
msgstr "Silinmə"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__disposal_date
msgid "Disposal Date"
msgstr "Silinmə Tarixi"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "Disposal Move"
msgstr "Silinmə Addımı"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "Disposal Moves"
msgstr "Silinmə Addımları"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Dispose"
msgstr "Yerləşdirmək"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__distribution_analytic_account_ids
msgid "Distribution Analytic Account"
msgstr ""

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__state__draft
msgid "Draft"
msgstr "Qaralama"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__draft_asset_exists
#: model:ir.model.fields,field_description:account_asset.field_account_move__draft_asset_exists
msgid "Draft Asset Exists"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__method_number
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__method_number
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_kanban
msgid "Duration"
msgstr "Müddət"

#. module: account_asset
#: model:account.report.column,name:account_asset.assets_report_duration_rate
msgid "Duration / Rate"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__account_depreciation_expense_id
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__account_depreciation_expense_id
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
msgid "Expense Account"
msgstr "Xərclərin Uçotu"

#. module: account_asset
#: model:account.report.column,name:account_asset.assets_report_first_depreciation
msgid "First Depreciation"
msgstr "İlk Amortizasiya "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__account_asset_id
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
msgid "Fixed Asset Account"
msgstr "Aktiv Hesabı uyğunlaşdırın"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_follower_ids
msgid "Followers"
msgstr "İzləyicilər"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_partner_ids
msgid "Followers (Partners)"
msgstr "İzləyicilər (Tərəfdaşlar)"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Gözəl şriftli ikon, məsələn fa-tapşırıqlar"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_account__form_view_ref
msgid "Form View Ref"
msgstr "Forma "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Future Activities"
msgstr "Gələcək Fəaliyyətlər"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__asset_modify__gain_or_loss__gain
msgid "Gain"
msgstr "Mənfəət"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__gain_account_id
#: model:ir.model.fields,field_description:account_asset.field_res_company__gain_account_id
msgid "Gain Account"
msgstr "Mənfəət Hesabı "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__gain_or_loss
msgid "Gain Or Loss"
msgstr "Mənfəət və ya Zərər"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__gain_value
msgid "Gain Value"
msgstr "Mənfəət Dəyəri "

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Gross Increase"
msgstr "Ümumi Artım"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__account_asset_id
msgid "Gross Increase Account"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__gross_increase_value
msgid "Gross Increase Value"
msgstr "Ümumi artım dəyəri"

#. module: account_asset
#. odoo-javascript
#: code:addons/account_asset/static/src/components/depreciation_schedule/groupby.xml:0
msgid "Group By Account"
msgstr ""

#. module: account_asset
#. odoo-javascript
#: code:addons/account_asset/static/src/components/depreciation_schedule/groupby.xml:0
msgid "Group By Asset Group"
msgstr ""

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Group By..."
msgstr "Aşağıdakılara görə Qrupla..."

#. module: account_asset
#. odoo-javascript
#: code:addons/account_asset/static/src/components/depreciation_schedule/groupby.xml:0
msgid "Group by Account"
msgstr ""

#. module: account_asset
#. odoo-javascript
#: code:addons/account_asset/static/src/components/depreciation_schedule/groupby.xml:0
msgid "Group by Asset Group"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__has_message
msgid "Has Message"
msgstr "Mesajı Var"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__id
#: model:ir.model.fields,field_description:account_asset.field_account_asset_group__id
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__id
msgid "ID"
msgstr "ID"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_exception_icon
msgid "Icon"
msgstr "Simvol"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "İstisna fəaliyyəti göstərən simvol."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__message_needaction
msgid "If checked, new messages require your attention."
msgstr "İşarələnibsə, yeni mesajlara baxmalısınız."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__message_has_error
#: model:ir.model.fields,help:account_asset.field_account_asset__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "İşarələnibsə, bəzi mesajların çatdırılmasında xəta var."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__already_depreciated_amount_import
msgid ""
"In case of an import from another software, you might need to use this field"
" to have the right depreciation table report. This is the value that was "
"already depreciated with entries not computed from this model"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__informational_text
msgid "Informational Text"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__invoice_line_ids
msgid "Invoice Line"
msgstr "Faktura Xətti"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_is_follower
msgid "Is Follower"
msgstr "İzləyicidir"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__salvage_value
#: model:ir.model.fields,help:account_asset.field_account_asset__salvage_value_pct
msgid "It is the amount you plan to have that you cannot depreciate."
msgstr ""
"Bu sizin əldə etməyə planlaşdırdığınız məbləğdir və onun amortizasiyası "
"mümkün deyil."

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__journal_id
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
msgid "Journal"
msgstr "Jurnal"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "Journal Entries"
msgstr "Jurnal Girişləri"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_move
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Journal Entry"
msgstr "Jurnal Girişi"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_move_line
msgid "Journal Item"
msgstr "Jurnal Sətirləri"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
#: model:ir.model.fields,field_description:account_asset.field_account_asset__original_move_line_ids
msgid "Journal Items"
msgstr "Jurnal Sətirləri"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_move.py:0
msgid ""
"Journal Items of %(account)s should have a label in order to generate an "
"asset"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__write_uid
#: model:ir.model.fields,field_description:account_asset.field_account_asset_group__write_uid
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__write_uid
msgid "Last Updated by"
msgstr "Son Yeniləyən"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__write_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset_group__write_date
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__write_date
msgid "Last Updated on"
msgstr "Son Yenilənmə tarixi"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Late Activities"
msgstr "Ən son Əməliyyatlar"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_assets_report.py:0
msgid "Linear"
msgstr "Xətti"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__linked_assets_ids
msgid "Linked Assets"
msgstr ""

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__asset_modify__gain_or_loss__loss
msgid "Loss"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__loss_account_id
#: model:ir.model.fields,field_description:account_asset.field_res_company__loss_account_id
msgid "Loss Account"
msgstr "Zərər Uçotu"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Manage Items"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_has_error
msgid "Message Delivery error"
msgstr "Mesajın Çatdırılmasında xəta"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_ids
msgid "Messages"
msgstr "Mesajlar"

#. module: account_asset
#: model:account.report.column,name:account_asset.assets_report_first_method
#: model:ir.model.fields,field_description:account_asset.field_account_asset__method
msgid "Method"
msgstr "Metod"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__model_id
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__state__model
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_kanban
msgid "Model"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__asset_properties_definition
msgid "Model Properties"
msgstr ""

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Modify"
msgstr "Modifikasiya Edin"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
#: model:ir.model,name:account_asset.model_asset_modify
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Modify Asset"
msgstr "Mülkü Modifikasiya Edin"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Modify Depreciation"
msgstr "Dəyər İtkilərini Modifikasiya Edin"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__method_period__1
#: model:ir.model.fields.selection,name:account_asset.selection__asset_modify__method_period__1
msgid "Months"
msgstr "Aylar"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_account__multiple_assets_per_line
msgid "Multiple Assets per Line"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_account__multiple_assets_per_line
msgid ""
"Multiple asset items will be generated depending on the bill line quantity "
"instead of 1 global asset."
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Mənim Fəaliyyətlərimin Son Tarixi "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_group__name
msgid "Name"
msgstr "Ad"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_move__asset_move_type__negative_revaluation
msgid "Negative revaluation"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__net_gain_on_sale
msgid "Net gain on sale"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__net_gain_on_sale
msgid "Net value of gain or loss on sale of an asset"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_asset_modify__value_residual
msgid "New residual amount for the asset"
msgstr "Aktiv üzrə yeni qalıq məbləğ"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_asset_modify__salvage_value
msgid "New salvage amount for the asset"
msgstr "Aktiv üçün yeni xilasetmə məbləği"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Növbəti Fəaliyyət Təqvimi Tədbiri"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Növbəti Fəaliyyətin Son Tarixi"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_summary
msgid "Next Activity Summary"
msgstr "Növbəti Fəaliyyət Xülasəsi"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_type_id
msgid "Next Activity Type"
msgstr "Yeni Fəaliyyət Növü"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_account__create_asset__no
#: model:ir.model.fields.selection,name:account_asset.selection__asset_modify__gain_or_loss__no
msgid "No"
msgstr "Tapılmayanlar"

#. module: account_asset
#. odoo-javascript
#: code:addons/account_asset/static/src/components/depreciation_schedule/groupby.xml:0
msgid "No Grouping"
msgstr ""

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__prorata_computation_type__none
msgid "No Prorata"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__non_deductible_tax_value
#: model:ir.model.fields,field_description:account_asset.field_account_move_line__non_deductible_tax_value
msgid "Non Deductible Tax Value"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__salvage_value
msgid "Not Depreciable Amount"
msgstr "Dəyərini İtirməyən məbləğ"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__salvage_value
msgid "Not Depreciable Value"
msgstr "Dəyərini İtirməyən Məbləğ"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__salvage_value_pct
msgid "Not Depreciable Value Percent"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__name
msgid "Note"
msgstr "Qeyd"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_needaction_counter
msgid "Number of Actions"
msgstr "Hərəkətlərin sayı"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_tree
msgid "Number of Depreciations"
msgstr "Dəyər İtkilərinin Sayı"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__method_period
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__method_period
msgid "Number of Months in a Period"
msgstr "Dövr üzrə Ayların Sayı"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__gross_increase_count
msgid "Number of assets made to increase the value of the asset"
msgstr "Aktivin dəyərini artırmaq üçün yaradılan aktivlərin sayı"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_number_days
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_number_days
msgid "Number of days"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__total_depreciation_entries_count
msgid "Number of depreciation entries (posted or not)"
msgstr ""
"Dəyər İtkiləri üzrə daxietmələrinin sayı (göndərilən və ya göndərilməyən)"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_has_error_counter
msgid "Number of errors"
msgstr "Xətaların sayı"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Əməliyyat tələb edən mesajların sayı"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Çatdırılma xətası olan mesajların sayı"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__state__paused
msgid "On Hold"
msgstr "Saxlanılma Haqqında "

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_assets_report.py:0
msgid "Open Asset"
msgstr "Aktivi Açın"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__original_value
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_kanban
msgid "Original Value"
msgstr "Orijinal Qiymət "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__parent_id
msgid "Parent"
msgstr "Əsas"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Parent Asset"
msgstr ""

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Pause"
msgstr "Dayandır"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__paused_prorata_date
msgid "Paused Prorata Date"
msgstr ""

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_tree
msgid "Period length"
msgstr "Dövrün davam etdiyi müddət"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_move__asset_move_type__positive_revaluation
msgid "Positive revaluation"
msgstr ""

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Posted Entries"
msgstr "Göndərilmiş Girişlər "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__asset_properties
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Properties"
msgstr "Xüsusiyyətlər"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__prorata_date
msgid "Prorata Date"
msgstr "Mütənasib Tarix"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_move__asset_move_type__purchase
msgid "Purchase"
msgstr "Satın alın"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__rating_ids
msgid "Ratings"
msgstr "Qiymətləndirmələr"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "Re-evaluate"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_group__linked_asset_ids
#: model:ir.model.fields,field_description:account_asset.field_account_move_line__asset_ids
#: model_terms:ir.ui.view,arch_db:account_asset.view_move_line_form_asset_inherit
msgid "Related Assets"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__related_purchase_value
msgid "Related Purchase Value"
msgstr ""

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "Reset to running"
msgstr "Başlamaq üçün sıfırla"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_user_id
msgid "Responsible User"
msgstr "Məsul İstifadəçi"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Resume"
msgstr ""

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Resume Depreciation"
msgstr "Amortizasiyanı Bərpa Et"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid ""
"Reverse the depreciation entries posted in the future in order to modify the"
" depreciation"
msgstr ""
"Amortizasiyanı dəyişmək üçün gələcəkdə yerləşdirilmiş amortizasiya "
"qeydlərini ləğv edin"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__state__open
msgid "Running"
msgstr "Qüvvədə"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS-in Çatdırılmasında xəta"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_move__asset_move_type__sale
msgid "Sale"
msgstr "Satış"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Save as Model"
msgstr ""

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "Save model"
msgstr "Modeli saxlayın"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__select_invoice_line_id
msgid "Select Invoice Line"
msgstr "Faktura Xəttini Seçin"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Sell"
msgstr "Satış"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Set to Draft"
msgstr "Qaralama kimi Təyin Edin"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Set to Running"
msgstr "Fəaliyyətdədir kimi Təyin Edin"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Show all records which has next action date is before today"
msgstr "Növbəti fəaliyyət tarixi bu günə qədər olan bütün qeydləri göstərin"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_move.py:0
msgid "Some fields are missing %s"
msgstr ""

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "Some required values are missing"
msgstr "Tələb olunan bəzi dəyərlər yoxdur"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__prorata_date
msgid ""
"Starting date of the period used in the prorata calculation of the first "
"depreciation"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__state
msgid "Status"
msgstr "Status"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Fəaliyyətlərə əsaslanan status\n"
"Gecikmiş: Gözlənilən tarixdən keçib\n"
"Bu gün: Fəaliyyət tarixi bu gündür\n"
"Planlaşdırılıb: Gələcək fəaliyyətlər."

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__method__linear
msgid "Straight Line"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__book_value
msgid ""
"Sum of the depreciable value, the salvage value and the book value of all "
"value increase items"
msgstr ""
"Dəyərin düşməsi zamanı dəyərin məbləği, ləğvetmə dəyəri və bütün maddələrin "
"balans dəyərinin artırılması"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"İki simvolda ISO ölkə kodu. \n"
"Tez axtarış üçün bu sahəni istifadə edə bilərsiniz."

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid ""
"The account %(exp_acc)s has been credited by %(exp_delta)s, while the "
"account %(dep_acc)s has been debited by %(dep_delta)s. This corresponds to "
"%(move_count)s cancelled %(word)s:"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__method_period
#: model:ir.model.fields,help:account_asset.field_asset_modify__method_period
msgid "The amount of time between two depreciations"
msgstr "İki dəyərin itkiləri arasındakı vaxt intervalı"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid ""
"The amount you have entered (%(entered_amount)s) does not match the Related "
"Purchase's value (%(purchase_value)s). Please make sure this is what you "
"want."
msgstr ""

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_asset_modify__asset_id
msgid "The asset to be modified by this wizard"
msgstr "Bu sehrbaz tərəfindən dəyişdiriləcək aktiv"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__children_ids
msgid "The children are the gains in value of this asset"
msgstr "Alt qruplar bu aktivin dəyərinin artmasıdır"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_asset_modify__invoice_ids
msgid ""
"The disposal invoice is needed in order to generate the closing journal "
"entry."
msgstr ""
"Bağlanan junala girişi yaratmaq məqsədi ilə fakturanın silinməsi lazımdır."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__method_number
msgid "The number of depreciations needed to depreciate your asset"
msgstr "Aktivin dəyər itkiləri üçün lazım olan dəyər itkilərinin sayı"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "The remaining value on the last depreciation line must be 0"
msgstr "Son dəyər düşməsi xəttindəki qalan dəyər 0-a bərabər olmalıdır"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_asset_modify__invoice_line_ids
msgid "There are multiple lines that could be the related to this asset"
msgstr "Bu aktivlə əlaqəli ola biləcək bir neçə sətir var"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid ""
"There are unposted depreciations prior to the selected operation date, "
"please deal with them first."
msgstr ""

#. module: account_asset
#. odoo-javascript
#: code:addons/account_asset/static/src/components/move_reversed/move_reversed.xml:0
msgid "This move has been reversed"
msgstr "Bu hərəkət rədd edilib"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Today Activities"
msgstr "Bugünkü Fəaliyyətlər"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_assets_report.py:0
msgid "Total"
msgstr "Cəmi"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__total_depreciable_value
msgid "Total Depreciable Value"
msgstr ""

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_move.py:0
msgid "Turn as an asset"
msgstr "Aktiv kimi çevrilmə"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__account_type
msgid "Type of the account"
msgstr "Hesabın növü"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Qeyddəki istisna fəaliyyət növü."

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Value at Import"
msgstr ""

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "Value decrease for: %(asset)s"
msgstr ""

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "Value increase for: %(asset)s"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__warning_count_assets
msgid "Warning Count Assets"
msgstr ""

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "Warning for the Original Value of %s"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__website_message_ids
msgid "Website Messages"
msgstr "Veb sayt Mesajları"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__website_message_ids
msgid "Website communication history"
msgstr "Veb saytın kommunikasiya tarixçəsi"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__state
msgid ""
"When an asset is created, the status is 'Draft'.\n"
"If the asset is confirmed, the status goes in 'Running' and the depreciation lines can be posted in the accounting.\n"
"The 'On Hold' status can be set manually when you want to pause the depreciation of an asset for some time.\n"
"You can manually close an asset when the depreciation is over.\n"
"By cancelling an asset, all depreciation entries will be reversed"
msgstr ""

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__method_period__12
#: model:ir.model.fields.selection,name:account_asset.selection__asset_modify__method_period__12
msgid "Years"
msgstr "İllər"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_move.py:0
msgid ""
"You can't post an entry related to a draft asset. Please post the asset "
"before."
msgstr ""

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "You can't re-evaluate the asset before the lock date."
msgstr ""

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "You cannot archive a record that is not closed"
msgstr "Bağlı olmayan qeydi arxivləşdirə bilməzsiniz. "

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid ""
"You cannot automate the journal entry for an asset that has a running gross "
"increase. Please use 'Dispose' on the increase(s)."
msgstr ""
"Davamlı ümumi artımı olan bir aktiv üçün jurnal qeydini avtomatlaşdıra "
"bilməzsiniz. Artımda (lar)  \"Sonlandırmanı\" istifadə edin. "

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid ""
"You cannot create an asset from lines containing credit and debit on the "
"account or with a null amount"
msgstr ""
"Siz hesabda kredit və debet olan və ya sıfır məbləği olan sətirlərdən aktiv "
"yarada bilməzsiniz"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "You cannot delete a document that is in %s state."
msgstr "% s vəziyyətində olan sənədi silə bilməzsiniz."

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid ""
"You cannot delete an asset linked to posted entries.\n"
"You should either confirm the asset, then, sell or dispose of it, or cancel the linked journal entries."
msgstr ""

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "You cannot dispose of an asset before the lock date."
msgstr ""

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_move.py:0
msgid "You cannot reset to draft an entry related to a posted asset"
msgstr ""

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "You cannot resume at a date equal to or before the pause date"
msgstr ""

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "You cannot select the same account as the Depreciation Account"
msgstr ""

#. module: account_asset
#: model:account.report.column,name:account_asset.assets_report_balance
msgid "book_value"
msgstr ""

#. module: account_asset
#: model:account.report.column,name:account_asset.assets_report_date_from
#: model:account.report.column,name:account_asset.assets_report_depre_date_from
msgid "date from"
msgstr ""

#. module: account_asset
#: model:account.report.column,name:account_asset.assets_report_assets_date_to
#: model:account.report.column,name:account_asset.assets_report_depre_date_to
msgid "date to"
msgstr ""

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "depreciable)"
msgstr ""

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "e.g. Laptop iBook"
msgstr "məs. Noutbuk iBook"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "entries"
msgstr ""

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "entry"
msgstr ""

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "gain"
msgstr ""

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "gain/loss"
msgstr ""

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "loss"
msgstr ""
