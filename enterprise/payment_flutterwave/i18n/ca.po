# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_flutterwave
# 
# Translators:
# <PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON>, 2024
# Guspy12, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Guspy12, 2024\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: payment_flutterwave
#. odoo-python
#: code:addons/payment_flutterwave/models/payment_transaction.py:0
msgid ""
"An error occurred during the processing of your payment (status %s). Please "
"try again."
msgstr ""

#. module: payment_flutterwave
#: model:ir.model.fields,field_description:payment_flutterwave.field_payment_provider__code
msgid "Code"
msgstr "Codi"

#. module: payment_flutterwave
#. odoo-python
#: code:addons/payment_flutterwave/models/payment_provider.py:0
msgid "Could not establish the connection to the API."
msgstr "No s'ha pogut establir la connexió a l'API."

#. module: payment_flutterwave
#: model:ir.model.fields.selection,name:payment_flutterwave.selection__payment_provider__code__flutterwave
msgid "Flutterwave"
msgstr "Flutterwave"

#. module: payment_flutterwave
#: model:ir.model.fields,field_description:payment_flutterwave.field_payment_token__flutterwave_customer_email
msgid "Flutterwave Customer Email"
msgstr ""

#. module: payment_flutterwave
#: model:ir.model.fields,field_description:payment_flutterwave.field_payment_provider__flutterwave_public_key
msgid "Flutterwave Public Key"
msgstr ""

#. module: payment_flutterwave
#: model:ir.model.fields,field_description:payment_flutterwave.field_payment_provider__flutterwave_secret_key
msgid "Flutterwave Secret Key"
msgstr ""

#. module: payment_flutterwave
#: model:ir.model.fields,field_description:payment_flutterwave.field_payment_provider__flutterwave_webhook_secret
msgid "Flutterwave Webhook Secret"
msgstr ""

#. module: payment_flutterwave
#. odoo-python
#: code:addons/payment_flutterwave/models/payment_transaction.py:0
msgid "No transaction found matching reference %s."
msgstr "No s'ha trobat cap transacció que coincideixi amb la referència %s."

#. module: payment_flutterwave
#: model:ir.model,name:payment_flutterwave.model_payment_provider
msgid "Payment Provider"
msgstr "Proveïdor de pagament"

#. module: payment_flutterwave
#: model:ir.model,name:payment_flutterwave.model_payment_token
msgid "Payment Token"
msgstr "Token de pagament"

#. module: payment_flutterwave
#: model:ir.model,name:payment_flutterwave.model_payment_transaction
msgid "Payment Transaction"
msgstr "Transacció de pagament"

#. module: payment_flutterwave
#: model_terms:ir.ui.view,arch_db:payment_flutterwave.payment_provider_form
msgid "Public Key"
msgstr ""

#. module: payment_flutterwave
#. odoo-python
#: code:addons/payment_flutterwave/models/payment_transaction.py:0
msgid "Received data with missing reference."
msgstr ""

#. module: payment_flutterwave
#: model_terms:ir.ui.view,arch_db:payment_flutterwave.payment_provider_form
msgid "Secret Key"
msgstr "Clau secreta"

#. module: payment_flutterwave
#. odoo-python
#: code:addons/payment_flutterwave/models/payment_provider.py:0
msgid ""
"The communication with the API failed. Flutterwave gave us the following "
"information: '%s'"
msgstr ""

#. module: payment_flutterwave
#: model:ir.model.fields,help:payment_flutterwave.field_payment_token__flutterwave_customer_email
msgid "The email of the customer at the time the token was created."
msgstr ""

#. module: payment_flutterwave
#: model:ir.model.fields,help:payment_flutterwave.field_payment_provider__flutterwave_public_key
msgid "The key solely used to identify the account with Flutterwave."
msgstr ""

#. module: payment_flutterwave
#: model:ir.model.fields,help:payment_flutterwave.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr "El codi tècnic d'aquest proveïdor de pagaments."

#. module: payment_flutterwave
#. odoo-python
#: code:addons/payment_flutterwave/models/payment_transaction.py:0
msgid "The transaction is not linked to a token."
msgstr "La transacció no està enllaçada a un token."

#. module: payment_flutterwave
#. odoo-python
#: code:addons/payment_flutterwave/models/payment_transaction.py:0
msgid "Unknown payment status: %s"
msgstr ""

#. module: payment_flutterwave
#: model_terms:ir.ui.view,arch_db:payment_flutterwave.payment_provider_form
msgid "Webhook Secret"
msgstr ""
