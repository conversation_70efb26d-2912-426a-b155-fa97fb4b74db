# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_helpdesk_forum
# 
# Translators:
# emre <PERSON>tem, 2024
# <PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# Halil, 2024
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:25+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: website_helpdesk_forum
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.search_result
msgid ", by"
msgstr ", tarafından"

#. module: website_helpdesk_forum
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.search_result
msgid "<i class=\"fa fa-fw fa-2x fa-comments\" title=\"Forum Post\"/>"
msgstr ""

#. module: website_helpdesk_forum
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.question_dropdown
msgid ""
"<i class=\"fa fa-life-ring fa-fw\"/>\n"
"                    Create Ticket"
msgstr ""

#. module: website_helpdesk_forum
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.question_dropdown
msgid ""
"<i class=\"fa fa-life-ring fa-fw\"/>\n"
"                    View Ticket"
msgstr ""

#. module: website_helpdesk_forum
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.forum_forum_view_form_inherited
msgid "<span class=\"o_stat_text\">Helpdesk Teams</span>"
msgstr ""

#. module: website_helpdesk_forum
#: model:ir.model.fields,field_description:website_helpdesk_forum.field_helpdesk_ticket_select_forum_wizard__answer_content
msgid "Answer"
msgstr "Cevap"

#. module: website_helpdesk_forum
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.search_result
msgid "Answers"
msgstr "Yanıtlar"

#. module: website_helpdesk_forum
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.knowledge_base_forum_card
msgid "Ask questions and discuss tips with fellow members in a forum"
msgstr ""

#. module: website_helpdesk_forum
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.footer
msgid "Ask the Community"
msgstr ""

#. module: website_helpdesk_forum
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.knowledge_base_forum_card
msgid "Ask the community"
msgstr ""

#. module: website_helpdesk_forum
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.knowledge_base_forum_card
msgid "Community"
msgstr "Topluluk"

#. module: website_helpdesk_forum
#. odoo-javascript
#: code:addons/website_helpdesk_forum/static/src/components/create_ticket_dialog/create_ticket_dialog.xml:0
msgid "Create & View Ticket"
msgstr ""

#. module: website_helpdesk_forum
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.helpdesk_ticket_select_forum_wizard_view_form
msgid "Create Post"
msgstr ""

#. module: website_helpdesk_forum
#. odoo-javascript
#: code:addons/website_helpdesk_forum/static/src/components/create_ticket_dialog/create_ticket_dialog.xml:0
msgid "Create Ticket"
msgstr "Talep Oluştur"

#. module: website_helpdesk_forum
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.helpdesk_ticket_select_forum_wizard_view_form
msgid "Create and View Post"
msgstr ""

#. module: website_helpdesk_forum
#: model:ir.model.fields,field_description:website_helpdesk_forum.field_helpdesk_ticket_select_forum_wizard__create_uid
msgid "Created by"
msgstr "Tarafından oluşturuldu"

#. module: website_helpdesk_forum
#: model:ir.model.fields,field_description:website_helpdesk_forum.field_helpdesk_ticket_select_forum_wizard__create_date
msgid "Created on"
msgstr "Oluşturuldu"

#. module: website_helpdesk_forum
#: model:ir.model.fields,help:website_helpdesk_forum.field_helpdesk_team__website_forum_ids
msgid ""
"Customers will see only the posts from chosen forums in the help center. If "
"you want all forums to be accessible, just leave the field empty. "
"Alternatively, you can make forums private to restrict this feature to "
"internal users."
msgstr ""

#. module: website_helpdesk_forum
#: model:ir.model.fields,field_description:website_helpdesk_forum.field_helpdesk_ticket_select_forum_wizard__description
msgid "Description"
msgstr "Açıklama"

#. module: website_helpdesk_forum
#. odoo-javascript
#: code:addons/website_helpdesk_forum/static/src/components/create_ticket_dialog/create_ticket_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.helpdesk_ticket_select_forum_wizard_view_form
msgid "Discard"
msgstr "Sil"

#. module: website_helpdesk_forum
#: model:ir.model.fields,field_description:website_helpdesk_forum.field_helpdesk_ticket_select_forum_wizard__display_name
msgid "Display Name"
msgstr "İsim Göster"

#. module: website_helpdesk_forum
#: model:ir.model.fields,field_description:website_helpdesk_forum.field_forum_forum__filter_for_helpdesk_wizard
msgid "Filter For Helpdesk Wizard"
msgstr ""

#. module: website_helpdesk_forum
#: model:ir.model,name:website_helpdesk_forum.model_forum_forum
#: model:ir.model.fields,field_description:website_helpdesk_forum.field_helpdesk_ticket_select_forum_wizard__forum_id
msgid "Forum"
msgstr "Forum"

#. module: website_helpdesk_forum
#: model:ir.model,name:website_helpdesk_forum.model_forum_post
msgid "Forum Post"
msgstr "Forum Mesajı"

#. module: website_helpdesk_forum
#. odoo-python
#: code:addons/website_helpdesk_forum/wizards/helpdesk_ticket_select_forum.py:0
msgid "Forum Post Created"
msgstr ""

#. module: website_helpdesk_forum
#. odoo-python
#: code:addons/website_helpdesk_forum/models/helpdesk.py:0
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.helpdesk_ticket_view_form_inherit_website_helpdesk_forum
msgid "Forum Posts"
msgstr "Forum Mesajlar"

#. module: website_helpdesk_forum
#: model:ir.model.fields,field_description:website_helpdesk_forum.field_helpdesk_team__website_forum_ids
msgid "Forums"
msgstr "Forumlar"

#. module: website_helpdesk_forum
#. odoo-python
#: code:addons/website_helpdesk_forum/models/helpdesk.py:0
msgid "Help Center is not active for this team."
msgstr ""

#. module: website_helpdesk_forum
#. odoo-javascript
#: code:addons/website_helpdesk_forum/static/src/components/create_ticket_dialog/create_ticket_dialog.xml:0
#: model:ir.model,name:website_helpdesk_forum.model_helpdesk_team
#: model:ir.model.fields,field_description:website_helpdesk_forum.field_forum_forum__helpdesk_team_ids
msgid "Helpdesk Team"
msgstr "Destek Ekibi"

#. module: website_helpdesk_forum
#: model:ir.model.fields,field_description:website_helpdesk_forum.field_forum_forum__helpdesk_team_count
msgid "Helpdesk Team Count"
msgstr ""

#. module: website_helpdesk_forum
#. odoo-python
#: code:addons/website_helpdesk_forum/models/forum_forum.py:0
msgid "Helpdesk Teams"
msgstr "Yardım Masası Ekipleri"

#. module: website_helpdesk_forum
#: model:ir.model,name:website_helpdesk_forum.model_helpdesk_ticket
msgid "Helpdesk Ticket"
msgstr "Yardım Masası Talebi"

#. module: website_helpdesk_forum
#. odoo-javascript
#: code:addons/website_helpdesk_forum/static/src/components/create_ticket_dialog/create_ticket_dialog.js:0
msgid "Helpdesk ticket %s has been successfully created for this forum post."
msgstr ""

#. module: website_helpdesk_forum
#: model:ir.model.fields,field_description:website_helpdesk_forum.field_helpdesk_ticket_select_forum_wizard__id
msgid "ID"
msgstr "ID"

#. module: website_helpdesk_forum
#: model:ir.model.fields,field_description:website_helpdesk_forum.field_helpdesk_ticket_select_forum_wizard__write_uid
msgid "Last Updated by"
msgstr "Son Güncelleyen"

#. module: website_helpdesk_forum
#: model:ir.model.fields,field_description:website_helpdesk_forum.field_helpdesk_ticket_select_forum_wizard__write_date
msgid "Last Updated on"
msgstr "Son Güncelleme"

#. module: website_helpdesk_forum
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.knowledge_base_forum_card
msgid "Most voted"
msgstr "En çok oylanan"

#. module: website_helpdesk_forum
#. odoo-javascript
#: code:addons/website_helpdesk_forum/static/src/components/create_ticket_dialog/create_ticket_dialog.xml:0
msgid "Please fill out this field."
msgstr ""

#. module: website_helpdesk_forum
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.knowledge_base_forum_card
msgid "Quick Links"
msgstr ""

#. module: website_helpdesk_forum
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.helpdesk_ticket_select_forum_wizard_view_form
msgid "Select Forum"
msgstr ""

#. module: website_helpdesk_forum
#: model:ir.actions.act_window,name:website_helpdesk_forum.helpdesk_ticket_select_forum_wizard_action
#: model:ir.model,name:website_helpdesk_forum.model_helpdesk_ticket_select_forum_wizard
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.helpdesk_ticket_view_form_inherit_website_helpdesk_forum
msgid "Share on Forum"
msgstr ""

#. module: website_helpdesk_forum
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.search_result
msgid "Solved"
msgstr "Çözüldü"

#. module: website_helpdesk_forum
#: model:ir.model.fields,field_description:website_helpdesk_forum.field_helpdesk_ticket_select_forum_wizard__tag_ids
msgid "Tags"
msgstr "Etiketler"

#. module: website_helpdesk_forum
#. odoo-python
#: code:addons/website_helpdesk_forum/models/helpdesk.py:0
msgid "There are no posts associated with this ticket."
msgstr ""

#. module: website_helpdesk_forum
#: model:ir.model.fields,help:website_helpdesk_forum.field_helpdesk_team__top_forum_posts
msgid ""
"These are the top posts in the forums associated with this helpdesk team"
msgstr ""

#. module: website_helpdesk_forum
#. odoo-python
#: code:addons/website_helpdesk_forum/models/forum_forum.py:0
msgid "Ticket created from forum post"
msgstr ""

#. module: website_helpdesk_forum
#. odoo-javascript
#: code:addons/website_helpdesk_forum/static/src/components/create_ticket_dialog/create_ticket_dialog.xml:0
#: model:ir.model.fields,field_description:website_helpdesk_forum.field_helpdesk_ticket_select_forum_wizard__title
msgid "Title"
msgstr "Başlık"

#. module: website_helpdesk_forum
#: model:ir.model.fields,field_description:website_helpdesk_forum.field_helpdesk_team__top_forum_posts
msgid "Top Posts"
msgstr ""

#. module: website_helpdesk_forum
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.search_result
msgid "Views"
msgstr "Görünümler"

#. module: website_helpdesk_forum
#. odoo-python
#: code:addons/website_helpdesk_forum/wizards/helpdesk_ticket_select_forum.py:0
msgid "You must select a forum to share the ticket"
msgstr ""

#. module: website_helpdesk_forum
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.search_result
msgid "• 0 Answer"
msgstr ""
