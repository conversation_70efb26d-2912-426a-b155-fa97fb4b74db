# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_helpdesk_forum
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:25+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_helpdesk_forum
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.search_result
msgid ", by"
msgstr "、次により"

#. module: website_helpdesk_forum
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.search_result
msgid "<i class=\"fa fa-fw fa-2x fa-comments\" title=\"Forum Post\"/>"
msgstr "<i class=\"fa fa-fw fa-2x fa-comments\" title=\"Forum Post\"/>"

#. module: website_helpdesk_forum
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.question_dropdown
msgid ""
"<i class=\"fa fa-life-ring fa-fw\"/>\n"
"                    Create Ticket"
msgstr ""
"<i class=\"fa fa-life-ring fa-fw\"/>\n"
"                    チケット作成"

#. module: website_helpdesk_forum
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.question_dropdown
msgid ""
"<i class=\"fa fa-life-ring fa-fw\"/>\n"
"                    View Ticket"
msgstr ""
"<i class=\"fa fa-life-ring fa-fw\"/>\n"
"                    チケットを見る"

#. module: website_helpdesk_forum
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.forum_forum_view_form_inherited
msgid "<span class=\"o_stat_text\">Helpdesk Teams</span>"
msgstr "<span class=\"o_stat_text\">ヘルプデスクチーム</span>"

#. module: website_helpdesk_forum
#: model:ir.model.fields,field_description:website_helpdesk_forum.field_helpdesk_ticket_select_forum_wizard__answer_content
msgid "Answer"
msgstr "回答"

#. module: website_helpdesk_forum
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.search_result
msgid "Answers"
msgstr "回答"

#. module: website_helpdesk_forum
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.knowledge_base_forum_card
msgid "Ask questions and discuss tips with fellow members in a forum"
msgstr "フォーラムで仲間に質問したり、ヒントを話し合ったりする"

#. module: website_helpdesk_forum
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.footer
msgid "Ask the Community"
msgstr "コミュニティに聞く"

#. module: website_helpdesk_forum
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.knowledge_base_forum_card
msgid "Ask the community"
msgstr "コミュニティに聞く"

#. module: website_helpdesk_forum
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.knowledge_base_forum_card
msgid "Community"
msgstr "コミュニティ"

#. module: website_helpdesk_forum
#. odoo-javascript
#: code:addons/website_helpdesk_forum/static/src/components/create_ticket_dialog/create_ticket_dialog.xml:0
msgid "Create & View Ticket"
msgstr "チケット作成&表示"

#. module: website_helpdesk_forum
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.helpdesk_ticket_select_forum_wizard_view_form
msgid "Create Post"
msgstr "投稿"

#. module: website_helpdesk_forum
#. odoo-javascript
#: code:addons/website_helpdesk_forum/static/src/components/create_ticket_dialog/create_ticket_dialog.xml:0
msgid "Create Ticket"
msgstr "チケット作成"

#. module: website_helpdesk_forum
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.helpdesk_ticket_select_forum_wizard_view_form
msgid "Create and View Post"
msgstr "投稿＆照会"

#. module: website_helpdesk_forum
#: model:ir.model.fields,field_description:website_helpdesk_forum.field_helpdesk_ticket_select_forum_wizard__create_uid
msgid "Created by"
msgstr "作成者"

#. module: website_helpdesk_forum
#: model:ir.model.fields,field_description:website_helpdesk_forum.field_helpdesk_ticket_select_forum_wizard__create_date
msgid "Created on"
msgstr "作成日"

#. module: website_helpdesk_forum
#: model:ir.model.fields,help:website_helpdesk_forum.field_helpdesk_team__website_forum_ids
msgid ""
"Customers will see only the posts from chosen forums in the help center. If "
"you want all forums to be accessible, just leave the field empty. "
"Alternatively, you can make forums private to restrict this feature to "
"internal users."
msgstr ""
"顧客はヘルプセンタで選択したフォーラムの投稿のみを見ることができます。全てのフォーラムにアクセスしたい場合は、このフィールドを空にして下さい。また、フォーラムを非公開にして、この機能を内部ユーザに制限することもできます。"

#. module: website_helpdesk_forum
#: model:ir.model.fields,field_description:website_helpdesk_forum.field_helpdesk_ticket_select_forum_wizard__description
msgid "Description"
msgstr "説明"

#. module: website_helpdesk_forum
#. odoo-javascript
#: code:addons/website_helpdesk_forum/static/src/components/create_ticket_dialog/create_ticket_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.helpdesk_ticket_select_forum_wizard_view_form
msgid "Discard"
msgstr "破棄"

#. module: website_helpdesk_forum
#: model:ir.model.fields,field_description:website_helpdesk_forum.field_helpdesk_ticket_select_forum_wizard__display_name
msgid "Display Name"
msgstr "表示名"

#. module: website_helpdesk_forum
#: model:ir.model.fields,field_description:website_helpdesk_forum.field_forum_forum__filter_for_helpdesk_wizard
msgid "Filter For Helpdesk Wizard"
msgstr "ヘルプデスクウィザード用フィルタ"

#. module: website_helpdesk_forum
#: model:ir.model,name:website_helpdesk_forum.model_forum_forum
#: model:ir.model.fields,field_description:website_helpdesk_forum.field_helpdesk_ticket_select_forum_wizard__forum_id
msgid "Forum"
msgstr "フォーラム"

#. module: website_helpdesk_forum
#: model:ir.model,name:website_helpdesk_forum.model_forum_post
msgid "Forum Post"
msgstr "フォーラム投稿"

#. module: website_helpdesk_forum
#. odoo-python
#: code:addons/website_helpdesk_forum/wizards/helpdesk_ticket_select_forum.py:0
msgid "Forum Post Created"
msgstr "フォーラム投稿が作成されました"

#. module: website_helpdesk_forum
#. odoo-python
#: code:addons/website_helpdesk_forum/models/helpdesk.py:0
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.helpdesk_ticket_view_form_inherit_website_helpdesk_forum
msgid "Forum Posts"
msgstr "投稿"

#. module: website_helpdesk_forum
#: model:ir.model.fields,field_description:website_helpdesk_forum.field_helpdesk_team__website_forum_ids
msgid "Forums"
msgstr "フォーラム"

#. module: website_helpdesk_forum
#. odoo-python
#: code:addons/website_helpdesk_forum/models/helpdesk.py:0
msgid "Help Center is not active for this team."
msgstr "へスプセンターはこのチーム用には無効です。"

#. module: website_helpdesk_forum
#. odoo-javascript
#: code:addons/website_helpdesk_forum/static/src/components/create_ticket_dialog/create_ticket_dialog.xml:0
#: model:ir.model,name:website_helpdesk_forum.model_helpdesk_team
#: model:ir.model.fields,field_description:website_helpdesk_forum.field_forum_forum__helpdesk_team_ids
msgid "Helpdesk Team"
msgstr "ヘルプデスクチーム"

#. module: website_helpdesk_forum
#: model:ir.model.fields,field_description:website_helpdesk_forum.field_forum_forum__helpdesk_team_count
msgid "Helpdesk Team Count"
msgstr "ヘルプデスクチーム数"

#. module: website_helpdesk_forum
#. odoo-python
#: code:addons/website_helpdesk_forum/models/forum_forum.py:0
msgid "Helpdesk Teams"
msgstr "ヘルプデスクのチーム"

#. module: website_helpdesk_forum
#: model:ir.model,name:website_helpdesk_forum.model_helpdesk_ticket
msgid "Helpdesk Ticket"
msgstr "ヘルプデスクチケット"

#. module: website_helpdesk_forum
#. odoo-javascript
#: code:addons/website_helpdesk_forum/static/src/components/create_ticket_dialog/create_ticket_dialog.js:0
msgid "Helpdesk ticket %s has been successfully created for this forum post."
msgstr "ヘルプデスクチケット %s がこのフォーラム投稿用に正常に作成されました。"

#. module: website_helpdesk_forum
#: model:ir.model.fields,field_description:website_helpdesk_forum.field_helpdesk_ticket_select_forum_wizard__id
msgid "ID"
msgstr "ID"

#. module: website_helpdesk_forum
#: model:ir.model.fields,field_description:website_helpdesk_forum.field_helpdesk_ticket_select_forum_wizard__write_uid
msgid "Last Updated by"
msgstr "最終更新者"

#. module: website_helpdesk_forum
#: model:ir.model.fields,field_description:website_helpdesk_forum.field_helpdesk_ticket_select_forum_wizard__write_date
msgid "Last Updated on"
msgstr "最終更新日"

#. module: website_helpdesk_forum
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.knowledge_base_forum_card
msgid "Most voted"
msgstr "投票数が多い"

#. module: website_helpdesk_forum
#. odoo-javascript
#: code:addons/website_helpdesk_forum/static/src/components/create_ticket_dialog/create_ticket_dialog.xml:0
msgid "Please fill out this field."
msgstr "このフィールドを入力して下さい。"

#. module: website_helpdesk_forum
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.knowledge_base_forum_card
msgid "Quick Links"
msgstr "クリックリンク"

#. module: website_helpdesk_forum
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.helpdesk_ticket_select_forum_wizard_view_form
msgid "Select Forum"
msgstr "フォーラムを選択"

#. module: website_helpdesk_forum
#: model:ir.actions.act_window,name:website_helpdesk_forum.helpdesk_ticket_select_forum_wizard_action
#: model:ir.model,name:website_helpdesk_forum.model_helpdesk_ticket_select_forum_wizard
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.helpdesk_ticket_view_form_inherit_website_helpdesk_forum
msgid "Share on Forum"
msgstr "フォーラムで共有"

#. module: website_helpdesk_forum
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.search_result
msgid "Solved"
msgstr "解決済"

#. module: website_helpdesk_forum
#: model:ir.model.fields,field_description:website_helpdesk_forum.field_helpdesk_ticket_select_forum_wizard__tag_ids
msgid "Tags"
msgstr "タグ"

#. module: website_helpdesk_forum
#. odoo-python
#: code:addons/website_helpdesk_forum/models/helpdesk.py:0
msgid "There are no posts associated with this ticket."
msgstr "このセッションに関連した投稿リマインダはありません"

#. module: website_helpdesk_forum
#: model:ir.model.fields,help:website_helpdesk_forum.field_helpdesk_team__top_forum_posts
msgid ""
"These are the top posts in the forums associated with this helpdesk team"
msgstr "以下は、このヘルプデスクチームに関連するフォーラムの上位投稿です。"

#. module: website_helpdesk_forum
#. odoo-python
#: code:addons/website_helpdesk_forum/models/forum_forum.py:0
msgid "Ticket created from forum post"
msgstr "フォーラム投稿からチケットが作成されました"

#. module: website_helpdesk_forum
#. odoo-javascript
#: code:addons/website_helpdesk_forum/static/src/components/create_ticket_dialog/create_ticket_dialog.xml:0
#: model:ir.model.fields,field_description:website_helpdesk_forum.field_helpdesk_ticket_select_forum_wizard__title
msgid "Title"
msgstr "タイトル"

#. module: website_helpdesk_forum
#: model:ir.model.fields,field_description:website_helpdesk_forum.field_helpdesk_team__top_forum_posts
msgid "Top Posts"
msgstr "トップ投稿"

#. module: website_helpdesk_forum
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.search_result
msgid "Views"
msgstr "ビュー"

#. module: website_helpdesk_forum
#. odoo-python
#: code:addons/website_helpdesk_forum/wizards/helpdesk_ticket_select_forum.py:0
msgid "You must select a forum to share the ticket"
msgstr "チケットを共有するにはフォーラムを選択する必要があります。"

#. module: website_helpdesk_forum
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.search_result
msgid "• 0 Answer"
msgstr "• 0 回答"
