# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_recruitment_sign
# 
# Translators:
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON><PERSON> <a<PERSON><PERSON>@allegro.lv>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# Will Sensors, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 09:14+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Latvian (https://app.transifex.com/odoo/teams/41243/lv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lv\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n != 0 ? 1 : 2);\n"

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_applicant__sign_request_count
msgid "# Signatures"
msgstr "# Paraksti"

#. module: hr_recruitment_sign
#. odoo-python
#: code:addons/hr_recruitment_sign/wizard/hr_recruitment_sign_document_wizard.py:0
msgid "%(partner)s and %(responsible)s are the signatories."
msgstr ""

#. module: hr_recruitment_sign
#: model_terms:ir.ui.view,arch_db:hr_recruitment_sign.hr_applicant_sign_view_form
msgid "<span class=\"o_stat_text\">Signature Requests</span>"
msgstr ""

#. module: hr_recruitment_sign
#: model:ir.model,name:hr_recruitment_sign.model_hr_applicant
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__applicant_id
msgid "Applicant"
msgstr "Kandidāts"

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__applicant_role_id
msgid "Applicant Role"
msgstr ""

#. module: hr_recruitment_sign
#: model:ir.model.fields,help:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__applicant_role_id
msgid ""
"Applicant's role on the templates to sign. The same role must be present in "
"all the templates"
msgstr ""

#. module: hr_recruitment_sign
#: model_terms:ir.ui.view,arch_db:hr_recruitment_sign.hr_recruitment_sign_view
msgid "Attach a file"
msgstr "Pievienot failu"

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__attachment_ids
msgid "Attachment"
msgstr "Pielikums"

#. module: hr_recruitment_sign
#: model:ir.model,name:hr_recruitment_sign.model_hr_candidate
msgid "Candidate"
msgstr ""

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__partner_id
msgid "Contact"
msgstr "Kontakts"

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__cc_partner_ids
msgid "Copy to"
msgstr ""

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__create_uid
msgid "Created by"
msgstr "Izveidoja"

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__create_date
msgid "Created on"
msgstr "Izveidots"

#. module: hr_recruitment_sign
#: model_terms:ir.ui.view,arch_db:hr_recruitment_sign.hr_recruitment_sign_view
msgid "Discard"
msgstr "Atmest"

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__display_name
msgid "Display Name"
msgstr "Nosaukums"

#. module: hr_recruitment_sign
#: model:ir.actions.act_window,name:hr_recruitment_sign.sign_recruitment_wizard_action
msgid "Document Signature"
msgstr ""

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__sign_template_ids
msgid "Documents to sign"
msgstr ""

#. module: hr_recruitment_sign
#: model:ir.model.fields,help:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__sign_template_ids
msgid ""
"Documents to sign. Only documents with 1 or 2 different responsible are selectable.\n"
"        Documents with 1 responsible will only have to be signed by the applicant while documents with 2 different responsible will have to be signed by both the applicant and the responsible.\n"
"        "
msgstr ""

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__has_both_template
msgid "Has Both Template"
msgstr ""

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__id
msgid "ID"
msgstr "ID"

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__write_uid
msgid "Last Updated by"
msgstr "Pēdējo reizi atjaunoja"

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__write_date
msgid "Last Updated on"
msgstr "Pēdējās izmaiņas"

#. module: hr_recruitment_sign
#: model_terms:ir.ui.view,arch_db:hr_recruitment_sign.hr_recruitment_sign_view
msgid "Mail Options"
msgstr ""

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__message
msgid "Message"
msgstr "Ziņojums"

#. module: hr_recruitment_sign
#. odoo-python
#: code:addons/hr_recruitment_sign/wizard/hr_recruitment_sign_document_wizard.py:0
msgid ""
"No appropriate template could be found, please make sure you configured them"
" properly."
msgstr ""

#. module: hr_recruitment_sign
#: model_terms:ir.ui.view,arch_db:hr_recruitment_sign.hr_recruitment_sign_view
msgid "No template available"
msgstr ""

#. module: hr_recruitment_sign
#. odoo-python
#: code:addons/hr_recruitment_sign/wizard/hr_recruitment_sign_document_wizard.py:0
msgid "Only %s has to sign."
msgstr ""

#. module: hr_recruitment_sign
#: model_terms:ir.ui.view,arch_db:hr_recruitment_sign.hr_recruitment_sign_view
msgid "Optional Message..."
msgstr "Neobligāts ziņojums..."

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__partner_name
msgid "Partner Name"
msgstr "Partnera Vārds"

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__possible_template_ids
msgid "Possible Template"
msgstr ""

#. module: hr_recruitment_sign
#: model:ir.actions.server,name:hr_recruitment_sign.action_request_signature
msgid "Request Signature"
msgstr "Pieprasīt parakstu"

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__responsible_id
msgid "Responsible"
msgstr "Atbildīgais"

#. module: hr_recruitment_sign
#: model_terms:ir.ui.view,arch_db:hr_recruitment_sign.hr_recruitment_sign_view
msgid "Send"
msgstr "Nosūtīt"

#. module: hr_recruitment_sign
#: model_terms:ir.ui.view,arch_db:hr_recruitment_sign.hr_recruitment_sign_view
msgid "Sign Request Options"
msgstr ""

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__sign_template_responsible_ids
msgid "Sign Template Responsible"
msgstr ""

#. module: hr_recruitment_sign
#: model:ir.model,name:hr_recruitment_sign.model_hr_recruitment_sign_document_wizard
msgid "Sign document in recruitment"
msgstr ""

#. module: hr_recruitment_sign
#: model_terms:ir.ui.view,arch_db:hr_recruitment_sign.hr_recruitment_sign_view
msgid "Signature Request"
msgstr "Paraksta pieprasījums"

#. module: hr_recruitment_sign
#. odoo-python
#: code:addons/hr_recruitment_sign/wizard/hr_recruitment_sign_document_wizard.py:0
msgid "Signature Request - %s"
msgstr "Paraksta pieprasījums - %s"

#. module: hr_recruitment_sign
#. odoo-python
#: code:addons/hr_recruitment_sign/models/hr_applicant.py:0
msgid "Signature Requests"
msgstr "Paraksta pieprasījums"

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__subject
msgid "Subject"
msgstr "Priekšmets"

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__template_warning
msgid "Template Warning"
msgstr ""

#. module: hr_recruitment_sign
#: model_terms:ir.ui.view,arch_db:hr_recruitment_sign.hr_recruitment_sign_view
msgid "Write email or search contact..."
msgstr ""

#. module: hr_recruitment_sign
#. odoo-python
#: code:addons/hr_recruitment_sign/models/hr_applicant.py:0
msgid "You must define a Contact Name for this applicant."
msgstr ""

#. module: hr_recruitment_sign
#: model_terms:ir.ui.view,arch_db:hr_recruitment_sign.message_signature_request
msgid "requested a new signature on the following documents:"
msgstr ""
