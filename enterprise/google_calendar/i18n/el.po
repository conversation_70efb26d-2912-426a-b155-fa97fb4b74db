# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* google_calendar
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Ko<PERSON><PERSON> Goutoudi<PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Greek (https://app.transifex.com/odoo/teams/41243/el/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: el\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/google_sync.py:0
msgid "%(id)s and %(length)s following"
msgstr ""

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/calendar.py:0
msgid "%(reminder_type)s - %(duration)s Days"
msgstr ""

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/calendar.py:0
msgid "%(reminder_type)s - %(duration)s Hours"
msgstr ""

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/calendar.py:0
msgid "%(reminder_type)s - %(duration)s Minutes"
msgstr ""

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/calendar.py:0
msgid "(No title)"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_event__active
#: model:ir.model.fields,field_description:google_calendar.field_calendar_recurrence__active
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_sync__active
msgid "Active"
msgstr "Σε Ισχύ"

#. module: google_calendar
#. odoo-javascript
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.js:0
msgid ""
"An administrator needs to configure Google Synchronization before you can "
"use it!"
msgstr ""
"Ένας διαχειριστής θα πρέπει να ρυθμίσει τον συγχρονισμό Google για να "
"μπορέσετε να το χρησιμοποιήσετε!"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/res_users_settings.py:0
msgid ""
"An error occurred while generating the token. Your authorization code may be"
" invalid or has already expired [%s]. You should check your Client ID and "
"secret on the Google APIs plateform or try to stop and restart your calendar"
" synchronization."
msgstr ""

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_calendar_attendee
msgid "Calendar Attendee Information"
msgstr ""

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_calendar_event
msgid "Calendar Event"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_cal_id
#: model:ir.model.fields,field_description:google_calendar.field_res_users_settings__google_calendar_cal_id
msgid "Calendar ID"
msgstr "Αναγνωριστικό Ημερολογίου"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.google_calendar_reset_account_view_form
msgid "Cancel"
msgstr "Ακύρωση"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.res_config_settings_view_form
msgid "Client ID"
msgstr "Client ID"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.res_config_settings_view_form
msgid "Client Secret"
msgstr "Client Secret"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_config_settings__cal_client_id
msgid "Client_id"
msgstr "Client_id"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_config_settings__cal_client_secret
msgid "Client_key"
msgstr "Client_key"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_res_config_settings
msgid "Config Settings"
msgstr "Ρυθμίσεις διαμόρφωσης"

#. module: google_calendar
#. odoo-javascript
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.js:0
msgid "Configuration"
msgstr "Διαμόρφωση"

#. module: google_calendar
#. odoo-javascript
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.js:0
msgid "Configure"
msgstr "Παραμετροποίηση"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.google_calendar_reset_account_view_form
msgid "Confirm"
msgstr "Επιβεβαίωση"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__create_uid
msgid "Created by"
msgstr "Δημιουργήθηκε από"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__create_date
msgid "Created on"
msgstr "Δημιουργήθηκε στις"

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__delete_policy__delete_odoo
msgid "Delete from Odoo"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__delete_policy__delete_both
msgid "Delete from both"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__delete_policy__delete_google
msgid "Delete from the current Google Calendar account"
msgstr ""

#. module: google_calendar
#. odoo-javascript
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.js:0
msgid "Discard"
msgstr "Απόρριψη"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__display_name
msgid "Display Name"
msgstr "Εμφάνιση Ονόματος"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/calendar.py:0
msgid "Email"
msgstr "Email"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_calendar_recurrence
msgid "Event Recurrence Rule"
msgstr ""

#. module: google_calendar
#. odoo-javascript
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.xml:0
msgid "Google"
msgstr ""

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.view_users_form
msgid "Google Calendar"
msgstr "Google Ημερολόγιο"

#. module: google_calendar
#: model:ir.actions.act_window,name:google_calendar.google_calendar_reset_account_action
#: model:ir.model,name:google_calendar.model_google_calendar_account_reset
msgid "Google Calendar Account Reset"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_event__google_id
msgid "Google Calendar Event Id"
msgstr "Αναγνωριστικό Συμβάντος Google Calendar"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_recurrence__google_id
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_sync__google_id
msgid "Google Calendar Id"
msgstr ""

#. module: google_calendar
#: model:ir.actions.server,name:google_calendar.ir_cron_sync_all_cals_ir_actions_server
msgid "Google Calendar: synchronization"
msgstr "Ημερολόγιο Google: συγχρονισμός"

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__calendar_event__videocall_source__google_meet
msgid "Google Meet"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_config_settings__cal_sync_paused
msgid "Google Synchronization Paused"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_synchronization_stopped
#: model:ir.model.fields,field_description:google_calendar.field_res_users_settings__google_synchronization_stopped
msgid "Google Synchronization stopped"
msgstr ""

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/google_sync.py:0
msgid "Google gave the following explanation: %s"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_event__guests_readonly
msgid "Guests Event Modification Permission"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__id
msgid "ID"
msgstr "Κωδικός"

#. module: google_calendar
#: model:ir.model.fields,help:google_calendar.field_calendar_event__active
msgid ""
"If the active field is set to false, it will allow you to hide the event "
"alarm information without removing it."
msgstr ""
"Εάν το ενεργό πεδίο έχει οριστεί ως ψευδές, θα σας επιτρέψει να αποκρύψετε "
"τις πληροφορίες ειδοποίησης συμβάντος χωρίς να την καταργήσετε."

#. module: google_calendar
#: model:ir.model.fields,help:google_calendar.field_res_config_settings__cal_sync_paused
msgid "Indicates if synchronization with Google Calendar is paused or not."
msgstr ""

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/google_sync.py:0
msgid "It will not be synced as long at it is not updated."
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,help:google_calendar.field_res_users__google_calendar_cal_id
#: model:ir.model.fields,help:google_calendar.field_res_users_settings__google_calendar_cal_id
msgid ""
"Last Calendar ID who has been synchronized. If it is changed, we remove all "
"links between GoogleID and Odoo Google Internal ID"
msgstr ""
"Τελευταίο Αναγνωριστικό Ημερολογίου που έχει συγχρονιστεί. Εάν αλλάξει, "
"αφαιρέστε όλες τις συνδέσεις μεταξύ Αναγνωριστικό Google και Odoo Google "
"Εσωτερικό Αναγνωριστικό"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__write_uid
msgid "Last Updated by"
msgstr "Τελευταία Ενημέρωση από"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__write_date
msgid "Last Updated on"
msgstr "Τελευταία Ενημέρωση στις"

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__delete_policy__dont_delete
msgid "Leave them untouched"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_event__need_sync
#: model:ir.model.fields,field_description:google_calendar.field_calendar_recurrence__need_sync
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_sync__need_sync
msgid "Need Sync"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_sync_token
#: model:ir.model.fields,field_description:google_calendar.field_res_users_settings__google_calendar_sync_token
msgid "Next Sync Token"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__sync_policy
msgid "Next Synchronization"
msgstr "Επόμενος Συγχρονισμός"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/calendar.py:0
msgid "Notification"
msgstr "Ειδοποίηση"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.res_config_settings_view_form
msgid "Pause Synchronization"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_rtoken
#: model:ir.model.fields,field_description:google_calendar.field_res_users_settings__google_calendar_rtoken
msgid "Refresh Token"
msgstr "Ανανέωση Διακριτικού"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.view_users_form
msgid "Reset Account"
msgstr ""

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.google_calendar_reset_account_view_form
msgid "Reset Google Calendar Account"
msgstr ""

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_google_calendar_sync
msgid "Synchronize a record with Google Calendar"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__sync_policy__all
msgid "Synchronize all existing events"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__sync_policy__new
msgid "Synchronize only new events"
msgstr ""

#. module: google_calendar
#. odoo-javascript
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.js:0
msgid ""
"The Google Synchronization needs to be configured before you can use it, do "
"you want to do it now?"
msgstr ""
"Ο συγχρονισμός Google πρέπει να ρυθμιστεί για να μπορέσετε να τον "
"χρησιμοποιήσετε, θέλετε να το κάνετε τώρα;"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/res_users_settings.py:0
msgid "The account for the Google Calendar service is not configured."
msgstr ""

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/calendar.py:0
msgid ""
"The following event can only be updated by the organizer according to the "
"event permissions set on Google Calendar."
msgstr ""

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/google_sync.py:0
msgid "The following event could not be synced with Google Calendar."
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,help:google_calendar.field_google_calendar_account_reset__delete_policy
msgid "This will only affect events for which the user is the owner"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_token_validity
#: model:ir.model.fields,field_description:google_calendar.field_res_users_settings__google_calendar_token_validity
msgid "Token Validity"
msgstr "Εγκυρότητα Διακριτικού"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_res_users
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__user_id
msgid "User"
msgstr "Χρήστης"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_res_users_settings
msgid "User Settings"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_token
#: model:ir.model.fields,field_description:google_calendar.field_res_users_settings__google_calendar_token
msgid "User token"
msgstr "Διακριτικό Χρήστη"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__delete_policy
msgid "User's Existing Events"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_event__videocall_source
msgid "Videocall Source"
msgstr ""

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/google_sync.py:0
msgid "undefined time"
msgstr ""

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/google_sync.py:0
msgid ""
"you don't seem to have permission to modify this event on Google Calendar"
msgstr ""
