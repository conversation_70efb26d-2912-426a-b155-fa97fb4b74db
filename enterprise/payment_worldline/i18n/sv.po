# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_worldline
# 
# Translators:
# <PERSON> <and<PERSON>.<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# Lasse L, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-29 00:00+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Swedish (https://app.transifex.com/odoo/teams/41243/sv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sv\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: payment_worldline
#: model_terms:ir.ui.view,arch_db:payment_worldline.payment_provider_form
msgid "API Key"
msgstr "API-nyckel"

#. module: payment_worldline
#: model_terms:ir.ui.view,arch_db:payment_worldline.payment_provider_form
msgid "API Secret"
msgstr ""

#. module: payment_worldline
#: model:ir.model.fields,field_description:payment_worldline.field_payment_provider__code
msgid "Code"
msgstr "Kod"

#. module: payment_worldline
#. odoo-python
#: code:addons/payment_worldline/models/payment_provider.py:0
msgid "Could not establish the connection to the API."
msgstr "Det gick inte att upprätta anslutningen till API:et."

#. module: payment_worldline
#. odoo-python
#: code:addons/payment_worldline/models/payment_transaction.py:0
msgid "No transaction found matching reference %s."
msgstr "Ingen transaktion hittades som matchar referensen %s."

#. module: payment_worldline
#: model_terms:ir.ui.view,arch_db:payment_worldline.payment_provider_form
msgid "PSPID"
msgstr "PSPID"

#. module: payment_worldline
#: model:ir.model,name:payment_worldline.model_payment_provider
msgid "Payment Provider"
msgstr "Betalningsleverantör"

#. module: payment_worldline
#: model:ir.model,name:payment_worldline.model_payment_transaction
msgid "Payment Transaction"
msgstr "Betalningstransaktion"

#. module: payment_worldline
#. odoo-python
#: code:addons/payment_worldline/models/payment_transaction.py:0
msgid "Received data with missing payment state."
msgstr "Mottagen data med saknad betalningsstatus."

#. module: payment_worldline
#. odoo-python
#: code:addons/payment_worldline/models/payment_transaction.py:0
msgid "Received data with missing reference %(ref)s."
msgstr "Mottagna data med saknade referens %(ref)s."

#. module: payment_worldline
#. odoo-python
#: code:addons/payment_worldline/models/payment_transaction.py:0
msgid "Received invalid transaction status %(status)s."
msgstr ""

#. module: payment_worldline
#. odoo-python
#: code:addons/payment_worldline/models/payment_provider.py:0
msgid "The communication with the API failed. Details: %s"
msgstr "Kommunikationen med API:et misslyckades. Detaljerad information: %s"

#. module: payment_worldline
#: model:ir.model.fields,help:payment_worldline.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr "Den tekniska koden för denna betalningsleverantör."

#. module: payment_worldline
#. odoo-python
#: code:addons/payment_worldline/models/payment_transaction.py:0
msgid "The transaction is not linked to a token."
msgstr "Transaktionen är inte kopplad till en token."

#. module: payment_worldline
#: model_terms:ir.ui.view,arch_db:payment_worldline.payment_provider_form
msgid "Webhook Key"
msgstr ""

#. module: payment_worldline
#: model_terms:ir.ui.view,arch_db:payment_worldline.payment_provider_form
msgid "Webhook Secret"
msgstr "Webhook hemlighet"

#. module: payment_worldline
#: model:ir.model.fields.selection,name:payment_worldline.selection__payment_provider__code__worldline
msgid "Worldline"
msgstr "Världslinje"

#. module: payment_worldline
#: model:ir.model.fields,field_description:payment_worldline.field_payment_provider__worldline_api_key
msgid "Worldline API Key"
msgstr ""

#. module: payment_worldline
#: model:ir.model.fields,field_description:payment_worldline.field_payment_provider__worldline_api_secret
msgid "Worldline API Secret"
msgstr ""

#. module: payment_worldline
#: model:ir.model.fields,field_description:payment_worldline.field_payment_provider__worldline_pspid
msgid "Worldline PSPID"
msgstr ""

#. module: payment_worldline
#: model:ir.model.fields,field_description:payment_worldline.field_payment_provider__worldline_webhook_key
msgid "Worldline Webhook Key"
msgstr ""

#. module: payment_worldline
#: model:ir.model.fields,field_description:payment_worldline.field_payment_provider__worldline_webhook_secret
msgid "Worldline Webhook Secret"
msgstr ""
