# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_worldline
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-29 00:00+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Croatian (https://app.transifex.com/odoo/teams/41243/hr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hr\n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"

#. module: payment_worldline
#: model_terms:ir.ui.view,arch_db:payment_worldline.payment_provider_form
msgid "API Key"
msgstr "API ključ"

#. module: payment_worldline
#: model_terms:ir.ui.view,arch_db:payment_worldline.payment_provider_form
msgid "API Secret"
msgstr ""

#. module: payment_worldline
#: model:ir.model.fields,field_description:payment_worldline.field_payment_provider__code
msgid "Code"
msgstr "Šifra"

#. module: payment_worldline
#. odoo-python
#: code:addons/payment_worldline/models/payment_provider.py:0
msgid "Could not establish the connection to the API."
msgstr ""

#. module: payment_worldline
#. odoo-python
#: code:addons/payment_worldline/models/payment_transaction.py:0
msgid "No transaction found matching reference %s."
msgstr ""

#. module: payment_worldline
#: model_terms:ir.ui.view,arch_db:payment_worldline.payment_provider_form
msgid "PSPID"
msgstr ""

#. module: payment_worldline
#: model:ir.model,name:payment_worldline.model_payment_provider
msgid "Payment Provider"
msgstr "Pružatelj usluge naplate"

#. module: payment_worldline
#: model:ir.model,name:payment_worldline.model_payment_transaction
msgid "Payment Transaction"
msgstr "Transakcija plaćanja"

#. module: payment_worldline
#. odoo-python
#: code:addons/payment_worldline/models/payment_transaction.py:0
msgid "Received data with missing payment state."
msgstr ""

#. module: payment_worldline
#. odoo-python
#: code:addons/payment_worldline/models/payment_transaction.py:0
msgid "Received data with missing reference %(ref)s."
msgstr ""

#. module: payment_worldline
#. odoo-python
#: code:addons/payment_worldline/models/payment_transaction.py:0
msgid "Received invalid transaction status %(status)s."
msgstr ""

#. module: payment_worldline
#. odoo-python
#: code:addons/payment_worldline/models/payment_provider.py:0
msgid "The communication with the API failed. Details: %s"
msgstr ""

#. module: payment_worldline
#: model:ir.model.fields,help:payment_worldline.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr ""

#. module: payment_worldline
#. odoo-python
#: code:addons/payment_worldline/models/payment_transaction.py:0
msgid "The transaction is not linked to a token."
msgstr ""

#. module: payment_worldline
#: model_terms:ir.ui.view,arch_db:payment_worldline.payment_provider_form
msgid "Webhook Key"
msgstr ""

#. module: payment_worldline
#: model_terms:ir.ui.view,arch_db:payment_worldline.payment_provider_form
msgid "Webhook Secret"
msgstr ""

#. module: payment_worldline
#: model:ir.model.fields.selection,name:payment_worldline.selection__payment_provider__code__worldline
msgid "Worldline"
msgstr ""

#. module: payment_worldline
#: model:ir.model.fields,field_description:payment_worldline.field_payment_provider__worldline_api_key
msgid "Worldline API Key"
msgstr ""

#. module: payment_worldline
#: model:ir.model.fields,field_description:payment_worldline.field_payment_provider__worldline_api_secret
msgid "Worldline API Secret"
msgstr ""

#. module: payment_worldline
#: model:ir.model.fields,field_description:payment_worldline.field_payment_provider__worldline_pspid
msgid "Worldline PSPID"
msgstr ""

#. module: payment_worldline
#: model:ir.model.fields,field_description:payment_worldline.field_payment_provider__worldline_webhook_key
msgid "Worldline Webhook Key"
msgstr ""

#. module: payment_worldline
#: model:ir.model.fields,field_description:payment_worldline.field_payment_provider__worldline_webhook_secret
msgid "Worldline Webhook Secret"
msgstr ""
