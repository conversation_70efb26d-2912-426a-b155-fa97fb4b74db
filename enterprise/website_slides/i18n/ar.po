# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_slides
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__members_engaged_count
msgid "# Active Attendees"
msgstr "عدد الحاضرين النشطين "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_tree_report
msgid "# Attendees"
msgstr "عدد الحاضرين "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_tree_report
msgid "# Completed"
msgstr "عدد من أكملوا الدورة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__members_completed_count
msgid "# Completed Attendees"
msgstr "عدد الحاضرين الذين أكملوا الدورة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__completed_slides_count
msgid "# Completed Contents"
msgstr "عدد المحتويات المكتملة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__members_count
msgid "# Enrolled Attendees"
msgstr "عدد الحاضرين المسجلين "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__members_all_count
msgid "# Enrolled or Invited Attendees"
msgstr "عدد الحاضرين المسجلين والمدعووين "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__members_invited_count
msgid "# Invited Attendees"
msgstr "عدد الحاضرين المدعوين "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_partner_view_tree
msgid "# Likes"
msgstr "عدد الإعجابات "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_tree_report
msgid "# Questions"
msgstr "عدد الأسئلة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_partner_view_tree
msgid "# Quizz Attempts"
msgstr "عدد محاولات الاختبار القصير "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_partner_view_tree
msgid "# Total Attempts"
msgstr "إجمالي عدد المحاولات "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__total_views
msgid "# Total Views"
msgstr "إجمالي عدد المشاهدات "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__count_views
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_tree_report
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_tree_report
msgid "# Views"
msgstr "عدد المشاهدات"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__embed_count
msgid "# of Embeds"
msgstr "عدد الشرائح المضمنة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__public_views
msgid "# of Public Views"
msgstr "عدد المشاهدات العامة"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_views
msgid "# of Website Views"
msgstr "عدد مشاهدات الموقع الإلكتروني "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__completion
msgid "% Completed Contents"
msgstr "المحتوى المكتمل    "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.mail_notification_channel_invite
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "'. Showing results for '"
msgstr "'. يتم عرض النتائج لـ '"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_slides_list.js:0
#: code:addons/website_slides/static/src/js/slides_slide_archive.js:0
msgid "(empty)"
msgstr "(فارغ)"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid ". This way, they will be secured."
msgstr ". وبهذه الطريقة، سيكونون في أمان. "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_5
msgid "3 Main Methodologies"
msgstr "3 منهجيات رئيسية "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "<b>%s</b> is requesting access to this course."
msgstr "<b>%s</b> يطلب إذن الوصول إلى هذه الدورة. "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
msgid "<b>(empty)</b>"
msgstr "<b>(فارغ)</b>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "<b>Order by</b>"
msgstr "<b>ترتيب حسب</b>"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid ""
"<b>Save & Publish</b> your lesson to make it available to your attendees."
msgstr "<b>قم بحفظ ونشر</b> دروسك لجعلها متاحة لحاضريك. "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid "<b>Save</b> your question."
msgstr "<b>احفظ</b> سؤالك."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_training_category
msgid "<b>Uncategorized</b>"
msgstr "<b>غير مصنف</b>"

#. module: website_slides
#: model:mail.template,body_html:website_slides.mail_template_slide_channel_enroll
msgid ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Hello<br/><br/>\n"
"        You have been enrolled to a new course: <t t-out=\"object.channel_id.name or ''\">Basics of Gardening</t>.\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        مرحباً<br/><br/>\n"
"        لقد تم تسجيلك في دورة جديدة: <t t-out=\"object.channel_id.name or ''\">مبادئ العناية بالحدائق</t>.\n"
"    </p>\n"
"</div>\n"
"            "

#. module: website_slides
#: model:mail.template,body_html:website_slides.mail_template_slide_channel_invite
msgid ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Hello<br/><br/>\n"
"        You have been invited to check out this course: <t t-out=\"object.channel_id.name or ''\">Basics of Gardening</t>.\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        مرحباً<br/><br/>\n"
"        لقد تمت دعوتك لإلقاء نظرة على دورة: <t t-out=\"object.channel_id.name or ''\">مبادئ العناية بالحدائق</t>.\n"
"    </p>\n"
"</div>\n"
"            "

#. module: website_slides
#: model:mail.template,body_html:website_slides.mail_template_channel_completed
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        <p style=\"margin: 0px;\">Hello <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,</p><br/>\n"
"                        <p><b>Congratulations!</b></p>\n"
"                        <p>You've completed the course <b t-out=\"object.channel_id.name or ''\">Basics of Gardening</b></p>\n"
"                        <p>Check out the other available courses.</p><br/>\n"
"\n"
"                        <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a href=\"/slides/all\" style=\"background-color: #875a7b; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                                Explore courses\n"
"                            </a>\n"
"                        </div>\n"
"                        Enjoy this exclusive content!\n"
"                        <t t-if=\"object.channel_id.user_id.signature\" data-o-mail-quote-container=\"1\">\n"
"                            <br/>\n"
"                            <t t-out=\"object.channel_id.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"                        </t>\n"
"                    </div>\n"
"                </div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        <p style=\"margin: 0px;\">مرحباً <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>،</p><br/>\n"
"                        <p><b>تهانينا!</b></p>\n"
"                        <p>لقد أكملت دورة <b t-out=\"object.channel_id.name or ''\">مبادئ العناية بالحدائق</b></p>\n"
"                        <p>ألقِ نظرة على الدورات الأخرى المتاحة.</p><br/>\n"
"\n"
"                        <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a href=\"/slides/all\" style=\"background-color: #875a7b; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                                استكشف الدورات\n"
"                            </a>\n"
"                        </div>\n"
"                        استمتع بهذا المحتوى الحصري!\n"
"                        <t t-if=\"object.channel_id.user_id.signature\" data-o-mail-quote-container=\"1\">\n"
"                            <br/>\n"
"                            <t t-out=\"object.channel_id.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"                        </t>\n"
"                    </div>\n"
"                </div>\n"
"            "

#. module: website_slides
#: model:mail.template,body_html:website_slides.mail_template_channel_shared
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Hello<br/><br/>\n"
"                        <t t-out=\"user.name or ''\">Mitchell Admin</t> shared the <strong t-out=\"object.name or ''\">document</strong> with you!\n"
"                        </p><div style=\"margin: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a t-att-href=\"object.website_url\">\n"
"                                <img t-att-alt=\"object.name\" t-attf-src=\"{{ ctx.get('base_url') }}/web/image/slide.channel/{{ object.id }}/image_256\" style=\"height:auto; width:150px; margin: 16px;\"/>\n"
"                            </a>\n"
"                        </div>\n"
"                        <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a t-att-href=\"object.website_url\" style=\"background-color: #875a7b; padding: 8px 16px 8px 16px;                                 text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                                View <strong t-out=\"object.name or ''\">Document</strong></a>\n"
"                        </div>\n"
"                        <t t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"                            <br/>\n"
"                            <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"                        </t>\n"
"                    \n"
"                </div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        مرحباً<br/><br/>\n"
"                        <t t-out=\"user.name or ''\">قام ميتشل آدمن</t> بمشاركة مستند <strong t-out=\"object.name or ''\"></strong> معك!\n"
"                        </p><div style=\"margin: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a t-att-href=\"object.website_url\">\n"
"                                <img t-att-alt=\"object.name\" t-attf-src=\"{{ ctx.get('base_url') }}/web/image/slide.channel/{{ object.id }}/image_256\" style=\"height:auto; width:150px; margin: 16px;\"/>\n"
"                            </a>\n"
"                        </div>\n"
"                        <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a t-att-href=\"object.website_url\" style=\"background-color: #875a7b; padding: 8px 16px 8px 16px;                                 text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                                عرض المستند<strong t-out=\"object.name or ''\"></strong></a>\n"
"                        </div>\n"
"                        <t t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"                            <br/>\n"
"                            <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>ميتشل آدمن</t>\n"
"                        </t>\n"
"                    \n"
"                </div>\n"
"            "

#. module: website_slides
#: model:mail.template,body_html:website_slides.slide_template_shared
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Hello<br/><br/>\n"
"                        <t t-out=\"user.name or ''\">Mitchell Admin</t> shared the <t t-out=\"object.slide_category or ''\">document</t> <strong t-out=\"object.name or ''\">Trees</strong> with you!\n"
"                        </p><div style=\"margin: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a t-att-href=\"(object.website_share_url + '?fullscreen=1') if ctx.get('fullscreen') else object.website_share_url\">\n"
"                                <img t-att-alt=\"object.name\" t-attf-src=\"{{ ctx.get('base_url') }}/web/image/slide.slide/{{ object.id }}/image_1024\" style=\"height:auto; width:150px; margin: 16px;\"/>\n"
"                            </a>\n"
"                        </div>\n"
"                        <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a t-att-href=\"(object.website_share_url + '?fullscreen=1') if ctx.get('fullscreen') else object.website_share_url\" style=\"background-color: #875a7b; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">View <strong t-out=\"object.name or ''\">Trees</strong></a>\n"
"                        </div>\n"
"                        <t t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"                            <br/>\n"
"                            <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"                        </t>\n"
"                    \n"
"                </div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        مرحباً<br/><br/>\n"
"                        لقد قام <t t-out=\"user.name or ''\">ميتشل آدمن</t> بمشاركة <t t-out=\"object.slide_category or ''\">المستند</t> <strong t-out=\"object.name or ''\">الأشجار</strong> معك!\n"
"                        </p><div style=\"margin: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a t-att-href=\"(object.website_share_url + '?fullscreen=1') if ctx.get('fullscreen') else object.website_share_url\">\n"
"                                <img t-att-alt=\"object.name\" t-attf-src=\"{{ ctx.get('base_url') }}/web/image/slide.slide/{{ object.id }}/image_1024\" style=\"height:auto; width:150px; margin: 16px;\"/>\n"
"                            </a>\n"
"                        </div>\n"
"                        <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a t-att-href=\"(object.website_share_url + '?fullscreen=1') if ctx.get('fullscreen') else object.website_share_url\" style=\"background-color: #875a7b; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">عرض <strong t-out=\"object.name or ''\">الأشجار</strong></a>\n"
"                        </div>\n"
"                        <t t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"                            <br/>\n"
"                            <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>ميتشل آدمن</t>\n"
"                        </t>\n"
"                    \n"
"                </div>\n"
"            "

#. module: website_slides
#: model:mail.template,body_html:website_slides.slide_template_published
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Hello<br/><br/>\n"
"                        There is something new in the course <strong t-out=\"object.channel_id.name or ''\">Trees, Wood and Gardens</strong> you are following:<br/><br/>\n"
"                        </p><center><strong t-out=\"object.name or ''\">Trees</strong></center>\n"
"                        <t t-if=\"object.image_1024\">\n"
"                            <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                                <a t-att-href=\"object.website_share_url\">\n"
"                                <img t-att-alt=\"object.name\" t-attf-src=\"{{ ctx.get('base_url') }}/web/image/slide.slide/{{ object.id }}/image_1024\" style=\"height:auto; width:150px; margin: 16px;\"/>\n"
"                            </a>\n"
"                        </div>\n"
"                        </t>\n"
"                        <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a t-att-href=\"object.website_share_url\" style=\"background-color: #875a7b; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">View content</a>\n"
"                        </div>\n"
"                        Enjoy this exclusive content!\n"
"                        <t t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"                            <br/>\n"
"                            <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"                        </t>\n"
"                    \n"
"                </div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        مرحباً<br/><br/>\n"
"                        لقد تم إضافة محتوى جديد في دورة <strong t-out=\"object.channel_id.name or ''\">الأشجار، الخشب، والحدائق</strong> التي تتبعها:<br/><br/>\n"
"                        </p><center><strong t-out=\"object.name or ''\">الأشجار</strong></center>\n"
"                        <t t-if=\"object.image_1024\">\n"
"                            <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                                <a t-att-href=\"object.website_share_url\">\n"
"                                <img t-att-alt=\"object.name\" t-attf-src=\"{{ ctx.get('base_url') }}/web/image/slide.slide/{{ object.id }}/image_1024\" style=\"height:auto; width:150px; margin: 16px;\"/>\n"
"                            </a>\n"
"                        </div>\n"
"                        </t>\n"
"                        <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a t-att-href=\"object.website_share_url\" style=\"background-color: #875a7b; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">عرض المحتوى</a>\n"
"                        </div>\n"
"                        استمتع بهذا المحتوى الحصري!\n"
"                        <t t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"                            <br/>\n"
"                            <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>ميتشل آدمن</t>\n"
"                        </t>\n"
"                    \n"
"                </div>\n"
"            "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
msgid "<i class=\"fa fa-arrow-right\"/> Start Learning"
msgstr "<i class=\"fa fa-arrow-right\"/> ابدأ بالتعلم "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<i class=\"fa fa-bar-chart\"/> Statistics"
msgstr "<i class=\"fa fa-bar-chart\"/> الإحصائيات"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid ""
"<i class=\"fa fa-bars\"/><span class=\"d-none d-md-inline-block "
"ms-1\">Lessons</span>"
msgstr ""
"<i class=\"fa fa-bars\"/><span class=\"d-none d-md-inline-block "
"ms-1\">الدروس</span> "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_card
msgid "<i class=\"fa fa-check me-1\"/>Completed"
msgstr "<i class=\"fa fa-check me-1\"/> تم الإكمال "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid "<i class=\"fa fa-check\"/> Completed"
msgstr "<i class=\"fa fa-check\"/> تم الإكمال "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid "<i class=\"fa fa-circle-o-notch fa-spin me-2\"/><b>Loading...</b>"
msgstr "<i class=\"fa fa-circle-o-notch fa-spin me-2\"/><b>جارِ التحميل...</b> "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_embed
msgid "<i class=\"fa fa-clipboard\"/> Copy Embed Code"
msgstr "<i class=\"fa fa-clipboard\"/> نسخ الكود المضمن "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_link
msgid "<i class=\"fa fa-clipboard\"/> Copy Link"
msgstr "<i class=\"fa fa-clipboard\"/> نسخ الرابط "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid ""
"<i class=\"fa fa-clock-o me-2\" aria-label=\"Duration\" role=\"img\" "
"title=\"Duration\"/>"
msgstr ""
"<i class=\"fa fa-clock-o me-2\" aria-label=\"Duration\" role=\"img\" "
"title=\"المدة \"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "<i class=\"fa fa-cloud-upload me-1\"/>Add Content"
msgstr "<i class=\"fa fa-cloud-upload me-1\"/>إضافة محتوى "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<i class=\"fa fa-comments\"/> Comments ("
msgstr "<i class=\"fa fa-comments\"/> التعليقات ("

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid ""
"<i class=\"fa fa-desktop me-xl-2 my-1\"/>\n"
"                <span class=\"d-none d-xl-inline-block\">Fullscreen</span>"
msgstr ""
"<i class=\"fa fa-desktop me-xl-2 my-1\"/>\n"
"                <span class=\"d-none d-xl-inline-block\">ملء الشاشة</span> "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid "<i class=\"fa fa-envelope\"/> Send Email"
msgstr "<i class=\"fa fa-envelope\"/> إرسال بريد إلكتروني "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "<i class=\"fa fa-eraser me-1\"/>Clear filters"
msgstr "<i class=\"fa fa-eraser me-1\"/>إزالة عوامل التصفية "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "<i class=\"fa fa-eraser\"/> Clear filters"
msgstr "<i class=\"fa fa-eraser\"/> إزالة عوامل التصفية "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide_forbidden
msgid ""
"<i class=\"fa fa-exclamation-triangle\" role=\"img\" aria-"
"label=\"Attention\" title=\"Attention\"/> This document is private."
msgstr ""
"<i class=\"fa fa-exclamation-triangle\" role=\"img\" aria-"
"label=\"Attention\" title=\"انتباه \"/> هذا المستند خاص."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid "<i class=\"fa fa-eye me-2\" aria-label=\"Views\" role=\"img\" title=\"Views\"/>"
msgstr "<i class=\"fa fa-eye me-2\" aria-label=\"Views\" role=\"img\" title=\"المشاهدات \"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid ""
"<i class=\"fa fa-file-code-o me-2\" aria-label=\"article\" role=\"img\" "
"title=\"Article\"/>"
msgstr ""
"<i class=\"fa fa-file-code-o me-2\" aria-label=\"article\" role=\"img\" "
"title=\"المقال \"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid ""
"<i class=\"fa fa-file-image-o me-2\" aria-label=\"Infographic\" role=\"img\""
" title=\"Infographic\"/>"
msgstr ""
"<i class=\"fa fa-file-image-o me-2\" aria-label=\"Infographic\" role=\"img\""
" title=\"مخطط معلومات بياني \"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid ""
"<i class=\"fa fa-file-pdf-o me-2\" aria-label=\"Document\" role=\"img\" "
"title=\"Document\"/>"
msgstr ""
"<i class=\"fa fa-file-pdf-o me-2\" aria-label=\"Document\" role=\"img\" "
"title=\"المستند \"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid ""
"<i class=\"fa fa-file-video-o me-2\" aria-label=\"Video\" role=\"img\" "
"title=\"Video\"/>"
msgstr ""
"<i class=\"fa fa-file-video-o me-2\" aria-label=\"Video\" role=\"img\" "
"title=\"الفيديو \"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid "<i class=\"fa fa-flag me-2\" aria-label=\"Quiz\" role=\"img\" title=\"Quiz\"/>"
msgstr ""
"<i class=\"fa fa-flag me-2\" aria-label=\"Quiz\" role=\"img\" title=\"اختبار"
" قصير \"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_training_category
msgid "<i class=\"fa fa-flag text-warning\"/> Quiz"
msgstr "<i class=\"fa fa-flag text-warning\"/>اختبار قصير "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen_sidebar_category
msgid "<i class=\"fa fa-flag-checkered text-warning\"/>Quiz"
msgstr "<i class=\"fa fa-flag-checkered text-warning\"/>اختبار قصير "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
msgid "<i class=\"fa fa-folder-o me-1\"/><span>Add Section</span>"
msgstr "<i class=\"fa fa-folder-o me-1\"/><span>إضافة قسم</span> "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "<i class=\"fa fa-folder-o me-1\"/>Add a section"
msgstr "<i class=\"fa fa-folder-o me-1\"/>إضافة قسم "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_pages_kanban_view
msgid "<i class=\"fa fa-globe me-1\" title=\"Website\"/>"
msgstr "<i class=\"fa fa-globe me-1\" title=\"الموقع الإلكتروني \"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "<i class=\"fa fa-graduation-cap me-1\"/>All courses"
msgstr "<i class=\"fa fa-graduation-cap me-1\"/>كافة الدورات "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<i class=\"fa fa-home\"/> About"
msgstr "<i class=\"fa fa-home\"/> عن المحتوى"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
msgid "<i class=\"fa fa-home\"/> Course"
msgstr "<i class=\"fa fa-home\"/>الدورة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid ""
"<i class=\"fa fa-home\"/><span class=\"d-none d-md-inline-block ms-1\">Back "
"to course</span>"
msgstr ""
"<i class=\"fa fa-home\"/><span class=\"d-none d-md-inline-block "
"ms-1\">العودة إلى الدورة</span> "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid ""
"<i class=\"fa fa-lock\"/>\n"
"                        <span>Course Locked</span>"
msgstr ""
"<i class=\"fa fa-lock\"/>\n"
"                        <span>الدورة مقفلة</span> "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
msgid ""
"<i class=\"fa fa-plus me-1\"/> <span class=\"d-none d-md-inline-block\">Add "
"Content</span>"
msgstr ""
"<i class=\"fa fa-plus me-1\"/> <span class=\"d-none d-md-inline-"
"block\">إضافة محتوى</span> "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
msgid "<i class=\"fa fa-plus me-1\"/><span>Add Content</span>"
msgstr "<i class=\"fa fa-plus me-1\"/><span>إضافة محتوى</span> "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_content_quiz_add_buttons
msgid ""
"<i class=\"fa fa-plus me-2\"/>\n"
"            <span>Add Question</span>"
msgstr ""
"<i class=\"fa fa-plus me-2\"/>\n"
"            <span>إضافة سؤال</span> "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_content_quiz_add_buttons
msgid ""
"<i class=\"fa fa-plus me-2\"/>\n"
"            <span>Add Quiz</span>"
msgstr ""
"<i class=\"fa fa-plus me-2\"/>\n"
"            <span>إضافة اختبار قصير</span> "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid ""
"<i class=\"fa fa-question me-2\" aria-label=\"Number of Questions\" "
"role=\"img\" title=\"Number of Questions\"/>"
msgstr ""
"<i class=\"fa fa-question me-2\" aria-label=\"Number of Questions\" "
"role=\"img\" title=\"عدد الأسئلة \"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid ""
"<i class=\"fa fa-share-alt me-xl-2 my-1\"/>\n"
"                <span class=\"d-none d-xl-inline-block\">Share</span>"
msgstr ""
"<i class=\"fa fa-share-alt me-xl-2 my-1\"/>\n"
"                <span class=\"d-none d-xl-inline-block\">مشاركة</span> "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid ""
"<i class=\"fa fa-share-alt\" aria-label=\"Share\" title=\"Share\"/>\n"
"                                            Share"
msgstr ""
"<i class=\"fa fa-share-alt\" aria-label=\"Share\" title=\"مشاركة \"/>\n"
"                                            مشاركة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid ""
"<i class=\"fa fa-share-alt\"/>\n"
"                        <span class=\"d-none d-md-inline-block ms-2\">Share</span>"
msgstr ""
"<i class=\"fa fa-share-alt\"/>\n"
"                        <span class=\"d-none d-md-inline-block ms-2\">مشاركة</span> "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "<i class=\"fa fa-share-alt\"/> Share"
msgstr "<i class=\"fa fa-share-alt\"/> مشاركة"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid ""
"<i class=\"fa fa-sign-out\"/><span class=\"d-none d-md-inline-block "
"ms-1\">Exit Fullscreen</span>"
msgstr ""
"<i class=\"fa fa-sign-out\"/><span class=\"d-none d-md-inline-block "
"ms-1\">الخروج من وضع ملء الشاشة</span> "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid ""
"<i class=\"fa fa-tag me-2 text-muted\"/>\n"
"                      My Courses"
msgstr ""
"<i class=\"fa fa-tag me-2 text-muted\"/>\n"
"                      دوراتي "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
msgid "<i class=\"oi oi-arrow-right me-1\"/>All Courses"
msgstr "<i class=\"oi oi-arrow-right me-1\"/>كافة الدورات "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid ""
"<i class=\"oi oi-chevron-left me-2\"/> <span class=\"d-none d-sm-inline-"
"block\">Prev</span>"
msgstr ""
"<i class=\"oi oi-chevron-left me-2\"/> <span class=\"d-none d-sm-inline-"
"block\">السابق</span> "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid ""
"<small class=\"text-success\">\n"
"                        Request already sent\n"
"                    </small>"
msgstr ""
"<small class=\"text-success\">\n"
"                        لقد تم إرسال الطلب بالفعل\n"
"                    </small>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_main
msgid ""
"<small><span class=\"badge text-bg-success fw-normal\"><i class=\"fa fa-"
"check\"/> Completed</span></small>"
msgstr ""
"<small><span class=\"badge text-bg-success fw-normal\"><i class=\"fa fa-"
"check\"/> تم الانتهاء</span></small>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid ""
"<span class=\"badge fw-bold m-1 text-bg-warning\">\n"
"                            <i class=\"fa fa-fw fa-flag\"/> 10 xp\n"
"                        </span>"
msgstr ""
"<span class=\"badge fw-bold m-1 text-bg-warning\">\n"
"                            <i class=\"fa fa-fw fa-flag\"/> 10 xp\n"
"                        </span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid ""
"<span class=\"badge text-bg-info badge-arrow-right fw-normal "
"m-1\">New</span>"
msgstr ""
"<span class=\"badge text-bg-info badge-arrow-right fw-normal "
"m-1\">جديد</span> "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
msgid ""
"<span class=\"badge text-bg-primary badge-hide fw-normal m-1\">Add "
"Quiz</span>"
msgstr ""
"<span class=\"badge text-bg-primary badge-hide fw-normal m-1\">إضافة اختبار "
"قصير</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
msgid ""
"<span class=\"badge text-bg-success fw-normal "
"m-1\"><span>Preview</span></span>"
msgstr ""
"<span class=\"badge text-bg-success fw-normal "
"m-1\"><span>معاينة</span></span> "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span class=\"badge text-bg-success fw-normal m-1\">Preview</span>"
msgstr "<span class=\"badge text-bg-success fw-normal m-1\">معاينة</span> "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_card
msgid ""
"<span class=\"badge text-bg-success pull-right\"><i class=\"fa fa-check\"/> "
"Completed</span>"
msgstr ""
"<span class=\"badge text-bg-success pull-right\"><i class=\"fa fa-check\"/> "
"تم الانتهاء</span> "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid ""
"<span class=\"d-none d-sm-inline-block\">Next</span> <i class=\"oi oi-"
"chevron-right ms-2\"/>"
msgstr ""
"<span class=\"d-none d-sm-inline-block\">التالي</span> <i class=\"oi oi-"
"chevron-right ms-2\"/> "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_home_user_profile_small
msgid "<span class=\"fw-bold text-muted me-2\">Current rank:</span>"
msgstr "<span class=\"fw-bold text-muted me-2\">المرتبة الحالية:</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_main
msgid "<span class=\"fw-normal\">Last update:</span>"
msgstr "<span class=\"fw-normal\">آخر تحديث:</span> "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_embed
msgid "<span class=\"input-group-text\">Start at Page</span>"
msgstr "<span class=\"input-group-text\">البدء من الصفحة</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
msgid ""
"<span class=\"ms-1\">Lessons</span>\n"
"                                <span class=\"ms-1\">·</span>"
msgstr ""
"<span class=\"ms-1\">الدروس</span>\n"
"                                <span class=\"ms-1\">·</span> "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Slides</span>\n"
"                    <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"o_form_label\">الشرائح</span>\n"
"                    <span class=\"fa fa-lg fa-globe\" title=\"القيم المحددة هنا خاصة بالموقع الإلكتروني فقط. \" groups=\"website.group_multi_website\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "<span class=\"o_stat_text\">Attendees</span>"
msgstr "<span class=\"o_stat_text\">الحاضرون</span> "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_partner_view_form
msgid "<span class=\"o_stat_text\">Courses</span>"
msgstr "<span class=\"o_stat_text\">الدورات</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "<span class=\"o_stat_text\">Embed Views</span>"
msgstr "<span class=\"o_stat_text\">تضمين موقع إلكتروني خارجي</span> "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_card_information_arrow
msgid "<span class=\"o_wslides_arrow\">New Content</span>"
msgstr "<span class=\"o_wslides_arrow\">محتوى جديد</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid ""
"<span class=\"oi oi-arrow-right\"/>\n"
"                                Create a Google Project and Get a Key"
msgstr ""
"<span class=\"oi oi-arrow-right\"/>\n"
"                                إنشاء مشروع Google والحصول على مفتاح "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_training
msgid "<span class=\"p-2\">Course content</span>"
msgstr "<span class=\"p-2\">محتوى الدورة</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.user_quickkarma_card
msgid "<span class=\"text-500 mx-2\">•</span>"
msgstr "<span class=\"text-500 mx-2\">•</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid ""
"<span class=\"text-muted fw-bold col-4 col-md-3\">\n"
"                        Additional Resources\n"
"                    </span>"
msgstr ""
"<span class=\"text-muted fw-bold col-4 col-md-3\">\n"
"                        الموارد الإضافية\n"
"                    </span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<span class=\"text-muted fw-bold col-4 col-md-3\">External sources</span>"
msgstr ""
"<span class=\"text-muted fw-bold col-4 col-md-3\">المصادر الخارجية</span> "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<span class=\"text-muted fw-bold me-3\">Rating</span>"
msgstr "<span class=\"text-muted fw-bold me-3\">التقييم</span> "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid ""
"<span class=\"text-muted text-truncate mw-100\" "
"title=\"Invited\">Invited</span>"
msgstr ""
"<span class=\"text-muted text-truncate mw-100\" title=\"تمت دعوته \">تمت "
"دعوته</span> "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid ""
"<span class=\"text-muted text-truncate mw-100\" "
"title=\"Ongoing\">Ongoing</span>"
msgstr "<span class=\"text-muted text-truncate mw-100\" title=\"جاري \">جاري</span> "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "<span class=\"text-muted text-truncate mw-100\" title=\"Total\">Total</span>"
msgstr ""
"<span class=\"text-muted text-truncate mw-100\" title=\"الإجمالي "
"\">الإجمالي</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span class=\"text-muted\">Common tasks for a computer scientist</span>"
msgstr "<span class=\"text-muted\">المهام الاعتيادية لعالِم الحاسوب</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span class=\"text-muted\">Parts of computer science</span>"
msgstr "<span class=\"text-muted\">أجزاء من علوم الحاسوب</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid ""
"<span id=\"first\" class=\"me-1 me-sm-2\" title=\"First slide\" aria-label=\"First slide\" role=\"button\"><i class=\"fa fa-step-backward\"/></span>\n"
"                                        <span id=\"previous\" class=\"mx-1 mx-sm-2\" title=\"Previous slide\" aria-label=\"Previous slide\" role=\"button\"><i class=\"fa fa-arrow-circle-left\"/></span>\n"
"                                        <span id=\"next\" class=\"mx-1 mx-sm-2\" title=\"Next slide\" aria-label=\"Next slide\" role=\"button\"><i class=\"fa fa-arrow-circle-right\"/></span>\n"
"                                        <span id=\"last\" class=\"mx-1 mx-sm-2\" title=\"Last slide\" aria-label=\"Last slide\" role=\"button\"><i class=\"fa fa-step-forward\"/></span>"
msgstr ""
"<span id=\"first\" class=\"me-1 me-sm-2\" title=\"الشريحة الأولى \" aria-label=\"First slide\" role=\"button\"><i class=\"fa fa-step-backward\"/></span>\n"
"                                        <span id=\"previous\" class=\"mx-1 mx-sm-2\" title=\"الشريحة السابقة \" aria-label=\"Previous slide\" role=\"button\"><i class=\"fa fa-arrow-circle-left\"/></span>\n"
"                                        <span id=\"next\" class=\"mx-1 mx-sm-2\" title=\"الشريحة التالية \" aria-label=\"Next slide\" role=\"button\"><i class=\"fa fa-arrow-circle-right\"/></span>\n"
"                                        <span id=\"last\" class=\"mx-1 mx-sm-2\" title=\"الشريحة الأخيرة \" aria-label=\"Last slide\" role=\"button\"><i class=\"fa fa-step-forward\"/></span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid ""
"<span id=\"fullscreen\" class=\"ms-1 ms-sm-2\" title=\"View fullscreen\" aria-label=\"Fullscreen\" role=\"button\">\n"
"                                            <i class=\"fa fa-arrows-alt\"/>\n"
"                                        </span>"
msgstr ""
"<span id=\"fullscreen\" class=\"ms-1 ms-sm-2\" title=\"عرض ملء الشاشة \" aria-label=\"Fullscreen\" role=\"button\">\n"
"                                            <i class=\"fa fa-arrows-alt\"/>\n"
"                                        </span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid ""
"<span id=\"zoomout\" class=\"d-none d-sm-inline ms-2 me-2\" title=\"Zoom out\" aria-label=\"Zoom out\" role=\"button\">\n"
"                                            <i class=\"fa fa-search-minus\"/>\n"
"                                        </span>\n"
"                                        <span id=\"zoomin\" class=\"d-none d-sm-inline\" title=\"Zoom in\" aria-label=\"Zoom in\" role=\"button\">\n"
"                                            <i class=\"fa fa-search-plus\"/>\n"
"                                        </span>"
msgstr ""
"<span id=\"zoomout\" class=\"d-none d-sm-inline ms-2 me-2\" title=\"تصغير \" aria-label=\"Zoom out\" role=\"button\">\n"
"                                            <i class=\"fa fa-search-minus\"/>\n"
"                                        </span>\n"
"                                        <span id=\"zoomin\" class=\"d-none d-sm-inline\" title=\"تكبير \" aria-label=\"Zoom in\" role=\"button\">\n"
"                                            <i class=\"fa fa-search-plus\"/>\n"
"                                        </span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid ""
"<span name=\"done_members_count_label\" class=\"text-muted text-truncate "
"mw-100\" title=\"Finished\">Finished</span>"
msgstr ""
"<span name=\"done_members_count_label\" class=\"text-muted text-truncate "
"mw-100\" title=\"منتهية \">منتهية</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid ""
"<span name=\"members_completed_count_label\" "
"class=\"o_stat_text\">Finished</span>"
msgstr ""
"<span name=\"members_completed_count_label\" "
"class=\"o_stat_text\">منتهية</span> "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "<span> hours</span>"
msgstr "<span> الساعات</span>"

#. module: website_slides
#: model_terms:web_tour.tour,rainbow_man_message:website_slides.slides_tour
msgid "<span><b>Good job!</b> You went through all steps of this tour.</span>"
msgstr "<span><b>عمل رائع!</b> لقد تخطيت كافة مراحل هذه الجولة.</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "<span>Add Tag</span>"
msgstr "<span>إضافة علامة</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span>Answering Questions</span>"
msgstr "<span>إجابة الأسئلة</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span>Asking Question</span>"
msgstr "<span>طرح سؤال</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span>Asking the right question</span>"
msgstr "<span>طرح السؤال المناسب</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<span>Content only accessible to course attendees.</span>"
msgstr "<span>وحدهم حاضرو الدورة بوسعهم الوصول إلى المحتوى.</span> "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span>Logic</span>"
msgstr "<span>منطق</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span>Mathematics</span>"
msgstr "<span>الرياضيات</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
msgid "<span>Preview</span>"
msgstr "<span>معاينة</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span>Science</span>"
msgstr "<span>العلوم</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<span>XP</span>"
msgstr "<span>XP</span>"

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_channel_action_overview
#: model_terms:ir.actions.act_window,help:website_slides.slide_channel_action_report
msgid "<strong>Create a course</strong>"
msgstr "<strong>إنشاء دورة </strong>"

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_slide_partner_action_from_slide
msgid "<strong>No Attendee Yet!</strong>"
msgstr "<strong>لا يوجد حاضرون بعد!</strong>"

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_channel_partner_action
#: model_terms:ir.actions.act_window,help:website_slides.slide_channel_partner_action_report
msgid "<strong>No Attendees Yet!</strong>"
msgstr "<strong>لا يوجد حاضرون بعد!</strong> "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid "<strong>Sharing is caring!</strong> Email(s) sent."
msgstr ""
"<strong>المشاركة رائعة!</strong> تم إرسال رسالة (رسائل) البريد الإلكتروني. "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_2_1
msgid "A Mighty Forest from Ages"
msgstr "غابة هائلة من قديم الزمان "

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_0_4_question_0_0
msgid "A fruit"
msgstr "فاكهة "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid ""
"A good course has a structure. Pick a name for your first <b>Section</b>."
msgstr ""
"يجب أن تحتوي الدورة الجيدة على هيكل. اختر اسماً لـ <b>الجزء</b> الأول من "
"دورتك. "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_4
msgid "A little chat with Harry Potted"
msgstr "دردشة قصيرة مع Harry Potted "

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_2_gard2
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_2_gard2
msgid ""
"A lot of nice documentation: trees, wood, gardens. A gold mine for "
"references."
msgstr ""
"العديد من الوثائق الرائعة: الأشجار، الأخشاب، الحدائق. إنه منجم ذهب للمراجع. "

#. module: website_slides
#: model:ir.model.constraint,message:website_slides.constraint_slide_channel_partner_channel_partner_uniq
msgid "A partner membership to a channel must be unique!"
msgstr "يجب أن تكون عضوية الشريك في القناة فريدة! "

#. module: website_slides
#: model:ir.model.constraint,message:website_slides.constraint_slide_slide_partner_slide_partner_uniq
msgid "A partner membership to a slide must be unique!"
msgstr "يجب أن تكون عضوية الشريك في الشريحة فريدة! "

#. module: website_slides
#: model:ir.model.constraint,message:website_slides.constraint_slide_slide_resource_check_file_type
msgid "A resource of type file cannot contain a link."
msgstr "لا يمكن أن يحتوي مَورد نوعه ملف على رابط. "

#. module: website_slides
#: model:ir.model.constraint,message:website_slides.constraint_slide_slide_resource_check_url
msgid "A resource of type url must contain a link."
msgstr "يجب أن يحتوي المَورد من نوع URL على رابط. "

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_0_4_question_1_0
msgid "A shovel"
msgstr "مجرفة "

#. module: website_slides
#: model:ir.model.constraint,message:website_slides.constraint_slide_slide_exclusion_html_content_and_url
msgid "A slide is either filled with a url or HTML content. Not both."
msgstr ""
"عادة ما تكون الشريحة بها رابط URL أو محتوى HTML، ولكن ليس الاثنين معاً. "

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_0_4_question_1_1
msgid "A spoon"
msgstr "ملعقة "

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_5
msgid "A summary of know-how: how and what."
msgstr "ملخص المعرفة: كيف وماذا. "

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_0_0
msgid ""
"A summary of know-how: how and what. All the basics for this course about "
"gardening."
msgstr ""
"ملخص المعرفة: كيف وماذا. كافة المبادئ لهذه الدورة عن العناية بالحدائق. "

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_2_0
msgid ""
"A summary of know-how: what are the main trees categories and how to "
"differentiate them."
msgstr "ملخص المعرفة: فئات الأشجار الرئيسية وكيفية التفريق بينها. "

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_0_4_question_0_2
msgid "A table"
msgstr "طاولة "

#. module: website_slides
#: model:ir.model.constraint,message:website_slides.constraint_slide_tag_slide_tag_unique
msgid "A tag must be unique!"
msgstr "يجب أن تكون علامة التصنيف فريدةً من نوعها! "

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_0_4_question_0_1
msgid "A vegetable"
msgstr "خضار "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "API Key"
msgstr "مفتاح الواجهة البرمجية للتطبيق "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "Access Granted"
msgstr "تم السماح بالوصول "

#. module: website_slides
#: model:ir.model,name:website_slides.model_res_groups
msgid "Access Groups"
msgstr "مجموعات الوصول"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "Access Refused"
msgstr "تم رفض الوصول "

#. module: website_slides
#: model:mail.activity.type,name:website_slides.mail_activity_data_access_request
msgid "Access Request"
msgstr "أذونات الوصول "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__has_requested_access
msgid "Access Requested"
msgstr "تم طلب إذن بالوصول "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Access Rights"
msgstr "صلاحيات الوصول"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_partner_view_tree
msgid "Accessed on"
msgstr "تم فتحها في "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.snippet_options
msgid "Achievements"
msgstr "الإنجازات "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_needaction
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_needaction
msgid "Action Needed"
msgstr "إجراء مطلوب"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__active
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__active
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__active
msgid "Active"
msgstr "نشط"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__members_engaged_count
msgid "Active attendees include both 'joined' and 'ongoing' attendees."
msgstr "يشمل الحاضرون النشطون كل من الحاضرين \"المنضمين\" و\"الجارين\". "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_ids
msgid "Activities"
msgstr "الأنشطة"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "زخرفة استثناء النشاط"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_state
msgid "Activity State"
msgstr "حالة النشاط"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_type_icon
msgid "Activity Type Icon"
msgstr "أيقونة نوع النشاط"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/course_tag_add_dialog/course_tag_add_dialog.xml:0
msgid "Add"
msgstr "إضافة"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Add Attendees"
msgstr "إضافة حاضرين "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__karma_slide_comment
msgid "Add Comment"
msgstr "إضافة تعليق"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.js:0
#: model_terms:ir.actions.act_window,help:website_slides.slide_slide_action
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Add Content"
msgstr "إضافة محتوى"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__karma_review
msgid "Add Review"
msgstr "إضافة تقييم "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Add Section"
msgstr "إضافة قسم"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/course_tag_add_dialog/course_tag_add_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Add Tag"
msgstr "إضافة علامة تصنيف "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_category_add.js:0
msgid "Add a section"
msgstr "إضافة قسم"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
msgid "Add a tag"
msgstr "إضافة علامة تصنيف "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
msgid "Add an answer below this one"
msgstr "إضافة إجابة تحتها "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
msgid "Add comment on this answer"
msgstr "إضافة تعليق على هذه الإجابة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
msgid "Add contacts..."
msgstr "إضافة جهات الاتصال... "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
msgid "Add quiz"
msgstr "إضافة اختبار قصير "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Add your content here..."
msgstr "أضف محتواك هنا... "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Added On"
msgstr "تمت إضافته في "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_resource_ids
msgid "Additional Resource for this slide"
msgstr "مورد إضافي لهذه الشريحة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Additional Resources"
msgstr "موارد إضافية "

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_slide_resource
msgid "Additional resource for a particular slide"
msgstr "مورد إضافي لشريحة معيّنة "

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_level_advanced
msgid "Advanced"
msgstr "متقدم"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__channel_partner_all_ids
msgid "All Attendees Information"
msgstr "كافة معلومات الحاضرين "

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_channel_action_overview
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "All Courses"
msgstr "كافة الدورات"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_unsubscribe_dialog/slide_unsubscribe_dialog.xml:0
msgid "All progress will be lost until you rejoin this course."
msgstr "سيضيع كل التقدم الذي أحرزته إلى أن تنضم إلى هذه الدورة مجدداً. "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
msgid "All questions must be answered!"
msgstr "يجب الإجابة على كافة الأسئلة! "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_question.py:0
msgid ""
"All questions must have at least one correct answer and one incorrect answer: \n"
"%s\n"
msgstr ""
"يجب أن يكون لكافة الأسئلة إجابة واحدة صحيحة على الأقل وإجابة واحدة غير صحيحة: \n"
"%s\n"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
msgid "All the courses you attend will appear here. <br/>"
msgstr "ستظهر كافة الدورات التي تحضرها هنا. <br/>"

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_5_furn2
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_5_furn2
msgid "All you need to know about furniture creation."
msgstr "كل ما تحتاج لمعرفته عن صناعة الأثاث. "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__allow_comment
#: model:ir.model.fields,help:website_slides.field_slide_slide__channel_allow_comment
msgid ""
"Allow Attendees to like and comment your content and to submit reviews on "
"your course."
msgstr ""
"السماح للحاضرين بالإعجاب والتعليق على محتواك وإرسال التقييمات في دورتك. "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_resource_downloadable
msgid "Allow Download"
msgstr "السماح بالتحميل "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__is_preview
msgid "Allow Preview"
msgstr "السماح بالمعاينة"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_form_add
msgid "Allow Rating"
msgstr "السماح بالتقييم"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Allow Reviews"
msgstr "السماح بالتقييمات "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__allow_comment
msgid "Allow rating on Course"
msgstr "السماح بالتقييم في الدورة"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__slide_resource_downloadable
msgid "Allow the user to download the content of the slide."
msgstr "السماح للمستخدم بتحميل محتوى الشريحة. "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__channel_allow_comment
msgid "Allows comment"
msgstr "السماح بالتعليق"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "Already Requested"
msgstr "تم الطلب بالفعل "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "Already member"
msgstr "عضو بالفعل "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_quiz_finish_dialog/slide_quiz_finish_dialog.js:0
msgid "Amazing!"
msgstr "رائع! "

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_2_0_question_1_2
msgid "And also bananas"
msgstr "والموز أيضاً "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__text_value
#: model:ir.model.fields,field_description:website_slides.field_slide_question__answer_ids
msgid "Answer"
msgstr "الإجابة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Answers"
msgstr "الإجابات"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_gamification_challenge__challenge_category
msgid "Appears in"
msgstr "يظهر في"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_slide_archive.js:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Archive"
msgstr "الأرشيف "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_slide_archive.js:0
msgid "Archive Content"
msgstr "أرشفة المحتوى "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Archived"
msgstr "مؤرشف"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_slide_archive.js:0
#: code:addons/website_slides/static/src/xml/slide_management.xml:0
msgid "Are you sure you want to archive this content?"
msgstr "هل أنت متأكد من أنك ترغب في أرشفة هذا المحتوى؟ "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_category_delete.js:0
#: code:addons/website_slides/static/src/xml/slide_management.xml:0
msgid "Are you sure you want to delete this category?"
msgstr "هل أنت متأكد من أنك ترغب في حذف هذه الفئة؟ "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
msgid "Are you sure you want to delete this question \"<strong>%s</strong>\"?"
msgstr "هل أنت متأكد من أنك ترغب في حذف هذا السؤال: \"<strong>%s</strong>\"؟ "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.js:0
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_category__article
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__article
msgid "Article"
msgstr "مقال"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__nbr_article
msgid "Articles"
msgstr "المقالات"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_attachment_count
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_attachment_count
msgid "Attachment Count"
msgstr "عدد المرفقات"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__attachment_ids
msgid "Attachments"
msgstr "المرفقات "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_question__attempts_avg
msgid "Attempts Avg"
msgstr "متوسط المحاولات "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_question__attempts_count
msgid "Attempts Count"
msgstr "عدد المحاولات "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_partner_view_form
msgid "Attendee"
msgstr "الحاضر"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__member_status
msgid "Attendee Status"
msgstr "حالة الحاضر"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_channel_partner_action
#: model:ir.actions.act_window,name:website_slides.slide_channel_partner_action_report
#: model:ir.actions.act_window,name:website_slides.slide_slide_partner_action_from_slide
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__partner_ids
#: model:ir.ui.menu,name:website_slides.website_slides_menu_report_attendees
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_graph
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_pivot
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_partner_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_partner_view_tree
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Attendees"
msgstr "الحاضرين "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "Attendees of %s"
msgstr "حاضري %s"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__enroll_group_ids
msgid "Auto Enroll Groups"
msgstr "مجموعات التسجيل التلقائي "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_avg
msgid "Average Rating"
msgstr "متوسط التقييم "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_tree_report
msgid "Average Review"
msgstr "متوسط التقييم "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "BUILDING BLOCKS DROPPED HERE WILL BE SHOWN ACROSS ALL LESSONS"
msgstr "سيتم عرض الكتل الإنشائية التي يتم إفلاتها هنا في كافة الدروس "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/course_tag_add_dialog/course_tag_add_dialog.xml:0
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
#: code:addons/website_slides/static/src/js/slides_category_add.js:0
msgid "Back"
msgstr "العودة"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid "Back to course"
msgstr "العودة إلى الدورة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_default_background_image_url
msgid "Background image URL"
msgstr "رابط URL لصورة الخلفية "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_home_user_achievements_small
msgid "Badges"
msgstr "الشارات"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_level_basic
msgid "Basic"
msgstr "الأساسي "

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_5_furn2
msgid "Basics of Furniture Creation"
msgstr "مبادئ صناعة الأثاث "

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_0_gard_0
msgid "Basics of Gardening"
msgstr "مبادئ العناية بالحدائق "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_unsubscribe_dialog/slide_unsubscribe_dialog.xml:0
msgid "Be notified when a new content is added."
msgstr "كن على علم عندما تتم إضافة محتوى جديد. "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__body_has_template_value
msgid "Body content is the same as the template"
msgstr "محتوى المتن هو نفس القالب "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__can_comment
msgid "Can Comment"
msgstr "يمكنه التعليق"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__can_edit_body
msgid "Can Edit Body"
msgstr "يمكن تحرير النص "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__can_self_mark_completed
msgid "Can Mark Completed"
msgstr "يمكن تحديده كمكتمل "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__can_self_mark_uncompleted
msgid "Can Mark Uncompleted"
msgstr "يمكن تحديده كغير مكتمل "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__can_publish
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__can_publish
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__can_publish
msgid "Can Publish"
msgstr "بإمكانه النشر "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__can_review
msgid "Can Review"
msgstr "يمكن المراجعة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__can_upload
msgid "Can Upload"
msgstr "يمكنه الرفع "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__can_vote
msgid "Can Vote"
msgstr "يمكن التصويت "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_sidebar.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_sidebar_done_button
msgid "Can not be marked as done"
msgstr "لا يمكن تعيينه كتم الانتهاء منه "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_sidebar.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_sidebar_done_button
msgid "Can not be marked as not done"
msgstr "لا يمكن تعيينه كغير منتهٍ"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
msgid "Cancel"
msgstr "إلغاء"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_role_carpenter
msgid "Carpenter"
msgstr "نجّار "

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
msgid "Catchy Headline"
msgstr "عنوان جذاب"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__slide_category_ids
msgid "Categories"
msgstr "الفئات"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_category
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__slide_category
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Category"
msgstr "الفئة "

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_other_1
msgid "Certification"
msgstr "شهادة"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_config_settings__module_website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "Certifications"
msgstr "الشهادات"

#. module: website_slides
#: model:gamification.badge,name:website_slides.badge_data_certification
#: model:gamification.goal.definition,name:website_slides.badge_data_certification_goal
msgid "Certified Knowledge"
msgstr "معرفة مُعتَمدة "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "Change video privacy settings"
msgstr "تغيير إعدادات خصوصية الفيديو "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__channel_id
msgid "Channel"
msgstr "القناة"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_channel_partner
msgid "Channel / Partners (Members)"
msgstr "القناة / الشركاء (الأعضاء) "

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_channel_invite
msgid "Channel Invitation Wizard"
msgstr "معالج دعوة القناة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__share_channel_template_id
msgid "Channel Share Template"
msgstr "قالب مشاركة القناة "

#. module: website_slides
#: model:mail.template,name:website_slides.mail_template_channel_shared
msgid "Channel Shared"
msgstr "القناة المُشارَكة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__channel_type
msgid "Channel type"
msgstr "نوع القناة "

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_channel_tag_group
msgid "Channel/Course Groups"
msgstr "مجموعات القناة / الدورة "

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_channel_tag
msgid "Channel/Course Tag"
msgstr "علامة تصنيف القناة / الدورة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__channel_ids
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Channels"
msgstr "القنوات"

#. module: website_slides
#: model:slide.tag,name:website_slides.slide_tag_demo_cheatsheet
msgid "CheatSheet"
msgstr "ورقة المعلومات المرجعية "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_quiz_finish_dialog/slide_quiz_finish_dialog.xml:0
msgid "Check Profile"
msgstr "تحقق من الملف التعريفي "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
msgid "Check answers"
msgstr "تحقق من الإجابات "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
msgid "Check your answers"
msgstr "تحقق من إجاباتك"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid "Choose a <b>File</b> on your computer."
msgstr "اختر <b>ملفاً</b> في حاسوبك. "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.js:0
msgid "Choose a PDF"
msgstr "تحديد ملف PDF "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_form_add
msgid "Choose a layout"
msgstr "اختر مخططاً "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.js:0
msgid "Choose an Image"
msgstr "اختر صورة "

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_3_furn0
msgid "Choose your wood!"
msgstr "اختر نوع الخشب الذي تفضله! "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "Clear filters"
msgstr "إزالة عوامل التصفية "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid "Click <b>Save</b> to create it."
msgstr "اضغط على <b>حفظ</b> لإنشائها. "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.mail_notification_channel_invite
msgid "Click here to get started"
msgstr "اضغط هنا للبدء "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.mail_notification_channel_invite
msgid "Click here to start the course"
msgstr "انقر هنا لتبدأ الدورة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Click on \"New\" in the top-right corner to write your first course."
msgstr "انقر على \"جديد\" في الزاوية العلوية إلى اليسار لكتابة دورتك الأولى. "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid ""
"Click on the \"Edit\" button in the top corner of the screen to edit your "
"slide content."
msgstr "اضغط على زر \"تحرير\" في الزاوية العليا للشاشة لتحرير محتوى شرائحك. "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid "Click on the <b>Save</b> button to create your first course."
msgstr "اضغط على زر <b>حفظ</b> لإنشاء دورتك الأولى. "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid "Click on your <b>Course</b> to go back to the table of content."
msgstr "انقر على <b>دورتك</b> للعودة إلى جدول المحتويات. "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_quiz_finish_dialog/slide_quiz_finish_dialog.xml:0
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/slide_share_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_modal_header
msgid "Close"
msgstr "إغلاق"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
msgid "Color"
msgstr "اللون"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__color
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__color
msgid "Color Index"
msgstr "مؤشر اللون "

#. module: website_slides
#: model:slide.tag,name:website_slides.slide_tag_demo_colorful
msgid "Colorful"
msgstr "ملون "

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.rating_rating_action_slide_channel
msgid "Come back later to check the feedbacks given by your Attendees."
msgstr "عد مجدداً لاحقاً لتفقد الملاحظات التي أبداها حاضروك. "

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_question_action_report
msgid "Come back later to oversee how well your Attendees are doing."
msgstr "عد مجدداً لاحقاً لمراقبة أداء حاضريك. "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__comment
msgid "Comment"
msgstr "تعليق"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Commenting is not enabled on this course."
msgstr "التعليقات معطلة في هذه الدورة. "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Comments"
msgstr "التعليقات"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid ""
"Common tasks for a computer scientist is asking the right questions and "
"answering questions. In this course, you'll study those topics with "
"activities about mathematics, science and logic."
msgstr ""
"من المهام المعتادة لعالِم الحاسوب هي سؤال الأسئلة المناسبة والإجابة على "
"الأسئلة. سوف تتعلم في هذه الدورة عن تلك المواضيع عن طريق الأنشطة المتعلقة "
"بالرياضيات والعلوم والمنطق. "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_form_add
msgid ""
"Common tasks for a computer scientist is asking the right questions and "
"answering questions..."
msgstr ""
"من المهام المعتادة لعالِم الحاسوب هي طرح الأسئلة المناسبة والإجابة على "
"الأسئلة... "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Communication"
msgstr "التواصل "

#. module: website_slides
#: model:gamification.badge,name:website_slides.badge_data_karma
#: model:gamification.goal.definition,name:website_slides.badge_data_karma_goal
msgid "Community hero"
msgstr "بطل المجتمع "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_partner__slide_channel_company_count
#: model:ir.model.fields,field_description:website_slides.field_res_users__slide_channel_company_count
msgid "Company Course Count"
msgstr "عدد دورات الشركة "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_3_0
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_3_0
msgid "Comparing Hardness of Wood Species"
msgstr "مقارنة صلابة أنواع الخشب "

#. module: website_slides
#: model:gamification.challenge,name:website_slides.badge_data_course_challenge
#: model_terms:gamification.badge,description:website_slides.badge_data_course
msgid "Complete a course"
msgstr "إكمال دورة "

#. module: website_slides
#: model:gamification.challenge,name:website_slides.badge_data_profile_challenge
#: model_terms:gamification.badge,description:website_slides.badge_data_profile
msgid "Complete your profile"
msgstr "أكمل ملفك التعريفي "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__completed
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_partner_view_search
msgid "Completed"
msgstr "مكتملة "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "Completed Course"
msgstr "الدورة المكتملة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_partner__slide_channel_completed_ids
#: model:ir.model.fields,field_description:website_slides.field_res_users__slide_channel_completed_ids
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
msgid "Completed Courses"
msgstr "الدورات المكتملة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__completion
msgid "Completion"
msgstr "الإكمال "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__completed_template_id
msgid "Completion Notification"
msgstr "إشعار الانتهاء "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "Completion Time"
msgstr "وقت الإكمال "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
msgid "Compose Email"
msgstr "إنشاء رسالة جديدة "

#. module: website_slides
#: model:ir.model,name:website_slides.model_res_config_settings
msgid "Config Settings"
msgstr "تهيئة الإعدادات "

#. module: website_slides
#: model:ir.ui.menu,name:website_slides.website_slides_menu_configuration
msgid "Configuration"
msgstr "التهيئة "

#. module: website_slides
#: model:mail.template,subject:website_slides.mail_template_channel_completed
msgid "Congratulations! You completed {{ object.channel_id.name }}"
msgstr "تهانينا! لقد أكملت {{ object.channel_id.name }}"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid ""
"Congratulations! Your first lesson is available. Let's see the options "
"available here. The tag \"<b>New</b>\" indicates that this lesson was "
"created less than 7 days ago."
msgstr ""
"تهانينا! بات أول درس لك متاحاً الآن. فلنلقِ نظرة على الخيارات المتاحة هنا.  "
"تشير العلامة \"<b>جديد</b>\" إلى أنه قد تم إنشاء هذا الدرس قبل أقل من 7 أيام"
" مضت. "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_home_user_profile_small
msgid "Congratulations, you have reached the last rank!"
msgstr "تهانينا، لقد وصلت إلى المرتبة الأخيرة! "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid ""
"Congratulations, you've created your first course.<br/>Click on the title of"
" this content to see it in fullscreen mode."
msgstr ""
"تهانينا، لقد قمت بإنشاء دورتك الأولى.<br/>اضغط على عنوان هذا المحتوى لتتمكن "
"من رؤيته في وضع ملء الشاشة. "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid ""
"Congratulations, your course has been created, but there isn't any content "
"yet. First, let's add a <b>Section</b> to give your course a structure."
msgstr ""
"تهانينا، لقد تم إنشاء دورتك، ولكن لا يوجد أي محتوى بعد. أولاً، فلنقم بإضافة "
"<b>جزء</b> لإضفاء هيكل لدورتك. "

#. module: website_slides
#: model:ir.model,name:website_slides.model_res_partner
msgid "Contact"
msgstr "جهة الاتصال"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_0_gard_0
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_1_gard1
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_2_gard2
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_3_furn0
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_4_furn1
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_5_furn2
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_6_furn3
msgid "Contact Responsible"
msgstr "التواصل مع الشخص المسؤول "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
msgid "Contact the responsible to enroll."
msgstr "تواصل مع الشخص المسؤول عن التسجيل. "

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid "Contact us"
msgstr "تواصل معنا"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__slide_content_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_question__slide_id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__slide_id
#: model_terms:ir.ui.view,arch_db:website_slides.slide_embed_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_embed_view_tree
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_partner_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Content"
msgstr "المحتوى"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.xml:0
msgid "Content Preview"
msgstr "معاينة المحتوى"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_question
msgid "Content Quiz Question"
msgstr "سؤال الاختبار القصير الخاص بالمحتوى "

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.action_slide_tag
#: model:ir.ui.menu,name:website_slides.website_slides_menu_config_content_tags
msgid "Content Tags"
msgstr "علامات تصنيف المحتوى "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Content Title"
msgstr "عنوان المحتوى "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Content Type"
msgstr "نوع المحتوى"

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_slide_action
msgid ""
"Content are the lessons that compose a course\n"
"                    <br>and can be of different types (presentations, documents, videos, ...)."
msgstr ""
"المحتوى هو عبارة عن الدروس التي تُكوّن الدورة\n"
"                    <br>ولها أنواع مختلفة (العروض التقديمية، المستندات، مقاطع الفيديو، ...). "

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_slide_action
#: model:ir.actions.act_window,name:website_slides.slide_slide_action_report
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__body
#: model:ir.ui.menu,name:website_slides.website_slides_menu_courses_content
#: model:ir.ui.menu,name:website_slides.website_slides_menu_report_contents
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_tree_report
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_tree
msgid "Contents"
msgstr "المحتويات"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
msgid "Continue"
msgstr "استمرار "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/slide_share_dialog.js:0
msgid "Copied"
msgstr "تم النسخ "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/slide_share_dialog.js:0
msgid "Copy Embed Code"
msgstr "نسخ الكود المضمن "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/slide_share_dialog.js:0
msgid "Copy Link"
msgstr "نسخ الرابط "

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_1_4_question_0_0
#: model:slide.answer,comment:website_slides.slide_slide_demo_2_0_question_0_1
#: model:slide.answer,comment:website_slides.slide_slide_demo_2_0_question_1_1
msgid "Correct!"
msgstr "صحيح! "

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_0_4_question_1_0
msgid "Correct! A shovel is the perfect tool to dig a hole."
msgstr "صحيح! المجرفة هي الأداة المناسبة لحفر حفرة. "

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_0_4_question_0_0
msgid "Correct! A strawberry is a fruit because it's the product of a tree."
msgstr "صحيح! الفراولة هي فاكهة لأنها تأتي من شجرة. "

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_1_4_question_1_0
msgid "Correct! Congratulations you have time to loose"
msgstr "هذا صحيح! تهانينا، لديك وقت لإمضائه "

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_5_3_question_0_0
msgid "Correct! You did it!"
msgstr "صحيح! لقد فعلتها! "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
msgid ""
"Could not find your video. Please check if your link is correct and if the "
"video can be accessed."
msgstr ""
"لم نتمكن من العثور على مقطع الفيديو الخاص بك. يرجى التحقق من سلامة الرابط "
"ومن أن مقطع الفيديو يمكن الوصول إليه. "

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_channel
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__channel_id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__channel_id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__channel_id
#: model_terms:ir.ui.view,arch_db:website_slides.gamification_karma_tracking_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_form_slides
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_search_slide_channel
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_tree_slide_channel
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Course"
msgstr "دورة"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__visibility__members
msgid "Course Attendees"
msgstr "حاضرو الدورة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_partner__slide_channel_count
#: model:ir.model.fields,field_description:website_slides.field_res_users__slide_channel_count
msgid "Course Count"
msgstr "عدد الدورات "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "Course Finished"
msgstr "الدورة منجزة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
msgid "Course Group Name"
msgstr "اسم مجموعة الدورة "

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_channel_tag_group_action
#: model:ir.ui.menu,name:website_slides.website_slides_menu_config_course_groups
msgid "Course Groups"
msgstr "مجموعات الدورات "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__channel_invite_url
msgid "Course Link"
msgstr "رابط الدورة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_search
msgid "Course Member"
msgstr "عضو الدورة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Course Name"
msgstr "اسم الدورة "

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.action_slide_channel_pages_list
msgid "Course Pages"
msgstr "صفحات الدورة "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/gamification_karma_tracking.py:0
msgid "Course Quiz"
msgstr "اختبار الدورة القصير "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "Course Ranked"
msgstr "تم تصنيف الدورة "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "Course Set Uncompleted"
msgstr "مجموعة الدورة غير مكتملة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_view_form
msgid "Course Tag"
msgstr "علامة تصنيف الدورة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
msgid "Course Tag Group"
msgstr "مجموعة علامة تصنيف الدورة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_tree
msgid "Course Tag Groups"
msgstr "مجموعات علامات تصنيف الدورات "

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_channel_tag_action
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_view_tree
msgid "Course Tags"
msgstr "علامات تصنيف الدورات "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_form_add
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Course Title"
msgstr "عنوان الدورة"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_tree
msgid "Course Type"
msgstr "نوع الدورة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__karma_gen_channel_finish
msgid "Course finished"
msgstr "انتهت الدورة "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "Course not published yet"
msgstr "لم يتم نشر الدورة بعد "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__karma_gen_channel_rank
msgid "Course ranked"
msgstr "تم تصنيف الدورة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__channel_type
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__channel_type
msgid "Course type"
msgstr "نوع الدورة "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
msgid "Course: %s"
msgstr "الدورة: %s"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/res_partner.py:0
#: code:addons/website_slides/models/website.py:0
#: model:ir.actions.act_window,name:website_slides.slide_channel_action_report
#: model:ir.ui.menu,name:website_slides.menu_slide_channel_pages
#: model:ir.ui.menu,name:website_slides.website_slides_menu_courses
#: model:ir.ui.menu,name:website_slides.website_slides_menu_courses_courses
#: model:ir.ui.menu,name:website_slides.website_slides_menu_report_courses
#: model:website.menu,name:website_slides.website_menu_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_graph
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_tree
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_tree_report
#: model_terms:ir.ui.view,arch_db:website_slides.slide_searchbar_input_snippet_options
msgid "Courses"
msgstr "الدورات"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.snippet_options
msgid "Courses Page"
msgstr "صفحة الدورات "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__prerequisite_of_channel_ids
msgid "Courses that have this course as prerequisite."
msgstr "الدورات التي تحتوي على هذه الدورة كمتطلب. "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__cover_properties
msgid "Cover Properties"
msgstr "خصائص الغلاف"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.xml:0
msgid "Create New Category \""
msgstr "إنشاء فئة جديدة * "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.xml:0
msgid "Create New Tag \""
msgstr "إنشاء علامة تصنيف جديدة \" "

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.action_slide_tag
msgid "Create a Content Tag"
msgstr "إنشاء علامة تصنيف للمحتوى "

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_channel_tag_group_action
msgid "Create a Course Group"
msgstr "إنشاء مجموعة للدورة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "Create a community and let Attendees answer each others' questions."
msgstr "أنشئ مجتمعاً وأتح لحاضريك الإجابة على أسئلة بعضهم البعض. "

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_slide_action_report
msgid "Create new content for your eLearning"
msgstr "أنشئ محتوى جديد للتعلّم الإلكتروني لديك "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/course_tag_add_dialog/course_tag_add_dialog.xml:0
msgid "Create this tag \""
msgstr "إنشاء علامة التصنيف هذه \""

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/course_tag_add_dialog/course_tag_add_dialog.xml:0
msgid "Create this tag group\""
msgstr "إنشاء مجموعة علامات التصنيف هذه\""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_question__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_question__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__html_content
msgid "Custom HTML content for slides of category 'Article'."
msgstr "محتوى HTML مخصص للشرائح ذات الفئة 'مقال'. "

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_6_furn3
msgid "DIY Furniture"
msgstr "أثاث يمكنك صنعه بنفسك "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_searchbar_input_snippet_options
msgid "Date (new to old)"
msgstr "التاريخ (من الأحدث إلى الأقدم) "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_searchbar_input_snippet_options
msgid "Date (old to new)"
msgstr "التاريخ (من الأقدم إلى الأحدث) "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid "Default training image"
msgstr "صورة التدريب الافتراضية "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_gamification_challenge__challenge_category
msgid "Define the visibility of the challenge through menus"
msgstr "تحديد إمكانية ظهور التحدي عبر القوائم"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__enroll
#: model:ir.model.fields,help:website_slides.field_slide_channel_partner__channel_enroll
msgid "Defines how people can enroll to your Course."
msgstr "يوضح كيفية تسجيل الأفراد في دورتك. "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__promote_strategy
msgid "Defines the content that will be promoted on the course home page"
msgstr "يقوم بتحديد المحتوى الذي سيتم الترويج له في الصفحة الرئيسية للدورة "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__publish_template_id
msgid ""
"Defines the email your Attendees will receive each time you upload new "
"content."
msgstr ""
"يحدد البريد الإلكتروني الذي سيستلمه حاضروك في كل مرة تقوم فيها برفع محتوى "
"جديد. "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__completed_template_id
msgid ""
"Defines the email your Attendees will receive once they reach the end of "
"your course."
msgstr ""
"يحدد البريد الإلكتروني الذي سيستلمه حاضروك في كل مرة يصلون فيها إلى نهاية "
"الدورة. "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__visibility
#: model:ir.model.fields,help:website_slides.field_slide_channel_invite__channel_visibility
#: model:ir.model.fields,help:website_slides.field_slide_channel_partner__channel_visibility
msgid "Defines who can access your courses and their content."
msgstr "يحدد من بإمكانه الوصول إلى دوراتك ومحتواها. "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_category_delete.js:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "Delete"
msgstr "حذف"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_category_delete.js:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
msgid "Delete Category"
msgstr "حذف الفئة "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
msgid "Delete Question"
msgstr "حذف السؤال "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__description
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__description
#: model_terms:ir.ui.view,arch_db:website_slides.slide_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Description"
msgstr "الوصف"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__description_html
msgid "Detailed Description"
msgstr "الوصف المفصّل "

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_1_4_question_1
msgid "Did you read the whole article?"
msgstr "هل قرأت المقال بأكمله؟ "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_unsubscribe_dialog/slide_unsubscribe_dialog.xml:0
msgid "Discard"
msgstr "إهمال "

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid "Discover more"
msgstr "اكتشف المزيد "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_card
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Dislike"
msgstr "عدم الإعجاب"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__dislikes
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_card
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Dislikes"
msgstr "عدم الإعجاب"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Display"
msgstr "عرض"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_question__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_2_0_question_0
msgid "Do you make beams out of lemon trees?"
msgstr "هل يمكنك صنع عوارض خشبية من أشجار الليمون؟ "

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_2_0_question_1
msgid "Do you make lemons out of beams?"
msgstr "هل يمكنك صنع الليمون من العوارض الخشبية؟ "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_unsubscribe_dialog/slide_unsubscribe_dialog.xml:0
msgid "Do you really want to leave the course?"
msgstr "هل تريد حقًا مغادرة الدورة؟"

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_1_4_question_0
msgid "Do you think Harry Potted has a good name?"
msgstr "هل لدى Harry Potted اسم جيد برأيك؟ "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_install_module.js:0
msgid "Do you want to install \"%s\"?"
msgstr "هل ترغب في تثبيت \"%s\"؟ "

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_5_3_question_0
msgid "Do you want to reply correctly?"
msgstr "هل ترغب في الرد بشكل صحيح؟ "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_enroll_email.js:0
msgid "Do you want to request access to this course?"
msgstr "هل ترغب في طلب إذن للوصول لهذه الدورة؟ "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.js:0
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_category__document
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Document"
msgstr "المستند"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__doc
msgid "Document (Word, Google Doc, ...)"
msgstr "مستند (Word، Google Doc، ...) "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.js:0
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__document_google_url
msgid "Document Link"
msgstr "رابط المستند "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.js:0
msgid "Document Source"
msgstr "مصدر المستند "

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__channel_type__documentation
msgid "Documentation"
msgstr "التوثيق"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__nbr_document
#: model:slide.slide,name:website_slides.slide_category_demo_4_0
msgid "Documents"
msgstr "المستندات"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_other_2
msgid "Dog Friendly"
msgstr "صديق للكلاب "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
msgid "Don't have an account?"
msgstr "لا تملك حسابًا؟"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__completed
#: model_terms:ir.ui.view,arch_db:website_slides.slides_home_user_achievements_small
msgid "Done"
msgstr "منتهي "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_question__done_count
msgid "Done Count"
msgstr "عدد الأسئلة المنجزة "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
msgid "Done!"
msgstr "انتهيت! "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid "Download"
msgstr "تنزيل "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid "Download Content"
msgstr "تحميل المحتوى "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__download_url
msgid "Download URL"
msgstr "رابط URL للتنزيل "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_4_10
msgid "Drawing 1"
msgstr "الرسمة 1 "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_4_11
msgid "Drawing 2"
msgstr "الرسمة 2 "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__total_time
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__completion_time
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Duration"
msgstr "المدة"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Earn more Karma to leave a comment."
msgstr "احصل على المزيد من نقاط الكارما حتى تتمكن من كتابة التعليقات. "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "Edit"
msgstr "تحرير"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
msgid "Edit in backend"
msgstr "تحرير في الواجهة الخلفية "

#. module: website_slides
#: model:mail.template,name:website_slides.mail_template_slide_channel_enroll
msgid "Elearning: Add Attendees to Course"
msgstr "التعلم الإلكتروني: أضف الحاضرين إلى الدورة "

#. module: website_slides
#: model:mail.template,name:website_slides.mail_template_channel_completed
msgid "Elearning: Completed Course"
msgstr "التعلم الإلكتروني: تم إكمال الدورة "

#. module: website_slides
#: model:mail.template,name:website_slides.slide_template_shared
msgid "Elearning: Course Share"
msgstr "التعلم الإلكتروني: مشاركة الدورة "

#. module: website_slides
#: model:mail.template,name:website_slides.slide_template_published
msgid "Elearning: New Course Content Notification"
msgstr "التعلم الإلكتروني: إشعار محتوى الدورة الجديد "

#. module: website_slides
#: model:mail.template,name:website_slides.mail_template_slide_channel_invite
msgid "Elearning: Promotional Course Invitation"
msgstr "التعلم الإلكتروني: دعوة الدورة الترويجية "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__partner_email
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Email"
msgstr "البريد الإلكتروني"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__share_channel_template_id
msgid "Email template used when sharing a channel"
msgstr "قالب بريد إلكتروني ميُستخدم عند مشاركة قناة "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__share_slide_template_id
msgid "Email template used when sharing a slide"
msgstr "قالب البريد الإلكتروني المستخدَم عند مشاركة شريحة "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/email_sharing_input.xml:0
msgid "Email(s) sent."
msgstr "تم إرسال رسالة (رسائل) البريد الإلكتروني. "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__embed_code
msgid "Embed Code"
msgstr "Embed Code"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_embed_action
#: model_terms:ir.ui.view,arch_db:website_slides.slide_embed_view_tree
msgid "Embed Views"
msgstr "تضمين موقع إلكتروني خارجي "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
msgid "Embed code"
msgstr "تضمين الكود "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/slide_share_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_embed
msgid "Embed in another Website"
msgstr "التضمين في موقع إلكتروني آخر "

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_embed
msgid "Embedded Slides View Counter"
msgstr "عداد مشاهدات الشرائح المضمنة "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_quiz_finish_dialog/slide_quiz_finish_dialog.xml:0
msgid "End course"
msgstr "إنهاء الدورة "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_2
msgid "Energy Efficiency Facts"
msgstr "حقائق عن كفاءة الطاقة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.mail_notification_channel_invite
msgid "Enjoy this exclusive content!"
msgstr "استمتع بهذا المحتوى الحصري! "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "Enroll Attendees to %(course_name)s"
msgstr "تسجيل الحاضرين في %(course_name)s "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__enroll_msg
msgid "Enroll Message"
msgstr "رسالة التسجيل "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__enroll
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__channel_enroll
msgid "Enroll Policy"
msgstr "سياسة التسجيل "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__enroll_mode
msgid "Enroll partners"
msgstr "تسجيل الشركاء "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__channel_partner_ids
msgid "Enrolled Attendees Information"
msgstr "معلومات الحاضرين المسجلين "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__partner_ids
msgid "Enrolled partners in the course"
msgstr "الشركاء المسجلون في هذه الدورة "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid "Enter at least two possible <b>Answers</b>."
msgstr "أدخِل <b>إجابتين</b>محتملتين على الأقل. "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid "Enter your <b>Question</b>. Be clear and concise."
msgstr "أدخِل <b>سؤالك</b> بكل وضوح واختصار. "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_question__answers_validation_error
msgid "Error on Answers"
msgstr "خطأ في الإجابات "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.xml:0
msgid "Estimated Completion Time"
msgstr "وقت الإكمال المتوقع "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "Evaluate the knowledge of your Attendees and certify their skills."
msgstr "قم بتقييم معرفة حاضريك والتصديق على مهاراتهم. "

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__visibility__public
msgid "Everyone"
msgstr "الجميع"

#. module: website_slides
#: model:slide.tag,name:website_slides.slide_tag_demo_exercises
msgid "Exercises"
msgstr "التمارين "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid "Exit Fullscreen"
msgstr "مغادرة وضع ملء الشاشة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__embed_code_external
msgid "External Embed Code"
msgstr "كود مضمن خارجي "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__embed_ids
msgid "External Slide Embeds"
msgstr "تضمين المواقع الإلكترونية الخارجية "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__url
msgid "External URL"
msgstr "رابط URL خارجي "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_embed_view_tree
msgid "External Website"
msgstr "موقع إلكتروني خارجي "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_install_module.js:0
msgid "Failed to install \"%s\""
msgstr "فشل تثبيت \"%s\""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__promote_strategy
msgid "Featured Content"
msgstr "المحتوى المُبرز "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__binary_content
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide_resource__resource_type__file
msgid "File"
msgstr "الملف"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__file_name
msgid "File Name"
msgstr "اسم الملف"

#. module: website_slides
#. odoo-javascript
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.js:0
msgid "File is too big. File size cannot exceed 25MB"
msgstr "الملف كبير للغاية. يجب ألا يتجاوز حجم الملف 25 ميجابايت"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Filter &amp; order"
msgstr "التصفية والترتيب "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid "Finally you can click here to enjoy your content in fullscreen"
msgstr ""
"وأخيراً، بات بوسعك الاستمتاع بالمحتوى في وضع ملء الشاشة عن طريق الضغط هنا "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "Finish"
msgstr "إنهاء"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Finish Course"
msgstr "إنهاء الدورة "

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel_partner__member_status__completed
msgid "Finished"
msgstr "مُنتهي"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "First Try"
msgstr "المحاولة الأولى "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid ""
"First, create your lesson, then edit it with the website builder. You'll be "
"able to drop building blocks on your page and edit them."
msgstr ""
"أنشئ درسك أولاً ثم قم بتحريره باستخدام أداة بناء المواقع الإلكترونية. سوف "
"يكون بمقدورك سحب وإفلات الكتل البرمجية الإنشائية في صفحتك وتحريرها. "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid "First, let's add a <b>Document</b>. It has to be a .pdf file."
msgstr "أولاً، فلنقم بإضافة <b>مستند</b>. يجب أن يكون ملفاً بصيغة PDF. "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "First, upload the file on your Google Drive account."
msgstr "أولاً، قم برفع الملف في حسابك على Google Drive. "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "First, upload your videos on Vimeo and mark them as"
msgstr "أولاً قم برفع مقاطع الفيديو الخاصة بك على Vimeo وتحديدهم كـ"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "First, upload your videos on YouTube and mark them as"
msgstr "قم برفع مقاطع الفيديو الخاصة بك إلى اليوتيوب أولاً، ثم حددها كـ"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_follower_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_follower_ids
msgid "Followers"
msgstr "المتابعين"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_partner_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_partner_ids
msgid "Followers (Partners)"
msgstr "المتابعين (الشركاء) "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "أيقونة من Font awesome مثال: fa-tasks "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_4_0
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid "Foreword"
msgstr "المقدمة "

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_4_0
msgid "Foreword for this documentation: how to use it, main attention points"
msgstr "مقدمة هذه الوثيقة: كيفية الاستخدام والنقاط الأساسية الأكثر أهمية "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_config_settings__module_website_slides_forum
msgid "Forum"
msgstr "المنتدى"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Fourth Try & More"
msgstr "المحاولة الرابعة وأكثر "

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_5_2
msgid "From a piece of wood to a fully functional furniture, step by step."
msgstr "من مجرد قطعة خشب إلى أثاث عملي بالكامل، خطوة بخطوة. "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
#: model_terms:ir.actions.act_window,help:website_slides.slide_channel_partner_action
#: model_terms:ir.actions.act_window,help:website_slides.slide_channel_partner_action_report
#: model_terms:ir.actions.act_window,help:website_slides.slide_slide_partner_action_from_slide
msgid ""
"From here you'll be able to monitor attendees and to track their progress."
msgstr "من هنا، سيكون بوسعك مراقبة الحاضرين وتتبع تقدمهم. "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Fullscreen"
msgstr "ملء الشاشة "

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_role_furniture
msgid "Furniture Designer"
msgstr "مصمم أثاث "

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_4_furn1
msgid "Furniture Technical Specifications"
msgstr "المواصفات التقنية للأثاث "

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_4_12
msgid "GLork"
msgstr "GLork"

#. module: website_slides
#: model:ir.model,name:website_slides.model_gamification_challenge
msgid "Gamification Challenge"
msgstr "تحدي التلعيب "

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_role_gardener
msgid "Gardener"
msgstr "بستاني "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_0_0
msgid "Gardening: The Know-How"
msgstr "العناية بالحدائق: الدليل المعرفي "

#. module: website_slides
#: model:gamification.challenge,name:website_slides.badge_data_certification_challenge
#: model_terms:gamification.badge,description:website_slides.badge_data_certification
msgid "Get a certification"
msgstr "احصل على شهادة "

#. module: website_slides
#: model:gamification.badge,name:website_slides.badge_data_register
#: model:gamification.goal.definition,name:website_slides.badge_data_register_goal
msgid "Get started"
msgstr "ابدأ"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid "Give your course a helpful <b>Description</b>."
msgstr "اعطِ دورتك <b>وصفاً</b> مفيداً. "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid "Give your course an engaging <b>Title</b>."
msgstr "امنح دورتك <b>عنواناً</b> مثيراً للاهتمام. "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
msgid "Go through all its content to see a Course in this section. <br/>"
msgstr "قم بالاطلاع على المحتوى بأكمله لرؤية الدورة في هذا القسم. <br/> "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_config_settings__website_slide_google_app_key
#: model:ir.model.fields,field_description:website_slides.field_website__website_slide_google_app_key
msgid "Google Doc Key"
msgstr "مفتاح مستندات Google "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__video_source_type__google_drive
msgid "Google Drive"
msgstr "Google Drive "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "Google Drive API Key"
msgstr "مفتاح API لـ Google Drive  "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__google_drive_id
msgid "Google Drive ID of the external URL"
msgstr "مُعرّف Google Drive لرابط URL الخارجي "

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__google_drive_video
msgid "Google Drive Video"
msgstr "فيديو Google Drive "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/activity/activity_patch.xml:0
msgid "Grant Access"
msgstr "منح إذن الوصول "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_graph
msgid "Graph of Contents"
msgstr "المخطط البياني للمحتويات "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__group_id
msgid "Group"
msgstr "المجموعة"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_partner_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Group By"
msgstr "تجميع حسب"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__name
msgid "Group Name"
msgstr "اسم المجموعة"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__upload_group_ids
msgid "Group of users allowed to publish contents on a documentation course."
msgstr "مجموعة المستخدمين المسموح لهم بنشر المحتوى في دورة توثيقية. "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__group_sequence
msgid "Group sequence"
msgstr "تسلسل جماعي "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__html_content
msgid "HTML Content"
msgstr "محتوى HTML "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_5_1
msgid "Hand on!"
msgstr "بنفسك! "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__prerequisite_user_has_completed
msgid "Has Completed Prerequisite"
msgstr "يحتوي على متطلبات مكتملة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_search
msgid "Has Menu Entry"
msgstr "يحتوي على قيد القائمة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__has_message
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__has_message
msgid "Has Message"
msgstr "يحتوي على رسالة "

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_0_3
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_6
msgid "Here is How to get the Sweetest Strawberries you ever tasted!"
msgstr "إليك طريقة الحصوع على ألذ فراولة تتذوقها في حياتك! "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
msgid "Home"
msgstr "الرئيسية"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_0_1
msgid "Home Gardening"
msgstr "العناية بالحديقة المنزلية "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_5_2
msgid "How To Build a HIGH QUALITY Dining Table with LIMITED TOOLS"
msgstr "كيفية بناء طاولة عشاء ذات جودة عالية باستخدام أدوات محدودة "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "How do I add new content?"
msgstr "كيف أقوم بإضافة محتوى جديد؟ "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_0_3
msgid "How to Grow and Harvest The Best Strawberries | Basics"
msgstr "كيفية زراعة وحصد أفضل فراولة على الإطلاق | المبادئ "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_6
msgid ""
"How to Grow and Harvest The Best Strawberries | Gardening Tips and Tricks"
msgstr ""
"كيفية زراحة وحصد أفضل فراولة على الإطلاق | نصائح مفيدة للعناية بالحدائق "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "How to create a Lesson as an Article?"
msgstr "كيف أقوم بإضافة درس كمقال؟ "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_5_1
msgid "How to find quality wood"
msgstr "كيفية إيجاد خشب ذو جودة عالية "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_3
msgid "How to plant a potted.list"
msgstr "كيفية زراعة شجرة في أصيص "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "How to upload your PowerPoint Presentations or Word Documents?"
msgstr "كيف تقوم برفع عرضك التقديمي على PowerPoint أو مستند Word؟ "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "How to upload your videos?"
msgstr "كيف ترفع مقاطع الفيديو؟ "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "How to use Google Drive?"
msgstr "كيف أستخدم Google Drive؟ "

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_2_2
msgid "How to wall decorating by tree planting in hanging plastic bottles."
msgstr ""
"كيفية تزيين الجدران عن طريق زراعة النباتات في القوارير البلاستيكية المعلقة. "

#. module: website_slides
#: model:slide.tag,name:website_slides.slide_tag_demo_howto
msgid "HowTo"
msgstr "كيفية عمل "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__id
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__id
#: model:ir.model.fields,field_description:website_slides.field_slide_question__id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__id
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__id
msgid "ID"
msgstr "المُعرف"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_exception_icon
msgid "Icon"
msgstr "الأيقونة"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "الأيقونة للإشارة إلى النشاط المستثنى. "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_needaction
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_needaction
msgid "If checked, new messages require your attention."
msgstr "إذا كان محددًا، فهناك رسائل جديدة عليك رؤيتها. "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_has_error
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_has_sms_error
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_has_error
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "إذا كان محددًا، فقد حدث خطأ في تسليم بعض الرسائل."

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_4_furn1
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_4_furn1
msgid ""
"If you are looking for technical specifications, have a look at this "
"documentation."
msgstr "إذا كنت تبحث عن مواصفات تقنية، ألقِ نظرة على هذه الوثيقة. "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid ""
"If you want to be sure that attendees have understood and memorized the "
"content, you can add a Quiz on the lesson. Click on <b>Add Quiz</b>."
msgstr ""
"إذا كنت تريد التأكد من أن الحاضرين قد تمكنوا من فهم وحفظ المحتوى، يمكنك "
"إضافة اختبار قصير للدرس. اضغط على <b>إضافة اختبار قصير</b>. "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid ""
"If you want to use other types of files, you may want to use an external "
"source (Google Drive) instead."
msgstr ""
"إذا أردت استخدام أنواع ملفات أخرى، قد ترغب في استخدام مصدر خارجي (Google "
"Drive). "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.js:0
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__image_1920
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image_1920
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_category__infographic
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__image
msgid "Image"
msgstr "صورة"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__image_1024
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image_1024
msgid "Image 1024"
msgstr "صورة 1024"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__image_128
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image_128
msgid "Image 128"
msgstr "صورة 128"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__image_256
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image_256
msgid "Image 256"
msgstr "صورة 256"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__image_512
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image_512
msgid "Image 512"
msgstr "صورة 512"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image_binary_content
msgid "Image Content"
msgstr "محتوى الصورة "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.js:0
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image_google_url
msgid "Image Link"
msgstr "رابط الصورة "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.js:0
msgid "Image Source"
msgstr "مصدر الصورة "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid ""
"Impossible to send emails. Select a \"Channel Share Template\" for courses "
"%(course_names)s first"
msgstr ""
"لا يمكن إرسال رسائل البريد الإلكتروني. قم أولاً بتحديد \"قالب مشاركة "
"القناة\" للدورات %(course_names)s "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
msgid ""
"Impossible to send emails. Select a \"Share Template\" for courses "
"%(course_names)s first"
msgstr ""
"لا يمكن إرسال رسائل البريد الإلكتروني. قم أولاً بتحديد \"قالب المشاركة\" "
"للدورات %(course_names)s "

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_1_4_question_0_1
#: model:slide.answer,comment:website_slides.slide_slide_demo_2_0_question_0_0
#: model:slide.answer,comment:website_slides.slide_slide_demo_2_0_question_1_0
msgid "Incorrect!"
msgstr "غير صحيح! "

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_0_4_question_0_1
msgid "Incorrect! A strawberry is not a vegetable."
msgstr "غير صحيح! الفراولة ليست من الخضار. "

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_0_4_question_0_2
msgid "Incorrect! A table is a piece of furniture."
msgstr "غير صحيح! الطاولة هي قطعة أثاث. "

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_0_4_question_1_1
msgid "Incorrect! Good luck digging a hole with a spoon..."
msgstr "غير صحيح! حظاً موفقاً في حفر حفرة بملعقة... "

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_1_4_question_1_2
msgid "Incorrect! Seriously?"
msgstr "غير صحيح! بجدية؟ "

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_5_3_question_0_1
msgid "Incorrect! You better think twice..."
msgstr "غير صحيح! فكّر ملياً... "

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_1_4_question_1_1
msgid "Incorrect! You really should read it."
msgstr "غير صحيح! ربما عليك قراءة السؤال مجدداً. "

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_2_0_question_1_2
msgid "Incorrect! of course not ..."
msgstr "غير صحيح! بالطبع لا... "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__nbr_infographic
msgid "Infographics"
msgstr "مخططات المعلومات البيانية "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_install_module.xml:0
msgid "Install"
msgstr "تثبيت"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog_select.xml:0
msgid "Install the"
msgstr "تثبيت "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_install_module.js:0
msgid "Installing \"%s\"..."
msgstr "جاري تثبيت \"%s\"... "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_1_0
msgid "Interesting Facts"
msgstr "حقائق مثيرة للاهتمام "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_1
msgid "Interesting List Facts"
msgstr "حقائق مثيرة للاهتمام عن القائمة "

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_0_1
msgid "Interesting information about home gardening. Keep it close!"
msgstr ""
"معلومات مثيرة للاهتمام عن العناية بالحدائق المنزلية. أبقها قريبة منك! "

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_level_intermediate
msgid "Intermediate"
msgstr "متوسط "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
msgid ""
"Internal server error, please try again later or contact administrator.\n"
"Here is the error message: %s"
msgstr ""
"خطأ داخلي في الخادم، الرجاء المحاولة مرة أخرى لاحقًا أو التواصل مع المدير.\n"
"رسالة الخطأ: %s"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.js:0
msgid "Invalid file type. Please select pdf or image file"
msgstr "نوع الملف غير صالح. الرجاء اختيار ملف بصيغة pdf أو صورة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__invitation_link
msgid "Invitation Link"
msgstr "رابط الدعوة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Invite"
msgstr "دعوة"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "Invite Attendees to %(course_name)s"
msgstr "دعوة الحاضرين إلى %(course_name)s "

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel_partner__member_status__invited
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_search
msgid "Invite Sent"
msgstr "تم إرسال الدعوة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__user_has_completed_category
msgid "Is Category Completed"
msgstr "الفئة مكتملة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__is_mail_template_editor
msgid "Is Editor"
msgstr "محرر "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__is_member
msgid "Is Enrolled Attendee"
msgstr "حاضر مسجل "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_is_follower
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_is_follower
msgid "Is Follower"
msgstr "متابع"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__is_member_invited
msgid "Is Invited Attendee"
msgstr "حاضر تمت دعوته "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__user_has_completed
msgid "Is Member"
msgstr "عضو "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__is_new_slide
msgid "Is New Slide"
msgstr "شريحة جديدة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__is_published
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__channel_published
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__is_published
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__is_published
msgid "Is Published"
msgstr "تم نشره "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__is_category
msgid "Is a category"
msgstr "فئة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__is_correct
msgid "Is correct answer"
msgstr "الإجابة الصحيحة "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__is_member
msgid "Is the attendee actively enrolled."
msgstr "الحاضر مستجل بفعالية. "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__is_member_invited
msgid "Is the invitation for this attendee pending."
msgstr "الدعوة لهذا الحاضر قيد الانتظار. "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "It should look similar to"
msgstr "من المفترض أن يكون شبيهاً بـ"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_3
msgid ""
"Jim and Todd plant a potted tree for a customer of Knecht's Nurseries and "
"Landscaping. Narrated by Leif Knecht, owner."
msgstr ""
"يزرع Jim وTodd شجرة في أصيص لعميل في مشتل وخدمة تنسيق الحدائق Knecht's. بقلم"
" Leif Knecht، المالك. "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
msgid "Join & Submit"
msgstr "الانضمام والإرسال "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_course_join.xml:0
msgid "Join the Course"
msgstr "الانضمام للدورة "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
msgid "Join the course to take the quiz and verify your answers!"
msgstr ""
"قم بالانضمام للدورة حتى تتمكن من القيام بالاختبار القصير وتأكيد إجاباتك! "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_join.js:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
#: model_terms:ir.ui.view,arch_db:website_slides.join_course_link
msgid "Join this Course"
msgstr "المشاركة في الدورة "

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel_partner__member_status__joined
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_search
msgid "Joined"
msgstr "تم الانضمام "

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_2
msgid "Just some basics Energy Efficiency Facts."
msgstr "بعض الحقائق الأساسية عن كفاءة الطاقة. "

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_1
msgid "Just some basics Interesting Tree Facts."
msgstr "بعض الحقائق الأساسية المثيرة للاهتمام عن الأشجار. "

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_0
msgid "Just some basics Tree Infographic."
msgstr "بعض مخططات المعلومات البيانية الأساسية عن الأشجار. "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Karma"
msgstr "نقاط الكارما"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__karma_slide_comment
msgid "Karma needed to add a comment on a slide of this course"
msgstr ""
"نقاط الكارما المطلوبة حتى تتمكن من كتابة تعليق على إحدى شرائح هذه الدورة "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__karma_review
msgid "Karma needed to add a review on the course"
msgstr "نقاط الكارما المطلوبة حتى تتمكن من كتابة تقييم لهذه الدورة "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__karma_slide_vote
msgid "Karma needed to like/dislike a slide of this course."
msgstr ""
"نقاط الكارما المطلوبة حتى تتمكن من إبداء الإعجاب / عدم الإعجاب لشريحة في هذه"
" الدورة. "

#. module: website_slides
#: model:gamification.badge,name:website_slides.badge_data_profile
#: model:gamification.goal.definition,name:website_slides.badge_data_profile_goal
msgid "Know yourself"
msgstr "اعرف نفسك "

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_3_furn0
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_3_furn0
msgid ""
"Knowing which kind of wood to use depending on your application is important. In this course you\n"
"will learn the basics of wood characteristics."
msgstr ""
"من المهم معرفة أي أنواع الخشب عليك استخدامه بناء على طرق تطبيقك. سوف تتعلم في هذه الدورة\n"
"مبادئ خصائص الخشب. "

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_2_3
msgid ""
"Knowing wood characteristics is a requirement in order to know which kind of"
" wood to use in a given situation."
msgstr ""
"من الضروري معرفة خصائص الخشب حتى تتمكن من معرفة أي الأنواع عليك استخدامه في "
"المواقف المختلفة. "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__lang
msgid "Language"
msgstr "اللغة"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Last Action On"
msgstr "آخر إجراء في "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Last Invitation"
msgstr "آخر دعوة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__last_invitation_date
msgid "Last Invitation Date"
msgstr "تاريخ آخر دعوة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__slide_last_update
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "Last Update"
msgstr "آخر تحديث"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_question__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_question__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__promote_strategy__latest
msgid "Latest Created"
msgstr "الشرائح التي تم إنشاؤها مؤخراً "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.toggle_latest_achievements
msgid "Latest achievements"
msgstr "أحدث الإنجازات "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.snippet_options
#: model_terms:ir.ui.view,arch_db:website_slides.toggle_leaderboard
msgid "Leaderboard"
msgstr "لوحة الصدارة "

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_1_gard1
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_1_gard1
msgid ""
"Learn how to take care of your favorite trees. Learn when to plant, how to "
"manage potted trees, ..."
msgstr ""
"تعلم كيفية الاعتناء بأشجارك المفضلة. اعرف متى عليك أن تزرع وكيفية التعامل مع"
" الأشجار المزروعة في أصيص، ..."

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_0_gard_0
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_0_gard_0
msgid "Learn the basics of gardening!"
msgstr "تعلم مبادئ العناية بالحدائق! "

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_5_1
msgid "Learn to identify quality wood in order to create solid furnitures."
msgstr "تعلم كيفية التعرف على الخشب عالي الجودة حتى تتمكن من بناء أثاث متين. "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_unsubscribe_dialog/slide_unsubscribe_dialog.js:0
#: code:addons/website_slides/static/src/js/public/components/slide_unsubscribe_dialog/slide_unsubscribe_dialog.xml:0
msgid "Leave the course"
msgstr "مغادرة الدورة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Lesson"
msgstr "الدروس "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Lesson Nav"
msgstr "التنقل بين الدروس "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid "Lessons"
msgstr "الدروس "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_quiz_finish_dialog/slide_quiz_finish_dialog.js:0
msgid "Level up!"
msgstr "الارتقاء للمستوى التالي! "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_card
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Like"
msgstr "إعجاب "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__likes
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_card
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Likes"
msgstr "الإعجابات"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__link
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide_resource__resource_type__url
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Link"
msgstr "الرابط"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__document_google_url
msgid ""
"Link of the document (we currently only support Google Drive as source)"
msgstr "رابط المستند (ندعم حالياً Google Drive فقط كمصدر) "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__image_google_url
msgid "Link of the image (we currently only support Google Drive as source)"
msgstr "رابط الصورة (ندعم حالياً Google Drive فقط كمصدر) "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__video_url
msgid ""
"Link of the video (we support YouTube, Google Drive and Vimeo as sources)"
msgstr "رابط الفيديو (ندعم YouTube، Google Drive، وVimeo كمصادر) "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_0
msgid "List Infographic"
msgstr "قائمة مخطط معلومات بياني "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_2_2
msgid "List planting in hanging bottles on wall"
msgstr "زراعة الأشجار في قوارير معلقة على الحائط "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.xml:0
msgid "Loading content..."
msgstr "جاري تحميل المحتوى "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
msgid "Log in"
msgstr "تسجيل الدخول"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__template_id
msgid "Mail Template"
msgstr "قالب البريد الإلكتروني "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_config_settings__module_mass_mailing_slides
msgid "Mailing"
msgstr "البريد "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_2_0
msgid "Main Trees Categories"
msgstr "فئات الأشجار الأساسية "

#. module: website_slides
#: model:res.groups,name:website_slides.group_website_slides_manager
msgid "Manager"
msgstr "المدير"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Mark Done"
msgstr "التعيين كمكتمل "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Mark To Do"
msgstr "التعيين كيجب القيام به "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_sidebar.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_sidebar_done_button
msgid "Mark as done"
msgstr "التعيين كمنتهي "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_sidebar.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_sidebar_done_button
msgid "Mark as not done"
msgstr "التعيين كغير منتهٍ "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid "Mark the correct answer by checking the <b>correct</b> mark."
msgstr "قم بتحديد الإجابة الصحيحة عن طريق تحديد العلامة <b>الصحيحة</b>.  "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "Members"
msgstr "الأعضاء"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Members Views"
msgstr "مشاهدات الأعضاء "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__enroll_group_ids
msgid ""
"Members of those groups are automatically added as members of the channel."
msgstr "يتم إضافة أعضاء هذه المجموعات تلقائياً كأعضاء للقناة. "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_tree
msgid "Menu Entry"
msgstr "قيد القائمة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_has_error
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_has_error
msgid "Message Delivery error"
msgstr "خطأ في تسليم الرسائل"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__enroll_msg
msgid "Message explaining the enroll process"
msgstr "رسالة توضح عملية التسجيل "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_ids
msgid "Messages"
msgstr "الرسائل"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_1_1
msgid "Methods"
msgstr "الطرق "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_0_2
msgid "Mighty Carrots"
msgstr "جزر رائع "

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_2_1
msgid ""
"Mighty forest just don't appear in a few weeks. Learn how time made our "
"forests mighty and mysterious."
msgstr ""
"لا تظهر الغابات الرائعة خلال أسابيع قليلة. تعلّم كيف تمكنا من جعل غاباتنا "
"رائعة وساحرة مع الوقت. "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.xml:0
msgid "Minutes"
msgstr "الدقائق"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
msgid "Missing \"Tag Group\" for creating a new \"Tag\"."
msgstr "لم يتم العثور على \"مجموعة علامة التصنيف\" لإنشاء \"علامة تصنيف\" جديدة. "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
msgid "Mobile sub-nav"
msgstr "التنقل الفرعي في الهاتف المحمول "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "More info"
msgstr "المزيد من المعلومات "

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__promote_strategy__most_viewed
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_documentation
msgid "Most Viewed"
msgstr "الأكثر عرضًا"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__promote_strategy__most_voted
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Most Voted"
msgstr "الأكثر تصويتًا"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Most popular courses"
msgstr "الدورات الأكثر شعبية "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "الموعد النهائي لنشاطاتي "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "My Content"
msgstr "محتواي "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "My Courses"
msgstr "دوراتي "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "My courses"
msgstr "دوراتي "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__name
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__name
msgid "Name"
msgstr "الاسم"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
msgid "Nav"
msgstr "التنقل "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
msgid "Need help? Review related content:"
msgstr "أتحتاج إلى المساعدة؟ قم بعرض المحتوى ذي الصلة: "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/views/slide_channel_partner_list/slide_channel_partner_list_view.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
msgid "New"
msgstr "جديد"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__publish_template_id
msgid "New Content Notification"
msgstr "إشعار المحتوى الجديد "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.snippet_options
msgid "New Content Ribbon"
msgstr "شريطة المحتوى الجديد "

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_channel_action_add
msgid "New Course"
msgstr "دورة جديدة "

#. module: website_slides
#: model:mail.template,subject:website_slides.slide_template_published
msgid ""
"New {{ object.slide_category }} published on {{ object.channel_id.name }}"
msgstr ""
"تم نشر {{ object.slide_category }} جديد في {{ object.channel_id.name }} "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Newest"
msgstr "الأحدث"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Newest courses"
msgstr "أحدث الدورات "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_quiz_finish_dialog/slide_quiz_finish_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Next"
msgstr "التالي"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "الفعالية التالية في تقويم الأنشطة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "الموعد النهائي للنشاط التالي"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_summary
msgid "Next Activity Summary"
msgstr "ملخص النشاط التالي"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_type_id
msgid "Next Activity Type"
msgstr "نوع النشاط التالي"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__next_slide_id
msgid "Next Lesson"
msgstr "الدرس التالي "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_home_user_profile_small
msgid "Next rank:"
msgstr "التصنيف التالي: "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
#: model:slide.answer,text_value:website_slides.slide_slide_demo_1_4_question_0_1
#: model:slide.answer,text_value:website_slides.slide_slide_demo_1_4_question_1_1
#: model:slide.answer,text_value:website_slides.slide_slide_demo_2_0_question_0_1
#: model:slide.answer,text_value:website_slides.slide_slide_demo_2_0_question_1_1
#: model:slide.answer,text_value:website_slides.slide_slide_demo_5_3_question_0_1
msgid "No"
msgstr "لا"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "No Attendee has completed this course yet!"
msgstr "لم يكمل أي من الحاضرين هذه الدورة بعد! "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "No Attendees Yet!"
msgstr "لا يوجد أي حاضرين بعد! "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "No Course created yet."
msgstr "لم يتم إنشاء دورة بعد "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "No Notification"
msgstr "لا يوجد إشعار "

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_question_action_report
msgid "No Quiz data yet!"
msgstr "لا توجد بيانات للاختبار القصير بعد! "

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.rating_rating_action_slide_channel
msgid "No Reviews yet!"
msgstr "لا توجد أي تقييمات بعد! "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
msgid "No completed courses yet!"
msgstr "لم تكمل أي دورة بعد! "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "No content was found using your search"
msgstr "لم يتم العثور على أي محتوى باستخدام بحثك "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "No course was found matching your search"
msgstr "لم يتم العثور على أي دورة تطابق بحثك "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "No course was found matching your search."
msgstr "لم يتم العثور على أي دورة تطابق بحثك "

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_slide_action_report
msgid "No data yet!"
msgstr "لا توجد أي بيانات بعد! "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.toggle_leaderboard
msgid "No leaderboard currently :("
msgstr "لا توجد لوحة صدارة في الوقت الحالي :( "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_placeholder
msgid "No lessons are available yet."
msgstr "لا توجد دروس متاحة بعد. "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
msgid "No ongoing courses yet!"
msgstr "لا توجد دورات جارية بعد! "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_documentation
msgid "No presentation available."
msgstr "ليس هناك عرض تقديمي متاح. "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "No results found for '"
msgstr "لم يتم العثور على نتائج لـ’"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__promote_strategy__none
msgid "None"
msgstr "لا شيء"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_pages_kanban_view
msgid "Not Published"
msgstr "غير منشور "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
msgid "Not enough karma to comment"
msgstr "لا تملك نقاط كارما كافية لكتابة تعليق "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "Not enough karma to review"
msgstr "لا تملك نقاط كارما كافية لكتابة تقييم "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_unsubscribe_dialog/slide_unsubscribe_dialog.js:0
msgid "Notifications"
msgstr "الإشعارات "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_needaction_counter
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_needaction_counter
msgid "Number of Actions"
msgstr "عدد الإجراءات"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__nbr_article
msgid "Number of Articles"
msgstr "عدد المقالات "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__total_slides
msgid "Number of Contents"
msgstr "عدد المحتويات "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__nbr_document
msgid "Number of Documents"
msgstr "عدد المستندات"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__nbr_infographic
msgid "Number of Images"
msgstr "عدد الصور "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__nbr_quiz
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__nbr_quiz
msgid "Number of Quizs"
msgstr "عدد الاختبارات القصيرة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__nbr_video
msgid "Number of Videos"
msgstr "عدد مقاطع الفيديو"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__comments_count
msgid "Number of comments"
msgstr "عدد التعليقات "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_has_error_counter
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_has_error_counter
msgid "Number of errors"
msgstr "عدد الأخطاء "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_needaction_counter
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "عدد الرسائل التي تتطلب اتخاذ إجراء"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_has_error_counter
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "عدد الرسائل الحادث بها خطأ في التسليم"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__questions_count
msgid "Numbers of Questions"
msgstr "عدد الأسئلة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.mail_notification_channel_invite
msgid "Odoo"
msgstr "أودو"

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid "Odoo • Image and Text"
msgstr "أودو • صورة ونص"

#. module: website_slides
#: model:res.groups,name:website_slides.group_website_slides_officer
msgid "Officer"
msgstr "موظف"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "On Google Drive"
msgstr "على Google Drive "

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__enroll__invite
msgid "On Invitation"
msgstr "عن طريق الدعوة "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "On Vimeo"
msgstr "على Vimeo "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "On YouTube"
msgstr "على YouTube "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid "Once you're done, don't forget to <b>Publish</b> your course."
msgstr "بمجرد انتهائك، لا تنس <b>نشر</b> دورتك. "

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel_partner__member_status__ongoing
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_search
msgid "Ongoing"
msgstr "جاري"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
msgid "Ongoing Courses"
msgstr "الدورات الجارية "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "Only a single review can be posted per course."
msgstr "يمكن نشر مراجعة واحدة فقط لكل دورة. "

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__enroll__public
msgid "Open"
msgstr "فتح"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "Operation not supported"
msgstr "العملية غير مدعومة "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel_invite__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"لغة الترجمة الاختيارية (كود ISO) لاختيارها عند إرسال بريد إلكتروني. إذا لم "
"يتم تعيينها، سوف تُستخدم النسخة باللغة الإنجليزية. عادة ما يكون ذلك تمثيلاً "
"للعنصر النائب المسؤول عن التزويد باللغة المناسبة، مثال: {{ "
"object.partner_id.lang }}. "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Options"
msgstr "الخيارات"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__pdf
msgid "PDF"
msgstr "PDF"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__document_binary_content
msgid "PDF Content"
msgstr "محتوى بصيغة PDF "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "Paid Courses"
msgstr "الدورات المدفوعة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__partner_id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__partner_id
msgid "Partner"
msgstr "الشريك"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__partner_has_new_content
msgid "Partner Has New Content"
msgstr "لدى الشريك محتوى جديد "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_pivot
msgid "Pivot"
msgstr "محور"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/email_sharing_input.xml:0
#: code:addons/website_slides/static/src/xml/slide_course_join.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid "Please"
msgstr "رجاءً "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
msgid ""
"Please <a href=\"/web/login?redirect=%(url)s\">login</a> or <a "
"href=\"/web/signup?redirect=%(url)s\">create an account</a> to vote for this"
" lesson"
msgstr ""
"يرجى <a href=\"/web/login?redirect=%(url)s\">تسجيل الدخول</a> أو <a "
"href=\"/web/signup?redirect=%(url)s\">إنشاء حساب</a> للتصويت لهذا الدرس "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
msgid ""
"Please <a href=\"/web/login?redirect=%(url)s\">login</a> to vote for this "
"lesson"
msgstr ""
"يرجى <a href=\"/web/login?redirect=%(url)s\">تسجيل الدخول</a> للتصويت لهذا "
"الدرس "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
msgid "Please enter a valid Vimeo video link"
msgstr "يرجى إدخال رابط فيديو Vimeo صالح "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
msgid "Please enter valid Google Drive Link"
msgstr "يرجى إدخال رابط Google Drive صالح "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/email_sharing_input.js:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid "Please enter valid email(s)"
msgstr "يرجى إدخال عنوان (عناوين) بريد إلكتروني صالحة "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_quiz_question_form.js:0
msgid "Please fill in the question"
msgstr "يرجى الإجابة على السؤال "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/wizard/slide_channel_invite.py:0
msgid "Please select at least one recipient."
msgstr "الرجاء اختيار مستلم واحد على الأقل. "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Points Rewards"
msgstr "مكافآت النقاط "

#. module: website_slides
#: model:gamification.badge,name:website_slides.badge_data_course
#: model:gamification.goal.definition,name:website_slides.badge_data_course_goal
msgid "Power User"
msgstr "مستخدم متمرس "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.mail_notification_channel_invite
msgid "Powered by"
msgstr "مشغل بواسطة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__prerequisite_of_channel_ids
msgid "Prerequisite Of"
msgstr "متطلبات "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__prerequisite_channel_ids
msgid "Prerequisite courses to complete before accessing this one."
msgstr "الدورات المطلوب إكمالها قبل الوصول إلى هذه الدورة. "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__prerequisite_channel_ids
msgid "Prerequisites"
msgstr "المتطلبات "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__slide_id
#: model:slide.slide,name:website_slides.slide_slide_demo_4_12
msgid "Presentation"
msgstr "عرض تقديمي "

#. module: website_slides
#: model:mail.message.subtype,description:website_slides.mt_channel_slide_published
#: model:mail.message.subtype,name:website_slides.mt_channel_slide_published
msgid "Presentation Published"
msgstr "تم نشر العرض التقديمي "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_card
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Preview"
msgstr "معاينة"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Previous"
msgstr "السابق"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "Private"
msgstr "خاص"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "Private Course"
msgstr "دورة خاصة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Progress"
msgstr "مدى التقدم "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_quiz_finish_dialog/slide_xp_progress_bar.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_card
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
#: model_terms:ir.ui.view,arch_db:website_slides.slide_main
msgid "Progress bar"
msgstr "شريط التقدم "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__promoted_slide_id
msgid "Promoted Slide"
msgstr "الشريحة المروَّج لها "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Public Views"
msgstr "المشاهدات العامة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_searchbar_input_snippet_options
msgid "Publication Date"
msgstr "تاريخ النشر "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__date_published
msgid "Publish Date"
msgstr "تاريخ النشر"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_pages_kanban_view
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_tree
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Published"
msgstr "تم النشر "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Published Contents"
msgstr "المحتوى المنشور "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Published Date"
msgstr "تاريخ النشر"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
#: code:addons/website_slides/models/slide_slide.py:0
msgid ""
"Publishing is restricted to the responsible of training courses or members "
"of the publisher group for documentation courses"
msgstr ""
"تقتصر إمكانية النشر على الشخص المسؤول عن الدورات التدريبية أو أعضاء مجموعة "
"الناشر للدورات التوثيقية "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__question_id
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Question"
msgstr "السؤال"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_question__question
#: model_terms:ir.ui.view,arch_db:website_slides.slide_question_view_form
msgid "Question Name"
msgstr "اسم السؤال"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__question_ids
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Questions"
msgstr "الأسئلة"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.js:0
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_category__quiz
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__quiz
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_other_0
#: model_terms:ir.ui.view,arch_db:website_slides.gamification_karma_tracking_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_question_view_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Quiz"
msgstr "الاختبار القصير "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
msgid "Quiz Completed"
msgstr "تم إكمال الاختبار القصير "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "Quiz Demo Data"
msgstr "بيانات الاختبار القصير التجريبية "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
msgid "Quiz Set Uncompleted"
msgstr "مجموعة الاختيارات القصيرة غير مكتملة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__quiz_attempts_count
msgid "Quiz attempts count"
msgstr "عدد محاولات الاختبار القصير "

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_question_action_report
#: model:ir.ui.menu,name:website_slides.website_slides_menu_report_quizzes
#: model_terms:ir.ui.view,arch_db:website_slides.slide_question_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_question_view_tree
#: model_terms:ir.ui.view,arch_db:website_slides.slide_question_view_tree_report
msgid "Quizzes"
msgstr "الاختبارات القصيرة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_avg_stars
msgid "Rating Average (Stars)"
msgstr "متوسط التقييم (النجوم) "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_avg_text
msgid "Rating Avg Text"
msgstr "متوسط نص التقييم "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "آخر ملاحظات التقييم"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_last_image
msgid "Rating Last Image"
msgstr "آخر صورة للتقييم"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_last_value
msgid "Rating Last Value"
msgstr "آخر قيمة للتقييم"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_percentage_satisfaction
msgid "Rating Satisfaction"
msgstr "رضا التقييم "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_last_text
msgid "Rating Text"
msgstr "نص التقييم "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_count
msgid "Rating count"
msgstr "عدد التقييمات"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "Rating of %s"
msgstr "تقييم %s"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__rating_ids
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_form_slides
msgid "Ratings"
msgstr "التقييمات "

#. module: website_slides
#: model:gamification.challenge,name:website_slides.badge_data_karma_challenge
#: model_terms:gamification.badge,description:website_slides.badge_data_karma
msgid "Reach 2000 XP"
msgstr "الوصول إلى 2000 XP"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Reach new heights"
msgstr "الوصول إلى قمم جديدة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__partner_ids
msgid "Recipients"
msgstr "المستلمين"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/activity/activity_patch.xml:0
msgid "Refuse Access"
msgstr "رفض الوصول "

#. module: website_slides
#: model:gamification.challenge,name:website_slides.badge_data_register_challenge
#: model_terms:gamification.badge,description:website_slides.badge_data_register
msgid "Register to the platform"
msgstr "التسجيل في المنصة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_documentation
msgid "Related"
msgstr "ذو صلة "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
msgid "Remove the answer comment"
msgstr "إزالة تعليق الإجابة "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
msgid "Remove this answer"
msgstr "إزالة هذه الإجابة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__render_model
msgid "Rendering Model"
msgstr "نموذج التكوين "

#. module: website_slides
#: model:ir.ui.menu,name:website_slides.website_slides_menu_report
msgid "Reporting"
msgstr "إعداد التقارير "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_enroll_email.js:0
msgid "Request Access."
msgstr "طلب إذن الوصول "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_enroll_email.js:0
msgid "Request sent!"
msgstr "تم إرسال الطلب "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
msgid "Reset"
msgstr "إعادة الضبط "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide_resource.py:0
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__data
msgid "Resource"
msgstr "المورد"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide_resource.py:0
msgid ""
"Resource %(resource_name)s is a link and should not contain a data file"
msgstr ""
"المَورد %(resource_name)s هو عبارة عن رابط ويجب ألا يحتوي على ملف بيانات "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__resource_type
msgid "Resource Type"
msgstr "نوع المورد"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__user_id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__channel_user_id
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Responsible"
msgstr "المسؤول "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_user_id
msgid "Responsible User"
msgstr "المستخدم المسؤول"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
msgid "Responsible already contacted."
msgstr "تم التواصل مع الشخص المسؤول بالفعل. "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__website_id
#: model:ir.model.fields,help:website_slides.field_slide_channel_partner__channel_website_id
#: model:ir.model.fields,help:website_slides.field_slide_slide__website_id
msgid "Restrict to a specific website."
msgstr "التقييد لموقع إلكتروني محدد "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_source_types.xml:0
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__source_type__external
msgid "Retrieve from Google Drive"
msgstr "الإحضار من Google Drive "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_install_module.xml:0
msgid "Retry"
msgstr "إعادة المحاولة"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.profile_access_denied
msgid "Return to the course."
msgstr "العودة إلى الدورة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Review Course"
msgstr "مراجعة الدورة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_tree_slide_channel
msgid "Review Date"
msgstr "تاريخ التقييم "

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.rating_rating_action_slide_channel
#: model:ir.ui.menu,name:website_slides.website_slides_menu_report_reviews
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_graph_slide_channel
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Reviews"
msgstr "التقييمات "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__quiz_fourth_attempt_reward
msgid "Reward: every attempt after the third try"
msgstr "مكافأة: كل محاولة بعد المحاولة الثالثة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__quiz_first_attempt_reward
msgid "Reward: first attempt"
msgstr "مكافأة: المحاولة الأولى "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__quiz_second_attempt_reward
msgid "Reward: second attempt"
msgstr "مكافأة: المحاولة الثانية "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__quiz_third_attempt_reward
msgid "Reward: third attempt"
msgstr "مكافأة: المحاولة الثالثة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Rewards"
msgstr "المكافآت"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__is_seo_optimized
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__is_seo_optimized
msgid "SEO optimized"
msgstr "تم تحسين محركات البحث"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_has_sms_error
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_has_sms_error
msgid "SMS Delivery error"
msgstr "خطأ في تسليم الرسائل النصية القصيرة "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__embed_code_external
msgid ""
"Same as 'Embed Code' but used to embed the content on an external website."
msgstr ""
"شبيه بـ 'تضمين الكود' ولكن يُستخدم لتضمين المحتوى في موقع إلكتروني خارجي. "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "Sample"
msgstr "عينة"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_unsubscribe_dialog/slide_unsubscribe_dialog.xml:0
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.xml:0
#: code:addons/website_slides/static/src/js/slides_category_add.js:0
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
msgid "Save"
msgstr "حفظ"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.xml:0
msgid "Save and Publish"
msgstr "حفظ ونشر "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "Save your presentations or documents as PDF files and upload them."
msgstr "احفظ عروضك التقديمية أو مستنداتك كملفات بصيغة PDF وقم برفعها. "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_tree_slide_channel
msgid "Score"
msgstr "الدرجة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Search"
msgstr "بحث"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Search Contents"
msgstr " البحث عن المحتوى "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Search courses"
msgstr "البحث عن الدورات "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Search in content"
msgstr "البحث في المحتوى "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Second Try"
msgstr "المحاولة الثانية "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.xml:0
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__category_id
msgid "Section"
msgstr "القسم"

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid "Section Subtitle"
msgstr "عنوان فرعي للقسم"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/category_add_dialog/category_add_dialog.xml:0
msgid "Section name"
msgstr "اسم القسم"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__access_token
msgid "Security Token"
msgstr "رمز الحماية"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/res_users.py:0
msgid "See our eLearning"
msgstr "ألقِ نظرة على التعلم الإلكتروني "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid "Select <b>Course</b> to create it and manage it."
msgstr "اختر <b>دورةً</b> لإنشائها وإدارتها. "

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__promote_strategy__specific
msgid "Select Manually"
msgstr "التحديد يدوياً "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.js:0
msgid "Select or create a category"
msgstr "قم بتحديد أو إنشاء فئة "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/course_tag_add_dialog/course_tag_add_dialog.js:0
msgid "Select or create a tag"
msgstr "قم باختيار أو إنشاء علامة تصنيف "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/course_tag_add_dialog/course_tag_add_dialog.js:0
msgid "Select or create a tag group"
msgstr "قم باختيار أو إنشاء مجموعة علامات تصنيف "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_select_tags.xml:0
msgid "Select or create tags"
msgstr "قم بتحديد أو إنشاء علامات التصنيف "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
msgid "Select the correct answer below:"
msgstr "اختر الإجابة الصحيحة أدناه: "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "Sell access to your courses on your website and track revenues."
msgstr ""
"قم ببيع صلاحيات الوصول إلى دوراتك على موقعك الإلكتروني وتمكن من تتبع "
"إيراداتك. "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_config_settings__module_website_sale_slides
msgid "Sell on eCommerce"
msgstr "البيع في موقع التجارة الإلكترونية "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
msgid "Send"
msgstr "إرسال"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/email_sharing_input.xml:0
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__send_email
msgid "Send Email"
msgstr "إرسال بريد إلكتروني"

#. module: website_slides
#: model:mail.template,description:website_slides.mail_template_channel_completed
msgid "Sent to attendees once they've completed the course"
msgstr "يتم إرسالها إلى الحاضرين بمجرد إكمالهم للدورة "

#. module: website_slides
#: model:mail.template,description:website_slides.slide_template_published
msgid "Sent to attendees when new course is published"
msgstr "يتم إرسالها إلى الحاضرين عندما يتم نشر دورة جديدة "

#. module: website_slides
#: model:mail.template,description:website_slides.mail_template_slide_channel_enroll
msgid "Sent to attendees when they are added to a course"
msgstr "يتم إرسالها إلى الحاضرين بمجرد أن تتم إضافتهم إلى دورة "

#. module: website_slides
#: model:mail.template,description:website_slides.mail_template_slide_channel_invite
msgid "Sent to potential attendees to check out the course."
msgstr "يتم إرسالها إلى الحاضرين المحتملين لإلقاء نظرة على الدورة. "

#. module: website_slides
#: model:mail.template,description:website_slides.slide_template_shared
msgid "Sent when attendees share the course by email"
msgstr ""
"يتم إرسالها عندما يقوم الحاضرون بمشاركة الدورة عن طريق البريد الإلكتروني "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__seo_name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__seo_name
msgid "Seo name"
msgstr "اسم محسنات محرك البحث "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__sequence
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__sequence
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__sequence
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__sequence
#: model:ir.model.fields,field_description:website_slides.field_slide_question__sequence
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__sequence
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__sequence
msgid "Sequence"
msgstr "تسلسل "

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.website_slides_action_settings
#: model:ir.ui.menu,name:website_slides.website_slides_menu_config_settings
msgid "Settings"
msgstr "الإعدادات"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid "Share"
msgstr "مشاركة"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "Share Channel"
msgstr "مشاركة القناة "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/slide_share_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_link
msgid "Share Link"
msgstr "مشاركة الرابط"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__share_slide_template_id
msgid "Share Template"
msgstr "مشاركة القالب "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_modal_header
msgid "Share This Content"
msgstr "مشاركة هذه المحتوى "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_share_url
msgid "Share URL"
msgstr "مشاركة رابط URL "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/slide_share_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid "Share by Email"
msgstr "المشاركة عن طريق البريد الإلكتروني "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/slide_share_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_social
msgid "Share on Facebook"
msgstr "المشاركة على فيسبوك"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/slide_share_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_social
msgid "Share on LinkedIn"
msgstr "المشاركة على LinkedIn"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/slide_share_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_social
msgid "Share on Pinterest"
msgstr "المشاركة على Pinterest "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/slide_share_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_modal_body
msgid "Share on Social Media"
msgstr "المشاركة على مواقع التواصل الاجتماعي "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/slide_share_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_social
msgid "Share on Whatsapp"
msgstr "المشاركة عن طريق Whatsapp "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/slide_share_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_social
msgid "Share on X"
msgstr "المشاركة على X "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/slide_share_dialog.xml:0
msgid "Share this Content"
msgstr "مشاركة هذا المحتوى "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/slide_share_dialog.xml:0
msgid "Share this Course"
msgstr "مشاركة هذه الدورة "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/email_sharing_input.xml:0
msgid "Sharing is caring!"
msgstr "المشاركة رائعة! "

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__sheet
msgid "Sheet (Excel, Google Sheet, ...)"
msgstr "ورقة (Excel، Google Sheet، ...) "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__description_short
msgid "Short Description"
msgstr "وصف قصير "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__visibility
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__channel_visibility
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__channel_visibility
msgid "Show Course To"
msgstr "إظهار الدورة لـ "

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_0_4
msgid "Show your newly mastered knowledge!"
msgstr "أظهِر للجميع معارفك الجديدة! "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
msgid "Sign Up!"
msgstr "سجل الآن!"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
msgid "Sign in and join the course to verify your answers!"
msgstr "قم بتسجيل الدخول والانضمام إلى الدورة لتأكيد إجاباتك! "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
msgid "Sign up"
msgstr "تسجيل"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__visibility__connected
msgid "Signed In"
msgstr "تم تسجيل الدخول "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid ""
"Skill up and have an impact! Your business career starts here.<br/>Time to "
"start a course."
msgstr ""
"ارتق بمهاراتك وأحدِث فرقاً! رحلة الأعمال الخاصة بك تبدأ هنا.<br/>حان الوقت "
"لبدء الدورة. "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__slide_id
msgid "Slide"
msgstr "انزلاق "

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_slide_partner
msgid "Slide / Partner decorated m2m"
msgstr "الشريحة / m2m المزين للشريك "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_icon_class
msgid "Slide Icon fa-class"
msgstr "أيقونة التمرير fa-class "

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_answer
msgid "Slide Question's Answer"
msgstr "إجابة سؤال الشريحة "

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_tag
msgid "Slide Tag"
msgstr "علامة تصنيف الشريحة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_type
msgid "Slide Type"
msgstr "نوع الشريحة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__slide_partner_ids
msgid "Slide User Data"
msgstr "بيانات مستخدم الشريحة "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_fullscreen.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid "Slide image"
msgstr "صورة الشريحة"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
msgid ""
"Slide with questions must be marked as done when submitting all good answers"
" "
msgstr ""
"يجب تحديد الشرائح التي بها أسئلة كمنتهية، عند إرسال كافة الإجابات الجيدة "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_fullscreen.xml:0
#: model:ir.model,name:website_slides.model_slide_slide
msgid "Slides"
msgstr "الشرائح"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__slides
msgid "Slides (PowerPoint, Google Slides, ...)"
msgstr "الشرائح (PowerPoint، Google Slides، ...) "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__slide_ids
msgid "Slides and categories"
msgstr "الشرائح والفئات "

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_6_furn3
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_6_furn3
msgid "So much amazing certification."
msgstr "العديد من الشهادات الرائعة. "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Sort by"
msgstr "الفرز حسب "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__source_type
msgid "Source Type"
msgstr "نوع المصدر "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "Start Course"
msgstr "ابدأ الدورة"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/slide_share_dialog.xml:0
msgid "Start at Page"
msgstr "البدء في الصفحة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "Start this Course"
msgstr "بدء هذه الدورة "

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid "Start with the customer – find out what they want and give it to them."
msgstr "ابدأ بالعميل - تعرّف على ما يريده العميل وامنحه إياه. "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Start your online course today!"
msgstr "ابدأ دورتك عبر الإنترنت اليوم! "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Status"
msgstr "الحالة"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"الأنشطة المعتمدة على الحالة\n"
"المتأخرة: تاريخ الاستحقاق مر\n"
"اليوم: تاريخ النشاط هو اليوم\n"
"المخطط: الأنشطة المستقبلية."

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__subject
msgid "Subject"
msgstr "الموضوع "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
msgid "Subject..."
msgstr "الموضوع..."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_unsubscribe_dialog/slide_unsubscribe_dialog.js:0
msgid "Subscribe"
msgstr "اشتراك"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__user_membership_id
msgid "Subscriber information"
msgstr "معلومات المشترك "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__user_membership_id
msgid "Subscriber information for the current logged in user"
msgstr "معلومات المشترك للمستخدم المسجل دخوله حالياً "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__partner_ids
msgid "Subscribers"
msgstr "المشتركين "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_partner_ids
msgid "Subscribers information"
msgstr "معلومات المشتركين "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__slide_type
msgid ""
"Subtype of the slide category, allows more precision on the actual file type"
" / source type."
msgstr ""
"النوع الفرعي لفئة الشريحة. يمنحك دقة أعلى في النوع الفعلي للملف / نوع "
"المصدر. "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
msgid "Succeed and gain karma"
msgstr "انجح واكتسب نقاط كارما "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/course_tag_add_dialog/course_tag_add_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_tag_form
msgid "Tag"
msgstr "علامة تصنيف "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/course_tag_add_dialog/course_tag_add_dialog.xml:0
msgid "Tag Group"
msgstr "مجموعة علامة التصنيف "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
msgid "Tag Name"
msgstr "اسم علامة التصنيف "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel_tag__color
msgid ""
"Tag color used in both backend and website. No color means no display in "
"kanban or front-end, to distinguish internal tags from public categorization"
" tags"
msgstr ""
"تُستخدَم ألوان علامات التصنيف في الواجهة الخلفية وفي الموقع الإلكتروني. لو "
"لم يكن هناك لون، هذا يعني أنه لن يكون هناك عرض في كانبان أو الواجهة "
"الأمامية، للتمييز بين علامات التصنيف الداخلية وعلامات تصنيف الفئة العامة "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.xml:0
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__tag_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__tag_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__tag_ids
#: model:slide.channel.tag.group,name:website_slides.slide_channel_tag_group_data_other
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_form_add
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_tag_tree
msgid "Tags"
msgstr "علامات التصنيف "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Tags..."
msgstr "علامات التصنيف... "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Take Quiz"
msgstr "أخذ الاختبار القصير "

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_1_gard1
msgid "Taking care of Trees"
msgstr "العناية بالأشجار "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_4_1
msgid "Technical Drawings"
msgstr "الرسومات التقنية "

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_4_10
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_4_11
msgid "Technical drawing"
msgstr "رسم تقني "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_5_2
msgid "Test Yourself"
msgstr "اختبر نفسك "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_0_4
msgid "Test your knowledge"
msgstr "اختبر معرفتك "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_5_3
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_5_3
msgid "Test your knowledge!"
msgstr "اختبر معرفتك! "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "Test your students with small Quizzes"
msgstr "اختبر طلابك عن طريق الاختبارات القصيرة "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid ""
"The <b>Duration</b> of the lesson is based on the number of pages of your "
"document. You can change this number if your attendees will need more time "
"to assimilate the content."
msgstr ""
"تعتمد <b>مدة</b> الدرس على عدد صفحات مستندك. بوسعك تغيير الرقم إذا كان "
"حاضريك بحاجة إلى المزيد من الوقت لاستيعاب المحتوى. "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid ""
"The <b>Title</b> of your lesson is autocompleted but you can change it if "
"you want.</br>A <b>Preview</b> of your file is available on the right side "
"of the screen."
msgstr ""
" يتم إكمال<b>عنوان</b> درسك تلقائياً ولكن بإمكانك تغييره إذا أردت.</br>تتوفر"
" <b>معاينة</b> لملفك على يسار الشاشة. "

#. module: website_slides
#: model:ir.model.constraint,message:website_slides.constraint_slide_channel_check_enroll
msgid ""
"The Enroll Policy should be set to 'On Invitation' when visibility is set to"
" 'Course Attendees'"
msgstr ""
"يجب أن تكون سياسة التسجيل \"عند الدعوة\" عندما تكون إمكانية رؤية الدورة "
"مقتصرة على \"حاضري الدورة\" "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid ""
"The Google Drive link can be obtained by using the 'share' button in the "
"Google interface."
msgstr ""
"يمكن الحصول على رابط Google Drive عن طريق استخدام زر 'مشاركة' في واجهة "
"Google "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid ""
"The Google Drive link to use here can be obtained by clicking the \"Share\" "
"button in the Google interface."
msgstr ""
"يمكن الحصول على رابط Google Drive لاستخدامه هنا عن طريق استخدام زر 'مشاركة' "
"في واجهة Google "

#. module: website_slides
#: model:ir.model.constraint,message:website_slides.constraint_slide_channel_partner_check_completion
msgid ""
"The completion of a channel is a percentage and should be between 0% and "
"100."
msgstr "الإكمال في القناة هو عبارة عن نسبة مئوية ويجب أن تكون بين 0% و100%. "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
msgid "The contact associated with this invitation does not seem to be valid."
msgstr "يبدو أن جهة الاتصال المرتبطة بهذه الدعوة غير صالحة. "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__is_preview
msgid ""
"The course is accessible by anyone : the users don't need to join the "
"channel to access the content of the course."
msgstr ""
"بإمكان أي شخص الوصول إلى هذه الدورة: لن يحتاج المشتركون للانضمام إلى القناة "
"للوصول إلى محتوى الدورة. "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__description_short
msgid "The description that is displayed on the course card"
msgstr "الوصف المعروض على بطاقة الدورة "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__description
msgid ""
"The description that is displayed on top of the course page, just below the "
"title"
msgstr "الوصف المعروض أعلى صفحة الدورة، تحت العنوان مباشرة "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__website_url
#: model:ir.model.fields,help:website_slides.field_slide_channel_tag_group__website_url
#: model:ir.model.fields,help:website_slides.field_slide_slide__website_url
msgid "The full URL to access the document through the website."
msgstr "رابطURL الكامل للوصول إلى المستند من خلال الموقع الإلكتروني. "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__can_self_mark_completed
msgid "The slide can be marked as completed even without opening it"
msgstr "يمكن تعيين الشريحة كمكتملة حتى دون فتحها "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__can_self_mark_uncompleted
msgid "The slide can be marked as not completed and the progression"
msgstr "يمكن تعيين الشريحة كغير مكتملة والتقدم "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid ""
"The video link to input here can be obtained by using the 'share' button in "
"the Vimeo interface."
msgstr ""
"يمكن الحصول على رابط الفيديو لادخاله هنا عن طريق استخدام زر 'مشاركة' في "
"واجهة Vimeo. "

#. module: website_slides
#: model:ir.model.constraint,message:website_slides.constraint_slide_slide_partner_check_vote
msgid "The vote must be 1, 0 or -1."
msgstr "يجب أن يكون التصويت 1، 0، أو -1. "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "Then, go into the file permissions and set it as \"Anyone with the link\"."
msgstr "ثم اذهب إلى أذونات الملف وقم بتعيينها إلى \"أي شخص لديه الرابط\". "

#. module: website_slides
#: model:slide.tag,name:website_slides.slide_tag_demo_theory
msgid "Theory"
msgstr "نظري "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "There are no comments for now."
msgstr "لا توجد تعليقات حالياً. "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid ""
"There are no comments for now. Earn more Karma to be the first to leave a "
"comment."
msgstr ""
"لا توجد تعليقات الآن. اكسب المزيد من نقاط الكارما لتكون أول من يترك تعليقاً."
" "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
msgid "There was an error validating this quiz."
msgstr "حدث خطأ أثناء تصديق هذا الاختبار القصير. "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__url
msgid "Third Party Website URL"
msgstr "رابط موقع طرف ثالث "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Third Try"
msgstr "المحاولة الثالثة "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_answer__comment
msgid "This comment will be displayed to the user if they select this answer"
msgstr "سيتم عرض هذا التعليق للمستخدم إذا قام بتحديد هذه الإجابة "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
msgid "This course does not exist."
msgstr "هذه الدورة غير موجودة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
msgid ""
"This course is not published. Attendees may not be able to access its "
"contents."
msgstr "هذه الدورة غير منشورة. قد لا يتمكن الحاضرون من الوصول إلى المحتوى. "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
msgid "This course is private."
msgstr "هذه الدورة خاصة."

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
msgid "This identification link does not seem to be valid."
msgstr "يبدو أن رابط التعريف هذا غير صالح. "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
msgid "This invitation link has an invalid hash."
msgstr "يحتوي رابط الدعوة هذا على تشفير غير صحيح. "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
msgid "This invitation link has expired."
msgstr "لقد انتهت صلاحية رابط الدعوة هذا. "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
msgid "This invitation link is not for this contact."
msgstr "رابط الدعوة هذا ليس لجهة الاتصال هذه. "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
msgid "This is the correct answer"
msgstr "هذه هي الإجابة الصحيحة "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
msgid "This is the correct answer, congratulations"
msgstr "هذه هي الإجابة الصحيحة، تهانينا "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_question.py:0
msgid ""
"This question must have at least one correct answer and one incorrect "
"answer."
msgstr ""
"يجب أن يكون للهذا السؤال إجابة واحدة صحيحة على الأقل وإجابة واحدة غير صحيحة."
" "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
msgid "This quiz is already done. Retaking it is not possible."
msgstr "لقد خضعت لهذا الاختبار بالفعل. لا يمكنك إعادة المحاولة مجدداً. "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
msgid "This slide can not be marked as completed."
msgstr "لا يمكن تعيين هذه الشريحة كمكتملة. "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
msgid "This slide can not be marked as uncompleted."
msgstr "لا يمكن تعيين هذه الشريحة كغير مكتملة. "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
msgid "This video already exists in this channel on the following content: %s"
msgstr "هذا الفيديو موجود بالفعل في هذه القناة في المحتوى التالي: %s "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid ""
"Through Google Drive, we support most common types of documents.\n"
"            Including regular documents (Google Doc, .docx), Sheets (Google Sheet, .xlsx), PowerPoints, ..."
msgstr ""
"من خلال Google Drive، ندعم معظم أنواع المستندات الشائعة،\n"
"            من ضمنها المستندات الدورية (Google Doc, .docx)، الأوراق (Google Sheet, .xlsx), PowerPoints، ... "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.xml:0
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__name
msgid "Title"
msgstr "العنوان"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Toggle navigation"
msgstr "تنقل التبديل "

#. module: website_slides
#: model:slide.tag,name:website_slides.slide_tag_demo_tools
msgid "Tools"
msgstr "الأدوات"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_5_0
msgid "Tools and Methods"
msgstr "الأدوات والطرق "

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_5_0
msgid "Tools you will need to complete this course."
msgstr "الأدوات التي سوف تحتاج إليها لإكمال هذه الدورة. "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_tree
msgid "Total"
msgstr "الإجمالي"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_tree_report
msgid "Total Attendees"
msgstr "إجمالي عدد الحاضرين "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_tree_report
msgid "Total Completed"
msgstr "إجمالي عدد من أكملوا الدورة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_tree_report
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_tree_report
msgid "Total Duration"
msgstr "المدة الكلية "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_tree_report
msgid "Total Questions"
msgstr "إجمالي الأسئلة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__total_slides
msgid "Total Slides"
msgstr "مجموع الشرائح "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_tree_report
msgid "Total Views"
msgstr "مجموع المشاهدات "

#. module: website_slides
#: model:ir.model,name:website_slides.model_gamification_karma_tracking
msgid "Track Karma Changes"
msgstr "تتبع تغيرات كارما "

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__channel_type__training
msgid "Training"
msgstr "التدريب"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_2_0
msgid "Trees"
msgstr "الأشجار "

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_2_gard2
msgid "Trees, Wood and Gardens"
msgstr "الأشجار، الخشب، والحدائق "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_quiz_finish_dialog/slide_quiz_finish_dialog.xml:0
msgid "Triumphant hero"
msgstr "بطل الأبطال "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Type"
msgstr "النوع"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "نوع النشاط المستثنى في السجل. "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__url
msgid "URL of the Google Drive file or URL of the YouTube video"
msgstr "رابط URL لملف Google Drive أو رابط URL لمقطع فيديو على YouTube "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/wizard/slide_channel_invite.py:0
msgid "Unable to post message, please configure the sender's email address."
msgstr "تعذّر نشر الرسالة، يرجى تهيئة البريد الإلكتروني الخاص بالمرسل.  "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Unarchive"
msgstr "إلغاء الأرشفة "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
msgid "Uncategorized"
msgstr "غير مصنف"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_5_0
msgid "Unforgettable Tools"
msgstr "أدوات لا تُنسى "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_embed.py:0
msgid "Unknown Website"
msgstr "موقع إلكتروني غير معروف "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_join.js:0
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
msgid "Unknown error"
msgstr "خطأ غير معروف"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_enroll_email.js:0
msgid "Unknown error, try again."
msgstr "خطأ غير معروف، حاول مجدداً. "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_card
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_card
msgid "Unpublished"
msgstr "غير منشور"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
msgid "Update"
msgstr "تحديث"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "Update all your Attendees at once through mass mailings."
msgstr ""
"تمكن من إرسال التحديثات لكافة حاضريك في آن واحد عن طريق المراسلات الجماعية. "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Upload Document"
msgstr "رفع المستند "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__upload_group_ids
msgid "Upload Groups"
msgstr "رفع المجموعات"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_source_types.xml:0
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__source_type__local_file
msgid "Upload from Device"
msgstr "الرفع من الجهاز "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__user_id
msgid "Uploaded by"
msgstr "تم الرفع بواسطة "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "Uploading document ..."
msgstr "جارٍ رفع المستند ..."

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.action_slide_tag
msgid "Use Content Tags to classify your Content."
msgstr "استخدم علامات تصنيف المحتوى لتصنيف محتواك. "

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_channel_tag_group_action
msgid "Use Course Groups to classify and organize your Courses."
msgstr "استخدم مجموعات الدورات لتصنيف وتنظيم دوراتك. "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
msgid "Use template"
msgstr "استخدم القالب "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__tag_ids
msgid "Used to categorize and filter displayed channels/courses"
msgstr "مُستخدَم لتصنيف وتصفية القنوات/الدورات المعروضة "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__color
msgid "Used to decorate kanban view"
msgstr "مُستخدَم لتزيين عرض كانبان "

#. module: website_slides
#: model:ir.model,name:website_slides.model_res_users
msgid "User"
msgstr "المستخدم"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__user_vote
msgid "User vote"
msgstr "تصويت المستخدم "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_question_view_form
msgid "Validation error"
msgstr "خطأ في التصديق "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.js:0
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_category__video
msgid "Video"
msgstr "الفيديو"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.js:0
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__video_url
msgid "Video Link"
msgstr "رابط الفيديو"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__video_source_type
msgid "Video Source"
msgstr "مصدر الفيديو "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__vimeo_id
msgid "Video Vimeo ID"
msgstr "مُعرّف فيديو Vimeo "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__youtube_id
msgid "Video YouTube ID"
msgstr "مُعرّف فيديو YouTube "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__nbr_video
msgid "Videos"
msgstr "مقاطع الفيديو"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "View"
msgstr "أداة العرض"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
#: model_terms:ir.ui.view,arch_db:website_slides.slides_home_user_achievements_small
#: model_terms:ir.ui.view,arch_db:website_slides.toggle_leaderboard
msgid "View all"
msgstr "عرض الكل"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "View course"
msgstr "عرض الدورات "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Views"
msgstr "أدوات العرض"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_card
msgid "Views •"
msgstr "المشاهدات •"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__video_source_type__vimeo
msgid "Vimeo"
msgstr "Vimeo"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__vimeo_video
msgid "Vimeo Video"
msgstr "فيديو Vimeo "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_published
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__website_published
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_published
msgid "Visible on current website"
msgstr "مرئي في الموقع الإلكتروني الحالي "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__total_views
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Visits"
msgstr "الزيارات"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__karma_slide_vote
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__vote
msgid "Vote"
msgstr "التصويت"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__total_votes
msgid "Votes"
msgstr "التصويتات"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
msgid "Votes and comments are disabled for this course"
msgstr "تم تعطيل التصويت والتعليقات لهذه الدورة"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Waiting for validation"
msgstr "بانتظار التصديق "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
msgid "Want to test and certify your students?"
msgstr "أترغب في اختبار طلابك ومنحهم الشهادات؟ "

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_3_1
msgid "Watching the master(s) at work"
msgstr "مشاهدة المحترف(ين) في العمل "

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_4
msgid ""
"We had a little chat with Harry Potted, sure he had interesting things to "
"say!"
msgstr ""
"لقد تحدثنا قليلاً مع Harry Potted، وقد أخبرنا بالعديد من الأشياء المثيرة "
"للاهتمام! "

#. module: website_slides
#: model:ir.model,name:website_slides.model_website
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__channel_website_id
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__website_name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_id
msgid "Website"
msgstr "الموقع الإلكتروني"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__gamification_challenge__challenge_category__slides
msgid "Website / Slides"
msgstr "الموقع الإلكتروني / الشرائح "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_message_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_message_ids
msgid "Website Messages"
msgstr "رسائل الموقع الإلكتروني "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_url
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__website_url
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_url
msgid "Website URL"
msgstr "رابط URL للموقع الإلكتروني "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__website_message_ids
#: model:ir.model.fields,help:website_slides.field_slide_slide__website_message_ids
msgid "Website communication history"
msgstr "سجل تواصل الموقع الإلكتروني "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_meta_description
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_meta_description
msgid "Website meta description"
msgstr "الوصف الدلالي في الموقع الإلكتروني "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_meta_keywords
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_meta_keywords
msgid "Website meta keywords"
msgstr "الكلمات الدلالية بالموقع الإلكتروني "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_meta_title
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_meta_title
msgid "Website meta title"
msgstr "العنوان الدلالي بالموقع الإلكتروني "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_meta_og_img
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_meta_og_img
msgid "Website opengraph image"
msgstr "صورة الرسم البياني المفتوح للموقع الإلكتروني "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid ""
"Welcome on your course's home page. It's still empty for now. Click on "
"\"<b>New</b>\" to write your first course."
msgstr ""
"مرحباً بك في الصفحة الرئيسية لدورتك. إنها لا تزال فارغة في الوقت الحالي. "
"اضغط على \"<b>جديد</b>\" لكتابة دورتك الأولى. "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "What does"
msgstr "ماذا يفعل "

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_0_4_question_0
msgid "What is a strawberry?"
msgstr "ما هي الفراولة؟ "

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_0_4_question_1
msgid "What is the best tool to dig a hole for your plants?"
msgstr "ما هي أفضل أداة يمكن استخدامها لحفر حفرة لنباتاتك؟ "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "What types of documents do we support?"
msgstr "ما هي أنواع المستندات التي ندعمها؟ "

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_1_4_question_1_2
msgid "What was the question again?"
msgstr "ما السؤال مرة أخرى؟ "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "When using local files, we only support PDF files."
msgstr "عند استخدام الملفات المحلية، ندعم فقط الملفات بصيغة PDF. "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel_invite__enroll_mode
msgid ""
"Whether invited partners will be added as enrolled. Otherwise, they will be "
"added as invited."
msgstr ""
"ما إذا كان الشركاء المدعوون ستتم إضافتهم كمسجلين أم لا. وإلا، فستتم إضافتهم "
"كمدعوين. "

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_4_1
msgid ""
"Which wood type is best for my solid wood furniture? That's the question we "
"help you answer in this video!"
msgstr ""
"أي أنواع الخشب يُعد الأفضل للأثاث الخشبي المتين؟ هذا هو السؤال الذي سوف نجيب"
" عليه في هذا الفيديو! "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid ""
"With Quizzes you can keep your students focused and motivated by answering "
"some questions and gaining some karma points"
msgstr ""
"بإمكانك إبقاء طلابك مهتمين ومتأهبين عن طريق الإجابة على بعض الأسئلة واكتساب "
"نقاط الكارما، باستخدام الاختبارات القصيرة "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_2_1
msgid "Wood"
msgstr "الخشب "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_3_1
msgid "Wood Bending With Steam Box"
msgstr "ثني الخشب باستخدام صندوق البخار "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_2_3
msgid "Wood Characteristics"
msgstr "خصائص الخشب "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_4_1
msgid "Wood Types"
msgstr "أنواع الخشب "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_3_0
msgid "Working with Wood"
msgstr "العمل باستخدام الخشب "

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid ""
"Write one or two paragraphs describing your product or services. <br>To be "
"successful your content needs to be useful to your readers."
msgstr ""
"اكتب فقرة أو فقرتين تصف فيهما منتجك أو خدماتك. <br>حتى تكون ناجحاً، يجب أن "
"يكون محتواك مفيداً لقرائك. "

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid ""
"Write one or two paragraphs describing your product, services or a specific "
"feature.<br> To be successful your content needs to be useful to your "
"readers."
msgstr ""
"اكتب فقرة أو فقرتين تصف فيهما منتجك أو خدماتك أو خاصية معينة.<br> حتى تكون "
"ناجحاً، يجب أن يكون محتواك مفيداً لقرائك. "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_quiz_finish_dialog/slide_quiz_finish_dialog.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
msgid "XP"
msgstr "الخبرة "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_enroll_email.js:0
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
#: model:slide.answer,text_value:website_slides.slide_slide_demo_1_4_question_0_0
#: model:slide.answer,text_value:website_slides.slide_slide_demo_1_4_question_1_0
#: model:slide.answer,text_value:website_slides.slide_slide_demo_2_0_question_0_0
#: model:slide.answer,text_value:website_slides.slide_slide_demo_2_0_question_1_0
#: model:slide.answer,text_value:website_slides.slide_slide_demo_5_3_question_0_0
msgid "Yes"
msgstr "نعم"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid ""
"You are not allowed to add members to this course. Please contact the course"
" responsible or an administrator."
msgstr ""
"لا يُسمح لك بإضافة الأعضاء إلى هذه الدورة. يرجى التواصل مع مسؤول الدورة أو "
"مع أحد المدراء. "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid ""
"You can add <b>comments</b> on answers. This will be visible with the "
"results if the user select this answer."
msgstr ""
"بإمكانك إضافة <b>تعليقات</b> على الإجابات. سوف يكون مرئياً مع النتائج إذا "
"اختار المُستخدِم هذه الإجابة. "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "You can add questions to this quiz in the 'Quiz' tab."
msgstr ""
"بإمكانك إضافة أسئلة إلى هذا الاختبار القصير في شريط 'الاختبار القصير'. "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid ""
"You can either upload a file from your computer or insert a Google Drive "
"link."
msgstr "بوسعك إمّا رفع ملف من حاسوبك أو إدخال رابط Google Drive. "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.js:0
msgid "You can not upload password protected file."
msgstr "لا يمكنك رفع ملف محمي بكلمة مرور."

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
msgid "You cannot add tags to this course."
msgstr "لا يمكنك إضافة علامات تصنيف لهذه الدورة. "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
msgid "You cannot mark a slide as completed if you are not among its members."
msgstr "لا يمكنك تحديد شريحة كمكتملة إذا لم تكن أحد الأعضاء. "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
msgid ""
"You cannot mark a slide as uncompleted if you are not among its members."
msgstr "لا يمكنك تحديد شريحة كغير مكتملة إذا لم تكن أحد الأعضاء. "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
msgid "You cannot mark a slide as viewed if you are not among its members."
msgstr "لا يمكنك تحديد شريحة كشريحة تم عرضها إذا لم تكن أحد الأعضاء. "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
msgid ""
"You cannot mark a slide quiz as completed if you are not among its members "
"or it is unpublished."
msgstr ""
"لا يمكنك تحديد الاختبار القصير لشريحة كمكتمل إذا لم تكن أحد الأعضاء أو إذا "
"كان غير منشور. "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
msgid ""
"You cannot mark a slide quiz as not completed if you are not among its "
"members or it is unpublished."
msgstr ""
"لا يمكنك تحديد الاختبار القصير لشريحة كغير مكتمل إذا لم تكن أحد الأعضاء أو "
"إذا كان غير منشور. "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
msgid "You cannot upload on this channel."
msgstr "لا يمكنك الرفع على هذه القناة. "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_quiz_finish_dialog/slide_quiz_finish_dialog.xml:0
msgid "You did it!"
msgstr "لقد نجحت! "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
msgid "You do not have permission to access this course."
msgstr "لا تملك صلاحية الوصول إلى هذه الدورة. "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
msgid "You don't have access to this lesson"
msgstr "لا تملك صلاحية الوصول إلى هذا الدرس "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
msgid "You don't have enough karma to vote"
msgstr "لا تملك نقاط كارما كافية للتصويت "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_quiz_finish_dialog/slide_quiz_finish_dialog.xml:0
msgid "You gained"
msgstr "لقد اكتسبت "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_join.js:0
msgid "You have already joined this channel"
msgstr "لقد انضممت إلى هذه القناة بالفعل "

#. module: website_slides
#: model:mail.template,subject:website_slides.mail_template_slide_channel_invite
msgid "You have been invited to check out {{ object.channel_id.name }}"
msgstr "لقد تمت دعوتك للتحقق من {{ object.channel_id.name }} "

#. module: website_slides
#: model:mail.template,subject:website_slides.mail_template_slide_channel_enroll
msgid "You have been invited to join {{ object.channel_id.name }}"
msgstr "لقد تمت دعوتك للانضمام إلى {{ object.channel_id.name }}"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
msgid "You have been invited to this course."
msgstr "لقد تمت تعوتك إلى هذه الدورة. "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "You have to sign in before"
msgstr "عليك تسجيل الدخول أولاً "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "You may now participate in our eLearning."
msgstr "يمكنك الآن المشاركة في التعلم الإلكتروني. "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
msgid "You must be logged to submit the quiz."
msgstr "عليك تسجيل الدخول أولاً حتى تتمكن من تسليم الاختبار القصير. "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
msgid "You must be member of this course to vote"
msgstr "يجب أن تكون عضوا في هذه الدورة حتى تتمكن من التصويت "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
msgid "You need to join this course to access \""
msgstr "عليك الانضمام إلى هذه الدورة للوصول إلى \""

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_0_2
msgid "You won't believe those facts about carrots."
msgstr "لن تصدق هذه الحقائق عن الجزر. "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "You're enrolled"
msgstr "لقد قمت بالتسجيل "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__video_source_type__youtube
msgid "YouTube"
msgstr "YouTube"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__youtube_video
msgid "YouTube Video"
msgstr "فيديو YouTube "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.mail_notification_channel_invite
msgid "Your"
msgstr " "

#. module: website_slides
#: model:slide.channel.tag.group,name:website_slides.slide_channel_tag_group_level
msgid "Your Level"
msgstr "مستواك "

#. module: website_slides
#: model:slide.channel.tag.group,name:website_slides.slide_channel_tag_group_role
msgid "Your Role"
msgstr "دورك "

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_channel_action_overview
#: model_terms:ir.actions.act_window,help:website_slides.slide_channel_action_report
msgid ""
"Your eLearning platform starts here!<br>\n"
"                    Upload content, set up rewards, manage attendees..."
msgstr ""
"منصة التعلّم الإلكتروني الخاصة بك تبدأ هنا!<br>\n"
"                    قم برفع المحتوى وتعيين المكافآت وإدارة الحاضرين... "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
msgid ""
"Your file could not be found on Google Drive, please check the link and/or "
"privacy settings"
msgstr ""
"لم نتمكن من العثور على ملفك على Google Drive. يرجى التحقق من الرابط و/أو "
"إعدادات الخصوصية "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid ""
"Your first section is created, now it's time to add lessons to your course. "
"Click on <b>Add Content</b> to upload a document, create an article or link "
"a video."
msgstr ""
"تم إنشاء أول جزئية، حان الوقت الآن لإضافة دروس لدوراتك. اضغط على <b>إضافة "
"محتوى</b> لرفع مستند أو كتابة مقال أو ربط مقطع فيديو. "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
msgid ""
"Your video could not be found on Vimeo, please check the link and/or privacy"
" settings"
msgstr ""
"تعذر إيجاد مقطع الفيديو الخاص بك على Vimeo. يرجى التحقق من الرابط و/أو "
"إعدادات الخصوصية "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
msgid ""
"Your video could not be found on YouTube, please check the link and/or "
"privacy settings"
msgstr ""
"تعذر إيجاد مقطع الفيديو الخاص بك على YouTube. يرجى التحقق من الرابط و/أو "
"إعدادات الخصوصية "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "a course"
msgstr "دورة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.achievement_card
msgid "achieved"
msgstr "مؤرشف "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "and join this Community"
msgstr "وانضم إلى هذا المجتمع "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "anyway"
msgstr "بأي حال "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog_select.xml:0
msgid "app."
msgstr ". "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
msgid "breadcrumb"
msgstr "التتبع "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/email_sharing_input.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid "by email."
msgstr "عن طريق البريد الإلكتروني. "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/email_sharing_input.xml:0
msgid "course"
msgstr "دورة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "courses"
msgstr "الدورات "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_course_join.xml:0
msgid "create an account"
msgstr "إنشاء حساب "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "direct access"
msgstr "الوصول المباشر "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_source_types.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "e.g \"https://drive.google.com/file/...\""
msgstr "مثال: \"https://drive.google.com/file/...\" "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_source_types.xml:0
msgid "e.g \"https://www.youtube.com/watch?v=ebBez6bcSEc\""
msgstr "مثال: \"https://www.youtube.com/watch?v=ebBez6bcSEc\" "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "e.g \"www.youtube.com/watch?v=ebBez6bcSEc\""
msgstr "مثال: \"www.youtube.com/watch?v=ebBez6bcSEc\" "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_tag_tree
msgid "e.g 'HowTo'"
msgstr "مثال: 'كيفية' "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.xml:0
msgid "e.g. \"15\""
msgstr "مثال: \"15\" "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "e.g. \"Computer Science for kids\""
msgstr "مثال: \"علوم الحاسوب للأطفال\" "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/category_add_dialog/category_add_dialog.xml:0
msgid "e.g. \"Introduction\""
msgstr "مثال: \"المقدمة\" "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
msgid "e.g. \"Which animal cannot fly?\""
msgstr "مثال: \"أي الحيوانات لا يمكنه الطيران؟\" "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
msgid "e.g. \"{{placeholder || \"another animal\"}}\""
msgstr "مثال: \"{{placeholder || \"another animal\"}}\" "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_form_add
msgid "e.g. Computer Science for kids"
msgstr "مثال: علوم الحاسوب للأطفال "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid ""
"e.g. In this video, we'll give you the keys on how Odoo can help you to grow"
" your business. At the end, we'll propose you a quiz to test your knowledge."
msgstr ""
"مثال: في هذا الفيديو، سوف تحصل على المعلومات الأساسية عن كيف يمكن لأودو "
"مساعدتك في تنمية أعمالك. في نهاية الفيديو، سوف نطرح اختباراً قصيراً لنختبر "
"به معرفتك. "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "e.g. Setting up your computer"
msgstr "مثال: ضبط إعدادات حاسوبك "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_question_view_form
msgid "e.g. What powers a computer?"
msgstr "مثال: ما الذي يشغّل الحاسوب؟ "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
msgid "e.g. Your Level"
msgstr "مثال: مستواك "

#. module: website_slides
#: model:ir.ui.menu,name:website_slides.website_slides_menu_root
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "eLearning"
msgstr "التعلم الإلكتروني "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_partner__slide_channel_ids
#: model:ir.model.fields,field_description:website_slides.field_res_users__slide_channel_ids
msgid "eLearning Courses"
msgstr "دورات التعلم الإلكتروني "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "eLearning Overview"
msgstr "نظرة عامة على التعلم الإلكتروني "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "for 'Private' videos and similar to"
msgstr "لمقاطع الفيديو 'الخاصة' ويشبه "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "for public ones."
msgstr "للعامة منها. "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/email_sharing_input.xml:0
msgid "<EMAIL>, <EMAIL>"
msgstr "<EMAIL>, <EMAIL>"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "https://drive.google.com/file/d/ABC/view?usp=sharing"
msgstr "https://drive.google.com/file/d/ABC/view?usp=sharing"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "https://vimeo.com/558907333/30da9ff3d8"
msgstr "https://vimeo.com/558907333/30da9ff3d8"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "https://vimeo.com/558907555"
msgstr "https://vimeo.com/558907555"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "join"
msgstr "انضمام"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/email_sharing_input.xml:0
#: code:addons/website_slides/static/src/xml/slide_course_join.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid "login"
msgstr "تسجيل الدخول"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid ""
"mean? The Vimeo \"Private\" privacy setting means it is a video which can be viewed only by the users with the link to it.\n"
"                Your video will never come up in the search results nor on your channel."
msgstr ""
"؟ يعني إعداد الخصوصية \"خاص\" في Vimeo أنه مقطع فيديو يمكن عرضه فقط من قِبَل المستخدمين الذين يملكون الرابط.\n"
"                لن يظهر مقطع الفيديو ضمن نتائج البحث أو في قناتك. "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid ""
"means? The YouTube \"unlisted\" means it is a video which can be viewed only"
" by the users with the link to it. Your video will never come up in the "
"search results nor on your channel."
msgstr ""
"؟ يوتيوب \"غير مدرج\" تعني أنه مقطع فيديو يمكن عرضه فقط من قِبَل المستخدمين "
"الذين يملكون الرابط. لن يظهر مقطع الفيديو ضمن نتائج البحث أو في قناتك. "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_course_join.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "or"
msgstr "أو"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_unsubscribe_dialog/slide_unsubscribe_dialog.xml:0
msgid "or Leave the course"
msgstr "أو قم بمغادرة الدورة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "request"
msgstr "طلب "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "sign in"
msgstr "تسجيل الدخول"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "start"
msgstr "بدء"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_card
msgid "steps"
msgstr "الخطوات"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.join_course_link
msgid "to access resources"
msgstr "للوصول إلى الموارد "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "to be the first to leave a comment."
msgstr "لتكون أول من يترك تعليقاً. "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
msgid "to browse preview content and enroll."
msgstr "لتصفح محتوى المعاينة والتسجيل. "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "to contact responsible"
msgstr "للتواصل مع الشخص المسؤول "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
msgid "to enroll."
msgstr "للتسجيل."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_course_join.xml:0
msgid "to join this course"
msgstr "للانضمام إلى هذه الدورة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "to request access"
msgstr "لطلب صلاحية الوصول "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/email_sharing_input.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid "to share this"
msgstr "لمشاركة هذا"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "to unlock"
msgstr "لإلغاء القفل "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "unlisted"
msgstr "غير مدرج "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_training_category
#: model_terms:ir.ui.view,arch_db:website_slides.user_quickkarma_card
msgid "xp"
msgstr "xp"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid "<EMAIL>, <EMAIL>"
msgstr "<EMAIL>, <EMAIL>"

#. module: website_slides
#: model:mail.template,subject:website_slides.mail_template_channel_shared
msgid "{{ user.name }} shared a Course"
msgstr "{{ user.name }} قام بمشاركة دورة "

#. module: website_slides
#: model:mail.template,subject:website_slides.slide_template_shared
msgid "{{ user.name }} shared a {{ object.slide_category }} with you!"
msgstr "قام {{ user.name }} بمشاركة {{ object.slide_category }} معك! "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
msgid "└<span class=\"ms-1\">Uncategorized</span>"
msgstr "└<span class=\"ms-1\">غير مصنف</span> "
