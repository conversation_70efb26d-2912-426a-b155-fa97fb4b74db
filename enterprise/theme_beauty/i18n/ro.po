# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* theme_beauty
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-07 13:28+0000\n"
"PO-Revision-Date: 2024-09-25 18:03+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_quotes_carousel_minimal
msgid ""
"\" A trusted partner for beauty. <br/>Innovative, reliable, and always "
"setting new trends. \""
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_quotes_carousel_minimal
msgid ""
"\" Outstanding results and service! <br/>They consistently elevate our "
"beauty routine. \""
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_quotes_carousel_minimal
msgid ""
"\" Their products transformed our look. Effective, high-quality, and "
"luxurious. \""
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_pricelist_boxed
msgid "$100.00"
msgstr "$100.00"

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_pricelist_boxed
msgid "$110.00"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_pricelist_boxed
msgid "$120.00"
msgstr "$120.00"

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_pricelist_boxed
msgid "$25.00"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_pricelist_boxed
msgid "$30.00"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_pricelist_boxed
msgid "$50.00"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_text_image
msgid ""
"<br/>\n"
"        Escape from everyday busy reality and treat your body with intensive care. Beauty and health go hand in hand. Take time to slow down in our relaxing spa, and everything you’re chasing will come around."
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_text_cover
msgid ""
"<br/>Showcase and manage your cosmetics line online with a user-friendly "
"platform that simplifies all the steps, from setup to product display, "
"making it easy to highlight your beauty products.<br/>"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_empowerment
msgid ""
"<br/>Unleashing beauty with elegance and style, from runway to "
"everyday<br/><br/>"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_big_number
msgid "<font style=\"color: rgb(165, 35, 91);\">200+</font>"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_empowerment
msgid ""
"<i class=\"fa fa-fw fa-info-circle o_not-animable\" role=\"img\"/>  Top "
"Beauty Picks"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_image_title
msgid "A Deep Dive into Beauty and Innovation"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_pricelist_boxed
msgid ""
"A lightweight, long-lasting foundation that provides full coverage while "
"allowing your skin to breathe."
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_pricelist_boxed
msgid ""
"A rejuvenating facial treatment designed to reduce wrinkles and fine lines, "
"leaving your skin looking fresh and youthful."
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_pricelist_boxed
msgid ""
"A revitalizing facial that uses vitamin C and natural extracts to enhance "
"radiance and even out skin tone."
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_cards_grid
msgid "Advanced Beauty Treatments"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_company_team
msgid "Aline"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_pricelist_boxed
msgid "Anti-Aging Facial"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_quadrant
msgid "Beauty &amp; Care"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_unveil
msgid "Beauty Redefined"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_striped_center_top
msgid "Beauty perfected with care"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_call_to_action
msgid "Beauty without <br/>expression is boring"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_empowerment
msgid "Book a session"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_pricelist_boxed
msgid "Brightening Facial"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_title
msgid ""
"Close your eyes and let yourself go in the expert hands of our beauticians."
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_intro_pill
msgid "Confidence and<br/>Timeless Elegance"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_key_benefits
msgid "Custom Beauty Solutions"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_pricelist_boxed
msgid ""
"Deeply hydrating facial treatment that restores moisture and balances your "
"skin, perfect for dry or sensitive skin types."
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_quadrant
msgid "Discover Beauty"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_cards_grid
msgid ""
"Discover our exclusive range of high-quality beauty products, crafted with "
"natural ingredients to enhance your skin's radiance and health."
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_cta_box
msgid "Discover our large<br/>choice of cosmetics !"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_images_mosaic
msgid "Discover our latest cosmetic products for radiant and flawless looks."
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_intro_pill
msgid "Discover our products"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_freegrid
msgid ""
"Discover our range of beauty products designed to bring out the best in you."
" From skincare to cosmetics, we offer everything you need to look and feel "
"your best."
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_shape_image
msgid ""
"Discover our range of high-quality cosmetics designed to enhance your "
"beauty. From skincare essentials to makeup must-haves, our products are "
"crafted to meet your needs and exceed your expectations."
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_key_images
msgid "Discover the Beauty Within You"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_striped_top
msgid "Discover the Ultimate Beauty with Top Cosmetics"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_card_offset
msgid "Discover the beauty within you."
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_key_images
msgid "Discover vibrant colors that inspire confidence"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_carousel_intro
msgid "Discover your glow"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_striped_center_top
msgid "Embrace Your Genuine Glow"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_freegrid
msgid "Enhance Your Natural Beauty with Our Products"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_key_images
msgid "Enhance Your Natural Glow with Our Products"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_pricelist_boxed
msgid ""
"Enhance your natural beauty with our range of luxurious cosmetic services "
"and products. Expertly crafted for every skin type and style."
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_card_offset
msgid ""
"Enhance your natural beauty with our range of skincare and cosmetic "
"products. Our experts provide personalized care to help you look and feel "
"your best every day."
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_unveil
msgid ""
"Enhance your natural glow with our premium beauty products and expert "
"services."
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_carousel_intro
msgid ""
"Enhance your radiance and confidence with our carefully curated products "
"that bring out your natural beauty, every day."
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_key_benefits
msgid ""
"Enjoy access to special promotions and exclusive deals that elevate your "
"beauty routine while offering great value."
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_product_list
msgid "Essential oils"
msgstr "Uleiuri esențiale"

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_key_benefits
msgid "Exclusive Cosmetics Offers"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_cards_grid
msgid ""
"Experience cutting-edge beauty treatments, from innovative facial therapies "
"to advanced body treatments, designed to enhance your natural beauty."
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_key_images
msgid "Experience the magic of our exclusive beauty line"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_cards_grid
msgid "Expert Beauty Consultations"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_key_benefits
msgid "Expert Support Anytime"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_empowerment
msgid "Explore Beauty Essentials   <i class=\"fa fa-long-arrow-right\" role=\"img\"/>"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_carousel_intro
msgid ""
"Explore more and find beauty products that align with your unique skin "
"needs, crafted for a flawless finish."
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_features_wall
msgid ""
"Explore our luxurious makeup selection, offering vibrant colors and long-"
"lasting formulas for a flawless look every day."
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_product_list
msgid "Eyeliner"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_cta_box
msgid "Find the perfect products to express your beauty!<br/><br/>"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_features_wall
msgid ""
"Find your signature scent with our exclusive range of perfumes, crafted to "
"leave a lasting impression all day long."
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_features_wall
msgid "Fragrances and Scents"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_text_cover
msgid "Get started"
msgstr "Introducere"

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_striped_center_top
msgid "Get the Look"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_pricelist_boxed
msgid ""
"High-quality mascara that adds volume and length to your lashes, with a "
"smudge-proof and long-lasting formula."
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_pricelist_boxed
msgid "Hydrating Facial"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_striped_center_top
msgid ""
"Indulge in the finest beauty and health products, where expert care meets "
"luxurious cosmetics to enhance your natural glow."
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_quadrant
msgid ""
"Indulge in the ultimate beauty and health experience. Our range of products "
"and services, from cosmetics to hair care, are designed to enhance your "
"natural beauty.<br/><br/> Discover your glow with us."
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_wavy_grid
msgid "Innovation and Style"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_cards_grid
msgid "Innovative Beauty Treatments for Radiant Results"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_company_team
msgid "Iris"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_call_to_action
msgid "Join us and enjoy the pleasure of caring for yourself."
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_product_list
msgid "Lipstick"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_pricelist_boxed
msgid "Long-Lasting Foundation"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_key_images
msgid "Luxurious cosmetics for a flawless finish"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_product_list
msgid "Make-up"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_company_team
msgid "Mich"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_features_wall
msgid "Natural Skincare Products"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_pricelist_boxed
msgid "Our Beauty Collection"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_empowerment
msgid "Our Collections"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_images_mosaic
msgid "Our Newest Beauty Essentials"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_cards_grid
msgid ""
"Our experts provide tailored skincare consultations, recommending products "
"and treatments designed specifically for your skin type and concerns."
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_cards_grid
msgid ""
"Our skilled beauty professionals offer personalized consultations to help "
"you find the perfect look, from makeup tips to skincare routines."
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_key_benefits
msgid ""
"Our team is here to assist you at every stage of your project, ensuring a "
"smooth and stress-free process for your project."
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_text_image
msgid "Pamper Yourself"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_product_list
msgid "Perfume"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_wavy_grid
msgid "Personalized Beauty"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_cards_grid
msgid "Personalized Skincare Solutions"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_cards_grid
msgid "Premium Beauty Products"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_features_wall
msgid "Premium Makeup Collection"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_wavy_grid
msgid "Quality and Expertise"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_empowerment
msgid "Radiance by<br/>LuxeAura"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_carousel_intro
msgid "Refresh your routine with our exclusive formulas and expert tips."
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_features_wall
msgid ""
"Revitalize your skin with our range of organic, chemical-free skincare "
"products designed for all skin types."
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_pricelist_boxed
msgid ""
"Richly pigmented matte lipstick available in various shades, providing a "
"bold, long-lasting finish without drying out your lips."
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_freegrid
msgid "Shop Beauty"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_cta_box
msgid "Shop now"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_text_cover
msgid "Showcase<br/>your products."
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_product_list
msgid "Skin gel"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_carousel_intro
msgid "Skincare solutions crafted for you"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_key_images
msgid "Skincare solutions for every type and tone"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_wavy_grid
msgid "Sustainable Beauty"
msgstr ""

#. module: theme_beauty
#: model:ir.model,name:theme_beauty.model_theme_utils
msgid "Theme Utils"
msgstr "Utile Temă"

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_company_team
msgid "Tony"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_image_title
msgid ""
"Transform your beauty routine with our new collection, where luxury meets "
"effectiveness. Elevate your look with products that blend skincare and "
"makeup seamlessly."
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_card_offset
msgid "Unveil Your Natural Beauty"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_carousel_intro
msgid "Unveiling your natural beauty"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_pricelist_boxed
msgid "Velvet Matte Lipstick"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_pricelist_boxed
msgid "Volumizing Mascara"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_wavy_grid
msgid ""
"We create customized beauty solutions tailored to your individual needs. Our"
" team collaborates with you to ensure a flawless and radiant look from start"
" to finish."
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_wavy_grid
msgid ""
"We offer the latest beauty trends and innovative products. Leveraging "
"cutting-edge technology, we help you achieve your beauty goals."
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_key_benefits
msgid ""
"We provide custom pricing based on the unique needs of your project, "
"ensuring you receive exceptional design services that fit your budget."
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_wavy_grid
msgid "What we offer to our clients"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_wavy_grid
msgid ""
"With extensive experience and in-depth beauty knowledge, we provide tips and"
" products that keep you looking your best."
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_cover
msgid "Working beauty <br/>from the inside out"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_framed_intro
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_image_hexagonal
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_striped_top
msgid "Working beauty from the inside out"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_wavy_grid
msgid ""
"Your satisfaction is our priority. Our support team is always here to "
"assist, ensuring you enjoy a smooth and luxurious experience."
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_big_number
msgid "happy customers"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_pricelist_boxed
msgid "✽  Facial Treatments"
msgstr ""

#. module: theme_beauty
#: model_terms:theme.ir.ui.view,arch:theme_beauty.s_pricelist_boxed
msgid "✽  Makeup Products"
msgstr ""
