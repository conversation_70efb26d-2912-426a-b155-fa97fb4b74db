# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_stock_renting
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 09:15+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: sale_stock_renting
#. odoo-python
#: code:addons/sale_stock_renting/models/product_product.py:0
msgid "%(product)s (%(qty)s item)"
msgstr "%(product)s (%(qty)s article)"

#. module: sale_stock_renting
#. odoo-python
#: code:addons/sale_stock_renting/models/product_product.py:0
msgid "%(product)s (%(qty)s items)"
msgstr "%(product)s (%(qty)s articles)"

#. module: sale_stock_renting
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.rental_order_wizard_view_form
msgid ""
"<i class=\"fa fa-warning\"/>\n"
"                        <span>\n"
"                            Some products don't have the requested qty available for pickup\n"
"                        </span>"
msgstr ""
"<i class=\"fa fa-warning\"/>\n"
"                        <span>\n"
"                            Certains produits n'ont pas la quantité demandée disponible pour l'enlèvement\n"
"                        </span>"

#. module: sale_stock_renting
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.res_config_settings_inherit_view_form
msgid "Activate to use stock deliveries and receipts for rental orders"
msgstr ""
"Activer l'utilisation des livraisons et des réceptions pour les ordres de "
"location"

#. module: sale_stock_renting
#: model:ir.model.fields,help:sale_stock_renting.field_res_company__padding_time
msgid ""
"Amount of time (in hours) during which a product is considered unavailable "
"prior to renting (preparation time)."
msgstr ""
"Durée (en heures) pendant laquelle un produit est considéré comme "
"indisponible avant une location (temps de préparation)."

#. module: sale_stock_renting
#: model:ir.model.fields,help:sale_stock_renting.field_res_config_settings__padding_time
msgid ""
"Amount of time (in hours) during which a product is considered "
"unavailableprior to renting (preparation time)."
msgstr ""
"Temps (en heures) pendant lequel un produit est considéré comme indisponible"
" avant la location (temps de préparation)."

#. module: sale_stock_renting
#. odoo-javascript
#: code:addons/sale_stock_renting/static/src/widgets/qty_at_date_widget.xml:0
msgid "Availability"
msgstr "Disponibilité"

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_rental_order_wizard_line__qty_available
msgid "Available"
msgstr "Disponible"

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_sale_order_line__available_reserved_lots
msgid "Available Reserved Lots"
msgstr "Lots réservés disponibles"

#. module: sale_stock_renting
#. odoo-javascript
#: code:addons/sale_stock_renting/static/src/widgets/qty_at_date_widget.xml:0
msgid "Available for Rent"
msgstr "Disponible pour la location"

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_res_company
msgid "Companies"
msgstr "Sociétés"

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_res_config_settings
msgid "Config Settings"
msgstr "Paramètres de configuration"

#. module: sale_stock_renting
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.res_config_settings_inherit_view_form
msgid "Default Padding Time"
msgstr "Marge de temps par défaut"

#. module: sale_stock_renting
#: model:ir.model.fields,help:sale_stock_renting.field_rental_order_wizard_line__tracking
#: model:ir.model.fields,help:sale_stock_renting.field_sale_order_line__tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr "Assurez la traçabilité d'un produit stockable dans votre entrepôt."

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_rental_order_wizard__has_lines_missing_stock
msgid "Has lines whose products have insufficient stock"
msgstr "A des lignes dont les produits ont un stock insuffisant"

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_rental_order_wizard__has_tracked_lines
msgid "Has lines with tracked products"
msgstr "A des lignes avec produits trackés"

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_res_company__rental_loc_id
msgid "In rent"
msgstr "En location"

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_sale_rental_schedule__is_available
msgid "Is Available"
msgstr "Est disponible"

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_rental_order_wizard_line__is_product_storable
msgid "Is Product Storable"
msgstr "Est un produit stockable"

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_account_move
msgid "Journal Entry"
msgstr "Pièce comptable"

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_stock_lot
msgid "Lot/Serial"
msgstr "Lot/série"

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_stock_warehouse_orderpoint
msgid "Minimum Inventory Rule"
msgstr "Règle d'inventaire minimum"

#. module: sale_stock_renting
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.res_config_settings_inherit_view_form
msgid "Minimum amount of time between two rentals"
msgstr "Durée minimale entre deux locations"

#. module: sale_stock_renting
#. odoo-python
#: code:addons/sale_stock_renting/models/sale_order_line.py:0
msgid ""
"No valid quant has been found in location %(location)s for serial number "
"%(serial_number)s!"
msgstr ""
"Aucune quantité valide n'a été trouvée dans l'emplacement %(location)s pour "
"le numéro de série %(serial_number)s!"

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_res_config_settings__padding_time
msgid "Padding"
msgstr "Marge"

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_res_company__padding_time
msgid "Padding Time"
msgstr "Marge de temps"

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_rental_order_wizard
msgid "Pick-up/Return products"
msgstr "Enlever/retourner des produits"

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_rental_order_wizard_line__pickeable_lot_ids
msgid "Pickeable Lot"
msgstr "Lot sélectionnable"

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_rental_order_wizard_line__pickedup_lot_ids
#: model:ir.model.fields,field_description:sale_stock_renting.field_sale_order_line__pickedup_lot_ids
msgid "Pickedup Lot"
msgstr "Lot enlevé"

#. module: sale_stock_renting
#. odoo-python
#: code:addons/sale_stock_renting/wizard/rental_processing.py:0
msgid "Please specify the serial numbers picked up for the tracked products."
msgstr ""
"Veuillez préciser les numéros de série enlevés pour les produits trackés."

#. module: sale_stock_renting
#. odoo-python
#: code:addons/sale_stock_renting/wizard/rental_processing.py:0
msgid "Please specify the serial numbers returned for the tracked products."
msgstr ""
"Veuillez préciser les numéros de série retournés pour les produits trackés."

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_product_template
msgid "Product"
msgstr "Produit"

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_product_product
msgid "Product Variant"
msgstr "Variante de produit"

#. module: sale_stock_renting
#: model:stock.route,name:sale_stock_renting.route_rental
msgid "Rental"
msgstr "Location"

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_sale_rental_report
msgid "Rental Analysis Report"
msgstr "Rapport d'analyse de location"

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_sale_rental_schedule
msgid "Rental Schedule"
msgstr "Planification des locations"

#. module: sale_stock_renting
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.res_config_settings_inherit_view_form
msgid "Rental Transfers"
msgstr "Transferts de produits de location"

#. module: sale_stock_renting
#. odoo-python
#: code:addons/sale_stock_renting/models/sale_order_line.py:0
msgid "Rental move: %(order)s"
msgstr "Mouvement de location : %(order)s"

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_res_config_settings__group_rental_stock_picking
msgid "Rental pickings"
msgstr "Transferts de location"

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_rental_order_wizard_line
msgid "RentalOrderLine transient representation"
msgstr "RentalOrderLine représentation transitoire"

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_sale_order_line__reserved_lot_ids
msgid "Reserved Lot"
msgstr "Lot réservé"

#. module: sale_stock_renting
#. odoo-javascript
#: code:addons/sale_stock_renting/static/src/widgets/qty_at_date_widget.xml:0
msgid "Reserved lots unavailable"
msgstr "Lots réservés non disponibles"

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_stock_return_picking
msgid "Return Picking"
msgstr "Transfert de retour"

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_rental_order_wizard_line__returnable_lot_ids
msgid "Returnable Lot"
msgstr "Lot pouvant être retourné"

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_rental_order_wizard_line__returned_lot_ids
#: model:ir.model.fields,field_description:sale_stock_renting.field_sale_order_line__returned_lot_ids
msgid "Returned Lot"
msgstr "Lot retourné"

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_sale_order
msgid "Sales Order"
msgstr "Commande client"

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_sale_order_line
msgid "Sales Order Line"
msgstr "Ligne de commande"

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_product_product__preparation_time
#: model:ir.model.fields,field_description:sale_stock_renting.field_product_template__preparation_time
msgid "Security Time"
msgstr "Durée de sécurité"

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_sale_rental_report__lot_id
#: model:ir.model.fields,field_description:sale_stock_renting.field_sale_rental_schedule__lot_id
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.rental_report_search_view_inherit_lots
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.rental_schedule_view_search_inherit_lots
msgid "Serial Number"
msgstr "Numéro de série"

#. module: sale_stock_renting
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.rental_order_wizard_view_form
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.report_rental_order_document
msgid "Serial Numbers"
msgstr "Numéros de série"

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_stock_move
msgid "Stock Move"
msgstr "Mouvement de stock"

#. module: sale_stock_renting
#: model:ir.model.fields,help:sale_stock_renting.field_product_product__preparation_time
#: model:ir.model.fields,help:sale_stock_renting.field_product_template__preparation_time
msgid "Temporarily make this product unavailable before pickup."
msgstr "Rendre temporairement ce produit indisponible avant l'enlèvement."

#. module: sale_stock_renting
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.rental_schedule_view_gantt_inherit_stock
msgid "The product is not available during this period."
msgstr "Le produit n'est pas disponible pendant cette période."

#. module: sale_stock_renting
#: model:ir.model.fields,help:sale_stock_renting.field_res_company__rental_loc_id
msgid ""
"This technical location serves as stock for products currently in rentalThis"
" location is internal because products in rentalare still considered as "
"company assets."
msgstr ""
"Cet emplacement technique sert de stock pour les produits actuellement en "
"location Cet emplacement est interne car les produits en location sont "
"toujours considérés comme des actifs de l'entreprise."

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_rental_order_wizard_line__tracking
#: model:ir.model.fields,field_description:sale_stock_renting.field_sale_order_line__tracking
msgid "Tracking"
msgstr "Suivi"

#. module: sale_stock_renting
#. odoo-python
#: code:addons/sale_stock_renting/models/product_template.py:0
msgid ""
"Tracking by lots isn't supported for rental products.\n"
"You should rather change the tracking mode to unique serial numbers."
msgstr ""
"Le suivi par lots n'est pas soutenu pour les produits de location.\n"
"Vous devriez modifier le mode de suivi en numéros de série uniques."

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_sale_order_line__unavailable_lot_ids
msgid "Unavailable Lot"
msgstr "Lot indisponible"

#. module: sale_stock_renting
#: model:res.groups,name:sale_stock_renting.group_rental_stock_picking
msgid "Use pickings for rental orders"
msgstr "Utiliser des transferts pour les ordres de location"

#. module: sale_stock_renting
#. odoo-javascript
#: code:addons/sale_stock_renting/static/src/widgets/qty_at_date_widget.xml:0
msgid "View Rentals"
msgstr "Voir les locations"

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_stock_warehouse
#: model:ir.model.fields,field_description:sale_stock_renting.field_sale_rental_schedule__warehouse_id
msgid "Warehouse"
msgstr "Entrepôt"

#. module: sale_stock_renting
#. odoo-python
#: code:addons/sale_stock_renting/models/sale_order_line.py:0
msgid "You cannot change the product of lines linked to stock moves."
msgstr ""
"Vous ne pouvez pas changer le produit de lignes liées à des mouvements de "
"stock."

#. module: sale_stock_renting
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.res_config_settings_inherit_view_form
msgid "hours"
msgstr "heures"

#. module: sale_stock_renting
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.product_template_inherit_view_form_stock_rental
msgid "hours before orders"
msgstr "heures avant les commandes"

#. module: sale_stock_renting
#. odoo-javascript
#: code:addons/sale_stock_renting/static/src/widgets/qty_at_date_widget.xml:0
msgid "to"
msgstr "au"
