# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_xendit
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# 27cb721235bc771895826f594ae4f6eb_a256844 <219b303f361cd550c2362fb7cee8b2e6_513839>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>rt<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: payment_xendit
#. odoo-python
#: code:addons/payment_xendit/models/payment_transaction.py:0
msgid ""
"An error occurred during the processing of your payment (%s). Please try "
"again."
msgstr ""

#. module: payment_xendit
#: model_terms:ir.ui.view,arch_db:payment_xendit.inline_form
msgid "Card Code"
msgstr "Kart Kodu"

#. module: payment_xendit
#: model_terms:ir.ui.view,arch_db:payment_xendit.inline_form
msgid "Card Holder First Name"
msgstr ""

#. module: payment_xendit
#: model_terms:ir.ui.view,arch_db:payment_xendit.inline_form
msgid "Card Holder Last Name"
msgstr ""

#. module: payment_xendit
#: model_terms:ir.ui.view,arch_db:payment_xendit.inline_form
msgid "Card Number"
msgstr "Kart numarası"

#. module: payment_xendit
#: model:ir.model.fields,field_description:payment_xendit.field_payment_provider__code
msgid "Code"
msgstr "Kod"

#. module: payment_xendit
#. odoo-python
#: code:addons/payment_xendit/models/payment_provider.py:0
msgid "Could not establish the connection to the API."
msgstr "API bağlantısı kurulamadı."

#. module: payment_xendit
#: model_terms:ir.ui.view,arch_db:payment_xendit.inline_form
msgid "Email"
msgstr "E-Posta"

#. module: payment_xendit
#: model_terms:ir.ui.view,arch_db:payment_xendit.inline_form
msgid "Expiration"
msgstr "Geçerlilik Tarihi"

#. module: payment_xendit
#. odoo-javascript
#: code:addons/payment_xendit/static/src/js/payment_form.js:0
msgid "Invalid CVN"
msgstr ""

#. module: payment_xendit
#. odoo-javascript
#: code:addons/payment_xendit/static/src/js/payment_form.js:0
msgid "Invalid Card Number"
msgstr ""

#. module: payment_xendit
#. odoo-javascript
#: code:addons/payment_xendit/static/src/js/payment_form.js:0
msgid "Invalid Date"
msgstr ""

#. module: payment_xendit
#: model_terms:ir.ui.view,arch_db:payment_xendit.inline_form
msgid "John"
msgstr ""

#. module: payment_xendit
#: model_terms:ir.ui.view,arch_db:payment_xendit.inline_form
msgid "MM"
msgstr "MM"

#. module: payment_xendit
#. odoo-python
#: code:addons/payment_xendit/models/payment_transaction.py:0
msgid "No transaction found matching reference %s."
msgstr "Referans %s eşleşen bir işlem bulunamadı."

#. module: payment_xendit
#: model:ir.model,name:payment_xendit.model_payment_provider
msgid "Payment Provider"
msgstr "Ödeme Sağlayıcı"

#. module: payment_xendit
#: model:ir.model,name:payment_xendit.model_payment_transaction
msgid "Payment Transaction"
msgstr "Ödeme İşlemi"

#. module: payment_xendit
#. odoo-javascript
#: code:addons/payment_xendit/static/src/js/payment_form.js:0
msgid "Payment processing failed"
msgstr ""

#. module: payment_xendit
#: model_terms:ir.ui.view,arch_db:payment_xendit.inline_form
msgid "Phone Number"
msgstr "Telefon Numarası"

#. module: payment_xendit
#: model_terms:ir.ui.view,arch_db:payment_xendit.payment_provider_form_xendit
msgid "Public Key"
msgstr "Genel Anahtar"

#. module: payment_xendit
#. odoo-python
#: code:addons/payment_xendit/models/payment_transaction.py:0
msgid "Received data with missing reference."
msgstr ""

#. module: payment_xendit
#: model_terms:ir.ui.view,arch_db:payment_xendit.payment_provider_form_xendit
msgid "Secret Key"
msgstr "Gizli Şifre"

#. module: payment_xendit
#: model_terms:ir.ui.view,arch_db:payment_xendit.inline_form
msgid "Smith"
msgstr ""

#. module: payment_xendit
#. odoo-python
#: code:addons/payment_xendit/models/payment_provider.py:0
msgid ""
"The communication with the API failed. Xendit gave us the following "
"information: '%s'"
msgstr ""

#. module: payment_xendit
#: model:ir.model.fields,help:payment_xendit.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr "Bu ödeme sağlayıcısının teknik kodu."

#. module: payment_xendit
#. odoo-python
#: code:addons/payment_xendit/models/payment_transaction.py:0
msgid "The transaction is not linked to a token."
msgstr "İşlem bir belirteçle bağlantılı değildir."

#. module: payment_xendit
#: model_terms:ir.ui.view,arch_db:payment_xendit.payment_provider_form_xendit
msgid "Webhook Token"
msgstr ""

#. module: payment_xendit
#: model:ir.model.fields.selection,name:payment_xendit.selection__payment_provider__code__xendit
msgid "Xendit"
msgstr ""

#. module: payment_xendit
#: model:ir.model.fields,field_description:payment_xendit.field_payment_provider__xendit_public_key
msgid "Xendit Public Key"
msgstr ""

#. module: payment_xendit
#: model:ir.model.fields,field_description:payment_xendit.field_payment_provider__xendit_secret_key
msgid "Xendit Secret Key"
msgstr ""

#. module: payment_xendit
#: model:ir.model.fields,field_description:payment_xendit.field_payment_provider__xendit_webhook_token
msgid "Xendit Webhook Token"
msgstr ""

#. module: payment_xendit
#: model_terms:ir.ui.view,arch_db:payment_xendit.inline_form
msgid "YYYY"
msgstr ""

#. module: payment_xendit
#: model_terms:ir.ui.view,arch_db:payment_xendit.inline_form
msgid "<EMAIL>"
msgstr "<EMAIL>"
