# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* marketing_automation_whatsapp
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON><PERSON> Gözütok, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>n Atay, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:27+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: marketing_automation_whatsapp
#: model_terms:ir.ui.view,arch_db:marketing_automation_whatsapp.marketing_activity_summary_template_whatsapp
msgid "\" <strong>doesn't get opened</strong>,"
msgstr ""

#. module: marketing_automation_whatsapp
#: model_terms:ir.ui.view,arch_db:marketing_automation_whatsapp.marketing_activity_summary_template_whatsapp
msgid "\" <strong>doesn't get replied</strong>,"
msgstr ""

#. module: marketing_automation_whatsapp
#: model_terms:ir.ui.view,arch_db:marketing_automation_whatsapp.marketing_activity_summary_template_whatsapp
msgid "\" <strong>gets bounced</strong>,"
msgstr ""

#. module: marketing_automation_whatsapp
#: model:ir.model.fields,field_description:marketing_automation_whatsapp.field_marketing_campaign__whatsapp_template_count
msgid "# Whatsapp"
msgstr ""

#. module: marketing_automation_whatsapp
#: model_terms:ir.ui.view,arch_db:marketing_automation_whatsapp.marketing_campaign_view_form
msgid "<i class=\"fa fa-exclamation-circle\"/> Bounced after"
msgstr "<i class=\"fa fa-exclamation-circle\"/> Bounced after"

#. module: marketing_automation_whatsapp
#: model_terms:ir.ui.view,arch_db:marketing_automation_whatsapp.marketing_campaign_view_form
msgid "<i class=\"fa fa-exclamation-circle\"/> Clicked within"
msgstr ""

#. module: marketing_automation_whatsapp
#: model_terms:ir.ui.view,arch_db:marketing_automation_whatsapp.marketing_campaign_view_form
msgid "<i class=\"fa fa-exclamation-circle\"/> Not Clicked within"
msgstr ""

#. module: marketing_automation_whatsapp
#: model_terms:ir.ui.view,arch_db:marketing_automation_whatsapp.marketing_campaign_view_form
msgid "<i class=\"fa fa-exclamation-circle\"/> Not Opened after"
msgstr ""

#. module: marketing_automation_whatsapp
#: model_terms:ir.ui.view,arch_db:marketing_automation_whatsapp.marketing_campaign_view_form
msgid "<i class=\"fa fa-exclamation-circle\"/> Not Replied within"
msgstr ""

#. module: marketing_automation_whatsapp
#: model_terms:ir.ui.view,arch_db:marketing_automation_whatsapp.marketing_campaign_view_form
msgid "<i class=\"fa fa-exclamation-circle\"/> Opened after"
msgstr ""

#. module: marketing_automation_whatsapp
#: model_terms:ir.ui.view,arch_db:marketing_automation_whatsapp.marketing_campaign_view_form
msgid "<i class=\"fa fa-exclamation-circle\"/> Replied within"
msgstr ""

#. module: marketing_automation_whatsapp
#: model_terms:ir.ui.view,arch_db:marketing_automation_whatsapp.marketing_campaign_view_form
msgid "<i class=\"fa fa-pie-chart\"/> Details"
msgstr "<i class=\"fa fa-pie-chart\"/> Ayrıntılar"

#. module: marketing_automation_whatsapp
#: model_terms:ir.ui.view,arch_db:marketing_automation_whatsapp.marketing_campaign_view_form
msgid ""
"<i data-trigger-type=\"whatsapp_read\" class=\"fa fa-check-circle o_ma_text_read o_add_child_activity text-info\" title=\"Opened\" role=\"img\" aria-label=\"Opened\"/>\n"
"                <i data-trigger-type=\"whatsapp_not_read\" class=\"fa fa-check-circle o_ma_text_read o_add_child_activity text-danger\" title=\"Not Opened\" role=\"img\" aria-label=\"Not Opened\"/>\n"
"                <i data-trigger-type=\"whatsapp_replied\" class=\"fa fa-reply o_ma_text_replied o_add_child_activity text-success\" title=\"replied\" role=\"img\" aria-label=\"replied\"/>\n"
"                <i data-trigger-type=\"whatsapp_not_replied\" class=\"fa fa-reply o_ma_text_replied o_add_child_activity text-danger\" title=\"not replied\" role=\"img\" aria-label=\"not replied\"/>\n"
"                <i data-trigger-type=\"whatsapp_click\" class=\"fa fa-hand-pointer-o o_ma_text_processed o_add_child_activity text-success\" title=\"Clicked\" role=\"img\" aria-label=\"Clicked\"/>\n"
"                <i data-trigger-type=\"whatsapp_not_click\" class=\"fa fa-hand-pointer-o o_ma_text_rejected o_add_child_activity text-danger\" title=\"Not Clicked\" role=\"img\" aria-label=\"Not Clicked\"/>\n"
"                <i data-trigger-type=\"whatsapp_bounced\" class=\"fa fa-exclamation-circle o_ma_text_error o_add_child_activity text-danger\" title=\"Bounced\" role=\"img\" aria-label=\"Error\"/>"
msgstr ""

#. module: marketing_automation_whatsapp
#: model_terms:ir.ui.view,arch_db:marketing_automation_whatsapp.marketing_participant_view_form
msgid ""
"<span class=\"text-primary\">\n"
"                        <i class=\"fa fa-check-circle\"/> Not opened yet\n"
"                    </span>"
msgstr ""

#. module: marketing_automation_whatsapp
#: model_terms:ir.ui.view,arch_db:marketing_automation_whatsapp.marketing_participant_view_form
msgid ""
"<span class=\"text-primary\">\n"
"                        <i class=\"fa fa-exclamation-circle\"/> Didn't bounce yet\n"
"                    </span>"
msgstr ""

#. module: marketing_automation_whatsapp
#: model_terms:ir.ui.view,arch_db:marketing_automation_whatsapp.marketing_participant_view_form
msgid ""
"<span class=\"text-primary\">\n"
"                        <i class=\"fa fa-hand-pointer-o\"/> Not clicked yet\n"
"                    </span>"
msgstr ""

#. module: marketing_automation_whatsapp
#: model_terms:ir.ui.view,arch_db:marketing_automation_whatsapp.marketing_participant_view_form
msgid ""
"<span class=\"text-primary\">\n"
"                        <i class=\"fa fa-reply\"/> Not replied\n"
"                    </span>"
msgstr ""

#. module: marketing_automation_whatsapp
#: model:ir.model.fields,field_description:marketing_automation_whatsapp.field_marketing_activity__activity_type
msgid "Activity Type"
msgstr "Aktivite Türü"

#. module: marketing_automation_whatsapp
#: model_terms:ir.ui.view,arch_db:marketing_automation_whatsapp.marketing_campaign_view_form
msgid "Clicked"
msgstr "Tıklandı"

#. module: marketing_automation_whatsapp
#: model:ir.model.fields,field_description:marketing_automation_whatsapp.field_whatsapp_message__links_click_datetime
msgid "Clicked On"
msgstr "Tıklandı"

#. module: marketing_automation_whatsapp
#: model:ir.model,name:marketing_automation_whatsapp.model_discuss_channel
msgid "Discussion Channel"
msgstr "Mesajlaşma Kanalı"

#. module: marketing_automation_whatsapp
#. odoo-python
#: code:addons/marketing_automation_whatsapp/models/marketing_activity.py:0
msgid "Exception in Whatsapp Marketing: %s"
msgstr ""

#. module: marketing_automation_whatsapp
#: model:ir.model,name:marketing_automation_whatsapp.model_link_tracker_click
msgid "Link Tracker Click"
msgstr "Bağlantı İzleyici Tıklaması"

#. module: marketing_automation_whatsapp
#: model_terms:ir.ui.view,arch_db:marketing_automation_whatsapp.marketing_activity_view_form
msgid ""
"Make sure you select url tracking under buttons option, otherwise those "
"links won't be tracked"
msgstr ""

#. module: marketing_automation_whatsapp
#: model:ir.model,name:marketing_automation_whatsapp.model_marketing_activity
msgid "Marketing Activity"
msgstr "Pazarlama Aktitivesi"

#. module: marketing_automation_whatsapp
#: model:ir.actions.act_window,name:marketing_automation_whatsapp.whatsapp_template_action_marketing_automation
msgid "Marketing Automation Whatsapp"
msgstr ""

#. module: marketing_automation_whatsapp
#: model:ir.model,name:marketing_automation_whatsapp.model_marketing_campaign
msgid "Marketing Campaign"
msgstr "Pazarlama Kampanyası"

#. module: marketing_automation_whatsapp
#: model:ir.model.fields,field_description:marketing_automation_whatsapp.field_marketing_trace__whatsapp_message_id
msgid "Marketing Template"
msgstr "Pazarlama Şablonu"

#. module: marketing_automation_whatsapp
#: model:ir.model,name:marketing_automation_whatsapp.model_marketing_trace
#: model:ir.model.fields,field_description:marketing_automation_whatsapp.field_whatsapp_message__marketing_trace_ids
msgid "Marketing Trace"
msgstr "Pazarlama İzi"

#. module: marketing_automation_whatsapp
#: model_terms:ir.ui.view,arch_db:marketing_automation_whatsapp.marketing_campaign_view_form
msgid "Message Clicked"
msgstr ""

#. module: marketing_automation_whatsapp
#: model_terms:ir.ui.view,arch_db:marketing_automation_whatsapp.marketing_campaign_view_form
msgid "Message Opened"
msgstr ""

#. module: marketing_automation_whatsapp
#: model_terms:ir.ui.view,arch_db:marketing_automation_whatsapp.marketing_campaign_view_form
msgid "Message Replied"
msgstr ""

#. module: marketing_automation_whatsapp
#: model_terms:ir.ui.view,arch_db:marketing_automation_whatsapp.marketing_campaign_view_form
msgid "Message Sent"
msgstr ""

#. module: marketing_automation_whatsapp
#: model_terms:ir.ui.view,arch_db:marketing_automation_whatsapp.marketing_campaign_view_form
msgid "Opened"
msgstr "Açıldı"

#. module: marketing_automation_whatsapp
#. odoo-python
#: code:addons/marketing_automation_whatsapp/models/marketing_trace.py:0
msgid "Parent Whatsapp message got opened"
msgstr ""

#. module: marketing_automation_whatsapp
#. odoo-python
#: code:addons/marketing_automation_whatsapp/models/marketing_trace.py:0
msgid "Parent Whatsapp message was clicked"
msgstr ""

#. module: marketing_automation_whatsapp
#. odoo-python
#: code:addons/marketing_automation_whatsapp/models/marketing_trace.py:0
msgid "Parent Whatsapp was replied to"
msgstr ""

#. module: marketing_automation_whatsapp
#. odoo-python
#: code:addons/marketing_automation_whatsapp/models/marketing_trace.py:0
msgid "Parent whatsapp was bounced"
msgstr ""

#. module: marketing_automation_whatsapp
#. odoo-python
#: code:addons/marketing_automation_whatsapp/models/marketing_activity.py:0
msgid "Participants of %(name)s (%(filter)s)"
msgstr ""

#. module: marketing_automation_whatsapp
#: model_terms:ir.ui.view,arch_db:marketing_automation_whatsapp.marketing_activity_view_form
msgid "Pick a Template..."
msgstr "Bir Şablon Seçin..."

#. module: marketing_automation_whatsapp
#: model_terms:ir.ui.view,arch_db:marketing_automation_whatsapp.marketing_campaign_view_form
msgid "Replied"
msgstr "Cevaplandı"

#. module: marketing_automation_whatsapp
#: model_terms:ir.ui.view,arch_db:marketing_automation_whatsapp.marketing_campaign_view_form
msgid "Sent"
msgstr "Gönderildi"

#. module: marketing_automation_whatsapp
#: model:ir.model.fields,help:marketing_automation_whatsapp.field_whatsapp_message__links_click_datetime
msgid "Stores last click datetime in case of multi clicks."
msgstr "Çoklu tıklama durumunda son tıklama datetime'ı saklar."

#. module: marketing_automation_whatsapp
#: model:ir.model.fields.selection,name:marketing_automation_whatsapp.selection__whatsapp_template_button__url_type__tracked
msgid "Tracked"
msgstr "İzlemek"

#. module: marketing_automation_whatsapp
#: model:ir.model.fields,field_description:marketing_automation_whatsapp.field_marketing_activity__trigger_category
msgid "Trigger Category"
msgstr "Tetik Kategorisi"

#. module: marketing_automation_whatsapp
#: model:ir.model.fields,field_description:marketing_automation_whatsapp.field_marketing_activity__trigger_type
msgid "Trigger Type"
msgstr "Tetikleyici Türü"

#. module: marketing_automation_whatsapp
#: model:ir.model.fields,field_description:marketing_automation_whatsapp.field_whatsapp_template_button__url_type
msgid "Url Type"
msgstr ""

#. module: marketing_automation_whatsapp
#: model:ir.model.fields.selection,name:marketing_automation_whatsapp.selection__marketing_activity__trigger_category__whatsapp
#: model_terms:ir.ui.view,arch_db:marketing_automation_whatsapp.marketing_campaign_view_form
#: model_terms:ir.ui.view,arch_db:marketing_automation_whatsapp.marketing_participant_view_form
msgid "WhatsApp"
msgstr ""

#. module: marketing_automation_whatsapp
#: model:ir.model,name:marketing_automation_whatsapp.model_whatsapp_message
msgid "WhatsApp Messages"
msgstr ""

#. module: marketing_automation_whatsapp
#: model:ir.model,name:marketing_automation_whatsapp.model_whatsapp_template
msgid "WhatsApp Template"
msgstr "WhatsApp Şablonu"

#. module: marketing_automation_whatsapp
#: model:ir.model,name:marketing_automation_whatsapp.model_whatsapp_template_button
msgid "WhatsApp Template Button"
msgstr ""

#. module: marketing_automation_whatsapp
#. odoo-python
#: code:addons/marketing_automation_whatsapp/models/marketing_activity.py:0
#: code:addons/marketing_automation_whatsapp/models/whatsapp_message.py:0
msgid "WhatsApp canceled"
msgstr ""

#. module: marketing_automation_whatsapp
#. odoo-python
#: code:addons/marketing_automation_whatsapp/models/marketing_activity.py:0
msgid "WhatsApp failed"
msgstr ""

#. module: marketing_automation_whatsapp
#: model:ir.model.fields,field_description:marketing_automation_whatsapp.field_marketing_activity__whatsapp_error
msgid "Whatsapp Error"
msgstr ""

#. module: marketing_automation_whatsapp
#: model:ir.model.fields,field_description:marketing_automation_whatsapp.field_link_tracker_click__whatsapp_message_id
#: model:ir.model.fields.selection,name:marketing_automation_whatsapp.selection__marketing_activity__activity_type__whatsapp
msgid "Whatsapp Message"
msgstr ""

#. module: marketing_automation_whatsapp
#: model:ir.model.fields,field_description:marketing_automation_whatsapp.field_marketing_activity__whatsapp_template_id
msgid "Whatsapp Template"
msgstr ""

#. module: marketing_automation_whatsapp
#: model:ir.model.fields.selection,name:marketing_automation_whatsapp.selection__marketing_activity__trigger_type__whatsapp_click
msgid "Whatsapp: click"
msgstr ""

#. module: marketing_automation_whatsapp
#: model:ir.model.fields.selection,name:marketing_automation_whatsapp.selection__marketing_activity__trigger_type__whatsapp_bounced
msgid "Whatsapp: message bounced"
msgstr ""

#. module: marketing_automation_whatsapp
#: model:ir.model.fields.selection,name:marketing_automation_whatsapp.selection__marketing_activity__trigger_type__whatsapp_not_click
msgid "Whatsapp: not click"
msgstr ""

#. module: marketing_automation_whatsapp
#: model:ir.model.fields.selection,name:marketing_automation_whatsapp.selection__marketing_activity__trigger_type__whatsapp_not_read
msgid "Whatsapp: not opened"
msgstr ""

#. module: marketing_automation_whatsapp
#: model:ir.model.fields.selection,name:marketing_automation_whatsapp.selection__marketing_activity__trigger_type__whatsapp_not_replied
msgid "Whatsapp: not replied"
msgstr ""

#. module: marketing_automation_whatsapp
#: model:ir.model.fields.selection,name:marketing_automation_whatsapp.selection__marketing_activity__trigger_type__whatsapp_read
msgid "Whatsapp: opened"
msgstr ""

#. module: marketing_automation_whatsapp
#: model:ir.model.fields.selection,name:marketing_automation_whatsapp.selection__marketing_activity__trigger_type__whatsapp_replied
msgid "Whatsapp: replied"
msgstr ""

#. module: marketing_automation_whatsapp
#: model_terms:ir.ui.view,arch_db:marketing_automation_whatsapp.marketing_activity_summary_template_whatsapp
msgid "after the <strong>Whatsapp message</strong> sent by the Activity \""
msgstr ""

#. module: marketing_automation_whatsapp
#: model_terms:ir.ui.view,arch_db:marketing_automation_whatsapp.marketing_activity_summary_template_whatsapp
msgid ""
"after the Participant <strong>clicks</strong><br/>on any link included in "
"the <strong>Whatsapp message</strong> sent by the Activity \""
msgstr ""

#. module: marketing_automation_whatsapp
#: model_terms:ir.ui.view,arch_db:marketing_automation_whatsapp.marketing_activity_summary_template_whatsapp
msgid ""
"after the Participant <strong>opens the Whatsapp message</strong> sent by "
"the Activity \""
msgstr ""

#. module: marketing_automation_whatsapp
#: model_terms:ir.ui.view,arch_db:marketing_automation_whatsapp.marketing_activity_summary_template_whatsapp
msgid ""
"after the Participant <strong>replies to the Whatsapp message</strong> sent "
"by the Activity \""
msgstr ""

#. module: marketing_automation_whatsapp
#: model_terms:ir.ui.view,arch_db:marketing_automation_whatsapp.marketing_activity_summary_template_whatsapp
msgid "doesn't get clicked,"
msgstr ""

#. module: marketing_automation_whatsapp
#: model_terms:ir.ui.view,arch_db:marketing_automation_whatsapp.marketing_activity_summary_template_whatsapp
msgid "if <strong>Whatsapp message</strong> sent by the Activity \""
msgstr ""

#. module: marketing_automation_whatsapp
#: model_terms:ir.ui.view,arch_db:marketing_automation_whatsapp.marketing_activity_summary_template_whatsapp
msgid ""
"if no link included in the <strong>Whatsapp message</strong> sent by the "
"Activity \""
msgstr ""
