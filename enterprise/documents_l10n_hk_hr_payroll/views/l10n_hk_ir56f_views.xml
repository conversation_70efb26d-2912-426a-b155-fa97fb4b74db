<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="l10n_hk_ir56f_view_form" model="ir.ui.view">
        <field name="name">l10n_hk_ir56f.view.form.inherit</field>
        <field name="model">l10n_hk.ir56f</field>
        <field name="inherit_id" ref="l10n_hk_hr_payroll.l10n_hk_ir56f_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//p[@name='save_warning']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <div name="button_box" position="inside">
                <button name="action_see_documents" type="object" class="oe_stat_button" icon="fa-file-text-o" invisible="not documents_enabled or documents_count == 0">
                    <div class="o_field_widget o_stat_info">
                        <span class="o_stat_value"><field name="documents_count"/></span>
                        <span class="o_stat_text">Documents</span>
                    </div>
                </button>
            </div>
            <p name="save_warning" position="after">
                <field name="documents_enabled" invisible="1"/>
                <div>
                    <div invisible="documents_enabled">
                        <p><strong>Warning: </strong>In order to post the IR56F sheets in the employee portal, you have to Enable "Human Resources" in the <a name="%(documents.configuration_action)d" type="action" class="oe_link">"Documents" app settings</a>.</p>
                    </div>
                </div>
            </p>
        </field>
    </record>
</odoo>
