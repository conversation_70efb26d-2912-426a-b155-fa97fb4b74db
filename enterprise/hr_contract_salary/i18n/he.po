# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_contract_salary
# 
# Translators:
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2024
# yael terner, 2024
# <PERSON><PERSON> Blum <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# Hed <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# Fishfur A <PERSON> <<EMAIL>>, 2024
# Orel Nahmany, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON>aFarkash, 2024
# <PERSON><PERSON> BLONDER <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:53+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: ZVI BLONDER <<EMAIL>>, 2024\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=3; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: 2;\n"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_personal_info__impacts_net_salary
msgid ""
" If checked, any change on this information will trigger a new computation "
"of the gross-->net salary."
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__signatures_count
msgid "# Signatures"
msgstr "מס' חתימות"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/models/hr_contract_salary_offer.py:0
msgid "%(company)s: Job Offer - %(job_title)s"
msgstr ""

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/models/hr_contract_salary_offer.py:0
msgid "%s manually set the Offer to Refused"
msgstr ""

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,helper:hr_contract_salary.hr_contract_salary_personal_info_name
msgid "(Lastname Firstname)"
msgstr ""

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,placeholder:hr_contract_salary.hr_contract_salary_personal_info_identification_id
msgid "00.00.00-000.00"
msgstr ""

#. module: hr_contract_salary
#: model:mail.template,body_html:hr_contract_salary.mail_template_send_offer
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <h2>Congratulations!</h2>\n"
"    You can configure your salary package by clicking on the link below.<br/>\n"
"    The link will expire on the <t t-out=\"ctx.get('validity_end')\"/>.\n"
"    <div style=\"padding: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/salary_package/simulation/offer/{{ctx.get('offer_id')}}\" target=\"_blank\" style=\"background-color: #875a7b; text-decoration: none; color: #fff; padding: 8px 16px 8px 16px; border-radius: 5px;\">Configure your package</a>\n"
"    </div>\n"
"</div>\n"
"        "
msgstr ""

#. module: hr_contract_salary
#: model:mail.template,body_html:hr_contract_salary.mail_template_send_offer_applicant
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <h2>Congratulations!</h2>\n"
"    You can configure your salary package by clicking on the link below.<br/>\n"
"    The link will expire on the <t t-out=\"ctx.get('validity_end')\"/>.\n"
"    <div style=\"padding: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/salary_package/simulation/offer/{{ctx.get('offer_id')}}?token={{ctx.get('access_token')}}\" target=\"_blank\" style=\"background-color: #875a7b; text-decoration: none; color: #fff; padding: 8px 16px 8px 16px; border-radius: 5px;\">Configure your package</a>\n"
"    </div>\n"
"</div>\n"
"        "
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_sidebar
msgid "<i class=\"fa fa-check\"/> Your message was successfully sent!"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_thank_you
msgid "<i class=\"fa fa-check-circle-o mr8\"/>Congratulations"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_sidebar
msgid "<i class=\"fa fa-comment\"/> Feedback"
msgstr "<i class=\"fa fa-comment\"/> משוב"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "<span class=\"ms-3\">/ month</span>"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "<span class=\"ms-3\">/ year</span>"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "<span class=\"o_stat_text ml4\">Previous Contract</span>"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_applicant_view_form
msgid "<span class=\"o_stat_text\">Contracts</span>"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_applicant_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_candidate_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "<span class=\"o_stat_text\">Offers</span>"
msgstr "<span class=\"o_stat_text\">הצעות</span>"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_offer_view_form
msgid "<span class=\"o_stat_text\">Requested Signature</span>"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "<span class=\"o_stat_text\">Reviews</span>"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_offer_view_form
msgid "<span class=\"o_stat_text\">Signed Contract</span>"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_thank_you
msgid "<span class=\"text-muted mr4 ml4\">|</span>"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_benefit_view_form
msgid ""
"<span>\n"
"                                    This field can only be modified by a server administrator, contact them if these field requires modifications.\n"
"                                </span>\n"
"                                <span invisible=\"requested_documents_fields_string\">\n"
"                                    There are currently no requested fields.\n"
"                                </span>"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_offer_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.res_config_settings_view_form_hr
msgid "<span> days</span>"
msgstr "<span> ימים</span>"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "<span>/ month</span>"
msgstr "<span>/ חודש</span>"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "<span>days / year</span>"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.constraint,message:hr_contract_salary.constraint_hr_contract_salary_benefit_required_fold_res_field_id
msgid "A folded field is required"
msgstr ""

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/models/hr_contract_signatory.py:0
msgid ""
"A signatory role is unassigned. Please ensure there is a valid group or "
"person assigned to each role."
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__access_token
msgid "Access Token"
msgstr "אסימון גישה"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__message_needaction
msgid "Action Needed"
msgstr "נדרשת פעולה"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_benefit_view_form
msgid "Actions"
msgstr "פעולות"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__active
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__active
msgid "Active"
msgstr "פעיל"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__activity_ids
msgid "Activities"
msgstr "פעילויות"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_benefit_view_form
msgid "Activity"
msgstr "פעילות"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__activity_creation
msgid "Activity Creation"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__activity_creation_type
msgid "Activity Creation Type"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "סימון פעילות חריגה"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__activity_state
msgid "Activity State"
msgstr "מצב פעילות"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__activity_type_id
msgid "Activity Type"
msgstr "סוג פעילות"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__activity_type_icon
msgid "Activity Type Icon"
msgstr "סוג פעילות"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_benefit_view_form
msgid "Add a line"
msgstr "הוסף שורה"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_benefit_view_form
msgid "Add a section"
msgstr "הוסף סעיף"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_benefit__benefit_type_id
msgid "Allow to define the periodicity and output type of the advantage"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_benefit__display_type__always
msgid "Always Selected"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__always_show_description
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit_value__always_show_description
msgid "Always Show Description"
msgstr ""

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/models/hr_applicant.py:0
msgid ""
"An %(offer)s has been sent by %(user)s to the applicant (mail: %(email)s)"
msgstr ""

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/models/hr_contract.py:0
msgid ""
"An %(offer)s has been sent by %(user)s to the employee (mail: %(email)s)"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_employee_report__final_yearly_costs
msgid "Annual Employee Budget"
msgstr ""

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
msgid "Annual Employer Cost"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_applicant
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__applicant_id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__applicant_id
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_offer_view_search
msgid "Applicant"
msgstr "מועמד"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_offer_view_search
msgid "Applicants"
msgstr "מועמדים"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__applies_on
msgid "Applies On"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_next_step_button
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_sidebar
msgid "Apply Now"
msgstr ""

#. module: hr_contract_salary
#: model:ir.actions.server,name:hr_contract_salary.ir_cron_clean_redundant_salary_data_ir_actions_server
msgid "Archive/Delete redundant generated salary data"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_resume_view_search
msgid "Archived"
msgstr "בארכיון"

#. module: hr_contract_salary
#. odoo-javascript
#: code:addons/hr_contract_salary/static/src/js/binary_field_contract.js:0
msgid "Are you sure you want to delete this file permanently ?"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__activity_responsible_id
msgid "Assigned to"
msgstr "משויכת ל"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__message_attachment_count
msgid "Attachment Count"
msgstr "כמות קבצים מצורפים"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info.value,name:hr_contract_salary.hr_contract_salary_personal_info_certificate_bachelor
msgid "Bachelor"
msgstr "תואר ראשון"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__applies_on__bank_account
msgid "Bank Account"
msgstr "חשבון בנק"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_acc_number
msgid "Bank account"
msgstr "חשבון בנק"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit_value__benefit_id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__benefit_ids
msgid "Benefit"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit_value__benefit_display_type
msgid "Benefit Display Type"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__res_field_public
msgid "Benefit Field"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__benefit_type_id
msgid "Benefit Type"
msgstr ""

#. module: hr_contract_salary
#: model:ir.actions.act_window,name:hr_contract_salary.hr_contract_benefit_action
#: model:ir.ui.menu,name:hr_contract_salary.salary_package_benefit
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_benefit_view_form
msgid "Benefits"
msgstr ""

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/models/hr_contract_salary_benefit.py:0
msgid "Benefits that are not linked to a field should always be displayed."
msgstr ""

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_birthdate
msgid "Birthdate"
msgstr "תאריך לידה"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_candidate
msgid "Candidate"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__category_id
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_personal_info_view_form
msgid "Category"
msgstr "קטגוריה"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_certificate
msgid "Certificate"
msgstr "תעודה"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_diff_email_template
msgid "Changes summary since previous contract:"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__display_type__checkbox
msgid "Checkbox"
msgstr "תיבת סימון"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__child_ids
msgid "Child"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_benefit__activity_creation
msgid ""
"Choose when the activity is created:\n"
"- Employee signs his contract: Activity is created as soon as the employee signed the contract\n"
"- Contract is countersigned: HR responsible have signed the contract and conclude the process."
msgstr ""

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_city
msgid "City"
msgstr "עיר"

#. module: hr_contract_salary
#: model:sign.template,redirect_url_text:hr_contract_salary.sign_template_cdi_developer
msgid "Close"
msgstr "סגור"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__code
msgid "Code"
msgstr "קוד"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__company_id
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_offer_view_search
msgid "Company"
msgstr "חברה"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_res_config_settings
msgid "Config Settings"
msgstr "הגדרות תצורה"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.refuse_offer_wizard_view_form
msgid "Confirm"
msgstr "אשר"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/models/hr_contract_salary_offer.py:0
msgid "Contact"
msgstr "איש קשר"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/models/hr_contract_salary_offer.py:0
msgid "Contact Email"
msgstr "דוא\"ל ליצירת קשר"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/models/hr_contract_salary_offer.py:0
msgid "Contract"
msgstr "חוזה"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract_salary_benefit_type
msgid "Contract Benefit Type"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract_salary_benefit_value
msgid "Contract Benefit Value"
msgstr ""

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
msgid "Contract Information:"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__res_field_id
msgid "Contract Related Field"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_signatory__sign_role_id
msgid "Contract Role"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract_signatory
msgid "Contract Signatories"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__contract_start_date
msgid "Contract Start Date"
msgstr "תאריך התחלת חוזה"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__default_contract_id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_history__default_contract_id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__contract_template_id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_signatory__contract_template_id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_job__default_contract_id
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_offer_view_search
msgid "Contract Template"
msgstr ""

#. module: hr_contract_salary
#: model:ir.actions.act_window,name:hr_contract_salary.action_hr_contract_templates
#: model:ir.ui.menu,name:hr_contract_salary.hr_recruitment_menu_contract_templates
msgid "Contract Templates"
msgstr ""

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
msgid "Contract Type"
msgstr "סוג חוזה"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__contract_update_template_id
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "Contract Update"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_history__contract_update_template_id
msgid "Contract Update Document Template"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__contract_update_signatories_ids
msgid "Contract Update Signatories"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_resume__value_type__contract
msgid "Contract Value"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract_employee_report
msgid "Contract and Employee Analysis Report"
msgstr "דוח ניתוח נתוני חוזים ועובדים"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_benefit__manual_res_field_id
msgid "Contract field used to manually encode an benefit value."
msgstr ""

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract_history
msgid "Contract history"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_benefit__activity_creation__countersigned
msgid "Contract is countersigned"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.menu,name:hr_contract_salary.menu_hr_recruitment_config_contract_templates
msgid "Contracts"
msgstr "חוזים"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "Contracts Reviews"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__cost_res_field_id
msgid "Cost Field"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__cost_res_field_public
msgid "Cost Field (Public)"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__cost_field
msgid "Cost Field Name"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__dropdown_selection__country
msgid "Countries"
msgstr "ארצות"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_country
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__country_id
msgid "Country"
msgstr "ארץ"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_country_of_birth
msgid "Country of Birth"
msgstr "ארץ לידה"

#. module: hr_contract_salary
#: model_terms:ir.actions.act_window,help:hr_contract_salary.hr_contract_salary_offer_recruitment_action
msgid "Create new offer"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__hash_token
msgid "Created From Token"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__create_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit_type__create_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit_value__create_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__create_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer_refusal_reason__create_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__create_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_type__create_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_value__create_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__create_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume_category__create_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_signatory__create_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_refuse_offer_wizard__create_uid
msgid "Created by"
msgstr "נוצר על-ידי"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__create_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit_type__create_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit_value__create_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__create_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer_refusal_reason__create_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__create_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_type__create_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_value__create_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__create_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume_category__create_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_signatory__create_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_refuse_offer_wizard__create_date
msgid "Created on"
msgstr "נוצר ב-"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__currency_id
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_benefit__uom__currency
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_resume__uom__currency
msgid "Currency"
msgstr "מטבע"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Customize your salary"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__display_type__date
msgid "Date"
msgstr "תאריך"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_benefit__uom__days
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_resume__uom__days
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_benefits
msgid "Days"
msgstr "ימים"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_res_config_settings__access_token_validity
msgid "Default Access Token Validity Duration"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_res_config_settings__employee_salary_simulator_link_validity
msgid "Default Salary Configurator Link Validity Duration For Employees"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_job__default_contract_id
msgid ""
"Default contract used to generate an offer. If empty, benefits will be taken"
" from current contract of the employee/nothing for an applicant."
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract__default_contract_id
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_history__default_contract_id
msgid "Default contract used when making an offer to an applicant."
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract__sign_template_id
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_history__sign_template_id
msgid ""
"Default document that the applicant will have to sign to accept a contract "
"offer."
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract__contract_update_template_id
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_history__contract_update_template_id
msgid ""
"Default document that the employee will have to sign to update his contract."
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_benefit__activity_creation_type
msgid ""
"Define when the system creates a new activity:\n"
"- When the benefit is set: Unique creation the first time the employee will take the benefit\n"
"- When the benefit is modified: Activity will be created for each change regarding the benefit."
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_benefit__sign_frenquency
msgid ""
"Define when the system creates a new sign request:\n"
"- When the benefit is set: Unique signature request the first time the employee will take the benefit\n"
"- When the benefit is modified: Signature request will be created for each change regarding the benefit."
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__department_id
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_offer_view_search
msgid "Department"
msgstr "מחלקה"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__description
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer_refusal_reason__name
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_benefit_view_form
msgid "Description"
msgstr "תיאור"

#. module: hr_contract_salary
#. odoo-javascript
#: code:addons/hr_contract_salary/static/src/xml/resume_sidebar.xml:0
msgid "Details"
msgstr "פרטים"

#. module: hr_contract_salary
#: model_terms:ir.actions.act_window,help:hr_contract_salary.hr_contract_salary_offer_recruitment_action
msgid ""
"Did you know that you can create an offer for any applicant?<br>\n"
"                Why don't you try? They're listed"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.refuse_offer_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_sidebar
msgid "Discard"
msgstr "בטל"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__display_name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit_type__display_name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit_value__display_name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer_refusal_reason__display_name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__display_name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_type__display_name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_value__display_name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__display_name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume_category__display_name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_signatory__display_name
#: model:ir.model.fields,field_description:hr_contract_salary.field_refuse_offer_wizard__display_name
msgid "Display Name"
msgstr "שם לתצוגה"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__display_type
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit_value__display_type
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__display_type
msgid "Display Type"
msgstr "סוג תצוגה"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info.value,name:hr_contract_salary.hr_contract_salary_personal_info_certificate_doctor
msgid "Doctor"
msgstr "דוקטור"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__display_type__document
msgid "Document"
msgstr "מסמך"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_benefit__sign_template_id
msgid ""
"Documents selected here will be requested to the employee for additional "
"signatures related to the benefit. eg: A company car policy to approve if "
"you choose a company car."
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_sign_document_wizard__sign_template_ids
msgid "Documents to sign"
msgstr "מסמך לחתימה"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_sign_document_wizard__sign_template_ids
msgid ""
"Documents to sign. Only documents with 1 or 2 different responsible are selectable.\n"
"        Documents with 1 responsible will only have to be signed by the employee while documents with 2 different responsible will have to be signed by both the employee and the responsible.\n"
"        "
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_benefit_view_form
msgid "Doesn't impact net salary"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_benefit__display_type__dropdown
msgid "Dropdown"
msgstr "נפתח"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_benefit__display_type__dropdown-group
msgid "Dropdown Group"
msgstr ""

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_email
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__display_type__email
msgid "Email"
msgstr "דוא\"ל"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_benefit__sign_copy_partner_id
msgid "Email address to which to transfer the signature."
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__employee_id
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__applies_on__employee
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_signatory__signatory__employee
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_offer_view_search
msgid "Employee"
msgstr "עובד"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__employee_contract_id
msgid "Employee Contract"
msgstr "חוזה עובד"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__employee_job_id
msgid "Employee Job"
msgstr ""

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
msgid "Employee Name"
msgstr "שם העובד"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_image_1920
msgid "Employee Photo"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_benefit__activity_creation__running
msgid "Employee signs his contract"
msgstr ""

#. module: hr_contract_salary
#: model:mail.template,name:hr_contract_salary.mail_template_send_offer
msgid "Employee: Contract And Salary Package"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_offer_view_search
msgid "Employees"
msgstr "עובדים"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__final_yearly_costs
msgid "Employer Budget"
msgstr ""

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
msgid ""
"Equals to the sum of the following values:\n"
"\n"
"%s"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_personal_information_input
msgid "Existing file:"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_offer__state__expired
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_offer_view_search
msgid "Expired"
msgstr "פג תוקף"

#. module: hr_contract_salary
#: model:hr.contract.salary.benefit,name:hr_contract_salary.benefit_extra_time_off
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__holidays
msgid "Extra Time Off"
msgstr ""

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info.value,name:hr_contract_salary.hr_contract_salary_personal_info_gender_female
msgid "Female"
msgstr "נקבה"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__field
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__field
msgid "Field Name"
msgstr "שם שדה"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_study_field
msgid "Field of Study"
msgstr "תחום לימוד"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__fixed_value
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_resume__value_type__fixed
msgid "Fixed Value"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__fold_res_field_id
msgid "Fold Condition"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__fold_field
msgid "Fold Field Name"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__fold_label
msgid "Fold Label"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__folded
msgid "Folded"
msgstr "מקופל"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__message_follower_ids
msgid "Followers"
msgstr "עוקבים"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__message_partner_ids
msgid "Followers (Partners)"
msgstr "עוקבים (לקוחות/ספקים)"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_offer__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "פונט מדהים למשל עבור משימות fa-tasks"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_offer__state__full_signed
msgid "Fully Signed"
msgstr "חתום במלואו"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_gender
msgid "Gender"
msgstr "מין"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_applicant_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "Generate Offer"
msgstr "צור הצעה"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_benefit_view_search
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_offer_view_search
msgid "Group By"
msgstr "קבץ לפי"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_signatory__signatory__hr
msgid "HR Responsible"
msgstr "אחראי משאבי אנוש"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/models/hr_contract.py:0
msgid ""
"HR Responsible %s should be a user of Sign when New Contract Document "
"Template is specified"
msgstr ""

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/models/hr_contract.py:0
msgid ""
"HR Responsible %s should have a valid email address when New Contract "
"Document Template is specified"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__has_admin_access
msgid "Has Admin Access"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__has_message
msgid "Has Message"
msgstr "יש הודעה"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__helper
msgid "Helper"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_personal_information
msgid "Hide <i class=\"oi oi-chevron-up\"/>"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_value__hide_children
msgid "Hide Children"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_personal_info_value__hide_children
msgid "Hide children personal info when checked."
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit_type__id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit_value__id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer_refusal_reason__id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_type__id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_value__id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume_category__id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_signatory__id
#: model:ir.model.fields,field_description:hr_contract_salary.field_refuse_offer_wizard__id
msgid "ID"
msgstr "מזהה"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__icon
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__activity_exception_icon
msgid "Icon"
msgstr "סמל"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_offer__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "סמל לציון פעילות חריגה."

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_offer__message_needaction
msgid "If checked, new messages require your attention."
msgstr "אם מסומן, הודעות חדשות דורשות את תשומת לבך."

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_offer__message_has_error
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_offer__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "אם מסומן, בחלק מההודעות קיימת שגיאת משלוח."

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_resume__impacts_monthly_total
msgid ""
"If checked, the value of this information will be computed in all "
"information set as Monthly Total"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_benefit__always_show_description
msgid "If unchecked, Description will only be shown when Benefit is selected"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__image_1920
msgid "Image"
msgstr "תמונה"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__image_1920_filename
msgid "Image 1920 Filename"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__impacts_monthly_total
msgid "Impacts Monthly Total"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__impacts_net_salary
msgid "Impacts Net Salary"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_offer__state__open
msgid "In Progress"
msgstr "בתהליך"

#. module: hr_contract_salary
#: model_terms:hr.contract.salary.benefit,description:hr_contract_salary.benefit_extra_time_off
msgid ""
"In addition to your legal leaves, you can chose up to 30 extra days off.\n"
"            The amount of annual time off (legal leaves) you get depends on your work schedule in the previous year. A full-time work schedule through the 12 months of the last year, under your contract, will grant you 20 annual time off (legal leaves)."
msgstr ""

#. module: hr_contract_salary
#. odoo-javascript
#: code:addons/hr_contract_salary/static/src/js/hr_contract_salary.js:0
msgid ""
"In order to choose %s, first you need to choose:\n"
" %s"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__info_type_id
msgid "Info Type"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_personal_info_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_resume_view_form
msgid "Information"
msgstr "מידע"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__display_type__integer
msgid "Integer"
msgstr "מספר שלם"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__message_is_follower
msgid "Is Follower"
msgstr "עוקב"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__is_required
msgid "Is Required"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__is_origin_contract_template
msgid "Is origin contract a contract template?"
msgstr ""

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
#: model:ir.model,name:hr_contract_salary.model_hr_job
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_offer_view_search
msgid "Job Position"
msgstr "תפקיד"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__job_title
msgid "Job Title"
msgstr "שם המשרה"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__dropdown_selection__lang
msgid "Languages"
msgstr "שפות"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__write_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit_type__write_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit_value__write_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__write_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer_refusal_reason__write_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__write_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_type__write_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_value__write_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__write_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume_category__write_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_signatory__write_uid
#: model:ir.model.fields,field_description:hr_contract_salary.field_refuse_offer_wizard__write_uid
msgid "Last Updated by"
msgstr "עודכן לאחרונה על-ידי"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__write_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit_type__write_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit_value__write_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__write_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer_refusal_reason__write_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__write_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_type__write_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_value__write_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__write_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume_category__write_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_signatory__write_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_refuse_offer_wizard__write_date
msgid "Last Updated on"
msgstr "עדכון אחרון ב"

#. module: hr_contract_salary
#: model_terms:ir.actions.act_window,help:hr_contract_salary.action_hr_contract_templates
msgid "Let's create one"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_benefit_value__display_type__line
msgid "Line"
msgstr "שורה"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__url
msgid "Link"
msgstr "קישור"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__position__left
msgid "Main Panel"
msgstr ""

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info.value,name:hr_contract_salary.hr_contract_salary_personal_info_gender_male
msgid "Male"
msgstr "זכר"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__benefit_ids
msgid "Mandatory Benefits"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__manual_field
msgid "Manual Field Name"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_benefit__display_type__manual
msgid "Manual Input"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__manual_res_field_id
msgid "Manual Res Field"
msgstr ""

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info.value,name:hr_contract_salary.hr_contract_salary_personal_info_certificate_master
msgid "Master"
msgstr "תואר שני"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__message_has_error
msgid "Message Delivery error"
msgstr "הודעת שגיאת שליחה"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__message_ids
msgid "Messages"
msgstr "הודעות"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/models/hr_contract_salary_personal_info.py:0
msgid ""
"Mismatch between res_field_id %(field)s and model %(model)s for info "
"%(personal_info)s"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_benefit_type__periodicity__monthly
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_resume_category__periodicity__monthly
msgid "Monthly"
msgstr "חודשי"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_history_view_form
msgid "Monthly Cost"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__monthly_yearly_costs
msgid "Monthly Cost (Real)"
msgstr ""

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
msgid "Monthly Gross Salary"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_resume__value_type__monthly_total
msgid "Monthly Total"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_history__reference_monthly_wage
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_history_view_form
msgid "Monthly Wage"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "מועד אחרון לפעילות שלי"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit_type__name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit_value__name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_type__name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_value__name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__name
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume_category__name
msgid "Name"
msgstr "שם"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_personal_info__res_field_id
msgid "Name of the field related to this personal info."
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "Name or email..."
msgstr "שם או דוא\"ל ..."

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_identification_id
msgid "National Identification Number"
msgstr "מספר מזהה לאומי"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_country_id
msgid "Nationality"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Net calculation"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "New Contract"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_history__sign_template_id
msgid "New Contract Document Template"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__sign_template_id
msgid "New Contract Template"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "הפעילות הבאה ביומן"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "מועד אחרון לפעילות הבאה"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__activity_summary
msgid "Next Activity Summary"
msgstr "תיאור הפעילות הבאה "

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__activity_type_id
msgid "Next Activity Type"
msgstr "סוג הפעילות הבאה"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
msgid "No"
msgstr "לא"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
msgid ""
"No HR responsible defined on the job position. Please contact an "
"administrator."
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.actions.act_window,help:hr_contract_salary.action_hr_contract_templates
msgid "No Template found"
msgstr ""

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/wizard/refuse_offer_wizard.py:0
msgid "No offer selected"
msgstr ""

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
msgid ""
"No signature template defined on the contract. Please contact the HR "
"responsible."
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_benefit_value__selector_highlight__none
msgid "None"
msgstr "אף אחד"

#. module: hr_contract_salary
#. odoo-javascript
#: code:addons/hr_contract_salary/static/src/js/hr_contract_salary.js:0
msgid "Not a valid e-mail address"
msgstr ""

#. module: hr_contract_salary
#. odoo-javascript
#: code:addons/hr_contract_salary/static/src/js/hr_contract_salary.js:0
msgid "Not a valid input in integer field"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__message_needaction_counter
msgid "Number of Actions"
msgstr "מספר פעולות"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract__holidays
msgid "Number of days of paid leaves the employee gets per year."
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__message_has_error_counter
msgid "Number of errors"
msgstr "מספר השגיאות"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_offer__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "מספר הודעות הדורשות פעולה"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_offer__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "מספר הודעות עם שגיאת משלוח"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__offer_create_date
msgid "Offer Create Date"
msgstr ""

#. module: hr_contract_salary
#: model:ir.actions.act_window,name:hr_contract_salary.hr_contract_salary_offer_refusal_reasons
#: model:ir.ui.menu,name:hr_contract_salary.menu_hr_contract_salary_offer_refusal_reasons
msgid "Offer Refusal Reasons"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__offer_end_date
msgid "Offer Validity Date"
msgstr ""

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/models/hr_contract_salary_offer.py:0
msgid "Offer for %(recipient)s"
msgstr ""

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/models/hr_applicant.py:0
msgid ""
"Offer link can not be send. The applicant needs to have a name and email."
msgstr ""

#. module: hr_contract_salary
#: model:ir.actions.act_window,name:hr_contract_salary.hr_contract_salary_offer_action
#: model:ir.actions.act_window,name:hr_contract_salary.hr_contract_salary_offer_recruitment_action
#: model:ir.ui.menu,name:hr_contract_salary.menu_hr_contract_salary_job_offer
#: model:ir.ui.menu,name:hr_contract_salary.menu_salary_package_offer
msgid "Offers"
msgstr "הצעות"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
msgid "Oops"
msgstr "אופס"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__origin_contract_id
msgid "Origin Contract"
msgstr ""

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
msgid "Original Link"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__originated_offer_id
msgid "Originated Offer"
msgstr ""

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info.value,name:hr_contract_salary.hr_contract_salary_personal_info_certificate_other
#: model:hr.contract.salary.personal.info.value,name:hr_contract_salary.hr_contract_salary_personal_info_gender_other
msgid "Other"
msgstr "אחר"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "PDF Template"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__parent_id
msgid "Parent"
msgstr "אב"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_offer__state__half_signed
msgid "Partially Signed"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_signatory__partner_id
msgid "Partner"
msgstr "לקוח/ספק"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__applicant_name
msgid "Partner Name"
msgstr "שם לקוח/ספק"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_benefit__uom__percent
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_resume__uom__percent
msgid "Percent"
msgstr "אחוז"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit_type__periodicity
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume_category__periodicity
msgid "Periodicity"
msgstr "מחזוריות"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_emergency_contact
msgid "Person to call"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "Personal Documents"
msgstr ""

#. module: hr_contract_salary
#: model:ir.actions.act_window,name:hr_contract_salary.hr_contract_salary_personal_info_action
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_value__personal_info_id
#: model:ir.ui.menu,name:hr_contract_salary.salary_package_personal_info
msgid "Personal Info"
msgstr ""

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
msgid "Personal Information"
msgstr ""

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_phone
msgid "Phone Number"
msgstr "מספר טלפון"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_emergency_phone
msgid "Phone number"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_resume__category_id
msgid "Pick a category to display this information"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_resume__value_type
msgid ""
"Pick how the value of the information is computed:\n"
"Fixed value: Set a determined value static for all links\n"
"Contract value: Get the value from a field on the contract record\n"
"Payslip value: Get the value from a field on the payslip record\n"
"Sum of Benefits value: You can pick in all benefits and compute a sum of them\n"
"Monthly Total: The information will be a total of all the informations in the category Monthly Benefits"
msgstr ""

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_place_of_birth
msgid "Place of Birth"
msgstr "מקום לידה"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__placeholder
msgid "Placeholder"
msgstr "שומר מקום"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__position
msgid "Position"
msgstr "מעמד"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "Previous Contract"
msgstr ""

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_private_license_plate
msgid "Private License Plate"
msgstr ""

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/models/hr_applicant.py:0
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_applicant__proposed_contracts
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_applicant_view_form
msgid "Proposed Contracts"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_applicant__proposed_contracts_count
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__contract_reviews_count
msgid "Proposed Contracts Count"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__display_type__radio
msgid "Radio"
msgstr "רדיו"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_benefit__display_type__radio
msgid "Radio Buttons"
msgstr "כפתורי רדיו"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__rating_ids
msgid "Ratings"
msgstr "דירוגים"

#. module: hr_contract_salary
#. odoo-javascript
#: code:addons/hr_contract_salary/static/src/xml/resume_sidebar.xml:0
msgid "Recompute"
msgstr ""

#. module: hr_contract_salary
#: model:mail.template,name:hr_contract_salary.mail_template_send_offer_applicant
msgid "Recruitment: Your Salary Package"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_benefit_value__selector_highlight__red
msgid "Red"
msgstr "אדום"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__refusal_date
msgid "Refusal Date"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__refusal_reason
#: model:ir.model.fields,field_description:hr_contract_salary.field_refuse_offer_wizard__refusal_reason
msgid "Refusal Reason"
msgstr ""

#. module: hr_contract_salary
#: model:ir.actions.server,name:hr_contract_salary.action_refuse_salary_offer
msgid "Refuse"
msgstr "דחה"

#. module: hr_contract_salary
#: model:ir.actions.act_window,name:hr_contract_salary.open_refuse_wizard
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.refuse_offer_wizard_view_form
msgid "Refuse Offer"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_offer_refusal_reasons_view_tree
msgid "Refuse Reason"
msgstr "סיבה לאי קבלה"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_refuse_offer_wizard
msgid "Refuse an Offer"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_offer__state__refused
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_offer_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_offer_view_search
msgid "Refused"
msgstr "נדחה"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__res_field_id
msgid "Related Field"
msgstr "שדה מקושר"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_personal_info_view_form
msgid "Related Model"
msgstr "מודל קשור"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__requested_documents_fields_string
msgid "Requested Documents"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__requested_documents_field_ids
msgid "Requested Documents (IDs)"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__requested_documents
msgid "Requested Documents Fields"
msgstr ""

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/models/hr_contract_salary_offer.py:0
msgid "Requested Signature"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__sign_request_ids
msgid "Requested Signatures"
msgstr "חתימות דרושות"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__res_model
msgid "Res Model"
msgstr "מודל נתונים"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__activity_user_id
msgid "Responsible User"
msgstr "משתמש אחראי"

#. module: hr_contract_salary
#: model:ir.actions.act_window,name:hr_contract_salary.hr_contract_salary_resume_action
#: model:ir.ui.menu,name:hr_contract_salary.salary_package_resume
msgid "Resume"
msgstr "המשך"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_next_step_button
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_sidebar
msgid "Review Contract &amp; Sign"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__message_has_sms_error
msgid "SMS Delivery error"
msgstr "שגיאה בשליחת SMS"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_payroll_structure_type__salary_benefits_ids
msgid "Salary Benefits"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_offer_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.res_config_settings_view_form_hr
msgid "Salary Configurator"
msgstr "מחשבון שכר"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_benefit_view_form
msgid "Salary Configurator Display"
msgstr ""

#. module: hr_contract_salary
#: model:ir.actions.server,name:hr_contract_salary.ir_cron_update_offer_state_ir_actions_server
msgid "Salary Configurator: Update Offer State"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_applicant__salary_offer_ids
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__salary_offer_ids
msgid "Salary Offer"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract_salary_offer_refusal_reason
msgid "Salary Offer Refusal Reasons"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_applicant__salary_offers_count
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_candidate__salary_offers_count
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__salary_offers_count
msgid "Salary Offers Count"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract_salary_benefit
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_benefit_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_benefit_view_tree
msgid "Salary Package Benefit"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.menu,name:hr_contract_salary.salary_package_menu
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.res_config_settings_view_form_hr
msgid "Salary Package Configurator"
msgstr "חבילת השכר"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract_salary_offer
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_offer_view_form
msgid "Salary Package Offer"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_offer_view_tree
msgid "Salary Package Offers"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract_salary_personal_info
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_personal_info_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_personal_info_view_tree
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_resume_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_resume_view_tree
msgid "Salary Package Personal Info"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract_salary_personal_info_type
msgid "Salary Package Personal Info Type"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract_salary_personal_info_value
msgid "Salary Package Personal Info Value"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract_salary_resume
msgid "Salary Package Resume"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract_salary_resume_category
msgid "Salary Package Resume Category"
msgstr ""

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
msgid "Salary Package Summary"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_payroll_structure_type
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__structure_type_id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__structure_type_id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__structure_type_id
msgid "Salary Structure Type"
msgstr "סוג מבנה השכר"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_study_school
msgid "School"
msgstr "מוסד לימודים"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_offer_view_search
msgid "Search Offers"
msgstr "חפש הצעות"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_benefit_value__display_type__section
msgid "Section"
msgstr "סעיף"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,placeholder:hr_contract_salary.hr_contract_salary_personal_info_country
#: model:hr.contract.salary.personal.info,placeholder:hr_contract_salary.hr_contract_salary_personal_info_country_id
#: model:hr.contract.salary.personal.info,placeholder:hr_contract_salary.hr_contract_salary_personal_info_country_of_birth
msgid "Select a Country"
msgstr ""

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,placeholder:hr_contract_salary.hr_contract_salary_personal_info_state_id
msgid "Select a State"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_benefit__benefit_ids
msgid ""
"Select other Benefits that need to be selected to make this Benefit "
"available"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_benefit__cost_res_field_id
msgid "Select the contract's field to consider in salary computation"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_benefit__res_field_id
msgid ""
"Select the contract's field where the value of the benefit will be written"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__display_type__dropdown
msgid "Selection"
msgstr "בחירה"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__dropdown_selection
msgid "Selection Nature"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit_value__selector_highlight
msgid "Selector Highlight"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_sidebar
msgid "Send"
msgstr "שלח"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_offer_view_form
msgid "Send By Email"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__sign_copy_partner_id
msgid "Send a copy to"
msgstr "שליחת עותק ל"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_applicant_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_candidate_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "Sent Offers"
msgstr "שלח הצעות"

#. module: hr_contract_salary
#: model:mail.template,description:hr_contract_salary.mail_template_send_offer_applicant
msgid "Sent automatically when you generate an offer for an application"
msgstr ""

#. module: hr_contract_salary
#: model:mail.template,description:hr_contract_salary.mail_template_send_offer
msgid ""
"Sent manually when you generate a simulation link on the employee contract"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__sequence
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit_type__sequence
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit_value__sequence
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer_refusal_reason__sequence
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__sequence
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_type__sequence
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_value__sequence
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__sequence
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume_category__sequence
msgid "Sequence"
msgstr "רצף"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_personal_information
msgid "Show <i class=\"oi oi-chevron-down\"/>"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__show_name
msgid "Show Name"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__position__right
msgid "Side Panel"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_benefit_view_form
msgid "Sign"
msgstr "חתום"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__sign_frenquency
msgid "Sign Creation Type"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_signatory__order
msgid "Sign Order"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__sign_template_signatories_ids
msgid "Sign Template Signatories"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract_sign_document_wizard
msgid "Sign document in contract"
msgstr "חתום על המסמך בחוזה"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "Signatories"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_signatory__signatory
msgid "Signatory"
msgstr "חותם"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
msgid "Signature Request - %s"
msgstr "בקשת חתימה - %s"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_benefit__display_type__slider
msgid "Slider"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__slider_max
msgid "Slider Max"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__slider_min
msgid "Slider Min"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__slider_step
msgid "Slider Step"
msgstr ""

#. module: hr_contract_salary
#. odoo-javascript
#: code:addons/hr_contract_salary/static/src/js/hr_contract_salary.js:0
msgid "Some required fields are not filled"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_signatory__signatory__partner
msgid "Specific Partner"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__dropdown_selection__specific
msgid "Specific Values"
msgstr ""

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_state_id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__state
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_offer_view_search
msgid "State"
msgstr "סטטוס"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__dropdown_selection__state
msgid "States"
msgstr "מדינות"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_offer__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"סטטוס על בסיס פעילויות\n"
"איחור: תאריך היעד כבר חלף\n"
"היום: תאריך הפעילות הוא היום\n"
"מתוכנן: פעילויות עתידיות."

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_street
msgid "Street"
msgstr "רחוב"

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_street2
msgid "Street 2"
msgstr "רחוב 2"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_benefit_view_search
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_resume_view_search
msgid "Structure Type"
msgstr "סוג מבנה"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_resume__value_type__sum
msgid "Sum of Benefits Values"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__sign_template_id
msgid "Template to Sign"
msgstr "תבנית לחתימה"

#. module: hr_contract_salary
#: model:ir.ui.menu,name:hr_contract_salary.hr_menu_contract_templates
msgid "Templates"
msgstr "תבניות"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_benefit__display_type__text
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_personal_info__display_type__text
msgid "Text"
msgstr "טקסט"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract__origin_contract_id
msgid "The contract from which this contract has been duplicated."
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_benefit_view_form
msgid "The current requested documents are the followings:"
msgstr ""

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
msgid ""
"The employee is not linked to an existing user, please contact the "
"administrator.."
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_benefit__fold_res_field_id
msgid ""
"The field here needs to be set for this benefit to be folded by default."
msgstr ""

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/models/hr_contract_salary_benefit.py:0
msgid ""
"The minimum value for the slider should be inferior to the maximum value."
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract__signatures_count
msgid "The number of signatures on the pdf contract with the most signatures."
msgstr ""

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/models/hr_applicant.py:0
msgid ""
"The offer has been marked as refused when the linked applicant was declined."
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract__originated_offer_id
msgid "The original offer"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_benefit__activity_type_id
msgid ""
"The type of activity that will be created automatically on the contract if "
"this benefit is chosen by the employee."
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_benefits
msgid "There is no available option to customize your salary"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_diff_email_template
msgid "There were no changes since previous contract."
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_thank_you
msgid ""
"They will review your contract.<br/> Feel free to contact them if you have "
"any questions."
msgstr ""

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
msgid ""
"This link is invalid. Please contact the HR Responsible to get a new one..."
msgstr ""

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
msgid "This offer has been updated, please request an updated link.."
msgstr ""

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
msgid "This offer is outdated, please request an updated link..."
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__display_name
msgid "Title"
msgstr "תואר"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_offer_view_form
msgid "Token"
msgstr "אסימון"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract__monthly_yearly_costs
msgid "Total real monthly cost of the employee for the employer."
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract__final_yearly_costs
msgid "Total real yearly cost of the employee for the employer."
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_history__reference_yearly_cost
msgid "Total yearly cost of the employee for the employer."
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_offer__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "סוג הפעילות החריגה ברשומה."

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__uom
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__uom
msgid "Unit of Measure"
msgstr "יחידת מידה"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_signatory__update_contract_template_id
msgid "Update Contract Template"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__validity_days_count
msgid "Validity Days Count"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.res_config_settings_view_form_hr
msgid "Validity duration for salary package requests for employees"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.res_config_settings_view_form
msgid "Validity duration for salary package requests for new applicants"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit__value_ids
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_benefit_value__value
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info__value_ids
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_personal_info_value__value
msgid "Value"
msgstr "ערך"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_resume__value_type
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_resume_view_search
msgid "Value Type"
msgstr ""

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
msgid "Wage"
msgstr "שכר"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__wage_on_signature
msgid "Wage on Payroll"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract__wage_on_signature
msgid "Wage on contract signature"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_history__reference_monthly_wage
msgid "Wage update with holidays retenues"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__wage_with_holidays
msgid "Wage with Holidays"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_salary_offer__website_message_ids
msgid "Website Messages"
msgstr "הודעות מאתר האינטרנט"

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_offer__website_message_ids
msgid "Website communication history"
msgstr "היסטורית התקשרויות מאתר האינטרנט"

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_benefit__activity_creation_type__onchange
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_benefit__sign_frenquency__always
msgid "When the benefit is modified"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_benefit__activity_creation_type__always
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_benefit__sign_frenquency__onchange
msgid "When the benefit is set"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_salary_benefit__show_name
msgid "Whether the name should be displayed in the Salary Configurator"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_sidebar
msgid "Write your message here and we will come back to you."
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_benefit_type__periodicity__yearly
#: model:ir.model.fields.selection,name:hr_contract_salary.selection__hr_contract_salary_resume_category__periodicity__yearly
msgid "Yearly"
msgstr "שנתי"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_history__reference_yearly_cost
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_history_view_form
msgid "Yearly Cost"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__final_yearly_costs
msgid "Yearly Cost (Real)"
msgstr ""

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/controllers/main.py:0
msgid "Yes"
msgstr "כן"

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/models/hr_contract.py:0
msgid "You cannot have multiple person responsible for the same role"
msgstr ""

#. module: hr_contract_salary
#. odoo-python
#: code:addons/hr_contract_salary/models/hr_applicant.py:0
msgid ""
"You have to define contract templates to be used for offers. Go to "
"Configuration / Contract Templates to define a contract template"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_personal_information
msgid "Your Personal Information"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_thank_you
msgid "Your contract has been sent to:"
msgstr ""

#. module: hr_contract_salary
#: model:hr.contract.salary.personal.info,name:hr_contract_salary.hr_contract_salary_personal_info_zip
msgid "Zip Code"
msgstr "מיקוד"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "close"
msgstr "סגור"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_personal_info_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_resume_view_form
msgid "e.g. Birthdate"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_benefit_view_form
msgid "e.g. Meal Vouchers"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.actions.act_window,help:hr_contract_salary.hr_contract_salary_offer_recruitment_action
msgid "here"
msgstr "כאן"

#. module: hr_contract_salary
#: model:mail.template,subject:hr_contract_salary.mail_template_send_offer_applicant
msgid ""
"{{ object.company_id.name }}: Job Offer - {{ "
"object.applicant_id.partner_name }}"
msgstr ""

#. module: hr_contract_salary
#: model:mail.template,subject:hr_contract_salary.mail_template_send_offer
msgid ""
"{{ object.company_id.name }}: Job Offer - {{ "
"object.employee_contract_id.name }}"
msgstr ""
