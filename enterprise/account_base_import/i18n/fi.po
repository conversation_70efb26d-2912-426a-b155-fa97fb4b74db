# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_base_import
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON> <kari.l<PERSON><PERSON>@emsystems.fi>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# Konsta Aavaranta, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:52+0000\n"
"PO-Revision-Date: 2024-09-25 09:43+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.res_config_settings_import_view_form
msgid "<span class=\"text-muted\">(end of year balances)</span>"
msgstr "<span class=\"text-muted\">(vuoden lopun saldot)</span>"

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.res_config_settings_import_view_form
msgid "<span class=\"text-muted\">(for full history)</span>"
msgstr "<span class=\"text-muted\">(koko historia)</span>"

#. module: account_base_import
#: model:ir.model,name:account_base_import.model_account_account
msgid "Account"
msgstr "Tili"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "Account Winbooks Import module"
msgstr "Winbooks-tilien lataamisen moduuli"

#. module: account_base_import
#: model:ir.model,name:account_base_import.model_account_import_summary
msgid "Account import summary view"
msgstr "Tilin tuonnin yhteenvetonäkymä"

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.res_config_settings_import_view_form
msgid "Accounting Import"
msgstr "Kirjanpidon tuonti"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/js/account_import_guide.js:0
#: model:ir.actions.client,name:account_base_import.action_open_import_guide
msgid "Accounting Import Guide"
msgstr "Kirjanpidon tuonnin opas"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "Accounting Import Options"
msgstr "Kirjanpidon tuonnin asetukset"

#. module: account_base_import
#: model:ir.model,name:account_base_import.model_base_import_import
msgid "Base Import"
msgstr "Perustuonti"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/js/account_import_action.js:0
#: model:ir.actions.act_window,name:account_base_import.action_open_coa_setup
msgid "Chart of Accounts"
msgstr "Tilikartta"

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.res_config_settings_import_view_form
msgid "Choose how you want to setup your CoA"
msgstr "Valitse, miten haluat määrittää tilikarttasi"

#. module: account_base_import
#: model:ir.model.fields,field_description:account_base_import.field_account_import_summary__create_uid
msgid "Created by"
msgstr "Luonut"

#. module: account_base_import
#: model:ir.model.fields,field_description:account_base_import.field_account_import_summary__create_date
msgid "Created on"
msgstr "Luotu"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/js/account_import_action.js:0
msgid "Customers"
msgstr "Asiakkaat"

#. module: account_base_import
#: model:ir.model.fields,field_description:account_base_import.field_account_import_summary__display_name
msgid "Display Name"
msgstr "Näyttönimi"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "Download"
msgstr "Lataa"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "Excel Import"
msgstr "Excel-tuonti"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "FEC"
msgstr "FEC"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "FEC Import module"
msgstr "FEC-tuontimoduuli"

#. module: account_base_import
#: model:ir.model.fields,field_description:account_base_import.field_account_import_summary__id
msgid "ID"
msgstr "ID"

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.res_config_settings_import_view_form
#: model_terms:ir.ui.view,arch_db:account_base_import.view_account_base_import_list
#: model_terms:ir.ui.view,arch_db:account_base_import.view_account_setup_base_import_list
msgid "Import"
msgstr "Tuo"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#: model:ir.actions.client,name:account_base_import.action_account_import
msgid "Import Chart of Accounts"
msgstr "Tuo tilikartta"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "Import CoA"
msgstr "Tuo tilikartta"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "Import Contacts"
msgstr "Tuo kontaktit"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#: model:ir.actions.client,name:account_base_import.action_account_move_line_import
msgid "Import Journal Items"
msgstr "Tuo päiväkirjarivit"

#. module: account_base_import
#: model:ir.actions.client,name:account_base_import.action_partner_import
msgid "Import Partners"
msgstr "Tuo kumppanit"

#. module: account_base_import
#. odoo-python
#: code:addons/account_base_import/wizard/account_import_summary.py:0
msgid "Import Summary"
msgstr "Yhteenveto tuonnista"

#. module: account_base_import
#: model:ir.model.fields,field_description:account_base_import.field_account_import_summary__import_summary_account_ids
msgid "Import Summary Account"
msgstr "Tuo yhteenvetotili"

#. module: account_base_import
#: model:ir.model.fields,field_description:account_base_import.field_account_import_summary__import_summary_journal_ids
msgid "Import Summary Journal"
msgstr "Tuo yhteenvetopäiväkirja"

#. module: account_base_import
#: model:ir.model.fields,field_description:account_base_import.field_account_import_summary__import_summary_move_ids
msgid "Import Summary Move"
msgstr "Tuo yhteenvetosiirto"

#. module: account_base_import
#: model:ir.model.fields,field_description:account_base_import.field_account_import_summary__import_summary_name
msgid "Import Summary Name"
msgstr "Tuo yhteenvedon nimi"

#. module: account_base_import
#: model:ir.model.fields,field_description:account_base_import.field_account_import_summary__import_summary_partner_ids
msgid "Import Summary Partner"
msgstr "Tuo yhteenvedon partneri"

#. module: account_base_import
#: model:ir.model.fields,field_description:account_base_import.field_account_import_summary__import_summary_tax_ids
msgid "Import Summary Tax"
msgstr "Tuo yhteenvedon vero"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "Import contacts"
msgstr "Tuo kontaktit"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "Import customers or suppliers (partners) and their contacts using a"
msgstr ""
"Tuo asiakkaita tai toimittajia (yhteistyökumppaneita) ja heidän "
"yhteystietojaan käyttämällä"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "Import the Chart of Accounts and initial balances using a"
msgstr "Tuo tilikartta ja alkusaldot käyttämällä"

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.account_import_summary_form
msgid "Imported Data"
msgstr "Tuodut tiedot"

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.res_config_settings_import_view_form
msgid "Initial Setup"
msgstr "Alkuasetukset"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/js/account_import_guide.js:0
msgid "Install a module"
msgstr "Asenna moduuli"

#. module: account_base_import
#: model:ir.model,name:account_base_import.model_account_move_line
msgid "Journal Item"
msgstr "Päiväkirjatapahtuma"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/js/account_import_action.js:0
msgid "Journal Items"
msgstr "Päiväkirjan tapahtumat"

#. module: account_base_import
#: model:ir.model.fields,field_description:account_base_import.field_account_import_summary__write_uid
msgid "Last Updated by"
msgstr "Viimeksi päivittänyt"

#. module: account_base_import
#: model:ir.model.fields,field_description:account_base_import.field_account_import_summary__write_date
msgid "Last Updated on"
msgstr "Viimeksi päivitetty"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid ""
"Most accounting software in Europe support exporting SAF-T file for audit purposes.\n"
"                            Use the"
msgstr ""
"Useimmat Euroopan kirjanpito-ohjelmistot tukevat SAF-T-tiedoston vientiä tilintarkastusta varten.\n"
"                            Käytä"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid ""
"Most accounting software in France support exporting FEC file for audit purposes.\n"
"                            Use the"
msgstr ""
"Useimmat Ranskan kirjanpito-ohjelmistot tukevat FEC-tiedoston vientiä tilintarkastusta varten.\n"
"                            Käytä"

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.account_import_summary_form
msgid "No data was imported."
msgstr "Tietoja ei tuotu."

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "Optional, but useful to import open receivables & payables using a"
msgstr ""
"Vapaaehtoinen, mutta hyödyllinen avoinna olevien saamisten ja velkojen "
"tuominen käyttämällä"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#: model_terms:ir.ui.view,arch_db:account_base_import.res_config_settings_import_view_form
msgid "Review Manually"
msgstr "Tarkista manuaalisesti"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "SAF-T"
msgstr "SAF-T"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "SAF-T Import module"
msgstr "SAF-T-tuontimoduuli"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "SIE 5"
msgstr "SIE 5"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "SIE Import module"
msgstr "SIE Tuontimoduuli"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid ""
"The SIE standard file format is very common in Sweden for several purposes "
"such as auditing, importing and exporting data from and to other accounting "
"softwares."
msgstr ""
"SIE-standarditiedostomuoto on hyvin yleinen Ruotsissa useisiin "
"tarkoituksiin, kuten tilintarkastukseen sekä tietojen tuomiseen ja viemiseen"
" muista kirjanpito-ohjelmista ja muihin kirjanpito-ohjelmiin."

#. module: account_base_import
#. odoo-python
#: code:addons/account_base_import/models/account_move_line.py:0
msgid "The import file is missing the following required columns: %s"
msgstr "Tuontitiedostosta puuttuvat seuraavat vaaditut sarakkeet: %s"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid ""
"Tip: we recommend importing your initial balances using the Chart of Account"
" import. Only use the Journal Items import for unreconciled entries in your "
"Payable and Receivable Accounts."
msgstr ""
"Vinkki: suosittelemme alkuperäisten saldojen tuomista tilikarttatuonnilla. "
"Käytä Päiväkirjakohtien tuontia vain maksettavien ja saatavien tilien "
"täsmäyttämättömiin kirjauksiin."

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "Use predefined format to import your data faster."
msgstr "Käytä ennalta määriteltyä muotoa, jotta voit tuoda tietosi nopeammin."

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "Use templates to import CSV or Excel for your accounting setup."
msgstr ""
"Käytä malleja CSV- tai Excel-tuontia varten kirjanpidon asetuksia varten."

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid ""
"We will setup your charts of accounts and the history of journal entries, "
"that will stay in draft."
msgstr ""
"Määritämme tilikartat ja päiväkirjamerkintöjen historian, joka pysyy "
"luonnoksena."

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "Winbooks"
msgstr "Winbooks"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid ""
"Winbooks is an old school Belgian accounting software acquired by Exact.\n"
"                            Use the"
msgstr ""
"Winbooks on vanha belgialainen kirjanpito-ohjelmisto, jonka Exact on ostanut.\n"
"                            Käytä"

#. module: account_base_import
#. odoo-python
#: code:addons/account_base_import/models/account_account.py:0
msgid ""
"You must provide both the `code_mapping_ids/company_id` and the "
"`code_mapping_ids/code` columns."
msgstr ""
"Sinun on annettava sekä \"code_mapping_ids/company_id\"- että "
"\"code_mapping_ids/code\"-sarakkeet."

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.account_import_summary_form
msgid "accounts imported"
msgstr "tuodut tilit"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid ""
"is required to import the SIE and SIE entry files.\n"
"                            For general SIE, we will setup your charts of accounts balances, journals, partners, and the history of journal entries (journals data must be present in the file).\n"
"                            For the SIE entry, only entries and partners will be created, the rest must already be present in the system."
msgstr ""
"tarvitaan SIE- ja SIE-merkintätiedostojen tuomiseen.\n"
"                            Yleistä SIE:tä varten asetamme tilikarttasi saldot, päiväkirjat, kumppanit ja päiväkirjamerkintöjen historian (päiväkirjatietojen on oltava tiedostossa).\n"
"                            SIE-merkintää varten luodaan vain kirjaukset ja kumppanit, muiden tietojen on oltava jo järjestelmässä."

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.account_import_summary_form
msgid "journals imported"
msgstr "tuodut päiväkirjat"

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.account_import_summary_form
msgid "moves imported"
msgstr "tuodut siirrot"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "or"
msgstr "tai"

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.account_import_summary_form
msgid "partners imported"
msgstr "tuodut kumppanit"

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.account_import_summary_form
msgid "taxes imported"
msgstr "tuodut verot"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "template."
msgstr "pohja."

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid ""
"to import a Winbooks full back-up (Maintenance > Backup) to get the chart of accounts, contacts, taxes, history of journal entries, and documents.\n"
"                            Support versions: Winbooks Desktop 5.50, 6, 7, 8."
msgstr ""
"tuo Winbooksin täydellinen varmuuskopio (Ylläpito > Varmuuskopio), jotta saat tilikartan, yhteystiedot, verot, päiväkirjamerkintähistorian ja asiakirjat.\n"
"                            Tuetut versiot: Winbooks Desktop 5.50, 6, 7, 8."

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid ""
"to import the FEC file. We will setup your charts of accounts and the "
"history of journal entries."
msgstr ""
"tuo FEC-tiedosto. Määritämme tilikartat ja päiväkirjamerkintöjen historia."

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "to import the SAF-T file."
msgstr "tuodaan SAF-T-tiedosto."
