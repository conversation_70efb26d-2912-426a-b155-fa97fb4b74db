# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_be_hr_payroll_fleet
#
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON><PERSON>, 2024
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-11-20 08:49+0000\n"
"PO-Revision-Date: 2024-01-30 08:20+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"Language: nl\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: l10n_be_hr_payroll_fleet
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll_fleet.hr_contract_view_form
msgid "(Fuel Type:"
msgstr "(Brandstoftype:"

#. module: l10n_be_hr_payroll_fleet
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll_fleet.dmfa_xml_report
msgid "862"
msgstr "862"

#. module: l10n_be_hr_payroll_fleet
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll_fleet.fleet_vehicle_model_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll_fleet.fleet_vehicle_view_form
msgid "<span> / month</span>"
msgstr "<span> / maand</span>"

#. module: l10n_be_hr_payroll_fleet
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll_fleet.res_config_settings_view_form
msgid "<span> existing cars availables.</span>"
msgstr "<span> beschikbare bestaande voertuigen.</span>"

#. module: l10n_be_hr_payroll_fleet
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll_fleet.fleet_vehicle_view_form
msgid "<span>/ month</span>"
msgstr "<span>/ maand</span>"

#. module: l10n_be_hr_payroll_fleet
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll_fleet.res_config_settings_view_form
msgid ""
"<span>Display a warning in the salary configurator if we have more than</"
"span>"
msgstr ""
"<span>Een waarschuwing weergeven in de salarisconfigurator bij meer dan</"
"span>"

#. module: l10n_be_hr_payroll_fleet
#: model:hr.salary.rule,name:l10n_be_hr_payroll_fleet.cp200_employees_salary_co2_fee
msgid "Accounting: CO2 Fee (Employer)"
msgstr "Boekhouding: CO2-vergoeding (Werkgever)"

#. module: l10n_be_hr_payroll_fleet
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_fleet.field_fleet_vehicle__atn
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_fleet.field_fleet_vehicle_model__default_atn
msgid "BIK"
msgstr "VAA"

#. module: l10n_be_hr_payroll_fleet
#: model:ir.model.fields,help:l10n_be_hr_payroll_fleet.field_hr_contract__car_atn
msgid "Benefit in Kind (Company Car)"
msgstr "Voordeel alle aard (Bedrijfswagen)"

#. module: l10n_be_hr_payroll_fleet
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_fleet.field_fleet_vehicle_model__default_co2
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_fleet.field_hr_contract__co2
msgid "CO2 Emissions"
msgstr "CO2-uitstoot"

#. module: l10n_be_hr_payroll_fleet
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll_fleet.hr_salary_rule_category_co2_fee
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_fleet.field_fleet_vehicle__co2_fee
msgid "CO2 Fee"
msgstr "CO2-vergoeding"

#. module: l10n_be_hr_payroll_fleet
#: model:ir.model.fields,help:l10n_be_hr_payroll_fleet.field_hr_contract__co2
msgid "CO2 emissions of the vehicle"
msgstr "CO2-uitstoot van het voertuig"

#. module: l10n_be_hr_payroll_fleet
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_fleet.field_fleet_vehicle_model__co2_fee
msgid "CO2 fee"
msgstr "CO2-vergoeding"

#. module: l10n_be_hr_payroll_fleet
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll_fleet.fleet_vehicle_model_view_search
msgid "Can Be Requested"
msgstr "Kan aangevraagd worden"

#. module: l10n_be_hr_payroll_fleet
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_fleet.field_fleet_vehicle_model__can_be_requested
msgid "Can be requested"
msgstr "Kan aangevraagd worden"

#. module: l10n_be_hr_payroll_fleet
#: model:ir.model.fields,help:l10n_be_hr_payroll_fleet.field_fleet_vehicle_model__can_be_requested
msgid "Can be requested on a contract as a new vehicle"
msgstr "Kan aangevraagd worden op een contract als een nieuw voertuig"

#. module: l10n_be_hr_payroll_fleet
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_fleet.field_hr_contract__car_atn
msgid "Car BIK"
msgstr "VAA Auto"

#. module: l10n_be_hr_payroll_fleet
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_fleet.field_hr_contract__car_model_name
msgid "Car Model Name"
msgstr "Modelnaam auto"

#. module: l10n_be_hr_payroll_fleet
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_fleet.field_hr_contract__car_open_contracts_count
msgid "Car Open Contracts Count"
msgstr "Aantal lopende contracten auto"

#. module: l10n_be_hr_payroll_fleet
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll_fleet.hr_contract_view_form
msgid "Catalog Company Bike"
msgstr "Catalogus bedrijfsfiets"

#. module: l10n_be_hr_payroll_fleet
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll_fleet.hr_contract_view_form
msgid "Catalog Company Car"
msgstr "Catalogus bedrijfswagen"

#. module: l10n_be_hr_payroll_fleet
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_fleet.field_fleet_vehicle_model__default_car_value
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_fleet.field_hr_contract__car_value
msgid "Catalog Value (VAT Incl.)"
msgstr "Cataloguswaarde (Incl. BTW)"

#. module: l10n_be_hr_payroll_fleet
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_fleet.field_hr_contract__bike_id
msgid "Company Bike"
msgstr "Bedrijfsfiets"

#. module: l10n_be_hr_payroll_fleet
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_fleet.field_hr_contract__company_bike_depreciated_cost
msgid "Company Bike Depreciated Cost"
msgstr "Afgeschreven kosten bedrijfsfiets"

#. module: l10n_be_hr_payroll_fleet
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_fleet.field_hr_contract__car_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_fleet.field_hr_payslip__vehicle_id
msgid "Company Car"
msgstr "Bedrijfswagen"

#. module: l10n_be_hr_payroll_fleet
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_fleet.field_hr_contract__company_car_total_depreciated_cost
msgid "Company Car Total Depreciated Cost"
msgstr "Bedrijfswagen Totaal afgeschreven Kosten"

#. module: l10n_be_hr_payroll_fleet
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll_fleet.report_belgium_payslip
msgid "Company Car:"
msgstr "Bedrijfswagen:"

#. module: l10n_be_hr_payroll_fleet
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll_fleet.dmfa_pdf_report
msgid "Company Cars Global Contributions:"
msgstr "Globale contributies bedrijfswagens:"

#. module: l10n_be_hr_payroll_fleet
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll_fleet.dmfa_pdf_report
msgid "Company Cars List"
msgstr "Lijst bedrijfswagens"

#. module: l10n_be_hr_payroll_fleet
#: model:ir.model,name:l10n_be_hr_payroll_fleet.model_res_config_settings
msgid "Config Settings"
msgstr "Configuratie instellingen"

#. module: l10n_be_hr_payroll_fleet
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_fleet.field_fleet_vehicle_model__default_recurring_cost_amount_depreciated
msgid "Cost (Depreciated)"
msgstr "Kosten (afgeschreven)"

#. module: l10n_be_hr_payroll_fleet
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_fleet.field_fleet_vehicle_model__current_country_code
msgid "Current Country Code"
msgstr "Huidige landcode"

#. module: l10n_be_hr_payroll_fleet
#: model:ir.model,name:l10n_be_hr_payroll_fleet.model_l10n_be_dmfa
msgid "DMFA xml report"
msgstr "DMFA xml report"

#. module: l10n_be_hr_payroll_fleet
#: model:ir.model.fields,help:l10n_be_hr_payroll_fleet.field_fleet_vehicle__acquisition_date
#: model:ir.model.fields,help:l10n_be_hr_payroll_fleet.field_hr_contract__acquisition_date
msgid "Date of vehicle registration"
msgstr "Datum eerste inschrijving voertuig"

#. module: l10n_be_hr_payroll_fleet
#: model:ir.model.fields,help:l10n_be_hr_payroll_fleet.field_fleet_vehicle_model__default_recurring_cost_amount_depreciated
msgid ""
"Default recurring cost amount that should be applied to a new vehicle from "
"this model"
msgstr ""
"Standaard terugkerende kostenbedrag dat moet worden toegapast op een nieuw "
"voertuig van dit model"

#. module: l10n_be_hr_payroll_fleet
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_fleet.field_fleet_vehicle_log_contract__recurring_cost_amount_depreciated
msgid "Depreciated Cost Amount"
msgstr "Bedrag afgeschreven kosten"

#. module: l10n_be_hr_payroll_fleet
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_fleet.field_hr_contract__driver_id
msgid "Driver"
msgstr "Bestuurder"

#. module: l10n_be_hr_payroll_fleet
#: model:ir.model.fields,help:l10n_be_hr_payroll_fleet.field_hr_contract__driver_id
msgid "Driver address of the vehicle"
msgstr "Bestuurdersadres van het voertuig"

#. module: l10n_be_hr_payroll_fleet
#: model:ir.model,name:l10n_be_hr_payroll_fleet.model_hr_contract
msgid "Employee Contract"
msgstr "Arbeidsovereenkomst"

#. module: l10n_be_hr_payroll_fleet
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_fleet.field_fleet_vehicle__fuel_type
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_fleet.field_hr_contract__fuel_type
msgid "Fuel Type"
msgstr "Brandstoftype"

#. module: l10n_be_hr_payroll_fleet
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_fleet.field_hr_contract__max_unused_cars
msgid "Max Unused Cars"
msgstr "Maximum ongebruikte auto's"

#. module: l10n_be_hr_payroll_fleet
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_fleet.field_res_config_settings__max_unused_cars
msgid "Maximum unused cars"
msgstr "Maximum ongebruikte auto's"

#. module: l10n_be_hr_payroll_fleet
#: model:ir.model,name:l10n_be_hr_payroll_fleet.model_fleet_vehicle_model
msgid "Model of a vehicle"
msgstr "Model van een voertuig"

#. module: l10n_be_hr_payroll_fleet
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_fleet.field_hr_contract__new_bike_model_id
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll_fleet.hr_contract_view_form
msgid "New Company Bike"
msgstr "Nieuwe bedrijfsfiets"

#. module: l10n_be_hr_payroll_fleet
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_fleet.field_hr_contract__new_car_model_id
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll_fleet.hr_contract_view_form
msgid "New Company Car"
msgstr "Nieuwe bedrijfswagen"

#. module: l10n_be_hr_payroll_fleet
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll_fleet.res_config_settings_view_form
msgid "New Vehicle Request"
msgstr "Nieuwe aanvraag voertuig"

#. module: l10n_be_hr_payroll_fleet
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_fleet.field_hr_contract__available_cars_amount
msgid "Number of available cars"
msgstr "Aantal beschikbare auto's"

#. module: l10n_be_hr_payroll_fleet
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_fleet.field_hr_contract__ordered_car_id
msgid "Ordered New Car"
msgstr "Nieuwe auto besteld"

#. module: l10n_be_hr_payroll_fleet
#: model:ir.model,name:l10n_be_hr_payroll_fleet.model_hr_payslip
msgid "Pay Slip"
msgstr "Loonstrook"

#. module: l10n_be_hr_payroll_fleet
#: model:hr.payroll.dashboard.warning,name:l10n_be_hr_payroll_fleet.hr_payroll_dashboard_warning_car_mismatch
msgid "Payslips with car different than on employee's contract"
msgstr "Loonstroken met andere auto's dan op de werknemersovereenkomst"

#. module: l10n_be_hr_payroll_fleet
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_fleet.field_fleet_vehicle_log_contract__cost_generated
msgid "Recurring Cost"
msgstr "Terugkerende kosten"

#. module: l10n_be_hr_payroll_fleet
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_fleet.field_hr_contract__recurring_cost_amount_depreciated
msgid "Recurring Cost Amount Depreciated"
msgstr "Afgeschreven bedrag terugkerende kosten"

#. module: l10n_be_hr_payroll_fleet
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_fleet.field_fleet_vehicle__acquisition_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_fleet.field_hr_contract__acquisition_date
msgid "Registration Date"
msgstr "Inschrijvingsdatum"

#. module: l10n_be_hr_payroll_fleet
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_fleet.field_hr_contract__new_bike
msgid "Requested a new bike"
msgstr "Een nieuwe fiets aangevraagd"

#. module: l10n_be_hr_payroll_fleet
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_fleet.field_hr_contract__new_car
msgid "Requested a new car"
msgstr "Een nieuwe auto aangevraagd"

#. module: l10n_be_hr_payroll_fleet
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll_fleet.fleet_vehicle_model_view_form
msgid "Salary"
msgstr "Loon"

#. module: l10n_be_hr_payroll_fleet
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_fleet.field_fleet_vehicle__tax_deduction
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_fleet.field_fleet_vehicle_model__tax_deduction
msgid "Tax Deduction"
msgstr "Belastingaftrek"

#. module: l10n_be_hr_payroll_fleet
#. odoo-python
#: code:addons/l10n_be_hr_payroll_fleet/models/hr_dmfa.py:0
msgid ""
"The following license plates are invalid:\n"
"%s"
msgstr ""
"De volgende nummerplaten zijn ongeldig:\n"
"%s"

#. module: l10n_be_hr_payroll_fleet
#: model:ir.model.fields,help:l10n_be_hr_payroll_fleet.field_fleet_vehicle__total_cost
msgid "This include all the costs and the CO2 fee"
msgstr "Dit omvat alle kosten en de CO2-vergoeding"

#. module: l10n_be_hr_payroll_fleet
#: model:ir.model.fields,help:l10n_be_hr_payroll_fleet.field_fleet_vehicle__total_depreciated_cost
msgid "This includes all the depreciated costs and the CO2 fee"
msgstr "Dit omvat alle afgeschreven kosten en de CO2-vergoeding"

#. module: l10n_be_hr_payroll_fleet
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_fleet.field_fleet_vehicle__total_cost
msgid "Total Cost"
msgstr "Totale kosten"

#. module: l10n_be_hr_payroll_fleet
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_fleet.field_fleet_vehicle__total_depreciated_cost
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_fleet.field_fleet_vehicle_model__default_total_depreciated_cost
msgid "Total Cost (Depreciated)"
msgstr "Totale kosten (afgeschreven)"

#. module: l10n_be_hr_payroll_fleet
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_fleet.field_hr_contract__transport_mode_bike
msgid "Uses Bike"
msgstr "Maakt gebruikt van een fiets"

#. module: l10n_be_hr_payroll_fleet
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_fleet.field_hr_contract__transport_mode_private_car
msgid "Uses private car"
msgstr "Maakt gebruik van een privéauto"

#. module: l10n_be_hr_payroll_fleet
#: model:ir.model,name:l10n_be_hr_payroll_fleet.model_fleet_vehicle
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_fleet.field_l10n_be_dmfa__vehicle_ids
msgid "Vehicle"
msgstr "Voertuig"

#. module: l10n_be_hr_payroll_fleet
#: model:ir.model,name:l10n_be_hr_payroll_fleet.model_fleet_vehicle_log_contract
msgid "Vehicle Contract"
msgstr "Voertuigcontract"

#. module: l10n_be_hr_payroll_fleet
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll_fleet.res_config_settings_view_form
msgid ""
"When the limit is reached, all offers generated for employees will uncheck "
"the 'Show Company Car to Order' by default."
msgstr ""
"Als de limiet is bereikt, wordt standaard de optie 'Toon bedrijfsauto bij "
"bestelling' uitgeschakeld bij alle gegenereerde offertes voor werknemers."

#. module: l10n_be_hr_payroll_fleet
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_fleet.field_hr_contract__wishlist_car_total_depreciated_cost
msgid "Wishlist Car Total Depreciated Cost"
msgstr "Totaal afgeschreven kosten auto op verlanglijst"
