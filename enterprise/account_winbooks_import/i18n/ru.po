# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_winbooks_import
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:26+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: account_winbooks_import
#: model_terms:ir.ui.view,arch_db:account_winbooks_import.winbooks_import_form
msgid ""
"<span/>\n"
"                    <span class=\"text-warning mb4 mt16\" invisible=\"only_open\">\n"
"                        The export of data from Winbooks for closed years might contain unbalanced entries. However if you want to try to import everything, Odoo will set the difference of balance in a Suspense Account.\n"
"                    </span>"
msgstr ""
"<span/>\n"
"                    <span class=\"text-warning mb4 mt16\" invisible=\"only_open\">\n"
"                        Экспорт данных из Winbooks за закрытые годы может содержать несбалансированные записи. Однако если вы хотите попытаться импортировать все, Odoo установит разницу в балансе на промежуточном счете.\n"
"                    </span>"

#. module: account_winbooks_import
#: model:ir.model,name:account_winbooks_import.model_account_winbooks_import_wizard
msgid "Account Winbooks import wizard"
msgstr "Мастер импорта учетных записей Winbooks"

#. module: account_winbooks_import
#: model:ir.model,name:account_winbooks_import.model_account_import_summary
msgid "Account import summary view"
msgstr ""

#. module: account_winbooks_import
#. odoo-python
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
msgid "Accounting Settings"
msgstr "Бухгалтерские настройки"

#. module: account_winbooks_import
#. odoo-python
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
msgid ""
"At least one automatic counterpart has been created at import. This is "
"probably an error. Please check entry lines with reference: Counterpart "
"(generated at import from Winbooks)"
msgstr ""

#. module: account_winbooks_import
#: model_terms:ir.ui.view,arch_db:account_winbooks_import.winbooks_import_form
msgid "Cancel"
msgstr "Отменить"

#. module: account_winbooks_import
#: model:ir.model,name:account_winbooks_import.model_res_company
msgid "Companies"
msgstr "Компании"

#. module: account_winbooks_import
#. odoo-python
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
msgid "Company Settings"
msgstr "Настройки компании"

#. module: account_winbooks_import
#. odoo-python
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
msgid "Counterpart (generated at import from Winbooks)"
msgstr "Контрагент (генерируется при импорте из Winbooks)"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard__create_uid
msgid "Created by"
msgstr "Создано"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard__create_date
msgid "Created on"
msgstr "Создано"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard__display_name
msgid "Display Name"
msgstr "Отображаемое имя"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard__zip_file
msgid "File"
msgstr "Файл"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard__id
msgid "ID"
msgstr "ID"

#. module: account_winbooks_import
#: model_terms:ir.ui.view,arch_db:account_winbooks_import.winbooks_import_form
msgid "Import"
msgstr "Импорт"

#. module: account_winbooks_import
#: model_terms:ir.ui.view,arch_db:account_winbooks_import.winbooks_import_form
msgid "Import (safe-mode)"
msgstr "Импорт (безопасный режим)"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_import_summary__import_summary_analytic_ids
msgid "Import Summary Analytic"
msgstr ""

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_import_summary__import_summary_analytic_line_ids
msgid "Import Summary Analytic Line"
msgstr ""

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_import_summary__import_summary_len_analytic
msgid "Import Summary Len Analytic"
msgstr ""

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_import_summary__import_summary_len_analytic_line
msgid "Import Summary Len Analytic Line"
msgstr ""

#. module: account_winbooks_import
#. odoo-javascript
#: code:addons/account_winbooks_import/static/src/xml/account_winbooks_import.xml:0
msgid "Import WBK"
msgstr "Импорт WBK"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard__only_open
msgid "Import only open years"
msgstr "Импорт только открытых лет"

#. module: account_winbooks_import
#: model:ir.model,name:account_winbooks_import.model_account_move_line
msgid "Journal Item"
msgstr "Элемент журнала"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard__write_uid
msgid "Last Updated by"
msgstr "Последнее обновление"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard__write_date
msgid "Last Updated on"
msgstr "Последнее обновление"

#. module: account_winbooks_import
#: model:ir.model.fields,help:account_winbooks_import.field_account_move_line__winbooks_line_id
msgid "Line ID that was used in Winbooks"
msgstr "Идентификатор линии, который использовался в Winbooks"

#. module: account_winbooks_import
#. odoo-python
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
msgid ""
"No data zip in the main archive. Please use the complete Winbooks export."
msgstr "В архиве нет файла с данными. Используйте полный экспорт Winbooks."

#. module: account_winbooks_import
#. odoo-python
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
msgid "Please define the country on your company."
msgstr "Пожалуйста, определите страну, в которой находится ваша компания."

#. module: account_winbooks_import
#: model_terms:ir.ui.view,arch_db:account_winbooks_import.winbooks_import_form
msgid "Stage Search"
msgstr "Поиск этапов"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard__suspense_code
msgid "Suspense Account Code"
msgstr "Код промежуточного счета"

#. module: account_winbooks_import
#. odoo-python
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
msgid ""
"The code for the Suspense Account you entered doesn't match any account"
msgstr ""
"Введенный вами код приостановленного счета не совпадает ни с одним счетом"

#. module: account_winbooks_import
#. odoo-python
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
msgid ""
"The following banks were used for multiple partners in Winbooks, which is not allowed in Odoo. The bank number has been only set on one of each group:\n"
"%s"
msgstr ""
"Следующие банки были использованы для нескольких партнеров в Winbooks, что не разрешено в Odoo. Номер банка был установлен только для одного из каждой группы:\n"
"%s"

#. module: account_winbooks_import
#: model:ir.model.fields,help:account_winbooks_import.field_account_winbooks_import_wizard__suspense_code
msgid ""
"This is the code of the account in which you want to put the counterpart of "
"unbalanced moves. This might be an account from your Winbooks data, or an "
"account that you created in Odoo before the import."
msgstr ""
"Это код счета, в который вы хотите поместить контрагент несбалансированных "
"движений. Это может быть счет из ваших данных Winbooks или счет, который вы "
"создали в Odoo до импорта."

#. module: account_winbooks_import
#: model:ir.actions.act_window,name:account_winbooks_import.winbooks_import_action
msgid "Winbooks Import"
msgstr "Winbooks Import"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_move_line__winbooks_line_id
msgid "Winbooks Line"
msgstr "Линия Winbooks"

#. module: account_winbooks_import
#: model:ir.model.fields,help:account_winbooks_import.field_account_winbooks_import_wizard__only_open
msgid ""
"Years closed in Winbooks are likely to have incomplete data. The counter "
"part of incomplete entries will be set in a suspense account"
msgstr ""
"Годы, закрытые в Winbooks, скорее всего, будут содержать неполные данные. "
"Контрчасть неполных записей будет установлена на промежуточном счете"

#. module: account_winbooks_import
#. odoo-python
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
msgid "You should install a Fiscal Localization first."
msgstr "Сначала необходимо установить фискальную локализацию."

#. module: account_winbooks_import
#: model_terms:ir.ui.view,arch_db:account_winbooks_import.account_import_summary_form
msgid "account analytic lines imported"
msgstr ""

#. module: account_winbooks_import
#: model_terms:ir.ui.view,arch_db:account_winbooks_import.account_import_summary_form
msgid "account analytics imported"
msgstr ""
