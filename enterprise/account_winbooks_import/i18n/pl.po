# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_winbooks_import
# 
# Translators:
# <PERSON>il <PERSON>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:26+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Polish (https://app.transifex.com/odoo/teams/41243/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

#. module: account_winbooks_import
#: model_terms:ir.ui.view,arch_db:account_winbooks_import.winbooks_import_form
msgid ""
"<span/>\n"
"                    <span class=\"text-warning mb4 mt16\" invisible=\"only_open\">\n"
"                        The export of data from Winbooks for closed years might contain unbalanced entries. However if you want to try to import everything, Odoo will set the difference of balance in a Suspense Account.\n"
"                    </span>"
msgstr ""
"<span/>\n"
"                    <span class=\"text-warning mb4 mt16\" invisible=\"only_open\">\n"
"                        Eksport danych z Winbooks dla zamkniętych lat może zawierać niezbilansowane wpisy. Jeśli jednak chcesz spróbować zaimportować wszystko, Odoo ustawi różnicę salda na koncie przejściowym\n"
"                    </span>"

#. module: account_winbooks_import
#: model:ir.model,name:account_winbooks_import.model_account_winbooks_import_wizard
msgid "Account Winbooks import wizard"
msgstr "Kreator importu kont Winbooks"

#. module: account_winbooks_import
#: model:ir.model,name:account_winbooks_import.model_account_import_summary
msgid "Account import summary view"
msgstr "Widok podsumowania importu konta"

#. module: account_winbooks_import
#. odoo-python
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
msgid "Accounting Settings"
msgstr "Ustawienia księgowości"

#. module: account_winbooks_import
#. odoo-python
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
msgid ""
"At least one automatic counterpart has been created at import. This is "
"probably an error. Please check entry lines with reference: Counterpart "
"(generated at import from Winbooks)"
msgstr ""
"Podczas importu utworzono co najmniej jeden automatyczny odpowiednik. "
"Prawdopodobnie jest to wynikiem błędu. Sprawdź wiersze wpisu z referencją: "
"Odpowiednik (wygenerowany podczas importu z Winbooks)"

#. module: account_winbooks_import
#: model_terms:ir.ui.view,arch_db:account_winbooks_import.winbooks_import_form
msgid "Cancel"
msgstr "Anuluj"

#. module: account_winbooks_import
#: model:ir.model,name:account_winbooks_import.model_res_company
msgid "Companies"
msgstr "Firmy"

#. module: account_winbooks_import
#. odoo-python
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
msgid "Company Settings"
msgstr "Ustawienia firmy"

#. module: account_winbooks_import
#. odoo-python
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
msgid "Counterpart (generated at import from Winbooks)"
msgstr "Duplikat (wygenerowany przy imporcie z Winbooks)"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard__create_uid
msgid "Created by"
msgstr "Utworzono przez"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard__create_date
msgid "Created on"
msgstr "Data utworzenia"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard__display_name
msgid "Display Name"
msgstr "Nazwa wyświetlana"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard__zip_file
msgid "File"
msgstr "Plik"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard__id
msgid "ID"
msgstr "ID"

#. module: account_winbooks_import
#: model_terms:ir.ui.view,arch_db:account_winbooks_import.winbooks_import_form
msgid "Import"
msgstr "Importuj"

#. module: account_winbooks_import
#: model_terms:ir.ui.view,arch_db:account_winbooks_import.winbooks_import_form
msgid "Import (safe-mode)"
msgstr "Importuj (tryb bezpieczny)"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_import_summary__import_summary_analytic_ids
msgid "Import Summary Analytic"
msgstr "Import podsumowania analitycznego"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_import_summary__import_summary_analytic_line_ids
msgid "Import Summary Analytic Line"
msgstr "Import wiersza podsumowania analitycznego"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_import_summary__import_summary_len_analytic
msgid "Import Summary Len Analytic"
msgstr "Import podsumowania analitycznego len"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_import_summary__import_summary_len_analytic_line
msgid "Import Summary Len Analytic Line"
msgstr "Import wiersza podsumowania analitycznego len"

#. module: account_winbooks_import
#. odoo-javascript
#: code:addons/account_winbooks_import/static/src/xml/account_winbooks_import.xml:0
msgid "Import WBK"
msgstr "Importuj WBK"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard__only_open
msgid "Import only open years"
msgstr "Importuj tylko otwarte lata"

#. module: account_winbooks_import
#: model:ir.model,name:account_winbooks_import.model_account_move_line
msgid "Journal Item"
msgstr "Pozycja dziennika"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard__write_uid
msgid "Last Updated by"
msgstr "Ostatnio zaktualizowane przez"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard__write_date
msgid "Last Updated on"
msgstr "Data ostatniej aktualizacji"

#. module: account_winbooks_import
#: model:ir.model.fields,help:account_winbooks_import.field_account_move_line__winbooks_line_id
msgid "Line ID that was used in Winbooks"
msgstr "ID wiersza użytego w Winbooks"

#. module: account_winbooks_import
#. odoo-python
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
msgid ""
"No data zip in the main archive. Please use the complete Winbooks export."
msgstr "Brak zipu danych w głównym archiwum. Użyj pełnego eksportu Winbooks."

#. module: account_winbooks_import
#. odoo-python
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
msgid "Please define the country on your company."
msgstr "Proszę określić kraj swojej firmy."

#. module: account_winbooks_import
#: model_terms:ir.ui.view,arch_db:account_winbooks_import.winbooks_import_form
msgid "Stage Search"
msgstr "Szukaj etapu"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard__suspense_code
msgid "Suspense Account Code"
msgstr "Numer konta przejściowego"

#. module: account_winbooks_import
#. odoo-python
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
msgid ""
"The code for the Suspense Account you entered doesn't match any account"
msgstr "Podany kod konta przejściowego nie pasuje do żadnego konta"

#. module: account_winbooks_import
#. odoo-python
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
msgid ""
"The following banks were used for multiple partners in Winbooks, which is not allowed in Odoo. The bank number has been only set on one of each group:\n"
"%s"
msgstr ""
"W Winbooks użyto następujących banków dla więcej niż jednego partnera, co nie jest dozwolone w Odoo. Numer banku został ustawiony tylko na jednym z każdej grupy:\n"
"%s"

#. module: account_winbooks_import
#: model:ir.model.fields,help:account_winbooks_import.field_account_winbooks_import_wizard__suspense_code
msgid ""
"This is the code of the account in which you want to put the counterpart of "
"unbalanced moves. This might be an account from your Winbooks data, or an "
"account that you created in Odoo before the import."
msgstr ""
"To jest numer konta, na którym chcesz umieścić duplikat niezbilansowanych "
"ruchów. Może to być konto z danych Winbooks lub konto utworzone w Odoo przed"
" importem."

#. module: account_winbooks_import
#: model:ir.actions.act_window,name:account_winbooks_import.winbooks_import_action
msgid "Winbooks Import"
msgstr "Import Winbooks"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_move_line__winbooks_line_id
msgid "Winbooks Line"
msgstr "Wiersz Winbooks"

#. module: account_winbooks_import
#: model:ir.model.fields,help:account_winbooks_import.field_account_winbooks_import_wizard__only_open
msgid ""
"Years closed in Winbooks are likely to have incomplete data. The counter "
"part of incomplete entries will be set in a suspense account"
msgstr ""
"Lata zamknięte w Winbooks prawdopodobnie zawierają niepełne dane. Duplikaty "
"niekompletnych wpisów zostaną ustawione na koncie przejściowym"

#. module: account_winbooks_import
#. odoo-python
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
msgid "You should install a Fiscal Localization first."
msgstr "W pierwszej kolejności należy zainstalować lokalizację fiskalną."

#. module: account_winbooks_import
#: model_terms:ir.ui.view,arch_db:account_winbooks_import.account_import_summary_form
msgid "account analytic lines imported"
msgstr "wiersze analizy konta zaimportowane"

#. module: account_winbooks_import
#: model_terms:ir.ui.view,arch_db:account_winbooks_import.account_import_summary_form
msgid "account analytics imported"
msgstr "analiza konta zaimportowana"
