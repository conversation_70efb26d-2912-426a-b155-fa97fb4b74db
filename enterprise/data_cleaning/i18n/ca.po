# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* data_cleaning
# 
# Translators:
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# Harcogourmet, 2024
# <PERSON><PERSON>, 2024
# R<PERSON> Consulting <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON> Fons<PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:29+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Óscar Fonseca <<EMAIL>>, 2024\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: data_cleaning
#. odoo-python
#: code:addons/data_cleaning/models/data_merge_group.py:0
msgid "%(model)s - Similarity: %(similarity)s%%"
msgstr ""

#. module: data_cleaning
#. odoo-python
#: code:addons/data_cleaning/models/data_merge_model.py:0
msgid "%s (copy)"
msgstr "%s (còpia)"

#. module: data_cleaning
#. odoo-javascript
#: code:addons/data_cleaning/static/src/views/data_merge_list_view.js:0
msgid "%s records have been merged"
msgstr "S'han fusionat %s registres"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.data_merge_duplicate
msgid ""
"' deduplication rule.<br/>\n"
"You can merge them"
msgstr ""
"' regla de desduplicació.<br/>\n"
"Podeu fusionar-los"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.notification
msgid ""
"' field cleaning rule.<br/>\n"
"You can validate changes"
msgstr ""
"' regla de neteja de camps.<br/>\n"
"Podeu validar els canvis"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_merge_model_form
msgid "<span class=\"me-1\">Every</span>"
msgstr "<span class=\"me-1\">Cada</span>"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_merge_model_form
msgid "<span class=\"oe_inline\">%</span>"
msgstr "<span class=\"oe_inline\">%</span>"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_merge_model_form
msgid "<span invisible=\"not custom_merge_method\">Model specific</span>"
msgstr ""

#. module: data_cleaning
#: model:ir.model.constraint,message:data_cleaning.constraint_data_merge_rule_uniq_model_id_field_id
msgid "A field can only appear once!"
msgstr "Un camp només pot aparèixer una vegada!"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__action
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_cleaning_record_list
msgid "Action"
msgstr "Acció"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__action_display
msgid "Action Display"
msgstr "Visualització d'accions"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__action_technical
msgid "Action Technical"
msgstr "Acció tècnica"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__action
msgid "Actions"
msgstr "Accions"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__active
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__active
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_group__active
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_model__active
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_record__active
msgid "Active"
msgstr "Actiu"

#. module: data_cleaning
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_rule__action_case__lower
msgid "All Lowercase"
msgstr "Totes les minúscules"

#. module: data_cleaning
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_rule__action_trim__all
msgid "All Spaces"
msgstr "Tots els espais"

#. module: data_cleaning
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_rule__action_case__upper
msgid "All Uppercase"
msgstr "Totes les majúscules"

#. module: data_cleaning
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_merge_model__removal_mode__archive
msgid "Archive"
msgstr "Arxivar"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.data_merge_model_view_search
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_merge_record_search
msgid "Archived"
msgstr "Arxivat"

#. module: data_cleaning
#. odoo-javascript
#: code:addons/data_cleaning/static/src/views/data_merge_list_view.js:0
msgid ""
"Are you sure that you want to merge the selected records in their respective"
" group?"
msgstr ""
"Esteu segur que voleu fusionar els registres seleccionats en el seu grup "
"respectiu?"

#. module: data_cleaning
#. odoo-javascript
#: code:addons/data_cleaning/static/src/views/data_merge_list_view.js:0
msgid "Are you sure that you want to merge these records?"
msgstr "Segur que voleu fusionar aquests registres?"

#. module: data_cleaning
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_model__cleaning_mode__automatic
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_merge_model__merge_mode__automatic
msgid "Automatic"
msgstr "Automàtic"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_ir_model__is_merge_enabled
#: model_terms:ir.ui.view,arch_db:data_cleaning.ir_model_view_search
msgid "Can Be Merged"
msgstr "Es pot fusionar"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__action_case
msgid "Case"
msgstr "Cas"

#. module: data_cleaning
#. odoo-python
#: code:addons/data_cleaning/models/data_merge_rule.py:0
msgid "Case/Accent Insensitive Match"
msgstr "Coincidència insensible a majúscules i minúscules"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__cleaning_mode
msgid "Cleaning Mode"
msgstr "Mode de neteja"

#. module: data_cleaning
#: model:ir.model,name:data_cleaning.model_data_cleaning_model
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__cleaning_model_id
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__cleaning_model_id
msgid "Cleaning Model"
msgstr "Model de neteja"

#. module: data_cleaning
#: model:ir.model,name:data_cleaning.model_data_cleaning_record
msgid "Cleaning Record"
msgstr "S'està netejant el registre"

#. module: data_cleaning
#: model:ir.model,name:data_cleaning.model_data_cleaning_rule
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_cleaning_record_list
msgid "Cleaning Rule"
msgstr "Regla de neteja"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_cleaning_record_search
msgid "Cleaning Rules"
msgstr "Regles de neteja"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__company_id
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_record__company_id
msgid "Company"
msgstr "Empresa"

#. module: data_cleaning
#: model_terms:ir.actions.act_window,help:data_cleaning.action_data_merge_record
#: model_terms:ir.actions.act_window,help:data_cleaning.action_data_merge_record_notification
msgid "Configure rules to identify duplicate records"
msgstr "Configura les regles per identificar registres duplicats"

#. module: data_cleaning
#: model_terms:ir.actions.act_window,help:data_cleaning.action_data_cleaning_record
#: model_terms:ir.actions.act_window,help:data_cleaning.action_data_cleaning_record_notification
msgid "Configure rules to identify records to clean"
msgstr "Configura les regles per a identificar els registres a netejar"

#. module: data_cleaning
#: model:ir.model,name:data_cleaning.model_res_partner
msgid "Contact"
msgstr "Contacte"

#. module: data_cleaning
#: model:ir.model.fields,help:data_cleaning.field_ir_model__ref_merge_ir_act_server_id
msgid ""
"Contextual menu action that redirects to the deduplicate view of data_merge."
msgstr ""
"Acció contextual del menú que redirigeix a la vista desduplicada de la fusió"
" de dades."

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__country_id
msgid "Country"
msgstr "País"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_record__record_create_uid
msgid "Created By"
msgstr "Creat per"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_record__record_create_date
msgid "Created On"
msgstr "Creat el"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__create_uid
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__create_uid
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__create_uid
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_group__create_uid
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_model__create_uid
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_record__create_uid
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_rule__create_uid
msgid "Created by"
msgstr "Creat per"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__create_date
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__create_date
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__create_date
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_group__create_date
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_model__create_date
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_record__create_date
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_rule__create_date
msgid "Created on"
msgstr "Creat el"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_model__mix_by_company
msgid "Cross-Company"
msgstr "Cross-Company"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__current_value
msgid "Current"
msgstr "Actiu"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_model__custom_merge_method
msgid "Custom Merge Method"
msgstr "Mètode de fusió personalitzat"

#. module: data_cleaning
#: model:ir.actions.server,name:data_cleaning.ir_cron_clean_records_ir_actions_server
msgid "Data Cleaning: Clean Records"
msgstr "Neteja de dades: Registres nets"

#. module: data_cleaning
#: model:ir.actions.server,name:data_cleaning.ir_cron_cleanup_ir_actions_server
msgid "Data Merge: Cleanup Records"
msgstr "Fusió de dades: Neteja els registres"

#. module: data_cleaning
#: model:ir.actions.server,name:data_cleaning.ir_cron_find_duplicates_ir_actions_server
msgid "Data Merge: Find Duplicate Records"
msgstr "Combinació de dades: cerca registres duplicats"

#. module: data_cleaning
#. odoo-python
#: code:addons/data_cleaning/models/data_cleaning_model.py:0
msgid "Data to Clean"
msgstr ""

#. module: data_cleaning
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_model__notify_frequency_period__days
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_merge_model__notify_frequency_period__days
msgid "Days"
msgstr "Dies"

#. module: data_cleaning
#. odoo-python
#: code:addons/data_cleaning/models/data_merge_record.py:0
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_merge_model_form
msgid "Deduplicate"
msgstr "Desduplica"

#. module: data_cleaning
#: model:ir.ui.menu,name:data_cleaning.menu_data_merge_config_rules_deduplication
#: model:ir.ui.menu,name:data_cleaning.menu_data_merge_group
msgid "Deduplication"
msgstr "Desduplicació"

#. module: data_cleaning
#: model:ir.model,name:data_cleaning.model_data_merge_group
msgid "Deduplication Group"
msgstr "Grup de desduplicació"

#. module: data_cleaning
#: model:ir.model,name:data_cleaning.model_data_merge_model
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_group__model_id
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_record__model_id
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_rule__model_id
msgid "Deduplication Model"
msgstr "Model de desduplicació"

#. module: data_cleaning
#: model:ir.model,name:data_cleaning.model_data_merge_record
msgid "Deduplication Record"
msgstr "Registre de desduplicació"

#. module: data_cleaning
#: model:ir.model,name:data_cleaning.model_data_merge_rule
msgid "Deduplication Rule"
msgstr "Regla de desduplicació"

#. module: data_cleaning
#: model:ir.actions.act_window,name:data_cleaning.action_data_merge_config
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_model__rule_ids
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_merge_model_form
msgid "Deduplication Rules"
msgstr "Regles de desduplicació"

#. module: data_cleaning
#. odoo-python
#: code:addons/data_cleaning/models/data_merge_model.py:0
msgid "Deduplication is forbidden on the model: %s"
msgstr "La desduplicació està prohibida en el model: %s"

#. module: data_cleaning
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_merge_model__removal_mode__delete
msgid "Delete"
msgstr "Eliminar"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_storage_tree
msgid "Details"
msgstr "Detalls"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_record__differences
msgid "Differences"
msgstr "Diferencies"

#. module: data_cleaning
#: model:ir.model.fields,help:data_cleaning.field_data_merge_record__differences
msgid "Differences with the master record"
msgstr "Diferències amb el registre mestre"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.ir_model_view_form
msgid "Disable Merge"
msgstr "Desactiva la fusió"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_cleaning_record_list
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_merge_record_list
msgid "Discard"
msgstr "Descartar"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.data_merge_record_view_search_merge_action
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_cleaning_record_search
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_merge_record_search
msgid "Discarded"
msgstr "Descartat"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__display_name
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__display_name
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__display_name
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_group__display_name
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_model__display_name
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_record__display_name
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_rule__display_name
#: model:ir.model.fields,field_description:data_cleaning.field_ir_attachment_report__display_name
msgid "Display Name"
msgstr "Nom mostrat"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_group__divergent_fields
msgid "Divergent Fields"
msgstr "Camps divergents"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_model__domain
msgid "Domain"
msgstr "Domini "

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_model__removal_mode
msgid "Duplicate Removal"
msgstr "Duplica l'eliminació"

#. module: data_cleaning
#: model:ir.actions.act_window,name:data_cleaning.action_data_merge_record
#: model:ir.actions.act_window,name:data_cleaning.action_data_merge_record_notification
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_merge_model_form
msgid "Duplicates"
msgstr "Duplicats"

#. module: data_cleaning
#. odoo-python
#: code:addons/data_cleaning/models/data_merge_model.py:0
msgid "Duplicates to Merge"
msgstr ""

#. module: data_cleaning
#: model:ir.model.fields,help:data_cleaning.field_data_merge_model__create_threshold
msgid ""
"Duplicates with a similarity below this threshold will not be suggested"
msgstr ""
"No es suggeriran duplicats amb una similitud per sota d'aquest llindar"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.ir_model_view_form
msgid "Enable Merge"
msgstr "Habilitar la fusió"

#. module: data_cleaning
#. odoo-python
#: code:addons/data_cleaning/models/data_merge_rule.py:0
msgid "Exact Match"
msgstr "Coincidència exacta"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__field_id
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__field_id
msgid "Field"
msgstr "Camp"

#. module: data_cleaning
#: model:ir.ui.menu,name:data_cleaning.menu_data_cleaning_config_rules_cleaning
#: model:ir.ui.menu,name:data_cleaning.menu_data_cleaning_record
msgid "Field Cleaning"
msgstr "Neteja de camps"

#. module: data_cleaning
#: model:ir.actions.act_window,name:data_cleaning.action_data_cleaning_record
#: model:ir.actions.act_window,name:data_cleaning.action_data_cleaning_record_notification
msgid "Field Cleaning Records"
msgstr "Registres de neteja de camps"

#. module: data_cleaning
#: model:ir.actions.act_window,name:data_cleaning.action_data_cleaning_config
msgid "Field Cleaning Rules"
msgstr "Regles de neteja de camps"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__field_name
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__name
msgid "Field Name"
msgstr "Nom de camp"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_cleaning_field_form
msgid "Field To Clean"
msgstr "Camp a netejar"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_record__field_values
msgid "Field Values"
msgstr "Valors del camp"

#. module: data_cleaning
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_rule__action_case__first
msgid "First Letters to Uppercase"
msgstr "Primeres lletres a majúscules"

#. module: data_cleaning
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_rule__action__phone
msgid "Format Phone"
msgstr "Format Telèfon"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_storage_search
msgid "Group By"
msgstr "Agrupar per"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_ir_model__hide_merge_action
msgid "Hide merge action button"
msgstr "Amaga el botó d'acció de fusió"

#. module: data_cleaning
#: model:ir.model.fields,help:data_cleaning.field_data_cleaning_rule__action_case
msgid ""
"How the type case is set by the rule. 'First Letters to Uppercase' sets "
"every letter to lowercase except the first letter of each word, which is set"
" to uppercase."
msgstr ""
"Com s'estableix el cas tipus per la regla. «Primeres lletres a majúscules» "
"estableix cada lletra a minúscules excepte la primera lletra de cada "
"paraula, que s'estableix a majúscules."

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.data_merge_duplicate
#: model_terms:ir.ui.view,arch_db:data_cleaning.notification
msgid "I've identified"
msgstr "He identificat"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__id
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__id
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__id
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_group__id
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_model__id
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_record__id
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_rule__id
#: model:ir.model.fields,field_description:data_cleaning.field_ir_attachment_report__id
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_merge_record_list
msgid "ID"
msgstr "ID"

#. module: data_cleaning
#: model:ir.model.fields,help:data_cleaning.field_ir_model__is_merge_enabled
msgid ""
"If True, the generic data merge tool is available in the contextual menu of "
"this model."
msgstr ""
"Si és cert, l'eina genèrica de fusió de dades està disponible al menú "
"contextual d'aquest model."

#. module: data_cleaning
#: model:ir.model.fields,help:data_cleaning.field_data_merge_model__is_contextual_merge_action
msgid ""
"If True, this record is used for contextual menu action \"Merge\" on the "
"target model."
msgstr ""
"Si és cert, aquest registre s'utilitza per a l'acció del menú contextual "
"«Fusiona» en el model de destinació."

#. module: data_cleaning
#: model:ir.model.fields,help:data_cleaning.field_ir_model__hide_merge_action
msgid ""
"If the model already has a custom merge method, the class attribute `_merge_disabled` is set to true on\n"
"             that model and the generic data merge action should not be available on that model."
msgstr ""
"Si el model ja té un mètode de fusió personalitzat, l'atribut de classe «mergedisabled» s'estableix a «true»\n"
"             Aquest model i l'acció genèrica de fusió de dades no haurien d'estar disponibles en aquest model."

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_record__is_deleted
msgid "Is Deleted"
msgstr "S'ha suprimit"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_record__is_discarded
msgid "Is Discarded"
msgstr "Està descartat"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_record__is_master
msgid "Is Master"
msgstr "És mestre"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__last_notification
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_model__last_notification
msgid "Last Notification"
msgstr "Última notificació"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__write_uid
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__write_uid
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__write_uid
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_group__write_uid
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_model__write_uid
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_record__write_uid
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_rule__write_uid
msgid "Last Updated by"
msgstr "Última actualització per"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__write_date
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__write_date
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__write_date
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_group__write_date
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_model__write_date
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_record__write_date
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_rule__write_date
msgid "Last Updated on"
msgstr "Última actualització el"

#. module: data_cleaning
#: model:ir.model.fields,help:data_cleaning.field_data_merge_record__used_in
msgid "List of other models referencing this record"
msgstr "Llista d'altres models que fan referència a aquest registre"

#. module: data_cleaning
#: model:ir.model.fields,help:data_cleaning.field_data_cleaning_model__notify_user_ids
msgid "List of users to notify when there are new records to clean"
msgstr "Llista d'usuaris a notificar quan hi hagi registres nous per netejar"

#. module: data_cleaning
#: model:ir.model.fields,help:data_cleaning.field_data_merge_model__notify_user_ids
msgid "List of users to notify when there are new records to merge"
msgstr "Llista d'usuaris a notificar quan hi ha registres nous per fusionar"

#. module: data_cleaning
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_model__cleaning_mode__manual
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_merge_model__merge_mode__manual
msgid "Manual"
msgstr "Manual"

#. module: data_cleaning
#: model:ir.actions.act_window,name:data_cleaning.ir_model_action_merge
#: model:ir.ui.menu,name:data_cleaning.ir_model_menu_merge_action_manager
msgid "Manual Merge"
msgstr ""

#. module: data_cleaning
#. odoo-python
#: code:addons/data_cleaning/models/data_merge_record.py:0
msgid "Manual Selection - %s"
msgstr "Selecció manual - %s"

#. module: data_cleaning
#. odoo-javascript
#. odoo-python
#: code:addons/data_cleaning/models/ir_model.py:0
#: code:addons/data_cleaning/static/src/views/data_merge_list_view.xml:0
#: model:ir.actions.server,name:data_cleaning.merge_action_res_country
#: model:ir.actions.server,name:data_cleaning.merge_action_res_country_state
#: model:ir.actions.server,name:data_cleaning.merge_action_res_partner_category
#: model:ir.actions.server,name:data_cleaning.merge_action_res_partner_industry
#: model:ir.actions.server,name:data_cleaning.merge_action_res_partner_title
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_merge_record_list
msgid "Merge"
msgstr "Combina"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_rule__match_mode
msgid "Merge If"
msgstr "Fusiona si"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_model__merge_mode
msgid "Merge Mode"
msgstr "Mode de fusió"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_ir_model__ref_merge_ir_act_server_id
msgid "Merge Server Action"
msgstr "Acció del servidor de fusió"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_model__is_contextual_merge_action
msgid "Merge action attached"
msgstr "Acció de fusió adjuntada"

#. module: data_cleaning
#. odoo-python
#: code:addons/data_cleaning/models/data_merge_model.py:0
msgid "Missing required PostgreSQL extension: unaccent"
msgstr "Manca l'extensió PostgreSQL requerida: no accentua"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__res_model_id
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__res_model_id
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__res_model_id
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_group__res_model_id
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_model__res_model_id
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_record__res_model_id
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_rule__res_model_id
#: model:ir.model.fields,field_description:data_cleaning.field_ir_attachment_report__res_model
#: model_terms:ir.ui.view,arch_db:data_cleaning.data_merge_record_view_search_merge_action
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_merge_record_search
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_storage_search
msgid "Model"
msgstr "Model"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__res_model_name
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__res_model_name
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__res_model_name
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_group__res_model_name
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_model__res_model_name
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_record__res_model_name
msgid "Model Name"
msgstr "Nom del model"

#. module: data_cleaning
#: model:ir.model,name:data_cleaning.model_ir_model
msgid "Models"
msgstr "Models"

#. module: data_cleaning
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_model__notify_frequency_period__months
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_merge_model__notify_frequency_period__months
msgid "Months"
msgstr "Mesos"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__name
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_model__name
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_record__name
msgid "Name"
msgstr "Nom"

#. module: data_cleaning
#: model_terms:ir.actions.act_window,help:data_cleaning.action_data_cleaning_record
#: model_terms:ir.actions.act_window,help:data_cleaning.action_data_cleaning_record_notification
msgid "No cleaning suggestions"
msgstr "Sense suggeriments de neteja"

#. module: data_cleaning
#. odoo-python
#: code:addons/data_cleaning/models/data_merge_record.py:0
#: model_terms:ir.actions.act_window,help:data_cleaning.action_data_merge_record
#: model_terms:ir.actions.act_window,help:data_cleaning.action_data_merge_record_notification
msgid "No duplicates found"
msgstr "No s'ha trobat cap duplicat"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__notify_frequency
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_model__notify_frequency
msgid "Notify"
msgstr "Notifica"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__notify_frequency_period
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_model__notify_frequency_period
msgid "Notify Frequency Period"
msgstr "Notifica el període de freqüència"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__notify_user_ids
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_model__notify_user_ids
msgid "Notify Users"
msgstr "Notifica als usuaris"

#. module: data_cleaning
#. odoo-python
#: code:addons/data_cleaning/models/data_merge_record.py:0
msgid "Query Failed."
msgstr "Ha fallat la consulta."

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_group__record_ids
#: model:ir.model.fields,field_description:data_cleaning.field_ir_attachment_report__res_id
msgid "Record"
msgstr "Registre "

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_record__group_id
msgid "Record Group"
msgstr "Enregistra un grup"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__res_id
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_record__res_id
msgid "Record ID"
msgstr "ID Registre "

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__name
msgid "Record Name"
msgstr "Nom del registre"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.data_merge_record_view_search_merge_action
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_cleaning_record_search
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_merge_record_search
msgid "Records"
msgstr "Registre"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__records_to_clean_count
msgid "Records To Clean"
msgstr "Registres a netejar"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_model__records_to_merge_count
msgid "Records To Merge Count"
msgstr "Registres per fusionar el compte"

#. module: data_cleaning
#: model:ir.model.fields,help:data_cleaning.field_data_merge_model__domain
msgid "Records eligible for the deduplication process"
msgstr "Registres elegibles per al procés de desduplicació"

#. module: data_cleaning
#. odoo-python
#: code:addons/data_cleaning/models/data_merge_record.py:0
msgid "Records must be of the same model"
msgstr "Els registres han de ser del mateix model"

#. module: data_cleaning
#: model:ir.model.fields,help:data_cleaning.field_data_merge_model__merge_threshold
msgid ""
"Records with a similarity percentage above this threshold will be "
"automatically merged"
msgstr ""
"Els registres amb un percentatge de similitud per sobre d'aquest llindar es "
"fusionaran automàticament"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_ir_attachment_report__name
msgid "Resource Name"
msgstr "Nom del Recurs"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__rule_ids
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_merge_record_search
msgid "Rule"
msgstr "Regla"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__rule_ids
msgid "Rules"
msgstr "Regles"

#. module: data_cleaning
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_rule__action__html
msgid "Scrap HTML"
msgstr "Scrap HTML"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_merge_model_form
msgid "Select a model to configure deduplication rules"
msgstr "Seleccioneu un model per a configurar les regles de desduplicació"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__sequence
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_rule__sequence
msgid "Sequence"
msgstr "Seqüència"

#. module: data_cleaning
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_rule__action__case
msgid "Set Type Case"
msgstr "Estableix el tipus de cas"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_group__similarity
msgid "Similarity %"
msgstr "Similaritat %"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_model__merge_threshold
msgid "Similarity Threshold"
msgstr "Llindar de similitud"

#. module: data_cleaning
#: model:ir.model.fields,help:data_cleaning.field_data_merge_group__similarity
msgid ""
"Similarity coefficient based on the amount of text fields exactly in common."
msgstr ""
"Coeficient de similitud basat en la quantitat de camps de text exactament en"
" comú."

#. module: data_cleaning
#: model:ir.actions.act_window,name:data_cleaning.action_data_storage
#: model:ir.model,name:data_cleaning.model_ir_attachment_report
#: model:ir.ui.menu,name:data_cleaning.menu_data_cleaning_storage
msgid "Storage"
msgstr "Emmagatzematge"

#. module: data_cleaning
#. odoo-python
#: code:addons/data_cleaning/report/data_storage.py:0
msgid "Storage Detail: %(name)s"
msgstr ""

#. module: data_cleaning
#: model:ir.model.fields,help:data_cleaning.field_data_merge_model__rule_ids
msgid "Suggest to merge records matching at least one of these rules"
msgstr ""
"Suggereix fusionar els registres que coincideixin almenys amb una d'aquestes"
" regles"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__suggested_value_display
msgid "Suggested"
msgstr "Suggerit"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__suggested_value
msgid "Suggested Value"
msgstr "Valor suggerit"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_model__create_threshold
msgid "Suggestion Threshold"
msgstr "Llindar del suggeriment"

#. module: data_cleaning
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_rule__action_trim__superfluous
msgid "Superfluous Spaces"
msgstr "Espais superflus"

#. module: data_cleaning
#. odoo-python
#: code:addons/data_cleaning/models/data_cleaning_rule.py:0
msgid ""
"The Python module `phonenumbers` is not installed. Format phone will not "
"work."
msgstr ""
"El mòdul Python «phonenumbers» no està instal·lat. El format del telèfon no "
"funcionarà."

#. module: data_cleaning
#: model:ir.model.constraint,message:data_cleaning.constraint_data_cleaning_model_check_notif_freq
#: model:ir.model.constraint,message:data_cleaning.constraint_data_merge_model_check_notif_freq
msgid "The notification frequency should be greater than 0"
msgstr "La freqüència de notificació hauria de ser superior a 0"

#. module: data_cleaning
#. odoo-python
#: code:addons/data_cleaning/models/data_merge_record.py:0
msgid "The referenced record does not exist"
msgstr "El registre referenciat no existeix"

#. module: data_cleaning
#. odoo-javascript
#: code:addons/data_cleaning/static/src/views/data_merge_list_view.js:0
msgid "The selected records have been merged"
msgstr "S'han fusionat els registres seleccionats"

#. module: data_cleaning
#. odoo-python
#: code:addons/data_cleaning/models/data_merge_record.py:0
msgid "The target model does not exists."
msgstr "El model objectiu no existeix."

#. module: data_cleaning
#. odoo-python
#: code:addons/data_cleaning/models/data_merge_record.py:0
msgid "There is not referenced record"
msgstr "No hi ha cap registre referenciat"

#. module: data_cleaning
#: model:ir.model.constraint,message:data_cleaning.constraint_data_merge_model_uniq_name
msgid "This name is already taken"
msgstr "Aquest nom ja està agafat"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_ir_attachment_report__size
msgid "Total Size"
msgstr ""

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__action_trim
msgid "Trim"
msgstr "Trim"

#. module: data_cleaning
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_rule__action__trim
msgid "Trim Spaces"
msgstr "Retalla els espais"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_rule__field_id
msgid "Unique ID Field"
msgstr "Camp ID únic"

#. module: data_cleaning
#. odoo-javascript
#: code:addons/data_cleaning/static/src/views/data_cleaning_list_view.xml:0
#: code:addons/data_cleaning/static/src/views/data_merge_list_view.xml:0
msgid "Unselect"
msgstr "Desselecciona"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_record__record_write_uid
msgid "Updated By"
msgstr "Actualitzat per"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_record__record_write_date
msgid "Updated On"
msgstr "Actualitzat el"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_record__used_in
msgid "Used In"
msgstr "Utilitzat a"

#. module: data_cleaning
#. odoo-javascript
#: code:addons/data_cleaning/static/src/views/data_cleaning_list_view.xml:0
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_cleaning_record_list
msgid "Validate"
msgstr "Validar"

#. module: data_cleaning
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_model__notify_frequency_period__weeks
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_merge_model__notify_frequency_period__weeks
msgid "Weeks"
msgstr "Setmanes"

#. module: data_cleaning
#: model:ir.model.fields,help:data_cleaning.field_data_merge_model__mix_by_company
msgid "When enabled, duplicates across different companies will be suggested"
msgstr ""
"Quan estigui habilitat, es suggeriran duplicats entre diferents empreses"

#. module: data_cleaning
#: model:ir.model.fields,help:data_cleaning.field_data_cleaning_rule__action_trim
msgid ""
"Which spaces are trimmed by the rule. Leading, trailing, and successive "
"spaces are considered superfluous."
msgstr ""
"Quins espais són retallats per la regla. Els espais inicials, finals i "
"successius es consideren superflus."

#. module: data_cleaning
#. odoo-python
#: code:addons/data_cleaning/models/data_merge_record.py:0
msgid "You must select at least two %s in order to merge them."
msgstr "Heu de seleccionar com a mínim dos %s per tal de fusionar-los."

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.data_merge_duplicate
msgid "duplicate records with the '"
msgstr "duplica els registres amb l''"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.data_merge_duplicate
#: model_terms:ir.ui.view,arch_db:data_cleaning.notification
msgid "here"
msgstr "aquí"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.data_merge_merged
msgid "merged into"
msgstr "fusionat a"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.data_merge_main
msgid "merged into this one"
msgstr "fusionat en aquest"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.notification
msgid "records to clean with the '"
msgstr "registres a netejar amb el '"
