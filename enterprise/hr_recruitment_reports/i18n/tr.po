# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_recruitment_reports
# 
# Translators:
# emre <PERSON>tem, 2024
# Halil, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:27+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: hr_recruitment_reports
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.hr_recruitment_report_inherit_kanban_view
msgid "Analysis"
msgstr "Analiz"

#. module: hr_recruitment_reports
#. odoo-python
#: code:addons/hr_recruitment_reports/report/hr_recruitment_report.py:0
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__count
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_stage_report__applicant_id
msgid "Applicant"
msgstr "Aday"

#. module: hr_recruitment_reports
#: model:ir.actions.act_window,name:hr_recruitment_reports.recruitment_report_action
msgid "Applicant Analysis"
msgstr ""

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__name
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_stage_report__name
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_stage_report_view_search
msgid "Applicant Name"
msgstr "Adayın İsmi"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__create_date
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_search
msgid "Application Date"
msgstr ""

#. module: hr_recruitment_reports
#: model:ir.model.fields.selection,name:hr_recruitment_reports.selection__hr_recruitment_stage_report__state__archived
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_stage_report_view_search
msgid "Archived"
msgstr "Arşivlendi"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__company_id
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_stage_report__company_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_stage_report_view_search
msgid "Company"
msgstr "Firma"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__create_uid
msgid "Creator"
msgstr "Oluşturan"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_stage_report__days_in_stage
msgid "Days In Stage"
msgstr ""

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__display_name
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_stage_report__display_name
msgid "Display Name"
msgstr "İsim Göster"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__date_closed
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_stage_report__date_end
msgid "End Date"
msgstr "Bitiş Tarihi"

#. module: hr_recruitment_reports
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_stage_report_view_search
msgid "Group By"
msgstr "Grupla"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__hired
#: model:ir.model.fields.selection,name:hr_recruitment_reports.selection__hr_recruitment_report__state__is_hired
#: model:ir.model.fields.selection,name:hr_recruitment_reports.selection__hr_recruitment_stage_report__state__is_hired
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_stage_report_view_search
msgid "Hired"
msgstr "İşe Alınan"

#. module: hr_recruitment_reports
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_search
msgid "Hired Date"
msgstr ""

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__hiring_ratio
msgid "Hired Ratio"
msgstr ""

#. module: hr_recruitment_reports
#. odoo-python
#: code:addons/hr_recruitment_reports/report/hr_recruitment_report.py:0
msgid "Hiring ratio"
msgstr ""

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__id
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_stage_report__id
msgid "ID"
msgstr "ID"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__in_progress
#: model:ir.model.fields.selection,name:hr_recruitment_reports.selection__hr_recruitment_report__state__in_progress
#: model:ir.model.fields.selection,name:hr_recruitment_reports.selection__hr_recruitment_stage_report__state__in_progress
msgid "In Progress"
msgstr "İşlemde"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__job_id
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_stage_report__job_id
msgid "Job"
msgstr "İş"

#. module: hr_recruitment_reports
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_stage_report_view_search
msgid "Job Position"
msgstr "İş Pozisyonu"

#. module: hr_recruitment_reports
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_stage_report_view_search
msgid "Last 365 Days Applicant"
msgstr "Son 365 Gün Başvuru Sahibi"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__medium_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_search
msgid "Medium"
msgstr "Aracı:"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__meetings_amount
msgid "Meetings"
msgstr "Toplantılar"

#. module: hr_recruitment_reports
#: model_terms:ir.actions.act_window,help:hr_recruitment_reports.recruitment_report_action
#: model_terms:ir.actions.act_window,help:hr_recruitment_reports.recruitment_report_source_action
#: model_terms:ir.actions.act_window,help:hr_recruitment_reports.recruitment_report_source_job_action
#: model_terms:ir.actions.act_window,help:hr_recruitment_reports.recruitment_report_team_action
#: model_terms:ir.actions.act_window,help:hr_recruitment_reports.recruitment_stage_report_action
#: model_terms:ir.actions.act_window,help:hr_recruitment_reports.recruitment_stage_report_job_action
msgid "No data to display"
msgstr "Gösterilecek veri yok"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__process_duration
msgid "Process Duration"
msgstr "İşlem Süresi"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__user_id
msgid "Recruiter"
msgstr "İşe Alan"

#. module: hr_recruitment_reports
#: model:ir.actions.act_window,name:hr_recruitment_reports.recruitment_report_job_action
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.hr_recruitment_report_view_tree
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_graph
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_search
msgid "Recruitment Analysis"
msgstr "İşe Alım Analizi"

#. module: hr_recruitment_reports
#: model:ir.model,name:hr_recruitment_reports.model_hr_recruitment_report
msgid "Recruitment Analysis Report"
msgstr "İşe Alım Analiz Raporu"

#. module: hr_recruitment_reports
#: model:ir.model,name:hr_recruitment_reports.model_hr_recruitment_stage_report
msgid "Recruitment Stage Analysis"
msgstr ""

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__refuse_reason_id
msgid "Refuse Reason"
msgstr "Reddetme Sebebi"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__refused
#: model:ir.model.fields.selection,name:hr_recruitment_reports.selection__hr_recruitment_report__state__refused
#: model:ir.model.fields.selection,name:hr_recruitment_reports.selection__hr_recruitment_stage_report__state__refused
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_stage_report_view_search
msgid "Refused"
msgstr "Reddedildi"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__source_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.hr_recruitment_report_inherit_kanban_view
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_search
msgid "Source"
msgstr "Kaynak"

#. module: hr_recruitment_reports
#: model:ir.actions.act_window,name:hr_recruitment_reports.recruitment_report_source_action
#: model:ir.actions.act_window,name:hr_recruitment_reports.recruitment_report_source_job_action
#: model:ir.ui.menu,name:hr_recruitment_reports.hr_applicant_report_source_menu
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_source_view_graph
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_team_view_graph
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_source_pivot
msgid "Source Analysis"
msgstr "Kaynak Analizi"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__stage_id
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_stage_report__stage_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_stage_report_view_search
msgid "Stage"
msgstr "Aşama"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_stage_report__date_begin
msgid "Start Date"
msgstr "Başlama Tarihi"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__state
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_stage_report__state
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_stage_report_view_search
msgid "State"
msgstr "İl/Eyalet"

#. module: hr_recruitment_reports
#: model:ir.actions.act_window,name:hr_recruitment_reports.recruitment_report_team_action
#: model:ir.ui.menu,name:hr_recruitment_reports.hr_applicant_report_team_menu
msgid "Team Performance"
msgstr "Takım Performansı"

#. module: hr_recruitment_reports
#: model_terms:ir.actions.act_window,help:hr_recruitment_reports.recruitment_stage_report_action
#: model_terms:ir.actions.act_window,help:hr_recruitment_reports.recruitment_stage_report_job_action
msgid ""
"This report allows you to check the more time-consuming stages of your pipes"
" and optimize your recruitment flow."
msgstr ""

#. module: hr_recruitment_reports
#: model_terms:ir.actions.act_window,help:hr_recruitment_reports.recruitment_report_team_action
msgid ""
"This report allows you to compare the recruiters and their performances."
msgstr ""

#. module: hr_recruitment_reports
#: model_terms:ir.actions.act_window,help:hr_recruitment_reports.recruitment_report_source_action
#: model_terms:ir.actions.act_window,help:hr_recruitment_reports.recruitment_report_source_job_action
msgid ""
"This report allows you to compare various measures grouped by your sources "
"of applicants (e.g. LinkedIn, Monster, etc.)."
msgstr ""

#. module: hr_recruitment_reports
#: model_terms:ir.actions.act_window,help:hr_recruitment_reports.recruitment_report_action
msgid ""
"This report allows you to follow the evolution of the number of applicants "
"and hired employees over time."
msgstr ""

#. module: hr_recruitment_reports
#: model_terms:ir.actions.act_window,help:hr_recruitment_reports.recruitment_report_job_action
msgid "This report performs analysis on your recruitment."
msgstr "Bu rapor, işe alımınız hakkında analiz yapar."

#. module: hr_recruitment_reports
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.hr_recruitment_report_inherit_kanban_view
msgid "Time By Stages"
msgstr ""

#. module: hr_recruitment_reports
#: model:ir.actions.act_window,name:hr_recruitment_reports.recruitment_stage_report_job_action
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_stage_report_view_graph
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_stage_report_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_stage_report_view_search
msgid "Time In Stage Analysis"
msgstr "Durumda Geçirilen Zaman Analizi"

#. module: hr_recruitment_reports
#. odoo-python
#: code:addons/hr_recruitment_reports/report/hr_recruitment_report.py:0
msgid "Total Hired"
msgstr ""

#. module: hr_recruitment_reports
#. odoo-python
#: code:addons/hr_recruitment_reports/report/hr_recruitment_report.py:0
msgid "Total Meetings"
msgstr ""

#. module: hr_recruitment_reports
#. odoo-python
#: code:addons/hr_recruitment_reports/report/hr_recruitment_report.py:0
msgid "Total applicants"
msgstr ""

#. module: hr_recruitment_reports
#: model:ir.actions.act_window,name:hr_recruitment_reports.recruitment_stage_report_action
#: model:ir.ui.menu,name:hr_recruitment_reports.hr_applicant_stage_report_menu
msgid "Velocity Analysis"
msgstr ""
