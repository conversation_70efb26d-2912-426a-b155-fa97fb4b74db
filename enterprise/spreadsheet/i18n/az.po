# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* spreadsheet
# 
# Translators:
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# s<PERSON><PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:40+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Azerbaijani (https://app.transifex.com/odoo/teams/41243/az/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: az\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
" A string, possible empty, or a reference to a valid string. If empty, the "
"text will be simply concatenated."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "% Running total"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "% difference from"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "% of"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "% of column total"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "% of grand total"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "% of parent column total"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "% of parent row total"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "% of parent total"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "% of row total"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "%(hour_number)sh"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "%(matches)s matches in %(sheetName)s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "%(matches)s matches in range %(range)s of %(sheetName)s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "%(minute_number)s'"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "%(newPivotName)s (Pivot #%(formulaId)s)"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"%(replaceable_count)s match(es) replaced. %(irreplaceable_count)s match(es) "
"cannot be replaced as they are part of a formula."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "%(row_count)s rows and %(column_count)s columns selected"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "%(second_number)s''"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "%s %s and %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "%s (copy)"
msgstr "%s (surət)"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "%s (positional)"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "%s Columns left"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "%s Columns right"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "%s Rows above"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "%s Rows below"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/assets_backend/spreadsheet_action_loader.js:0
msgid "%s couldn't be loaded"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"%s duplicate rows found and removed.\n"
"%s unique rows remain."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"%s is not a valid day of month (it should be a number between 1 and 31)"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "%s is not a valid day of week (it should be a number between 1 and 7)"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "%s is not a valid hour (it should be a number between 0 and 23)"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "%s is not a valid minute (it should be a number between 0 and 59)"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "%s is not a valid month (it should be a number between 1 and 12)"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "%s is not a valid quarter (it should be a number between 1 and 4)"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "%s is not a valid second (it should be a number between 0 and 59)"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "%s is not a valid week (it should be a number between 0 and 53)"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "%s matches in all sheets"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"(0) Exact match. \n"
"        (-1) Return next smaller item if no match. \n"
"        (1) Return next greater item if no match. \n"
"        (2) Wildcard match."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"(1) Search starting at first item. \n"
"      (-1) Search starting at last item. \n"
"      (2) Perform a binary search that relies on lookup_array being sorted in ascending order. If not sorted, invalid results will be returned. \n"
"      (-2) Perform a binary search that relies on lookup_array being sorted in descending order. If not sorted, invalid results will be returned.\n"
"      "
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "(Blanks)"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "(Undefined)"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "(next)"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "(previous)"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "+ Add another rule"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "- [optional]"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "1 column"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "1 row"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "2 columns"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "2 rows"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"A boolean indicating whether to use A1 style notation (TRUE) or R1C1 style "
"notation (FALSE)."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"A boolean; if TRUE, empty cells selected in the text arguments won't be "
"included in the result."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"A column or row containing true or false values corresponding to the first "
"column or row of range."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "A conditional count across a range."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "A conditional sum across a range."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "A flag specifying wheter to compute the slope or not"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "A flag specifying whether to compute the intercept or not."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"A flag specifying whether to return additional regression statistics or only"
" the linear coefficients and the y-intercept"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "A maximum range limit value is needed"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "A minimum range limit value is needed"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"A number indicating which numbering system to use to represent weekdays. By "
"default, counts starting with Sunday = 1."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"A number or string representing which days of the week are considered "
"weekends."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "A number raised to a power."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "A number representing the day that a week starts on. Sunday = 1."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "A number representing the way to display ties."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "A number with the sign reversed."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "A random number between 0 inclusive and 1 exclusive."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"A range containing the income or payments associated with the investment. "
"The array should contain bot payments and incomes."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "A range needs to be defined"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"A range or array constant containing the date serial numbers to consider "
"holidays."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"A range or array constant containing the dates to consider as holidays."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "A range or array constant containing the dates to consider holidays."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "A segment of a string."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "A series of interest rates to compound against the principal."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "A sheet with the name %s already exists. Please select another name."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "A specified number, unchanged."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"A string indicating the name of the sheet into which the address points."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "A substring from the end of a specified string."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "A table can only be created on a continuous selection."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"A text abbreviation for unit of time. Accepted values are \"Y\" (the number "
"of whole years between start_date and end_date), \"M\" (the number of whole "
"months between start_date and end_date), \"D\" (the number of days between "
"start_date and end_date), \"MD\" (the number of days between start_date and "
"end_date after subtracting whole months), \"YM\" (the number of whole months"
" between start_date and end_date after subtracting whole years), \"YD\" (the"
" number of days between start_date and end_date, assuming start_date and "
"end_date were no more than one year apart)."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "ABOUT"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "AVERAGE"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Absolute value"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Absolute value of a number."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Access to the clipboard denied by the browser. Please enable clipboard "
"permission for this page in your browser settings."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Accounting"
msgstr "Mühasibat Uçotu"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Accounting format"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Accrued interest of security paying at maturity."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/global_filters/plugins/global_filters_ui_plugin.js:0
msgid "Active Filters"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Add"
msgstr "Əlavə et"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Add a title"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Add another item"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Add any characters or symbol"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Add calculated measure"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Add filters"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Add new columns to avoid overwriting cells"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Add range"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Additional column or row containing true or false values."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Additional conditions to be evaluated if the previous ones are FALSE."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Additional criteria to check."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Additional criteria_range and criterion to check."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Additional future cash flows."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Additional numbers or ranges to add to value1."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Additional ranges over which to evaluate the additional criteria. The "
"filtered set will be the intersection of the sets produced by each "
"criterion-range pair."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Additional ranges to add to range1."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Additional ranges to check."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Additional ranges to flatten."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Additional text item(s)."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Additional values or ranges in which to count the number of blanks."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Additional values or ranges to consider for uniqueness."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Additional values or ranges to consider when calculating the average value."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Additional values or ranges to consider when calculating the maximum value."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Additional values or ranges to consider when calculating the median value."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Additional values or ranges to consider when calculating the minimum value."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Additional values or ranges to consider when counting."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Additional values or ranges to include in the population."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Additional values or ranges to include in the sample."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Additional values to average."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Additional values to be returned if their corresponding conditions are TRUE."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Additional weights."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Aggregate"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Aggregated by"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Alignment"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "All"
msgstr "BÜTÜN"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "All sheets"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"All the dates should be greater or equal to the first date in cashflow_dates"
" (%s)."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "All the ranges must have the same dimensions."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Amount received at maturity for a security."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"An array or range containing the income or payments associated with the "
"investment."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"An array or range containing zero or more criteria to filter the database "
"values by before operating."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "An estimate for what the interest rate will be."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "An estimate for what the internal rate of return will be."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"An expression or reference to a cell containing an expression that "
"represents some logical value, i.e. TRUE or FALSE, or an expression that can"
" be coerced to a logical value."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"An expression or reference to a cell containing an expression that "
"represents some logical value, i.e. TRUE or FALSE."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"An expression or reference to a cell holding an expression that represents "
"some logical value."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "An indicator of what day count method to use."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"An indicator of what day count method to use. (0) US NASD method (1) "
"European method"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"An indicator of whether the reference is row/column absolute. 1 is row and "
"column absolute (e.g. $A$1), 2 is row absolute and column relative (e.g. "
"A$1), 3 is row relative and column absolute (e.g. $A1), and 4 is row and "
"column relative (e.g. A1)."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"An integer specifying the dimension size of the unit matrix. It must be "
"positive."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"An range containing the income or payments associated with the investment."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"An range with an equal number of rows and columns representing a matrix "
"whose determinant will be calculated."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"An range with an equal number of rows and columns representing a matrix "
"whose multiplicative inverse will be calculated."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"An range with dates corresponding to the cash flows in cashflow_amounts."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "An unexpected error occurred during the image transfer"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"An unexpected error occurred while pasting content.\n"
"          This is probably due to a spreadsheet version mismatch."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"An unexpected error occurred. Submit a support ticket at odoo.com/help."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Angle from the X axis to a point (x,y), in radians."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Annual effective interest rate."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Annual nominal interest rate."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Annual yield of a discount security."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Annual yield of a security paying interest at maturity."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Annual yield of a security paying periodic interest."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Anonymous"
msgstr "Anonim"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Any real value to calculate the hyperbolic cosecant of."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Any real value to calculate the hyperbolic cosine of."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Any real value to calculate the hyperbolic cotangent of."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Any real value to calculate the hyperbolic secant of."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Any real value to calculate the hyperbolic sine of."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Any real value to calculate the hyperbolic tangent of."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Any text item. This could be a string, or an array of strings in a range."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Appends ranges horizontally and in sequence to return a larger array."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Appends ranges vertically and in sequence to return a larger array."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Appends strings to one another."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Apply"
msgstr "Tətbiq edin"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Apply a large number format"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Apply all changes"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Apply to range"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/assets_backend/constants.js:0
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "April"
msgstr "Aprel"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Are you sure you want to delete this pivot?"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Are you sure you want to delete this sheet?"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Area"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Argument ignore must be between 0 and 3"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Argument must be a reference to a cell or range."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Argument range must be a single row or column."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Array"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Array arguments to [[FUNCTION_NAME]] are of different size."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Array or range containing the dataset to consider."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Array result was not expanded because it would overwrite data in %s."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Arrow"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Ascending"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Ascending (A ⟶ Z)"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "At least one measure and/or dimension is not correct."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "At least one of the provided values is an invalid formula"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/assets_backend/constants.js:0
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "August"
msgstr "Avqust"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Auto-adjust to formula result"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Automatic"
msgstr "Avtomatik"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Automatically autofill formulas"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/spreadsheet/static/src/pivot/pivot_helpers.js:0
msgid "Average"
msgstr "Orta"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Average magnitude of deviations from mean."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Average of a set of values from a table-like range."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Average of values depending on criteria."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Average of values depending on multiple criteria."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Avg"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Axes"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Axis title"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Background color"
msgstr "Fonun Rəngi"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Bad zone format"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Banded columns"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Banded rows"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/chart/odoo_chart/odoo_bar_chart.js:0
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Bar"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Base field:"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Base item:"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Baseline"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Baseline colors"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Baseline configuration"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Baseline description"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Black"
msgstr "Qara"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Bold"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/spreadsheet/static/src/pivot/pivot_helpers.js:0
msgid "Boolean And"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/spreadsheet/static/src/pivot/pivot_helpers.js:0
msgid "Boolean Or"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Border Color"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Border color"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Borders"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Bottom"
msgstr "Alt"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "COUNT"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Calculated measure %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Calculates effective interest rate."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Calculates the expected y-value for a specified x based on a linear "
"regression of a dataset."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Calculates the frequency distribution of a range."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Calculates the matrix product of two matrices."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Calculates the number of days, months, or years between two dates."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Calculates the price of a security paying interest at maturity, based on "
"expected yield."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Calculates the standard error of the predicted y-value for each x in the "
"regression of a dataset."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Calculates the sum of squares of the differences of values in two array."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Calculates the sum of the difference of the squares of the values in two "
"array."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Calculates the sum of the products of corresponding entries in equal-sized "
"ranges."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Calculates the sum of the sum of the squares of the values in two array."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Calculates the value as a percentage for successive items in the Base field "
"that are displayed as a running total."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Calculates values as follows:\n"
"((value in cell) x (Grand Total of Grand Totals)) / ((Grand Row Total) x (Grand Column Total))"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Calculates values as follows:\n"
"(value for the item) / (value for the parent item of the selected Base field)"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Calculates values as follows:\n"
"(value for the item) / (value for the parent item on columns)"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Calculates values as follows:\n"
"(value for the item) / (value for the parent item on rows)"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Cancel"
msgstr "Ləğv edin"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Cannot do a special paste of a figure."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Cannot find workbook relations file"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Cannot have filters without a header row"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Cannot hide all the columns of a sheet."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Cannot hide all the rows of a sheet."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Cannot multiply matrices : incompatible matrices size."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Cannot open the link because the linked sheet is hidden."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Cannot remove duplicates for an unknown reason"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Cannot sort. To sort, select only cells or only merges that have the same "
"size."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Cannot split the selection for an unknown reason"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Capitalizes each word in a specified string."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Categories / Labels"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Cell values"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Center"
msgstr "Mərkəz"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Change color"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Changing the pivot definition requires to reload the data. It may take some "
"time."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Chart"
msgstr "Diaqram"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/chart/plugins/odoo_chart_ui_plugin.js:0
msgid "Chart - %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Chart title"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Chart type"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Checkbox"
msgstr "Bayrağın qoyulması üçün dairəvi sahə"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/global_filters/components/filter_text_value/filter_text_value.xml:0
msgid "Choose a value..."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Circular reference"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/global_filters/components/filter_value/filter_value.xml:0
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Clear"
msgstr "AYDIN"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Clear column %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Clear columns"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Clear columns %s - %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Clear formatting"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Clear row %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Clear rows"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Clear rows %s - %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Clip"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Code"
msgstr "Kod"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Collapse all column groups"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Collapse all row groups"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Collapse column group"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Collapse row group"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Color"
msgstr "Rəng"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Color Down"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Color Up"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Color of negative values"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Color of positive values"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Color of subtotals"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Color on value decrease"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Color on value increase"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Color scale"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Colors"
msgstr "Rənglər"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Column"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Column %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Column left"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Column number of a specified cell."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Column right"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Columns"
msgstr "Sütunlar"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Columns to analyze"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Combines text from multiple strings and/or arrays."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Combo"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Comma"
msgstr "Nöqtə"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Compare two numeric values, returning 1 if they're equal."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Compute the Matthews correlation coefficient of a dataset."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Compute the Pearson product-moment correlation coefficient of a dataset."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Compute the Spearman rank correlation coefficient of a dataset."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Compute the coefficients of polynomial regression of the dataset."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Compute the intercept of the linear regression."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Compute the slope of the linear regression."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Compute the square of r, the Pearson product-moment correlation coefficient "
"of a dataset."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Computes the number of periods needed for an investment to reach a value."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Computes the rate needed for an investment to reach a specific value within "
"a specific number of periods."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Concatenates elements of arrays with delimiter."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Concatenation of two values."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Conditional formatting"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Configuration"
msgstr "Konfiqurasiya"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/hooks.js:0
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Confirm"
msgstr "Təsdiq edin"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Consider using a dynamic pivot formula: %s. Or re-insert the static pivot "
"from the Data menu."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Contains"
msgstr "İbarətdir"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Convert a decimal fraction to decimal value."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Convert a decimal value to decimal fraction."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Convert to individual formulas"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Converts a date string to a date value."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Converts a number to text according to a specified format."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Converts a numeric value to a different unit of measure."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Converts a specified string to lowercase."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Converts a specified string to uppercase."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Converts a time string into its serial number representation."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Converts an angle value in radians to degrees."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Converts from another base to decimal."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Converts hour/minute/second into a time."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Converts year/month/day into a date."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/components/share_button/share_button.js:0
msgid "Copied"
msgstr "Kopyalandı"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Copy"
msgstr "Surət"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Copy of %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Cosecant of an angle provided in radians."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Cosine of an angle provided in radians."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Cotangent of an angle provided in radians."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/spreadsheet/static/src/pivot/pivot_helpers.js:0
msgid "Count"
msgstr "Hesabla"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/spreadsheet/static/src/pivot/pivot_helpers.js:0
msgid "Count Distinct"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Count Numbers"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Count values depending on multiple criteria."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Counts number of unique values in a range, filtered by a set of criteria."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Counts number of unique values in a range."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Counts values and text from a table-like range."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Counts values from a table-like range."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Create custom table style"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Creates a hyperlink in a cell."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Creates a new array from the selected columns in the existing range."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Creates a new array from the selected rows in the existing range."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Criteria"
msgstr "Meyar"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Cumulative data"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Cumulative interest paid over a set of periods."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Cumulative principal paid over a set of periods."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
#: model:ir.model,name:spreadsheet.model_res_currency
msgid "Currency"
msgstr "Valyuta"

#. module: spreadsheet
#: model:ir.model,name:spreadsheet.model_res_currency_rate
msgid "Currency Rate"
msgstr "Valyuta Məzənnəsi"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/currency/plugins/currency.js:0
msgid "Currency not available for this company."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/currency/plugins/currency.js:0
msgid "Currency rate unavailable."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Currency rounded"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Current date and time as a date value."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Current date as a date value."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Current sheet"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Custom"
msgstr "Xüsusi"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Custom Table Style"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Custom currency"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Custom currency format"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Custom formula"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Custom formula %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Custom separator"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Cut"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Dark"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Data"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Data Validation"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Data bar"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Data cleanup"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Data has header row"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Data not available"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Data range"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Data series"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Data validation"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Database"
msgstr "Verilənlər bazası"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Date"
msgstr "Tarix"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Date a number of months before/after another date."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Date after a number of workdays (specifying weekends)."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Date after a number of workdays."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/global_filters/components/filter_date_from_to_value/filter_date_from_to_value.xml:0
msgid "Date from..."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Date is"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Date is %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Date is after"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Date is after %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Date is before"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Date is before %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Date is between"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Date is between %s and %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Date is not between"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Date is not between %s and %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Date is on or after"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Date is on or after %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Date is on or before"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Date is on or before %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Date is valid"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/currency/formulas.js:0
msgid "Date of the rate."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Date time"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Date time:"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/global_filters/components/filter_date_from_to_value/filter_date_from_to_value.xml:0
msgid "Date to..."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Date:"
msgstr "Tarix:"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Day"
msgstr "Gün"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Day and full month"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Day and short month"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Day of Month"
msgstr "Ayın Günü"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Day of Week"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Day of the month that a specific date falls on."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Day of the week of the date provided (as number)."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Days from settlement until next coupon."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Days in coupon period containing settlement date."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/assets_backend/constants.js:0
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "December"
msgstr "Dekabr"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Decrease decimal places"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Defer updates"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Degree"
msgstr "Dərəcə"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Delete"
msgstr "Silin"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Delete cell and shift left"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Delete cell and shift up"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Delete cells"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Delete column %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Delete columns"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Delete columns %s - %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Delete row %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Delete rows"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Delete rows %s - %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Delete table"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Delete table style"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Delete values"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Depreciation for an accounting period."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Depreciation of an asset using the straight-line method."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Depreciation via declining balance method."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Depreciation via double-declining balance method."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Depreciation via sum of years digit method."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Descending"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Descending (Z ⟶ A)"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Design"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Detect automatically"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Did not find value '%s' in [[FUNCTION_NAME]] evaluation."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Difference from"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Difference of two numbers."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Dimension %s does not exist"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/pivot/pivot_model.js:0
msgid "Dimension %s is not a group by"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Dimensions don't match the pivot definition"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Discard all changes"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Discount rate of a security based on price."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Display style"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Displayed as % difference from \"%s\""
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Displayed as % of \"%s\""
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Displayed as % of column total"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Displayed as % of grand total"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Displayed as % of parent \"%s\" total"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Displayed as % of parent column total"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Displayed as % of parent row total of \"%s\""
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Displayed as % of row total"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Displayed as % running total based on \"%s\""
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Displayed as difference from \"%s\""
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Displayed as index"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Displayed as rank from smallest to largest based on \"%s\""
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Displayed as rank largest to smallest based on \"%s\""
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Displayed as running total based on \"%s\""
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Displays all the values in each column or series as a percentage of the "
"total for the column or series."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Displays the rank of selected values in a specific field, listing the "
"largest item in the field as 1, and each smaller value with a higher rank "
"value."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Displays the rank of selected values in a specific field, listing the "
"smallest item in the field as 1, and each larger value with a higher rank "
"value."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Displays the value for successive items in the Base field as a running "
"total."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Displays the value in each row or category as a percentage of the total for "
"the row or category."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Displays the value that is entered in the field."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Displays values as a percentage of the grand total of all the values or data"
" points in the report."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Displays values as a percentage of the value of the Base item in the Base "
"field."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Displays values as the difference from the value of the Base item in the "
"Base field."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Displays values as the percentage difference from the value of the Base item"
" in the Base field."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Distance"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Does not contain"
msgstr "ehtiva etmir"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Doughnut"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/public_readonly_app/public_readonly.js:0
#: model_terms:ir.ui.view,arch_db:spreadsheet.public_spreadsheet_layout
msgid "Download"
msgstr "Yüklə"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Dropdown list"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Duplicate"
msgstr "Təkrar"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Duration"
msgstr "Müddət"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Edit"
msgstr "Redaktə et"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Edit Pivot"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Edit custom table style"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Edit link"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Edit table"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Edit table style"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Else"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Ending period to calculate depreciation."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Ends with"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Energy"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Engineering"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Equal."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Equivalent rate of return for a US Treasury bill."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Error"
msgstr "Xəta"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Euler's number, e (~2.718) raised to a power."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Evaluation of function [[FUNCTION_NAME]] caused a divide by zero error."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Exact number of years between two dates."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Expand all column groups"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Expand all row groups"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Expand column group"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Expand row group"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Expands or pads an array to specified row and column dimensions."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Exponential"
msgstr "Göstərici nömrəsi"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "FILTER has mismatched sizes on the range and conditions."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/assets_backend/constants.js:0
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "February"
msgstr "Fevral"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Field \"%s\" not found in pivot for measure display calculation"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Field %(field)s is not supported because of its type (%(type)s)"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/pivot/odoo_pivot.js:0
#: code:addons/spreadsheet/static/src/pivot/pivot_helpers.js:0
#: code:addons/spreadsheet/static/src/pivot/pivot_model.js:0
msgid "Field %s does not exist"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Field %s is not a measure"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Field name."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/list/list_data_source.js:0
msgid "Fields of type \"%s\" are not supported"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "File"
msgstr "Fayl"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Fill Color"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Filter"
msgstr "FİLTER"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/global_filters/plugins/global_filters_ui_plugin.js:0
msgid "Filter \"%s\" not found"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Filter button"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/public_readonly_app/public_readonly.xml:0
msgid "Filters"
msgstr "Filterlər"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Financial"
msgstr "Maliyyə"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Find and Replace"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Find and replace"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "First column"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/currency/formulas.js:0
msgid "First currency code."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "First day of the month preceding a date."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "First day of the quarter of the year a specific date falls in."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "First day of the year a specific date falls in."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "First day of week:"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "First position of string found in text, case-sensitive."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "First position of string found in text, ignoring case."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Fits points to exponential growth trend."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Fits points to linear trend derived via least-squares."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Fixed Number"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Flattens all the values from one or more ranges into a single column."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Flip axes"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Font Size"
msgstr "Şrift Ölçüsü"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Font size"
msgstr "Şrift Ölçüsü"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "For tables based on array formulas only"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Force"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Format"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Format as percent"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Format cells if..."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Format rules"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Formatting style"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Formula"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Formulas"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Freeze"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Friday"
msgstr "Cümə günü"

#. module: spreadsheet
#: model_terms:ir.ui.view,arch_db:spreadsheet.public_spreadsheet_layout
msgid "Frozen and copied on"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/components/share_button/share_button.xml:0
msgid "Frozen version - Anyone can view"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Full date time"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Full month"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Full quarter"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Full week day and month"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Function"
msgstr "Funksiya"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Function ${name} has an argument that has been declared with more than one "
"type whose type 'META'. The 'META' type can only be declared alone."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Function ${name} has at mandatory arguments declared after optional ones. "
"All optional arguments must be after all mandatory arguments."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Function ${name} has no-repeatable arguments declared after repeatable ones."
" All repeatable arguments must be declared last."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Function %s expects the parameter '%s' to be reference to a cell or range."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Function PIVOT takes an even number of arguments."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Function [[FUNCTION_NAME]] A regression of order less than 1 cannot be "
"possible."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Function [[FUNCTION_NAME]] caused a divide by zero error."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Function [[FUNCTION_NAME]] didn't find any result."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Function [[FUNCTION_NAME]] expects criteria_range and criterion to be in "
"pairs."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Function [[FUNCTION_NAME]] expects criteria_range to have the same dimension"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Function [[FUNCTION_NAME]] expects number values for %s, but got a %s."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Function [[FUNCTION_NAME]] invert matrix error, only square matrices are "
"invertible"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Function [[FUNCTION_NAME]] parameter 2 value (%s) is out of range."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Function [[FUNCTION_NAME]] parameter 2 value is out of range."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Future value of an annuity investment."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Future value of principal from series of rates."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Gauge"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Gauge Design"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "General"
msgstr "Ümumi"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/components/share_button/share_button.xml:0
msgid "Generating sharing link"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Get a pivot table."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/list/list_functions.js:0
msgid "Get the header of a list."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Get the header of a pivot."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/translation.js:0
msgid "Get the translated value of the given string"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/list/list_functions.js:0
msgid "Get the value from a list."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Get the value from a pivot."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Gets character associated with number."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Gets information about a cell."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Given a general exponential form of y = b*m^x for a curve fit, calculates b "
"if TRUE or forces b to be 1 and only calculates the m values if FALSE."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Given a general linear form of y = m*x+b for a curve fit, calculates b if "
"TRUE or forces b to be 0 and only calculates the m values if FALSE, i.e. "
"forces the curve fit to pass through the origin."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Given partial data about a linear trend, calculates various parameters about"
" the ideal linear trend using the least-squares method."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Given partial data about an exponential growth curve, calculates various "
"parameters about the best fit ideal exponential growth curve."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Granularity"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Gray"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Greater than or equal to."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Gridlines"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Group"
msgstr "Qrup"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Group column %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Group columns %s - %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Group row %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Group rows %s - %s"
msgstr ""

#. module: spreadsheet
#: model:ir.model,name:spreadsheet.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP Marşrutizasiyası"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Header row(s)"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Height value is %(_height)s. It should be greater than or equal to 1."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Hide"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Hide column %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Hide columns"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Hide columns %s - %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Hide row %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Hide rows"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Hide rows %s - %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Hide sheet"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Horizontal align"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Horizontal alignment"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Horizontal axis"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Horizontal lookup"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Hour"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Hour component of a specific time."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Humanize numbers"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Hyperbolic cosecant of any real number."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Hyperbolic cosine of any real number."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Hyperbolic cotangent of any real number."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Hyperbolic secant of any real number."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Hyperbolic sine of any real number."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Hyperbolic tangent of any real number."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/list/list_functions.js:0
msgid "ID of the list."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "ID of the pivot."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "ISO week number of the year."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Icon set"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Icons"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "If a valid match is not found, return this value."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"If number is negative, specifies the rounding direction. If 0 or blank, it "
"is rounded away from zero. Otherwise, it is rounded towards zero."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"If number is negative, specifies the rounding direction. If 0 or blank, it "
"is rounded towards zero. Otherwise, it is rounded away from zero."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "If the data is invalid"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Image"
msgstr "Şəkil"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "In [[FUNCTION_NAME]] evaluation, cannot find '%s' within '%s'."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"In [[FUNCTION_NAME]], the number of columns of the first matrix (%s) must be equal to the \n"
"        number of rows of the second matrix (%s)."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Incompatible units of measure ('%s' vs '%s')"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Increase decimal places"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Index"
msgstr "İndeks"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Index out of range."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Indicates whether the column to be searched (the first column of the "
"specified range) is sorted, in which case the closest match for search_key "
"will be returned."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Indicates whether the row to be searched (the first row of the specified "
"range) is sorted, in which case the closest match for search_key will be "
"returned."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Indicates which column in database contains the values to be extracted and "
"operated on."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Info"
msgstr "Məlumat"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Information"
msgstr "Məlumat"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Insert"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Insert %s columns"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Insert %s columns left"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Insert %s columns right"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Insert %s rows"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Insert %s rows above"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Insert %s rows below"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Insert cells"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Insert cells and shift down"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Insert cells and shift right"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Insert column"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Insert column left"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Insert column right"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Insert link"
msgstr "Link Daxil Et"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Insert row"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Insert row above"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Insert row below"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Insert sheet"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Insert table"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Interest rate of an annuity investment."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Internal rate of return given non-periodic cash flows."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Internal rate of return given periodic cashflows."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Invalid"
msgstr "Yalnış"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Invalid Maxpoint formula"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Invalid Midpoint formula"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Invalid Minpoint formula"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Invalid expression"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Invalid formula"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Invalid function name %s. Function names can exclusively contain "
"alphanumerical values separated by dots (.) or underscore (_)"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Invalid lower inflection point formula"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Invalid number of arguments for the %s function. Expected %s maximum, but "
"got %s instead."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Invalid number of arguments for the %s function. Expected %s minimum, but "
"got %s instead."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Invalid number of arguments for the %s function. Expected all arguments "
"after position %s to be supplied by groups of %s arguments"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Invalid reference"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Invalid sheet"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Invalid sheet name: %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Invalid units of measure ('%s')"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Invalid upper inflection point formula"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Inverse cosine of a value, in radians."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Inverse cotangent of a value."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Inverse hyperbolic cosine of a number."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Inverse hyperbolic cotangent of a value."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Inverse hyperbolic sine of a number."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Inverse hyperbolic tangent of a number."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Inverse sine of a value, in radians."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Inverse tangent of a value, in radians."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Is between"
msgstr "arasındadır"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Is empty"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Is equal to"
msgstr "bərabərdir"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Is greater or equal to"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Is greater than"
msgstr "Daha böyükdür"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Is greater than or equal to"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Is less or equal to"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Is less than"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Is less than or equal to"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Is not between"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Is not empty"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Is not equal to"
msgstr "-a bərabər deyil"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Is valid date"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Italic"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/assets_backend/constants.js:0
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "January"
msgstr "Yanvar"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/assets_backend/constants.js:0
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "July"
msgstr "İyul"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/assets_backend/constants.js:0
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "June"
msgstr "İyun"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Key value"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Labels are invalid"
msgstr ""

#. module: spreadsheet
#: model:ir.model,name:spreadsheet.model_res_lang
msgid "Languages"
msgstr "Dillər"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/helpers/constants.js:0
msgid "Last 180 Days"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/helpers/constants.js:0
msgid "Last 3 Years"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/helpers/constants.js:0
msgid "Last 30 Days"
msgstr "Son 30 Gün"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/helpers/constants.js:0
msgid "Last 365 Days"
msgstr "Son 365 Gün"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/helpers/constants.js:0
msgid "Last 7 Days"
msgstr "Son 7 Gün"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/helpers/constants.js:0
msgid "Last 90 Days"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Last column"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Last coupon date prior to or on the settlement date."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Last day of a month before or after a date."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Last day of the month following a date."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Last day of the quarter of the year a specific date falls in."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Last day of the year a specific date falls in."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Left"
msgstr "Sol"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Left axis"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Legend position"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Length of a string."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Less than or equal to."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Less than."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Light"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Light blue"
msgstr "Açıq göy"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Light green"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/chart/odoo_chart/odoo_line_chart.js:0
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Line"
msgstr "Sətir"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Line Break"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Line style"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Linear"
msgstr "Xətti"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Link"
msgstr "Bağlantı"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Link URL"
msgstr "Link URL"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Link label"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Link sheet"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/list/plugins/list_core_plugin.js:0
msgid "List #%s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Locale"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Logarithmic"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Logical"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Logical `and` operator."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Logical `or` operator."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Logical `xor` operator."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Logical value `false`."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Logical value `true`."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Look up a value."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Lookup"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Lower inflection point must be smaller than upper inflection point"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "MAX"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "MIN"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Magnetism"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/assets_backend/constants.js:0
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "March"
msgstr "Mart"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Match case"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Match entire cell content"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Match(es) cannot be replaced as they are part of a formula."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Math"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Matrix is not invertible"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Max"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "MaxPoint"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/spreadsheet/static/src/pivot/pivot_helpers.js:0
msgid "Maximum"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Maximum numeric value in a dataset."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Maximum of values from a table-like range."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Maximum value in a numeric dataset."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/assets_backend/constants.js:0
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "May"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Measure \"%s\" options"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Measures"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Median value in a numeric dataset."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Medium"
msgstr "Reklam Vasitəsi"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/ir_ui_menu/index.js:0
msgid "Menu %s not found. You may not have the required access rights."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Merge cells"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Merged cells are preventing this operation. Unmerge those cells and try "
"again."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Merged cells found in the spill zone. Please unmerge cells before using "
"array formulas."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Merging these cells will only preserve the top-leftmost value. Merge anyway?"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "MidPoint"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Middle"
msgstr "Orta"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Midpoint must be smaller then Maximum"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Min"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/spreadsheet/static/src/pivot/pivot_helpers.js:0
msgid "Minimum"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Minimum must be smaller then Maximum"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Minimum must be smaller then Midpoint"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Minimum numeric value in a dataset."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Minimum of values from a table-like range."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Minimum range limit must be smaller than maximum range limit"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Minimum value in a numeric dataset."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Minpoint"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Minute"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Minute component of a specific time."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Misc"
msgstr "MÜXTƏLİF"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Miscellaneous"
msgstr "Müxtəlif"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Missing closing parenthesis"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Missing opening parenthesis"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Modified Macaulay duration."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Modified internal rate of return."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Modulo (remainder) operator."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Monday"
msgstr "Bazar ertəsi"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Month"
msgstr "Ay"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Month & Year"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Month of the year a specific date falls in"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "More date formats"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "More expressions that evaluate to logical values."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "More expressions that represent logical values."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "More formats"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "More numbers or ranges to calculate for the product."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "More strings to append in sequence."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "More than one match found in DGET evaluation."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "More values to be appended using delimiter."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Move left"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Move right"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Name"
msgstr "Ad"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/list/list_functions.js:0
msgid "Name of the field."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Name of the measure."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Negative values"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Net present value given to non-periodic cash flows.."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Net working days between two dates (specifying weekends)."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Net working days between two provided days."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "New pivot"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Next coupon date after the settlement date."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "No Color"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "No active dimension in the pivot"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "No calculations"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "No columns"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "No match found in FILTER evaluation"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "No match."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "No results"
msgstr "Nəticə yoxdur"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "No results for the given arguments of TOCOL."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "No results for the given arguments of TOROW."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "No rows"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "No selected cells had whitespace trimmed."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "No unique values found"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
#: code:addons/spreadsheet/static/src/pivot/pivot_model.js:0
#: code:addons/spreadsheet/static/src/pivot/pivot_time_adapters.js:0
msgid "None"
msgstr "Heçbiri"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Not equal."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Not implemented operator %s for kind of conditional formatting:  %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/assets_backend/constants.js:0
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "November"
msgstr "Noyabr"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Nth largest element from a data set."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Nth smallest element in a data set."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Number"
msgstr "Say"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Number formatting"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Number of columns in a specified array or range."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Number of coupons between settlement and maturity."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Number of days between two dates on a 360-day year (months of 30 days)."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Number of days between two dates."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Number of empty values."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Number of payment periods for an investment."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Number of periods for an investment to reach a value."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Number of rows in a specified array or range."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Number:"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Numerical average value in a dataset, ignoring text."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Numerical average value in a dataset."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "OFFSET evaluates to an out of bounds range."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/assets_backend/constants.js:0
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "October"
msgstr "Oktyabr"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/chart/plugins/odoo_chart_core_plugin.js:0
msgid "Odoo Bar Chart"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/chart/plugins/odoo_chart_core_plugin.js:0
msgid "Odoo Line Chart"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/chart/plugins/odoo_chart_core_plugin.js:0
msgid "Odoo Pie Chart"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/hooks.js:0
msgid "Odoo Spreadsheet"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"One method of using this function is to provide a single sorted row or "
"column search_array to look through for the search_key with a second "
"argument result_range. The other way is to combine these two arguments into "
"one search_array where the first row or column is searched and a value is "
"returned from the last row or column in the array. If search_key is not "
"found, a non-exact match may be returned."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "One number divided by another."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Only a selection from a single column can be split"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Operator"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Options"
msgstr "Opsionlar"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Orange"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Order by"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Overflow"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Paint Format"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Parser"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Paste"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Paste as value"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Paste format only"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Paste special"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Pasting from the context menu is not supported in this browser. Use keyboard"
" shortcuts ctrl+c / ctrl+v instead."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Payment on the principal of an investment."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Percent"
msgstr "Faiz"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Percentage"
msgstr "Faiz"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Percentage change from key value"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Percentile"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Periodic payment for an annuity investment."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/chart/odoo_chart/odoo_pie_chart.js:0
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Pie"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Pivot #%(formulaId)s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/spreadsheet/static/src/pivot/plugins/pivot_core_global_filter_plugin.js:0
msgid "Pivot #%s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Pivot duplicated."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Pivot duplication failed"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Pivot table"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Pivot updates only work with dynamic pivot tables. Use %s or re-insert the "
"static pivot from the Data menu."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Plain text"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Please enter a number between 0 and 10000."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Please select a range of cells containing values."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Please select at latest one column to analyze."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Please select only one range of cells"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Polynomial"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Population Pyramid"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Position of item in range that matches value."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/list/list_functions.js:0
msgid "Position of the record in the list."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Positive square root of a positive number."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Positive values"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Power"
msgstr "Enerji "

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Predict value by computing a polynomial regression of the dataset."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Present value of an annuity investment."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Pressure"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Preview"
msgstr "İlkin Baxış"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Preview text"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Price of a US Treasury bill."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Price of a discount security."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Price of a security paying periodic interest."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Product of two numbers"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Product of values from a table-like range."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Progress bar"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Progress bar colors"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Purple"
msgstr "Bönövşəyi"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Q%(quarter)s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/pivot/pivot_time_adapters.js:0
msgid "Q%(quarter)s %(year)s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Q%(quarter_number)s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Quarter"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Quarter %(quarter)s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Quarter %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Quarter & Year"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Quarter of the year a specific date falls in"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "R1C1 notation is not supported."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Random integer between two values, inclusive."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Range"
msgstr "Aralıq"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Range of values"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Rank largest to smallest"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Rank smallest to largest"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Re-insert dynamic pivot"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Re-insert static pivot"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Readonly Access"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Red"
msgstr "Qırmızı"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Redo"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Reference should be defined."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Reference to the cell that will be checked for emptiness."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Reject the input"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Remove column group"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Remove duplicates"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Remove link"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Remove non-printable characters from a piece of text."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Remove row group"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Remove rule"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Remove selected filters"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Removes space characters."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Rename"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Replace"
msgstr "Yer dəyişdir"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Replace all"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Replaces existing text with new text in a string."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Replaces part of a text string with different text."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Reset"
msgstr "Sıfırla"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Reset size"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Result couldn't be automatically expanded. Please insert more columns and "
"rows."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Result couldn't be automatically expanded. Please insert more columns."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Result couldn't be automatically expanded. Please insert more rows."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Result of multiplying a series of numbers together."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Return a whole number or a decimal value."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/pivot/pivot_functions.js:0
msgid "Return the current value of a spreadsheet filter."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Returns a cell reference as a string. "
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Returns a filtered version of the source range, returning only rows or "
"columns that meet the specified conditions."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Returns a grid of random numbers between 0 inclusive and 1 exclusive."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Returns a n x n unit matrix, where n is the input dimension."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Returns a range reference shifted by a specified number of rows and columns "
"from a starting cell reference."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Returns a result array constrained to a specific width and height."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Returns a sequence of numbers."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Returns a value depending on multiple logical expressions."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Returns opposite of provided logical value."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Returns the content of a cell, specified by a string."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Returns the content of a cell, specified by row and column offset."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Returns the error value #N/A."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Returns the first n items in a data set after performing a sort."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Returns the interest paid at a particular period of an investment."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Returns the matrix determinant of a square matrix."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Returns the maximum value in a range of cells, filtered by a set of "
"criteria."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Returns the minimum value in a range of cells, filtered by a set of "
"criteria."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Returns the multiplicative inverse of a square matrix."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Returns the rank of a specified value in a dataset."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Returns value depending on logical expression."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Reverse icons"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Right"
msgstr "Sağ"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Right axis"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Rounds a number according to standard rules."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Rounds a number down to the nearest integer that is less than or equal to "
"it."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Rounds a number up to the nearest odd integer."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Rounds down a number."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Rounds number down to nearest multiple of factor."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Rounds number up to nearest multiple of factor."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Rounds up a number."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Row above"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Row below"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Row number of a specified cell."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Rows"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Running total"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "SUM"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Saturday"
msgstr "Şənbə günü"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Save"
msgstr "Yadda Saxla"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Scatter"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Scorecard"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Search"
msgstr "Axtarın"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Search a range for a match and return the corresponding item from a second "
"range."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Search in formulas"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Search..."
msgstr "Axtar..."

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Secant of an angle provided in radians."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Second"
msgstr "İkinci"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/currency/formulas.js:0
msgid "Second currency code."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/list/index.js:0
msgid "See record"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/pivot/index.js:0
msgid "See records"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Select all"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/global_filters/components/filter_date_value/filter_date_value.xml:0
#: code:addons/spreadsheet/static/src/global_filters/components/filter_value/filter_value.xml:0
msgid "Select period..."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/global_filters/components/filter_date_value/filter_date_value.xml:0
msgid "Select year..."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Semicolon"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Separator"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/assets_backend/constants.js:0
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "September"
msgstr "Sentyabr"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Serie type"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Series"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Series color"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Series name"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Settings"
msgstr "Parametrlər"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/components/share_button/share_button.xml:0
msgid "Share"
msgstr "Paylaşın"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/components/share_button/share_button.xml:0
msgid "Share to web"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Sheet"
msgstr ""

#. module: spreadsheet
#. odoo-python
#: code:addons/spreadsheet/models/spreadsheet_mixin.py:0
msgid "Sheet1"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Shift down"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Shift left"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Shift right"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Shift up"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Short month"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Short week day"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Show"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/public_readonly_app/public_readonly.xml:0
msgid "Show Filters"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Show a warning"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Show connector lines"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Show formula help"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Show measure as:"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Show subtotals at the end of series"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Show trend line"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Show values"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Show values as"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Sine of an angle provided in radians."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Single color"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Single value from a table-like range."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/pivot/pivot_model.js:0
msgid "Some measures are not available: %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Some used characters are not allowed in a sheet name (Forbidden characters "
"are %s)."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Sort ascending (A ⟶ Z)"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Sort column"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Sort columns"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Sort descending (Z ⟶ A)"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Sort range"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Sorts the rows of a given array or range by the values in one or more "
"columns."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Space"
msgstr "Ara"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Specific range"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Speed"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Spill range is not empty"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Split text by specific character delimiter(s)."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Split text into columns"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Split text to columns"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Splitting will overwrite existing content"
msgstr ""

#. module: spreadsheet
#: model:ir.model.fields,field_description:spreadsheet.field_spreadsheet_mixin__spreadsheet_data
msgid "Spreadsheet Data"
msgstr ""

#. module: spreadsheet
#: model:ir.model.fields,field_description:spreadsheet.field_spreadsheet_mixin__spreadsheet_file_name
msgid "Spreadsheet File Name"
msgstr ""

#. module: spreadsheet
#: model:ir.model.fields,field_description:spreadsheet.field_spreadsheet_mixin__spreadsheet_binary_data
msgid "Spreadsheet file"
msgstr ""

#. module: spreadsheet
#: model:ir.model,name:spreadsheet.model_spreadsheet_mixin
msgid "Spreadsheet mixin"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/components/share_button/share_button.xml:0
msgid "Spreadsheet published"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Spreadsheet settings"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Stacked Area"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Stacked Bar"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Stacked Column"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Stacked Line"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Stacked area chart"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Stacked bar chart"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Stacked column chart"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Stacked line chart"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Standard"
msgstr "Standart"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Standard deviation of entire population (text as 0)."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Standard deviation of entire population from table."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Standard deviation of entire population."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Standard deviation of population sample from table."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Standard deviation of sample (text as 0)."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Standard deviation."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Starting period to calculate depreciation."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Starts with"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Statistical"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Strictly greater than."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Strikethrough"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Style color"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Style name"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Style options"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Style template"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Substring from beginning of specified string."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Subtotal"
msgstr "Yekun"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Subtotals"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/spreadsheet/static/src/pivot/pivot_helpers.js:0
msgid "Sum"
msgstr "Cəm "

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Sum of a series of numbers and/or cells."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Sum of two numbers."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Sum of values from a table-like range."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Sums a range depending on multiple criteria."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Sunday"
msgstr "Bazar günü"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Symbol"
msgstr "Simvol"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"TRUE or FALSE indicating whether to sort sort_column in ascending order. "
"FALSE sorts in descending order."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Table"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Tangent of an angle provided in radians."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Temperature"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Tests whether two strings are identical."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Text"
msgstr "Mətn"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Text Color"
msgstr "Lentə yazı rəngi"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Text contains"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Text contains \"%s\""
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Text does not contain \"%s\""
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Text does not contains"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Text is exactly"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Text is exactly \"%s\""
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Text is valid email"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Text is valid link"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The _delimiter (%s) must be not be empty."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The amortization period, in terms of number of periods."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The amount invested (irrespective of face value of each security)."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The amount invested in the security."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The amount of each payment made."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The amount of initial capital or value to compound against."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The amount per period to be paid."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The amount to be received at maturity."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The amount to increment each value in the sequence"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The anchor must be part of the provided zone"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The angle to convert from radians to degrees."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The angle to find the cosecant of, in radians."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The angle to find the cosine of, in radians."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The angle to find the cotangent of, in radians."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The angle to find the secant of, in radians."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The angle to find the sine of, in radians."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The angle to find the tangent of, in radians."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The annualized rate of interest."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The argument %s is not a valid measure. Here are the measures: %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The argument dimension must be positive"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The argument is missing. Please provide a value"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The argument square_matrix must have the same number of columns and rows."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The arguments array_x and array_y must contain at least one pair of numbers."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The arguments condition must be a single column or row."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The arguments conditions must have the same dimensions."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The array of ranges containing the values to be counted."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The array or range containing dependent (y) values that are already known, "
"used to curve fit an ideal exponential growth curve."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The array or range containing dependent (y) values that are already known, "
"used to curve fit an ideal linear trend."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The array or range containing the data to consider, structured in such a way"
" that the first row contains the labels for each column's values."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The array or range containing the dataset to consider."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The array or range of values that will be reduced by corresponding entries "
"in array_y, squared, and added together."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The array or range of values that will be subtracted from corresponding "
"entries in array_x, the result squared, and all such results added together."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The array or range of values whose squares will be added to the squares of "
"corresponding entries in array_x and added together."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The array or range of values whose squares will be added to the squares of "
"corresponding entries in array_y and added together."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The array or range of values whose squares will be reduced by the squares of"
" corresponding entries in array_y and added together."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The array or range of values whose squares will be subtracted from the "
"squares of corresponding entries in array_x and added together."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The array that contains the columns to be returned."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The array that contains the rows to be returned."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The array to expand."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The array which will be transformed."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The base (%s) must be between 2 and 36 inclusive."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The base to convert the value from."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The baseline value is invalid"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The cashflow_amounts and cashflow_dates ranges must have the same "
"dimensions."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The cashflow_amounts must include negative and positive values."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The cell whose column number will be returned. Column A corresponds to 1. By"
" default, the function use the cell in which the formula is entered."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The cell whose row number will be returned. By default, this function uses "
"the cell in which the formula is entered."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The cell you are trying to edit has been deleted."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The character or characters to use to split text."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The character or string to place between each concatenated value."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The character within text_to_search at which to start the search."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The chart definition is invalid for an unknown reason"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The column index of the value to be returned, where the first column in "
"range is numbered 1."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The column number (not name) of the cell reference. A is column number 1. "
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The columns argument (%s) must be strictly positive."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The columns arguments (%s) must be greater or equal than the number of "
"columns of the array."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The columns arguments must be between -%s and %s (got %s), excluding 0."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The columns indexes of the columns to be returned."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/currency/formulas.js:0
msgid "The company to take the exchange rate from."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The control to ignore blanks and errors. 0 (default) is to keep all values, "
"1 is to ignore blanks, 2 is to ignore errors, and 3 is to ignore blanks and "
"errors."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The cost (%s) must be positive or null."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The cost (%s) must be strictly positive."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The covariance of a dataset."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The criteria range contains %s row, it must be at least 2 rows."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The current value of the annuity."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The current window is too small to display this sheet properly. Consider "
"resizing your browser window or adjusting frozen rows and columns."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The data points to return the y values for on the ideal curve fit."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The data range is invalid"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The data to be filtered."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The data to be sorted."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The data to filter by unique entries."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The data you entered in %s violates the data validation rule set on the cell:\n"
"%s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The dataset is invalid"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The date for which to determine the ISO week number. Must be a reference to "
"a cell containing a date, a function returning a date type, or a number."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The date for which to determine the day of the week. Must be a reference to "
"a cell containing a date, a function returning a date type, or a number."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The date for which to determine the week number. Must be a reference to a "
"cell containing a date, a function returning a date type, or a number."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The date from which to begin counting."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The date from which to calculate the end of quarter."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The date from which to calculate the end of the year."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The date from which to calculate the result."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The date from which to calculate the start of quarter."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The date from which to calculate the start of the year."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The date from which to extract the day."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The date from which to extract the month."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The date from which to extract the quarter."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The date from which to extract the year."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The date the asset was purchased."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The date the first period ended."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The date the security was initially issued."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The date_string (%s) cannot be parsed to date/time."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The day component of the date."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The day_count_convention (%s) must be between 0 and 4 inclusive."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The deprecation rate."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The depreciation factor (%s) must be strictly positive."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The discount (%s) must be different from -1."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The discount (%s) must be smaller than 1."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The discount (%s) must be strictly positive."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The discount rate of the bill at time of purchase."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The discount rate of the investment over one period."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The discount rate of the security at time of purchase."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The discount rate of the security invested in."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The divisor must be different from 0."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The divisor must be different from zero."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The effective interest rate per year."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The effective rate (%s) must must strictly greater than 0."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The end date of the period from which to calculate the number of net working"
" days."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The end date to consider in the calculation."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The end date to consider in the calculation. Must be a reference to a cell "
"containing a DATE, a function returning a DATE type, or a number."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The end date to consider in the calculation. Must be a reference to a cell "
"containing a date, a function returning a date type, or a number."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The end of the date range."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The end_date (%s) must be positive or null."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The end_period (%s) must be greater or equal than 0."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The end_period (%s) must be smaller or equal to the life (%s)."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The expected annual yield of the security."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The exponent (%s) must be an integer when the base is negative."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The exponent to raise base to."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The exponent to raise e."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The extract_length argument (%s) must be positive or null."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The factor (%s) must be positive when the value (%s) is positive."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The factor by which depreciation decreases."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/list/list_data_source.js:0
msgid "The field %s does not exist or you do not have access to that field"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The field (%(fieldValue)s) must be one of %(dimRowDB)s or must be a number "
"between 1 and %s inclusive."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The field (%s) must be one of %s."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The field must be a number or a string"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The first addend."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The first column index of the columns to be returned."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The first condition to be evaluated. This can be a boolean, a number, an "
"array, or a reference to any of those."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The first future cash flow."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The first matrix in the matrix multiplication operation."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The first multiplicand."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The first number in the sequence"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The first number or range to add together."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The first number or range to calculate for the product."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The first number to compare."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The first range to be appended."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The first range to flatten."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The first range whose entries will be multiplied with corresponding entries "
"in the other ranges."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The first row index of the rows to be returned."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The first string to compare."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The first value must be a number"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The first value or range in which to count the number of blanks."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The first value or range of the population."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The first value or range of the sample."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The first value or range to consider for uniqueness."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The first value or range to consider when calculating the average value."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The first value or range to consider when calculating the maximum value."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The first value or range to consider when calculating the median value."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The first value or range to consider when calculating the minimum value."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The first value or range to consider when counting."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The first value."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The first_period (%s) must be smaller or equal to the last_period (%s)."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The first_period (%s) must be strictly positive."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The formatting unit should be 'k', 'm' or 'b'."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The formatting unit. Use 'k', 'm', or 'b' to force the unit"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The frequency (%s) must be one of %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The full URL of the link enclosed in quotation marks."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The function [[FUNCTION_NAME]] expects a boolean value, but '%s' is a text, "
"and cannot be coerced to a boolean."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The function [[FUNCTION_NAME]] expects a number value between %s and %s "
"inclusive, but receives %s."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The function [[FUNCTION_NAME]] expects a number value to be greater than or "
"equal to 1, but receives %s."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The function [[FUNCTION_NAME]] expects a number value, but '%s' is a string,"
" and cannot be coerced to a number."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The function [[FUNCTION_NAME]] has an argument with value '%s'. It should be"
" one of: %s."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The function [[FUNCTION_NAME]] result cannot be negative"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The function [[FUNCTION_NAME]] result must be greater than or equal "
"01/01/1900."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The future value of the investment."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The future value remaining after the final payment has been made."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The future_value (%s) must be strictly positive."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The high (%s) must be greater than or equal to the low (%s)."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The high end of the random range."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The hour component of the time."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The index from the left of string from which to begin extracting. The first "
"character in string has the index 1."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The index of the column in range or a range outside of range containing the "
"values by which to sort."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The index of the column to be returned from within the reference range of "
"cells."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The index of the row to be returned from within the reference range of "
"cells."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The info_type should be one of %s."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The initial cost of the asset."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The initial string."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The instance of search_for within text_to_search to replace with "
"replace_with. By default, all occurrences of search_for are replaced; "
"however, if occurrence_number is specified, only the indicated instance of "
"search_for is replaced."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The interest rate paid on funds invested."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The interest rate."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The investment (%s) must be strictly positive."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The investment's current value."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The investment's desired future value."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The issue (%s) must be positive or null."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The key value is invalid"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/pivot/pivot_functions.js:0
msgid "The label of the filter whose value to return."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The last_period (%s) must be smaller or equal to the number_of_periods (%s)."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The last_period (%s) must be strictly positive."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The length of the segment to extract."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The life (%s) must be strictly positive."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The logarithm of a number, base e (euler's number)."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The low end of the random range."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The lower inflection point value must be a number"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The matrix is not invertible."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The maturity (%s) must be strictly greater than the settlement (%s)."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The maturity date of the security."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The maturity or end date of the security, when it can be redeemed at face, "
"or par value."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The maximum (%s) and minimum (%s) must be integers when whole_number is "
"TRUE."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The maximum (%s) must be greater than or equal to the minimum (%s)."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The maximum number of cells for each column, rounded down to the nearest "
"whole number."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The maximum number of cells for each row, rounded down to the nearest whole "
"number."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The maximum number you would like returned."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The maximum range limit value must be a number"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The maxpoint must be a number"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/chart/odoo_menu/figure_component.js:0
msgid ""
"The menu linked to this chart doesn't have an corresponding action. Please "
"link the chart to another menu."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The midpoint must be a number"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The minimum number you would like returned."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The minimum range limit value must be a number"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The minpoint must be a number"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The minuend, or number to be subtracted from."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The minute component of the time."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/data_sources/data_source.js:0
#: code:addons/spreadsheet/static/src/pivot/odoo_pivot_loader.js:0
msgid "The model \"%(model)s\" does not exist."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/global_filters/components/filter_value/filter_value.js:0
msgid ""
"The model (%(model)s) of this global filter is not valid (it may have been "
"renamed/deleted)."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The month (%s) must be between 1 and 12 inclusive."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The month component of the date."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The net present value of an investment based on a series of periodic cash "
"flows and a discount rate."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The nominal interest rate per year."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The nominal rate (%s) must be strictly greater than 0."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The number for which to calculate the positive square root."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The number of characters in the text to be replaced."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The number of characters to return from the left side of string."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The number of characters to return from the right side of string."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The number of columns (%s) must be positive."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The number of columns in the constrained array."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The number of columns in the expanded array. If missing, columns will not be"
" expanded."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The number of columns must be positive."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The number of columns of the range to return starting at the offset target."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The number of columns to be returned."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The number of columns to offset by."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The number of columns to return"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The number of compounding periods per year."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The number of decimal places to which to round."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The number of interest or coupon payments per year (1, 2, or 4)."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The number of items to return."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The number of months before (negative) or after (positive) 'start_date' to "
"calculate."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The number of months before (negative) or after (positive) 'start_date' to "
"consider."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The number of months in the first year of depreciation."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The number of numeric values in dataset."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The number of payments to be made."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The number of periods by year (%s) must strictly greater than 0."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The number of periods must be different than 0."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The number of periods over which the asset is depreciated."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The number of periods."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The number of rows (%s) must be positive."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The number of rows in the constrained array."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The number of rows in the expanded array. If missing, rows will not be "
"expanded."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The number of rows must be positive."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The number of rows of the range to return starting at the offset target."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The number of rows to be returned."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The number of rows to offset by."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The number of rows to return"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The number of significant digits to the right of the decimal point to "
"retain."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The number of the character to look up from the current Unicode table in "
"decimal format."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The number of the payment period to begin the cumulative calculation."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The number of the payment period to end the cumulative calculation."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The number of values in a dataset."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The number of which to return the absolute value."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The number of working days to advance from start_date. If negative, counts "
"backwards."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The number pi."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The number to be divided to find the remainder."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The number to be divided."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The number to convert."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The number to divide by."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The number to have its sign reversed. Equivalently, the number to multiply "
"by -1."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The number to raise to the exponent power."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The number to return."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The number to round down to the nearest integer."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The number to whose multiples number will be rounded."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The number to whose multiples number will be rounded. The sign of "
"significance will be ignored."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The number to whose multiples value will be rounded."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The number, date or time to format."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The number."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The number_of_characters (%s) must be positive or null."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The number_of_periods (%s) must be greater than 0."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The occurrenceNumber (%s) must be positive or null."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The one-dimensional array to be searched."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The order of the polynomial to fit the data, between 1 and 6."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The other range whose entries will be multiplied with corresponding entries "
"in the other ranges."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The pattern by which to format the number, enclosed in quotation marks."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The pattern or test to apply to criteria_range."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The pattern or test to apply to criteria_range1, such that each cell that "
"evaluates to TRUE will be included in the filtered set."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The pattern or test to apply to criteria_range1."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The pattern or test to apply to criteria_range2."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The pattern or test to apply to range."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The percentile whose value within data will be calculated and returned."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The percentile, exclusive of 0 and 1, whose value within 'data' will be "
"calculated and returned."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The period (%s) must be less than or equal life (%s)."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The period (%s) must be less than or equal to %s."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The period (%s) must be positive or null."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The period (%s) must be strictly positive."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The period for which you want to view the interest payment."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The period must be between 1 and number_of_periods (%s)"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The pivot cannot be created because cell %s contains a reserved value"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The pivot cannot be created because cell %s contains an error"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The pivot cannot be created because cell %s is empty"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The pivot cannot be created because the dataset is missing."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The position (%s) must be greater than or equal to 1."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The position where the replacement will begin (starting from 1)."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The present value (%s) must be strictly positive."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The present value of the investment."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The present_value (%s) must be strictly positive."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The price (%s) must be strictly positive."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The price at which the security is bought per 100 face value."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The price at which the security is bought."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The price quotation given as a decimal value."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The price quotation given using fractional decimal conventions."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The provided anchor is invalid. The cell must be part of the zone."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The purchase_date (%s) must be before the first_period_end (%s)."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The purchase_date (%s) must be positive or null."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The range containing the dataset to consider."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The range containing the return value. Should have the same dimensions as "
"lookup_range."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The range containing the set of classes."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The range from which to return a result. The value returned corresponds to "
"the location where search_key is found in search_range. This range must be "
"only a single row or column and should not be used if using the "
"search_result_array method."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The range is invalid"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The range is out of the sheet"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The range must be a single row or a single column."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The range of cells from which the maximum will be determined."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The range of cells from which the minimum will be determined."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The range of cells from which the number of unique values will be counted."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The range of cells from which the values are returned."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The range of cells over which to evaluate criterion1."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The range representing the array or matrix of dependent data."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The range representing the array or matrix of independent data."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The range representing the array or matrix of observed data."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The range representing the array or matrix of predicted data."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The range that is tested against criterion."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The range to average."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The range to average. If not included, criteria_range is used for the "
"average instead."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The range to be summed, if different from range."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The range to be transposed."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The range to check against criterion."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The range to check against criterion1."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The range to consider for the search. Should be a single column or a single "
"row."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The range to consider for the search. The first column in the range is "
"searched for the key specified in search_key."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The range to consider for the search. The first row in the range is searched"
" for the key specified in search_key."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The range to constrain."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The range to sum."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The range to wrap."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The range which is tested against criterion."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The range whose column count will be returned."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The range whose row count will be returned."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The rank from largest to smallest of the element to return."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The rank from smallest to largest of the element to return."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The rate (%s) must be positive or null."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The rate (%s) must be strictly positive."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The rate at which the investment grows each period."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The rate_guess (%s) must be strictly greater than -1."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The redemption (%s) must be strictly positive."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The redemption amount per 100 face value, or par."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The reference to the cell."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The result_range must be a single row or a single column."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The return (as a percentage) earned on reinvestment of income received from "
"the investment."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The returned value if condition1 is TRUE."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The row index of the value to be returned, where the first row in range is "
"numbered 1."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The row number of the cell reference. "
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The rows argument (%s) must be strictly positive."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The rows arguments (%s) must be greater or equal than the number of rows of "
"the array."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The rows arguments must be between -%s and %s (got %s), excluding 0."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The rows indexes of the rows to be returned."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The rule is invalid for an unknown reason"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The salvage (%s) must be positive or null."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The salvage (%s) must be smaller or equal than the cost (%s)."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The sample covariance of a dataset."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The search method. 1 (default) finds the largest value less than or equal to"
" search_key when range is sorted in ascending order. 0 finds the exact value"
" when range is unsorted. -1 finds the smallest value greater than or equal "
"to search_key when range is sorted in descending order."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The second addend."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The second argument is missing. Please provide a value"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The second component of the time."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The second matrix in the matrix multiplication operation."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The second multiplicand."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The second number to compare."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The second string to compare."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The second value must be a number"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The second value."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The settlement (%s) must be greater than or equal to the issue (%s)."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The settlement date (%s) must at most one year after the maturity date (%s)."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The settlement date (%s) must be strictly greater than the issue date (%s)."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The settlement date of the security, the date after issuance when the "
"security is delivered to the buyer."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The sheet name cannot be empty."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The single period within life for which to calculate depreciation."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The start date of the period from which to calculate the number of net "
"working days."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The start date to consider in the calculation."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The start date to consider in the calculation. Must be a reference to a cell"
" containing a DATE, a function returning a DATE type, or a number."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The start date to consider in the calculation. Must be a reference to a cell"
" containing a date, a function returning a date type, or a number."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The start of the date range."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The start_date (%s) must be positive or null."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The start_period (%s) must be greater or equal than 0."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The start_period (%s) must be smaller or equal to the end_period (%s)."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The starting point from which to count the offset rows and columns."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The starting unit, the unit currently assigned to value"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The starting_at (%s) must be greater than or equal to 1."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The starting_at argument (%s) must be positive greater than one."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The string from which the left portion will be returned."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The string from which the right portion will be returned."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The string representing the date."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The string that holds the time representation."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The string that will replace search_for."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The string to convert to lowercase."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The string to convert to uppercase."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The string to extract a segment from."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The string to look for within text_to_search."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The string to search for within text_to_search."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The string whose length will be returned."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The subtrahend, or number to subtract from value1."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The table zone is invalid for an unknown reason"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The table_number (%s) is out of range."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The text or reference to a cell containing text to be trimmed."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The text to display in the cell, enclosed in quotation marks."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The text to divide."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The text to search for the first occurrence of search_for."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The text which will be inserted into the original text."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The text which will be returned with the first letter of each word in "
"uppercase and all other letters in lowercase."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The text whose non-printable characters are to be removed."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The text within which to search and replace."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The text, a part of which will be replaced."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The text_to_search must be non-empty."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The time from which to calculate the hour component."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The time from which to calculate the minute component."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The time from which to calculate the second component."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The time_string (%s) cannot be parsed to date/time."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The type (%s) is out of range."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The type (%s) must be 1, 2 or 3."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The type of information requested. Can be one of %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The unit (%s) must be strictly positive."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The unit of measure into which to convert value"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The units of the desired fraction, e.g. 8 for 1/8ths or 32 for 1/32nds."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The units of the fraction, e.g. 8 for 1/8ths or 32 for 1/32nds."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The upper inflection point value must be a number"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value (%s) cannot be between -1 and 1 inclusive."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value (%s) must be a valid base %s representation."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value (%s) must be between -1 and 1 exclusive."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value (%s) must be between -1 and 1 inclusive."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value (%s) must be greater than or equal to 1."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value (%s) must be positive or null."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value (%s) must be strictly positive."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value does not match the custom formula data validation rule"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The value for which to calculate the inverse cosine. Must be between -1 and "
"1, inclusive."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value for which to calculate the inverse cotangent."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The value for which to calculate the inverse hyperbolic cosine. Must be "
"greater than or equal to 1."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The value for which to calculate the inverse hyperbolic cotangent. Must not "
"be between -1 and 1, inclusive."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value for which to calculate the inverse hyperbolic sine."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The value for which to calculate the inverse hyperbolic tangent. Must be "
"between -1 and 1, exclusive."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The value for which to calculate the inverse sine. Must be between -1 and 1,"
" inclusive."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value for which to calculate the inverse tangent."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value for which to calculate the logarithm, base e."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value must be %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value must be a boolean"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value must be a date"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value must be a date after %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value must be a date before %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value must be a date between %s and %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value must be a date not between %s and %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value must be a date on or after %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value must be a date on or before %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value must be a formula"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value must be a number"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value must be a text that contains \"%s\""
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value must be a text that does not contain \"%s\""
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value must be a valid date"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value must be a valid email address"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value must be a valid link"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value must be a valid range"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value must be a value in the range %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value must be between %s and %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value must be equal to %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value must be exactly \"%s\""
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value must be greater or equal to %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value must be greater than %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value must be less or equal to %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value must be less than %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value must be one of: %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value must be the date %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value must not be a formula"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value must not be between %s and %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value must not be empty"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value must not be equal to %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value of the asset at the end of depreciation."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value or values to be appended using delimiter."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value should be a scalar or a 1x1 matrix"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value the function returns if logical_expression is FALSE."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value the function returns if logical_expression is TRUE."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value the function returns if value is an #N/A error."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value the function returns if value is an error."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value to append to value1."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value to be checked."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value to be truncated."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value to be verified as a logical TRUE or FALSE."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value to be verified as a number."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value to be verified as an error type."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value to be verified as even."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value to be verified as text."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value to interpret as a percentage."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value to return if value itself is not #N/A an error."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value to return if value itself is not an error."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value to round down to the nearest integer multiple of factor."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The value to round down to the nearest integer multiple of significance."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value to round to places number of places, always rounding down."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value to round to places number of places, always rounding up."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value to round to places number of places."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value to round to the next greatest odd number."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value to round up to the nearest integer multiple of factor."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value to round up to the nearest integer multiple of significance."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value to search for."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value to search for. For example, 42, 'Cats', or I24."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value to test against value1 for equality."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value to test against value1 for inequality."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value to test as being greater than or equal to value2."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value to test as being greater than value2."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value to test as being less than or equal to value2."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value to test as being less than value2."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value to which value2 will be appended."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value whose rank will be determined."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value with which to fill the extra cells in the range."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value with which to pad."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The value(s) on the x-axis to forecast."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The values of the independent variable(s) corresponding with known_data_y."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The weekend (%s) must be a string or a number in the range 1-7 or 11-17."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The weekend must be a number or a string."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The weekend must be different from '1111111'."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The x coordinate of the endpoint of the line segment for which to calculate "
"the angle from the x-axis."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"The y coordinate of the endpoint of the line segment for which to calculate "
"the angle from the x-axis."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The year (%s) must be between 0 and 9999 inclusive."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The year component of the date."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The yield (%s) must be positive or null."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "The yield of a US Treasury bill based on price."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/list/list_functions.js:0
msgid "There is no list with id \"%s\""
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "There is no match for the selected separator in the selection"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "There is no pivot with id \"%s\""
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "There is not enough visible sheets"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"There must be both positive and negative values in [payment_amount, "
"present_value, future_value]."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "There must be both positive and negative values in cashflow_amounts."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"There must be the same number of values in cashflow_amounts and "
"cashflow_dates."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"This formula has over 100 parts. It can't be processed properly, consider "
"splitting it into multiple cells"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/currency/formulas.js:0
msgid ""
"This function takes in two currency codes as arguments, and returns the "
"exchange rate from the first currency to the second as float."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "This operation is not allowed due to an overlapping frozen pane."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "This operation is not allowed with multiple selections."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"This operation is not possible due to a merge. Please remove the merges "
"first than try again."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "This pivot is not used"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "This range is invalid"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "This setting affects all users."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "This will overwrite data in the subsequent columns. Split anyway?"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Thresholds"
msgstr ""

#. module: spreadsheet
#: model:ir.model.fields,field_description:spreadsheet.field_spreadsheet_mixin__thumbnail
msgid "Thumbnail"
msgstr "Miniatür"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Thursday"
msgstr "Cümə axşamı"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Time"
msgstr "Vaxt"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Title"
msgstr "Başlıq"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Top"
msgstr "Ən məşhur"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/spreadsheet/static/src/pivot/odoo_pivot.js:0
msgid "Total"
msgstr "Cəmi"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Total row"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Transforms a range of cells into a single column."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Transforms a range of cells into a single row."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Transposes the rows and columns of a range."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Treat labels as text"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Trend line"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Trend line color"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Trend line for %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Trim whitespace"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Trimmed whitespace from %s cells."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Truncates a number."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Tuesday"
msgstr "Çərşənbə axşamı"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Type"
msgstr "Tip"

#. module: spreadsheet
#. odoo-python
#: code:addons/spreadsheet/models/spreadsheet_mixin.py:0
msgid "Uh-oh! Looks like the spreadsheet file contains invalid data."
msgstr ""

#. module: spreadsheet
#. odoo-python
#: code:addons/spreadsheet/models/spreadsheet_mixin.py:0
msgid ""
"Uh-oh! Looks like the spreadsheet file contains invalid data.\n"
"\n"
"%(errors)s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/pivot/pivot_model.js:0
msgid "Unable to fetch the label of %(id)s of model %(model)s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Underline"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Undo"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Unexpected token: %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Unfreeze"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Ungroup column %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Ungroup columns %s - %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Ungroup row %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Ungroup rows %s - %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Unhide all columns"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Unhide all rows"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Unhide columns"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Unhide rows"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Unique rows in the provided source range."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Unknown function"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Unknown function: \"%s\""
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Unsorted"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/helpers/constants.js:0
msgid "Untitled spreadsheet"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Up to current column"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Up to current row"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Update"
msgstr "Yeniləyin"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Use first value as subtotal"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Use row %s as headers"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Value"
msgstr "Dəyər"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Value at a given percentile of a dataset exclusive of 0 and 1."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Value at a given percentile of a dataset."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Value change from key value"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Value for parameter %d is missing, while the function [[FUNCTION_NAME]] "
"expect a number or a range."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Value if it is not an #N/A error, otherwise 2nd argument."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Value if it is not an error, otherwise 2nd argument."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Value in list"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Value in range"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Value in range %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Value interpreted as a percentage."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Value is between %s and %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Value is equal to %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Value is greater or equal to %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Value is greater than %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Value is less or equal to %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Value is less than %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Value is not between %s and %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Value is not equal to %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Value nearest to a specific quartile of a dataset exclusive of 0 and 4."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Value nearest to a specific quartile of a dataset."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Value not found in the given data."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Value one of: %s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Value or formula"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/translation.js:0
msgid "Value to translate."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Value."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Values"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Values to average."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Variable declining balance. WARNING : does not handle decimal periods."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Variance of a population from a table-like range."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Variance of entire population (text as 0)."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Variance of entire population."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Variance of population sample from table-like range."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Variance of sample (text as 0)."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Variance."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Vertical align"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Vertical axis"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Vertical axis position"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Vertical lookup."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "View"
msgstr "Baxın"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Volume"
msgstr "Həcmi"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/pivot/pivot_time_adapters.js:0
msgid "W%(week)s %(year)s"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Waterfall"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "Waterfall design"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"We found data next to your selection. Since this data was not selected, it "
"will not be sorted. Do you want to extend your selection?"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Web"
msgstr "Veb"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Wednesday"
msgstr "Çərşənbə günü"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Week"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Week & Year"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Week number of the year."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Weight"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Weighted average."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Weights for each corresponding value."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "When value is"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "When weekend is a string (%s) it must be composed of \"0\" or \"1\"."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Whether a value is `true` or `false`."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Whether a value is a number."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Whether a value is an error other than #N/A."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Whether a value is an error."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Whether a value is non-textual."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Whether a value is text."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Whether a value is the error #N/A."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Whether or not to divide text around each character contained in delimiter."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Whether or not to remove empty text messages from the split results. The default behavior is to treat \n"
"        consecutive delimiters as one (if TRUE). If FALSE, empty cells values are added between consecutive delimiters."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Whether payments are due at the end (0) or beginning (1) of each period."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Whether the array should be scanned by column. True scans the array by column and false (default) \n"
"      scans the array by row."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Whether the provided value is even."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Whether the referenced cell is empty"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Whether to consider the values in data in descending or ascending order."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Whether to filter the data by columns or by rows."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Whether to include the column titles or not."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Whether to include total/sub-totals or not."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Whether to return only entries with no duplicates."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Whether to switch to straight-line depreciation when the depreciation is "
"greater than the declining balance calculation."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Which quartile value to return."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Which quartile value, exclusive of 0 and 4, to return."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Width value is %(_width)s. It should be greater than or equal to 1."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Wrap"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Wrapping"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Wraps the provided row or column of cells by columns after a specified "
"number of elements to form a new array."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Wraps the provided row or column of cells by rows after a specified number "
"of elements to form a new array."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Wrong function call"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Wrong number of arguments. Expected an even number of arguments."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Wrong size for %s. Expected a range of size 1x%s. Got %sx%s."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"Wrong value of 'display_ties_mode'. Expected a positive number between 0 and"
" 3. Got %s."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Wrong value of 'n'. Expected a positive number. Got %s."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Year"
msgstr "İL"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "Year specified by a given date."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/helpers/constants.js:0
msgid "Year to Date"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "You can't merge cells inside of an existing filter."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "You cannot create overlapping tables."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "[[FUNCTION_NAME]] evaluates to an out of bounds range."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "[[FUNCTION_NAME]] evaluates to an out of range column value %s."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "[[FUNCTION_NAME]] evaluates to an out of range row value %s."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "[[FUNCTION_NAME]] expects non-empty ranges for both parameters."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "[[FUNCTION_NAME]] expects number values."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "[[FUNCTION_NAME]] expects the weight to be positive or equal to 0."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "[[FUNCTION_NAME]] has mismatched argument count %s vs %s."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"[[FUNCTION_NAME]] has mismatched dimensions for argument %s (%s vs %s)."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "[[FUNCTION_NAME]] has mismatched range sizes."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "[[FUNCTION_NAME]] has no valid input data."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "[[FUNCTION_NAME]] needs at least two values for both parameters."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "activeSheet"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "allSheets"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "and"
msgstr "və"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "arrow"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "asc"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "below"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "below or equal to"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "bottom"
msgstr "Alt"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "code"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "default:"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "desc"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "difference"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "e.g. 'Link label'"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "e.g. 'http://www.odoo.com'"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "e.g. 'replace me'"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "e.g. 'search me'"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "e.g. A1:A2"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/global_filters/components/filter_date_value/filter_date_value.xml:0
msgid "empty"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "exact date"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "exponential"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "false"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "formula"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "ge"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "gt"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "in the past month"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "in the past week"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "in the past year"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "left"
msgstr "Sol"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "linear"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "logarithmic"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "lookup_range should be either a single row or single column."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "match_mode should be a value in [-1, 0, 1, 2]."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "more rows at the bottom"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "negative"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "none"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "number"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "number of columns"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "number of rows"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "one month ago"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "one week ago"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "one year ago"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "percentage"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "percentile"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "plainText"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "polynomial"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "positive"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "progress"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "repeatable"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "return_range should have the same dimensions as lookup_range."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "right"
msgstr "Sağ"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "search_mode should be a value in [-1, 1, -2, 2]."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "specificRange"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "start_date (%s) should be on or before end_date (%s)."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "symbol"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "text"
msgstr "Mətn"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "the numeric value in start_unit to convert to end_unit"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid ""
"the search and match mode combination is not supported for XLOOKUP "
"evaluation."
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "today"
msgstr "Bu gün"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "tomorrow"
msgstr "Sabahkı"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "top"
msgstr "Ən məşhur"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "true"
msgstr "Doğru"

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.xml:0
msgid "value"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "yesterday"
msgstr ""

#. module: spreadsheet
#. odoo-javascript
#: code:addons/spreadsheet/static/src/o_spreadsheet/o_spreadsheet.js:0
msgid "zero"
msgstr ""
