# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_in_withholding
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-02 09:58+0000\n"
"PO-Revision-Date: 2024-08-02 09:58+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_in_withholding
#: model_terms:ir.ui.view,arch_db:l10n_in_withholding.account_move_view_form_inherit_l10n_in_withholding
#: model_terms:ir.ui.view,arch_db:l10n_in_withholding.view_account_payment_form_inherit_l10n_in_withholding
msgid "<span class=\"o_stat_text\">TDS</span>"
msgstr ""

#. module: l10n_in_withholding
#: model_terms:ir.ui.view,arch_db:l10n_in_withholding.res_config_settings_view_form_inherit_l10n_in_withholding
msgid "Account"
msgstr ""

#. module: l10n_in_withholding
#: model:ir.model,name:l10n_in_withholding.model_account_chart_template
msgid "Account Chart Template"
msgstr ""

#. module: l10n_in_withholding
#: model:ir.model.fields,field_description:l10n_in_withholding.field_l10n_in_withhold_wizard_line__base
msgid "Base"
msgstr ""

#. module: l10n_in_withholding
#: model_terms:ir.ui.view,arch_db:l10n_in_withholding.account_move_view_form_inherit_l10n_in_withholding
msgid "Base Amount"
msgstr ""

#. module: l10n_in_withholding
#: model:ir.model,name:l10n_in_withholding.model_res_company
msgid "Companies"
msgstr ""

#. module: l10n_in_withholding
#: model:ir.model.fields,field_description:l10n_in_withholding.field_l10n_in_withhold_wizard__company_id
msgid "Company"
msgstr ""

#. module: l10n_in_withholding
#: model:ir.model,name:l10n_in_withholding.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: l10n_in_withholding
#: model_terms:ir.ui.view,arch_db:l10n_in_withholding.tds_entry_view_form
msgid "Confirm"
msgstr ""

#. module: l10n_in_withholding
#: model:ir.actions.act_window,name:l10n_in_withholding.l10n_in_withholding_entry_form_action
msgid "Create TDS Entry"
msgstr ""

#. module: l10n_in_withholding
#: model:ir.model.fields,field_description:l10n_in_withholding.field_l10n_in_withhold_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_in_withholding.field_l10n_in_withhold_wizard_line__create_uid
msgid "Created by"
msgstr ""

#. module: l10n_in_withholding
#: model:ir.model.fields,field_description:l10n_in_withholding.field_l10n_in_withhold_wizard__create_date
#: model:ir.model.fields,field_description:l10n_in_withholding.field_l10n_in_withhold_wizard_line__create_date
msgid "Created on"
msgstr ""

#. module: l10n_in_withholding
#: model:ir.model.fields,field_description:l10n_in_withholding.field_l10n_in_withhold_wizard__currency_id
#: model:ir.model.fields,field_description:l10n_in_withholding.field_l10n_in_withhold_wizard_line__currency_id
msgid "Currency"
msgstr ""

#. module: l10n_in_withholding
#: model:ir.model.fields,field_description:l10n_in_withholding.field_l10n_in_withhold_wizard__date
msgid "Date"
msgstr ""

#. module: l10n_in_withholding
#: model_terms:ir.ui.view,arch_db:l10n_in_withholding.tds_entry_view_form
msgid "Discard"
msgstr ""

#. module: l10n_in_withholding
#: model:ir.model.fields,field_description:l10n_in_withholding.field_l10n_in_withhold_wizard__display_name
#: model:ir.model.fields,field_description:l10n_in_withholding.field_l10n_in_withhold_wizard_line__display_name
msgid "Display Name"
msgstr ""

#. module: l10n_in_withholding
#: model:ir.model.fields,field_description:l10n_in_withholding.field_l10n_in_withhold_wizard__id
#: model:ir.model.fields,field_description:l10n_in_withholding.field_l10n_in_withhold_wizard_line__id
msgid "ID"
msgstr ""

#. module: l10n_in_withholding
#: model_terms:ir.ui.view,arch_db:l10n_in_withholding.res_config_settings_view_form_inherit_l10n_in_withholding
msgid "India TDS Control:"
msgstr ""

#. module: l10n_in_withholding
#: model:ir.model.fields,field_description:l10n_in_withholding.field_account_bank_statement_line__l10n_in_withhold_move_ids
#: model:ir.model.fields,field_description:l10n_in_withholding.field_account_move__l10n_in_withhold_move_ids
#: model:ir.model.fields,field_description:l10n_in_withholding.field_account_payment__l10n_in_withhold_move_ids
msgid "Indian TDS Entries"
msgstr ""

#. module: l10n_in_withholding
#: model:ir.model.fields,field_description:l10n_in_withholding.field_account_bank_statement_line__l10n_in_withholding_line_ids
#: model:ir.model.fields,field_description:l10n_in_withholding.field_account_move__l10n_in_withholding_line_ids
#: model:ir.model.fields,field_description:l10n_in_withholding.field_account_payment__l10n_in_withholding_line_ids
msgid "Indian TDS Lines"
msgstr ""

#. module: l10n_in_withholding
#: model:ir.model.fields,field_description:l10n_in_withholding.field_account_bank_statement_line__l10n_in_withholding_ref_move_id
#: model:ir.model.fields,field_description:l10n_in_withholding.field_account_move__l10n_in_withholding_ref_move_id
#: model:ir.model.fields,field_description:l10n_in_withholding.field_account_payment__l10n_in_withholding_ref_move_id
msgid "Indian TDS Ref Move"
msgstr ""

#. module: l10n_in_withholding
#: model:ir.model.fields,field_description:l10n_in_withholding.field_l10n_in_withhold_wizard__l10n_in_tds_tax_type
#: model:ir.model.fields,field_description:l10n_in_withholding.field_l10n_in_withhold_wizard_line__l10n_in_tds_tax_type
msgid "Indian Tax Type"
msgstr ""

#. module: l10n_in_withholding
#: model:ir.model.fields,field_description:l10n_in_withholding.field_l10n_in_withhold_wizard__related_move_id
msgid "Invoice/Bill"
msgstr ""

#. module: l10n_in_withholding
#: model:ir.model.fields,field_description:l10n_in_withholding.field_account_bank_statement_line__l10n_in_is_withholding
#: model:ir.model.fields,field_description:l10n_in_withholding.field_account_move__l10n_in_is_withholding
#: model:ir.model.fields,field_description:l10n_in_withholding.field_account_payment__l10n_in_is_withholding
msgid "Is Indian TDS Entry"
msgstr ""

#. module: l10n_in_withholding
#: model:ir.model.fields,field_description:l10n_in_withholding.field_l10n_in_withhold_wizard__journal_id
#: model_terms:ir.ui.view,arch_db:l10n_in_withholding.res_config_settings_view_form_inherit_l10n_in_withholding
msgid "Journal"
msgstr ""

#. module: l10n_in_withholding
#: model:ir.model,name:l10n_in_withholding.model_account_move
msgid "Journal Entry"
msgstr ""

#. module: l10n_in_withholding
#: model:ir.model,name:l10n_in_withholding.model_account_move_line
msgid "Journal Item"
msgstr ""

#. module: l10n_in_withholding
#: model:ir.model.fields,field_description:l10n_in_withholding.field_l10n_in_withhold_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_in_withholding.field_l10n_in_withhold_wizard_line__write_uid
msgid "Last Updated by"
msgstr ""

#. module: l10n_in_withholding
#: model:ir.model.fields,field_description:l10n_in_withholding.field_l10n_in_withhold_wizard__write_date
#: model:ir.model.fields,field_description:l10n_in_withholding.field_l10n_in_withhold_wizard_line__write_date
msgid "Last Updated on"
msgstr ""

#. module: l10n_in_withholding
#. odoo-python
#: code:addons/l10n_in_withholding/wizard/l10n_in_withhold_wizard.py:0
#, python-format
msgid "Negative or zero values are not allowed in amount for withhold lines"
msgstr ""

#. module: l10n_in_withholding
#. odoo-python
#: code:addons/l10n_in_withholding/wizard/l10n_in_withhold_wizard.py:0
#, python-format
msgid "Negative or zero values are not allowed in base for withhold lines"
msgstr ""

#. module: l10n_in_withholding
#: model:ir.model.fields,field_description:l10n_in_withholding.field_l10n_in_withhold_wizard__related_payment_id
msgid "Payment"
msgstr ""

#. module: l10n_in_withholding
#: model:ir.model,name:l10n_in_withholding.model_account_payment
msgid "Payments"
msgstr ""

#. module: l10n_in_withholding
#. odoo-python
#: code:addons/l10n_in_withholding/wizard/l10n_in_withhold_wizard.py:0
#, python-format
msgid "Please configure the withholding account from the settings"
msgstr ""

#. module: l10n_in_withholding
#. odoo-python
#: code:addons/l10n_in_withholding/wizard/l10n_in_withhold_wizard.py:0
#, python-format
msgid "Please set a partner on the %s before creating a withhold."
msgstr ""

#. module: l10n_in_withholding
#: model:ir.model.fields.selection,name:l10n_in_withholding.selection__account_tax__l10n_in_tds_tax_type__purchase
#: model:ir.model.fields.selection,name:l10n_in_withholding.selection__l10n_in_withhold_wizard__withhold_type__in_withhold
msgid "Purchase"
msgstr ""

#. module: l10n_in_withholding
#: model:ir.model.fields.selection,name:l10n_in_withholding.selection__l10n_in_withhold_wizard__withhold_type__in_refund_withhold
msgid "Purchase Refund"
msgstr ""

#. module: l10n_in_withholding
#: model:ir.model.fields,field_description:l10n_in_withholding.field_l10n_in_withhold_wizard__reference
msgid "Reference"
msgstr ""

#. module: l10n_in_withholding
#: model:ir.model.fields,help:l10n_in_withholding.field_account_bank_statement_line__l10n_in_withholding_ref_move_id
#: model:ir.model.fields,help:l10n_in_withholding.field_account_move__l10n_in_withholding_ref_move_id
#: model:ir.model.fields,help:l10n_in_withholding.field_account_payment__l10n_in_withholding_ref_move_id
msgid "Reference move for withholding entry"
msgstr ""

#. module: l10n_in_withholding
#: model:ir.model.fields.selection,name:l10n_in_withholding.selection__account_tax__l10n_in_tds_tax_type__sale
#: model:ir.model.fields.selection,name:l10n_in_withholding.selection__l10n_in_withhold_wizard__withhold_type__out_withhold
msgid "Sale"
msgstr ""

#. module: l10n_in_withholding
#: model:ir.model.fields.selection,name:l10n_in_withholding.selection__l10n_in_withhold_wizard__withhold_type__out_refund_withhold
msgid "Sale Refund"
msgstr ""

#. module: l10n_in_withholding
#: model:ir.model.fields,field_description:l10n_in_withholding.field_res_company__l10n_in_withholding_account_id
#: model:ir.model.fields,field_description:l10n_in_withholding.field_res_config_settings__l10n_in_withholding_account_id
msgid "TDS Account"
msgstr ""

#. module: l10n_in_withholding
#: model:ir.model.fields,field_description:l10n_in_withholding.field_l10n_in_withhold_wizard_line__amount
#: model_terms:ir.ui.view,arch_db:l10n_in_withholding.account_move_view_form_inherit_l10n_in_withholding
msgid "TDS Amount"
msgstr ""

#. module: l10n_in_withholding
#: model_terms:ir.ui.view,arch_db:l10n_in_withholding.account_move_view_form_inherit_l10n_in_withholding
#: model_terms:ir.ui.view,arch_db:l10n_in_withholding.view_account_payment_form_inherit_l10n_in_withholding
msgid "TDS Entry"
msgstr ""

#. module: l10n_in_withholding
#: model_terms:ir.ui.view,arch_db:l10n_in_withholding.account_move_view_form_inherit_l10n_in_withholding
msgid "TDS Information"
msgstr ""

#. module: l10n_in_withholding
#: model:ir.model.fields,field_description:l10n_in_withholding.field_res_company__l10n_in_withholding_journal_id
#: model:ir.model.fields,field_description:l10n_in_withholding.field_res_config_settings__l10n_in_withholding_journal_id
msgid "TDS Journal"
msgstr ""

#. module: l10n_in_withholding
#: model:ir.model.fields,field_description:l10n_in_withholding.field_l10n_in_withhold_wizard__withhold_line_ids
msgid "TDS Lines"
msgstr ""

#. module: l10n_in_withholding
#: model:ir.model.fields,field_description:l10n_in_withholding.field_l10n_in_withhold_wizard_line__tax_id
msgid "TDS Tax"
msgstr ""

#. module: l10n_in_withholding
#: model:ir.model.fields,field_description:l10n_in_withholding.field_account_move_line__l10n_in_withhold_tax_amount
msgid "TDS Tax Amount"
msgstr ""

#. module: l10n_in_withholding
#: model_terms:ir.ui.view,arch_db:l10n_in_withholding.tds_entry_view_form
msgid "TDS Tax Details"
msgstr ""

#. module: l10n_in_withholding
#: model:ir.model.fields,field_description:l10n_in_withholding.field_account_tax__l10n_in_tds_tax_type
msgid "TDS Tax Type"
msgstr ""

#. module: l10n_in_withholding
#. odoo-python
#: code:addons/l10n_in_withholding/wizard/l10n_in_withhold_wizard.py:0
#, python-format
msgid "TDS created from"
msgstr ""

#. module: l10n_in_withholding
#. odoo-python
#: code:addons/l10n_in_withholding/wizard/l10n_in_withhold_wizard.py:0
#, python-format
msgid ""
"TDS must be created from Posted Customer Invoices, Customer Credit Notes, "
"Vendor Bills or Vendor Refunds."
msgstr ""

#. module: l10n_in_withholding
#. odoo-python
#: code:addons/l10n_in_withholding/wizard/l10n_in_withhold_wizard.py:0
#, python-format
msgid "TDS must be created from an Invoice or a Payment."
msgstr ""

#. module: l10n_in_withholding
#. odoo-python
#: code:addons/l10n_in_withholding/wizard/l10n_in_withhold_wizard.py:0
#, python-format
msgid "TDS of %s"
msgstr ""

#. module: l10n_in_withholding
#: model:ir.model,name:l10n_in_withholding.model_account_tax
#: model_terms:ir.ui.view,arch_db:l10n_in_withholding.account_move_view_form_inherit_l10n_in_withholding
msgid "Tax"
msgstr ""

#. module: l10n_in_withholding
#: model:ir.model.fields,help:l10n_in_withholding.field_account_bank_statement_line__l10n_in_is_withholding
#: model:ir.model.fields,help:l10n_in_withholding.field_account_move__l10n_in_is_withholding
#: model:ir.model.fields,help:l10n_in_withholding.field_account_payment__l10n_in_is_withholding
msgid "Technical field to identify Indian withholding entry"
msgstr ""

#. module: l10n_in_withholding
#: model_terms:ir.ui.view,arch_db:l10n_in_withholding.account_move_view_form_inherit_l10n_in_withholding
msgid "Total"
msgstr ""

#. module: l10n_in_withholding
#: model_terms:ir.ui.view,arch_db:l10n_in_withholding.tds_entry_view_form
msgid "Total Amount"
msgstr ""

#. module: l10n_in_withholding
#: model_terms:ir.ui.view,arch_db:l10n_in_withholding.tds_entry_view_form
msgid "Total Base"
msgstr ""

#. module: l10n_in_withholding
#: model:ir.model.fields,field_description:l10n_in_withholding.field_account_bank_statement_line__l10n_in_total_withholding_amount
#: model:ir.model.fields,field_description:l10n_in_withholding.field_account_move__l10n_in_total_withholding_amount
#: model:ir.model.fields,field_description:l10n_in_withholding.field_account_payment__l10n_in_total_withholding_amount
msgid "Total Indian TDS Amount"
msgstr ""

#. module: l10n_in_withholding
#: model:ir.model.fields,help:l10n_in_withholding.field_account_bank_statement_line__l10n_in_total_withholding_amount
#: model:ir.model.fields,help:l10n_in_withholding.field_account_move__l10n_in_total_withholding_amount
#: model:ir.model.fields,help:l10n_in_withholding.field_account_payment__l10n_in_total_withholding_amount
msgid "Total withholding amount for the move"
msgstr ""

#. module: l10n_in_withholding
#: model:ir.model.fields,field_description:l10n_in_withholding.field_l10n_in_withhold_wizard__type_name
msgid "Type"
msgstr ""

#. module: l10n_in_withholding
#: model:ir.model.fields,field_description:l10n_in_withholding.field_l10n_in_withhold_wizard__warning_message
msgid "Warning Message"
msgstr ""

#. module: l10n_in_withholding
#. odoo-python
#: code:addons/l10n_in_withholding/wizard/l10n_in_withhold_wizard.py:0
#, python-format
msgid ""
"Warning: The base amount of TDS lines is greater than the amount of the %s"
msgstr ""

#. module: l10n_in_withholding
#. odoo-python
#: code:addons/l10n_in_withholding/wizard/l10n_in_withhold_wizard.py:0
#, python-format
msgid ""
"Warning: The base amount of TDS lines is greater than the untaxed amount of "
"the %s"
msgstr ""

#. module: l10n_in_withholding
#: model:ir.model.fields,field_description:l10n_in_withholding.field_l10n_in_withhold_wizard_line__withhold_id
msgid "Withhold"
msgstr ""

#. module: l10n_in_withholding
#: model:ir.model,name:l10n_in_withholding.model_l10n_in_withhold_wizard
msgid "Withhold Wizard"
msgstr ""

#. module: l10n_in_withholding
#: model:ir.model,name:l10n_in_withholding.model_l10n_in_withhold_wizard_line
msgid "Withhold Wizard Lines"
msgstr ""

#. module: l10n_in_withholding
#: model:ir.model.fields,field_description:l10n_in_withholding.field_l10n_in_withhold_wizard__withhold_type
msgid "Withholding Type"
msgstr ""

#. module: l10n_in_withholding
#. odoo-python
#: code:addons/l10n_in_withholding/wizard/l10n_in_withhold_wizard.py:0
#, python-format
msgid "You can only create a withhold for only one record at a time."
msgstr ""

#. module: l10n_in_withholding
#. odoo-python
#: code:addons/l10n_in_withholding/wizard/l10n_in_withhold_wizard.py:0
#, python-format
msgid "You must input at least one withhold line"
msgstr ""
