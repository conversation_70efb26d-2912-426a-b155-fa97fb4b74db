# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_crm_iap_reveal
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Czech (https://app.transifex.com/odoo/teams/41243/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n <= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_form
msgid ""
"1 credit is consumed per visitor matching the website traffic conditions and"
" whose company can be identified.<br/>"
msgstr ""
"Na každého návštěvníka, který odpovídá podmínkám návštěvnosti webu a jehož "
"společnost lze identifikovat, je spotřebován 1 kredit.<br/>"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_form
msgid "<span class=\"o_stat_text\"> Leads </span>"
msgstr "<span class=\"o_stat_text\"> Potenciální zákazníci </span>"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_form
msgid "<span class=\"o_stat_text\"> Opportunities </span>"
msgstr "<span class=\"o_stat_text\"> Příležitosti </span>"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__active
msgid "Active"
msgstr "Aktivní"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_view_search
msgid "Archived"
msgstr "Archivováno"

#. module: website_crm_iap_reveal
#: model:ir.model,name:website_crm_iap_reveal.model_crm_reveal_rule
msgid "CRM Lead Generation Rules"
msgstr "CRM pravidlo pro generování prospektů"

#. module: website_crm_iap_reveal
#: model:ir.model,name:website_crm_iap_reveal.model_crm_reveal_view
msgid "CRM Reveal View"
msgstr "Zobrazení generování prospektů"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,help:website_crm_iap_reveal.field_crm_reveal_rule__lead_for
msgid "Choose whether to track companies only or companies and their contacts"
msgstr ""
"Vyberte, zda chcete sledovat pouze společnosti nebo společnosti a jejich "
"kontakty"

#. module: website_crm_iap_reveal
#: model:ir.model.fields.selection,name:website_crm_iap_reveal.selection__crm_reveal_rule__lead_for__companies
msgid "Companies"
msgstr "Společnosti"

#. module: website_crm_iap_reveal
#: model:ir.model.fields.selection,name:website_crm_iap_reveal.selection__crm_reveal_rule__lead_for__people
msgid "Companies and their Contacts"
msgstr "Společnosti a jejich kontakty"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__company_size_min
msgid "Company Size"
msgstr "Velikost společnosti"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__company_size_max
msgid "Company Size Max"
msgstr "Velikost společnosti max"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_form
msgid "Contact Filter"
msgstr "Filtr kontaktů"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__country_ids
msgid "Countries"
msgstr "Země"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_view__create_date
msgid "Create Date"
msgstr "Vytvořit datum"

#. module: website_crm_iap_reveal
#: model_terms:ir.actions.act_window,help:website_crm_iap_reveal.crm_reveal_rule_action
msgid "Create a conversion rule"
msgstr "Vytvořte pravidlo převodu"

#. module: website_crm_iap_reveal
#: model_terms:ir.actions.act_window,help:website_crm_iap_reveal.crm_reveal_rule_action
msgid ""
"Create rules to generate B2B leads/opportunities from your website visitors."
msgstr ""
"Vytvořte pravidla pro generování B2B potenciálních zákazníků / příležitostí "
"od návštěvníků vašeho webu."

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__create_uid
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_view__create_uid
msgid "Created by"
msgstr "Vytvořeno uživatelem"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__create_date
msgid "Created on"
msgstr "Vytvořeno dne"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__lead_for
msgid "Data Tracking"
msgstr "Sledování dat"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__display_name
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_view__display_name
msgid "Display Name"
msgstr "Zobrazovací název"

#. module: website_crm_iap_reveal
#. odoo-python
#: code:addons/website_crm_iap_reveal/models/crm_reveal_rule.py:0
msgid "Enter Valid Regex."
msgstr "Zadejte Platný regulární výraz."

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__contact_filter_type
msgid "Filter On"
msgstr "Filtr zapnutý"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,help:website_crm_iap_reveal.field_crm_reveal_rule__filter_on_size
msgid "Filter companies based on their size."
msgstr "Filtrujte společnosti podle jejich velikosti."

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__filter_on_size
msgid "Filter on Size"
msgstr "Filtr podle velikosti"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_form
msgid "From"
msgstr "Od"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__lead_ids
msgid "Generated Lead / Opportunity"
msgstr "Generovaný potenciální zákazník / příležitost"

#. module: website_crm_iap_reveal
#: model:ir.model,name:website_crm_iap_reveal.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP Routing"

#. module: website_crm_iap_reveal
#: model:ir.model.fields.selection,name:website_crm_iap_reveal.selection__crm_reveal_rule__priority__2
msgid "High"
msgstr "Vysoká"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_lead__reveal_iap_credits
msgid "IAP Credits"
msgstr "IAP kredity"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__id
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_view__id
msgid "ID"
msgstr "ID"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_lead__reveal_ip
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_view__reveal_ip
msgid "IP Address"
msgstr "IP adresa"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__industry_tag_ids
msgid "Industries"
msgstr "Odvětví"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__write_uid
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_view__write_uid
msgid "Last Updated by"
msgstr "Naposledy upraveno uživatelem"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__write_date
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_view__write_date
msgid "Last Updated on"
msgstr "Naposledy upraveno dne"

#. module: website_crm_iap_reveal
#: model:ir.model.fields.selection,name:website_crm_iap_reveal.selection__crm_reveal_rule__lead_type__lead
msgid "Lead"
msgstr "Potenciální zákazník"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_form
msgid "Lead Data"
msgstr "Potenciální zákazník dáta"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_lead_opportunity_form
msgid "Lead Generation Information"
msgstr "Informace o generování potenciálních zákazníků"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_lead__reveal_rule_id
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_view__reveal_rule_id
msgid "Lead Generation Rule"
msgstr "Pravidlo pro generování potenciální zákazník"

#. module: website_crm_iap_reveal
#: model:ir.actions.act_window,name:website_crm_iap_reveal.crm_reveal_view_action
#: model:ir.ui.menu,name:website_crm_iap_reveal.crm_reveal_view_menu_action
msgid "Lead Generation Views"
msgstr "Návštěvy z generovaných prospektů"

#. module: website_crm_iap_reveal
#: model:ir.actions.server,name:website_crm_iap_reveal.ir_cron_crm_reveal_lead_ir_actions_server
msgid "Lead Generation: Leads/Opportunities Generation"
msgstr ""
"Generování potenciálních zákazníků: Generování potenciálních zákazníků / "
"příležitostí"

#. module: website_crm_iap_reveal
#: model:ir.model,name:website_crm_iap_reveal.model_crm_lead
msgid "Lead/Opportunity"
msgstr "Prospekt/příležitost"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,help:website_crm_iap_reveal.field_crm_reveal_rule__industry_tag_ids
msgid "Leave empty to always match. Odoo will not create lead if no match"
msgstr ""
"Ponechejte prázdné, aby se vždy shodovaly. Odoo nevytvoří potenciální "
"zákazník, pokud nebude shoda"

#. module: website_crm_iap_reveal
#: model:ir.model.fields.selection,name:website_crm_iap_reveal.selection__crm_reveal_rule__priority__0
msgid "Low"
msgstr "Nízké"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_form
msgid ""
"Make sure you know if you have to be GDPR compliant for storing personal "
"data."
msgstr ""
"Ujistěte se, zda na ukládání osobních údajů musíte být v souladu s GDPR."

#. module: website_crm_iap_reveal
#: model:ir.model.constraint,message:website_crm_iap_reveal.constraint_crm_reveal_rule_limit_extra_contacts
msgid "Maximum 5 contacts are allowed!"
msgstr "Povoleno je maximálně 5 kontaktů!"

#. module: website_crm_iap_reveal
#: model:ir.model.fields.selection,name:website_crm_iap_reveal.selection__crm_reveal_rule__priority__1
msgid "Medium"
msgstr "Médium"

#. module: website_crm_iap_reveal
#: model:ir.model.fields.selection,name:website_crm_iap_reveal.selection__crm_reveal_view__reveal_state__not_found
msgid "Not Found"
msgstr "Nenalezeno"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__extra_contacts
msgid "Number of Contacts"
msgstr "Počet kontaktů"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__lead_count
msgid "Number of Generated Leads"
msgstr "Počet generovaných ptenciálních zákazníku"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__opportunity_count
msgid "Number of Generated Opportunity"
msgstr "Počet vygenerovaných příležitostí"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,help:website_crm_iap_reveal.field_crm_reveal_rule__country_ids
msgid ""
"Only visitors of following countries will be converted into "
"leads/opportunities (using GeoIP)."
msgstr ""
"Pouze návštěvníci z následujících zemí budou převedeni na příležitosti / "
"příležitosti (pomocí GeoIP)."

#. module: website_crm_iap_reveal
#: model:ir.model.fields,help:website_crm_iap_reveal.field_crm_reveal_rule__state_ids
msgid ""
"Only visitors of following states will be converted into "
"leads/opportunities."
msgstr ""
"Na potenciální zákazníky / příležitosti budou převedeni pouze návštěvníci "
"následujících států."

#. module: website_crm_iap_reveal
#: model:ir.model.fields.selection,name:website_crm_iap_reveal.selection__crm_reveal_rule__lead_type__opportunity
msgid "Opportunity"
msgstr "Příležitost"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_form
msgid "Opportunity Data"
msgstr "Data příležitostí"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_form
msgid "Opportunity Generation Conditions"
msgstr "Podmínky generování příležitostí"

#. module: website_crm_iap_reveal
#. odoo-python
#: code:addons/website_crm_iap_reveal/models/crm_reveal_rule.py:0
msgid "Opportunity created by Odoo Lead Generation"
msgstr "Příležitost vytvořená generací Odoo potenciální zákazník"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__other_role_ids
msgid "Other Roles"
msgstr "Další role"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__preferred_role_id
msgid "Preferred Role"
msgstr "Preferovaná role"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__priority
msgid "Priority"
msgstr "Priorita"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,help:website_crm_iap_reveal.field_crm_reveal_rule__regex_url
msgid ""
"Regex to track website pages. Leave empty to track the entire website, or / "
"to target the homepage. Example: /page* to track all the pages which begin "
"with /page"
msgstr ""
"Regex na sledování webových stránek. Pokud chcete sledovat celý web nebo "
"zacílit na domovskou stránku, nechte ho prázdné. Příklad: / page * na "
"sledování  všech stránek, které začínají s /page"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,help:website_crm_iap_reveal.field_crm_reveal_rule__website_id
msgid "Restrict Lead generation to this website."
msgstr "Omezte generování potenciálních zákazníků na tuto webstránku."

#. module: website_crm_iap_reveal
#: model:ir.model.fields.selection,name:website_crm_iap_reveal.selection__crm_reveal_rule__contact_filter_type__role
msgid "Role"
msgstr "Role"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_view_search
msgid "Rule"
msgstr "Pravidlo"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__name
msgid "Rule Name"
msgstr "Název pravidla"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__team_id
msgid "Sales Team"
msgstr "Obchodní tým"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__user_id
msgid "Salesperson"
msgstr "Obchodník"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_view_search
msgid "Search CRM Reveal Rule"
msgstr "Vyhledejte pravidlo odhalení CRM"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__seniority_id
#: model:ir.model.fields.selection,name:website_crm_iap_reveal.selection__crm_reveal_rule__contact_filter_type__seniority
msgid "Seniority"
msgstr "Profesní zkušenost"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__sequence
msgid "Sequence"
msgstr "Sekvence"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_view__reveal_state
msgid "State"
msgstr "Stát"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__state_ids
msgid "States"
msgstr "Státy"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__suffix
msgid "Suffix"
msgstr "Přípona"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__tag_ids
msgid "Tags"
msgstr "Štítky"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,help:website_crm_iap_reveal.field_crm_reveal_rule__extra_contacts
msgid ""
"This is the number of contacts to track if their role/seniority match your "
"criteria. Their details will show up in the history thread of generated "
"leads/opportunities. One credit is consumed per tracked contact."
msgstr ""
"Toto je počet kontaktů ke sledování, zda jejich role / počet odpracovaných "
"let odpovídají vašim kritériím. Jejich podrobnosti se zobrazí v historickém "
"vlákně generovaných potenciálních zákazníků / příležitostí. Na sledovaný "
"kontakt se spotřebuje jeden kredit."

#. module: website_crm_iap_reveal
#: model:ir.model.fields,help:website_crm_iap_reveal.field_crm_reveal_rule__suffix
msgid ""
"This will be appended in name of generated lead so you can identify "
"lead/opportunity is generated with this rule"
msgstr ""
"Toto bude připojeno k názvu vygenerovaného potenciálního zákazníka, abyste "
"mohli identifikovat potenciálního zákazníka / příležitost generovanou tímto "
"pravidlem"

#. module: website_crm_iap_reveal
#: model:ir.model.fields.selection,name:website_crm_iap_reveal.selection__crm_reveal_view__reveal_state__to_process
msgid "To Process"
msgstr "Zpracovat"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__lead_type
msgid "Type"
msgstr "Typ"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__regex_url
msgid "URL Expression"
msgstr "URL výraz"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_form
msgid "Up to"
msgstr "Až do"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,help:website_crm_iap_reveal.field_crm_reveal_rule__sequence
msgid ""
"Used to order the rules with same URL and countries. Rules with a lower "
"sequence number will be processed first."
msgstr ""
"Slouží k seřazení pravidel se stejnou adresou URL a zeměmi. Nejprve budou "
"zpracována pravidla s nižším pořadovým číslem."

#. module: website_crm_iap_reveal
#: model:ir.model.fields.selection,name:website_crm_iap_reveal.selection__crm_reveal_rule__priority__3
msgid "Very High"
msgstr "Velmi vysoko"

#. module: website_crm_iap_reveal
#: model:ir.actions.act_window,name:website_crm_iap_reveal.crm_reveal_rule_action
#: model:ir.ui.menu,name:website_crm_iap_reveal.crm_reveal_rule_menu_action
msgid "Visits to Leads Rules"
msgstr "Pravidla pro konverzi návštěvníků na prospekty"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__website_id
msgid "Website"
msgstr "Webová stránka"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_form
msgid "Website Traffic Conditions"
msgstr "Podmínky provozu webstránek"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_form
msgid "additional credit(s) are consumed if the company matches this rule."
msgstr "další kredit(y) se spotřebují, pokud společnost splní toto pravidlo."

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_form
msgid "e.g. /page"
msgstr "např. /strana"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_form
msgid "e.g. US Visitors"
msgstr "např. návštěvníci z USA"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_form
msgid "employees"
msgstr "zaměstnanci"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_form
msgid "to"
msgstr "k"
