# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_spreadsheet
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 09:14+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: documents_spreadsheet
#. odoo-python
#: code:addons/documents_spreadsheet/wizard/save_spreadsheet_template.py:0
msgid "\"%s\" saved as template"
msgstr "\"%s\" 저장된 양식"

#. module: documents_spreadsheet
#. odoo-python
#: code:addons/documents_spreadsheet/models/documents_document.py:0
msgid "%(folder)s is used by %(company)s"
msgstr "%(folder)s는 %(company)s에서 사용됩니다."

#. module: documents_spreadsheet
#. odoo-python
#: code:addons/documents_spreadsheet/models/spreadsheet_template.py:0
msgid "%s (copy)"
msgstr "%s (사본)"

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/bundle/actions/spreadsheet_action.js:0
msgid "%s - Template"
msgstr "%s - 서식"

#. module: documents_spreadsheet
#: model:ir.model.constraint,message:documents_spreadsheet.constraint_spreadsheet_contributor_spreadsheet_user_unique
msgid "A combination of the spreadsheet and the user already exist"
msgstr "스프레드 시트와 사용자의 조합이 이미 존재합니다"

#. module: documents_spreadsheet
#: model:ir.model.constraint,message:documents_spreadsheet.constraint_documents_document_frozen_spreadsheet_access_via_link_access_internal
msgid "A frozen spreadsheet can not be editable"
msgstr "고정된 스프레드시트는 편집할 수 없습니다."

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.save_spreadsheet_template_view_form
msgid ""
"Any user will be able to create a new spreadsheet based on this template."
msgstr "모든 사용자는 이 템플릿을 기반으로 신규 스프레드 시트를 만들 수 있습니다."

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.save_spreadsheet_template_view_form
msgid "Cancel"
msgstr "취소"

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.res_config_settings_view_form_inherit_documents_spreadsheet
msgid "Centralize your spreadsheets"
msgstr "스프레드시트 중앙 집중화"

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_res_company
msgid "Companies"
msgstr "회사"

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_res_config_settings
msgid "Config Settings"
msgstr "환경설정"

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.save_spreadsheet_template_view_form
msgid "Confirm"
msgstr "승인"

#. module: documents_spreadsheet
#: model:ir.actions.act_window,name:documents_spreadsheet.spreadsheet_contributor_action
#: model:ir.ui.menu,name:documents_spreadsheet.menu_technical_spreadsheet_contributor
msgid "Contributors"
msgstr "도움을 주신 분들"

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/spreadsheet_template/template_dialog.xml:0
msgid "Create"
msgstr "작성"

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/spreadsheet_template/spreadsheet_template_dialog.js:0
msgid "Create a Spreadsheet or select a Template"
msgstr "스프레드시트를 만들거나 서식을 선택하세요"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__create_uid
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__create_uid
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__create_uid
msgid "Created by"
msgstr "작성자"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__create_date
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__create_date
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__create_date
msgid "Created on"
msgstr "작성일자"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_document__current_revision_uuid
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__current_revision_uuid
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__current_revision_uuid
msgid "Current Revision Uuid"
msgstr "현재 수정 버전 Uuid"

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/documents_view/documents_spreadsheet_controller_mixin.js:0
#: code:addons/documents_spreadsheet/static/src/spreadsheet_template/template_dialog.xml:0
msgid "Discard"
msgstr "취소"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__display_name
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__display_name
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__display_name
msgid "Display Name"
msgstr "표시명"

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_documents_document
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_cell_thread__document_id
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__document_id
msgid "Document"
msgstr "문서"

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_documents_access
msgid "Document / Partner"
msgstr "문서 / 파트너"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_res_company__document_spreadsheet_folder_id
#: model:ir.model.fields,field_description:documents_spreadsheet.field_res_config_settings__document_spreadsheet_folder_id
msgid "Document Spreadsheet Folder"
msgstr "문서 스프레드시트 폴더"

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.spreadsheet_template_view_tree
msgid "Edit"
msgstr "편집"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_document__excel_export
msgid "Excel Export"
msgstr "엑셀 내보내기"

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/documents_view/documents_spreadsheet_controller_mixin.js:0
msgid "Excel file preview"
msgstr "엑셀 파일 미리 보기"

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/spreadsheet_clone_xlsx_dialog/spreadsheet_clone_xlsx_dialog.xml:0
msgid ""
"Excel files cannot be previewed or edited directly in Odoo. Opening your "
"file with Odoo Spreadsheet will allow you to do so."
msgstr "엑셀 파일은 Odoo에서 미리보기 하거나 편집할 수 없습니다. Odoo 스프레드시트로 파일을 열어 시도하세요."

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__file_name
msgid "File Name"
msgstr "파일명"

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/bundle/actions/spreadsheet_freeze_and_share/spreadsheet_freeze_and_share.xml:0
#: code:addons/documents_spreadsheet/static/src/documents_control_panel/documents_control_panel.xml:0
msgid "Freeze and share"
msgstr "고정 및 공유"

#. module: documents_spreadsheet
#: model:ir.model.fields.selection,name:documents_spreadsheet.selection__documents_document__handler__frozen_folder
msgid "Frozen Folder"
msgstr "고정 폴더"

#. module: documents_spreadsheet
#: model:ir.model.fields.selection,name:documents_spreadsheet.selection__documents_document__handler__frozen_spreadsheet
msgid "Frozen Spreadsheet"
msgstr "고정 스프레드시트"

#. module: documents_spreadsheet
#. odoo-python
#: code:addons/documents_spreadsheet/models/documents_access.py:0
msgid "Frozen Spreadsheets can not be editable."
msgstr "고정된 스프레드시트는 편집할 수 없습니다."

#. module: documents_spreadsheet
#. odoo-python
#: code:addons/documents_spreadsheet/models/documents_document.py:0
msgid "Frozen at %(date)s: %(name)s"
msgstr "%(date)s에 고정: %(name)s"

#. module: documents_spreadsheet
#. odoo-python
#: code:addons/documents_spreadsheet/models/documents_document.py:0
msgid "Frozen spreadsheets"
msgstr "고정 스프레드시트"

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP 라우팅"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_document__handler
msgid "Handler"
msgstr "핸들러"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__id
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__id
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__id
msgid "ID"
msgstr "ID"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__write_uid
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__write_uid
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__write_uid
msgid "Last Updated by"
msgstr "최근 갱신한 사람"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__write_date
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__write_date
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__write_date
msgid "Last Updated on"
msgstr "최근 갱신 일자"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__last_update_date
msgid "Last update date"
msgstr "최근 업데이트 날짜"

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.spreadsheet_template_view_tree
msgid "Make a copy"
msgstr "복사본 만들기"

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.spreadsheet_template_view_search
msgid "My Templates"
msgstr "나의 서식"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__name
msgid "Name"
msgstr "이름"

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.spreadsheet_template_view_tree
msgid "New spreadsheet"
msgstr "새 스프레드시트"

#. module: documents_spreadsheet
#. odoo-javascript
#. odoo-python
#: code:addons/documents_spreadsheet/models/documents_document.py:0
#: code:addons/documents_spreadsheet/static/src/bundle/actions/spreadsheet_action.js:0
msgid "New spreadsheet created in Documents"
msgstr "문서에 새 스프레드시트 저장"

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/models/documents_document.py:0
#: code:addons/documents_spreadsheet/static/src/bundle/actions/spreadsheet_action.js:0
msgid "New spreadsheet created in My Drive"
msgstr ""

#. module: documents_spreadsheet
#. odoo-javascript
#. odoo-python
#: code:addons/documents_spreadsheet/models/spreadsheet_template.py:0
#: code:addons/documents_spreadsheet/static/src/bundle/actions/spreadsheet_template/spreadsheet_template_action.js:0
msgid "New spreadsheet template created"
msgstr "새 스프레드시트 서식 생성"

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/documents_view/documents_spreadsheet_controller_mixin.js:0
msgid "Open with Odoo Spreadsheet"
msgstr "Odoo 스프레드시트로 열기"

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/documents_view/documents_spreadsheet_controller_mixin.js:0
msgid "Restore"
msgstr "복원"

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/documents_view/documents_spreadsheet_controller_mixin.js:0
msgid "Restore file?"
msgstr "파일을 복원하시겠습니까?"

#. module: documents_spreadsheet
#: model:ir.actions.act_window,name:documents_spreadsheet.save_spreadsheet_template_action
msgid "Save as template"
msgstr "서식으로 저장"

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/documents_control_panel/documents_control_panel.js:0
msgid "Select one and only one spreadsheet"
msgstr "스프레드시트를 하나만 선택하세요."

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/spreadsheet_clone_xlsx_dialog/spreadsheet_clone_xlsx_dialog.xml:0
msgid "Send source file to trash"
msgstr "소스 파일을 휴지통으로 보내기"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__sequence
msgid "Sequence"
msgstr "순서"

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/bundle/actions/spreadsheet_share/spreadsheet_share.xml:0
msgid "Share"
msgstr "공유"

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/bundle/actions/spreadsheet_action.js:0
#: code:addons/documents_spreadsheet/static/src/documents_view/documents_spreadsheet_controller_mixin.xml:0
#: code:addons/documents_spreadsheet/static/src/spreadsheet_action_loader.js:0
#: model:ir.model.fields.selection,name:documents_spreadsheet.selection__documents_document__handler__spreadsheet
msgid "Spreadsheet"
msgstr "스프레드시트"

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_spreadsheet_contributor
msgid "Spreadsheet Contributor"
msgstr "스프레드시트 기여자"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_document__spreadsheet_data
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__spreadsheet_data
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__spreadsheet_data
msgid "Spreadsheet Data"
msgstr "스프레드시트 데이터"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_document__spreadsheet_file_name
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__spreadsheet_file_name
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__spreadsheet_file_name
msgid "Spreadsheet File Name"
msgstr "스프레드시트 파일 이름"

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.spreadsheet_document_view_kanban
msgid "Spreadsheet Preview"
msgstr "스프레드시트 미리보기"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_document__spreadsheet_revision_ids
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__spreadsheet_revision_ids
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__spreadsheet_revision_ids
msgid "Spreadsheet Revision"
msgstr "스프레드시트 수정"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_document__spreadsheet_snapshot
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__spreadsheet_snapshot
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__spreadsheet_snapshot
msgid "Spreadsheet Snapshot"
msgstr "스프레드시트 스냅샷"

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_spreadsheet_template
msgid "Spreadsheet Template"
msgstr "스프레드시트 서식"

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_save_spreadsheet_template
msgid "Spreadsheet Template Save Wizard"
msgstr "스프레드시트 서식 저장 마법사"

#. module: documents_spreadsheet
#: model:ir.actions.act_window,name:documents_spreadsheet.spreadsheet_template_action
#: model:ir.ui.menu,name:documents_spreadsheet.menu_technical_spreadsheet_template
msgid "Spreadsheet Templates"
msgstr "스프레드시트 서식"

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_spreadsheet_cell_thread
msgid "Spreadsheet discussion thread"
msgstr "스프레드시트 토론 스레드"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_document__spreadsheet_binary_data
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__spreadsheet_binary_data
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__spreadsheet_binary_data
msgid "Spreadsheet file"
msgstr "스프레드시트 파일"

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/documents_view/documents_spreadsheet_controller_mixin.js:0
msgid ""
"Spreadsheet files cannot be handled from the Trash. Would you like to "
"restore this document?"
msgstr "스프레드시트 파일은 휴지통에서 처리할 수 없습니다. 이 문서를 복원하시겠습니까?"

#. module: documents_spreadsheet
#. odoo-python
#: code:addons/documents_spreadsheet/models/documents_document.py:0
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.document_view_search_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.res_config_settings_view_form_inherit_documents_spreadsheet
msgid "Spreadsheets"
msgstr "스프레드시트"

#. module: documents_spreadsheet
#. odoo-python
#: code:addons/documents_spreadsheet/models/documents_access.py:0
msgid "Spreadsheets can not be shared in edit mode to non-internal users."
msgstr "스프레드시트는 편집 모드에서 내부 사용자가 아닌 다른 사용자와 공유할 수 없습니다."

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_cell_thread__template_id
msgid "Template"
msgstr "서식"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__template_name
msgid "Template Name"
msgstr "서식명"

#. module: documents_spreadsheet
#. odoo-python
#: code:addons/documents_spreadsheet/models/documents_document.py:0
msgid ""
"The company for a folder cannot be changed if it is already used as the "
"spreadsheet workspace for at least one other company: %s"
msgstr "폴더가 이미 다른 회사에 대한 스프레드시트 작업 공간으로 사용된 경우, 폴더의 회사를 변경할 수 없습니다: %s"

#. module: documents_spreadsheet
#. odoo-python
#: code:addons/documents_spreadsheet/models/res_company.py:0
msgid ""
"The company of %(folder)s should either be undefined or set to %(company)s. "
"Otherwise, it is not possible to link the workspace to the company."
msgstr ""
"%(folder)s의 회사는 정의되지 않거나 %(company)s로 설정되어야 합니다. 그렇지 않으면 작업 공간을 회사에 연결할 수 "
"없습니다."

#. module: documents_spreadsheet
#. odoo-python
#: code:addons/documents_spreadsheet/models/documents_document.py:0
msgid "The file is not a xlsx file"
msgstr "xlsx 형식의 파일이 아닙니다"

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/documents_permission_panel/documents_access_settings.js:0
#: code:addons/documents_spreadsheet/static/src/documents_permission_panel/documents_member_invite.js:0
#: code:addons/documents_spreadsheet/static/src/documents_permission_panel/documents_partner_access.js:0
msgid "The frozen spreadsheets are readonly."
msgstr "고정된 스프레드시트는 읽기 전용입니다."

#. module: documents_spreadsheet
#. odoo-python
#: code:addons/documents_spreadsheet/models/documents_document.py:0
msgid "The spreadsheet you are trying to access does not exist."
msgstr "접근하려는 스프레드시트가 존재하지 않습니다."

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/documents_permission_panel/documents_access_settings.js:0
msgid ""
"The spreadsheets can not be shared in edit mode with a link, change Internal"
" to give write access."
msgstr "스프레드시트는 링크를 통해 편집 모드에서 공유할 수 없으므로 쓰기 권한을 부여하려면 설정을 내부로 변경해야 합니다."

#. module: documents_spreadsheet
#. odoo-python
#: code:addons/documents_spreadsheet/models/documents_document.py:0
msgid "The xlsx file is corrupted"
msgstr "xlsx 파일이 손상되었습니다"

#. module: documents_spreadsheet
#. odoo-python
#: code:addons/documents_spreadsheet/models/documents_document.py:0
msgid "The xlsx file is too big"
msgstr "xlsx 파일이 너무 큽니다"

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.documents_error_live_data
msgid "This spreadsheet contains live data, only internal users can view it."
msgstr "이 스프레드시트에는 라이브 데이터가 포함되어 있으며 내부 사용자만 액세스할 수 있습니다."

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_document__thumbnail
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__thumbnail
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__thumbnail
msgid "Thumbnail"
msgstr "미리보기 이미지"

#. module: documents_spreadsheet
#: model:ir.model.constraint,message:documents_spreadsheet.constraint_documents_document_spreadsheet_access_via_link
msgid "To share a spreadsheet in edit mode, add the user in the accesses"
msgstr "편집 모드에서 스프레드시트를 공유하려면 액세스 설정에서 사용자를 추가하세요."

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/bundle/actions/spreadsheet_action.xml:0
msgid "Toggle favorite"
msgstr "즐겨찾기 토글"

#. module: documents_spreadsheet
#. odoo-python
#: code:addons/documents_spreadsheet/models/documents_document.py:0
msgid "Untitled spreadsheet"
msgstr "제목없는 스프레드시트"

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/bundle/actions/spreadsheet_template/spreadsheet_template_action.js:0
msgid "Untitled spreadsheet template"
msgstr "제목 없는 스프레드시트 서식"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__user_id
msgid "User"
msgstr "사용자"

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/spreadsheet_template/template_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.res_config_settings_view_form_inherit_documents_spreadsheet
msgid "Workspace"
msgstr "저장공간"

#. module: documents_spreadsheet
#. odoo-python
#: code:addons/documents_spreadsheet/models/documents_document.py:0
msgid "You are not allowed to freeze spreadsheets in Company"
msgstr "회사에서 스프레드시트를 고정시킬 수 있는 권한이 없습니다."

#. module: documents_spreadsheet
#. odoo-python
#: code:addons/documents_spreadsheet/models/documents_document.py:0
msgid "You can not edit a frozen spreadsheet"
msgstr "고정된 스프레드시트는 편집할 수 없습니다."

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/bundle/actions/spreadsheet_action.js:0
msgid "You can not freeze a frozen spreadsheet"
msgstr "이미 고정된 스프레드시트는 고정할 수 없습니다."

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/documents_permission_panel/documents_member_invite.js:0
#: code:addons/documents_spreadsheet/static/src/documents_permission_panel/documents_partner_access.js:0
msgid "You can not share spreadsheet in edit mode to non-internal user."
msgstr "편집 모드의 스프레드시트는 내부 사용자가 아닌 다른 사용자와 공유할 수 없습니다."

#. module: documents_spreadsheet
#. odoo-python
#: code:addons/documents_spreadsheet/models/documents_document.py:0
msgid "You don't have access to this document"
msgstr "이 문서에 대한 액세스 권한이 없습니다."
