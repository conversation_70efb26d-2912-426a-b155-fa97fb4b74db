# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_subcontracting_dropshipping
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: mrp_subcontracting_dropshipping
#: model:ir.model.fields,help:mrp_subcontracting_dropshipping.field_purchase_order__default_location_dest_id_is_subcontracting_loc
msgid ""
"Check this box to create a new dedicated subcontracting location for this "
"company. Note that standard subcontracting routes will be adapted so as to "
"take these into account automatically."
msgstr "勾選此方塊以為此公司建立新的專屬分判地點。請注意，標準分判路線將會調整，以便自動考慮這些因素。"

#. module: mrp_subcontracting_dropshipping
#: model:ir.model,name:mrp_subcontracting_dropshipping.model_res_company
msgid "Companies"
msgstr "公司"

#. module: mrp_subcontracting_dropshipping
#: model:ir.model.fields,field_description:mrp_subcontracting_dropshipping.field_res_company__dropship_subcontractor_pick_type_id
msgid "Dropship Subcontractor Pick Type"
msgstr "外包直運商揀貨類型"

#. module: mrp_subcontracting_dropshipping
#. odoo-python
#: code:addons/mrp_subcontracting_dropshipping/models/stock_warehouse.py:0
#: model:stock.route,name:mrp_subcontracting_dropshipping.route_subcontracting_dropshipping
msgid "Dropship Subcontractor on Order"
msgstr "外包直運商訂單"

#. module: mrp_subcontracting_dropshipping
#: model:ir.model.fields,field_description:mrp_subcontracting_dropshipping.field_stock_warehouse__subcontracting_dropshipping_to_resupply
msgid "Dropship Subcontractors"
msgstr "外包直運商"

#. module: mrp_subcontracting_dropshipping
#: model:ir.model.fields,help:mrp_subcontracting_dropshipping.field_stock_warehouse__subcontracting_dropshipping_to_resupply
msgid "Dropship subcontractors with components"
msgstr "帶組件的 外包直運商"

#. module: mrp_subcontracting_dropshipping
#: model:ir.model.fields,field_description:mrp_subcontracting_dropshipping.field_purchase_order__default_location_dest_id_is_subcontracting_loc
msgid "Is a Subcontracting Location?"
msgstr "是分判地點？"

#. module: mrp_subcontracting_dropshipping
#: model:ir.model,name:mrp_subcontracting_dropshipping.model_stock_warehouse_orderpoint
msgid "Minimum Inventory Rule"
msgstr "最小庫存規則"

#. module: mrp_subcontracting_dropshipping
#. odoo-python
#: code:addons/mrp_subcontracting_dropshipping/models/purchase.py:0
msgid "Please note this purchase order is for subcontracting purposes."
msgstr "請注意，此採購訂單用於分判目的。"

#. module: mrp_subcontracting_dropshipping
#: model:ir.model,name:mrp_subcontracting_dropshipping.model_stock_replenish_mixin
msgid "Product Replenish Mixin"
msgstr "產品補貨混入程式"

#. module: mrp_subcontracting_dropshipping
#: model:ir.model,name:mrp_subcontracting_dropshipping.model_purchase_order
msgid "Purchase Order"
msgstr "採購訂單"

#. module: mrp_subcontracting_dropshipping
#: model:ir.model,name:mrp_subcontracting_dropshipping.model_stock_move
msgid "Stock Move"
msgstr "庫存移動"

#. module: mrp_subcontracting_dropshipping
#: model:ir.model,name:mrp_subcontracting_dropshipping.model_stock_rule
msgid "Stock Rule"
msgstr "庫存規則"

#. module: mrp_subcontracting_dropshipping
#: model:ir.model.fields,field_description:mrp_subcontracting_dropshipping.field_stock_warehouse__subcontracting_dropshipping_pull_id
msgid "Subcontracting-Dropshipping MTS Rule"
msgstr "外包直運 MTS 規則"

#. module: mrp_subcontracting_dropshipping
#: model:ir.model,name:mrp_subcontracting_dropshipping.model_stock_picking
msgid "Transfer"
msgstr "轉移"

#. module: mrp_subcontracting_dropshipping
#: model:ir.model,name:mrp_subcontracting_dropshipping.model_stock_warehouse
msgid "Warehouse"
msgstr "倉庫"

#. module: mrp_subcontracting_dropshipping
#. odoo-python
#: code:addons/mrp_subcontracting_dropshipping/models/purchase.py:0
msgid "Warning"
msgstr "警告"
