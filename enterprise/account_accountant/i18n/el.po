# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_accountant
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON> <george_tarasid<PERSON>@yahoo.com>, 2024
# e4fce5bedf6252b8b8ee3eca125b0937_74696f9 <f02d253b60232e0435ee99961a36d326_521572>, 2024
# <AUTHOR> <EMAIL>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:52+0000\n"
"PO-Revision-Date: 2024-09-25 09:43+0000\n"
"Last-Translator: Kostas Goutoudis <<EMAIL>>, 2024\n"
"Language-Team: Greek (https://app.transifex.com/odoo/teams/41243/el/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: el\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget_line.py:0
msgid ""
"%(display_name_html)s with an open amount of %(open_amount)s will be fully "
"reconciled by the transaction."
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget_line.py:0
msgid ""
"%(display_name_html)s with an open amount of %(open_amount)s will be reduced"
" by %(amount)s."
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_form
msgid "-> Reconcile"
msgstr "-> Συμψηφισμός"

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_0
msgid "<b class=\"tip_title\">Tip: Bulk update journal items</b>"
msgstr ""

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_1
msgid ""
"<b class=\"tip_title\">Tip: Find an Accountant or register your Accounting "
"Firm</b>"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_reconcile_model_form_inherit_account_accountant
msgid ""
"<i title=\"Run manually\" role=\"img\" aria-label=\"Run manually\" class=\"fa fa-refresh\"/>\n"
"                            Run manually"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "<i>Lock transactions up to specific dates, inclusive</i>"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_form_inherit
msgid "<span class=\"o_stat_text\">1 Bank Transaction</span>"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_form_inherit
msgid "<span class=\"o_stat_text\">Bank Statement</span>"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_wizard
msgid ""
"<span class=\"oe_inline o_form_label mx-3\" "
"invisible=\"single_currency_mode\"> in </span>"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid ""
"<span class=\"text-muted o_form_label\" invisible=\"not min_fiscalyear_lock_date_exception_for_me_id                                                  or not min_fiscalyear_lock_date_exception_for_everyone_id\">\n"
"                                      <span style=\"white-space: pre\">; </span>\n"
"                                </span>"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid ""
"<span class=\"text-muted o_form_label\" invisible=\"not min_purchase_lock_date_exception_for_me_id                                                  or not min_purchase_lock_date_exception_for_everyone_id\">\n"
"                                      <span style=\"white-space: pre\">; </span>\n"
"                                </span>"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid ""
"<span class=\"text-muted o_form_label\" invisible=\"not min_sale_lock_date_exception_for_me_id                                                  or not min_sale_lock_date_exception_for_everyone_id\">\n"
"                                      <span style=\"white-space: pre\">; </span>\n"
"                                </span>"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid ""
"<span class=\"text-muted o_form_label\" invisible=\"not min_tax_lock_date_exception_for_me_id                                                  or not min_tax_lock_date_exception_for_everyone_id\">\n"
"                                      <span style=\"white-space: pre\">; </span>\n"
"                                </span>"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid ""
"<span class=\"text-muted\" invisible=\"hard_lock_date != current_hard_lock_date\">\n"
"                                    <i>to ensure inalterability</i>\n"
"                                </span>\n"
"                                <span class=\"text-danger o_form_label\" invisible=\"hard_lock_date == current_hard_lock_date\">\n"
"                                    <i>This change is irreversible</i>\n"
"                                </span>"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid ""
"<span class=\"text-muted\" invisible=\"min_fiscalyear_lock_date_exception_for_me_id or min_fiscalyear_lock_date_exception_for_everyone_id\">\n"
"                                    <i>but allow exceptions</i>\n"
"                                </span>"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid ""
"<span class=\"text-muted\" invisible=\"min_tax_lock_date_exception_for_me_id or min_tax_lock_date_exception_for_everyone_id\">\n"
"                                    <i>after a tax closing</i>\n"
"                                </span>"
msgstr ""

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_1
msgid "<span class=\"tip_button_text\">Find an Accountant</span>"
msgstr ""

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_1
msgid "<span class=\"tip_button_text\">Register your Accounting Firm</span>"
msgstr ""

#. module: account_accountant
#: model_terms:web_tour.tour,rainbow_man_message:account_accountant.account_accountant_tour
msgid ""
"<span><strong><b>Good job!</b> You went through all steps of this tour.</strong>\n"
"            <br>See how to manage your customer invoices in the <b>Customers/Invoices</b> menu\n"
"        </span>"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "<span>Exception</span>"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
#: model:ir.model,name:account_accountant.model_account_account
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__account_id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__account_id
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "Account"
msgstr "Λογαριασμός"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_chart_template
msgid "Account Chart Template"
msgstr "Πρότυπο γραφήματος λογαριασμού"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.action_account_group_tree
#: model:ir.ui.menu,name:account_accountant.menu_account_group
msgid "Account Groups"
msgstr "Ομάδες Λογαριασμού"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.account_tag_action
#: model:ir.ui.menu,name:account_accountant.account_tag_menu
msgid "Account Tags"
msgstr "Ετικέτες Λογαριασμού"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__transfer_from_account_id
msgid "Account Transfer From"
msgstr ""

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_auto_reconcile_wizard
msgid "Account automatic reconciliation wizard"
msgstr ""

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_reconcile_wizard
msgid "Account reconciliation wizard"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__deferred_expense_account_id
msgid "Account used for deferred expenses"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__deferred_revenue_account_id
msgid "Account used for deferred revenues"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__account_ids
msgid "Accounts"
msgstr "Λογαριασμοί"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__message_needaction
msgid "Action Needed"
msgstr "Απαιτείται ενέργεια"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_quick_create.xml:0
msgid "Add & Close"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_quick_create.xml:0
msgid "Add & New"
msgstr ""

#. module: account_accountant
#: model_terms:ir.actions.act_window,help:account_accountant.account_tag_action
msgid "Add a new tag"
msgstr "Προσθήκη νέας ετικέτας"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
msgid ""
"After the data extraction, check and validate the bill. If no vendor has "
"been found, add one before validating."
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/finish_buttons.xml:0
msgid "All Transactions"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__reco_model_autocomplete_ids
msgid "All reconciliation models"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__allow_partials
msgid "Allow partials"
msgstr ""

#. module: account_accountant
#: model:res.groups,name:account_accountant.group_fiscal_year
msgid "Allow to define fiscal years of more or less than a year"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__amount_currency
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_wizard
msgid "Amount"
msgstr "Ποσό"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "Amount (Company Currency)"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "Amount (Foreign Currency)"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__amount_currency
msgid "Amount Currency"
msgstr "Ποσό Σχετ. Νομίσματος"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_list_bank_rec_widget
msgid "Amount Due"
msgstr "Οφειλόμενο Ποσό"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_list_bank_rec_widget
msgid "Amount Due (in currency)"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__amount_transaction_currency
msgid "Amount in Currency"
msgstr "Ποσό σε νόμισμα"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__amount
msgid "Amount in company currency"
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
msgid ""
"An entry will transfer %(amount)s from %(from_account)s to %(to_account)s."
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
msgid "Analytic"
msgstr "Αναλυτικοί"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__analytic_distribution
msgid "Analytic Distribution"
msgstr "Αναλυτική Κατανομή"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__analytic_precision
msgid "Analytic Precision"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__use_anglo_saxon
msgid "Anglo-Saxon Accounting"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_change_lock_date__current_hard_lock_date
#: model:ir.model.fields,help:account_accountant.field_account_change_lock_date__hard_lock_date
msgid ""
"Any entry up to and including that date will be postponed to a later time, "
"in accordance with its journal sequence. This lock date is irreversible and "
"does not allow any exception."
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_change_lock_date__fiscalyear_lock_date
msgid ""
"Any entry up to and including that date will be postponed to a later time, "
"in accordance with its journal's sequence."
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_change_lock_date__tax_lock_date
msgid ""
"Any entry with taxes up to and including that date will be postponed to a "
"later time, in accordance with its journal's sequence. The tax lock date is "
"automatically set when the tax closing entry is posted."
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_change_lock_date__purchase_lock_date
msgid ""
"Any purchase entry prior to and including this date will be postponed to a "
"later date, in accordance with its journal's sequence."
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_change_lock_date__sale_lock_date
msgid ""
"Any sales entry prior to and including this date will be postponed to a "
"later date, in accordance with its journal's sequence."
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__sign_invoice
msgid "Authorized Signatory on invoice"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.report_invoice_document
msgid "Authorized signatory"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/move_line_list_reconcile/move_line_list_reconcile.xml:0
msgid "Auto-reconcile"
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_auto_reconcile_wizard.py:0
msgid "Automatically Reconciled Entries"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__available_reco_model_ids
msgid "Available Reco Model"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/finish_buttons.xml:0
msgid "Back to"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/global_info.xml:0
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__balance
msgid "Balance"
msgstr "Υπόλοιπο"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_digest_digest__kpi_account_bank_cash
msgid "Bank & Cash Moves"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__bank_account
msgid "Bank Account"
msgstr "Τραπεζικός Λογαριασμός"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_bank_statement.py:0
#: model:ir.actions.act_window,name:account_accountant.action_bank_statement_line_transactions
#: model:ir.actions.act_window,name:account_accountant.action_bank_statement_line_transactions_kanban
msgid "Bank Reconciliation"
msgstr ""

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_bank_statement
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_form_bank_rec_widget
msgid "Bank Statement"
msgstr "Κατάσταση Κίνησης Τραπεζικού Λογαριασμού"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_bank_statement.py:0
msgid "Bank Statement %s.pdf"
msgstr ""

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr "Γραμμή Κίνησης Τραπεζικού Λογαριασμού"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_bank_statement.py:0
msgid "Bank Statement.pdf"
msgstr ""

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_bank_rec_widget
msgid "Bank reconciliation widget for a single statement line"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Based on"
msgstr "Βασισμένο στο"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_model_widget_wizard
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_form_bank_rec_widget
msgid "Cancel"
msgstr "Ακύρωση"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_change_lock_date
msgid "Change Lock Date"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_reconcile_wizard__to_check
msgid ""
"Check if you are not certain of all the information of the counterpart."
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__st_line_checked
msgid "Checked"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/move_line_list/move_line_list.xml:0
msgid "Choose a line to preview its attachments."
msgstr ""

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__account_auto_reconcile_wizard__search_mode__zero_balance
msgid "Clear Account"
msgstr ""

#. module: account_accountant
#: model_terms:ir.actions.act_window,help:account_accountant.actions_account_fiscal_year
msgid "Click here to create a new fiscal year."
msgstr ""

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_1
msgid ""
"Click here to find an accountant or if you want to list out your accounting "
"services on Odoo"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
msgid ""
"Click on a fetched bank transaction to start the reconciliation process."
msgstr ""

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_res_company
msgid "Companies"
msgstr "Εταιρίες"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__company_id
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__company_id
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__company_id
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__company_id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__company_id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__company_id
msgid "Company"
msgstr "Εταιρία"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__company_currency_id
msgid "Company currency"
msgstr ""

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_res_config_settings
msgid "Config Settings"
msgstr "Ρυθμίσεις διαμόρφωσης"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
msgid "Confirm the transaction."
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/rainbowman_content.xml:0
msgid "Congrats, you're all done!"
msgstr "Συγχαρητήρια, είστε σε όλα έτοιμοι!"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
msgid "Connect your bank and get your latest transactions."
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_model_widget_wizard
msgid "Counterpart Values"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__country_code
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__country_code
msgid "Country Code"
msgstr "Κωδικός Χώρας"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.action_bank_statement_form_bank_rec_widget
msgid "Create Statement"
msgstr ""

#. module: account_accountant
#: model_terms:ir.actions.act_window,help:account_accountant.action_account_group_tree
msgid "Create a new account group"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
msgid "Create a new transaction."
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "Create model"
msgstr "Δημιουργία μοντέλου"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
msgid ""
"Create your first vendor bill.<br/><br/><i>Tip: If you don’t have one on "
"hand, use our sample bill.</i>"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__create_uid
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__create_uid
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__create_uid
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__create_uid
msgid "Created by"
msgstr "Δημιουργήθηκε από"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__create_date
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__create_date
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__create_date
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__create_date
msgid "Created on"
msgstr "Δημιουργήθηκε στις"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__credit
msgid "Credit"
msgstr "Πίστωση"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement_line__cron_last_check
msgid "Cron Last Check"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
#: model:ir.model,name:account_accountant.model_res_currency
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__currency_id
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Currency"
msgstr "Νόμισμα"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__reco_currency_id
msgid "Currency to use for reconciliation"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__current_hard_lock_date
msgid "Current Hard Lock"
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
msgid "Customer/Vendor"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__date
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__date
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_search_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Date"
msgstr "Ημερομηνία"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_move_line__deferred_end_date
msgid "Date at which the deferred expense/revenue ends"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_move_line__deferred_start_date
msgid "Date at which the deferred expense/revenue starts"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__res_company__deferred_expense_amount_computation_method__day
#: model:ir.model.fields.selection,name:account_accountant.selection__res_company__deferred_revenue_amount_computation_method__day
msgid "Days"
msgstr "Ημέρες"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__debit
msgid "Debit"
msgstr "Χρέωση"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__journal_default_account_id
msgid "Default Account"
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
msgid "Deferral of %s"
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement_line__deferred_move_ids
#: model:ir.model.fields,field_description:account_accountant.field_account_move__deferred_move_ids
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_form_inherit
msgid "Deferred Entries"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement_line__deferred_entry_type
#: model:ir.model.fields,field_description:account_accountant.field_account_move__deferred_entry_type
msgid "Deferred Entry Type"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__account_move__deferred_entry_type__expense
msgid "Deferred Expense"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__deferred_expense_account_id
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__deferred_expense_account_id
msgid "Deferred Expense Account"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__deferred_expense_amount_computation_method
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__deferred_expense_amount_computation_method
msgid "Deferred Expense Based on"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__deferred_expense_journal_id
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__deferred_expense_journal_id
msgid "Deferred Expense Journal"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__account_move__deferred_entry_type__revenue
msgid "Deferred Revenue"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__deferred_revenue_account_id
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__deferred_revenue_account_id
msgid "Deferred Revenue Account"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__deferred_revenue_amount_computation_method
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__deferred_revenue_amount_computation_method
msgid "Deferred Revenue Based on"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__deferred_revenue_journal_id
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__deferred_revenue_journal_id
msgid "Deferred Revenue Journal"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Deferred expense"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Deferred expense entries:"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Deferred revenue"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Deferred revenue entries:"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Define fiscal years of more or less than one year"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Deposits"
msgstr ""

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_digest_digest
msgid "Digest"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_auto_reconcile_wizard
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_wizard
msgid "Discard"
msgstr "Απόρριψη"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Discount Amount"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Discount Date"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "Discuss"
msgstr "Συζήτηση"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__display_allow_partials
msgid "Display Allow Partials"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__display_name
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__display_name
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__display_name
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__display_name
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__display_name
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__display_name
msgid "Display Name"
msgstr "Εμφάνιση Ονόματος"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__display_stroked_amount_currency
msgid "Display Stroked Amount Currency"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__display_stroked_balance
msgid "Display Stroked Balance"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__sign_invoice
msgid "Display signing field on invoices"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__distribution_analytic_account_ids
msgid "Distribution Analytic Account"
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/digest.py:0
msgid "Do not have access, skip this data for user's digest email"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_list_bank_rec_widget
msgid "Document"
msgstr "Έγγραφο"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_change_lock_date.py:0
msgid "Draft Entries"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__edit_mode
msgid "Edit Mode"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__edit_mode_amount
msgid "Edit Mode Amount"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__edit_mode_reco_currency_id
msgid "Edit Mode Reco Currency"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__edit_mode_amount_currency
msgid "Edit mode amount"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__date_to
#: model:ir.model.fields,field_description:account_accountant.field_account_move_line__deferred_end_date
msgid "End Date"
msgstr "Ημερ. Λήξης"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_fiscal_year__date_to
msgid "Ending Date, included in the fiscal year."
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_company__invoicing_switch_threshold
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__invoicing_switch_threshold
msgid ""
"Every payment and invoice before this date will receive the 'From Invoicing'"
" status, hiding all the accounting entries related to it. Use this option "
"after installing Accounting if you were using only Invoicing before, before "
"importing all your actual accounting data in to Odoo."
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__exception_duration
msgid "Exception Duration"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__exception_needed_fields
msgid "Exception Needed Fields"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__exception_reason
msgid "Exception Reason"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__exception_applies_to
msgid "Exception applies"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__exception_needed
msgid "Exception needed"
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
msgid "Exchange Difference: %s"
msgstr ""

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_fiscal_year
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Fiscal Year"
msgstr "Λογιστική Χρήση"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.action_account_fiscal_year_form
msgid "Fiscal Year 2018"
msgstr ""

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.actions_account_fiscal_year
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__group_fiscal_year
#: model:ir.ui.menu,name:account_accountant.menu_account_fiscal_year
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Fiscal Years"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__fiscalyear_last_day
msgid "Fiscalyear Last Day"
msgstr "Τελευταία Ημέρα Οικονομικού Έτους"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__fiscalyear_last_month
msgid "Fiscalyear Last Month"
msgstr "Τελευταίος Μήνας Οικονομικού Έτους"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__flag
msgid "Flag"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__message_follower_ids
msgid "Followers"
msgstr "Ακόλουθοι"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__message_partner_ids
msgid "Followers (Partners)"
msgstr "Ακόλουθοι (Συνεργάτες)"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "For everyone:"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "For me:"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__force_partials
msgid "Force Partials"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__force_price_included_taxes
msgid "Force Price Included Taxes"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__transaction_currency_id
msgid "Foreign Currency"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__form_index
msgid "Form Index"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__from_date
msgid "From"
msgstr "Από"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "From Trade Payable accounts"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "From Trade Receivable accounts"
msgstr ""

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_0
msgid ""
"From any list view, select multiple records and the list becomes editable. "
"If you update a cell, selected records are updated all at once. Use this "
"feature to update multiple journal entries from the General Ledger, or any "
"Journal view."
msgstr ""

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__res_company__deferred_expense_amount_computation_method__full_months
#: model:ir.model.fields.selection,name:account_accountant.selection__res_company__deferred_revenue_amount_computation_method__full_months
msgid "Full Months"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__generate_deferred_expense_entries_method
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__generate_deferred_expense_entries_method
msgid "Generate Deferred Expense Entries"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__generate_deferred_revenue_entries_method
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__generate_deferred_revenue_entries_method
msgid "Generate Deferred Revenue Entries"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Generate Entries"
msgstr "Δημιουργία Εγγραφών"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "Group By"
msgstr "Ομαδοποίηση κατά"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__group_tax_id
msgid "Group Tax"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__hard_lock_date
msgid "Hard Lock"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_move_line__has_abnormal_deferred_dates
msgid "Has Abnormal Deferred Dates"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_move_line__has_deferred_moves
msgid "Has Deferred Moves"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__has_message
msgid "Has Message"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__id
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__id
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__id
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__id
msgid "ID"
msgstr "Κωδικός"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_bank_statement__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Εάν επιλεγεί τα νέα μηνύματα χρειάζονται την προσοχή σας."

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_bank_statement__message_has_error
#: model:ir.model.fields,help:account_accountant.field_account_bank_statement__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_bank_rec_widget__st_line_checked
msgid ""
"If this checkbox is not ticked, it means that the user was not sure of all "
"the related information at the time of the creation of the move and that the"
" move needs to be checked again."
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "In Currency"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_search_bank_rec_widget
msgid "Incoming"
msgstr "Εισερχόμενα"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/res_config_settings.py:0
msgid ""
"Incorrect fiscal year date: day is out of range for month. Month: %(month)s;"
" Day: %(day)s"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__index
msgid "Index"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget__state__invalid
msgid "Invalid"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Invalid statements"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_bank_rec_widget__state
msgid ""
"Invalid: The bank transaction can't be validate since the suspense account is still involved\n"
"Valid: The bank transaction can be validated.\n"
"Reconciled: The bank transaction has already been processed. Nothing left to do."
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_list_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_search_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Invoice Date"
msgstr "Ημερ. Τιμολογίου"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__invoicing_switch_threshold
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__invoicing_switch_threshold
msgid "Invoicing Switch Threshold"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__message_is_follower
msgid "Is Follower"
msgstr "Είναι Ακόλουθος"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__is_multi_currency
msgid "Is Multi Currency"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__is_rec_pay_account
msgid "Is Rec Pay Account"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__st_line_is_reconciled
msgid "Is Reconciled"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__is_write_off_required
msgid "Is a write-off move required to reconcile"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__is_transfer_required
msgid "Is an account transfer required"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__transfer_warning_message
msgid "Is an account transfer required to reconcile"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__lock_date_violated_warning_message
msgid "Is the date violating the lock date of moves"
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_change_lock_date.py:0
msgid "It is not possible to decrease or remove the Hard Lock Date."
msgstr ""

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_journal
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__journal_id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__st_line_journal_id
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Journal"
msgstr "Ημερολόγιο"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__journal_currency_id
msgid "Journal Currency"
msgstr ""

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_move
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__move_id
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_search_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Journal Entry"
msgstr "Εγγραφή Ημερολογίου"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_move_line
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_search_bank_rec_widget
msgid "Journal Item"
msgstr "Στοιχείο Ημερολογίου"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Journal Items"
msgstr "Εγγραφές Ημερολογίων"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.action_move_line_posted_unreconciled
msgid "Journal Items to reconcile"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "Journal items where matching number isn't set"
msgstr "Στοιχεία ημερολογίου όπου ο αριθμός αντιστοίχησης δεν έχει οριστεί"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid ""
"Journal items where the account allows reconciliation no matter the residual"
" amount"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__deferred_expense_journal_id
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__deferred_revenue_journal_id
msgid "Journal used for deferred entries"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_digest_digest__kpi_account_bank_cash_value
msgid "Kpi Account Bank Cash Value"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__label
msgid "Label"
msgstr "Ετικέτα"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Last Day"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
msgid "Last Statement"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__write_uid
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__write_uid
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__write_uid
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__write_uid
msgid "Last Updated by"
msgstr "Τελευταία Ενημέρωση από"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__write_date
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__write_date
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__write_date
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__write_date
msgid "Last Updated on"
msgstr "Τελευταία Ενημέρωση στις"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
msgid "Latest Statement"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Legal signatory"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
msgid "Let’s go back to the dashboard."
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__line_ids
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__line_ids
msgid "Line"
msgstr "Γραμμή"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_bank_rec_widget_line
msgid "Line of the bank reconciliation widget"
msgstr ""

#. module: account_accountant
#: model:ir.ui.menu,name:account_accountant.menu_action_change_lock_date
msgid "Lock Dates"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__fiscalyear_lock_date
msgid "Lock Everything"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__fiscalyear_lock_date_for_everyone
msgid "Lock Everything For Everyone"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__fiscalyear_lock_date_for_me
msgid "Lock Everything For Me"
msgstr ""

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.action_view_account_change_lock_date
msgid "Lock Journal Entries"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__purchase_lock_date
msgid "Lock Purchases"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__purchase_lock_date_for_everyone
msgid "Lock Purchases For Everyone"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__purchase_lock_date_for_me
msgid "Lock Purchases For Me"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__sale_lock_date
msgid "Lock Sales"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__sale_lock_date_for_everyone
msgid "Lock Sales For Everyone"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__sale_lock_date_for_me
msgid "Lock Sales For Me"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__tax_lock_date
msgid "Lock Tax Return"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__tax_lock_date_for_everyone
msgid "Lock Tax Return For Everyone"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__tax_lock_date_for_me
msgid "Lock Tax Return For Me"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__message_main_attachment_id
msgid "Main Attachment"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "Manual Operations"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__res_company__generate_deferred_expense_entries_method__manual
#: model:ir.model.fields.selection,name:account_accountant.selection__res_company__generate_deferred_revenue_entries_method__manual
msgid "Manually & Grouped"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__manually_modified
msgid "Manually Modified"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/list_view_switcher.js:0
msgid "Match"
msgstr "Αντιστοίχιση "

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "Match Existing Entries"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_kanban_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Matched"
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_payment.py:0
msgid "Matched Transactions"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Matching"
msgstr "Aντιστοίχιση"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__matching_rules_allow_auto_reconcile
msgid "Matching Rules Allow Auto Reconcile"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_quick_create_form_bank_rec_widget
msgid "Memo"
msgstr "Σχετικά "

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_ir_ui_menu
msgid "Menu"
msgstr "Μενού"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__message_ids
msgid "Messages"
msgstr "Μηνύματα"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__deferred_expense_amount_computation_method
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__deferred_revenue_amount_computation_method
msgid "Method used to compute the amount of deferred entries"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__generate_deferred_expense_entries_method
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__generate_deferred_revenue_entries_method
msgid "Method used to generate deferred entries"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__min_fiscalyear_lock_date_exception_for_everyone_id
msgid "Min Fiscalyear Lock Date Exception For Everyone"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__min_fiscalyear_lock_date_exception_for_me_id
msgid "Min Fiscalyear Lock Date Exception For Me"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__min_purchase_lock_date_exception_for_everyone_id
msgid "Min Purchase Lock Date Exception For Everyone"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__min_purchase_lock_date_exception_for_me_id
msgid "Min Purchase Lock Date Exception For Me"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__min_sale_lock_date_exception_for_everyone_id
msgid "Min Sale Lock Date Exception For Everyone"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__min_sale_lock_date_exception_for_me_id
msgid "Min Sale Lock Date Exception For Me"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__min_tax_lock_date_exception_for_everyone_id
msgid "Min Tax Lock Date Exception For Everyone"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__min_tax_lock_date_exception_for_me_id
msgid "Min Tax Lock Date Exception For Me"
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
msgid "Misc"
msgstr "Διάφορα"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__res_company__deferred_expense_amount_computation_method__month
#: model:ir.model.fields.selection,name:account_accountant.selection__res_company__deferred_revenue_amount_computation_method__month
msgid "Months"
msgstr "Μήνες"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "More"
msgstr "Περισσότερα"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_move_line__move_attachment_ids
msgid "Move Attachment"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__move_line_ids
msgid "Move lines to reconcile"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__name
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__name
msgid "Name"
msgstr "Περιγραφή"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__narration
msgid "Narration"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "New"
msgstr "Νέα"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.action_bank_statement_line_form_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_form_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_quick_create_form_bank_rec_widget
msgid "New Transaction"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/move_line_list/move_line_list.xml:0
msgid "No attachments linked."
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "No statement"
msgstr ""

#. module: account_accountant
#: model_terms:ir.actions.act_window,help:account_accountant.action_bank_statement_line_transactions
#: model_terms:ir.actions.act_window,help:account_accountant.action_bank_statement_line_transactions_kanban
msgid "No transactions matching your filters were found."
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Not Matched"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "Not locked"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_tree_bank_rec_widget
msgid "Notes"
msgstr "Σημειώσεις"

#. module: account_accountant
#: model_terms:ir.actions.act_window,help:account_accountant.action_bank_statement_line_transactions
#: model_terms:ir.actions.act_window,help:account_accountant.action_bank_statement_line_transactions_kanban
msgid "Nothing to do here!"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
msgid "Now, we'll create your first invoice (accountant)"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__message_needaction_counter
msgid "Number of Actions"
msgstr "Πλήθος ενεργειών"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__message_has_error_counter
msgid "Number of errors"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_bank_statement__message_needaction_counter
msgid "Number of messages requiring action"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_bank_statement__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__res_company__generate_deferred_expense_entries_method__on_validation
#: model:ir.model.fields.selection,name:account_accountant.selection__res_company__generate_deferred_revenue_entries_method__on_validation
msgid "On bill validation"
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_change_lock_date.py:0
msgid "Only Billing Administrators are allowed to change lock dates!"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_wizard
msgid ""
"Only partial reconciliation is possible. Proceed in multiple steps if you "
"want to full reconcile."
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/move_line_list/move_line_list.xml:0
msgid "Open attachment in pop out"
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
msgid "Open balance of %(amount)s"
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
msgid "Original Deferred Entries"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement_line__deferred_original_move_ids
#: model:ir.model.fields,field_description:account_accountant.field_account_move__deferred_original_move_ids
msgid "Original Invoices"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Originator Tax"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_search_bank_rec_widget
msgid "Outgoing"
msgstr "Εξερχόμενα"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__to_partner_id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__partner_id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__partner_id
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_quick_create_form_bank_rec_widget
msgid "Partner"
msgstr "Συναλλασόμενος"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__partner_currency_id
msgid "Partner Currency"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__partner_name
msgid "Partner Name"
msgstr "Όνομα Συνεργάτη"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__partner_payable_account_id
msgid "Partner Payable Account"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__partner_payable_amount
msgid "Partner Payable Amount"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__partner_receivable_account_id
msgid "Partner Receivable Account"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__partner_receivable_amount
msgid "Partner Receivable Amount"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__partner_ids
msgid "Partners"
msgstr "Συνεργάτες"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "Payable"
msgstr "Πληρωτέα"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "Payable:"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_payment_form_inherit_account_accountant
msgid "Payment Matching"
msgstr "Αντιστοίχιση Συναλλαγής"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement_line__payment_state_before_switch
#: model:ir.model.fields,field_description:account_accountant.field_account_move__payment_state_before_switch
msgid "Payment State Before Switch"
msgstr ""

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_payment
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Payments"
msgstr "Συναλλαγές"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
msgid "Payments Matching"
msgstr "Αντιστοίχιση Συναλλαγών"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__account_auto_reconcile_wizard__search_mode__one_to_one
msgid "Perfect Match"
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
msgid "Please set the deferred accounts in the accounting settings."
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
msgid "Please set the deferred journal in the accounting settings."
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__predict_bill_product
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__predict_bill_product
msgid "Predict Bill Product"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Predict vendor bill product"
msgstr ""

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_reconcile_model
msgid ""
"Preset to create journal entries during a invoices and payments matching"
msgstr ""
"Προεπιλογή για τη δημιουργία καταχωρήσεων ημερολογίου κατά τη διάρκεια της "
"αντιστοίχισης τιμολογίων και πληρωμών"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__rating_ids
msgid "Ratings"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "Reason..."
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "Receivable"
msgstr "Εισπρακτέα"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "Receivable:"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__search_mode
#: model:ir.ui.menu,name:account_accountant.menu_account_reconcile
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_auto_reconcile_wizard
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_wizard
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_payment_tree
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_tree
msgid "Reconcile"
msgstr "Συμψηφισμός"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_wizard
msgid "Reconcile & open"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__reco_account_id
msgid "Reconcile Account"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__reconcile_model_id
msgid "Reconcile Model"
msgstr ""

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.action_open_auto_reconcile_wizard
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_auto_reconcile_wizard
msgid "Reconcile automatically"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_auto_reconcile_wizard__search_mode
msgid ""
"Reconcile journal items with opposite balance or clear accounts with a zero "
"balance"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget__state__reconciled
msgid "Reconciled"
msgstr "Συμψηφισμένη"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__reco_model_id
msgid "Reconciliation model"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Record cost of goods sold in your journal entries"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__ref
msgid "Ref"
msgstr "Σχετικό"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "Reference"
msgstr "Σχετικό"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_form_inherit
msgid "Related Purchase(s)"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_form_inherit
msgid "Related Sale(s)"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "Reset"
msgstr "Επαναφορά"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Residual"
msgstr "Υπόλοιπο"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Residual in Currency"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__return_todo_command
msgid "Return Todo Command"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "Review"
msgstr "Επισκόπηση"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "Revoke"
msgstr ""

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_reconcile_model_line
msgid "Rules for the reconciliation model"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__message_has_sms_error
msgid "SMS Delivery error"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "Save"
msgstr "Αποθήκευση"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_form_bank_rec_widget
msgid "Save & Close"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_form_bank_rec_widget
msgid "Save & New"
msgstr "Αποθήκευση  & Δημιουργία Νέου"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "Search Journal Items to Reconcile"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__signing_user
msgid ""
"Select a user here to override every signature on invoice by this user's "
"signature"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__selected_aml_ids
msgid "Selected Aml"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__selected_reco_model_id
msgid "Selected Reco Model"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
msgid "Set an amount."
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "Set as Checked"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
msgid "Set the payment reference."
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__show_draft_entries_warning
msgid "Show Draft Entries Warning"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement_line__show_signature_area
#: model:ir.model.fields,field_description:account_accountant.field_account_move__show_signature_area
msgid "Show Signature Area"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__module_sign
msgid "Sign"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement_line__signature
#: model:ir.model.fields,field_description:account_accountant.field_account_move__signature
msgid "Signature"
msgstr "Υπογραφή"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__signing_user
msgid "Signature used to sign all the invoice"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement_line__signing_user
#: model:ir.model.fields,field_description:account_accountant.field_account_move__signing_user
msgid "Signer"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__signing_user
msgid "Signing User"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__single_currency_mode
msgid "Single Currency Mode"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_aml_id
msgid "Source Aml"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_aml_move_id
msgid "Source Aml Move"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_aml_move_name
msgid "Source Aml Move Name"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_amount_currency
msgid "Source Amount Currency"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_balance
msgid "Source Balance"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_credit
msgid "Source Credit"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_debit
msgid "Source Debit"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__st_line_id
msgid "St Line"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__st_line_transaction_details
msgid "St Line Transaction Details"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__date_from
#: model:ir.model.fields,field_description:account_accountant.field_account_move_line__deferred_start_date
msgid "Start Date"
msgstr "Ημερομηνία Έναρξης"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_fiscal_year__date_from
msgid "Start Date, included in the fiscal year."
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__state
msgid "State"
msgstr "Νομός/Πολιτεία"

#. module: account_accountant
#: model:ir.actions.server,name:account_accountant.action_bank_statement_attachment
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_kanban_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Statement"
msgstr "Δήλωση"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Statement Line"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__suggestion_amount_currency
msgid "Suggestion Amount Currency"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__suggestion_balance
msgid "Suggestion Balance"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__suggestion_html
msgid "Suggestion Html"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_list_bank_rec_widget
msgid "Suggestions"
msgstr ""

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_tax
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__tax_id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__tax_ids
msgid "Tax"
msgstr "Φόρος"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__tax_base_amount_currency
msgid "Tax Base Amount Currency"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Tax Grids"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__tax_repartition_line_id
msgid "Tax Repartition Line"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__tax_tag_ids
msgid "Tax Tag"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
msgid "Taxes"
msgstr "Φόροι"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/rainbowman_content.xml:0
msgid "That's on average"
msgstr "Κατά μέσο όρο"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_bank_rec_widget__country_code
#: model:ir.model.fields,help:account_accountant.field_bank_rec_widget_line__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_bank_rec_widget_line__amount_transaction_currency
msgid ""
"The amount expressed in an optional other currency if it is a multi-currency"
" entry."
msgstr ""
"Το ποσό, εκφρασμένο σε ένα (προαιρετικό) διαφορετικό νόμισμα αν πρόκειται "
"για εγγραφή πολλαπλών νομισμάτων."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
msgid ""
"The amount of the write-off of a single credit line should be strictly "
"negative."
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
msgid ""
"The amount of the write-off of a single debit line should be strictly "
"positive."
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
msgid "The amount of the write-off of a single line cannot be 0."
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
msgid ""
"The date you set violates the lock date of one of your entry. It will be "
"overriden by the following date : %(replacement_date)s"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_bank_statement_line__deferred_move_ids
#: model:ir.model.fields,help:account_accountant.field_account_move__deferred_move_ids
msgid "The deferred entries created by this invoice"
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_fiscal_year.py:0
msgid "The ending date must not be prior to the starting date."
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget_line.py:0
msgid ""
"The invoice %(display_name_html)s with an open amount of %(open_amount)s "
"will be entirely paid by the transaction."
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget_line.py:0
msgid ""
"The invoice %(display_name_html)s with an open amount of %(open_amount)s "
"will be reduced by %(amount)s."
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid ""
"The invoices up to this date will not be taken into account as accounting "
"entries"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_bank_rec_widget_line__transaction_currency_id
msgid "The optional other currency if it is a multi-currency entry."
msgstr "Το προαιρετικό νόμισμα αν πρόκειται για εγγραφή πολλαπλών νομισμάτων."

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_bank_statement_line__deferred_original_move_ids
#: model:ir.model.fields,help:account_accountant.field_account_move__deferred_original_move_ids
msgid "The original invoices that created the deferred entries"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid ""
"The system will try to predict the product on vendor bill lines based on the"
" label of the line"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid ""
"There are still draft entries in the period you want to lock.\n"
"                                You should either post or delete them."
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_bank_statement.py:0
msgid ""
"This bank transaction has been automatically validated using the "
"reconciliation model '%s'."
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
msgid ""
"This bank transaction is locked up tighter than a squirrel in a nut factory!"
" You can't hit the reset button on it. So, do you want to \"unreconcile\" it"
" instead?"
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
msgid "This can only be used on journal items"
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_reconcile_model_line.py:0
msgid ""
"This reconciliation model can't be used in the manual reconciliation widget "
"because its configuration is not adapted"
msgstr ""

#. module: account_accountant
#: model:digest.tip,name:account_accountant.digest_tip_account_accountant_0
msgid "Tip: Bulk update journal items"
msgstr ""

#. module: account_accountant
#: model:digest.tip,name:account_accountant.digest_tip_account_accountant_1
msgid "Tip: Find an Accountant or register your Accounting Firm"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__to_date
msgid "To"
msgstr "Σε"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__to_check
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "To Check"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_kanban_bank_rec_widget
msgid "To check"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "To enhance authenticity, add a signature to your invoices"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__todo_command
msgid "Todo Command"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Total Balance"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Total Credit"
msgstr "Πιστωτικό Σύνολο"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Total Debit"
msgstr "Χρεωστικό Σύνολο"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Total Residual"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Total Residual in Currency"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Transaction"
msgstr "Συναλλαγή"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__transaction_currency_id
msgid "Transaction Currency"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "Transaction Details"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_tree
msgid "Transactions"
msgstr "Συναλλαγές"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
msgid "Transfer from %s"
msgstr "Μεταφορά από %s"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
msgid "Transfer to %s"
msgstr "Μεταφορά σε %s"

#. module: account_accountant
#: model:ir.actions.server,name:account_accountant.auto_reconcile_bank_statement_line_ir_actions_server
msgid "Try to reconcile automatically your statement lines"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "Unreconciled"
msgstr "Μη Συμψηφισμένα"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/res_company.py:0
msgid "Unreconciled statements lines"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget__state__valid
msgid "Valid"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_model_widget_wizard
msgid "Validate"
msgstr "Επικύρωση"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/list_view_switcher.js:0
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_list_bank_rec_widget
msgid "View"
msgstr "Προβολή"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
msgid "View Reconciled Entries"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "View models"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__website_message_ids
msgid "Website Messages"
msgstr "Μηνύματα Ιστότοπου"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_bank_statement__website_message_ids
msgid "Website communication history"
msgstr "Ιστορικό επικοινωνίας ιστότοπου"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "With residual"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__wizard_id
msgid "Wizard"
msgstr "Αυτόματος Οδηγός"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__company_currency_id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__company_currency_id
msgid "Wizard Company Currency"
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
msgid "Write-Off"
msgstr "Διαγραφή"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
msgid "Write-Off Entry"
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_fiscal_year.py:0
msgid ""
"You can not have an overlap between two fiscal years, please correct the "
"start and/or end dates of your fiscal years."
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
msgid "You can only reconcile entries with up to two different accounts: %s"
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
msgid "You can't hit the reset button on a secured bank transaction."
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
msgid ""
"You cannot change the account for a deferred line in %(move_name)s if it has"
" already been deferred."
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
msgid "You cannot create a deferred entry with a start date but no end date."
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
msgid ""
"You cannot create a deferred entry with a start date later than the end "
"date."
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
msgid ""
"You cannot generate deferred entries for a miscellaneous journal entry."
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_fiscal_year.py:0
msgid "You cannot have a fiscal year on a child company."
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
msgid ""
"You cannot reset to draft an invoice that is grouped in deferral entry. You "
"can create a credit note instead."
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_change_lock_date.py:0
msgid "You cannot set a Lock Date in the future."
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget_line.py:0
msgid ""
"You might want to %(btn_start)sfully reconcile%(btn_end)s the document."
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget_line.py:0
msgid ""
"You might want to make a %(btn_start)spartial reconciliation%(btn_end)s "
"instead."
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget_line.py:0
msgid "You might want to record a %(btn_start)spartial payment%(btn_end)s."
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget_line.py:0
msgid ""
"You might want to set the invoice as %(btn_start)sfully paid%(btn_end)s."
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_change_lock_date.py:0
msgid "You need to select a duration for the exception."
msgstr ""

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_change_lock_date.py:0
msgid "You need to select who the exception applies to."
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/rainbowman_content.xml:0
msgid "You reconciled"
msgstr "Συμψηφίσατε"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__aml
msgid "aml"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__auto_balance
msgid "auto_balance"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_model_widget_wizard
msgid "e.g. Bank Fees"
msgstr "π.χ. Χρεώσεις Τραπεζών"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__early_payment
msgid "early_payment"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__exchange_diff
msgid "exchange_diff"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__account_change_lock_date__exception_duration__1h
msgid "for 1 hour"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__account_change_lock_date__exception_duration__15min
msgid "for 15 minutes"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__account_change_lock_date__exception_duration__24h
msgid "for 24 hours"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__account_change_lock_date__exception_duration__5min
msgid "for 5 minutes"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__account_change_lock_date__exception_applies_to__everyone
msgid "for everyone"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__account_change_lock_date__exception_applies_to__me
msgid "for me"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__account_change_lock_date__exception_duration__forever
msgid "forever"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "in"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__liquidity
msgid "liquidity"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__manual
msgid "manual"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__new_aml
msgid "new_aml"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/rainbowman_content.xml:0
msgid "seconds per transaction."
msgstr "δευτερόλεπτα ανά συναλλαγή."

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__tax_line
msgid "tax_line"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
msgid "to check"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
msgid "to reconcile"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/rainbowman_content.xml:0
msgid "transaction in"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/rainbowman_content.xml:0
msgid "transactions in"
msgstr "συναλλαγές σε"
