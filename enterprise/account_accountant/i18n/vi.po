# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_accountant
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:52+0000\n"
"PO-Revision-Date: 2024-09-25 09:43+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget_line.py:0
msgid ""
"%(display_name_html)s with an open amount of %(open_amount)s will be fully "
"reconciled by the transaction."
msgstr ""
"%(display_name_html)s với số tiền mở là %(open_amount)s sẽ được đối chiếu "
"toàn bộ bởi giao dịch."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget_line.py:0
msgid ""
"%(display_name_html)s with an open amount of %(open_amount)s will be reduced"
" by %(amount)s."
msgstr ""
"%(display_name_html)s với số tiền mở là %(open_amount)s sẽ được giảm "
"%(amount)s."

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_form
msgid "-> Reconcile"
msgstr "-> Đối chiếu"

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_0
msgid "<b class=\"tip_title\">Tip: Bulk update journal items</b>"
msgstr "<b class=\"tip_title\">Mẹo: Cập nhật hàng loạt hạng mục bút toán</b>"

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_1
msgid ""
"<b class=\"tip_title\">Tip: Find an Accountant or register your Accounting "
"Firm</b>"
msgstr ""
"<b class=\"tip_title\">Mẹo: Tìm Kế toán viên hoặc đăng ký Công ty kế toán "
"của bạn</b>"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_reconcile_model_form_inherit_account_accountant
msgid ""
"<i title=\"Run manually\" role=\"img\" aria-label=\"Run manually\" class=\"fa fa-refresh\"/>\n"
"                            Run manually"
msgstr ""
"<i title=\"Chạy thủ công\" role=\"img\" aria-label=\"Chạy thủ công\" class=\"fa fa-refresh\"/>\n"
"                            Chạy thủ công"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "<i>Lock transactions up to specific dates, inclusive</i>"
msgstr "<i>Khoá sổ giao dịch cho đến ngày cụ thể, kể cả ngày đó</i>"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_form_inherit
msgid "<span class=\"o_stat_text\">1 Bank Transaction</span>"
msgstr "<span class=\"o_stat_text\">1 giao dịch ngân hàng</span>"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_form_inherit
msgid "<span class=\"o_stat_text\">Bank Statement</span>"
msgstr "<span class=\"o_stat_text\">Sao kê ngân hàng</span>"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_wizard
msgid ""
"<span class=\"oe_inline o_form_label mx-3\" "
"invisible=\"single_currency_mode\"> in </span>"
msgstr ""
"<span class=\"oe_inline o_form_label mx-3\" "
"invisible=\"single_currency_mode\"> bằng </span>"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid ""
"<span class=\"text-muted o_form_label\" invisible=\"not min_fiscalyear_lock_date_exception_for_me_id                                                  or not min_fiscalyear_lock_date_exception_for_everyone_id\">\n"
"                                      <span style=\"white-space: pre\">; </span>\n"
"                                </span>"
msgstr ""
"<span class=\"text-muted o_form_label\" invisible=\"not min_fiscalyear_lock_date_exception_for_me_id                                                  or not min_fiscalyear_lock_date_exception_for_everyone_id\">\n"
"                                      <span style=\"white-space: pre\">; </span>\n"
"                                </span>"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid ""
"<span class=\"text-muted o_form_label\" invisible=\"not min_purchase_lock_date_exception_for_me_id                                                  or not min_purchase_lock_date_exception_for_everyone_id\">\n"
"                                      <span style=\"white-space: pre\">; </span>\n"
"                                </span>"
msgstr ""
"<span class=\"text-muted o_form_label\" invisible=\"not min_purchase_lock_date_exception_for_me_id                                                  or not min_purchase_lock_date_exception_for_everyone_id\">\n"
"                                      <span style=\"white-space: pre\">; </span>\n"
"                                </span>"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid ""
"<span class=\"text-muted o_form_label\" invisible=\"not min_sale_lock_date_exception_for_me_id                                                  or not min_sale_lock_date_exception_for_everyone_id\">\n"
"                                      <span style=\"white-space: pre\">; </span>\n"
"                                </span>"
msgstr ""
"<span class=\"text-muted o_form_label\" invisible=\"not min_sale_lock_date_exception_for_me_id                                                  or not min_sale_lock_date_exception_for_everyone_id\">\n"
"                                      <span style=\"white-space: pre\">; </span>\n"
"                                </span>"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid ""
"<span class=\"text-muted o_form_label\" invisible=\"not min_tax_lock_date_exception_for_me_id                                                  or not min_tax_lock_date_exception_for_everyone_id\">\n"
"                                      <span style=\"white-space: pre\">; </span>\n"
"                                </span>"
msgstr ""
"<span class=\"text-muted o_form_label\" invisible=\"not min_tax_lock_date_exception_for_me_id                                                  or not min_tax_lock_date_exception_for_everyone_id\">\n"
"                                      <span style=\"white-space: pre\">; </span>\n"
"                                </span>"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid ""
"<span class=\"text-muted\" invisible=\"hard_lock_date != current_hard_lock_date\">\n"
"                                    <i>to ensure inalterability</i>\n"
"                                </span>\n"
"                                <span class=\"text-danger o_form_label\" invisible=\"hard_lock_date == current_hard_lock_date\">\n"
"                                    <i>This change is irreversible</i>\n"
"                                </span>"
msgstr ""
"<span class=\"text-muted\" invisible=\"hard_lock_date != current_hard_lock_date\">\n"
"                                    <i>để đảm bảo tính không thể thay đổi</i>\n"
"                                </span>\n"
"                                <span class=\"text-danger o_form_label\" invisible=\"hard_lock_date == current_hard_lock_date\">\n"
"                                    <i>Không thể đảo ngược thay đổi này</i>\n"
"                                </span>"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid ""
"<span class=\"text-muted\" invisible=\"min_fiscalyear_lock_date_exception_for_me_id or min_fiscalyear_lock_date_exception_for_everyone_id\">\n"
"                                    <i>but allow exceptions</i>\n"
"                                </span>"
msgstr ""
"<span class=\"text-muted\" invisible=\"min_fiscalyear_lock_date_exception_for_me_id or min_fiscalyear_lock_date_exception_for_everyone_id\">\n"
"                                    <i>nhưng cho phép ngoại lệ</i>\n"
"                                </span>"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid ""
"<span class=\"text-muted\" invisible=\"min_tax_lock_date_exception_for_me_id or min_tax_lock_date_exception_for_everyone_id\">\n"
"                                    <i>after a tax closing</i>\n"
"                                </span>"
msgstr ""
"<span class=\"text-muted\" invisible=\"min_tax_lock_date_exception_for_me_id or min_tax_lock_date_exception_for_everyone_id\">\n"
"                                    <i>sau khi khoá sổ thuế</i>\n"
"                                </span>"

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_1
msgid "<span class=\"tip_button_text\">Find an Accountant</span>"
msgstr "<span class=\"tip_button_text\">Tìm kế toán viên</span>"

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_1
msgid "<span class=\"tip_button_text\">Register your Accounting Firm</span>"
msgstr "<span class=\"tip_button_text\">Đăng ký Công ty kế toán của bạn</span>"

#. module: account_accountant
#: model_terms:web_tour.tour,rainbow_man_message:account_accountant.account_accountant_tour
msgid ""
"<span><strong><b>Good job!</b> You went through all steps of this tour.</strong>\n"
"            <br>See how to manage your customer invoices in the <b>Customers/Invoices</b> menu\n"
"        </span>"
msgstr ""
"<span><strong><b>Tuyệt vời!</b> Bạn đã hoàn thành tour này.</strong>\n"
"            <br>Xem cách quản lý hóa đơn mua hàng của bạn trong menu <b>Khách hàng/Hoá đơn</b>\n"
"        </span>"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "<span>Exception</span>"
msgstr "<span>Ngoại lệ</span>"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
#: model:ir.model,name:account_accountant.model_account_account
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__account_id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__account_id
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "Account"
msgstr "Tài khoản"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_chart_template
msgid "Account Chart Template"
msgstr "Mẫu hệ thống tài khoản"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.action_account_group_tree
#: model:ir.ui.menu,name:account_accountant.menu_account_group
msgid "Account Groups"
msgstr "Nhóm tài khoản"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.account_tag_action
#: model:ir.ui.menu,name:account_accountant.account_tag_menu
msgid "Account Tags"
msgstr "Thẻ tài khoản"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__transfer_from_account_id
msgid "Account Transfer From"
msgstr "Luân chuyển tài khoản từ"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_auto_reconcile_wizard
msgid "Account automatic reconciliation wizard"
msgstr "Công cụ đối chiếu tài khoản tự động"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_reconcile_wizard
msgid "Account reconciliation wizard"
msgstr "Công cụ đối chiếu tài khoản"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__deferred_expense_account_id
msgid "Account used for deferred expenses"
msgstr "Tài khoản sử dụng cho chi phí chờ kết chuyển"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__deferred_revenue_account_id
msgid "Account used for deferred revenues"
msgstr "Tài khoản sử dụng cho doanh thu chưa thực hiện"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__account_ids
msgid "Accounts"
msgstr "Tài khoản"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__message_needaction
msgid "Action Needed"
msgstr "Tác vụ cần thiết"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_quick_create.xml:0
msgid "Add & Close"
msgstr "Thêm & Đóng"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_quick_create.xml:0
msgid "Add & New"
msgstr "Thêm & Mới"

#. module: account_accountant
#: model_terms:ir.actions.act_window,help:account_accountant.account_tag_action
msgid "Add a new tag"
msgstr "Thêm một thẻ mới"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
msgid ""
"After the data extraction, check and validate the bill. If no vendor has "
"been found, add one before validating."
msgstr ""
"Sau khi trích xuất dữ liệu, kiểm tra và xác nhận hóa đơn. Nếu không tìm thấy"
" nhà cung cấp nào, hãy thêm một nhà cung cấp trước khi xác thực."

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/finish_buttons.xml:0
msgid "All Transactions"
msgstr "Tất cả giao dịch"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__reco_model_autocomplete_ids
msgid "All reconciliation models"
msgstr "Tất cả mẫu đối chiếu"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__allow_partials
msgid "Allow partials"
msgstr "Cho phép một phần"

#. module: account_accountant
#: model:res.groups,name:account_accountant.group_fiscal_year
msgid "Allow to define fiscal years of more or less than a year"
msgstr "Cho phép xác định năm tài chính trên hoặc dưới một năm"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__amount_currency
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_wizard
msgid "Amount"
msgstr "Số tiền"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "Amount (Company Currency)"
msgstr "Số tiền (đơn vị tiền tệ của công ty)"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "Amount (Foreign Currency)"
msgstr "Số tiền (ngoại tệ)"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__amount_currency
msgid "Amount Currency"
msgstr "Tiền tệ của số tiền"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_list_bank_rec_widget
msgid "Amount Due"
msgstr "Số tiền phải trả"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_list_bank_rec_widget
msgid "Amount Due (in currency)"
msgstr "Số tiền phải trả (theo loại tiền tệ)"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__amount_transaction_currency
msgid "Amount in Currency"
msgstr "Số tiền theo loại tiền tệ"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__amount
msgid "Amount in company currency"
msgstr "Số tiền theo loại tiền tệ của công ty"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
msgid ""
"An entry will transfer %(amount)s from %(from_account)s to %(to_account)s."
msgstr ""
"Một bút toán sẽ luân chuyển %(amount)s từ %(from_account)s sang "
"%(to_account)s."

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
msgid "Analytic"
msgstr "Phân tích"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__analytic_distribution
msgid "Analytic Distribution"
msgstr "Phân bổ phân tích"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__analytic_precision
msgid "Analytic Precision"
msgstr "Độ chính xác phân tích"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__use_anglo_saxon
msgid "Anglo-Saxon Accounting"
msgstr "Kế toán Anglo-Saxon"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_change_lock_date__current_hard_lock_date
#: model:ir.model.fields,help:account_accountant.field_account_change_lock_date__hard_lock_date
msgid ""
"Any entry up to and including that date will be postponed to a later time, "
"in accordance with its journal sequence. This lock date is irreversible and "
"does not allow any exception."
msgstr ""
"Bất kỳ bút toán nào cho đến và bao gồm ngày đó sẽ được hoãn lại đến một thời"
" gian sau, theo trình tự nhật ký của bút toán đó. Ngày khóa sổ này không thể"
" đảo ngược và không cho phép bất kỳ ngoại lệ nào."

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_change_lock_date__fiscalyear_lock_date
msgid ""
"Any entry up to and including that date will be postponed to a later time, "
"in accordance with its journal's sequence."
msgstr ""
"Bất kỳ bút toán nào cho đến và bao gồm ngày đó sẽ được hoãn lại đến một thời"
" gian sau, theo trình tự sổ nhật ký của bút toán đó."

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_change_lock_date__tax_lock_date
msgid ""
"Any entry with taxes up to and including that date will be postponed to a "
"later time, in accordance with its journal's sequence. The tax lock date is "
"automatically set when the tax closing entry is posted."
msgstr ""
"Bất kỳ bút toán nào có thuế cho đến và bao gồm ngày đó sẽ được hoãn lại đến "
"một thời gian sau, theo trình tự sổ nhật ký của bút toán đó. Ngày khóa sổ "
"thuế được tự động thiết lập khi bút toán quyết toán thuế được ghi sổ."

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_change_lock_date__purchase_lock_date
msgid ""
"Any purchase entry prior to and including this date will be postponed to a "
"later date, in accordance with its journal's sequence."
msgstr ""
"Bất kỳ bút toán mua hàng nào trước và tính cả ngày này sẽ được hoãn lại đến "
"một thời gian sau, theo trình tự sổ nhật ký của bút toán đó."

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_change_lock_date__sale_lock_date
msgid ""
"Any sales entry prior to and including this date will be postponed to a "
"later date, in accordance with its journal's sequence."
msgstr ""
"Bất kỳ bút toán bán hàng nào trước và tính cả ngày này sẽ được hoãn lại đến "
"một thời gian sau, theo trình tự sổ nhật ký của bút toán đó."

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__message_attachment_count
msgid "Attachment Count"
msgstr "Số tệp đính kèm"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__sign_invoice
msgid "Authorized Signatory on invoice"
msgstr "Người ký tên được uỷ quyền trên hoá đơn"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.report_invoice_document
msgid "Authorized signatory"
msgstr "Người ký tên được uỷ quyền"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/move_line_list_reconcile/move_line_list_reconcile.xml:0
msgid "Auto-reconcile"
msgstr "Tự động đối chiếu"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_auto_reconcile_wizard.py:0
msgid "Automatically Reconciled Entries"
msgstr "Bút toán được đối chiếu tự động"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__available_reco_model_ids
msgid "Available Reco Model"
msgstr "Mẫu đối chiếu khả dụng"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/finish_buttons.xml:0
msgid "Back to"
msgstr "Quay lại"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/global_info.xml:0
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__balance
msgid "Balance"
msgstr "Số dư"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_digest_digest__kpi_account_bank_cash
msgid "Bank & Cash Moves"
msgstr "Bút toán ngân hàng & tiền mặt"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__bank_account
msgid "Bank Account"
msgstr "Tài khoản ngân hàng"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_bank_statement.py:0
#: model:ir.actions.act_window,name:account_accountant.action_bank_statement_line_transactions
#: model:ir.actions.act_window,name:account_accountant.action_bank_statement_line_transactions_kanban
msgid "Bank Reconciliation"
msgstr "Đối chiếu ngân hàng"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_bank_statement
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_form_bank_rec_widget
msgid "Bank Statement"
msgstr "Sao kê ngân hàng"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_bank_statement.py:0
msgid "Bank Statement %s.pdf"
msgstr "Sao kê ngân hàng %s.pdf"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr "Dòng sao kê ngân hàng"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_bank_statement.py:0
msgid "Bank Statement.pdf"
msgstr "Sao kê ngân hàng.pdf"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_bank_rec_widget
msgid "Bank reconciliation widget for a single statement line"
msgstr "Tiện ích đối chiếu ngân hàng cho một dòng sao kê"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Based on"
msgstr "Dựa trên"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_model_widget_wizard
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_form_bank_rec_widget
msgid "Cancel"
msgstr "Hủy"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_change_lock_date
msgid "Change Lock Date"
msgstr "Đổi ngày khoá sổ"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_reconcile_wizard__to_check
msgid ""
"Check if you are not certain of all the information of the counterpart."
msgstr ""
"Chọn nếu bạn không chắc chắn về tất cả thông tin của tài khoản đối ứng."

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__st_line_checked
msgid "Checked"
msgstr "Đã kiểm tra"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/move_line_list/move_line_list.xml:0
msgid "Choose a line to preview its attachments."
msgstr "Chọn một dòng để xem trước tệp đính kèm."

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__account_auto_reconcile_wizard__search_mode__zero_balance
msgid "Clear Account"
msgstr "Xoá tài khoản"

#. module: account_accountant
#: model_terms:ir.actions.act_window,help:account_accountant.actions_account_fiscal_year
msgid "Click here to create a new fiscal year."
msgstr "Nhấp vào đây để tạo một năm tài chính mới."

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_1
msgid ""
"Click here to find an accountant or if you want to list out your accounting "
"services on Odoo"
msgstr ""
"Nhấp vào đây để tìm kế toán viên hoặc nếu bạn muốn đăng ký các dịch vụ kế "
"toán của mình trên Odoo"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
msgid ""
"Click on a fetched bank transaction to start the reconciliation process."
msgstr "Nhấp vào giao dịch ngân hàng đã lấy để bắt đầu quá trình đối chiếu."

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_res_company
msgid "Companies"
msgstr "Công ty"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__company_id
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__company_id
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__company_id
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__company_id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__company_id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__company_id
msgid "Company"
msgstr "Công ty"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__company_currency_id
msgid "Company currency"
msgstr "Tiền tệ của công ty"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_res_config_settings
msgid "Config Settings"
msgstr "Cài đặt cấu hình"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
msgid "Confirm the transaction."
msgstr "Xác nhận giao dịch."

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/rainbowman_content.xml:0
msgid "Congrats, you're all done!"
msgstr "Chúc mừng, bạn đã hoàn tất!"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
msgid "Connect your bank and get your latest transactions."
msgstr ""
"Kết nối với ngân hàng của bạn và nhận thông tin các giao dịch mới nhất."

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_model_widget_wizard
msgid "Counterpart Values"
msgstr "Giá trị đối ứng"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__country_code
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__country_code
msgid "Country Code"
msgstr "Mã quốc gia"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.action_bank_statement_form_bank_rec_widget
msgid "Create Statement"
msgstr "Tạo sao kê"

#. module: account_accountant
#: model_terms:ir.actions.act_window,help:account_accountant.action_account_group_tree
msgid "Create a new account group"
msgstr "Tạo nhóm tài khoản mới"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
msgid "Create a new transaction."
msgstr "Tạo một giao dịch mới."

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "Create model"
msgstr "Tạo mẫu"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
msgid ""
"Create your first vendor bill.<br/><br/><i>Tip: If you don’t have one on "
"hand, use our sample bill.</i>"
msgstr ""
"Tạo hóa đơn mua hàng đầu tiên của bạn.<br/><br/><i>Mẹo: Nếu bạn chưa có mẫu "
"nào, hãy sử dụng hóa đơn mẫu của Odoo.</i>"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__create_uid
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__create_uid
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__create_uid
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__create_uid
msgid "Created by"
msgstr "Được tạo bởi"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__create_date
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__create_date
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__create_date
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__create_date
msgid "Created on"
msgstr "Được tạo vào"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__credit
msgid "Credit"
msgstr "Có"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement_line__cron_last_check
msgid "Cron Last Check"
msgstr "Lần cuối cùng kiểm tra cron"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
#: model:ir.model,name:account_accountant.model_res_currency
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__currency_id
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Currency"
msgstr "Tiền tệ"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__reco_currency_id
msgid "Currency to use for reconciliation"
msgstr "Tiền tệ dùng cho đối chiếu"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__current_hard_lock_date
msgid "Current Hard Lock"
msgstr "Khoá sổ kế toán hiện tại"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
msgid "Customer/Vendor"
msgstr "Khách hàng/Nhà cung cấp"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__date
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__date
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_search_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Date"
msgstr "Ngày"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_move_line__deferred_end_date
msgid "Date at which the deferred expense/revenue ends"
msgstr "Ngày chi phí chờ kết chuyển/doanh thu chưa thực hiện kết thúc"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_move_line__deferred_start_date
msgid "Date at which the deferred expense/revenue starts"
msgstr "Ngày chi phí chờ kết chuyển/doanh thu chưa thực hiện bắt đầu"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__res_company__deferred_expense_amount_computation_method__day
#: model:ir.model.fields.selection,name:account_accountant.selection__res_company__deferred_revenue_amount_computation_method__day
msgid "Days"
msgstr "Ngày"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__debit
msgid "Debit"
msgstr "Nợ"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__journal_default_account_id
msgid "Default Account"
msgstr "Tài khoản mặc định"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
msgid "Deferral of %s"
msgstr "Hoãn lại của %s"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement_line__deferred_move_ids
#: model:ir.model.fields,field_description:account_accountant.field_account_move__deferred_move_ids
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_form_inherit
msgid "Deferred Entries"
msgstr "Bút toán hoãn lại"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement_line__deferred_entry_type
#: model:ir.model.fields,field_description:account_accountant.field_account_move__deferred_entry_type
msgid "Deferred Entry Type"
msgstr "Loại bút toán hoãn lại"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__account_move__deferred_entry_type__expense
msgid "Deferred Expense"
msgstr "Chi phí chờ kết chuyển"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__deferred_expense_account_id
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__deferred_expense_account_id
msgid "Deferred Expense Account"
msgstr "Tài khoản chi phí chờ kết chuyển"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__deferred_expense_amount_computation_method
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__deferred_expense_amount_computation_method
msgid "Deferred Expense Based on"
msgstr "Chi phí chờ kết chuyển dựa trên"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__deferred_expense_journal_id
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__deferred_expense_journal_id
msgid "Deferred Expense Journal"
msgstr "Sổ nhật ký chi phí chờ kết chuyển"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__account_move__deferred_entry_type__revenue
msgid "Deferred Revenue"
msgstr "Doanh thu chưa thực hiện"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__deferred_revenue_account_id
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__deferred_revenue_account_id
msgid "Deferred Revenue Account"
msgstr "Tài khoản doanh thu chưa thực hiện"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__deferred_revenue_amount_computation_method
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__deferred_revenue_amount_computation_method
msgid "Deferred Revenue Based on"
msgstr "Doanh thu chưa thực hiện dựa trên"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__deferred_revenue_journal_id
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__deferred_revenue_journal_id
msgid "Deferred Revenue Journal"
msgstr "Nhật ký doanh thu chưa thực hiện"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Deferred expense"
msgstr "Chi phí chờ kết chuyển"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Deferred expense entries:"
msgstr "Bút toán chi phí chờ kết chuyển:"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Deferred revenue"
msgstr "Doanh thu chưa thực hiện"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Deferred revenue entries:"
msgstr "Bút toán doanh thu chưa thực hiện:"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Define fiscal years of more or less than one year"
msgstr "Xác định năm tài chính trên hoặc dưới một năm"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Deposits"
msgstr "Tiền gửi"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_digest_digest
msgid "Digest"
msgstr "Tóm tắt"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_auto_reconcile_wizard
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_wizard
msgid "Discard"
msgstr "Huỷ bỏ"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Discount Amount"
msgstr "Số tiền chiết khấu"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Discount Date"
msgstr "Ngày chiết khấu"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "Discuss"
msgstr "Thảo luận"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__display_allow_partials
msgid "Display Allow Partials"
msgstr "Hiển thị Cho phép một phần"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__display_name
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__display_name
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__display_name
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__display_name
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__display_name
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__display_name
msgid "Display Name"
msgstr "Tên hiển thị"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__display_stroked_amount_currency
msgid "Display Stroked Amount Currency"
msgstr "Hiển thị tiền tệ của số tiền đã tìm kiếm"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__display_stroked_balance
msgid "Display Stroked Balance"
msgstr "Hiển thị số dư đã tìm kiếm"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__sign_invoice
msgid "Display signing field on invoices"
msgstr "Hiển thị trường ký tên trên hoá đơn"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__distribution_analytic_account_ids
msgid "Distribution Analytic Account"
msgstr "Tài khoản phân tích phân phối"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/digest.py:0
msgid "Do not have access, skip this data for user's digest email"
msgstr ""
"Không có quyền truy cập, bỏ qua dữ liệu này đối với email tóm tắt của người "
"dùng"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_list_bank_rec_widget
msgid "Document"
msgstr "Tài liệu"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_change_lock_date.py:0
msgid "Draft Entries"
msgstr "Bút toán nháp"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__edit_mode
msgid "Edit Mode"
msgstr "Chế độ chỉnh sửa"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__edit_mode_amount
msgid "Edit Mode Amount"
msgstr "Chế độ chỉnh sửa Số tiền"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__edit_mode_reco_currency_id
msgid "Edit Mode Reco Currency"
msgstr "Chế độ chỉnh sửa Đối chiếu loại tiền tệ"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__edit_mode_amount_currency
msgid "Edit mode amount"
msgstr "Chế độ chỉnh sửa số tiền"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__date_to
#: model:ir.model.fields,field_description:account_accountant.field_account_move_line__deferred_end_date
msgid "End Date"
msgstr "Ngày kết thúc"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_fiscal_year__date_to
msgid "Ending Date, included in the fiscal year."
msgstr "Ngày kết thúc, nằm trong năm tài chính."

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_company__invoicing_switch_threshold
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__invoicing_switch_threshold
msgid ""
"Every payment and invoice before this date will receive the 'From Invoicing'"
" status, hiding all the accounting entries related to it. Use this option "
"after installing Accounting if you were using only Invoicing before, before "
"importing all your actual accounting data in to Odoo."
msgstr ""
"Mọi khoản thanh toán và hóa đơn trước ngày này sẽ nhận được trạng thái 'Từ "
"Hóa đơn', ẩn tất cả các bút toán kế toán liên quan đến nó. Sử dụng tùy chọn "
"này sau khi cài đặt ứng dụng Kế toán nếu trước đây bạn chỉ sử dụng ứng dụng "
"Hóa đơn, trước khi nhập tất cả dữ liệu kế toán thực tế của bạn vào Odoo."

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__exception_duration
msgid "Exception Duration"
msgstr "Thời hạn ngoại lệ"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__exception_needed_fields
msgid "Exception Needed Fields"
msgstr "Exception Needed Fields"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__exception_reason
msgid "Exception Reason"
msgstr "Lý do ngoại lệ"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__exception_applies_to
msgid "Exception applies"
msgstr "Ngoại lệ áp dụng cho"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__exception_needed
msgid "Exception needed"
msgstr "Ngoại lệ cần thiết"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
msgid "Exchange Difference: %s"
msgstr "Chênh lệch tỷ giá: %s"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_fiscal_year
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Fiscal Year"
msgstr "Năm tài chính"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.action_account_fiscal_year_form
msgid "Fiscal Year 2018"
msgstr "Năm tài chính 2018"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.actions_account_fiscal_year
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__group_fiscal_year
#: model:ir.ui.menu,name:account_accountant.menu_account_fiscal_year
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Fiscal Years"
msgstr "Năm tài chính"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__fiscalyear_last_day
msgid "Fiscalyear Last Day"
msgstr "Ngày cuối cùng năm tài chính"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__fiscalyear_last_month
msgid "Fiscalyear Last Month"
msgstr "Tháng cuối cùng năm tài chính"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__flag
msgid "Flag"
msgstr "Cờ"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__message_follower_ids
msgid "Followers"
msgstr "Người theo dõi"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__message_partner_ids
msgid "Followers (Partners)"
msgstr "Người theo dõi (Đối tác)"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "For everyone:"
msgstr "Đối với mọi người:"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "For me:"
msgstr "Đối với tôi:"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__force_partials
msgid "Force Partials"
msgstr "Bắt buộc một phần"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__force_price_included_taxes
msgid "Force Price Included Taxes"
msgstr "Buộc tuỳ chọn Giá đã bao gồm thuế"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__transaction_currency_id
msgid "Foreign Currency"
msgstr "Ngoại tệ"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__form_index
msgid "Form Index"
msgstr "Chỉ số biểu mẫu"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__from_date
msgid "From"
msgstr "Từ"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "From Trade Payable accounts"
msgstr "Từ tài khoản phải trả thương mại"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "From Trade Receivable accounts"
msgstr "Từ tài khoản phải thu thương mại"

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_0
msgid ""
"From any list view, select multiple records and the list becomes editable. "
"If you update a cell, selected records are updated all at once. Use this "
"feature to update multiple journal entries from the General Ledger, or any "
"Journal view."
msgstr ""
"Từ bất kỳ chế độ xem danh sách nào, hãy chọn nhiều bản ghi và bạn sẽ có thể "
"chỉnh sửa danh sách. Nếu bạn cập nhật một ô, tất cả các bản ghi đã chọn sẽ "
"được cập nhật đồng thời. Sử dụng tính năng này để cập nhật nhiều bút tán từ "
"sổ cái hoặc bất kỳ chế độ xem sổ nhật ký nào."

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__res_company__deferred_expense_amount_computation_method__full_months
#: model:ir.model.fields.selection,name:account_accountant.selection__res_company__deferred_revenue_amount_computation_method__full_months
msgid "Full Months"
msgstr "Tháng đầy đủ"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__generate_deferred_expense_entries_method
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__generate_deferred_expense_entries_method
msgid "Generate Deferred Expense Entries"
msgstr "Tạo bút toán chi phí chờ kết chuyển"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__generate_deferred_revenue_entries_method
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__generate_deferred_revenue_entries_method
msgid "Generate Deferred Revenue Entries"
msgstr "Tạo bút toán doanh thu chưa thực hiện"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Generate Entries"
msgstr "Tạo bút toán"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "Group By"
msgstr "Nhóm theo"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__group_tax_id
msgid "Group Tax"
msgstr "Nhóm thuế"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__hard_lock_date
msgid "Hard Lock"
msgstr "Khoá sổ kế toán"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_move_line__has_abnormal_deferred_dates
msgid "Has Abnormal Deferred Dates"
msgstr "Có ngày hoãn lại bất thường"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_move_line__has_deferred_moves
msgid "Has Deferred Moves"
msgstr "Có bút toán hoãn lại"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__has_message
msgid "Has Message"
msgstr "Có tin nhắn"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__id
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__id
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__id
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__id
msgid "ID"
msgstr "ID"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_bank_statement__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Nếu chọn, bạn cần chú ý tới các tin nhắn mới."

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_bank_statement__message_has_error
#: model:ir.model.fields,help:account_accountant.field_account_bank_statement__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Nếu chọn, một số tin nhắn sẽ có lỗi gửi."

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_bank_rec_widget__st_line_checked
msgid ""
"If this checkbox is not ticked, it means that the user was not sure of all "
"the related information at the time of the creation of the move and that the"
" move needs to be checked again."
msgstr ""
"Nếu hộp kiểm này không được đánh dấu, điều đó có nghĩa là người dùng không "
"chắc chắn về tất cả thông tin liên quan tại thời điểm tạo bút toán này và "
"bút toán cần được kiểm tra lại."

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "In Currency"
msgstr "Theo tiền tệ"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_search_bank_rec_widget
msgid "Incoming"
msgstr "Vào"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/res_config_settings.py:0
msgid ""
"Incorrect fiscal year date: day is out of range for month. Month: %(month)s;"
" Day: %(day)s"
msgstr ""
"Năm tài chính không chính xác: ngày này không có trong tháng. Tháng: "
"%(month)s; Ngày: %(day)s"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__index
msgid "Index"
msgstr "Chỉ số"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget__state__invalid
msgid "Invalid"
msgstr "Không hợp lệ"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Invalid statements"
msgstr "Sao kê không hợp lệ"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_bank_rec_widget__state
msgid ""
"Invalid: The bank transaction can't be validate since the suspense account is still involved\n"
"Valid: The bank transaction can be validated.\n"
"Reconciled: The bank transaction has already been processed. Nothing left to do."
msgstr ""
"Không hợp lệ: Không thể xác thực giao dịch ngân hàng vì tài khoản tạm thời vẫn còn liên quan\n"
"Hợp lệ: Giao dịch ngân hàng có thể được xác thực.\n"
"Đã đối chiếu: Giao dịch ngân hàng đã được xử lý. Không còn việc gì để làm."

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_list_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_search_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Invoice Date"
msgstr "Ngày lập hóa đơn"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__invoicing_switch_threshold
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__invoicing_switch_threshold
msgid "Invoicing Switch Threshold"
msgstr "Ngưỡng chuyển đổi hóa đơn"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__message_is_follower
msgid "Is Follower"
msgstr "Là người theo dõi"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__is_multi_currency
msgid "Is Multi Currency"
msgstr "Nhiều loại tiền tệ"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__is_rec_pay_account
msgid "Is Rec Pay Account"
msgstr "Is Rec Pay Account"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__st_line_is_reconciled
msgid "Is Reconciled"
msgstr "Đã đối chiếu"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__is_write_off_required
msgid "Is a write-off move required to reconcile"
msgstr "Bút toán xoá bỏ yêu cầu đối chiếu"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__is_transfer_required
msgid "Is an account transfer required"
msgstr "Luân chuyển tài khoản yêu cầu"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__transfer_warning_message
msgid "Is an account transfer required to reconcile"
msgstr "Luân chuyển tài khoản yêu cầu đối chiếu"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__lock_date_violated_warning_message
msgid "Is the date violating the lock date of moves"
msgstr "Ngày vi phạm ngày khoá sổ của bút toán"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_change_lock_date.py:0
msgid "It is not possible to decrease or remove the Hard Lock Date."
msgstr "Không thể giảm hoặc gỡ bỏ Ngày khoá sổ kế toán."

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_journal
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__journal_id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__st_line_journal_id
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Journal"
msgstr "Sổ nhật ký"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__journal_currency_id
msgid "Journal Currency"
msgstr "Tiền tệ bút toán"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_move
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__move_id
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_search_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Journal Entry"
msgstr "Bút toán"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_move_line
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_search_bank_rec_widget
msgid "Journal Item"
msgstr "Hạng mục bút toán"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Journal Items"
msgstr "Hạng mục bút toán"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.action_move_line_posted_unreconciled
msgid "Journal Items to reconcile"
msgstr "Hạng mục bút toán cần đối chiếu"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "Journal items where matching number isn't set"
msgstr "Hạng mục bút toán không có số đối chiếu"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid ""
"Journal items where the account allows reconciliation no matter the residual"
" amount"
msgstr "Các bút toán mà tài khoản cho phép đối chiếu bất kể số tiền còn lại"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__deferred_expense_journal_id
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__deferred_revenue_journal_id
msgid "Journal used for deferred entries"
msgstr "Sổ nhật ký sử dụng cho bút toán hoãn lại"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_digest_digest__kpi_account_bank_cash_value
msgid "Kpi Account Bank Cash Value"
msgstr "Kpi Account Bank Cash Value"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__label
msgid "Label"
msgstr "Nhãn"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Last Day"
msgstr "Ngày cuối cùng"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
msgid "Last Statement"
msgstr "Báo cáo cuối cùng"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__write_uid
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__write_uid
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__write_uid
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__write_uid
msgid "Last Updated by"
msgstr "Cập nhật lần cuối bởi"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__write_date
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__write_date
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__write_date
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__write_date
msgid "Last Updated on"
msgstr "Cập nhật lần cuối vào"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
msgid "Latest Statement"
msgstr "Báo cáo mới nhất"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Legal signatory"
msgstr "Người ký tên hợp pháp"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
msgid "Let’s go back to the dashboard."
msgstr "Hãy quay lại trang chủ."

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__line_ids
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__line_ids
msgid "Line"
msgstr "Dòng"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_bank_rec_widget_line
msgid "Line of the bank reconciliation widget"
msgstr "Dòng tiện ích đối chiếu ngân hàng"

#. module: account_accountant
#: model:ir.ui.menu,name:account_accountant.menu_action_change_lock_date
msgid "Lock Dates"
msgstr "Ngày khóa sổ"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__fiscalyear_lock_date
msgid "Lock Everything"
msgstr "Khoá tất cả"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__fiscalyear_lock_date_for_everyone
msgid "Lock Everything For Everyone"
msgstr "Khoá tất cả đối với mọi người"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__fiscalyear_lock_date_for_me
msgid "Lock Everything For Me"
msgstr "Khoá tất cả đối với tôi"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.action_view_account_change_lock_date
msgid "Lock Journal Entries"
msgstr "Khoá sổ bút toán"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__purchase_lock_date
msgid "Lock Purchases"
msgstr "Khoá sổ mua hàng"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__purchase_lock_date_for_everyone
msgid "Lock Purchases For Everyone"
msgstr "Khoá sổ mua hàng đối với mọi người"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__purchase_lock_date_for_me
msgid "Lock Purchases For Me"
msgstr "Khoá sổ mua hàng đối với tôi"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__sale_lock_date
msgid "Lock Sales"
msgstr "Khóa sổ bán hàng"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__sale_lock_date_for_everyone
msgid "Lock Sales For Everyone"
msgstr "Khoá sổ bán hàng đối với mọi người"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__sale_lock_date_for_me
msgid "Lock Sales For Me"
msgstr "Khoá sổ bán hàng đối với tôi"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__tax_lock_date
msgid "Lock Tax Return"
msgstr "Khoá tờ khai thuế"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__tax_lock_date_for_everyone
msgid "Lock Tax Return For Everyone"
msgstr "Khoá tờ khai thuế đối với mọi người"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__tax_lock_date_for_me
msgid "Lock Tax Return For Me"
msgstr "Khoá tờ khai thuế đối với tôi"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__message_main_attachment_id
msgid "Main Attachment"
msgstr "Tệp đính kèm chính"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "Manual Operations"
msgstr "Hoạt động thủ công"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__res_company__generate_deferred_expense_entries_method__manual
#: model:ir.model.fields.selection,name:account_accountant.selection__res_company__generate_deferred_revenue_entries_method__manual
msgid "Manually & Grouped"
msgstr "Thủ công & được nhóm"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__manually_modified
msgid "Manually Modified"
msgstr "Sửa đổi thủ công"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/list_view_switcher.js:0
msgid "Match"
msgstr "Khớp"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "Match Existing Entries"
msgstr "Khớp bút toán hiện có"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_kanban_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Matched"
msgstr "Đã khớp"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_payment.py:0
msgid "Matched Transactions"
msgstr "Giao dịch đã khớp"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Matching"
msgstr "Khớp"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__matching_rules_allow_auto_reconcile
msgid "Matching Rules Allow Auto Reconcile"
msgstr "Quy tắc khớp cho phép đối chiếu tự động"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_quick_create_form_bank_rec_widget
msgid "Memo"
msgstr "Nội dung giao dịch"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_ir_ui_menu
msgid "Menu"
msgstr "Menu"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__message_has_error
msgid "Message Delivery error"
msgstr "Lỗi gửi tin nhắn"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__message_ids
msgid "Messages"
msgstr "Tin nhắn"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__deferred_expense_amount_computation_method
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__deferred_revenue_amount_computation_method
msgid "Method used to compute the amount of deferred entries"
msgstr "Phương pháp dùng để tính toán số tiền của bút toán hoãn lại"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__generate_deferred_expense_entries_method
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__generate_deferred_revenue_entries_method
msgid "Method used to generate deferred entries"
msgstr "Phương pháp dùng để tạo bút toán hoãn lại"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__min_fiscalyear_lock_date_exception_for_everyone_id
msgid "Min Fiscalyear Lock Date Exception For Everyone"
msgstr "Ngoại lệ ngày khoá sổ năm tài chính tối thiểu đối với mọi người"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__min_fiscalyear_lock_date_exception_for_me_id
msgid "Min Fiscalyear Lock Date Exception For Me"
msgstr "Ngoại lệ ngày khoá sổ năm tài chính tối thiểu đối với tôi"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__min_purchase_lock_date_exception_for_everyone_id
msgid "Min Purchase Lock Date Exception For Everyone"
msgstr "Ngoại lệ ngày khoá sổ mua hàng tối thiểu đối với mọi người"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__min_purchase_lock_date_exception_for_me_id
msgid "Min Purchase Lock Date Exception For Me"
msgstr "Ngoại lệ ngày khoá sổ mua hàng tối thiểu đối với tôi"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__min_sale_lock_date_exception_for_everyone_id
msgid "Min Sale Lock Date Exception For Everyone"
msgstr "Ngoại lệ ngày khoá sổ bán hàng tối thiểu đối với mọi người"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__min_sale_lock_date_exception_for_me_id
msgid "Min Sale Lock Date Exception For Me"
msgstr "Ngoại lệ ngày khoá sổ bán hàng tối thiểu đối với tôi"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__min_tax_lock_date_exception_for_everyone_id
msgid "Min Tax Lock Date Exception For Everyone"
msgstr "Ngoại lệ ngày khoá tờ khai thuế tối thiểu đối với mọi người"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__min_tax_lock_date_exception_for_me_id
msgid "Min Tax Lock Date Exception For Me"
msgstr "Ngoại lệ ngày khoá tờ khai thuế tối thiểu đối với tôi"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
msgid "Misc"
msgstr "Thông tin khác"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__res_company__deferred_expense_amount_computation_method__month
#: model:ir.model.fields.selection,name:account_accountant.selection__res_company__deferred_revenue_amount_computation_method__month
msgid "Months"
msgstr "Tháng"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "More"
msgstr "Thêm"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_move_line__move_attachment_ids
msgid "Move Attachment"
msgstr "Tệp đình kèm bút toán"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__move_line_ids
msgid "Move lines to reconcile"
msgstr "Dòng bút toán cần đối chiếu"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__name
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__name
msgid "Name"
msgstr "Tên"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__narration
msgid "Narration"
msgstr "Narration"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "New"
msgstr "Mới"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.action_bank_statement_line_form_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_form_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_quick_create_form_bank_rec_widget
msgid "New Transaction"
msgstr "Giao dịch mới"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/move_line_list/move_line_list.xml:0
msgid "No attachments linked."
msgstr "Không có tệp đình kèm."

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "No statement"
msgstr "Không có sao kê"

#. module: account_accountant
#: model_terms:ir.actions.act_window,help:account_accountant.action_bank_statement_line_transactions
#: model_terms:ir.actions.act_window,help:account_accountant.action_bank_statement_line_transactions_kanban
msgid "No transactions matching your filters were found."
msgstr "Không tìm thấy giao dịch nào khớp với bộ lọc của bạn."

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Not Matched"
msgstr "Không khớp"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "Not locked"
msgstr "Không được khoá"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_tree_bank_rec_widget
msgid "Notes"
msgstr "Ghi chú"

#. module: account_accountant
#: model_terms:ir.actions.act_window,help:account_accountant.action_bank_statement_line_transactions
#: model_terms:ir.actions.act_window,help:account_accountant.action_bank_statement_line_transactions_kanban
msgid "Nothing to do here!"
msgstr "Không có gì để làm!"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
msgid "Now, we'll create your first invoice (accountant)"
msgstr "Bây giờ, chúng tôi sẽ tạo hóa đơn đầu tiên của bạn (kế toán viên)"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__message_needaction_counter
msgid "Number of Actions"
msgstr "Số lượng tác vụ"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__message_has_error_counter
msgid "Number of errors"
msgstr "Số lượng lỗi"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_bank_statement__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Số tin nhắn cần xử lý"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_bank_statement__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Số tin nhắn bị gửi lỗi"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__res_company__generate_deferred_expense_entries_method__on_validation
#: model:ir.model.fields.selection,name:account_accountant.selection__res_company__generate_deferred_revenue_entries_method__on_validation
msgid "On bill validation"
msgstr "Khi xác thực hoá đơn thanh toán"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_change_lock_date.py:0
msgid "Only Billing Administrators are allowed to change lock dates!"
msgstr "Chỉ Quản trị viên hoá đơn mới được phép thay đổi ngày khóa sổ!"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_wizard
msgid ""
"Only partial reconciliation is possible. Proceed in multiple steps if you "
"want to full reconcile."
msgstr ""
"Chỉ có thể đối chiếu một phần. Hãy thực hiện nhiều bước nếu bạn muốn đối "
"chiếu toàn bộ."

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/move_line_list/move_line_list.xml:0
msgid "Open attachment in pop out"
msgstr "Mở tệp đính kèm trong cửa sổ bật lên"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
msgid "Open balance of %(amount)s"
msgstr "Số dư mở là %(amount)s"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
msgid "Original Deferred Entries"
msgstr "Bút toán hoãn lại gốc"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement_line__deferred_original_move_ids
#: model:ir.model.fields,field_description:account_accountant.field_account_move__deferred_original_move_ids
msgid "Original Invoices"
msgstr "Hoá đơn gốc"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Originator Tax"
msgstr "Thuế khởi tạo"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_search_bank_rec_widget
msgid "Outgoing"
msgstr "Ra"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__to_partner_id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__partner_id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__partner_id
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_quick_create_form_bank_rec_widget
msgid "Partner"
msgstr "Đối tác"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__partner_currency_id
msgid "Partner Currency"
msgstr "Tiền tệ của đối tác"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__partner_name
msgid "Partner Name"
msgstr "Tên đối tác"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__partner_payable_account_id
msgid "Partner Payable Account"
msgstr "Tài khoản phải trả đối tác"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__partner_payable_amount
msgid "Partner Payable Amount"
msgstr "Số tiền phải trả đối tác"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__partner_receivable_account_id
msgid "Partner Receivable Account"
msgstr "Tài khoản phải thu đối tác"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__partner_receivable_amount
msgid "Partner Receivable Amount"
msgstr "Số tiền phải thu đối tác"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__partner_ids
msgid "Partners"
msgstr "Đối tác"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "Payable"
msgstr "Phải trả"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "Payable:"
msgstr "Phải trả:"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_payment_form_inherit_account_accountant
msgid "Payment Matching"
msgstr "Khớp thanh toán"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement_line__payment_state_before_switch
#: model:ir.model.fields,field_description:account_accountant.field_account_move__payment_state_before_switch
msgid "Payment State Before Switch"
msgstr "Trạng thái thanh toán trước khi chuyển đổi"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_payment
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Payments"
msgstr "Thanh toán"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
msgid "Payments Matching"
msgstr "Khớp thanh toán"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__account_auto_reconcile_wizard__search_mode__one_to_one
msgid "Perfect Match"
msgstr "Khớp hoàn toàn"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
msgid "Please set the deferred accounts in the accounting settings."
msgstr "Vui lòng thiết lập các tài khoản hoãn lại trong cài đặt kế toán."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
msgid "Please set the deferred journal in the accounting settings."
msgstr "Vui lòng thiết lập sổ nhật ký hoãn lại trong cài đặt kế toán."

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__predict_bill_product
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__predict_bill_product
msgid "Predict Bill Product"
msgstr "Dự đoán sản phẩm hoá đơn"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Predict vendor bill product"
msgstr "Dự đoán sản phẩm hoá đơn mua hàng"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_reconcile_model
msgid ""
"Preset to create journal entries during a invoices and payments matching"
msgstr ""
"Thiết lập trước để tạo các bút toán trong quá trình khớp hóa đơn và thanh "
"toán"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__rating_ids
msgid "Ratings"
msgstr "Đánh giá"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "Reason..."
msgstr "Lý do..."

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "Receivable"
msgstr "Khoản phải thu"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "Receivable:"
msgstr "Phải thu:"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__search_mode
#: model:ir.ui.menu,name:account_accountant.menu_account_reconcile
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_auto_reconcile_wizard
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_wizard
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_payment_tree
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_tree
msgid "Reconcile"
msgstr "Đối chiếu"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_wizard
msgid "Reconcile & open"
msgstr "Đối chiếu & mở"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__reco_account_id
msgid "Reconcile Account"
msgstr "Tài khoản đối chiếu"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__reconcile_model_id
msgid "Reconcile Model"
msgstr "Mẫu đối chiếu"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.action_open_auto_reconcile_wizard
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_auto_reconcile_wizard
msgid "Reconcile automatically"
msgstr "Đối chiếu tự động"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_auto_reconcile_wizard__search_mode
msgid ""
"Reconcile journal items with opposite balance or clear accounts with a zero "
"balance"
msgstr ""
"Đối chiếu các hạng mục bút toán có số dư đối lập hoặc xóa những tài khoản có"
" số dư bằng không"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget__state__reconciled
msgid "Reconciled"
msgstr "Đã đối chiếu"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__reco_model_id
msgid "Reconciliation model"
msgstr "Mẫu đối chiếu"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Record cost of goods sold in your journal entries"
msgstr "Ghi nhận giá vốn hàng bán trong các bút toán của bạn"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__ref
msgid "Ref"
msgstr "Tham chiếu"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "Reference"
msgstr "Tham chiếu"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_form_inherit
msgid "Related Purchase(s)"
msgstr "(Các) đơn mua hàng liên quan"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_form_inherit
msgid "Related Sale(s)"
msgstr "(Các) đơn bán hàng liên quan"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "Reset"
msgstr "Đặt lại"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Residual"
msgstr "Giá trị còn lại"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Residual in Currency"
msgstr "Giá trị còn lại theo loại tiền tệ"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__return_todo_command
msgid "Return Todo Command"
msgstr "Hoàn lệnh việc phải làm"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "Review"
msgstr "Xem trước"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "Revoke"
msgstr "Thu hồi"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_reconcile_model_line
msgid "Rules for the reconciliation model"
msgstr "Quy tắc dành cho mẫu đối chiếu"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Lỗi gửi SMS"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "Save"
msgstr "Lưu"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_form_bank_rec_widget
msgid "Save & Close"
msgstr "Lưu & đóng"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_form_bank_rec_widget
msgid "Save & New"
msgstr "Lưu & tạo mới"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "Search Journal Items to Reconcile"
msgstr "Tìm hạng mục bút toán cần đối chiếu"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__signing_user
msgid ""
"Select a user here to override every signature on invoice by this user's "
"signature"
msgstr ""
"Chọn một người dùng ở đây để ghi đè mọi chữ ký trên hóa đơn bằng chữ ký của "
"người dùng này"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__selected_aml_ids
msgid "Selected Aml"
msgstr "Aml đã chọn"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__selected_reco_model_id
msgid "Selected Reco Model"
msgstr "Mẫu đối chiếu đã chọn"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
msgid "Set an amount."
msgstr "Đặt số tiền."

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "Set as Checked"
msgstr "Đặt là đã kiểm tra"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
msgid "Set the payment reference."
msgstr "Đặt tham chiếu thanh toán."

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__show_draft_entries_warning
msgid "Show Draft Entries Warning"
msgstr "Hiển thị cảnh báo bút toán nháp"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement_line__show_signature_area
#: model:ir.model.fields,field_description:account_accountant.field_account_move__show_signature_area
msgid "Show Signature Area"
msgstr "Hiện phần chữ ký"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__module_sign
msgid "Sign"
msgstr "Ký tên"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement_line__signature
#: model:ir.model.fields,field_description:account_accountant.field_account_move__signature
msgid "Signature"
msgstr "Chữ ký"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__signing_user
msgid "Signature used to sign all the invoice"
msgstr "Chữ ký được dùng để ký tất cả hoá đơn"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement_line__signing_user
#: model:ir.model.fields,field_description:account_accountant.field_account_move__signing_user
msgid "Signer"
msgstr "Người ký"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__signing_user
msgid "Signing User"
msgstr "Người ký tên"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__single_currency_mode
msgid "Single Currency Mode"
msgstr "Chế độ một tiền tệ"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_aml_id
msgid "Source Aml"
msgstr "Aml gốc"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_aml_move_id
msgid "Source Aml Move"
msgstr "Bút toán Aml gốc"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_aml_move_name
msgid "Source Aml Move Name"
msgstr "Tên bút toán Aml gốc"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_amount_currency
msgid "Source Amount Currency"
msgstr "Tiền tệ số tiền gốc"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_balance
msgid "Source Balance"
msgstr "Số dư gốc"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_credit
msgid "Source Credit"
msgstr "Có gốc"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_debit
msgid "Source Debit"
msgstr "Nợ gốc"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__st_line_id
msgid "St Line"
msgstr "Dòng sao kê"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__st_line_transaction_details
msgid "St Line Transaction Details"
msgstr "Chi tiết giao dịch dòng sao kê"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__date_from
#: model:ir.model.fields,field_description:account_accountant.field_account_move_line__deferred_start_date
msgid "Start Date"
msgstr "Ngày bắt đầu"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_fiscal_year__date_from
msgid "Start Date, included in the fiscal year."
msgstr "Ngày bắt đầu, nằm trong năm tài chính."

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__state
msgid "State"
msgstr "Trạng thái"

#. module: account_accountant
#: model:ir.actions.server,name:account_accountant.action_bank_statement_attachment
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_kanban_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Statement"
msgstr "Sao kê"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Statement Line"
msgstr "Dòng sao kê"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__suggestion_amount_currency
msgid "Suggestion Amount Currency"
msgstr "Tiền tệ số tiền gợi ý"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__suggestion_balance
msgid "Suggestion Balance"
msgstr "Số dư gợi ý"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__suggestion_html
msgid "Suggestion Html"
msgstr "Html gợi ý"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_list_bank_rec_widget
msgid "Suggestions"
msgstr "Gợi ý"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_tax
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__tax_id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__tax_ids
msgid "Tax"
msgstr "Thuế"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__tax_base_amount_currency
msgid "Tax Base Amount Currency"
msgstr "Tiền tệ số tiền cơ sở thuế"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Tax Grids"
msgstr "Lưới thuế"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__tax_repartition_line_id
msgid "Tax Repartition Line"
msgstr "Dòng phân bổ thuế"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__tax_tag_ids
msgid "Tax Tag"
msgstr "Thẻ thuế"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
msgid "Taxes"
msgstr "Thuế"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/rainbowman_content.xml:0
msgid "That's on average"
msgstr "Đó là theo trung bình"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_bank_rec_widget__country_code
#: model:ir.model.fields,help:account_accountant.field_bank_rec_widget_line__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"Mã quốc gia ISO với hai ký tự.\n"
"Bạn có thể sử dụng trường này để tìm kiếm nhanh."

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_bank_rec_widget_line__amount_transaction_currency
msgid ""
"The amount expressed in an optional other currency if it is a multi-currency"
" entry."
msgstr ""
"Số tiền được biểu thị bằng đơn vị tiền tệ tùy chọn khác nếu nó là một bút "
"toán đa tiền tệ."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
msgid ""
"The amount of the write-off of a single credit line should be strictly "
"negative."
msgstr "Số tiền xóa bỏ của một dòng ghi có phải hoàn toàn âm."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
msgid ""
"The amount of the write-off of a single debit line should be strictly "
"positive."
msgstr "Số tiền xóa bỏ của một dòng ghi nợ phải hoàn toàn dương."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
msgid "The amount of the write-off of a single line cannot be 0."
msgstr "Số tiền xóa bỏ của một dòng không thể bằng 0."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
msgid ""
"The date you set violates the lock date of one of your entry. It will be "
"overriden by the following date : %(replacement_date)s"
msgstr ""
"Ngày bạn thiết lập vi phạm ngày khóa sổ của một trong các bút toán. Ngày này"
" sẽ được ghi đè bằng ngày: %(replacement_date)s"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_bank_statement_line__deferred_move_ids
#: model:ir.model.fields,help:account_accountant.field_account_move__deferred_move_ids
msgid "The deferred entries created by this invoice"
msgstr "Bút toán hoãn lại được tạo bởi hóa đơn này"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_fiscal_year.py:0
msgid "The ending date must not be prior to the starting date."
msgstr "Ngày kết thúc không thể sớm hơn ngày bắt đầu."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget_line.py:0
msgid ""
"The invoice %(display_name_html)s with an open amount of %(open_amount)s "
"will be entirely paid by the transaction."
msgstr ""
"Hoá đơn %(display_name_html)s với số tiền mở là %(open_amount)s sẽ được "
"thanh toán toàn bộ bởi giao dịch."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget_line.py:0
msgid ""
"The invoice %(display_name_html)s with an open amount of %(open_amount)s "
"will be reduced by %(amount)s."
msgstr ""
"Hóa đơn %(display_name_html)s với số tiền mở là %(open_amount)s sẽ được giảm"
" %(amount)s."

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid ""
"The invoices up to this date will not be taken into account as accounting "
"entries"
msgstr "Các hóa đơn tính đến ngày này sẽ không được tính là bút toán kế toán."

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_bank_rec_widget_line__transaction_currency_id
msgid "The optional other currency if it is a multi-currency entry."
msgstr "Đơn vị tiền tệ tùy chọn khác nếu là bút toán đa tiền tệ."

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_bank_statement_line__deferred_original_move_ids
#: model:ir.model.fields,help:account_accountant.field_account_move__deferred_original_move_ids
msgid "The original invoices that created the deferred entries"
msgstr "Hóa đơn gốc đã tạo ra bút toán hoãn lại"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid ""
"The system will try to predict the product on vendor bill lines based on the"
" label of the line"
msgstr ""
"Hệ thống sẽ cố gắng dự đoán sản phẩm trên dòng hóa đơn mua hàng dựa trên "
"nhãn của dòng"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid ""
"There are still draft entries in the period you want to lock.\n"
"                                You should either post or delete them."
msgstr ""
"Vẫn còn bút toán nháp trong kỳ mà bạn muốn khoá sổ.\n"
"Bạn nên vào sổ hoặc xóa chúng."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_bank_statement.py:0
msgid ""
"This bank transaction has been automatically validated using the "
"reconciliation model '%s'."
msgstr ""
"Giao dịch ngân hàng này đã được xác thực tự động bằng mô hình đối chiếu "
"'%s'."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
msgid ""
"This bank transaction is locked up tighter than a squirrel in a nut factory!"
" You can't hit the reset button on it. So, do you want to \"unreconcile\" it"
" instead?"
msgstr ""
"Giao dịch ngân hàng này bị khóa chặt như nêm! Bạn không thể nhấn nút đặt lại"
" trên giao dịch đó, vậy bạn có muốn \"hủy đối chiếu\" nó không?"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
msgid "This can only be used on journal items"
msgstr "Chỉ có thể sử dụng cho hạng mục bút toán"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_reconcile_model_line.py:0
msgid ""
"This reconciliation model can't be used in the manual reconciliation widget "
"because its configuration is not adapted"
msgstr ""
"Không thể sử dụng mẫu đối chiếu này trong tiện ích đối chiếu thủ công vì cấu"
" hình của nó không thích hợp"

#. module: account_accountant
#: model:digest.tip,name:account_accountant.digest_tip_account_accountant_0
msgid "Tip: Bulk update journal items"
msgstr "Mẹo: Cập nhật hàng loạt hạng mục bút toán "

#. module: account_accountant
#: model:digest.tip,name:account_accountant.digest_tip_account_accountant_1
msgid "Tip: Find an Accountant or register your Accounting Firm"
msgstr "Mẹo: Tìm Kế toán viên hoặc đăng ký Công ty kế toán của bạn"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__to_date
msgid "To"
msgstr "Đến"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__to_check
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "To Check"
msgstr "Cần kiểm tra"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_kanban_bank_rec_widget
msgid "To check"
msgstr "Cần kiểm tra"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "To enhance authenticity, add a signature to your invoices"
msgstr "Để tăng tính xác thực, hãy thêm chữ ký vào hóa đơn của bạn"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__todo_command
msgid "Todo Command"
msgstr "Lệnh việc phải làm"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Total Balance"
msgstr "Tổng số dư"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Total Credit"
msgstr "Tổng có"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Total Debit"
msgstr "Tổng nợ"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Total Residual"
msgstr "Tổng còn lại"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Total Residual in Currency"
msgstr "Tổng còn lại theo loại tiền tệ"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Transaction"
msgstr "Giao dịch"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__transaction_currency_id
msgid "Transaction Currency"
msgstr "Tiền tệ giao dịch"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "Transaction Details"
msgstr "Chi tiết giao dịch"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_tree
msgid "Transactions"
msgstr "Giao dịch"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
msgid "Transfer from %s"
msgstr "Luân chuyển từ %s"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
msgid "Transfer to %s"
msgstr "Luân chuyển tới %s"

#. module: account_accountant
#: model:ir.actions.server,name:account_accountant.auto_reconcile_bank_statement_line_ir_actions_server
msgid "Try to reconcile automatically your statement lines"
msgstr "Cố gắng tự động đối chiếu các dòng sao kê của bạn"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "Unreconciled"
msgstr "Chưa đối chiếu"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/res_company.py:0
msgid "Unreconciled statements lines"
msgstr "Dòng sao kê chưa đối chiếu"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget__state__valid
msgid "Valid"
msgstr "Hợp lệ"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_model_widget_wizard
msgid "Validate"
msgstr "Xác nhận"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/list_view_switcher.js:0
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_list_bank_rec_widget
msgid "View"
msgstr "Chế độ xem"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
msgid "View Reconciled Entries"
msgstr "Xem bút toán đã đối chiếu"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "View models"
msgstr "Xem mẫu"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__website_message_ids
msgid "Website Messages"
msgstr "Thông báo trên trang web"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_bank_statement__website_message_ids
msgid "Website communication history"
msgstr "Lịch sử trao đổi qua trang web"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "With residual"
msgstr "Có số tiền còn lại"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__wizard_id
msgid "Wizard"
msgstr "Công cụ"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__company_currency_id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__company_currency_id
msgid "Wizard Company Currency"
msgstr "Công cụ tiền tệ của công ty"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
msgid "Write-Off"
msgstr "Xoá bỏ"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
msgid "Write-Off Entry"
msgstr "Bút toán xoá bỏ"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_fiscal_year.py:0
msgid ""
"You can not have an overlap between two fiscal years, please correct the "
"start and/or end dates of your fiscal years."
msgstr ""
"Không thể có sự trùng lặp giữa hai năm tài chính, vui lòng sửa ngày bắt đầu "
"và/hoặc ngày kết thúc năm tài chính của bạn."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
msgid "You can only reconcile entries with up to two different accounts: %s"
msgstr ""
"Bạn chỉ có thể đối chiếu các bút toán có tối đa hai tài khoản khác nhau: %s"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
msgid "You can't hit the reset button on a secured bank transaction."
msgstr "Bạn không thể nhấn nút đặt lại trên giao dịch ngân hàng được bảo mật."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
msgid ""
"You cannot change the account for a deferred line in %(move_name)s if it has"
" already been deferred."
msgstr ""
"Bạn không thể thay đổi tài khoản cho dòng hoãn lại trong %(move_name)s nếu "
"nó đã được hoãn lại."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
msgid "You cannot create a deferred entry with a start date but no end date."
msgstr ""
"Bạn không thể tạo một bút toán hoãn lại có ngày bắt đầu mà không có ngày kết"
" thúc."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
msgid ""
"You cannot create a deferred entry with a start date later than the end "
"date."
msgstr ""
"Bạn không thể tạo một bút toán hoãn lại có ngày bắt đầu sau ngày kết thúc."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
msgid ""
"You cannot generate deferred entries for a miscellaneous journal entry."
msgstr "Bạn không thể tạo bút toán hoãn lại cho bút toán về hoạt động khác."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_fiscal_year.py:0
msgid "You cannot have a fiscal year on a child company."
msgstr "Công ty con không thể có năm tài chính."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
msgid ""
"You cannot reset to draft an invoice that is grouped in deferral entry. You "
"can create a credit note instead."
msgstr ""
"Bạn không thể đặt lại một hóa đơn được nhóm trong bút toán hoãn lại thành "
"bản nháp. Thay vào đó, bạn có thể tạo một giấy báo có."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_change_lock_date.py:0
msgid "You cannot set a Lock Date in the future."
msgstr "Bạn không thể đặt Ngày khóa sổ trong tương lai."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget_line.py:0
msgid ""
"You might want to %(btn_start)sfully reconcile%(btn_end)s the document."
msgstr "Bạn nên %(btn_start)s đối chiếu toàn bộ %(btn_end)s chứng từ."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget_line.py:0
msgid ""
"You might want to make a %(btn_start)spartial reconciliation%(btn_end)s "
"instead."
msgstr ""
"Thay vào đó, bạn nên tạo %(btn_start)s đối chiếu một phần %(btn_end)s."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget_line.py:0
msgid "You might want to record a %(btn_start)spartial payment%(btn_end)s."
msgstr ""
"Bạn nên ghi nhận một %(btn_start)s khoản thanh toán một phần %(btn_end)s."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget_line.py:0
msgid ""
"You might want to set the invoice as %(btn_start)sfully paid%(btn_end)s."
msgstr ""
"Bạn nên đặt hoá đơn thành %(btn_start)s đã thanh toán hết %(btn_end)s."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_change_lock_date.py:0
msgid "You need to select a duration for the exception."
msgstr "Bạn cần chọn thời hạn cho ngoại lệ."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_change_lock_date.py:0
msgid "You need to select who the exception applies to."
msgstr "Bạn cần chọn người mà ngoại lệ này sẽ áp dụng cho."

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/rainbowman_content.xml:0
msgid "You reconciled"
msgstr "Bạn đã đối chiếu"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__aml
msgid "aml"
msgstr "aml"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__auto_balance
msgid "auto_balance"
msgstr "auto_balance"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_model_widget_wizard
msgid "e.g. Bank Fees"
msgstr "VD: Phí ngân hàng"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__early_payment
msgid "early_payment"
msgstr "early_payment"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__exchange_diff
msgid "exchange_diff"
msgstr "exchange_diff"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__account_change_lock_date__exception_duration__1h
msgid "for 1 hour"
msgstr "trong 1 giờ"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__account_change_lock_date__exception_duration__15min
msgid "for 15 minutes"
msgstr "trong 15 phút"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__account_change_lock_date__exception_duration__24h
msgid "for 24 hours"
msgstr "trong 24 giờ"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__account_change_lock_date__exception_duration__5min
msgid "for 5 minutes"
msgstr "trong 5 phút"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__account_change_lock_date__exception_applies_to__everyone
msgid "for everyone"
msgstr "đối với mọi người"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__account_change_lock_date__exception_applies_to__me
msgid "for me"
msgstr "đối với tôi"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__account_change_lock_date__exception_duration__forever
msgid "forever"
msgstr "mãi mãi"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "in"
msgstr "trong"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__liquidity
msgid "liquidity"
msgstr "thanh khoản"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__manual
msgid "manual"
msgstr "thủ công"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__new_aml
msgid "new_aml"
msgstr "new_aml"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/rainbowman_content.xml:0
msgid "seconds per transaction."
msgstr "giây mỗi giao dịch."

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__tax_line
msgid "tax_line"
msgstr "tax_line"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
msgid "to check"
msgstr "để kiểm tra"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
msgid "to reconcile"
msgstr "cần đối chiếu"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/rainbowman_content.xml:0
msgid "transaction in"
msgstr "giao dịch bằng"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/rainbowman_content.xml:0
msgid "transactions in"
msgstr "giao dịch bằng"
