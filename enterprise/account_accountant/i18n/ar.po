# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_accountant
# 
# Translators:
# <PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# Wil Odoo, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:52+0000\n"
"PO-Revision-Date: 2024-09-25 09:43+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget_line.py:0
msgid ""
"%(display_name_html)s with an open amount of %(open_amount)s will be fully "
"reconciled by the transaction."
msgstr ""
"%(display_name_html)s مع مبلغ مفتوح قدره %(open_amount)s ستتم تسويته تماماً "
"بواسطة المعاملة. "

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget_line.py:0
msgid ""
"%(display_name_html)s with an open amount of %(open_amount)s will be reduced"
" by %(amount)s."
msgstr ""
"%(display_name_html)s مع مبلغ مفتوح قدره %(open_amount)s سيتم تقليله بـ "
"%(amount)s. "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_form
msgid "-> Reconcile"
msgstr "-> تسوية "

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_0
msgid "<b class=\"tip_title\">Tip: Bulk update journal items</b>"
msgstr "<b class=\"tip_title\">نصيحة: قم بتحديث بنود اليومية بالجملة</b>"

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_1
msgid ""
"<b class=\"tip_title\">Tip: Find an Accountant or register your Accounting "
"Firm</b>"
msgstr ""
"<b class=\"tip_title\">نصيحة: اعثر على محاسب ليقوم بتسجيل شركة المحاسبة "
"الخاصة بك</b>"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_reconcile_model_form_inherit_account_accountant
msgid ""
"<i title=\"Run manually\" role=\"img\" aria-label=\"Run manually\" class=\"fa fa-refresh\"/>\n"
"                            Run manually"
msgstr ""
"<i title=\"التشغيل يدوياً \" role=\"img\" aria-label=\"Run manually\" class=\"fa fa-refresh\"/>\n"
"                            التشغيل يدوياً "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "<i>Lock transactions up to specific dates, inclusive</i>"
msgstr "<i>قفل المعاملات حتى تواريخ محددة، شاملة التواريخ نفسها</i> "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_form_inherit
msgid "<span class=\"o_stat_text\">1 Bank Transaction</span>"
msgstr "<span class=\"o_stat_text\">معاملة بنكية واحدة 1</span>"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_form_inherit
msgid "<span class=\"o_stat_text\">Bank Statement</span>"
msgstr "<span class=\"o_stat_text\">كشف حساب بنكي</span> "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_wizard
msgid ""
"<span class=\"oe_inline o_form_label mx-3\" "
"invisible=\"single_currency_mode\"> in </span>"
msgstr ""
"<span class=\"oe_inline o_form_label mx-3\" "
"invisible=\"single_currency_mode\"> في </span>"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid ""
"<span class=\"text-muted o_form_label\" invisible=\"not min_fiscalyear_lock_date_exception_for_me_id                                                  or not min_fiscalyear_lock_date_exception_for_everyone_id\">\n"
"                                      <span style=\"white-space: pre\">; </span>\n"
"                                </span>"
msgstr ""
"<span class=\"text-muted o_form_label\" invisible=\"not min_fiscalyear_lock_date_exception_for_me_id                                                  or not min_fiscalyear_lock_date_exception_for_everyone_id\">\n"
"                                      <span style=\"white-space: pre\">; </span>\n"
"                                </span>"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid ""
"<span class=\"text-muted o_form_label\" invisible=\"not min_purchase_lock_date_exception_for_me_id                                                  or not min_purchase_lock_date_exception_for_everyone_id\">\n"
"                                      <span style=\"white-space: pre\">; </span>\n"
"                                </span>"
msgstr ""
"<span class=\"text-muted o_form_label\" invisible=\"not min_purchase_lock_date_exception_for_me_id                                                  or not min_purchase_lock_date_exception_for_everyone_id\">\n"
"                                      <span style=\"white-space: pre\">; </span>\n"
"                                </span>"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid ""
"<span class=\"text-muted o_form_label\" invisible=\"not min_sale_lock_date_exception_for_me_id                                                  or not min_sale_lock_date_exception_for_everyone_id\">\n"
"                                      <span style=\"white-space: pre\">; </span>\n"
"                                </span>"
msgstr ""
"<span class=\"text-muted o_form_label\" invisible=\"not min_sale_lock_date_exception_for_me_id                                                  or not min_sale_lock_date_exception_for_everyone_id\">\n"
"                                      <span style=\"white-space: pre\">; </span>\n"
"                                </span>"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid ""
"<span class=\"text-muted o_form_label\" invisible=\"not min_tax_lock_date_exception_for_me_id                                                  or not min_tax_lock_date_exception_for_everyone_id\">\n"
"                                      <span style=\"white-space: pre\">; </span>\n"
"                                </span>"
msgstr ""
"<span class=\"text-muted o_form_label\" invisible=\"not min_tax_lock_date_exception_for_me_id                                                  or not min_tax_lock_date_exception_for_everyone_id\">\n"
"                                      <span style=\"white-space: pre\">; </span>\n"
"                                </span>"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid ""
"<span class=\"text-muted\" invisible=\"hard_lock_date != current_hard_lock_date\">\n"
"                                    <i>to ensure inalterability</i>\n"
"                                </span>\n"
"                                <span class=\"text-danger o_form_label\" invisible=\"hard_lock_date == current_hard_lock_date\">\n"
"                                    <i>This change is irreversible</i>\n"
"                                </span>"
msgstr ""
"<span class=\"text-muted\" invisible=\"hard_lock_date != current_hard_lock_date\">\n"
"                                    <i>حتى نضمن عدم قابلية التعديل</i>\n"
"                                </span>\n"
"                                <span class=\"text-danger o_form_label\" invisible=\"hard_lock_date == current_hard_lock_date\">\n"
"                                    <i>لا يمكن التراجع عن هذا التغيير</i>\n"
"                                </span>"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid ""
"<span class=\"text-muted\" invisible=\"min_fiscalyear_lock_date_exception_for_me_id or min_fiscalyear_lock_date_exception_for_everyone_id\">\n"
"                                    <i>but allow exceptions</i>\n"
"                                </span>"
msgstr ""
"<span class=\"text-muted\" invisible=\"min_fiscalyear_lock_date_exception_for_me_id or min_fiscalyear_lock_date_exception_for_everyone_id\">\n"
"                                    <i>ولكن يُسمَح بالاستثناءات</i>\n"
"                                </span> "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid ""
"<span class=\"text-muted\" invisible=\"min_tax_lock_date_exception_for_me_id or min_tax_lock_date_exception_for_everyone_id\">\n"
"                                    <i>after a tax closing</i>\n"
"                                </span>"
msgstr ""
"<span class=\"text-muted\" invisible=\"min_tax_lock_date_exception_for_me_id or min_tax_lock_date_exception_for_everyone_id\">\n"
"                                    <i>بعد الإقفال الضريبي</i>\n"
"                                </span> "

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_1
msgid "<span class=\"tip_button_text\">Find an Accountant</span>"
msgstr "<span class=\"tip_button_text\">اعثر على محاسب</span>"

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_1
msgid "<span class=\"tip_button_text\">Register your Accounting Firm</span>"
msgstr "<span class=\"tip_button_text\">قم بتسجيل شركة المحاسبة الخاصة بك</span>"

#. module: account_accountant
#: model_terms:web_tour.tour,rainbow_man_message:account_accountant.account_accountant_tour
msgid ""
"<span><strong><b>Good job!</b> You went through all steps of this tour.</strong>\n"
"            <br>See how to manage your customer invoices in the <b>Customers/Invoices</b> menu\n"
"        </span>"
msgstr ""
"<span><strong><b>عمل رائع!</b> لقد اجتزت كافة خطوات هذه الجولة.</strong>\n"
"            <br>تعرّف على كيفية إدارة فواتير العملاء من قائمة <b>العملاء/فواتير العملاء</b>\n"
"        </span>"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "<span>Exception</span>"
msgstr "<span>استثناء</span> "

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
#: model:ir.model,name:account_accountant.model_account_account
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__account_id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__account_id
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "Account"
msgstr "الحساب "

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_chart_template
msgid "Account Chart Template"
msgstr "نموذج مخطط الحساب "

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.action_account_group_tree
#: model:ir.ui.menu,name:account_accountant.menu_account_group
msgid "Account Groups"
msgstr "مجموعات الحساب"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.account_tag_action
#: model:ir.ui.menu,name:account_accountant.account_tag_menu
msgid "Account Tags"
msgstr "علامات تصنيف الحساب "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__transfer_from_account_id
msgid "Account Transfer From"
msgstr "استمارة تحويل الحساب "

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_auto_reconcile_wizard
msgid "Account automatic reconciliation wizard"
msgstr "معالج تسوية الحساب الآلية "

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_reconcile_wizard
msgid "Account reconciliation wizard"
msgstr "معالج تسوية الحساب "

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__deferred_expense_account_id
msgid "Account used for deferred expenses"
msgstr "الحساب المستخدم للنفقات المؤجلة "

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__deferred_revenue_account_id
msgid "Account used for deferred revenues"
msgstr "الحساب المستخدم للإيرادات المؤجلة "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__account_ids
msgid "Accounts"
msgstr "الحسابات"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__message_needaction
msgid "Action Needed"
msgstr "إجراء مطلوب"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_quick_create.xml:0
msgid "Add & Close"
msgstr "إضافة وإغلاق "

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_quick_create.xml:0
msgid "Add & New"
msgstr "إضافة وجديد "

#. module: account_accountant
#: model_terms:ir.actions.act_window,help:account_accountant.account_tag_action
msgid "Add a new tag"
msgstr "إضافة علامة تصنيف جديدة "

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
msgid ""
"After the data extraction, check and validate the bill. If no vendor has "
"been found, add one before validating."
msgstr ""
"بعد استخلاص البيانات، تحقق من الفاتورة وقم بتصديقها. في حال عدم إيجادك "
"لمورّد، قم بإضافة واحد قبل التصديق. "

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/finish_buttons.xml:0
msgid "All Transactions"
msgstr "كافة المعاملات "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__reco_model_autocomplete_ids
msgid "All reconciliation models"
msgstr "كافة نماذج التسوية "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__allow_partials
msgid "Allow partials"
msgstr "السماج بالأجزاء "

#. module: account_accountant
#: model:res.groups,name:account_accountant.group_fiscal_year
msgid "Allow to define fiscal years of more or less than a year"
msgstr "السماح بإنشاء سنوات مالية أطول أو أقصر من عام"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__amount_currency
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_wizard
msgid "Amount"
msgstr "مبلغ"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "Amount (Company Currency)"
msgstr "المبلغ (عملة الشركة) "

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "Amount (Foreign Currency)"
msgstr "المبلغ (عملة أجنبية) "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__amount_currency
msgid "Amount Currency"
msgstr "عملة المبلغ"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_list_bank_rec_widget
msgid "Amount Due"
msgstr "المبلغ المستحق"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_list_bank_rec_widget
msgid "Amount Due (in currency)"
msgstr "المبلغ المستحق (بالعملة) "

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__amount_transaction_currency
msgid "Amount in Currency"
msgstr "المبلغ بالعملة"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__amount
msgid "Amount in company currency"
msgstr "المبلغ بعملة الشركة "

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
msgid ""
"An entry will transfer %(amount)s from %(from_account)s to %(to_account)s."
msgstr ""
"سيقوم القيد بتحويل %(amount)s من %(from_account)s إلى %(to_account)s. "

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
msgid "Analytic"
msgstr "تحليلي"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__analytic_distribution
msgid "Analytic Distribution"
msgstr "التوزيع التحليلي"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__analytic_precision
msgid "Analytic Precision"
msgstr "الدقة التحليلية "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__use_anglo_saxon
msgid "Anglo-Saxon Accounting"
msgstr "المحاسبة الأنجلو-ساكسونية"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_change_lock_date__current_hard_lock_date
#: model:ir.model.fields,help:account_accountant.field_account_change_lock_date__hard_lock_date
msgid ""
"Any entry up to and including that date will be postponed to a later time, "
"in accordance with its journal sequence. This lock date is irreversible and "
"does not allow any exception."
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_change_lock_date__fiscalyear_lock_date
msgid ""
"Any entry up to and including that date will be postponed to a later time, "
"in accordance with its journal's sequence."
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_change_lock_date__tax_lock_date
msgid ""
"Any entry with taxes up to and including that date will be postponed to a "
"later time, in accordance with its journal's sequence. The tax lock date is "
"automatically set when the tax closing entry is posted."
msgstr ""
"سيتم تأجيل أي قيد يتضمن ضرائب حتى ذلك التاريخ إلى وقت لاحق، وفقاً لتسلسل "
"دفتر اليومية الخاص به. يتم تعيين تاريخ قفل الضرائب تلقائياً عند ترحيل قيد "
"الإقفال الضريبي. "

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_change_lock_date__purchase_lock_date
msgid ""
"Any purchase entry prior to and including this date will be postponed to a "
"later date, in accordance with its journal's sequence."
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_change_lock_date__sale_lock_date
msgid ""
"Any sales entry prior to and including this date will be postponed to a "
"later date, in accordance with its journal's sequence."
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__message_attachment_count
msgid "Attachment Count"
msgstr "عدد المرفقات"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__sign_invoice
msgid "Authorized Signatory on invoice"
msgstr "الموقِّع المصرّح له في الفاتورة "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.report_invoice_document
msgid "Authorized signatory"
msgstr "الموقِّع المصرّح له "

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/move_line_list_reconcile/move_line_list_reconcile.xml:0
msgid "Auto-reconcile"
msgstr "التسوية التلقائية "

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_auto_reconcile_wizard.py:0
msgid "Automatically Reconciled Entries"
msgstr "القيود المسواة آلياً "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__available_reco_model_ids
msgid "Available Reco Model"
msgstr "نموذج التسوية المتاح "

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/finish_buttons.xml:0
msgid "Back to"
msgstr "العودة إلى "

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/global_info.xml:0
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__balance
msgid "Balance"
msgstr "الرصيد"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_digest_digest__kpi_account_bank_cash
msgid "Bank & Cash Moves"
msgstr "تحركات النقد والبنوك "

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__bank_account
msgid "Bank Account"
msgstr "الحساب البنكي"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_bank_statement.py:0
#: model:ir.actions.act_window,name:account_accountant.action_bank_statement_line_transactions
#: model:ir.actions.act_window,name:account_accountant.action_bank_statement_line_transactions_kanban
msgid "Bank Reconciliation"
msgstr "التسوية البنكية"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_bank_statement
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_form_bank_rec_widget
msgid "Bank Statement"
msgstr "كشف الحساب البنكي "

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_bank_statement.py:0
msgid "Bank Statement %s.pdf"
msgstr "كشف الحساب البنكي %s.pdf "

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr "بند كشف الحساب البنكي"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_bank_statement.py:0
msgid "Bank Statement.pdf"
msgstr "كشف الحساب البنكي.pdf"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_bank_rec_widget
msgid "Bank reconciliation widget for a single statement line"
msgstr "أداة التسوية البنكية لبند كشف حساب واحد "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Based on"
msgstr "بناءً على"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_model_widget_wizard
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_form_bank_rec_widget
msgid "Cancel"
msgstr "إلغاء"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_change_lock_date
msgid "Change Lock Date"
msgstr "تغيير تاريخ الإقفال"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_reconcile_wizard__to_check
msgid ""
"Check if you are not certain of all the information of the counterpart."
msgstr "تحقق من تأكدك من كافة المعلومات المقابلة. "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__st_line_checked
msgid "Checked"
msgstr "تم تحديده "

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/move_line_list/move_line_list.xml:0
msgid "Choose a line to preview its attachments."
msgstr "قم بتحديد بند لمعاينة مرفقاته. "

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__account_auto_reconcile_wizard__search_mode__zero_balance
msgid "Clear Account"
msgstr "تصفية الحساب "

#. module: account_accountant
#: model_terms:ir.actions.act_window,help:account_accountant.actions_account_fiscal_year
msgid "Click here to create a new fiscal year."
msgstr "انقر هنا لإنشاء سنة مالية جديدة."

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_1
msgid ""
"Click here to find an accountant or if you want to list out your accounting "
"services on Odoo"
msgstr ""
"انقر هنا لإيجاد محاسب أو إذا كنت ترغب في إدراج خدماتك المحاسبية على أودو "

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
msgid ""
"Click on a fetched bank transaction to start the reconciliation process."
msgstr "انقر على المعاملة المصرفية التي تم جلبها لبدء عملية التسوية. "

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_res_company
msgid "Companies"
msgstr "الشركات"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__company_id
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__company_id
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__company_id
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__company_id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__company_id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__company_id
msgid "Company"
msgstr "الشركة "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__company_currency_id
msgid "Company currency"
msgstr "عملة الشركة "

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_res_config_settings
msgid "Config Settings"
msgstr "تهيئة الإعدادات "

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
msgid "Confirm the transaction."
msgstr "قم بتأكيد المعاملة. "

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/rainbowman_content.xml:0
msgid "Congrats, you're all done!"
msgstr "تهانينا، لقد انتهيت!"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
msgid "Connect your bank and get your latest transactions."
msgstr "قم بربط مصرفك لتتمكن من رؤية أحدث معاملاتك. "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_model_widget_wizard
msgid "Counterpart Values"
msgstr "قيم مقابلة"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__country_code
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__country_code
msgid "Country Code"
msgstr "رمز الدولة "

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.action_bank_statement_form_bank_rec_widget
msgid "Create Statement"
msgstr "إنشاء كشف الحساب"

#. module: account_accountant
#: model_terms:ir.actions.act_window,help:account_accountant.action_account_group_tree
msgid "Create a new account group"
msgstr "إنشاء مجموعة حساب جديدة "

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
msgid "Create a new transaction."
msgstr "إنشاء معاملة جديدة. "

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "Create model"
msgstr "إنشاء نموذج "

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
msgid ""
"Create your first vendor bill.<br/><br/><i>Tip: If you don’t have one on "
"hand, use our sample bill.</i>"
msgstr ""
"أنشئ فاتورة المورّد الأولى الخاصة بك. <br/><br/><i>نصيحة: إذا لم تكن لديك "
"فاتورة في متناول اليد، فبإمكانك الاستعانة بنموذج الفاتورة لدينا.</i> "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__create_uid
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__create_uid
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__create_uid
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__create_date
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__create_date
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__create_date
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__credit
msgid "Credit"
msgstr "الدائن"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement_line__cron_last_check
msgid "Cron Last Check"
msgstr "آخر فحص Cron "

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
#: model:ir.model,name:account_accountant.model_res_currency
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__currency_id
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Currency"
msgstr "العملة"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__reco_currency_id
msgid "Currency to use for reconciliation"
msgstr "العملة لاستخدامها في التسوية "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__current_hard_lock_date
msgid "Current Hard Lock"
msgstr "تاريخ القفل الثابت الحالي "

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
msgid "Customer/Vendor"
msgstr "العميل/المورد"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__date
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__date
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_search_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Date"
msgstr "التاريخ"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_move_line__deferred_end_date
msgid "Date at which the deferred expense/revenue ends"
msgstr "التاريخ الذي تنتهي فيه النفقات/الإيرادات المؤجلة "

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_move_line__deferred_start_date
msgid "Date at which the deferred expense/revenue starts"
msgstr "التاريخ الذي تبدأ فيه النفقات/الإيرادات المؤجلة "

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__res_company__deferred_expense_amount_computation_method__day
#: model:ir.model.fields.selection,name:account_accountant.selection__res_company__deferred_revenue_amount_computation_method__day
msgid "Days"
msgstr "أيام "

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__debit
msgid "Debit"
msgstr "المدين"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__journal_default_account_id
msgid "Default Account"
msgstr "الحساب الافتراضي "

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
msgid "Deferral of %s"
msgstr "تأجيل %s "

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement_line__deferred_move_ids
#: model:ir.model.fields,field_description:account_accountant.field_account_move__deferred_move_ids
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_form_inherit
msgid "Deferred Entries"
msgstr "القيم المؤجلة "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement_line__deferred_entry_type
#: model:ir.model.fields,field_description:account_accountant.field_account_move__deferred_entry_type
msgid "Deferred Entry Type"
msgstr "نوع القيد المؤجل "

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__account_move__deferred_entry_type__expense
msgid "Deferred Expense"
msgstr "النفقات المؤجلة "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__deferred_expense_account_id
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__deferred_expense_account_id
msgid "Deferred Expense Account"
msgstr "حساب النفقات المؤجلة "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__deferred_expense_amount_computation_method
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__deferred_expense_amount_computation_method
msgid "Deferred Expense Based on"
msgstr "النفقات المؤجلة بناءً على "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__deferred_expense_journal_id
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__deferred_expense_journal_id
msgid "Deferred Expense Journal"
msgstr "حساب الإيرادات المؤجلة "

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__account_move__deferred_entry_type__revenue
msgid "Deferred Revenue"
msgstr "الإيرادات المؤجلة "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__deferred_revenue_account_id
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__deferred_revenue_account_id
msgid "Deferred Revenue Account"
msgstr "حساب الإيرادات المؤجلة "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__deferred_revenue_amount_computation_method
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__deferred_revenue_amount_computation_method
msgid "Deferred Revenue Based on"
msgstr "الإيرادات المؤجلة بناءً على "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__deferred_revenue_journal_id
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__deferred_revenue_journal_id
msgid "Deferred Revenue Journal"
msgstr "دفتر يومية الإيرادات المؤجلة "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Deferred expense"
msgstr "النفقة المؤجلة "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Deferred expense entries:"
msgstr "قيود النفقات المؤجلة: "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Deferred revenue"
msgstr "الإيرادات المؤجلة "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Deferred revenue entries:"
msgstr "قيود الإيرادات المؤجلة: "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Define fiscal years of more or less than one year"
msgstr "تحديد السنوات المالية التي تزيد أو تقل عن السنة."

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Deposits"
msgstr "الدفعات المقدّمة"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_digest_digest
msgid "Digest"
msgstr "الموجز "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_auto_reconcile_wizard
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_wizard
msgid "Discard"
msgstr "إهمال "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Discount Amount"
msgstr "مبلغ الخصم "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Discount Date"
msgstr "تاريخ الخصم"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "Discuss"
msgstr "المناقشة "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__display_allow_partials
msgid "Display Allow Partials"
msgstr "عرض خيار السماح بعمليات التسوية الجزئية "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__display_name
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__display_name
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__display_name
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__display_name
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__display_name
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__display_stroked_amount_currency
msgid "Display Stroked Amount Currency"
msgstr "عرض عملة المبلغ المشطوب "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__display_stroked_balance
msgid "Display Stroked Balance"
msgstr "عرض الرصيد المشطوب "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__sign_invoice
msgid "Display signing field on invoices"
msgstr "عرض حقل التوقيع على الفاتورة "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__distribution_analytic_account_ids
msgid "Distribution Analytic Account"
msgstr "حساب التوزيع التحليلي "

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/digest.py:0
msgid "Do not have access, skip this data for user's digest email"
msgstr "لا تملك صلاحيات الوصول. تخط هذه البيانات لبريد الملخص للمستخدم. "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_list_bank_rec_widget
msgid "Document"
msgstr "المستند "

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_change_lock_date.py:0
msgid "Draft Entries"
msgstr "القيود في حالة المسودة "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__edit_mode
msgid "Edit Mode"
msgstr "وضع التحرير "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__edit_mode_amount
msgid "Edit Mode Amount"
msgstr "وضع التحرير للمبلغ "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__edit_mode_reco_currency_id
msgid "Edit Mode Reco Currency"
msgstr "وضع التحرير لعملة التسوية "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__edit_mode_amount_currency
msgid "Edit mode amount"
msgstr "وضع التحرير للمبلغ "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__date_to
#: model:ir.model.fields,field_description:account_accountant.field_account_move_line__deferred_end_date
msgid "End Date"
msgstr "تاريخ الانتهاء"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_fiscal_year__date_to
msgid "Ending Date, included in the fiscal year."
msgstr "تاريخ الانتهاء، ضمن السنة المالية. "

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_company__invoicing_switch_threshold
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__invoicing_switch_threshold
msgid ""
"Every payment and invoice before this date will receive the 'From Invoicing'"
" status, hiding all the accounting entries related to it. Use this option "
"after installing Accounting if you were using only Invoicing before, before "
"importing all your actual accounting data in to Odoo."
msgstr ""
"سوف يكون لكل فاتورة وعملية دفع قبل هذا التاريخ حالة ’من تطبيق الفوترة‘ والتي"
" ستخفي كافة القيود المحاسبية المتعلقة بها. استخدم هذا الخيار بعد تثبيت تطبيق"
" المحاسبة إذا كنت تستخدم تطبيق الفوترة وحده من قبل، قبل إدخالك لكافة بياناتك"
" المحاسبية الفعلية في أودو. "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__exception_duration
msgid "Exception Duration"
msgstr "مدة الاستثناء "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__exception_needed_fields
msgid "Exception Needed Fields"
msgstr "الحقول التي تحتاج إلى استثناء "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__exception_reason
msgid "Exception Reason"
msgstr "سبب الاستثناء "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__exception_applies_to
msgid "Exception applies"
msgstr "يمكن تطبيق الاستثناء "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__exception_needed
msgid "Exception needed"
msgstr "بحاجة إلى استثناء "

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
msgid "Exchange Difference: %s"
msgstr "فرق سعر الصرف: %s"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_fiscal_year
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Fiscal Year"
msgstr "سنة مالية"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.action_account_fiscal_year_form
msgid "Fiscal Year 2018"
msgstr "السنة المالية 2018"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.actions_account_fiscal_year
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__group_fiscal_year
#: model:ir.ui.menu,name:account_accountant.menu_account_fiscal_year
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Fiscal Years"
msgstr "السنوات المالية"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__fiscalyear_last_day
msgid "Fiscalyear Last Day"
msgstr "آخر أيام السنة المالية"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__fiscalyear_last_month
msgid "Fiscalyear Last Month"
msgstr "آخر شهور السنة المالية"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__flag
msgid "Flag"
msgstr "إبلاغ"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__message_follower_ids
msgid "Followers"
msgstr "المتابعين"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__message_partner_ids
msgid "Followers (Partners)"
msgstr "المتابعين (الشركاء) "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "For everyone:"
msgstr "للجميع: "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "For me:"
msgstr "لي: "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__force_partials
msgid "Force Partials"
msgstr "فرض عمليات التسوية الجزئية "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__force_price_included_taxes
msgid "Force Price Included Taxes"
msgstr "فرض الأسعار شاملة الضريبة "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__transaction_currency_id
msgid "Foreign Currency"
msgstr "عملة أجنبية "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__form_index
msgid "Form Index"
msgstr "فهرس النموذج"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__from_date
msgid "From"
msgstr "من"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "From Trade Payable accounts"
msgstr "من الحسابات الدائنة التجارية "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "From Trade Receivable accounts"
msgstr "من الحسابات المدينة التجارية "

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_0
msgid ""
"From any list view, select multiple records and the list becomes editable. "
"If you update a cell, selected records are updated all at once. Use this "
"feature to update multiple journal entries from the General Ledger, or any "
"Journal view."
msgstr ""
"قم باختيار سجلات متعددة من أي نافذة عرض القائمة، وستصبح القائمة قابلة "
"للتحرير. إذا قمت بتحديث إحدى الخلايا، يتم تحديث كافة السجلات المختارة دفعة "
"واحدة. استخدم هذه الخاصية لتحديث عدة بنود في اليومية من دفتر الأستاذ العام "
"أو أي نافذة عرض لليومية. "

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__res_company__deferred_expense_amount_computation_method__full_months
#: model:ir.model.fields.selection,name:account_accountant.selection__res_company__deferred_revenue_amount_computation_method__full_months
msgid "Full Months"
msgstr "أشهر كاملة "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__generate_deferred_expense_entries_method
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__generate_deferred_expense_entries_method
msgid "Generate Deferred Expense Entries"
msgstr "إنشاء قيود النفقات المؤجلة "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__generate_deferred_revenue_entries_method
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__generate_deferred_revenue_entries_method
msgid "Generate Deferred Revenue Entries"
msgstr "إنشاء قيود الإيرادات المؤجلة "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Generate Entries"
msgstr "إنشاء القيود"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "Group By"
msgstr "تجميع حسب"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__group_tax_id
msgid "Group Tax"
msgstr "مجموعة الضريبة "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__hard_lock_date
msgid "Hard Lock"
msgstr "تاريخ القفل الثابت "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_move_line__has_abnormal_deferred_dates
msgid "Has Abnormal Deferred Dates"
msgstr "يحتوي على تواريخ مؤجلة غير معتادة "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_move_line__has_deferred_moves
msgid "Has Deferred Moves"
msgstr "يحتوي على حركات مؤجلة "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__has_message
msgid "Has Message"
msgstr "يحتوي على رسالة "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__id
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__id
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__id
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__id
msgid "ID"
msgstr "المُعرف"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_bank_statement__message_needaction
msgid "If checked, new messages require your attention."
msgstr "إذا كان محددًا، فهناك رسائل جديدة عليك رؤيتها. "

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_bank_statement__message_has_error
#: model:ir.model.fields,help:account_accountant.field_account_bank_statement__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "إذا كان محددًا، فقد حدث خطأ في تسليم بعض الرسائل."

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_bank_rec_widget__st_line_checked
msgid ""
"If this checkbox is not ticked, it means that the user was not sure of all "
"the related information at the time of the creation of the move and that the"
" move needs to be checked again."
msgstr ""
"إذا لم يكن هذا المربع محدداً، هذا يعني أن المستخدم لم يكن متأكداً من كافة "
"المعلومات ذات الصلة في الوقت الذي أُنشئت فيه الحركة، وأنه يجب التحقق من "
"الحركة مجدداً. "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "In Currency"
msgstr "بالعملة "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_search_bank_rec_widget
msgid "Incoming"
msgstr "واردة "

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/res_config_settings.py:0
msgid ""
"Incorrect fiscal year date: day is out of range for month. Month: %(month)s;"
" Day: %(day)s"
msgstr ""
"تاريخ السنة المالية غير صحيح: اليوم المدخل غير موجود في هذا الشهر. الشهر: "
"%(month)s;اليوم:%(day)s "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__index
msgid "Index"
msgstr "الفهرس "

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget__state__invalid
msgid "Invalid"
msgstr "غير صالح "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Invalid statements"
msgstr "كشوفات الحساب غير صالحة "

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_bank_rec_widget__state
msgid ""
"Invalid: The bank transaction can't be validate since the suspense account is still involved\n"
"Valid: The bank transaction can be validated.\n"
"Reconciled: The bank transaction has already been processed. Nothing left to do."
msgstr ""
"غير صالح: لا يمكن تصديق المعاملة البنكية بما أن الحساب المعلق لا يزال موجوداً\n"
"صالح: يمكن تصديق المعاملة البنكية.\n"
"تمت التسوية: لقد تمت تسوية المعاملة البنكية بالفعل. لم يتبقَّ شيء للقيام به. "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_list_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_search_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Invoice Date"
msgstr "تاريخ الفاتورة"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__invoicing_switch_threshold
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__invoicing_switch_threshold
msgid "Invoicing Switch Threshold"
msgstr "الحد الأدنى لتبديل الفوترة "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__message_is_follower
msgid "Is Follower"
msgstr "متابع"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__is_multi_currency
msgid "Is Multi Currency"
msgstr "متعدد العملات "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__is_rec_pay_account
msgid "Is Rec Pay Account"
msgstr "حساب دفع التسوية "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__st_line_is_reconciled
msgid "Is Reconciled"
msgstr "تمت تسويته "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__is_write_off_required
msgid "Is a write-off move required to reconcile"
msgstr "حركة الشطب مطلوبة للمساواة "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__is_transfer_required
msgid "Is an account transfer required"
msgstr "تحويل الحساب مطلوب "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__transfer_warning_message
msgid "Is an account transfer required to reconcile"
msgstr "تحويل الحساب مطلوب للتسوية "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__lock_date_violated_warning_message
msgid "Is the date violating the lock date of moves"
msgstr "التاريخ يتعدى على تاريخ إقفال الحركات "

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_change_lock_date.py:0
msgid "It is not possible to decrease or remove the Hard Lock Date."
msgstr "لا يمكن تقديم تاريخ القفل الثابت أو إزالته. "

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_journal
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__journal_id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__st_line_journal_id
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Journal"
msgstr "دفتر اليومية"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__journal_currency_id
msgid "Journal Currency"
msgstr "عملة اليومية "

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_move
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__move_id
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_search_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Journal Entry"
msgstr "قيد اليومية"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_move_line
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_search_bank_rec_widget
msgid "Journal Item"
msgstr "عنصر اليومية"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Journal Items"
msgstr "عناصر اليومية"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.action_move_line_posted_unreconciled
msgid "Journal Items to reconcile"
msgstr "عناصر دفتر اليومية المُراد تسويتها "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "Journal items where matching number isn't set"
msgstr "عناصر دفتر اليومية التي لم يتم تعيين رقم مطابق لها "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid ""
"Journal items where the account allows reconciliation no matter the residual"
" amount"
msgstr ""
"بنود دفتر اليومية حيث يسمح الحساب بالتسوية بغض النظر عن المبلغ المتبقي "

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__deferred_expense_journal_id
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__deferred_revenue_journal_id
msgid "Journal used for deferred entries"
msgstr "دفتر اليومية المستخدم للقيود المؤجلة "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_digest_digest__kpi_account_bank_cash_value
msgid "Kpi Account Bank Cash Value"
msgstr "حساب المؤشر الرئيسي للأداء للقيمة النقدية للبنك "

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__label
msgid "Label"
msgstr "بطاقة عنوان"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Last Day"
msgstr "اليوم الأخير"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
msgid "Last Statement"
msgstr "آخر كشف حساب "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__write_uid
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__write_uid
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__write_uid
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__write_date
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__write_date
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__write_date
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
msgid "Latest Statement"
msgstr "أحدث كشف حساب "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Legal signatory"
msgstr "الطرف الموقِّع القانوني "

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
msgid "Let’s go back to the dashboard."
msgstr "فلنعد إلى لوحة البيانات. "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__line_ids
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__line_ids
msgid "Line"
msgstr "البند "

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_bank_rec_widget_line
msgid "Line of the bank reconciliation widget"
msgstr "بند أداة التسوية البنكية "

#. module: account_accountant
#: model:ir.ui.menu,name:account_accountant.menu_action_change_lock_date
msgid "Lock Dates"
msgstr "تواريخ الإقفال"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__fiscalyear_lock_date
msgid "Lock Everything"
msgstr "قفل الكل "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__fiscalyear_lock_date_for_everyone
msgid "Lock Everything For Everyone"
msgstr "إقفال كل شيء للجميع "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__fiscalyear_lock_date_for_me
msgid "Lock Everything For Me"
msgstr "إقفال كل شيء بالنسبة لي "

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.action_view_account_change_lock_date
msgid "Lock Journal Entries"
msgstr "إقفال قيود اليومية "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__purchase_lock_date
msgid "Lock Purchases"
msgstr "إقفال المشتريات "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__purchase_lock_date_for_everyone
msgid "Lock Purchases For Everyone"
msgstr "إقفال المشتريات للجميع "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__purchase_lock_date_for_me
msgid "Lock Purchases For Me"
msgstr "إقفال المشتريات بالنسبة لي "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__sale_lock_date
msgid "Lock Sales"
msgstr "إقفال المبيعات "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__sale_lock_date_for_everyone
msgid "Lock Sales For Everyone"
msgstr "إقفال المبيعات للجميع "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__sale_lock_date_for_me
msgid "Lock Sales For Me"
msgstr "إقفال المبيعات بالنسبة لي "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__tax_lock_date
msgid "Lock Tax Return"
msgstr "إقفال الإقرار الضريبي "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__tax_lock_date_for_everyone
msgid "Lock Tax Return For Everyone"
msgstr "إقفال الإقرار الضريبي للجميع "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__tax_lock_date_for_me
msgid "Lock Tax Return For Me"
msgstr "إقفال الإقرار الضريبي بالنسبة لي "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__message_main_attachment_id
msgid "Main Attachment"
msgstr "المرفق الرئيسي"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "Manual Operations"
msgstr "العمليات اليدوية "

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__res_company__generate_deferred_expense_entries_method__manual
#: model:ir.model.fields.selection,name:account_accountant.selection__res_company__generate_deferred_revenue_entries_method__manual
msgid "Manually & Grouped"
msgstr "يدوياً ومجمع "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__manually_modified
msgid "Manually Modified"
msgstr "تم التعديل يدوياً "

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/list_view_switcher.js:0
msgid "Match"
msgstr "مطابقة"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "Match Existing Entries"
msgstr "مطابقة القيود الموجودة "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_kanban_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Matched"
msgstr "تمت المطابقة "

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_payment.py:0
msgid "Matched Transactions"
msgstr "المعاملات المتطابقة "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Matching"
msgstr "مطابقة"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__matching_rules_allow_auto_reconcile
msgid "Matching Rules Allow Auto Reconcile"
msgstr "تسمح قواعد المطابقة بالتسوية التلقائية "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_quick_create_form_bank_rec_widget
msgid "Memo"
msgstr "مذكرة "

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_ir_ui_menu
msgid "Menu"
msgstr "القائمة "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__message_has_error
msgid "Message Delivery error"
msgstr "خطأ في تسليم الرسائل"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__message_ids
msgid "Messages"
msgstr "الرسائل"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__deferred_expense_amount_computation_method
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__deferred_revenue_amount_computation_method
msgid "Method used to compute the amount of deferred entries"
msgstr "الطريقة المستخدمة لاحتساب مبلغ القيود المؤجلة "

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__generate_deferred_expense_entries_method
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__generate_deferred_revenue_entries_method
msgid "Method used to generate deferred entries"
msgstr "الطريقة المستخدمة لإنشاء القيود المؤجلة "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__min_fiscalyear_lock_date_exception_for_everyone_id
msgid "Min Fiscalyear Lock Date Exception For Everyone"
msgstr "الحد الأدنى لاستثناء تاريخ إقفال السنة المالية للجميع "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__min_fiscalyear_lock_date_exception_for_me_id
msgid "Min Fiscalyear Lock Date Exception For Me"
msgstr "الحد الأدنى لاستثناء تاريخ إقفال السنة المالية بالنسبة لي "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__min_purchase_lock_date_exception_for_everyone_id
msgid "Min Purchase Lock Date Exception For Everyone"
msgstr "الحد الأدنى لاستثناء تاريخ إقفال المشتريات للجميع "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__min_purchase_lock_date_exception_for_me_id
msgid "Min Purchase Lock Date Exception For Me"
msgstr "الحد الأدنى لاستثناء تاريخ إقفال المشتريات بالنسبة لي "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__min_sale_lock_date_exception_for_everyone_id
msgid "Min Sale Lock Date Exception For Everyone"
msgstr "الحد الأدنى لاستثناء تاريخ إقفال المبيعات للجميع "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__min_sale_lock_date_exception_for_me_id
msgid "Min Sale Lock Date Exception For Me"
msgstr "الحد الأدنى لاستثناء تاريخ إقفال المبيعات بالنسبة لي "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__min_tax_lock_date_exception_for_everyone_id
msgid "Min Tax Lock Date Exception For Everyone"
msgstr "الحد الأدنى لاستثناء تاريخ الإقفال الضريبي للجميع "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__min_tax_lock_date_exception_for_me_id
msgid "Min Tax Lock Date Exception For Me"
msgstr "الحد الأدنى لاستثناء تاريخ الإقفال الضريبي بالنسبة لي "

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
msgid "Misc"
msgstr "متنوعات"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__res_company__deferred_expense_amount_computation_method__month
#: model:ir.model.fields.selection,name:account_accountant.selection__res_company__deferred_revenue_amount_computation_method__month
msgid "Months"
msgstr "شهور "

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "More"
msgstr "المزيد "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_move_line__move_attachment_ids
msgid "Move Attachment"
msgstr "نقل المرفق "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__move_line_ids
msgid "Move lines to reconcile"
msgstr "بنود الحركات بانتظار التسوية "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__name
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__name
msgid "Name"
msgstr "الاسم"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__narration
msgid "Narration"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "New"
msgstr "جديد"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.action_bank_statement_line_form_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_form_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_quick_create_form_bank_rec_widget
msgid "New Transaction"
msgstr "معاملة جديدة "

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/move_line_list/move_line_list.xml:0
msgid "No attachments linked."
msgstr "لم يتم ربط أي مرفقات. "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "No statement"
msgstr "لا يوجد كشف حساب "

#. module: account_accountant
#: model_terms:ir.actions.act_window,help:account_accountant.action_bank_statement_line_transactions
#: model_terms:ir.actions.act_window,help:account_accountant.action_bank_statement_line_transactions_kanban
msgid "No transactions matching your filters were found."
msgstr "لم يتم العثور على أي معاملات تطابق عوامل التصفية الخاصة بك. "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Not Matched"
msgstr "غير مطابق "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "Not locked"
msgstr "لم يتم إقفاله "

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_tree_bank_rec_widget
msgid "Notes"
msgstr "الملاحظات"

#. module: account_accountant
#: model_terms:ir.actions.act_window,help:account_accountant.action_bank_statement_line_transactions
#: model_terms:ir.actions.act_window,help:account_accountant.action_bank_statement_line_transactions_kanban
msgid "Nothing to do here!"
msgstr "لا شيء لتفعله هنا! "

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
msgid "Now, we'll create your first invoice (accountant)"
msgstr "والآن، سوف نقوم بإنشاء فاتورتك الأولى (محاسب) "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__message_needaction_counter
msgid "Number of Actions"
msgstr "عدد الإجراءات"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__message_has_error_counter
msgid "Number of errors"
msgstr "عدد الأخطاء "

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_bank_statement__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "عدد الرسائل التي تتطلب اتخاذ إجراء"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_bank_statement__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "عدد الرسائل الحادث بها خطأ في التسليم"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__res_company__generate_deferred_expense_entries_method__on_validation
#: model:ir.model.fields.selection,name:account_accountant.selection__res_company__generate_deferred_revenue_entries_method__on_validation
msgid "On bill validation"
msgstr "عند تصديق فاتورة المورّد "

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_change_lock_date.py:0
msgid "Only Billing Administrators are allowed to change lock dates!"
msgstr "مديرو الفوترة وحدهم المصرح لهم بتغيير تواريخ الإقفال! "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_wizard
msgid ""
"Only partial reconciliation is possible. Proceed in multiple steps if you "
"want to full reconcile."
msgstr ""
"يُسمَح بالتسوية الجزئية فقط. يمكنك الاستمرار بعدة خطوات إذا أردت التسوية "
"الكلية. "

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/move_line_list/move_line_list.xml:0
msgid "Open attachment in pop out"
msgstr "فتح المرفق في نافذة منبثقة "

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
msgid "Open balance of %(amount)s"
msgstr "رصيد مفتوح بقيمة %(amount)s "

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
msgid "Original Deferred Entries"
msgstr "القيود المؤجلة الأصلية "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement_line__deferred_original_move_ids
#: model:ir.model.fields,field_description:account_accountant.field_account_move__deferred_original_move_ids
msgid "Original Invoices"
msgstr "الفواتير الأصلية "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Originator Tax"
msgstr "ضريبة المُنشئ "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_search_bank_rec_widget
msgid "Outgoing"
msgstr "الصادرة "

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__to_partner_id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__partner_id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__partner_id
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_quick_create_form_bank_rec_widget
msgid "Partner"
msgstr "الشريك"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__partner_currency_id
msgid "Partner Currency"
msgstr "عملة الشريك "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__partner_name
msgid "Partner Name"
msgstr "اسم الشريك"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__partner_payable_account_id
msgid "Partner Payable Account"
msgstr "حساب الشريك الدائن "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__partner_payable_amount
msgid "Partner Payable Amount"
msgstr "مبلغ الشريك الدائن "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__partner_receivable_account_id
msgid "Partner Receivable Account"
msgstr "حساب الشريك المدين "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__partner_receivable_amount
msgid "Partner Receivable Amount"
msgstr "مبلغ الشريك المدين "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__partner_ids
msgid "Partners"
msgstr "الشركاء"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "Payable"
msgstr "الدائن"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "Payable:"
msgstr "الدائن: "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_payment_form_inherit_account_accountant
msgid "Payment Matching"
msgstr "مطابقة المدفوعات"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement_line__payment_state_before_switch
#: model:ir.model.fields,field_description:account_accountant.field_account_move__payment_state_before_switch
msgid "Payment State Before Switch"
msgstr "حالة الدفع قبل التحويل "

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_payment
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Payments"
msgstr "الدفعات"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
msgid "Payments Matching"
msgstr "مطابقة المدفوعات"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__account_auto_reconcile_wizard__search_mode__one_to_one
msgid "Perfect Match"
msgstr "مطابق تماماً "

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
msgid "Please set the deferred accounts in the accounting settings."
msgstr "يرجى تعيين الحسابات المؤجلة في إعدادات المحاسبة. "

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
msgid "Please set the deferred journal in the accounting settings."
msgstr "يرجى إعداد دفتر اليومية المؤجل في إعدادات المحاسبة. "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__predict_bill_product
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__predict_bill_product
msgid "Predict Bill Product"
msgstr "توقع منتج الفاتورة "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Predict vendor bill product"
msgstr "توقع منتج فاتورة المورّد "

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_reconcile_model
msgid ""
"Preset to create journal entries during a invoices and payments matching"
msgstr "الإعداد المسبق لإنشاء قيود يومية خلال مطابقة الفواتير والدفعات"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__rating_ids
msgid "Ratings"
msgstr "التقييمات "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "Reason..."
msgstr "السبب..."

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "Receivable"
msgstr "المدين"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "Receivable:"
msgstr "المدين: "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__search_mode
#: model:ir.ui.menu,name:account_accountant.menu_account_reconcile
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_auto_reconcile_wizard
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_wizard
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_payment_tree
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_tree
msgid "Reconcile"
msgstr "تسوية"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_wizard
msgid "Reconcile & open"
msgstr "تسوية وفتح "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__reco_account_id
msgid "Reconcile Account"
msgstr "تسوية الحساب "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__reconcile_model_id
msgid "Reconcile Model"
msgstr "نموذج التسوية "

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.action_open_auto_reconcile_wizard
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_auto_reconcile_wizard
msgid "Reconcile automatically"
msgstr "التسوية تلقائياً "

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_auto_reconcile_wizard__search_mode
msgid ""
"Reconcile journal items with opposite balance or clear accounts with a zero "
"balance"
msgstr ""
"تسوية بنود دفتر اليومية ذات الرصيد المعاكس أو تصفية الحسابات ذات الرصيد "
"الصفري "

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget__state__reconciled
msgid "Reconciled"
msgstr "تمت التسوية"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__reco_model_id
msgid "Reconciliation model"
msgstr "نموذج التسوية "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Record cost of goods sold in your journal entries"
msgstr "سجّل تكاليف البضاعة المباعة في قيود اليومية الخاصة بك "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__ref
msgid "Ref"
msgstr "المرجع "

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "Reference"
msgstr "الرقم المرجعي "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_form_inherit
msgid "Related Purchase(s)"
msgstr "عمليات الشراء ذات الصلة "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_form_inherit
msgid "Related Sale(s)"
msgstr "المبيعات ذات الصلة "

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "Reset"
msgstr "إعادة الضبط "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Residual"
msgstr "المتبقي"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Residual in Currency"
msgstr "المتبقي بالعملة "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__return_todo_command
msgid "Return Todo Command"
msgstr "إرجاع أمر قائمة المهام "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "Review"
msgstr "مراجعة"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "Revoke"
msgstr "إلغاء "

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_reconcile_model_line
msgid "Rules for the reconciliation model"
msgstr "قواعد نموذج التسوية "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__message_has_sms_error
msgid "SMS Delivery error"
msgstr "خطأ في تسليم الرسائل النصية القصيرة "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "Save"
msgstr "حفظ"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_form_bank_rec_widget
msgid "Save & Close"
msgstr "حفظ وإغلاق"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_form_bank_rec_widget
msgid "Save & New"
msgstr "حفظ و جديد"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "Search Journal Items to Reconcile"
msgstr "البحث عن القيود اليومية المُراد تسويتها "

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__signing_user
msgid ""
"Select a user here to override every signature on invoice by this user's "
"signature"
msgstr ""
"قم بتحديد مستخدم هنا لاستبدال كل توقيع في الفاتورة بتوقيع هذا المستخدم "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__selected_aml_ids
msgid "Selected Aml"
msgstr "Aml المحددة "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__selected_reco_model_id
msgid "Selected Reco Model"
msgstr "تحديد نموذج التسوية "

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
msgid "Set an amount."
msgstr "قم بتعيين مبلغ. "

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "Set as Checked"
msgstr "التعيين كتمّ التحقق منه "

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
msgid "Set the payment reference."
msgstr "قم بتعيين مرجع الدفع. "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__show_draft_entries_warning
msgid "Show Draft Entries Warning"
msgstr "إظهار تحذير القيود بحالة المسودة "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement_line__show_signature_area
#: model:ir.model.fields,field_description:account_accountant.field_account_move__show_signature_area
msgid "Show Signature Area"
msgstr "إظهار مكان التوقيع "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__module_sign
msgid "Sign"
msgstr "توقيع"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement_line__signature
#: model:ir.model.fields,field_description:account_accountant.field_account_move__signature
msgid "Signature"
msgstr "التوقيع "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__signing_user
msgid "Signature used to sign all the invoice"
msgstr "التوقيع المستَخدَم للتوقيع على الفاتورة بأكملها "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement_line__signing_user
#: model:ir.model.fields,field_description:account_accountant.field_account_move__signing_user
msgid "Signer"
msgstr "الطرف الموقِّع "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__signing_user
msgid "Signing User"
msgstr "المُستخدِم الموقِّع "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__single_currency_mode
msgid "Single Currency Mode"
msgstr "وضع العملة الواحدة "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_aml_id
msgid "Source Aml"
msgstr "Aml المصدرية "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_aml_move_id
msgid "Source Aml Move"
msgstr "حركة Aml المصدرية "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_aml_move_name
msgid "Source Aml Move Name"
msgstr "اسم حركة Aml المصدرية "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_amount_currency
msgid "Source Amount Currency"
msgstr "عملة المبلغ المصدري "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_balance
msgid "Source Balance"
msgstr "الرصيد المصدري "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_credit
msgid "Source Credit"
msgstr "مصدر الائتمان"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_debit
msgid "Source Debit"
msgstr "مصدر الخصم"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__st_line_id
msgid "St Line"
msgstr "بند كشف الحساب "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__st_line_transaction_details
msgid "St Line Transaction Details"
msgstr "تفاصيل معاملة بند كشف الحساب "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__date_from
#: model:ir.model.fields,field_description:account_accountant.field_account_move_line__deferred_start_date
msgid "Start Date"
msgstr "تاريخ البدء "

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_fiscal_year__date_from
msgid "Start Date, included in the fiscal year."
msgstr "تاريخ البداية، ضمن السنة المالية. "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__state
msgid "State"
msgstr "الحالة"

#. module: account_accountant
#: model:ir.actions.server,name:account_accountant.action_bank_statement_attachment
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_kanban_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Statement"
msgstr "كشف الحساب"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Statement Line"
msgstr "بند كشف الحساب"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__suggestion_amount_currency
msgid "Suggestion Amount Currency"
msgstr "عملة المبلغ المقترح "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__suggestion_balance
msgid "Suggestion Balance"
msgstr "الرصيد المقترح "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__suggestion_html
msgid "Suggestion Html"
msgstr "Html المقترح "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_list_bank_rec_widget
msgid "Suggestions"
msgstr "الاقتراحات "

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_tax
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__tax_id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__tax_ids
msgid "Tax"
msgstr "الضريبة"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__tax_base_amount_currency
msgid "Tax Base Amount Currency"
msgstr "عملة المبلغ الأساسي للضريبة "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Tax Grids"
msgstr "شبكات الضرائب"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__tax_repartition_line_id
msgid "Tax Repartition Line"
msgstr "بند التوزيع الضريبي"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__tax_tag_ids
msgid "Tax Tag"
msgstr "علامة تصنيف الضريبة "

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
msgid "Taxes"
msgstr "الضرائب"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/rainbowman_content.xml:0
msgid "That's on average"
msgstr "هذا في المتوسط"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_bank_rec_widget__country_code
#: model:ir.model.fields,help:account_accountant.field_bank_rec_widget_line__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"كود الدولة حسب المعيار الدولي أيزو المكون من حرفين.\n"
"يمكنك استخدام هذا الحقل لإجراء بحث سريع."

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_bank_rec_widget_line__amount_transaction_currency
msgid ""
"The amount expressed in an optional other currency if it is a multi-currency"
" entry."
msgstr "يتم عرض المبلغ بعملة اختيارية أخرى إذا كان قيداً متعدد العملات. "

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
msgid ""
"The amount of the write-off of a single credit line should be strictly "
"negative."
msgstr "يجب أن يكون مبلغ الشطب لبند ائتماني واحد قيمة سالبة فقط. "

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
msgid ""
"The amount of the write-off of a single debit line should be strictly "
"positive."
msgstr "يجب أن يكون مبلغ الشطب لبند خصم واحد قيمة موجبة فقط. "

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
msgid "The amount of the write-off of a single line cannot be 0."
msgstr "لا يمكن أن يكون مبلغ الشطب لبند واحد 0. "

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
msgid ""
"The date you set violates the lock date of one of your entry. It will be "
"overriden by the following date : %(replacement_date)s"
msgstr ""
"التاريخ الذي قمت بتحديده يتضارب مع تاريخ الإقفال لإحدى قيودك. سيتم تجاوزه من"
" قِبَل التاريخ التالي: %(replacement_date)s"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_bank_statement_line__deferred_move_ids
#: model:ir.model.fields,help:account_accountant.field_account_move__deferred_move_ids
msgid "The deferred entries created by this invoice"
msgstr "القيود المؤجلة التي تم إنشاؤها من قِبَل هذه الفاتورة "

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_fiscal_year.py:0
msgid "The ending date must not be prior to the starting date."
msgstr "يجب ألا يقع تاريخ الانتهاء قبل تاريخ البداية. "

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget_line.py:0
msgid ""
"The invoice %(display_name_html)s with an open amount of %(open_amount)s "
"will be entirely paid by the transaction."
msgstr ""
"الفاتورة %(display_name_html)s التي بها مبلغ مفتوح قيمته %(open_amount)s "
"سيتم دفعها كاملة بواسطة المعاملة. "

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget_line.py:0
msgid ""
"The invoice %(display_name_html)s with an open amount of %(open_amount)s "
"will be reduced by %(amount)s."
msgstr ""
"الفاتورة %(display_name_html)s مع مبلغ مفتوح قدره %(open_amount)s سيتم "
"تقليله بـ %(amount)s. "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid ""
"The invoices up to this date will not be taken into account as accounting "
"entries"
msgstr "لن يتم اعتبار الفواتير حتى هذا التاريخ كقيود محاسبية "

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_bank_rec_widget_line__transaction_currency_id
msgid "The optional other currency if it is a multi-currency entry."
msgstr "العملة الاختيارية الأخرى إذا كان القيد متعدد العملات."

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_bank_statement_line__deferred_original_move_ids
#: model:ir.model.fields,help:account_accountant.field_account_move__deferred_original_move_ids
msgid "The original invoices that created the deferred entries"
msgstr "الفواتير الأصلية التي قامت بإنشاء القيود المؤجلة "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid ""
"The system will try to predict the product on vendor bill lines based on the"
" label of the line"
msgstr ""
"سيحاول النظام توقع المنتج في بنود فاتورة المورد بناءً على عنوان البند. "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid ""
"There are still draft entries in the period you want to lock.\n"
"                                You should either post or delete them."
msgstr ""
"لا تزال هناك قيود بحالة المسودة في الفترة التي تريد إقفالها.\n"
"                                عليك إما ترحيلها أو حذفها. "

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_bank_statement.py:0
msgid ""
"This bank transaction has been automatically validated using the "
"reconciliation model '%s'."
msgstr ""
"هذه المعاملة البنكية قد تم تصديقها تلقائياً باستخدام نموذج التسوية '%s'. "

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
msgid ""
"This bank transaction is locked up tighter than a squirrel in a nut factory!"
" You can't hit the reset button on it. So, do you want to \"unreconcile\" it"
" instead?"
msgstr ""
"هذه المعاملة البنكية مغلقة بإحكام أكثر من السنجاب في مصنع الجوز! لا يمكنك "
"الضغط على زر إعادة ضبطها. لذا، أترغب في \"إلغاءها\" عوضاً عن ذلك؟ "

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
msgid "This can only be used on journal items"
msgstr "يمكن استخدام ذلك فقط في عناصر اليومية "

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_reconcile_model_line.py:0
msgid ""
"This reconciliation model can't be used in the manual reconciliation widget "
"because its configuration is not adapted"
msgstr ""
"لا يمكن استخدام نموذج التسوية في أداة التسوية اليدوية لعدم اعتماد تهيئته "

#. module: account_accountant
#: model:digest.tip,name:account_accountant.digest_tip_account_accountant_0
msgid "Tip: Bulk update journal items"
msgstr "نصيحة: قم بتحديث قيود اليومية بالجملة "

#. module: account_accountant
#: model:digest.tip,name:account_accountant.digest_tip_account_accountant_1
msgid "Tip: Find an Accountant or register your Accounting Firm"
msgstr "نصيحة: ابحث عن محاسب أو قم بتسجيل شركتك المحاسبة الخاصة بك "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__to_date
msgid "To"
msgstr "إلى"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__to_check
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "To Check"
msgstr "للتحقق منه "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_kanban_bank_rec_widget
msgid "To check"
msgstr "للتحقق منه "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "To enhance authenticity, add a signature to your invoices"
msgstr "لتعزيز صحة مستنداتك، أضف توقيعاً إلى فواتيرك "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__todo_command
msgid "Todo Command"
msgstr "أمر قائمة المهام "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Total Balance"
msgstr "الرصيد الكلي "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Total Credit"
msgstr "إجمالي الائتمان"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Total Debit"
msgstr "إجمالي الدين"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Total Residual"
msgstr "إجمالي المتبقي "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Total Residual in Currency"
msgstr "إجمالي المتبقي بالعملة "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Transaction"
msgstr "معاملة"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__transaction_currency_id
msgid "Transaction Currency"
msgstr "عملة المعاملة "

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "Transaction Details"
msgstr "تفاصيل المعاملة "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_tree
msgid "Transactions"
msgstr "المعاملات "

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
msgid "Transfer from %s"
msgstr "التحويل من %s"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
msgid "Transfer to %s"
msgstr "التحويل إلى %s"

#. module: account_accountant
#: model:ir.actions.server,name:account_accountant.auto_reconcile_bank_statement_line_ir_actions_server
msgid "Try to reconcile automatically your statement lines"
msgstr "حاول تسوية بنود كشف حسابك تلقائياً "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "Unreconciled"
msgstr "غير المسواة"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/res_company.py:0
msgid "Unreconciled statements lines"
msgstr "بنود كشف الحساب غير المسواة "

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget__state__valid
msgid "Valid"
msgstr "صالح"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_model_widget_wizard
msgid "Validate"
msgstr "تصديق "

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/list_view_switcher.js:0
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_list_bank_rec_widget
msgid "View"
msgstr "أداة العرض"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
msgid "View Reconciled Entries"
msgstr "عرض القيود المسواة "

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "View models"
msgstr "عرض النماذج "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__website_message_ids
msgid "Website Messages"
msgstr "رسائل الموقع الإلكتروني "

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_bank_statement__website_message_ids
msgid "Website communication history"
msgstr "سجل تواصل الموقع الإلكتروني "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "With residual"
msgstr "مع متبقي "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__wizard_id
msgid "Wizard"
msgstr "المعالج"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__company_currency_id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__company_currency_id
msgid "Wizard Company Currency"
msgstr "معالج عملة الشركة "

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
msgid "Write-Off"
msgstr "شطب"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
msgid "Write-Off Entry"
msgstr "شطب القيد "

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_fiscal_year.py:0
msgid ""
"You can not have an overlap between two fiscal years, please correct the "
"start and/or end dates of your fiscal years."
msgstr ""
"لا يمكن أن يكون هناك تداخل بين سنتين ماليتين، الرجاء تصحيح تواريخ بدء و/أو "
"انتهاء سنواتك المالية. "

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
msgid "You can only reconcile entries with up to two different accounts: %s"
msgstr "يمكنك فقط تسوية القيود حتى حسابين مختلفين: %s"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
msgid "You can't hit the reset button on a secured bank transaction."
msgstr "لا يمكنك الضغط على زر إعادة الضبط في معاملة بنكية مؤمَّنة. "

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
msgid ""
"You cannot change the account for a deferred line in %(move_name)s if it has"
" already been deferred."
msgstr ""
"لا يمكنك تغيير الحساب للبند المؤجل %(move_name)s إذا كان مؤجلاً بالفعل. "

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
msgid "You cannot create a deferred entry with a start date but no end date."
msgstr "لا يمكنك إنشاء قيد مؤجل مع تاريخ بدء ودون تاريخ انتهاء. "

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
msgid ""
"You cannot create a deferred entry with a start date later than the end "
"date."
msgstr "لا يمكنك إنشاء قيد مؤجل مع تاريخ بدء أبعد من تاريخ انتهاء. "

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
msgid ""
"You cannot generate deferred entries for a miscellaneous journal entry."
msgstr "لا يمكنك إنشاء قيود مؤجلة لقيد يومية من المتفرقات. "

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_fiscal_year.py:0
msgid "You cannot have a fiscal year on a child company."
msgstr "لا يمكن أن يكون لديك عام مالي في شركة تابعة. "

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
msgid ""
"You cannot reset to draft an invoice that is grouped in deferral entry. You "
"can create a credit note instead."
msgstr ""
"لا يمكنك إعادة تعيين فاتورة قد تم تجميعها في قيد مؤجل إلى حالة المسودة. "
"يمكنك إنشاء إشعار دائن عوضاً عن ذلك. "

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_change_lock_date.py:0
msgid "You cannot set a Lock Date in the future."
msgstr "لا يمكنك تعيين تاريخ إقفال في المستقبل. "

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget_line.py:0
msgid ""
"You might want to %(btn_start)sfully reconcile%(btn_end)s the document."
msgstr "قد ترغب بإجراء %(btn_start)sالتسوية الكلية%(btn_end)s للمستند.  "

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget_line.py:0
msgid ""
"You might want to make a %(btn_start)spartial reconciliation%(btn_end)s "
"instead."
msgstr "قد ترغب بإجراء %(btn_start)sالتسوية الجزئية%(btn_end)s عوضاً عن ذلك. "

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget_line.py:0
msgid "You might want to record a %(btn_start)spartial payment%(btn_end)s."
msgstr "قد ترغب بتسجيل %(btn_start)sالتسوية الجزئية%(btn_end)s. "

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget_line.py:0
msgid ""
"You might want to set the invoice as %(btn_start)sfully paid%(btn_end)s."
msgstr "قد ترغب بتعيين الفاتورة كـ %(btn_start)sمدفوعة بالكامل%(btn_end)s. "

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_change_lock_date.py:0
msgid "You need to select a duration for the exception."
msgstr "عليك تحديد مدة للاستثناء. "

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_change_lock_date.py:0
msgid "You need to select who the exception applies to."
msgstr "عليك تحديد الأفراد الذين ينطبق عليهم الاستثناء. "

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/rainbowman_content.xml:0
msgid "You reconciled"
msgstr "لقد قمت بتسوية "

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__aml
msgid "aml"
msgstr "aml"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__auto_balance
msgid "auto_balance"
msgstr "auto_balance"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_model_widget_wizard
msgid "e.g. Bank Fees"
msgstr "مثال: الرسوم البنكية "

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__early_payment
msgid "early_payment"
msgstr "early_payment"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__exchange_diff
msgid "exchange_diff"
msgstr "exchange_diff"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__account_change_lock_date__exception_duration__1h
msgid "for 1 hour"
msgstr "لمدة ساعة واحدة "

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__account_change_lock_date__exception_duration__15min
msgid "for 15 minutes"
msgstr "لمدة 15 دقيقة "

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__account_change_lock_date__exception_duration__24h
msgid "for 24 hours"
msgstr "لمدة 24 ساعة "

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__account_change_lock_date__exception_duration__5min
msgid "for 5 minutes"
msgstr "لمدة 5 دقائق "

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__account_change_lock_date__exception_applies_to__everyone
msgid "for everyone"
msgstr "للجميع "

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__account_change_lock_date__exception_applies_to__me
msgid "for me"
msgstr "لي "

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__account_change_lock_date__exception_duration__forever
msgid "forever"
msgstr "للأبد "

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "in"
msgstr "في"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__liquidity
msgid "liquidity"
msgstr "السيولة "

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__manual
msgid "manual"
msgstr "اليدوية "

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__new_aml
msgid "new_aml"
msgstr "new_aml"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/rainbowman_content.xml:0
msgid "seconds per transaction."
msgstr "ثوان لكل معاملة. "

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__tax_line
msgid "tax_line"
msgstr "tax_line"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
msgid "to check"
msgstr "للتفقد"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
msgid "to reconcile"
msgstr "بانتظار التسوية "

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/rainbowman_content.xml:0
msgid "transaction in"
msgstr "معاملة في "

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/rainbowman_content.xml:0
msgid "transactions in"
msgstr "معاملات في "
