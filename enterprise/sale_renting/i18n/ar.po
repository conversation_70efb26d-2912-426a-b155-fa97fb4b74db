# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_renting
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 09:12+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/sale_order_line.py:0
msgid ""
"\n"
"%(from_date)s to %(to_date)s"
msgstr ""
"\n"
"%(from_date)sإلى %(to_date)s "

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/product_template.py:0
msgid "%(amount)s (fixed)"
msgstr "%(amount)s (ثابت)"

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/product_pricing.py:0
msgid "%(amount)s / %(duration)s"
msgstr "%(amount)s / %(duration)s"

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/product_template.py:0
#: code:addons/sale_renting/models/sale_order_recurrence.py:0
msgid "%(duration)s %(unit)s"
msgstr "%(duration)s %(unit)s"

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/product_product.py:0
#: code:addons/sale_renting/models/product_template.py:0
msgid "%s (Rental)"
msgstr "%s (تأجير) "

#. module: sale_renting
#: model:ir.actions.report,print_report_name:sale_renting.action_report_rental_saleorder
msgid ""
"(object.rental_status not in ('draft', 'sent') and 'Delivery or Return "
"Receipt - %s' %(object.name)) or 'Order - %s' % (object.name)"
msgstr ""
"(object.rental_status not in ('draft', 'sent') and 'Delivery or Return "
"Receipt - %s' %(object.name)) or 'Order - %s' % (object.name)"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "123 Main St"
msgstr "123 Main St"

#. module: sale_renting
#: model:sale.temporal.recurrence,name:sale_renting.recurrence_2_weeks
msgid "2 Weeks"
msgstr "أسبوعين 2 "

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "2023-08-01"
msgstr "2023-08-01"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "2023-08-10"
msgstr "2023-08-10"

#. module: sale_renting
#: model:sale.temporal.recurrence,name:sale_renting.recurrence_3_hours
msgid "3 Hours"
msgstr "3 ساعات "

#. module: sale_renting
#: model:sale.temporal.recurrence,name:sale_renting.recurrence_3_year
msgid "3 years"
msgstr "3 سنوات "

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "456 Other St"
msgstr "456 Other St"

#. module: sale_renting
#: model:sale.temporal.recurrence,name:sale_renting.recurrence5_year
msgid "5 Years"
msgstr "5 سنوات "

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_kanban
msgid "<i class=\"fa fa-warning\"/> Late"
msgstr "<i class=\"fa fa-warning\"/> متأخر "

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.product_product_form_view_rental_gantt
#: model_terms:ir.ui.view,arch_db:sale_renting.product_template_form_view_rental_gantt
msgid "<span class=\"o_stat_text\">in Rental</span>"
msgstr "<span class=\"o_stat_text\">في الإيجار</span>"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
msgid ""
"<span invisible=\"duration_days != 1\"> day </span>\n"
"                    <span invisible=\"duration_days in [0,1]\"> days </span>\n"
"                    <span invisible=\"duration_days == 0 or remaining_hours == 0\">and </span>"
msgstr ""
"<span invisible=\"duration_days != 1\"> يوم </span>\n"
"                    <span invisible=\"duration_days in [0,1]\"> أيام </span>\n"
"                    <span invisible=\"duration_days == 0 or remaining_hours == 0\">و </span>"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
msgid ""
"<span invisible=\"remaining_hours != 1\"> hour </span>\n"
"                    <span invisible=\"remaining_hours in [0,1]\"> hours </span>"
msgstr ""
"<span invisible=\"remaining_hours != 1\"> ساعة </span>\n"
"                    <span invisible=\"remaining_hours in [0,1]\"> ساعات </span>"

#. module: sale_renting
#: model_terms:web_tour.tour,rainbow_man_message:sale_renting.rental_tour
msgid "<span><b>Good job!</b> You went through all steps of this tour.</span>"
msgstr "<span><b>عمل رائع!</b> لقد تخطيت كافة مراحل هذه الجولة.</span>"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
msgid "<strong>Order # — </strong>"
msgstr "<strong>رقم الطلب — </strong> "

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
msgid "<strong>Pickup  — </strong>"
msgstr "<strong>الاستلام  — </strong> "

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
msgid "<strong>Return — </strong>"
msgstr "<strong>الإرجاع — </strong> "

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "<strong>Salesperson:</strong>"
msgstr "<strong>مندوب المبيعات:</strong>"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "<strong>Shipping Address:</strong>"
msgstr "<strong>عنوان الشحن:</strong> "

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
msgid "<strong>Status — </strong>"
msgstr "<strong>الحالة — </strong> "

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/product_template.py:0
msgid "A rental combo product can only contain rental products."
msgstr ""

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_temporal_recurrence__active
msgid "Active"
msgstr "نشط"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.product_template_form_view_rental
msgid "Add a price"
msgstr "إضافة سعر "

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.res_config_settings_view_form
msgid "Additional costs for late returns"
msgstr "تكاليف إضافية للتأخر في الإرجاع "

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_product_product__rent_ok
#: model:ir.model.fields,help:sale_renting.field_product_template__rent_ok
#: model:ir.model.fields,help:sale_renting.field_sale_order_line__is_product_rentable
msgid "Allow renting of this product."
msgstr "السماح بتأجير هذا المنتج. "

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.res_config_settings_view_form
msgid "Apply after"
msgstr "التطبيق بعد "

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.res_config_settings_view_form
msgid "Ask customer to sign documents on the spot."
msgstr "اطلب من العميل التوقيع على المستندات حالاً. "

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid "At first, let's create some products to rent."
msgstr "في البداية، فلنقم بإنشاء بعض المنتجات لتأجيرها. "

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_form
msgid "Booked"
msgstr "محجوز "

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_wizard_view_form
msgid "Cancel"
msgstr "إلغاء"

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_order__rental_status__cancel
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_report__state__cancel
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__rental_status__cancel
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__state__cancel
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
msgid "Cancelled"
msgstr "تم الإلغاء "

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid "Click here to create a new quotation."
msgstr "اضغط هنا لإنشاء عرض سعر جديد. "

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid "Click here to register the pickup."
msgstr "اضغط هنا لتسجيل الاستلام. "

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid "Click here to set up your first rental product."
msgstr "اضغط هنا لضبط منتجك الأول للتأجير. "

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid "Click here to start filling the quotation."
msgstr "اضغط هنا لبدء ملء عرض السعر. "

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__color
msgid "Color"
msgstr "اللون"

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_res_company
msgid "Companies"
msgstr "الشركات"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_pricing__company_id
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__company_id
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__company_id
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_search_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Company"
msgstr "الشركة "

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_res_config_settings
msgid "Config Settings"
msgstr "تهيئة الإعدادات "

#. module: sale_renting
#: model:ir.ui.menu,name:sale_renting.menu_rental_config
msgid "Configuration"
msgstr "التهيئة "

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid "Confirm the order when the customer agrees with the terms."
msgstr "قم بتأكيد الطلب عندما يوافق العميل على الشروط. "

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid "Confirm the returned quantities and hit Validate."
msgstr "قم بتأكيد الكميات المرجعة ثم اضغط على تصديق. "

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_search_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Confirmed Orders"
msgstr "الطلبات المؤكدة "

#. module: sale_renting
#: model:ir.actions.act_window,name:sale_renting.action_create_rental_order
msgid "Create Rental Orders"
msgstr "إنشاء أوامر تأجير "

#. module: sale_renting
#: model_terms:ir.actions.act_window,help:sale_renting.sale_temporal_recurrence_action
msgid "Create a new period"
msgstr "إنشاء فترة زمنية جديدة "

#. module: sale_renting
#: model_terms:ir.actions.act_window,help:sale_renting.rental_order_action
msgid "Create a new quotation, the first step of a new rental!"
msgstr "إنشاء عرض سعر جديد، وهى أول خطوة لعملية التأجير الجديدة! "

#. module: sale_renting
#: model_terms:ir.actions.act_window,help:sale_renting.product_pricing_action
msgid "Create a new recurrence"
msgstr "إنشاء تكرار جديد "

#. module: sale_renting
#: model_terms:ir.actions.act_window,help:sale_renting.action_rental_report
msgid "Create a new rental order"
msgstr "إنشاء أمر تأجير جديد "

#. module: sale_renting
#: model_terms:ir.actions.act_window,help:sale_renting.rental_product_template_action
msgid "Create a new rental product!"
msgstr "إنشاء منتج تأجير جديد! "

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid "Create or select a customer here."
msgstr "قم بإنشاء أو تحديد عميل من هنا. "

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order__is_rental_order
msgid "Created In App Rental"
msgstr "تم إنشاؤه في تطبيق التأجير "

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_pricing__create_uid
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard__create_uid
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__create_uid
#: model:ir.model.fields,field_description:sale_renting.field_sale_temporal_recurrence__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_pricing__create_date
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard__create_date
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__create_date
#: model:ir.model.fields,field_description:sale_renting.field_sale_temporal_recurrence__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_pricing__currency_id
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__currency_id
msgid "Currency"
msgstr "العملة"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_product__product_pricing_ids
#: model:ir.model.fields,field_description:sale_renting.field_product_template__product_pricing_ids
msgid "Custom Pricings"
msgstr "الأسعار المخصصة "

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__partner_id
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__partner_id
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_search_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Customer"
msgstr "العميل"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__country_id
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Customer Country"
msgstr "بلد العميل"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__commercial_partner_id
msgid "Customer Entity"
msgstr "الكيان التجاري للعميل"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__card_name
msgid "Customer Name"
msgstr "اسم العميل"

#. module: sale_renting
#: model:ir.ui.menu,name:sale_renting.menu_orders_customers
msgid "Customers"
msgstr "العملاء"

#. module: sale_renting
#: model:sale.temporal.recurrence,name:sale_renting.recurrence_daily
msgid "Daily"
msgstr "يوميًا"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__price
msgid "Daily Amount"
msgstr "المبلغ اليومي "

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__quantity
msgid "Daily Ordered Qty"
msgstr "الكمية اليومية المطلوبة "

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__qty_delivered
msgid "Daily Picked-Up Qty"
msgstr "الكمية التي يتم استلامها يومياً "

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__qty_returned
msgid "Daily Returned Qty"
msgstr "الكمية التي يتم إرجاعها يومياً "

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__date
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_search_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Date"
msgstr "التاريخ"

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/sale_order_recurrence.py:0
msgid "Day"
msgstr "اليوم"

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_temporal_recurrence__unit__day
msgid "Days"
msgstr "الأيام"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.res_config_settings_view_form
msgid "Default Delay Costs"
msgstr "تكاليف التأخير الافتراضية "

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_res_config_settings__extra_product
msgid "Delay Product"
msgstr "المنتج المتأخر "

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_pricing__description
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__description
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "Description"
msgstr "الوصف"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_res_config_settings__module_sale_renting_sign
msgid "Digital Documents"
msgstr "المستندات الرقمية "

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_pricing__display_name
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard__display_name
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__display_name
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__display_name
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__display_name
#: model:ir.model.fields,field_description:sale_renting.field_sale_temporal_recurrence__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_temporal_recurrence__duration
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
msgid "Duration"
msgstr "المدة"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_pricing__name
#: model:ir.model.fields,field_description:sale_renting.field_sale_temporal_recurrence__duration_display
msgid "Duration Display"
msgstr "عرض المدة "

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order__duration_days
msgid "Duration in days"
msgstr "المدة بالأيام"

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid "Enter the product name."
msgstr "أدخل اسم المنتج. "

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "Expected Return"
msgstr "الإرجاع المتوقع "

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/sale_order_line.py:0
msgid "Expected: %(date)s"
msgstr "المتوقع: %(date)s "

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_product__extra_daily
#: model:ir.model.fields,field_description:sale_renting.field_product_template__extra_daily
msgid "Extra Day"
msgstr "يوم إضافي "

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_product__extra_hourly
#: model:ir.model.fields,field_description:sale_renting.field_product_template__extra_hourly
msgid "Extra Hour"
msgstr "ساعة إضافية "

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_product_product__extra_daily
#: model:ir.model.fields,help:sale_renting.field_product_template__extra_daily
msgid "Fine by day overdue"
msgstr "الغرامة لكل يوم تأخير "

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_product_product__extra_hourly
#: model:ir.model.fields,help:sale_renting.field_product_template__extra_hourly
msgid "Fine by hour overdue"
msgstr "الغرامة لكل ساعة تأخير "

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_product_product__display_price
#: model:ir.model.fields,help:sale_renting.field_product_template__display_price
msgid "First rental pricing of the product"
msgstr "أول تسعير تأجير للمنتج "

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid "Go to the orders menu."
msgstr "الذهاب إلى قائمة الطلبات. "

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_search_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Group By"
msgstr "تجميع حسب"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order__show_update_duration
msgid "Has Duration Changed"
msgstr "لقد تغيرت المدة "

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order__has_pickable_lines
msgid "Has Pickable Lines"
msgstr "يحتوي على بنود يمكن استلامها "

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order__has_rented_products
msgid "Has Rented Products"
msgstr "قام بتأجير منتجات "

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order__has_returnable_lines
msgid "Has Returnable Lines"
msgstr "يحتوي على بنود يمكن إرجاعها "

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/sale_order_recurrence.py:0
msgid "Hour"
msgstr "ساعة"

#. module: sale_renting
#: model:sale.temporal.recurrence,name:sale_renting.recurrence_hourly
msgid "Hourly"
msgstr "في الساعة "

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_temporal_recurrence__unit__hour
msgid "Hours"
msgstr "ساعات "

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_pricing__id
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard__id
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__id
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__id
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__id
#: model:ir.model.fields,field_description:sale_renting.field_sale_temporal_recurrence__id
msgid "ID"
msgstr "المُعرف"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "Invoicing Address:"
msgstr "عنوان الفوترة: "

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "Invoicing and Shipping Address:"
msgstr "عنوان الفاتورة والشحن:"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__late
msgid "Is Late"
msgstr "متأخر "

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order_line__is_rental
msgid "Is Rental"
msgstr "للتأجير "

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard__is_late
#: model:ir.model.fields,field_description:sale_renting.field_sale_order__is_late
msgid "Is overdue"
msgstr "متأخر "

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "Jane Doe"
msgstr "Jane Doe"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "John Doe"
msgstr "جون دو "

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_pricing__write_uid
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard__write_uid
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__write_uid
#: model:ir.model.fields,field_description:sale_renting.field_sale_temporal_recurrence__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_pricing__write_date
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard__write_date
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__write_date
#: model:ir.model.fields,field_description:sale_renting.field_sale_temporal_recurrence__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
msgid "Late"
msgstr "متأخر"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_form
msgid "Late Pickup"
msgstr "استلام متأخر "

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_form
msgid "Late Return"
msgstr "إرجاع متأخر "

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid "Let's now create an order."
msgstr "فلنقم بإنشاء أمر الآن. "

#. module: sale_renting
#: model:product.template,name:sale_renting.rental_product_2_product_template
msgid "Meeting Room"
msgstr "غرفة اجتماعات "

#. module: sale_renting
#: model:ir.model.constraint,message:sale_renting.constraint_res_company_min_extra_hour
msgid "Minimal delay time before applying fines has to be positive."
msgstr "يجب أن يكون الحد الأدنى للتأخير قبل تطبيق الغرامات قيمة موجبة. "

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_res_company__min_extra_hour
#: model:ir.model.fields,field_description:sale_renting.field_res_config_settings__min_extra_hour
msgid "Minimum delay time before applying fines."
msgstr "الحد الأدنى للتأخير قبل تطبيق الغرامات. "

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_sale_temporal_recurrence__duration
msgid ""
"Minimum duration before this rule is applied. If set to 0, it represents a "
"fixedrental price."
msgstr ""
"المدة الدنيا قبل أن يتم تطبيق هذه القاعدة. إذا تم تحديدها كـ 0، هذا يعني أن "
"سعر التأجير ثابت. "

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/sale_order_recurrence.py:0
msgid "Month"
msgstr "الشهر"

#. module: sale_renting
#: model:sale.temporal.recurrence,name:sale_renting.recurrence_monthly
msgid "Monthly"
msgstr "شهرياً"

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_temporal_recurrence__unit__month
msgid "Months"
msgstr "شهور"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "My Orders"
msgstr "طلباتي"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_temporal_recurrence__name
msgid "Name"
msgstr "الاسم"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order__next_action_date
msgid "Next Action"
msgstr "الإجراء التالي"

#. module: sale_renting
#: model_terms:ir.actions.act_window,help:sale_renting.action_rental_report
msgid "No data yet!"
msgstr "لا توجد أي بيانات بعد! "

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_form
msgid "Ok"
msgstr "موافق"

#. module: sale_renting
#: model_terms:ir.actions.act_window,help:sale_renting.rental_order_action
msgid ""
"Once the quotation is confirmed, it becomes a rental order.<br> You will be "
"able to create an invoice and collect the payment."
msgstr ""
"بمجرد أن يتم تأكيد عرض السعر، يتحول إلى أمر تأجير. <br> سيكون بمقدورك إنشاء "
"فاتورة وتحصيل الدفع. "

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid "Once the rental is done, you can register the return."
msgstr "بمجرد انتهاء مدة التأجير، بإمكانك تسجيل الإرجاع. "

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard__order_id
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
msgid "Order"
msgstr "الطلب"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__order_id
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__order_id
msgid "Order #"
msgstr "رقم الأمر "

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__order_date
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Order Date"
msgstr "تاريخ الطلب "

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__order_line_id
msgid "Order Line"
msgstr "بند الأمر"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__name
msgid "Order Reference"
msgstr "مرجع الطلب "

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__order_line_id
msgid "Order line #"
msgstr "رقم بند الطلب "

#. module: sale_renting
#: model:ir.ui.menu,name:sale_renting.rental_order_menu
#: model:ir.ui.menu,name:sale_renting.rental_orders_all
msgid "Orders"
msgstr "الطلبات "

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_res_company__extra_day
#: model:ir.model.fields,field_description:sale_renting.field_res_config_settings__extra_day
msgid "Per Day"
msgstr "في اليوم "

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_res_company__extra_hour
#: model:ir.model.fields,field_description:sale_renting.field_res_config_settings__extra_hour
msgid "Per Hour"
msgstr "في الساعة "

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.product_pricelist_view
#: model_terms:ir.ui.view,arch_db:sale_renting.product_template_form_view_rental
msgid "Period"
msgstr "الفترة"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.sale_temporal_recurrence_view_form
#: model_terms:ir.ui.view,arch_db:sale_renting.sale_temporal_recurrence_view_tree
msgid "Periodicity"
msgstr "الوتيرة "

#. module: sale_renting
#: model:ir.actions.act_window,name:sale_renting.sale_temporal_recurrence_action
msgid "Periods"
msgstr "الفترات "

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_rental_order_wizard
msgid "Pick-up/Return products"
msgstr "استلام/إرجاع المنتجات "

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
msgid "Picked-Up"
msgstr "تم الاستلام "

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__qty_delivered
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_form
msgid "Picked-up"
msgstr "تم الاستلام "

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_order__rental_status__return
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__rental_status__return
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__report_line_status__pickedup
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "Pickedup"
msgstr "تم الاستلام "

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__rental_order_wizard__status__pickup
#: model:ir.ui.menu,name:sale_renting.rental_orders_pickup
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
msgid "Pickup"
msgstr "استلام "

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__pickup_date
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "Pickup Date"
msgstr "تاريخ الاستلام "

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "Pickup Receipt #"
msgstr "رقم إيصال الاستلام "

#. module: sale_renting
#: model:ir.actions.report,name:sale_renting.action_report_rental_saleorder
msgid "Pickup and Return Receipt"
msgstr "إيصال الاستلام والإرجاع "

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order_line__reservation_begin
msgid "Pickup date - padding time"
msgstr "تاريخ الاستلام - وقت الفحص والترميم "

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_kanban
msgid "Pickup:"
msgstr "الاستلام: "

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_pricing__price
msgid "Price"
msgstr "السعر"

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_product_pricelist
#: model:ir.model.fields,field_description:sale_renting.field_product_pricing__pricelist_id
msgid "Pricelist"
msgstr "قائمه الأسعار"

#. module: sale_renting
#: model:ir.actions.act_window,name:sale_renting.product_pricing_action
#: model_terms:ir.ui.view,arch_db:sale_renting.product_pricing_tree
msgid "Prices"
msgstr "الأسعار"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.product_template_form_view_rental
msgid "Pricing"
msgstr "التسعير"

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_product_pricing
msgid "Pricing rule of rental products"
msgstr "قاعدة التسعير لمنتجات التأجير "

#. module: sale_renting
#: model:product.template,name:sale_renting.rental_product_3_product_template
msgid "Printer"
msgstr "الطابعة"

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_product_template
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__product_id
#: model:ir.model.fields,field_description:sale_renting.field_res_company__extra_product
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__product_id
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__product_id
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_search_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.res_config_settings_view_form
msgid "Product"
msgstr "المنتج"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "Product A"
msgstr "المنتج أ "

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__categ_id
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__categ_id
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_search_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Product Category"
msgstr "فئة المنتج"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__product_name
msgid "Product Reference"
msgstr "مرجع المنتج "

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_pricing__product_template_id
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__product_tmpl_id
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__product_tmpl_id
msgid "Product Template"
msgstr "قالب المنتج"

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_product_product
#: model:ir.model.fields,field_description:sale_renting.field_product_pricing__product_variant_ids
msgid "Product Variant"
msgstr "متغير المنتج "

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.res_config_settings_view_form
msgid "Product to charge extra time"
msgstr "المنتج لفرض رسوم على الوقت الإضافي "

#. module: sale_renting
#: model:ir.actions.act_window,name:sale_renting.rental_product_template_action
#: model:ir.ui.menu,name:sale_renting.menu_rental_products
#: model_terms:ir.ui.view,arch_db:sale_renting.product_pricelist_view
msgid "Products"
msgstr "المنتجات"

#. module: sale_renting
#: model:product.template,name:sale_renting.rental_product_1_product_template
msgid "Projector"
msgstr "جهاز العرض "

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__product_uom_qty
msgid "Qty Ordered"
msgstr "الكمية المطلوبة"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__qty_delivered
msgid "Qty Picked-Up"
msgstr "الكمية المستلمة "

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__qty_returned
msgid "Qty Returned"
msgstr "الكمية المرجعة "

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_product__qty_in_rent
#: model:ir.model.fields,field_description:sale_renting.field_product_template__qty_in_rent
msgid "Quantity currently in rent"
msgstr "الكمية المؤجرة حالياً "

#. module: sale_renting
#: model:sale.temporal.recurrence,name:sale_renting.recurrence_quarterly
msgid "Quarterly"
msgstr "ربع سنوي"

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_order__rental_status__draft
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_report__state__draft
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__rental_status__draft
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__state__draft
msgid "Quotation"
msgstr "عرض سعر"

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_order__rental_status__sent
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_report__state__sent
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__rental_status__sent
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__state__sent
msgid "Quotation Sent"
msgstr "تم إرسال عرض السعر"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_search_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Quotations"
msgstr "عروض الأسعار"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
msgid "Recompute all prices based on this duration"
msgstr "إعادة احتساب كافة الأسعار بناءً على هذه المدة "

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order__remaining_hours
msgid "Remaining duration in hours"
msgstr "المدة المتبقية بالساعات "

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_product__rent_ok
#: model:ir.model.fields,field_description:sale_renting.field_product_template__rent_ok
#: model:ir.model.fields,field_description:sale_renting.field_sale_order_line__is_product_rentable
#: model:ir.ui.menu,name:sale_renting.rental_menu_root
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_product_template_search_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
#: model_terms:ir.ui.view,arch_db:sale_renting.res_config_settings_view_form
msgid "Rental"
msgstr "تأجير "

#. module: sale_renting
#: model:ir.actions.act_window,name:sale_renting.action_rental_report
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_graph_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_pivot_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_search_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.sale_rental_report_view_tree
msgid "Rental Analysis"
msgstr "تحليل التأجير "

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_sale_rental_report
msgid "Rental Analysis Report"
msgstr "تقرير تحليل التأجير "

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_form
msgid "Rental Order"
msgstr "أمر التأجير "

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__rental_order_wizard_id
msgid "Rental Order Wizard"
msgstr "معالج أمر التأجير "

#. module: sale_renting
#: model:ir.actions.act_window,name:sale_renting.rental_order_action
#: model:ir.actions.act_window,name:sale_renting.rental_order_today_pickup_action
#: model:ir.actions.act_window,name:sale_renting.rental_order_today_return_action
msgid "Rental Orders"
msgstr "أوامر التأجير "

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.product_pricelist_view
msgid "Rental Pricelist Rules"
msgstr "قواعد قائمة أسعار التأجير "

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order__rental_return_date
#: model:ir.model.fields,field_description:sale_renting.field_sale_order_line__return_date
msgid "Rental Return Date"
msgstr "تاريخ إرجاع منتج التأجير "

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_sale_rental_schedule
msgid "Rental Schedule"
msgstr "جدول التأجير "

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order__rental_start_date
#: model:ir.model.fields,field_description:sale_renting.field_sale_order_line__start_date
msgid "Rental Start Date"
msgstr "تاريخ بدء التأجير "

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order__rental_status
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__rental_status
msgid "Rental Status"
msgstr "حالة التأجير "

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__report_line_status
msgid "Rental Status (advanced)"
msgstr "حالة التأجير (متقدم) "

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard__rental_wizard_line_ids
msgid "Rental Wizard Line"
msgstr "بند معالج التأجير "

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
msgid "Rental period"
msgstr "فترة التأجير "

#. module: sale_renting
#: model:ir.ui.menu,name:sale_renting.menu_sale_renting_periods
msgid "Rental periods"
msgstr "فترات الإيجار"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_product__display_price
#: model:ir.model.fields,field_description:sale_renting.field_product_template__display_price
msgid "Rental price"
msgstr "سعر التأجير "

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.product_template_form_view_rental
msgid "Rental prices"
msgstr "أسعار التأجير "

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/sale_order.py:0
msgid "Rental prices have been recomputed with the new period."
msgstr "تمت إعادة احتساب أسعار التأجير مع الفترة الزمنية الجديدة. "

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.product_pricelist_view
msgid "Rental rules"
msgstr "قواعد التأجير "

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_rental_order_wizard_line
msgid "RentalOrderLine transient representation"
msgstr "تمثيل لبند أمر التأجير العابر "

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
msgid "Rentals"
msgstr "التأجيرات "

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_pricing__recurrence_id
msgid "Renting Period"
msgstr "فترة التأجير "

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_pricelist__product_pricing_ids
msgid "Renting Price Rules"
msgstr "قواعد أسعار التأجير "

#. module: sale_renting
#: model:ir.ui.menu,name:sale_renting.menu_rental_reporting
msgid "Reporting"
msgstr "إعداد التقارير "

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.product_template_form_view_rental
msgid "Reservations"
msgstr "الحجوزات"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__qty_reserved
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_order__rental_status__pickup
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__rental_status__pickup
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__report_line_status__reserved
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
msgid "Reserved"
msgstr "محجوز"

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__rental_order_wizard__status__return
#: model:ir.ui.menu,name:sale_renting.rental_orders_return
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
msgid "Return"
msgstr "إرجاع "

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__return_date
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Return Date"
msgstr "تاريخ الإرجاع "

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_kanban
msgid "Return:"
msgstr "إرجاع: "

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__qty_returned
#: model:ir.model.fields,field_description:sale_renting.field_sale_order_line__qty_returned
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_order__rental_status__returned
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__rental_status__returned
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__report_line_status__returned
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_form
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "Returned"
msgstr "تم الإرجاع "

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/sale_order_line.py:0
msgid "Returned: %(date)s"
msgstr "تم الإرجاع: %(date)s"

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_sale_temporal_recurrence
msgid "Sale temporal Recurrence"
msgstr "التكرار المؤقت للمبيعات "

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_sale_order
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_report__state__sale
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__state__sale
msgid "Sales Order"
msgstr "أمر البيع"

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_sale_order_line
msgid "Sales Order Line"
msgstr "بند أمر المبيعات"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__team_id
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Sales Team"
msgstr "فريق المبيعات"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__user_id
msgid "Salesman"
msgstr "مندوب المبيعات"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__user_id
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_search_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Salesperson"
msgstr "مندوب المبيعات "

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "Sample Document"
msgstr "نموذج مستند "

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid "Save the product."
msgstr "حفظ المنتج. "

#. module: sale_renting
#: model:ir.ui.menu,name:sale_renting.menu_rental_schedule
msgid "Schedule"
msgstr "جدولة "

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/product_product.py:0
#: code:addons/sale_renting/models/product_template.py:0
#: model:ir.actions.act_window,name:sale_renting.action_rental_order_schedule
msgid "Scheduled Rentals"
msgstr "التأجيرات المجدولة "

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_product_pricing__product_variant_ids
msgid ""
"Select Variants of the Product for which this rule applies.Leave empty if "
"this rule applies for any variant of this template."
msgstr ""
"قم باختيار متغيرات المنتج التي تنطبق عليها هذه القاعدة. اتركه فارغاً إذا "
"كانت هذه القاعدة تنطبق على أي متغير لهذا القالب. "

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_product_pricing__product_template_id
msgid "Select products on which this pricing will be applied."
msgstr "قم بتحديد المنتجات التي سيتم تطبيق هذا التسعير عليها. "

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid "Select the rental dates and check the price."
msgstr "قم بتحديد تواريخ التأجير وتحقق من السعر. "

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid "Select your rental product."
msgstr "قم بتحديد منتج التأجير. "

#. module: sale_renting
#: model:ir.actions.act_window,name:sale_renting.action_rental_config_settings
#: model:ir.ui.menu,name:sale_renting.menu_rental_settings
msgid "Settings"
msgstr "الإعدادات"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_wizard_view_form
msgid "Some delay cost will be added to the sales order."
msgstr "سوف تتم إضافة بعض رسوم التأخير إلى أمر البيع. "

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_search_view
msgid "State"
msgstr "الحالة "

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard__status
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__status
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__state
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__state
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Status"
msgstr "الحالة"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.sale_rental_report_view_tree
msgid "Sum of Daily Amount"
msgstr "إجمالي المبلغ اليومي "

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.sale_rental_report_view_tree
msgid "Sum of Daily Ordered Qty"
msgstr "إجمالي الكمية المطلوبة يومياً "

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.sale_rental_report_view_tree
msgid "Sum of Daily Picked-Up Qty"
msgstr "إجمالي الكمية التي يتم استلامها يومياً "

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_sale_order__duration_days
msgid "The duration in days of the rental period."
msgstr "مدة التأجير بالأيام. "

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_sale_order__remaining_hours
msgid "The leftover hours of the rental period."
msgstr "الساعات المتبقية لفترة التأجير. "

#. module: sale_renting
#: model:ir.model.constraint,message:sale_renting.constraint_sale_temporal_recurrence_temporal_recurrence_duration
msgid "The pricing duration has to be greater or equal to 0."
msgstr "يجب أن تكون مدة التسعير أكبر من أو مساوية لـ 0. "

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_res_company__extra_product
msgid "The product is used to add the cost to the sales order"
msgstr "المنتج مستخدم لإضافة التكلفة إلى أمر البيع "

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_rental_order_wizard__is_late
#: model:ir.model.fields,help:sale_renting.field_sale_order__is_late
msgid "The products haven't been picked-up or returned in time"
msgstr "لم يتم استلام أو إرجاع المنتجات في الوقت المحدد "

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid "The rental configuration is available here."
msgstr "تهيئة التأجير متوفرة هنا. "

#. module: sale_renting
#: model:ir.model.constraint,message:sale_renting.constraint_sale_order_rental_period_coherence
msgid "The rental start date must be before the rental return date if any."
msgstr "يجب أن يكون تاريخ بدء التأجير قبل تاريخ الإرجاع. "

#. module: sale_renting
#: model_terms:ir.actions.act_window,help:sale_renting.rental_order_today_pickup_action
#: model_terms:ir.actions.act_window,help:sale_renting.rental_order_today_return_action
msgid "There isn't any scheduled pickup or return."
msgstr "لا توجد أي عمليات استلام أو إرجاع مجدولة. "

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_res_config_settings__extra_day
msgid ""
"This is the default extra cost per day set on newly created products.You can"
" change this value for existing products directly on the product itself."
msgstr ""

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_res_config_settings__extra_hour
msgid ""
"This is the default extra cost per hour set on newly created products.You "
"can change this value for existing products directly on the product itself."
msgstr ""

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_res_config_settings__extra_product
msgid "This product will be used to add fines in the Rental Order."
msgstr "سوف يتم استخدام هذا المنتج لإضافة غرامات في أمر التأجير. "

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
msgid ""
"This will update the unit price of all rental products based on the new "
"period."
msgstr ""
"سيؤدي ذلك إلى تحديث سعر الوحدة لكافة منتجات التأجير بناءً علىالفترة الزمنية "
"الجديدة. "

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.res_config_settings_view_form
msgid ""
"Those values are applied to any new rental product and can be changed on "
"product forms."
msgstr ""
"تلك القيم مطبقة على أي منتج تأجير جديد ويمكن تغييرها في استمارات المنتج. "

#. module: sale_renting
#: model:ir.ui.menu,name:sale_renting.rental_orders_today
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "To Do Today"
msgstr "ما يجب فعله اليوم "

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search_without_searchpanel
msgid "To Pickup"
msgstr "بانتظار الاستلام "

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search_without_searchpanel
msgid "To Return"
msgstr "بانتظار الإرجاع "

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_tree
msgid "Total Tax Included"
msgstr "الإجمالي شامل الضريبة "

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_temporal_recurrence__unit
msgid "Unit"
msgstr "الوحدة"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__product_uom
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__product_uom
msgid "Unit of Measure"
msgstr "وحدة القياس"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
msgid "Update Rental Prices"
msgstr "تحديث أسعار التأجير "

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_wizard_view_form
msgid "Validate"
msgstr "تصديق "

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/sale_order.py:0
msgid "Validate a pickup"
msgstr "تصديق عملية الاستلام "

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/sale_order.py:0
msgid "Validate a return"
msgstr "تصديق عملية الإرجاع "

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid "Validate the operation after checking the picked-up quantities."
msgstr "قم بتصديق العملية بعد التحقق من الكميات المستلمة. "

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.product_pricelist_view
msgid "Variants"
msgstr "المتغيرات "

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid ""
"Want to <b>rent products</b>? \n"
" Let's discover Odoo Rental App."
msgstr ""
"أترغب في <b>تأجير المنتجات</b>؟ \n"
" فلنستكشف تطبيق الإيجار لدى أودو. "

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/sale_order_recurrence.py:0
msgid "Week"
msgstr "الأسبوع"

#. module: sale_renting
#: model:sale.temporal.recurrence,name:sale_renting.recurrence_weekly
msgid "Weekly"
msgstr "أسبوعيًا"

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_temporal_recurrence__unit__week
msgid "Weeks"
msgstr "أسابيع"

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/sale_order_recurrence.py:0
msgid "Year"
msgstr "السنة"

#. module: sale_renting
#: model:sale.temporal.recurrence,name:sale_renting.recurrence_yearly
msgid "Yearly"
msgstr "سنويًا"

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_temporal_recurrence__unit__year
msgid "Years"
msgstr "سنوات"

#. module: sale_renting
#: model_terms:ir.actions.act_window,help:sale_renting.rental_product_template_action
msgid ""
"You can also create additional products or services to sell by checking *Can"
" be Sold* in the product form (e.g. insurance)."
msgstr ""
"بوسعك أيضاً إنشاء منتجات إضافية أو خدمات لبيعها عن طريق تحديد الخيار *يمكن "
"بيعها* في استمارة المنتج (مثال: الضمان). "

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/product_pricelist.py:0
msgid "You can not have a time-based rule for products that are not rentable."
msgstr "لا يمكن أن تكون لديك قاعدة مبنية على الوقت غير قابلة للتأجير. "

#. module: sale_renting
#: model_terms:ir.actions.act_window,help:sale_renting.rental_order_today_pickup_action
#: model_terms:ir.actions.act_window,help:sale_renting.rental_order_today_return_action
msgid ""
"You can search on a larger period using the filters here above\n"
"                <br>\n"
"                or create a new rental order."
msgstr ""
"يمكنك البحث في مدة زمنية أوسع باستخدام عوامل التصفية أعلاه\n"
"                <br>\n"
"                أو إنشاء أمر تأجير جديد. "

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/wizard/rental_processing.py:0
msgid "You can't return more than what's been picked-up."
msgstr "لا يمكنك إرجاع كمية أكبر من الكمية المستلمة. "

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/product_pricing.py:0
msgid ""
"You cannot have multiple pricing for the same variant, recurrence and "
"pricelist"
msgstr ""
"لا يمكن أن يكون لديك عدة أسعار لنفس متغير المنتج أو نفس التكرار أو نفس قائمة"
" الأسعار "

#. module: sale_renting
#: model:ir.model.constraint,message:sale_renting.constraint_sale_order_line_rental_stock_coherence
msgid "You cannot return more than what has been picked up."
msgstr "لا يمكنك إرجاع كمية أكبر من الكمية المستلمة. "

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid "You're done with your fist rental. Congratulations!"
msgstr "لقد أتممت عملية التأجير الأولى لك بنجاح. تهانينا! "

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/product_pricing.py:0
msgid "all variants"
msgstr "كافة المتغيرات "

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.res_config_settings_view_form
msgid "hours"
msgstr "ساعات"
