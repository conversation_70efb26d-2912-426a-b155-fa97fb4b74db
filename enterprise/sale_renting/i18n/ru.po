# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_renting
# 
# Translators:
# Wil O<PERSON>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 09:12+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/sale_order_line.py:0
msgid ""
"\n"
"%(from_date)s to %(to_date)s"
msgstr ""
"\n"
"%(from_date)s - %(to_date)s"

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/product_template.py:0
msgid "%(amount)s (fixed)"
msgstr "%(amount)s (исправлено)"

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/product_pricing.py:0
msgid "%(amount)s / %(duration)s"
msgstr "%(amount)s / %(duration)s"

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/product_template.py:0
#: code:addons/sale_renting/models/sale_order_recurrence.py:0
msgid "%(duration)s %(unit)s"
msgstr "%(duration)s %(unit)s"

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/product_product.py:0
#: code:addons/sale_renting/models/product_template.py:0
msgid "%s (Rental)"
msgstr "%s (Аренда)"

#. module: sale_renting
#: model:ir.actions.report,print_report_name:sale_renting.action_report_rental_saleorder
msgid ""
"(object.rental_status not in ('draft', 'sent') and 'Delivery or Return "
"Receipt - %s' %(object.name)) or 'Order - %s' % (object.name)"
msgstr ""
"(object.rental_status not in ('draft', 'sent') and 'Delivery or Return "
"Receipt - %s' %(object.name)) or 'Order - %s' %(object.name)"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "123 Main St"
msgstr "123 Главная улица"

#. module: sale_renting
#: model:sale.temporal.recurrence,name:sale_renting.recurrence_2_weeks
msgid "2 Weeks"
msgstr "2 недели"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "2023-08-01"
msgstr "2023-08-01"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "2023-08-10"
msgstr "2023-08-10"

#. module: sale_renting
#: model:sale.temporal.recurrence,name:sale_renting.recurrence_3_hours
msgid "3 Hours"
msgstr "3 часа"

#. module: sale_renting
#: model:sale.temporal.recurrence,name:sale_renting.recurrence_3_year
msgid "3 years"
msgstr "3 года"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "456 Other St"
msgstr "456 Другая улица"

#. module: sale_renting
#: model:sale.temporal.recurrence,name:sale_renting.recurrence5_year
msgid "5 Years"
msgstr "5 лет"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_kanban
msgid "<i class=\"fa fa-warning\"/> Late"
msgstr "<i class=\"fa fa-warning\"/> Поздно"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.product_product_form_view_rental_gantt
#: model_terms:ir.ui.view,arch_db:sale_renting.product_template_form_view_rental_gantt
msgid "<span class=\"o_stat_text\">in Rental</span>"
msgstr "<span class=\"o_stat_text\">в аренде</span>"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
msgid ""
"<span invisible=\"duration_days != 1\"> day </span>\n"
"                    <span invisible=\"duration_days in [0,1]\"> days </span>\n"
"                    <span invisible=\"duration_days == 0 or remaining_hours == 0\">and </span>"
msgstr ""
"<span invisible=\"duration_days != 1\"> день </span>\n"
"                    <span invisible=\"duration_days in [0,1]\"> дни </span>\n"
"                    <span invisible=\"duration_days == 0 or remaining_hours == 0\">и </span>"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
msgid ""
"<span invisible=\"remaining_hours != 1\"> hour </span>\n"
"                    <span invisible=\"remaining_hours in [0,1]\"> hours </span>"
msgstr ""
"<span invisible=\"remaining_hours != 1\"> час </span>\n"
"                    <span invisible=\"remaining_hours in [0,1]\"> часы </span>"

#. module: sale_renting
#: model_terms:web_tour.tour,rainbow_man_message:sale_renting.rental_tour
msgid "<span><b>Good job!</b> You went through all steps of this tour.</span>"
msgstr ""

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
msgid "<strong>Order # — </strong>"
msgstr "<strong>Заказ # — </strong>"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
msgid "<strong>Pickup  — </strong>"
msgstr "<strong>Прием — </strong>"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
msgid "<strong>Return — </strong>"
msgstr "<strong>Возврат — </strong>"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "<strong>Salesperson:</strong>"
msgstr "<strong>Менеджер:</strong>"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "<strong>Shipping Address:</strong>"
msgstr "<strong>Адрес доставки:</strong>"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
msgid "<strong>Status — </strong>"
msgstr "<strong>Статус — </strong>"

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/product_template.py:0
msgid "A rental combo product can only contain rental products."
msgstr ""

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_temporal_recurrence__active
msgid "Active"
msgstr "Активный"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.product_template_form_view_rental
msgid "Add a price"
msgstr "Добавить цену"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.res_config_settings_view_form
msgid "Additional costs for late returns"
msgstr "Дополнительные расходы за несвоевременный возврат"

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_product_product__rent_ok
#: model:ir.model.fields,help:sale_renting.field_product_template__rent_ok
#: model:ir.model.fields,help:sale_renting.field_sale_order_line__is_product_rentable
msgid "Allow renting of this product."
msgstr "Разрешите аренду этого продукта."

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.res_config_settings_view_form
msgid "Apply after"
msgstr "Применять после"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.res_config_settings_view_form
msgid "Ask customer to sign documents on the spot."
msgstr "Попросите клиента подписать документы на месте."

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid "At first, let's create some products to rent."
msgstr "Сначала давайте создадим несколько товаров для аренды."

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_form
msgid "Booked"
msgstr "Забронированно"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_wizard_view_form
msgid "Cancel"
msgstr "Отменить"

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_order__rental_status__cancel
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_report__state__cancel
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__rental_status__cancel
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__state__cancel
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
msgid "Cancelled"
msgstr "Отменен"

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid "Click here to create a new quotation."
msgstr "Нажмите здесь, чтобы создать новое Коммерческое Предложение. "

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid "Click here to register the pickup."
msgstr "Нажмите здесь, чтобы зарегистрировать получение."

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid "Click here to set up your first rental product."
msgstr "Нажмите здесь, чтобы создать свой первый продукт для проката."

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid "Click here to start filling the quotation."
msgstr "Нажмите здесь, чтобы начать заполнять коммерческое предложение. "

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__color
msgid "Color"
msgstr "Цвет"

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_res_company
msgid "Companies"
msgstr "Компании"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_pricing__company_id
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__company_id
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__company_id
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_search_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Company"
msgstr "Компания"

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_res_config_settings
msgid "Config Settings"
msgstr "Параметры конфигурации"

#. module: sale_renting
#: model:ir.ui.menu,name:sale_renting.menu_rental_config
msgid "Configuration"
msgstr "Конфигурация"

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid "Confirm the order when the customer agrees with the terms."
msgstr "Подтвердить заказ, когда клиент согласится с условиями."

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid "Confirm the returned quantities and hit Validate."
msgstr "Проверьте возвращаемое количество и нажмите Подтвердить."

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_search_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Confirmed Orders"
msgstr "Подтвержденные заказы"

#. module: sale_renting
#: model:ir.actions.act_window,name:sale_renting.action_create_rental_order
msgid "Create Rental Orders"
msgstr "Создание заказов на аренду"

#. module: sale_renting
#: model_terms:ir.actions.act_window,help:sale_renting.sale_temporal_recurrence_action
msgid "Create a new period"
msgstr "Создайте новый период"

#. module: sale_renting
#: model_terms:ir.actions.act_window,help:sale_renting.rental_order_action
msgid "Create a new quotation, the first step of a new rental!"
msgstr ""
"Создайте новое коммерческое предложение - первый шаг к новому проекту "
"проката! "

#. module: sale_renting
#: model_terms:ir.actions.act_window,help:sale_renting.product_pricing_action
msgid "Create a new recurrence"
msgstr "Создайте новое повторение"

#. module: sale_renting
#: model_terms:ir.actions.act_window,help:sale_renting.action_rental_report
msgid "Create a new rental order"
msgstr "Создайте новый заказ на аренду"

#. module: sale_renting
#: model_terms:ir.actions.act_window,help:sale_renting.rental_product_template_action
msgid "Create a new rental product!"
msgstr "Создайте новый продукт для аренды!"

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid "Create or select a customer here."
msgstr "Создайте или выберите клиента здесь."

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order__is_rental_order
msgid "Created In App Rental"
msgstr "Создано в приложении Аренда"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_pricing__create_uid
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard__create_uid
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__create_uid
#: model:ir.model.fields,field_description:sale_renting.field_sale_temporal_recurrence__create_uid
msgid "Created by"
msgstr "Создано"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_pricing__create_date
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard__create_date
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__create_date
#: model:ir.model.fields,field_description:sale_renting.field_sale_temporal_recurrence__create_date
msgid "Created on"
msgstr "Создано"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_pricing__currency_id
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__currency_id
msgid "Currency"
msgstr "Валюта"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_product__product_pricing_ids
#: model:ir.model.fields,field_description:sale_renting.field_product_template__product_pricing_ids
msgid "Custom Pricings"
msgstr "Индивидуальные цены"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__partner_id
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__partner_id
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_search_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Customer"
msgstr "Клиент"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__country_id
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Customer Country"
msgstr "Страна клиента"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__commercial_partner_id
msgid "Customer Entity"
msgstr "Субъект клиента"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__card_name
msgid "Customer Name"
msgstr "Имя Клиента"

#. module: sale_renting
#: model:ir.ui.menu,name:sale_renting.menu_orders_customers
msgid "Customers"
msgstr "Клиенты"

#. module: sale_renting
#: model:sale.temporal.recurrence,name:sale_renting.recurrence_daily
msgid "Daily"
msgstr "Ежедневно"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__price
msgid "Daily Amount"
msgstr "Суточная сумма"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__quantity
msgid "Daily Ordered Qty"
msgstr "Ежедневно заказываемое количество"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__qty_delivered
msgid "Daily Picked-Up Qty"
msgstr "Ежедневно забираемое количество"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__qty_returned
msgid "Daily Returned Qty"
msgstr "Ежедневно возвращаемое количество"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__date
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_search_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Date"
msgstr "Дата"

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/sale_order_recurrence.py:0
msgid "Day"
msgstr "День"

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_temporal_recurrence__unit__day
msgid "Days"
msgstr "Дней"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.res_config_settings_view_form
msgid "Default Delay Costs"
msgstr "Расходы на задержку по умолчанию"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_res_config_settings__extra_product
msgid "Delay Product"
msgstr "Задержать продукт"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_pricing__description
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__description
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "Description"
msgstr "Описание"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_res_config_settings__module_sale_renting_sign
msgid "Digital Documents"
msgstr "Цифровые документы"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_pricing__display_name
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard__display_name
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__display_name
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__display_name
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__display_name
#: model:ir.model.fields,field_description:sale_renting.field_sale_temporal_recurrence__display_name
msgid "Display Name"
msgstr "Отображаемое имя"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_temporal_recurrence__duration
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
msgid "Duration"
msgstr "Продолжительность"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_pricing__name
#: model:ir.model.fields,field_description:sale_renting.field_sale_temporal_recurrence__duration_display
msgid "Duration Display"
msgstr "Отображение продолжительности"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order__duration_days
msgid "Duration in days"
msgstr "Длительность в днях"

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid "Enter the product name."
msgstr "Введите название продукта."

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "Expected Return"
msgstr "Ожидаемая доходность"

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/sale_order_line.py:0
msgid "Expected: %(date)s"
msgstr "Ожидается: %(date)s"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_product__extra_daily
#: model:ir.model.fields,field_description:sale_renting.field_product_template__extra_daily
msgid "Extra Day"
msgstr "Дополнительный день"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_product__extra_hourly
#: model:ir.model.fields,field_description:sale_renting.field_product_template__extra_hourly
msgid "Extra Hour"
msgstr "Дополнительный час"

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_product_product__extra_daily
#: model:ir.model.fields,help:sale_renting.field_product_template__extra_daily
msgid "Fine by day overdue"
msgstr "Штраф по дням просрочки"

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_product_product__extra_hourly
#: model:ir.model.fields,help:sale_renting.field_product_template__extra_hourly
msgid "Fine by hour overdue"
msgstr "Штраф по часам просрочки"

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_product_product__display_price
#: model:ir.model.fields,help:sale_renting.field_product_template__display_price
msgid "First rental pricing of the product"
msgstr "Первая арендная цена на продукт"

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid "Go to the orders menu."
msgstr "Перейдите в меню заказов."

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_search_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Group By"
msgstr "Группировать по"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order__show_update_duration
msgid "Has Duration Changed"
msgstr "Изменилась ли продолжительность"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order__has_pickable_lines
msgid "Has Pickable Lines"
msgstr "Имеет получаемые линии"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order__has_rented_products
msgid "Has Rented Products"
msgstr "Сдавал продукты в аренду"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order__has_returnable_lines
msgid "Has Returnable Lines"
msgstr "Имеет возвратные линии"

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/sale_order_recurrence.py:0
msgid "Hour"
msgstr "Час"

#. module: sale_renting
#: model:sale.temporal.recurrence,name:sale_renting.recurrence_hourly
msgid "Hourly"
msgstr "Каждый час"

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_temporal_recurrence__unit__hour
msgid "Hours"
msgstr "Часов"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_pricing__id
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard__id
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__id
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__id
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__id
#: model:ir.model.fields,field_description:sale_renting.field_sale_temporal_recurrence__id
msgid "ID"
msgstr "ID"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "Invoicing Address:"
msgstr "Выставить счет в адрес:"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "Invoicing and Shipping Address:"
msgstr "Адрес для выставления счета и доставки:"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__late
msgid "Is Late"
msgstr "Опаздывает"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order_line__is_rental
msgid "Is Rental"
msgstr "Аренда"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard__is_late
#: model:ir.model.fields,field_description:sale_renting.field_sale_order__is_late
msgid "Is overdue"
msgstr "Просрочен"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "Jane Doe"
msgstr "Иван Петров"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "John Doe"
msgstr "Иван Иванов"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_pricing__write_uid
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard__write_uid
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__write_uid
#: model:ir.model.fields,field_description:sale_renting.field_sale_temporal_recurrence__write_uid
msgid "Last Updated by"
msgstr "Последнее обновление"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_pricing__write_date
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard__write_date
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__write_date
#: model:ir.model.fields,field_description:sale_renting.field_sale_temporal_recurrence__write_date
msgid "Last Updated on"
msgstr "Последнее обновление"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
msgid "Late"
msgstr "Просрочено"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_form
msgid "Late Pickup"
msgstr "Поздний вывоз"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_form
msgid "Late Return"
msgstr "Поздний возврат"

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid "Let's now create an order."
msgstr "Теперь давайте создадим заказ."

#. module: sale_renting
#: model:product.template,name:sale_renting.rental_product_2_product_template
msgid "Meeting Room"
msgstr "Переговорная"

#. module: sale_renting
#: model:ir.model.constraint,message:sale_renting.constraint_res_company_min_extra_hour
msgid "Minimal delay time before applying fines has to be positive."
msgstr ""
"Минимальное время задержки перед применением штрафов должно быть "
"положительным."

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_res_company__min_extra_hour
#: model:ir.model.fields,field_description:sale_renting.field_res_config_settings__min_extra_hour
msgid "Minimum delay time before applying fines."
msgstr "Минимальное время задержки перед применением штрафов."

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_sale_temporal_recurrence__duration
msgid ""
"Minimum duration before this rule is applied. If set to 0, it represents a "
"fixedrental price."
msgstr ""
"Минимальная продолжительность до применения данного правила. Если "
"установлено значение 0, это означает фиксированную цену аренды."

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/sale_order_recurrence.py:0
msgid "Month"
msgstr "Месяц"

#. module: sale_renting
#: model:sale.temporal.recurrence,name:sale_renting.recurrence_monthly
msgid "Monthly"
msgstr "Ежемесячно"

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_temporal_recurrence__unit__month
msgid "Months"
msgstr "Месяцев"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "My Orders"
msgstr "Мои заказы"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_temporal_recurrence__name
msgid "Name"
msgstr "Имя"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order__next_action_date
msgid "Next Action"
msgstr "Следующее действие"

#. module: sale_renting
#: model_terms:ir.actions.act_window,help:sale_renting.action_rental_report
msgid "No data yet!"
msgstr "Пока нет данных!"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_form
msgid "Ok"
msgstr "Ок"

#. module: sale_renting
#: model_terms:ir.actions.act_window,help:sale_renting.rental_order_action
msgid ""
"Once the quotation is confirmed, it becomes a rental order.<br> You will be "
"able to create an invoice and collect the payment."
msgstr ""
"Как только предложение подтверждено, оно становится заказом на аренду.<br> "
"Вы сможете выставить счет и получить оплату."

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid "Once the rental is done, you can register the return."
msgstr ""
"Как только аренда будет завершена, вы сможете зарегистрировать возврат."

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard__order_id
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
msgid "Order"
msgstr "Заказ"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__order_id
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__order_id
msgid "Order #"
msgstr "Заказ #"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__order_date
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Order Date"
msgstr "Дата заказа"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__order_line_id
msgid "Order Line"
msgstr "Позиция заказа "

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__name
msgid "Order Reference"
msgstr "Ссылка на заказ"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__order_line_id
msgid "Order line #"
msgstr "Позиция заказа #"

#. module: sale_renting
#: model:ir.ui.menu,name:sale_renting.rental_order_menu
#: model:ir.ui.menu,name:sale_renting.rental_orders_all
msgid "Orders"
msgstr "Заказы"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_res_company__extra_day
#: model:ir.model.fields,field_description:sale_renting.field_res_config_settings__extra_day
msgid "Per Day"
msgstr "В день"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_res_company__extra_hour
#: model:ir.model.fields,field_description:sale_renting.field_res_config_settings__extra_hour
msgid "Per Hour"
msgstr "В час"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.product_pricelist_view
#: model_terms:ir.ui.view,arch_db:sale_renting.product_template_form_view_rental
msgid "Period"
msgstr "Период"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.sale_temporal_recurrence_view_form
#: model_terms:ir.ui.view,arch_db:sale_renting.sale_temporal_recurrence_view_tree
msgid "Periodicity"
msgstr "Периодичность"

#. module: sale_renting
#: model:ir.actions.act_window,name:sale_renting.sale_temporal_recurrence_action
msgid "Periods"
msgstr "Периоды"

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_rental_order_wizard
msgid "Pick-up/Return products"
msgstr "Прием/возврат продукции"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
msgid "Picked-Up"
msgstr "Принят"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__qty_delivered
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_form
msgid "Picked-up"
msgstr "Принят"

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_order__rental_status__return
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__rental_status__return
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__report_line_status__pickedup
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "Pickedup"
msgstr "Подобранный"

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__rental_order_wizard__status__pickup
#: model:ir.ui.menu,name:sale_renting.rental_orders_pickup
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
msgid "Pickup"
msgstr "Самовывоз"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__pickup_date
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "Pickup Date"
msgstr "Дата приема"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "Pickup Receipt #"
msgstr "Квитанция о приеме #"

#. module: sale_renting
#: model:ir.actions.report,name:sale_renting.action_report_rental_saleorder
msgid "Pickup and Return Receipt"
msgstr "Квитанция о приеме и возврате"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order_line__reservation_begin
msgid "Pickup date - padding time"
msgstr "Дата приема - время задержки"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_kanban
msgid "Pickup:"
msgstr "Прием:"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_pricing__price
msgid "Price"
msgstr "Цена"

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_product_pricelist
#: model:ir.model.fields,field_description:sale_renting.field_product_pricing__pricelist_id
msgid "Pricelist"
msgstr "Прайс-лист"

#. module: sale_renting
#: model:ir.actions.act_window,name:sale_renting.product_pricing_action
#: model_terms:ir.ui.view,arch_db:sale_renting.product_pricing_tree
msgid "Prices"
msgstr "Цены"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.product_template_form_view_rental
msgid "Pricing"
msgstr "Стоимость"

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_product_pricing
msgid "Pricing rule of rental products"
msgstr "Правила ценообразования на продукты проката"

#. module: sale_renting
#: model:product.template,name:sale_renting.rental_product_3_product_template
msgid "Printer"
msgstr "Принтер"

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_product_template
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__product_id
#: model:ir.model.fields,field_description:sale_renting.field_res_company__extra_product
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__product_id
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__product_id
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_search_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.res_config_settings_view_form
msgid "Product"
msgstr "Товар"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "Product A"
msgstr "Продукт A"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__categ_id
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__categ_id
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_search_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Product Category"
msgstr "Категория товара"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__product_name
msgid "Product Reference"
msgstr "Ссылка на продукт"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_pricing__product_template_id
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__product_tmpl_id
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__product_tmpl_id
msgid "Product Template"
msgstr "Шаблон Продукта"

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_product_product
#: model:ir.model.fields,field_description:sale_renting.field_product_pricing__product_variant_ids
msgid "Product Variant"
msgstr "Вариант продукта"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.res_config_settings_view_form
msgid "Product to charge extra time"
msgstr "Продукт для оплаты дополнительного времени"

#. module: sale_renting
#: model:ir.actions.act_window,name:sale_renting.rental_product_template_action
#: model:ir.ui.menu,name:sale_renting.menu_rental_products
#: model_terms:ir.ui.view,arch_db:sale_renting.product_pricelist_view
msgid "Products"
msgstr "Товары"

#. module: sale_renting
#: model:product.template,name:sale_renting.rental_product_1_product_template
msgid "Projector"
msgstr "Проектор"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__product_uom_qty
msgid "Qty Ordered"
msgstr "К-во заказанных"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__qty_delivered
msgid "Qty Picked-Up"
msgstr "К-во принятых"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__qty_returned
msgid "Qty Returned"
msgstr "К-во возвращенных"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_product__qty_in_rent
#: model:ir.model.fields,field_description:sale_renting.field_product_template__qty_in_rent
msgid "Quantity currently in rent"
msgstr "Количество, находящееся в аренде в данный момент"

#. module: sale_renting
#: model:sale.temporal.recurrence,name:sale_renting.recurrence_quarterly
msgid "Quarterly"
msgstr "Ежеквартально"

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_order__rental_status__draft
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_report__state__draft
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__rental_status__draft
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__state__draft
msgid "Quotation"
msgstr "Коммерческое предложение"

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_order__rental_status__sent
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_report__state__sent
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__rental_status__sent
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__state__sent
msgid "Quotation Sent"
msgstr "Коммерческое предложение отправлено"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_search_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Quotations"
msgstr "Коммерческие предложения"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
msgid "Recompute all prices based on this duration"
msgstr "Пересчитайте все цены на основе этой продолжительности"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order__remaining_hours
msgid "Remaining duration in hours"
msgstr "Оставшаяся продолжительность в часах"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_product__rent_ok
#: model:ir.model.fields,field_description:sale_renting.field_product_template__rent_ok
#: model:ir.model.fields,field_description:sale_renting.field_sale_order_line__is_product_rentable
#: model:ir.ui.menu,name:sale_renting.rental_menu_root
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_product_template_search_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
#: model_terms:ir.ui.view,arch_db:sale_renting.res_config_settings_view_form
msgid "Rental"
msgstr "Аренда"

#. module: sale_renting
#: model:ir.actions.act_window,name:sale_renting.action_rental_report
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_graph_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_pivot_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_search_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.sale_rental_report_view_tree
msgid "Rental Analysis"
msgstr "Анализ аренды"

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_sale_rental_report
msgid "Rental Analysis Report"
msgstr "Отчет об анализе аренды"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_form
msgid "Rental Order"
msgstr "Заказ аренды"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__rental_order_wizard_id
msgid "Rental Order Wizard"
msgstr "Мастер заказа аренды"

#. module: sale_renting
#: model:ir.actions.act_window,name:sale_renting.rental_order_action
#: model:ir.actions.act_window,name:sale_renting.rental_order_today_pickup_action
#: model:ir.actions.act_window,name:sale_renting.rental_order_today_return_action
msgid "Rental Orders"
msgstr "Заказы на аренду"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.product_pricelist_view
msgid "Rental Pricelist Rules"
msgstr "Правила арендного прейскуранта"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order__rental_return_date
#: model:ir.model.fields,field_description:sale_renting.field_sale_order_line__return_date
msgid "Rental Return Date"
msgstr "Дата возврата аренды"

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_sale_rental_schedule
msgid "Rental Schedule"
msgstr "График аренды"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order__rental_start_date
#: model:ir.model.fields,field_description:sale_renting.field_sale_order_line__start_date
msgid "Rental Start Date"
msgstr "Дата начала аренды"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order__rental_status
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__rental_status
msgid "Rental Status"
msgstr "Статус аренды"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__report_line_status
msgid "Rental Status (advanced)"
msgstr "Статус аренды (расширенный)"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard__rental_wizard_line_ids
msgid "Rental Wizard Line"
msgstr "Строка мастера по аренде"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
msgid "Rental period"
msgstr "Срок аренды"

#. module: sale_renting
#: model:ir.ui.menu,name:sale_renting.menu_sale_renting_periods
msgid "Rental periods"
msgstr "Сроки аренды"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_product__display_price
#: model:ir.model.fields,field_description:sale_renting.field_product_template__display_price
msgid "Rental price"
msgstr "Стоимость дома"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.product_template_form_view_rental
msgid "Rental prices"
msgstr "Цены на аренду"

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/sale_order.py:0
msgid "Rental prices have been recomputed with the new period."
msgstr "Цены на аренду были пересчитаны с учетом нового периода."

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.product_pricelist_view
msgid "Rental rules"
msgstr "Правила аренды"

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_rental_order_wizard_line
msgid "RentalOrderLine transient representation"
msgstr "Временное представление строки заказа на аренду"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
msgid "Rentals"
msgstr "Аренды"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_pricing__recurrence_id
msgid "Renting Period"
msgstr "Период аренды"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_pricelist__product_pricing_ids
msgid "Renting Price Rules"
msgstr "Правила ценообразования при аренде"

#. module: sale_renting
#: model:ir.ui.menu,name:sale_renting.menu_rental_reporting
msgid "Reporting"
msgstr "Отчет"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.product_template_form_view_rental
msgid "Reservations"
msgstr "Бронирования"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__qty_reserved
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_order__rental_status__pickup
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__rental_status__pickup
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__report_line_status__reserved
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
msgid "Reserved"
msgstr "Зарезервировано"

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__rental_order_wizard__status__return
#: model:ir.ui.menu,name:sale_renting.rental_orders_return
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
msgid "Return"
msgstr "Возврат"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__return_date
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Return Date"
msgstr "Дата возврата"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_kanban
msgid "Return:"
msgstr "Возврат:"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__qty_returned
#: model:ir.model.fields,field_description:sale_renting.field_sale_order_line__qty_returned
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_order__rental_status__returned
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__rental_status__returned
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__report_line_status__returned
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_form
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "Returned"
msgstr "Возвращено"

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/sale_order_line.py:0
msgid "Returned: %(date)s"
msgstr "Возвращено: %(date)s"

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_sale_temporal_recurrence
msgid "Sale temporal Recurrence"
msgstr "Временное повторение продажи"

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_sale_order
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_report__state__sale
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__state__sale
msgid "Sales Order"
msgstr "Заказ на продажу"

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_sale_order_line
msgid "Sales Order Line"
msgstr "Позиция заказа на продажу"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__team_id
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Sales Team"
msgstr "Команда продаж"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__user_id
msgid "Salesman"
msgstr "Продавец"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__user_id
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_search_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Salesperson"
msgstr "Продавец"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "Sample Document"
msgstr "Образец документа"

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid "Save the product."
msgstr "Сохраните продукт."

#. module: sale_renting
#: model:ir.ui.menu,name:sale_renting.menu_rental_schedule
msgid "Schedule"
msgstr "Запланировать встречу"

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/product_product.py:0
#: code:addons/sale_renting/models/product_template.py:0
#: model:ir.actions.act_window,name:sale_renting.action_rental_order_schedule
msgid "Scheduled Rentals"
msgstr "Запланированная аренда"

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_product_pricing__product_variant_ids
msgid ""
"Select Variants of the Product for which this rule applies.Leave empty if "
"this rule applies for any variant of this template."
msgstr ""
"Выберите Варианты продукта, для которых применяется это правило. Оставьте "
"пустым, если правило применяется для любого варианта этого шаблона."

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_product_pricing__product_template_id
msgid "Select products on which this pricing will be applied."
msgstr "Выберите продукты, к которым будет применяться данная цена."

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid "Select the rental dates and check the price."
msgstr "Выберите даты аренды и проверьте цену."

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid "Select your rental product."
msgstr "Выберите продукт для аренды."

#. module: sale_renting
#: model:ir.actions.act_window,name:sale_renting.action_rental_config_settings
#: model:ir.ui.menu,name:sale_renting.menu_rental_settings
msgid "Settings"
msgstr "Настройки"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_wizard_view_form
msgid "Some delay cost will be added to the sales order."
msgstr "Некоторая стоимость задержки будет добавлена к заказу на продажу."

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_search_view
msgid "State"
msgstr "Область"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard__status
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__status
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__state
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__state
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Status"
msgstr "Статус"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.sale_rental_report_view_tree
msgid "Sum of Daily Amount"
msgstr "Сумма ежедневного количества"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.sale_rental_report_view_tree
msgid "Sum of Daily Ordered Qty"
msgstr "Сумма ежедневного принятого к-ва"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.sale_rental_report_view_tree
msgid "Sum of Daily Picked-Up Qty"
msgstr "Сумма ежедневно забираемого количества"

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_sale_order__duration_days
msgid "The duration in days of the rental period."
msgstr "Продолжительность периода аренды в днях."

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_sale_order__remaining_hours
msgid "The leftover hours of the rental period."
msgstr "Оставшиеся часы периода аренды."

#. module: sale_renting
#: model:ir.model.constraint,message:sale_renting.constraint_sale_temporal_recurrence_temporal_recurrence_duration
msgid "The pricing duration has to be greater or equal to 0."
msgstr "Продолжительность ценообразования должна быть больше или равна 0."

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_res_company__extra_product
msgid "The product is used to add the cost to the sales order"
msgstr "Продукт используется для добавления стоимости к заказу на продажу"

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_rental_order_wizard__is_late
#: model:ir.model.fields,help:sale_renting.field_sale_order__is_late
msgid "The products haven't been picked-up or returned in time"
msgstr "Продукция не была вовремя забрана или возвращена"

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid "The rental configuration is available here."
msgstr "Конфигурация аренды доступна здесь."

#. module: sale_renting
#: model:ir.model.constraint,message:sale_renting.constraint_sale_order_rental_period_coherence
msgid "The rental start date must be before the rental return date if any."
msgstr ""
"Дата начала аренды должна быть раньше даты возврата аренды, если таковая "
"имеется."

#. module: sale_renting
#: model_terms:ir.actions.act_window,help:sale_renting.rental_order_today_pickup_action
#: model_terms:ir.actions.act_window,help:sale_renting.rental_order_today_return_action
msgid "There isn't any scheduled pickup or return."
msgstr "Нет никаких запланированных получений или возвратов."

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_res_config_settings__extra_day
msgid ""
"This is the default extra cost per day set on newly created products.You can"
" change this value for existing products directly on the product itself."
msgstr ""

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_res_config_settings__extra_hour
msgid ""
"This is the default extra cost per hour set on newly created products.You "
"can change this value for existing products directly on the product itself."
msgstr ""

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_res_config_settings__extra_product
msgid "This product will be used to add fines in the Rental Order."
msgstr ""
"Этот продукт будет использоваться для добавления штрафов в заказ на аренду."

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
msgid ""
"This will update the unit price of all rental products based on the new "
"period."
msgstr ""
"Это обновит стоимость единицы всех продуктов аренды на основе нового "
"периода."

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.res_config_settings_view_form
msgid ""
"Those values are applied to any new rental product and can be changed on "
"product forms."
msgstr ""
"Эти значения применяются к любому новому продукту для аренды и могут быть "
"изменены в формах продукта."

#. module: sale_renting
#: model:ir.ui.menu,name:sale_renting.rental_orders_today
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "To Do Today"
msgstr "Сделать сегодня"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search_without_searchpanel
msgid "To Pickup"
msgstr "К получению"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search_without_searchpanel
msgid "To Return"
msgstr "К возврату"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_tree
msgid "Total Tax Included"
msgstr "Общий налог включен"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_temporal_recurrence__unit
msgid "Unit"
msgstr "Единица"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__product_uom
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__product_uom
msgid "Unit of Measure"
msgstr "Ед. изм"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
msgid "Update Rental Prices"
msgstr "Обновление цен на аренду"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_wizard_view_form
msgid "Validate"
msgstr "Подтвердить"

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/sale_order.py:0
msgid "Validate a pickup"
msgstr "Проверить прием"

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/sale_order.py:0
msgid "Validate a return"
msgstr "Проверить возврат"

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid "Validate the operation after checking the picked-up quantities."
msgstr "Подтвердите операцию, проверив принятое количество."

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.product_pricelist_view
msgid "Variants"
msgstr "Варианты"

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid ""
"Want to <b>rent products</b>? \n"
" Let's discover Odoo Rental App."
msgstr ""
"Хотите <b>сдавать продукты в аренду</b>? \n"
" Давайте познакомимся с приложением Аренда."

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/sale_order_recurrence.py:0
msgid "Week"
msgstr "Неделя"

#. module: sale_renting
#: model:sale.temporal.recurrence,name:sale_renting.recurrence_weekly
msgid "Weekly"
msgstr "Еженедельно"

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_temporal_recurrence__unit__week
msgid "Weeks"
msgstr "Недель"

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/sale_order_recurrence.py:0
msgid "Year"
msgstr "Год"

#. module: sale_renting
#: model:sale.temporal.recurrence,name:sale_renting.recurrence_yearly
msgid "Yearly"
msgstr "Ежегодно"

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_temporal_recurrence__unit__year
msgid "Years"
msgstr "Лет"

#. module: sale_renting
#: model_terms:ir.actions.act_window,help:sale_renting.rental_product_template_action
msgid ""
"You can also create additional products or services to sell by checking *Can"
" be Sold* in the product form (e.g. insurance)."
msgstr ""
"Вы также можете создать дополнительные продукты или услуги для продажи, "
"отметив *Можно продавать* в форме продукта (например, страхование)."

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/product_pricelist.py:0
msgid "You can not have a time-based rule for products that are not rentable."
msgstr ""
"Вы не можете установить правило, основанное на времени, для продуктов, "
"которые не подлежат аренде."

#. module: sale_renting
#: model_terms:ir.actions.act_window,help:sale_renting.rental_order_today_pickup_action
#: model_terms:ir.actions.act_window,help:sale_renting.rental_order_today_return_action
msgid ""
"You can search on a larger period using the filters here above\n"
"                <br>\n"
"                or create a new rental order."
msgstr ""
"Вы можете выполнить поиск по большему периоду, используя фильтры, приведенные здесь выше\n"
"               <br>\n"
"                или создать новый заказ на аренду."

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/wizard/rental_processing.py:0
msgid "You can't return more than what's been picked-up."
msgstr "Вы не можете вернуть больше того, что забрали."

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/product_pricing.py:0
msgid ""
"You cannot have multiple pricing for the same variant, recurrence and "
"pricelist"
msgstr ""
"Нельзя установить несколько цен для одного и того же варианта, повтора и "
"прейскуранта"

#. module: sale_renting
#: model:ir.model.constraint,message:sale_renting.constraint_sale_order_line_rental_stock_coherence
msgid "You cannot return more than what has been picked up."
msgstr "Вы не можете вернуть больше того, что забрали."

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid "You're done with your fist rental. Congratulations!"
msgstr "Вы закончили свой первый прокат. Поздравляем!"

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/product_pricing.py:0
msgid "all variants"
msgstr "все варианты"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.res_config_settings_view_form
msgid "hours"
msgstr "часов"
