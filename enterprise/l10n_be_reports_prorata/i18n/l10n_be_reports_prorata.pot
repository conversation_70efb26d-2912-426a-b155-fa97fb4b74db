# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_be_reports_prorata
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-07-08 08:41+0000\n"
"PO-Revision-Date: 2024-07-08 08:41+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_be_reports_prorata
#: model:ir.model.fields,field_description:l10n_be_reports_prorata.field_l10n_be_reports_periodic_vat_xml_export__special_prorata_deduction
msgid "% Special pro rata deduction"
msgstr ""

#. module: l10n_be_reports_prorata
#: model_terms:ir.ui.view,arch_db:l10n_be_reports_prorata.view_account_financial_report_export
msgid "<span>Actual Use Incoming Transactions</span>"
msgstr ""

#. module: l10n_be_reports_prorata
#: model:ir.model.fields,field_description:l10n_be_reports_prorata.field_l10n_be_reports_periodic_vat_xml_export__prorata_at_0
msgid "Actual Use at 0%"
msgstr ""

#. module: l10n_be_reports_prorata
#: model:ir.model.fields,field_description:l10n_be_reports_prorata.field_l10n_be_reports_periodic_vat_xml_export__prorata_at_100
msgid "Actual Use at 100%"
msgstr ""

#. module: l10n_be_reports_prorata
#: model:ir.model,name:l10n_be_reports_prorata.model_l10n_be_reports_periodic_vat_xml_export
msgid "Belgian Periodic VAT Report Export Wizard"
msgstr ""

#. module: l10n_be_reports_prorata
#: model:ir.model,name:l10n_be_reports_prorata.model_l10n_be_tax_report_handler
msgid "Belgian Tax Report Custom Prorata Handler"
msgstr ""

#. module: l10n_be_reports_prorata
#: model:ir.model.fields,field_description:l10n_be_reports_prorata.field_l10n_be_reports_periodic_vat_xml_export__prorata
msgid "Definitive pro rata"
msgstr ""

#. module: l10n_be_reports_prorata
#. odoo-python
#: code:addons/l10n_be_reports_prorata/wizard/vat_report_export.py:0
#, python-format
msgid "Definitive prorata must be an integer between 1 and 100"
msgstr ""

#. module: l10n_be_reports_prorata
#: model:ir.model.fields,field_description:l10n_be_reports_prorata.field_l10n_be_reports_periodic_vat_xml_export__submit_more
msgid "I want to submit more than 5 specific pro rata"
msgstr ""

#. module: l10n_be_reports_prorata
#. odoo-python
#: code:addons/l10n_be_reports_prorata/wizard/vat_report_export.py:0
#, python-format
msgid "Please enter a valid pro rata year (after 2000)"
msgstr ""

#. module: l10n_be_reports_prorata
#: model:ir.model.fields,field_description:l10n_be_reports_prorata.field_l10n_be_reports_periodic_vat_xml_export__is_prorata_necessary
msgid "Prorata"
msgstr ""

#. module: l10n_be_reports_prorata
#: model:ir.model.fields,field_description:l10n_be_reports_prorata.field_l10n_be_reports_periodic_vat_xml_export__prorata_year
msgid "Prorata Year"
msgstr ""

#. module: l10n_be_reports_prorata
#: model:ir.model.fields,field_description:l10n_be_reports_prorata.field_l10n_be_reports_periodic_vat_xml_export__show_prorata
msgid "Show Prorata"
msgstr ""

#. module: l10n_be_reports_prorata
#: model:ir.model.fields,field_description:l10n_be_reports_prorata.field_l10n_be_reports_periodic_vat_xml_export__special_prorata_1
msgid "Special pro rata 1"
msgstr ""

#. module: l10n_be_reports_prorata
#: model:ir.model.fields,field_description:l10n_be_reports_prorata.field_l10n_be_reports_periodic_vat_xml_export__special_prorata_2
msgid "Special pro rata 2"
msgstr ""

#. module: l10n_be_reports_prorata
#: model:ir.model.fields,field_description:l10n_be_reports_prorata.field_l10n_be_reports_periodic_vat_xml_export__special_prorata_3
msgid "Special pro rata 3"
msgstr ""

#. module: l10n_be_reports_prorata
#: model:ir.model.fields,field_description:l10n_be_reports_prorata.field_l10n_be_reports_periodic_vat_xml_export__special_prorata_4
msgid "Special pro rata 4"
msgstr ""

#. module: l10n_be_reports_prorata
#: model:ir.model.fields,field_description:l10n_be_reports_prorata.field_l10n_be_reports_periodic_vat_xml_export__special_prorata_5
msgid "Special pro rata 5"
msgstr ""

#. module: l10n_be_reports_prorata
#. odoo-python
#: code:addons/l10n_be_reports_prorata/wizard/vat_report_export.py:0
#, python-format
msgid ""
"The percentage of uses and special pro rata must have values between 0 and "
"100"
msgstr ""

#. module: l10n_be_reports_prorata
#. odoo-python
#: code:addons/l10n_be_reports_prorata/wizard/vat_report_export.py:0
#, python-format
msgid "The sum of the prorata uses must be 100%"
msgstr ""

#. module: l10n_be_reports_prorata
#: model_terms:ir.ui.view,arch_db:l10n_be_reports_prorata.vat_export_prorata
msgid "true"
msgstr ""
