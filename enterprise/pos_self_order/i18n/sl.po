# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_self_order
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Slovenian (https://app.transifex.com/odoo/teams/41243/sl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sl\n"
"Plural-Forms: nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n%100==4 ? 2 : 3);\n"

#. module: pos_self_order
#: model:ir.actions.report,print_report_name:pos_self_order.report_self_order_qr_codes_page
msgid "\"QR codes\""
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
msgid ""
"%s is not available anymore, it has thus been removed from your order. "
"Please review your order and validate it again."
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/store/order_change_receipt_template.xml:0
msgid "/ Tracker number:"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid ""
"<br/>\n"
"                    URL:"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.pos_self_order_product_pos_category_form_view
msgid "<sup title=\"Only works for kiosk and mobile\">?</sup>"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_pos_config__self_ordering_default_user_id
#: model:ir.model.fields,help:pos_self_order.field_res_config_settings__pos_self_ordering_default_user_id
msgid ""
"Access rights of this user will be used when visiting self order website "
"when no session is open."
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_config__status__active
msgid "Active"
msgstr "Aktivno"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Add Languages"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Add an image to brand your header."
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_image_home_ids
#: model:ir.model.fields,field_description:pos_self_order.field_res_config_settings__pos_self_ordering_image_home_ids
msgid "Add images"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/product_info_popup/product_info_popup.xml:0
msgid "Add to Cart"
msgstr "Dodaj v košarico"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/combo_page/combo_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/product_page/product_page.xml:0
msgid "Add to cart"
msgstr "Dodaj v voziček"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/cancel_popup/cancel_popup.xml:0
msgid "All the items will be removed from the cart."
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Allow self-order customers to set their order for takeout."
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Allow takeout order"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
msgid "An error has occurred"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/cancel_popup/cancel_popup.xml:0
msgid "Any items already sent will not be cancelled"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/cancel_popup/cancel_popup.xml:0
msgid "Are you sure you want to cancel this order?"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_category__hour_after
msgid "Availability After"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_category__hour_until
msgid "Availability Until"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/overrides/components/product_info_banner/product_info_banner.xml:0
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Available"
msgstr "Razpoložljivo"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_available_language_ids
#: model:ir.model.fields,field_description:pos_self_order.field_res_config_settings__pos_self_ordering_available_language_ids
msgid "Available Languages"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.pos_self_order_product_pos_category_form_view
msgid "Available between"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.product_template_search_view_pos
msgid "Available in Self"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_product_product__self_order_available
#: model:ir.model.fields,field_description:pos_self_order.field_product_template__self_order_available
msgid "Available in Self Order"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Available interface languages"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/order_widget/order_widget.js:0
#: code:addons/pos_self_order/static/src/app/pages/eating_location_page/eating_location_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/order_history_page/order_history_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/payment_page/payment_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/stand_number_page/stand_number_page.xml:0
msgid "Back"
msgstr "Nazaj"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/order_widget/order_widget.js:0
msgid "Cancel"
msgstr "Prekliči"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/cancel_popup/cancel_popup.xml:0
msgid "Cancel Order"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/order_widget/order_widget.js:0
#: code:addons/pos_self_order/static/src/app/pages/product_list_page/product_list_page.js:0
msgid "Cancel order"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_pos_config__self_ordering_service_mode
#: model:ir.model.fields,help:pos_self_order.field_res_config_settings__pos_self_ordering_service_mode
msgid "Choose the kiosk mode"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_pos_config__self_ordering_mode
#: model:ir.model.fields,help:pos_self_order.field_res_config_settings__pos_self_ordering_mode
msgid "Choose the self ordering mode"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_pos_config__self_ordering_pay_after
#: model:ir.model.fields,help:pos_self_order.field_res_config_settings__pos_self_ordering_pay_after
msgid "Choose when the customer will pay"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/combo_selection/combo_selection.xml:0
msgid "Choose your"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/eating_location_page/eating_location_page.xml:0
msgid "Choose your eating location"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/out_of_paper_popup/out_of_paper_popup.xml:0
#: code:addons/pos_self_order/static/src/app/pages/confirmation_page/confirmation_page.xml:0
msgid "Close"
msgstr "Zaključi"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.pos_self_order_menu_item
msgid "Close Session"
msgstr "Zaključek seje"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/order_history_page/order_history_page.xml:0
msgid "Combo"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_order_line__combo_id
msgid "Combo reference"
msgstr ""

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_res_config_settings
msgid "Config Settings"
msgstr "Uredi nastavitve"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
msgid "Connection lost, please try again later"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/timeout_popup/timeout_popup.xml:0
msgid "Continue"
msgstr "Naprej"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/popup_table/popup_table.xml:0
msgid "Continue with table"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/popup_table/popup_table.xml:0
msgid "Could you please confirm your table number?"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__create_uid
msgid "Created by"
msgstr "Ustvaril"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__create_date
msgid "Created on"
msgstr "Ustvarjeno"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/order_history_page/order_history_page.js:0
msgid "Current"
msgstr "Trenutno"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.custom_link_tree
msgid "Custom Links"
msgstr ""

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_pos_self_order_custom_link
msgid ""
"Custom links that the restaurant can configure to be displayed on the self "
"order screen"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Customize Header"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_self_order_custom_link__style__danger
msgid "Danger"
msgstr "Nevarnost"

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_self_order_custom_link__style__dark
msgid "Dark"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Default"
msgstr "Privzeto"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_default_language_id
#: model:ir.model.fields,field_description:pos_self_order.field_res_config_settings__pos_self_ordering_default_language_id
msgid "Default Language"
msgstr "Privzeti jezik"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_default_user_id
#: model:ir.model.fields,field_description:pos_self_order.field_res_config_settings__pos_self_ordering_default_user_id
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Default User"
msgstr "Privzeti uporabnik"

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_pos_config__self_ordering_default_language_id
#: model:ir.model.fields,help:pos_self_order.field_res_config_settings__pos_self_ordering_default_language_id
msgid "Default language for the kiosk mode"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_config__self_ordering_mode__nothing
msgid "Disable"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/cancel_popup/cancel_popup.xml:0
#: code:addons/pos_self_order/static/src/app/components/language_popup/language_popup.xml:0
#: code:addons/pos_self_order/static/src/app/pages/combo_page/combo_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/product_page/product_page.xml:0
msgid "Discard"
msgstr "Opusti"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__display_name
msgid "Display Name"
msgstr "Prikazani naziv"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Download QR Codes"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/confirmation_page/confirmation_page.xml:0
msgid "Download your receipt here"
msgstr ""

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/pos_config.py:0
msgid "Each Order"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid ""
"Each table in your floor plan is assigned a unique QR code based on your configuration. For security reasons,\n"
"                    both the point of sale and table names are encrypted in the generated URL, as shown in the example below:."
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/eating_location_page/eating_location_page.xml:0
msgid "Eat In"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/combo_page/combo_page.xml:0
msgid "Edit"
msgstr "Uredi"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/attribute_selection/attribute_selection.xml:0
msgid "Enter your custom value"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid ""
"Feel free to use and print this QR code as many times as needed according to"
" your requirements."
msgstr ""

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_ir_binary
msgid "File streaming helper model for controllers"
msgstr "Pomočni model za pretočno pretakanje datotek za krmilnike"

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_account_fiscal_position
msgid "Fiscal Position"
msgstr "Davčno območje"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/payment_page/payment_page.xml:0
msgid "Follow instructions on the terminal"
msgstr ""

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/pos_config.py:0
msgid "Generic"
msgstr "Splošno"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/stand_number_page/stand_number_page.xml:0
msgid "Get a tracker and enter its number here"
msgstr ""

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP usmerjanje"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__has_paper
msgid "Has paper"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_index.xml:0
msgid ""
"Hey, looks like you forgot to create products or add them to pos_config. "
"Please add them before using the Self Order"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Home buttons"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/confirmation_page/confirmation_page.xml:0
msgid "Hope you enjoyed your meal!"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid "How to customize"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid "How to use"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__id
msgid "ID"
msgstr "ID"

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_product_product__self_order_available
#: model:ir.model.fields,help:pos_self_order.field_product_template__self_order_available
msgid "If this product is available in the Self Order screens"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid ""
"If you need customized QR codes, start by scanning the relevant QR code to acquire the URL. Then, make\n"
"                use of a QR code generator like https://www.qrcode-monkey.com or https://www.qr-code-generator.com"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_pos_config__self_ordering_image_brand
#: model:ir.model.fields,help:pos_self_order.field_pos_config__self_ordering_image_home_ids
#: model:ir.model.fields,help:pos_self_order.field_res_config_settings__pos_self_ordering_image_brand
#: model:ir.model.fields,help:pos_self_order.field_res_config_settings__pos_self_ordering_image_home_ids
msgid "Image to display on the self order screen"
msgstr ""

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/res_config_settings.py:0
msgid ""
"In Self-Order mode, you must have at least one table to generate QR codes"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_config__status__inactive
msgid "Inactive"
msgstr "Neaktivno"

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_self_order_custom_link__style__info
msgid "Info"
msgstr "Info"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/timeout_popup/timeout_popup.xml:0
msgid ""
"It seems there hasn't been any activity on this kiosk. Would you like to "
"continue?"
msgstr ""

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/pos_config.py:0
#: model:ir.actions.act_window,name:pos_self_order.action_pos_self_order_search_view
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_config__self_ordering_mode__kiosk
#: model_terms:ir.ui.view,arch_db:pos_self_order.pos_self_order_search_view
msgid "Kiosk"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__name
msgid "Label"
msgstr "Naziv"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Language"
msgstr "Jezik"

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_pos_config__self_ordering_available_language_ids
#: model:ir.model.fields,help:pos_self_order.field_res_config_settings__pos_self_ordering_available_language_ids
msgid "Languages available for the kiosk mode"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__write_uid
msgid "Last Updated by"
msgstr "Zadnji posodobil"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__write_date
msgid "Last Updated on"
msgstr "Zadnjič posodobljeno"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Let your customers order using their mobile or a kiosk."
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_self_order_custom_link__style__light
msgid "Light"
msgstr "Light"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/confirmation_page/confirmation_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/order_history_page/order_history_page.xml:0
msgid "Loading..."
msgstr "Nalaganje …"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid ""
"Make it easy for your customers to explore your menu\n"
"                online or order with the QR codes on your tables"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid ""
"Make it easy for your customers to explore your menu\n"
"                online with the QR codes on your tables"
msgstr ""

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/pos_config.py:0
msgid "Meal"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.pos_self_order_menu_item
msgid "Mobile Menu"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Mobile self-order & Kiosk"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/landing_page/landing_page.xml:0
msgid "My Order"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/landing_page/landing_page.xml:0
msgid "My Orders"
msgstr "Moja naročila"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/store/order_change_receipt_template.xml:0
msgid "NEW"
msgstr "NOVO"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/store/order_change_receipt_template.xml:0
msgid "NOTE"
msgstr "OPOMBA"

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_pos_config__self_ordering_image_brand_name
#: model:ir.model.fields,help:pos_self_order.field_res_config_settings__pos_self_ordering_image_brand_name
msgid "Name of the image to display on the self order screen"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/combo_page/combo_page.xml:0
msgid "Next"
msgstr "Naprej"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid "No"
msgstr "Ne"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/order_history_page/order_history_page.xml:0
msgid "No order found"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/product_list_page/product_list_page.xml:0
msgid "No products found"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/overrides/components/product_info_banner/product_info_banner.xml:0
msgid "Not available"
msgstr "Ni na voljo"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.product_template_search_view_pos
msgid "Not available in Self"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/confirmation_page/confirmation_page.xml:0
msgid "Ok"
msgstr "V Redu"

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/res_config_settings.py:0
msgid "Only pay after each is available with kiosk mode."
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.pos_self_order_menu_item
msgid "Open Kiosk"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/order_widget/order_widget.js:0
msgid "Order"
msgstr "Naroči"

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/pos_config.py:0
msgid "Order Now"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/confirmation_page/confirmation_page.xml:0
msgid "Order to pick-up at the counter"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
msgid "Orders not found on server"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/combo_selection/combo_selection.xml:0
#: code:addons/pos_self_order/static/src/app/components/product_card/product_card.xml:0
msgid "Out of stock"
msgstr "Ni na zalogi"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/order_widget/order_widget.js:0
#: code:addons/pos_self_order/static/src/app/pages/stand_number_page/stand_number_page.xml:0
msgid "Pay"
msgstr "Plačaj"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_pay_after
#: model:ir.model.fields,field_description:pos_self_order.field_res_config_settings__pos_self_ordering_pay_after
msgid "Pay After:"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Pay after"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/confirmation_page/confirmation_page.xml:0
msgid "Pay at the cashier"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid ""
"Personalize your splash screen by adding one or multiple images to create a "
"slideshow"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/overrides/components/receipt_header/receipt_header.xml:0
msgid "Pickup At Counter"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_config__self_ordering_service_mode__counter
msgid "Pickup zone"
msgstr ""

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/pos_session.py:0
msgid "PoS Order by Session"
msgstr ""

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_pos_load_mixin
msgid "PoS data loading mixin"
msgstr ""

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_pos_category
msgid "Point of Sale Category"
msgstr "Kategorija POS"

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Nastavitve POS-blagajne"

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_pos_order_line
msgid "Point of Sale Order Lines"
msgstr ""

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_pos_order
msgid "Point of Sale Orders"
msgstr "Naročila POS"

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_pos_payment_method
msgid "Point of Sale Payment Methods"
msgstr ""

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_pos_session
msgid "Point of Sale Session"
msgstr "Seja POS"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid "Point of sale:"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__pos_config_ids
msgid "Points of Sale"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__link_html
msgid "Preview"
msgstr "Predogled"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Preview Web interface"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_self_order_custom_link__style__primary
msgid "Primary"
msgstr "Primarna"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Print QR Codes"
msgstr ""

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_product_template
msgid "Product"
msgstr "Izdelek"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/product_card/product_card.xml:0
msgid "Product Information"
msgstr "Podatki o izdelku"

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_product_product
msgid "Product Variant"
msgstr "Različica izdelka"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/combo_selection/combo_selection.xml:0
#: code:addons/pos_self_order/static/src/app/components/product_card/product_card.xml:0
#: code:addons/pos_self_order/static/src/app/pages/cart_page/cart_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/combo_page/combo_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/product_list_page/product_list_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/product_page/product_page.xml:0
msgid "Product image"
msgstr "Slika izdelka"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
msgid "Product is not available"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
msgid "Product not found"
msgstr "Nisem dobil nobenega izdelka"

#. module: pos_self_order
#: model:ir.actions.report,name:pos_self_order.report_self_order_qr_codes_page
msgid "QR Codes"
msgstr ""

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/res_config_settings.py:0
msgid "QR codes can only be generated in mobile or consultation mode."
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_config__self_ordering_mode__consultation
msgid "QR menu"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "QR menu & Kiosk activation"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_config__self_ordering_mode__mobile
msgid "QR menu + Ordering"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/product_info_popup/product_info_popup.xml:0
#: code:addons/pos_self_order/static/src/app/pages/combo_page/combo_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/product_page/product_page.xml:0
msgid "Quantity select"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Reset QR Codes"
msgstr ""

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_restaurant_floor
msgid "Restaurant Floor"
msgstr ""

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_restaurant_table
msgid "Restaurant Table"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/payment_page/payment_page.xml:0
msgid "Retry"
msgstr "Ponovno"

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_self_order_custom_link__style__secondary
msgid "Secondary"
msgstr "Sekundarne"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_restaurant_table__identifier
msgid "Security Token"
msgstr "Varnostni žeton"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/popup_table/popup_table.xml:0
msgid "Select a table"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_pos_self_order_custom_link__pos_config_ids
msgid ""
"Select for which points of sale you want to display this link. Leave empty "
"to display it for all points of sale. You have to select among the points of"
" sale that have the 'QR Code Menu' feature enabled."
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_image_brand
#: model:ir.model.fields,field_description:pos_self_order.field_res_config_settings__pos_self_ordering_image_brand
msgid "Self Order Kiosk Image Brand"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_image_brand_name
#: model:ir.model.fields,field_description:pos_self_order.field_res_config_settings__pos_self_ordering_image_brand_name
msgid "Self Order Kiosk Image Brand Name"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid "Self Order:"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Self Ordering"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.pos_self_order_menu_item
msgid "Self Ordering Enabled"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_mode
#: model:ir.model.fields,field_description:pos_self_order.field_res_config_settings__pos_self_ordering_mode
msgid "Self Ordering Mode"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_service_mode
#: model:ir.model.fields,field_description:pos_self_order.field_res_config_settings__pos_self_ordering_service_mode
msgid "Self Ordering Service Mode"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_url
msgid "Self Ordering Url"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_takeaway
#: model:ir.model.fields,field_description:pos_self_order.field_res_config_settings__pos_self_ordering_takeaway
msgid "Self Takeaway"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
msgid "Self-Order"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/overrides/components/product_info_banner/product_info_banner.xml:0
msgid "Self-ordering:"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__sequence
msgid "Sequence"
msgstr "Zaporedje"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Service at"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/overrides/components/receipt_header/receipt_header.xml:0
msgid "Service at Table"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/confirmation_page/confirmation_page.xml:0
msgid "Service at table"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Splash screens"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.pos_self_order_menu_item
msgid "Start Kiosk"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__status
msgid "Status"
msgstr "Status"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__style
msgid "Style"
msgstr "Stil"

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_self_order_custom_link__style__success
msgid "Success"
msgstr "Uspešno"

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_config__self_ordering_service_mode__table
msgid "Table"
msgstr "Miza"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_order__table_stand_number
msgid "Table Stand Number"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/overrides/components/receipt_header/receipt_header.xml:0
msgid "Table Tracker:"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/popup_table/popup_table.xml:0
msgid "Table detective time!"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid "Table:"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/eating_location_page/eating_location_page.xml:0
msgid "Take Out"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/order_history_page/order_history_page.xml:0
msgid "Tax:"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/cart_page/cart_page.xml:0
msgid "Taxes:"
msgstr "Davki:"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/popup_table/popup_table.xml:0
msgid "Thanks a lot!"
msgstr ""

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/pos_category.py:0
msgid "The Availability After must be set between 00:00 and 24:00"
msgstr ""

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/pos_category.py:0
msgid "The Availability Until must be greater than Availability After."
msgstr ""

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/pos_category.py:0
msgid "The Availability Until must be set between 00:00 and 24:00"
msgstr ""

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/pos_config.py:0
msgid "The Self-Order default user must be a POS user"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_pos_category__hour_after
msgid "The product will be available after this hour."
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_pos_category__hour_until
msgid "The product will be available until this hour."
msgstr ""

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/res_config_settings.py:0
msgid "The user must be a POS user"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/product_page/product_page.xml:0
msgid "This combination does not exist."
msgstr "Ta kombinacija ne obstaja."

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/cart_page/cart_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/order_history_page/order_history_page.xml:0
msgid "Total:"
msgstr "Skupaj:"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__url
msgid "URL"
msgstr "URL"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid "URL:"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/product_card/product_card.xml:0
msgid "Unavailable"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/product_list_page/product_list_page.xml:0
msgid "Unavailable at this time of the day"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
msgid "Uncategorised"
msgstr ""

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_self_order_custom_link__style__warning
msgid "Warning"
msgstr "Opozorilo"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
msgid "We're currently closed"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_index.xml:0
msgid "We're currently closed."
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/confirmation_page/confirmation_page.xml:0
msgid "We're preparing your order!"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid "Yes"
msgstr "Da"

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/pos_config.py:0
#: code:addons/pos_self_order/models/res_config_settings.py:0
msgid "You cannot add cash payment methods in kiosk mode."
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/cart_page/cart_page.js:0
msgid "You cannot edit a posted orderline !"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
msgid "You're not authorized to perform this action"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/order_widget/order_widget.xml:0
#: code:addons/pos_self_order/static/src/app/pages/cart_page/cart_page.xml:0
msgid "Your Order"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/combo_page/combo_page.xml:0
msgid "Your Selection"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
msgid "Your order has been cancelled"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
msgid "Your order has been paid"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/confirmation_page/confirmation_page.xml:0
msgid "Your order number"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
msgid "Your order status has been changed"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.pos_self_order_product_pos_category_form_view
msgid "and"
msgstr "in"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.custom_link_tree
msgid "empty = all points of sale"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/popup_table/popup_table.xml:0
msgid "floor"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.custom_link_tree
msgid "https://odoo.com"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.custom_link_tree
msgid "odoo"
msgstr ""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/product_page/product_page.xml:0
msgid "options"
msgstr ""
