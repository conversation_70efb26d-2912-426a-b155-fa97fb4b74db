# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_picking_batch
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# Wil <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:40+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Hindi (https://app.transifex.com/odoo/teams/41243/hi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "2023-08-20"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_type_kanban_batch
msgid "<span class=\"col-6\">Waves</span>"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.view_picking_type_form_inherit
msgid ""
"<span class=\"o_form_label fw-bold\" invisible=\"not auto_batch\" "
"style=\"white-space: nowrap;\">Wave Grouping</span>"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.view_picking_type_form_inherit
msgid ""
"<span class=\"o_form_label fw-bold\" invisible=\"not auto_batch\">Batch "
"Grouping</span>"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "<strong>FROM</strong>"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "<strong>Lot/Serial Number</strong>"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "<strong>Package</strong>"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "<strong>Product Barcode</strong>"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "<strong>Responsible:</strong>"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "<strong>TO</strong>"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "<strong>→</strong>"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_type__batch_max_lines
msgid ""
"A transfer will not be automatically added to batches that will exceed this number of lines if the transfer is added to it.\n"
"Leave this value as '0' if no line limit."
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_type__batch_max_pickings
msgid ""
"A transfer will not be automatically added to batches that will exceed this number of transfers.\n"
"Leave this value as '0' if no transfer limit."
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_needaction
msgid "Action Needed"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__activity_ids
msgid "Activities"
msgstr "गतिविधियाँ"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__activity_state
msgid "Activity State"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__activity_type_icon
msgid "Activity Type Icon"
msgstr ""

#. module: stock_picking_batch
#. odoo-python
#: code:addons/stock_picking_batch/models/stock_picking.py:0
#: code:addons/stock_picking_batch/wizard/stock_add_to_wave.py:0
msgid "Add Operations"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_to_batch_form
msgid "Add pickings to"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_add_to_wave_form
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_to_batch_form
msgid "Add to"
msgstr ""

#. module: stock_picking_batch
#. odoo-python
#: code:addons/stock_picking_batch/models/stock_move_line.py:0
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_add_to_wave_form
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.view_move_line_tree_detailed_wave
msgid "Add to Wave"
msgstr ""

#. module: stock_picking_batch
#: model:ir.actions.act_window,name:stock_picking_batch.stock_picking_to_batch_action_stock_picking
msgid "Add to batch"
msgstr ""

#. module: stock_picking_batch
#: model:ir.actions.act_window,name:stock_picking_batch.stock_add_to_wave_action_stock_picking
msgid "Add to wave"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Allocation"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__allowed_picking_ids
msgid "Allowed Picking"
msgstr ""

#. module: stock_picking_batch
#. odoo-python
#: code:addons/stock_picking_batch/models/stock_picking.py:0
msgid "Assigned to %s Responsible"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_type__batch_auto_confirm
msgid "Auto-confirm"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_type__auto_batch
msgid "Automatic Batches"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_type__batch_group_by_partner
msgid "Automatically group batches by contacts."
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_type__batch_group_by_destination
msgid "Automatically group batches by destination country."
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_type__batch_group_by_dest_loc
msgid "Automatically group batches by their destination location."
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_type__batch_group_by_src_loc
msgid "Automatically group batches by their source location."
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_type__auto_batch
msgid ""
"Automatically put pickings into batches as they are confirmed when possible."
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Barcode"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_form_inherit
msgid "Batch"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.view_picking_type_form_inherit
msgid "Batch & Wave Transfers"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Batch Name"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_type__batch_properties_definition
msgid "Batch Properties"
msgstr ""

#. module: stock_picking_batch
#. odoo-python
#: code:addons/stock_picking_batch/models/stock_picking_batch.py:0
#: code:addons/stock_picking_batch/wizard/stock_picking_to_batch.py:0
#: model:ir.actions.report,name:stock_picking_batch.action_report_picking_batch
#: model:ir.model,name:stock_picking_batch.model_stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_move_line__batch_id
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking__batch_id
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__name
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__batch_id
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_move_line_view_search_inherit_stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.view_picking_internal_search_inherit
msgid "Batch Transfer"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model,name:stock_picking_batch.model_stock_picking_to_batch
msgid "Batch Transfer Lines"
msgstr ""

#. module: stock_picking_batch
#: model:ir.actions.act_window,name:stock_picking_batch.stock_picking_batch_action
#: model:ir.ui.menu,name:stock_picking_batch.stock_picking_batch_menu
msgid "Batch Transfers"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Batch Transfers not finished"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_move_line__batch_id
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking__batch_id
msgid "Batch associated to this transfer"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.actions.act_window,help:stock_picking_batch.action_picking_tree_wave
#: model_terms:ir.actions.act_window,help:stock_picking_batch.stock_picking_batch_action
msgid "Batch transfers"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_type_kanban_batch
msgid "Batches"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_calendar
msgid "Calendar View"
msgstr "कैलेंडर देखना"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_add_to_wave_form
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_to_batch_form
msgid "Cancel"
msgstr "रद्द"

#. module: stock_picking_batch
#: model:ir.model.fields.selection,name:stock_picking_batch.selection__stock_picking_batch__state__cancel
msgid "Cancelled"
msgstr "निरस्त"

#. module: stock_picking_batch
#. odoo-python
#: code:addons/stock_picking_batch/wizard/stock_add_to_wave.py:0
msgid "Cannot create wave transfers"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_type__wave_category_ids
msgid "Categories to consider when grouping waves."
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Check Availability"
msgstr ""

#. module: stock_picking_batch
#. odoo-python
#: code:addons/stock_picking_batch/models/stock_picking_batch.py:0
msgid "Choose Labels Layout"
msgstr ""

#. module: stock_picking_batch
#. odoo-python
#: code:addons/stock_picking_batch/models/stock_picking_batch.py:0
msgid "Choose Type of Labels To Print"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.actions.act_window,help:stock_picking_batch.action_picking_tree_wave
#: model_terms:ir.actions.act_window,help:stock_picking_batch.stock_picking_batch_action
msgid "Cluster transfers"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__company_id
msgid "Company"
msgstr "संस्था"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_add_to_wave_form
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_to_batch_form
msgid "Confirm"
msgstr "पुष्टि करें"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_type__batch_group_by_partner
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.view_picking_type_form_inherit
msgid "Contact"
msgstr "संपर्क"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_type__count_picking_batch
msgid "Count Picking Batch"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_type__count_picking_wave
msgid "Count Picking Wave"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave__create_uid
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__create_uid
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__create_uid
msgid "Created by"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave__create_date
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__create_date
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__create_date
msgid "Created on"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__description
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__description
msgid "Description"
msgstr "विवरण"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_type__batch_group_by_destination
msgid "Destination Country"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.view_picking_type_form_inherit
msgid "Destination Location"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Detailed Operations"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave__display_name
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__display_name
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__display_name
msgid "Display Name"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields.selection,name:stock_picking_batch.selection__stock_picking_batch__state__done
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Done"
msgstr "हो गया"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__is_create_draft
#: model:ir.model.fields.selection,name:stock_picking_batch.selection__stock_picking_batch__state__draft
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Draft"
msgstr "मसौदा"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_follower_ids
msgid "Followers"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Future Activities"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Group By"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_type__batch_group_by_dest_loc
msgid "Group by Destination Location"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_type__batch_group_by_src_loc
msgid "Group by Source Location"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Gujarat"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__has_message
msgid "Has Message"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave__id
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__id
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__id
msgid "ID"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__activity_exception_icon
msgid "Icon"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__message_has_error
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: stock_picking_batch
#. odoo-python
#: code:addons/stock_picking_batch/models/stock_picking.py:0
msgid ""
"If the Automatic Batches feature is enabled, at least one 'Group by' option "
"must be selected."
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "In Progress"
msgstr "प्रगति में है"

#. module: stock_picking_batch
#: model:ir.model.fields.selection,name:stock_picking_batch.selection__stock_picking_batch__state__in_progress
msgid "In progress"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: stock_picking_batch
#: model:ir.ui.menu,name:stock_picking_batch.menu_stock_jobs
msgid "Jobs"
msgstr "कार्य"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "John Doe"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave__write_uid
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__write_uid
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__write_uid
msgid "Last Updated by"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave__write_date
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__write_date
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__write_date
msgid "Last Updated on"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Late Activities"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.actions.act_window,help:stock_picking_batch.action_picking_tree_wave
#: model_terms:ir.actions.act_window,help:stock_picking_batch.stock_picking_batch_action
msgid ""
"Launch picking orders by aisle or area and regroup at packing zone. Ideal "
"for large warehouses."
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave__line_ids
msgid "Line"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__picking_ids
msgid "List of transfers associated to this batch"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_type__wave_group_by_location
msgid "Location"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Location From Name"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Location To Name"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_type__wave_location_ids
msgid "Locations to consider when grouping waves."
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Lot Number"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_type__batch_max_lines
msgid "Maximum lines"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_type__batch_max_pickings
msgid "Maximum transfers"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_ids
msgid "Messages"
msgstr "संदेश"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave__mode
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__mode
msgid "Mode"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.view_move_line_tree
msgid "Move Lines"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "My Transfers"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__activity_date_deadline
msgid "Next Activity Deadline"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__activity_summary
msgid "Next Activity Summary"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__activity_type_id
msgid "Next Activity Type"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_has_error_counter
msgid "Number of errors"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__message_needaction_counter
msgid "Number of messages requiring action"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__picking_type_id
msgid "Operation Type"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Operations"
msgstr "संचालन"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Package ID"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.actions.act_window,help:stock_picking_batch.action_picking_tree_wave
#: model_terms:ir.actions.act_window,help:stock_picking_batch.stock_picking_batch_action
msgid ""
"Pick multiple orders in one trip and prepare orders as you pick. This "
"reduces packing time and is ideal for small products."
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave__picking_ids
msgid "Picking"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model,name:stock_picking_batch.model_stock_picking_type
msgid "Picking Type"
msgstr ""

#. module: stock_picking_batch
#. odoo-python
#: code:addons/stock_picking_batch/models/stock_picking_batch.py:0
msgid ""
"Please add 'Done' quantities to the batch picking to create a new pack."
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_type_kanban_batch
msgid "Prepare Batch"
msgstr ""

#. module: stock_picking_batch
#: model:ir.actions.act_window,name:stock_picking_batch.action_prepare_wave
#: model:ir.actions.act_window,name:stock_picking_batch.action_prepare_wave_for_picking_type
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_type_kanban_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_wave_kanban
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_wave_tree
msgid "Prepare Wave"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Print"
msgstr "प्रिंट"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Print Labels"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_type__wave_group_by_product
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Product"
msgstr "उत्पाद"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Product Barcode"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_type__wave_group_by_category
msgid "Product Category"
msgstr "उत्पाद श्रेणी"

#. module: stock_picking_batch
#: model:ir.model,name:stock_picking_batch.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Product Name"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__properties
msgid "Properties"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Put in Pack"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Quantity"
msgstr "मात्रा"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Quantity Done"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__rating_ids
msgid "Ratings"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.actions.act_window,help:stock_picking_batch.action_picking_tree_wave
#: model_terms:ir.actions.act_window,help:stock_picking_batch.stock_picking_batch_action
msgid ""
"Regroup multiple orders into one picking and consolidate at the packing "
"zone."
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave__user_id
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__user_id
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__user_id
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Responsible"
msgstr "जिम्मेदार"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__activity_user_id
msgid "Responsible User"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Result Package ID"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_has_sms_error
msgid "SMS Delivery error"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__scheduled_date
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Scheduled Date"
msgstr "अनुसूचित तिथि"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__scheduled_date
msgid ""
"Scheduled date for the transfers to be processed.\n"
"              - If manually set then scheduled date for all transfers in batch will automatically update to this date.\n"
"              - If not manually changed and transfers are added/removed/updated then this will be their earliest scheduled date\n"
"                but this scheduled date will not be set for all transfers in batch."
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Search Batch Transfer"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.view_picking_type_form_inherit
msgid "Select locations you want to group"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.view_picking_type_form_inherit
msgid "Select product categories you want to group"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking__batch_sequence
msgid "Sequence"
msgstr "अनुक्रम"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__show_allocation
msgid "Show Allocation Button"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__show_check_availability
msgid "Show Check Availability"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__show_lots_text
msgid "Show Lots Text"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Show all records which has next action date is before today"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.view_picking_type_form_inherit
msgid "Source Location"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_type__wave_group_by_location
msgid ""
"Split transfers by defined locations, then group transfers with the same "
"location."
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_type__wave_group_by_category
msgid ""
"Split transfers by product category, then group transfers that have the same"
" product category."
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_type__wave_group_by_product
msgid ""
"Split transfers by product then group transfers that have the same product."
msgstr ""

#. module: stock_picking_batch
#: model:mail.message.subtype,description:stock_picking_batch.mt_batch_state
#: model:mail.message.subtype,name:stock_picking_batch.mt_batch_state
msgid "Stage Changed"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__state
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "State"
msgstr "स्थिति"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Status"
msgstr "स्थिति"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_tree
msgid "Stock Batch Transfer"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model,name:stock_picking_batch.model_stock_move
msgid "Stock Move"
msgstr "चाल स्टॉक"

#. module: stock_picking_batch
#: model:ir.model,name:stock_picking_batch.model_stock_package_destination
msgid "Stock Package Destination"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__move_line_ids
msgid "Stock move lines"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__move_ids
msgid "Stock moves"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Summary:"
msgstr ""

#. module: stock_picking_batch
#. odoo-python
#: code:addons/stock_picking_batch/models/stock_picking_batch.py:0
msgid ""
"The following transfers cannot be added to batch transfer %(batch)s. Please check their states and operation types.\n"
"\n"
"Incompatibilities: %(incompatible_transfers)s"
msgstr ""

#. module: stock_picking_batch
#. odoo-python
#: code:addons/stock_picking_batch/wizard/stock_add_to_wave.py:0
msgid "The selected operations should belong to a unique company."
msgstr ""

#. module: stock_picking_batch
#. odoo-python
#: code:addons/stock_picking_batch/wizard/stock_picking_to_batch.py:0
msgid "The selected pickings should belong to an unique company."
msgstr ""

#. module: stock_picking_batch
#. odoo-python
#: code:addons/stock_picking_batch/wizard/stock_add_to_wave.py:0
msgid "The selected transfers should belong to a unique company."
msgstr ""

#. module: stock_picking_batch
#. odoo-python
#: code:addons/stock_picking_batch/wizard/stock_add_to_wave.py:0
msgid "The selected transfers should belong to the same operation type"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__is_wave
msgid "This batch is a wave"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "To Do"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Today Activities"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model,name:stock_picking_batch.model_stock_picking
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Transfer"
msgstr "स्थानांतरण"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Transfer Display Name"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Transfer Name"
msgstr ""

#. module: stock_picking_batch
#. odoo-python
#: code:addons/stock_picking_batch/models/stock_picking_batch.py:0
msgid "Transferred by"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__picking_ids
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Transfers"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__picking_type_code
msgid "Type of Operation"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Unit of Measure"
msgstr ""

#. module: stock_picking_batch
#: model:ir.actions.server,name:stock_picking_batch.action_unreserve_batch_picking
msgid "Unreserve"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Validate"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model,name:stock_picking_batch.model_stock_warehouse
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__warehouse_id
msgid "Warehouse"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_type__wave_location_ids
msgid "Wave Locations"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_type__wave_category_ids
msgid "Wave Product Categories"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave__wave_id
msgid "Wave Transfer"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model,name:stock_picking_batch.model_stock_add_to_wave
msgid "Wave Transfer Lines"
msgstr ""

#. module: stock_picking_batch
#: model:ir.actions.act_window,name:stock_picking_batch.action_picking_tree_wave
#: model:ir.ui.menu,name:stock_picking_batch.stock_picking_wave_menu
msgid "Wave Transfers"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.actions.act_window,help:stock_picking_batch.action_picking_tree_wave
#: model_terms:ir.actions.act_window,help:stock_picking_batch.stock_picking_batch_action
msgid "Wave transfers"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_to_batch__is_create_draft
msgid "When checked, create the batch in draft status"
msgstr ""

#. module: stock_picking_batch
#. odoo-python
#: code:addons/stock_picking_batch/models/stock_picking_batch.py:0
msgid "You cannot delete Done batch transfers."
msgstr ""

#. module: stock_picking_batch
#. odoo-python
#: code:addons/stock_picking_batch/models/stock_picking_batch.py:0
msgid "You have to set some pickings to batch."
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields.selection,name:stock_picking_batch.selection__stock_picking_to_batch__mode__new
msgid "a new batch transfer"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields.selection,name:stock_picking_batch.selection__stock_add_to_wave__mode__new
msgid "a new wave transfer"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields.selection,name:stock_picking_batch.selection__stock_picking_to_batch__mode__existing
msgid "an existing batch transfer"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields.selection,name:stock_picking_batch.selection__stock_add_to_wave__mode__existing
msgid "an existing wave transfer"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__estimated_shipping_volume
msgid "shipping_volume"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__estimated_shipping_weight
msgid "shipping_weight"
msgstr ""
