
.o-cell-thread {
    display: flex;
    flex-direction: column;
    border-radius: 2px;

    .o-mail-Thread {
        display: inline-block;
        padding-left : 5px;
        padding-right : 5px;
    }

    .o-cell-thread-body {
        overflow-y: auto;
    }

    .o-mail-Composer {
        padding: 3px 10px 0 10px;
    }
}

.o-thread-start-composer {
    padding: 5px 10px 0 10px;
}

.o-spreadsheet .o-selfAuthored {
    .o-mail-Message-core {
        flex-direction: row-reverse;
    }
    .o-mail-Message-header {
        justify-content: flex-end;
    }
}

.o-thread-popover {
    overflow-y: auto;
    /** counteract the generic content-box set on "o-spreadsheet *"  */
    * {
        box-sizing: border-box !important;
    }

    .o-thread-highlight, .o-cell-thread, .o-thread-start-composer {
        min-width: 270px;
        width: 100%;
    }

    .o-thread-highlight {
        &, .o-cell-thread {
            background-color: #E8EDFF;
        }
    }
}
