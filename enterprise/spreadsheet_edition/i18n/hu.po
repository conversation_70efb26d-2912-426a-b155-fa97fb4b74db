# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* spreadsheet_edition
# 
# Translators:
# f1b3a33e3b33fcf18004a5292e501f50_3500ca8 <373b677b151624c4521d9efc77b996fd_750224>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON> Trigaux, 2024
# <PERSON><PERSON>, 2024
# krnkris, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:54+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: krnkris, 2024\n"
"Language-Team: Hungarian (https://app.transifex.com/odoo/teams/41243/hu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/list_view/list_renderer.js:0
msgid "%(field name)s by %(order)s"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/list/list_init_callback.js:0
msgid "%(list_name)s (List #%(list_id)s)"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/actions/version_history/version_history_action.js:0
msgid "%(name)s (restored from %(timestamp)s)"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/pivot/pivot_init_callback.js:0
msgid "%(pivot_name)s (Pivot #%(pivot_id)s)"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/pivot_view/pivot_view.js:0
msgid "%(pivot_title)s by %(group_by)s"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/pivot_view/pivot_view.js:0
msgid "%(pivot_title)s by %(group_by)s (%(granularity)s)"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/comments/side_panel/comment_threads_side_panel.js:0
msgid "%s threads"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/components/locale_status/locale_status.js:0
msgid "- dates: %s"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/components/locale_status/locale_status.js:0
msgid "- numbers: %s"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/comments/side_panel/comment_threads_side_panel.js:0
msgid "1 thread"
msgstr ""

#. module: spreadsheet_edition
#: model:ir.model.constraint,message:spreadsheet_edition.constraint_spreadsheet_revision_parent_unique
msgid "A revision based on the same revision already exists"
msgstr ""

#. module: spreadsheet_edition
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_cell_thread__message_needaction
msgid "Action Needed"
msgstr "Akció szükséges"

#. module: spreadsheet_edition
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_revision__active
msgid "Active"
msgstr "Aktív"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/comments/components/cell_thread.xml:0
#: code:addons/spreadsheet_edition/static/src/bundle/comments/components/cell_thread_popover.xml:0
msgid "Add a comment..."
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/global_filter_side_panel.xml:0
msgid "Add a new filter..."
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_field_offset.js:0
msgid "After next"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/comments/index.js:0
msgid "All Comments"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/comments/side_panel/comment_threads_side_panel.xml:0
msgid "All sheets"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/list/side_panels/list_details_side_panel.js:0
msgid "Are you sure you want to delete this list?"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/chart/side_panels/index.js:0
msgid "Area"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/list/side_panels/edit_list_sorting_section/edit_list_sorting_section.xml:0
msgid "Ascending"
msgstr "Növekvő"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/relation_filter_editor_side_panel.js:0
msgid ""
"At least one data source has an invalid model. Please delete it before "
"editing this global filter."
msgstr ""

#. module: spreadsheet_edition
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_cell_thread__message_attachment_count
msgid "Attachment Count"
msgstr "Mellékletek száma"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/date_filter_editor_side_panel.xml:0
msgid "Automatically filter on the current period"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/relation_filter_editor_side_panel.xml:0
msgid "Automatically filter on the current user"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/chart/side_panels/odoo_chart_with_axis/design_panel.xml:0
msgid "Axis"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/version_history/side_panel/version_history_side_panel.xml:0
msgid "Back"
msgstr "Vissza"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_field_offset.js:0
msgid "Before previous"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/components/spreadsheet_selector_grid/spreadsheet_selector_grid.js:0
msgid "Blank spreadsheet"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/chart/side_panels/odoo_chart_with_axis/design_panel.xml:0
msgid "Bottom"
msgstr "Alul"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/components/spreadsheet_selector_dialog/spreadsheet_selector_dialog.xml:0
#: code:addons/spreadsheet_edition/static/src/bundle/actions/input_dialog/input_dialog.xml:0
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/filter_editor_side_panel.xml:0
#: code:addons/spreadsheet_edition/static/src/bundle/ir_menu_selector/ir_menu_selector.xml:0
#: code:addons/spreadsheet_edition/static/src/bundle/version_history/restore_version_dialog/restore_version_dialog.xml:0
msgid "Cancel"
msgstr "Visszavonás"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/pivot/side_panels/pivot_details_side_panel.js:0
msgid ""
"Changing the pivot definition requires to reload the data. It may take some "
"time."
msgstr ""

#. module: spreadsheet_edition
#: model:ir.model,name:spreadsheet_edition.model_spreadsheet_revision
msgid "Collaborative spreadsheet revision"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/chart/side_panels/index.js:0
msgid "Column"
msgstr "Oszlop"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/list/side_panels/list_details_side_panel.xml:0
msgid "Columns"
msgstr "Oszlopok"

#. module: spreadsheet_edition
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_revision__commands
msgid "Commands"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/comments/index.js:0
msgid "Comments"
msgstr "Megjegyzések"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/components/spreadsheet_selector_dialog/spreadsheet_selector_dialog.xml:0
#: code:addons/spreadsheet_edition/static/src/bundle/actions/input_dialog/input_dialog.xml:0
#: code:addons/spreadsheet_edition/static/src/bundle/ir_menu_selector/ir_menu_selector.xml:0
msgid "Confirm"
msgstr "Megerősítés"

#. module: spreadsheet_edition
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_cell_thread__create_uid
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_revision__create_uid
msgid "Created by"
msgstr "Létrehozta"

#. module: spreadsheet_edition
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_cell_thread__create_date
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_revision__create_date
msgid "Created on"
msgstr "Létrehozva"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/hooks.js:0
msgid "Currency"
msgstr "Pénznem"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/version_history/side_panel/version_history_item.xml:0
msgid "Current"
msgstr "Jelenlegi"

#. module: spreadsheet_edition
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_dashboard__current_revision_uuid
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_dashboard_share__current_revision_uuid
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_mixin__current_revision_uuid
msgid "Current Revision Uuid"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/global_filter_side_panel.xml:0
msgid "Date"
msgstr "Dátum"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/date_filter_editor_side_panel.xml:0
msgid "Date field"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/pivot_view/pivot_view.js:0
msgid "Day"
msgstr "Nap"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/pivot_view/pivot_view.js:0
msgid "Day of Month"
msgstr "A hónap napja"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/date_filter_editor_side_panel.xml:0
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/relation_filter_editor_side_panel.xml:0
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/text_filter_editor_side_panel.xml:0
msgid "Default value"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/pivot/side_panels/pivot_details_side_panel.js:0
msgid "Defer updates"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/chart/side_panels/odoo_chart_with_axis/design_panel.xml:0
msgid "Degree"
msgstr "Végzettség"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/pivot/side_panels/pivot_details_side_panel.xml:0
msgid "Delete Pivot"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/list/side_panels/edit_list_sorting_section/edit_list_sorting_section.xml:0
msgid "Descending"
msgstr "Csökkenő"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/components/locale_status/locale_status.js:0
msgid ""
"Difference between user locale (%(user_locale)s) and spreadsheet locale "
"(%(spreadsheet_locale)s). This spreadsheet is using the formats below:"
msgstr ""

#. module: spreadsheet_edition
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_cell_thread__display_name
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_revision__display_name
msgid "Display Name"
msgstr "Megjelenített név"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/chart/side_panels/common/config_panel.xml:0
#: code:addons/spreadsheet_edition/static/src/bundle/components/side_panel_domain/side_panel_domain.xml:0
msgid "Domain"
msgstr "Tartomány"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/o_spreadsheet/menu_item_registry.js:0
msgid "Download"
msgstr "Letöltés"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/o_spreadsheet/menu_item_registry.js:0
msgid "Download as JSON"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/filter_editor_side_panel.js:0
msgid "Duplicated Label"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/global_filter_side_panel.xml:0
msgid "Edit"
msgstr "Szerkesztés"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/chart/side_panels/common/config_panel.xml:0
#: code:addons/spreadsheet_edition/static/src/bundle/components/side_panel_domain/side_panel_domain.xml:0
msgid "Edit domain"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/chart/side_panels/odoo_chart_with_axis/design_panel.xml:0
msgid "Exponential"
msgstr "Exponenciális"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/date_filter_editor_side_panel.xml:0
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/relation_filter_editor_side_panel.xml:0
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/text_filter_editor_side_panel.xml:0
msgid "Field matching"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/comments/side_panel/comment_threads_side_panel.xml:0
msgid "Filter comments"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/index.js:0
msgid "Filter properties"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/text_filter_editor_side_panel.xml:0
msgid "Filter value"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/filter_component.xml:0
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/index.js:0
msgid "Filters"
msgstr "Szűrők"

#. module: spreadsheet_edition
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_cell_thread__message_follower_ids
msgid "Followers"
msgstr "Követők"

#. module: spreadsheet_edition
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_cell_thread__message_partner_ids
msgid "Followers (Partners)"
msgstr "Követők (Partnerek)"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/date_filter_editor_side_panel.js:0
msgid "From / To"
msgstr ""

#. module: spreadsheet_edition
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_cell_thread__has_message
msgid "Has Message"
msgstr "Van üzenet"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/actions/version_history/version_history_action.js:0
msgid "Heads up!"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/chart/side_panels/odoo_chart_with_axis/design_panel.js:0
msgid "Horizontal axis"
msgstr ""

#. module: spreadsheet_edition
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_cell_thread__id
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_revision__id
msgid "ID"
msgstr "ID"

#. module: spreadsheet_edition
#: model:ir.model.fields,help:spreadsheet_edition.field_spreadsheet_cell_thread__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Ha be van jelölve, akkor az új üzenetek figyelmet igényelnek."

#. module: spreadsheet_edition
#: model:ir.model.fields,help:spreadsheet_edition.field_spreadsheet_cell_thread__message_has_error
#: model:ir.model.fields,help:spreadsheet_edition.field_spreadsheet_cell_thread__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""
"Ha be van jelölve, akkor néhány üzenetnél kézbesítési hiba lépett fel."

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/actions/version_history/version_history_action.js:0
msgid ""
"If you go ahead, your document will go back to the version from %s.\n"
"Any changes you've made after that time will disappear. Ready to proceed?"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/relation_filter_editor_side_panel.xml:0
msgid "Include children"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/comments/index.js:0
msgid "Insert comment"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/graph_view/graph_view.xml:0
#: code:addons/spreadsheet_edition/static/src/bundle/pivot/pivot.xml:0
msgid "Insert in Spreadsheet"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/list_view/list_controller.js:0
msgid "Insert in spreadsheet"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/list_view/insert_list_spreadsheet_menu.xml:0
msgid "Insert list in spreadsheet"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/components/spreadsheet_selector_dialog/spreadsheet_selector_dialog.xml:0
msgid "Insert the first"
msgstr ""

#. module: spreadsheet_edition
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_cell_thread__message_is_follower
msgid "Is Follower"
msgstr "Követő"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/filter_editor_side_panel.xml:0
msgid "Label"
msgstr "Felirat"

#. module: spreadsheet_edition
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_cell_thread__write_uid
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_revision__write_uid
msgid "Last Updated by"
msgstr "Frissítette"

#. module: spreadsheet_edition
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_cell_thread__write_date
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_revision__write_date
msgid "Last Updated on"
msgstr "Frissítve"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/chart/side_panels/common/config_panel.xml:0
#: code:addons/spreadsheet_edition/static/src/bundle/list/side_panels/list_details_side_panel.xml:0
#: code:addons/spreadsheet_edition/static/src/bundle/pivot/side_panels/pivot_details_side_panel.xml:0
msgid "Last updated at"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/chart/side_panels/odoo_chart_with_axis/design_panel.xml:0
msgid "Left"
msgstr "Bal"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/chart/side_panels/odoo_chart_with_axis/design_panel.xml:0
msgid "Legend position"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/chart/side_panels/index.js:0
msgid "Line"
msgstr "Sor"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/chart/side_panels/odoo_chart_with_axis/design_panel.xml:0
msgid "Linear"
msgstr "Lineáris"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/ir_ui_menu/index.js:0
msgid "Link an Odoo menu"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/insert_action_link_menu/insert_action_link_menu.xml:0
msgid "Link menu in spreadsheet"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/chart/odoo_menu/chart_panel.xml:0
msgid "Link to Odoo menu"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/list/side_panels/list_details_side_panel.xml:0
msgid "List Name"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/list/side_panels/list_details_side_panel.js:0
msgid ""
"List duplicated. Use the \"Re-insert list\" menu item to insert it in a "
"sheet."
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/list/side_panels/list_details_side_panel.js:0
msgid "List duplication failed"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/list/index.js:0
msgid "List properties"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/version_history/side_panel/version_history_side_panel.xml:0
msgid "Load More"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/chart/side_panels/odoo_chart_with_axis/design_panel.xml:0
msgid "Logarithmic"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/o_spreadsheet/menu_item_registry.js:0
#: code:addons/spreadsheet_edition/static/src/bundle/version_history/side_panel/version_history_item.js:0
msgid "Make a copy"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/version_history/restore_version_dialog/restore_version_dialog.xml:0
msgid "Make a copy instead"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/filter_editor_side_panel.xml:0
msgid "Match this filter to a field for each data source"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/ir_menu_selector/ir_menu_selector.js:0
msgid "Menu Items"
msgstr "Menüelemek"

#. module: spreadsheet_edition
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_cell_thread__message_has_error
msgid "Message Delivery error"
msgstr "Üzenetkézbesítési hiba"

#. module: spreadsheet_edition
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_cell_thread__message_ids
msgid "Messages"
msgstr "Üzenetek"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/list/plugins/list_autofill_plugin.js:0
msgid "Missing list #%s"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/pivot/plugins/pivot_autofill_plugin.js:0
msgid "Missing pivot"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/pivot/plugins/pivot_autofill_plugin.js:0
msgid "Missing pivot #%s"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/chart/side_panels/common/config_panel.xml:0
#: code:addons/spreadsheet_edition/static/src/bundle/list/side_panels/list_details_side_panel.xml:0
#: code:addons/spreadsheet_edition/static/src/bundle/pivot/side_panels/pivot_details_side_panel.xml:0
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_revision__res_model
msgid "Model"
msgstr "Modell"

#. module: spreadsheet_edition
#: model:ir.model,name:spreadsheet_edition.model_ir_model
msgid "Models"
msgstr "Modellek"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/pivot_view/pivot_view.js:0
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/date_filter_editor_side_panel.js:0
msgid "Month"
msgstr "Hónap"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/pivot_view/pivot_view.js:0
msgid "Month & Year"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/date_filter_editor_side_panel.js:0
msgid "Month / Quarter"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/date_filter_editor_side_panel.xml:0
msgid "Months"
msgstr "Hónap"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/components/spreadsheet_selector_dialog/spreadsheet_selector_dialog.js:0
msgid "Name of the %s:"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/version_history/side_panel/version_history_item.js:0
msgid "Name this version"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/o_spreadsheet/menu_item_registry.js:0
msgid "New"
msgstr "Új"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/filter_editor_side_panel.js:0
msgid "New %s filter"
msgstr ""

#. module: spreadsheet_edition
#. odoo-python
#: code:addons/spreadsheet_edition/models/spreadsheet_cell_thread.py:0
msgid "New Mention in %s"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/components/spreadsheet_selector_dialog/spreadsheet_selector_panel.js:0
msgid "New sheet inserted in '%s'"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_field_offset.js:0
msgid "Next"
msgstr "Következő"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/components/spreadsheet_selector_grid/spreadsheet_selector_grid.xml:0
msgid "No preview"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/chart/side_panels/odoo_chart_with_axis/design_panel.xml:0
msgid "None"
msgstr "Nincs"

#. module: spreadsheet_edition
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_cell_thread__message_needaction_counter
msgid "Number of Actions"
msgstr "Akciók száma"

#. module: spreadsheet_edition
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_cell_thread__message_has_error_counter
msgid "Number of errors"
msgstr "Hibák száma"

#. module: spreadsheet_edition
#: model:ir.model.fields,help:spreadsheet_edition.field_spreadsheet_cell_thread__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Üzenetek száma, melyek akciót igényelnek"

#. module: spreadsheet_edition
#: model:ir.model.fields,help:spreadsheet_edition.field_spreadsheet_cell_thread__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Kézbesítési hibával rendelkező üzenetek száma"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/actions/input_dialog/input_dialog.js:0
#: code:addons/spreadsheet_edition/static/src/bundle/actions/version_history/version_history_action.js:0
msgid "Odoo Spreadsheet"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/comments/components/cell_thread_popover.xml:0
msgid "Open all comments"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/list/side_panels/edit_list_sorting_section/edit_list_sorting_section.xml:0
msgid "Order"
msgstr "Rendelés"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/actions/version_history/version_history_action.js:0
msgid "Original data"
msgstr ""

#. module: spreadsheet_edition
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_revision__parent_revision_id
msgid "Parent Revision"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_field_offset.xml:0
msgid "Period offset"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_field_offset.js:0
msgid "Period offset applied to this source"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/chart/side_panels/index.js:0
msgid "Pie"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/pivot_view/pivot_view.js:0
msgid "Pivot contains duplicate groupbys"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/actions/input_dialog/input_dialog.js:0
msgid "Please enter a valid number."
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/chart/side_panels/odoo_chart_with_axis/design_panel.xml:0
msgid "Polynomial"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/relation_filter_editor_side_panel.xml:0
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/text_filter_editor_side_panel.xml:0
msgid "Possible values"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_field_offset.js:0
msgid "Previous"
msgstr "Előző"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/o_spreadsheet/menu_item_registry.js:0
msgid "Print"
msgstr "Nyomtatás"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/pivot_view/pivot_view.js:0
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/date_filter_editor_side_panel.js:0
msgid "Quarter"
msgstr "Negyedév"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/pivot_view/pivot_view.js:0
msgid "Quarter & Year"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/date_filter_editor_side_panel.xml:0
msgid "Quarters"
msgstr ""

#. module: spreadsheet_edition
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_cell_thread__rating_ids
msgid "Ratings"
msgstr "Értékelések"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/o_spreadsheet/menu_item_registry.js:0
msgid "Re-insert dynamic pivot"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/actions/abstract_spreadsheet_action.js:0
#: code:addons/spreadsheet_edition/static/src/bundle/o_spreadsheet/menu_item_registry.js:0
msgid "Re-insert list"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/o_spreadsheet/menu_item_registry.js:0
msgid "Re-insert static pivot"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/comments/side_panel/comment_threads_side_panel.js:0
msgid "Re-open this thread"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/list/plugins/list_autofill_plugin.js:0
msgid "Record #%(record_number)s"
msgstr ""

#. module: spreadsheet_edition
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_revision__res_id
msgid "Record id"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/o_spreadsheet/menu_item_registry.js:0
msgid "Refresh all data"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/relation_filter_editor_side_panel.xml:0
msgid "Related model"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/global_filter_side_panel.xml:0
msgid "Relation"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/date_filter_editor_side_panel.js:0
msgid "Relative Period"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/filter_editor_side_panel.xml:0
msgid "Remove"
msgstr "Eltávolítás"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/actions/control_panel/spreadsheet_name.xml:0
#: code:addons/spreadsheet_edition/static/src/bundle/version_history/side_panel/version_history_item.js:0
msgid "Rename"
msgstr "Átnevezés"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_field_offset.js:0
msgid "Requires a selected field"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/comments/side_panel/comment_threads_side_panel.js:0
msgid "Resolve this thread"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/comments/side_panel/comment_threads_side_panel.xml:0
msgid "Resolved"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/version_history/side_panel/version_history_item.js:0
msgid "Restore this version"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/text_filter_editor_side_panel.xml:0
msgid "Restrict values to a range"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/relation_filter_editor_side_panel.xml:0
msgid "Restrict values with a domain"
msgstr ""

#. module: spreadsheet_edition
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_revision__revision_uuid
msgid "Revision Uuid"
msgstr ""

#. module: spreadsheet_edition
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_revision__name
msgid "Revision name"
msgstr ""

#. module: spreadsheet_edition
#: model:ir.actions.act_window,name:spreadsheet_edition.spreadsheet_revision_action
#: model:ir.ui.menu,name:spreadsheet_edition.menu_technical_spreadsheet_revision
msgid "Revisions"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/chart/side_panels/odoo_chart_with_axis/design_panel.xml:0
msgid "Right"
msgstr "Jobb"

#. module: spreadsheet_edition
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_cell_thread__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS kézbesítési hiba"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/filter_editor_side_panel.xml:0
msgid "Save"
msgstr "Mentés"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/o_spreadsheet/menu_item_registry.js:0
msgid "Save as template"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/components/collaborative_status/collaborative_status.xml:0
msgid "Saved"
msgstr "Mentve"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/components/collaborative_status/collaborative_status.xml:0
msgid "Saving"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/components/spreadsheet_selector_dialog/spreadsheet_selector_panel.xml:0
msgid "Search..."
msgstr "Keresés..."

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/list/index.js:0
msgid "See list properties"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/pivot/index.js:0
msgid "See pivot properties"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/version_history/index.js:0
msgid "See version history"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/ir_menu_selector/ir_menu_selector.js:0
msgid "Select a menu..."
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/components/spreadsheet_selector_dialog/spreadsheet_selector_dialog.js:0
msgid "Select a spreadsheet to insert your %s."
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/ir_menu_selector/ir_menu_selector.xml:0
msgid "Select an Odoo menu to link in your spreadsheet"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/actions/abstract_spreadsheet_action.js:0
msgid "Select the number of records to insert"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/comments/components/composer_patch.js:0
msgid "Send"
msgstr "Küldés"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/index.js:0
msgid "Set as filter"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/chart/side_panels/odoo_chart_with_axis/design_panel.xml:0
msgid "Show trend line"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/chart/side_panels/odoo_chart_with_axis/design_panel.xml:0
msgid "Show values"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/o_spreadsheet/menu_item_registry.js:0
msgid "Snapshot"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/filter_editor_side_panel.js:0
msgid "Some required fields are not valid"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/list/side_panels/edit_list_sorting_section/edit_list_sorting_section.xml:0
#: code:addons/spreadsheet_edition/static/src/bundle/pivot/side_panels/pivot_details_side_panel.xml:0
msgid "Sorting"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/spreadsheet_cog_menu/spreadsheet_cog_menu.xml:0
#: code:addons/spreadsheet_edition/static/src/bundle/components/spreadsheet_navbar/spreadsheet_navbar.xml:0
#: model:ir.ui.menu,name:spreadsheet_edition.menu_technical_spreadsheet
msgid "Spreadsheet"
msgstr ""

#. module: spreadsheet_edition
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_dashboard__spreadsheet_revision_ids
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_dashboard_share__spreadsheet_revision_ids
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_mixin__spreadsheet_revision_ids
msgid "Spreadsheet Revision"
msgstr ""

#. module: spreadsheet_edition
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_dashboard__spreadsheet_snapshot
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_dashboard_share__spreadsheet_snapshot
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_mixin__spreadsheet_snapshot
msgid "Spreadsheet Snapshot"
msgstr ""

#. module: spreadsheet_edition
#: model:ir.model,name:spreadsheet_edition.model_spreadsheet_cell_thread
msgid "Spreadsheet discussion thread"
msgstr ""

#. module: spreadsheet_edition
#: model:ir.model,name:spreadsheet_edition.model_spreadsheet_mixin
msgid "Spreadsheet mixin"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/chart/side_panels/index.js:0
msgid "Stacked Area"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/chart/side_panels/index.js:0
msgid "Stacked Column"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/chart/side_panels/index.js:0
msgid "Stacked Line"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/global_filter_side_panel.xml:0
msgid "Text"
msgstr "Szöveg"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/actions/version_history/version_history_action.js:0
msgid ""
"The history of your spreadsheet is corrupted and you are likely missing "
"recent revisions. This feature cannot be used."
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/chart/side_panels/common/config_panel.js:0
msgid ""
"The model (%(model)s) of this chart is not valid (it may have been "
"renamed/deleted). Please re-insert a new chart."
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/list/side_panels/list_details_side_panel.js:0
msgid ""
"The model (%(model)s) of this list is not valid (it may have been "
"renamed/deleted). Please re-insert a new list."
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/pivot/side_panels/pivot_details_side_panel.js:0
msgid ""
"The model (%(model)s) of this pivot is not valid (it may have been "
"renamed/deleted). Please re-insert a new pivot."
msgstr ""

#. module: spreadsheet_edition
#. odoo-python
#: code:addons/spreadsheet_edition/models/spreadsheet_mixin.py:0
msgid ""
"The operation could not be applied because of a concurrent update. Please "
"try again."
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/actions/version_history/version_history_action.js:0
msgid ""
"There are missing revisions that prevent to restore the whole edition history.\n"
"\n"
"Would you like to load the more recent modifications?"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/version_history/side_panel/version_history_side_panel.xml:0
msgid "There are no prior revisions for this spreadsheet."
msgstr ""

#. module: spreadsheet_edition
#: model:ir.model.constraint,message:spreadsheet_edition.constraint_spreadsheet_revision_initial_unique
msgid "There can be only one initial revision per spreadsheet"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/list/side_panels/list_details_side_panel.js:0
#: code:addons/spreadsheet_edition/static/src/bundle/o_spreadsheet/icons.xml:0
msgid "This list is not used"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/pivot/side_panels/pivot_details_side_panel.js:0
msgid "This pivot is not used"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/comments/side_panel/comment_threads_side_panel.xml:0
msgid "This sheet"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/date_filter_editor_side_panel.xml:0
msgid "Time range"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/chart/side_panels/odoo_chart_with_axis/design_panel.xml:0
msgid "Top"
msgstr "Felső"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/pivot/plugins/pivot_autofill_plugin.js:0
msgid "Total"
msgstr "Összesen"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/chart/side_panels/odoo_chart_with_axis/design_panel.xml:0
msgid "Type"
msgstr "Típus"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/date_filter_editor_side_panel.xml:0
msgid "Value options"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/version_history/index.js:0
msgid "Version History"
msgstr ""

#. module: spreadsheet_edition
#. odoo-python
#: code:addons/spreadsheet_edition/models/spreadsheet_mixin.py:0
msgid "Version restored"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/chart/side_panels/odoo_chart_with_axis/design_panel.js:0
msgid "Vertical axis"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/chart/side_panels/odoo_chart_with_axis/design_panel.xml:0
msgid "Vertical axis position"
msgstr ""

#. module: spreadsheet_edition
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_cell_thread__website_message_ids
msgid "Website Messages"
msgstr "Weboldal üzenetek"

#. module: spreadsheet_edition
#: model:ir.model.fields,help:spreadsheet_edition.field_spreadsheet_cell_thread__website_message_ids
msgid "Website communication history"
msgstr "Weboldal kommunikációs előzmények"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/pivot_view/pivot_view.js:0
msgid "Week"
msgstr "Hét"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/pivot_view/pivot_view.js:0
msgid "Week & Year"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/pivot_view/pivot_view.js:0
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/date_filter_editor_side_panel.js:0
msgid "Year"
msgstr "Év"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/version_history/restore_version_dialog/restore_version_dialog.xml:0
msgid "Yes, restore"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/comments/side_panel/comment_threads_side_panel.xml:0
msgid "activeSheet"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/comments/side_panel/comment_threads_side_panel.xml:0
msgid "allSheets"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/pivot/side_panels/pivot_details_side_panel.js:0
msgid "ascending"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/chart/side_panels/odoo_chart_with_axis/design_panel.xml:0
msgid "bottom"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/pivot/side_panels/pivot_details_side_panel.js:0
msgid "descending"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/chart/side_panels/odoo_chart_with_axis/design_panel.xml:0
msgid "exponential"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/list/side_panels/edit_list_sorting_section/edit_list_sorting_section.xml:0
msgid "false"
msgstr "hamis"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/components/spreadsheet_selector_dialog/spreadsheet_selector_dialog.js:0
msgid "graph"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/chart/side_panels/odoo_chart_with_axis/design_panel.xml:0
msgid "left"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/chart/side_panels/odoo_chart_with_axis/design_panel.xml:0
msgid "linear"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/components/spreadsheet_selector_dialog/spreadsheet_selector_dialog.js:0
msgid "link"
msgstr "hivatkozás"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/components/spreadsheet_selector_dialog/spreadsheet_selector_dialog.js:0
msgid "list"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/chart/side_panels/odoo_chart_with_axis/design_panel.xml:0
msgid "logarithmic"
msgstr ""

#. module: spreadsheet_edition
#: model_terms:ir.ui.view,arch_db:spreadsheet_edition.mail_notification_layout
msgid "mentioned you in a comment:"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/chart/side_panels/common/config_panel.js:0
#: code:addons/spreadsheet_edition/static/src/bundle/list/side_panels/list_details_side_panel.js:0
#: code:addons/spreadsheet_edition/static/src/bundle/pivot/side_panels/pivot_details_side_panel.js:0
msgid "never"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/chart/side_panels/odoo_chart_with_axis/design_panel.xml:0
msgid "none"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/components/spreadsheet_selector_dialog/spreadsheet_selector_dialog.js:0
msgid "pivot"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/chart/side_panels/odoo_chart_with_axis/design_panel.xml:0
msgid "polynomial"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/components/spreadsheet_selector_dialog/spreadsheet_selector_dialog.xml:0
msgid "records of the list."
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/chart/side_panels/odoo_chart_with_axis/design_panel.xml:0
msgid "right"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/chart/side_panels/odoo_chart_with_axis/design_panel.xml:0
msgid "top"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/list/side_panels/edit_list_sorting_section/edit_list_sorting_section.xml:0
msgid "true"
msgstr "igaz"

#. module: spreadsheet_edition
#: model:ir.model,name:spreadsheet_edition.model_ir_websocket
msgid "websocket message handling"
msgstr ""
