# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* survey
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Polish (https://app.transifex.com/odoo/teams/41243/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__question_count
msgid "# Questions"
msgstr "# Pytania"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__random_questions_count
msgid "# Questions Randomly Picked"
msgstr "# Losowo wybrane pytania"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_4
msgid "$100"
msgstr "100 USD"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_1
msgid "$20"
msgstr "20 USD"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_5
msgid "$200"
msgstr "200 USD"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_6
msgid "$300"
msgstr "300 USD"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_2
msgid "$50"
msgstr "50 USD"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_3
msgid "$80"
msgstr "80 USD"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_progression
msgid "% completed"
msgstr "% zakończone"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
msgid "%(participant)s just participated in \"%(survey_title)s\"."
msgstr ""

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid "%s (copy)"
msgstr "%s (kopiuj)"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid "%s certification passed"
msgstr "%s certyfikacja zaliczona"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid "%s challenge certification"
msgstr "%s certyfikacja wyzwania"

#. module: survey
#: model:ir.actions.report,print_report_name:survey.certification_report
msgid "'Certification - %s' % (object.survey_id.display_name)"
msgstr "'Certyfikacja - %s' % (object.survey_id.display_name)"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid "0000000010"
msgstr ""

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q2_sug2
msgid "10 kg"
msgstr "10 kg"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q3_sug2
msgid "100 years"
msgstr "100 lat"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q1_sug3
msgid "1055"
msgstr "1055"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q3_sug3
msgid "116 years"
msgstr "116 lat"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q1_sug1
msgid "1227"
msgstr "1227"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q3_sug4
msgid "127 years"
msgstr "127 lat"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q1_sug2
msgid "1324"
msgstr "1324"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q1_sug1
msgid "1450 km"
msgstr "1450 km"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q2_sug3
msgid "16.2 kg"
msgstr "16.2 kg"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid "2023-08-18"
msgstr "2023-08-18"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q1_sug2
msgid "3700 km"
msgstr "3700 km"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_403_page
msgid "403: Forbidden"
msgstr "403: Odmowa dostępu"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q2_sug4
msgid "47 kg"
msgstr "47 kg"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q2_sug1
msgid "5.7 kg"
msgstr "5.7 kg"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q1_sug3
msgid "6650 km"
msgstr "6650 km"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q3_sug1
msgid "99 years"
msgstr "99 lat"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid ""
"<b>Certificate</b>\n"
"                            <br/>"
msgstr ""
"<b>Certyfikat</b>\n"
"                            <br/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid ""
"<br/>\n"
"                                    <span class=\"text-muted\">Completed</span>"
msgstr ""
"<br/>\n"
"<span class=\"text-muted\">Ukończono</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid ""
"<br/>\n"
"                                    <span class=\"text-muted\">Registered</span>"
msgstr ""
"<br/>\n"
"<span class=\"text-muted\">Zarejestrowany</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid "<br/> <span>by</span>"
msgstr "<br/><span>przez</span>"

#. module: survey
#: model:mail.template,body_html:survey.mail_template_certification
msgid ""
"<div style=\"background:#F0F0F0;color:#515166;padding:10px 0px;font-family:Arial,Helvetica,sans-serif;font-size:14px;\">\n"
"    <table style=\"width:600px;margin:5px auto;\">\n"
"        <tbody>\n"
"            <tr><td>\n"
"                <!-- We use the logo of the company that created the survey (to handle multi company cases) -->\n"
"                <a href=\"/\"><img t-if=\"not object.survey_id.create_uid.company_id.uses_default_logo\" t-attf-src=\"/logo.png?company={{ object.survey_id.create_uid.company_id.id }}\" style=\"vertical-align:baseline;max-width:100px;\"/></a>\n"
"            </td><td style=\"text-align:right;vertical-align:middle;\">\n"
"                    Certification: <t t-out=\"object.survey_id.display_name or ''\">Feedback Form</t>\n"
"            </td></tr>\n"
"        </tbody>\n"
"    </table>\n"
"    <table style=\"width:600px;margin:0px auto;background:white;border:1px solid #e1e1e1;\">\n"
"        <tbody>\n"
"            <tr><td style=\"padding:15px 20px 10px 20px;\">\n"
"                <p>Dear <span t-out=\"object.partner_id.name or 'participant'\">participant</span></p>\n"
"                <p>\n"
"                    Please find attached your\n"
"                        <strong t-out=\"object.survey_id.display_name or ''\">Furniture Creation</strong>\n"
"                    certification\n"
"                </p>\n"
"                <p>Congratulations for passing the test with a score of <strong t-out=\"object.scoring_percentage\"/>%!</p>\n"
"            </td></tr>\n"
"        </tbody>\n"
"    </table>\n"
"</div>\n"
"            "
msgstr ""

#. module: survey
#: model:mail.template,body_html:survey.mail_template_user_input_invite
msgid ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dear <t t-out=\"object.partner_id.name or 'participant'\">participant</t><br/><br/>\n"
"        <t t-if=\"object.survey_id.certification\">\n"
"            You have been invited to take a new certification.\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            We are conducting a survey and your response would be appreciated.\n"
"        </t>\n"
"        </p><div style=\"margin: 16px 0px 16px 0px;\">\n"
"            <a t-att-href=\"(object.get_start_url())\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                <t t-if=\"object.survey_id.certification\">\n"
"                    Start Certification\n"
"                </t>\n"
"                <t t-else=\"\">\n"
"                    Start Survey\n"
"                </t>\n"
"            </a>\n"
"        </div>\n"
"        <t t-if=\"object.deadline\">\n"
"            Please answer the survey for <t t-out=\"format_date(object.deadline) or ''\">05/05/2021</t>.<br/><br/>\n"
"        </t>\n"
"        <t t-if=\"object.survey_id.certification\">\n"
"            We wish you good luck!\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            Thank you in advance for your participation.\n"
"        </t>\n"
"    \n"
"</div>\n"
"            "
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "<i class=\"fa fa-bar-chart\"/> Results"
msgstr "Wyniki"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-check-square-o fa-lg me-2\"/>answer"
msgstr "<i class=\"fa fa-check-square-o fa-lg me-2\"/>odpowiedź"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-circle-o  fa-lg me-2\"/>answer"
msgstr "<i class=\"fa fa-circle-o  fa-lg me-2\"/>odpowiedź"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<i class=\"fa fa-circle-o fa-lg\" role=\"img\" aria-label=\"Not checked\" "
"title=\"Not checked\"/>"
msgstr ""
"<i class=\"fa fa-circle-o fa-lg\" role=\"img\" aria-label=\"Niesprawdzone\" "
"title=\"Niesprawdzone\"/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "<i class=\"fa fa-close\"/> Close"
msgstr "Zamknij"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-dot-circle-o fa-lg me-2\"/>answer"
msgstr "<i class=\"fa fa-dot-circle-o fa-lg me-2\"/>odpowiedź"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<i class=\"fa fa-dot-circle-o fa-lg\" role=\"img\" aria-label=\"Checked\" "
"title=\"Checked\"/>"
msgstr ""
"<i class=\"fa fa-dot-circle-o fa-lg\" role=\"img\" aria-label=\"Sprawdzone\""
" title=\"Sprawdzone\"/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_button_form_view
msgid ""
"<i class=\"fa fa-exclamation-triangle\"/> It is currently not possible to "
"pass this assessment because no question is configured to give any points."
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid ""
"<i class=\"fa fa-fw fa-trophy\" role=\"img\" aria-label=\"Download certification\" title=\"Download certification\"/>\n"
"                                        Download certification"
msgstr ""
"<i class=\"fa fa-fw fa-trophy\" role=\"img\" aria-label=\"Download certification\" title=\"Download certification\"/>\n"
"                                        Pobierz certyfikat"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\"/>"
msgstr ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\"/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-square-o fa-lg me-2\"/>answer"
msgstr "<i class=\"fa fa-square-o fa-lg me-2\"/>odpowiedź"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_button_form_view
msgid "<i class=\"oi oi-fw oi-arrow-right\"/>Go to Survey"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid ""
"<span class=\"badge text-bg-secondary only_left_radius px-2 "
"pt-1\">Avg</span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid ""
"<span class=\"badge text-bg-secondary only_left_radius px-2 "
"pt-1\">Max</span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid ""
"<span class=\"badge text-bg-secondary only_left_radius px-2 "
"pt-1\">Min</span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid ""
"<span class=\"fw-bold text-muted ms-2 d-none d-md-inline\" id=\"enter-"
"tooltip\"> or press CTRL+Enter</span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid ""
"<span class=\"fw-bold text-muted ms-2 d-none d-md-inline\">\n"
"                            <span id=\"enter-tooltip\">or press Enter</span>\n"
"                        </span>"
msgstr ""
"<span class=\"fw-bold text-muted ms-2 d-none d-md-inline\">\n"
"                            <span id=\"enter-tooltip\">lub naciśnij Enter</span>\n"
"                        </span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid ""
"<span class=\"fw-bold text-muted ms-2 d-none d-md-inline\">\n"
"                    <span id=\"enter-tooltip\">or press CTRL+Enter</span>\n"
"                </span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "<span class=\"mx-1\">-</span>"
msgstr "<span class=\"mx-1\">-</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.res_partner_view_form
msgid ""
"<span class=\"o_stat_text\" invisible=\"certifications_company_count &lt; 2\">Certifications</span>\n"
"                        <span class=\"o_stat_text\" invisible=\"certifications_company_count &gt; 1\">Certification</span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.res_partner_view_form
msgid ""
"<span class=\"o_stat_text\" invisible=\"certifications_count &lt; 2\">Certifications</span>\n"
"                        <span class=\"o_stat_text\" invisible=\"certifications_count &gt; 1\">Certification</span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid ""
"<span class=\"o_survey_enter fw-bold text-muted ms-2 d-none d-md-inline\">or"
" press Enter</span>"
msgstr ""
"<span class=\"o_survey_enter fw-bold text-muted ms-2 d-none d-md-"
"inline\">lub naciśnij Enter</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_selection_key
msgid ""
"<span class=\"o_survey_key text-center position-absolute bg-white rounded-"
"start py-0 ps-2\"><span class=\"text-primary text-center text-center w-100 "
"position-relative\">Key</span></span>"
msgstr ""
"<span class=\"o_survey_key text-center position-absolute bg-white rounded-"
"start py-0 ps-2\"><span class=\"text-primary text-center text-center w-100 "
"position-relative\">Klucz</span></span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid ""
"<span class=\"o_survey_results_topbar_clear_filters text-primary\">\n"
"                                <i class=\"fa fa-trash me-1\"/>Remove all filters\n"
"                            </span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid ""
"<span class=\"o_survey_session_answer_count\">0</span>\n"
"                                     /"
msgstr ""
"<span class=\"o_survey_session_answer_count\">0</span>\n"
"                                     /"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_open
msgid ""
"<span class=\"o_survey_session_navigation_next_label\">Start</span>\n"
"                        <i class=\"oi oi-chevron-right\"/>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "<span class=\"text-break text-muted\">Completed</span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "<span class=\"text-break text-muted\">Registered</span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "<span class=\"text-break text-muted\">Success</span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "<span class=\"text-muted\">Average Duration</span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "<span class=\"text-muted\">Questions</span>"
msgstr "<span class=\"text-muted\">Pytania</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid "<span class=\"text-muted\">Responded</span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid "<span class=\"text-success\">Correct</span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid "<span class=\"text-warning\">Partial</span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<span invisible=\"not is_scored_question\">Points</span>"
msgstr ""

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p3_q1
msgid ""
"<span>\"Red\" is not a category, I know what you are trying to do ;)</span>"
msgstr "<span>\"Czerwony\" to nie kategoria, wiem co próbujesz zrobić ;)</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "<span>%</span>"
msgstr "<span>%</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "<span>All surveys</span>"
msgstr "<span>Wszystkie ankiety</span>"

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p3_q6
msgid "<span>Best time to do it, is the right time to do it.</span>"
msgstr ""
"<span>Najlepszy czas, aby to zrobić, to właściwy czas, aby to zrobić.</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "<span>Completed surveys</span>"
msgstr "<span>Wypełnione ankiety</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid "<span>Date</span>"
msgstr "<span>Data</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<span>Do you like it?</span><br/>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "<span>Failed only</span>"
msgstr "<span>Tylko niepowodzenie</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<span>How many ...?</span><br/>\n"
"                                    <i class=\"fa fa-2x\" role=\"img\" aria-label=\"Numeric\" title=\"Numeric\">123 </i>\n"
"                                    <i class=\"fa fa-2x fa-sort\" role=\"img\" aria-label=\"Numeric\"/>"
msgstr ""
"<span>Ile ...?</span><br/>\n"
"<i class=\"fa fa-2x\" role=\"img\" aria-label=\"Numeric\" title=\"Numeric\">123 </i>\n"
"<i class=\"fa fa-2x fa-sort\" role=\"img\" aria-label=\"Numeric\"/>"

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p5_q1
msgid ""
"<span>If you don't like us, please try to be as objective as "
"possible.</span>"
msgstr ""
"<span>Jeśli nas nie lubisz, postaraj się być jak najbardziej "
"obiektywny.</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<span>Name all the animals</span><br/>\n"
"                                    <i class=\"fa fa-align-justify fa-4x\" role=\"img\" aria-label=\"Multiple lines\" title=\"Multiple Lines\"/>"
msgstr ""
"<span>Nazwij wszystkie zwierzęta</span><br/>\n"
"<i class=\"fa fa-align-justify fa-4x\" role=\"img\" aria-label=\"Multiple lines\" title=\"Multiple Lines\"/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<span>Name one animal</span><br/>\n"
"                                    <i class=\"fa fa-minus fa-4x\" role=\"img\" aria-label=\"Single Line\" title=\"Single Line\"/>"
msgstr ""
"<span>Nazwij jedno zwierzę</span><br/>\n"
"<i class=\"fa fa-minus fa-4x\" role=\"img\" aria-label=\"Single Line\" title=\"Single Line\"/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_button_retake
msgid "<span>Number of attempts left</span>:"
msgstr "<span>Pozostała liczba prób</span>:"

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p2_q1
msgid "<span>Our famous Leader!</span>"
msgstr "<span>Nasz słynny lider!</span>"

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p3_q3
msgid "<span>Our sales people have an advantage, but you can do it!</span>"
msgstr ""
"<span>Nasi sprzedawcy mają przewagę, ale Ty też możesz to zrobić!</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "<span>Passed and Failed</span>"
msgstr "<span>Zaliczone i niezaliczone</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "<span>Passed only</span>"
msgstr "<span>Tylko zaliczone</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid ""
"<span>This certificate is presented to</span>\n"
"                                <br/>"
msgstr ""
"<span>Ten certyfikat jest wręczany</span>\n"
"<br/>"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "<span>Try It</span>"
msgstr "<span>Wypróbuj</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "<span>Waiting for attendees...</span>"
msgstr "<span>Oczekiwanie na uczestników...</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<span>When does ... start?</span><br/>"
msgstr "<span>Kiedy zaczyna się ...?</span><br/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<span>When is Christmas?</span><br/>"
msgstr "<span>Kiedy jest Boże Narodzenie?</span><br/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<span>Which are yellow?</span><br/>"
msgstr "<span>Które są żółte?</span><br/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<span>Which is yellow?</span><br/>"
msgstr "<span>Który jest żółty?</span><br/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid ""
"<span>for successfully completing</span>\n"
"                                <br/>"
msgstr ""
"<span>za pomyślne ukończenie</span>\n"
"<br/>"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q4
msgid "A \"Citrus\" could give you ..."
msgstr "\"Citrus\" może dać ci ..."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
msgid "A label must be attached to only one question."
msgstr "Etykieta musi być dołączona tylko do jednego pytania."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_positive_len_max
#: model:ir.model.constraint,message:survey.constraint_survey_question_positive_len_min
msgid "A length must be positive!"
msgstr "Długość musi być dodatnia!"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_3_choice_4
msgid "A little bit overpriced"
msgstr "Trochę zawyżone"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_3_choice_5
msgid "A lot overpriced"
msgstr "Dużo zawyżone"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_session_speed_rating_has_time_limit
msgid ""
"A positive default time limit is required when the session rewards quick "
"answers."
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question_answer__answer_score
msgid ""
"A positive score indicates a correct choice; a negative or null score "
"indicates a wrong answer"
msgstr ""
"Pozytywny wynik oznacza poprawny wybór; negatywny lub zerowy wynik oznacza "
"złą odpowiedź"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form
msgid "A problem has occurred"
msgstr "Wystąpił problem"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
msgid "A question can either be skipped or answered, not both."
msgstr "Pytanie można albo pominąć, albo na nie odpowiedzieć, ale nie oba."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid ""
"A scored survey needs at least one question that gives points.\n"
"Please check answers and their scores."
msgstr ""

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p2
msgid "About our ecommerce"
msgstr "O naszym e-commerce"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p1
msgid "About you"
msgstr "O Tobie"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__survey_access_mode
#: model:ir.model.fields,field_description:survey.field_survey_survey__access_mode
msgid "Access Mode"
msgstr "Tryb dostępu"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__access_token
msgid "Access Token"
msgstr "Token dostępu"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_access_token_unique
msgid "Access token should be unique"
msgstr "Token dostępu powinien być unikalny"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_needaction
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_needaction
msgid "Action Needed"
msgstr "Wymagane działanie"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__active
msgid "Active"
msgstr "Aktywne"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_ids
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_ids
msgid "Activities"
msgstr "Czynności"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_exception_decoration
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Dekoracja wyjątku aktywności"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_state
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_state
msgid "Activity State"
msgstr "Stan aktywności"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_type_icon
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ikona typu aktywności"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Add a question"
msgstr "Dodaj pytanie"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Add a section"
msgstr "Dodaj sekcję"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Add existing contacts..."
msgstr "Dodaj istniejące kontakty..."

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "Add some fun to your presentations by sharing questions live"
msgstr ""
"Dodaj trochę zabawy do swoich prezentacji, udostępniając pytania na żywo"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__emails
msgid "Additional emails"
msgstr "Dodatkowe e-maile"

#. module: survey
#: model:res.groups,name:survey.group_survey_manager
msgid "Administrator"
msgstr "Administrator"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q4_sug1
msgid "Africa"
msgstr "Afryka"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q6
msgid ""
"After watching this video, will you swear that you are not going to "
"procrastinate to trim your hedge this year?"
msgstr ""
"Czy po obejrzeniu tego filmu przysięgniesz, że w tym roku nie będziesz "
"zwlekać z przycięciem żywopłotu?"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_col3
msgid "Agree"
msgstr "Zgoda"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_scored_date_have_answers
msgid ""
"All \"Is a scored question = True\" and \"Question Type: Date\" questions "
"need an answer"
msgstr ""
"Wszystkie pytania \"Is a scored question = True\" i \"Question Type: Data\" "
"wymagają odpowiedzi"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_scored_datetime_have_answers
msgid ""
"All \"Is a scored question = True\" and \"Question Type: Datetime\" "
"questions need an answer"
msgstr ""
"Wszystkie pytania \"Is a scored question = True\" i \"Question Type: "
"Datetime\" wymagają odpowiedzi"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__questions_selection__all
msgid "All questions"
msgstr "Wszystkie pytania"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "All surveys"
msgstr "Wszystkie ankiety"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_is_time_limited_have_time_limit
msgid "All time-limited questions need a positive time limit"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Allow Roaming"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__allowed_triggering_question_ids
msgid "Allowed Triggering Questions"
msgstr "Dozwolone pytania wyzwalające"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__allowed_survey_types
msgid "Allowed survey types"
msgstr ""

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q2_sug2
msgid "Amenhotep"
msgstr "Amenhotep"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_user_input_unique_token
msgid "An access token must be unique!"
msgstr "Token dostępu musi być unikalny!"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_positive_answer_score
msgid "An answer score for a non-multiple choice question cannot be negative!"
msgstr "Wynik odpowiedzi na pytanie wielokrotnego wyboru nie może być ujemny!"

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p3
msgid "An apple a day keeps the doctor away."
msgstr "Jedno jabłko dziennie trzyma lekarza z daleka."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_leaderboard
msgid "Anonymous"
msgstr "Anonimowo"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
#: model_terms:ir.ui.view,arch_db:survey.survey_question_answer_view_tree
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Answer"
msgstr "Odpowiedź"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__answer_type
msgid "Answer Type"
msgstr "Typ odpowiedzi"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__deadline
msgid "Answer deadline"
msgstr "Termin odpowiedzi"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question_answer__value_label
msgid ""
"Answer label as either the value itself if not empty or a letter "
"representing the index of the answer otherwise."
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__user_input_line_ids
#: model:ir.model.fields,field_description:survey.field_survey_user_input__user_input_line_ids
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Answers"
msgstr "Odpowiedzi"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_answer_count
msgid "Answers Count"
msgstr "Liczba odpowiedzi"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__access_mode__public
msgid "Anyone with the link"
msgstr "Każdy z linkiem"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_gamification_challenge__challenge_category
msgid "Appears in"
msgstr "Pojawia się w"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q3_sug1
msgid "Apple Trees"
msgstr "Jabłonie"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_row1
msgid "Apples"
msgstr "Jabłka"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Archived"
msgstr "Zarchiwizowane"

#. module: survey
#: model:survey.question,title:survey.survey_demo_food_preferences_q1
msgid "Are you vegetarian?"
msgstr ""

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p5
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p1_q1_sug4
msgid "Art & Culture"
msgstr "Sztuka i kultura"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q1_sug1
msgid "Arthur B. McDonald"
msgstr "Arthur B. McDonald"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q4_sug2
msgid "Asia"
msgstr "Azja"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__survey_type__assessment
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "Assessment"
msgstr "Ocena"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_attachment_count
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_attachment_count
msgid "Attachment Count"
msgstr "Liczba załączników"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__attachment_ids
msgid "Attachments"
msgstr "Załączniki"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__attempts_number
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Attempt n°"
msgstr "Próba nr"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__answer_done_count
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Attempts"
msgstr "Próby"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__attempts_count
msgid "Attempts Count"
msgstr "Próby Ilość"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_user_input__nickname
msgid ""
"Attendee nickname, mainly used to identify them in the survey session "
"leaderboard."
msgstr ""
"Pseudonim uczestnika, używany głównie do identyfikacji w tabeli liderów "
"sesji ankiet."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "Attendees are answering the question..."
msgstr "Uczestnicy odpowiadają na pytanie..."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__session_speed_rating
msgid "Attendees get more points if they answer quickly"
msgstr "Uczestnicy otrzymują więcej punktów, jeśli odpowiedzą szybko"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__author_id
msgid "Author"
msgstr "Autor"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__certification_mail_template_id
msgid ""
"Automated email sent to the user when they succeed the certification, "
"containing their certification document."
msgstr ""
"Zautomatyzowana wiadomość e-mail wysyłana do użytkownika po pomyślnym "
"przejściu certyfikacji, zawierająca jego dokument certyfikacyjny."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_sug3
msgid "Autumn"
msgstr "Jesień"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid "Average"
msgstr "Przeciętny"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__answer_duration_avg
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
msgid "Average Duration"
msgstr "Średni czas trwania"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
msgid "Average Score"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__answer_duration_avg
msgid "Average duration of the survey (in hours)"
msgstr "Średni czas trwania ankiety (w godzinach)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__answer_score_avg
msgid "Avg Score (%)"
msgstr "Średni wynik (%)"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q1_sug3
msgid "Avicii"
msgstr "Avicii"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__background_image
#: model:ir.model.fields,field_description:survey.field_survey_survey__background_image
msgid "Background Image"
msgstr "Obrazek tła"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__background_image_url
#: model:ir.model.fields,field_description:survey.field_survey_survey__background_image_url
msgid "Background Url"
msgstr "URL tła"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.gamification_badge_form_view_simplified
msgid "Badge"
msgstr "Odznaka"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q3_sug3
msgid "Baobab Trees"
msgstr "Drzewa baobabu"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q2_sug1
msgid "Bees"
msgstr "Pszczoły"

#. module: survey
#: model:survey.question,question_placeholder:survey.vendor_certification_page_3_question_3
msgid "Beware of leap years!"
msgstr "Uważaj na lata przestępne!"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Blue Pen"
msgstr "Niebieski długopis"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__body_has_template_value
msgid "Body content is the same as the template"
msgstr "Treść jest taka sama jak w szablonie"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q4_sug4
msgid "Bricks"
msgstr "Cegły"

#. module: survey
#: model:survey.question,question_placeholder:survey.survey_feedback_p1_q1
msgid "Brussels"
msgstr "Bruksela"

#. module: survey
#: model:survey.question,question_placeholder:survey.survey_demo_quiz_p1_q3
msgid "Brussels, Belgium"
msgstr "Bruksela, Belgia"

#. module: survey
#: model:survey.survey,title:survey.survey_demo_burger_quiz
msgid "Burger Quiz"
msgstr "Burger Quiz"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "But first, keep listening to the host."
msgstr "Ale najpierw słuchaj gospodarza."

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_2_choice_3
msgid "Cabinet with Doors"
msgstr "Szafka z drzwiami"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q5_row1
msgid "Cactus"
msgstr "Kaktus"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__can_edit_body
msgid "Can Edit Body"
msgstr "Można edytować treść"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p4_q3
msgid "Can Humans ever directly see a photon?"
msgstr "Czy ludzie mogą kiedykolwiek bezpośrednio zobaczyć foton?"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_tree
msgid "Certification"
msgstr "Certyfikacja"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification_badge_id
msgid "Certification Badge"
msgstr "Odznaka certyfikacyjna"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification_badge_id_dummy
msgid "Certification Badge "
msgstr "Odznaka certyfikacyjna "

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid "Certification Badge is not configured for the survey %(survey_name)s"
msgstr ""
"Odznaka certyfikacyjna nie jest skonfigurowana dla ankiety %(survey_name)s"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid "Certification Failed"
msgstr "Certyfikacja nie powiodła się"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid "Certification n°"
msgstr "Nr certyfikatu"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification_report_layout
msgid "Certification template"
msgstr "Szablon certyfikacji"

#. module: survey
#: model:mail.template,subject:survey.mail_template_certification
msgid "Certification: {{ object.survey_id.display_name }}"
msgstr "Certyfikacja: {{ object.survey_id.display_name }}"

#. module: survey
#: model:ir.actions.report,name:survey.certification_report
#: model:ir.model.fields.selection,name:survey.selection__gamification_challenge__challenge_category__certification
msgid "Certifications"
msgstr "Certyfikacje"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_res_partner__certifications_count
#: model:ir.model.fields,field_description:survey.field_res_users__certifications_count
msgid "Certifications Count"
msgstr "Liczba certyfikacji"

#. module: survey
#: model:ir.actions.act_window,name:survey.res_partner_action_certifications
msgid "Certifications Succeeded"
msgstr "Certyfikacja powiodła się"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "Certified"
msgstr "Certyfikowany"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification_mail_template_id
msgid "Certified Email Template"
msgstr "Certyfikowany szablon wiadomości e-mail"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_2_choice_1
msgid "Chair floor protection"
msgstr "Ochrona podłogi na krzesło"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Cheating on your neighbors will not help!"
msgstr "Oszukiwanie sąsiadów nie pomoże!"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__is_attempts_limited
#: model:ir.model.fields,help:survey.field_survey_user_input__is_attempts_limited
msgid "Check this option if you want to limit the number of attempts per user"
msgstr "Zaznacz tę opcję, jeśli chcesz ograniczyć liczbę prób na użytkownika"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/views/widgets/boolean_update_flag_field/boolean_update_flag_fields.js:0
msgid "Checkbox updating comparison flag"
msgstr ""

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q2_sug2
msgid "China"
msgstr "Chiny"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Choices"
msgstr "Wybory"

#. module: survey
#: model_terms:survey.survey,description:survey.survey_demo_burger_quiz
msgid "Choose your favourite subject and show how good you are. Ready?"
msgstr "Wybierz swój ulubiony przedmiot i pokaż, jaki jesteś dobry. Gotowy?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_food_preferences_q3
msgid "Choose your green meal"
msgstr ""

#. module: survey
#: model:survey.question,title:survey.survey_demo_food_preferences_q4
msgid "Choose your meal"
msgstr ""

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__classic_blue
msgid "Classic Blue"
msgstr "Klasyczny niebieski"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__classic_gold
msgid "Classic Gold"
msgstr "Klasyczne złoto"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__classic_purple
msgid "Classic Purple"
msgstr "Klasyczny fiolet"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_row3
msgid "Clementine"
msgstr "Klementyna"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q1_sug4
msgid "Cliff Burton"
msgstr "Cliff Burton"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Close"
msgstr "Zamknij"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Close Live Session"
msgstr "Zamknięcie sesji na żywo"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_3_choice_1
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "Color"
msgstr "Kolor"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__color
msgid "Color Index"
msgstr "Indeks kolorów"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid ""
"Combining roaming and \"Scoring with answers after each page\" is not possible; please update the following surveys:\n"
"- %(survey_names)s"
msgstr ""

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_question_form
msgid "Come back once you have added questions to your Surveys."
msgstr "Wróć, gdy dodasz pytania do swoich ankiet."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_comments
msgid "Comment"
msgstr "Komentarz"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__comments_message
msgid "Comment Message"
msgstr "Skomentuj Wiadomość"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__comment_count_as_answer
msgid "Comment is an answer"
msgstr "Komentarz jest odpowiedzią"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_res_partner__certifications_company_count
#: model:ir.model.fields,field_description:survey.field_res_users__certifications_company_count
msgid "Company Certifications Count"
msgstr "Liczba certyfikacji firmowych"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input__state__done
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Completed"
msgstr "Ukończona"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "Completed surveys"
msgstr "Ukończone ankiety"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Compose Email"
msgstr "Utwórz wiadomość e-mail"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
msgid "Computing score requires a question in arguments."
msgstr "Obliczanie wyniku wymaga pytania w argumentach."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Conditional display"
msgstr ""

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/views/widgets/survey_question_trigger/survey_question_trigger.js:0
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"Conditional display is not available when questions are randomly picked."
msgstr ""
"Wyświetlanie warunkowe nie jest dostępne, gdy pytania są wybierane losowo."

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_2_choice_3
msgid "Conference chair"
msgstr "Krzesło konferencyjne"

#. module: survey
#: model_terms:web_tour.tour,rainbow_man_message:survey.survey_tour
msgid "Congratulations! You are now ready to collect feedback like a pro :-)"
msgstr ""
"Gratulacje! Jesteś teraz gotowy do zbierania opinii jak profesjonalista :-)"

#. module: survey
#: model_terms:gamification.badge,description:survey.vendor_certification_badge
msgid "Congratulations, you are now official vendor of MyCompany"
msgstr "Gratulacje, jesteś teraz oficjalnym sprzedawcą MyCompany"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "Congratulations, you have passed the test!"
msgstr "Gratulacje, zaliczyłeś test!"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Constraints"
msgstr "Ograniczenia"

#. module: survey
#: model:ir.model,name:survey.model_res_partner
#: model:ir.model.fields,field_description:survey.field_survey_user_input__partner_id
msgid "Contact"
msgstr "Kontakt"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__has_conditional_questions
msgid "Contains conditional questions"
msgstr "Zawiera pytania warunkowe"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__body
msgid "Contents"
msgstr "Zawartość"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "Continue"
msgstr "Kontynuuj"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form
msgid "Continue here"
msgstr "Kontynuuj tutaj"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q3_sug4
msgid "Cookies"
msgstr "Pliki cookie"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_session_manage.js:0
msgid "Copied!"
msgstr "Skopiowane!"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q1_sug3
msgid "Cornaceae"
msgstr "Rogowate"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_2_choice_1
msgid "Corner Desk Right Sit"
msgstr "Biurko narożne, siedzenie po prawej stronie"

#. module: survey
#. odoo-javascript
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
#: code:addons/survey/static/src/js/survey_result.js:0
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__is_correct
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__answer_is_correct
msgid "Correct"
msgstr "Poprawne"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Correct Answer"
msgstr "Prawidłowa odpowiedź"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__answer_datetime
msgid "Correct date and time answer for this question."
msgstr "Prawidłowa data i godzina odpowiedzi na to pytanie."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__answer_date
msgid "Correct date answer"
msgstr "Prawidłowa data odpowiedzi"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__answer_date
msgid "Correct date answer for this question."
msgstr "Prawidłowa data odpowiedzi na to pytanie."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__answer_datetime
msgid "Correct datetime answer"
msgstr "Prawidłowa odpowiedź na pytanie o czas"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__answer_numerical_box
msgid "Correct number answer for this question."
msgstr "Prawidłowa odpowiedź numeryczna dla tego pytania."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__answer_numerical_box
msgid "Correct numerical answer"
msgstr "Prawidłowa odpowiedź liczbowa"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_3_choice_3
msgid "Correctly priced"
msgstr "Prawidłowo wyceniony"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q4_sug3
msgid "Cosmic rays"
msgstr "Promienie kosmiczne"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Create Live Session"
msgstr "Utwórz sesję na żywo"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "Create a custom survey from scratch"
msgstr "Tworzenie niestandardowej ankiety od podstaw"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_question__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_survey__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__create_uid
msgid "Created by"
msgstr "Utworzył(a)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__create_date
#: model:ir.model.fields,field_description:survey.field_survey_question__create_date
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__create_date
#: model:ir.model.fields,field_description:survey.field_survey_survey__create_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input__create_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__create_date
msgid "Created on"
msgstr "Data utworzenia"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid "Creating test token is not allowed for you."
msgstr "Tworzenie tokena testowego jest dla Ciebie niedozwolone."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid ""
"Creating token for anybody else than employees is not allowed for internal "
"surveys."
msgstr ""
"Tworzenie tokenów dla osób innych niż pracownicy nie jest dozwolone w "
"przypadku wewnętrznych ankiet."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid "Creating token for closed/archived surveys is not allowed."
msgstr ""
"Tworzenie tokena dla zakończonych/zarchiwizowanych ankiet jest niemożliwe."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid ""
"Creating token for external people is not allowed for surveys requesting "
"authentication."
msgstr ""
"Tworzenie tokenów dla osób zewnętrznych jest niedozwolone w przypadku ankiet"
" wymagających uwierzytelnienia."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_question_id
msgid "Current Question"
msgstr "Aktualne pytanie"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_question_start_time
msgid "Current Question Start Time"
msgstr "Aktualny czas rozpoczęcia pytania"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_start_time
msgid "Current Session Start Time"
msgstr "Godzina rozpoczęcia bieżącej sesji"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__is_time_limited
msgid "Currently only supported for live sessions."
msgstr "Obecnie obsługiwane są tylko sesje na żywo."

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__survey_type__custom
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "Custom"
msgstr "Własne"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid ""
"Customers will receive a new token and be able to completely retake the "
"survey."
msgstr "Klienci otrzymają nowy token i będą mogli ponownie wypełnić ankietę."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Customers will receive the same token."
msgstr "Klienci otrzymają ten sam token."

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_2_choice_5
msgid "Customizable Lamp"
msgstr "Lampa z możliwością dostosowania"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__is_time_customized
msgid "Customized speed rewards"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid "DEMO_CERTIFIED_NAME"
msgstr ""

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__date
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__date
msgid "Date"
msgstr "Data"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_date
msgid "Date answer"
msgstr "Data odpowiedzi"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__datetime
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__datetime
msgid "Datetime"
msgstr "Czas"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_datetime
msgid "Datetime answer"
msgstr "Czas odpowiedzi"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_user_input__deadline
msgid "Datetime until customer can open the survey and submit answers"
msgstr ""
"Czas, do którego klient będzie mógł otworzyć ankietę i przesłać odpowiedzi"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__deadline
msgid "Deadline"
msgstr "Termin"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__session_speed_rating_time_limit
msgid "Default time given to receive additional points for right answers"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_gamification_challenge__challenge_category
msgid "Define the visibility of the challenge through menus"
msgstr "Definiuj widoczność wyzwania w menu"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "Delete"
msgstr "Usuń"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__description
#: model:ir.model.fields,field_description:survey.field_survey_survey__description
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Description"
msgstr "Opis"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_2_choice_2
msgid "Desk Combination"
msgstr "Kombinacja biurka"

#. module: survey
#: model:ir.actions.act_window,name:survey.survey_user_input_line_action
#: model:ir.ui.menu,name:survey.menu_survey_response_line_form
msgid "Detailed Answers"
msgstr "Szczegółowe odpowiedzi"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_col2
msgid "Disagree"
msgstr "Nie zgadzam się"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__display_name
#: model:ir.model.fields,field_description:survey.field_survey_question__display_name
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__display_name
#: model:ir.model.fields,field_description:survey.field_survey_survey__display_name
#: model:ir.model.fields,field_description:survey.field_survey_user_input__display_name
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__display_name
msgid "Display Name"
msgstr "Nazwa wyświetlana"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__progression_mode
msgid "Display Progress as"
msgstr "Wyświetl postęp jako"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/views/widgets/survey_question_trigger/survey_question_trigger.js:0
msgid "Displayed if \"%s\"."
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Displayed when the answer entered is not valid."
msgstr "Wyświetlane, gdy wprowadzona odpowiedź jest nieprawidłowa."

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1_question_1
msgid "Do we sell Acoustic Bloc Screens?"
msgstr "Czy sprzedajemy ekrany akustyczne?"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p2_q3
msgid "Do you have any other comments, questions, or concerns?"
msgstr "Czy masz jakieś inne uwagi, pytania lub wątpliwości?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1_question_5
msgid "Do you think we have missing products in our catalog? (not rated)"
msgstr "Czy uważasz, że w naszym katalogu brakuje produktów? (nie oceniony)"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q2_sug2
msgid "Dogs"
msgstr "Psy"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q1
msgid "Dogwood is from which family of trees?"
msgstr "Dereń należy do jakiej rodziny drzew?"

#. module: survey
#: model:survey.question,question_placeholder:survey.survey_demo_quiz_p1_q2
msgid "Don't be shy, be wild!"
msgstr "Nie wstydź się, bądź dziki!"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q5_sug1
msgid "Douglas Fir"
msgstr "Jodła Douglas"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_2_choice_4
msgid "Drawer"
msgstr "Szuflada"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Duplicate Question"
msgstr "Zduplikowane pytanie"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "Edit Survey"
msgstr "Edytuj Ankietę"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_void_content
msgid "Edit in backend"
msgstr "Edytuj na backendzie"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__email
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Email"
msgstr "E-mail"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "End Live Session"
msgstr "Zakończenie sesji na żywo"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__description_done
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "End Message"
msgstr "Wiadomość końcowa"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__end_datetime
msgid "End date and time"
msgstr "Data i godzina zakończenia"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_session_manage.js:0
msgid "End of Survey"
msgstr "Koniec ankiety"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_session_code
msgid "Enter Session Code"
msgstr "Wprowadź kod sesji"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__constr_error_msg
msgid "Error message"
msgstr "Komunikat o błędzie"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q4_sug3
msgid "Europe"
msgstr "Europa"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q5_sug3
msgid "European Yew"
msgstr "Cis europejski"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Exclude Tests"
msgstr "Wyklucz testy"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__existing_partner_ids
msgid "Existing Partner"
msgstr "Istniejący partner"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__existing_emails
msgid "Existing emails"
msgstr "Istniejące e-maile"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Extremely likely"
msgstr ""

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q3_sug2
msgid "Eyjafjallajökull (Iceland)"
msgstr "Eyjafjallajökull (Islandia)"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Failed"
msgstr "Niepowodzenie"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "Failed only"
msgstr "Tylko nieudane"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_2_choice_2
msgid "Fanta"
msgstr "Fanta"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#: model:survey.survey,title:survey.survey_feedback
msgid "Feedback Form"
msgstr "Formularz opinii"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q5_row2
msgid "Ficus"
msgstr "Figowiec"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
#: model_terms:ir.ui.view,arch_db:survey.question_result_matrix
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date_or_datetime
#: model_terms:ir.ui.view,arch_db:survey.question_result_text
msgid "Filter surveys"
msgstr "Filtruj ankiety"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_session_manage.js:0
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "Final Leaderboard"
msgstr "Końcowa tabela liderów"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_food_preferences_q4_sug2
msgid "Fish"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_follower_ids
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_follower_ids
msgid "Followers"
msgstr "Obserwatorzy"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_partner_ids
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_partner_ids
msgid "Followers (Partners)"
msgstr "Obserwatorzy (partnerzy)"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__activity_type_icon
#: model:ir.model.fields,help:survey.field_survey_user_input__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Ikona Font awesome np. fa-tasks"

#. module: survey
#: model:survey.survey,title:survey.survey_demo_food_preferences
msgid "Food Preferences"
msgstr ""

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__text_box
msgid "Free Text"
msgstr "Wolny tekst"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_text_box
msgid "Free Text answer"
msgstr "Wolny tekst odpowiedź"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q4
msgid "From which continent is native the Scots pine (pinus sylvestris)?"
msgstr "Z którego kontynentu pochodzi sosna zwyczajna (pinus sylvestris)?"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q1_sug1
msgid "Fruits"
msgstr "Owoce"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3
msgid "Fruits and vegetables"
msgstr "Owoce i warzywa"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid "Functional Training"
msgstr ""

#. module: survey
#: model:ir.model,name:survey.model_gamification_badge
msgid "Gamification Badge"
msgstr "Odznaka grywalizacji"

#. module: survey
#: model:ir.model,name:survey.model_gamification_challenge
msgid "Gamification Challenge"
msgstr "Wyzwanie"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "Gather feedbacks from your employees and customers"
msgstr "Zbieranie informacji zwrotnych od pracowników i klientów"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p2
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p1_q1_sug1
msgid "Geography"
msgstr "Geografia"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification_give_badge
msgid "Give Badge"
msgstr "Daj odznakę"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p2_q3
msgid "Give the list of all types of wood we sell."
msgstr "Podaj listę wszystkich rodzajów drewna, które sprzedajemy."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p5_q1_sug1
msgid "Good"
msgstr "Dobry"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Good luck!"
msgstr "Powodzenia!"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug4
msgid "Good value for money"
msgstr "Dobry stosunek jakości do ceny"

#. module: survey
#: model_terms:survey.survey,description_done:survey.survey_demo_food_preferences
msgid "Got it!"
msgstr ""

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q4_sug2
msgid "Grapefruits"
msgstr "Grejpfruty"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_answer_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_line_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Group By"
msgstr "Grupuj wg"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__existing_mode
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Handle existing"
msgstr "Obsługuj istniejące"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "Handle quiz &amp; certifications"
msgstr "Obsługa quizów i certyfikatów"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q3_sug1
msgid "Hard"
msgstr "Ciężki"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__has_message
#: model:ir.model.fields,field_description:survey.field_survey_user_input__has_message
msgid "Has Message"
msgstr "Ma wiadomość"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__has_image_only_suggested_answer
msgid "Has image only suggested answer"
msgstr ""

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_3_choice_2
msgid "Height"
msgstr "Wysokość"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Help Participants know what to write"
msgstr "Pomóż uczestnikom dowiedzieć się, co napisać"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q2_sug3
msgid "Hemiunu"
msgstr "Hemiunu"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Here, you can overview all the participations."
msgstr "W tym miejscu można przejrzeć wszystkich uczestników."

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug1
msgid "High quality"
msgstr "Wysoka jakość"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p3
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p1_q1_sug2
msgid "History"
msgstr "Historia"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p1_q3
msgid "How frequently do you buy products online?"
msgstr "Jak często kupujesz produkty online?"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "How frequently do you use our products?"
msgstr "Jak często korzystasz z naszych produktów?"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "How good of a presenter are you? Let's find out!"
msgstr "Jak dobrym prezenterem jesteś? Przekonajmy się!"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "How likely are you to recommend the following products to a friend?"
msgstr ""
"Jak bardzo prawdopodobne jest, że polecisz poniższe produkty znajomemu?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p2_q1
msgid "How long is the White Nile river?"
msgstr "Jak długa jest rzeka Nil Biały?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_6
msgid ""
"How many chairs do you think we should aim to sell in a year (not rated)?"
msgstr ""
"Jak myślisz, ile krzeseł powinniśmy sprzedać w ciągu roku (bez oceny)?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_1
msgid "How many days is our money-back guarantee?"
msgstr "Ile dni wynosi gwarancja zwrotu pieniędzy?"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "How many orders did you pass during the last 6 months?"
msgstr "Ile zamówień przekazałeś w ciągu ostatnich 6 miesięcy?"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p1_q4
msgid "How many times did you order products on our website?"
msgstr "Ile razy zamawiałeś produkty na naszej stronie WWW?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1_question_4
msgid "How many versions of the Corner Desk do we have?"
msgstr "Ile mamy wersji biurka narożnego?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p3_q3
msgid "How many years did the 100 years war last?"
msgstr "Ile lat trwała wojna stuletnia?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_2_question_1
msgid "How much do we sell our Cable Management Box?"
msgstr "Jak duża jest nasza skrzynka kablowa, którą sprzedajemy?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q5
msgid "How often should you water those plants"
msgstr "Jak często należy podlewać rośliny?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p1_q4
msgid "How old are you?"
msgstr "Ile masz lat?"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q3_sug4
msgid ""
"I actually don't like thinking. I think people think I like to think a lot. "
"And I don't. I do not like to think at all."
msgstr ""
"Właściwie to nie lubię myśleć. Myślę, że ludzie myślą, że lubię dużo myśleć."
" A tak nie jest. W ogóle nie lubię myśleć."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q3_sug2
msgid ""
"I am fascinated by air. If you remove the air from the sky, all the birds "
"would fall to the ground. And all the planes, too."
msgstr ""
"Jestem zafascynowany powietrzem. Gdyby usunąć powietrze z nieba, wszystkie "
"ptaki spadłyby na ziemię. I wszystkie samoloty też."

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_row5
msgid "I have added products to my wishlist"
msgstr "Dodałem produkty do mojej listy życzeń"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p5_q1_sug4
msgid "I have no idea, I'm a dog!"
msgstr "Nie mam pojęcia, jestem psem!"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q3_sug3
msgid "I've been noticing gravity since I was very young!"
msgstr "Zauważyłem grawitację, odkąd byłem bardzo młody!"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__id
#: model:ir.model.fields,field_description:survey.field_survey_question__id
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__id
#: model:ir.model.fields,field_description:survey.field_survey_survey__id
#: model:ir.model.fields,field_description:survey.field_survey_user_input__id
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__id
msgid "ID"
msgstr "ID"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_exception_icon
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_exception_icon
msgid "Icon"
msgstr "Ikona"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__activity_exception_icon
#: model:ir.model.fields,help:survey.field_survey_user_input__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikona wskazująca na wyjątek aktywności."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__access_token
msgid "Identification token"
msgstr "Token identyfikacji"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__progression_mode
msgid ""
"If Number is selected, it will display the number of questions answered on "
"the total number of question to answer."
msgstr ""
"Jeśli wybrana zostanie liczba, wyświetlona zostanie liczba pytań, na które "
"udzielono odpowiedzi w stosunku do całkowitej liczby pytań, na które należy "
"odpowiedzieć."

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_3
msgid ""
"If a customer purchases a 1 year warranty on 6 January 2020, when do we "
"expect the warranty to expire?"
msgstr ""
"Jeśli klient zakupi roczną gwarancję w dniu 6 stycznia 2020 r., kiedy "
"spodziewamy się wygaśnięcia gwarancji?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_2
msgid ""
"If a customer purchases a product on 6 January 2020, what is the latest day "
"we expect to ship it?"
msgstr ""
"Jeśli klient kupi produkt 6 stycznia 2020 r., jaki jest najpóźniejszy dzień,"
" w którym spodziewamy się go wysłać?"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__message_needaction
#: model:ir.model.fields,help:survey.field_survey_user_input__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Jeśli zaznaczone, nowe wiadomości wymagają twojej uwagi."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__message_has_error
#: model:ir.model.fields,help:survey.field_survey_survey__message_has_sms_error
#: model:ir.model.fields,help:survey.field_survey_user_input__message_has_error
#: model:ir.model.fields,help:survey.field_survey_user_input__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""
"Jeśli zaznaczone, niektóre wiadomości napotkały błędy podczas doręczenia."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__save_as_email
msgid ""
"If checked, this option will save the user's answer as its email address."
msgstr ""
"Jeśli opcja ta jest zaznaczona, odpowiedź użytkownika zostanie zapisana jako"
" jego adres e-mail."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__save_as_nickname
msgid "If checked, this option will save the user's answer as its nickname."
msgstr ""
"Jeśli opcja ta jest zaznaczona, odpowiedź użytkownika zostanie zapisana jako"
" jego pseudonim."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__users_can_go_back
msgid "If checked, users can go back to previous pages."
msgstr "Jeśli zaznaczone, użytkownicy mogą wrócić do poprzednich stron."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__survey_users_login_required
#: model:ir.model.fields,help:survey.field_survey_survey__users_login_required
msgid ""
"If checked, users have to login before answering even with a valid token."
msgstr ""
"Jeśli ta opcja jest zaznaczona, użytkownicy muszą się zalogować, aby "
"odpowiedzieć nawet przy użyciu ważnego tokena."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_container
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "If other, please specify:"
msgstr "Jeśli inne, proszę opisać:"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__questions_selection
msgid ""
"If randomized is selected, add the number of random questions next to the "
"section."
msgstr "Jeśli wybrano opcję losową, dodaj liczbę losowych pytań obok sekcji."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__questions_selection
msgid ""
"If randomized is selected, you can configure the number of random questions "
"by section. This mode is ignored in live session."
msgstr ""
"Jeśli wybrano opcję randomizacji, można skonfigurować liczbę losowych pytań "
"według sekcji. Ten tryb jest ignorowany podczas sesji na żywo."

#. module: survey
#: model:survey.question,question_placeholder:survey.vendor_certification_page_1_question_5
msgid "If yes, explain what you think is missing, give examples."
msgstr "Jeśli tak, wyjaśnij, czego Twoim zdaniem brakuje, podaj przykłady."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__value_image
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
msgid "Image"
msgstr "Obraz"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__value_image_filename
msgid "Image Filename"
msgstr "Nazwa pliku obrazu"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/xml/survey_image_zoomer_templates.xml:0
msgid "Image Zoom Dialog"
msgstr "Okno dialogowe powiększenia obrazu"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q2_sug1
msgid "Imhotep"
msgstr "Imhotep"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug6
msgid "Impractical"
msgstr "Niepraktyczny"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__session_state__in_progress
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input__state__in_progress
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "In Progress"
msgstr "W toku"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q5
msgid "In the list below, select all the coniferous."
msgstr "Z poniższej listy wybierz wszystkie drzewa iglaste."

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q2
msgid "In which country did the bonsai technique develop?"
msgstr "W którym kraju rozwinęła się technika bonsai?"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__is_scored_question
msgid ""
"Include this question as part of quiz scoring. Requires an answer and answer"
" score to be taken into account."
msgstr ""
"Uwzględnienie tego pytania w punktacji quizu. Wymaga odpowiedzi i "
"uwzględnienia wyniku odpowiedzi."

#. module: survey
#. odoo-javascript
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
#: code:addons/survey/static/src/js/survey_result.js:0
msgid "Incorrect"
msgstr "Niepoprawne"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug7
msgid "Ineffective"
msgstr "Nieskuteczny"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_email
msgid "Input must be an email"
msgstr "Dane wejściowe muszą być wiadomościami e-mail"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/views/widgets/integer_update_flag_field/integer_update_flag_fields.js:0
msgid "Integer updating comparison flag"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__invite_token
msgid "Invite token"
msgstr "Token zaproszenia"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__access_mode__token
msgid "Invited people only"
msgstr "Tylko osoby zaproszone"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__is_mail_template_editor
msgid "Is Editor"
msgstr "Jest edytorem"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_is_follower
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_is_follower
msgid "Is Follower"
msgstr "Jest obserwatorem"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Is a Certification"
msgstr "Jest certyfikatem"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__is_page
msgid "Is a page?"
msgstr "Czy strona?"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__is_session_answer
msgid "Is in a Session"
msgstr "Jest w sesji"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__is_placed_before_trigger
msgid "Is misplaced?"
msgstr "Czy jest źle umieszczony?"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Is not a Certification"
msgstr "Nie jest certyfikacją"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_user_input__is_session_answer
msgid "Is that user input part of a survey session or not."
msgstr "Czy dane wejściowe użytkownika są częścią sesji ankiety, czy nie."

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q3
msgid "Is the wood of a coniferous hard or soft?"
msgstr "Czy drewno drzew iglastych jest twarde czy miękkie?"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__is_placed_before_trigger
msgid "Is this question placed before any of its trigger questions?"
msgstr ""

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q2_sug4
msgid "Istanbul"
msgstr "Stambuł"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_food_preferences_q1_sug3
msgid "It depends"
msgstr ""

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "It does not mean anything specific"
msgstr "Nie oznacza to niczego konkretnego"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "It helps attendees focus on what you are saying"
msgstr "Pomaga uczestnikom skupić się na tym, co mówisz"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "It helps attendees remember the content of your presentation"
msgstr "Pomaga uczestnikom zapamiętać treść prezentacji"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "It is a small bit of text, displayed to help participants answer"
msgstr ""
"Jest to niewielki fragment tekstu, wyświetlany, aby pomóc uczestnikom w "
"udzieleniu odpowiedzi"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "It is an option that can be different for each Survey"
msgstr "Jest to opcja, która może być różna dla każdej ankiety"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_row2
msgid "It is easy to find the product that I want"
msgstr "Łatwo jest znaleźć produkt, który chcę"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "It is more engaging for your audience"
msgstr "Jest to bardziej angażujące dla odbiorców"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "It's a Belgian word for \"Management\""
msgstr "To belgijskie słowo oznaczające \"zarządzanie\""

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p5_q1_sug3
msgid "Iznogoud"
msgstr "Iznogoud"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q3_sug1
msgid ""
"I’ve never really wanted to go to Japan. Simply because I don’t like eating "
"fish. And I know that’s very popular out there in Africa."
msgstr ""
"Nigdy tak naprawdę nie chciałem jechać do Japonii. Po prostu dlatego, że nie"
" lubię jeść ryb. A wiem, że w Afryce jest to bardzo popularne."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q2_sug1
msgid "Japan"
msgstr "Japonia"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_session_code
msgid "Join Session"
msgstr "Dołącz do sesji"

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p1_q4
msgid "Just to categorize your answers, don't worry."
msgstr "Aby skategoryzować twoje odpowiedzi, nie martw się."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q1_sug2
msgid "Kim Jong-hyun"
msgstr "Kim Jong-hyun"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q1_sug1
msgid "Kurt Cobain"
msgstr "Kurt Cobain"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Label"
msgstr "Etykieta"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__sequence
msgid "Label Sequence order"
msgstr "Kolejność sekwencji etykiet"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__matrix_row_ids
msgid "Labels used for proposed choices: rows of matrix"
msgstr "Labels used for proposed choices: rows of matrix"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__suggested_answer_ids
msgid ""
"Labels used for proposed choices: simple choice, multiple choice and columns"
" of matrix"
msgstr ""
"Etykiety stosowane do proponowanych wyborów: prosty wybór, wielokrotny wybór"
" i kolumny macierzy"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__lang
msgid "Language"
msgstr "Język"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_2_choice_4
msgid "Large Desk"
msgstr "Duże biurko"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_question__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_survey__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__write_uid
msgid "Last Updated by"
msgstr "Ostatnio aktualizowane przez"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__write_date
#: model:ir.model.fields,field_description:survey.field_survey_question__write_date
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__write_date
#: model:ir.model.fields,field_description:survey.field_survey_survey__write_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input__write_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__write_date
msgid "Last Updated on"
msgstr "Data ostatniej aktualizacji"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__last_displayed_page_id
msgid "Last displayed question/page"
msgstr "Ostatnio wyświetlane pytanie / strona"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Late Activities"
msgstr "Czynności zaległe"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_session_code
msgid "Launch Session"
msgstr "Uruchom sesję"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "Leaderboard"
msgstr "Tablica Wyników"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_access_error
msgid "Leave"
msgstr "Opuść"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_3_choice_4
msgid "Legs"
msgstr "Nogi"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q3_sug2
msgid "Lemon Trees"
msgstr "Drzewa cytrynowe"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Let's get started!"
msgstr "Zaczynajmy!"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Let's give it a spin!"
msgstr "Spróbujmy!"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Let's have a look at your answers!"
msgstr "Przyjrzyjmy się twoim odpowiedziom!"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Let's open the survey you just submitted."
msgstr "Otwórzmy właśnie przesłaną ankietę."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Likely"
msgstr "Prawdopodobnie"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Limit Attempts"
msgstr "Limit prób"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__is_attempts_limited
#: model:ir.model.fields,field_description:survey.field_survey_user_input__is_attempts_limited
msgid "Limited number of attempts"
msgstr "Ograniczona liczba prób"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Live Session"
msgstr "Sesja na żywo"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__session_available
msgid "Live Session available"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Live Sessions"
msgstr "Sesje na żywo"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__survey_type__live_session
msgid "Live session"
msgstr "Sesja na żywo"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_available
msgid "Live session available"
msgstr ""

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Load a <b>sample Survey</b> to get started quickly."
msgstr "Załaduj <b>przykładową ankietę</b>, aby szybko rozpocząć."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_auth_required
msgid "Login required"
msgstr "Wymagane logowanie"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__template_id
msgid "Mail Template"
msgstr "Szablon wiadomości"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__constr_mandatory
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Mandatory Answer"
msgstr "odpowiedź obowiązkowa"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__matrix
msgid "Matrix"
msgstr "Macierz"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__matrix_row_ids
msgid "Matrix Rows"
msgstr "Wiersze matrycy"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__matrix_subtype
msgid "Matrix Type"
msgstr "Typ macierzy"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_validation_date
msgid "Max date cannot be smaller than min date!"
msgstr "Maksymalna data nie może być mniejsza niż data minimalna!"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_validation_datetime
msgid "Max datetime cannot be smaller than min datetime!"
msgstr "Maksymalny czas nie może być mniejszy niż minimalny czas!"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_validation_length
msgid "Max length cannot be smaller than min length!"
msgstr "Maksymalna długość nie może być krótsza niż długość minimalna!"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_validation_float
msgid "Max value cannot be smaller than min value!"
msgstr "Maksymalna wartość nie może być mniejsza niż wartość minimalna!"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Maximum"
msgstr "Maksymalnie"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_max_date
msgid "Maximum Date"
msgstr "Maksymalna data"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_max_datetime
msgid "Maximum Datetime"
msgstr "Maksymalny czas"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_length_max
msgid "Maximum Text Length"
msgstr "Maksymalna długość tekstu"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__scoring_max_obtainable
msgid "Maximum obtainable score"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_max_float_value
msgid "Maximum value"
msgstr "Wartość maksymalna"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_403_page
msgid "Maybe you were looking for"
msgstr "Może szukałeś"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_has_error
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_has_error
msgid "Message Delivery error"
msgstr "Błąd doręczenia wiadomości"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_ids
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_ids
msgid "Messages"
msgstr "Wiadomości"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Min/Max Limits"
msgstr "Limity minimalne/maksymalne"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Minimum"
msgstr "Minimum"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_min_date
msgid "Minimum Date"
msgstr "Minimalna data"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_min_datetime
msgid "Minimum Datetime"
msgstr "Minimalny czas"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_length_min
msgid "Minimum Text Length"
msgstr "Minimalna długość tekstu"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_min_float_value
msgid "Minimum value"
msgstr "Wartość minimalna"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__modern_blue
msgid "Modern Blue"
msgstr "Nowoczesny niebieski"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__modern_gold
msgid "Modern Gold"
msgstr "Nowoczesny złoty"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__modern_purple
msgid "Modern Purple"
msgstr "Nowoczesny fiolet"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q2_sug3
msgid "Mooses"
msgstr "Łosie"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q3_sug4
msgid "Mount Elbrus (Russia)"
msgstr "Góra Elbrus (Rosja)"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q3_sug3
msgid "Mount Etna (Italy - Sicily)"
msgstr "Etna (Włochy - Sycylia)"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q3_sug1
msgid "Mount Teide (Spain - Tenerife)"
msgstr "Góra Teide (Hiszpania - Teneryfa)"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q5_sug4
msgid "Mountain Pine"
msgstr "Sosna górska"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__text_box
msgid "Multiple Lines Text Box"
msgstr "Wieloliniowe pole tekstowe"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Multiple choice with multiple answers"
msgstr "Wielokrotny wybór z wieloma odpowiedziami"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Multiple choice with one answer"
msgstr "Wielokrotny wybór z jedną odpowiedzią"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__multiple_choice
msgid "Multiple choice: multiple answers allowed"
msgstr "Wielokrotny wybór: dozwolona wielokrotna odpowiedź"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__simple_choice
msgid "Multiple choice: only one answer"
msgstr "Wielokrotny wybór: tylko jedna odpowiedź"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__matrix_subtype__multiple
msgid "Multiple choices per row"
msgstr "Wiele opcji w rzędzie"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__my_activity_date_deadline
#: model:ir.model.fields,field_description:survey.field_survey_user_input__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Ostateczny terminin moich aktywności"

#. module: survey
#: model:gamification.badge,name:survey.vendor_certification_badge
msgid "MyCompany Vendor"
msgstr "Sprzedawca MyCompany"

#. module: survey
#: model:survey.survey,title:survey.vendor_certification
msgid "MyCompany Vendor Certification"
msgstr "Certyfikacja dostawcy mojej firmy"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Neutral"
msgstr "Neutralne"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Never (less than once a month)"
msgstr "Nigdy (rzadziej niż raz w miesiącu)"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input__state__new
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "New"
msgstr "Nowe"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q2_sug3
msgid "New York"
msgstr "Nowy Jork"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_invite__existing_mode__new
msgid "New invite"
msgstr "Nowe zaproszenie"

#. module: survey
#: model:mail.message.subtype,description:survey.mt_survey_survey_user_input_completed
msgid "New participation completed."
msgstr ""

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_session_manage.js:0
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "Next"
msgstr "Następny"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_calendar_event_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Następna Czynność wydarzenia w kalendarzu"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_date_deadline
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Termin kolejnej czynności"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_summary
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_summary
msgid "Next Activity Summary"
msgstr "Podsumowanie kolejnej czynności"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_type_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_type_id
msgid "Next Activity Type"
msgstr "Typ następnej czynności"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "Next Skipped"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__nickname
msgid "Nickname"
msgstr "Pseudonim"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_food_preferences_q1_sug2
#: model:survey.question.answer,value:survey.survey_demo_food_preferences_q2_sug2
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q6_sug2
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_1_choice_1
msgid "No"
msgstr "Nie"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_question_form
msgid "No Questions yet!"
msgstr "Nie ma jeszcze pytań!"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "No Survey Found"
msgstr "Nie znaleziono ankiety"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_user_input
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid "No answers yet!"
msgstr "Nie ma jeszcze odpowiedzi!"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid "No attempts left."
msgstr "Brak prób."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_void_content
msgid "No question yet, come back later."
msgstr "Nie mam jeszcze pytań, wróć później."

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__scoring_type__no_scoring
msgid "No scoring"
msgstr "Brak punktacji"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.survey_question_answer_action
msgid "No survey labels found"
msgstr "Nie znaleziono etykiet ankiety"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.survey_user_input_line_action
msgid "No user input lines found"
msgstr "Nie znaleziono wypenionych pozycji"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q3_sug2
msgid "No, it's too small for the human eye."
msgstr ""

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q5_sug2
msgid "Norway Spruce"
msgstr "Świerk norweski"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p5_q1_sug2
msgid "Not Good, Not Bad"
msgstr "Nie jest dobrze, nie jest źle"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Not likely at all"
msgstr ""

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Now that you are done, submit your form."
msgstr "Po zakończeniu prześlij swój formularz."

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Now, use this shortcut to go back to the survey."
msgstr "Teraz użyj tego skrótu, aby wrócić do ankiety."

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__progression_mode__number
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__numerical_box
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__scale
msgid "Number"
msgstr "Numer"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_needaction_counter
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_needaction_counter
msgid "Number of Actions"
msgstr "Liczba akcji"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__attempts_limit
#: model:ir.model.fields,field_description:survey.field_survey_user_input__attempts_limit
msgid "Number of attempts"
msgstr "Liczba prób"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_3_choice_5
msgid "Number of drawers"
msgstr "Liczba szuflad"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_has_error_counter
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_has_error_counter
msgid "Number of errors"
msgstr "Liczba błędów"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__message_needaction_counter
#: model:ir.model.fields,help:survey.field_survey_user_input__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Liczba wiadomości wymagających akcji"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__message_has_error_counter
#: model:ir.model.fields,help:survey.field_survey_user_input__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Liczba wiadomości z błędami przy doręczeniu"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__numerical_box
msgid "Numerical Value"
msgstr "Wartości liczbowe"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_numerical_box
msgid "Numerical answer"
msgstr "Numeryczna odpowiedź"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Object-Directed Open Organization"
msgstr "Otwarta organizacja kierowana obiektem"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date_or_datetime
msgid "Occurrence"
msgstr "Występowanie"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid "Odoo"
msgstr "Odoo"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Odoo Certification"
msgstr "Certyfikacja Odoo"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_2_choice_5
msgid "Office Chair Black"
msgstr "Krzesło biurowe czarne"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Often (1-3 times per week)"
msgstr "Często (1-3 razy w tygodniu)"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid ""
"On Survey questions, one can define \"placeholders\". But what are they for?"
msgstr ""
"W pytaniach ankietowych można zdefiniować \"symbole zastępcze\". Ale do "
"czego one służą?"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p1_q3_sug1
msgid "Once a day"
msgstr "Raz dziennie"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q5_sug1
#: model:survey.question.answer,value:survey.survey_feedback_p1_q3_sug3
msgid "Once a month"
msgstr "Raz w miesiącu"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q5_sug2
#: model:survey.question.answer,value:survey.survey_feedback_p1_q3_sug2
msgid "Once a week"
msgstr "Raz w tygodniu"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p1_q3_sug4
msgid "Once a year"
msgstr "Raz w roku"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__matrix_subtype__simple
msgid "One choice per row"
msgstr "Jeden wybór w rzędzie"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "One needs to answer at least half the questions correctly"
msgstr "Należy odpowiedzieć poprawnie na co najmniej połowę pytań"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "One needs to get 50% of the total score"
msgstr "Należy uzyskać 50% całkowitego wyniku"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__questions_layout__page_per_question
msgid "One page per question"
msgstr "Jedna strona na pytanie"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__questions_layout__page_per_section
msgid "One page per section"
msgstr "Jedna strona na sekcję"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__questions_layout__one_page
msgid "One page with all the questions"
msgstr "Jedna strona ze wszystkimi pytaniami"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Only a single question left!"
msgstr "Zostało tylko jedno pytanie!"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
#: model_terms:ir.ui.view,arch_db:survey.question_result_matrix
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date_or_datetime
#: model_terms:ir.ui.view,arch_db:survey.question_result_text
msgid "Only show survey results having selected this answer"
msgstr "Pokaż wyniki ankiety tylko po wybraniu tej odpowiedzi"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid "Only survey users can manage sessions."
msgstr "Tylko użytkownicy ankiety mogą zarządzać sesjami."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_session_code
msgid "Oops! No survey matches this code."
msgstr "Ups! Żadna ankieta nie pasuje do tego kodu."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_access_error
msgid ""
"Oopsie! We could not let you open this survey. Make sure you are using the correct link and are allowed to\n"
"                        participate or get in touch with its organizer."
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Open Session Manager"
msgstr "Menedżer otwartych sesji"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/question_page/description_page_field.xml:0
msgid "Open section"
msgstr "Sekcja otwarta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Optional previous answers required"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"Opcjonalny język tłumaczenia (kod ISO) do wyboru podczas wysyłania "
"wiadomości e-mail. Jeśli nie jest ustawiony, zostanie użyta wersja "
"angielska. Powinno to być zazwyczaj wyrażenie placeholder, które zapewnia "
"odpowiedni język, np. {{ object.partner_id.lang }}."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Options"
msgstr "Opcje"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Organizational Development for Operation Officers"
msgstr "Rozwój organizacyjny dla oficerów operacyjnych"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
msgid "Other (see comments)"
msgstr "Inne (patrz komentarze)"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p2
msgid "Our Company in a few questions ..."
msgstr "Nasza firma w kilku pytaniach ..."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__mail_server_id
msgid "Outgoing mail server"
msgstr "Serwer poczty wychodzącej"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_result.js:0
msgid "Overall Performance"
msgstr "Wynik całkowity"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug5
msgid "Overpriced"
msgstr "Zawyżone ceny"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__page_id
msgid "Page"
msgstr "Strona"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__page_ids
msgid "Pages"
msgstr "Strony"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__questions_layout
msgid "Pagination"
msgstr "Paginacja"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q2_sug4
msgid "Papyrus"
msgstr "Papirus"

#. module: survey
#. odoo-javascript
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
#: code:addons/survey/static/src/js/survey_result.js:0
msgid "Partially"
msgstr "Częściowe"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Participant"
msgstr "Uczestnik"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Participants"
msgstr "Uczestnicy"

#. module: survey
#. odoo-python
#: code:addons/survey/wizard/survey_invite.py:0
msgid "Participate to %(survey_name)s"
msgstr "Weź udział w %(survey_name)s"

#. module: survey
#: model:mail.template,subject:survey.mail_template_user_input_invite
msgid "Participate to {{ object.survey_id.display_name }} survey"
msgstr "Weź udział w ankiecie {{ object.survey_id.display_name }}"

#. module: survey
#: model:mail.message.subtype,name:survey.mt_survey_survey_user_input_completed
#: model:mail.message.subtype,name:survey.mt_survey_user_input_completed
msgid "Participation completed"
msgstr ""

#. module: survey
#: model:mail.message.subtype,description:survey.mt_survey_user_input_completed
msgid "Participation completed."
msgstr ""

#. module: survey
#: model:ir.actions.act_window,name:survey.action_survey_user_input
#: model:ir.ui.menu,name:survey.menu_survey_type_form1
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Participations"
msgstr "Udziały"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Partner"
msgstr "Kontrahent"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Passed"
msgstr "Zaliczone"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "Passed and Failed"
msgstr "Zaliczone i niezaliczone"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "Passed only"
msgstr "Tylko zaliczone"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "Pay attention to the host screen until the next question."
msgstr "Zwracaj uwagę na ekran hosta aż do następnego pytania."

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__progression_mode__percent
msgid "Percentage left"
msgstr "Pozostały procent"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_result.js:0
msgid "Performance by Section"
msgstr "Wyniki według sekcji"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q6_sug3
msgid "Perhaps"
msgstr "Być może"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q1_sug2
msgid "Peter W. Higgs"
msgstr "Peter W. Higgs"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Pick a Badge..."
msgstr "Wybierz odznakę..."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Pick a Style..."
msgstr "Wybierz styl..."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Pick a Template..."
msgstr "Wybierz szablon..."

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p1_q1
msgid "Pick a subject"
msgstr "Wybierz temat"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__triggering_answer_ids
msgid ""
"Picking any of these answers will trigger this question.\n"
"Leave the field empty if the question should always be displayed."
msgstr ""

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q1_sug1
msgid "Pinaceae"
msgstr "Sosnowate (Pinaceae)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__question_placeholder
msgid "Placeholder"
msgstr "Miejsce"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid ""
"Please complete this very short survey to let us know how satisfied your are"
" with our products."
msgstr ""
"Wypełnij tę krótką ankietę, aby poinformować nas, jak bardzo jesteś "
"zadowolony z naszych produktów."

#. module: survey
#. odoo-python
#: code:addons/survey/wizard/survey_invite.py:0
msgid "Please enter at least one valid recipient."
msgstr "Wprowadź co najmniej jednego aktualnego odbiorcę."

#. module: survey
#: model_terms:survey.survey,description:survey.survey_demo_food_preferences
msgid "Please give us your preferences for this event's dinner!"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_void_content
msgid ""
"Please make sure you have at least one question in your survey. You also "
"need at least one section if you chose the \"Page per section\" layout.<br/>"
msgstr ""
"Upewnij się, że masz w ankiecie przynajmniej jedno pytanie. Potrzebujesz "
"także co najmniej jednej sekcji, jeśli wybierzesz układ \"Strona na "
"sekcję\".<br/>"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3
msgid "Policies"
msgstr "Zasady"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q4_sug1
msgid "Pomelos"
msgstr "Pomelo"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug8
msgid "Poor quality"
msgstr "Słaba jakość"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Practice in front of a mirror"
msgstr "Ćwiczenie przed lustrem"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__predefined_question_ids
msgid "Predefined Questions"
msgstr "Predefiniowane pytania"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Preview"
msgstr "Podgląd"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_2
msgid "Prices"
msgstr "Ceny"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_header
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Print"
msgstr "Drukuj"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_print
msgid "Print Results"
msgstr ""

#. module: survey
#: model:ir.actions.server,name:survey.action_survey_print
msgid "Print Survey"
msgstr ""

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1
msgid "Products"
msgstr "Produkty"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "Progress bar"
msgstr "Pasek postępu"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__question_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__question_id
#: model_terms:ir.ui.view,arch_db:survey.survey_question_answer_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Question"
msgstr "Pytanie"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Question & Pages"
msgstr "Pytania i strony"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__matrix_question_id
msgid "Question (as matrix row)"
msgstr "Pytanie (jako wiersz macierzy)"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_answer_view_form
msgid "Question Answer Form"
msgstr "Formularz odpowiedzi na pytanie"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_question_answer_count
msgid "Question Answers Count"
msgstr "Liczba odpowiedzi na pytania"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__questions_selection
#: model:ir.model.fields,field_description:survey.field_survey_survey__questions_selection
msgid "Question Selection"
msgstr "Wybór pytania"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__question_time_limit_reached
msgid "Question Time Limit Reached"
msgstr "Osiągnięto limit czasu pytań"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__question_type
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__question_type
msgid "Question Type"
msgstr "Typ pytania"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
msgid "Question type should be empty for these pages: %s"
msgstr "Typ pytania powinien być pusty dla tych stron: %s"

#. module: survey
#: model:ir.actions.act_window,name:survey.action_survey_question_form
#: model:ir.model.fields,field_description:survey.field_survey_question__question_ids
#: model:ir.model.fields,field_description:survey.field_survey_survey__question_ids
#: model:ir.ui.menu,name:survey.menu_survey_question_form1
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Questions"
msgstr "Pytania"

#. module: survey
#: model:ir.ui.menu,name:survey.survey_menu_questions
msgid "Questions & Answers"
msgstr "Pytania i odpowiedzi"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__triggering_question_ids
msgid ""
"Questions containing the triggering answer(s) to display the current "
"question."
msgstr ""

#. module: survey
#: model:survey.survey,title:survey.survey_demo_quiz
msgid "Quiz about our Company"
msgstr "Quiz o naszej firmie"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__scoring_success
msgid "Quizz Passed"
msgstr "Zaliczony quiz"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Quizz passed"
msgstr "Quiz zaliczony"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/views/widgets/radio_selection_with_filter/radio_selection_field_with_filter.js:0
msgid "Radio for Selection With Filter"
msgstr ""

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__questions_selection__random
msgid "Randomized per Section"
msgstr "Randomizowane według sekcji"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Rarely (1-3 times per month)"
msgstr "Rzadko (1-3 razy w miesiącu)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__rating_ids
#: model:ir.model.fields,field_description:survey.field_survey_user_input__rating_ids
msgid "Ratings"
msgstr "Oceny"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__session_state__ready
msgid "Ready"
msgstr "Gotowe"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Ready to change the way you <b>gather data</b>?"
msgstr "Gotowy na zmianę sposobu <b>gromadzenia danych</b>?"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "Ready to test? Pick a sample or create one from scratch..."
msgstr "Gotowy do testów? Wybierz próbkę lub stwórz ją od podstaw..."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__partner_ids
msgid "Recipients"
msgstr "Odbiorcy"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Red Pen"
msgstr "Czerwony długopis"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__answer_count
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Registered"
msgstr "Zarejestrowany"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__render_model
msgid "Rendering Model"
msgstr "Model renderowania"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Reopen"
msgstr "Otwórz ponownie"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__survey_users_login_required
#: model:ir.model.fields,field_description:survey.field_survey_survey__users_login_required
msgid "Require Login"
msgstr "Wymagane logowanie"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__scoring_success_min
msgid "Required Score (%)"
msgstr "Wymagany wynik (%)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__existing_text
msgid "Resend Comment"
msgstr "Wyślij ponownie komentarz"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Resend Invitation"
msgstr "Wyślij ponownie zaproszenie"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_invite__existing_mode__resend
msgid "Resend invite"
msgstr "Wyślij ponownie zaproszenie"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__user_id
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Responsible"
msgstr "Odpowiedzialny"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_user_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_user_id
msgid "Responsible User"
msgstr "Użytkownik odpowiedzialny"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__restrict_user_ids
msgid "Restricted to"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_button_retake
msgid "Retry"
msgstr "Ponów"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "Review your answers"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_speed_rating
msgid "Reward quick answers"
msgstr "Nagradzaj szybkie odpowiedzi"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.gamification_badge_form_view_simplified
msgid "Rewards for challenges"
msgstr "Nagrody za wyzwania"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__matrix_row_id
msgid "Row answer"
msgstr "Odpowiedź w rzędzie"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Row1"
msgstr "Row1"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Row2"
msgstr "Row2"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Row3"
msgstr "Row3"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Rows"
msgstr "Wiersze"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_has_sms_error
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Błąd dostarczenia wiadomości SMS"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q1_sug4
msgid "Salicaceae"
msgstr "Salicaceae (łososiowate)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__save_as_email
msgid "Save as user email"
msgstr "Zapisz jako e-mail użytkownika"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__save_as_nickname
msgid "Save as user nickname"
msgstr "Zapisz jako pseudonim użytkownika"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__scale
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Scale"
msgstr "Skala"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__scale_max_label
msgid "Scale Maximum Label"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__scale_max
msgid "Scale Maximum Value"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Scale Maximum Value (0 to 10)"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__scale_mid_label
msgid "Scale Middle Label"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__scale_min_label
msgid "Scale Minimum Label"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__scale_min
msgid "Scale Minimum Value"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Scale Minimum Value (0 to 10)"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_scale
msgid "Scale value"
msgstr ""

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p4
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p1_q1_sug3
msgid "Sciences"
msgstr "Nauki ścisłe"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__answer_score
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__answer_score
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__answer_score
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date_or_datetime
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Score"
msgstr "Ocena"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__scoring_percentage
msgid "Score (%)"
msgstr "Ocena (%)"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__answer_score
msgid "Score value for a correct answer to this question."
msgstr "Wartość punktowa za poprawną odpowiedź na to pytanie."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__is_scored_question
msgid "Scored"
msgstr "Punktacja"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__scoring_type
#: model:ir.model.fields,field_description:survey.field_survey_user_input__scoring_type
msgid "Scoring"
msgstr "Ocenianie"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__scoring_type
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__scoring_type
msgid "Scoring Type"
msgstr "Rodzaj punktacji"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__scoring_type__scoring_with_answers_after_page
msgid "Scoring with answers after each page"
msgstr ""

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__scoring_type__scoring_with_answers
msgid "Scoring with answers at the end"
msgstr "Punktacja z odpowiedziami na końcu"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__scoring_type__scoring_without_answers
msgid "Scoring without answers"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_answer_view_search
msgid "Search Label"
msgstr "Wyszukaj etykietę"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
msgid "Search Question"
msgstr "Wyszukaj pytanie"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Search Survey User Inputs"
msgstr "Dane wejściowe użytkownika ankiety wyszukiwania"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_line_view_search
msgid "Search User input lines"
msgstr "Wyszukaj linie wejściowe użytkownika"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__page_id
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Section"
msgstr "Sekcja"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__question_and_page_ids
msgid "Sections and Questions"
msgstr "Sekcje i pytania"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "See results"
msgstr "Zobacz wyniki"

#. module: survey
#: model_terms:survey.survey,description_done:survey.survey_demo_food_preferences
msgid "See you soon!"
msgstr ""

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1_question_3
msgid "Select all the available customizations for our Customizable Desk"
msgstr ""
"Wybierz wszystkie dostępne dostosowania naszego konfigurowalnego biurka"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1_question_2
msgid "Select all the existing products"
msgstr "Wybierz wszystkie istniejące produkty"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_2_question_2
msgid "Select all the products that sell for $100 or more"
msgstr "Wybierz wszystkie produkty sprzedawane za 100 USD lub więcej"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q3
msgid "Select trees that made more than 20K sales this year"
msgstr "Wybierz drzewa, których sprzedaż w tym roku przekroczyła 20 tys."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Send"
msgstr "Wyślij"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__send_email
msgid "Send Email"
msgstr "Wyślij e-mail"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Send by Email"
msgstr "Wyślij mailem"

#. module: survey
#: model:mail.template,description:survey.mail_template_certification
msgid "Sent to participant if they succeeded the certification"
msgstr "Wysłane do uczestnika, jeśli pomyślnie przeszedł certyfikację"

#. module: survey
#: model:mail.template,description:survey.mail_template_user_input_invite
msgid "Sent to participant when you share a survey"
msgstr "Wysyłane do uczestnika po udostępnieniu ankiety"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__sequence
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__question_sequence
msgid "Sequence"
msgstr "Sekwencja"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_code
msgid "Session Code"
msgstr "Kod sesji"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_link
msgid "Session Link"
msgstr "Link do sesji"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_state
msgid "Session State"
msgstr "Stan sesji"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_session_code_unique
msgid "Session code should be unique"
msgstr "Kod sesji powinien być unikalny"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q2_sug1
msgid "Shanghai"
msgstr "Szanghaj"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "Share"
msgstr "Udostępnij"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid "Share a Survey"
msgstr "Udostępnij ankietę"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_table_pagination
msgid "Show All"
msgstr "Pokaż wszystko"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__comments_allowed
msgid "Show Comments Field"
msgstr "Pokaż pole komentarzy"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_session_manage.js:0
msgid "Show Correct Answer(s)"
msgstr "Pokaż poprawne odpowiedzi"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_session_manage.js:0
msgid "Show Final Leaderboard"
msgstr "Pokaż końcową tabelę wyników"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_session_manage.js:0
msgid "Show Leaderboard"
msgstr "Pokaż tabelę wyników"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_session_manage.js:0
msgid "Show Results"
msgstr "Pokaż wyniki"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_show_leaderboard
msgid "Show Session Leaderboard"
msgstr "Pokaż tabelę liderów sesji"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Show all records which has next action date is before today"
msgstr ""
"Pokaż wszystkie rekordy, których data kolejnej czynności przypada przed "
"dzisiaj"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Show them slides with a ton of text they need to read fast"
msgstr "Pokaż im slajdy z mnóstwem tekstu, który muszą szybko przeczytać."

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__char_box
msgid "Single Line Text Box"
msgstr "Jednoliniowe pole tekstowe"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__skipped
#: model_terms:ir.ui.view,arch_db:survey.survey_page_print
msgid "Skipped"
msgstr "Pominięte"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q3_sug2
msgid "Soft"
msgstr "Miękki"

#. module: survey
#. odoo-python
#: code:addons/survey/wizard/survey_invite.py:0
msgid "Some emails you just entered are incorrect: %s"
msgstr "Niektóre wprowadzone e-maile są nieprawidłowe: %s"

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p1
msgid ""
"Some general information about you. It will be used internally for "
"statistics only."
msgstr ""
"Ogólne informacje o użytkowniku. Będą one wykorzystywane wewnętrznie "
"wyłącznie do celów statystycznych."

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p2
msgid "Some questions about our company. Do you really know us?"
msgstr "Kilka pytań na temat naszej firmy. Czy naprawdę nas znasz?"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
msgid "Someone just participated in \"%(survey_title)s\"."
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_header
msgid "Sorry, no one answered this survey yet."
msgstr "Przepraszamy, nikt jeszcze nie odpowiedział na tę ankietę."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "Sorry, you have not been fast enough."
msgstr "Niestety, nie byłeś wystarczająco szybki."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q4_sug4
msgid "South America"
msgstr "Ameryka Południowa"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q2_sug4
msgid "South Korea"
msgstr "Korea Południowa"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q1_sug3
msgid "Space stations"
msgstr "Stacje kosmiczne"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Speak softly so that they need to focus to hear you"
msgstr "Mów cicho, tak aby musieli się skupić, aby cię usłyszeć."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Speak too fast"
msgstr "Mów zbyt szybko"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_sug1
msgid "Spring"
msgstr "Wiosna"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_session_manage.js:0
#: model:survey.question,title:survey.survey_demo_burger_quiz_p1
msgid "Start"
msgstr "Uruchom"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid "Start Certification"
msgstr "Rozpocznij certyfikację"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "Start Live Session"
msgstr "Rozpoczęcie sesji na żywo"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid "Start Survey"
msgstr "Rozpocznij ankietę"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__start_datetime
msgid "Start date and time"
msgstr "Data i godzina rozpoczęcia"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__state
msgid "Status"
msgstr "Status"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__activity_state
#: model:ir.model.fields,help:survey.field_survey_user_input__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status na podstawie czynności\n"
"Zaległe: Termin już minął\n"
"Dzisiaj: Data czynności przypada na dzisiaj\n"
"Zaplanowane: Przyszłe czynności."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_food_preferences_q4_sug1
msgid "Steak with french fries"
msgstr ""

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_row2
msgid "Strawberries"
msgstr "Truskawki"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__subject
msgid "Subject"
msgstr "Temat"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Subject..."
msgstr "Temat..."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "Submit"
msgstr "Wyślij"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__success_count
msgid "Success"
msgstr "Powodzenie"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__success_ratio
msgid "Success Ratio (%)"
msgstr "Współczynnik sukcesu (%)"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
msgid "Success rate"
msgstr ""

#. module: survey
#: model:ir.actions.act_window,name:survey.survey_question_answer_action
#: model:ir.ui.menu,name:survey.menu_survey_label_form1
msgid "Suggested Values"
msgstr "Sugerowane wartości"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__suggested_answer_id
msgid "Suggested answer"
msgstr "Sugerowana odpowiedź"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_answer_value_not_empty
msgid ""
"Suggested answer value must not be empty (a text and/or an image must be "
"provided)."
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__value
msgid "Suggested value"
msgstr "Sugerowana wartość"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__suggestion
msgid "Suggestion"
msgstr "Sugestia"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_sug2
msgid "Summer"
msgstr "Lato"

#. module: survey
#: model:ir.model,name:survey.model_survey_survey
#: model:ir.model.fields,field_description:survey.field_gamification_badge__survey_id
#: model:ir.model.fields,field_description:survey.field_survey_invite__survey_id
#: model:ir.model.fields,field_description:survey.field_survey_question__survey_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input__survey_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__survey_id
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__survey_type__survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_activity
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_tree
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_line_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Survey"
msgstr "Ankieta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_access_error
msgid "Survey Access Error"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_response_line_view_tree
msgid "Survey Answer Line"
msgstr "Linia odpowiedzi Ankiety"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__survey_first_submitted
msgid "Survey First Submitted"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_gamification_badge__survey_ids
msgid "Survey Ids"
msgstr "Survey Ids"

#. module: survey
#: model:ir.model,name:survey.model_survey_invite
msgid "Survey Invitation Wizard"
msgstr "Kreator zaproszenia do ankiety"

#. module: survey
#: model:ir.model,name:survey.model_survey_question_answer
#: model_terms:ir.ui.view,arch_db:survey.survey_question_answer_view_tree
msgid "Survey Label"
msgstr "Etykieta ankiety"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Survey Link"
msgstr "Link do ankiety"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
msgid "Survey Participant"
msgstr "Uczestnik ankiety"

#. module: survey
#: model:ir.model,name:survey.model_survey_question
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
#: model_terms:ir.ui.view,arch_db:survey.survey_question_tree
msgid "Survey Question"
msgstr "Pytanie ankiety"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Survey Time Limit"
msgstr "Limit czasu ankiety"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__survey_time_limit_reached
msgid "Survey Time Limit Reached"
msgstr "Osiągnięto limit czasu ankiety"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__title
msgid "Survey Title"
msgstr "Tytuł ankiety"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__survey_type
msgid "Survey Type"
msgstr "Typ ankiety"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__survey_start_url
msgid "Survey URL"
msgstr "Adres URL ankiety"

#. module: survey
#: model:ir.model,name:survey.model_survey_user_input
msgid "Survey User Input"
msgstr "Wkład użytkownika ankiety"

#. module: survey
#: model:ir.model,name:survey.model_survey_user_input_line
msgid "Survey User Input Line"
msgstr "Linia wejściowa użytkownika ankiety"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_tree
msgid "Survey User inputs"
msgstr "Wkłady użytkownika ankiety"

#. module: survey
#: model:mail.template,name:survey.mail_template_certification
msgid "Survey: Certification Success"
msgstr "Ankieta: Sukces certyfikacji"

#. module: survey
#: model:mail.template,name:survey.mail_template_user_input_invite
msgid "Survey: Invite"
msgstr "Ankieta: Zaproszenie"

#. module: survey
#: model:ir.actions.act_window,name:survey.action_survey_form
#: model:ir.ui.menu,name:survey.menu_survey_form
#: model:ir.ui.menu,name:survey.menu_surveys
msgid "Surveys"
msgstr "Ankiety"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q1_sug3
msgid "Takaaki Kajita"
msgstr "Takaaki Kajita"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_button_retake
msgid "Take Again"
msgstr "Weź ponownie"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "Test"
msgstr "Test"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__test_entry
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Test Entry"
msgstr "Wpis testowy"

#. module: survey
#: model_terms:survey.question,description:survey.vendor_certification_page_3
msgid "Test your knowledge of our policies."
msgstr "Sprawdź swoją wiedzę na temat naszych zasad."

#. module: survey
#: model_terms:survey.question,description:survey.vendor_certification_page_2
msgid "Test your knowledge of our prices."
msgstr "Sprawdź swoją wiedzę na temat naszych cen."

#. module: survey
#: model_terms:survey.question,description:survey.vendor_certification_page_1
msgid "Test your knowledge of your products!"
msgstr "Sprawdź swoją wiedzę o produktach!"

#. module: survey
#: model_terms:survey.survey,description:survey.vendor_certification
msgid "Test your vendor skills!"
msgstr "Sprawdź swoje umiejętności sprzedawcy!"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Tests Only"
msgstr "Tylko testy"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__char_box
msgid "Text"
msgstr "Tekst"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_char_box
msgid "Text answer"
msgstr "Odpowiedź testowa"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Thank you for your participation, hope you had a blast!"
msgstr "Dziękujemy za udział, mamy nadzieję, że dobrze się bawiliście!"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Thank you very much for your feedback. We highly value your opinion!"
msgstr "Bardzo dziękujemy za informację zwrotną. Bardzo cenimy Twoją opinię!"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "Thank you!"
msgstr "Dziękujemy!"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Thank you. We will contact you soon."
msgstr "Dziękujemy. Skontaktujemy się wkrótce."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid ""
"The access of the following surveys is restricted. Make sure their responsible still has access to it: \n"
"%(survey_names)s\n"
msgstr ""

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
msgid "The answer must be in the right type"
msgstr "Odpowiedź musi być w odpowiednim typie"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
#: model_terms:ir.ui.view,arch_db:survey.question_container
msgid "The answer you entered is not valid."
msgstr "Podana odpowiedź jest nieprawidłowa."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_attempts_limit_check
msgid ""
"The attempts limit needs to be a positive number if the survey has a limited"
" number of attempts."
msgstr ""
"Limit prób musi być liczbą dodatnią, jeśli ankieta ma ograniczoną liczbę "
"prób."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_badge_uniq
msgid "The badge for each survey should be unique!"
msgstr "Identyfikator każdej ankiety powinien być niepowtarzalny!"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_row4
msgid "The checkout process is clear and secure"
msgstr "Proces realizacji zamówienia jest przejrzysty i bezpieczny"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_print
msgid "The correct answer was:"
msgstr "Prawidłowa odpowiedź brzmiała:"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__session_question_id
msgid "The current question of the survey session."
msgstr "Aktualne pytanie sesji ankietowej."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__description
msgid ""
"The description will be displayed on the home page of the survey. You can "
"use this to give the purpose and guidelines to your candidates before they "
"start it."
msgstr ""
"Opis zostanie wyświetlony na stronie głównej ankiety. Możesz użyć go, aby "
"podać cel i wytyczne swoim kandydatom, zanim zaczną."

#. module: survey
#. odoo-python
#: code:addons/survey/wizard/survey_invite.py:0
msgid "The following customers have already received an invite"
msgstr "Następujący klienci otrzymali już zaproszenie"

#. module: survey
#. odoo-python
#: code:addons/survey/wizard/survey_invite.py:0
msgid "The following emails have already received an invite"
msgstr "Następujące e-maile otrzymały już zaproszenie"

#. module: survey
#. odoo-python
#: code:addons/survey/wizard/survey_invite.py:0
msgid ""
"The following recipients have no user account: %s. You should create user "
"accounts for them or allow external signup in configuration."
msgstr ""
"Następujący odbiorcy nie mają konta użytkownika: %s. Powinieneś utworzyć dla"
" nich konta użytkowników lub zezwolić na zewnętrzną rejestrację w "
"konfiguracji."

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_row1
msgid "The new layout and design is fresh and up-to-date"
msgstr "Nowy układ i design są świeże i aktualne"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_403_page
msgid "The page you were looking for could not be authorized."
msgstr "Strona, której szukasz nie mogła być autoryzowana."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_scoring_success_min_check
msgid "The percentage of success has to be defined between 0 and 100."
msgstr "Procent powodzenia należy określić w przedziale od 0 do 100."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__is_time_limited
msgid "The question is limited in time"
msgstr "Pytanie jest ograniczone w czasie"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_scale
msgid ""
"The scale must be a growing non-empty range between 0 and 10 (inclusive)"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_session_code
msgid "The session did not start yet."
msgstr "Sesja jeszcze się nie rozpoczęła."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid "The session will begin automatically when the host starts."
msgstr "Sesja rozpocznie się automatycznie po uruchomieniu hosta."

#. module: survey
#. odoo-python
#: code:addons/survey/controllers/main.py:0
msgid "The survey has already started."
msgstr "Badanie już się rozpoczęło."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__is_time_limited
msgid "The survey is limited in time"
msgstr "Ankieta jest ograniczona w czasie"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__session_question_start_time
msgid ""
"The time at which the current question has started, used to handle the timer"
" for attendees."
msgstr ""
"Czas rozpoczęcia bieżącego pytania, używany do obsługi licznika czasu dla "
"uczestników."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_time_limit_check
msgid ""
"The time limit needs to be a positive number if the survey is time limited."
msgstr ""
"Limit czasu musi być liczbą dodatnią, jeśli badanie jest ograniczone "
"czasowo."

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_row3
msgid "The tool to compare the products is useful to make a choice"
msgstr ""
"Narzędzie do porównywania produktów jest przydatne do dokonania wyboru"

#. module: survey
#. odoo-python
#: code:addons/survey/controllers/main.py:0
msgid "The user has not succeeded the certification"
msgstr "Użytkownikowi nie udało się uzyskać certyfikacji"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form
msgid "There was an error during the validation of the survey."
msgstr "Podczas walidacji ankiety wystąpił błąd."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "They are a default answer, used if the participant skips the question"
msgstr "Są one domyślną odpowiedzią, używaną, jeśli uczestnik pominie pytanie"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid ""
"They are technical parameters that guarantees the responsiveness of the page"
msgstr "Są to parametry techniczne gwarantujące responsywność strony"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
msgid "This answer cannot be overwritten."
msgstr ""

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
#: code:addons/survey/tests/test_survey.py:0
msgid "This answer must be an email address"
msgstr "Ta odpowiedź musi być adresem e-mail"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_form.js:0
msgid "This answer must be an email address."
msgstr "Odpowiedzią musi być adres e-mail."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__session_code
msgid ""
"This code will be used by your attendees to reach your session. Feel free to"
" customize it however you like!"
msgstr ""
"Ten kod będzie używany przez uczestników, aby dotrzeć do Twojej sesji. "
"Możesz go dowolnie dostosować!"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_button_form_view
msgid "This is a Test Survey Entry."
msgstr ""

#. module: survey
#. odoo-javascript
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
#: code:addons/survey/static/src/js/survey_form.js:0
#: code:addons/survey/tests/test_survey.py:0
msgid "This is not a date"
msgstr "To nie jest data"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
#: code:addons/survey/tests/test_survey.py:0
msgid "This is not a number"
msgstr "To nie jest numer"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__description_done
msgid "This message will be displayed when survey is completed"
msgstr "Ta wiadomość się wyświetli gdy ankieta zostanie ukończona"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
#: model_terms:ir.ui.view,arch_db:survey.question_container
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "This question requires an answer."
msgstr "To pytanie wymaga odpowiedzi."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_print
msgid "This question was skipped"
msgstr "To pytanie zostało pominięte"

#. module: survey
#: model_terms:survey.question,description:survey.survey_feedback_p1
msgid ""
"This section is about general information about you. Answering them helps "
"qualifying your answers."
msgstr ""
"Ta sekcja zawiera ogólne informacje o użytkowniku. Udzielenie odpowiedzi "
"pomoże zakwalifikować Twoje odpowiedzi."

#. module: survey
#: model_terms:survey.question,description:survey.survey_feedback_p2
msgid "This section is about our eCommerce experience itself."
msgstr ""
"Ta sekcja poświęcona jest naszemu doświadczeniu w handlu elektronicznym."

#. module: survey
#: model_terms:survey.survey,description:survey.survey_demo_quiz
msgid ""
"This small quiz will test your knowledge about our Company. Be prepared!"
msgstr ""
"Ten mały quiz sprawdzi Twoją wiedzę o naszej firmie. Bądź przygotowany!"

#. module: survey
#: model_terms:survey.survey,description:survey.survey_feedback
msgid ""
"This survey allows you to give a feedback about your experience with our products.\n"
"    Filling it helps us improving your experience."
msgstr ""
"Ta ankieta umożliwia wyrażenie opinii na temat doświadczeń związanych z naszymi produktami.\n"
"Wypełnienie jej pomoże nam poprawić Twoje doświadczenia."

#. module: survey
#. odoo-python
#: code:addons/survey/wizard/survey_invite.py:0
msgid ""
"This survey does not allow external people to participate. You should create"
" user accounts or update survey access mode accordingly."
msgstr ""
"Ta ankieta nie pozwala na uczestnictwo osób zewnętrznych. Powinieneś "
"utworzyć konta użytkowników lub odpowiednio zaktualizować tryb dostępu do "
"ankiety."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_closed_expired
msgid "This survey is now closed. Thank you for your interest!"
msgstr "Ta ankieta jest już zamknięta. Dziękujemy za zainteresowanie!"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_auth_required
msgid "This survey is open only to registered people. Please"
msgstr "Ta ankieta jest otwarta tylko dla zarejestrowanych. Proszę"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Time & Scoring"
msgstr "Czas i punktacja"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Time Limit"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Time Limit (seconds)"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__time_limit
msgid "Time limit (minutes)"
msgstr "Limit czasu (minuty)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__time_limit
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_speed_rating_time_limit
msgid "Time limit (seconds)"
msgstr "Limit czasu (sekundy)"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid "Time limit for this certification:"
msgstr "Limit czasowy dla tej certyfikacji:"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid "Time limit for this survey:"
msgstr "Limit czasu dla tej ankiety:"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Time limits are only available for Live Sessions."
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__title
msgid "Title"
msgstr "Tytuł"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "To join:"
msgstr "Aby dołączyć:"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form
msgid ""
"To take this survey, please close all other tabs on <strong class=\"text-"
"danger\"/>."
msgstr ""
"Aby wziąć udział w tej ankiecie, zamknij wszystkie inne karty na <strong "
"class=\"text-danger\"/>."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Today Activities"
msgstr "Dzisiejsze czynności"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q2_sug2
msgid "Tokyo"
msgstr "Tokio"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date_or_datetime
msgid "Top User Responses"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__scoring_total
msgid "Total Score"
msgstr "Łączny wynik"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_col4
msgid "Totally agree"
msgstr "Kompletnie się zgadzam"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_col1
msgid "Totally disagree"
msgstr "Kompletnie się nie zgadzam"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4
msgid "Trees"
msgstr "Drzewa"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__triggering_answer_ids
msgid "Triggering Answers"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__triggering_question_ids
msgid "Triggering Questions"
msgstr ""

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/views/widgets/survey_question_trigger/survey_question_trigger.js:0
msgid ""
"Triggers based on the following questions will not work because they are positioned after this question:\n"
"\"%s\"."
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
msgid "Type"
msgstr "Typ"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__activity_exception_decoration
#: model:ir.model.fields,help:survey.field_survey_user_input__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Typ wyjątku działania na rekordzie."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__suggested_answer_ids
msgid "Types of answers"
msgstr "Typy odpowiedzi"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q1_sug2
msgid "Ulmaceae"
msgstr "Ulmaceae"

#. module: survey
#. odoo-python
#: code:addons/survey/wizard/survey_invite.py:0
msgid "Unable to post message, please configure the sender's email address."
msgstr "Nie można wysłać wiadomości, skonfiguruj adres e-mail nadawcy."

#. module: survey
#. odoo-javascript
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
#: code:addons/survey/static/src/js/survey_result.js:0
msgid "Unanswered"
msgstr "Bez odpowiedzi"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
msgid "Uncategorized"
msgstr "Bez kategorii"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_3_choice_2
msgid "Underpriced"
msgstr "Zaniżona cena"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "Unfortunately, you have failed the test."
msgstr "Niestety, nie zaliczyłeś testu."

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug3
msgid "Unique"
msgstr "Wyjątkowy"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Unlikely"
msgstr "Mało prawdopodobne"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Upcoming Activities"
msgstr "Nadchodzące zadania"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Use a fun visual support, like a live presentation"
msgstr "Użyj zabawnego wsparcia wizualnego, takiego jak prezentacja na żywo."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Use humor and make jokes"
msgstr "Używaj humoru i żartuj"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Use template"
msgstr "Zastosuj szablon"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Use the breadcrumbs to quickly go back to the dashboard."
msgstr "Użyj bułki tartej, aby szybko wrócić do konsoli."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__description
msgid ""
"Use this field to add additional explanations about your question or to "
"illustrate it with pictures or a video"
msgstr ""
"Użyj tego pola, aby dodać dodatkowe wyjaśnienia dotyczące pytania lub "
"zilustrować je zdjęciami lub filmem"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__random_questions_count
msgid ""
"Used on randomized sections to take X random questions from all the "
"questions of that section."
msgstr ""
"Używany w losowych sekcjach, aby pobrać X losowych pytań ze wszystkich pytań"
" z tej sekcji."

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug2
msgid "Useful"
msgstr "Przydatny"

#. module: survey
#: model:res.groups,name:survey.group_survey_user
msgid "User"
msgstr "Użytkownik"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
msgid "User Choice"
msgstr "Wybór użytkownika"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__user_input_id
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_line_view_search
msgid "User Input"
msgstr "Wkład użytkownika"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date_or_datetime
#: model_terms:ir.ui.view,arch_db:survey.question_result_text
msgid "User Responses"
msgstr "Odpowiedzi Użytkowników"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_line_view_form
msgid "User input line details"
msgstr "Szczegóły linii wejściowych użytkownika"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__user_input_ids
msgid "User responses"
msgstr "Odpowiedzi użytkowników"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__users_can_go_back
msgid "Users can go back"
msgstr "Użytkownicy mogą wrócić"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__survey_users_can_signup
#: model:ir.model.fields,field_description:survey.field_survey_survey__users_can_signup
msgid "Users can signup"
msgstr "Użytkownicy mogą się zarejestrować"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_required
msgid "Validate entry"
msgstr "Zatwierdź wpis"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/question_page/question_page_one2many_field.js:0
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_error_msg
msgid "Validation Error"
msgstr "Błąd walidacji"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__value_label
msgid "Value Label"
msgstr ""

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q1_sug2
msgid "Vegetables"
msgstr "Warzywa"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_food_preferences_q3_sug2
msgid "Vegetarian burger"
msgstr ""

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_food_preferences_q3_sug1
msgid "Vegetarian pizza"
msgstr ""

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_3_choice_1
msgid "Very underpriced"
msgstr "Bardzo niedoszacowane"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q2_sug3
msgid "Vietnam"
msgstr "Wietnam"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_open
msgid "Waiting for attendees..."
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid ""
"We have registered your answer! Please wait for the host to go to the next "
"question."
msgstr ""
"Twoja odpowiedź została zarejestrowana! Poczekaj, aż gospodarz przejdzie do "
"następnego pytania."

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p4
msgid ""
"We like to say that the apple doesn't fall far from the tree, so here are "
"trees."
msgstr "Lubimy mawiać, że jabłko nie pada daleko od jabłoni, więc oto drzewa."

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p5
msgid "We may be interested by your input."
msgstr "Twój wkład może nas zainteresować."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__website_message_ids
#: model:ir.model.fields,field_description:survey.field_survey_user_input__website_message_ids
msgid "Website Messages"
msgstr "Wiadomości"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__website_message_ids
#: model:ir.model.fields,help:survey.field_survey_user_input__website_message_ids
msgid "Website communication history"
msgstr "Historia komunikacji"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid ""
"Welcome to this Odoo certification. You will receive 2 random questions out "
"of a pool of 3."
msgstr "Witamy w certyfikacji Odoo. Otrzymasz 2 losowe pytania z puli 3."

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_5
msgid ""
"What day and time do you think most customers are most likely to call "
"customer service (not rated)?"
msgstr ""
"Którego dnia i o której godzinie klienci najczęściej dzwonią do działu "
"obsługi klienta (bez oceny)?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_4
msgid ""
"What day to you think is best for us to start having an annual sale (not "
"rated)?"
msgstr ""
"Jak myślisz, który dzień jest najlepszy na rozpoczęcie corocznej wyprzedaży "
"(bez oceny)?"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p2_q2
msgid "What do you think about our new eCommerce?"
msgstr "Co sądzisz o naszym nowym eCommerce?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_2_question_3
msgid "What do you think about our prices (not rated)?"
msgstr "Co sądzisz o naszych cenach (bez oceny)"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p5_q1
msgid "What do you think about this survey?"
msgstr "Co sądzisz o tej ankiecie?"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "What does \"ODOO\" stand for?"
msgstr "Co oznacza skrót \"ODOO\"?"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "What does one need to get to pass an Odoo Survey?"
msgstr "Co trzeba mieć, aby przejść ankietę Odoo?"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "What is a frequent mistake public speakers do?"
msgstr "Jaki jest częsty błąd popełniany przez mówców publicznych?"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "What is the best way to catch the attention of an audience?"
msgstr "Jaki jest najlepszy sposób na przyciągnięcie uwagi odbiorców?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p2_q2
msgid "What is the biggest city in the world?"
msgstr "Jakie jest największe miasto na świecie?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p1_q1
msgid "What is your email?"
msgstr "Jaki jest Twój adres e-mail?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p1_q2
msgid "What is your nickname?"
msgstr "Jaki jest twój pseudonim?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p4_q2
msgid "What is, approximately, the critical mass of plutonium-239?"
msgstr "Jaka jest w przybliżeniu masa krytyczna plutonu-239?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p3_q1
msgid "When did Genghis Khan die?"
msgstr "Kiedy zmarł Czyngis-chan?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p2_q2
msgid "When did precisely Marc Demo crop its first apple tree?"
msgstr "Kiedy dokładnie Marc Demo posadził swoją pierwszą jabłoń?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q6
msgid "When do you harvest those fruits"
msgstr "Kiedy zbierasz te owoce?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p2_q1
msgid "When is Mitchell Admin born?"
msgstr "Kiedy urodził się Mitchell Admin?"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p1_q2
msgid "When is your date of birth?"
msgstr "Kiedy jest Twoja data urodzenia?"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Whenever you pick an answer, Odoo saves it for you."
msgstr ""
"Za każdym razem, gdy wybierzesz odpowiedź, Odoo zapisze ją dla Ciebie."

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p1_q3
msgid "Where are you from?"
msgstr "Skąd jesteś?"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p1_q1
msgid "Where do you live?"
msgstr "Gdzie mieszkasz?"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__session_show_leaderboard
msgid ""
"Whether or not we want to show the attendees leaderboard for this survey."
msgstr "Czy chcemy wyświetlać tabelę wyników dla tej ankiety."

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p5_q1
msgid "Which Musician is not in the 27th Club?"
msgstr "Którego muzyka nie ma w Klubie 27?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q1
msgid "Which category does a tomato belong to"
msgstr "Do której kategorii należy pomidor?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p2_q3
msgid "Which is the highest volcano in Europe?"
msgstr "Który wulkan jest najwyższy w Europie?"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p2_q1
msgid "Which of the following words would you use to describe our products?"
msgstr "Których z poniższych słów użyłbyś do opisania naszych produktów?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q2
msgid "Which of the following would you use to pollinate"
msgstr "Którego z poniższych produktów użyłbyś do zapylania?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p5_q2
msgid "Which painting/drawing was not made by Pablo Picasso?"
msgstr "Który obraz/rysunek nie został namalowany przez Pabla Picassa?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p5_q3
msgid "Which quote is from Jean-Claude Van Damme"
msgstr "Który cytat pochodzi z Jean-Claude Van Damme'a?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p1
msgid "Who are you?"
msgstr "Kim jesteś?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p3_q2
msgid "Who is the architect of the Great Pyramid of Giza?"
msgstr "Kto jest architektem Wielkiej Piramidy w Gizie?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p4_q1
msgid ""
"Who received a Nobel prize in Physics for the discovery of neutrino "
"oscillations, which shows that neutrinos have mass?"
msgstr ""
"Kto otrzymał nagrodę Nobla w dziedzinie fizyki za odkrycie oscylacji "
"neutrin, które pokazuje, że neutrina mają masę?"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid ""
"Why should you consider making your presentation more fun with a small quiz?"
msgstr "Dlaczego warto rozważyć urozmaicenie prezentacji małym quizem?"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_3_choice_3
msgid "Width"
msgstr "Szerokość"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q1_sug4
msgid "Willard S. Boyle"
msgstr "Willard S. Boyle"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_sug4
msgid "Winter"
msgstr "Zima"

#. module: survey
#: model:survey.question,title:survey.survey_demo_food_preferences_q2
msgid "Would you prefer a veggie meal if possible?"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"YYYY-MM-DD\n"
"                                        <i class=\"fa fa-calendar\" role=\"img\" aria-label=\"Calendar\" title=\"Calendar\"/>"
msgstr ""
"RRRR-MM-DD\n"
"<i class=\"fa fa-calendar\" role=\"img\" aria-label=\"Calendar\" title=\"Calendar\"/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"YYYY-MM-DD hh:mm:ss\n"
"                                        <i class=\"fa fa-calendar\" role=\"img\" aria-label=\"Calendar\" title=\"Calendar\"/>"
msgstr ""
"RRRR-MM-DD hh:mm:ss\n"
"<i class=\"fa fa-calendar\" role=\"img\" aria-label=\"Calendar\" title=\"Calendar\"/>"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Yellow Pen"
msgstr "Żółty długopis"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_food_preferences_q1_sug1
#: model:survey.question.answer,value:survey.survey_demo_food_preferences_q2_sug1
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q6_sug1
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_1_choice_2
msgid "Yes"
msgstr "Tak"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q3_sug1
msgid "Yes, that's the only thing a human eye can see."
msgstr "Tak, to jedyna rzecz, którą może zobaczyć ludzkie oko."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_certification_check
msgid ""
"You can only create certifications for surveys that have a scoring "
"mechanism."
msgstr ""
"Możesz tworzyć certyfikaty tylko dla ankiet, które mają mechanizm oceniania."

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_user_input
msgid ""
"You can share your links through different means: email, invite shortcut, "
"live presentation, ..."
msgstr ""
"Linki można udostępniać w różny sposób: e-mailem, zapraszając do skrótu, "
"podczas prezentacji na żywo, ..."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
msgid ""
"You cannot delete questions from surveys \"%(survey_names)s\" while live "
"sessions are in progress."
msgstr ""
"Nie można usuwać pytań z ankiet \"%(survey_names)s\" podczas trwania sesji "
"na żywo."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid ""
"You cannot send an invitation for a \"One page per section\" survey if the "
"survey has no sections."
msgstr ""
"Nie można wysłać zaproszenia do ankiety \"Jedna strona na sekcję\", jeśli "
"ankieta nie ma sekcji."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid ""
"You cannot send an invitation for a \"One page per section\" survey if the "
"survey only contains empty sections."
msgstr ""
"Nie można wysłać zaproszenia do ankiety \"Jedna strona na sekcję\", jeśli "
"ankieta zawiera tylko puste sekcje."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid "You cannot send an invitation for a survey that has no questions."
msgstr "Nie możesz wysłać zaproszenia do ankiety, która nie posiada pytań."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid "You cannot send invitations for closed surveys."
msgstr "Nie możesz wysłać zaproszenia do zamkniętej ankiety."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "You received the badge"
msgstr "Otrzymałeś odznakę"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "You scored"
msgstr "Twój wynik"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p5
msgid "Your feeling"
msgstr "Twoje uczucie"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid ""
"Your responses will help us improve our product range to serve you even "
"better."
msgstr ""
"Twoje odpowiedzi pomogą nam ulepszyć naszą ofertę produktów, aby służyć Ci "
"jeszcze lepiej."

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/xml/survey_image_zoomer_templates.xml:0
msgid "Zoom in"
msgstr "Przybliżanie"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/xml/survey_image_zoomer_templates.xml:0
msgid "Zoom out"
msgstr "Oddalanie"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/xml/survey_image_zoomer_templates.xml:0
msgid "Zoomed Image"
msgstr "Powiększony obraz"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
msgid "[Question Title]"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "ans"
msgstr "odp"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_progression
msgid "answered"
msgstr "odpowiedzi"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "attempts"
msgstr "prób"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/xml/survey_image_zoomer_templates.xml:0
msgid "close"
msgstr "zamknij"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid ""
"e.g.  'Rick Sanchez' <<EMAIL>>, <EMAIL>"
msgstr "np. \"Rick Sanchez\" <<EMAIL>>, <EMAIL>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "e.g. \"Thank you very much for your feedback!\""
msgstr "np. \"Dziękuję bardzo za Twoją opinię!\"."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "e.g. \"The following Survey will help us...\""
msgstr "np. \"Poniższa ankieta pomoże nam...\"."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "e.g. \"What is the...\""
msgstr "np. \"Co to jest...\""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_session_code
msgid "e.g. 4812"
msgstr "np. 4812"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "e.g. Guidelines, instructions, picture, ... to help attendees answer"
msgstr ""
"np. wskazówki, instrukcje, obrazek, ... aby pomóc uczestnikom w udzieleniu "
"odpowiedzi"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.gamification_badge_form_view_simplified
msgid "e.g. No one can solve challenges like you do"
msgstr "np. Nikt nie potrafi rozwiązywać problemów tak jak Ty"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.gamification_badge_form_view_simplified
msgid "e.g. Problem Solver"
msgstr "np. Rozwiązywacz problemów"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "e.g. Satisfaction Survey"
msgstr "np. Badanie zadowolenia"

#. module: survey
#: model:survey.question,question_placeholder:survey.survey_demo_quiz_p1_q1
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_auth_required
msgid "log in"
msgstr "zaloguj"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "minutes"
msgstr "minut"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_progression
msgid "of"
msgstr "z"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid "of achievement"
msgstr "osiągnięcia"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_form.js:0
msgid "or press CTRL+Enter"
msgstr "lub naciśnij CTRL+Enter"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_form.js:0
msgid "or press Enter"
msgstr "lub naciśnij Enter"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_form.js:0
msgid "or press ⌘+Enter"
msgstr "lub naciśnij ⌘+Enter"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_progression
msgid "pages"
msgstr "strony"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_closed_expired
msgid "survey expired"
msgstr "ankieta wygasła"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_void_content
msgid "survey is empty"
msgstr "ankieta jest pusta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_403_page
msgid "this page"
msgstr "ta strona"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "to"
msgstr "do"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"⚠️ This question is positioned before some or all of its triggers and could "
"be skipped."
msgstr ""
