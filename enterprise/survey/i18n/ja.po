# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* survey
# 
# Translators:
# W<PERSON>, 2024
# <PERSON><PERSON> (Quartile) <<EMAIL>>, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__question_count
msgid "# Questions"
msgstr "質問"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__random_questions_count
msgid "# Questions Randomly Picked"
msgstr "無作為に選ばれた質問数"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_4
msgid "$100"
msgstr "$100"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_1
msgid "$20"
msgstr "$20"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_5
msgid "$200"
msgstr "$200"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_6
msgid "$300"
msgstr "$300"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_2
msgid "$50"
msgstr "$50"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_3
msgid "$80"
msgstr "$80"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_progression
msgid "% completed"
msgstr "% 完了"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
msgid "%(participant)s just participated in \"%(survey_title)s\"."
msgstr "%(participant)sが \"%(survey_title)s\"に参加しました。"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid "%s (copy)"
msgstr "%s (コピー)"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid "%s certification passed"
msgstr "%s 検定合格"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid "%s challenge certification"
msgstr "%s 検定を検証"

#. module: survey
#: model:ir.actions.report,print_report_name:survey.certification_report
msgid "'Certification - %s' % (object.survey_id.display_name)"
msgstr "'検定 - %s' % (object.survey_id.display_name)"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid "0000000010"
msgstr "0000000010"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q2_sug2
msgid "10 kg"
msgstr "10 kg"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q3_sug2
msgid "100 years"
msgstr "100 years"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q1_sug3
msgid "1055"
msgstr "1055"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q3_sug3
msgid "116 years"
msgstr "116 years"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q1_sug1
msgid "1227"
msgstr "1227"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q3_sug4
msgid "127 years"
msgstr "127 years"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q1_sug2
msgid "1324"
msgstr "1324"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q1_sug1
msgid "1450 km"
msgstr "1450 km"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q2_sug3
msgid "16.2 kg"
msgstr "16.2 kg"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid "2023-08-18"
msgstr "2023-08-18"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q1_sug2
msgid "3700 km"
msgstr "3700 km"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_403_page
msgid "403: Forbidden"
msgstr "403: Forbidden"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q2_sug4
msgid "47 kg"
msgstr "47 kg"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q2_sug1
msgid "5.7 kg"
msgstr "5.7 kg"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q1_sug3
msgid "6650 km"
msgstr "6650 km"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q3_sug1
msgid "99 years"
msgstr "99 years"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid ""
"<b>Certificate</b>\n"
"                            <br/>"
msgstr ""
"<b>証明書</b>\n"
"                            <br/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid ""
"<br/>\n"
"                                    <span class=\"text-muted\">Completed</span>"
msgstr ""
"<br/>\n"
"                                    <span class=\"text-muted\">完了</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid ""
"<br/>\n"
"                                    <span class=\"text-muted\">Registered</span>"
msgstr ""
"<br/>\n"
"                                    <span class=\"text-muted\">登録済</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid "<br/> <span>by</span>"
msgstr "<br/> <span>以下による:</span>"

#. module: survey
#: model:mail.template,body_html:survey.mail_template_certification
msgid ""
"<div style=\"background:#F0F0F0;color:#515166;padding:10px 0px;font-family:Arial,Helvetica,sans-serif;font-size:14px;\">\n"
"    <table style=\"width:600px;margin:5px auto;\">\n"
"        <tbody>\n"
"            <tr><td>\n"
"                <!-- We use the logo of the company that created the survey (to handle multi company cases) -->\n"
"                <a href=\"/\"><img t-if=\"not object.survey_id.create_uid.company_id.uses_default_logo\" t-attf-src=\"/logo.png?company={{ object.survey_id.create_uid.company_id.id }}\" style=\"vertical-align:baseline;max-width:100px;\"/></a>\n"
"            </td><td style=\"text-align:right;vertical-align:middle;\">\n"
"                    Certification: <t t-out=\"object.survey_id.display_name or ''\">Feedback Form</t>\n"
"            </td></tr>\n"
"        </tbody>\n"
"    </table>\n"
"    <table style=\"width:600px;margin:0px auto;background:white;border:1px solid #e1e1e1;\">\n"
"        <tbody>\n"
"            <tr><td style=\"padding:15px 20px 10px 20px;\">\n"
"                <p>Dear <span t-out=\"object.partner_id.name or 'participant'\">participant</span></p>\n"
"                <p>\n"
"                    Please find attached your\n"
"                        <strong t-out=\"object.survey_id.display_name or ''\">Furniture Creation</strong>\n"
"                    certification\n"
"                </p>\n"
"                <p>Congratulations for passing the test with a score of <strong t-out=\"object.scoring_percentage\"/>%!</p>\n"
"            </td></tr>\n"
"        </tbody>\n"
"    </table>\n"
"</div>\n"
"            "
msgstr ""
"<div style=\"background:#F0F0F0;color:#515166;padding:10px 0px;font-family:Arial,Helvetica,sans-serif;font-size:14px;\">\n"
"    <table style=\"width:600px;margin:5px auto;\">\n"
"        <tbody>\n"
"            <tr><td>\n"
"                <!-- アンケート調査を作成した会社のロゴを使用します(複数会社のケースに対応するため)  -->\n"
"                <a href=\"/\"><img t-if=\"not object.survey_id.create_uid.company_id.uses_default_logo\" t-attf-src=\"/logo.png?company={{ object.survey_id.create_uid.company_id.id }}\" style=\"vertical-align:baseline;max-width:100px;\"/></a>\n"
"            </td><td style=\"text-align:right;vertical-align:middle;\">\n"
"                    認定書: <t t-out=\"object.survey_id.display_name or ''\">フィードバックフォーム</t>\n"
"            </td></tr>\n"
"        </tbody>\n"
"    </table>\n"
"    <table style=\"width:600px;margin:0px auto;background:white;border:1px solid #e1e1e1;\">\n"
"        <tbody>\n"
"            <tr><td style=\"padding:15px 20px 10px 20px;\">\n"
"                <p> <span t-out=\"object.partner_id.name or 'participant'\">ご参加者</span>様</p>\n"
"                <p>\n"
"                    添付にて\n"
"                        <strong t-out=\"object.survey_id.display_name or ''\">家具製作</strong>\n"
"                    認定書をお送りいたします。\n"
"                </p>\n"
"                <p>テストに合格されました。おめでとうございます。 スコア:<strong t-out=\"object.scoring_percentage\"/>%!</p>\n"
"            </td></tr>\n"
"        </tbody>\n"
"    </table>\n"
"</div>\n"
"            "

#. module: survey
#: model:mail.template,body_html:survey.mail_template_user_input_invite
msgid ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dear <t t-out=\"object.partner_id.name or 'participant'\">participant</t><br/><br/>\n"
"        <t t-if=\"object.survey_id.certification\">\n"
"            You have been invited to take a new certification.\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            We are conducting a survey and your response would be appreciated.\n"
"        </t>\n"
"        </p><div style=\"margin: 16px 0px 16px 0px;\">\n"
"            <a t-att-href=\"(object.get_start_url())\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                <t t-if=\"object.survey_id.certification\">\n"
"                    Start Certification\n"
"                </t>\n"
"                <t t-else=\"\">\n"
"                    Start Survey\n"
"                </t>\n"
"            </a>\n"
"        </div>\n"
"        <t t-if=\"object.deadline\">\n"
"            Please answer the survey for <t t-out=\"format_date(object.deadline) or ''\">05/05/2021</t>.<br/><br/>\n"
"        </t>\n"
"        <t t-if=\"object.survey_id.certification\">\n"
"            We wish you good luck!\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            Thank you in advance for your participation.\n"
"        </t>\n"
"    \n"
"</div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        <t t-out=\"object.partner_id.name or 'participant'\">ご参加者</t>様<br/><br/>\n"
"        <t t-if=\"object.survey_id.certification\">\n"
"            新しい認定のご案内です。\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            アンケート調査にご協力をお願い致します。\n"
"        </t>\n"
"        </p><div style=\"margin: 16px 0px 16px 0px;\">\n"
"            <a t-att-href=\"(object.get_start_url())\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                <t t-if=\"object.survey_id.certification\">\n"
"                    認定を受ける\n"
"                </t>\n"
"                <t t-else=\"\">\n"
"                    アンケートに答える\n"
"                </t>\n"
"            </a>\n"
"        </div>\n"
"        <t t-if=\"object.deadline\">\n"
"            アンケート回答期限: <t t-out=\"format_date(object.deadline) or ''\">05/05/2021</t><br/><br/>\n"
"        </t>\n"
"        <t t-if=\"object.survey_id.certification\">\n"
"            ご健闘をお祈りしております。\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            ご協力頂きありがとうございます。\n"
"        </t>\n"
"    \n"
"</div>\n"
"            "

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "<i class=\"fa fa-bar-chart\"/> Results"
msgstr "<i class=\"fa fa-bar-chart\"/> 結果"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-check-square-o fa-lg me-2\"/>answer"
msgstr "<i class=\"fa fa-check-square-o fa-lg me-2\"/>回答"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-circle-o  fa-lg me-2\"/>answer"
msgstr "<i class=\"fa fa-circle-o  fa-lg me-2\"/>回答"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<i class=\"fa fa-circle-o fa-lg\" role=\"img\" aria-label=\"Not checked\" "
"title=\"Not checked\"/>"
msgstr ""
"<i class=\"fa fa-circle-o fa-lg\" role=\"img\" aria-label=\"Not checked\" "
"title=\"Not checked\"/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "<i class=\"fa fa-close\"/> Close"
msgstr "<i class=\"fa fa-close\"/> 閉じる"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-dot-circle-o fa-lg me-2\"/>answer"
msgstr "<i class=\"fa fa-dot-circle-o fa-lg me-2\"/>回答"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<i class=\"fa fa-dot-circle-o fa-lg\" role=\"img\" aria-label=\"Checked\" "
"title=\"Checked\"/>"
msgstr ""
"<i class=\"fa fa-dot-circle-o fa-lg\" role=\"img\" aria-label=\"Checked\" "
"title=\"Checked\"/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_button_form_view
msgid ""
"<i class=\"fa fa-exclamation-triangle\"/> It is currently not possible to "
"pass this assessment because no question is configured to give any points."
msgstr ""
"<i class=\"fa fa-exclamation-"
"triangle\"/>現在、どの問題も得点を与えるように設定されていないため、この評価に合格することはできません。 "

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid ""
"<i class=\"fa fa-fw fa-trophy\" role=\"img\" aria-label=\"Download certification\" title=\"Download certification\"/>\n"
"                                        Download certification"
msgstr ""
"<i class=\"fa fa-fw fa-trophy\" role=\"img\" aria-label=\"Download certification\" title=\"Download certification\"/>\n"
"                                        検定書のダウンロード"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\"/>"
msgstr "<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" title=\"矢印\"/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-square-o fa-lg me-2\"/>answer"
msgstr "<i class=\"fa fa-square-o fa-lg me-2\"/>回答"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_button_form_view
msgid "<i class=\"oi oi-fw oi-arrow-right\"/>Go to Survey"
msgstr "<i class=\"oi oi-fw oi-arrow-right\"/>アンケート調査へ移動"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid ""
"<span class=\"badge text-bg-secondary only_left_radius px-2 "
"pt-1\">Avg</span>"
msgstr ""
"<span class=\"badge text-bg-secondary only_left_radius px-2 pt-1\">平均</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid ""
"<span class=\"badge text-bg-secondary only_left_radius px-2 "
"pt-1\">Max</span>"
msgstr ""
"<span class=\"badge text-bg-secondary only_left_radius px-2 pt-1\">最大</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid ""
"<span class=\"badge text-bg-secondary only_left_radius px-2 "
"pt-1\">Min</span>"
msgstr ""
"<span class=\"badge text-bg-secondary only_left_radius px-2 pt-1\">最小 "
"</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid ""
"<span class=\"fw-bold text-muted ms-2 d-none d-md-inline\" id=\"enter-"
"tooltip\"> or press CTRL+Enter</span>"
msgstr ""
"<span class=\"fw-bold text-muted ms-2 d-none d-md-inline\" id=\"enter-"
"tooltip\">またはCTRL+Enterを入力</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid ""
"<span class=\"fw-bold text-muted ms-2 d-none d-md-inline\">\n"
"                            <span id=\"enter-tooltip\">or press Enter</span>\n"
"                        </span>"
msgstr ""
"<span class=\"fw-bold text-muted ms-2 d-none d-md-inline\">\n"
"                            <span id=\"enter-tooltip\">またはエンターを押す</span>\n"
"                        </span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid ""
"<span class=\"fw-bold text-muted ms-2 d-none d-md-inline\">\n"
"                    <span id=\"enter-tooltip\">or press CTRL+Enter</span>\n"
"                </span>"
msgstr ""
"<span class=\"fw-bold text-muted ms-2 d-none d-md-inline\">\n"
"                    <span id=\"enter-tooltip\">またはCTRL+Enterを入力</span>\n"
"                </span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "<span class=\"mx-1\">-</span>"
msgstr "<span class=\"mx-1\">-</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.res_partner_view_form
msgid ""
"<span class=\"o_stat_text\" invisible=\"certifications_company_count &lt; 2\">Certifications</span>\n"
"                        <span class=\"o_stat_text\" invisible=\"certifications_company_count &gt; 1\">Certification</span>"
msgstr ""
"<span class=\"o_stat_text\" invisible=\"certifications_company_count &lt; 2\">検定</span>\n"
"                        <span class=\"o_stat_text\" invisible=\"certifications_company_count &gt; 1\">検定</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.res_partner_view_form
msgid ""
"<span class=\"o_stat_text\" invisible=\"certifications_count &lt; 2\">Certifications</span>\n"
"                        <span class=\"o_stat_text\" invisible=\"certifications_count &gt; 1\">Certification</span>"
msgstr ""
"<span class=\"o_stat_text\" invisible=\"certifications_count &lt; 2\">検定</span>\n"
"                        <span class=\"o_stat_text\" invisible=\"certifications_count &gt; 1\">検定</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid ""
"<span class=\"o_survey_enter fw-bold text-muted ms-2 d-none d-md-inline\">or"
" press Enter</span>"
msgstr ""
"<span class=\"o_survey_enter fw-bold text-muted ms-2 d-none d-md-"
"inline\">またはエンターを押す</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_selection_key
msgid ""
"<span class=\"o_survey_key text-center position-absolute bg-white rounded-"
"start py-0 ps-2\"><span class=\"text-primary text-center text-center w-100 "
"position-relative\">Key</span></span>"
msgstr ""
"<span class=\"o_survey_key text-center position-absolute bg-white rounded-"
"start py-0 ps-2\"><span class=\"text-primary text-center text-center w-100 "
"position-relative\">キー</span></span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid ""
"<span class=\"o_survey_results_topbar_clear_filters text-primary\">\n"
"                                <i class=\"fa fa-trash me-1\"/>Remove all filters\n"
"                            </span>"
msgstr ""
"<span class=\"o_survey_results_topbar_clear_filters text-primary\">\n"
"                                <i class=\"fa fa-trash me-1\"/>全てのフィルタを削除\n"
"                            </span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid ""
"<span class=\"o_survey_session_answer_count\">0</span>\n"
"                                     /"
msgstr ""
"<span class=\"o_survey_session_answer_count\">0</span>\n"
"                                     /"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_open
msgid ""
"<span class=\"o_survey_session_navigation_next_label\">Start</span>\n"
"                        <i class=\"oi oi-chevron-right\"/>"
msgstr ""
"<span class=\"o_survey_session_navigation_next_label\">開始</span>\n"
"                        <i class=\"oi oi-chevron-right\"/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "<span class=\"text-break text-muted\">Completed</span>"
msgstr "<span class=\"text-break text-muted\">完了済</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "<span class=\"text-break text-muted\">Registered</span>"
msgstr "<span class=\"text-break text-muted\">登録済</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "<span class=\"text-break text-muted\">Success</span>"
msgstr "<span class=\"text-break text-muted\">成功</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "<span class=\"text-muted\">Average Duration</span>"
msgstr "<span class=\"text-muted\">平均期間</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "<span class=\"text-muted\">Questions</span>"
msgstr "<span class=\"text-muted\">質問</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid "<span class=\"text-muted\">Responded</span>"
msgstr "<span class=\"text-muted\">回答済</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid "<span class=\"text-success\">Correct</span>"
msgstr "<span class=\"text-success\">正解</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid "<span class=\"text-warning\">Partial</span>"
msgstr "<span class=\"d-block text-warning\">部分的</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<span invisible=\"not is_scored_question\">Points</span>"
msgstr "<span invisible=\"not is_scored_question\">ポイント</span>"

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p3_q1
msgid ""
"<span>\"Red\" is not a category, I know what you are trying to do ;)</span>"
msgstr "<span>\"赤\"は分類ではありません。何をしたいかは分かっています;）</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "<span>%</span>"
msgstr "<span>%</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "<span>All surveys</span>"
msgstr "<span>全てのアンケート</span>"

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p3_q6
msgid "<span>Best time to do it, is the right time to do it.</span>"
msgstr "<span>何かを行うための最善なタイミングは、それを行うのに最適なタイミングである。</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "<span>Completed surveys</span>"
msgstr "<span>完了済アンケート</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid "<span>Date</span>"
msgstr "<span>日付</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<span>Do you like it?</span><br/>"
msgstr "<span>気に入りましたか？</span><br/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "<span>Failed only</span>"
msgstr "<span>不合格のみ</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<span>How many ...?</span><br/>\n"
"                                    <i class=\"fa fa-2x\" role=\"img\" aria-label=\"Numeric\" title=\"Numeric\">123 </i>\n"
"                                    <i class=\"fa fa-2x fa-sort\" role=\"img\" aria-label=\"Numeric\"/>"
msgstr ""
"<span>いくつでしょう ...?</span><br/>\n"
"                                    <i class=\"fa fa-2x\" role=\"img\" aria-label=\"Numeric\" title=\"Numeric\">123 </i>\n"
"                                    <i class=\"fa fa-2x fa-sort\" role=\"img\" aria-label=\"Numeric\"/>"

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p5_q1
msgid ""
"<span>If you don't like us, please try to be as objective as "
"possible.</span>"
msgstr "<span>気に入らない場合、可能な限り客観的になってみてください。</span>の"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<span>Name all the animals</span><br/>\n"
"                                    <i class=\"fa fa-align-justify fa-4x\" role=\"img\" aria-label=\"Multiple lines\" title=\"Multiple Lines\"/>"
msgstr ""
"<span>全ての動物の名前を挙げる</span><br/>\n"
"                                    <i class=\"fa fa-align-justify fa-4x\" role=\"img\" aria-label=\"Multiple lines\" title=\"Multiple Lines\"/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<span>Name one animal</span><br/>\n"
"                                    <i class=\"fa fa-minus fa-4x\" role=\"img\" aria-label=\"Single Line\" title=\"Single Line\"/>"
msgstr ""
"<span>動物の名前を1つ挙げる</span><br/>\n"
"                                    <i class=\"fa fa-minus fa-4x\" role=\"img\" aria-label=\"Single Line\" title=\"Single Line\"/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_button_retake
msgid "<span>Number of attempts left</span>:"
msgstr "<span>残試行回数</span>:"

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p2_q1
msgid "<span>Our famous Leader!</span>"
msgstr "<span>有名なリーダー!</span>"

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p3_q3
msgid "<span>Our sales people have an advantage, but you can do it!</span>"
msgstr "<span>当社の営業担当者が優位ですが、あなたにもできます！</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "<span>Passed and Failed</span>"
msgstr "<span>合格と不合格</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "<span>Passed only</span>"
msgstr "<span>合格のみ</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid ""
"<span>This certificate is presented to</span>\n"
"                                <br/>"
msgstr ""
"<span>以下にこの証明書を贈呈します:</span>\n"
"                                <br/>"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "<span>Try It</span>"
msgstr "<span>やってみましょう</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "<span>Waiting for attendees...</span>"
msgstr "<span>参加者を待っています... </span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<span>When does ... start?</span><br/>"
msgstr "<span>いつ始まりますか？</span><br/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<span>When is Christmas?</span><br/>"
msgstr "<span>クリスマスはいつですか？</span><br/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<span>Which are yellow?</span><br/>"
msgstr "<span>黄色はどれですか？(複数)</span><br/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<span>Which is yellow?</span><br/>"
msgstr "<span>黄色はどれですか？</span><br/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid ""
"<span>for successfully completing</span>\n"
"                                <br/>"
msgstr ""
"<span>以下に合格</span>\n"
"                                <br/>"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q4
msgid "A \"Citrus\" could give you ..."
msgstr "\"シトラス\"があなたに提供するのは..."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
msgid "A label must be attached to only one question."
msgstr "ラベルは、一つの質問のみに紐づく必要があります。"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_positive_len_max
#: model:ir.model.constraint,message:survey.constraint_survey_question_positive_len_min
msgid "A length must be positive!"
msgstr "長さは正でなければなりません!"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_3_choice_4
msgid "A little bit overpriced"
msgstr "少々割高"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_3_choice_5
msgid "A lot overpriced"
msgstr "かなり割高"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_session_speed_rating_has_time_limit
msgid ""
"A positive default time limit is required when the session rewards quick "
"answers."
msgstr "セッションで迅速な回答が報われる場合、正のデフォルトの制限時間が必要です。"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question_answer__answer_score
msgid ""
"A positive score indicates a correct choice; a negative or null score "
"indicates a wrong answer"
msgstr "正のスコアが正しい選択を示しています。負またはnullスコアが間違った答えを示します"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form
msgid "A problem has occurred"
msgstr "問題が発生しました"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
msgid "A question can either be skipped or answered, not both."
msgstr "質問はスキップまたは回答するかのどちらかで、両方はできません。"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid ""
"A scored survey needs at least one question that gives points.\n"
"Please check answers and their scores."
msgstr ""
"採点されたアンケートは、少なくとも 1 つは得点を与える質問が必要です。\n"
"回答と点数を確認してください。"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p2
msgid "About our ecommerce"
msgstr "私たちのeコマースについて"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p1
msgid "About you"
msgstr "あなた自身について"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__survey_access_mode
#: model:ir.model.fields,field_description:survey.field_survey_survey__access_mode
msgid "Access Mode"
msgstr "アクセスモード"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__access_token
msgid "Access Token"
msgstr "アクセストークン"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_access_token_unique
msgid "Access token should be unique"
msgstr "アクセストークンは一意である必要があります"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_needaction
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_needaction
msgid "Action Needed"
msgstr "要アクション"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__active
msgid "Active"
msgstr "有効化"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_ids
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_ids
msgid "Activities"
msgstr "活動"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_exception_decoration
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "例外活動文字装飾"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_state
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_state
msgid "Activity State"
msgstr "活動状態"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_type_icon
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_type_icon
msgid "Activity Type Icon"
msgstr "活動種別アイコン"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Add a question"
msgstr "質問を追加"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Add a section"
msgstr "セクションを追加"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Add existing contacts..."
msgstr "既存の連絡先を追加..."

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "Add some fun to your presentations by sharing questions live"
msgstr "ライブで質問を共有することで、プレゼンテーションを楽しくしましょう。"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__emails
msgid "Additional emails"
msgstr "追加の電子メール"

#. module: survey
#: model:res.groups,name:survey.group_survey_manager
msgid "Administrator"
msgstr "管理者"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q4_sug1
msgid "Africa"
msgstr "アフリカ"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q6
msgid ""
"After watching this video, will you swear that you are not going to "
"procrastinate to trim your hedge this year?"
msgstr "このビデオを見たからには、今年は生け垣の刈り込みを先延ばしにしないと誓えますか？"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_col3
msgid "Agree"
msgstr "一致します"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_scored_date_have_answers
msgid ""
"All \"Is a scored question = True\" and \"Question Type: Date\" questions "
"need an answer"
msgstr "すべての\"スコア有設問か= True\"と\"質問の種類:日付\"の質問には答えを必要とします"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_scored_datetime_have_answers
msgid ""
"All \"Is a scored question = True\" and \"Question Type: Datetime\" "
"questions need an answer"
msgstr "すべての\"スコア有設問か = True\"と''質問の種類:日時\"の質問には答えを必要とします"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__questions_selection__all
msgid "All questions"
msgstr "すべての質問"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "All surveys"
msgstr "すべてのアンケート"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_is_time_limited_have_time_limit
msgid "All time-limited questions need a positive time limit"
msgstr "時間制限のある質問には全て正の制限時間が必要です。"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Allow Roaming"
msgstr "ローミングを許可"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__allowed_triggering_question_ids
msgid "Allowed Triggering Questions"
msgstr "許可済のトリガとなる質問"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__allowed_survey_types
msgid "Allowed survey types"
msgstr "可能な調査タイプ"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q2_sug2
msgid "Amenhotep"
msgstr "アメンホテプ"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_user_input_unique_token
msgid "An access token must be unique!"
msgstr "アクセストークンは一意である必要があります!"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_positive_answer_score
msgid "An answer score for a non-multiple choice question cannot be negative!"
msgstr "非複数の選択肢の質問の回答スコアが負にすることはできません!"

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p3
msgid "An apple a day keeps the doctor away."
msgstr "リンゴ1日1個で医者いらず"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_leaderboard
msgid "Anonymous"
msgstr "匿名"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
#: model_terms:ir.ui.view,arch_db:survey.survey_question_answer_view_tree
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Answer"
msgstr "回答"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__answer_type
msgid "Answer Type"
msgstr "回答タイプ"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__deadline
msgid "Answer deadline"
msgstr "回答期限"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question_answer__value_label
msgid ""
"Answer label as either the value itself if not empty or a letter "
"representing the index of the answer otherwise."
msgstr "回答ラベルは、空でない場合は値そのもの、そうでない場合は回答のインデックスを表す文字となります。"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__user_input_line_ids
#: model:ir.model.fields,field_description:survey.field_survey_user_input__user_input_line_ids
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Answers"
msgstr "回答"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_answer_count
msgid "Answers Count"
msgstr "回答数"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__access_mode__public
msgid "Anyone with the link"
msgstr "リンクをお持ちの方"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_gamification_challenge__challenge_category
msgid "Appears in"
msgstr "表示されます"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q3_sug1
msgid "Apple Trees"
msgstr "リンゴの樹"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_row1
msgid "Apples"
msgstr "Apples"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Archived"
msgstr "アーカイブ済"

#. module: survey
#: model:survey.question,title:survey.survey_demo_food_preferences_q1
msgid "Are you vegetarian?"
msgstr "ベジタリアンですか？"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p5
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p1_q1_sug4
msgid "Art & Culture"
msgstr "アート＆カルチャー"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q1_sug1
msgid "Arthur B. McDonald"
msgstr "アーサー・B・マクドナルド"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q4_sug2
msgid "Asia"
msgstr "アジア"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__survey_type__assessment
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "Assessment"
msgstr "評価"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_attachment_count
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_attachment_count
msgid "Attachment Count"
msgstr "添付数"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__attachment_ids
msgid "Attachments"
msgstr "添付"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__attempts_number
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Attempt n°"
msgstr "試行回数"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__answer_done_count
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Attempts"
msgstr "試行"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__attempts_count
msgid "Attempts Count"
msgstr "試行数"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_user_input__nickname
msgid ""
"Attendee nickname, mainly used to identify them in the survey session "
"leaderboard."
msgstr "主にアンケートセッションのリーダーボードで参加者を識別するために使用されます。"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "Attendees are answering the question..."
msgstr "参加者は質問に答えています..."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__session_speed_rating
msgid "Attendees get more points if they answer quickly"
msgstr "参加者はすぐに答えると、より多くのポイントを取得します"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__author_id
msgid "Author"
msgstr "著作者"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__certification_mail_template_id
msgid ""
"Automated email sent to the user when they succeed the certification, "
"containing their certification document."
msgstr "検定に合格すると、検定書を含む自動メールがユーザーに送信されます。"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_sug3
msgid "Autumn"
msgstr "秋"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid "Average"
msgstr "平均"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__answer_duration_avg
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
msgid "Average Duration"
msgstr "平均所要時間"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
msgid "Average Score"
msgstr "平均スコア"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__answer_duration_avg
msgid "Average duration of the survey (in hours)"
msgstr "アンケートの平均所要時間 (時間)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__answer_score_avg
msgid "Avg Score (%)"
msgstr "平均スコア (%)"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q1_sug3
msgid "Avicii"
msgstr "Avici"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__background_image
#: model:ir.model.fields,field_description:survey.field_survey_survey__background_image
msgid "Background Image"
msgstr "背景画像"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__background_image_url
#: model:ir.model.fields,field_description:survey.field_survey_survey__background_image_url
msgid "Background Url"
msgstr "背景URL"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.gamification_badge_form_view_simplified
msgid "Badge"
msgstr "バッジ"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q3_sug3
msgid "Baobab Trees"
msgstr "バオバブの木"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q2_sug1
msgid "Bees"
msgstr "ミツバチ"

#. module: survey
#: model:survey.question,question_placeholder:survey.vendor_certification_page_3_question_3
msgid "Beware of leap years!"
msgstr "うるう年に注意して下さい！"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Blue Pen"
msgstr "青ペン"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__body_has_template_value
msgid "Body content is the same as the template"
msgstr "本文はテンプレートと同じです。"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q4_sug4
msgid "Bricks"
msgstr "レンガ"

#. module: survey
#: model:survey.question,question_placeholder:survey.survey_feedback_p1_q1
msgid "Brussels"
msgstr "ブリュッセル"

#. module: survey
#: model:survey.question,question_placeholder:survey.survey_demo_quiz_p1_q3
msgid "Brussels, Belgium"
msgstr "ブリュッセル、ベルギー"

#. module: survey
#: model:survey.survey,title:survey.survey_demo_burger_quiz
msgid "Burger Quiz"
msgstr "バーガークイズ"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "But first, keep listening to the host."
msgstr "その前に、ホストの話を聞き続けましょう。"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_2_choice_3
msgid "Cabinet with Doors"
msgstr "ドア付きキャビネット"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q5_row1
msgid "Cactus"
msgstr "サボテン"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__can_edit_body
msgid "Can Edit Body"
msgstr "本文編集可"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p4_q3
msgid "Can Humans ever directly see a photon?"
msgstr "人間は光子を直接見ることができますか？"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_tree
msgid "Certification"
msgstr "検定"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification_badge_id
msgid "Certification Badge"
msgstr "検定バッジ"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification_badge_id_dummy
msgid "Certification Badge "
msgstr "検定バッジ"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid "Certification Badge is not configured for the survey %(survey_name)s"
msgstr "検定バッジはアンケート %(survey_name)s用には作成されません。"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid "Certification Failed"
msgstr "検定不合格"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid "Certification n°"
msgstr "検定書番号"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification_report_layout
msgid "Certification template"
msgstr "検定テンプレート"

#. module: survey
#: model:mail.template,subject:survey.mail_template_certification
msgid "Certification: {{ object.survey_id.display_name }}"
msgstr "検定: {{ object.survey_id.display_name }}"

#. module: survey
#: model:ir.actions.report,name:survey.certification_report
#: model:ir.model.fields.selection,name:survey.selection__gamification_challenge__challenge_category__certification
msgid "Certifications"
msgstr "検定"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_res_partner__certifications_count
#: model:ir.model.fields,field_description:survey.field_res_users__certifications_count
msgid "Certifications Count"
msgstr "検定数"

#. module: survey
#: model:ir.actions.act_window,name:survey.res_partner_action_certifications
msgid "Certifications Succeeded"
msgstr "検定合格"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "Certified"
msgstr "認証済み"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification_mail_template_id
msgid "Certified Email Template"
msgstr "認証済メールテンプレート"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_2_choice_1
msgid "Chair floor protection"
msgstr "チェアマット"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Cheating on your neighbors will not help!"
msgstr "隣の人のカンニングをしても役に立ちませんよ！"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__is_attempts_limited
#: model:ir.model.fields,help:survey.field_survey_user_input__is_attempts_limited
msgid "Check this option if you want to limit the number of attempts per user"
msgstr "ユーザーごとの試行回数を制限したい場合は、このオプションをチェックしてください"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/views/widgets/boolean_update_flag_field/boolean_update_flag_fields.js:0
msgid "Checkbox updating comparison flag"
msgstr "チェックボックス更新比較フラグ"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q2_sug2
msgid "China"
msgstr "中国"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Choices"
msgstr "選択肢"

#. module: survey
#: model_terms:survey.survey,description:survey.survey_demo_burger_quiz
msgid "Choose your favourite subject and show how good you are. Ready?"
msgstr "好きな科目を選び、実力を見せて下さい。準備はできましたか？"

#. module: survey
#: model:survey.question,title:survey.survey_demo_food_preferences_q3
msgid "Choose your green meal"
msgstr "菜食を選ぶ"

#. module: survey
#: model:survey.question,title:survey.survey_demo_food_preferences_q4
msgid "Choose your meal"
msgstr "食事を選ぶ"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__classic_blue
msgid "Classic Blue"
msgstr "クラシックブルー"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__classic_gold
msgid "Classic Gold"
msgstr "クラシックゴールド"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__classic_purple
msgid "Classic Purple"
msgstr "クラシックパープル"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_row3
msgid "Clementine"
msgstr "クレメンタイン"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q1_sug4
msgid "Cliff Burton"
msgstr "クリフ・バートン"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Close"
msgstr "閉じる"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Close Live Session"
msgstr "ライブセッションを閉じる"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_3_choice_1
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "Color"
msgstr "色"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__color
msgid "Color Index"
msgstr "カラーインデクス"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid ""
"Combining roaming and \"Scoring with answers after each page\" is not possible; please update the following surveys:\n"
"- %(survey_names)s"
msgstr ""
"ローミングと\"各ページ後の回答による採点\"を組み合わせることはできません: \n"
"- %(survey_names)s"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_question_form
msgid "Come back once you have added questions to your Surveys."
msgstr "アンケートに質問を追加したら、また戻ってきて下さい。"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_comments
msgid "Comment"
msgstr "コメント"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__comments_message
msgid "Comment Message"
msgstr "コメントメッセージ"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__comment_count_as_answer
msgid "Comment is an answer"
msgstr "コメントは回答です。"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_res_partner__certifications_company_count
#: model:ir.model.fields,field_description:survey.field_res_users__certifications_company_count
msgid "Company Certifications Count"
msgstr "会社の検定件数"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input__state__done
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Completed"
msgstr "完了"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "Completed surveys"
msgstr "完了済アンケート"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Compose Email"
msgstr "Eメール作成"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
msgid "Computing score requires a question in arguments."
msgstr "スコアを計算するには、引数に質問が必要です。"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Conditional display"
msgstr "条件付き表示"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/views/widgets/survey_question_trigger/survey_question_trigger.js:0
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"Conditional display is not available when questions are randomly picked."
msgstr "質問がランダムに選択される場合、条件付き表示は利用できません。"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_2_choice_3
msgid "Conference chair"
msgstr "会議チェア"

#. module: survey
#: model_terms:web_tour.tour,rainbow_man_message:survey.survey_tour
msgid "Congratulations! You are now ready to collect feedback like a pro :-)"
msgstr "おめでとうございます！これでプロのようにフィードバックを集める準備が整いました :-)"

#. module: survey
#: model_terms:gamification.badge,description:survey.vendor_certification_badge
msgid "Congratulations, you are now official vendor of MyCompany"
msgstr "おめでとう、今のMyCompanyの公式ベンダーです"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "Congratulations, you have passed the test!"
msgstr "おめでとう、試験に合格しました!"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Constraints"
msgstr "制約"

#. module: survey
#: model:ir.model,name:survey.model_res_partner
#: model:ir.model.fields,field_description:survey.field_survey_user_input__partner_id
msgid "Contact"
msgstr "連絡先"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__has_conditional_questions
msgid "Contains conditional questions"
msgstr "条件付きの質問が含まれています"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__body
msgid "Contents"
msgstr "内容"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "Continue"
msgstr "次へ進む"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form
msgid "Continue here"
msgstr "ここで続行"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q3_sug4
msgid "Cookies"
msgstr "クッキー"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_session_manage.js:0
msgid "Copied!"
msgstr "コピーされました。"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q1_sug3
msgid "Cornaceae"
msgstr "ミズキ科"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_2_choice_1
msgid "Corner Desk Right Sit"
msgstr "コーナーデスク右座"

#. module: survey
#. odoo-javascript
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
#: code:addons/survey/static/src/js/survey_result.js:0
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__is_correct
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__answer_is_correct
msgid "Correct"
msgstr "正しい"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Correct Answer"
msgstr "正解"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__answer_datetime
msgid "Correct date and time answer for this question."
msgstr "この質問に対する正しい日付と時刻の答え。"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__answer_date
msgid "Correct date answer"
msgstr "正しい日付の答え"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__answer_date
msgid "Correct date answer for this question."
msgstr "この質問に対する正しい日付の答え。"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__answer_datetime
msgid "Correct datetime answer"
msgstr "正しい日時の答え"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__answer_numerical_box
msgid "Correct number answer for this question."
msgstr "この質問に対する正しい数値の答え。"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__answer_numerical_box
msgid "Correct numerical answer"
msgstr "正しい数値の答え"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_3_choice_3
msgid "Correctly priced"
msgstr "適正な価格"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q4_sug3
msgid "Cosmic rays"
msgstr "宇宙線"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Create Live Session"
msgstr "ライブセッションを作成"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "Create a custom survey from scratch"
msgstr "顧客アンケートをゼロから作成"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_question__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_survey__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__create_uid
msgid "Created by"
msgstr "作成者"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__create_date
#: model:ir.model.fields,field_description:survey.field_survey_question__create_date
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__create_date
#: model:ir.model.fields,field_description:survey.field_survey_survey__create_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input__create_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__create_date
msgid "Created on"
msgstr "作成日"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid "Creating test token is not allowed for you."
msgstr "テストトークンの作成は許可されていません。"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid ""
"Creating token for anybody else than employees is not allowed for internal "
"surveys."
msgstr "社内調査では、従業員以外のためにトークンを作成することは許可されていません。"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid "Creating token for closed/archived surveys is not allowed."
msgstr "クローズ/アーカイブされたアンケート用のトークン作成は許可されていません。"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid ""
"Creating token for external people is not allowed for surveys requesting "
"authentication."
msgstr "認証を要求するタイプのアンケートについて外部の人々のためのトークン作成は許可されていません。"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_question_id
msgid "Current Question"
msgstr "現在の質問"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_question_start_time
msgid "Current Question Start Time"
msgstr "現在の質問開始時間"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_start_time
msgid "Current Session Start Time"
msgstr "現在のセッション開始時間"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__is_time_limited
msgid "Currently only supported for live sessions."
msgstr "現在はライブ・セッションのためにサポートされています。"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__survey_type__custom
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "Custom"
msgstr "カスタム"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid ""
"Customers will receive a new token and be able to completely retake the "
"survey."
msgstr "お客様が新しいトークンを受信して 、アンケートを再受験することができるようになります。"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Customers will receive the same token."
msgstr "お客様は、同じトークンを受け取ります。"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_2_choice_5
msgid "Customizable Lamp"
msgstr "カスタマイズ可能なランプ"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__is_time_customized
msgid "Customized speed rewards"
msgstr "カスタマイズされたスピード報酬"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid "DEMO_CERTIFIED_NAME"
msgstr "DEMO_CERTIFIED_NAME"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__date
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__date
msgid "Date"
msgstr "日付"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_date
msgid "Date answer"
msgstr "回答日"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__datetime
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__datetime
msgid "Datetime"
msgstr "日付時刻"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_datetime
msgid "Datetime answer"
msgstr "日時答え"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_user_input__deadline
msgid "Datetime until customer can open the survey and submit answers"
msgstr "日時までの顧客は、アンケートを開いて答えを提出することができます"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__deadline
msgid "Deadline"
msgstr "期日"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__session_speed_rating_time_limit
msgid "Default time given to receive additional points for right answers"
msgstr "正解の追加ポイントを受け取るための既定時間"

#. module: survey
#: model:ir.model.fields,help:survey.field_gamification_challenge__challenge_category
msgid "Define the visibility of the challenge through menus"
msgstr "メニューからチャレンジの可視性を定義する"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "Delete"
msgstr "削除"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__description
#: model:ir.model.fields,field_description:survey.field_survey_survey__description
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Description"
msgstr "説明"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_2_choice_2
msgid "Desk Combination"
msgstr "デスクコンビネーション"

#. module: survey
#: model:ir.actions.act_window,name:survey.survey_user_input_line_action
#: model:ir.ui.menu,name:survey.menu_survey_response_line_form
msgid "Detailed Answers"
msgstr "詳細な回答"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_col2
msgid "Disagree"
msgstr "同意"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__display_name
#: model:ir.model.fields,field_description:survey.field_survey_question__display_name
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__display_name
#: model:ir.model.fields,field_description:survey.field_survey_survey__display_name
#: model:ir.model.fields,field_description:survey.field_survey_user_input__display_name
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__display_name
msgid "Display Name"
msgstr "表示名"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__progression_mode
msgid "Display Progress as"
msgstr "進捗状況の表示"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/views/widgets/survey_question_trigger/survey_question_trigger.js:0
msgid "Displayed if \"%s\"."
msgstr "もし\"%s\"であれば表示"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Displayed when the answer entered is not valid."
msgstr "入力された回答が無効な場合に表示"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1_question_1
msgid "Do we sell Acoustic Bloc Screens?"
msgstr "当社は遮音スクリーンを販売していますか？"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p2_q3
msgid "Do you have any other comments, questions, or concerns?"
msgstr "コメント、質問、または何か気になることはありますか？"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1_question_5
msgid "Do you think we have missing products in our catalog? (not rated)"
msgstr "カタログに何かの製品が不足していますか？ (未評価)"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q2_sug2
msgid "Dogs"
msgstr "犬"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q1
msgid "Dogwood is from which family of trees?"
msgstr "ハナミズキは何科の樹木ですか？"

#. module: survey
#: model:survey.question,question_placeholder:survey.survey_demo_quiz_p1_q2
msgid "Don't be shy, be wild!"
msgstr "恥ずかしがらず、大胆に！"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q5_sug1
msgid "Douglas Fir"
msgstr "Douglas Fir"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_2_choice_4
msgid "Drawer"
msgstr "引き出し"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Duplicate Question"
msgstr "重複した質問"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "Edit Survey"
msgstr "調査の編集"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_void_content
msgid "Edit in backend"
msgstr "バックエンドで編集"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__email
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Email"
msgstr "メール"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "End Live Session"
msgstr "ライブセッションを終了"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__description_done
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "End Message"
msgstr "最後のメッセージ"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__end_datetime
msgid "End date and time"
msgstr "終了日時"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_session_manage.js:0
msgid "End of Survey"
msgstr "アンケート終了"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_session_code
msgid "Enter Session Code"
msgstr "セッションコードを入力"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__constr_error_msg
msgid "Error message"
msgstr "エラーメッセージ"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q4_sug3
msgid "Europe"
msgstr "ヨーロッパ"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q5_sug3
msgid "European Yew"
msgstr "ヨーロッパイチイ"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Exclude Tests"
msgstr "テストを除外"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__existing_partner_ids
msgid "Existing Partner"
msgstr "既存のパートナー"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__existing_emails
msgid "Existing emails"
msgstr "既存の電子メール"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Extremely likely"
msgstr "可能性が極めて高い"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q3_sug2
msgid "Eyjafjallajökull (Iceland)"
msgstr "Eyjafjallajökull (アイスランド)"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Failed"
msgstr "不合格"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "Failed only"
msgstr "不合格のみ"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_2_choice_2
msgid "Fanta"
msgstr "Fanta"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#: model:survey.survey,title:survey.survey_feedback
msgid "Feedback Form"
msgstr "フィードバックフォーム"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q5_row2
msgid "Ficus"
msgstr "イチジク"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
#: model_terms:ir.ui.view,arch_db:survey.question_result_matrix
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date_or_datetime
#: model_terms:ir.ui.view,arch_db:survey.question_result_text
msgid "Filter surveys"
msgstr "アンケートをフィルタ"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_session_manage.js:0
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "Final Leaderboard"
msgstr "最終的なリーダーボード"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_food_preferences_q4_sug2
msgid "Fish"
msgstr "魚"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_follower_ids
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_follower_ids
msgid "Followers"
msgstr "フォロワー"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_partner_ids
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_partner_ids
msgid "Followers (Partners)"
msgstr "フォロワー (取引先)"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__activity_type_icon
#: model:ir.model.fields,help:survey.field_survey_user_input__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesomeのアイコン 例. fa-tasks"

#. module: survey
#: model:survey.survey,title:survey.survey_demo_food_preferences
msgid "Food Preferences"
msgstr "食事の好み"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__text_box
msgid "Free Text"
msgstr "自由記入欄"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_text_box
msgid "Free Text answer"
msgstr "自由記入回答"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q4
msgid "From which continent is native the Scots pine (pinus sylvestris)?"
msgstr "セイヨウアカマツ (pinus sylvestris)の原産大陸はどこでしょうか？"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q1_sug1
msgid "Fruits"
msgstr "果物"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3
msgid "Fruits and vegetables"
msgstr "果物と野菜"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid "Functional Training"
msgstr "機能トレーニング"

#. module: survey
#: model:ir.model,name:survey.model_gamification_badge
msgid "Gamification Badge"
msgstr "ゲーミフィケーションのバッジ"

#. module: survey
#: model:ir.model,name:survey.model_gamification_challenge
msgid "Gamification Challenge"
msgstr "ゲーミフィケーションの挑戦"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "Gather feedbacks from your employees and customers"
msgstr "従業員と顧客からのフィードバックを集める"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p2
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p1_q1_sug1
msgid "Geography"
msgstr "地理"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification_give_badge
msgid "Give Badge"
msgstr "バッジを付けます"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p2_q3
msgid "Give the list of all types of wood we sell."
msgstr "私達が販売する木材のすべてのタイプのリストを提供してください。"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p5_q1_sug1
msgid "Good"
msgstr "良"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Good luck!"
msgstr "幸運を！"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug4
msgid "Good value for money"
msgstr "お金に良い値"

#. module: survey
#: model_terms:survey.survey,description_done:survey.survey_demo_food_preferences
msgid "Got it!"
msgstr "了解です！"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q4_sug2
msgid "Grapefruits"
msgstr "グレープフルーツ"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_answer_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_line_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Group By"
msgstr "グループ化"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__existing_mode
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Handle existing"
msgstr "既存のハンドル"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "Handle quiz &amp; certifications"
msgstr "クイズ &amp; 検定を処理する"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q3_sug1
msgid "Hard"
msgstr "ハード"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__has_message
#: model:ir.model.fields,field_description:survey.field_survey_user_input__has_message
msgid "Has Message"
msgstr "メッセージあり"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__has_image_only_suggested_answer
msgid "Has image only suggested answer"
msgstr "画像のみの回答提案あり"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_3_choice_2
msgid "Height"
msgstr "高さ"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Help Participants know what to write"
msgstr "参加者が何を書けばいいのかわかるようにします"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q2_sug3
msgid "Hemiunu"
msgstr "ヘミウヌ"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Here, you can overview all the participations."
msgstr "ここで全ての参加者の概要を見ることができます。"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug1
msgid "High quality"
msgstr "高品質"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p3
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p1_q1_sug2
msgid "History"
msgstr "履歴"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p1_q3
msgid "How frequently do you buy products online?"
msgstr "どのくらいの頻度でオンラインでプロダクトを購入しますか？"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "How frequently do you use our products?"
msgstr "当社のプロダクトをどのくらいの頻度で使用しますか？"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "How good of a presenter are you? Let's find out!"
msgstr "あなたのプレゼン能力は？見てみましょう！"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "How likely are you to recommend the following products to a friend?"
msgstr "以下のプロダクトを友人に薦める可能性は？"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p2_q1
msgid "How long is the White Nile river?"
msgstr "白ナイル川はどのくらいの長さですか？"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_6
msgid ""
"How many chairs do you think we should aim to sell in a year (not rated)?"
msgstr "年間で何台の椅子を販売することを目標にすべきでしょうか？(未評価)"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_1
msgid "How many days is our money-back guarantee?"
msgstr "当社の返金保証は何日ですか？"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "How many orders did you pass during the last 6 months?"
msgstr "過去6か月間に何件のオーダを受けましたか？"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p1_q4
msgid "How many times did you order products on our website?"
msgstr "当ウェブサイトで何回プロダクトを注文しましたか？"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1_question_4
msgid "How many versions of the Corner Desk do we have?"
msgstr "私たちはどのように多くのコーナーデスクのバージョンがありますか？"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p3_q3
msgid "How many years did the 100 years war last?"
msgstr "100年戦争は何年続きましたか？"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_2_question_1
msgid "How much do we sell our Cable Management Box?"
msgstr "どのくらい私たちはケーブルマネジメントボックスを販売していますか？"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q5
msgid "How often should you water those plants"
msgstr "どのくらいの頻度で、これらの植物に水やりをすべきですか"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p1_q4
msgid "How old are you?"
msgstr "年齢を教えて下さい。"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q3_sug4
msgid ""
"I actually don't like thinking. I think people think I like to think a lot. "
"And I don't. I do not like to think at all."
msgstr ""
"私は実は思考が好きではありません。人々は、私は沢山思考するのが好きだと思っていると思います。ですが、そうではありません。私はすべてにおいて考えることが好きではありません。"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q3_sug2
msgid ""
"I am fascinated by air. If you remove the air from the sky, all the birds "
"would fall to the ground. And all the planes, too."
msgstr "私は空気に魅せられています。空から空気を除去した場合、すべての鳥は地面に落ちるでしょう。そして、すべての飛行機も。"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_row5
msgid "I have added products to my wishlist"
msgstr "ウィッシュリストに製品を追加しました"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p5_q1_sug4
msgid "I have no idea, I'm a dog!"
msgstr "全く思いつきません、私は犬です!"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q3_sug3
msgid "I've been noticing gravity since I was very young!"
msgstr "私は幼い頃から重力に気づいていました！"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__id
#: model:ir.model.fields,field_description:survey.field_survey_question__id
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__id
#: model:ir.model.fields,field_description:survey.field_survey_survey__id
#: model:ir.model.fields,field_description:survey.field_survey_user_input__id
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__id
msgid "ID"
msgstr "ID"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_exception_icon
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_exception_icon
msgid "Icon"
msgstr "アイコン"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__activity_exception_icon
#: model:ir.model.fields,help:survey.field_survey_user_input__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "例外活動を示すアイコン"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__access_token
msgid "Identification token"
msgstr "識別トークン"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__progression_mode
msgid ""
"If Number is selected, it will display the number of questions answered on "
"the total number of question to answer."
msgstr "番号が選択されている場合、それは答えへの質問の総数に答えた質問の数が表示されます。"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_3
msgid ""
"If a customer purchases a 1 year warranty on 6 January 2020, when do we "
"expect the warranty to expire?"
msgstr "顧客が2020年1月6日に1年間の保証を購入した場合、保証が期限切れになるのはいつですか？"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_2
msgid ""
"If a customer purchases a product on 6 January 2020, what is the latest day "
"we expect to ship it?"
msgstr "顧客が2020年1月6日で製品を購入した場合、最遅でいつ発送になりますか？"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__message_needaction
#: model:ir.model.fields,help:survey.field_survey_user_input__message_needaction
msgid "If checked, new messages require your attention."
msgstr "チェックした場合は、新しいメッセージに注意が必要です。"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__message_has_error
#: model:ir.model.fields,help:survey.field_survey_survey__message_has_sms_error
#: model:ir.model.fields,help:survey.field_survey_user_input__message_has_error
#: model:ir.model.fields,help:survey.field_survey_user_input__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "チェックした場合は、一部のメッセージに配信エラーが発生されました。"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__save_as_email
msgid ""
"If checked, this option will save the user's answer as its email address."
msgstr "このオプションをチェックした場合、ユーザーの答えが電子メールアドレスとして保存されます。"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__save_as_nickname
msgid "If checked, this option will save the user's answer as its nickname."
msgstr "このオプションをチェックした場合、ユーザーの答えがニックネームとして保存されます。"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__users_can_go_back
msgid "If checked, users can go back to previous pages."
msgstr "チェックした場合、ユーザーは、前のページに戻ることができます。"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__survey_users_login_required
#: model:ir.model.fields,help:survey.field_survey_survey__users_login_required
msgid ""
"If checked, users have to login before answering even with a valid token."
msgstr "チェックした場合、ユーザーが有効なトークンでも、答える前に、ログインする必要があります。"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_container
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "If other, please specify:"
msgstr "その他の場合は、指定してください:"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__questions_selection
msgid ""
"If randomized is selected, add the number of random questions next to the "
"section."
msgstr "ランダム化が選択されたされた場合は、次のセクションにランダムな質問の数を追加します。"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__questions_selection
msgid ""
"If randomized is selected, you can configure the number of random questions "
"by section. This mode is ignored in live session."
msgstr "無作為化が選択された場合は、セクションごとにランダムな質問の数を設定することができます。このモードは、ライブセッションでは無視されます。"

#. module: survey
#: model:survey.question,question_placeholder:survey.vendor_certification_page_1_question_5
msgid "If yes, explain what you think is missing, give examples."
msgstr "もしそうなら、何が欠けていると思うか、例を挙げて説明して下さい。"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__value_image
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
msgid "Image"
msgstr "画像"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__value_image_filename
msgid "Image Filename"
msgstr "画像ファイル名"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/xml/survey_image_zoomer_templates.xml:0
msgid "Image Zoom Dialog"
msgstr "画像の拡大ダイアログ"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q2_sug1
msgid "Imhotep"
msgstr "イムホテプ"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug6
msgid "Impractical"
msgstr "非実際的"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__session_state__in_progress
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input__state__in_progress
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "In Progress"
msgstr "進行中"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q5
msgid "In the list below, select all the coniferous."
msgstr "下記のリストで、針葉樹をすべて選択してください。"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q2
msgid "In which country did the bonsai technique develop?"
msgstr "盆栽の技術はどの国で発展しましたか？"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__is_scored_question
msgid ""
"Include this question as part of quiz scoring. Requires an answer and answer"
" score to be taken into account."
msgstr "クイズのスコアリングの一環として、この質問を含めます。考慮に入れるべき解答と解答スコアが必要です。"

#. module: survey
#. odoo-javascript
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
#: code:addons/survey/static/src/js/survey_result.js:0
msgid "Incorrect"
msgstr "不正解"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug7
msgid "Ineffective"
msgstr "効果がありません"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_email
msgid "Input must be an email"
msgstr "入力は、電子メールでなければなりません"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/views/widgets/integer_update_flag_field/integer_update_flag_fields.js:0
msgid "Integer updating comparison flag"
msgstr "整数更新比較フラグ"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__invite_token
msgid "Invite token"
msgstr "トークン招待"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__access_mode__token
msgid "Invited people only"
msgstr "招待された人のみ"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__is_mail_template_editor
msgid "Is Editor"
msgstr "編集者であり"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_is_follower
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_is_follower
msgid "Is Follower"
msgstr "フォロー中　"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Is a Certification"
msgstr "認定"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__is_page
msgid "Is a page?"
msgstr "ページのですか？"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__is_session_answer
msgid "Is in a Session"
msgstr "セッションであります"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__is_placed_before_trigger
msgid "Is misplaced?"
msgstr "見当違いですか？"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Is not a Certification"
msgstr "認定以外"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_user_input__is_session_answer
msgid "Is that user input part of a survey session or not."
msgstr "そのユーザ入力アンケートセッションの一部またはではありません。"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q3
msgid "Is the wood of a coniferous hard or soft?"
msgstr "針葉樹の木は硬いのか柔らかいのでしょうか？"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__is_placed_before_trigger
msgid "Is this question placed before any of its trigger questions?"
msgstr "この質問は、トリガとなる質問の前に置かれていますか？"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q2_sug4
msgid "Istanbul"
msgstr "イスタンブール"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_food_preferences_q1_sug3
msgid "It depends"
msgstr "以下によります:"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "It does not mean anything specific"
msgstr "具体的な意味はありません"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "It helps attendees focus on what you are saying"
msgstr "参加者があなたが話すことに集中する助けになります。"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "It helps attendees remember the content of your presentation"
msgstr "参加者があなたのプレゼンテーションの内容を記憶する助けになります。"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "It is a small bit of text, displayed to help participants answer"
msgstr "参加者の回答を助けるために表示される、短いテキストです。"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "It is an option that can be different for each Survey"
msgstr "アンケートごとに異なるオプションです。"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_row2
msgid "It is easy to find the product that I want"
msgstr "私が欲しい製品を見つけることは容易です"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "It is more engaging for your audience"
msgstr "視聴者をより惹きつけます。"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "It's a Belgian word for \"Management\""
msgstr "ベルギー語で \"経営 \"を意味する単語です。"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p5_q1_sug3
msgid "Iznogoud"
msgstr "Iznogoud"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q3_sug1
msgid ""
"I’ve never really wanted to go to Japan. Simply because I don’t like eating "
"fish. And I know that’s very popular out there in Africa."
msgstr ""
"私は本当に日本に行きたいと思ったことがありません。単に私が魚を食べるようにしていないため。そして、私はそれがアフリカでは人気であることを知っています。"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q2_sug1
msgid "Japan"
msgstr "日本"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_session_code
msgid "Join Session"
msgstr "セッションに参加"

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p1_q4
msgid "Just to categorize your answers, don't worry."
msgstr "回答を分類するためです、ご心配なく"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q1_sug2
msgid "Kim Jong-hyun"
msgstr "キム・ジョン・ジヒョン"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q1_sug1
msgid "Kurt Cobain"
msgstr "カート・コバーン"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Label"
msgstr "ラベル"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__sequence
msgid "Label Sequence order"
msgstr "ラベル列の順序"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__matrix_row_ids
msgid "Labels used for proposed choices: rows of matrix"
msgstr "提案された選択肢のために使用されるラベル:マトリクスの行"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__suggested_answer_ids
msgid ""
"Labels used for proposed choices: simple choice, multiple choice and columns"
" of matrix"
msgstr "提案された選択肢に使用されるラベル: 単純選択、複数選択、行列のカラム"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__lang
msgid "Language"
msgstr "言語"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_2_choice_4
msgid "Large Desk"
msgstr "大型デスク"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_question__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_survey__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__write_uid
msgid "Last Updated by"
msgstr "最終更新者"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__write_date
#: model:ir.model.fields,field_description:survey.field_survey_question__write_date
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__write_date
#: model:ir.model.fields,field_description:survey.field_survey_survey__write_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input__write_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__write_date
msgid "Last Updated on"
msgstr "最終更新日"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__last_displayed_page_id
msgid "Last displayed question/page"
msgstr "最後の質問/ページを表示しました"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Late Activities"
msgstr "遅れた活動"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_session_code
msgid "Launch Session"
msgstr "セッションをローンチ"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "Leaderboard"
msgstr "リーダーボード"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_access_error
msgid "Leave"
msgstr "解除"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_3_choice_4
msgid "Legs"
msgstr "脚"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q3_sug2
msgid "Lemon Trees"
msgstr "レモンの木"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Let's get started!"
msgstr "始めましょう！"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Let's give it a spin!"
msgstr "試してみましょう！"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Let's have a look at your answers!"
msgstr "あなたの回答を見てみましょう！"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Let's open the survey you just submitted."
msgstr "今提出したアンケートを開きましょう。"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Likely"
msgstr "おそらく"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Limit Attempts"
msgstr "試行回数制限"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__is_attempts_limited
#: model:ir.model.fields,field_description:survey.field_survey_user_input__is_attempts_limited
msgid "Limited number of attempts"
msgstr "試みの限られた数"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Live Session"
msgstr "ライブセッション"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__session_available
msgid "Live Session available"
msgstr "ライブセッション利用可能"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Live Sessions"
msgstr "ライブセッション"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__survey_type__live_session
msgid "Live session"
msgstr "ライブセッション"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_available
msgid "Live session available"
msgstr "ライブセッション利用可能"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Load a <b>sample Survey</b> to get started quickly."
msgstr " <b>アンケートサンプル</b>をロードしてすぐに始める "

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_auth_required
msgid "Login required"
msgstr "ログインが必要"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__template_id
msgid "Mail Template"
msgstr "メールテンプレート"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__constr_mandatory
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Mandatory Answer"
msgstr "必須回答"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__matrix
msgid "Matrix"
msgstr "マトリクス"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__matrix_row_ids
msgid "Matrix Rows"
msgstr "マトリクスの行"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__matrix_subtype
msgid "Matrix Type"
msgstr "マトリクスタイプ"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_validation_date
msgid "Max date cannot be smaller than min date!"
msgstr "マックス日は分日付より小さくすることはできません!"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_validation_datetime
msgid "Max datetime cannot be smaller than min datetime!"
msgstr "最大日時は分日時より小さくすることはできません!"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_validation_length
msgid "Max length cannot be smaller than min length!"
msgstr "最大の長さは最小長より小さくすることはできません!"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_validation_float
msgid "Max value cannot be smaller than min value!"
msgstr "最大値は最小値より小さくすることはできません!"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Maximum"
msgstr "最大"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_max_date
msgid "Maximum Date"
msgstr "最大日"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_max_datetime
msgid "Maximum Datetime"
msgstr "最大日時"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_length_max
msgid "Maximum Text Length"
msgstr "最大文字数"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__scoring_max_obtainable
msgid "Maximum obtainable score"
msgstr "最大獲得可能点数"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_max_float_value
msgid "Maximum value"
msgstr "最大値"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_403_page
msgid "Maybe you were looking for"
msgstr "たぶん探していました"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_has_error
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_has_error
msgid "Message Delivery error"
msgstr "メッセージ配信エラー"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_ids
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_ids
msgid "Messages"
msgstr "メッセージ"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Min/Max Limits"
msgstr "最小/最大限度"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Minimum"
msgstr "最低"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_min_date
msgid "Minimum Date"
msgstr "最小日"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_min_datetime
msgid "Minimum Datetime"
msgstr "最小日時"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_length_min
msgid "Minimum Text Length"
msgstr "最小文字数"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_min_float_value
msgid "Minimum value"
msgstr "最低数"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__modern_blue
msgid "Modern Blue"
msgstr "モダンブルー"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__modern_gold
msgid "Modern Gold"
msgstr "モダンゴールド"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__modern_purple
msgid "Modern Purple"
msgstr "モダンパープル"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q2_sug3
msgid "Mooses"
msgstr "モーセ"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q3_sug4
msgid "Mount Elbrus (Russia)"
msgstr "エルブルス山(ロシア)"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q3_sug3
msgid "Mount Etna (Italy - Sicily)"
msgstr "エトナ山(イタリア - シチリア島)"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q3_sug1
msgid "Mount Teide (Spain - Tenerife)"
msgstr "テイデ山(スペイン - テネリフェ島)"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q5_sug4
msgid "Mountain Pine"
msgstr "マウンテンパイン"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__text_box
msgid "Multiple Lines Text Box"
msgstr "複数行のテキストボックス"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Multiple choice with multiple answers"
msgstr "複数回答で複数の選択肢"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Multiple choice with one answer"
msgstr "1つの答えを持つ複数の選択肢"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__multiple_choice
msgid "Multiple choice: multiple answers allowed"
msgstr "複数の選択肢（複数回答）"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__simple_choice
msgid "Multiple choice: only one answer"
msgstr "複数の選択肢（回答は１つのみ）"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__matrix_subtype__multiple
msgid "Multiple choices per row"
msgstr "行ごとに複数の選択肢"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__my_activity_date_deadline
#: model:ir.model.fields,field_description:survey.field_survey_user_input__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "活動期限"

#. module: survey
#: model:gamification.badge,name:survey.vendor_certification_badge
msgid "MyCompany Vendor"
msgstr "MyCompanyのベンダー"

#. module: survey
#: model:survey.survey,title:survey.vendor_certification
msgid "MyCompany Vendor Certification"
msgstr "MyCompanyのベンダー認定"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Neutral"
msgstr "普通"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Never (less than once a month)"
msgstr "決してない (月に1回未満)"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input__state__new
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "New"
msgstr "新規"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q2_sug3
msgid "New York"
msgstr "ニューヨーク"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_invite__existing_mode__new
msgid "New invite"
msgstr "新しい招待"

#. module: survey
#: model:mail.message.subtype,description:survey.mt_survey_survey_user_input_completed
msgid "New participation completed."
msgstr "新規参加が完了しました。"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_session_manage.js:0
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "Next"
msgstr "次へ"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_calendar_event_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "次の活動カレンダーイベント"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_date_deadline
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "次の活動期限"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_summary
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_summary
msgid "Next Activity Summary"
msgstr "次の活動概要"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_type_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_type_id
msgid "Next Activity Type"
msgstr "次の活動タイプ"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "Next Skipped"
msgstr "次のスキップされた質問"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__nickname
msgid "Nickname"
msgstr "ニックネーム"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_food_preferences_q1_sug2
#: model:survey.question.answer,value:survey.survey_demo_food_preferences_q2_sug2
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q6_sug2
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_1_choice_1
msgid "No"
msgstr "いいえ"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_question_form
msgid "No Questions yet!"
msgstr "まだ質問がありません"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "No Survey Found"
msgstr "アンケートが見つかりません"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_user_input
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid "No answers yet!"
msgstr "まだ回答がありません"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid "No attempts left."
msgstr "試行が残っていません。"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_void_content
msgid "No question yet, come back later."
msgstr "まだ質問がありません。後で戻って来て下さい。"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__scoring_type__no_scoring
msgid "No scoring"
msgstr "スコアなし"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.survey_question_answer_action
msgid "No survey labels found"
msgstr "アンケートのラベルが見つかりませんでした"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.survey_user_input_line_action
msgid "No user input lines found"
msgstr "ユーザ入力行が見つかりません"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q3_sug2
msgid "No, it's too small for the human eye."
msgstr "いいえ、それは人間の目には小さすぎます。"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q5_sug2
msgid "Norway Spruce"
msgstr "ノルウェースプルース"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p5_q1_sug2
msgid "Not Good, Not Bad"
msgstr "よくはありませんが、悪くもありません"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Not likely at all"
msgstr "非常に低い確率"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Now that you are done, submit your form."
msgstr "完了しました。フォームを提出して下さい。"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Now, use this shortcut to go back to the survey."
msgstr "このショートカットを使ってアンケートに戻りましょう。"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__progression_mode__number
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__numerical_box
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__scale
msgid "Number"
msgstr "番号"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_needaction_counter
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_needaction_counter
msgid "Number of Actions"
msgstr "アクション数"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__attempts_limit
#: model:ir.model.fields,field_description:survey.field_survey_user_input__attempts_limit
msgid "Number of attempts"
msgstr "試行回数"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_3_choice_5
msgid "Number of drawers"
msgstr "引き出しの数"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_has_error_counter
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_has_error_counter
msgid "Number of errors"
msgstr "エラー数"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__message_needaction_counter
#: model:ir.model.fields,help:survey.field_survey_user_input__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "アクションを必要とするメッセージの数"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__message_has_error_counter
#: model:ir.model.fields,help:survey.field_survey_user_input__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "配信エラーが発生されたメッセージ数"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__numerical_box
msgid "Numerical Value"
msgstr "数値"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_numerical_box
msgid "Numerical answer"
msgstr "数値の回答"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Object-Directed Open Organization"
msgstr "目標指向のオープンな組織"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date_or_datetime
msgid "Occurrence"
msgstr "発生"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid "Odoo"
msgstr "Odoo"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Odoo Certification"
msgstr "Odoo検定"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_2_choice_5
msgid "Office Chair Black"
msgstr "オフィスチェアブラック"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Often (1-3 times per week)"
msgstr "頻繁 (週に1-3回)"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid ""
"On Survey questions, one can define \"placeholders\". But what are they for?"
msgstr "アンケートの質問では、\"プレースホルダー\"を定義することができますが、何のためにあるのでしょうか？"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p1_q3_sug1
msgid "Once a day"
msgstr "一日一回"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q5_sug1
#: model:survey.question.answer,value:survey.survey_feedback_p1_q3_sug3
msgid "Once a month"
msgstr "毎月1回"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q5_sug2
#: model:survey.question.answer,value:survey.survey_feedback_p1_q3_sug2
msgid "Once a week"
msgstr "週に一度"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p1_q3_sug4
msgid "Once a year"
msgstr "一年に一度"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__matrix_subtype__simple
msgid "One choice per row"
msgstr "行ごとに一つの選択肢"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "One needs to answer at least half the questions correctly"
msgstr "少なくとも半分の問題に正解する必要があります"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "One needs to get 50% of the total score"
msgstr "合計得点の50％を獲得する必要があります"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__questions_layout__page_per_question
msgid "One page per question"
msgstr "質問ごとに一つのページ"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__questions_layout__page_per_section
msgid "One page per section"
msgstr "セクションごとに一つのページ"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__questions_layout__one_page
msgid "One page with all the questions"
msgstr "一つのページにすべての質問"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Only a single question left!"
msgstr "残りあと1つの質問だけです！"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
#: model_terms:ir.ui.view,arch_db:survey.question_result_matrix
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date_or_datetime
#: model_terms:ir.ui.view,arch_db:survey.question_result_text
msgid "Only show survey results having selected this answer"
msgstr "この回答を選択したアンケート結果のみを表示します"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid "Only survey users can manage sessions."
msgstr "唯一のアンケートユーザーがセッションを管理することができます。"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_session_code
msgid "Oops! No survey matches this code."
msgstr "おっと！このコードに一致するアンケートがありません。"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_access_error
msgid ""
"Oopsie! We could not let you open this survey. Make sure you are using the correct link and are allowed to\n"
"                        participate or get in touch with its organizer."
msgstr ""
"おっと！このアンケートを開くことができませんでした。正しいリンクを使用し、参加が許可されている\n"
"ことを確認するか、または主催者に連絡して下さい。"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Open Session Manager"
msgstr "セッションマネージャを開く"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/question_page/description_page_field.xml:0
msgid "Open section"
msgstr "セクションを開く"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Optional previous answers required"
msgstr "任意で過去の回答が必要です"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"メールを送信時に、オプションで選択可能な翻訳言語 "
"(ISOコード)。セットしてない場合は、英語版が使われます。これは通常、適切な言語を提供するプレースホルダ式であります、例: {{ "
"object.partner_id.lang }}"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Options"
msgstr "オプション"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Organizational Development for Operation Officers"
msgstr "経営役員のための組織開発"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
msgid "Other (see comments)"
msgstr "その他(コメント参照)"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p2
msgid "Our Company in a few questions ..."
msgstr "私たちの会社についてのいくつかの質問..."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__mail_server_id
msgid "Outgoing mail server"
msgstr "送信メールサーバ"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_result.js:0
msgid "Overall Performance"
msgstr "総合成績"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug5
msgid "Overpriced"
msgstr "高値"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__page_id
msgid "Page"
msgstr "ページ"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__page_ids
msgid "Pages"
msgstr "ページ"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__questions_layout
msgid "Pagination"
msgstr "ページネーション"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q2_sug4
msgid "Papyrus"
msgstr "パピルス"

#. module: survey
#. odoo-javascript
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
#: code:addons/survey/static/src/js/survey_result.js:0
msgid "Partially"
msgstr "部分的に"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Participant"
msgstr "参加者"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Participants"
msgstr "対象者"

#. module: survey
#. odoo-python
#: code:addons/survey/wizard/survey_invite.py:0
msgid "Participate to %(survey_name)s"
msgstr "%(survey_name)sに参加する"

#. module: survey
#: model:mail.template,subject:survey.mail_template_user_input_invite
msgid "Participate to {{ object.survey_id.display_name }} survey"
msgstr "{{ object.survey_id.display_name }} 調査へご参加ください"

#. module: survey
#: model:mail.message.subtype,name:survey.mt_survey_survey_user_input_completed
#: model:mail.message.subtype,name:survey.mt_survey_user_input_completed
msgid "Participation completed"
msgstr "新規参加が完了しました"

#. module: survey
#: model:mail.message.subtype,description:survey.mt_survey_user_input_completed
msgid "Participation completed."
msgstr "参加が完了しました"

#. module: survey
#: model:ir.actions.act_window,name:survey.action_survey_user_input
#: model:ir.ui.menu,name:survey.menu_survey_type_form1
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Participations"
msgstr "参加状況"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Partner"
msgstr "取引先"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Passed"
msgstr "合格"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "Passed and Failed"
msgstr "合格と不合格"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "Passed only"
msgstr "合格のみ"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "Pay attention to the host screen until the next question."
msgstr "次の質問まで、ホスト画面に注意してください。"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__progression_mode__percent
msgid "Percentage left"
msgstr "残っている％"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_result.js:0
msgid "Performance by Section"
msgstr "セクションごとの成績"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q6_sug3
msgid "Perhaps"
msgstr "多分"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q1_sug2
msgid "Peter W. Higgs"
msgstr "Peter W. Higgs"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Pick a Badge..."
msgstr "バッジを選択..."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Pick a Style..."
msgstr "スタイルを選ぶ"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Pick a Template..."
msgstr "テンプレートを選択..."

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p1_q1
msgid "Pick a subject"
msgstr "対象を選んでください"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__triggering_answer_ids
msgid ""
"Picking any of these answers will trigger this question.\n"
"Leave the field empty if the question should always be displayed."
msgstr ""
"これら回答のいずれかを選択すると、この質問が表示されます。\n"
"質問が常に表示されるようにする場合は、フィールドを空のままにします。"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q1_sug1
msgid "Pinaceae"
msgstr "マツ科"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__question_placeholder
msgid "Placeholder"
msgstr "プレースホルダ"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid ""
"Please complete this very short survey to let us know how satisfied your are"
" with our products."
msgstr "当社プロダクトの満足度をお知らせ頂くために、簡単なアンケートにご協力下さい。"

#. module: survey
#. odoo-python
#: code:addons/survey/wizard/survey_invite.py:0
msgid "Please enter at least one valid recipient."
msgstr "少なくとも一つの有効な受信者を入力してください。"

#. module: survey
#: model_terms:survey.survey,description:survey.survey_demo_food_preferences
msgid "Please give us your preferences for this event's dinner!"
msgstr "このイベントのディナーのご希望をお聞かせ下さい！"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_void_content
msgid ""
"Please make sure you have at least one question in your survey. You also "
"need at least one section if you chose the \"Page per section\" layout.<br/>"
msgstr ""
"アンケートにおいて少なくとも一つの設問があることを確認してください。、'セクションごとに1ページ'のレイアウトを選択した場合は、少なくとも1つのセクションを必要とします。<br/>"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3
msgid "Policies"
msgstr "ポリシー"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q4_sug1
msgid "Pomelos"
msgstr "Pomelos"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug8
msgid "Poor quality"
msgstr "質の悪い"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Practice in front of a mirror"
msgstr "鏡の前で練習しましょう"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__predefined_question_ids
msgid "Predefined Questions"
msgstr "事前に定義された質問"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Preview"
msgstr "プレビュー"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_2
msgid "Prices"
msgstr "物価"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_header
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Print"
msgstr "印刷"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_print
msgid "Print Results"
msgstr "結果を印刷"

#. module: survey
#: model:ir.actions.server,name:survey.action_survey_print
msgid "Print Survey"
msgstr "アンケート調査を員策"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1
msgid "Products"
msgstr "プロダクト"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "Progress bar"
msgstr "進捗バー"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__question_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__question_id
#: model_terms:ir.ui.view,arch_db:survey.survey_question_answer_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Question"
msgstr "質問"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Question & Pages"
msgstr "質問 & ページ"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__matrix_question_id
msgid "Question (as matrix row)"
msgstr "(マトリクスの行として)質問"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_answer_view_form
msgid "Question Answer Form"
msgstr "質問回答フォーム"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_question_answer_count
msgid "Question Answers Count"
msgstr "質問回答カウント"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__questions_selection
#: model:ir.model.fields,field_description:survey.field_survey_survey__questions_selection
msgid "Question Selection"
msgstr "質問の選択"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__question_time_limit_reached
msgid "Question Time Limit Reached"
msgstr "質問時間制限に達し"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__question_type
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__question_type
msgid "Question Type"
msgstr "質問の種類"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
msgid "Question type should be empty for these pages: %s"
msgstr "質問タイプは以下のページ用に空欄にして下さい:%s"

#. module: survey
#: model:ir.actions.act_window,name:survey.action_survey_question_form
#: model:ir.model.fields,field_description:survey.field_survey_question__question_ids
#: model:ir.model.fields,field_description:survey.field_survey_survey__question_ids
#: model:ir.ui.menu,name:survey.menu_survey_question_form1
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Questions"
msgstr "質問"

#. module: survey
#: model:ir.ui.menu,name:survey.survey_menu_questions
msgid "Questions & Answers"
msgstr "質問＆回答"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__triggering_question_ids
msgid ""
"Questions containing the triggering answer(s) to display the current "
"question."
msgstr "現在の質問を表示するためのトリガとなる答えを含む質問"

#. module: survey
#: model:survey.survey,title:survey.survey_demo_quiz
msgid "Quiz about our Company"
msgstr "当社に関するクイズ"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__scoring_success
msgid "Quizz Passed"
msgstr "クイズに合格"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Quizz passed"
msgstr "クイズに合格"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/views/widgets/radio_selection_with_filter/radio_selection_field_with_filter.js:0
msgid "Radio for Selection With Filter"
msgstr "フィルター付き選択用ラジオ"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__questions_selection__random
msgid "Randomized per Section"
msgstr "セクションごとにランダム"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Rarely (1-3 times per month)"
msgstr "稀である (月に1-3回)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__rating_ids
#: model:ir.model.fields,field_description:survey.field_survey_user_input__rating_ids
msgid "Ratings"
msgstr "評価"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__session_state__ready
msgid "Ready"
msgstr "準備完了"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Ready to change the way you <b>gather data</b>?"
msgstr "<b>データ収集</b>の方法を変える準備はできていますか？"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "Ready to test? Pick a sample or create one from scratch..."
msgstr "テストの用意はできましたか？サンプルを選ぶか、ゼロから作成しましょう。"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__partner_ids
msgid "Recipients"
msgstr "宛先"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Red Pen"
msgstr "赤ペン"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__answer_count
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Registered"
msgstr "登録"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__render_model
msgid "Rendering Model"
msgstr "レンダリングモデル"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Reopen"
msgstr "再開"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__survey_users_login_required
#: model:ir.model.fields,field_description:survey.field_survey_survey__users_login_required
msgid "Require Login"
msgstr "ログインが必要"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__scoring_success_min
msgid "Required Score (%)"
msgstr "必要なスコア (%)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__existing_text
msgid "Resend Comment"
msgstr "再送コメント"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Resend Invitation"
msgstr "再送招待"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_invite__existing_mode__resend
msgid "Resend invite"
msgstr "招待再送"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__user_id
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Responsible"
msgstr "担当者"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_user_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_user_id
msgid "Responsible User"
msgstr "担当ユーザ"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__restrict_user_ids
msgid "Restricted to"
msgstr "以下へ制限:"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_button_retake
msgid "Retry"
msgstr "再試行"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "Review your answers"
msgstr "回答を確認"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_speed_rating
msgid "Reward quick answers"
msgstr "迅速な回答に報酬"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.gamification_badge_form_view_simplified
msgid "Rewards for challenges"
msgstr "課題・目標に対する褒章"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__matrix_row_id
msgid "Row answer"
msgstr "行の答え"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Row1"
msgstr "行1"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Row2"
msgstr "行2"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Row3"
msgstr "行3"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Rows"
msgstr "行"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_has_sms_error
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS配信エラー"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q1_sug4
msgid "Salicaceae"
msgstr "ヤナギ科"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__save_as_email
msgid "Save as user email"
msgstr "ユーザーの電子メールとして保存"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__save_as_nickname
msgid "Save as user nickname"
msgstr "ユーザーのニックネームとして保存"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__scale
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Scale"
msgstr "スケール"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__scale_max_label
msgid "Scale Maximum Label"
msgstr "スケール最大ラベル"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__scale_max
msgid "Scale Maximum Value"
msgstr "スケール最大値"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Scale Maximum Value (0 to 10)"
msgstr "スケール最大値(0から10)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__scale_mid_label
msgid "Scale Middle Label"
msgstr "スケール中間ラベル"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__scale_min_label
msgid "Scale Minimum Label"
msgstr "スケール最小ラベル"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__scale_min
msgid "Scale Minimum Value"
msgstr "スケール最小値"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Scale Minimum Value (0 to 10)"
msgstr "スケール最小値(0から10)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_scale
msgid "Scale value"
msgstr "スケール値"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p4
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p1_q1_sug3
msgid "Sciences"
msgstr "科学"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__answer_score
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__answer_score
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__answer_score
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date_or_datetime
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Score"
msgstr "スコア"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__scoring_percentage
msgid "Score (%)"
msgstr "スコア (%)"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__answer_score
msgid "Score value for a correct answer to this question."
msgstr "この質問に対する正しい答えの値をスコア。"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__is_scored_question
msgid "Scored"
msgstr "得点"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__scoring_type
#: model:ir.model.fields,field_description:survey.field_survey_user_input__scoring_type
msgid "Scoring"
msgstr "得点"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__scoring_type
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__scoring_type
msgid "Scoring Type"
msgstr "スコアリングタイプ"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__scoring_type__scoring_with_answers_after_page
msgid "Scoring with answers after each page"
msgstr "各ページの後に解答がある採点"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__scoring_type__scoring_with_answers
msgid "Scoring with answers at the end"
msgstr "最後に回答とスコア"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__scoring_type__scoring_without_answers
msgid "Scoring without answers"
msgstr "回答なし採点"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_answer_view_search
msgid "Search Label"
msgstr "検索ラベル"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
msgid "Search Question"
msgstr "質問の検索"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Search Survey User Inputs"
msgstr "アンケートユーザ入力を検索"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_line_view_search
msgid "Search User input lines"
msgstr "ユーザ入力ラインを検索"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__page_id
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Section"
msgstr "セクション"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__question_and_page_ids
msgid "Sections and Questions"
msgstr "セクションと質問"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "See results"
msgstr "結果を参照"

#. module: survey
#: model_terms:survey.survey,description_done:survey.survey_demo_food_preferences
msgid "See you soon!"
msgstr "お会いできるのを楽しみにしております。"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1_question_3
msgid "Select all the available customizations for our Customizable Desk"
msgstr "カスタマイザブルデスクで利用可能なすべてのカスタマイズを選択します"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1_question_2
msgid "Select all the existing products"
msgstr "すべての既存の製品を選択します"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_2_question_2
msgid "Select all the products that sell for $100 or more"
msgstr "$ 100以上販売しているすべての製品を選択する"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q3
msgid "Select trees that made more than 20K sales this year"
msgstr "今年以上の20Kの販売をした木を選択"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Send"
msgstr "送信"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__send_email
msgid "Send Email"
msgstr "メールを送信"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Send by Email"
msgstr "Eメールで送信"

#. module: survey
#: model:mail.template,description:survey.mail_template_certification
msgid "Sent to participant if they succeeded the certification"
msgstr "検定に合格した場合に参加者に送信されます。"

#. module: survey
#: model:mail.template,description:survey.mail_template_user_input_invite
msgid "Sent to participant when you share a survey"
msgstr "アンケート共有時に参加者に送信されます"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__sequence
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__question_sequence
msgid "Sequence"
msgstr "シーケンス"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_code
msgid "Session Code"
msgstr "セッションコード"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_link
msgid "Session Link"
msgstr "セッションリンク"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_state
msgid "Session State"
msgstr "セッション状態"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_session_code_unique
msgid "Session code should be unique"
msgstr "セッションコードは一意である必要があります"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q2_sug1
msgid "Shanghai"
msgstr "上海"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "Share"
msgstr "共有"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid "Share a Survey"
msgstr "アンケートを共有"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_table_pagination
msgid "Show All"
msgstr "すべて表示する"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__comments_allowed
msgid "Show Comments Field"
msgstr "コメントフィールドを表示"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_session_manage.js:0
msgid "Show Correct Answer(s)"
msgstr "正解を表示"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_session_manage.js:0
msgid "Show Final Leaderboard"
msgstr "最終リーダーボードを表示"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_session_manage.js:0
msgid "Show Leaderboard"
msgstr "リーダーボードを表示"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_session_manage.js:0
msgid "Show Results"
msgstr "結果を表示"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_show_leaderboard
msgid "Show Session Leaderboard"
msgstr "セッションリーダーボードを表示"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Show all records which has next action date is before today"
msgstr "次のアクションの日付が今日より前のすべてのレコードを表示"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Show them slides with a ton of text they need to read fast"
msgstr "速く読む必要のある大量のテキストをスライドで表示します。"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__char_box
msgid "Single Line Text Box"
msgstr "1行のテキストボックス"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__skipped
#: model_terms:ir.ui.view,arch_db:survey.survey_page_print
msgid "Skipped"
msgstr "無効回答"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q3_sug2
msgid "Soft"
msgstr "柔らかい"

#. module: survey
#. odoo-python
#: code:addons/survey/wizard/survey_invite.py:0
msgid "Some emails you just entered are incorrect: %s"
msgstr "あなたの入力したいくつかの電子メールが正しくありません:%s"

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p1
msgid ""
"Some general information about you. It will be used internally for "
"statistics only."
msgstr "お客様に関する一般的な情報。統計のためにのみ内部で使用されます。"

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p2
msgid "Some questions about our company. Do you really know us?"
msgstr "当社についての質問。当社を本当にご存知ですか？"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
msgid "Someone just participated in \"%(survey_title)s\"."
msgstr "以下に今参加した人がいます: \"%(survey_title)s\"."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_header
msgid "Sorry, no one answered this survey yet."
msgstr "申し訳ありませんが、誰もまだこのアンケートに答えていません。"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "Sorry, you have not been fast enough."
msgstr "申し訳ありませんが、十分に速くなかったです。"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q4_sug4
msgid "South America"
msgstr "南アメリカ"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q2_sug4
msgid "South Korea"
msgstr "韓国"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q1_sug3
msgid "Space stations"
msgstr "宇宙ステーション"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Speak softly so that they need to focus to hear you"
msgstr "相手が集中しないと聞こえないように、小さな声で話しましょう。"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Speak too fast"
msgstr "話すのが速すぎます"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_sug1
msgid "Spring"
msgstr "春"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_session_manage.js:0
#: model:survey.question,title:survey.survey_demo_burger_quiz_p1
msgid "Start"
msgstr "開始"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid "Start Certification"
msgstr "検定スタート"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "Start Live Session"
msgstr "ライブセッションを開始"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid "Start Survey"
msgstr "アンケート開始"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__start_datetime
msgid "Start date and time"
msgstr "開始日時"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__state
msgid "Status"
msgstr "状態"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__activity_state
#: model:ir.model.fields,help:survey.field_survey_user_input__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"活動に基づくステータス\n"
"遅延: 期限が既に過ぎています\n"
"今日: 活動日は今日です\n"
"予定: 将来の活動。"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_food_preferences_q4_sug1
msgid "Steak with french fries"
msgstr "ステーキとポテトフライ"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_row2
msgid "Strawberries"
msgstr "いちご"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__subject
msgid "Subject"
msgstr "件名"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Subject..."
msgstr "件名..."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "Submit"
msgstr "提出"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__success_count
msgid "Success"
msgstr "成功"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__success_ratio
msgid "Success Ratio (%)"
msgstr "合格率 (%)"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
msgid "Success rate"
msgstr "成功率"

#. module: survey
#: model:ir.actions.act_window,name:survey.survey_question_answer_action
#: model:ir.ui.menu,name:survey.menu_survey_label_form1
msgid "Suggested Values"
msgstr "推奨値"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__suggested_answer_id
msgid "Suggested answer"
msgstr "推奨された答え"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_answer_value_not_empty
msgid ""
"Suggested answer value must not be empty (a text and/or an image must be "
"provided)."
msgstr "回答候補の値が空であってはなりません (テキストおよび/または画像が必要です)。"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__value
msgid "Suggested value"
msgstr "推奨される回答"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__suggestion
msgid "Suggestion"
msgstr "提案"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_sug2
msgid "Summer"
msgstr "夏"

#. module: survey
#: model:ir.model,name:survey.model_survey_survey
#: model:ir.model.fields,field_description:survey.field_gamification_badge__survey_id
#: model:ir.model.fields,field_description:survey.field_survey_invite__survey_id
#: model:ir.model.fields,field_description:survey.field_survey_question__survey_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input__survey_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__survey_id
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__survey_type__survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_activity
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_tree
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_line_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Survey"
msgstr "調査"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_access_error
msgid "Survey Access Error"
msgstr "アンケートアクセスエラー"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_response_line_view_tree
msgid "Survey Answer Line"
msgstr "調査の回答行"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__survey_first_submitted
msgid "Survey First Submitted"
msgstr "アンケート調査が初めて提出されました"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_gamification_badge__survey_ids
msgid "Survey Ids"
msgstr "アンケートのIds"

#. module: survey
#: model:ir.model,name:survey.model_survey_invite
msgid "Survey Invitation Wizard"
msgstr "アンケート招待ウィザード"

#. module: survey
#: model:ir.model,name:survey.model_survey_question_answer
#: model_terms:ir.ui.view,arch_db:survey.survey_question_answer_view_tree
msgid "Survey Label"
msgstr "アンケートのラベル"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Survey Link"
msgstr "アンケートリンク"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
msgid "Survey Participant"
msgstr "アンケート参加者"

#. module: survey
#: model:ir.model,name:survey.model_survey_question
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
#: model_terms:ir.ui.view,arch_db:survey.survey_question_tree
msgid "Survey Question"
msgstr "調査の質問"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Survey Time Limit"
msgstr "アンケート時間制限"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__survey_time_limit_reached
msgid "Survey Time Limit Reached"
msgstr "アンケート時間制限に達しました"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__title
msgid "Survey Title"
msgstr "調査タイトル"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__survey_type
msgid "Survey Type"
msgstr "アンケートタイプ"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__survey_start_url
msgid "Survey URL"
msgstr "アンケートURL"

#. module: survey
#: model:ir.model,name:survey.model_survey_user_input
msgid "Survey User Input"
msgstr "ユーザーの入力を調査する"

#. module: survey
#: model:ir.model,name:survey.model_survey_user_input_line
msgid "Survey User Input Line"
msgstr "アンケートユーザ入力ライン"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_tree
msgid "Survey User inputs"
msgstr "アンケートユーザ入力"

#. module: survey
#: model:mail.template,name:survey.mail_template_certification
msgid "Survey: Certification Success"
msgstr "アンケート: 検定合格"

#. module: survey
#: model:mail.template,name:survey.mail_template_user_input_invite
msgid "Survey: Invite"
msgstr "アンケート：招待"

#. module: survey
#: model:ir.actions.act_window,name:survey.action_survey_form
#: model:ir.ui.menu,name:survey.menu_survey_form
#: model:ir.ui.menu,name:survey.menu_surveys
msgid "Surveys"
msgstr "アンケート調査"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q1_sug3
msgid "Takaaki Kajita"
msgstr "Takaaki Kajita"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_button_retake
msgid "Take Again"
msgstr "もう一度行う"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "Test"
msgstr "インポートテスト"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__test_entry
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Test Entry"
msgstr "テスト項目"

#. module: survey
#: model_terms:survey.question,description:survey.vendor_certification_page_3
msgid "Test your knowledge of our policies."
msgstr "当社のポリシーに関する知識をテストする"

#. module: survey
#: model_terms:survey.question,description:survey.vendor_certification_page_2
msgid "Test your knowledge of our prices."
msgstr "当社の価格に関する知識をテストする"

#. module: survey
#: model_terms:survey.question,description:survey.vendor_certification_page_1
msgid "Test your knowledge of your products!"
msgstr "プロダクトに関する知識をテストする"

#. module: survey
#: model_terms:survey.survey,description:survey.vendor_certification
msgid "Test your vendor skills!"
msgstr "仕入先のスキルをテスト！"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Tests Only"
msgstr "テストのみ"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__char_box
msgid "Text"
msgstr "テキスト"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_char_box
msgid "Text answer"
msgstr "テキスト解答"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Thank you for your participation, hope you had a blast!"
msgstr "ご参加ありがとうございました。楽しんで頂けましたか。"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Thank you very much for your feedback. We highly value your opinion!"
msgstr "ご意見ありがとうございました。お客様のご意見は大変貴重なものです。"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "Thank you!"
msgstr "回答にご協力頂き、誠に有難う御座います！"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Thank you. We will contact you soon."
msgstr "ありがとうございました。またご連絡致します。"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid ""
"The access of the following surveys is restricted. Make sure their responsible still has access to it: \n"
"%(survey_names)s\n"
msgstr ""
"以下の調査へのアクセスは制限されています。担当者がアクセスできることを確認して下さい:\n"
"%(survey_names)s\n"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
msgid "The answer must be in the right type"
msgstr "答えは右のタイプでなければなりません"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
#: model_terms:ir.ui.view,arch_db:survey.question_container
msgid "The answer you entered is not valid."
msgstr "入力した答えは有効ではありません。"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_attempts_limit_check
msgid ""
"The attempts limit needs to be a positive number if the survey has a limited"
" number of attempts."
msgstr "試行限界がアンケートが試みの数が限られている場合、正の数である必要があります。"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_badge_uniq
msgid "The badge for each survey should be unique!"
msgstr "各アンケートのためのバッジが一意である必要があります!"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_row4
msgid "The checkout process is clear and secure"
msgstr "チェックアウトプロセスは、明確かつ安全です"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_print
msgid "The correct answer was:"
msgstr "正解は:"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__session_question_id
msgid "The current question of the survey session."
msgstr "アンケートセッションの現在の問題。"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__description
msgid ""
"The description will be displayed on the home page of the survey. You can "
"use this to give the purpose and guidelines to your candidates before they "
"start it."
msgstr "説明は、アンケートのホームページに表示されます。、彼らはそれを開始する前に、候補者に目的や指針を与えるためにこれを使用することができます。"

#. module: survey
#. odoo-python
#: code:addons/survey/wizard/survey_invite.py:0
msgid "The following customers have already received an invite"
msgstr "以下のお客様は、すでに招待受けています"

#. module: survey
#. odoo-python
#: code:addons/survey/wizard/survey_invite.py:0
msgid "The following emails have already received an invite"
msgstr "次のメールアドレスはすでに招待を受けています"

#. module: survey
#. odoo-python
#: code:addons/survey/wizard/survey_invite.py:0
msgid ""
"The following recipients have no user account: %s. You should create user "
"accounts for them or allow external signup in configuration."
msgstr ""
"以下の宛先:%sは、ユーザアカウントを持っていません。それらのユーザにアカウントを作成するか、外部連携でのサインアップを可能にする必要があります。"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_row1
msgid "The new layout and design is fresh and up-to-date"
msgstr "新しいレイアウトとデザインは新鮮かつ最新です"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_403_page
msgid "The page you were looking for could not be authorized."
msgstr "あなたが探していたページを承認できませんでした。"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_scoring_success_min_check
msgid "The percentage of success has to be defined between 0 and 100."
msgstr "成功の割合は、0〜100の間で定義する必要があります。"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__is_time_limited
msgid "The question is limited in time"
msgstr "質問は、時間が限られています"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_scale
msgid ""
"The scale must be a growing non-empty range between 0 and 10 (inclusive)"
msgstr "目盛りは、0 から 10以下の、空でない範囲で指定します。"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_session_code
msgid "The session did not start yet."
msgstr "セッションはまだ開始していません。"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid "The session will begin automatically when the host starts."
msgstr "ホストの起動時にセッションが自動的に開始されます。"

#. module: survey
#. odoo-python
#: code:addons/survey/controllers/main.py:0
msgid "The survey has already started."
msgstr "アンケートは、すでに始まっています。"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__is_time_limited
msgid "The survey is limited in time"
msgstr "アンケートは、時間が限られています"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__session_question_start_time
msgid ""
"The time at which the current question has started, used to handle the timer"
" for attendees."
msgstr "現在の質問が始まった時点では、参加者のためのタイマーを処理するために使用しました。"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_time_limit_check
msgid ""
"The time limit needs to be a positive number if the survey is time limited."
msgstr "アンケートに時間制限であれば、制限時間を正の数にする必要があります。"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_row3
msgid "The tool to compare the products is useful to make a choice"
msgstr "製品を比較するためのツールは、決めるのに便利です"

#. module: survey
#. odoo-python
#: code:addons/survey/controllers/main.py:0
msgid "The user has not succeeded the certification"
msgstr "ユーザーが認証を成功していません"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form
msgid "There was an error during the validation of the survey."
msgstr "アンケートの検証中にエラーが発生しました。"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "They are a default answer, used if the participant skips the question"
msgstr "参加者が質問をスキップした場合に使用される、デフォルトの回答です。"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid ""
"They are technical parameters that guarantees the responsiveness of the page"
msgstr "これらは、ページの応答性を保証する技術的なパラメーターです。"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
msgid "This answer cannot be overwritten."
msgstr "この回答は上書きできません"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
#: code:addons/survey/tests/test_survey.py:0
msgid "This answer must be an email address"
msgstr "この回答は、電子メールアドレスでなければなりません"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_form.js:0
msgid "This answer must be an email address."
msgstr "この回答は、電子メールアドレスでなければなりません。"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__session_code
msgid ""
"This code will be used by your attendees to reach your session. Feel free to"
" customize it however you like!"
msgstr "このコードは、セッションに到達するために参加者によって使用されます。以下のような、しかし、それをカスタマイズしてお気軽に!"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_button_form_view
msgid "This is a Test Survey Entry."
msgstr "これはテスト調査エントリです。"

#. module: survey
#. odoo-javascript
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
#: code:addons/survey/static/src/js/survey_form.js:0
#: code:addons/survey/tests/test_survey.py:0
msgid "This is not a date"
msgstr "これは日付ではありません"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
#: code:addons/survey/tests/test_survey.py:0
msgid "This is not a number"
msgstr "これは数値ではありません"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__description_done
msgid "This message will be displayed when survey is completed"
msgstr "アンケートが完了したときにこのメッセージが表示されます。"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
#: model_terms:ir.ui.view,arch_db:survey.question_container
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "This question requires an answer."
msgstr "この質問に回答が必要です。"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_print
msgid "This question was skipped"
msgstr "この質問はスキップされました。"

#. module: survey
#: model_terms:survey.question,description:survey.survey_feedback_p1
msgid ""
"This section is about general information about you. Answering them helps "
"qualifying your answers."
msgstr "このセクションでは、お客様に関する一般的な情報をお伺いします。これらの質問にお答え頂くことで、お客様の回答がより明確になります。"

#. module: survey
#: model_terms:survey.question,description:survey.survey_feedback_p2
msgid "This section is about our eCommerce experience itself."
msgstr "このセクションでは、eコマース体験そのものについてご紹介します。"

#. module: survey
#: model_terms:survey.survey,description:survey.survey_demo_quiz
msgid ""
"This small quiz will test your knowledge about our Company. Be prepared!"
msgstr "この小さなクイズで当社に関するあなたの知識をテストします。ご準備下さい。"

#. module: survey
#: model_terms:survey.survey,description:survey.survey_feedback
msgid ""
"This survey allows you to give a feedback about your experience with our products.\n"
"    Filling it helps us improving your experience."
msgstr ""
"このアンケートにより、当社製品についてのフィードバックをして頂けます。\n"
"　　ご記入頂くことで、当社の改善に役立たせて頂きます。"

#. module: survey
#. odoo-python
#: code:addons/survey/wizard/survey_invite.py:0
msgid ""
"This survey does not allow external people to participate. You should create"
" user accounts or update survey access mode accordingly."
msgstr ""
"このアンケートでは、外部の人々が参加することはできません。それに応じて、ユーザーアカウントまたは更新サーベイアクセスモードを作成する必要があります。"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_closed_expired
msgid "This survey is now closed. Thank you for your interest!"
msgstr "このアンケートは終了しました。ご関心をお寄せいただきありがとうございます！"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_auth_required
msgid "This survey is open only to registered people. Please"
msgstr "このアンケートでは、唯一の登録の人々に開かれています。お願いします"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Time & Scoring"
msgstr "タイム & スコア"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Time Limit"
msgstr "時間制限"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Time Limit (seconds)"
msgstr "時間制限(秒)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__time_limit
msgid "Time limit (minutes)"
msgstr "制限時間(分)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__time_limit
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_speed_rating_time_limit
msgid "Time limit (seconds)"
msgstr "制限時間(秒)"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid "Time limit for this certification:"
msgstr "この検定の時間期限"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid "Time limit for this survey:"
msgstr "このアンケートの制限時間:"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Time limits are only available for Live Sessions."
msgstr "時間制限はライブセッションでのみ有効です。"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__title
msgid "Title"
msgstr "タイトル"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "To join:"
msgstr "参加する:"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form
msgid ""
"To take this survey, please close all other tabs on <strong class=\"text-"
"danger\"/>."
msgstr "このアンケートを取るために、他のすべてのタブを閉じてください <strong class=\"text-danger\"/>.。"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Today Activities"
msgstr "本日の活動"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q2_sug2
msgid "Tokyo"
msgstr "東京"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date_or_datetime
msgid "Top User Responses"
msgstr "トップユーザ回答"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__scoring_total
msgid "Total Score"
msgstr "総得点"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_col4
msgid "Totally agree"
msgstr "完全に同意する"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_col1
msgid "Totally disagree"
msgstr "完全同意"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4
msgid "Trees"
msgstr "木"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__triggering_answer_ids
msgid "Triggering Answers"
msgstr "トリガ回答"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__triggering_question_ids
msgid "Triggering Questions"
msgstr "トリガ質問"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/views/widgets/survey_question_trigger/survey_question_trigger.js:0
msgid ""
"Triggers based on the following questions will not work because they are positioned after this question:\n"
"\"%s\"."
msgstr ""
"以下の質問に基づくトリガは、この質問の後に配置されているため、機能しません。\n"
"\"%s\""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
msgid "Type"
msgstr "タイプ"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__activity_exception_decoration
#: model:ir.model.fields,help:survey.field_survey_user_input__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "記録上の例外活動の種類。"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__suggested_answer_ids
msgid "Types of answers"
msgstr "回答形式"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q1_sug2
msgid "Ulmaceae"
msgstr "ニレ科"

#. module: survey
#. odoo-python
#: code:addons/survey/wizard/survey_invite.py:0
msgid "Unable to post message, please configure the sender's email address."
msgstr "メッセージを投稿することができません、送信者の電子メールアドレスを設定してください。"

#. module: survey
#. odoo-javascript
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
#: code:addons/survey/static/src/js/survey_result.js:0
msgid "Unanswered"
msgstr "未回答"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
msgid "Uncategorized"
msgstr "未分類"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_3_choice_2
msgid "Underpriced"
msgstr "割安"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "Unfortunately, you have failed the test."
msgstr "残念ながら、テスト不合格です。"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug3
msgid "Unique"
msgstr "ユニーク"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Unlikely"
msgstr "可能性は低い"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Upcoming Activities"
msgstr "今後の取り組み"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Use a fun visual support, like a live presentation"
msgstr "ライブプレゼンテーションのような楽しい画像サポートを使う"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Use humor and make jokes"
msgstr "ユーモアや冗談を使う"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Use template"
msgstr "テンプレートを使用"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Use the breadcrumbs to quickly go back to the dashboard."
msgstr "素早くダッシュボードに戻るには、パンくずを使います。"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__description
msgid ""
"Use this field to add additional explanations about your question or to "
"illustrate it with pictures or a video"
msgstr "質問に関する追加の説明を追加するには、このフィールドを使用して説明を追加するか、写真やビデオを追加してください。"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__random_questions_count
msgid ""
"Used on randomized sections to take X random questions from all the "
"questions of that section."
msgstr "そのセクションのすべての質問からXランダムに質問を取得するために、ランダム化のセクションで使用されます。"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug2
msgid "Useful"
msgstr "有用"

#. module: survey
#: model:res.groups,name:survey.group_survey_user
msgid "User"
msgstr "ユーザ"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
msgid "User Choice"
msgstr "ユーザーの選択"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__user_input_id
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_line_view_search
msgid "User Input"
msgstr "ユーザ回答"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date_or_datetime
#: model_terms:ir.ui.view,arch_db:survey.question_result_text
msgid "User Responses"
msgstr "ユーザー応答"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_line_view_form
msgid "User input line details"
msgstr "ユーザ入力ラインの詳細"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__user_input_ids
msgid "User responses"
msgstr "ユーザー応答"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__users_can_go_back
msgid "Users can go back"
msgstr "ユーザーが戻れます"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__survey_users_can_signup
#: model:ir.model.fields,field_description:survey.field_survey_survey__users_can_signup
msgid "Users can signup"
msgstr "ユーザーがサインアップできます"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_required
msgid "Validate entry"
msgstr "検証項目"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/question_page/question_page_one2many_field.js:0
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_error_msg
msgid "Validation Error"
msgstr "検証エラー"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__value_label
msgid "Value Label"
msgstr "値ラベル"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q1_sug2
msgid "Vegetables"
msgstr "野菜"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_food_preferences_q3_sug2
msgid "Vegetarian burger"
msgstr "ベジタリアンバーガー"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_food_preferences_q3_sug1
msgid "Vegetarian pizza"
msgstr "ベジタリアンピザ"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_3_choice_1
msgid "Very underpriced"
msgstr "かなり割安"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q2_sug3
msgid "Vietnam"
msgstr "ベトナム"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_open
msgid "Waiting for attendees..."
msgstr "出席者を待っています..."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid ""
"We have registered your answer! Please wait for the host to go to the next "
"question."
msgstr "答えを登録しました!次の質問に行くためにホストお待ちください。"

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p4
msgid ""
"We like to say that the apple doesn't fall far from the tree, so here are "
"trees."
msgstr "りんごは木から遠くに落ちない、ということで、これらが木です。"

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p5
msgid "We may be interested by your input."
msgstr "あなたのご意見に興味があるかもしれません。"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__website_message_ids
#: model:ir.model.fields,field_description:survey.field_survey_user_input__website_message_ids
msgid "Website Messages"
msgstr "ウェブサイトメッセージ"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__website_message_ids
#: model:ir.model.fields,help:survey.field_survey_user_input__website_message_ids
msgid "Website communication history"
msgstr "ウェブサイト通信履歴"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid ""
"Welcome to this Odoo certification. You will receive 2 random questions out "
"of a pool of 3."
msgstr "Odoo検定へようこそ。3つの質問の中からランダムに2つ出題されます。"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_5
msgid ""
"What day and time do you think most customers are most likely to call "
"customer service (not rated)?"
msgstr "顧客がカスタマーサービスへコールする可能性が最も高い曜日と時刻はいつでしょうか(未評価)？"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_4
msgid ""
"What day to you think is best for us to start having an annual sale (not "
"rated)?"
msgstr "毎年恒例のセールをするために何曜日が最善であると思いますか(未評価)？"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p2_q2
msgid "What do you think about our new eCommerce?"
msgstr "新しいeコマースについてどう思いますか？"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_2_question_3
msgid "What do you think about our prices (not rated)?"
msgstr "私達の価格についてどう思いますか(未評価)？"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p5_q1
msgid "What do you think about this survey?"
msgstr "このアンケートについてどう思いますか？"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "What does \"ODOO\" stand for?"
msgstr " \"ODOO\" の意味は何ですか？"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "What does one need to get to pass an Odoo Survey?"
msgstr "Odooアンケートに合格するには何が必要ですか？"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "What is a frequent mistake public speakers do?"
msgstr "人前で話す人がよく犯す間違いとは？"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "What is the best way to catch the attention of an audience?"
msgstr "観客の注意を引く最善の方法とは？"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p2_q2
msgid "What is the biggest city in the world?"
msgstr "世界最大の都市は？"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p1_q1
msgid "What is your email?"
msgstr "あなたのEメールアドレスは？"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p1_q2
msgid "What is your nickname?"
msgstr "あなたのニックネームは？"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p4_q2
msgid "What is, approximately, the critical mass of plutonium-239?"
msgstr "プルトニウム-239の臨界質量はおよそどれくらいですか？"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p3_q1
msgid "When did Genghis Khan die?"
msgstr "チンギス・ハーンはいつ亡くなりましたか？"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p2_q2
msgid "When did precisely Marc Demo crop its first apple tree?"
msgstr "Marc Demoが初めてリンゴの木を植えたのは厳密にいつですか？"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q6
msgid "When do you harvest those fruits"
msgstr "いつそれらの果物を収穫しますか"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p2_q1
msgid "When is Mitchell Admin born?"
msgstr "Mitchell Adminはいつ生まれましたか？"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p1_q2
msgid "When is your date of birth?"
msgstr "あなたの生年月日はいつですか？"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Whenever you pick an answer, Odoo saves it for you."
msgstr "答えを選ぶと、Odooがそれを保存してくれます。"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p1_q3
msgid "Where are you from?"
msgstr "どこ出身ですか？"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p1_q1
msgid "Where do you live?"
msgstr "どこに住んでいますか？"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__session_show_leaderboard
msgid ""
"Whether or not we want to show the attendees leaderboard for this survey."
msgstr "今回のアンケートのためのリーダーボードの出席者を表示したくてもしたくなくても"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p5_q1
msgid "Which Musician is not in the 27th Club?"
msgstr "27thクラブに所属していないミュージシャンは？"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q1
msgid "Which category does a tomato belong to"
msgstr "トマトはどのカテゴリに属しますか？"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p2_q3
msgid "Which is the highest volcano in Europe?"
msgstr "ヨーロッパで最も高い火山は？"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p2_q1
msgid "Which of the following words would you use to describe our products?"
msgstr "当社のプロダクトを説明するのに、次のどの言葉を使いますか？"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q2
msgid "Which of the following would you use to pollinate"
msgstr "受粉に使用するのは次のうちどれですか？"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p5_q2
msgid "Which painting/drawing was not made by Pablo Picasso?"
msgstr "パブロ・ピカソが描いたのではない絵画/ドローイングは？"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p5_q3
msgid "Which quote is from Jean-Claude Van Damme"
msgstr "ジャン=クロード・ヴァン・ダムからの引用です"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p1
msgid "Who are you?"
msgstr "あなたは誰ですか？"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p3_q2
msgid "Who is the architect of the Great Pyramid of Giza?"
msgstr "ギザの大ピラミッドの設計者は？"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p4_q1
msgid ""
"Who received a Nobel prize in Physics for the discovery of neutrino "
"oscillations, which shows that neutrinos have mass?"
msgstr "ニュートリノに質量があることを示すニュートリノ振動の発見でノーベル物理学賞を受賞したのは誰ですか？"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid ""
"Why should you consider making your presentation more fun with a small quiz?"
msgstr "なぜ、ちょっとしたクイズでプレゼンテーションをより楽しくする必要があるのでしょう？"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_3_choice_3
msgid "Width"
msgstr "幅"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q1_sug4
msgid "Willard S. Boyle"
msgstr "ウィラードS.ボイル"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_sug4
msgid "Winter"
msgstr "冬"

#. module: survey
#: model:survey.question,title:survey.survey_demo_food_preferences_q2
msgid "Would you prefer a veggie meal if possible?"
msgstr "可能であれば菜食をお好みですか？"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"YYYY-MM-DD\n"
"                                        <i class=\"fa fa-calendar\" role=\"img\" aria-label=\"Calendar\" title=\"Calendar\"/>"
msgstr ""
"YYYY-MM-DD\n"
"                                        <i class=\"fa fa-calendar\" role=\"img\" aria-label=\"Calendar\" title=\"Calendar\"/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"YYYY-MM-DD hh:mm:ss\n"
"                                        <i class=\"fa fa-calendar\" role=\"img\" aria-label=\"Calendar\" title=\"Calendar\"/>"
msgstr ""
"YYYY-MM-DD hh:mm:ss\n"
"                                        <i class=\"fa fa-calendar\" role=\"img\" aria-label=\"Calendar\" title=\"Calendar\"/>"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Yellow Pen"
msgstr "黄色ペン"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_food_preferences_q1_sug1
#: model:survey.question.answer,value:survey.survey_demo_food_preferences_q2_sug1
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q6_sug1
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_1_choice_2
msgid "Yes"
msgstr "はい"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q3_sug1
msgid "Yes, that's the only thing a human eye can see."
msgstr "はい、それのみが、人間の目が見ることができるものです。"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_certification_check
msgid ""
"You can only create certifications for surveys that have a scoring "
"mechanism."
msgstr "スコアリングメカニズムを持っているアンケートのためのみ証明書を作成できます。"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_user_input
msgid ""
"You can share your links through different means: email, invite shortcut, "
"live presentation, ..."
msgstr "Eメール、招待状ショートカット、ライブプレゼンテーションなど、さまざまな方法でリンクを共有できます。"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
msgid ""
"You cannot delete questions from surveys \"%(survey_names)s\" while live "
"sessions are in progress."
msgstr "ライブセッション開催中に、アンケート \"%(survey_names)s\" を削除することはできません。"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid ""
"You cannot send an invitation for a \"One page per section\" survey if the "
"survey has no sections."
msgstr "アンケートにセクションがない場合、 \"セクションごとに1ページ\"アンケート用の招待状を送ることはできません。"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid ""
"You cannot send an invitation for a \"One page per section\" survey if the "
"survey only contains empty sections."
msgstr "アンケートに空のセクションしかない場合、 \"セクションごとに1ページ\"アンケート用の招待状を送ることはできません。"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid "You cannot send an invitation for a survey that has no questions."
msgstr "何の設問もないアンケートのための招待状を送信することはできません。"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid "You cannot send invitations for closed surveys."
msgstr "終了したアンケートのための招待状を送信することはできません。"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "You received the badge"
msgstr "バッジを受け取りました"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "You scored"
msgstr "あなたのスコア"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p5
msgid "Your feeling"
msgstr "気持ち"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid ""
"Your responses will help us improve our product range to serve you even "
"better."
msgstr "皆様からのご回答は、より良いサービスをご提供するためのプロダクトラインナップの改善に役立たせて頂きます。"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/xml/survey_image_zoomer_templates.xml:0
msgid "Zoom in"
msgstr "拡大"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/xml/survey_image_zoomer_templates.xml:0
msgid "Zoom out"
msgstr "縮小"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/xml/survey_image_zoomer_templates.xml:0
msgid "Zoomed Image"
msgstr "拡大画像"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
msgid "[Question Title]"
msgstr "[質問タイトル]"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "ans"
msgstr "年"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_progression
msgid "answered"
msgstr "答え"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "attempts"
msgstr "回試行"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/xml/survey_image_zoomer_templates.xml:0
msgid "close"
msgstr "閉じる"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid ""
"e.g.  'Rick Sanchez' <<EMAIL>>, <EMAIL>"
msgstr ""
"例:  'Rick Sanchez' <<EMAIL>>, <EMAIL>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "e.g. \"Thank you very much for your feedback!\""
msgstr "例: \"フィードバック頂きありがとうございます！\""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "e.g. \"The following Survey will help us...\""
msgstr "例:  \"以下のアンケートは、当社の…\""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "e.g. \"What is the...\""
msgstr "例: \"何が...\""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_session_code
msgid "e.g. 4812"
msgstr "例: 4812"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "e.g. Guidelines, instructions, picture, ... to help attendees answer"
msgstr "例: 参加者の回答の助けになるガイドライン、指示、写真など"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.gamification_badge_form_view_simplified
msgid "e.g. No one can solve challenges like you do"
msgstr "例: あなたのように課題を解決できる人はいません"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.gamification_badge_form_view_simplified
msgid "e.g. Problem Solver"
msgstr "例: 問題解決者"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "e.g. Satisfaction Survey"
msgstr "例 満足度アンケート"

#. module: survey
#: model:survey.question,question_placeholder:survey.survey_demo_quiz_p1_q1
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_auth_required
msgid "log in"
msgstr "ログインする"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "minutes"
msgstr "分"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_progression
msgid "of"
msgstr "の"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid "of achievement"
msgstr "成果の"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_form.js:0
msgid "or press CTRL+Enter"
msgstr "または、Ctrlキーとエンターキーを押してください"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_form.js:0
msgid "or press Enter"
msgstr "または、エンターキーを押してください"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_form.js:0
msgid "or press ⌘+Enter"
msgstr "または ⌘+Enterを押して下さい。"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_progression
msgid "pages"
msgstr "ページ"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_closed_expired
msgid "survey expired"
msgstr "有効期限切れアンケート"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_void_content
msgid "survey is empty"
msgstr "アンケートは空欄です。"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_403_page
msgid "this page"
msgstr "このページ"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "to"
msgstr "to"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"⚠️ This question is positioned before some or all of its triggers and could "
"be skipped."
msgstr "⚠️この質問は、いくつかの、あるいはすべてのトリガの前に置かれ、スキップすることもできます。"
