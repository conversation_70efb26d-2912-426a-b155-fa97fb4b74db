<?xml version='1.0' encoding='UTF-8'?>
<Invoice xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2" xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2" xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2">
    <cbc:CustomizationID>urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0</cbc:CustomizationID>
    <cbc:ProfileID>urn:fdc:peppol.eu:2017:poacc:billing:01:1.0</cbc:ProfileID>
    <cbc:ID>___ignore___</cbc:ID>
    <cbc:IssueDate>2017-01-01</cbc:IssueDate>
    <cbc:DueDate>2017-01-31</cbc:DueDate>
    <cbc:InvoiceTypeCode>380</cbc:InvoiceTypeCode>
    <cbc:Note>test narration</cbc:Note>
    <cbc:DocumentCurrencyCode>EUR</cbc:DocumentCurrencyCode>
    <cbc:BuyerReference>ref_partner_2</cbc:BuyerReference>
    <cac:OrderReference>
        <cbc:ID>ref_move</cbc:ID>
    </cac:OrderReference>
    <cac:AdditionalDocumentReference>
        <cbc:ID>___ignore___</cbc:ID>
        <cac:Attachment>
            <cbc:EmbeddedDocumentBinaryObject mimeCode="___ignore___" filename="___ignore___">
                ___ignore___
            </cbc:EmbeddedDocumentBinaryObject>
        </cac:Attachment>
    </cac:AdditionalDocumentReference>
    <cac:AccountingSupplierParty>
        <cac:Party>
            <cbc:EndpointID schemeID="0208">**********</cbc:EndpointID>
            <cac:PartyName>
                <cbc:Name>partner_1</cbc:Name>
            </cac:PartyName>
            <cac:PostalAddress>
                <cbc:StreetName>Chaussée de Namur 40</cbc:StreetName>
                <cbc:CityName>Ramillies</cbc:CityName>
                <cbc:PostalZone>1367</cbc:PostalZone>
                <cac:Country>
                    <cbc:IdentificationCode>BE</cbc:IdentificationCode>
                </cac:Country>
            </cac:PostalAddress>
            <cac:PartyTaxScheme>
                <cbc:CompanyID>BE**********</cbc:CompanyID>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:PartyTaxScheme>
            <cac:PartyLegalEntity>
                <cbc:RegistrationName>partner_1</cbc:RegistrationName>
                <cbc:CompanyID>BE**********</cbc:CompanyID>
            </cac:PartyLegalEntity>
            <cac:Contact>
                <cbc:Name>partner_1</cbc:Name>
            </cac:Contact>
        </cac:Party>
    </cac:AccountingSupplierParty>
    <cac:AccountingCustomerParty>
        <cac:Party>
            <cbc:EndpointID schemeID="0208">**********</cbc:EndpointID>
            <cac:PartyName>
                <cbc:Name>partner_2</cbc:Name>
            </cac:PartyName>
            <cac:PostalAddress>
                <cbc:StreetName>Rue des Bourlottes 9</cbc:StreetName>
                <cbc:CityName>Ramillies</cbc:CityName>
                <cbc:PostalZone>1367</cbc:PostalZone>
                <cac:Country>
                    <cbc:IdentificationCode>BE</cbc:IdentificationCode>
                </cac:Country>
            </cac:PostalAddress>
            <cac:PartyTaxScheme>
                <cbc:CompanyID>BE**********</cbc:CompanyID>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:PartyTaxScheme>
            <cac:PartyLegalEntity>
                <cbc:RegistrationName>partner_2</cbc:RegistrationName>
                <cbc:CompanyID>BE**********</cbc:CompanyID>
            </cac:PartyLegalEntity>
            <cac:Contact>
                <cbc:Name>partner_2</cbc:Name>
            </cac:Contact>
        </cac:Party>
    </cac:AccountingCustomerParty>
    <cac:Delivery>
        <cac:DeliveryLocation>
            <cac:Address>
                <cbc:StreetName>Rue des Bourlottes 9</cbc:StreetName>
                <cbc:CityName>Ramillies</cbc:CityName>
                <cbc:PostalZone>1367</cbc:PostalZone>
                <cac:Country>
                    <cbc:IdentificationCode>BE</cbc:IdentificationCode>
                </cac:Country>
            </cac:Address>
        </cac:DeliveryLocation>
    </cac:Delivery>
    <cac:PaymentMeans>
        <cbc:PaymentMeansCode name="credit transfer">30</cbc:PaymentMeansCode>
        <cbc:PaymentID>___ignore___</cbc:PaymentID>
        <cac:PayeeFinancialAccount>
            <cbc:ID>****************</cbc:ID>
        </cac:PayeeFinancialAccount>
    </cac:PaymentMeans>
    <cac:PaymentTerms>
        <cbc:Note>Payment terms: 30 Days, 2% Early Payment Discount under 7 days</cbc:Note>
    </cac:PaymentTerms>
    <cac:AllowanceCharge>
        <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
        <cbc:AllowanceChargeReasonCode>66</cbc:AllowanceChargeReasonCode>
        <cbc:AllowanceChargeReason>Conditional cash/payment discount</cbc:AllowanceChargeReason>
        <cbc:Amount currencyID="EUR">355.48</cbc:Amount>
        <cac:TaxCategory>
            <cbc:ID>S</cbc:ID>
            <cbc:Percent>21.0</cbc:Percent>
            <cac:TaxScheme>
                <cbc:ID>VAT</cbc:ID>
            </cac:TaxScheme>
        </cac:TaxCategory>
    </cac:AllowanceCharge>
    <cac:AllowanceCharge>
        <cbc:ChargeIndicator>true</cbc:ChargeIndicator>
        <cbc:AllowanceChargeReasonCode>ZZZ</cbc:AllowanceChargeReasonCode>
        <cbc:AllowanceChargeReason>Conditional cash/payment discount</cbc:AllowanceChargeReason>
        <cbc:Amount currencyID="EUR">355.48</cbc:Amount>
        <cac:TaxCategory>
            <cbc:ID>E</cbc:ID>
            <cbc:Percent>0.0</cbc:Percent>
            <cac:TaxScheme>
                <cbc:ID>VAT</cbc:ID>
            </cac:TaxScheme>
        </cac:TaxCategory>
    </cac:AllowanceCharge>
    <cac:TaxTotal>
        <cbc:TaxAmount currencyID="EUR">3657.91</cbc:TaxAmount>
        <cac:TaxSubtotal>
            <cbc:TaxableAmount currencyID="EUR">17418.59</cbc:TaxableAmount>
            <cbc:TaxAmount currencyID="EUR">3657.91</cbc:TaxAmount>
            <cac:TaxCategory>
                <cbc:ID>S</cbc:ID>
                <cbc:Percent>21.0</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:TaxCategory>
        </cac:TaxSubtotal>
        <cac:TaxSubtotal>
            <cbc:TaxableAmount currencyID="EUR">355.48</cbc:TaxableAmount>
            <cbc:TaxAmount currencyID="EUR">0.00</cbc:TaxAmount>
            <cac:TaxCategory>
                <cbc:ID>E</cbc:ID>
                <cbc:Percent>0.0</cbc:Percent>
                <cbc:TaxExemptionReason>Exempt from tax</cbc:TaxExemptionReason>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:TaxCategory>
        </cac:TaxSubtotal>
    </cac:TaxTotal>
    <cac:LegalMonetaryTotal>
        <cbc:LineExtensionAmount currencyID="EUR">17774.07</cbc:LineExtensionAmount>
        <cbc:TaxExclusiveAmount currencyID="EUR">17774.07</cbc:TaxExclusiveAmount>
        <cbc:TaxInclusiveAmount currencyID="EUR">21431.98</cbc:TaxInclusiveAmount>
        <cbc:AllowanceTotalAmount currencyID="EUR">355.48</cbc:AllowanceTotalAmount>
        <cbc:ChargeTotalAmount currencyID="EUR">355.48</cbc:ChargeTotalAmount>
        <cbc:PrepaidAmount currencyID="EUR">0.00</cbc:PrepaidAmount>
        <cbc:PayableAmount currencyID="EUR">21431.98</cbc:PayableAmount>
    </cac:LegalMonetaryTotal>
    <cac:InvoiceLine>
        <cbc:ID>___ignore___</cbc:ID>
        <cbc:InvoicedQuantity unitCode="C62">20.0</cbc:InvoicedQuantity>
        <cbc:LineExtensionAmount currencyID="EUR">2132.85</cbc:LineExtensionAmount>
        <cac:AllowanceCharge>
            <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
            <cbc:AllowanceChargeReasonCode>95</cbc:AllowanceChargeReasonCode>
            <cbc:Amount currencyID="EUR">1482.15</cbc:Amount>
        </cac:AllowanceCharge>
        <cac:Item>
            <cbc:Description>product_a</cbc:Description>
            <cbc:Name>product_a</cbc:Name>
            <cac:ClassifiedTaxCategory>
                <cbc:ID>S</cbc:ID>
                <cbc:Percent>21.0</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:ClassifiedTaxCategory>
        </cac:Item>
        <cac:Price>
            <cbc:PriceAmount currencyID="EUR">180.75</cbc:PriceAmount>
        </cac:Price>
    </cac:InvoiceLine>
    <cac:InvoiceLine>
        <cbc:ID>___ignore___</cbc:ID>
        <cbc:InvoicedQuantity unitCode="C62">480.0</cbc:InvoicedQuantity>
        <cbc:LineExtensionAmount currencyID="EUR">7306.56</cbc:LineExtensionAmount>
        <cac:AllowanceCharge>
            <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
            <cbc:AllowanceChargeReasonCode>95</cbc:AllowanceChargeReasonCode>
            <cbc:Amount currencyID="EUR">5077.44</cbc:Amount>
        </cac:AllowanceCharge>
        <cac:Item>
            <cbc:Description>product_a</cbc:Description>
            <cbc:Name>product_a</cbc:Name>
            <cac:ClassifiedTaxCategory>
                <cbc:ID>S</cbc:ID>
                <cbc:Percent>21.0</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:ClassifiedTaxCategory>
        </cac:Item>
        <cac:Price>
            <cbc:PriceAmount currencyID="EUR">25.8</cbc:PriceAmount>
        </cac:Price>
    </cac:InvoiceLine>
    <cac:InvoiceLine>
        <cbc:ID>___ignore___</cbc:ID>
        <cbc:InvoicedQuantity unitCode="C62">3.0</cbc:InvoicedQuantity>
        <cbc:LineExtensionAmount currencyID="EUR">974.48</cbc:LineExtensionAmount>
        <cac:AllowanceCharge>
            <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
            <cbc:AllowanceChargeReasonCode>95</cbc:AllowanceChargeReasonCode>
            <cbc:Amount currencyID="EUR">623.03</cbc:Amount>
        </cac:AllowanceCharge>
        <cac:Item>
            <cbc:Description>product_a</cbc:Description>
            <cbc:Name>product_a</cbc:Name>
            <cac:ClassifiedTaxCategory>
                <cbc:ID>S</cbc:ID>
                <cbc:Percent>21.0</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:ClassifiedTaxCategory>
        </cac:Item>
        <cac:Price>
            <cbc:PriceAmount currencyID="EUR">532.5027322404</cbc:PriceAmount>
        </cac:Price>
    </cac:InvoiceLine>
    <cac:InvoiceLine>
        <cbc:ID>___ignore___</cbc:ID>
        <cbc:InvoicedQuantity unitCode="C62">3.0</cbc:InvoicedQuantity>
        <cbc:LineExtensionAmount currencyID="EUR">135.88</cbc:LineExtensionAmount>
        <cac:AllowanceCharge>
            <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
            <cbc:AllowanceChargeReasonCode>95</cbc:AllowanceChargeReasonCode>
            <cbc:Amount currencyID="EUR">86.87</cbc:Amount>
        </cac:AllowanceCharge>
        <cac:Item>
            <cbc:Description>product_a</cbc:Description>
            <cbc:Name>product_a</cbc:Name>
            <cac:ClassifiedTaxCategory>
                <cbc:ID>S</cbc:ID>
                <cbc:Percent>21.0</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:ClassifiedTaxCategory>
        </cac:Item>
        <cac:Price>
            <cbc:PriceAmount currencyID="EUR">74.2513661202</cbc:PriceAmount>
        </cac:Price>
    </cac:InvoiceLine>
    <cac:InvoiceLine>
        <cbc:ID>___ignore___</cbc:ID>
        <cbc:InvoicedQuantity unitCode="C62">3.0</cbc:InvoicedQuantity>
        <cbc:LineExtensionAmount currencyID="EUR">675.27</cbc:LineExtensionAmount>
        <cac:AllowanceCharge>
            <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
            <cbc:AllowanceChargeReasonCode>95</cbc:AllowanceChargeReasonCode>
            <cbc:Amount currencyID="EUR">431.73</cbc:Amount>
        </cac:AllowanceCharge>
        <cac:Item>
            <cbc:Description>product_a</cbc:Description>
            <cbc:Name>product_a</cbc:Name>
            <cac:ClassifiedTaxCategory>
                <cbc:ID>S</cbc:ID>
                <cbc:Percent>21.0</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:ClassifiedTaxCategory>
        </cac:Item>
        <cac:Price>
            <cbc:PriceAmount currencyID="EUR">369.0</cbc:PriceAmount>
        </cac:Price>
    </cac:InvoiceLine>
    <cac:InvoiceLine>
        <cbc:ID>___ignore___</cbc:ID>
        <cbc:InvoicedQuantity unitCode="C62">5.0</cbc:InvoicedQuantity>
        <cbc:LineExtensionAmount currencyID="EUR">242.48</cbc:LineExtensionAmount>
        <cac:AllowanceCharge>
            <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
            <cbc:AllowanceChargeReasonCode>95</cbc:AllowanceChargeReasonCode>
            <cbc:Amount currencyID="EUR">155.03</cbc:Amount>
        </cac:AllowanceCharge>
        <cac:Item>
            <cbc:Description>product_a</cbc:Description>
            <cbc:Name>product_a</cbc:Name>
            <cac:ClassifiedTaxCategory>
                <cbc:ID>S</cbc:ID>
                <cbc:Percent>21.0</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:ClassifiedTaxCategory>
        </cac:Item>
        <cac:Price>
            <cbc:PriceAmount currencyID="EUR">79.5016393443</cbc:PriceAmount>
        </cac:Price>
    </cac:InvoiceLine>
    <cac:InvoiceLine>
        <cbc:ID>___ignore___</cbc:ID>
        <cbc:InvoicedQuantity unitCode="C62">5.0</cbc:InvoicedQuantity>
        <cbc:LineExtensionAmount currencyID="EUR">327.88</cbc:LineExtensionAmount>
        <cac:AllowanceCharge>
            <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
            <cbc:AllowanceChargeReasonCode>95</cbc:AllowanceChargeReasonCode>
            <cbc:Amount currencyID="EUR">209.63</cbc:Amount>
        </cac:AllowanceCharge>
        <cac:Item>
            <cbc:Description>product_a</cbc:Description>
            <cbc:Name>product_a</cbc:Name>
            <cac:ClassifiedTaxCategory>
                <cbc:ID>S</cbc:ID>
                <cbc:Percent>21.0</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:ClassifiedTaxCategory>
        </cac:Item>
        <cac:Price>
            <cbc:PriceAmount currencyID="EUR">107.5016393443</cbc:PriceAmount>
        </cac:Price>
    </cac:InvoiceLine>
    <cac:InvoiceLine>
        <cbc:ID>___ignore___</cbc:ID>
        <cbc:InvoicedQuantity unitCode="C62">5.0</cbc:InvoicedQuantity>
        <cbc:LineExtensionAmount currencyID="EUR">488.00</cbc:LineExtensionAmount>
        <cac:AllowanceCharge>
            <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
            <cbc:AllowanceChargeReasonCode>95</cbc:AllowanceChargeReasonCode>
            <cbc:Amount currencyID="EUR">312.00</cbc:Amount>
        </cac:AllowanceCharge>
        <cac:Item>
            <cbc:Description>product_a</cbc:Description>
            <cbc:Name>product_a</cbc:Name>
            <cac:ClassifiedTaxCategory>
                <cbc:ID>S</cbc:ID>
                <cbc:Percent>21.0</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:ClassifiedTaxCategory>
        </cac:Item>
        <cac:Price>
            <cbc:PriceAmount currencyID="EUR">160.0</cbc:PriceAmount>
        </cac:Price>
    </cac:InvoiceLine>
    <cac:InvoiceLine>
        <cbc:ID>___ignore___</cbc:ID>
        <cbc:InvoicedQuantity unitCode="C62">5.0</cbc:InvoicedQuantity>
        <cbc:LineExtensionAmount currencyID="EUR">844.09</cbc:LineExtensionAmount>
        <cac:AllowanceCharge>
            <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
            <cbc:AllowanceChargeReasonCode>95</cbc:AllowanceChargeReasonCode>
            <cbc:Amount currencyID="EUR">539.66</cbc:Amount>
        </cac:AllowanceCharge>
        <cac:Item>
            <cbc:Description>product_a</cbc:Description>
            <cbc:Name>product_a</cbc:Name>
            <cac:ClassifiedTaxCategory>
                <cbc:ID>S</cbc:ID>
                <cbc:Percent>21.0</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:ClassifiedTaxCategory>
        </cac:Item>
        <cac:Price>
            <cbc:PriceAmount currencyID="EUR">276.7508196721</cbc:PriceAmount>
        </cac:Price>
    </cac:InvoiceLine>
    <cac:InvoiceLine>
        <cbc:ID>___ignore___</cbc:ID>
        <cbc:InvoicedQuantity unitCode="C62">60.0</cbc:InvoicedQuantity>
        <cbc:LineExtensionAmount currencyID="EUR">304.51</cbc:LineExtensionAmount>
        <cac:AllowanceCharge>
            <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
            <cbc:AllowanceChargeReasonCode>95</cbc:AllowanceChargeReasonCode>
            <cbc:Amount currencyID="EUR">194.69</cbc:Amount>
        </cac:AllowanceCharge>
        <cac:Item>
            <cbc:Description>product_a</cbc:Description>
            <cbc:Name>product_a</cbc:Name>
            <cac:ClassifiedTaxCategory>
                <cbc:ID>S</cbc:ID>
                <cbc:Percent>21.0</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:ClassifiedTaxCategory>
        </cac:Item>
        <cac:Price>
            <cbc:PriceAmount currencyID="EUR">8.3199453552</cbc:PriceAmount>
        </cac:Price>
    </cac:InvoiceLine>
    <cac:InvoiceLine>
        <cbc:ID>___ignore___</cbc:ID>
        <cbc:InvoicedQuantity unitCode="C62">60.0</cbc:InvoicedQuantity>
        <cbc:LineExtensionAmount currencyID="EUR">304.51</cbc:LineExtensionAmount>
        <cac:AllowanceCharge>
            <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
            <cbc:AllowanceChargeReasonCode>95</cbc:AllowanceChargeReasonCode>
            <cbc:Amount currencyID="EUR">194.69</cbc:Amount>
        </cac:AllowanceCharge>
        <cac:Item>
            <cbc:Description>product_a</cbc:Description>
            <cbc:Name>product_a</cbc:Name>
            <cac:ClassifiedTaxCategory>
                <cbc:ID>S</cbc:ID>
                <cbc:Percent>21.0</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:ClassifiedTaxCategory>
        </cac:Item>
        <cac:Price>
            <cbc:PriceAmount currencyID="EUR">8.3199453552</cbc:PriceAmount>
        </cac:Price>
    </cac:InvoiceLine>
    <cac:InvoiceLine>
        <cbc:ID>___ignore___</cbc:ID>
        <cbc:InvoicedQuantity unitCode="C62">12.0</cbc:InvoicedQuantity>
        <cbc:LineExtensionAmount currencyID="EUR">275.60</cbc:LineExtensionAmount>
        <cac:AllowanceCharge>
            <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
            <cbc:AllowanceChargeReasonCode>95</cbc:AllowanceChargeReasonCode>
            <cbc:Amount currencyID="EUR">176.20</cbc:Amount>
        </cac:AllowanceCharge>
        <cac:Item>
            <cbc:Description>product_a</cbc:Description>
            <cbc:Name>product_a</cbc:Name>
            <cac:ClassifiedTaxCategory>
                <cbc:ID>S</cbc:ID>
                <cbc:Percent>21.0</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:ClassifiedTaxCategory>
        </cac:Item>
        <cac:Price>
            <cbc:PriceAmount currencyID="EUR">37.*********</cbc:PriceAmount>
        </cac:Price>
    </cac:InvoiceLine>
    <cac:InvoiceLine>
        <cbc:ID>___ignore___</cbc:ID>
        <cbc:InvoicedQuantity unitCode="C62">12.0</cbc:InvoicedQuantity>
        <cbc:LineExtensionAmount currencyID="EUR">654.41</cbc:LineExtensionAmount>
        <cac:AllowanceCharge>
            <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
            <cbc:AllowanceChargeReasonCode>95</cbc:AllowanceChargeReasonCode>
            <cbc:Amount currencyID="EUR">418.39</cbc:Amount>
        </cac:AllowanceCharge>
        <cac:Item>
            <cbc:Description>product_a</cbc:Description>
            <cbc:Name>product_a</cbc:Name>
            <cac:ClassifiedTaxCategory>
                <cbc:ID>S</cbc:ID>
                <cbc:Percent>21.0</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:ClassifiedTaxCategory>
        </cac:Item>
        <cac:Price>
            <cbc:PriceAmount currencyID="EUR">89.*********</cbc:PriceAmount>
        </cac:Price>
    </cac:InvoiceLine>
    <cac:InvoiceLine>
        <cbc:ID>___ignore___</cbc:ID>
        <cbc:InvoicedQuantity unitCode="C62">12.0</cbc:InvoicedQuantity>
        <cbc:LineExtensionAmount currencyID="EUR">1093.61</cbc:LineExtensionAmount>
        <cac:AllowanceCharge>
            <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
            <cbc:AllowanceChargeReasonCode>95</cbc:AllowanceChargeReasonCode>
            <cbc:Amount currencyID="EUR">699.19</cbc:Amount>
        </cac:AllowanceCharge>
        <cac:Item>
            <cbc:Description>product_a</cbc:Description>
            <cbc:Name>product_a</cbc:Name>
            <cac:ClassifiedTaxCategory>
                <cbc:ID>S</cbc:ID>
                <cbc:Percent>21.0</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:ClassifiedTaxCategory>
        </cac:Item>
        <cac:Price>
            <cbc:PriceAmount currencyID="EUR">149.*********</cbc:PriceAmount>
        </cac:Price>
    </cac:InvoiceLine>
    <cac:InvoiceLine>
        <cbc:ID>___ignore___</cbc:ID>
        <cbc:InvoicedQuantity unitCode="C62">6.0</cbc:InvoicedQuantity>
        <cbc:LineExtensionAmount currencyID="EUR">456.77</cbc:LineExtensionAmount>
        <cac:AllowanceCharge>
            <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
            <cbc:AllowanceChargeReasonCode>95</cbc:AllowanceChargeReasonCode>
            <cbc:Amount currencyID="EUR">292.03</cbc:Amount>
        </cac:AllowanceCharge>
        <cac:Item>
            <cbc:Description>product_a</cbc:Description>
            <cbc:Name>product_a</cbc:Name>
            <cac:ClassifiedTaxCategory>
                <cbc:ID>S</cbc:ID>
                <cbc:Percent>21.0</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:ClassifiedTaxCategory>
        </cac:Item>
        <cac:Price>
            <cbc:PriceAmount currencyID="EUR">124.8005464481</cbc:PriceAmount>
        </cac:Price>
    </cac:InvoiceLine>
    <cac:InvoiceLine>
        <cbc:ID>___ignore___</cbc:ID>
        <cbc:InvoicedQuantity unitCode="C62">1.0</cbc:InvoicedQuantity>
        <cbc:LineExtensionAmount currencyID="EUR">154.45</cbc:LineExtensionAmount>
        <cac:AllowanceCharge>
            <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
            <cbc:AllowanceChargeReasonCode>95</cbc:AllowanceChargeReasonCode>
            <cbc:Amount currencyID="EUR">98.75</cbc:Amount>
        </cac:AllowanceCharge>
        <cac:Item>
            <cbc:Description>product_a</cbc:Description>
            <cbc:Name>product_a</cbc:Name>
            <cac:ClassifiedTaxCategory>
                <cbc:ID>S</cbc:ID>
                <cbc:Percent>21.0</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:ClassifiedTaxCategory>
        </cac:Item>
        <cac:Price>
            <cbc:PriceAmount currencyID="EUR">253.1967213115</cbc:PriceAmount>
        </cac:Price>
    </cac:InvoiceLine>
    <cac:InvoiceLine>
        <cbc:ID>___ignore___</cbc:ID>
        <cbc:InvoicedQuantity unitCode="C62">12.0</cbc:InvoicedQuantity>
        <cbc:LineExtensionAmount currencyID="EUR">353.56</cbc:LineExtensionAmount>
        <cac:AllowanceCharge>
            <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
            <cbc:AllowanceChargeReasonCode>95</cbc:AllowanceChargeReasonCode>
            <cbc:Amount currencyID="EUR">226.05</cbc:Amount>
        </cac:AllowanceCharge>
        <cac:Item>
            <cbc:Description>product_a</cbc:Description>
            <cbc:Name>product_a</cbc:Name>
            <cac:ClassifiedTaxCategory>
                <cbc:ID>S</cbc:ID>
                <cbc:Percent>21.0</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:ClassifiedTaxCategory>
        </cac:Item>
        <cac:Price>
            <cbc:PriceAmount currencyID="EUR">48.3005464481</cbc:PriceAmount>
        </cac:Price>
    </cac:InvoiceLine>
    <cac:InvoiceLine>
        <cbc:ID>___ignore___</cbc:ID>
        <cbc:InvoicedQuantity unitCode="C62">20.0</cbc:InvoicedQuantity>
        <cbc:LineExtensionAmount currencyID="EUR">424.56</cbc:LineExtensionAmount>
        <cac:AllowanceCharge>
            <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
            <cbc:AllowanceChargeReasonCode>95</cbc:AllowanceChargeReasonCode>
            <cbc:Amount currencyID="EUR">271.44</cbc:Amount>
        </cac:AllowanceCharge>
        <cac:Item>
            <cbc:Description>product_a</cbc:Description>
            <cbc:Name>product_a</cbc:Name>
            <cac:ClassifiedTaxCategory>
                <cbc:ID>S</cbc:ID>
                <cbc:Percent>21.0</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:ClassifiedTaxCategory>
        </cac:Item>
        <cac:Price>
            <cbc:PriceAmount currencyID="EUR">34.8</cbc:PriceAmount>
        </cac:Price>
    </cac:InvoiceLine>
    <cac:InvoiceLine>
        <cbc:ID>___ignore___</cbc:ID>
        <cbc:InvoicedQuantity unitCode="C62">10.0</cbc:InvoicedQuantity>
        <cbc:LineExtensionAmount currencyID="EUR">294.63</cbc:LineExtensionAmount>
        <cac:AllowanceCharge>
            <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
            <cbc:AllowanceChargeReasonCode>95</cbc:AllowanceChargeReasonCode>
            <cbc:Amount currencyID="EUR">188.37</cbc:Amount>
        </cac:AllowanceCharge>
        <cac:Item>
            <cbc:Description>product_a</cbc:Description>
            <cbc:Name>product_a</cbc:Name>
            <cac:ClassifiedTaxCategory>
                <cbc:ID>S</cbc:ID>
                <cbc:Percent>21.0</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:ClassifiedTaxCategory>
        </cac:Item>
        <cac:Price>
            <cbc:PriceAmount currencyID="EUR">48.3</cbc:PriceAmount>
        </cac:Price>
    </cac:InvoiceLine>
    <cac:InvoiceLine>
        <cbc:ID>___ignore___</cbc:ID>
        <cbc:InvoicedQuantity unitCode="C62">10.0</cbc:InvoicedQuantity>
        <cbc:LineExtensionAmount currencyID="EUR">439.20</cbc:LineExtensionAmount>
        <cac:AllowanceCharge>
            <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
            <cbc:AllowanceChargeReasonCode>95</cbc:AllowanceChargeReasonCode>
            <cbc:Amount currencyID="EUR">280.80</cbc:Amount>
        </cac:AllowanceCharge>
        <cac:Item>
            <cbc:Description>product_a</cbc:Description>
            <cbc:Name>product_a</cbc:Name>
            <cac:ClassifiedTaxCategory>
                <cbc:ID>S</cbc:ID>
                <cbc:Percent>21.0</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:ClassifiedTaxCategory>
        </cac:Item>
        <cac:Price>
            <cbc:PriceAmount currencyID="EUR">72.0</cbc:PriceAmount>
        </cac:Price>
    </cac:InvoiceLine>
    <cac:InvoiceLine>
        <cbc:ID>___ignore___</cbc:ID>
        <cbc:InvoicedQuantity unitCode="C62">5.0</cbc:InvoicedQuantity>
        <cbc:LineExtensionAmount currencyID="EUR">292.80</cbc:LineExtensionAmount>
        <cac:AllowanceCharge>
            <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
            <cbc:AllowanceChargeReasonCode>95</cbc:AllowanceChargeReasonCode>
            <cbc:Amount currencyID="EUR">187.20</cbc:Amount>
        </cac:AllowanceCharge>
        <cac:Item>
            <cbc:Description>product_a</cbc:Description>
            <cbc:Name>product_a</cbc:Name>
            <cac:ClassifiedTaxCategory>
                <cbc:ID>S</cbc:ID>
                <cbc:Percent>21.0</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:ClassifiedTaxCategory>
        </cac:Item>
        <cac:Price>
            <cbc:PriceAmount currencyID="EUR">96.0</cbc:PriceAmount>
        </cac:Price>
    </cac:InvoiceLine>
    <cac:InvoiceLine>
        <cbc:ID>___ignore___</cbc:ID>
        <cbc:InvoicedQuantity unitCode="C62">3.0</cbc:InvoicedQuantity>
        <cbc:LineExtensionAmount currencyID="EUR">211.37</cbc:LineExtensionAmount>
        <cac:AllowanceCharge>
            <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
            <cbc:AllowanceChargeReasonCode>95</cbc:AllowanceChargeReasonCode>
            <cbc:Amount currencyID="EUR">135.14</cbc:Amount>
        </cac:AllowanceCharge>
        <cac:Item>
            <cbc:Description>product_a</cbc:Description>
            <cbc:Name>product_a</cbc:Name>
            <cac:ClassifiedTaxCategory>
                <cbc:ID>S</cbc:ID>
                <cbc:Percent>21.0</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:ClassifiedTaxCategory>
        </cac:Item>
        <cac:Price>
            <cbc:PriceAmount currencyID="EUR">115.5027322404</cbc:PriceAmount>
        </cac:Price>
    </cac:InvoiceLine>
    <cac:InvoiceLine>
        <cbc:ID>___ignore___</cbc:ID>
        <cbc:InvoicedQuantity unitCode="C62">4.0</cbc:InvoicedQuantity>
        <cbc:LineExtensionAmount currencyID="EUR">123.83</cbc:LineExtensionAmount>
        <cac:AllowanceCharge>
            <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
            <cbc:AllowanceChargeReasonCode>95</cbc:AllowanceChargeReasonCode>
            <cbc:Amount currencyID="EUR">79.17</cbc:Amount>
        </cac:AllowanceCharge>
        <cac:Item>
            <cbc:Description>product_a</cbc:Description>
            <cbc:Name>product_a</cbc:Name>
            <cac:ClassifiedTaxCategory>
                <cbc:ID>S</cbc:ID>
                <cbc:Percent>21.0</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:ClassifiedTaxCategory>
        </cac:Item>
        <cac:Price>
            <cbc:PriceAmount currencyID="EUR">50.75</cbc:PriceAmount>
        </cac:Price>
    </cac:InvoiceLine>
    <cac:InvoiceLine>
        <cbc:ID>___ignore___</cbc:ID>
        <cbc:InvoicedQuantity unitCode="C62">30.0</cbc:InvoicedQuantity>
        <cbc:LineExtensionAmount currencyID="EUR">391.07</cbc:LineExtensionAmount>
        <cac:AllowanceCharge>
            <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
            <cbc:AllowanceChargeReasonCode>95</cbc:AllowanceChargeReasonCode>
            <cbc:Amount currencyID="EUR">250.03</cbc:Amount>
        </cac:AllowanceCharge>
        <cac:Item>
            <cbc:Description>product_a</cbc:Description>
            <cbc:Name>product_a</cbc:Name>
            <cac:ClassifiedTaxCategory>
                <cbc:ID>S</cbc:ID>
                <cbc:Percent>21.0</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:ClassifiedTaxCategory>
        </cac:Item>
        <cac:Price>
            <cbc:PriceAmount currencyID="EUR">21.3699453552</cbc:PriceAmount>
        </cac:Price>
    </cac:InvoiceLine>
    <cac:InvoiceLine>
        <cbc:ID>___ignore___</cbc:ID>
        <cbc:InvoicedQuantity unitCode="C62">3.0</cbc:InvoicedQuantity>
        <cbc:LineExtensionAmount currencyID="EUR">74.66</cbc:LineExtensionAmount>
        <cac:AllowanceCharge>
            <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
            <cbc:AllowanceChargeReasonCode>95</cbc:AllowanceChargeReasonCode>
            <cbc:Amount currencyID="EUR">47.73</cbc:Amount>
        </cac:AllowanceCharge>
        <cac:Item>
            <cbc:Description>product_a</cbc:Description>
            <cbc:Name>product_a</cbc:Name>
            <cac:ClassifiedTaxCategory>
                <cbc:ID>S</cbc:ID>
                <cbc:Percent>21.0</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:ClassifiedTaxCategory>
        </cac:Item>
        <cac:Price>
            <cbc:PriceAmount currencyID="EUR">40.7978142077</cbc:PriceAmount>
        </cac:Price>
    </cac:InvoiceLine>
    <cac:InvoiceLine>
        <cbc:ID>___ignore___</cbc:ID>
        <cbc:InvoicedQuantity unitCode="C62">3.0</cbc:InvoicedQuantity>
        <cbc:LineExtensionAmount currencyID="EUR">74.66</cbc:LineExtensionAmount>
        <cac:AllowanceCharge>
            <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
            <cbc:AllowanceChargeReasonCode>95</cbc:AllowanceChargeReasonCode>
            <cbc:Amount currencyID="EUR">47.73</cbc:Amount>
        </cac:AllowanceCharge>
        <cac:Item>
            <cbc:Description>product_a</cbc:Description>
            <cbc:Name>product_a</cbc:Name>
            <cac:ClassifiedTaxCategory>
                <cbc:ID>S</cbc:ID>
                <cbc:Percent>21.0</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:ClassifiedTaxCategory>
        </cac:Item>
        <cac:Price>
            <cbc:PriceAmount currencyID="EUR">40.7978142077</cbc:PriceAmount>
        </cac:Price>
    </cac:InvoiceLine>
    <cac:InvoiceLine>
        <cbc:ID>___ignore___</cbc:ID>
        <cbc:InvoicedQuantity unitCode="C62">3.0</cbc:InvoicedQuantity>
        <cbc:LineExtensionAmount currencyID="EUR">60.21</cbc:LineExtensionAmount>
        <cac:AllowanceCharge>
            <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
            <cbc:AllowanceChargeReasonCode>95</cbc:AllowanceChargeReasonCode>
            <cbc:Amount currencyID="EUR">38.49</cbc:Amount>
        </cac:AllowanceCharge>
        <cac:Item>
            <cbc:Description>product_a</cbc:Description>
            <cbc:Name>product_a</cbc:Name>
            <cac:ClassifiedTaxCategory>
                <cbc:ID>S</cbc:ID>
                <cbc:Percent>21.0</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:ClassifiedTaxCategory>
        </cac:Item>
        <cac:Price>
            <cbc:PriceAmount currencyID="EUR">32.9016393443</cbc:PriceAmount>
        </cac:Price>
    </cac:InvoiceLine>
    <cac:InvoiceLine>
        <cbc:ID>___ignore___</cbc:ID>
        <cbc:InvoicedQuantity unitCode="C62">-1.0</cbc:InvoicedQuantity>
        <cbc:LineExtensionAmount currencyID="EUR">-1337.83</cbc:LineExtensionAmount>
        <cac:Item>
            <cbc:Description>product_a</cbc:Description>
            <cbc:Name>product_a</cbc:Name>
            <cac:ClassifiedTaxCategory>
                <cbc:ID>S</cbc:ID>
                <cbc:Percent>21.0</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:ClassifiedTaxCategory>
        </cac:Item>
        <cac:Price>
            <cbc:PriceAmount currencyID="EUR">1337.83</cbc:PriceAmount>
        </cac:Price>
    </cac:InvoiceLine>
</Invoice>
