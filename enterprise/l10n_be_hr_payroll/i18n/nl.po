# Translation of Odoo Server.
# This file contains the translation of the following modules:
#   * l10n_be_hr_payroll
#
# Translators:
# Wil <PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2024
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-11-20 08:49+0000\n"
"PO-Revision-Date: 2024-01-30 08:20+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"Language: nl\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "# Days"
msgstr "# Dagen"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "# Hours"
msgstr "# Uren"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "# Hours:"
msgstr "# Uren:"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__insured_relative_children
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "# Insured Children < 19 y/o"
msgstr "# Verzekerde kinderen < 19 jaar oud"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__insured_relative_adults
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "# Insured Children >= 19 y/o"
msgstr "# Verzekerde kinderen >= 19 jaar oud"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__months_count
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__months_count
msgid "# Months"
msgstr "# Maanden"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__l10n_be_dependent_children_attachment
msgid "# dependent children for salary attachement"
msgstr "# kinderen ten laste voor loonbeslag"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__other_disabled_juniors_dependent
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__other_disabled_juniors_dependent
msgid "# disabled people (<65)"
msgstr "# gehandicapte personen (<65)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__other_disabled_senior_dependent
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__other_disabled_senior_dependent
msgid "# disabled seniors (>=65)"
msgstr "# gehandicapte senioren (>=65)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__other_juniors_dependent
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__other_juniors_dependent
msgid "# people (<65)"
msgstr "# personen (<65)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__other_senior_dependent
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__other_senior_dependent
msgid "# seniors (>=65)"
msgstr "# senioren (>=65)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_double_pay_recovery_line_view_form
msgid "#Months"
msgstr "#Maanden"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "%(date_from)s to %(date_to)s"
msgstr "%(date_from)s t/m %(date_to)s"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_hr_payroll_schedule_change_wizard.py:0
msgid "%(employee)s - Part Time %(calendar)s"
msgstr "%(employee)s - Deeltijds %(calendar)s"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_leave.py:0
msgid ""
"%(employee)s is in %(holiday_status)s. Fill in the appropriate eDRS here: "
"%(link)s"
msgstr ""
"%(employee)s is in %(holiday_status)s. Vul hier de juiste eDRS in: %(link)s"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_individual_account.py:0
msgid "%(employee_name)s-individual-account-%(year)s"
msgstr "%(employee_name)s-individuele-rekening-%(year)s"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/hr_payroll_employee_departure_notice.py:0
msgid "%(months)s months"
msgstr "%(months)s maanden"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_dmfa.py:0
msgid "%(reference)s %(quarter)s quarter %(year)s"
msgstr "%(reference)s %(quarter)s quarter %(year)s"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
msgid "%(year)s-%(employee)s-281_10"
msgstr "%(year)s-%(employee)s-281_10"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_45.py:0
msgid "%(year)s-%(employee)s-281_45"
msgstr "%(year)s-%(employee)s-281_45"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/hr_payroll_employee_departure_notice.py:0
msgid "%(years)s years and %(months)s months"
msgstr "%(years)s jaren en %(months)s maanden"

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,print_report_name:l10n_be_hr_payroll.action_report_termination_holidays_n
msgid "'Holiday Attest (Year N) - %s' % (object.employee_id.name)"
msgstr "'Vakantieattest (Jaar N) - %s' % (object.employee_id.name)"

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,print_report_name:l10n_be_hr_payroll.action_report_termination_holidays_n1
msgid "'Holiday Attest (Year N-1) - %s' % (object.employee_id.name)"
msgstr "'Vakantieattest (Jaar N-1) - %s' % (object.employee_id.name)"

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,print_report_name:l10n_be_hr_payroll.action_report_bonus_month
#: model:ir.actions.report,print_report_name:l10n_be_hr_payroll.action_report_light_payslip_be
#: model:ir.actions.report,print_report_name:l10n_be_hr_payroll.action_report_payslip_be
msgid "'Payslip - %s' % (object.employee_id.name)"
msgstr "'Loonstrook - %s' % (object.employee_id.naam)"

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,print_report_name:l10n_be_hr_payroll.action_report_termination_fees
msgid "'Termination - %s' % (object.employee_id.name)"
msgstr "'Beëindiging - %s' % (object.employee_id.naam)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
msgid "(Hours / Week)"
msgstr "(Uren / Week)"

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,print_report_name:l10n_be_hr_payroll.action_report_individual_account
msgid "(object._get_report_base_filename())"
msgstr "(object._get_report_base_filename())"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "* Amount"
msgstr "* Bedrag"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"* Compensation granted to insurance inspectors in reimbursement of costs"
msgstr ""
"* Vergoeding toegekend aan verzekeringsinspecteurs als vergoeding van de "
"kosten"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "* Individual agreement available"
msgstr "* Individuele overeenkomst beschikbaar"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "* Km"
msgstr "* Km"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "* Mention: MOBILITY ALLOWANCE"
msgstr "* Vermelding: MOBILITEITSVERGOEDING"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"* Options granted by a foreign company not having an establishment in Belgium"
msgstr ""
"* Opties toegekend door een buitenlandse vennootschap zonder vestiging in "
"België"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "* Social Security package"
msgstr "* Forfait sociale zekerheid"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "* Total compensation"
msgstr "* Totale vergoeding"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "* amount"
msgstr "* bedrag"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "* code"
msgstr "* code"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "* nature"
msgstr "* aard"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "* number of days out of border area"
msgstr "* aantal dagen gewerkt buiten de grenszone"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "* percentage(s)"
msgstr "* percentage(s)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"* specific remuneration paid in 2023 for specific services performed in "
"2023, 2022, 2021 and/or 2020"
msgstr ""
"* specifieke vergoeding betaald in 2023 voor specifieke diensten verleend in "
"2023, 2022, 2021 en/of 2020"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"* total amount of all remuneration paid under a student employment contract"
msgstr "* totale bedrag van alle bezoldigingen uit een studentenovereenkomst"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_belgium_light_payslip
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_belgium_payslip
msgid "*10€ + 1*"
msgstr "*10 € + 1*"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_employee_form__l10n_be_view_for
msgid "- Previous occupation for Double Holiday Pay Recovery in"
msgstr "- Vorige beroepsbezigheid voor invordering dubbel vakantiegeld in"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_employee_form__l10n_be_view_for
msgid "- Simple Holiday Pay from previous employer to recover in"
msgstr "- In te vorderen enkel vakantiegeld van vorige werkgever in"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_employee.py:0
msgid ""
"- Without Income: The spouse of the income recipient has no professional "
"income.\n"
"\n"
"- High income: The spouse of the recipient of the income has professional "
"income, other than pensions, annuities or similar income, which exceeds "
"%(low_income_threshold)s€ net per month.\n"
"\n"
"- Low Income: The spouse of the recipient of the income has professional "
"income, other than pensions, annuities or similar income, which does not "
"exceed %(low_income_threshold)s€ net per month.\n"
"\n"
"- Low Pensions: The spouse of the beneficiary of the income has professional "
"income which consists exclusively of pensions, annuities or similar income "
"and which does not exceed %(other_income_threshold)s€ net per month.\n"
"\n"
"- High Pensions: The spouse of the beneficiary of the income has "
"professional income which consists exclusively of pensions, annuities or "
"similar income and which exceeds %(other_income_threshold)s€ net per month."
msgstr ""
"- Zonder inkomen: De echtgeno(o)t(e) van de inkomentrekker heeft geen "
"beroepsinkomen.\n"
"\n"
"- Hoog inkomen: De echtgeno(o)t(e) van de inkomentrekker heeft een "
"beroepsinkomen, andere dan pensioenen, lijfrenten of soortgelijke inkomen, "
"die hoger zijn dan %(low_income_threshold)s€ netto per maand.\n"
"\n"
"- Laag inkomen: De echtgeno(o)t(e) van de inkomentrekker heeft een "
"beroepsinkomen, andere dan pensioenen, lijfrenten of soortgelijke inkomen, "
"die hoger zijn dan %(low_income_threshold)s€ netto per maand.\n"
"\n"
"- Laag pensioen: De echtgeno(o)t(e) van de inkomentrekker heeft "
"beroepsinkomen die uitsluitend bestaan uit pensioenen, lijfrenten of "
"soortgelijke inkomsten en die niet meer bedragen dan "
"%(other_income_threshold)s€ netto per maand.\n"
"\n"
"- Hoog pensioen: De echtgeno(o)t(e) van de inkomentrekker heeft "
"beroepsinkomen die uitsluitend bestaan uit pensioenen, lijfrenten of "
"soortgelijke inkomsten en die meer bedragen dan %(other_income_threshold)s€ "
"netto per maand."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"- for overtime worked between 01.01.2021 and 30.06.2021 included with "
"employers belonging to crucial sectors or in the public sector"
msgstr ""
"- voor overuren gepresteerd van 01.01.2021 tot 30.06.2021 bij werkgevers in "
"cruciale sectoren of in de openbare sector"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"- for overtime worked between 01.07.2021 and 31.12.2021 as part of the "
"recovery plan"
msgstr ""
"- voor overuren gepresteerd van 01.07.2021 tot 31.12.2021 in het kader van "
"de relance"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "- in the construction sector with registration system"
msgstr "- bouw met registratiesysteem"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "- lump sum reimbursements based on serious standards"
msgstr "- forfaitaire vergoedingen overeenkomstig ernstige normen"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "- lump sum reimbursements in the absence of serious standards"
msgstr "- forfaitaire vergoedingen niet overeenkomstig ernstige normen"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "- mobility allowance"
msgstr "- mobiliteitsvergoeding"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"- provided from 01.01.2021 to 30.06.2021 inclusive with employers belonging "
"to crucial sectors or in the public sector and paid in 2023"
msgstr ""
"- voor overuren gepresteerd van 01.01.2021 tot 30.06.2021 bij werkgevers in "
"cruciale sectoren of in de openbare sector en betaald in 2023"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"- provided from 01.07.2021 to 31.12.2021 included as part of the recovery "
"plan and paid in 2023"
msgstr ""
"- voor overuren gepresteerd van 01.07.2021 tot 31.12.2021 in het kader van "
"de relance en betaald in 2023"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "- reimbursements based on supporting documents"
msgstr "- vergoedingen op basis van bewijsstukken"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid ""
".\n"
"                    <span>Your future holidays won't be paid. So you are "
"advised to keep this amount\n"
"                    until you take these days off.</span>"
msgstr ""
".\n"
"                            <span>Je toekomstige vakantiedagen worden niet "
"betaald. We raden je aan  dit bedrag te bewaren\n"
"                            totdat je deze vakantiedagen opneemt.</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "0,87% of gross reference remuneration"
msgstr "0,87% van de bruto referentievergoeding"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "0987"
msgstr "0987"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__paid_time_off
msgid "1 day is 7 hours and 36 minutes"
msgstr "1 dag bedraagt 7 uur en 36 minuten"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__paid_time_off_to_allocate
msgid ""
"1 day is the number of the amount of hours per day in the working schedule"
msgstr "1 dag is het aantal uren per dag in het werkrooster"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid ""
"1) Amount which does not exceed the thresholds set in art. 269, § 1, 4°, CIR "
"92:"
msgstr ""
"1) Bedrag dat de in art. 269, § 1, 4°, WIB 92, vastgelegde grenzen niet "
"overschrijdt:"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "1) Remuneration"
msgstr "1) Bezoldiging"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_company__onss_company_id
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_config_settings__onss_company_id
msgid "10-digit code given by ONSS"
msgstr "10-cijferige code uitgegeven door de RSZ"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "100000"
msgstr "100000"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1001"
msgstr "1001"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1002"
msgstr "1002"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1003"
msgstr "1003"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1011"
msgstr "1011"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1012"
msgstr "1012"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1013"
msgstr "1013"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "102: Staff Costs:"
msgstr "102: Personeelskosten:"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "103: Benefits Above Salary:"
msgstr "103: Voordelen bovenop het loon:"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "105"
msgstr "105"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "110"
msgstr "110"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "111"
msgstr "111"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "112"
msgstr "112"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "113"
msgstr "113"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_contract__l10n_be_impulsion_plan__12mo
msgid "12 months +"
msgstr "12 maanden +"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "120"
msgstr "120"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1200"
msgstr "1200"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1201"
msgstr "1201"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1202"
msgstr "1202"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1203"
msgstr "1203"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "121"
msgstr "121"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1210"
msgstr "1210"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1211"
msgstr "1211"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1212"
msgstr "1212"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1213"
msgstr "1213"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "130"
msgstr "130"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "132"
msgstr "132"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "133"
msgstr "133"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "134"
msgstr "134"

#. module: l10n_be_hr_payroll
#: model:hr.payroll.structure,payslip_name:l10n_be_hr_payroll.hr_payroll_structure_cp200_thirteen_month
msgid "13th Month Slip"
msgstr "Loonstrook 13e maand"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "15"
msgstr "15"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "1500.00"
msgstr "1500.00"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "1999"
msgstr "1999"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_dmfa__quarter__1
msgid "1st"
msgstr "1e"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "1° - which count for the limit up to 180 hours"
msgstr "1° - die in aanmerking komen voor de begrenzing tot 180 uren"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "1° Awarded in 2023"
msgstr "1° Toegekend in 2023"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "1° Compensation, not mentioned in 2°, 3°, 4°"
msgstr "1° Vergoeding, niet vermeld in 2°, 3°, 4°"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "1° Ordinary remuneration"
msgstr "1° Gewone bezoldiging"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid "2) Amount that exceeds the thresholds set in art. 269, § 1, 4°, CIR 92:"
msgstr ""
"2) Bedrag dat de in art. 269, § 1, 4°, WIB 92, vastgelegde grenzen "
"overschrijdt:"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "2) Overtime"
msgstr "2) Overuren"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "2) Overtime hours worked and paid from 01.07.2023"
msgstr "2) Overuren gepresteerd en betaald vanaf 01.07.2023"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "2) Overtime hours worked in 2022 and paid in 2023"
msgstr "2) Overuren gepresteerd in 2022 en betaald in 2023"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "2.00"
msgstr "2.00"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "20"
msgstr "20"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "20-03-2000"
msgstr "20-03-2000"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "20.00"
msgstr "20.00"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "200.000"
msgstr "200.000"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "2022"
msgstr "2022"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "2023"
msgstr "2023"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "205"
msgstr "205"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "21-04-2001"
msgstr "21-04-2001"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "210"
msgstr "210"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "211"
msgstr "211"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "212"
msgstr "212"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "213"
msgstr "213"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "233"
msgstr "233"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "2334"
msgstr "2334"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "234"
msgstr "234"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "240"
msgstr "240"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "242"
msgstr "242"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "243"
msgstr "243"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "247"
msgstr "247"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "250"
msgstr "250"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "251"
msgstr "251"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "252"
msgstr "252"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "254"
msgstr "254"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "263"
msgstr "263"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "267"
msgstr "267"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "271"
msgstr "271"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "273"
msgstr "273"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.273S_xml_report
msgid "273S"
msgstr "273S"

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,name:l10n_be_hr_payroll.action_report_ip_273S
msgid "273S PDF"
msgstr "273S PDF"

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_273S_action
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_273s
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_l10n_be_273S
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_273S_view_form
msgid "273S Sheet"
msgstr "Fiche 273S"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_273S_view_tree
msgid "273S Sheets"
msgstr "Fiches 273S"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "274"
msgstr "274"

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,name:l10n_be_hr_payroll.action_report_employee_274_10
msgid "274.10 PDF"
msgstr "274.10 PDF"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__sheet_274_10
msgid "274.10 Sheet"
msgstr "Fiche 274.10"

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_274_XX_action
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_274_xx
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_l10n_be_274_XX
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_tree
msgid "274.XX Sheets"
msgstr "274.XX aangiftes"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_274_xx_line
msgid "274.XX Sheets Line"
msgstr "Regel fiche 274.XX"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "275"
msgstr "275"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "276"
msgstr "276"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "277"
msgstr "277"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "278"
msgstr "278"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "279"
msgstr "279"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "280"
msgstr "280"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_10_action_view_tree
msgid "281.10 Forms"
msgstr "Fiches 281.10"

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,name:l10n_be_hr_payroll.action_report_employee_281_10
msgid "281.10 PDF"
msgstr "281.10 PDF"

#. module: l10n_be_hr_payroll
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_l10n_be_l10n_be_281_10
msgid "281.10 Sheet"
msgstr "Fiche 281.10"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "281.10 Sheet: Income"
msgstr "Fiche 281.10: Inkomen"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_45_action_view_tree
msgid "281.45 Forms"
msgstr "Fiches 281.45"

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,name:l10n_be_hr_payroll.action_report_employee_281_45
msgid "281.45 PDF"
msgstr "281.45 PDF"

#. module: l10n_be_hr_payroll
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_l10n_be_l10n_be_281_45
msgid "281.45 Sheet"
msgstr "Fiche 281.45"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid "281.45 Sheet - Year"
msgstr "Fiche 281.45  - Jaar"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "283"
msgstr "283"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "284"
msgstr "284"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "285"
msgstr "285"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "286"
msgstr "286"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "287"
msgstr "287"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "290"
msgstr "290"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid "2b. Name and address of income debtor:"
msgstr "2. Naam en adres van de schuldenaar van de inkomsten:"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_dmfa__quarter__2
msgid "2nd"
msgstr "2de"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "2° Actions"
msgstr "2° Acties"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "2° Arrears"
msgstr "2° Achterstallen"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "2° Awarded before 2023"
msgstr "2° Toegekend voor 2023"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "2° which are taken into account for the limit up to 360 hours"
msgstr "2° die in aanmerking komen voor de begrenzing tot 360 uren"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_company__dmfa_employer_class
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_config_settings__dmfa_employer_class
msgid "3-digit code given by ONSS"
msgstr "3-cijferige code gegeven door ONSS"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "305"
msgstr "305"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "308"
msgstr "308"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "310"
msgstr "310"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "311"
msgstr "311"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "312"
msgstr "312"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "313"
msgstr "313"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "317"
msgstr "317"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid "32"
msgstr "32"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "32.00"
msgstr "32.00"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid "33"
msgstr "33"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "335"
msgstr "335"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "336"
msgstr "336"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "337"
msgstr "337"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "338"
msgstr "338"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid "34"
msgstr "34"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "340"
msgstr "340"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "341"
msgstr "341"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "342"
msgstr "342"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "343"
msgstr "343"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "3456"
msgstr "3456"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "34565"
msgstr "34565"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "34893"
msgstr "34893"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "378"
msgstr "378"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "379"
msgstr "379"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "381"
msgstr "381"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "382"
msgstr "382"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "386"
msgstr "386"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "387"
msgstr "387"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "395"
msgstr "395"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "396"
msgstr "396"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "397"
msgstr "397"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "398"
msgstr "398"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid "3b. Income recipient"
msgstr "3b. Verkrijger van de inkomsten"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_dmfa__quarter__3
msgid "3rd"
msgstr "3e"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "3° Bonuses, premiums and stock options"
msgstr "3° Bonussen, premies en aandelenopties"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_dmfa__quarter__4
msgid "4th"
msgstr "4e"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "4° Benefits in kind"
msgstr "4° Voordelen van alle aard"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "5 Hours/Week"
msgstr "5 uren/week"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "5.00"
msgstr "5.00"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_contract__l10n_be_impulsion_plan__55yo
msgid "55+ years old"
msgstr "55+ jaar oud"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "567877"
msgstr "567877"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "57.75 %"
msgstr "57,75 %"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5801"
msgstr "5801"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5802"
msgstr "5802"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5803"
msgstr "5803"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "58031"
msgstr "58031"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "58032"
msgstr "58032"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "58033"
msgstr "58033"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5811"
msgstr "5811"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5812"
msgstr "5812"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5813"
msgstr "5813"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "58131"
msgstr "58131"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "58132"
msgstr "58132"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "58133"
msgstr "58133"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5821"
msgstr "5821"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5822"
msgstr "5822"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5823"
msgstr "5823"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5831"
msgstr "5831"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5832"
msgstr "5832"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5833"
msgstr "5833"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5841"
msgstr "5841"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5842"
msgstr "5842"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5843"
msgstr "5843"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5851"
msgstr "5851"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5852"
msgstr "5852"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5853"
msgstr "5853"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
msgid "6,8% of gross reference remuneration"
msgstr "6,8% van de bruto referentievergoeding"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "6,8% of gross reference remuneration - additional time off amount"
msgstr "6,8% van de bruto referentiebezoldiging - bedrag extra verlof"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid "6. Amount of retained withholding tax:"
msgstr "6. Bedrag van de roerende voorheffing:"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "6000.00"
msgstr "6000.00"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "66.81 %"
msgstr "66,81 %"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "6870"
msgstr "6870"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
msgid "7,67% of gross reference remuneration"
msgstr "7,67% van de bruto referentievergoeding"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid ""
"7,67% of gross reference remuneration * (time off not taken) / (right to "
"time off)"
msgstr ""
"7,67% van de bruto referentiebezoldiging * (niet opgenomen verlof) / (recht "
"op verlof)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "789"
msgstr "789"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "8.00"
msgstr "8.00"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_xml_report
msgid "865"
msgstr "865"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_xml_report
msgid "870"
msgstr "870"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_company__onss_registration_number
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_config_settings__onss_registration_number
msgid "9-digit code given by ONSS"
msgstr "9-cijferige code gegeven door ONSS"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "9876"
msgstr "9876"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.finprof_xml_report_single_declaration
msgid "9998"
msgstr "9998"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_contract__l10n_be_impulsion_plan__25yo
msgid "< 25 years old"
msgstr "< 25 jaar oud"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "<b>Contributions Summary:</b>"
msgstr "<b>Overzicht bijdragen:</b>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "<b>Deductions Summary:</b>"
msgstr "<b>Overzicht inhoudingen:</b>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "<b>Remunerations Summary:</b>"
msgstr "<b>Overzicht bezoldigingen:</b>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "<b>Services Summary:</b>"
msgstr "<b>Overzicht diensten:</b>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "<b>Student Contributions Summary:</b>"
msgstr "<b>Overzicht studentbijdragen:</b>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid "<span class=\"fw-bold\">2a. Company number: </span>"
msgstr "<span class=\"fw-bold\">2a. Ondernemingsnummer: </span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid ""
"<span class=\"fw-bold\">3a. Nature of beneficiary: </span>\n"
"                                    <span>Natural Person</span>"
msgstr ""
"<span class=\"fw-bold\">3a. Hoedanigheid van de verkrijger: </span>\n"
"                                    <span>Natuurlijke persoon</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid "<span class=\"fw-bold\">3c. NISS: </span>"
msgstr "<span class=\"fw-bold\">3c. INSZ: </span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "<span class=\"ms-3\" invisible=\"wage_type == 'hourly'\">%</span>"
msgstr "<span class=\"ms-3\" invisible=\"wage_type == 'hourly'\">%</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "<span class=\"ms-3\"> / month</span>"
msgstr "<span class=\"ms-3\"> / maand</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "<span class=\"ms-3\">/ month</span>"
msgstr "<span class=\"ms-3\">/ maand</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "<span class=\"ms-3\">/ worked day</span>"
msgstr "<span class=\"ms-3\">/ gewerkte dag</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "<span class=\"ms-3\">/ year</span>"
msgstr "<span class=\"ms-3\">/ jaar</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid ""
"<span class=\"o_form_label\" groups=\"hr.group_hr_user\" invisible=\"not "
"transport_mode_private_car\">Distance home-work</span>"
msgstr ""
"<span class=\"o_form_label\" groups=\"hr.group_hr_user\" invisible=\"not "
"transport_mode_private_car\">Afstand woonplaats-werkplek</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid ""
"<span class=\"o_form_label\" invisible=\"not "
"transport_mode_private_car\">Reimboursed amount</span>"
msgstr ""
"<span class=\"o_form_label\" invisible=\"not "
"transport_mode_private_car\">Terugbetaald bedrag</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_10_form_wizard
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_45_form_wizard
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_individual_account_view_form
msgid "<span class=\"o_stat_text\">Eligible Employees</span>"
msgstr "<span class=\"o_stat_text\">In aanmerking komende werknemers</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_group_insurance_wizard_view_form
msgid "<span class=\"oe_inline\">From</span>"
msgstr "<span class=\"oe_inline\">Van</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_group_insurance_wizard_view_form
msgid "<span class=\"oe_inline\">To</span>"
msgstr "<span class=\"oe_inline\">Tot</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid ""
"<span class=\"text-start fw-bold\">4. Gross amount of income referred to in "
"art. 17, § 1, 3°, CIR 92, with regard to copyright and related rights:</span>"
msgstr ""
"<span class=\"text-start fw-bold\">4. Brutobedrag van de inkomsten zoals "
"bedoeld in art. 17, § 1, 3°, WIB 92, wat betreft de auteursrechten en "
"naburige rechten:</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid ""
"<span class=\"text-start fw-bold\">5. Gross amount of revenue from copyright "
"and related rights referred to in art. 17, § 1, 5°, CIR 92:</span>"
msgstr ""
"<span class=\"text-start fw-bold\">5. Brutobedrag van de inkomsten uit "
"auteursrechten en naburige rechten zoals bedoeld in art. 17, § 1, 5°, WIB 92:"
"</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">10. Taxable at the rate of "
"33%:</span>"
msgstr ""
"<span class=\"text-start text-uppercase fw-bold\">10. Tegen 33% belastbaar:</"
"span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">11. Remuneration obtained "
"by athletes within the framework of their sporting activity:</span>"
msgstr ""
"<span class=\"text-start text-uppercase fw-bold\">11. Door sportbeoefenaars "
"voor hun sportieve activiteiten verkregen bezoldigingen:</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">12. Remuneration obtained "
"by sports competition referees for their refereeing services, or by "
"trainers, coaches and accompanying persons for their activity for the "
"benefit of sportsmen:</span>"
msgstr ""
"<span class=\"text-start text-uppercase fw-bold\">12. Door scheidsrechters "
"voor hun activiteiten als scheidsrechter tijdens sportwedstrijden, of door "
"opleiders, trainers, en begeleiders voor hun activiteiten ten behoeve van "
"sportbeoefenaars verkregen bezoldigingen:</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "<span class=\"text-start text-uppercase fw-bold\">13. PC Privé</span>"
msgstr "<span class=\"text-start text-uppercase fw-bold\">13. Privé-PC</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">14. Contribution to travel "
"costs:</span>"
msgstr ""
"<span class=\"text-start text-uppercase fw-bold\">14. Bijdragen in de "
"verplaatsingskosten:</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">15. Impulse Fund</span>"
msgstr ""
"<span class=\"text-start text-uppercase fw-bold\">15. Impulsfonds</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">16. Deductions for "
"supplementary pensions</span>"
msgstr ""
"<span class=\"text-start text-uppercase fw-bold\">16. Inhoudingen voor "
"aanvullend pensioen</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">17. Remuneration for "
"overtime in the hospitality industry which qualifies for the exemption</span>"
msgstr ""
"<span class=\"text-start text-uppercase fw-bold\">17. Voor vrijstelling in "
"aanmerking komende bezoldigingen voor overuren in de horeca</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">18. Overtime which gives "
"the right to extra pay</span>"
msgstr ""
"<span class=\"text-start text-uppercase fw-bold\">18. Overuren die recht "
"geven op een overwerktoeslag</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">19. Remuneration which is "
"taken into account for the exemption for voluntary overtime or net overtime "
"hours in the public sector</span>"
msgstr ""
"<span class=\"text-start text-uppercase fw-bold\">19. Voor vrijstelling in "
"aanmerking komende bezoldigingen voor vrijwillige overuren of netto overuren "
"in de openbare sector</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">20. Purchasing power "
"premium which is taken into account for the exemption</span>"
msgstr ""
"<span class=\"text-start text-uppercase fw-bold\">20. Voor vrijstelling in "
"aanmerking komende koopkrachtpremie</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">21. Withholding Taxes</"
"span>"
msgstr ""
"<span class=\"text-start text-uppercase fw-bold\">21. Bedrijfsvoorheffing</"
"span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">22. Special contributions "
"for social security</span>"
msgstr ""
"<span class=\"text-start text-uppercase fw-bold\">22. Bijzondere bijdrage "
"voor de sociale zekerheid</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">23. Public sector staff "
"without an employment contract</span>"
msgstr ""
"<span class=\"text-start text-uppercase fw-bold\">23. Overheidspersoneel "
"zonder arbeidsovereenkomst</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">24. Employment bonus</span>"
msgstr "<span class=\"text-start text-uppercase fw-bold\">24. Werkbonus</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">25. Miscellaneous "
"information</span>"
msgstr ""
"<span class=\"text-start text-uppercase fw-bold\">25. Diverse inlichtingen</"
"span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">26. Remuneration and other "
"benefits received from a related foreign company</span>"
msgstr ""
"<span class=\"text-start text-uppercase fw-bold\">26. Bezoldigingen en "
"andere voordelen ontvangen van een buitenlandse verbonden vennootschap</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">27. Foreign seasonal "
"worker in agriculture and horticulture subject to professional withholding "
"tax who provided a residence certificate</span>"
msgstr ""
"<span class=\"text-start text-uppercase fw-bold\">27. Niet-inwoner werkzaam "
"als seizoenarbeiderin de land- en tuinbouw onderworpen aan de bevrijdende "
"bedrijfsvoorheffind die een woonplaatsverklaring heeft bezorgd</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">28. Foreign executive or "
"researcher</span>"
msgstr ""
"<span class=\"text-start text-uppercase fw-bold\">28. Buitenlands kaderlid "
"of vorser</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">29. Special tax regimes "
"for inpatriate taxpayers and inpatriate researchers</span>"
msgstr ""
"<span class=\"text-start text-uppercase fw-bold\">29. Bijzondere "
"belastingstelsels voor ingekomen belastingplichtigen en ingekomen "
"onderzoekers</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">30. Remuneration paid or "
"awarded for services carried out within the framework of association "
"activities after exceeding an hourly limit</span>"
msgstr ""
"<span class=\"text-start text-uppercase fw-bold\">30. Bezoldigingen betaald "
"of toegekend voor prestaties in het kader van verenigingsactiviteiten na "
"overschrijding van een uurgrens</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">6. REMUNERATION (other "
"than referred to under 10, 11a and 12a)</span>"
msgstr ""
"<span class=\"text-start text-uppercase fw-bold\">6. BEZOLDIGING (andere dan "
"bedoeld onder 10, 11a en 12a)</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">7. Miscellaneous taxable "
"income</span>"
msgstr ""
"<span class=\"text-start text-uppercase fw-bold\">7. Afzonderlijk belastbare "
"inkomsten</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">8. Bad weather stamps</"
"span>"
msgstr ""
"<span class=\"text-start text-uppercase fw-bold\">8. Weerverletzegels</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">9. Non-recurring benefits "
"linked to results</span>"
msgstr ""
"<span class=\"text-start text-uppercase fw-bold\">9. Niet-recurrente "
"resultaatsgebonden voordelen</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">Nature des revenus</span>"
msgstr ""
"<span class=\"text-start text-uppercase fw-bold\">Aard van de inkomsten</"
"span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">Précompte Professionnel "
"Retenu</span>"
msgstr ""
"<span class=\"text-start text-uppercase fw-bold\">Ingehouden "
"bedrijfsvoorheffing</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">Précompte professionnel "
"dû</span>"
msgstr ""
"<span class=\"text-start text-uppercase fw-bold\">Verschuldigde "
"bedrijfsvoorheffing</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">Revenus imposables "
"répondant aux conditions d’application de la dispense</span>"
msgstr ""
"<span class=\"text-start text-uppercase fw-bold\">Belastbaar inkomen dat in "
"aanmerking komt voor de vrijstelling</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">Revenus imposables</span>"
msgstr ""
"<span class=\"text-start text-uppercase fw-bold\">Belastbaar inkomen</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_notice
msgid ""
"<span invisible=\"notice_duration_month_before_2014 == 0\"> months and </"
"span>"
msgstr ""
"<span invisible=\"notice_duration_month_before_2014 == 0\"> maanden en </"
"span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "<span> %</span>"
msgstr "<span> %</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<span> included:</span>"
msgstr "<span> inbegrepen:</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_notice
msgid "<span> weeks</span>"
msgstr "<span> weken</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "<span> years old</span>"
msgstr "<span> jaar oud</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_employee_form__l10n_be_view_for
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_hr_payroll_schedule_change_wizard_view_form
msgid "<span>%</span>"
msgstr "<span>%</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "<span>/ month</span>"
msgstr "<span>/maand</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<span>/Holiday year </span>"
msgstr "<span>/Vakantiejaar</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "<span>Attendance (hours)</span>"
msgstr "<span>Aanwezigheid (uren)</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<span>Holiday exercise </span>"
msgstr "<span>Vakantie</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "<span>Individual Account Report for year </span>"
msgstr "<span>Individueel rekeningoverzicht voor jaar</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "<span>No</span>"
msgstr "<span>Nee</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<span>Remuneration for the period from </span>"
msgstr "<span>Vergoeding voor de periode van </span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid ""
"<span>Social contributions on the various vacation pay have already been "
"paid.</span>"
msgstr ""
"<span>De sociale bijdragen op de verschillende vakantiegelden zijn reeds "
"betaald.</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
msgid ""
"<span>The amount covered by this certificate pre-emptively compensates the "
"vacation days you will take with\n"
"                        your next employer in </span>"
msgstr ""
"<span>Het bedrag dat door dit certificaat wordt gedekt, compenseert "
"preventief de vakantiedagen die je bij\n"
"                                je volgende werkgever zult opnemen in </span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid ""
"<span>The amount covered by this certificate pre-emptively compensates the "
"vacation days you will take with your next\n"
"                    employer in </span>"
msgstr ""
"<span>Het bedrag dat door dit certificaat wordt gedekt, compenseert "
"preventief de vakantiedagen die je bij\n"
"                            je volgende werkgever zult opnemen in </span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "<span>Yes</span>"
msgstr "<span>Ja</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid ""
"<span>You must return this certificate to your next employer, or failing "
"that, to your allowance payment agency.\n"
"                    Social security contributions on holiday pay have "
"already been retained.</span>"
msgstr ""
"<span>Je moet dit certificaat teruggeven aan je volgende werkgever, of "
"anders aan je uitbetalingsinstantie.\n"
"                            De sociale bijdragen op het vakantiegeld zijn al "
"ingehouden.</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_employee_form__l10n_be_view_for
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_hr_payroll_schedule_change_wizard_view_form
msgid "<span>days</span>"
msgstr "<span>dagen</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
msgid "<span>included:</span>"
msgstr "<span>inbegrepen:</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<span>to </span>"
msgstr "<span>tot </span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_config_settings_view_form
msgid "<span>€ / month</span>"
msgstr "<span>€ / maand</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_config_settings_view_form
msgid "<span>€ / year</span>"
msgstr "<span>€ / jaar</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_employee_form__l10n_be_view_for
msgid "<span>€</span>"
msgstr "<span>€</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_belgium_payslip
msgid "<strong class=\"me-2\">Eco-Vouchers:</strong>"
msgstr "<strong class=\"me-2\">Bedrag ecocheques</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_belgium_payslip
msgid "<strong class=\"me-2\">Person in Charge:</strong>"
msgstr "<strong class=\"me-2\">Persoon ten laste</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_hr_payroll_schedule_change_wizard_view_form
msgid ""
"<strong class=\"o_group_col_12\" invisible=\"not leave_type_id or "
"found_leave_allocation\" style=\"color:#ff6600;\">\n"
"                        No time off allocation has been found for this time "
"off type, no changes will occur to time off for this employee.\n"
"                    </strong>"
msgstr ""
"<strong class=\"o_group_col_12\" invisible=\"not leave_type_id or "
"found_leave_allocation\" style=\"color:#ff6600;\">\n"
"                        Er is geen verloftoewijzing gevonden voor dit soort "
"verlof, er zullen geen wijzigingen plaatsvinden in het verlof voor deze "
"werknemer.\n"
"                    </strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "<strong> Departure Date: </strong>"
msgstr "<strong> Datum van vertrek: </strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "<strong>1. Nr.</strong>"
msgstr "<strong>1. N°</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid "<strong>1. Nᵒ:</strong>"
msgstr "<strong>1. Nr:</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "<strong>2. Date of entry: </strong>"
msgstr "<strong>2. Datum van indiensttreding: </strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "<strong>3. Income debtor:</strong> <br/>"
msgstr "<strong>3. Schuldenaar van de inkomsten:</strong> <br/>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "<strong>4. Beneficiary:</strong><br/>"
msgstr "<strong>4. Geadresseerde:</strong><br/>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "<strong>4. Sender:</strong> <br/>"
msgstr "<strong>4. Verzender:</strong> <br/>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "<strong>5. National number:</strong>"
msgstr "<strong>5. Rijksregisternummer:</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<strong>Address</strong>"
msgstr "<strong>Adres</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
msgid "<strong>Amount</strong>"
msgstr "<strong>Bedrag</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid "<strong>Au:</strong>"
msgstr "<strong>Op:</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<strong>Authorized signature</strong>"
msgstr "<strong>Geautoriseerde handtekening</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
msgid "<strong>Bank Account</strong>"
msgstr "<strong>Bankrekening</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "<strong>Code</strong>"
msgstr "<strong>Code</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<strong>Company Information</strong>"
msgstr "<strong>Bedrijfsinformatie</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
msgid "<strong>Designation</strong>"
msgstr "<strong>Benoeming</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid "<strong>Du:</strong>"
msgstr "<strong>Van:</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
msgid "<strong>Email</strong>"
msgstr "<strong>E-mail</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
msgid "<strong>Employee</strong>"
msgstr "<strong>Werknemer</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<strong>Identification No</strong>"
msgstr "<strong>Identificatie nr</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<strong>Job Position</strong>"
msgstr "<strong>Functie</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid "<strong>Montant</strong>"
msgstr "<strong>Bedrag</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<strong>Name</strong>"
msgstr "<strong>Naam</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid "<strong>Nr.</strong>"
msgstr "<strong>Nr.</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
msgid "<strong>Pay holiday double complementary</strong>"
msgstr "<strong>Aanvullend dubbel vakantiegeld</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
msgid "<strong>Pay holiday double</strong>"
msgstr "<strong>Dubbel vakantiegeld</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid ""
"<strong>Pay holiday double</strong> only if the majority of vacation days\n"
"                        have not yet been taken"
msgstr ""
"<strong>Aanvullend dubbel vakantiegeld</strong> alleen als de meeste "
"vakantiedagen\n"
"                                nog niet zijn opgenomen"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<strong>Pay simple</strong>"
msgstr "<strong>Eenvoudig vakantiegeld</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
msgid "<strong>Quantity</strong>"
msgstr "<strong>Hoeveelheid</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
msgid "<strong>Rate</strong>"
msgstr "<strong>Hoeveel</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<strong>Reference</strong>"
msgstr "<strong>Referentie</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
msgid "<strong>Registration Number</strong>"
msgstr "<strong>Registratienummer</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "<strong>Salary Computation</strong>"
msgstr "<strong>Loonberekening</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid "<strong>Société:</strong> <br/>"
msgstr "<strong>Onderneming:</strong> <br/>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
msgid "<strong>Start notice period</strong>"
msgstr "<strong>Start opzegtermijn</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<strong>TOTAL</strong>"
msgstr "<strong>TOTAAL</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
msgid "<strong>Total</strong>"
msgstr "<strong>Totaal</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "<strong>Worked Days</strong>"
msgstr "<strong>Gewerkte dagen</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "<strong>Worked Time</strong>"
msgstr "<strong>Gewerkte uren</strong>"

#. module: l10n_be_hr_payroll
#: model:ir.model.constraint,message:l10n_be_hr_payroll.constraint_l10n_be_dmfa_location_unit__unique
msgid ""
"A DMFA location cannot be set more than once for the same company and "
"partner."
msgstr ""
"Een DMFA-locatie kan niet meer dan één keer worden ingesteld voor hetzelfde "
"bedrijf en dezelfde partner."

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract__has_laptop
msgid "A benefit in kind is paid when the employee uses its laptop at home."
msgstr ""
"Een voordeel van alle aard wordt betaald wanneer de werknemer zijn laptop "
"thuis gebruikt."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "A. Total: (6a + 6b + 6c + 6d, 1° + 6d + 6e, 2° + 6e)"
msgstr "A. Totaal: (6a + 6b + 6c + 6d, 1° + 6d + 6e, 2° + 6e)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "APR"
msgstr "APR"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_advantage_any_kind
msgid "ATN"
msgstr "VAA"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_inverse_atn_warrant
msgid "ATN Warrant"
msgstr "Garantie VAA"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "AUG"
msgstr "AUG"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__absence_work_entry_type_id
msgid "Absence Work Entry Type"
msgstr "Type afwezigheid"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_config_settings_view_form
msgid "Accident Insurance"
msgstr "Ongevallenverzekering"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_company__accident_insurance_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__accident_insurance_name
msgid "Accident Insurance Name"
msgstr "Naam ongevallenverzekering"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_company__accident_insurance_number
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__accident_insurance_number
msgid "Accident Insurance Number"
msgstr "Nummer ongevallenverzekering"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Accident insurance organization"
msgstr "Organisatie ongevallenverzekering"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_onss_employer
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_termination_ONSS
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_onss_employer
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_pay_onss_employer
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_thirteen_month_onss_employer
#: model:hr.salary.rule,name:l10n_be_hr_payroll.hr_salary_rule_student_onss_employer
msgid "Accounting: ONSS (Employer)"
msgstr "Boekhouding: RSZ (werkgever)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_onss_employer_basic
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_onss_employer_basic
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_onss_employer_basic
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_pay_onss_employer_basic
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_thirteen_month_onss_employer_basic
msgid "Accounting: ONSS Basic (Employer)"
msgstr "Boekhouding: RSZ basis (werkgever)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_onss_employer_cpae
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_onss_employer_cpae
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_onss_employer_cpae
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_pay_onss_employer_cpae
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_thirteen_month_onss_employer_cpae
msgid "Accounting: ONSS CPAE (Employer)"
msgstr "Boekhouding: RSZ APCB (werkgever)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_onss_employer_ffe
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_onss_employer_ffe
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_onss_employer_ffe
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_pay_onss_employer_ffe
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_thirteen_month_onss_employer_ffe
msgid "Accounting: ONSS FFE (Employer)"
msgstr "Boekhouding: RSZ FSO (werkgever)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_onss_employer_special_ffe
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_onss_employer_special_ffe
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_onss_employer_special_ffe
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_pay_onss_employer_special_ffe
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_thirteen_month_onss_employer_special_ffe
msgid "Accounting: ONSS Special FFE (Employer)"
msgstr "Boekhouding: RSZ bijzondere FSO (werkgever)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_onss_employer_temporary_unemployment
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_onss_employer_temporary_unemployment
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_onss_employer_temporary_unemployment
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_pay_onss_employer_temporary_unemployment
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_thirteen_month_onss_employer_temporary_unemployment
msgid "Accounting: ONSS Temporary Unemployment (Employer)"
msgstr "Boekhouding: RSZ tijdelijke werkloosheid (werkgever)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_onss_employer_wage_restreint
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_onss_employer_wage_restreint
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_onss_employer_wage_restreint
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_pay_onss_employer_wage_restreint
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_thirteen_month_onss_employer_wage_restreint
msgid "Accounting: ONSS Wage Restreint (Employer)"
msgstr "Boekhouding: RSZ loonmatiging (werkgever)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_salary_rule_category_owed_remuneration
msgid "Accounting: Owed Remuneration"
msgstr "Boekhouding: Verschuldige bezoldiging"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_remuneration
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_remuneration
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_salary_rule_category_remuneration
msgid "Accounting: Remuneration"
msgstr "Boekhouding: Bezoldiging"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__actual_notice_duration
msgid "Actual Notice Duration"
msgstr "Werkelijke opzeggingstermijn"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Actual number of hours worked full time"
msgstr "Werkelijk aantal voltijds gewerkte uren"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Actual number of hours worked part-time"
msgstr "Werkelijk aantal halftijds gewerkte uren"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract__wage_with_holidays
msgid ""
"Adapted salary, according to the sacrifices defined on the contract "
"(Example: Extra-legal time off, a percentage of the salary invested in a "
"group insurance, etc...)"
msgstr ""
"Aangepast loon, volgens de in het contract bepaalde keuzes (Voorbeeld: "
"extralegaal verlof, een percentage van het loon geïnvesteerd in een "
"groepsverzekering, enz.)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_10__type_treatment__2
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_45__type_treatment__2
msgid "Add"
msgstr "Toevoegen"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_additional_gross
msgid "Additional Gross"
msgstr "Aanvullend bruto"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "Additional Information"
msgstr "Aanvullende informatie"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_additional_paid
msgid "Additional Time (Paid)"
msgstr "Extra tijd (betaald)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_additional_leave
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_additional_leave
msgid "Additional Time Off"
msgstr "Extra verlof"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "Additional amount to deduct"
msgstr "Aanvullend af te trekken bedrag"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "Additional time off (european, ...)"
msgstr "Extra verlof (Europees,...)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Address"
msgstr "Adres"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Administration générale de la Fiscalité"
msgstr "Algemene Administratie van de Fiscaliteit"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Adresse e-mail :"
msgstr "E-mailadres:"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_payslip.py:0
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_after_contract_public_holiday
msgid "After Contract Public Holidays"
msgstr "Feestdagen na contract"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__employee_age
msgid "Age of Employee"
msgstr "Leeftijd van de werknemer"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__aggregation_level
msgid "Aggregation Level"
msgstr "Aggregatieniveau"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "Aggregation Level:"
msgstr "Aggregatieniveau:"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__alloc_employee_ids
msgid "Alloc Employee"
msgstr "Toewijzing werknemer"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__alloc_paid_leave_id
msgid "Alloc Paid Leave"
msgstr "Toewijzing betaald verlof"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Allocation Time Off"
msgstr "Toewijzing verlof"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__time_off_allocation_n_ids
msgid "Allocations N"
msgstr "Toelagen N"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_onss_restructuring
msgid "Allow ONSS Reduction for Restructuring"
msgstr "RSZ-verlaging voor herstructurering toestaan"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "Ambulatory Insurance"
msgstr "Ambulante verzekering"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_ambulatory_insurance_notes
msgid "Ambulatory Insurance: Additional Info"
msgstr "Ambulante verzekering: Aanvullende informatie"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_ambulatory_insured_children
msgid "Ambulatory: # Insured Children < 19 y/o"
msgstr "Ambulante: # verzekerde kinderen < 19 jaar"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_ambulatory_insured_adults
msgid "Ambulatory: # Insured Children >= 19 y/o"
msgstr "Ambulante: # verzekerde kinderen >= 19 jaar"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_ambulatory_amount_per_adult
msgid "Ambulatory: Amount per Adult"
msgstr "Ambulante: Bedrag per volwassene"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_ambulatory_amount_per_child
msgid "Ambulatory: Amount per Child"
msgstr "Ambulante: Bedrag per kind"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_ambulatory_insurance_amount
msgid "Ambulatory: Insurance Amount"
msgstr "Ambulante: Bedrag verzekering"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_ambulatory_insured_spouse
msgid "Ambulatory: Insured Spouse"
msgstr "Ambulante: Verzekerde echtgeno(o)t(e)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__amount
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__amount
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_line_wizard__amount
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_line_wizard__amount
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_double_pay_recovery_line_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "Amount"
msgstr "Bedrag"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "Amount of the employer's intervention"
msgstr "Bedrag van de tussenkomst van de werkgever"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__l10n_be_holiday_pay_recovered_n
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__l10n_be_holiday_pay_recovered_n1
msgid ""
"Amount of the holiday pay paid by the previous employer already recovered."
msgstr ""
"Het bedrag van de door de vorige werkgever betaalde vakantiegeld is reeds "
"teruggevorderd."

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__l10n_be_holiday_pay_to_recover_n
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__l10n_be_holiday_pay_to_recover_n1
msgid "Amount of the holiday pay paid by the previous employer to recover."
msgstr ""
"Het bedrag van het door de vorige werkgever betaald vakantiegeld om terug te "
"vorderen."

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__hospital_insurance_amount_per_adult
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "Amount per Adult"
msgstr "Bedrag per volwassene"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__hospital_insurance_amount_per_child
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "Amount per Child"
msgstr "Bedrag per kind"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract__meal_voucher_amount
msgid ""
"Amount the employee receives in the form of meal vouchers per worked day."
msgstr ""
"Bedrag dat de werknemer ontvangt in de vorm van maaltijdcheques per gewerkte "
"dag."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_employee_form__l10n_be_view_for
msgid "Amount to recover"
msgstr "In te vorderen bedrag"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_double_pay_recovery_wizard_view_form
msgid "Amounts to Recover"
msgstr "Terug te vorderen bedragen"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_annual_salary_revalued
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_termination
msgid "Annual salary revalued"
msgstr "Jaarsalaris geherwaardeerd"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_annual_variable_salary
msgid "Annual variable salary"
msgstr "Jaarlijks variabel salaris"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Another reason"
msgstr "Andere reden"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Anticipated Holiday Pay Retenue"
msgstr "Voorziene inhouding vakantiegeld"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__month__4
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__month__4
msgid "April"
msgstr "April"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_asignment_salary
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_asignment_salary
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_asignment_salary
msgid "Assignment of Salary"
msgstr "Loonbeslag"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "At the end of the exercise"
msgstr "Aan het einde van het boekjaar"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_attachment_salary
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_attachment_salary
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_attachment_salary
msgid "Attachment of Salary"
msgstr "Loonbeslag"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__month__8
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__month__8
msgid "August"
msgstr "Augustus"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Average number of full-time workers"
msgstr "Gemiddeld aantal voltijdse werknemers"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Average number of part-time workers"
msgstr "Gemiddeld aantal deeltijdse werknemers"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Average number of total workers or FTEs"
msgstr "Gemiddeld aantal totale werknemers of voltijds equivalenten"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__unpaid_average_remunaration_n
msgid "Average remuneration by month current year"
msgstr "Gemiddelde vergoeding per maand huidig jaar"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__unpaid_average_remunaration_n1
msgid "Average remuneration by month previous year"
msgstr "Gemiddelde vergoeding per maand vorig jaar"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__unpaid_average_remunaration_n
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__unpaid_average_remunaration_n1
msgid "Average remuneration for the 12 months preceding unpaid leave"
msgstr ""
"Gemiddelde vergoeding voor de 12 maanden voorafgaand aan onbetaald verlof"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "Bachelors"
msgstr "Bachelor"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_double_complementary_basic
msgid "Basic Complementary Double Holiday"
msgstr "Basis aanvullend dubbel vakantiegeld"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_basic_pay_simple
msgid "Basic Pay Simple"
msgstr "Basis eenvoudig inkomen"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_basic
msgid "Basic Salary"
msgstr "Basisloon"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_double_basic
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_pay_double_basic
msgid "Basic double"
msgstr "Basis dubbel"

#. module: l10n_be_hr_payroll
#: model:hr.departure.reason,name:l10n_be_hr_payroll.departure_freelance
msgid "Became Freelance"
msgstr "Freelance geworden"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_config_settings_view_form
msgid "Belgian Localization"
msgstr "Belgische lokalisatie"

#. module: l10n_be_hr_payroll
#: model:ir.actions.server,name:l10n_be_hr_payroll.ir_cron_schedule_change_allocation_ir_actions_server
msgid "Belgian Payroll: Update time off allocations on schedule change"
msgstr ""
"Belgische loonadministratie: Verloftoewijzingen bijwerken in geval van "
"roosterwijziging"

#. module: l10n_be_hr_payroll
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_l10n_be_hr_payroll_configuration
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_reporting_l10n_be
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "Belgium"
msgstr "België"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_social_balance_sheet
msgid "Belgium: Social Balance Sheet"
msgstr "België: Sociale balans"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_social_security_certificate
msgid "Belgium: Social Security Certificate"
msgstr "België: Certificaat Sociale Zekerheid"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Beneficiary holdings"
msgstr "Begunstigde bedrijven"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_work_entry_daily_benefit_report__benefit_name
msgid "Benefit Name"
msgstr "Naam voordeel"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_company_car
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_company_car_2
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_company_car_annual
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_pfi_company_car
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_pfi_company_car_2
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract__car_atn
msgid "Benefit in Kind (Company Car)"
msgstr "Voordeel alle aard (Bedrijfswagen)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_atn_internet
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_atn_internet_2
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_pfi_atn_internet
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_pfi_atn_internet_2
msgid "Benefit in Kind (Internet)"
msgstr "Voordeel alle aard (internet)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_atn_laptop
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_atn_laptop_2
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_pfi_atn_laptop
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_pfi_atn_laptop_2
msgid "Benefit in Kind (Laptop)"
msgstr "Voordeel alle aard (Laptop)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_atn_mobile
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_atn_mobile_2
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_pfi_atn_mobile
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_pfi_atn_mobile_2
msgid "Benefit in Kind (Phone Subscription)"
msgstr "Voordeel alle aard (telefoonabonnement)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_report__l10n_be_atn_deduction
msgid "Benefit in Kind Deductions (All)"
msgstr "Aftrek voordelen alle aard (alle)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Benefit in kind deduction"
msgstr "Aftrek voordeel alle aard"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_config_settings_view_form
msgid "Benefits"
msgstr "Voordelen"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "Benefits In Kind (Company Car)"
msgstr "Voordeel alle aard (Bedrijfswagen)"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "Benefits In Kind Deduction"
msgstr "Aftrek van voordelen alle aard"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "Benefits In Kind Submitted To ONSS"
msgstr "Voordelen alle aard ingediend bij de RSZ"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "Benefits In Kind Without ONSS"
msgstr "Voordelen alle aard zonder RSZ"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Benefits in kind (Company Car)"
msgstr "Voordelen alle aard (bedrijfswagen)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Benefits in kind and bonuses"
msgstr "Voordelen van alle aard en bonussen"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Benefits in kind submitted to ONSS"
msgstr "Voordelen alle aard ingediend bij de RSZ"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Benefits in kind without ONSS"
msgstr "Voordelen alle aard zonder RSZ"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__has_bicycle
msgid "Bicycle to work"
msgstr "Fietsen naar het werk"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "Boulevard de Waterloo"
msgstr "Boulevard de Waterloo"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_breast_feeding
msgid "Breastfeeding Break"
msgstr "Borstvoedingspauze"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "Business Closure Fund Cotisation"
msgstr ""
"Fonds tot vergoeding van de in geval van sluiting van ondernemingen "
"ontslagen werknemers"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Business closure fund cotisation"
msgstr ""
"Fonds tot vergoeding van de in geval van sluiting van ondernemingen "
"ontslagen werknemers"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
msgid "By"
msgstr "Door"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
msgid "By Contract Type"
msgstr "Per contracttype"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_social_security_certificate__aggregation_level__department
msgid "By Department"
msgstr "Per afdeling"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_social_security_certificate__aggregation_level__employee
msgid "By Employee"
msgstr "Per werknemer"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
msgid "By Gender"
msgstr "Per geslacht"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "By contract type"
msgstr "Per contracttype"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "By gender"
msgstr "Per geslacht"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "By professional category"
msgstr "Per beroepscategorie"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "By reason for termination of contract"
msgstr "Per reden voor beëindiging van het contract"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
msgid "By reason for termination of the contract"
msgstr "Per reden voor beëindiging van het contract"

#. module: l10n_be_hr_payroll
#: model:hr.contract.type,name:l10n_be_hr_payroll.l10n_be_contract_type_cdd
msgid "CDD"
msgstr "Arbeidsovereenkomst voor bepaalde tijd"

#. module: l10n_be_hr_payroll
#: model:hr.contract.type,name:l10n_be_hr_payroll.l10n_be_contract_type_cdi
msgid "CDI"
msgstr "Arbeidsovereenkomst voor onbepaalde tijd"

#. module: l10n_be_hr_payroll
#: model:hr.contract.type,name:l10n_be_hr_payroll.l10n_be_contract_type_cip
msgid "CIP"
msgstr "BIS"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_job_view_form
msgid "CP200 Category"
msgstr "PC 200 Categorie"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_december_slip_wizard
msgid "CP200: December Slip Computation"
msgstr "PC 200: Berekening loonstrook december"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_double_pay_recovery_line
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_double_pay_recovery_line_wizard
msgid "CP200: Double Pay Recovery Line Wizard"
msgstr "PC 200: Wizard invorderingsregel dubbel vakantiegeld"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_double_pay_recovery_wizard
msgid "CP200: Double Pay Recovery Wizard"
msgstr "PC 200: Wizard invordering dubbel vakantiegeld"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payroll_generate_warrant_payslips_view_form
msgid ""
"CSV format: you may edit it directly with your favorite spreadsheet "
"software, the rightmost column (value) contains the commission value over 3 "
"months. When you're done, reimport the file to generate the commission "
"payslips with the accurate commissions."
msgstr ""
"CSV-formaat: je kan het rechtstreeks bewerken met je favoriete spreadsheet-"
"software, de meest rechtse kolom (waarde) bevat de provisiewaarde over 3 "
"maanden. Als je klaar bent, importeer je het bestand opnieuw om de "
"provisieloonstroken met de juiste commissies te genereren."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Cadre I. - Calcul du Précompte Mobilier (Pr.M) à payer"
msgstr "Kader I. - Berekening van de te betalen roerende voorheffing (RV)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Cadre II. - Bénéficiaire(s) des revenus"
msgstr "Kader II. - Begunstigde(n) van de inkomsten"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Calculation Basis"
msgstr "Berekeningsbasis"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_10__type_treatment__3
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_45__type_treatment__3
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payroll_generate_warrant_payslips_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_hr_payroll_employee_lang_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_hr_payroll_schedule_change_wizard_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_notice
msgid "Cancel"
msgstr "Annuleren"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_canteen
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_canteen_cost
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__default_l10n_be_canteen_cost
msgid "Canteen Cost"
msgstr "Kantinekosten"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Canteen Costs"
msgstr "Kantinekosten"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__capped_amount_34
msgid "Capped Amount"
msgstr "Maximaal bedrag"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__car_atn
msgid "Car BIK"
msgstr "VAA Auto"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_job__l10n_be_scale_category__a
msgid "Category A"
msgstr "Categorie A"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_job__l10n_be_scale_category
msgid ""
"Category A - Executive functions:\n"
"Included in this class are functions characterized by performing a limited "
"number of simple and repetitive tasks. For example: the worker exclusively "
"responsible for typing.\n"
"\n"
"Category B - Support functions.\n"
"Included in this class are functions characterized by making a contribution "
"to the achievement of a larger mission. For example: the administrative "
"employee or the receptionist.\n"
"\n"
"Category C - Management functions.\n"
"Included in this class are functions characterized by carrying out a "
"complete set of tasks which, together, constitute one and the same mission. "
"For example: the personnel administration employee or the PC technician.\n"
"\n"
"Category D - Advisory functions.\n"
"Included in this class are functions characterized by monitoring and "
"developing the same professional process within the framework of a specific "
"objective. For example: the programmer, accountant or consultant"
msgstr ""
"Categorie A - Executieve functies:\n"
"Tot deze categorie behoren functies die gekenmerkt worden door het uitvoeren "
"van een beperkt aantal eenvoudige en repetitieve taken. Bijvoorbeeld: de "
"werknemers die uitsluitend verantwoordelijk is voor het typen.\n"
"\n"
"Categorie B - Ondersteunende functies:\n"
"Tot deze categorie behoren functies die gekenmerkt worden door het leveren "
"van een bijdrage aan de verwezenlijking van een grotere opdracht. "
"Bijvoorbeeld: de administratieve medewerker of de receptioniste.\n"
"\n"
"Categorie C - Leidinggevende functies:\n"
"Tot deze categorie behoren functies die gekenmerkt worden door het uitvoeren "
"van een volledige reeks taken die samen eenzelfde opdracht vormen. "
"Bijvoorbeeld: de medewerker van de personeelsadministratie of de pc-"
"technicus.\n"
"\n"
"Categorie D - Adviesfuncties:\n"
"Tot deze categorie behoren functies die gekenmerkt orden door het "
"controleren en ontwikkelen van hetzelfde beroepsproces in het kader van een "
"specifieke doelstelling. Bijvoorbeeld: de programmeur, boekhouder of adviseur"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_job__l10n_be_scale_category__b
msgid "Category B"
msgstr "Categorie B"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_job__l10n_be_scale_category__c
msgid "Category C"
msgstr "Categorie C"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_job__l10n_be_scale_category__d
msgid "Category D"
msgstr "Categorie D"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_certificate_certificate
msgid "Certificate"
msgstr "Certificaat"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__certificate
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx_line__certificate
msgid "Certificate Level"
msgstr "Certificaat niveau"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "Certificate of Holidays"
msgstr "Vakantieattest"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_company__onss_certificate_id
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_config_settings__onss_certificate_id
msgid "Certificate to allow access to batch declarations"
msgstr "Certificaat om toegang te krijgen tot een partij verklaringen"

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_hr_payroll_employee_lang_wizard_action
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_hr_payroll_employee_lang_wizard
msgid "Change Employee Language"
msgstr "Taal werknemer veranderen"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_hr_payroll_employee_lang_wizard_line
msgid "Change Employee Language Line"
msgstr "Taalregel werknemer veranderen"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_hr_payroll_schedule_change_wizard
msgid "Change contract working schedule"
msgstr "Werkschema contract veranderen"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "Checkout"
msgstr "Verificatie"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_head_child_alw
msgid "Child Allowance Belgium"
msgstr "Kinderbijslag België"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_child_support
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_child_support
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_child_support
msgid "Child Support"
msgstr "Kinderbijslag"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "Circ. administrative 19.03.1982 Ci. RH. 241/315.785"
msgstr "Administratieve circulaire 19.03.1982 Ci.RH.241/315.785"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_dmfa__file_type__t
msgid "Circuit Test (T)"
msgstr "Circuittest (T)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_december_slip_wizard_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_double_pay_recovery_wizard_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_group_insurance_wizard_view_form
msgid "Close"
msgstr "Sluiten"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa_location_unit__code
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Code"
msgstr "Code"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_fixed_commission
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__commission_on_target
msgid "Commission"
msgstr "Commissie"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips_line__commission_amount
msgid "Commission Amount"
msgstr "Commissiebedrag"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__default_commission_on_target
msgid "Commission on Target"
msgstr "Commissie voor streefcijfer"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.salary_rule_category_commissions
msgid "Commissions"
msgstr "Commissies"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.salary_rule_category_commissions_adjustment
msgid "Commissions Adjustment"
msgstr "Aanpassing commissies"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_res_company
msgid "Companies"
msgstr "Bedrijven"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx_line__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa_location_unit__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_wizard__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_wizard__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__company_id
msgid "Company"
msgstr "Bedrijf"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__company_car_total_depreciated_cost
msgid "Company Car Total Depreciated Cost"
msgstr "Bedrijfswagen Totaal afgeschreven kosten"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_config_settings_view_form
msgid "Company Information"
msgstr "Bedrijfsgegevens"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_company__l10n_be_company_number
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__l10n_be_company_number
msgid "Company Number"
msgstr "Ondernemingsnummer"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Company number"
msgstr "Ondernemingsnummer"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_double_complementary_already_paid
msgid "Complementary Double Holidays (Already Paid)"
msgstr "Aanvullend dubbel vakantiegeld (reeds betaald)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_double_complementary_december
msgid "Complementary Double Holidays (Lost due to working time reduction)"
msgstr "Aanvullend dubbel vakantiegeld (verlies door arbeidstijdverkorting)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payslip_view_form_inherit_double_pay
msgid "Compute December Holiday Pay"
msgstr "Vakantiegeld december berekenen"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payslip_view_form_inherit_double_pay
msgid "Compute Double Pay Recovery"
msgstr "Invordering dubbel vakantiegeld berekenen"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_res_config_settings
msgid "Config Settings"
msgstr "Config-instellingen"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_config_settings_view_form
msgid "Configure Default Values for Belgian Benefits"
msgstr "Standaardwaarden voor Belgische voordelen instellen"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_config_settings_view_form
msgid "Configure ONSS codes"
msgstr "ONSS-codes configureren"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_config_settings_view_form
msgid "Configure your company accident insurance"
msgstr "Stel je bedrijfsongevallenverzekering in"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_hr_payroll_employee_lang_view_form
msgid "Confirm"
msgstr "Bevestigen"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__dependent_children
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__dependent_children
msgid "Considered number of dependent children"
msgstr "Beschouwd aantal kinderen ten laste"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__dependent_juniors
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__dependent_juniors
msgid "Considered number of dependent juniors"
msgstr "Beschouwd aantal junioren ten laste"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__dependent_seniors
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__dependent_seniors
msgid "Considered number of dependent seniors"
msgstr "Beschouwd aantal senioren ten laste"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__contract_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__contract_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__contract_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_schedule_change_allocation__contract_id
msgid "Contract"
msgstr "Contract"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__contract_next_year_id
msgid "Contract Active Next Year"
msgstr "Actief contract volgend jaar"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_config_settings_view_form
msgid "Contract Number"
msgstr "Contractnummer"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_contract_employee_report
msgid "Contract and Employee Analysis Report"
msgstr "Rapport contract- en werknemersanalyse"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Contract for the execution of a clearly defined work"
msgstr "Contrat voor het uitvoeren van een duidelijk omschreven werk"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_contract_history
msgid "Contract history"
msgstr "Contractgeschiedenis"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Contribution Type"
msgstr "Soort bijdrage"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Contribution:"
msgstr "Bijdrage:"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Contributions paid and payments to collective funds"
msgstr "Contributies betaald en betalingen aan collectieve fondsen"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_corona
msgid "Corona Unemployment"
msgstr "Corona werkloosheid"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_employee.py:0
msgid ""
"Count of dependent people/children or disabled dependent people/children "
"must be positive."
msgstr ""
"Aantal personen/kinderen ten laste of gehandicapte personen/kinderen ten "
"laste moet positief zijn."

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_employee.py:0
msgid ""
"Count of disabled dependent people/children must be less or equal to the "
"number of dependent people/children."
msgstr ""
"Aantal gehandicapte personen/kinderen ten laste moet kleiner of gelijk zijn "
"aan het aantal personen/kinderen ten laste."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "Create 274.XX Sheets"
msgstr "274.XX fiches aanmaken"

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_281_10_action
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_10_form_wizard
msgid "Create 281.10 Form"
msgstr "Formulier 281.10 maken"

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_281_45_action
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_45_form_wizard
msgid "Create 281.45 Form"
msgstr "Formulier 281.45 maken"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_10_form_wizard
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_45_form_wizard
msgid "Create XML"
msgstr "XML maken"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_hr_payroll_schedule_change_wizard_view_form
msgid "Create new contract and adapt time off allocation"
msgstr "Een nieuw contract maken en de toewijzing van het verlof aanpassen"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips_line__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx_line__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa_location_unit__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_line_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_line_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard_line__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_schedule_change_allocation__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__create_uid
msgid "Created by"
msgstr "Gemaakt door"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips_line__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx_line__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa_location_unit__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_line_wizard__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_wizard__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_line_wizard__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_wizard__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard_line__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_schedule_change_allocation__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__create_date
msgid "Created on"
msgstr "Gemaakt op"

#. module: l10n_be_hr_payroll
#: model:hr.leave.type,name:l10n_be_hr_payroll.holiday_type_credit_time
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_credit_time
msgid "Credit Time"
msgstr "Tijdskrediet"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_hr_payroll_schedule_change_wizard.py:0
msgid "Credit time contract"
msgstr "Contract tijdskrediet"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips_line__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx_line__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_line_wizard__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_wizard__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_line_wizard__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_wizard__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__currency_id
msgid "Currency"
msgstr "Valuta"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Currency:"
msgstr "Valuta:"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__months_count_description
msgid "Current Occupation Duration (Description)"
msgstr "Huidige bezettingsduur (omschrijving)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__months_count
msgid "Current Occupation Duration (Months)"
msgstr "Huidige bezettingsduur (maanden)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_schedule_change_allocation__current_resource_calendar_id
msgid "Current Resource Calendar"
msgstr "Huidige kalender"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__resource_calendar_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__current_resource_calendar_id
msgid "Current Working Schedule"
msgstr "Huidig werkschema"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_hr_payroll_schedule_change_wizard.py:0
msgid "Current contract is finished before the end of the new contract."
msgstr ""
"Het huidig contract is afgelopen voor het einde van het nieuwe contract."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Current work regime"
msgstr "Huidige arbeidsregeling"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Cycle Allowance"
msgstr "Fietstoeslag"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_cycle_transportation
msgid "Cycle Transportation (Days Count)"
msgstr "Verplaatsing per fiets (aantal dagen)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "DATE D’ATTRIBUTION OU DE MISE EN PAIEMENT DES REVENUS :"
msgstr "DATUM VAN TOEWIJZING OF BETALING VAN INKOMSTEN:"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "DEC"
msgstr "DEC"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "DECLARATION AU PRECOMPTE MOBILIER (Pr.M)"
msgstr "AANGIFTE ROERENDE VOORHEFFING (RV)"

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.hr_payslip_report_action_dmfa
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_hr_payroll_dmfa
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_xml_report
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_view_form
msgid "DMFA"
msgstr "DMFA"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_company__dmfa_employer_class
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__dmfa_employer_class
msgid "DMFA Employer Class"
msgstr "DMFA Employer Class"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_view_tree
msgid "DMFA Reports"
msgstr "DMFA Reports"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_work_entry_type__dmfa_code
msgid "DMFA code"
msgstr "DMFA code"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_dmfa
msgid "DMFA xml report"
msgstr "DMFA xml report"

#. module: l10n_be_hr_payroll
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_l10n_be_dmfa_location_unit
msgid "DMFA: Work Locations"
msgstr "DMFA: Werklocaties"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Data for:"
msgstr "Gegevens voor:"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_work_entry_daily_benefit_report_search
msgid "Date"
msgstr "Datum"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_wizard__date_end
msgid "Date End"
msgstr "Einddatum"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__date_start
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_wizard__date_from
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet__date_from
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__date_from
msgid "Date From"
msgstr "Datum vanaf"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_wizard__date_start
msgid "Date Start"
msgstr "Begindatum"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__date_end
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_wizard__date_to
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet__date_to
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__date_to
msgid "Date To"
msgstr "Datum t/m"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Date de réception de la déclaration :"
msgstr "Ontvangstdatum van de aangifte:"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Date of birth"
msgstr "Geboortedatum"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_work_entry_daily_benefit_report__day
msgid "Day"
msgstr "Dag"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip__private_car_missing_days
msgid "Days Not Granting Private Car Reimbursement"
msgstr "Dagen waarop geen privé-autovergoeding wordt toegekend"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip__representation_fees_missing_days
msgid "Days Not Granting Representation Fees"
msgstr "Dagen waarop geen vertegenwoordigingskosten worden toegekend"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Days Per Week:"
msgstr "Dagen per week:"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__unpaid_time_off_n
msgid "Days Unpaid time off current year"
msgstr "Dagen onbetaald verlof huidig jaar"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__unpaid_time_off_n1
msgid "Days Unpaid time off previous year"
msgstr "Dagen onbetaald verlof vorig jaar"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__month__12
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__month__12
msgid "December"
msgstr "December"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_december_slip_wizard_view_form
msgid "December Pay"
msgstr "Loon december"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_december_slip_wizard_view_form
msgid "December Slip"
msgstr "Loonstrook december"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__notice_respect
msgid ""
"Decides whether the employee will still work during his notice period or not."
msgstr ""
"Bepaalt of de werknemer tijdens zijn opzegperiode nog zal werken of niet."

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_dmfa__file_type__s
msgid "Declaration Test (S)"
msgstr "Verklaring test (S)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__declaration_type
msgid "Declaration Type"
msgstr "Type verklaring"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__line_ids
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__line_ids
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account__line_ids
msgid "Declarations"
msgstr "Verklaringen"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__deducted_amount_32
msgid "Deducted Amount 32"
msgstr "Afgetrokken bedrag 32"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__deducted_amount_33
msgid "Deducted Amount 33"
msgstr "Afgetrokken bedrag 33"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__deducted_amount_34
msgid "Deducted Amount 34"
msgstr "Afgetrokken bedrag 34"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_deduction
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_deduction
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_deduction
msgid "Deduction"
msgstr "Aftrek"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__presence_work_entry_type_id
msgid "Default Work Entry Type"
msgstr "Standaard type werkinvoer"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__company_calendar
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__company_calendar
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__company_calendar
msgid "Default Working Hours"
msgstr "Standaard werkuren"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Demo Name"
msgstr "Naam demo"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__department_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__department_id
msgid "Department"
msgstr "Afdeling"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__departure_date
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_notice
msgid "Departure Date"
msgstr "Datum van vertrek"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__departure_description
msgid "Departure Description"
msgstr "Vertrek omschrijving"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_departure_reason
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__leaving_type_id
msgid "Departure Reason"
msgstr "Reden vertrek"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Departure date"
msgstr "Datum van vertrek"

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.departure_holiday_wizard_action
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_l10n_be_hr_payroll_holiday_attests
msgid "Departure: Holiday Attests"
msgstr "Vertrek: vakantieattesten"

#. module: l10n_be_hr_payroll
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_l10n_be_hr_payroll_notice
msgid "Departure: Notice period"
msgstr "Vertrek: Opzegperiode"

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.departure_notice_wizard_action
msgid "Departure: Notice period and payslip"
msgstr "Vertrek: Opzegtermijn en loonstrook"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Departures"
msgstr "Vertrekken"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account__name
msgid "Description"
msgstr "Omschrijving"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__disabled
msgid "Disabled"
msgstr "Gehandicapt"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__disabled_children_bool
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__disabled_children_bool
msgid "Disabled Children"
msgstr "Gehandicapte kinderen"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__disabled_spouse_bool
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__disabled_spouse_bool
msgid "Disabled Spouse"
msgstr "Gehandicapte echtgeno(o)t(e)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payroll_allocating_paid_time_off_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_eco_vouchers_wizard_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_balance_sheet_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_security_certificate_view_form
msgid "Discard"
msgstr "Negeren"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Dismissal"
msgstr "Ontslag"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_notice
msgid "Dismissal Date"
msgstr "Ontslagdatum"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_job__display_l10n_be_scale
msgid "Display L10N Be Scale"
msgstr "L10N Be schaal weergeven"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips_line__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx_line__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa_location_unit__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_line_wizard__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_wizard__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_line_wizard__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_wizard__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard_line__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_schedule_change_allocation__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_work_entry_daily_benefit_report__display_name
msgid "Display Name"
msgstr "Schermnaam"

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,name:l10n_be_hr_payroll.action_report_dmfa
msgid "DmfA"
msgstr "DmfA"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "DmfA Declaration ("
msgstr "DmfA aangifte ("

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__dmfa_go_filename
msgid "Dmfa Go Filename"
msgstr "Dmfa Go Bestandsnaam"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__dmfa_pdf_filename
msgid "Dmfa Pdf Filename"
msgstr "Dmfa Pdf bestandsnaam"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__dmfa_signature_filename
msgid "Dmfa Signature Filename"
msgstr "Dmfa handtekening bestandsnaam"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__dmfa_xml_filename
msgid "Dmfa Xml Filename"
msgstr "Dmfa Xml-bestandsnaam"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "Doctors / Civil Engineers"
msgstr "Dokters / Burgerlijk Ingenieurs"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__documents_count
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__documents_count
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account__documents_count
msgid "Documents Count"
msgstr "Aantal documenten"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__documents_enabled
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__documents_enabled
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account__documents_enabled
msgid "Documents Enabled"
msgstr "Documenten beschikbaar"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid ""
"Domicile, siège social ou siège du principal établissement administratif "
"(adresse complète) :"
msgstr ""
"Woonplaats, vennootschapszetel of zetel van het hoofdkantoor (volledig "
"adres):"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__state__done
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__state__done
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_social_balance_sheet__state__done
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_social_balance_sheet__state_xlsx__done
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_social_security_certificate__state__done
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_social_security_certificate__state_xlsx__done
msgid "Done"
msgstr "Gereed"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.l10n_be_double_december_category
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__double_december_pay
msgid "Double December Pay"
msgstr "Dubbel loon december"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_double_december_basic
msgid "Double December Pay Basic"
msgstr "Dubbel basisloon december"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_double_december_gross
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.l10n_be_double_december_category_gross
msgid "Double December Pay Gross"
msgstr "Dubbel brutoloon december"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_double_december_net
msgid "Double December Pay Net"
msgstr "Dubbel nettoloon december"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_double_december_pp
msgid "Double December Pay Withholding Tax"
msgstr "Bronbelasting dubbel loon december"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Double Holiday"
msgstr "Dubbel vakantiegeld"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_double_already_paid
msgid "Double Holiday (Already Paid)"
msgstr "Dubbel vakantiegeld (reeds betaald)"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Double Holiday Gross"
msgstr "Bruto dubbel vakantiegeld"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_head_dp
msgid "Double Holiday Pay"
msgstr "Dubbel vakantiegeld"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_double_pay_december
msgid "Double Holiday Pay (Lost due to working time reduction)"
msgstr "Dubbel vakantiegeld (verlies door arbeidstijdverkorting)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Double Holiday Pay Global Amount:"
msgstr "Totaalbedrag dubbel vakantiegeld:"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Double Holiday Pay Global Contributions:"
msgstr "Globale bijdragen dubbel vakantiegeld:"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__double_holiday_n
msgid "Double Holiday Pay N"
msgstr "Dubbel vakantiegeld N"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__double_holiday_n1
msgid "Double Holiday Pay N-1"
msgstr "Dubbel vakantiegeld N-1"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_double_holiday_pay_recovery
msgid "Double Holiday Pay Recovery"
msgstr "Invordering dubbel vakantiegeld"

#. module: l10n_be_hr_payroll
#: model:hr.payroll.structure,payslip_name:l10n_be_hr_payroll.hr_payroll_structure_cp200_double_holiday
msgid "Double Holidays Slip"
msgstr "Loonstrook dubbel vakantiegeld"

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_december_slip_wizard_action
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_double_pay_recovery_wizard_action
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_double_pay_recovery_wizard_view_form
msgid "Double Pay Recovery Computation"
msgstr "Berekening invordering dubbel vakantiegeld"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__double_pay_to_recover
msgid "Double Pay To Recover"
msgstr "Terug te vorderen dubbel vakantiegeld"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_273S_view_form
msgid "Download the 273S PDF file:"
msgstr "Het PDF-bestand 273S downloaden:"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "Download the 274.XX PDF file:"
msgstr "Het PDF-bestand 274.XX downloaden:"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_10_form_wizard
msgid "Download the 281.10 XML file:"
msgstr "Download het XML-bestand van 281.10:"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_45_form_wizard
msgid "Download the 281.45 XML file:"
msgstr "Het XML-bestand 281.45 downloaden:"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_balance_sheet_view_form
msgid "Download the Social Balance Sheet PDF file:"
msgstr "Het PDF-bestand sociale balans downloaden:"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_balance_sheet_view_form
msgid "Download the Social Balance Sheet XLSX file:"
msgstr "Het XLSX-bestand sociale balans downloaden:"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_security_certificate_view_form
msgid "Download the Social Security Certificate PDF file:"
msgstr "Het PDF-bestand certificaat sociale zekerheid downloaden:"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_security_certificate_view_form
msgid "Download the Social Security Certificate XLSX file:"
msgstr "Het XLSX-bestand certificaat sociale zekerheid downloaden:"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "Download the XLSX details file:"
msgstr "Het XLSX detailbestand downloaden:"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_273S_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "Download the XML Export file:"
msgstr "Het XML-exportbestand downloaden:"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_payroll_generate_warrant_payslips__state__draft
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__state__draft
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__state__draft
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_social_balance_sheet__state__draft
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_social_balance_sheet__state_xlsx__draft
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_social_security_certificate__state__draft
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_social_security_certificate__state_xlsx__draft
msgid "Draft"
msgstr "Concept"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "During the exercise"
msgstr "Tijdens het boekjaar"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "Early Holiday Pay"
msgstr "Vroegtijdig vakantiegeld"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Early holiday pay"
msgstr "Vroegtijdig vakantiegeld"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_eco_checks
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__eco_checks
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__default_eco_checks
msgid "Eco Vouchers"
msgstr "Ecocheques"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_eco_vouchers_wizard.py:0
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_eco_vouchers_wizard_action
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_l10n_be_eco_vouchers_wizard
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payslip_run_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_eco_vouchers_wizard_view_form
msgid "Eco-Vouchers"
msgstr "Ecocheques"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_eco_vouchers_line_wizard
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_eco_vouchers_wizard
msgid "Eco-Vouchers Wizard"
msgstr "Assistent ecocheques"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_belgium_light_payslip
msgid "Eco-Vouchers:"
msgstr "Ecocheques"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_economic_unemployment
msgid "Economic Unemployment"
msgstr "Economische werkloosheid"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_individual_account.py:0
msgid "Ecovouchers"
msgstr "Ecocheques"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_training_time_off
msgid "Educational Time Off"
msgstr "Educatief verlof"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_schedule_change_allocation__effective_date
msgid "Effective Date"
msgstr "Ingangsdatum"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "Eghezee"
msgstr "Eghezee"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_employee
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__employee_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips_line__employee_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__employee_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__employee_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx_line__employee_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__employee_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__employee_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__employee_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_line_wizard__employee_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_line_wizard__employee_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard_line__employee_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__employee_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_work_entry_daily_benefit_report__employee_id
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_alloc_employee_view_tree
msgid "Employee"
msgstr "Werknemer"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_contract
msgid "Employee Contract"
msgstr "Werknemerscontract"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_notice
msgid "Employee Departure"
msgstr "Vertrek van werknemer"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Employee Departure - Holiday Attests"
msgstr "Vertrek van de werknemer - Vakantietesten"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Employee Information"
msgstr "Informatie werknemer"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Employee Name"
msgstr "Naam werknemer"

#. module: l10n_be_hr_payroll
#: model:hr.payroll.structure,payslip_name:l10n_be_hr_payroll.hr_payroll_structure_cp200_reimbursement
msgid "Employee Reimbursement"
msgstr "Terugbetaling werknemer"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_payslip_employee_depature_notice__notice_respect__without
msgid "Employee doesn't work during his notice period"
msgstr "Werknemer vertrekt voor opzegtermijn"

#. module: l10n_be_hr_payroll
#: model:hr.payroll.dashboard.warning,name:l10n_be_hr_payroll.hr_payroll_dashboard_warning_sick_more_than_30_days
msgid "Employee on Mutual Health (> 30 days Illness)"
msgstr "Werknemer bij ziekenfonds (> 30 dagen ziekte)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_payslip_employee_depature_notice__notice_respect__with
msgid "Employee works during his notice period"
msgstr "Werknemer vertrekt na opzegtermijn"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_payslip_employee_depature_notice__notice_respect__partial
msgid "Employee works partially during his notice period"
msgstr "Werknemer werkt gedeeltelijk tijdens zijn opzeggingstermijn"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__wage
msgid "Employee's monthly gross wage for the new contract."
msgstr "Het maandelijks brutoloon van de werknemer voor het nieuwe contract."

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__resource_calendar_id
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__current_resource_calendar_id
msgid ""
"Employee's working schedule.\n"
"        When left empty, the employee is considered to have a fully flexible "
"schedule, allowing them to work without any time limit, anytime of the "
"week.\n"
"        "
msgstr ""
"Werkrooster van de werknemer.\n"
"        Indien leeg gelaten, wordt aangenomen dat de werknemer een volledig flexibel rooster heeft, "
"waardoor hij/zij op elk moment van de week kan werken "
"zonder tijdslimiet.\n"
"        "

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Employee:"
msgstr "Werknemer:"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__employee_ids
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Employees"
msgstr "Werknemers"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Employees Information"
msgstr "Informatie werknemers"

#. module: l10n_be_hr_payroll
#: model:hr.payroll.dashboard.warning,name:l10n_be_hr_payroll.hr_payroll_dashboard_warning_invalid_gender
msgid "Employees With Invalid Configured Gender"
msgstr "Werknemers met ongeldig bepaald geslacht"

#. module: l10n_be_hr_payroll
#: model:hr.payroll.dashboard.warning,name:l10n_be_hr_payroll.hr_payroll_dashboard_warning_invalid_lang
msgid "Employees With Invalid Configured Language"
msgstr "Werknemers met ongeldig geconfigureerde taal"

#. module: l10n_be_hr_payroll
#: model:hr.payroll.dashboard.warning,name:l10n_be_hr_payroll.hr_payroll_dashboard_warning_invalid_niss
msgid "Employees With Invalid NISS Numbers"
msgstr "Werknemers met ongeldige INSZ-nummers"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "Employees:"
msgstr "Werknemers:"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Employer"
msgstr "Werkgever"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Employer Class:"
msgstr "Werkgeverscategorie:"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Employer Information"
msgstr "Informatie werkgever"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Employer contribution to the Fund"
msgstr "Werkgeversbijdrage aan het Fonds"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
msgid "Employer contribution to the fund"
msgstr "Werkgeversbijdrage aan het Fonds"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Employer details"
msgstr "Gegevens werkgever"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__res_company__l10n_be_ffe_employer_type__commercial
msgid "Employers with industrial or commercial purposes"
msgstr "Werkgevers met industriële of commerciële doelen"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__res_company__l10n_be_ffe_employer_type__non_commercial
msgid "Employers without industrial or commercial purposes"
msgstr "Werkgevers zonder industriële of commerciële doelen"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_employment_bonus_employees
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_head_employment_bonus
msgid "Employment Bonus"
msgstr "Werkgelegenheidsbonus"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__date_end
msgid "End Date"
msgstr "Einddatum"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__end_notice_period
msgid "End Notice Period"
msgstr "Einde opzegperiode"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__date_end
msgid "End Period"
msgstr "Eindeperiode"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__date_end
msgid "End date of the new contract."
msgstr "Einddatum van het nieuwe contract."

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__end_notice_period
msgid "End notice period"
msgstr "Einde opzegperiode"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "End-of-year bonus, 13th month or other similar amount"
msgstr "Eindejaarsbonus, 13e maand of ander vergelijkbaar bedrag"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Entries"
msgstr "Boekingen"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Entry date"
msgstr "Invoerdatum"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__error_message
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__error_message
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__error_message
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__error_message
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__error_message
msgid "Error Message"
msgstr "Foutmelding"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/hr_payroll_generate_warrant_payslips.py:0
msgid "Error while importing file"
msgstr "Fout tijdens het importeren van het bestand"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "Established on"
msgstr "Vastgesteld op"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Established on:"
msgstr "Opgemaakt op:"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_payslip.py:0
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_double_holiday_european_leaves_deduction
msgid "European Leaves Deduction"
msgstr "Inhouding Europees verlof"

#. module: l10n_be_hr_payroll
#: model:hr.leave.type,name:l10n_be_hr_payroll.holiday_type_european
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_european
msgid "European Time Off"
msgstr "Europees verlof"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Example Day"
msgstr "Voorbeelddag"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Example Hour"
msgstr "Voorbeelduur"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__deducted_amount
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx_line__amount
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "Exempted Amount"
msgstr "Vrijgesteld bedrag"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_expatriate
msgid "Expatriate Allowance"
msgstr "Uitkering voor expats"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__representation_fees
msgid "Expense Fees"
msgstr "Onkosten"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payroll_generate_warrant_payslips_view_form
msgid "Export"
msgstr "Export"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payroll_generate_warrant_payslips_view_form
msgid "Export Complete"
msgstr "Export voltooid"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_273S_view_form
msgid "Export PDF file"
msgstr "PDF-bestand exporteren"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_eco_vouchers_wizard_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_group_insurance_wizard_view_form
msgid "Export XLS"
msgstr "XLS exporteren"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "Export XLSX details"
msgstr "XLSX details exporteren"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_273S_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "Export XML file"
msgstr "XML-bestand exporteren"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_payroll_generate_warrant_payslips__state__export
msgid "Export the employees file"
msgstr "Het werknemersbestand exporteren"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_balance_sheet_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_security_certificate_view_form
msgid "Export to PDF"
msgstr "Exporteer naar pdf"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_balance_sheet_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_security_certificate_view_form
msgid "Export to XLSX"
msgstr "Exporteer naar XLSX"

#. module: l10n_be_hr_payroll
#: model:hr.leave.type,name:l10n_be_hr_payroll.holiday_type_extra_legal
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_extra_legal
msgid "Extra Legal Time Off"
msgstr "Extra wettelijk verlof"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "FEB"
msgstr "FEB"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.finprof_xml_report
msgid "FINPROF"
msgstr "FINPROF"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__month__2
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__month__2
msgid "February"
msgstr "Februari"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Female"
msgstr "Vrouwelijk"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__l10n_be_ffe_employer_type
msgid "Ffe Employer Type"
msgstr "FSO werkgeverstype"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid "Fiche 274.10: Précompte professionnel versé."
msgstr "Fiche 274.10: Betaalde bedrijfsvoorheffing."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid ""
"Fiche 274.32: Dispense de précompte professionnel pour doctorants/ingénieurs "
"civils."
msgstr ""
"Fiche 274.32: Vrijstelling van bedrijfsvoorheffing voor promovendi/"
"burgerlijk ingenieurs."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid "Fiche 274.33: Dispense de précompte professionnel pour master."
msgstr ""
"Fiche 274.33: Vrijstelling van bedrijfsvoorheffing foor masterstudenten."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid "Fiche 274.34: Dispense de précompte professionnel pour bacheliers."
msgstr ""
"Fiche 274.34: Vrijstelling van bedrijfsvoorheffing voor bachelorstudenten."

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_dmfa.py:0
msgid "Field Year does not seem to be a year. It must be an integer."
msgstr "Veldjaar lijkt geen jaar te zijn. Het moet een geheel getal zijn."

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__file_type
msgid "File Type"
msgstr "Bestandstype"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__first_contract_year
msgid "First Contract Year"
msgstr "Eerste jaar overeenkomst"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__first_contract_year_n
msgid "First Contract Year N"
msgstr "Eerste jaar overeenkomst N"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__first_contract_year_n_plus_1
msgid "First Contract Year N Plus 1"
msgstr "Eerste jaar overeenkomst N plus 1"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__first_contract_year_n1
msgid "First Contract Year N1"
msgstr "Eerste jaar overeenkomst N1"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__first_contract_in_company
msgid "First contract in company"
msgstr "Eerste contract in bedrijf"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__first_contract
msgid "First contract start date."
msgstr "Startdatum eerste contract."

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__start_notice_period
msgid ""
"First monday from the departure date (or the following open day if it is a "
"public holiday)."
msgstr ""
"Eerste maandag na vertrekdatum (of eerstvolgende werkdag indien het "
"een feestdag is)."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_employee_form__l10n_be_view_for
msgid "Fiscal Voluntarism"
msgstr "Fiscale voluntarisme"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__fiscal_voluntary_rate
msgid "Fiscal Voluntary Rate"
msgstr "Fiscaal vrijwillig tarief"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Fixed and variable remuneration"
msgstr "Vaste en variabele beloning"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Fixed term contract (CDD)"
msgstr "Arbeidsovereenkomst voor bepaalde tijd"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
msgid "Fixed-term contract (CDD)"
msgstr "Arbeidsovereenkomst voor bepaalde tijd"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_flemish_training_time_off
msgid "Flemish Educational Time Off"
msgstr "Vlaams educatief verlof"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_alloc_employee_view_tree
msgid "For The Period"
msgstr "Voor de periode"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Foreign Expenses"
msgstr "Buitenlandse uitgaven"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Formal continuous trainings at the employer's expense"
msgstr "Formele bijscholing op kosten van de werkgever"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__found_leave_allocation
msgid "Found Leave Allocation"
msgstr "Gevonden verloftoewijzing"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Frais déductibles"
msgstr "Aftrekbare kosten"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Frais déduits"
msgstr "Afgetrokken kosten"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Frequency (Month)"
msgstr "Frequentie (Maand)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "From"
msgstr "Van"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__fuel_card
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract_employee_report__fuel_card
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__default_fuel_card
msgid "Fuel Card"
msgstr "Tankkaart"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Full Time"
msgstr "Voltijds"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract_employee_report__fte
msgid "Full Time Equivalent (Today)"
msgstr "Voltijdsequivalent (vandaag)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__full_wage
msgid "Full Time Equivalent Wage"
msgstr "Loon voltijdsequivalent"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__full_time_off_allocation
msgid "Full Time Off Allocation"
msgstr "Volledige verloftoewijzing"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__full_resource_calendar_id
msgid "Full Working Schedule"
msgstr "Volledig werkrooster"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Function held"
msgstr "Functie"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "Fund or company"
msgstr "Fonds of bedrijf"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Gender"
msgstr "Geslacht"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_view_form
msgid "Generate PDF report"
msgstr "Genereer PDF-rapport"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payroll_generate_warrant_payslips_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_eco_vouchers_wizard_view_form
msgid "Generate Payslips"
msgstr "Genereer loonstroken"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payroll_allocating_paid_time_off_view_form
msgid "Generate Time Off Allocations"
msgstr "Genereer verloftoewijzingen"

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.action_hr_payroll_generate_warrant_payslips
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_payroll_generate_warrant_payslips
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payroll_generate_warrant_payslips_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payslip_run_view_tree
msgid "Generate Warrant Payslips"
msgstr "Genereer warrant loonstroken"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_payroll_generate_warrant_payslips_line
msgid "Generate Warrant Payslips Lines"
msgstr "Genereer warrant loonstrookregels"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_view_form
msgid "Generate XML report"
msgstr "XML-rapport genereren"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_payslip_employees
msgid "Generate payslips for all selected employees"
msgstr "Genereer loonstroken voor alle geselecteerde werknemers"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_273S_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_10_form_wizard
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_45_form_wizard
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_balance_sheet_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_security_certificate_view_form
msgid "Generation Complete"
msgstr "Generatie volledig"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_report_l10n_be_hr_payroll_273s_274_273s
msgid "Get 273S report as PDF."
msgstr "Ontvang 273S rapport als PDF."

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_report_l10n_be_hr_payroll_273s_274_274_10
msgid "Get 274.10 report as PDF."
msgstr "Ontvang 274.10 rapport als PDF."

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_report_l10n_be_hr_payroll_dmfa_pdf_report
msgid "Get DmfA declaration as PDF"
msgstr "Ontvang DmfA aangifte als PDF"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_report_l10n_be_hr_payroll_report_social_balance
msgid "Get Social Balance Sheet as PDF"
msgstr "Ontvang Sociale balans als PDF"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_report_l10n_be_hr_payroll_report_social_security_certificate
msgid "Get Social Security Certificate as PDF"
msgstr "Ontvang certificaat sociale zekerheid als PDF"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "Gift In Kind"
msgstr "Geschenk in natura"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Gifts in kind"
msgstr "Geschenken in natura"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Global Information"
msgstr "Globale informatie"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__dmfa_go
msgid "Go file"
msgstr "Go bestand"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Grants and other financial benefits received (to be deducted)"
msgstr ""
"Ontvangen subsidies en andere financiële voordelen (in mindering te brengen)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_head_salary
msgid "Gross"
msgstr "Bruto"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__net_n
msgid "Gross Annual Remuneration Current Year"
msgstr "Bruto Jaarlijkse bezoldiging Lopende jaar"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__net_n1
msgid "Gross Annual Remuneration Previous Year"
msgstr "Bruto jaarlijkse bezoldiging vorig jaar"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__salary_december_2013
msgid "Gross Annual Salary as of December 31, 2013"
msgstr "Bruto jaarsalaris per 31 december 2013"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_double_december_salary
msgid "Gross Double December Pay Salary"
msgstr "Bruto dubbel decemberloon"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_double_holiday_gross_salary
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_gross_salary
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_thirteen_month_gross_salary
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_pfi_gross_salary
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__gross_salary
msgid "Gross Salary"
msgstr "Brutoloon"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "Gross Salary Before ONSS"
msgstr "Brutoloon voor RSZ"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_basic_12_92
msgid "Gross Yearly Salary"
msgstr "Bruto maandloon"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_payslip_employee_depature_notice__salary_december_2013__inferior
msgid "Gross annual salary < 32.254 €"
msgstr "Bruto jaarloon < 32.254 €"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_payslip_employee_depature_notice__salary_december_2013__superior
msgid "Gross annual salary > 32.254 €"
msgstr "Bruto jaarloon > 32.254 €"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Gross cost directly linked to training"
msgstr "Brutokosten gekoppeld aan opleiding"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "Gross reference remuneration"
msgstr "Bruto referentievergoeding"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Gross salary before ONSS"
msgstr "Brutoloon voor RSZ"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Gross wage"
msgstr "Brutoloon"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_group_insurance
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_salary_rule_category_group_insurance
msgid "Group Insurance (Employer)"
msgstr "Groepsverzekering (werkgever)"

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_group_insurance_wizard_action
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_l10n_be_group_insurance_wizard
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_group_insurance_wizard_view_form
msgid "Group Insurance Export"
msgstr "Exporteer groepsverzekering"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Group Insurance Global Contributions:"
msgstr "Globale bijdragen groepsverzekering:"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_group_insurance_rate
msgid "Group Insurance Sacrifice Rate"
msgstr "Percentage keuze groepsverzekering"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_group_insurance_wizard
msgid "Group Insurance Wizard"
msgstr "Assistent groepsverzekering"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_group_insurance_line_wizard
msgid "Group Insurance Wizard Line"
msgstr "Assistentregel groepsverzekering"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_individual_account
msgid "HR Individual Account Report By Employee"
msgstr "HR Individueel Accountrapport per werknemer"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_281_10
msgid "HR Payroll 281.10 Wizard"
msgstr "HR Loonadministratie 281.10 Wizard"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_281_45
msgid "HR Payroll 281.45 Wizard"
msgstr "HR Loonadministratie 281.45 Wizard"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_work_entry
msgid "HR Work Entry"
msgstr "HR-werkinvoer"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_work_entry_type
msgid "HR Work Entry Type"
msgstr "TYPE HR-werkinvoer"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_has_ambulatory_insurance
msgid "Has Ambulatory Insurance"
msgstr "Heeft een ambulante verzekering"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__has_hospital_insurance
msgid "Has Hospital Insurance"
msgstr "Heeft een hospitalisatieverzekering"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract_history__has_valid_schedule_change_contract
msgid "Has Valid Schedule Change Contract"
msgstr "Heeft een geldig contract voor roosterwijziging"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_hiring_bonus
msgid "Hiring Bonus"
msgstr "Aanwervingsbonus"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_employee_form__l10n_be_view_for
msgid "Holiday Attest"
msgstr "Vakantieattest"

#. module: l10n_be_hr_payroll
#: model:hr.payroll.structure,payslip_name:l10n_be_hr_payroll.hr_payroll_structure_cp200_employee_departure_n_holidays
msgid "Holiday Pay (N Year)"
msgstr "Vakantiegeld (Jaar N)"

#. module: l10n_be_hr_payroll
#: model:hr.payroll.structure,payslip_name:l10n_be_hr_payroll.hr_payroll_structure_cp200_employee_departure_n1_holidays
msgid "Holiday Pay (N-1 Year)"
msgstr "Vakantiegeld (Jaar N-1)"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Holiday Pay Provision"
msgstr "Voorziening vakantiegeld"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_holiday_pay_recovery_n
msgid "Holiday Pay Recovery (Year N)"
msgstr "Invordering vakantiegeld (jaar N)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_holiday_pay_recovery_n1
msgid "Holiday Pay Recovery (Year N-1)"
msgstr "Invordering vakantiegeld (Jaar N-1)"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "Holiday Pay Supplement"
msgstr "Aanvullend vakantiegeld"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Holiday Pay Supplement Retenue"
msgstr "Inhouding aanvullend vakantiegeld"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__amount
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__amount
msgid "Holiday pay amount on the holiday attest from the previous employer"
msgstr ""
"Bedrag vakantiegeld op basis van het vakantieattest van de vorige werkgever"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "Holiday pay details:"
msgstr "Details vakantiegeld:"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Holiday pay supplement"
msgstr "Aanvullend vakantiegeld"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__distance_home_work
msgid "Home-Work Distance"
msgstr "Woon-werkafstand"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__km_home_work
msgid "Home-Work Distance in Km"
msgstr "Woon-werkafstand in km"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__distance_home_work_unit
msgid "Home-Work Distance unit"
msgstr "Woon-werkafstandseenheid"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_residence
msgid "Home/Residence Allowance"
msgstr "Verblijfstoelage"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "Hospital Insurance"
msgstr "Hospitalisatieverzekering"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__hospital_insurance_amount_adult
msgid "Hospital Insurance Amount per Adult"
msgstr "Hospitalisatieverzekering bedrag per volwassene"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__hospital_insurance_amount_child
msgid "Hospital Insurance Amount per Child"
msgstr "Hospitalisatieverzekering bedrag per kind"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_hospital_insurance_notes
msgid "Hospital Insurance: Additional Info"
msgstr "Hospitalisatieverzekering: Aanvullende informatie"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips_line__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx_line__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa_location_unit__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_line_wizard__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_wizard__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_line_wizard__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_wizard__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard_line__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_schedule_change_allocation__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_work_entry_daily_benefit_report__id
msgid "ID"
msgstr "ID"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "IDENTIFICATION DU REDEVABLE"
msgstr "IDENTIFICATIE VAN DE BELASTINGPLICHTIGE"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__ip_wage_rate
msgid "IP percentage"
msgstr "Percentage IE"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_salary_rule_category_ip_part
msgid "IP. Part."
msgstr "IE. Deel."

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "Identification Of The Company And Infos"
msgstr "Identificatie van het bedrijf en info"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Identification du bénéficiaire"
msgstr "Identificatie verkrijger"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Identification of the company"
msgstr "Identificatie bedrijf"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__previous_contract_creation
msgid ""
"If checked, the wizard will create another contract after the new working "
"schedule contract, with current working schedule"
msgstr ""
"Indien aangevinkt, zal de wizard een ander contract aanmaken na het nieuwe "
"werkschemacontract, met het huidige werkschema"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__other_dependent_people
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_users__other_dependent_people
msgid "If other people are dependent on the employee"
msgstr "Als andere mensen ten laste zijn van de werknemer"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_users__disabled
msgid "If the employee is declared disabled by law"
msgstr "Als de werknemer bij wet gehandicapt wordt verklaard"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payroll_generate_warrant_payslips_view_form
msgid "Import Complete"
msgstr "Import voltooid"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__import_file
msgid "Import File"
msgstr "Bestand importeren"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_payroll_generate_warrant_payslips__state__import
msgid "Import the employee file"
msgstr "Het werknemersbestand importeren"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_impulsion_plan
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "Impulsion Plan"
msgstr "Impulsieplan"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__first_contract
msgid "In the Company Since"
msgstr "In het bedrijf sinds"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__occupation_rate
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__occupation_rate
msgid "Included between 0 and 100%"
msgstr "Inbegrepen tussen 0 en 100%"

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,name:l10n_be_hr_payroll.action_report_individual_account
msgid "Individual Account"
msgstr "Individueel account"

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_individual_account_action
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_l10n_be_individual_account
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_individual_account_view_form
msgid "Individual Accounts"
msgstr "Individuele accounts"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_individual_account.py:0
msgid "Individual Accounts - Year %s"
msgstr "Individuele accounts - Jaar %s"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Informal continuous trainings at the employer's expense"
msgstr "Informele bijscholing op kosten van de werkgever"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Information on training for workers during the exercise"
msgstr "Informatie over de opleiding van werknemers tijdens het boekjaar"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__initial_time_off_allocation
msgid "Initial Time Off Allocation"
msgstr "Initiele verloftoewijzing"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Initial trainings at the employer's expense"
msgstr "Initiële opleidingen op kosten van de werkgever"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__insurance_amount
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "Insurance Amount"
msgstr "Bedrag verzekering"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__insured_relative_adults_total
msgid "Insured Relative Adults Total"
msgstr "Totaal verzekerde verwante volwassenen"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__insured_relative_spouse
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "Insured Spouse"
msgstr "Verzekerde echtgeno(o)t(e)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_config_settings_view_form
msgid "Insurer"
msgstr "Verzekeraar"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_ip
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_ip_part
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__ip
msgid "Intellectual Property"
msgstr "Intellectuele eigendom"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_ip_deduction
msgid "Intellectual Property Income Deduction"
msgstr "Aftrek van intellectuele eigendomsinkomsten"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__default_internet
msgid "Internet"
msgstr "Internet"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__internet
msgid "Internet Subscription"
msgstr "Internetabonnement"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__xml_validation_state__invalid
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__xml_validation_state__invalid
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_10__xml_validation_state__invalid
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_45__xml_validation_state__invalid
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_dmfa__validation_state__invalid
msgid "Invalid"
msgstr "Ongeldig"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_dmfa.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_273S.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_45.py:0
msgid ""
"Invalid NISS number for those employees:\n"
" %s"
msgstr ""
"Ongeldig INSZ-nummer voor deze werknemers:\n"
" %s"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__ip_value
msgid "Ip Value"
msgstr "Ip-waarde"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_is_below_scale
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract_history__l10n_be_is_below_scale
msgid "Is below CP200 salary scale"
msgstr "Ligt onder de PC 200 salarisschaal"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__is_test
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__is_test
msgid "Is it a test?"
msgstr "Is dit een test?"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "JAN"
msgstr "JAN"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "JUL"
msgstr "JUL"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "JUN"
msgstr "JUN"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__month__1
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__month__1
msgid "January"
msgstr "Januari"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_job
msgid "Job Position"
msgstr "Functie"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Joint Commission:"
msgstr "Paritair comité:"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Joint committee"
msgstr "Paritair comité"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Joint committee for employees"
msgstr "Paritair comité voor werknemers"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__month__7
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__month__7
msgid "July"
msgstr "Juli"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__month__6
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__month__6
msgid "June"
msgstr "Juni"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_work_entry_type__leave_right
msgid "Keep Time Off Right"
msgstr "Verlofrecht behouden"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_ambulatory_insured_adults_total
msgid "L10N Be Ambulatory Insured Adults Total"
msgstr "L10N Be Ambulante totaal verzekerde volwassenen"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_run__l10n_be_display_eco_voucher_button
msgid "L10N Be Display Eco Voucher Button"
msgstr "L10N Be weergave knop ecocheque"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_company__l10n_be_ffe_employer_type
msgid "L10N Be Ffe Employer Type"
msgstr "L10N Be FSO Werkgeverstype"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_group_insurance_amount
msgid "L10N Be Group Insurance Amount"
msgstr "L10N Be Bedrag groepsverzekering"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_group_insurance_cost
msgid "L10N Be Group Insurance Cost"
msgstr "L10N Be Kosten groepsverzekering"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip__l10n_be_has_eco_vouchers
msgid "L10N Be Has Eco Vouchers"
msgstr "L10N Be Heeft ecocheques"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_is_below_scale_warning
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract_history__l10n_be_is_below_scale_warning
msgid "L10N Be Is Below Scale Warning"
msgstr "L10N Be Ligt onder schaal waarschuwing"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip__l10n_be_is_december
msgid "L10N Be Is December"
msgstr "L10N Be Is December"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip__l10n_be_is_double_pay
msgid "L10N Be Is Double Pay"
msgstr "L10N Be Is dubbel vakantiegeld"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip__l10n_be_max_seizable_amount
msgid "L10N Be Max Seizable Amount"
msgstr "L10N Be Max belastbaar bedrag"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip__l10n_be_max_seizable_warning
msgid "L10N Be Max Seizable Warning"
msgstr "L10N Be Max belastbaar waarschuwing"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_job__l10n_be_scale_category
msgid "L10N Be Scale Category"
msgstr "L10N Be Schaal Categorie"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_leave__l10n_be_sickness_can_relapse
msgid "L10N Be Sickness Can Relapse"
msgstr "L10N Kan hervallen"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard_line__lang
msgid "Language"
msgstr "Taal"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__has_laptop
msgid "Laptop"
msgstr "Laptop"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips_line__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx_line__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa_location_unit__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_line_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_line_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard_line__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_schedule_change_allocation__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips_line__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx_line__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa_location_unit__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_line_wizard__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_wizard__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_line_wizard__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_wizard__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard_line__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_schedule_change_allocation__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__write_date
msgid "Last Updated on"
msgstr "Laatst bijgewerkt op"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid ""
"Le précompte mobilier est supporté par le débiteur des revenus à la décharge "
"du bénéficiaire :"
msgstr ""
"De roerende voorheffing wordt ten laste genomen door de schuldenaar van de "
"inkomsten tot kwijting van de begunstigde:"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__leave_allocation_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_schedule_change_allocation__leave_allocation_id
msgid "Leave Allocation"
msgstr "Toewijzing verlof"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Legal nature of the contract"
msgstr "Juridische aard van het contract"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "Limburg"
msgstr "Limburg"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__line_ids
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__line_ids
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_wizard__line_ids
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_wizard__line_ids
msgid "Line"
msgstr "Regel"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard__line_ids
msgid "Lines"
msgstr "Regels"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__lines_count
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__lines_count
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account__lines_count
msgid "Lines Count"
msgstr "Aantal regels"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_long_sick
msgid "Long Term Sick"
msgstr "Langdurig ziek"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "MAR"
msgstr "MAA"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "MAY"
msgstr "MEI"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Male"
msgstr "Mannelijk"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_payroll_alloc_paid_leave
msgid "Manage the Allocation of Paid Time Off"
msgstr "De toewijzing van betaald verlof beheren"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_payroll_alloc_employee
msgid "Manage the Allocation of Paid Time Off Employee"
msgstr "De toewijzing van betaald verlof van werknemer beheren"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_payslip_employee_depature_notice
msgid "Manage the Employee Departure - Notice Duration"
msgstr "Vertrek van de werknemer beheren - Duur van de kennisgeving"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_payslip_employee_depature_holiday_attests
msgid "Manage the Employee Departure Holiday Attests"
msgstr "De vakantieattesten bij vertrek van werknemers beheren"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Management staff"
msgstr "Leidinggevend personeel"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "Marc Demo"
msgstr "Marc Demo"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__month__3
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__month__3
msgid "March"
msgstr "Maart"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Marital Status"
msgstr "Burgerlijke staat"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_employee__certificate__civil_engineer
msgid "Master: Civil Engineering"
msgstr "Master: Burgerlijke Ingenieur"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "Masters"
msgstr "Master"

#. module: l10n_be_hr_payroll
#: model:hr.leave.type,name:l10n_be_hr_payroll.holiday_type_maternity
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_maternity
msgid "Maternity Time Off"
msgstr "Moederschapsverlof"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_leave_allocation__max_leaves_allocated
msgid "Max Leaves Allocated"
msgstr "Max toegewezen verlof"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_schedule_change_allocation__maximum_days
msgid "Maximum Days"
msgstr "Maximum aantal dagen"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__month__5
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__month__5
msgid "May"
msgstr "Mei"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_ch_year
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_work_entry_type__meal_voucher
msgid "Meal Voucher"
msgstr "Maaltijdcheques"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "Meal Voucher (Employee Part)"
msgstr "Maaltijdcheques (deel werknemer)"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "Meal Voucher (Employer Part)"
msgstr "Maaltijdcheques (deel werkgever)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_report__l10n_be_meal_voucher_employer
msgid "Meal Voucher (Employer)"
msgstr "Maaltijdcheques (werkgever)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__meal_voucher_average_monthly_amount
msgid "Meal Voucher Average Monthly Amount"
msgstr "Maaltijdcheques gemiddeld maandelijks bedrag"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_report__l10n_be_meal_voucher_count
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip__meal_voucher_count
msgid "Meal Voucher Count"
msgstr "Aantal maaltijdcheques"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__meal_voucher_paid_monthly_by_employer
msgid "Meal Voucher Paid Monthly By Employer"
msgstr "Maaltijdcheques maandelijks betaald door werkgever"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__meal_voucher_paid_by_employer
msgid "Meal Voucher Paid by Employer"
msgstr "Maaltijdcheques betaald door werkgever"

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_work_entry_daily_benefit_report_action
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__meal_voucher_amount
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__default_meal_voucher_amount
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_hr_payroll_meal_voucher
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_work_entry_daily_benefit_report_view_pivot
msgid "Meal Vouchers"
msgstr "Maaltijdcheques"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Meal Vouchers (Employee Part)"
msgstr "Maaltijdcheques (deel werknemer)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Meal Vouchers (Employer Part)"
msgstr "Maaltijdcheques (deel werkgever)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Mean Working Hours:"
msgstr "Gemiddelde werkuren:"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_medical_assistance
msgid "Medical Assistance"
msgstr "Medische hulp"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_ir_ui_menu
msgid "Menu"
msgstr "Menu"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_head_div_net
msgid "Misc. Net"
msgstr "Misc. Netto"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_head_div_impos
msgid "Misc. Taxable"
msgstr "Misc. Belastbaar"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__default_mobile
msgid "Mobile"
msgstr "Mobiele"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__mobile
msgid "Mobile Subscription"
msgstr "GSM-abonnement"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Mobility Bonus"
msgstr "Mobiliteitsbonus"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_10__type_treatment__1
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_45__type_treatment__1
msgid "Modification"
msgstr "Wijziging"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Montant brut des revenus"
msgstr "Brutobedrag aan inkomsten"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Montant du Pr.M"
msgstr "Bedrag roerende voorheffing"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Montant imposable"
msgstr "Belastbaar bedrag"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Montant à payer :"
msgstr "Te betalen bedrag:"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__month
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__month
msgid "Month"
msgstr "Maand"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_ambulatory_insurance
msgid "Monthly ambulatory insurance (employer's share)"
msgstr "Maandelijkse ambulante verzekering (deel werkgever)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract__fuel_card
msgid "Monthly amount the employee receives on his fuel card."
msgstr "Maandelijks bedrag dat de werknemer ontvangt op zijn tankkaart."

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_benefit_in_kind
msgid "Monthly benefit in kind"
msgstr "Maandelijks voordeel alle aard"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract__commission_on_target
msgid ""
"Monthly gross amount that the employee receives if the target is reached."
msgstr ""
"Maandelijks brutobedrag dat de werknemer ontvangt als het doel is bereikt."

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_group_insurance
msgid "Monthly group insurance (employer's share)"
msgstr "Maandelijkse groepsverzekering (werkgeversaandeel)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_hospital_insurance
msgid "Monthly hospital insurance (employer's share)"
msgstr "Maandelijkse hospitalisatieverzekering (werkgeversaandeel)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract__representation_fees
msgid ""
"Monthly net amount the employee receives to cover his representation fees."
msgstr ""
"Maandelijks nettobedrag dat de werknemer ontvangt om zijn "
"representatiekosten te dekken."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.finprof_xml_report
msgid "Multiple_PRP_Declaration"
msgstr "Multiple_PRP_Declaration"

#. module: l10n_be_hr_payroll
#: model:hr.departure.reason,name:l10n_be_hr_payroll.departure_mutual_agreement
msgid "Mutual Agreement"
msgstr "Wederkerige overeenkomst"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_december_slip_wizard_view_form
msgid "N Year"
msgstr "Jaar N"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_december_slip_wizard_view_form
msgid "N-1 Year"
msgstr "Jaar N-1"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__xml_validation_state__normal
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__xml_validation_state__normal
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_10__xml_validation_state__normal
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_45__xml_validation_state__normal
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_dmfa__validation_state__normal
msgid "N/A"
msgstr "Nvt"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_line_wizard__niss
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_line_wizard__niss
msgid "NISS"
msgstr "INSZ"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__niss
msgid "NISS Number"
msgstr "INSZ-nummer"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "NISS:"
msgstr "NISS:"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "NOV"
msgstr "NOV"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__name
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Name"
msgstr "Naam"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Name of the joint committee"
msgstr "Naam van het paritair comité"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "National Register Identification Number"
msgstr "Rijksregisternummer"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_negative_net
msgid "Negative Net"
msgstr "Negatief netto"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_net_termination
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_pay_net_termination
msgid "Net"
msgstr "Netto"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_warrant_deduction
msgid "Net Deductions from Wages"
msgstr "Netto-inhoudingen op lonen"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_net
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Net Salary"
msgstr "Nettoloon"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "Net Salary Paid By Third Party"
msgstr "Nettoloon betaald door een derde"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Net cost to the business"
msgstr "Nettokosten voor het bedrijf"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_impulsion_12mo
msgid "Net part payable by the Onem (12+ months)"
msgstr "Netto te betalen deel door de RVA (12+ maanden)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_impulsion_25yo
msgid "Net part payable by the Onem (< 25 years old)"
msgstr "Netto te betalen deel door de RVA (< 25 jaar oud)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Net salary paid by third party"
msgstr "Nettoloon betaald door een derde"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_schedule_change_allocation__new_resource_calendar_id
msgid "New Resource Calendar"
msgstr "Nieuwe kalender"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__wage
msgid "New Wage"
msgstr "Nieuw loon"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__resource_calendar_id
msgid "New Working Schedule"
msgstr "Nieuw werkschema"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_schedule_change_allocation.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_hr_payroll_schedule_change_wizard.py:0
msgid "New working schedule on %(contract_name)s.<br/>New total: %(days)s"
msgstr "Nieuw werkschema op %(contract_name)s.<br/>Nieuw totaal: %(days)s"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "No"
msgstr "Nee"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_dmfa.py:0
msgid "No Certificate definer on the Payroll Configuration"
msgstr "Geen definitie van certificaten op de loonlijstconfiguratie"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__no_onss
msgid "No ONSS"
msgstr "Geen RSZ"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_dmfa.py:0
msgid ""
"No ONSS registration number nor company ID was found for company %s. Please "
"provide at least one."
msgstr ""
"Er is geen RSZ-registratienummer of bedrijfs-ID gevonden voor het bedrijf "
"%s. Geef er ten minste één op."

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__no_withholding_taxes
msgid "No Withholding Taxes"
msgstr "Geen bronbelasting"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
msgid "No first contract date found for employee %s"
msgstr "Geen eerste contractdatum gevonden voor de werknemer %s"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/certificate.py:0
msgid ""
"No private key linked to the certificate, it is required to sign documents."
msgstr ""
"Geen privésleutel gekoppeld aan het certificaat. Dit is nodig om documenten te ondertekenen."

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_dmfa.py:0
msgid "No start/end notice period defined for %s"
msgstr "Geen begin/einde opzegperiode gedefinieerd voor %s"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Nom, prénoms, dénomination sociale ou officielle :"
msgstr "Naam, voornamen, vennootschapsnaam of officiële benaming:"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_termination_non_respect_motivation
msgid "Non respect motivation (= 2 weeks)"
msgstr "Niet-naleving motivatie (= 2 weken)"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Non-university higher education"
msgstr "Hoger niet universitair onderwijs"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_45.py:0
msgid "None"
msgstr "Geen"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_december_slip_wizard_view_form
msgid ""
"Note: The double holiday pay should only be computed if the reduction in "
"working time took place between 01/01 and 30/06 compared to year N-1."
msgstr ""
"Opmerking: Het dubbel vakantiegeld moet enkel berekend worden als de "
"arbeidstijdvermindering plaatsvond tussen 01/01 en 30/06 ten opzichte van "
"het jaar N-1."

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_notice
msgid "Notice (Unprovided)"
msgstr "Opzeg (Niet opgegeven)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_notice_duration
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_notice
msgid "Notice Duration"
msgstr "Opzegtermijn"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__notice_duration_month_before_2014
msgid "Notice Duration in month"
msgstr "Opzegtermijn in maanden"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__notice_duration_week_after_2014
msgid "Notice Duration in weeks"
msgstr "Opzegtermijn in weken"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_notice
msgid "Notice Period"
msgstr "Opzegtermijn"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
msgid "Notice duration"
msgstr "Opzegtermijn"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/hr_payroll_employee_departure_holiday_attest.py:0
msgid ""
"Notice period not set for %s. Please, set the departure notice period first."
msgstr ""
"Opzegtermijn niet ingesteld voor %s. Geef eerst de opzegtermijn voor vertrek "
"op."

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_termination_salary
msgid "Notice salary"
msgstr "Salaris opzegtermijn"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__month__11
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__month__11
msgid "November"
msgstr "November"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Number of Affected Employees"
msgstr "Aantal betrokken werknemers"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
msgid "Number of Workers"
msgstr "Aantal werknemers"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Number of completed training hours"
msgstr "Aantal voltooide opleidingsuren"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_employee_form__l10n_be_view_for
msgid "Number of days"
msgstr "Nombre de jours"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__unpaid_time_off_n
msgid "Number of days of unpaid time off taken during current year"
msgstr "Aantal dagen van onbetaald verlof genomen tijdens het lopende jaar"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__unpaid_time_off_n1
msgid "Number of days of unpaid time off taken during previous year"
msgstr "Aantal dagen van onbetaald verlof genomen tijdens vorig jaar"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__l10n_be_holiday_pay_number_of_days_n
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__l10n_be_holiday_pay_number_of_days_n1
msgid "Number of days on which you should recover the holiday pay."
msgstr "Aantal dagen waarop je het vakantiegeld moet terugvorderen."

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__l10n_be_holiday_pay_number_of_days_n
msgid "Number of days to recover (N)"
msgstr "Aantal terug te vorderen dagen (N)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__l10n_be_holiday_pay_number_of_days_n1
msgid "Number of days to recover (N-1)"
msgstr "Aantal terug te vorderen dagen (N-1)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_employee_form__l10n_be_view_for
msgid "Number of dependent children declared as disabled"
msgstr "Aantal kinderen ten laste die als gehandicapt zijn opgegeven"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__disabled_children_number
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__disabled_children_number
msgid "Number of disabled children"
msgstr "Aantal gehandicapte kinderen"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "Number of hours"
msgstr "Aantal uren"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "Number of joint committees"
msgstr "Nummers van de paritaire comités"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__other_juniors_dependent
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_users__other_juniors_dependent
msgid ""
"Number of juniors dependent on the employee, including the disabled ones"
msgstr ""
"Aantal juniors ten laste van de werknemer, inclusief de gehandicapte juniors"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "Number of overtime hours"
msgstr "Aantal overuren"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__other_senior_dependent
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_users__other_senior_dependent
msgid ""
"Number of seniors dependent on the employee, including the disabled ones"
msgstr ""
"Aantal senioren ten laste van de werknemer, inclusief de gehandicapte "
"senioren"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Number of tax dependents"
msgstr "Aantal belastingplichtigen"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Number of workers"
msgstr "Aantal werknemers"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Numbers of joint committees: 20000"
msgstr "Nummers van de paritaire comités: 20000"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "N° d’entreprise ou, à défaut, n° national :"
msgstr "Ondernemingsnummer of, desgevallend, rijksregisternummer:"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "N° téléphone :"
msgstr "Telefoonnummer:"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "N°273S - 2020"
msgstr "N°273S - 2020"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "OCT"
msgstr "OKT"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
msgid "ONNS Employer"
msgstr "RSZ werkgever"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_onss
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_config_settings_view_form
msgid "ONSS"
msgstr "RSZ"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_rules_special_contribution_termination
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_rules_special_contribution_termination
msgid "ONSS (Double Holiday)"
msgstr "RSZ (dubbel vakantiegeld)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_salary_rule_category_onss_employer
msgid "ONSS (Employer)"
msgstr "RSZ (werkgever)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "ONSS (Office National de sécurité sociale)"
msgstr "ONSS (Office National de sécurité sociale)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_rules_onss_termination
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_rules_onss_termination
msgid "ONSS (Simple Holiday)"
msgstr "RSZ (Enkel vakantiegeld)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_onss_total
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_onss_total
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_rules_special_contribution_onss_total
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_rules_special_contribution_onss_total
msgid "ONSS (TOTAL)"
msgstr "RSZ (TOTAAL)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_salary_rule_category_onss_total
msgid "ONSS (Total)"
msgstr "RSZ (Totaal)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_company__onss_certificate_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__onss_certificate_id
msgid "ONSS Certificate"
msgstr "RSZ certificaat"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_company__onss_company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__onss_company_id
msgid "ONSS Company ID"
msgstr "RSZ Bedrijfs-ID"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "ONSS Company ID:"
msgstr "Bedrijfs-ID RSZ:"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "ONSS Cotisation"
msgstr "Bijdrage RSZ"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "ONSS Cotisation Termination Fees"
msgstr "Bijdrage RSZ op beëindigingsvergoeding"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "ONSS Cotisation on termination fees"
msgstr "Bijdrage RSZ op beëindigingsvergoeding"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "ONSS Cotisation: Charges Redistribution"
msgstr "Bijdrage RSZ: Herverderling van de kosten"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "ONSS Cotisation: Charges redistribution"
msgstr "Bijdrage RSZ: Herverderling van de kosten"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_salary_rule_category_onss_employer_detail
msgid "ONSS Detail (Employer)"
msgstr "Detail RSZ (werkgever)"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "ONSS Double Holiday"
msgstr "RSZ Dubbel vakantiegeld"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "ONSS Employer"
msgstr "RSZ werkgever"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_company__onss_expeditor_number
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__onss_expeditor_number
msgid "ONSS Expeditor Number"
msgstr "RSZ Expeditornummer"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_company__onss_expeditor_number
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_config_settings__onss_expeditor_number
msgid ""
"ONSS Expeditor Number provided when registering service on the technical user"
msgstr ""
"RSZ Expeditornummer opgegevens bij het registreren van diensten op de "
"technische gebruiker"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "ONSS Number"
msgstr "ONSS-nummer"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "ONSS Number:"
msgstr "RSZ-nummer:"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "ONSS Reduction"
msgstr "RSZ vermindering"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_company__onss_registration_number
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__onss_registration_number
msgid "ONSS Registration Number"
msgstr "RSZ-registratienummer"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "ONSS Registration Number:"
msgstr "Identificatienummer RSZ:"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_onss_restructuring
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_onss_restructuring
msgid "ONSS Restructuring Reduction"
msgstr "RSZ herstructurering Vermindering"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "ONSS Student"
msgstr "RSZ Student"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "ONSS Thirteen Month"
msgstr "RSZ dertiende maand"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "ONSS cotisation"
msgstr "Bijdrage RSZ"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "ONSS termination fees cotisation"
msgstr "RSZ bijdrage beëindigingsvergoedingen"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__line_ids
msgid "Occupation Lines"
msgstr "Beroepsregels"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__occupation_rate
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__occupation_rate
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_double_pay_recovery_line_view_form
msgid "Occupation Rate"
msgstr "Bezettingsgraad"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Occupations #"
msgstr "Beroepsbezigheden #"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__month__10
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__month__10
msgid "October"
msgstr "Oktober"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "Odoo"
msgstr "Odoo"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_config_settings_view_form
msgid "Offical Company Information"
msgstr "Officiële bedrijfsinformatie"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__oldest_contract_id
msgid "Oldest Contract"
msgstr "Oudste contract"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_contract.py:0
msgid ""
"Only employees with a Bachelor/Master/Doctor/Civil Engineer degree can "
"benefit from the withholding taxes exemption."
msgstr ""
"Enkel werknemers met een Bachelor/Master/Doctoraat/Burgerlijk ingenieur-"
"diploma kunnen genieten van de vrijstelling van bronbelasting."

#. module: l10n_be_hr_payroll
#: model:ir.model.constraint,message:l10n_be_hr_payroll.constraint_l10n_be_dmfa__unique
msgid ""
"Only one DMFA per year and per quarter is allowed. Another one already "
"exists."
msgstr ""
"Enkel een DFMA per jaar en per kwartaal is toegestaan. Een andere bestaat al."

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_contract.py:0
#: code:addons/l10n_be_hr_payroll/models/hr_payslip.py:0
#: code:addons/l10n_be_hr_payroll/report/hr_contract_history.py:0
msgid "Operation not supported"
msgstr "Bewerking niet ondersteund"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_10__type_treatment__0
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_45__type_treatment__0
msgid "Original"
msgstr "Origineel"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_10__type_sending__0
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_45__type_sending__0
msgid "Original send"
msgstr "Origineel verzenden"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__other_dependent_people
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__other_dependent_people
msgid "Other Dependent People"
msgstr "Andere personen ten laste"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "Other Exempted Amount From ONSS"
msgstr "Ander vrijgesteld bedrag van de RSZ"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Other employer contributions"
msgstr "Andere werkgeversbijdragen"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Other exempted amount from ONSS"
msgstr "Ander vrijgesteld bedrag van de RSZ"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_other_annual
msgid "Other monthly/yearly"
msgstr "Overige maandelijkse/jaarlijkse"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Others"
msgstr "Andere"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_termination_outplacement
msgid "Outplacement (if more than 30 weeks notice duration)"
msgstr "Outplacement (als meer dan 30 weken opzegtermijn)"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Overseas Social Security"
msgstr "Overzeese sociale zekerheid"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Overtime worked"
msgstr "Gewerkte overuren"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__pdf_error
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__pdf_error
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account__pdf_error
msgid "PDF Error Message"
msgstr "PDF foutmelding"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__pdf_file
msgid "PDF File"
msgstr "PDF-bestand"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__pdf_filename
msgid "PDF Filename"
msgstr "PDF-bestandsnaam"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__dmfa_pdf
msgid "PDF file"
msgstr "PDF bestand"

#. module: l10n_be_hr_payroll
#: model:hr.contract.type,name:l10n_be_hr_payroll.l10n_be_contract_type_pfi
msgid "PFI"
msgstr "PFI"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/hr_payroll_allocating_paid_time_off.py:0
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.allocating_paid_time_off_wizard_action
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_hr_allocating_paid_time_off_view
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payroll_allocating_paid_time_off_view_form
msgid "Paid Time Off Allocation"
msgstr "Toewijzing betaald verlof"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__paid_time_off
msgid "Paid Time Off For The Period"
msgstr "Betaald verlof voor de periode"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__paid_time_off_to_allocate
msgid "Paid Time Off To Allocate"
msgstr "Toe te wijzen betaald verlof"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_parental_time_off
msgid "Parental Time Off"
msgstr "Ouderschapsverlof"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__part_time
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_history_view_search
msgid "Part Time"
msgstr "Deeltijds"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_contract.py:0
msgid "Part Time of %(employee)s must be stated at %(link)s."
msgstr "Deeltijds van %(employee)s moeten worden vermeld op %(link)s."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Part Time:"
msgstr "Deeltijds:"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Part-Time"
msgstr "Deeltijds"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_partial_incapacity
msgid "Partial Incapacity (due to illness)"
msgstr "Gedeeltelijke arbeidsongeschiktheid (wegens ziekte)"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_paternity_legal
msgid "Paternity Time Off (Legal)"
msgstr "Vaderschapsverlof (wettelijk)"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_paternity_company
msgid "Paternity Time Off (Paid by Company)"
msgstr "Vaderschapsverlof (betaald door bedrijf)"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_payslip
msgid "Pay Slip"
msgstr "Loonstrook"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_double
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_pay_double
msgid "Pay double"
msgstr "Dubbel betaald"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_double_complementary
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_pay_double_complementary
msgid "Pay double complementary"
msgstr "Aanvullend dubbel betaald"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_pay_variable_salary
msgid "Pay on variable salary (15.34 of the annual amount)"
msgstr "Salaris op variabel salaris (15,34 van het jaarbedrag)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_simple
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_pay_simple
msgid "Pay simple"
msgstr "Betaal eenvoudig"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "Payment Structured Communication:"
msgstr "Gestructureerde communicatie betaling:"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_payroll_report
msgid "Payroll Analysis Report"
msgstr "Loonstrookanalyse rapport"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__payslip_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__payslip_id
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Payslip"
msgstr "Loonstrook"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_payslip_run
msgid "Payslip Batches"
msgstr "Loonstrookbatches"

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,name:l10n_be_hr_payroll.action_report_bonus_month
msgid "Payslip Double Holidays / 13th Month"
msgstr "Loonstrook dubbele feestdagen / 13e maand"

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,name:l10n_be_hr_payroll.action_report_payslip_be
msgid "Payslip Regular Pay"
msgstr "Loonstrook Standaardloon"

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,name:l10n_be_hr_payroll.action_report_light_payslip_be
msgid "Payslip Regular Pay (Light)"
msgstr "Loonstrook Standaardloon (Light)"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_payslip_worked_days
msgid "Payslip Worked Days"
msgstr "Loonstrookwerkdagen"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Payslip current year"
msgstr "Loonstrook huidig jaar"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Payslip previous year"
msgstr "Loonstrook vorig jaar"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__payslip_n_ids
msgid "Payslips N"
msgstr "Loonstroken N"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__payslip_n1_ids
msgid "Payslips N-1"
msgstr "Loonstroken N-1"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payslip_filter
msgid "Payslips with Eco-Vouchers"
msgstr "Loonstroken met ecocheques"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Pension"
msgstr "Pensioen"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__period
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payroll_generate_warrant_payslips_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_balance_sheet_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_security_certificate_view_form
msgid "Period"
msgstr "Periode"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Period -"
msgstr "Periode -"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Period: From"
msgstr "Periode: Van"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Permanent contract (CDI)"
msgstr "Vast contract"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
msgid ""
"Please configure a gender (either male or female) for the following "
"employees:\n"
"\n"
"%s"
msgstr ""
"Gelieve een geslacht (mannelijk of vrouwelijk) te configureren voor de "
"volgende werknemers:\n"
"\n"
"%s"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_274_XX.py:0
msgid ""
"Please configure the 'Company Number' and the 'Revenue Code' on the Payroll "
"Settings."
msgstr ""
"Gelieve het 'Ondernemingsnummer' en de 'Inkomstencode' te configureren in de "
"instellingen van de loondienst."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid ""
"Please find attached some data that will be useful to you to establish the "
"Social Report for the accounting year noted below. The Social Report for the "
"previous year may be useful for you to complete information concerning the "
"previous accounting year."
msgstr ""
"Hierbij treft je enkele gegevens aan die voor jou van nut kunnen zijn bij "
"het opstellen van het Sociaal Verslag over het hieronder vermelde boekjaar. "
"Het Sociaal Verslag over het voorgaande jaar kan nuttig zijn om de "
"informatie over het voorgaande boekjaar aan te vullen."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_view_form
msgid ""
"Please note that the declaration by batch is rather complex and requires "
"some technical knowledge (using some concepts like ssh keys, SFTP servers, "
"and electronical signatures). You may want to take a look at the"
msgstr ""
"Merk op dat de aangifte per batch vrij complex is en enige technische kennis "
"vereist (met behulp van enkele concepten zoals ssh-sleutels, SFTP-servers en "
"elektronische handtekeningen). Je kan een kijkje nemen in de"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_dmfa.py:0
msgid ""
"Please provide an employer class for company %s. The employer class is given "
"by the ONSS and should be encoded in the Payroll setting."
msgstr ""
"Geef een werkgeversklasse op voor het bedrijf %s. De werkgeversklasse wordt "
"gegeven door de RSZ en moet worden gecodeerd in de instellingen van de "
"loondienst."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_10_form_wizard
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_45_form_wizard
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_individual_account_view_form
msgid "Populate"
msgstr "Invullen"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__previous_contract_creation
msgid "Post Change Contract Creation"
msgstr "Wijziging aanmaak contract"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "Posted Employee"
msgstr "Geplaatst werknemer"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__pp_amount_32
msgid "Pp Amount 32"
msgstr "BV bedrag 32"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__pp_amount_33
msgid "Pp Amount 33"
msgstr "BV bedrag 33"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__pp_amount_34
msgid "Pp Amount 34"
msgstr "BV bedrag 34"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Pr.M versé"
msgstr "Betaalde OV"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"Premium from the Impulse Fund for general medicine obtained by a general "
"practitioner approved to settle in a \"priority\" area"
msgstr ""
"Premie van het Impulsfonds voor de huisartsengeneeskunde verkregen door een "
"erkend huisarts om zich te vestigen in een 'prioritaire' zone"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__double_pay_line_ids
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_double_pay_recovery_wizard_view_form
msgid "Previous Occupations"
msgstr "Vorig beroep"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__double_pay_line_n_ids
msgid "Previous Occupations (N)"
msgstr "Vorige beroepsbezigheden (N)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__double_pay_line_n1_ids
msgid "Previous Occupations (N-1)"
msgstr "Vorige beroepsbezigheden (N-1)"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Primary education"
msgstr "Basisonderwijs"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Private Car"
msgstr "Eigen auto"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__private_car_reimbursed_amount
msgid "Private Car Reimbursed Amount"
msgstr "Eigen auto terugbetaald bedrag"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_work_entry_type__private_car
msgid "Private Car Reimbursement"
msgstr "Terugbetaling eigen auto"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_private_car
#: model:hr.salary.rule,name:l10n_be_hr_payroll.hr_salary_rule_student_regular_pay_private_car
msgid "Private car"
msgstr "Eigen auto"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_rules_professional_tax_termination
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_rules_professional_tax_termination
msgid "Professional Tax"
msgstr "Professionele belasting"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Professional qualification"
msgstr "Beroepskwalificatie"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_bank_holiday
msgid "Public Holiday"
msgstr "Feestdag"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_phc
msgid "Public Holiday Compensation"
msgstr "Compensatie feestdag"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "Public Transport"
msgstr "Openbaar vervoer"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__public_transport_reimbursed_amount
msgid "Public Transport Reimbursed amount"
msgstr "Terugbetaald bedrag openbaar vervoer"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Public Transportation"
msgstr "Openbaar vervoer"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_public_transport
msgid "Public Transportation (Tram - Bus - Metro)"
msgstr "Openbaar vervoer (Tram - Bus - Metro)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__public_transport_employee_amount
msgid "Public transport paid by the employee (Monthly)"
msgstr "Openbaar vervoer betaald door de werknemer (maandelijks)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__quarter
msgid "Quarter"
msgstr "Kwartaal"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Quarter 1"
msgstr "Kwartaal 1"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Quarter 2"
msgstr "Kwartaal 2"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Quarter 3"
msgstr "Kwartaal 3"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Quarter 4"
msgstr "Kwartaal 4"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__quarter_end
msgid "Quarter End"
msgstr "Einde van het kwartaal"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Quarter End:"
msgstr "Begin kwartaal:"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__quarter_start
msgid "Quarter Start"
msgstr "Begin van het kwartaal"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Quarter Start:"
msgstr "Einde kwartaal:"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "REVENUS MOBILIERS des DROITS D'AUTEUR et DROITS VOISINS"
msgstr "ROERENDE INKOMSTEN uit COPYRIGHT en AANVERWANTE RECHTEN"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_view_form
msgid "Re-Generate PDF report"
msgstr "PDF-rapport opnieuw genereren"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_view_form
msgid "Re-Generate XML report"
msgstr "XML-rapport opnieuw genereren"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_dmfa__file_type__r
msgid "Real File (R)"
msgstr "Werkelijk bestand (R)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__departure_reason_code
msgid "Reason Code"
msgstr "Reden code"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_employee_form__l10n_be_view_for
msgid "Recovered Amount"
msgstr "Ingevorderd bedrag"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__l10n_be_holiday_pay_recovered_n
msgid "Recovered Simple Holiday Pay (N)"
msgstr "Teruggevorderd enkel vakantiegeld (N)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__l10n_be_holiday_pay_recovered_n1
msgid "Recovered Simple Holiday Pay (N-1)"
msgstr "Teruggevorderd enkel vakantiegeld (N-1)"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_recovery_additional
msgid "Recovery Additional Time"
msgstr "Recuperatie extra tijd"

#. module: l10n_be_hr_payroll
#: model:hr.leave.type,name:l10n_be_hr_payroll.holiday_type_recovery
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_recovery
msgid "Recovery Bank Holiday"
msgstr "Recuperatie feestdag"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__reference
msgid "Reference"
msgstr "Referentie"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Reference Mean Working Hours"
msgstr "Referentie gemiddelde werkuren"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__year
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_wizard__reference_period
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "Reference Period"
msgstr "Referentieperiode"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_history_view_form
msgid "Reference Working Time"
msgstr "Referentie werktijd"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_wizard__reference_year
msgid "Reference Year"
msgstr "Referentiejaar"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_expense_refund
msgid "Refund Expenses"
msgstr "Terugbetaling onkosten"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Reimbursed Expenses"
msgstr "Terugbetaalde onkosten"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Reimbursed Expenses (Code 330)"
msgstr "Terugbetaalde onkosten (Code 330)"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Reimbursed Expenses (Representation Fees)"
msgstr "Terugbetaalde onkosten (representatiekosten)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_reimbursement
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_reimbursement
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_reimbursement
msgid "Reimbursement"
msgstr "Terugbetaling"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__remuneration_n1
msgid "Remuneration N-1"
msgstr "Bezoldiging N-1"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__fictitious_remuneration_n
msgid "Remuneration fictitious current year"
msgstr "Vergoeding fictief huidig jaar"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__fictitious_remuneration_n1
msgid "Remuneration fictitious previous year"
msgstr "Vergoeding fictief vorig jaar"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid ""
"Remuneration of statutory holidays occurring within 30 days of the end date "
"of the contract"
msgstr ""
"Bezoldiging van wettelijke vakantiedagen binnen 30 dagen na de einddatum van "
"het contract"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Remuneration payment frequency"
msgstr "Frequentie van betalingen loon"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Reorganization Measures:"
msgstr "Reorganisatiemaatregelen:"

#. module: l10n_be_hr_payroll
#: model:hr.contract.type,name:l10n_be_hr_payroll.l10n_be_contract_type_replacement
msgid "Replacement"
msgstr "Vervanging"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Replacement contract"
msgstr "Vervangingscontract"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_representation_fees
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_work_entry_type__representation_fees
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__default_representation_fees
msgid "Representation Fees"
msgstr "Representatiekosten"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_representation_fees_volatile
msgid "Representation Fees (Without Serious Standards)"
msgstr "Representatiekosten (zonder serieuze normen)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_rep_fees_regul
msgid "Representation Fees Regularization"
msgstr "Regularisatie representatiekosten"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__requires_new_contract
msgid "Requires New Contract"
msgstr "Vereist een nieuw contract"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_notice
msgid "Resignation Date"
msgstr "Ontslagdatum"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__notice_respect
msgid "Respect of the notice period"
msgstr "Naleving van de opzegtermijn"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_ch_worker
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_pfi_ch_worker
#: model:hr.salary.rule,name:l10n_be_hr_payroll.hr_salary_rule_student_regular_pay_ch_worker
msgid "Retain on Meal Voucher"
msgstr "Inhouding op maaltijdcheques"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Retenue de versement: précompte professionnel (salaires)"
msgstr "Roerende voorheffing: voorheffing op inkomen uit arbeid (loon)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_notice
msgid "Retirement Date"
msgstr "Pensioendatum"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_company__l10n_be_revenue_code
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__l10n_be_revenue_code
msgid "Revenue Code"
msgstr "Inkomen code"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "Right to time off"
msgstr "Recht op verlof"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "Rue du Paradis"
msgstr "Rue du Paradis"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_specific_CP
msgid "Rules specific to Auxiliary Joint Committee"
msgstr "Regels die specifiek zijn voor het Aanvullend Paritair Comité"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Réduction CPDI"
msgstr "Vermindering Overeenkomsten tot het vermijden van dubbele belasting"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Réservé à l'administration"
msgstr "Voorbehouden aan de administratie"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
msgid "SBS %(month)s %(year)s"
msgstr "SBS %(month)s %(year)s"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "SDE"
msgstr "SDE"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "SEP"
msgstr "SEP"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "SOCIAL BALANCE SHEET -  COMPLETE SCHEME"
msgstr "SOCIALE BALANS - VOLLEDIG SCHEMA"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "SOCIAL SECURITY CERTIFICATE"
msgstr "CERTIFICAAT SOCIALE ZEKERHEID"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_xml_report
msgid "SU"
msgstr "SU"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Salaries paid in relation to previous years"
msgstr "Betaalde lonen ten opzicht van voorgaande jaren"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_advance
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Salary Advance"
msgstr "Voorschot salaris"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Salary Assignment"
msgstr "Loonbeslag"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "Salary Attachment"
msgstr "Loonbeslag"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Salary Attachments"
msgstr "Loonbeslag"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__structure_type_id
msgid "Salary Structure Type"
msgstr "Soort salarisstructuur"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__salary_visibility
msgid "Salary as of December 2013"
msgstr "Salaris vanaf december 2013"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_work_entry_daily_benefit_report_search
msgid "Search Meal Voucher Report"
msgstr "Rapport over maaltijdcheque zoeken"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Secondary education"
msgstr "Middelbaar onderwijs"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "Secondment allowance"
msgstr "Detacheringsvergoeding"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_10__type_sending__1
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_45__type_sending__1
msgid "Send grouped corrections"
msgstr "Gegroepeerde correcties verzenden"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__type_sending
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__type_sending
msgid "Sending Type"
msgstr "Type verzenden"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__seniority_description
msgid "Seniority"
msgstr "Anciënniteit"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__l10n_be_scale_seniority
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__l10n_be_scale_seniority
msgid "Seniority at Hiring"
msgstr "Anciënniteit bij aanwerving"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__month__9
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__month__9
msgid "September"
msgstr "September"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Service Public Fédéral FINANCES"
msgstr "Federale Overheidsdienst FINANCIËN"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx_line__sheet_id
msgid "Sheet"
msgstr "Formulier"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__sheet_274_10_filename
msgid "Sheet 274 10 Filename"
msgstr "Formulier 274 10 Bestandsnaam"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract__ip_wage_rate
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract__l10n_be_group_insurance_rate
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__fiscal_voluntary_rate
msgid "Should be between 0 and 100 %"
msgstr "Tussen 0 en 100 %"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_part_sick
msgid "Sick Time Off (Without Guaranteed Salary)"
msgstr "Ziekteverlof (zonder salarisgarantie)"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_work_entry.py:0
msgid "Sick time off to report to DRS for %s."
msgstr "Ziekteverlof te melden aan de ASR voor %s."

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_leave__l10n_be_sickness_relapse
msgid "Sickness Relapse"
msgstr "Herval ziekte"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__dmfa_signature
msgid "Signature file"
msgstr "Handtekening bestand"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_simple_december
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.l10n_be_simple_december_category
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__simple_december_pay
msgid "Simple December Pay"
msgstr "Enkel loon december"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_simple_pay_december
msgid "Simple Holiday Pay (Lost due to working time reduction)"
msgstr "Enkel vakantiegeld (verlies door arbeidstijdverkorting)"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_simple_holiday_pay_variable_salary
msgid "Simple Holiday Pay - Variable Salary"
msgstr "Enkel vakantiegeld - Variabel loon"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__simple_holiday_n
msgid "Simple Holiday Pay N"
msgstr "Enkel vakantiegeld N"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__simple_holiday_n1
msgid "Simple Holiday Pay N-1"
msgstr "Enkel vakantiegeld N-1"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__l10n_be_holiday_pay_to_recover_n
msgid "Simple Holiday Pay to Recover (N)"
msgstr "Terug te vorderen enkel vakantiegeld (N)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__l10n_be_holiday_pay_to_recover_n1
msgid "Simple Holiday Pay to Recover (N-1)"
msgstr "Terug te vorderen enkel vakantiegeld (N-1)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard__slip_ids
msgid "Slip"
msgstr "Loonstrook"

#. module: l10n_be_hr_payroll
#: model:hr.leave.type,name:l10n_be_hr_payroll.holiday_type_small_unemployment
msgid "Small Unemployment"
msgstr "Klein verlet"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_small_unemployment
msgid "Small Unemployment (Brief Holiday)"
msgstr "Klein verlet (korte vakantie)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet__social_balance_filename
msgid "Social Balance Filename"
msgstr "Sociale balans bestandnaam"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet__social_balance_filename_xlsx
msgid "Social Balance Filename Xlsx"
msgstr "Xlsx-bestandsnaam sociale balans"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_social_balance_sheet_action
#: model:ir.actions.report,name:l10n_be_hr_payroll.action_report_social_balance
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet__social_balance_sheet
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_l10n_be_social_balance_sheet
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_balance_sheet_view_form
msgid "Social Balance Sheet"
msgstr "Sociale balans"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet__social_balance_xlsx
msgid "Social Balance Sheet Spreadsheet"
msgstr "Spreadsheet sociale balans"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_social_security_certificate_action
#: model:ir.actions.report,name:l10n_be_hr_payroll.action_report_social_security_certificate
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__social_security_sheet
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_l10n_be_social_security_certificate
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_security_certificate_view_form
msgid "Social Security Certificate"
msgstr "Certificaat sociale zekerheid"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__social_security_xlsx
msgid "Social Security Certificate Spreadsheet"
msgstr "Spreadsheet certificaat sociale zekerheid"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__social_security_filename
msgid "Social Security Filename"
msgstr "Bestandnaam sociale zekerheid"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__social_security_filename_xlsx
msgid "Social Security Filename Xlsx"
msgstr "Xlsx-bestandnaam sociale zekerheid"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_double_holiday_onss_rule
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_double_december_onss
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_onss_rule
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_thirteen_month_onss_rule
msgid "Social contribution"
msgstr "Sociale bijdrage"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Social security organization"
msgstr "Sociale zekerheidsorganisatie"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "SocialBalance-%(date_from)s-%(date_to)s.pdf"
msgstr "SocialeBalans-%(date_from)s-%(date_to)s.pdf"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "SocialBalance-%(date_from)s-%(date_to)s.xlsx"
msgstr "SocialeBalans-%(date_from)s-%(date_to)s.xlsx"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_solicitation_time_off
msgid "Solicitation Time Off"
msgstr "Verzoek verlof"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.hr_salary_rule_student_regular_pay_solidarity_cotisation
msgid "Solidarity Cotisation - Student Job"
msgstr "Solidariteitsbijdrage - Vakantiejob"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Solidarity Cotisation: Company Cars"
msgstr "Solidariteitsbijdrage: Bedrijfswagens"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
msgid ""
"Some employee don't have any contract.:\n"
"%s"
msgstr ""
"Bepaalde werknemers hebben geen overeenkomst:\n"
"%s"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_45.py:0
msgid ""
"Some employee has no contract:\n"
"%s"
msgstr ""
"Sommige werknemers hebben geen overeenkomst:\n"
"%s"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Special Social Cotisation"
msgstr "Bijzondere sociale bijdrage"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_salary_rule_category_m_onss_total
msgid "Special Social Cotisation (Total)"
msgstr "Bijzondere sociale bijdrage (Totaal)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_salary_rule_category_spec_soc_contribution
msgid "Special social contribution"
msgstr "Bijzondere sociale bijdrage"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_mis_ex_onss
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_thirteen_month_mis_ex_onss
msgid "Special social cotisation"
msgstr "Speciale sociale bijdrage"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__spouse_fiscal_status_explanation
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__spouse_fiscal_status_explanation
msgid "Spouse Fiscal Status Explanation"
msgstr "Verklaring fiscale status echtgeno(o)t(e)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Staff movements during the exercise"
msgstr "Personeelsbewegingen tijdens het boekjaar"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/res_company.py:0
msgid "Standard 38 hours/week"
msgstr "Standaard 38 uur/week"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__date_start
msgid "Start Date"
msgstr "Begindatum"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__start_notice_period
msgid "Start Notice Period"
msgstr "Begin opzegtermijn"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__date_start
msgid "Start Period"
msgstr "Beginperiode"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_hr_payroll_schedule_change_wizard.py:0
msgid "Start date must be earlier than end date."
msgstr "De begindatum moet voor de einddatum liggen."

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_hr_payroll_schedule_change_wizard.py:0
msgid "Start date must be later than the current contract's start date."
msgstr "De begindatum moet na de begindatum van huidig contract liggen."

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__date_start
msgid "Start date of the new contract."
msgstr "Begindatum van het nieuwe contract."

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__start_notice_period
msgid "Start notice period"
msgstr "Begin opzegperiode"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_dmfa.py:0
msgid "Start notice period is defined after end notice period for %s"
msgstr ""
"Het begin van de opzegperiode is bepaald na het einde van de opzegperiode "
"voor %s"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__state
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__state
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__state
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__state
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__state
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet__state
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__state
msgid "State"
msgstr "Status"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet__state_xlsx
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__state_xlsx
msgid "State Xlsx"
msgstr "Status Xlsx"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_contract.py:0
msgid "State the Dimona at %(link)s to declare the arrival of %(employee)s."
msgstr ""
"Status van de Dimona op %(link)s om de komst van %(employee)s aan te geven."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Status of employed persons"
msgstr "Status van werknemers"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_stock_option
msgid "Stock Option"
msgstr "Aandelenoptie"

#. module: l10n_be_hr_payroll
#: model:hr.leave.type,name:l10n_be_hr_payroll.holiday_type_strike
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_strike
msgid "Strike"
msgstr "Staking"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "Structural Reductions"
msgstr "Structurele verminderingen"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Structural reductions"
msgstr "Structurele verminderingen"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_report__struct_id
msgid "Structure"
msgstr "Structuur"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__structure_type_id
msgid "Structure Type"
msgstr "Structuurtype"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "Student"
msgstr "Student"

#. module: l10n_be_hr_payroll
#: model:hr.payroll.structure,payslip_name:l10n_be_hr_payroll.hr_payroll_structure_student_regular_pay
msgid "Student: Regular Pay"
msgstr "Student: Regelmatig betalen"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Students"
msgstr "Studenten"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "Sub-Total Gross"
msgstr "Bruto subtotaal"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Sub-total Gross"
msgstr "Bruto subtotaal"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "System 5:"
msgstr "System 5:"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "TOTAL"
msgstr "TOTAAL"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Taking into account for remuneration:"
msgstr "Rekening houdend met de vergoeding:"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Taux du Pr.M"
msgstr "Percentage roerende voorheffing"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__spouse_fiscal_status
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__spouse_fiscal_status
msgid "Tax status for spouse"
msgstr "Fiscale status voor echtgenoot"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Taxable Adaptation"
msgstr "Belastbare aanpassing"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__taxable_amount
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx_line__taxable_amount
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "Taxable Amount"
msgstr "Belastbaar bedrag"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__taxable_amount_32
msgid "Taxable Amount 32"
msgstr "Belastbaar bedrag 32"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__taxable_amount_33
msgid "Taxable Amount 33"
msgstr "Belastbaar bedrag 33"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__taxable_amount_34
msgid "Taxable Amount 34"
msgstr "Belastbaar bedrag 34"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Taxable Amounts (325)"
msgstr "Belastbare bedragen (325)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_gross
msgid "Taxable Salary"
msgstr "Belastbaar loon"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_rules_taxable_termination
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_rules_taxable_termination
msgid "Taxable Termination Amount"
msgstr "Belastbaar opzegbedrag"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_termination_holidays
msgid "Terminaison Holidays"
msgstr "Einde vakantie"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_termination_holidays_additional_leave
msgid "Terminaison Holidays Additional Leave"
msgstr "Einde vakantie aanvullend verlof"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_termination_holidays_double
msgid "Terminaison Holidays Double Pay"
msgstr "Einde vakantie dubbel vakantiegeld"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_termination_holidays_double_basic
msgid "Terminaison Holidays Double Pay Basic"
msgstr "Einde basis dubbel vakantiegeld"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_termination_holidays_simple
msgid "Terminaison Holidays Simple Pay"
msgstr "Einde vakantie eenvoudig vakantiegeld"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/hr_payroll_employee_departure_holiday_attest.py:0
#: code:addons/l10n_be_hr_payroll/wizard/hr_payroll_employee_departure_notice.py:0
#: model:ir.actions.report,name:l10n_be_hr_payroll.action_report_termination_fees
msgid "Termination"
msgstr "Beëindiging"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model:hr.payroll.structure,payslip_name:l10n_be_hr_payroll.hr_payroll_structure_cp200_employee_termination_fees
msgid "Termination Fees"
msgstr "Beëindigingskosten"

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,name:l10n_be_hr_payroll.action_report_termination_holidays_n
msgid "Termination Holidays Current Year"
msgstr "Beëindiging feestdagen huidig jaar"

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,name:l10n_be_hr_payroll.action_report_termination_holidays_n1
msgid "Termination Holidays Previous Year"
msgstr "Beëindiging feestdagen vorig jaar"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Termination fees"
msgstr "Beëindigingskosten"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_work_entry_type__dmfa_code
msgid "The DMFA Code will identify the work entry in DMFA report."
msgstr "De DMFA-code identificeert het werkinvoer in het DMFA-verslag."

#. module: l10n_be_hr_payroll
#: model:ir.model.constraint,message:l10n_be_hr_payroll.constraint_hr_employee_check_percentage_fiscal_voluntary_rate
msgid "The Fiscal Voluntary rate on wage should be between 0 and 100."
msgstr "Het fiscaal vrijwillige loontarief moet tussen 0 en 100 liggen."

#. module: l10n_be_hr_payroll
#: model:ir.model.constraint,message:l10n_be_hr_payroll.constraint_hr_contract_check_percentage_ip_rate
msgid "The IP rate on wage should be between 0 and 100."
msgstr "Het ip-tarief op het loon moet tussen 0 en 100 liggen."

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_45.py:0
msgid "The VAT or the ZIP number is not specified on your company"
msgstr "Het btw-nummer of de postcode is niet bepaald op je bedrijf"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_45.py:0
msgid ""
"The belgian postcode length shouldn't exceed 4 characters and should contain "
"only numbers for employee %s"
msgstr ""
"De lengte van de Belgische postcode mag niet langer zijn dan 4 tekens en mag "
"alleen cijfers bevatten voor werknemer %s"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_45.py:0
msgid ""
"The company is not correctly configured on your employees. Please be sure "
"that the following pieces of information are set: street, zip, city, phone "
"and vat"
msgstr ""
"Het bedrijf is niet correct geconfigureerd bij je werknemers. Zorg ervoor "
"dat de volgende gegevens ingevuld zijn: straat, postcode, stad, "
"telefoonnummer en btw-nummer"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/res_company.py:0
msgid ""
"The company number should contain digits only, starts with a '0' or a '1' "
"and be 10 characters long."
msgstr ""
"Het ondernemingsnummer mag alleen cijfers bevatten, te beginnen met een '0' "
"of een '1' en moet 10 tekens lang zijn."

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_45.py:0
msgid "The company phone number shouldn't exceed 12 characters"
msgstr "Het telefoonnummer van het bedrijf mag niet langer zijn dan 12 tekens"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__time_off_allocation
msgid ""
"The computed amount is the sum of the new right to time off and the number "
"of time off already taken by the employee. Example: Moving from a full time "
"to a 4/5 part time with 6 days already taken will result into an amount of "
"80%% of 14 days + 6 days (rounded down) = 17 days."
msgstr ""
"Het berekende bedrag is de som van het nieuwe recht op verlof en het aantal "
"reeds opgenomen verlofdagen van de werknemer. Voorbeeld: Overgang van een "
"voltijdse naar een 4/5de functie met reeds 6 opgenomen verlofdagen leidt tot "
"een bedrag van 80%% van 14 dagen + 6 dagen (naar beneden afgerond) = 17 "
"dagen."

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
msgid ""
"The contract %(contract_name)s for %(employee)s is not of one the following "
"types: CDI, CDD. Replacement, For a clearly defined work"
msgstr ""
"Het contract %(contract_name)s voor %(employee)s is niet één van de volgende "
"types: Onbepaalde duur, bepaalde duur, vervanging, voor een duidelijk "
"omschreven werk"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
msgid ""
"The contract %(contract_name)s for %(employee)s is not of one the following "
"types: CP200 Employees or Student"
msgstr ""
"Het contract %(contract_name)s voor %(employee)s is niet één van de volgende "
"types: PC 200 werknemers of studenten"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
msgid "The employee %s doesn't have a specified certificate"
msgstr "De werknemer %s heeft geen gespecificeerd certificaat"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_45.py:0
msgid "The employee first name shouldn't exceed 30 characters for employee %s"
msgstr ""
"De voornaam van de werknemer mag niet langer zijn dan 30 tekens voor "
"werknemer %s"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_double_pay_recovery_wizard.py:0
msgid "The employee is occupied from the %(date_from)s to the %(date_to)s."
msgstr "De werknemer is bezet van %(date_from)s tot %(date_to)s."

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_double_pay_recovery_wizard.py:0
msgid ""
"The employee is occupied from the %(date_from)s to the %(date_to)s. There is "
"nothing to recover as the employee is there for more than 12 months"
msgstr ""
"De werknemer is bezet van %(date_from)s tot %(date_to)s. Er valt niets terug "
"te vorderen aangezien de werknemer er meer dan 12 maanden is"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_employee.py:0
msgid ""
"The employee start notice period should be set before the end notice period"
msgstr ""
"Het begin van de opzegperiode voor de werknemer moet ingesteld worden voor "
"het einde van de opzegperiode"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract__internet
msgid "The employee's internet subcription will be paid up to this amount."
msgstr ""
"De internetonderschrijving van de werknemer wordt tot dit bedrag betaald."

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract__mobile
msgid "The employee's mobile subscription will be paid up to this amount."
msgstr "Het mobiele abonnement van de werknemer wordt tot dit bedrag betaald."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payroll_generate_warrant_payslips_view_form
msgid "The file has been downloaded."
msgstr "Het bestand is gedownload."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_10_form_wizard
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_45_form_wizard
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_individual_account_view_form
msgid ""
"The files won't be posted in the employee portal if you don't have the "
"Documents app."
msgstr ""
"De bestanden worden niet in het werknemersportaal geplaatst als je de "
"Documenten-app niet hebt."

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_dmfa.py:0
msgid ""
"The following employees are linked to work addresses without any ONSS "
"identification code:\n"
" %s"
msgstr ""
"De volgende werknemers zijn gekoppeld aan werkadressen zonder RSZ-"
"identificatiecodes:\n"
" %s "

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_45.py:0
msgid ""
"The following employees don't have a valid private address (with a street, a "
"zip, a city and a country):\n"
"%s"
msgstr ""
"De volgende werknemers hebben geen geldig privé-adres (met een straat, "
"postcode, stad en land):\n"
"%s"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_hr_payroll_employee_lang_view_form
msgid ""
"The following employees have an invalid language for the selected salary "
"structure.\n"
"                        <br/>\n"
"                        Please assign them a language below before "
"continuing."
msgstr ""
"De volgende werknemers hebben een ongeldige taal voor de geselecteerde "
"salarisstructuur.\n"
"                        <br/>\n"
"                        Wijs hen hieronder een taal toe voordat je verder "
"gaat."

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_dmfa.py:0
msgid ""
"The following work entry types do not have any DMFA code set:\n"
" %s"
msgstr ""
"De volgende soorten werkboekingen hebben geen DMFA-code:\n"
"%s"

#. module: l10n_be_hr_payroll
#: model:ir.model.constraint,message:l10n_be_hr_payroll.constraint_hr_contract_check_percentage_group_insurance_rate
msgid ""
"The group insurance salary sacrifice rate on wage should be between 0 and "
"100."
msgstr ""
"Het keuzepercentage op de groepsverzekering moet tussen 0 en 100 liggen."

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_274_XX.py:0
msgid "The payslips should be from the same company."
msgstr "De loonstroken moeten van hetzelfde bedrijf zijn."

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_274_XX.py:0
msgid ""
"The payslips should cover the same period:\n"
"%s"
msgstr ""
"De loonstroken moeten dezelfde periode bestrijken:\n"
"%s"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_45.py:0
msgid "The phone number is not specified on your company"
msgstr "Het telefoonnummer is niet bepaald op je bedrijf"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_view_form
msgid ""
"The printed pdf contains all the data to encode into the 'DmfA declaration "
"web interface' that you can find"
msgstr ""
"De afgedrukte PDF bevat alle gegevens die gecodeerd moeten worden in de "
"'DmfA aangifte webinterface' die je kan vinden"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_eco_vouchers_wizard.py:0
msgid ""
"The reference period is from the <b>1st of June %(previous_year)s</b> to the "
"<b>31st of May %(reference_year)s</b>"
msgstr ""
"De referentieperiode loopt van <b>1 juni %(previous_year)s</b> tot en met "
"<b>31 mei %(reference_year)s</b>"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_payslip.py:0
msgid ""
"The seized amount (%(seized_amount)s€) is above the belgian ceilings. Given "
"a global net salary of %(net_amount)s€ for the pay period and "
"%(dependent_children)s dependent children, the maximum seizable amount is "
"equal to %(max_seizable_amount)s€"
msgstr ""
"Het in beslag genomen bedrag (%(seized_amount)s€) ligt boen de Belgische "
"maxima. Bij een globaal nettoloon van %(net_amount)s€ voor de loonperiode en "
"%(dependent_children)s kinderen ten laste, is het maximaal beslagbaar bedrag "
"gelijk aan %(max_seizable_amount)s€"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_contract.py:0
msgid "The time Percentage in R&D should be between 0-100"
msgstr "Het tijdpercentage in R&D moet tussen 0-100 liggen"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_contract.py:0
msgid ""
"The wage is under the minimum scale of %(amount)s€ for a seniority of "
"%(years)s years."
msgstr ""
"Het loon ligt onder de minimumbarema van %(amount)s€ voor een anciënniteit "
"van %(years)s jaren."

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__absence_work_entry_type_id
msgid ""
"The work entry type used when generating work entries to fit full time "
"working schedule."
msgstr ""
"Het type werkboeking dat wordt gebruikt bij het genereren van werkboekingen "
"die passen bij een voltijds werkschema."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_notice
msgid "Theoretical Notice Duration"
msgstr "Theoretische opzegtermijn"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_dmfa.py:0
msgid "There is no defined expeditor number for the company."
msgstr "Er is geen vastgesteld expeditienummer voor het bedrijf."

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/hr_payroll_generate_warrant_payslips.py:0
msgid "There is no payslip to generate for those employees"
msgstr "Er hoeft geen loonstrook te worden gegenereerd voor die werknemers"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_274_XX.py:0
msgid "There is no valid payslip to declare."
msgstr "Er is geen geldig te verklaren loonstrook."

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Thirteen Month"
msgstr "Dertiende maand"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/certificate.py:0
msgid "This certificate is not valid, its validity has expired."
msgstr "Dit certificaat is niet geldig, de geldigheid is verlopen."

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_payslip.py:0
msgid "This document is a translation. This is not a legal document."
msgstr "Dit document is een vertaling. Dit is geen juridisch document."

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_double_pay_recovery_wizard.py:0
msgid "This employee doesn't have a first contract date"
msgstr "Deze werknemer heeft geen eerste contractdatum"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_contract.py:0
msgid "This feature can only be used on a single contract."
msgstr "Deze functie kan slechts op één contract worden toegepast."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_eco_vouchers_wizard_view_form
msgid ""
"This will add the eco-vouchers amounts on the open payslips in this batch "
"associated with the respective employees, and create new payslips where they "
"don't exist. Are you sure you want to proceed?"
msgstr ""
"Dit zal de bedragen van de ecocheques toevoegen aan de openstaande "
"loonstroken in deze batch die gekoppeld zijn aan de respectievelijke "
"werknemers, en nieuwe loonstroken aanmaken waar ze nog niet bestaan. Weet je "
"zeker dat je verder wilt gaan?"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__threshold
msgid "Threshold"
msgstr "Drempel"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_leave
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Time Off"
msgstr "Verlof"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_leave_allocation
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__time_off_allocation
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_hr_payroll_schedule_change_wizard_view_form
msgid "Time Off Allocation"
msgstr "Verloftoewijzing"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__time_off_n_ids
msgid "Time Off N"
msgstr "Verlof N"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__holiday_status_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__leave_type_id
msgid "Time Off Type"
msgstr "Verloftype"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__rd_percentage
msgid "Time Percentage in R&D"
msgstr "Tijdpercentage in R&D"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__time_off_allocated
msgid "Time off allocated during current year"
msgstr "Verlof toegewezen tijdens het lopende jaar"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "Time off already taken"
msgstr "Reeds ingenomen verlof"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__time_off_taken
msgid "Time off taken during current year"
msgstr "Verlof genomen tijdens het lopende jaar"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "To"
msgstr "Tot"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_alloc_employee_view_tree
msgid "To Allocate"
msgstr "Toe te wijzen"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__l10n_be_dependent_children_attachment
msgid ""
"To benefit from this increase in the elusive or non-transferable quotas, the "
"worker whose remuneration is subject to seizure or transfer, must declare it "
"using a form, the model of which has been published in the Belgian Official "
"Gazette. of 30 November 2006.\n"
"\n"
"He must attach to this form the documents establishing the reality of the "
"charge invoked.\n"
"\n"
"Source: Opinion on the indexation of the amounts set in Article 1, paragraph "
"4, of the Royal Decree of 27 December 2004 implementing Articles 1409, § 1, "
"paragraph 4, and 1409, § 1 bis, paragraph 4 , of the Judicial Code relating "
"to the limitation of seizure when there are dependent children, MB, December "
"13, 2019."
msgstr ""
"Om de verhoging te kunnen genieten van de delen die niet vatbaar zijn voor "
"beslag of overdracht moet de werknemers met loonbeslag of loonoverdracht "
"hiervan aangifte doen. De aangifte moet gebeuren aan de hand van een "
"formulier waarvan het model verschenen is in het Belgisch Staatsblad van 30 "
"november 2006. \n"
"\n"
"Bij het formulier moet hij de bewijsstukken voegen die aantonen dat de "
"aangevoerde last daadwerkelijk bestaat.\n"
"\n"
"Bron: Bericht over de indexering van de bedragen vermeld in artikel 1, "
"vierde lid, van het Koninklijk Besluit van 27 december 2004 ter uitvoering "
"van artikel 1409, § 1, vierde lid, en 1409, § 1bis, vierde lid, van het "
"Gerechtelijk Wetboek inzake de beperking van de inbeslagneming wanneer er "
"kinderen ten laste zijn, B.S., 19 december 2022."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "To the attention of the next employer"
msgstr "Ter attentie van de volgende werkgever"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "To the attention of the worker"
msgstr "Ter attentie van de werknemer"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_employee_form__l10n_be_view_for
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_users_view_form
msgid "Toggle Explanation"
msgstr "Toggle uitleg"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_termination_total
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_total_n
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_total_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Total"
msgstr "Totaal"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "Total (10.a + 10.b)"
msgstr "Totaal (10.a + 10.b)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "Total (18.a.1)"
msgstr "Totaal (18.a.1)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Total (FTE)"
msgstr "Totaal (FTE)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Total Basic before ONSS"
msgstr "Totaal Basis voor RSZ"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Total Employer Cost"
msgstr "Totale werkgeverskost"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Total Full Time (Code 1021)"
msgstr "Totaal voltijds (Code 1021)"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
msgid "Total Full Time (code 1021)"
msgstr "Totaal voltijds (Code 1021)"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
msgid "Total Full Time + Part Time (code 1023)"
msgstr "Totaal voltijds + deeltijds (Code 1023)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Total Full Time + Part-Time (Code 1023)"
msgstr "Totaal voltijds + deeltijds (Code 1023)"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_gross_with_ip
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_salary_rule_category_gross_with_ip
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Total Gross"
msgstr "Totaal bruto"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "Total Gross Before ONSS"
msgstr "Totaal Basis voor RSZ"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "Total Net"
msgstr "Totaal netto"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Total Net (Advance included)"
msgstr "Totaal netto (voorschot inbegrepen)"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
msgid "Total Part Time (code 1022)"
msgstr "Totaal deeltijds (Code 1022)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Total Part-Time (Code 1022)"
msgstr "Totaal deeltijds (Code 1022)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Total Year"
msgstr "Totaal jaar"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Total actual number of hours worked or FTE"
msgstr "Totaal werkelijk aantal gewerkte uren of FTE"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Total allocated days"
msgstr "Totaal toegewezen dagen"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"Total amount paid or awarded (the taxable part must be added to Total "
"remuneration 2.060)"
msgstr ""
"Totaal betaald of toegekend bedrag (het belastbaar gedeelde moet toevoegd "
"worden aan de totale bezoldiging 2.060)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Total basic wage"
msgstr "Totaal basisloon"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_termination_total_cost
msgid "Total cost employer"
msgstr "Totale kosten werkgever"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Total gross wage"
msgstr "Totaal brutoloon"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Total net wage"
msgstr "Totaal nettoloon"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Total of all Contributions:"
msgstr "Totaal van alle bijdragen:"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Total time off days"
msgstr "Totaal aantal dagen verlof"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "Total: (14a + 14b + 14c)"
msgstr "Totaal: (14a + 14b + 14c)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Totaux :"
msgstr "Totalen:"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__train_transport_reimbursed_amount
msgid "Train Transport Reimbursed amount"
msgstr "Treinvervoer Terugbetaald bedrag"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__train_transport_employee_amount
msgid "Train transport paid by the employee (Monthly)"
msgstr "Treinvervoer betaald door de werknemer (maandelijks)"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_training
msgid "Training"
msgstr "Opleiding"

#. module: l10n_be_hr_payroll
#: model:hr.leave.type,name:l10n_be_hr_payroll.holiday_type_training
msgid "Training Time Off"
msgstr "Trainingsverlof"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "Transportation"
msgstr "Transport"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__type_treatment
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__type_treatment
msgid "Treatment Type"
msgstr "Behandelingstype"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Unemployment with company supplement"
msgstr "Werkloosheid met bedrijfstoeslag"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "University education"
msgstr "Universitair onderwijs"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_unjustified_reason
msgid "Unjustified Reason"
msgstr "Ongerechtvaardigde reden"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Unpaid Time Off"
msgstr "Onbetaald verlof"

#. module: l10n_be_hr_payroll
#: model:hr.leave.type,name:l10n_be_hr_payroll.holiday_type_unpredictable
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_unpredictable
msgid "Unpredictable Reason"
msgstr "Onvoorspelbare reden"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_termination_unreasonable_dismissal
msgid "Unreasonable dismissal"
msgstr "Onredelijk ontslag"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_45.py:0
msgid "Unsupported country code %s. Please contact an administrator."
msgstr "Niet ondersteunde landcode %s. Neem contact op met een beheerder."

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_schedule_change_allocation
msgid "Update allocation on schedule change"
msgstr "Toewijzing bijwerken bij wijziging van de planning"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract__has_bicycle
msgid "Use a bicycle as a transport mode to go to work"
msgstr "Gebruik een fiets als transportmodus om naar het werk te gaan"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__employee_ids
msgid "Use this to limit the employees to compute"
msgstr "Gebruik dit om de berekeningen van de werknemers te beperken"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_res_users
msgid "User"
msgstr "Gebruiker"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__transport_mode_public
msgid "Uses another public transportation"
msgstr "Maakt gebruik van een ander openbaar vervoer"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__transport_mode_car
msgid "Uses company car"
msgstr "Maakt gebruik van bedrijfswagen"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__transport_mode_private_car
msgid "Uses private car"
msgstr "Maakt gebruik van een eigen auto"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__transport_mode_train
msgid "Uses train transportation"
msgstr "Maakt gebruik van treinvervoer"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "VAT Number"
msgstr "Btw-nummer:"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "VAT Number:"
msgstr "Btw-nummer:"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__xml_validation_state__done
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__xml_validation_state__done
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_10__xml_validation_state__done
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_45__xml_validation_state__done
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_dmfa__validation_state__done
msgid "Valid"
msgstr "Geldig"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_work_entry_type_view_form
msgid "Valid For Advantages"
msgstr "Van toepassing op voordelen"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_december_slip_wizard_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_double_pay_recovery_wizard_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_notice
msgid "Validate"
msgstr "Bevestigen"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Validate & Compute holiday attests"
msgstr "Bevestig & berekend vakantieattesten"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_notice
msgid "Validate & Compute termination fees"
msgstr "Bevestig & berekend beëindigingskosten"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__validation_state
msgid "Validation State"
msgstr "Validatiestatus"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Various bonuses"
msgstr "Diverse bonussen"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_dmfa__declaration_type__batch
msgid "Via Batch"
msgstr "Via batch"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_dmfa__declaration_type__web
msgid "Via Web"
msgstr "Via web"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__current_wage
msgid "Wage"
msgstr "Salaris"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__wage_with_holidays
msgid "Wage With Sacrifices"
msgstr "Loonoffer"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__state__waiting
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__state__waiting
msgid "Waiting"
msgstr "Wachten"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__warrants_cost
msgid "Warrant monthly cost for the employer"
msgstr "Garantie maandelijkse kosten voor de werkgever"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__warrant_value_employee
msgid "Warrant monthly value for the employee"
msgstr "Garantie maandelijkse waarde voor de werknemer"

#. module: l10n_be_hr_payroll
#: model:hr.payroll.structure,payslip_name:l10n_be_hr_payroll.hr_payroll_structure_cp200_structure_warrant
msgid "Warrants"
msgstr "Warrants"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid ""
"We draw your attention to the fact that this information is based on the "
"data in Odoo and / or that you\n"
"                            have introduced in Odoo and that it is important "
"that they be accompanied by a verification on your part\n"
"                            according to the particularities related to "
"contract of the worker or your company which Odoo would not\n"
"                            know."
msgstr ""
"We vestigen je aandacht op het feit dat deze informatie gebaseerd is op de "
"gegevens in Odoo en/of die je\n"
"hebt ingevoerd in Odoo en dat het belangrijk is dat jij ze controleert in "
"functie van\n"
"de bijzonderheden met betrekking tot het contract van de werknemer of je "
"bedrijf die Odoo niet zou\n"
"kennen."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payroll_generate_warrant_payslips_view_form
msgid ""
"When you're done with the commission edition, click on the 'Generate "
"Payslip' button to generate a batch of payslips using the commissions you've "
"provided."
msgstr ""
"Als je klaar bent met het bewerken van de commissie, klik je op de knop "
"'Loonstrook genereren' om een batch loonstroken te genereren met de "
"commissies die je hebt opgegeven."

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract_history__has_valid_schedule_change_contract
msgid ""
"Whether or not the employee has a contract candidate for a working schedule "
"change"
msgstr ""
"Of de werknemer al dan niet een contractkandidaat heeft voor een wijzinging "
"van het werkrooster"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_social_security_certificate__aggregation_level__company
msgid "Whole Company"
msgstr "Gans bedrijf"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_employee__spouse_fiscal_status__high_pension
msgid "With High Pensions"
msgstr "Met hoge pensioenen"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_employee__spouse_fiscal_status__high_income
msgid "With High income"
msgstr "Met een hoog inkomen"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_employee__spouse_fiscal_status__low_income
msgid "With Low Income"
msgstr "Met een laag inkomen"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_employee__spouse_fiscal_status__low_pension
msgid "With Low Pensions"
msgstr "Met lage pensioenen"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Withdrawal not retained"
msgstr "Niet weerhouden inhouding"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_double_holiday_pay_p_p
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_atn_warrant_p_p
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_withholding_taxes
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_withholding_taxes
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_thirteen_month_p_p
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_pfi_salary_withholding_taxes
msgid "Withholding Tax"
msgstr "Bronbelasting"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__payment_reference
msgid "Withholding Tax Payment Reference"
msgstr "Betalingsreferentie bronbelasting"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_head_pp
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__pp_amount
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Withholding Taxes"
msgstr "Bronbelastingen"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_double_holiday_pay_pp_total
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_withholding_taxes_total
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_withholding_taxes_total
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_rules_withholding_taxes_total
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_rules_withholding_taxes_total
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_thirteen_month_pp_total
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_salary_rule_category_withholding_taxes_total
msgid "Withholding Taxes (Total)"
msgstr "Bronbelastingen (Totaal)"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Withholding Taxes Capping (Bachelors)"
msgstr "Plafonnering bronbelasting (Bachelors)"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "Withholding Taxes Deduction"
msgstr "Aftrek bronbelasting"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_report__l10n_be_withholding_taxes_exemption
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "Withholding Taxes Exemption"
msgstr "Vrijstelling van bronbelasting"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Withholding Taxes Exemption (Scientific Research) - Bachelors"
msgstr "Vrijstelling van bronbelasting (wetenschappelijk onderzoek) - Bachelor"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid ""
"Withholding Taxes Exemption (Scientific Research) - Doctors / Civil Engineers"
msgstr ""
"Vrijstelling van bronbelasting (wetenschappelijk onderzoek) - Doctors / "
"Burgerlijk ingenieurs"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Withholding Taxes Exemption (Scientific Research) - Masters"
msgstr "Vrijstelling van bronbelasting (wetenschappelijk onderzoek) - Masters"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_withholding_reduction
msgid "Withholding reduction"
msgstr "Inhouding bronbelasting"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_employee__spouse_fiscal_status__without_income
msgid "Without Income"
msgstr "Zonder inkomen"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips_line__wizard_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__wizard_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_line_wizard__wizard_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_line_wizard__wizard_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard_line__wizard_id
msgid "Wizard"
msgstr "Wizard"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_work_accident
msgid "Work Accident"
msgstr "Werkongeval"

#. module: l10n_be_hr_payroll
#: model:hr.leave.type,name:l10n_be_hr_payroll.holiday_status_work_accident
msgid "Work Accident Time Off"
msgstr "Verlof wegens werkongeval"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Work Address:"
msgstr "Werkadres:"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_work_entry_daily_benefit_report
msgid "Work Entry Related Benefit Report"
msgstr "Rapport werkgerelateerd voordeel"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_dmfa_location_unit
msgid "Work Place defined by ONSS"
msgstr "Werkplek gedefinieerd door ONSS"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Work Place:"
msgstr "Werkplek:"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__work_time_rate
msgid "Work Time Rate"
msgstr "Werktijd"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Work accident policy number"
msgstr "Polisnummer arbeidsongeval"

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_hr_payroll_action_work_address_codes
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_company__dmfa_location_unit_ids
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_location_unit_view_tree
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_config_settings_view_form
msgid "Work address DMFA codes"
msgstr "DMFA codes werkadres"

#. module: l10n_be_hr_payroll
#: model:hr.payroll.dashboard.warning,name:l10n_be_hr_payroll.hr_payroll_dashboard_warning_invalid_work_address
msgid "Work addresses without ONSS identification code"
msgstr "Werkadres zonder RSZ-identificatiecode"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_work_entry_type__meal_voucher
msgid "Work entries counts for meal vouchers"
msgstr "Aantal werkboekingen voor maaltijdcheques"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_work_entry_type__private_car
msgid "Work entries counts for private car reimbursement"
msgstr "Aantal werkboekingen voor terugbetaling gebruik eigen wagen"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_work_entry_type__representation_fees
msgid "Work entries counts for representation fees"
msgstr "Aantal werkboekingen voor representatiekosten"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_work_entry_type__leave_right
msgid "Work entries counts for time off right for next year."
msgstr "Aantal werkboekingen voor recht op verlof van volgend jaar"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__presence_work_entry_type_id
msgid "Work entry type for regular attendances."
msgstr "Type werkboeking voor regelmatige aanwezigheid."

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__work_time_rate
msgid ""
"Work time rate versus full time working schedule, should be between 0 and "
"100 %."
msgstr ""
"Werktijdpercentage versus fulltime werkschema, moet tussen 0 en 100% liggen."

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Worker"
msgstr "Werknemer"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Worker Code:"
msgstr "Arbeiderscode:"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_head_onss
msgid "Worker Social Contribution"
msgstr "Sociale bijdragen van de werknemer"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Workers"
msgstr "Werknemers"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid ""
"Workers for whom the company has submitted a DIMONA declaration or who are "
"registered in the general staff register"
msgstr ""
"Werknemers waarvoor het bedrijf een DIMONA-aangifte heeft ingediend of die "
"in het algemene personeelsregister zijn ingeschreven"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa_location_unit__partner_id
msgid "Working Address"
msgstr "Werkadres"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "Working Schedule"
msgstr "Werkschema"

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.schedule_change_wizard_action
#: model:ir.actions.server,name:l10n_be_hr_payroll.action_working_schedule_change_request
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_history_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_hr_payroll_schedule_change_wizard_view_form
msgid "Working Schedule Change"
msgstr "Wijziging werkschema"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_hr_payroll_schedule_change_wizard.py:0
msgid ""
"Working schedule would stay unchanged by this action. Please select another "
"working schedule."
msgstr ""
"Het werkschema blijft ongewijzigd door deze actie. Selecteer een ander "
"werkschema."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Workplace"
msgstr "Werkplaats"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__xls_file
msgid "XLS file"
msgstr "XLS-bestand"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__xml_file
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__xml_file
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__xml_file
msgid "XML File"
msgstr "XML-bestand"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__xml_filename
msgid "XML Filename"
msgstr "XML-bestandnaam"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__xml_file
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__dmfa_xml
msgid "XML file"
msgstr "XML-bestand"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__xls_filename
msgid "Xls Filename"
msgstr "Xls-bestandnaam"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__xml_filename
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__xml_filename
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__xml_filename
msgid "Xml Filename"
msgstr "Xml-bestandsnaam"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__xml_validation_state
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__xml_validation_state
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__xml_validation_state
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__xml_validation_state
msgid "Xml Validation State"
msgstr "XML-validatiestatut"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__year
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__year
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__year
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__year
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__year
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__year
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__year
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account__year
msgid "Year"
msgstr "Jaar"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__year
msgid "Year of the period to consider"
msgstr "Jaar van de in aanmerking te nemen periode"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_year_end_bonus
msgid "Year-end bonus"
msgstr "Eindejaarsbonus"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__yearly_commission
msgid "Yearly Commission"
msgstr "Jaarlijkse Commissie"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__yearly_commission_cost
msgid "Yearly Commission Cost"
msgstr "Jaarlijkse commissiekosten"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract__eco_checks
msgid "Yearly amount the employee receives in the form of eco vouchers."
msgstr "Jaarlijks bedrag ontvangt de werknemer in de vorm van ecovouchers."

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/hr_payroll_allocating_paid_time_off.py:0
msgid "You don't have the right to do this. Please contact your administrator!"
msgstr ""
"Je hebt het recht niet om dit te doen. Neem contact op met je beheerder!"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_273S.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_274_XX.py:0
#: code:addons/l10n_be_hr_payroll/wizard/hr_payroll_allocating_paid_time_off.py:0
#: code:addons/l10n_be_hr_payroll/wizard/hr_payroll_employee_departure_holiday_attest.py:0
#: code:addons/l10n_be_hr_payroll/wizard/hr_payroll_employee_departure_notice.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_december_slip_wizard.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_double_pay_recovery_wizard.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_eco_vouchers_wizard.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_group_insurance_wizard.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "You must be logged in a Belgian company to use this feature"
msgstr ""
"Je moet ingelogd zijn in een Belgisch bedrijf om deze functie te kunnen "
"gebruiken"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/hr_payroll_generate_warrant_payslips.py:0
msgid "You should upload a file to import first."
msgstr "Je moet eerste een bestand uploaden om te importeren."

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Youth Hiring Plan"
msgstr "Plan voor het aanwerven van jongeren"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_youth_time_off
msgid "Youth Time Off"
msgstr "Jeugdverlof"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "a) Advantages"
msgstr "a) Voordelen"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid ""
"a) Amount of income paid or allocated, broken down according to the "
"applicable withholding tax rate:"
msgstr ""
"a) Bedrag van de betaalde of toegekende inkomsten, opgesplitst naargeland de "
"aanslagvoet van de roerende voorheffing die van toepassing is:"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid "a) Amount of income paid or attributed:"
msgstr "a) Bedrag van de betaalde of toegekende inkomsten:"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "a) Code 250"
msgstr "a) Code 250"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "a) Early vacation pay (other than referred to under 11b and 12b):"
msgstr "a) Vervroegd vakantiegeld (ander dan bedoeld onder 11b en 12b):"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "a) Forfaitaires"
msgstr "a) Forfaitair"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "a) Movements by cycle or by speed-pedelec"
msgstr "a) Verplaatsingen met een rijwiel of speed pedelec"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "a) Nom et prénom, ou dénomination"
msgstr "a) Naam en voornaam, of benaming"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "a) Normal contributions and premiums"
msgstr "a) Gewone bijdragen en premies"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "a) Occasional worker in the Horeca sector"
msgstr "a) Gelegenheidswerknemer in de horecasector"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "a) Public transport"
msgstr "a) Openbaar gemeenschappelijk vervoer"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "a) Remuneration"
msgstr "a) Bezoldigingen"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "a) Remuneration:"
msgstr "a) Bezoldigingen:"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "a) Repetitive expenses"
msgstr "a) Terugkerende uitgaven"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "a) Total number of overtime hours actually worked"
msgstr "a) Totaal aantal werkelijk gepresteerde overuren"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "a) Voluntary overtime worked from 01.07.2023 as part of the relaunch:"
msgstr ""
"a) vanaf 01.07.2023 gepresteerde vrijwillige overuren in het kader van de "
"relance:"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "a) based on income received from employer"
msgstr "a) berekend op inkomsten verkregen van de werkgever"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "a) from employers who do not use the cash register system"
msgstr "a) bij werkgevers zonder een geregistreerd kassasysteem"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_view_form
msgid "and at the"
msgstr "en op de"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid ""
"b) Amount recognized as an expense in 2023 if this amount does not "
"correspond to the amount indicated in section 4.a:"
msgstr ""
"b) Bedrag dat in kosten is geboekt in 2023 indien dat bedrag niet "
"overeenstemt met het in rubriek 4.a. ingevulde bedrag:"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid ""
"b) Amount recognized as an expense in 2023 if this amount does not "
"correspond to the total of the amounts indicated in sections 5.a.1) and 5."
"a.2):"
msgstr ""
"b) Bedrag dat in kosten is geboekt in 2023 indien dat bedrag niet "
"overeenstemt met het totaal van de in de rubrieken 5.a.1) en 5.a.2) "
"ingevulge bedragen:"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "b) Arrears"
msgstr "b) Achterstallen"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "b) Arrears (other than referred to in 9b, 11c and 12c):"
msgstr "b) Achterstallen (andere dan bedoeld onder 9b, 11c en 12c):"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"b) Basis for the calculation of the additional salary relating to overtime "
"giving right to a reduction of:"
msgstr ""
"b) Berekeningsgrondslag van de overwerktoeslag voor overuren die recht geven "
"op een vermindering van:"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "b) Benefits in kind:"
msgstr "b) Voordelen van alle aard"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "b) Contributions and bonuses for individual continuation"
msgstr "b) Bijdragen en premies voor individuele voortzetting"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "b) Early vacation pay"
msgstr "b) Vervroegd vakantiegeld"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "b) Employer-specific expenses"
msgstr "b) Eigen kosten voor de werkgever"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "b) Organized public transport"
msgstr "b) Georganiseerd gemeenschappelijk vervoer"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "b) Other codes"
msgstr "b) Andere codes"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "b) Other expenses resulting directly from employment in Belgium"
msgstr ""
"b) Andere uitgaven die rechtstreeks voortkomen uit de tewerkstelling in "
"België"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "b) Retired worker in the care sector"
msgstr "b) Gepensioneerde werkzaam in de zorgsector"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "b) Rue et numéro/boîte"
msgstr "b) Straat en nummer/bus"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "b) Réels"
msgstr "b) Werkelijke"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "b) Voluntary overtime worked in 2022 as part of the recovery"
msgstr ""
"b) In 2022 gepresteerde vrijwillige overuren in het kader van de relance"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "b) based on income received from a related foreign company"
msgstr ""
"b) berekend op inkomsten verkregen van een buitenlandse verbonden "
"vennootschap"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "b) from employers who use the cash register system"
msgstr "b) bij werkgevers met een geregistreerd kassasysteem"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "c) Arrears"
msgstr "c) Achterstallen"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"c) Cancellation indemnities (other than referred to under 11d and 12d) and "
"redeployment indemnities:"
msgstr ""
"c) Opzeggingsvergoedingen (ander dan bedoeld onder 11d en 12d) en "
"inschakelingsvergoedingen:"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "c) Code pays, code postal et commun"
msgstr "c) Landcode, postcode en gemeente"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"c) Contributions and premiums for free supplementary pensions for salaried "
"workers"
msgstr ""
"c) Bijdragen en premies voor het vrij aanvullend pensioen voor werknemers"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "c) Gross amount of remuneration"
msgstr "c) Bruto bezoldigingen"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "c) Loyalty stamps"
msgstr "c) Getrouwheidszegels"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "c) Other means of transport"
msgstr "c) Ander vervoermiddel"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"c) Provided in 2021 as part of the fight against COVID-19 and / or as part "
"of the recovery plan or net overtime worked in 2021 in the public sector"
msgstr ""
"c) Gepresteerd in 2021 in de strijd tegen COVID-19 en/of in het kader van de "
"relance of in 2021 gepresteerde netto overuren in de openbare sector"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "c) Tips"
msgstr "c) Fooien"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "d) Cancellation indemnities"
msgstr "d) Opzeggingsvergoedingen"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "d) Frontier workers"
msgstr "d) Grensarbeiders"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "d) Numéro d'identification fiscale (facultatif)"
msgstr "d) Fiscaal identificatienummer (facultatief)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "d) Remuneration for the month of December (Public authority) (2):"
msgstr "d) Bezoldigingen van de maand december (Overheid) (2):"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "d) Stock options"
msgstr "d) Aandelenopties"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_notice_duration_day
msgid "days"
msgstr "dagen"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "departments:"
msgstr "departementen:"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"e) Benefit of any kind resulting from temporary unemployment benefits "
"directly reimbursed to ONEM"
msgstr ""
"e) Rechtstreeks aan de RVA terugbetaalde tijdelijke werkloosheidsuitkeringen"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "e) Exempt income received in fulfillment of a flexi-job contract"
msgstr ""
"e) Vrijgestelde inkomsten verkregen in uitvoering van een flexi-"
"jobarbeidsovereenkomst"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "f) Beneficiary premium"
msgstr "f) Winstpremie"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.273S_xml_report
msgid "false"
msgstr "onwaar"

#. module: l10n_be_hr_payroll
#: model:hr.contract.type,name:l10n_be_hr_payroll.l10n_be_contract_type_clearly_defined_work
msgid "for clearly defined work"
msgstr "voor duidelijk bepaald werk"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_view_form
msgid "for more informations"
msgstr "voor meer informatie"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.273S_xml_report
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.finprof_xml_report_single_declaration
msgid "fr"
msgstr "fr"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "g) Mobility budget: total amount"
msgstr "g) Mobiliteitsbudget: totaalbedrag"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_10__state__generate
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_45__state__generate
msgid "generate"
msgstr "genereren"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_10__state__get
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_45__state__get
msgid "get"
msgstr "toevoegen"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "h) First employment agreement: compensatory supplement"
msgstr "h) Startbaanovereenkomst: compenserende toeslag"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_view_form
msgid "here"
msgstr "hier"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "hours/week"
msgstr "uren/week"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"i) Volunteer firefighter, volunteer ambulance driver and volunteer Civil "
"Protection agent: allowances which are taken into account for the exemption"
msgstr ""
"i) Vrijwillige brandweerman, vrijwillige ambulancier voor prestaties "
"geleverd in het kader van dringende geneeskundige hulpverlening en "
"vrijwilliger bij de Civiele Bescherming: voor vrijstelling in aanmerking "
"komende vergoedingen"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__disabled_children_bool
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_users__disabled_children_bool
msgid "if recipient children is/are declared disabled by law"
msgstr "als ontvangende kinderen door de wet gehandicapt zijn/worden verklaard"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__disabled_spouse_bool
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_users__disabled_spouse_bool
msgid "if recipient spouse is declared disabled by law"
msgstr "als de ontvangende echtgenoot bij wet gehandicapt wordt verklaard"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_employee_form__l10n_be_view_for
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_users_view_form
msgid "if spouse has professionnel income or not"
msgstr "als echtgeno(o)t(e) beroepsinkomen heeft of niet"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "j) Student job"
msgstr "j) Studentenjob"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_notice_duration_month
msgid "months"
msgstr "maanden"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_users_view_form
msgid "number of dependent children declared as disabled"
msgstr "aantal kinderen ten laste die als gehandicapt zijn opgegeven"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_view_form
msgid "process overview"
msgstr "procesoverzicht"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_view_form
msgid "the official documentation"
msgstr "de officiële documentatie"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "to"
msgstr "aan"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_notice_duration_week
msgid "weeks"
msgstr "weken"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_belgium_light_payslip
msgid ""
"€<span class=\"me-2\"/>\n"
"                (="
msgstr ""
"€<span class=\"me-2\"/>\n"
"                (="

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_belgium_payslip
msgid ""
"€<span class=\"me-2\"/>\n"
"            (="
msgstr ""
"€<span class=\"me-2\"/>\n"
"            (="
