<?xml version="1.0" encoding="utf-8"?>
<odoo auto_sequence="1">
    <record id="account_report_gstr3b" model="account.report">
        <field name="name">GSTR-3B</field>
        <field name="country_id" ref="base.in"/>
        <field name="filter_multi_company">tax_units</field>
        <field name="root_report_id" ref="account.generic_tax_report"/>
        <field name="column_ids">
            <record id="account_report_gstr3b_base_column" model="account.report.column">
                <field name="name">Base</field>
                <field name="expression_label">tax_base</field>
            </record>
            <record id="account_report_gstr3b_cgst_column" model="account.report.column">
                <field name="name">CGST</field>
                <field name="expression_label">tax_cgst</field>
            </record>
            <record id="account_report_gstr3b_sgst_column" model="account.report.column">
                <field name="name">SGST</field>
                <field name="expression_label">tax_sgst</field>
            </record>
            <record id="account_report_gstr3b_igst_column" model="account.report.column">
                <field name="name">IGST</field>
                <field name="expression_label">tax_igst</field>
            </record>
            <record id="account_report_gstr3b_cess_column" model="account.report.column">
                <field name="name">CESS</field>
                <field name="expression_label">tax_cess</field>
            </record>
        </field>
        <field name="line_ids">
            <!-- 3.1 Details of Outward Supplies and inward supplies liable to reverse charge -->
            <record id="account_report_gstr3b_3_1" model="account.report.line">
                <field name="name">3.1 Details of Outward Supplies and inward supplies liable to reverse charge</field>
                <field name="hierarchy_level">0</field>
                <field name="expression_ids">
                    <record id="account_report_gstr3b_3_1_expression_tax_base" model="account.report.expression">
                        <field name="label">tax_base</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">3_1_A.tax_base + 3_1_B.tax_base + 3_1_C.tax_base + 3_1_D.tax_base + 3_1_E.tax_base</field>
                    </record>
                    <record id="account_report_gstr3b_3_1_expression_tax_cgst" model="account.report.expression">
                        <field name="label">tax_cgst</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">3_1_A.tax_cgst + 3_1_D.tax_cgst</field>
                    </record>
                    <record id="account_report_gstr3b_3_1_expression_tax_sgst" model="account.report.expression">
                        <field name="label">tax_sgst</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">3_1_A.tax_sgst + 3_1_D.tax_sgst</field>
                    </record>
                    <record id="account_report_gstr3b_3_1_expression_tax_igst" model="account.report.expression">
                        <field name="label">tax_igst</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">3_1_A.tax_igst + 3_1_B.tax_igst + 3_1_D.tax_igst</field>
                    </record>
                    <record id="account_report_gstr3b_3_1_expression_tax_cess" model="account.report.expression">
                        <field name="label">tax_cess</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">3_1_A.tax_cess + 3_1_B.tax_cess + 3_1_D.tax_cess</field>
                    </record>
                </field>
                <field name="children_ids">
                    <record id="account_report_gstr3b_3_1_a" model="account.report.line">
                        <field name="name">(a) Outward Taxable supplies (other than zero rated, nil rated and exempted)</field>
                        <field name="code">3_1_A</field>
                            <field name="expression_ids">
                                <record id="account_report_gstr3b_3_1_a_tax_base" model="account.report.expression">
                                    <field name="label">tax_base</field>
                                    <field name="engine">domain</field>
                                    <field name="formula" eval="[('move_id.l10n_in_gst_treatment', '!=', 'overseas'), ('move_id.move_type', '=', 'out_invoice'), ('tax_tag_ids','in',[ref('l10n_in.tax_tag_base_sgst'), ref('l10n_in.tax_tag_base_cgst'), ref('l10n_in.tax_tag_base_igst'), ref('l10n_in.tax_tag_base_cess')])]"/>
                                    <field name="subformula">-sum</field>
                                </record>
                                <record id="account_report_gstr3b_3_1_a_tax_cgst" model="account.report.expression">
                                        <field name="label">tax_cgst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.l10n_in_gst_treatment', '!=', 'overseas'), ('move_id.move_type', '=', 'out_invoice'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_cgst'))]"/>
                                        <field name="subformula">-sum</field>
                                </record>
                                <record id="account_report_gstr3b_3_1_a_tax_sgst" model="account.report.expression">
                                    <field name="label">tax_sgst</field>
                                    <field name="engine">domain</field>
                                    <field name="formula" eval="[('move_id.l10n_in_gst_treatment', '!=', 'overseas'), ('move_id.move_type', '=', 'out_invoice'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_sgst'))]"/>
                                    <field name="subformula">-sum</field>
                                </record>
                                <record id="account_report_gstr3b_3_1_a_tax_igst" model="account.report.expression">
                                    <field name="label">tax_igst</field>
                                    <field name="engine">domain</field>
                                    <field name="formula" eval="[('move_id.l10n_in_gst_treatment', '!=', 'overseas'), ('move_id.move_type', '=', 'out_invoice'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                    <field name="subformula">-sum</field>
                                </record>
                                <record id="account_report_gstr3b_3_1_a_tax_cess" model="account.report.expression">
                                    <field name="label">tax_cess</field>
                                    <field name="engine">domain</field>
                                    <field name="formula" eval="[('move_id.l10n_in_gst_treatment', '!=', 'overseas'), ('move_id.move_type', '=', 'out_invoice'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_cess'))]"/>
                                    <field name="subformula">-sum</field>
                                </record>
                            </field>
                    </record>
                    <record id="account_report_gstr3b_3_1_b" model="account.report.line">
                        <field name="name">(b) Outward Taxable supplies (zero rated)</field>
                        <field name="code">3_1_B</field>
                        <field name="expression_ids">
                            <record id="account_report_gstr3b_3_1_b_tax_base" model="account.report.expression">
                                <field name="label">tax_base</field>
                                <field name="engine">domain</field>
                                <field name="formula" eval="[('move_id.l10n_in_gst_treatment', '=', 'overseas'), ('move_id.move_type', '=', 'out_invoice'), ('tax_tag_ids','in',[ref('l10n_in.tax_tag_base_igst'), ref('l10n_in.tax_tag_base_cess')])]"/>
                                <field name="subformula">-sum</field>
                            </record>
                            <record id="account_report_gstr3b_3_1_b_tax_igst" model="account.report.expression">
                                <field name="label">tax_igst</field>
                                <field name="engine">domain</field>
                                <field name="formula" eval="[('move_id.l10n_in_gst_treatment', '=', 'overseas'), ('move_id.move_type', '=', 'out_invoice'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                <field name="subformula">-sum</field>
                            </record>
                            <record id="account_report_gstr3b_3_1_b_tax_cess" model="account.report.expression">
                                <field name="label">tax_cess</field>
                                <field name="engine">domain</field>
                                <field name="formula" eval="[('move_id.l10n_in_gst_treatment', '=', 'overseas'), ('move_id.move_type', '=', 'out_invoice'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_cess'))]"/>
                                <field name="subformula">-sum</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_report_gstr3b_3_1_c" model="account.report.line">
                        <field name="name">(c) Other Outward Taxable supplies (Nil rated, exempted)</field>
                        <field name="code">3_1_C</field>
                        <field name="expression_ids">
                            <record id="account_report_gstr3b_3_1_c_tax_base" model="account.report.expression">
                                <field name="label">tax_base</field>
                                <field name="engine">domain</field>
                                <field name="formula" eval="[('move_id.move_type', '=', 'out_invoice'), ('tax_tag_ids','in',[ref('l10n_in.tax_tag_exempt'), ref('l10n_in.tax_tag_nil_rated')])]"/>
                                <field name="subformula">-sum</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_report_gstr3b_3_1_d" model="account.report.line">
                        <field name="name">(d) Inward supplies (liable to reverse charge)</field>
                        <field name="code">3_1_D</field>
                        <field name="expression_ids">
                            <record id="account_report_gstr3b_3_1_d_tax_base" model="account.report.expression">
                                <field name="label">tax_base</field>
                                <field name="engine">domain</field>
                                <field name="formula" eval="[('move_id.move_type','in',('entry', 'in_invoice')), ('tax_tag_ids','in',[ref('l10n_in.tax_tag_base_sgst_rc'), ref('l10n_in.tax_tag_base_cgst_rc'), ref('l10n_in.tax_tag_base_igst_rc'), ref('l10n_in.tax_tag_base_cess_rc')])]"/>
                                <field name="subformula">sum</field>
                            </record>
                             <record id="account_report_gstr3b_3_1_d_tax_cgst" model="account.report.expression">
                                <field name="label">tax_cgst</field>
                                <field name="engine">domain</field>
                                <field name="formula" eval="[('move_id.move_type','in',('entry', 'in_invoice')), ('tax_tag_ids','=', ref('l10n_in.tax_tag_cgst_rc'))]"/>
                                <field name="subformula">-sum</field>
                            </record>
                            <record id="account_report_gstr3b_3_1_d_tax_sgst" model="account.report.expression">
                                <field name="label">tax_sgst</field>
                                <field name="engine">domain</field>
                                <field name="formula" eval="[('move_id.move_type','in',('entry', 'in_invoice')), ('tax_tag_ids','=', ref('l10n_in.tax_tag_sgst_rc'))]"/>
                                <field name="subformula">-sum</field>
                            </record>
                            <record id="account_report_gstr3b_3_1_d_tax_igst" model="account.report.expression">
                                <field name="label">tax_igst</field>
                                <field name="engine">domain</field>
                                <field name="formula" eval="[('move_id.move_type','in',('entry', 'in_invoice')), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst_rc'))]"/>
                                <field name="subformula">-sum</field>
                            </record>
                            <record id="account_report_gstr3b_3_1_d_tax_cess" model="account.report.expression">
                                <field name="label">tax_cess</field>
                                <field name="engine">domain</field>
                                <field name="formula" eval="[('move_id.move_type','in',('entry', 'in_invoice')), ('tax_tag_ids','=', ref('l10n_in.tax_tag_cess_rc'))]"/>
                                <field name="subformula">-sum</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_report_gstr3b_3_1_e" model="account.report.line">
                        <field name="name">(e) Non-GST Outward supplies</field>
                        <field name="code">3_1_E</field>
                        <field name="expression_ids">
                            <record id="account_report_gstr3b_3_1_e_tax_base" model="account.report.expression">
                                <field name="label">tax_base</field>
                                <field name="engine">domain</field>
                                <field name="formula" eval="[('move_id.move_type', '=', 'out_invoice'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_non_gst_supplies'))]"/>
                                <field name="subformula">-sum</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>

            <!-- 4 Eligible ITC -->
            <record id="account_report_gstr3b_4" model="account.report.line">
                <field name="name">4. Eligible ITC</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="account_report_gstr3b_4_a" model="account.report.line">
                        <field name="name">(A) ITC Available (Whether in full or part)</field>
                        <field name="children_ids">
                            <record id="account_report_gstr3b_4_a_1" model="account.report.line">
                                <field name="name">(1) Import of goods</field>
                                    <field name="expression_ids">
                                        <record id="account_report_gstr3b_4_a_1_tax_igst" model="account.report.expression">
                                            <field name="label">tax_igst</field>
                                            <field name="engine">domain</field>
                                            <field name="formula" eval="[('move_id.move_type','in',('entry', 'in_invoice')), ('move_id.l10n_in_gst_treatment','=','overseas'), ('tax_line_id.tax_scope', '!=', 'service'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                            <field name="subformula">sum</field>
                                        </record>
                                        <record id="account_report_gstr3b_4_a_1_tax_cess" model="account.report.expression">
                                            <field name="label">tax_cess</field>
                                            <field name="engine">domain</field>
                                            <field name="formula" eval="[('move_id.move_type','in',('entry', 'in_invoice')), ('move_id.l10n_in_gst_treatment','=','overseas'), ('tax_line_id.tax_scope', '!=', 'service'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_cess'))]"/>
                                            <field name="subformula">sum</field>
                                        </record>
                                    </field>
                            </record>
                            <record id="account_report_gstr3b_4_a_2" model="account.report.line">
                                <field name="name">(2) Import of services</field>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_4_a_2_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'in_invoice')), ('move_id.l10n_in_gst_treatment','=','overseas'), ('tax_line_id.tax_scope', '=', 'service'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_4_a_2_tax_cess" model="account.report.expression">
                                        <field name="label">tax_cess</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'in_invoice')), ('move_id.l10n_in_gst_treatment','=','overseas'), ('tax_line_id.tax_scope', '=', 'service'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_cess'))]"/>
                                        <field name="subformula">sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_4_a_3" model="account.report.line">
                                <field name="name">(3) Inward supplies liable to reverse charge (other than 1 &amp; 2 above)</field>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_4_a_3_tax_cgst" model="account.report.expression">
                                        <field name="label">tax_cgst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'in_invoice')), ('tax_tag_ids','=', ref('l10n_in.tax_tag_cgst_rc'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_4_a_3_tax_sgst" model="account.report.expression">
                                        <field name="label">tax_sgst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'in_invoice')), ('tax_tag_ids','=', ref('l10n_in.tax_tag_sgst_rc'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_4_a_3_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'in_invoice')), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst_rc'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_4_a_3_tax_cess" model="account.report.expression">
                                        <field name="label">tax_cess</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'in_invoice')), ('tax_tag_ids','=', ref('l10n_in.tax_tag_cess_rc'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_4_a_4" model="account.report.line">
                                <field name="name">(4) All other ITC</field>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_4_a_4_tax_cgst" model="account.report.expression">
                                        <field name="label">tax_cgst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'in_invoice')), ('move_id.l10n_in_gst_treatment','!=', 'overseas'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_cgst'))]"/>
                                        <field name="subformula">sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_4_a_4_tax_sgst" model="account.report.expression">
                                        <field name="label">tax_sgst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'in_invoice')), ('move_id.l10n_in_gst_treatment','!=', 'overseas'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_sgst'))]"/>
                                        <field name="subformula">sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_4_a_4_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'in_invoice')), ('move_id.l10n_in_gst_treatment','!=', 'overseas'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_4_a_4_tax_cess" model="account.report.expression">
                                        <field name="label">tax_cess</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'in_invoice')), ('move_id.l10n_in_gst_treatment','!=', 'overseas'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_cess'))]"/>
                                        <field name="subformula">sum</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="account_report_gstr3b_4_b" model="account.report.line">
                        <field name="name">(B) Ineligible ITC</field>
                        <field name="children_ids">
                            <record id="account_report_gstr3b_4_b_1" model="account.report.line">
                                <field name="name">(1) As per section 17(5) of CGST/SGST Act</field>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_4_b_1_tax_cgst" model="account.report.expression">
                                        <field name="label">tax_cgst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'in_invoice')), ('tax_tag_ids','=', ref('l10n_in.tax_tag_non_itc_cgst'))]"/>
                                        <field name="subformula">sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_4_b_1_tax_sgst" model="account.report.expression">
                                        <field name="label">tax_sgst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'in_invoice')), ('tax_tag_ids','=', ref('l10n_in.tax_tag_non_itc_sgst'))]"/>
                                        <field name="subformula">sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_4_b_1_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'in_invoice')), ('tax_tag_ids','=', ref('l10n_in.tax_tag_non_itc_igst'))]"/>
                                        <field name="subformula">sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_4_b_1_tax_cess" model="account.report.expression">
                                        <field name="label">tax_cess</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'in_invoice')), ('tax_tag_ids','=', ref('l10n_in.tax_tag_non_itc_cess'))]"/>
                                        <field name="subformula">sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_4_b_2" model="account.report.line">
                                <field name="name">(2) Others</field>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_4_b_2_tax_cgst" model="account.report.expression">
                                        <field name="label">tax_cgst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'in_invoice')), ('tax_tag_ids','=', ref('l10n_in.tax_tag_other_non_itc_cgst'))]"/>
                                        <field name="subformula">sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_4_b_2_tax_sgst" model="account.report.expression">
                                        <field name="label">tax_sgst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'in_invoice')), ('tax_tag_ids','=', ref('l10n_in.tax_tag_other_non_itc_sgst'))]"/>
                                        <field name="subformula">sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_4_b_2_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'in_invoice')), ('tax_tag_ids','=', ref('l10n_in.tax_tag_other_non_itc_igst'))]"/>
                                        <field name="subformula">sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_4_b_2_tax_cess" model="account.report.expression">
                                        <field name="label">tax_cess</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'in_invoice')), ('tax_tag_ids','=', ref('l10n_in.tax_tag_other_non_itc_cess'))]"/>
                                        <field name="subformula">sum</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>

            <!-- 5. Values of exempt, Nil-rated and non-GST inward supplies -->
            <record id="account_report_gstr3b_5" model="account.report.line">
                <field name="name">5. Values of exempt, Nil-rated and non-GST inward supplies</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="account_report_gstr3b_5_a" model="account.report.line">
                        <field name="name">From a supplier under composition scheme, Exempt and Nil rated supply</field>
                        <field name="children_ids">
                            <record id="account_report_gstr3b_5_a_1" model="account.report.line">
                                <field name="name">Inter-State supplies</field>
                                <field name="domain_formula">sum([('move_id.move_type','in',('entry', 'in_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('tax_tag_ids','in',[ref('l10n_in.tax_tag_exempt'), ref('l10n_in.tax_tag_nil_rated')])])</field>
                            </record>
                            <record id="account_report_gstr3b_5_a_2" model="account.report.line">
                                <field name="name">Intra-State Supplies</field>
                                <field name="domain_formula">sum([('move_id.move_type','in',('entry', 'in_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'intra_state'), ('tax_tag_ids','in',[ref('l10n_in.tax_tag_exempt'), ref('l10n_in.tax_tag_nil_rated')])])</field>
                            </record>
                        </field>
                    </record>

                    <record id="account_report_gstr3b_5_b" model="account.report.line">
                        <field name="name">Non GST supply</field>
                        <field name="children_ids">
                            <record id="account_report_gstr3b_5_b_1" model="account.report.line">
                                <field name="name">Inter-State Supplies </field>
                                <field name="code">INTER_NONGST</field>
                                <field name="domain_formula">sum([('move_id.move_type','in',('entry', 'in_invoice')), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_non_gst_supplies'))])</field>
                            </record>
                            <record id="account_report_gstr3b_5_b_2" model="account.report.line">
                                <field name="name">Intra-State Supplies</field>
                                <field name="code">INTRA_NONGST</field>
                                <field name="domain_formula">sum([('move_id.move_type','in',('entry', 'in_invoice')), ('move_id.l10n_in_transaction_type', '=', 'intra_state'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_non_gst_supplies'))])</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>

            <!-- 3.2  Of the supplies shown in 3.1 (a), details of inter-state supplies made to unregistered persons, composition taxable person and UIN holders -->
            <record id="account_report_gstr3b_3_2" model="account.report.line">
                <field name="name">3.2  Of the supplies shown in 3.1 (a), details of inter-state supplies made to unregistered persons, composition taxable person and UIN holders</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="account_report_gstr3b_3_2_ur" model="account.report.line">
                        <field name="name">Supplies made to Unregistered Persons</field>
                        <field name="code">3_2_UR</field>
                        <field name="children_ids">
                            <record id="account_report_gstr3b_3_2_ur_01" model="account.report.line">
                                <field name="name">Jammu and Kashmir</field>
                                <field name="code">3_2_UR_01</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_01_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '01'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_01_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '01'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_02" model="account.report.line">
                                <field name="name">Himachal Pradesh</field>
                                <field name="code">3_2_UR_02</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_02_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '02'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_02_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '02'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_03" model="account.report.line">
                                <field name="name">Punjab</field>
                                <field name="code">3_2_UR_03</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_03_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '03'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_03_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '03'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_04" model="account.report.line">
                                <field name="name">Chandigarh</field>
                                <field name="code">3_2_UR_04</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_04_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '04'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_04_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '04'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_05" model="account.report.line">
                                <field name="name">Uttarakhand</field>
                                <field name="code">3_2_UR_05</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_05_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '05'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_05_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '05'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_06" model="account.report.line">
                                <field name="name">Haryana</field>
                                <field name="code">3_2_UR_06</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_06_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '06'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_06_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '06'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_07" model="account.report.line">
                                <field name="name">Delhi</field>
                                <field name="code">3_2_UR_07</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_07_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '07'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_07_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '07'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_08" model="account.report.line">
                                <field name="name">Rajasthan</field>
                                <field name="code">3_2_UR_08</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_08_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in', ['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '08'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_08_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '08'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_09" model="account.report.line">
                                <field name="name">Uttar Pradesh</field>
                                <field name="code">3_2_UR_09</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_09_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in', ['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '09'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_09_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '09'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_10" model="account.report.line">
                                <field name="name">Bihar</field>
                                <field name="code">3_2_UR_10</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_10_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in', ['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '10'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_10_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '10'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_11" model="account.report.line">
                                <field name="name">Sikkim</field>
                                <field name="code">3_2_UR_11</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_11_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in', ['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '11'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_11_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '11'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_12" model="account.report.line">
                                <field name="name">Arunachal Pradesh</field>
                                <field name="code">3_2_UR_12</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_12_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in', ['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '12'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_12_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '12'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_13" model="account.report.line">
                                <field name="name">Nagaland</field>
                                <field name="code">3_2_UR_13</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_13_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '13'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_13_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '13'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_14" model="account.report.line">
                                <field name="name">Manipur</field>
                                <field name="code">3_2_UR_14</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_14_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '14'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_14_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '14'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_15" model="account.report.line">
                                <field name="name">Mizoram</field>
                                <field name="code">3_2_UR_15</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_15_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '15'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_15_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '15'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_16" model="account.report.line">
                                <field name="name">Tripura</field>
                                <field name="code">3_2_UR_16</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_16_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '16'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_16_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '16'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_17" model="account.report.line">
                                <field name="name">Meghalaya</field>
                                <field name="code">3_2_UR_17</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_17_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '17'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_17_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '17'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_18" model="account.report.line">
                                <field name="name">Assam</field>
                                <field name="code">3_2_UR_18</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_18_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '18'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_18_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '18'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_19" model="account.report.line">
                                <field name="name">West Bengal</field>
                                <field name="code">3_2_UR_19</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_19_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '19'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_19_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '19'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_20" model="account.report.line">
                                <field name="name">Jharkhand</field>
                                <field name="code">3_2_UR_20</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_20_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '20'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_20_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '20'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_21" model="account.report.line">
                                <field name="name">Orissa</field>
                                <field name="code">3_2_UR_21</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_21_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '21'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_21_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '21'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_22" model="account.report.line">
                                <field name="name">Chattisgarh</field>
                                <field name="code">3_2_UR_22</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_22_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '22'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_22_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '22'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_23" model="account.report.line">
                                <field name="name">Madhya Pradesh</field>
                                <field name="code">3_2_UR_23</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_23_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '23'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_23_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '23'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_24" model="account.report.line">
                                <field name="name">Gujarat</field>
                                <field name="code">3_2_UR_24</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_24_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '24'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_24_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '24'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_25" model="account.report.line">
                                <field name="name">Daman and Diu</field>
                                <field name="code">3_2_UR_25</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_25_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '25'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_25_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '25'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_26" model="account.report.line">
                                <field name="name">Dadra and Nagar Haveli</field>
                                <field name="code">3_2_UR_26</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_26_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '26'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_26_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '26'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_27" model="account.report.line">
                                <field name="name">Maharashtra</field>
                                <field name="code">3_2_UR_27</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_27_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '27'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_27_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '27'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_29" model="account.report.line">
                                <field name="name">Karnataka</field>
                                <field name="code">3_2_UR_29</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_29_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '29'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_29_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '29'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_30" model="account.report.line">
                                <field name="name">Goa</field>
                                <field name="code">3_2_UR_30</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_30_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '30'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_30_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '30'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_31" model="account.report.line">
                                <field name="name">Lakshadweep</field>
                                <field name="code">3_2_UR_31</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_31_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '31'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_31_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '31'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_32" model="account.report.line">
                                <field name="name">Kerala</field>
                                <field name="code">3_2_UR_32</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_32_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '32'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_32_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '32'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_33" model="account.report.line">
                                <field name="name">Tamil Nadu</field>
                                <field name="code">3_2_UR_33</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_33_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '33'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_33_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '33'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_34" model="account.report.line">
                                <field name="name">Puducherry</field>
                                <field name="code">3_2_UR_34</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_34_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '34'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_34_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '34'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_35" model="account.report.line">
                                <field name="name">Andaman and Nicobar</field>
                                <field name="code">3_2_UR_35</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_35_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '35'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_35_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '35'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_36" model="account.report.line">
                                <field name="name">Telangana</field>
                                <field name="code">3_2_UR_36</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_36_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '36'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_36_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '36'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_37" model="account.report.line">
                                <field name="name">Andhra Pradesh</field>
                                <field name="code">3_2_UR_37</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_37_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '37'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_37_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '37'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_ur_97" model="account.report.line">
                                <field name="name">Other Territory</field>
                                <field name="code">3_2_UR_97</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_ur_97_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '97'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_ur_97_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','in',['unregistered', 'consumer']), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '97'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="account_report_gstr3b_3_2_cp" model="account.report.line">
                        <field name="name">Supplies made to Composition Taxable Persons</field>
                        <field name="code">3_2_CP</field>
                        <field name="children_ids">
                            <record id="account_report_gstr3b_3_2_cp_01" model="account.report.line">
                                <field name="name">Jammu and Kashmir</field>
                                <field name="code">3_2_CP_01</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_01_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '01'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_01_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '01'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_02" model="account.report.line">
                                <field name="name">Himachal Pradesh</field>
                                <field name="code">3_2_CP_02</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_02_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '02'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_02_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '02'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_03" model="account.report.line">
                                <field name="name">Punjab</field>
                                <field name="code">3_2_CP_03</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_03_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '03'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_03_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '03'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_04" model="account.report.line">
                                <field name="name">Chandigarh</field>
                                <field name="code">3_2_CP_04</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_04_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '04'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_04_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '04'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_05" model="account.report.line">
                                <field name="name">Uttarakhand</field>
                                <field name="code">3_2_CP_05</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_05_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '05'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_05_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '05'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_06" model="account.report.line">
                                <field name="name">Haryana</field>
                                <field name="code">3_2_CP_06</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_06_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '06'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_06_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '06'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_07" model="account.report.line">
                                <field name="name">Delhi</field>
                                <field name="code">3_2_CP_07</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_07_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '07'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_07_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '07'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_08" model="account.report.line">
                                <field name="name">Rajasthan</field>
                                <field name="code">3_2_CP_08</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_08_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '08'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_08_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '08'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_09" model="account.report.line">
                                <field name="name">Uttar Pradesh</field>
                                <field name="code">3_2_CP_09</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_09_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '09'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_09_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '09'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_10" model="account.report.line">
                                <field name="name">Bihar</field>
                                <field name="code">3_2_CP_10</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_10_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '10'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_10_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '10'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_11" model="account.report.line">
                                <field name="name">Sikkim</field>
                                <field name="code">3_2_CP_11</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_11_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '11'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_11_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '11'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_12" model="account.report.line">
                                <field name="name">Arunachal Pradesh</field>
                                <field name="code">3_2_CP_12</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_12_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '12'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_12_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '12'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_13" model="account.report.line">
                                <field name="name">Nagaland</field>
                                <field name="code">3_2_CP_13</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_13_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '13'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_13_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '13'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_14" model="account.report.line">
                                <field name="name">Manipur</field>
                                <field name="code">3_2_CP_14</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_14_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '14'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_14_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '14'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_15" model="account.report.line">
                                <field name="name">Mizoram</field>
                                <field name="code">3_2_CP_15</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_15_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '15'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_15_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '15'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_16" model="account.report.line">
                                <field name="name">Tripura</field>
                                <field name="code">3_2_CP_16</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_16_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '16'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_16_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '16'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_17" model="account.report.line">
                                <field name="name">Meghalaya</field>
                                <field name="code">3_2_CP_17</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_17_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '17'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_17_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '17'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_18" model="account.report.line">
                                <field name="name">Assam</field>
                                <field name="code">3_2_CP_18</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_18_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '18'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_18_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '18'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_19" model="account.report.line">
                                <field name="name">West Bengal</field>
                                <field name="code">3_2_CP_19</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_19_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '19'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_19_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '19'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_20" model="account.report.line">
                                <field name="name">Jharkhand</field>
                                <field name="code">3_2_CP_20</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_20_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '20'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_20_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '20'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_21" model="account.report.line">
                                <field name="name">Orissa</field>
                                <field name="code">3_2_CP_21</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_21_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '21'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_21_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '21'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_22" model="account.report.line">
                                <field name="name">Chattisgarh</field>
                                <field name="code">3_2_CP_22</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_22_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '22'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_22_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '22'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_23" model="account.report.line">
                                <field name="name">Madhya Pradesh</field>
                                <field name="code">3_2_CP_23</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_23_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '23'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_23_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '23'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_24" model="account.report.line">
                                <field name="name">Gujarat</field>
                                <field name="code">3_2_CP_24</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_24_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '24'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_24_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '24'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_25" model="account.report.line">
                                <field name="name">Daman and Diu</field>
                                <field name="code">3_2_CP_25</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_25_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '25'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_25_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '25'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_26" model="account.report.line">
                                <field name="name">Dadra and Nagar Haveli</field>
                                <field name="code">3_2_CP_26</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_26_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '26'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_26_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '26'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_27" model="account.report.line">
                                <field name="name">Maharashtra</field>
                                <field name="code">3_2_CP_27</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_27_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '27'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_27_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '27'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_29" model="account.report.line">
                                <field name="name">Karnataka</field>
                                <field name="code">3_2_CP_29</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_29_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '29'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_29_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '29'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_30" model="account.report.line">
                                <field name="name">Goa</field>
                                <field name="code">3_2_CP_30</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_30_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '30'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_30_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '30'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_31" model="account.report.line">
                                <field name="name">Lakshadweep</field>
                                <field name="code">3_2_CP_31</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_31_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '31'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_31_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '31'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_32" model="account.report.line">
                                <field name="name">Kerala</field>
                                <field name="code">3_2_CP_32</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_32_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '32'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_32_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '32'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_33" model="account.report.line">
                                <field name="name">Tamil Nadu</field>
                                <field name="code">3_2_CP_33</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_33_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '33'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_33_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '33'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_34" model="account.report.line">
                                <field name="name">Puducherry</field>
                                <field name="code">3_2_CP_34</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_34_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '34'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_34_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '34'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_35" model="account.report.line">
                                <field name="name">Andaman and Nicobar</field>
                                <field name="code">3_2_CP_35</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_35_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '35'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_35_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '35'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_36" model="account.report.line">
                                <field name="name">Telangana</field>
                                <field name="code">3_2_CP_36</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_36_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '36'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_36_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '36'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_37" model="account.report.line">
                                <field name="name">Andhra Pradesh</field>
                                <field name="code">3_2_CP_37</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_37_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '37'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_37_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '37'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_cp_97" model="account.report.line">
                                <field name="name">Other Territory</field>
                                <field name="code">3_2_CP_97</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_cp_97_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '97'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_cp_97_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','composition'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '97'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="account_report_gstr3b_3_2_uin" model="account.report.line">
                        <field name="name">Supplies made to UIN holders</field>
                        <field name="code">3_2_UIN</field>
                        <field name="children_ids">
                            <record id="account_report_gstr3b_3_2_uin_01" model="account.report.line">
                                <field name="name">Jammu and Kashmir</field>
                                <field name="code">3_2_UIN_01</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_01_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '01'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_01_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '01'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_02" model="account.report.line">
                                <field name="name">Himachal Pradesh</field>
                                <field name="code">3_2_UIN_02</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_02_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '02'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_02_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '02'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_03" model="account.report.line">
                                <field name="name">Punjab</field>
                                <field name="code">3_2_UIN_03</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_03_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '03'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_03_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '03'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_04" model="account.report.line">
                                <field name="name">Chandigarh</field>
                                <field name="code">3_2_UIN_04</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_04_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '04'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_04_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '04'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_05" model="account.report.line">
                                <field name="name">Uttarakhand</field>
                                <field name="code">3_2_UIN_05</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_05_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '05'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_05_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '05'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_06" model="account.report.line">
                                <field name="name">Haryana</field>
                                <field name="code">3_2_UIN_06</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_06_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '06'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_06_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '06'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_07" model="account.report.line">
                                <field name="name">Delhi</field>
                                <field name="code">3_2_UIN_07</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_07_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '07'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_07_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '07'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_08" model="account.report.line">
                                <field name="name">Rajasthan</field>
                                <field name="code">3_2_UIN_08</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_08_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '08'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_08_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '08'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_09" model="account.report.line">
                                <field name="name">Uttar Pradesh</field>
                                <field name="code">3_2_UIN_09</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_09_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '09'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_09_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '09'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_10" model="account.report.line">
                                <field name="name">Bihar</field>
                                <field name="code">3_2_UIN_10</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_10_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '10'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_10_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '10'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_11" model="account.report.line">
                                <field name="name">Sikkim</field>
                                <field name="code">3_2_UIN_11</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_11_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '11'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_11_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '11'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_12" model="account.report.line">
                                <field name="name">Arunachal Pradesh</field>
                                <field name="code">3_2_UIN_12</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_12_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '12'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_12_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '12'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_13" model="account.report.line">
                                <field name="name">Nagaland</field>
                                <field name="code">3_2_UIN_13</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_13_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '13'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_13_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '13'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_14" model="account.report.line">
                                <field name="name">Manipur</field>
                                <field name="code">3_2_UIN_14</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_14_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '14'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_14_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '14'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_15" model="account.report.line">
                                <field name="name">Mizoram</field>
                                <field name="code">3_2_UIN_15</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_15_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '15'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_15_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '15'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_16" model="account.report.line">
                                <field name="name">Tripura</field>
                                <field name="code">3_2_UIN_16</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_16_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '16'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_16_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '16'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_17" model="account.report.line">
                                <field name="name">Meghalaya</field>
                                <field name="code">3_2_UIN_17</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_17_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '17'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_17_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '17'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_18" model="account.report.line">
                                <field name="name">Assam</field>
                                <field name="code">3_2_UIN_18</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_18_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '18'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_18_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '18'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_19" model="account.report.line">
                                <field name="name">West Bengal</field>
                                <field name="code">3_2_UIN_19</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_19_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '19'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_19_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '19'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_20" model="account.report.line">
                                <field name="name">Jharkhand</field>
                                <field name="code">3_2_UIN_20</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_20_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '20'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_20_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '20'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_21" model="account.report.line">
                                <field name="name">Orissa</field>
                                <field name="code">3_2_UIN_21</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_21_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '21'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_21_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '21'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_22" model="account.report.line">
                                <field name="name">Chattisgarh</field>
                                <field name="code">3_2_UIN_22</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_22_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '22'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_22_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '22'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_23" model="account.report.line">
                                <field name="name">Madhya Pradesh</field>
                                <field name="code">3_2_UIN_23</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_23_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '23'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_23_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '23'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_24" model="account.report.line">
                                <field name="name">Gujarat</field>
                                <field name="code">3_2_UIN_24</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_24_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '24'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_24_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '24'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_25" model="account.report.line">
                                <field name="name">Daman and Diu</field>
                                <field name="code">3_2_UIN_25</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_25_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '25'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_25_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '25'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_26" model="account.report.line">
                                <field name="name">Dadra and Nagar Haveli</field>
                                <field name="code">3_2_UIN_26</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_26_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '26'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_26_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '26'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_27" model="account.report.line">
                                <field name="name">Maharashtra</field>
                                <field name="code">3_2_UIN_27</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_27_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '27'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_27_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '27'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_29" model="account.report.line">
                                <field name="name">Karnataka</field>
                                <field name="code">3_2_UIN_29</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_29_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '29'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_29_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '29'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_30" model="account.report.line">
                                <field name="name">Goa</field>
                                <field name="code">3_2_UIN_30</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_30_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '30'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_30_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '30'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_31" model="account.report.line">
                                <field name="name">Lakshadweep</field>
                                <field name="code">3_2_UIN_31</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_31_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '31'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_31_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '31'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_32" model="account.report.line">
                                <field name="name">Kerala</field>
                                <field name="code">3_2_UIN_32</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_32_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '32'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_32_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '32'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_33" model="account.report.line">
                                <field name="name">Tamil Nadu</field>
                                <field name="code">3_2_UIN_33</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_33_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '33'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_33_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '33'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_34" model="account.report.line">
                                <field name="name">Puducherry</field>
                                <field name="code">3_2_UIN_34</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_34_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '34'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_34_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '34'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_35" model="account.report.line">
                                <field name="name">Andaman and Nicobar</field>
                                <field name="code">3_2_UIN_35</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_35_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '35'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_35_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '35'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_36" model="account.report.line">
                                <field name="name">Telangana</field>
                                <field name="code">3_2_UIN_36</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_36_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '36'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_36_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '36'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_37" model="account.report.line">
                                <field name="name">Andhra Pradesh</field>
                                <field name="code">3_2_UIN_37</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_37_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '37'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_37_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '37'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_report_gstr3b_3_2_uin_97" model="account.report.line">
                                <field name="name">Other Territory</field>
                                <field name="code">3_2_UIN_97</field>
                                <field name="hide_if_zero" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_report_gstr3b_3_2_uin_97_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '97'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_base_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                    <record id="account_report_gstr3b_3_2_uin_97_tax_igst" model="account.report.expression">
                                        <field name="label">tax_igst</field>
                                        <field name="engine">domain</field>
                                        <field name="formula" eval="[('move_id.move_type','in',('entry', 'out_invoice')), ('move_id.l10n_in_gst_treatment','=','uin_holders'), ('move_id.l10n_in_transaction_type', '=', 'inter_state'), ('move_id.l10n_in_state_id.l10n_in_tin', '=', '97'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
                                        <field name="subformula">-sum</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
        </field>
    </record>

    <record id="action_l10n_in_gstr3b" model="ir.actions.client">
        <field name="name">GSTR-3B Report</field>
        <field name="tag">account_report</field>
        <field name="context" eval="{'report_id': ref('l10n_in_reports.account_report_gstr3b')}"/>
    </record>

    <menuitem id="menu_account_report_gstr3b" name="GSTR-3B" sequence="6" parent="l10n_in.account_reports_in_statements_menu" action="action_l10n_in_gstr3b" groups="base.group_no_one"/>

</odoo>
