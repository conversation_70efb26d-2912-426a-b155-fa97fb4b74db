# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_delivery
# 
# Translators:
# Wil <PERSON>, 2025
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/sale_order.py:0
msgid "%(name)s (Estimated Cost: %(cost)s)"
msgstr "%(name)s (Coût estimé : %(cost)s)"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.view_quant_package_weight_form
msgid "(computed:"
msgstr "(Calculé :"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.stock_report_delivery_package_section_line_inherit_delivery
msgid "<span> - Weight (estimated): </span>"
msgstr "<span> - Poids (estimé) : </span>"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.delivery_stock_report_delivery_no_package_section_line
#: model_terms:ir.ui.view,arch_db:stock_delivery.stock_report_delivery_package_section_line_inherit_delivery
msgid "<span> - Weight: </span>"
msgstr "<span> - Poids : </span>"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_package_barcode_small_delivery
msgid "<span>Shipping Weight: </span>"
msgstr "<span>Poids d'expédition : </span>"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_delivery_document2
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_shipping2
msgid "<strong>Carrier</strong>"
msgstr "<strong>Transporteur</strong>"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_shipping2
msgid "<strong>Shipping Method:</strong>"
msgstr "<strong>Mode d'expédition :</strong>"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_package_barcode_delivery
msgid "<strong>Shipping Weight: </strong>"
msgstr "<strong>Poids d'expédition :</strong>"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_package_barcode_delivery
msgid ""
"<strong>Shipping Weight:</strong>\n"
"                    <br/>"
msgstr ""
"<strong>Poids d'expédition :</strong>\n"
"<br/>"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_delivery_document2
msgid "<strong>Total Weight</strong>"
msgstr "<strong>Poids total</strong>"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_delivery_document2
msgid "<strong>Tracking Number</strong>"
msgstr "<strong>Numéro de suivi</strong>"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_package_barcode_delivery
msgid "<strong>Weight: </strong>"
msgstr "<strong>Poids :</strong>"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_shipping2
msgid "<strong>Weight</strong>"
msgstr "<strong>Poids</strong>"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_route__shipping_selectable
msgid "Applicable on Shipping Methods"
msgstr "Applicable sur les modes d'expédition"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.view_picking_withcarrier_out_form
msgid "Cancel"
msgstr "Annuler"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.view_picking_withcarrier_out_form
msgid ""
"Cancelling a delivery may not be undoable. Are you sure you want to "
"continue?"
msgstr ""
"L'annulation d'une livraison peut être irréversible. Êtes-vous sûr de "
"vouloir continuer ?"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_move_line__carrier_id
#: model:ir.model.fields,field_description:stock_delivery.field_stock_package_type__package_carrier_type
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__carrier_id
#: model_terms:ir.ui.view,arch_db:stock_delivery.stock_move_line_view_search_delivery
msgid "Carrier"
msgstr "Transporteur"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_package_type__shipper_package_code
msgid "Carrier Code"
msgstr "Code transporteur"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.stock_move_line_view_search_delivery
msgid "Carrier name"
msgstr "Nom du transporteur"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_choose_delivery_package__company_id
msgid "Company"
msgstr "Société"

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/stock_picking.py:0
msgid "Cost: %(price).2f %(currency)s"
msgstr "Coût : %(price).2f %(currency)s"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_choose_delivery_package__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_choose_delivery_package__create_date
msgid "Created on"
msgstr "Créé le"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_choose_delivery_carrier
msgid "Delivery Carrier Selection Wizard"
msgstr "Assistant de sélection du transporteur"

#. module: stock_delivery
#: model:ir.ui.menu,name:stock_delivery.menu_action_delivery_carrier_form
msgid "Delivery Methods"
msgstr "Modes de livraison"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_choose_delivery_package
msgid "Delivery Package Selection Wizard"
msgstr "Assistant de sélection de colis"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_choose_delivery_package__delivery_package_type_id
msgid "Delivery Package Type"
msgstr "Type de colis de livraison"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.vpicktree_view_tree
msgid "Destination"
msgstr "Destination"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_move_line__destination_country_code
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__destination_country_code
msgid "Destination Country"
msgstr "Pays de destination"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.choose_delivery_package_view_form
msgid "Discard"
msgstr "Ignorer"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_choose_delivery_package__display_name
msgid "Display Name"
msgstr "Nom d'affichage"

#. module: stock_delivery
#: model:ir.actions.act_window,name:stock_delivery.act_delivery_trackers_url
msgid "Display tracking links"
msgstr "Afficher les liens de suivi"

#. module: stock_delivery
#: model:ir.model.fields,help:stock_delivery.field_delivery_carrier__invoice_policy
msgid ""
"Estimated Cost: the customer will be invoiced the estimated cost of the shipping.\n"
"Real Cost: the customer will be invoiced the real cost of the shipping, the cost of theshipping will be updated on the SO after the delivery."
msgstr ""
"Coût estimé : le client se verra facturer le coût estimé de l'expédition. \n"
"Coût réel : le client se verra facturer le coût réel de l'expédition, le coût de l'expédition sera mis à jour sur le bon de commande après la livraison."

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/stock_picking.py:0
msgid "Exception occurred with respect to carrier on the transfer"
msgstr "Une exception est survenue concernant le transporteur du transfert."

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/stock_picking.py:0
msgid "Exception:"
msgstr "Exception :"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_shipping2
msgid "FedEx"
msgstr "FedEx"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_product_product__hs_code
#: model:ir.model.fields,field_description:stock_delivery.field_product_template__hs_code
#: model_terms:ir.ui.view,arch_db:stock_delivery.product_template_hs_code
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_delivery_document2
msgid "HS Code"
msgstr "Code SH"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_choose_delivery_package__id
msgid "ID"
msgstr "ID"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_stock_route
msgid "Inventory Routes"
msgstr "Routes d'inventaire"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_delivery_carrier__invoice_policy
msgid "Invoicing Policy"
msgstr "Politique de facturation"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__is_return_picking
msgid "Is Return Picking"
msgstr "Est un transfert de retour"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_choose_delivery_package__write_uid
msgid "Last Updated by"
msgstr "Mis à jour par"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_choose_delivery_package__write_date
msgid "Last Updated on"
msgstr "Mis à jour le"

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/stock_picking.py:0
msgid "Manual actions might be needed."
msgstr "Des actions manuelles peuvent être nécessaires."

#. module: stock_delivery
#: model:ir.model.fields.selection,name:stock_delivery.selection__stock_package_type__package_carrier_type__none
msgid "No carrier integration"
msgstr "Aucune intégration avec un transporteur"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.delivery_tracking_url_warning_form
msgid "OK"
msgstr "OK"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_product_product__country_of_origin
#: model:ir.model.fields,field_description:stock_delivery.field_product_template__country_of_origin
msgid "Origin of Goods"
msgstr "Origine des marchandises"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.choose_delivery_package_view_form
msgid "Package"
msgstr "Colis"

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/stock_picking.py:0
msgid "Package Details"
msgstr "Détails du colis"

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/wizard/choose_delivery_package.py:0
msgid "Package too heavy!"
msgstr "Colis trop lourd !"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_stock_quant_package
msgid "Packages"
msgstr "Colis"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_choose_delivery_package__picking_id
msgid "Picking"
msgstr "Transfert"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.sale_order_portal_content_inherit_sale_stock_inherit_website_sale
#: model_terms:ir.ui.view,arch_db:stock_delivery.view_picking_withcarrier_out_form
msgid "Print Return Label"
msgstr "Imprimer l'étiquette de retour"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_product_template
msgid "Product"
msgstr "Produit"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "Mouvements de produit (Ligne de mouvement de stock)"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__delivery_type
msgid "Provider"
msgstr "Fournisseur"

#. module: stock_delivery
#: model:ir.model.fields.selection,name:stock_delivery.selection__delivery_carrier__invoice_policy__real
msgid "Real cost"
msgstr "Coût réel"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__return_label_ids
msgid "Return Label"
msgstr "Étiquette de retour"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_stock_return_picking
msgid "Return Picking"
msgstr "Transfert de retour"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_delivery_carrier__route_ids
#: model_terms:ir.ui.view,arch_db:stock_delivery.view_delivery_carrier_form_inherit_stock_delivery
msgid "Routes"
msgstr "Routes"

#. module: stock_delivery
#: model:ir.model.fields,help:stock_delivery.field_product_product__country_of_origin
#: model:ir.model.fields,help:stock_delivery.field_product_template__country_of_origin
msgid ""
"Rules of origin determine where goods originate, i.e. not where they have been shipped from, but where they have been produced or manufactured.\n"
"As such, the ‘origin’ is the 'economic nationality' of goods traded in commerce."
msgstr ""
"Les règles d'origine déterminent l'origine des marchandises, c'est-à-dire non pas l'endroit d'où elles ont été expédiées, mais celui où elles ont été produites ou fabriquées.\n"
"En tant que telle, l'‘origine’ est la 'nationalité économique' des marchandises échangées dans le commerce."

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_move_line__sale_price
msgid "Sale Price"
msgstr "Prix de vente"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_sale_order
msgid "Sales Order"
msgstr "Commande client"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_sale_order_line
msgid "Sales Order Line"
msgstr "Ligne de commande"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.choose_delivery_package_view_form
msgid "Save"
msgstr "Enregistrer"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.view_picking_withcarrier_out_form
msgid "Send to Shipper"
msgstr "Envoyer à l'expéditeur"

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/stock_picking.py:0
msgid ""
"Shipment sent to carrier %(carrier_name)s for shipping with tracking number "
"%(ref)s"
msgstr ""
"Envoi au transporteur %(carrier_name)s pour expédition avec le numéro de "
"suivi %(ref)s"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__carrier_price
msgid "Shipping Cost"
msgstr "Frais d'expédition"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.view_picking_withcarrier_out_form
msgid "Shipping Information"
msgstr "Informations d'expédition"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_delivery_carrier
#: model_terms:ir.ui.view,arch_db:stock_delivery.stock_location_route_view_form_inherit_stock_delivery
msgid "Shipping Methods"
msgstr "Modes d'expédition"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_choose_delivery_package__shipping_weight
msgid "Shipping Weight"
msgstr "Poids d'expédition"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_package_barcode_small_delivery
msgid "Shipping Weight:"
msgstr "Poids d'expédition :"

#. module: stock_delivery
#: model:ir.model.fields,help:stock_delivery.field_product_product__hs_code
#: model:ir.model.fields,help:stock_delivery.field_product_template__hs_code
msgid "Standardized code for international shipping and goods declaration."
msgstr ""
"Code normalisé pour l'expédition internationale et la déclaration de "
"marchandises."

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_stock_move
msgid "Stock Move"
msgstr "Mouvement de stock"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_stock_package_type
msgid "Stock package type"
msgstr "Type de colis de stock"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_quant_package__weight_uom_rounding
msgid "Technical field indicating weight's number of decimal places"
msgstr "Champ technique indiquant le nombre de décimales du poids."

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_quant_package__weight_is_kg
msgid "Technical field indicating whether weight uom is kg or not (i.e. lb)"
msgstr ""
"Champ technique indiquant si l'unité de mesure de poids est en kg ou non "
"(c'est-à-dire lb)"

#. module: stock_delivery
#: model:ir.model.fields,help:stock_delivery.field_stock_move_line__destination_country_code
#: model:ir.model.fields,help:stock_delivery.field_stock_picking__destination_country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"Code de pays ISO en deux caractères. \n"
"Vous pouvez utiliser ce champ pour une recherche rapide."

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/delivery_carrier.py:0
msgid ""
"The package cannot be created because the total weight of the products in "
"the picking is 0.0 %s"
msgstr ""
"Le colis ne peut pas être créé, parce que le poids total des produits dans "
"le transfert est 0.0 %s"

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/wizard/choose_delivery_carrier.py:0
msgid "The shipping price will be set once the delivery is done."
msgstr "Le prix d'expédition sera fixé une fois la livraison effectuée."

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/wizard/choose_delivery_package.py:0
msgid ""
"The weight of your package is higher than the maximum weight authorized for "
"this package type. Please choose another package type."
msgstr ""
"Le poids de votre colis est supérieur au poids maximal autorisé pour ce type"
" de colis. Veuillez choisir un autre type de colis."

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/delivery_carrier.py:0
msgid "There is no matching delivery rule."
msgstr "Il n'existe pas de règle de livraison correspondante."

#. module: stock_delivery
#: model:ir.model.fields,help:stock_delivery.field_stock_quant_package__weight
msgid "Total weight of all the products contained in the package."
msgstr "Poids total de tous les produits contenus dans le colis."

#. module: stock_delivery
#: model:ir.model.fields,help:stock_delivery.field_stock_picking__weight
msgid "Total weight of the products in the picking."
msgstr "Poids total des produits dans le transfert."

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.delivery_tracking_url_warning_form
msgid "Trackers URL"
msgstr "URL du tracker"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.view_picking_withcarrier_out_form
msgid "Tracking"
msgstr "Suivi"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__carrier_tracking_ref
msgid "Tracking Reference"
msgstr "Référence de suivi"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__carrier_tracking_url
msgid "Tracking URL"
msgstr "URL de suivi"

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/stock_picking.py:0
msgid "Tracking links for shipment:"
msgstr "Liens de suivi de l'envoi :"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.sale_order_portal_content_inherit_sale_stock_inherit_website_sale
msgid "Tracking:"
msgstr "Suivi :"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_stock_picking
msgid "Transfer"
msgstr "Transfert"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_move__weight
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__weight
#: model:ir.model.fields,field_description:stock_delivery.field_stock_quant_package__weight
#: model_terms:ir.ui.view,arch_db:stock_delivery.view_picking_withcarrier_out_form
msgid "Weight"
msgstr "Poids"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.view_picking_withcarrier_out_form
msgid "Weight for shipping"
msgstr "Poids pour l'expédition"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_choose_delivery_package__weight_uom_name
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__weight_uom_name
#: model:ir.model.fields,field_description:stock_delivery.field_stock_quant_package__weight_uom_name
msgid "Weight unit of measure label"
msgstr "Intitulé de l'unité de mesure de poids "

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_package_barcode_small_delivery
msgid "Weight:"
msgstr "Poids :"

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/stock_picking.py:0
msgid ""
"You cannot pack products into the same package when they have different "
"carriers (i.e. check that all of their transfers have a carrier assigned and"
" are using the same carrier)."
msgstr ""
"Il n'est pas possible de regrouper des produits dans un même colis s'ils ont"
" des transporteurs différents (vérifiez que tous leurs transferts ont un "
"transporteur assigné et qu'ils ont recours au même transporteur)."

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.delivery_tracking_url_warning_form
msgid "You have multiple tracker links, they are available in the chatter."
msgstr ""
"Il existe de multiples liens de tracker, ils sont disponibles dans le "
"chatter."

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/stock_picking.py:0
msgid ""
"Your delivery method has no redirect on courier provider's website to track "
"this order."
msgstr ""
"Votre mode de livraison n'a pas de redirection sur le site web du "
"fournisseur de messagerie pour suivre cette commande."

#. module: stock_delivery
#: model:ir.ui.menu,name:stock_delivery.menu_delivery_zip_prefix
msgid "Zip Prefix"
msgstr "Préfixe postal"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.label_package_template_view_delivery
msgid "^A0N,44,33^FDShipping Weight:"
msgstr "^A0N,44,33^FDShipping Weight:"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.label_package_template_view_delivery
msgid "^A0N,44,33^FDWeight:"
msgstr "^A0N,44,33^FDWeight:"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.label_package_template_view_delivery
msgid "^FO310,200"
msgstr "^FO310,200"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.label_package_template_view_delivery
msgid "^FS"
msgstr "^FS"
