# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# Konsta Aavaranta, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON> <miu<PERSON><EMAIL>>, 2024
# Mi<PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <jenni.heikki<PERSON>@sv-oy.fi>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>p<PERSON> <<EMAIL>>, 2024
# Martin Trigaux, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# Jessica Jakara, 2025
# <AUTHOR> <EMAIL>, 2025
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "\" category."
msgstr "\" kategoria."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"\"Optional\" allows guests to register from the order confirmation email to "
"track their order."
msgstr ""
"\"Valinnainen\" antaa vieraille mahdollisuuden rekisteröityä "
"tilausvahvistussähköpostista. Näin he voivat seurata tilaustaan."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_image_view_kanban
msgid "#{record.name.value}"
msgstr "#{record.name.value}"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "%s review"
msgstr "%s arvostelu"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "%s reviews"
msgstr "%s arvostelut"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "&amp; Delivery"
msgstr "&amp; Toimitus"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_item
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout_layout
msgid "&amp;nbsp;item(s)&amp;nbsp;-&amp;nbsp;"
msgstr "&amp;nbsp;item(s)&amp;nbsp;-&amp;nbsp;"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "'. Showing results for '"
msgstr "'. Näytetään tulokset  '"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_width__100_pc
msgid "100 %"
msgstr "100%"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "100 percent"
msgstr "100 prosenttia"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_width__50_pc
msgid "50 %"
msgstr "50%"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "50 percent"
msgstr "50 prosenttia"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_width__66_pc
msgid "66 %"
msgstr "66 %"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "66 percent"
msgstr "66 prosenttia"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout_layout
msgid "<b class=\"w-100\">Order summary</b>"
msgstr "<b class=\"w-100\">Tilausyhteenveto</b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.o_wsale_offcanvas
#: model_terms:ir.ui.view,arch_db:website_sale.products_categories_list
#: model_terms:ir.ui.view,arch_db:website_sale.products_categories_list_collapsible
msgid "<b>Categories</b>"
msgstr "<b>Kategoriat</b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
msgid "<b>Communication: </b>"
msgstr "<b>Viite: </b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_on_payment
msgid "<b>Deliver to pickup point: </b>"
msgstr "<b>Toimita noutopaikkaan: </b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_on_payment
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<b>Delivery: </b>"
msgstr "<b>Toimitus: </b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.filter_products_price
msgid "<b>Price Range</b>"
msgstr "<b>Hintahaarukka</b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.o_wsale_offcanvas
msgid "<b>Pricelist</b>"
msgstr "<b>Hinnasto</b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.o_wsale_offcanvas
msgid "<b>Sort By</b>"
msgstr "<b>Lajittelu</b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.filter_products_tags
#: model_terms:ir.ui.view,arch_db:website_sale.o_wsale_offcanvas
msgid "<b>Tags</b>"
msgstr "<b>Tunnisteet</b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_custom_text
msgid ""
"<br/>\n"
"                30-day money-back guarantee<br/>\n"
"                Shipping: 2-3 Business Days"
msgstr ""
"<br/>\n"
"                30 päivän rahat takaisin takuu<br/>\n"
"                Toimitus: 2-3 arkipäivää"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid ""
"<i class=\"fa fa-angle-left me-2 fw-light\"/>\n"
"                                    Return to shipping"
msgstr ""
"<i class=\"fa fa-angle-left me-2 fw-light\"/>\n"
"                                    Paluu toimitukseen"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_buy_now
msgid ""
"<i class=\"fa fa-bolt me-2\"/>\n"
"                Buy now"
msgstr ""
"<i class=\"fa fa-bolt me-2\"/>\n"
"                Osta nyt"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_add_to_cart
msgid "<i class=\"fa fa-cart-plus me-2\"/>Add to Cart"
msgstr "<i class=\"fa fa-cart-plus me-2\"/>Lisää ostoskoriin"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid ""
"<i class=\"fa fa-fw fa-bolt\"/>\n"
"                    Buy Now"
msgstr ""
"<i class=\"fa fa-fw fa-bolt\"/>\n"
"                    Osta nyt"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_pages_kanban_view
msgid "<i class=\"fa fa-globe me-1\" title=\"Website\"/>"
msgstr "<i class=\"fa fa-globe me-1\" title=\"Verkkosivu\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_kanban
#: model_terms:ir.ui.view,arch_db:website_sale.address_on_payment
msgid "<i class=\"fa fa-pencil me-1\"/>Edit"
msgstr "<i class=\"fa fa-pencil me-1\"/>Muokkaa"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_row
msgid ""
"<i class=\"fa fa-plus me-md-2\"/>\n"
"                    <span class=\"d-none d-md-inline\">Add address</span>"
msgstr ""
"<i class=\"fa fa-plus me-md-2\"/>\n"
"                    <span class=\"d-none d-md-inline\">Lisää osoite</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<i class=\"fa fa-print me-2\"/>Print"
msgstr "<i class=\"fa fa-print me-2\"/>Tulosta"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_order_re_order_btn
msgid ""
"<i class=\"fa fa-rotate-right me-1\"/>\n"
"                    Order Again"
msgstr ""
"<i class=\"fa fa-rotate-right me-1\"/>\n"
"                    Tilaa uudelleen"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid ""
"<i class=\"fa fa-shopping-cart me-2\"/>\n"
"                                                Add to cart"
msgstr ""
"<i class=\"fa fa-shopping-cart me-2\"/>\n"
"                                                Lisää ostoskoriin"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "<i class=\"fw-light fa fa-angle-left me-2\"/>Discard"
msgstr "<i class=\"fw-light fa fa-angle-left me-2\"/>Peruuta"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid ""
"<i class=\"oi oi-chevron-left d-lg-none me-1\" role=\"presentation\"/>All "
"Products"
msgstr ""
"<i class=\"oi oi-chevron-left d-lg-none me-1\" role=\"presentation\"/>Kaikki"
" tuotteet"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "<i class=\"oi oi-chevron-left me-1\" role=\"presentation\"/>All Products"
msgstr "<i class=\"oi oi-chevron-left me-1\" role=\"presentation\"/>Kaikki tuotteet"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.shop_product_carousel
msgid ""
"<i class=\"oi oi-chevron-left oe_unmovable border bg-white text-900\" "
"role=\"img\" aria-label=\"Previous\" title=\"Previous\"/>"
msgstr ""
"<i class=\"oi oi-chevron-left oe_unmovable border bg-white text-900\" "
"role=\"img\" aria-label=\"Edellinen\" title=\"Edellinen\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.shop_product_carousel
msgid ""
"<i class=\"oi oi-chevron-right oe_unmovable border bg-white text-900\" "
"role=\"img\" aria-label=\"Next\" title=\"Next\"/>"
msgstr ""
"<i class=\"oi oi-chevron-right oe_unmovable border bg-white text-900\" "
"role=\"img\" aria-label=\"Seuraava\" title=\"Seuraava\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "<option value=\"\">Country...</option>"
msgstr "<option value=\"\">Maa...</option>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "<option value=\"\">State / Province...</option>"
msgstr "<option value=\"\">Osavaltio / maakunta...</option>"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/crm_team.py:0
msgid ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                        You can find all abandoned carts here, i.e. the carts generated by your website's visitors from over an hour ago that haven't been confirmed yet.</p>\n"
"                        <p>You should send an email to the customers to encourage them!</p>\n"
"                    "
msgstr ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                        Täältä löydät kaikki hylätyt ostoskorit. Nämä ovat ostoskorit, jotka käyttäjät ovat luoneet yli tunti sitten, mutta joita ei ole vielä vahvistettu.\n"
"</p>\n"
"                        <p>Sinun kannattaisi lähettää asiakkaalle sähköpostia ja kannustaa häntä viemään kauppa loppuun saakka!</p> "

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sort
msgid "<small class=\"d-none d-lg-inline text-muted\">Sort By:</small>"
msgstr "<small class=\"d-none d-lg-inline text-muted\">Lajittelu:</small>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid ""
"<small class=\"form-text text-muted\">\n"
"                                                        Changing company name or VAT number is not allowed once document(s) have been issued for your account. Please contact us directly for this operation.\n"
"                                                    </small>"
msgstr ""
"<small class=\"form-text text-muted\">\n"
"                                                        Yrityksen nimen tai ALV-numeron muuttaminen ei ole sallittua sen jälkeen, kun järjestelmään on kirjattu yksikin asiakirja tai tapahtuma. Ota yhteyttä suoraan meihin tätä toimenpidettä varten.\n"
"                                                    </small>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_attributes
msgid ""
"<small class=\"mx-auto\"><b>Clear Filters</b></small>\n"
"                            <i class=\"oi oi-close\" role=\"presentation\"/>"
msgstr ""
"<small class=\"mx-auto\"><b>Tyhjennä suodattimet</b></small>\n"
"                            <i class=\"oi oi-close\" role=\"presentation\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.suggested_products_list
msgid ""
"<span class=\"d-md-none fa fa-shopping-cart\"/>\n"
"                            <span class=\"d-none d-md-inline\">Add to cart</span>"
msgstr ""
"<span class=\"d-md-none fa fa-shopping-cart\"/>\n"
"                            <span class=\"d-none d-md-inline\">Lisää ostoskoriin</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-"
"specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-"
"specific.\" groups=\"website.group_multi_website\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_preview_data
msgid ""
"<span class=\"fw-bold\" name=\"product_price\" data-oe-type=\"monetary\" data-oe-expression=\"data['price']\">\n"
"                                                <span class=\"oe_currency_value\">1,500.00</span>\n"
"                                            </span>"
msgstr ""
"<span class=\"fw-bold\" name=\"product_price\" data-oe-type=\"monetary\" data-oe-expression=\"data['price']\">\n"
"                                                <span class=\"oe_currency_value\">1,500.00</span>\n"
"                                            </span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_preview_data
msgid ""
"<span class=\"fw-bold\" name=\"product_price\" data-oe-type=\"monetary\" data-oe-expression=\"data['price']\">\n"
"                                                <span class=\"oe_currency_value\">140.00</span>\n"
"                                            </span>"
msgstr ""
"<span class=\"fw-bold\" name=\"product_price\" data-oe-type=\"monetary\" data-oe-expression=\"data['price']\">\n"
"                                                <span class=\"oe_currency_value\">140.00</span>\n"
"                                            </span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_preview_data
msgid ""
"<span class=\"fw-bold\" name=\"product_price\" data-oe-type=\"monetary\" data-oe-expression=\"data['price']\">\n"
"                                                <span class=\"oe_currency_value\">33.00</span>\n"
"                                            </span>"
msgstr ""
"<span class=\"fw-bold\" name=\"product_price\" data-oe-type=\"monetary\" data-oe-expression=\"data['price']\">\n"
"                                                <span class=\"oe_currency_value\">33.00</span>\n"
"                                            </span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_preview_data
msgid ""
"<span class=\"fw-bold\" name=\"product_price\" data-oe-type=\"monetary\" data-oe-expression=\"data['price']\">\n"
"                                                <span class=\"oe_currency_value\">750.00</span>\n"
"                                            </span>"
msgstr ""
"<span class=\"fw-bold\" name=\"product_price\" data-oe-type=\"monetary\" data-oe-expression=\"data['price']\">\n"
"                                                <span class=\"oe_currency_value\">750.00</span>\n"
"                                            </span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "<span class=\"mx-2 o_wsale_ppr_by\">by</span>"
msgstr "<span class=\"mx-2 o_wsale_ppr_by\">tekijänä </span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.delivery_method
msgid ""
"<span class=\"o_wsale_delivery_price_badge float-end fw-bold\" name=\"price\">\n"
"                    Select to compute delivery rate\n"
"                </span>"
msgstr ""
"<span class=\"o_wsale_delivery_price_badge float-end fw-bold\" name=\"price\">\n"
"                    Valitse toimitusnopeuden laskeminen\n"
"                </span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
#: model_terms:ir.ui.view,arch_db:website_sale.navigation_buttons
msgid "<span class=\"px-3\">or</span>"
msgstr "<span class=\"px-3\">tai</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "<span class=\"s_website_form_label_content\">Give us your feedback</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Anna meille palautteesi</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "<span class=\"s_website_form_label_content\">Upload a document</span>"
msgstr "<span class=\"s_website_form_label_content\">Lataa asiakirja</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "<span class=\"s_website_form_label_content\">Your Reference</span>"
msgstr "<span class=\"s_website_form_label_content\">Viitteesi</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_only_website_form_view
msgid ""
"<span class=\"text-muted\" invisible=\"product_variant_count &lt;= 1\">Based"
" on variants</span>"
msgstr ""
"<span class=\"text-muted\" invisible=\"product_variant_count &lt;= "
"1\">Perustuu variantteihin</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "<span class=\"visually-hidden\">filters active</span>"
msgstr "<span class=\"visually-hidden\">suodattimet aktiivisia</span>"

#. module: website_sale
#: model_terms:web_tour.tour,rainbow_man_message:website_sale.test_01_admin_shop_tour
msgid "<span><b>Good job!</b> You went through all steps of this tour.</span>"
msgstr ""
"<span><b>Hyvää työtä!</b> Kävit läpi kaikki tämän kiertueen vaiheet.</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "<span>Already have an account?</span>"
msgstr "<span>Onko sinulla jo tili ?</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<span>Order</span>"
msgstr "<span>Tilaus</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_document_kanban
msgid "<span>Publish on website</span>"
msgstr "<span>Julkaise verkkosivustolla</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
msgid ""
"<span>Unfortunately your order can not be confirmed as the amount of your payment does not match the amount of your cart.\n"
"                        Please contact the responsible of the shop for more information.</span>"
msgstr ""
"<span>Valitettavasti tilaustasi ei voida vahvistaa. Maksusi summa ei vastaa ostoskorisi summaa.\n"
"                        Ota yhteyttä kaupan vastuuhenkilöön saadaksesi lisätietoja.</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_product_image_form
msgid "<span>Video Preview</span>"
msgstr "<span>Esikatseluvideo</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.delivery_form
msgid "<strong>No suitable delivery method could be found.</strong>"
msgstr "<strong>Sopivaa toimitustapaa ei löytynyt.</strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<strong>Total:</strong>"
msgstr "<strong>Yhteensä:</strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.total
msgid "<strong>Total</strong>"
msgstr "<strong>Yhteensä</strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.checkout_layout
msgid "<strong>Warning!</strong>"
msgstr "<strong>Varoitus!</strong>"

#. module: website_sale
#: model:mail.template,body_html:website_sale.mail_template_sale_cart_recovery
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 0px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 0px 0px 0px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <h1 style=\"color:#A9A9A9;\">THERE'S SOMETHING IN YOUR CART.</h1>\n"
"                    Would you like to complete your purchase?<br/><br/>\n"
"                    <t t-if=\"object.order_line\">\n"
"                        <t t-foreach=\"object.website_order_line\" t-as=\"line\">\n"
"                            <hr/>\n"
"                            <table width=\"100%\">\n"
"                                <tr>\n"
"                                    <td style=\"padding: 10px; width:150px;\">\n"
"                                        <img t-attf-src=\"/web/image/product.product/{{ line.product_id.id }}/image_128\" style=\"width: 100px; height: 100px; object-fit: contain;\" alt=\"Product image\"/>\n"
"                                    </td>\n"
"                                    <td>\n"
"                                        <strong t-out=\"line.product_id.display_name or ''\">[FURN_7800] Desk Combination</strong><br/><t t-out=\"line.name or ''\">[FURN_7800] Desk Combination Desk combination, black-brown: chair + desk + drawer.</t>\n"
"                                    </td>\n"
"                                    <td width=\"100px\" align=\"right\">\n"
"                                        <t t-out=\"int(line.product_uom_qty) or ''\">10000</t> <t t-out=\"line.product_uom.name or ''\">Units</t>\n"
"                                    </td>\n"
"                                </tr>\n"
"                            </table>\n"
"                        </t>\n"
"                        <hr/>\n"
"                    </t>\n"
"                    <div style=\"text-align: center; padding: 16px 0px 16px 0px; font-size: 14px;\">\n"
"                        <a t-attf-href=\"{{ object.get_base_url() }}/shop/cart?access_token={{ object.access_token }}\" target=\"_blank\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                            Resume order\n"
"                        </a>\n"
"                    </div>\n"
"                    <t t-set=\"company\" t-value=\"object.company_id or object.user_id.company_id or user.company_id\"/>\n"
"                    <div style=\"text-align: center;\"><strong>Thank you for shopping with <t t-out=\"company.name or ''\">My Company (San Francisco)</t>!</strong></div>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 0px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 0px 0px 0px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                   <h1 style=\"color:#A9A9A9;\">OSTOSKORIISI ON JÄÄNYT TUOTTEITA.</h1>\n"
"                    Haluaisitko saattaa ostokset loppuun?<br/><br/>\n"
"                    <t t-if=\"object.order_line\">\n"
"                        <t t-foreach=\"object.website_order_line\" t-as=\"line\">\n"
"                            <hr/>\n"
"                            <table width=\"100%\">\n"
"                                <tr>\n"
"                                    <td style=\"padding: 10px; width:150px;\">\n"
"                                        <img t-attf-src=\"/web/image/product.product/{{ line.product_id.id }}/image_128\" style=\"width: 100px; height: 100px; object-fit: contain;\" alt=\"Product image\"/>\n"
"                                    </td>\n"
"                                    <td>\n"
"                                        <strong t-out=\"line.product_id.display_name or ''\">[FURN_7800] Työpöytäyhdistelmä</strong><br/><t t-out=\"line.name or ''\">[FURN_7800] Työpöytäyhdistelmä Työpöytäyhdistelmä, musta-ruskea: tuoli + työpöytä + laatikko.</t>\n"
"                                    </td>\n"
"                                    <td width=\"100px\" align=\"right\">\n"
"                                        <t t-out=\"int(line.product_uom_qty) or ''\">10000</t> <t t-out=\"line.product_uom.name or ''\">kappaletta</t>\n"
"                                    </td>\n"
"                                </tr>\n"
"                            </table>\n"
"                        </t>\n"
"                        <hr/>\n"
"                    </t>\n"
"                    <div style=\"text-align: center; padding: 16px 0px 16px 0px; font-size: 14px;\">\n"
"                        <a t-attf-href=\"{{ object.get_base_url() }}/shop/cart?access_token={{ object.access_token }}\" target=\"_blank\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                            Jatka tilausta\n"
"                        </a>\n"
"                    </div>\n"
"                   <t t-set=\"company\" t-value=\"object.company_id or object.user_id.company_id or user.company_id\"/>\n"
"                    <div style=\"text-align: center;\"><strong>Kiitos, että asioit <t t-out=\"company.name or ''\">yrityksessäni (San Francisco</t>)!</strong></div>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"            "

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_custom_text
msgid "<u>Terms and Conditions</u>"
msgstr "<u>Myyntiehdot</u>"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_delivery_carrier__website_description
msgid ""
"A description of the Product that you want to communicate to your customers."
" This description will be copied to every Sales Order, Delivery Order and "
"Customer Invoice/Credit Note"
msgstr ""
"Kuvaus tuotteesta, jonka haluat viestiä asiakkaillesi. Tämä kuvaus "
"kopioidaan jokaiselle myyntitilaukselle, toimitusjärjestykselle ja asiakkaan"
" laskulle / hyvityslaskulle"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid ""
"A detailed, formatted description to promote your product on this page. Use "
"'/' to discover more features."
msgstr ""
"Yksityiskohtainen, muotoiltu kuvaus tuotteesi mainostamiseksi tällä sivulla."
" Käytä '/' löytääksesi lisää ominaisuuksia."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid ""
"A detailed,formatted description to promote your product on"
"                                      this page. Use '/' to discover more "
"features."
msgstr ""
"Yksityiskohtainen, muotoiltu kuvaus tuotteesi mainostamiseksi tällä sivulla."
" Käytä '/' löytääksesi lisää ominaisuuksia."

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.product_template_action_website
msgid ""
"A product can be either a physical product or a service that you sell to "
"your customers."
msgstr ""
"Tuote voi olla joko fyysinen tuote tai palvelu jota myyt asiakkaillesi."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
msgid "Abandoned"
msgstr "Abandoned"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__is_abandoned_cart
#: model:ir.model.fields,field_description:website_sale.field_sale_report__is_abandoned_cart
msgid "Abandoned Cart"
msgstr "Hylätty ostoskori"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/crm_team.py:0
#: model:ir.actions.act_window,name:website_sale.action_view_abandoned_tree
#: model:ir.ui.menu,name:website_sale.menu_orders_abandoned_orders
msgid "Abandoned Carts"
msgstr "Hylätyt ostoskorit"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.crm_team_view_kanban_dashboard
msgid "Abandoned Carts to Recover"
msgstr "Hylättyjä ostoskoreja palautettavaksi"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__cart_abandoned_delay
#: model:ir.model.fields,field_description:website_sale.field_website__cart_abandoned_delay
msgid "Abandoned Delay"
msgstr "Hylkäysviive"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__send_abandoned_cart_email
msgid "Abandoned Email"
msgstr "Hylätty sähköposti"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_snippet_filter__product_cross_selling
msgid "About cross selling products"
msgstr "Tietoa ristiinmyyntituotteista"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Accept Terms & Conditions"
msgstr "Hyväksy ehdot"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_image_view_kanban
msgid "Acceptable file size"
msgstr "Hyväksytty tiedoston koko"

#. module: website_sale
#: model:website.snippet.filter,name:website_sale.dynamic_filter_cross_selling_accessories
msgid "Accessories for Product"
msgstr "Tuotteen lisävarusteet"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__accessory_product_ids
#: model:ir.model.fields,help:website_sale.field_product_template__accessory_product_ids
msgid ""
"Accessories show up when the customer reviews the cart before payment "
"(cross-sell strategy)."
msgstr ""
"Lisävarusteet näytetään kun asiakas tarkistaa ostoskorin ennen maksua "
"(ristikkäismyntistrategia)."

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__accessory_product_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template__accessory_product_ids
msgid "Accessory Products"
msgstr "Oheistuotteet"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_add_to_cart_options
msgid "Action"
msgstr "Toiminto"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_needaction
msgid "Action Needed"
msgstr "Vaatii toimenpiteitä"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Add"
msgstr "Lisää"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_view_form_easy_inherit_website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "Add Media"
msgstr "Lisää media"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
msgid "Add To Cart"
msgstr "Lisää ostoskoriin"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__add_to_cart_action
#: model:ir.model.fields,field_description:website_sale.field_website__add_to_cart_action
msgid "Add To Cart Action"
msgstr "Lisää ostoskoriin Toiminta"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Add a customizable form during checkout (after address)"
msgstr "Lisää muokattava lomake maksutapahtumaan (osoitteen jälkeen)"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Add a reference price per UoM on products (i.e $/kg), in addition to the "
"sale price"
msgstr ""
"Lisätään tuotteisiin mittayksikön viitehinta (esim. $/kg) myyntihinnan "
"lisäksi"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__compare_list_price
#: model:ir.model.fields,help:website_sale.field_product_template__compare_list_price
#: model:ir.model.fields,help:website_sale.field_res_config_settings__group_product_price_comparison
msgid ""
"Add a strikethrough price to your /shop and product pages for comparison "
"purposes.It will not be displayed if pricelists apply."
msgstr ""
"Lisää /shop- ja tuotesivuillesi yliviivattu hinta vertailua varten. Sitä ei "
"näytetä, jos hinnastot ovat voimassa."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Add a strikethrough price, as a comparison"
msgstr "Lisää vertailun vuoksi yliviivattu hinta"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.product_quantity
msgid "Add one"
msgstr "Lisää yksi"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/snippets/s_add_to_cart/options.js:0
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_add_to_cart
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_banner
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_borderless_2
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_horizontal_card
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_horizontal_card_2
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website_sale.s_add_to_cart_options
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Add to Cart"
msgstr "Lisää ostoskoriin"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippets
msgid "Add to Cart Button"
msgstr "Lisää ostoskoriin -painike"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_website_sale_autocomplete
msgid "Address Autocomplete"
msgstr "Osoitteen automaattinen täydentäminen"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
#: model_terms:ir.ui.view,arch_db:website_sale.products_attributes
msgid "All"
msgstr "Kaikki"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_categories_list
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "All Products"
msgstr "Kaikki tuotteet"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__all_pricelist_ids
msgid "All pricelists"
msgstr "Kaikki hinnastot"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__ecommerce_access__everyone
msgid "All users"
msgstr "Kaikki käyttäjät"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_delivery_carrier_form_website_delivery
msgid "All websites"
msgstr "Kaikki verkkosivut"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Allow customers to pick up their online purchases at your store and pay in "
"person"
msgstr ""
"Anna asiakkaiden noutaa verkko-ostoksensa myymälästäsi ja maksaa ne "
"noudettaessa"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Allow shoppers to compare products based on their attributes"
msgstr ""
"Antaa asiakkaalle mahdollisuuden vertailla tuotteita ominaisuuksien "
"perusteella"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Allow signed-in users to save product in a wishlist"
msgstr "Salli sisäänkirjautuneiden käyttäjien tallentaa tuote toivelistalleen"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_pricelist__selectable
msgid "Allow the end user to choose this price list"
msgstr "Antaa loppukäyttäjän valita hinnasto"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Allow your customer to add products from previous order in their cart."
msgstr ""
"Anna asiakkaasi lisätä tuotteita edellisestä tilauksesta ostoskoriinsa."

#. module: website_sale
#: model:ir.actions.server,name:website_sale.dynamic_snippet_alternative_products
#: model:ir.model.fields,field_description:website_sale.field_product_product__alternative_product_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template__alternative_product_ids
#: model:website.snippet.filter,name:website_sale.dynamic_filter_cross_selling_alternative_products
msgid "Alternative Products"
msgstr "Vaihtoehtoiset tuotteet"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_crm_team__abandoned_carts_amount
msgid "Amount of Abandoned Carts"
msgstr "Hylättyjen ostoskorien määrä"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/delivery.py:0
msgid "Anonymous express checkout partner for order %s"
msgstr "Anonyymi pikakassakumppani tilausta varten %s"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Apartment, suite, etc."
msgstr "Osoitetarkenne"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.coupon_form
msgid "Apply"
msgstr "Käytä"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale.editor.js:0
msgid "Are you sure you want to delete this ribbon?"
msgstr "Haluatko varmasti poistaa tämän nauhan?"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Assignment"
msgstr "Tehtävä"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Assignment of online orders"
msgstr "Verkkotilausten jakaminen"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_attachment_count
msgid "Attachment Count"
msgstr "Liitteiden määrä"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_product_attribute_action
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Attributes"
msgstr "Määritteet"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_multimedia_augmented_reality
msgid "Augmented Reality Tools"
msgstr "Lisätyn todellisuuden työkalut"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Automatically send abandoned checkout emails"
msgstr "Lähetä automaattisesti hylätyn ostoskorin sähköpostit"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/product_list/product_list.js:0
msgid "Available options"
msgstr "Valinnat"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_avg
msgid "Average Rating"
msgstr "Keskimääräinen arvio"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
msgid "Back to cart"
msgstr "Takaisin ostoskoriin"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
msgid "Back to delivery"
msgstr "Takaisin toimitukseen"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Background"
msgstr "Tausta"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__bg_color
msgid "Background Color"
msgstr "Taustaväri"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__base_unit_count
#: model:ir.model.fields,field_description:website_sale.field_product_template__base_unit_count
msgid "Base Unit Count"
msgstr "Perusyksikön määrä"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__base_unit_name
#: model:ir.model.fields,field_description:website_sale.field_product_template__base_unit_name
msgid "Base Unit Name"
msgstr "Perusyksikön nimi"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__group_show_uom_price
msgid "Base Unit Price"
msgstr "Perusyksikön hinta"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.base_unit_action
msgid "Base Units"
msgstr "Perusyksiköt"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_cabinets_bathroom
msgid "Bathroom Cabinets"
msgstr "Kylpyhuoneen kaapit"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Be aware!"
msgstr "Huomio!"

#. module: website_sale
#: model:res.country.group,name:website_sale.benelux
msgid "BeNeLux"
msgstr "Benelux"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_furnitures_beds
msgid "Beds"
msgstr "Sänkyjä"

#. module: website_sale
#: model:product.pricelist,name:website_sale.list_benelux
msgid "Benelux"
msgstr "Benelux"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_spacing__big
msgid "Big"
msgstr "Suuri"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Big Icons Subtitles"
msgstr "Suuret kuvakkeiden kuvatekstit"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Billing"
msgstr "Laskutus"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
#: model_terms:ir.ui.view,arch_db:website_sale.billing_address_row
msgid "Billing address"
msgstr "Laskutusosoite"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_on_payment
msgid "Billing:"
msgstr "Laskutus:"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
msgid "Bin"
msgstr "Roskis"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_bins
msgid "Bins"
msgstr "Korit"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Boost your sales with multiple kinds of programs: Coupons, Promotions, Gift "
"Card, Loyalty. Specific conditions can be set (products, customers, minimum "
"purchase amount, period). Rewards can be discounts (% or amount) or free "
"products."
msgstr ""
"Tehosta myyntiäsi useilla erilaisilla myynninedistämisohjelmilla. Tarjolla "
"on kuponkeja, kampanjoita, lahjakortteja, kanta-asiakasohjelmia. "
"Erityisehtoja voidaan asettaa (tuotteet, asiakkaat, vähimmäisostosumma, "
"ajanjakso). Palkinnot voivat olla alennuksia (% or määrä) tai ilmaisia "
"tuotteita."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Both"
msgstr "Molemmat"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Bottom"
msgstr "Alhaalla"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
msgid "Box"
msgstr "Laatikko"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_boxes
msgid "Boxes"
msgstr "Laatikot"

#. module: website_sale
#: model:product.attribute,name:website_sale.product_attribute_brand
msgid "Brand"
msgstr "Merkki"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__website_sale_contact_us_button_url
msgid "Button Url"
msgstr "URL:n nappi"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Button url"
msgstr "Painikkeen URL"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Buttons"
msgstr "Painikkeet"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__enabled_buy_now_button
#: model_terms:ir.ui.view,arch_db:website_sale.s_add_to_cart_options
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Buy Now"
msgstr "Osta nyt"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/snippets/s_add_to_cart/options.js:0
msgid "Buy now"
msgstr "Osta heti"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_preview_data
msgid "Cabinet with Doors"
msgstr "Kaappi ovilla"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_cabinets
msgid "Cabinets"
msgstr "Kabinetit"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__can_image_1024_be_zoomed
msgid "Can Image 1024 be zoomed"
msgstr "Voiko kuvaa 1024 zoomata"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_delivery_carrier__can_publish
#: model:ir.model.fields,field_description:website_sale.field_product_template__can_publish
msgid "Can Publish"
msgstr "Voi julkaista"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Cards"
msgstr "Kortit"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_layout__carousel
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Carousel"
msgstr "Karuselli"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Cart"
msgstr "Ostoskori"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__cart_quantity
msgid "Cart Quantity"
msgstr "Määrä ostoskorissa"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__cart_recovery_mail_template
#: model:ir.model.fields,field_description:website_sale.field_website__cart_recovery_mail_template_id
msgid "Cart Recovery Email"
msgstr "Ostoskorin palautussähköposti"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__cart_recovery_email_sent
msgid "Cart recovery email already sent"
msgstr "Ostoskorin palautussähköposti on jo lähetettty"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Carts are flagged as abandoned after this delay."
msgstr "Aika jonka jälkeen ostoskori merkitään hylätyksi"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_view_tree_website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Categories"
msgstr "Tuoteryhmä(t)"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.product_public_category_action
msgid ""
"Categories are used to browse your products through the\n"
"                touchscreen interface."
msgstr ""
"Ryhmiä käytetään tuotteiden selaamiseen\n"
"             kosketusnäytön käyttöliittymässä."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_category_extra_link
msgid "Categories:"
msgstr "Kategoriat:"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "Category"
msgstr "Kategoria"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_description
msgid "Category Description"
msgstr "Kategorian kuvaus"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_footer
msgid "Category Footer"
msgstr "Kategorian alatunniste"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_category_extra_link
msgid "Category:"
msgstr "Kategoria:"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_lamps_ceiling
msgid "Ceiling Lamps"
msgstr "Kattovalaisimet"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
msgid "Chair"
msgstr "Tuoli"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_furnitures_chairs
msgid "Chairs"
msgstr "Tuolit"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_lamps_chandelier
msgid "Chandeliers"
msgstr "Kattokruunut"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.delivery_method
msgid "Change location"
msgstr "Vaihda sijaintia"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid ""
"Changing VAT number is not allowed once document(s) have been issued for "
"your account. Please contact us directly for this operation."
msgstr ""
"ALV-numeron muuttaminen ei ole sallittua sen jälkeen kun dokumentti on "
"liitetty tiliisi. Jos tämä toiminto täytyy tehdä, ota meihin yhteyttä."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid ""
"Changing company name is not allowed once document(s) have been issued for "
"your account. Please contact us directly for this operation."
msgstr ""
"Yrityksen nimen muuttaminen ei ole sallittua sen jälkeen kun dokumentti on "
"liitetty tiliisi. Jos tämä toiminto täytyy tehdä, ota meihin yhteyttä."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
msgid ""
"Changing your name is not allowed once invoices have been issued for your "
"account. Please contact us directly for this operation."
msgstr ""
"Nimen muuttaminen ei ole sallittua sen jälkeen kun sinua on laskutettu. "
"Tähän liittyen, ota meihin yhteyttä."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
msgid "Checkout"
msgstr "Maksamaan"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Checkout Pages"
msgstr "Kassan sivut"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__child_id
msgid "Children Categories"
msgstr "Alikategoriat"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.delivery_form
msgid "Choose a delivery method"
msgstr "Valitse toimitustapa"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/location_selector/location_selector_dialog/location_selector_dialog.js:0
msgid "Choose a pick-up point"
msgstr "Valitse noutopaikka"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/location_selector/location_selector_dialog/location_selector_dialog.js:0
#: code:addons/website_sale/static/src/js/location_selector/map_container/map_container.js:0
msgid "Choose this location"
msgstr "Valitse tämä sijainti"

#. module: website_sale
#: model:product.pricelist,name:website_sale.list_christmas
msgid "Christmas"
msgstr "Joulu"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "City"
msgstr "Kaupunki"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.o_wsale_offcanvas
#: model_terms:ir.ui.view,arch_db:website_sale.products_attributes
msgid "Clear Filters"
msgstr "Nollaa suotimet"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_website_sale_collect
msgid "Click & Collect"
msgstr "Osta verkosta ja nouda myymälästä"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid ""
"Click <i>'New'</i> in the top-right corner to create your first product."
msgstr ""
"Klikkaa<i>'Uusi'</i> oikeassa yläkulmassa luodaksesi ensimmäisen tuotteesi."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "Click here"
msgstr "Klikkaa tästä"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
msgid "Click here to open the reporting menu"
msgstr "Avaa raportointivalikko napsauttamalla tästä"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
msgid "Click on <em>Save</em> to create the product."
msgstr "Luo tuote napsauttamalla <em>Tallenna</em>."

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
msgid "Click on this button so your customers can see it."
msgstr "Klikkaa tätä nappia jotta asiakkaat voivat nähdä sen."

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/notification/cart_notification/cart_notification.xml:0
#: model_terms:ir.ui.view,arch_db:website_sale.o_wsale_offcanvas
msgid "Close"
msgstr "Sulje"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/location_selector/location_schedule/location_schedule.js:0
msgid "Closed"
msgstr "Suljettu"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Collapse Category Recursive"
msgstr "Sulje kategoriat alitasoineen"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_boxes_collapsible
msgid "Collapsible Boxes"
msgstr "Kokoontaitettavat laatikot"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Collapsible sidebar"
msgstr "Kokoontaitettava sivupalkki"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Color"
msgstr "Väri"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Columns"
msgstr "Sarakkeet"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_product_combos
msgid "Combo Choices"
msgstr "Yhdistelmävalinnat"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid ""
"Comma-separated list of parts of product names, barcodes or internal "
"reference"
msgstr ""
"Pilkulla erotettu luettelo tuotenimien osista, viivakoodeista tai sisäisistä"
" viitteistä"

#. module: website_sale
#: model:ir.model,name:website_sale.model_res_company
msgid "Companies"
msgstr "Yritykset"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_form_editor.js:0
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Company Name"
msgstr "Yrityksen nimi"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__compare_list_price
#: model:ir.model.fields,field_description:website_sale.field_product_template__compare_list_price
msgid "Compare to Price"
msgstr "Vertaa hintaan"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__group_product_price_comparison
#: model:res.groups,name:website_sale.group_product_price_comparison
msgid "Comparison Price"
msgstr "Vertailuhinta"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_desks_components
msgid "Components"
msgstr "Komponentit"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping cost and ship with Easypost"
msgstr "Laske toimituskulu ja lähetä Easypostilla"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping cost and ship with Shiprocket"
msgstr "Laske toimituskustannukset ja lähetä Shiprocketilla"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with DHL"
msgstr "Laske toimituskulut ja lähetä DHL:llä"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with FedEx"
msgstr "Laske toimituskulut ja lähetä FedEx:llä"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with UPS"
msgstr "Laske toimituskulut ja lähetä UPS:llä"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with USPS"
msgstr "Laske toimituskulut ja lähetä USPS:llä"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with bpost"
msgstr "Laske toimituskulut ja lähetä bpostilla"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs on orders"
msgstr "Laske toimituskulut tilauksille"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_preview_data
msgid "Conference Chair"
msgstr "luentosalin tuoli"

#. module: website_sale
#: model:ir.model,name:website_sale.model_res_config_settings
msgid "Config Settings"
msgstr "Asetukset"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Configure Form"
msgstr "Määritä lomake"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/product_configurator_dialog/product_configurator_dialog.js:0
msgid "Configure your product"
msgstr "Määritä tuotteesi"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
msgid "Confirm"
msgstr "Vahvista"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.navigation_buttons
msgid "Confirm Order"
msgstr "Vahvista tilaus"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Confirm order"
msgstr "Vahvista tilaus"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
msgid "Confirmed"
msgstr "Vahvistettu"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Confirmed Orders"
msgstr "Vahvistetut tilaukset"

#. module: website_sale
#: model:ir.model,name:website_sale.model_res_partner
msgid "Contact"
msgstr "Kontakti"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "Contact Us"
msgstr "Ota yhteyttä"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__contact_us_button_url
msgid "Contact Us Button URL"
msgstr "Ota yhteyttä -painikkeen URL-osoite"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/combo_configurator_dialog/combo_configurator_dialog.xml:0
#: code:addons/website_sale/static/src/js/product_configurator_dialog/product_configurator_dialog.xml:0
msgid "Continue Shopping"
msgstr "Lisää ostoskoriin ja jatka ostoksia"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Continue checkout"
msgstr "Jatka kassalle"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid ""
"Continue checkout\n"
"                                    <i class=\"fa fa-angle-right ms-2 fw-light\"/>"
msgstr ""
"Jatka kassalle\n"
"                                   <i class=\"fa fa-angle-right ms-2 fw-light\"/>"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
msgid "Continue shopping"
msgstr "Jatka ostoksia"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_furnitures_couches
msgid "Couches"
msgstr "Sohvat"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Country"
msgstr "Maa"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Create"
msgstr "Luo"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.product_template_action_website
msgid "Create a new product"
msgstr "Luo uusi tuote"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__create_uid
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__create_uid
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__create_uid
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__create_uid
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__create_uid
msgid "Created by"
msgstr "Luonut"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_mega_menu_images_subtitles
#: model_terms:ir.ui.view,arch_db:website_sale.s_mega_menu_little_icons
msgid ""
"Created in 2021, the company is young and dynamic. Discover the composition "
"of the team and their skills."
msgstr ""
"Vuonna 2021 perustettu yritys on nuori ja dynaaminen. Tutustu tiimin "
"kokoonpanoon ja osaamiseen."

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__create_date
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__create_date
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__create_date
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__create_date
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__create_date
msgid "Created on"
msgstr "Luotu"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Creation Date"
msgstr "Luontipäivä"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "Current Category or All"
msgstr "Nykyinen luokka tai Kaikki"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__base_unit_id
#: model:ir.model.fields,field_description:website_sale.field_product_template__base_unit_id
msgid "Custom Unit of Measure"
msgstr "Mukautettu mittayksikkö"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Customer"
msgstr "Asiakas"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__auth_signup_uninvited
msgid "Customer Account"
msgstr "Asiakastili"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__account_on_checkout
#: model:ir.model.fields,field_description:website_sale.field_website__account_on_checkout
msgid "Customer Accounts"
msgstr "Asiakastilit"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Customer Country"
msgstr "Asiakkaan maa"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_comment
msgid "Customer Reviews"
msgstr "Asiakkaiden arvostelut"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Customer needs to be signed in otherwise the mail address is not known.     \n"
"\n"
"- If a potential customer creates one or more abandoned checkouts and then completes a sale before the recovery email gets sent, then the email won't be sent.     \n"
"\n"
"- If user has manually sent a recovery email, the mail will not be sent a second time     \n"
"\n"
"- If a payment processing error occurred when the customer tried to complete their checkout, then the email won't be sent.     \n"
"\n"
"- If your shop does not support shipping to the customer's address, then the email won't be sent.     \n"
"\n"
"- If none of the products in the checkout are available for purchase (empty inventory, for example), then the email won't be sent.     \n"
"\n"
"- If all the products in the checkout are free, and the customer does not visit the shipping page to add a shipping fee or the shipping fee is also free, then the email won't be sent."
msgstr ""
"Asiakkaan on kirjauduttava sisään, muuten sähköpostiosoite ei ole tiedossa.     \n"
"\n"
"- Jos potentiaalinen asiakas luo yhden tai useamman hylätyn ostostapahtuman ja suorittaa sitten myynnin ennen kuin palautussähköposti lähetetään, sähköpostia ei lähetetä.     \n"
"\n"
"- Jos käyttäjä on lähettänyt palautussähköpostin manuaalisesti, sähköpostia ei lähetetä toista kertaa\n"
"\n"
"- Jos maksun käsittelyvirhe tapahtui, kun asiakas yritti saattaa kassan loppuun, sähköpostia ei lähetetä.     \n"
"\n"
"- Jos kauppasi ei tue toimitusta asiakkaan osoitteeseen, sähköpostia ei lähetetä.     \n"
"\n"
"- Jos yksikään kassalla olevista tuotteista ei ole ostettavissa (esimerkiksi tyhjä varasto), sähköpostia ei lähetetä.     \n"
"\n"
"- Jos kaikki kassalla olevat tuotteet ovat ilmaisia, eikä asiakas käy toimitussivulla lisäämässä toimitusmaksua tai jos toimitusmaksu on myös ilmainen, sähköpostia ei lähetetä."

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_orders_customers
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Customers"
msgstr "Asiakkaat"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_preview_data
msgid "Customizable Desk"
msgstr "Muokattava pöytä"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Customize Abandoned Email Template"
msgstr "Mukauta hylätyn sähköpostin mallia"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/res_config_settings.py:0
msgid "Customize Email Templates"
msgstr "Mukauta sähköpostimalleja"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "DHL Express Connector"
msgstr "DHL Express Liitin"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "DHL Shipping Methods"
msgstr "DHL Toimitusmenetelmät"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "DROP BUILDING BLOCKS HERE TO MAKE THEM AVAILABLE ACROSS ALL PRODUCTS"
msgstr "PUDOTA RAKENNUSPALIKAT TÄHÄN KÄYTTÄÄKSESI NIITÄ KAIKILLA TUOTTEILLA"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Default"
msgstr "Oletus"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Default (1/1)"
msgstr "Oletusarvo (1/1)"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Default (1x1)"
msgstr "Oletus (1x1)"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__currency_id
msgid "Default Currency"
msgstr "Oletusvaluutta"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__pricelist_id
msgid "Default Pricelist if any"
msgstr "Mahdollinen oletushinnasto"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Default Sort"
msgstr "Oletuslajittelu"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__base_unit_id
#: model:ir.model.fields,help:website_sale.field_product_template__base_unit_id
#: model:ir.model.fields,help:website_sale.field_website_base_unit__name
msgid ""
"Define a custom unit to display in the price per unit of measure field."
msgstr ""
"Määritä mukautettu yksikkö, joka näytetään hinta per mittayksikkö -kentässä."

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.product_public_category_action
msgid "Define a new category"
msgstr "Luo uusi tuoteryhmä"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.product_ribbon_action
msgid "Define a new ribbon"
msgstr "Määritä uusi nauha"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Delete Ribbon"
msgstr "Poista nauha"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website_sale.total
msgid "Delivery"
msgstr "Toimitus"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_on_payment
msgid "Delivery &amp;"
msgstr "Toimitus &amp;"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__amount_delivery
msgid "Delivery Amount"
msgstr "Toimitusmäärä"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_delivery
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Delivery Methods"
msgstr "Toimitustavat"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
#: model_terms:ir.ui.view,arch_db:website_sale.delivery_address_row
msgid "Delivery address"
msgstr "Toimitusosoite"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_services_delivery_and_installation
msgid "Delivery and Installation"
msgstr "Toimitus ja asennus"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.total
msgid "Delivery will be updated after choosing a new delivery method"
msgstr "Toimitus päivitetään uuden toimitustavan valinnan jälkeen"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
msgid "Description"
msgstr "Kuvaus"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_delivery_carrier_form_website_delivery
msgid "Description displayed on the eCommerce and on online quotations."
msgstr "Kuvaus verkkokaupan ja verkkosivuston tarjouksiin."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_public_category_form_view
msgid "Description displayed on the eCommerce categories page."
msgstr "Verkkokaupan kategoriasivulla näkyvä kuvaus."

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_delivery_carrier__website_description
msgid "Description for Online Quotations"
msgstr "Kuvaus verkkosivun tarjouksiin"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_description
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_description
msgid "Description for the website"
msgstr "Verkkosivuston kuvaus"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_services_design_and_planning
msgid "Design and Planning"
msgstr "Suunnittelu ja suunnittelu"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_lamps_desk
msgid "Desk Lamps"
msgstr "Pöytävalaisimet"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_desks
msgid "Desks"
msgstr "Pöydät"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__website_sequence
#: model:ir.model.fields,help:website_sale.field_product_template__website_sequence
msgid "Determine the display order in the Website E-commerce"
msgstr "Määritä verkkokauppasivuston esitysjärjestys"

#. module: website_sale
#: model:ir.model,name:website_sale.model_digest_digest
msgid "Digest"
msgstr "Yhteenveto"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__res_config_settings__account_on_checkout__disabled
#: model:ir.model.fields.selection,name:website_sale.selection__website__account_on_checkout__disabled
msgid "Disabled (buy as guest)"
msgstr "Ei käytössä (osta vierailijana)"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
msgid "Discard"
msgstr "Hylkää"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.coupon_form
msgid "Discount code..."
msgstr "Alennuskoodi..."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Discounts, Loyalty & Gift Card"
msgstr "Alennukset, kanta-asiakkuus ja lahjakortti"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_mega_menu_images_subtitles
#: model_terms:ir.ui.view,arch_db:website_sale.s_mega_menu_little_icons
msgid "Discover our team"
msgstr "Tutustu tiimiimme"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__display_name
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__display_name
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__display_name
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__display_name
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__display_name
msgid "Display Name"
msgstr "Näyttönimi"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Display Product Prices"
msgstr "Näytä tuotteiden hinnat"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Display Type"
msgstr "Näyttötyyppi"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__base_unit_count
#: model:ir.model.fields,help:website_sale.field_product_template__base_unit_count
msgid ""
"Display base unit price on your eCommerce pages. Set to 0 to hide it for "
"this product."
msgstr ""
"Näytä perusyksikköhinta verkkokauppasivuillasi. Aseta arvoksi 0 "
"piilottaaksesi sen tältä tuotteelta."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "Displayed in bottom of product pages"
msgstr "Näytetään tuotesivujen alareunassa"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__base_unit_name
#: model:ir.model.fields,help:website_sale.field_product_template__base_unit_name
msgid ""
"Displays the custom unit for the products if defined or the selected unit of"
" measure otherwise."
msgstr ""
"Näyttää tuotteiden mukautetun yksikön, jos se on määritelty, tai muuten "
"valitun mittayksikön."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/digest.py:0
msgid "Do not have access, skip this data for user's digest email"
msgstr ""
"Sinulla ei ole oikeutta tähän toimintoon. Et voi käyttää tätä tietoa "
"käyttäjien  yhteenvetopostissa"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_reorder.js:0
msgid "Do you wish to clear your cart before adding products to it?"
msgstr "Haluatko tyhjentää ostoskorisi ennen tuotteiden lisäämistä siihen?"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "Documents"
msgstr "Dokumentit"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/product_document.py:0
msgid ""
"Documents shown on product page cannot be restricted to a specific variant"
msgstr ""
"Tuotesivulla näkyviä asiakirjoja ei voi rajoittaa tiettyyn vaihtoehtoon"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
msgid "Double click here to set an image describing your product."
msgstr "Kaksoisklikkaa tästä asettaaksesi kuvan joka kuvailee tuotettasi."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid ""
"Drag building blocks here to customize the footer for\n"
"                                    \""
msgstr ""
"Vedä rakennuspalikoita tänne, jotta voit muokata alatunnisteen\n"
"                                    \""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid ""
"Drag building blocks here to customize the header for\n"
"                                    \""
msgstr ""
"Vedä rakennuspalikoita tänne muokataksesi ylätunnistetta\n"
"                                    \""

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
msgid "Drawer"
msgstr "Laatikko"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_drawers
msgid "Drawers"
msgstr "Laatikot"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__shop_extra_field_ids
msgid "E-Commerce Extra Fields"
msgstr "Verkkokaupan lisäkentät"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_sale_extra_field
msgid "E-Commerce Extra Info Shown on product page"
msgstr "Verkkokaupan lisätiedot näkyvät tuotesivulla"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_document_form
msgid "E-commerce"
msgstr "Verkkokauppa"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_pricelist__code
msgid "E-commerce Promotional Code"
msgstr "Verkkokaupan etukoodi"

#. module: website_sale
#: model:product.pricelist,name:website_sale.list_europe
msgid "EUR"
msgstr "EUR"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Easypost"
msgstr "Easypost"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Easypost Shipping Methods"
msgstr "Easypost Toimitusmenetelmät"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_pricelist_form_view
msgid "Ecommerce"
msgstr "Verkkokauppa"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__ecommerce_access
#: model:ir.model.fields,field_description:website_sale.field_website__ecommerce_access
msgid "Ecommerce Access"
msgstr "Pääsy verkkokauppaan"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "Ecommerce Description"
msgstr "Sähköinen kaupankäynti kuvaus"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "Ecommerce Shop"
msgstr "Verkkokauppa"

#. module: website_sale
#: model:mail.template,name:website_sale.mail_template_sale_cart_recovery
msgid "Ecommerce: Cart Recovery"
msgstr "Sähköinen kaupankäynti: Ostoskorin palautus"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Edit"
msgstr "Muokkaa"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
msgid "Edit the price of this product by clicking on the amount."
msgstr "Muokkaa tämän tuotteen hintaa klikkaamalla määrää."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_kanban
msgid "Edit this address"
msgstr "Muokkaa osoitetta"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_multimedia_education
msgid "Education Tools"
msgstr "Koulutustyökalut"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
#: model_terms:ir.ui.view,arch_db:website_sale.product_share_buttons
msgid "Email"
msgstr "Sähköposti"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__embed_code
msgid "Embed Code"
msgstr "Upota koodi"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Enforce a customer to be logged in to access 'Shop'"
msgstr "Edellytä sisäänkirjautumista ennen pääsyä verkkokauppaan ('Shop')"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/product_template_attribute_line/product_template_attribute_line.js:0
msgid "Enter a customized value"
msgstr "Syötä mukautettu arvo"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
msgid "Enter a name for your new product"
msgstr "Syötä nimi uudelle tuotteellesi"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/product_public_category.py:0
msgid "Error! You cannot create recursive categories."
msgstr "Virhe! Et voi luoda rekursiivisia luokkia."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Extra Images"
msgstr "Lisää kuvia"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
msgid "Extra Info"
msgstr "Lisätiedot"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__product_template_image_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template__product_template_image_ids
msgid "Extra Product Media"
msgstr "Tuotteen lisäkuvat"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Extra Step"
msgstr "Lisäaskel"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__enabled_extra_checkout_step
msgid "Extra Step During Checkout"
msgstr "Lisävaihe maksutapahtuman aikana"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__product_variant_image_ids
msgid "Extra Variant Images"
msgstr "Lisävarianttikuvat"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_view_form_easy_inherit_website_sale
msgid "Extra Variant Media"
msgstr "Lisävarianttimedia"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "Extra info"
msgstr "Lisätieto"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_share_buttons
msgid "Facebook"
msgstr "Facebook"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
msgid "Featured"
msgstr "Suosituimmat"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "FedEx"
msgstr "FedEx"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "FedEx Shipping Methods"
msgstr "FedEx Toimitusmenetelmät"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__field_id
msgid "Field"
msgstr "Kenttä"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__label
msgid "Field Label"
msgstr "Kentän otsikko"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__name
msgid "Field Name"
msgstr "Kentän nimi"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_drawers_file
msgid "File Drawers"
msgstr "Arkistolaatikot"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Fill"
msgstr "Täytä"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Fill in your address"
msgstr "Täytä osoitteesi"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__fiscal_position_id
msgid "Fiscal Position"
msgstr "Verokanta"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_desks_foldable
msgid "Foldable Desks"
msgstr "Kokoontaitettavat työpöydät"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_follower_ids
msgid "Followers"
msgstr "Seuraajat"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seuraajat (kumppanit)"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_bins_storage
msgid "Food Storage Bins"
msgstr "Ruoan säilytysastiat"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/checkout.js:0
msgid "Free"
msgstr "Ilmainen"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
msgid "From Website"
msgstr "Nettisivulta"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Full name"
msgstr "Koko nimi"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_furnitures
msgid "Furnitures"
msgstr "Kalusteet"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_desks_gaming
msgid "Gaming Desks"
msgstr "Pelipöydät"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Gap"
msgstr "Väli"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Generate the invoice automatically when the online payment is confirmed"
msgstr "Luo lasku automaattisesti, kun verkkomaksu on vahvistettu"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_desks_glass
msgid "Glass Desks"
msgstr "Lasipöydät"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/tour_utils.js:0
#: model:ir.model.fields.selection,name:website_sale.selection__website__add_to_cart_action__go_to_cart
msgid "Go to cart"
msgstr "Siirry ostoskoriin"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_layout__grid
#: model_terms:ir.ui.view,arch_db:website_sale.add_grid_or_list_option
#: model_terms:ir.ui.view,arch_db:website_sale.products
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Grid"
msgstr "Taulukko"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__shop_gap
msgid "Grid-gap on the shop"
msgstr "Taulukon väli kaupassa"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Group By"
msgstr "Ryhmittely"

#. module: website_sale
#: model:ir.model,name:website_sale.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP-reititys"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__has_message
msgid "Has Message"
msgstr "Sisältää viestin"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__product_attribute__visibility__hidden
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_width__none
msgid "Hidden"
msgstr "Piilotettu"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__prevent_zero_price_sale
msgid "Hide 'Add To Cart' when price = 0"
msgstr "Piilota 'Lisää ostoskoriin' kun hinta = 0"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Hours."
msgstr "Tuntia."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_image_view_kanban
msgid "Huge file size. The image should be optimized/reduced."
msgstr "Suuri tiedostokoko. Kuvaa kannattaa optimoida/pienentää."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.accept_terms_and_conditions
msgid "I agree to the"
msgstr "Hyväksyn"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__id
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__id
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__id
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__id
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__id
msgid "ID"
msgstr "ID"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_tag_form_view_inherit_website_sale
msgid "If an image is set, the color will not be used on eCommerce."
msgstr "Jos kuva on asetettu, väriä ei käytetä verkkokaupassa."

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_template__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Jos valittu, uudet viestit vaativat huomiotasi."

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_template__message_has_error
#: model:ir.model.fields,help:website_sale.field_product_template__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Jos valittu, joitakin viestejä ei ole toimitettu."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "If product price equals 0, replace 'Add to Cart' by 'Contact us'."
msgstr ""
"Jos tuotteen hinta on 0, korvaa 'Lisää ostoskoriin' sanalla 'Ota yhteyttä'."

#. module: website_sale
#: model:mail.template,description:website_sale.mail_template_sale_cart_recovery
msgid ""
"If the setting is set, sent to authenticated visitors who abandoned their "
"cart"
msgstr ""
"Jos asetettu, lähetetään todennetuille kävijöille, jotka ovat hylänneet "
"ostoskorinsa"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
msgid ""
"If you are ordering for an external person, please place your order via the "
"backend. If you wish to change your name or email address, please do so in "
"the account settings or contact your administrator."
msgstr ""
"Jos teet tilauksen ulkopuoliselle henkilölle, tee tilaus backendin kautta. "
"Jos haluat muuttaa nimeäsi tai sähköpostiosoitettasi, tee se tilin "
"asetuksissa tai ota yhteyttä ylläpitäjääsi."

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_pricelist__website_id
msgid ""
"If you want a pricelist to be available on a website,you must fill in this "
"field or make it selectable.Otherwise, the pricelist will not apply to any "
"website."
msgstr ""
"Jos haluat, että hinnasto on saatavilla jollakin verkkosivulla, sinun on "
"täytettävä tämä kenttä tai asetettava se valittavaksi.Muussa tapauksessa "
"hinnasto ei koske mitään verkkosivua."

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__image_1920
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__image_1920
#: model:ir.model.fields,field_description:website_sale.field_product_tag__image
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
msgid "Image"
msgstr "Kuva"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__image_1024
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__image_1024
msgid "Image 1024"
msgstr "Kuva 1024"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__image_128
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__image_128
msgid "Image 128"
msgstr "Kuva 128"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__image_256
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__image_256
msgid "Image 256"
msgstr "Kuva 256"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__image_512
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__image_512
msgid "Image 512"
msgstr "Kuva 512"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Image Menu"
msgstr "Kuva-valikko"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_product_image_form
msgid "Image Name"
msgstr "Kuvan nimi"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Image Spacing"
msgstr "Kuvan välistys"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Image Zoom"
msgstr "Kuvan zoomaus"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Images Ratio"
msgstr "Kuvien suhde"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Images Size"
msgstr "Kuvat koko"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Images Subtitles"
msgstr "Kuvien aliotsikot"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Images Width"
msgstr "Kuvien leveys"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Instant checkout, instead of adding to cart"
msgstr "Välitön sirtyminen kassalle ostoskoriin lisäämisen sijaan"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
msgid "Invalid Email! Please enter a valid email address."
msgstr "Sähköpostiosoite on virheellinen. Anna oikean muotoinen osoite."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
msgid "Invalid image"
msgstr "Virheellinen kuva"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_account
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Invoicing"
msgstr "Laskutus"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Invoicing Policy"
msgstr "Laskutus perustuu"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_is_follower
msgid "Is Follower"
msgstr "On seuraaja"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_delivery_carrier__is_published
#: model:ir.model.fields,field_description:website_sale.field_product_template__is_published
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_website_tree_view
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_view_tree_website_sale
msgid "Is Published"
msgstr "On julkaistu"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Issue invoices to customers"
msgstr "Lähetä laskuja asiakkaille"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/sale_order.py:0
msgid "It is forbidden to modify a sales order which is not in draft status."
msgstr "Vain luonnos-tilassa olevaa tilausta saa muokata."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/delivery.py:0
msgid ""
"It seems that a delivery method is not compatible with your address. Please "
"refresh the page and try again."
msgstr ""
"Näyttää siltä, että toimitustapa ei ole yhteensopiva osoitteesi kanssa. "
"Päivitä sivu ja yritä uudelleen."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/delivery.py:0
msgid ""
"It seems that there is already a transaction for your order; you can't "
"change the delivery method anymore."
msgstr ""
"Näyttää siltä, että tilauksellesi on jo tehty maksutapahtuma; et voi enää "
"muuttaa toimitustapaa."

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_utils.js:0
msgid "Item(s) added to your cart"
msgstr "Tuotteet on lisätty ostoskoriin"

#. module: website_sale
#: model:ir.model,name:website_sale.model_account_move
msgid "Journal Entry"
msgstr "Päiväkirjan kirjaus"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_cabinets_kitchen
msgid "Kitchen Cabinets"
msgstr "Keittiön kaapit"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_drawers_kitchen
msgid "Kitchen Drawer Units"
msgstr "Keittiön vetolaatikot"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_digest_digest__kpi_website_sale_total_value
msgid "Kpi Website Sale Total Value"
msgstr "KPI Verkkosivun myyntien kokonaisarvo"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
msgid "Lamp"
msgstr "Lamppu"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_lamps
msgid "Lamps"
msgstr "Lamput"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Landscape (4/3)"
msgstr "Maisema (4/3)"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Last Month"
msgstr "Edellinen kuukausi"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_partner__last_website_so_id
#: model:ir.model.fields,field_description:website_sale.field_res_users__last_website_so_id
msgid "Last Online Sales Order"
msgstr "Viimeisin verkkokauppatilaus"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__write_uid
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__write_uid
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__write_uid
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__write_uid
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__write_uid
msgid "Last Updated by"
msgstr "Viimeksi päivittänyt"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__write_date
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__write_date
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__write_date
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__write_date
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__write_date
msgid "Last Updated on"
msgstr "Viimeksi päivitetty"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Last Week"
msgstr "Viime viikko"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Last Year"
msgstr "Edellinen vuosi"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_bins_laundry
msgid "Laundry Bins"
msgstr "Pyykkikorit"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Layout"
msgstr "Ulkoasu"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__product_ribbon__position__left
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Left"
msgstr "Vasen"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Let the customer enter a delivery address"
msgstr "Anna asiakkaan syöttää toimitusosoite"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Let the customer select a Mondial Relay shipping point"
msgstr "Anna asiakkaan valita Mondial Relay -lähetyspiste"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__add_to_cart_action__force_dialog
msgid "Let the user decide (dialog)"
msgstr "Anna käyttäjän päättää (dialogi)"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
msgid "Let's create your first product."
msgstr "Luodaan ensimmäinen tuotteesi."

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
msgid ""
"Let's now take a look at your eCommerce dashboard to get your eCommerce "
"website ready in no time."
msgstr ""
"Tutustutaan verkkokaupan kojelautaan, jotta saat verkkokauppasivustosi "
"valmiiksi hetkessä."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
msgid "Lightbulb sold separately"
msgstr "Hehkulamppu myydään erikseen"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__show_line_subtotals_tax_selection
#: model:ir.model.fields,field_description:website_sale.field_website__show_line_subtotals_tax_selection
msgid "Line Subtotals Tax Display"
msgstr "Linjan osamaksujen veronäyttö"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_share_buttons
msgid "LinkedIn"
msgstr "LinkedIn"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.add_grid_or_list_option
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "List"
msgstr "Lista"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Little Icons"
msgstr "Pienet ikonit"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/location_selector/location_selector_dialog/location_selector_dialog.js:0
msgid "List view"
msgstr "Listanäkymä"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/location_selector/location_selector_dialog/location_selector_dialog.js:0
msgid "Loading..."
msgstr "Lataa..."

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__ecommerce_access__logged_in
msgid "Logged in users"
msgstr "Logged in users"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Logos"
msgstr "Logot"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_boxes_luxury
msgid "Luxury Boxes"
msgstr "Ylelliset laatikot"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Magnifier on hover"
msgstr "Suurenna kuvaa, kun hiiren kursori viedään kohdalle"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Mail only sent to signed in customers with items available for sale in their"
" cart."
msgstr ""
"Posti lähetetään vain kirjautuneille asiakkaille, joiden ostoskorissa on "
"myytäviä tuotteita."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Main Image"
msgstr "Pääkuva"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Manage Promotions, coupons, loyalty cards, Gift cards & eWallet"
msgstr ""
"Hallitse kampanjoita, kuponkeja, kanta-asiakaskortteja, lahjakortteja ja "
"sähköistä lompakkoa"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Manage pricelists to apply specific prices per country, customer, products, "
"etc"
msgstr ""
"Hallitse hinnastoja, jotta voit soveltaa tiettyjä hintoja maittain, "
"asiakkaittain, tuotteittain jne"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__res_config_settings__account_on_checkout__mandatory
#: model:ir.model.fields.selection,name:website_sale.selection__website__account_on_checkout__mandatory
msgid "Mandatory (no guest checkout)"
msgstr ""
"Pakollinen (tunnistamaton käyttäjä ei voi suorittaa ostosta loppuun saakka)"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_cabinets_medicine
msgid "Medicine Cabinets"
msgstr "Lääkekaapit"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/location_selector/location_selector_dialog/location_selector_dialog.js:0
msgid "Map view"
msgstr "Karttanäkymä"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_spacing__medium
msgid "Medium"
msgstr "Media"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_mega_menu_menu_image_menu
msgid "Mega menu default image"
msgstr "Mega-valikon oletuskuva"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_has_error
msgid "Message Delivery error"
msgstr "Ongelma viestin toimituksessa"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_ids
msgid "Messages"
msgstr "Viestit"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Mondial Relay"
msgstr "Mondial Relay"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_delivery_mondialrelay
msgid "Mondial Relay Connector"
msgstr "Mondial Relay -liitin"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.accordion_more_information
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "More Information"
msgstr "Lisätietoja"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippets_options_web_editor
msgid "Move to first"
msgstr "Siirrä ensimmäiseksi"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippets_options_web_editor
msgid "Move to last"
msgstr "Siirrä viimeiseksi"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippets_options_web_editor
msgid "Move to next"
msgstr "Siirrä seuraavaksi"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippets_options_web_editor
msgid "Move to previous"
msgstr "Siirrä edelliseksi"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Multi Menus"
msgstr "Monivalikot"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_multimedia
msgid "Multimedia"
msgstr "Multimedia"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.header_cart_link
msgid "My Cart"
msgstr "Ostoskori"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__name
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__name
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__name
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Name"
msgstr "Nimi"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
msgid "Name (A-Z)"
msgstr "Nimi (A-Ö)"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order_line__name_short
msgid "Name Short"
msgstr "Lyhyt nimi"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/product_public_category.py:0
msgid "New"
msgstr "Uusi"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.product_product_action_add
msgid "New Product"
msgstr "Uusi tuote"

#. module: website_sale
#: model:product.ribbon,name:website_sale.new_ribbon
msgid "New!"
msgstr "Uusi!"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
msgid "Newest Arrivals"
msgstr "Viimeksi saapuneet"

#. module: website_sale
#: model:website.snippet.filter,name:website_sale.dynamic_filter_newest_products
msgid "Newest Products"
msgstr "Uusimmat tuotteet"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_image_viewer.xml:0
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_preview_data
msgid "Next"
msgstr "Seuraava"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_image_viewer.xml:0
msgid "Next (Right-Arrow)"
msgstr "Seuraava (Nuoli oikealle)"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_drawers_nightstand
msgid "Nightstand Drawers"
msgstr "Yöpöydän vetolaatikot"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
msgid "No"
msgstr "Ei"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_view_abandoned_tree
msgid "No abandoned carts found"
msgstr "Hylättyjä ostoskoreja ei löydy"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No product defined"
msgstr "Tuotetta ei ole asetettu"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No product defined in this category."
msgstr "Tässä kategoriassa ei ole määritelty tuotetta."

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.website_sale_visitor_product_action
msgid "No product views yet for this visitor"
msgstr "Tällä kävijällä ei ole vielä tuotenäkymiä"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/location_selector/location_selector_dialog/location_selector_dialog.js:0
msgid "No result"
msgstr "Ei tulosta"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No results"
msgstr "Ei tuloksia"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No results for \""
msgstr "Ei tuloksia haulle \""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No results found for '"
msgstr "Ei tuloksia. Kokeile jotain muuta hakusanaa. '"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
msgid ""
"No shipping method is available for your current order and shipping address."
" Please contact us for more information."
msgstr ""
"Nykyiselle tilauksellesi ja toimitusosoitteellesi ei ole toimitustapaa. Ota "
"meihin yhteyttä saadaksesi lisätietoja."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/sale_order.py:0
msgid "No shipping method is selected."
msgstr "Toimitustapaa ei ole valittu."

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_spacing__none
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "None"
msgstr "Ei mitään"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_pages_kanban_view
msgid "Not Published"
msgstr "Ei julkaistu"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/product/product.xml:0
msgid "Not available for sale"
msgstr "Ei myynnissä"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/sale_variant_mixin.js:0
msgid "Not available with %s"
msgstr "Ei saatavilla %s kanssa"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_crm_team__abandoned_carts_count
msgid "Number of Abandoned Carts"
msgstr "Hylättyjen ostoskorien määrä"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_needaction_counter
msgid "Number of Actions"
msgstr "Toimenpiteiden määrä"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_has_error_counter
msgid "Number of errors"
msgstr "Virheiden määrä"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__shop_ppr
msgid "Number of grid columns on the shop"
msgstr "Ruudukon sarakkeiden määrä kaupassa"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_template__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Toimenpiteitä vaativien viestien määrä"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_template__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Toimitusvirheellisten viestien määrä"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__shop_ppg
msgid "Number of products in the grid on the shop"
msgstr "Tuotteiden lukumäärä verkkokaupan ruudukossa"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Odoo Menu"
msgstr "Odoo-valikko"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_desks_office
msgid "Office Desks"
msgstr "Toimistopöydät"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
msgid "On wheels"
msgstr "Pyörillä"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
msgid "Once you click on <b>Save</b>, your product is updated."
msgstr "Kun napsautat <b>Tallenna</b>, tuotteesi päivitetään."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "One product might have different attributes (size, color, ...)"
msgstr "Yhdellä tuotteella voi olla erilaisia ominaisuuksia (koko, väri, ...)"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_report_sales
msgid "Online Sales"
msgstr "Verkkokauppamyynti"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.sale_report_action_dashboard
msgid "Online Sales Analysis"
msgstr "Verkkokauppamyynnin analyysi"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__only_services
msgid "Only Services"
msgstr "Vain palveluita"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/product_pricelist.py:0
msgid ""
"Only the company's websites are allowed.\n"
"Leave the Company field empty or select a website from that company."
msgstr ""
"Ainoastaan yrityksen verkkosivut ovat sallittuja.\n"
"Jätä Yritys-kenttä tyhjäksi tai valitse kyseisen yrityksen verkkosivusto."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/res_partner.py:0
msgid "Open Sale Orders"
msgstr "Avoimet myyntitilaukset"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.brand_promotion
msgid "Open Source eCommerce"
msgstr "Avoimen lähdekoodin verkkokauppa"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/location_selector/location/location.js:0
#: code:addons/website_sale/static/src/js/location_selector/map_container/map_container.js:0
msgid "Opening hours"
msgstr "Aukioloajat"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_image_view_kanban
msgid ""
"Optimization required! Reduce the image size or increase your compression "
"settings."
msgstr ""
"Optimointi vaaditaan! Pienennä kuvakokoa tai lisää pakkausasetuksiasi."

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__res_config_settings__account_on_checkout__optional
#: model:ir.model.fields.selection,name:website_sale.selection__website__account_on_checkout__optional
msgid "Optional"
msgstr "Vapaaehtoinen"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Options"
msgstr "Vaihtoehdot"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
msgid "Or scan me with your banking app."
msgstr "Tai skannaa pankkisovelluksella."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_unpaid
msgid "Order Date"
msgstr "Tilauksen pvm"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__website_order_line
msgid "Order Lines displayed on Website"
msgstr "Tilausrivit jotka näytetään verkkosivuilla"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "Order overview"
msgstr "Tilauksen yleiskatsaus"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.action_orders_ecommerce
#: model:ir.ui.menu,name:website_sale.menu_orders
#: model:ir.ui.menu,name:website_sale.menu_orders_orders
msgid "Orders"
msgstr "Tilaukset"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.sale_order_action_to_invoice
msgid "Orders To Invoice"
msgstr "Laskutettavat tilaukset"

#. module: website_sale
#: model:product.ribbon,name:website_sale.out_of_stock_ribbon
msgid "Out of stock"
msgstr "Loppu varastosta"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__parent_id
msgid "Parent Category"
msgstr "Ylempi ryhmä"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__parent_path
msgid "Parent Path"
msgstr "Ylempi polku"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__parents_and_self
msgid "Parents And Self"
msgstr "Ylätasot ja tämä tietue"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
msgid "Pay now"
msgstr "Maksa nyt"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
msgid "Payment"
msgstr "Maksu"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Payment Information"
msgstr "Maksutiedot"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_payment_methods
msgid "Payment Methods"
msgstr "Maksutavat"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_payment_providers
msgid "Payment Providers"
msgstr "Maksupalvelujen tarjoajat"

#. module: website_sale
#: model:ir.model,name:website_sale.model_payment_token
msgid "Payment Token"
msgstr "Maksutunniste"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_payment_tokens
msgid "Payment Tokens"
msgstr "Maksutunnisteet"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_payment_transactions
msgid "Payment Transactions"
msgstr "Maksutapahtumat"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
msgid "Pedal-based opening system"
msgstr "Polkimiin perustuva avausjärjestelmä"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Phone"
msgstr "Puhelin"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_form_editor.js:0
msgid "Phone Number"
msgstr "Puhelinnumero"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Pills"
msgstr "Pillerit"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_share_buttons
msgid "Pinterest"
msgstr "Pinterest"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_product_image_form
msgid "Please enter a valid Video URL."
msgstr "Ole hyvä ja anna toimiva videon URL."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "Please proceed your current cart."
msgstr "Ole hyvä ja jatka ostotapahtumaa nykyisellä ostoskorilla."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Pop-up on Click"
msgstr "Ponnahdusikkuna napsautettaessa"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Portrait (4/5)"
msgstr "Muotokuva (4/5)"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__position
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Position"
msgstr "Asema"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__website_sale_prevent_zero_price_sale
msgid "Prevent Sale of Zero Priced Product"
msgstr "Estä nollahintaisen tuotteen myynti"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_image_viewer.xml:0
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_preview_data
msgid "Previous"
msgstr "Edellinen"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_image_viewer.xml:0
msgid "Previous (Left-Arrow)"
msgstr "Edellinen (nuoli vasemmalle)"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
msgid "Price"
msgstr "Hinta"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
msgid "Price - High to Low"
msgstr "Hinta - korkeimmasta matalimpaan"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
msgid "Price - Low to High"
msgstr "Hinta - alhaisesta korkeimpaan"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Price Filter"
msgstr "Hintasuodatin"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__base_unit_price
#: model:ir.model.fields,field_description:website_sale.field_product_template__base_unit_price
msgid "Price Per Unit"
msgstr "Hinta per yksikkö"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__pricelist_ids
msgid "Price list available for this Ecommerce/Website"
msgstr "Tämän verkkokaupan/verkkosivuston hinnasto on saatavilla"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_pricelist
msgid "Pricelist"
msgstr "Hinnasto"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_pricelist_item
msgid "Pricelist Rule"
msgstr "Hinnastosääntö"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_catalog_pricelists
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Pricelists"
msgstr "Hinnastot"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Prices displayed on your eCommerce"
msgstr "Verkkokaupassa näkyvät hinnat"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Print"
msgstr "Tulosta"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/combo_configurator_dialog/combo_configurator_dialog.xml:0
#: code:addons/website_sale/static/src/js/product_configurator_dialog/product_configurator_dialog.xml:0
msgid "Proceed to Checkout"
msgstr "Lisää ostoskoriin ja siirry maksamaan"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_unpaid_orders_ecommerce
#: model_terms:ir.actions.act_window,help:website_sale.action_view_unpaid_quotation_tree
msgid "Process the order once the payment is received."
msgstr "Käsittele tilaus, kun maksu on vastaanotettu."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Prod. Desc."
msgstr "Tuotekuvaus."

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#: model:ir.model,name:website_sale.model_product_template
#: model:ir.model.fields,field_description:website_sale.field_website_track__product_id
#: model_terms:ir.ui.view,arch_db:website_sale.products
#: model_terms:ir.ui.view,arch_db:website_sale.s_add_to_cart_options
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_page_view_search
msgid "Product"
msgstr "Tuote"

#. module: website_sale
#: model:ir.actions.server,name:website_sale.dynamic_snippet_accessories_action
msgid "Product Accessories"
msgstr "Tuotteen lisävarusteet"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_attribute
msgid "Product Attribute"
msgstr "Tuotteen ominaisuus"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.shop_product_carousel
msgid "Product Carousel"
msgstr "Tuote karuselli"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Product Category"
msgstr "Tuotekategoria"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Product Comparison Tool"
msgstr "Tuotteiden vertailutyökalu"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_document
msgid "Product Document"
msgstr "Tuoteasiakirja"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_image
msgid "Product Image"
msgstr "Tuotekuva"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_image_view_kanban
#: model_terms:ir.ui.view,arch_db:website_sale.view_product_image_form
msgid "Product Images"
msgstr "Tuotekuvat"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "Product Name"
msgstr "Tuotteen nimi"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "Product Names"
msgstr "Tuotteen nimet"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Product Page"
msgstr "Tuotesivu"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_website_sale_website_form
msgid "Product Page Extra Fields"
msgstr "Tuotesivun lisäkentät"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__product_page_grid_columns
msgid "Product Page Grid Columns"
msgstr "Tuotesivun ruudukon sarakkeet"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__product_page_image_layout
msgid "Product Page Image Layout"
msgstr "Tuotesivun kuvan asettelu"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__product_page_image_spacing
msgid "Product Page Image Spacing"
msgstr "Tuotesivun kuvaväli"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__product_page_image_width
msgid "Product Page Image Width"
msgstr "Tuotesivun kuvan leveys"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.action_product_pages_list
msgid "Product Pages"
msgstr "Tuotesivut"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_public_category_tree_view
msgid "Product Public Categories"
msgstr "Julkiset tuotekategoriat"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Product Reference Price"
msgstr "Tuotteen viitehinta"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_ribbon_view_tree
msgid "Product Ribbon"
msgstr "Tuotteen nauha"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.product_ribbon_action
#: model:ir.ui.menu,name:website_sale.product_catalog_product_ribbons
msgid "Product Ribbons"
msgstr "Tuotteiden nauhat"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_tag
msgid "Product Tag"
msgstr "Tuotteen tunniste"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.product_catalog_product_tags
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Product Tags"
msgstr "Tuotteen tunnisteet"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Product Tags Filter"
msgstr "Tuotetunnisteiden suodatin"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__product_tmpl_id
msgid "Product Template"
msgstr "Tuotemalli"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_template_attribute_line
msgid "Product Template Attribute Line"
msgstr "Tuotemallin ominaisuusrivi"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_template_attribute_value
msgid "Product Template Attribute Value"
msgstr "Tuotemallin ominaisuuden arvo"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__product_tmpl_ids
msgid "Product Tmpl"
msgstr "Tuotemalli"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_product
#: model:ir.model.fields,field_description:website_sale.field_product_image__product_variant_id
msgid "Product Variant"
msgstr "Tuotevariaatio"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Product Variants"
msgstr "Tuotevariaatiot"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_visitor__visitor_product_count
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_view_form
msgid "Product Views"
msgstr "Tuotteen katselukerrat"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.website_sale_visitor_product_action
msgid "Product Views History"
msgstr "Tuotteen katselukertojen historia"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
msgid "Product not found"
msgstr "Tuotetta ei löydy"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_ribbon
msgid "Product ribbon"
msgstr "Tuotenauha"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.product_template_action_website
#: model:ir.ui.menu,name:website_sale.menu_catalog
#: model:ir.ui.menu,name:website_sale.menu_catalog_products
#: model:ir.ui.menu,name:website_sale.menu_product_pages
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_sale.products_breadcrumb
#: model_terms:ir.ui.view,arch_db:website_sale.snippets
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_page_view_search
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_view_form
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_view_tree
msgid "Products"
msgstr "Tuotteet"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Products List"
msgstr "Tuoteluettelo"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Products Page"
msgstr "Tuotteet Sivu"

#. module: website_sale
#: model:ir.actions.server,name:website_sale.dynamic_snippet_recently_sold_with_action
msgid "Products Recently Sold With"
msgstr "Tuotteet, jotka on myyty hiljattain toisen kanssa"

#. module: website_sale
#: model:website.snippet.filter,name:website_sale.dynamic_filter_cross_selling_recently_sold_with
msgid "Products Recently Sold With Product"
msgstr "Tuotteet, jotka on myyty hiljattain toisen tuotteen kanssa"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_visitor__product_count
msgid "Products Views"
msgstr "Tuotteiden katselukerrat"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Promo Code"
msgstr "Kampanjakoodi"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/product_image.py:0
msgid ""
"Provided video URL for '%s' is not valid. Please enter a valid video URL."
msgstr ""
"Annettu videon URL-osoite '%s' ei ole voimassa. Anna voimassa oleva videon "
"URL-osoite."

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_document__shown_on_product_page
msgid "Publish on website"
msgstr "Julkaise verkkosivustolla"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_pages_kanban_view
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_search_view_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_delivery_carrier_search
msgid "Published"
msgstr "Julkaistu"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Push down"
msgstr "Työnnä alas"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Push to bottom"
msgstr "Työnnä alimmaksi"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Push to top"
msgstr "Työnnä ylimmäksi"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Push up"
msgstr "Työnnä ylös"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
msgid "Quantity"
msgstr "Määrä"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Radio"
msgstr "Radio"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Rating"
msgstr "Arvostelu"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_avg_text
msgid "Rating Avg Text"
msgstr "Kekismääräisen luokituksen teksti"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "Arvion viimeisin palaute"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_last_image
msgid "Rating Last Image"
msgstr "Arvion viimeisin kuva"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_last_value
msgid "Rating Last Value"
msgstr "Arvion viimeisin arvo"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_percentage_satisfaction
msgid "Rating Satisfaction"
msgstr "Arvion tyytyväisyys"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_last_text
msgid "Rating Text"
msgstr "Luokitusteksti"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_count
msgid "Rating count"
msgstr "Arvioiden määrä"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_ids
msgid "Ratings"
msgstr "Arviointi"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
msgid "Re-Order"
msgstr "Tilaa uudelleen"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
#: model_terms:ir.ui.view,arch_db:website_sale.snippets_options_web_editor
msgid "Re-order"
msgstr "Tilaa uudelleen"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__website_sale_enabled_portal_reorder_button
#: model:ir.model.fields,field_description:website_sale.field_website__enabled_portal_reorder_button
msgid "Re-order From Portal"
msgstr "Tilaa uudelleen portaalista"

#. module: website_sale
#: model:ir.actions.server,name:website_sale.dynamic_snippet_latest_sold_products_action
#: model:website.snippet.filter,name:website_sale.dynamic_filter_latest_sold_products
msgid "Recently Sold Products"
msgstr "Äskettäin myydyt tuotteet"

#. module: website_sale
#: model:ir.actions.server,name:website_sale.dynamic_snippet_latest_viewed_products_action
#: model:website.snippet.filter,name:website_sale.dynamic_filter_latest_viewed_products
msgid "Recently Viewed Products"
msgstr "Viimeksi katsotut tuotteet"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_furnitures_recliners
msgid "Recliners"
msgstr "Lepotuolit"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Recovery Email Sent"
msgstr "Palautusposti lähetetty"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Recovery Email to Send"
msgstr "Lähetettävä palautusposti"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
msgid "Reinforced for heavy loads"
msgstr "Vahvistettu raskaita kuormia varten"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_services_relocation_and_moving
msgid "Relocation and Moving"
msgstr "Siirtyminen ja muuttaminen"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.snippets_options_web_editor
msgid "Remove"
msgstr "Poista"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Remove all"
msgstr "Poista kaikki"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
msgid "Remove from cart"
msgstr "Poista ostoskorista"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.product_quantity
msgid "Remove one"
msgstr "Poista yksi"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_services_repair_and_maintenance
msgid "Repair and Maintenance"
msgstr "Korjaus ja huolto"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Replace"
msgstr "Korvaa"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Reset Categories"
msgstr "Nollaa kategoriat"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_delivery_carrier__website_id
#: model:ir.model.fields,help:website_sale.field_product_product__website_id
#: model:ir.model.fields,help:website_sale.field_product_public_category__website_id
#: model:ir.model.fields,help:website_sale.field_product_tag__website_id
#: model:ir.model.fields,help:website_sale.field_product_template__website_id
msgid "Restrict to a specific website."
msgstr "Rajoita tiettyyn verkkosivustoon."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/sale_order.py:0
msgid "Resume Order"
msgstr "Jatka tilausta"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
msgid "Review Order"
msgstr "Tarkista tilaus"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_ribbon_id
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_ribbon_id
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Ribbon"
msgstr "Nauha"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale.editor.js:0
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__name
msgid "Ribbon Name"
msgstr "Nauhan nimi"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__product_ribbon__position__right
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Right"
msgstr "Oikea"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_boxes_rustic
msgid "Rustic Boxes"
msgstr "Maalaislaatikot"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__is_seo_optimized
#: model:ir.model.fields,field_description:website_sale.field_product_template__is_seo_optimized
msgid "SEO optimized"
msgstr "Hakukoneoptimoitu"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Tekstiviestin toimitusvirhe"

#. module: website_sale
#: model:product.ribbon,name:website_sale.sale_ribbon
msgid "Sale"
msgstr "Ale"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_graph_website
msgid "Sale Analysis"
msgstr "Kassa-analyysi kassoittain"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.sale_report_action_carts
#: model_terms:ir.ui.view,arch_db:website_sale.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Sales"
msgstr "Myynti"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_pivot_website
msgid "Sales Analysis"
msgstr "Myyntianalyysi"

#. module: website_sale
#: model:ir.model,name:website_sale.model_sale_report
msgid "Sales Analysis Report"
msgstr "Myyntianalyysi"

#. module: website_sale
#: model:ir.model,name:website_sale.model_sale_order
msgid "Sales Order"
msgstr "Myyntitilaus"

#. module: website_sale
#: model:ir.model,name:website_sale.model_sale_order_line
msgid "Sales Order Line"
msgstr "Myyntitilausrivi"

#. module: website_sale
#: model:ir.model,name:website_sale.model_crm_team
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__salesteam_id
#: model:ir.model.fields,field_description:website_sale.field_website__salesteam_id
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Sales Team"
msgstr "Myyntitiimi"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__salesperson_id
#: model:ir.model.fields,field_description:website_sale.field_website__salesperson_id
msgid "Salesperson"
msgstr "Myyjä"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.billing_address_row
msgid "Same as delivery address"
msgstr "Sama kuin toimitusosoite"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_add_to_cart
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_banner
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_borderless_1
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_borderless_2
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_card_group
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_centered
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_horizontal_card
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_horizontal_card_2
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_mini_image
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_mini_name
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_mini_price
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_view_detail
msgid "Sample"
msgstr "Esimerkki"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Save address"
msgstr "Tallenna osoite"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Search Abandoned Sales Orders"
msgstr "Hae hylätyistä myyntitilauksista"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Search Bar"
msgstr "Hakupalkki"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Select"
msgstr "Valitse"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
msgid ""
"Select <b>New Product</b> to create it and manage its properties to boost "
"your sales."
msgstr ""
"Valitse <b>Uusi</b> tuote luodaksesi sen ja hallinnoidaksesi tuotteen "
"ominaisuuksia myynnin lisäämiseksi."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Select Quantity"
msgstr "Valitse määrä"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.delivery_method
msgid "Select a location"
msgstr "Valitse sijainti"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_pricelist_form_view
msgid "Select a website"
msgstr "Valitse verkkosivu"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_pricelist__selectable
msgid "Selectable"
msgstr "Valittavissa"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_order_view_form_cart_recovery
msgid "Send a Recovery Email"
msgstr "Lähetä palautussähköposti"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Send after"
msgstr "Lähetä jälkeen"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_order_view_form_cart_recovery
msgid "Send by Email"
msgstr "Lähetä sähköpostilla"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__send_abandoned_cart_email
msgid "Send email to customers who abandoned their cart."
msgstr "Lähetä sähköpostia asiakkaille, jotka ovat hylänneet ostoskorinsa."

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__seo_name
#: model:ir.model.fields,field_description:website_sale.field_product_template__seo_name
msgid "Seo name"
msgstr "SEO-nimi"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__sequence
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__sequence
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__sequence
msgid "Sequence"
msgstr "Järjestys"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_services
msgid "Services"
msgstr "Palvelut"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_share_buttons
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Share"
msgstr "Jaa"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__group_delivery_invoice_address
msgid "Shipping Address"
msgstr "Toimitusosoite"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Shipping Costs"
msgstr "Toimituskulut"

#. module: website_sale
#: model:ir.model,name:website_sale.model_delivery_carrier
msgid "Shipping Methods"
msgstr "Toimitustavat"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Shiprocket"
msgstr "Shiprocket"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Shiprocket Shipping Methods"
msgstr "Shiprocketin toimitustavat"

#. module: website_sale
#: model:website.menu,name:website_sale.menu_shop
msgid "Shop"
msgstr "Kauppa"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Shop - Checkout"
msgstr "Kauppa - Kassa"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Shop - Checkout Process"
msgstr "Kauppa - Kassaprosessi"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Shop - Confirmed"
msgstr "Kauppa - Vahvistettu"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Shop - Products"
msgstr "Kauppa - Tuotteet"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Shop - Select Payment Method"
msgstr "Kauppa - Valitse maksutapa"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__shop_default_sort
msgid "Shop Default Sort"
msgstr "Kauppa Oletuslajittelu"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Shop, products, cart and wishlist visibility"
msgstr "Kaupan, tuotteiden, ostoskorin ja toivelistan näkyvyys"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_add_to_cart
msgid "Shopping cart"
msgstr "Ostoskori"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Show B2B Fields"
msgstr "Näytä B2B-kentät"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Show Empty"
msgstr "Näytä tyhjä"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_document_search
msgid "Show on Ecommerce"
msgstr "Näytä verkkokaupassa"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "Show variants"
msgstr "Näytä variantit"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Show/hide shopping cart"
msgstr "Näytä/piilota ostoskori"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
msgid "Sign In"
msgstr "Kirjaudu sisään"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Sign Up"
msgstr "Liity"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Sign in"
msgstr "Kirjaudu sisään"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Sign in/up at checkout"
msgstr "Kirjautuminen sisään/kirjautuminen kassalla"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
msgid "Sit comfortably"
msgstr "Istu mukavasti"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Size"
msgstr "Koko"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_size_x
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_size_x
msgid "Size X"
msgstr "Koko X"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_size_y
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_size_y
msgid "Size Y"
msgstr "Koko Y"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_spacing__small
msgid "Small"
msgstr "Pieni"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_furnitures_sofas
msgid "Sofas"
msgstr "Sohvat"

#. module: website_sale
#: model:product.ribbon,name:website_sale.sold_out_ribbon
msgid "Sold out"
msgstr "Ei saatavilla"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
msgid "Some required fields are empty."
msgstr "Täytä kaikki vaaditut kentät."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
msgid "Sorry, we are unable to ship your order."
msgstr "Valitettavasti emme voi lähettää tilaustasi."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Sort by"
msgstr "Järjestä"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_normal_website_form_view
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_view_form_easy_inherit_website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_only_website_form_view
msgid "Specify unit"
msgstr "Määritä yksikkö"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_mega_menu_menus_logos
msgid "Spring collection has arrived!"
msgstr "Kevätmallisto on saapunut!"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_boxes_stackable
msgid "Stackable Boxes"
msgstr "Pinottavat laatikot"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_desks_standing
msgid "Standing Desks"
msgstr "Seisontatyöpöydät"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "State / Province"
msgstr "Osavaltio / provinssi"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Status"
msgstr "Tila"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__add_to_cart_action__stay
msgid "Stay on Product Page"
msgstr "Pysy tuotesivulla"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_cabinets_storage
msgid "Storage Cabinets"
msgstr "Säilytyskaapit"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Street and Number"
msgstr "Katu ja numero"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Style"
msgstr "Tyyli"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.total
msgid "Subtotal"
msgstr "Välisumma"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__alternative_product_ids
#: model:ir.model.fields,help:website_sale.field_product_template__alternative_product_ids
msgid ""
"Suggest alternatives to your customer (upsell strategy). Those products show"
" up on the product page."
msgstr ""
"Ehdota asiakkaalle vaihtoehtoja (upsell-strategia). Nämä tuotteet näkyvät "
"tuotesivulla."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Suggested Accessories"
msgstr "Ehdotetut lisävarusteet"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.suggested_products_list
msgid "Suggested accessories"
msgstr "Ehdotetut lisävarusteet"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "Suggested accessories in the eCommerce cart"
msgstr "Ehdotetut lisävarusteet verkkokaupan ostoskorissa"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "Tags"
msgstr "Tunnisteet"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__show_line_subtotals_tax_selection__tax_excluded
#: model_terms:ir.ui.view,arch_db:website_sale.tax_indication
msgid "Tax Excluded"
msgstr "Veroton"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__show_line_subtotals_tax_selection__tax_included
#: model_terms:ir.ui.view,arch_db:website_sale.tax_indication
msgid "Tax Included"
msgstr "Verollinen"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Tax Indication"
msgstr "Veroindikaattori"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_sale_order__amount_delivery
msgid "Tax included or excluded depending on the website configuration."
msgstr "Vero sisältyy tai ei sisälly verkkosivuston kokoonpanosta riippuen."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.total
msgid "Taxes"
msgstr "Verot"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Text"
msgstr "Teksti"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__text_color
msgid "Text Color"
msgstr "Tekstin väri"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__prevent_zero_price_sale_text
msgid "Text to show instead of price"
msgstr "Hinnan sijasta näytettävä teksti"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Thank you for your order."
msgstr "Kiitos tilauksestasi."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.brand_promotion
msgid "The #1"
msgstr "Numero #1"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/payment.py:0
msgid "The access token is invalid."
msgstr "Pääsytunniste on virheellinen."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/payment.py:0
msgid "The cart has been updated. Please refresh the page."
msgstr "Ostoskori on päivitetty. Päivitä selain."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/sale_order.py:0
msgid ""
"The company of the website you are trying to sell from (%(website_company)s)"
" is different than the one you want to use (%(company)s)"
msgstr ""
"Sivuston yritys, jolta yrität myydä (%(website_company)s), on eri kuin "
"yritys, jota haluat käyttää (%(company)s)"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_delivery_carrier__website_url
#: model:ir.model.fields,help:website_sale.field_product_product__website_url
#: model:ir.model.fields,help:website_sale.field_product_template__website_url
msgid "The full URL to access the document through the website."
msgstr "Dokumentin URL-osoite verkkosivustolla."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/sale_order.py:0
msgid ""
"The given combination does not exist therefore it cannot be added to cart."
msgstr "Annettua yhdistelmää ei ole, joten sitä ei voi lisätä ostoskoriin."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/sale_order.py:0
msgid "The given product does not exist therefore it cannot be added to cart."
msgstr "Tuotetta ei ole olemassa, joten sitä voida lisätä ostoskärryyn."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/sale_order.py:0
msgid ""
"The given product does not have a price therefore it cannot be added to "
"cart."
msgstr "Tuotteella ei ole hintaa, joten sitä ei voi lisätä ostoskoriin."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"The mode selected here applies as invoicing policy of any new product "
"created but not of products already existing."
msgstr ""
"Tässä valittua tilaa sovelletaan laskutuskäytäntönä kaikkiin uusiin "
"tuotteisiin, mutta ei jo olemassa oleviin tuotteisiin."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/payment.py:0
msgid "The order has been cancelled."
msgstr "Tilauksesi on peruttu."

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__public_categ_ids
#: model:ir.model.fields,help:website_sale.field_product_template__public_categ_ids
msgid ""
"The product will be available in each mentioned eCommerce category. Go to "
"Shop > Edit Click on the page and enable 'Categories' to view all eCommerce "
"categories."
msgstr ""
"Tuote on saatavilla jokaisessa mainitussa verkkokauppaluokassa. Siirry "
"kohtaan Kauppa > Muokkaa Klikkaa sivua ja ota 'Kategoriat' käyttöön "
"nähdäksesi kaikki verkkokauppakategoriat."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_mega_menu_images_subtitles
#: model_terms:ir.ui.view,arch_db:website_sale.s_mega_menu_little_icons
msgid "The team"
msgstr "Tiimi"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_view_abandoned_tree
msgid "The time to mark a cart as abandoned can be changed in the settings."
msgstr ""
"Asetuksissa voidaan muuttaa aikaa, jonka kuluessa ostoskori merkitään "
"hylätyksi."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/product_product.py:0
msgid ""
"The value of Base Unit Count must be greater than 0. Use 0 to hide the price"
" per unit on this product."
msgstr ""
"Perusyksikkömäärän arvon on oltava suurempi kuin 0. Käytä arvoa 0 "
"piilottaaksesi tämän tuotteen yksikköhinnan."

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_orders_ecommerce
msgid "There is no confirmed order from the website"
msgstr "Sivustolta ei ole vahvistettua tilausta"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_unpaid_orders_ecommerce
#: model_terms:ir.actions.act_window,help:website_sale.action_view_unpaid_quotation_tree
msgid "There is no unpaid order from the website yet"
msgstr "Sivustolta ei ole vielä maksamattomia tilauksia"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/location_selector/map_container/map_container.js:0
msgid "There was an error loading the map"
msgstr "Kartan lataamisessa tapahtui virhe"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "This combination does not exist."
msgstr "Tätä yhdistelmää ei ole olemassa."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.accordion_more_information
msgid "This content will be shared across all product pages."
msgstr "Tämä sisältö jaetaan kaikilla tuotesivuilla."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "This is your current cart."
msgstr "Tämä on nykyinen ostoskorisi."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/res_partner.py:0
msgid ""
"This partner has an open cart. Please note that the pricelist will not be "
"updated on that cart. Also, the cart might not be visible for the customer "
"until you update the pricelist of that cart."
msgstr ""
"Tällä kumppanilla on avoin ostoskori. Huomaa, että hinnastoa ei päivitetä "
"kyseiseen ostoskoriin. Ostoskori ei myöskään välttämättä näy asiakkaalle "
"ennen kuin päivität kyseisen ostoskorin hinnaston."

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/snippets/s_add_to_cart/000.js:0
msgid "This product does not exist therefore it cannot be added to cart."
msgstr "Tuotetta ei ole olemassa. Sitä ei voi lisätä ostoskoriin."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "This product has no valid combination."
msgstr "Tällä tuotteella ei ole kelvollista yhdistelmää."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "This product is no longer available."
msgstr "Tuotetta ei ole enää saatavilla."

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_reorder.js:0
msgid "This product is not available for purchase."
msgstr "Tämä tuote ei ole ostettavissa."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_item
msgid "This product is unpublished."
msgstr "Tuotetta ei ole julkaistu."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.coupon_form
msgid "This promo code is not available."
msgstr "Tämä tarjouskoodi ei ole käytettävissä."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_preview_data
msgid "Three-Seat Sofa"
msgstr "Kolmen istuttava sohva"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Thumbnails"
msgstr "Pienoiskuvat"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"To send invitations in B2B mode, open a contact or select several ones in "
"list view and click on 'Portal Access Management' option in the dropdown "
"menu *Action*."
msgstr ""
"Lähettääksesi kutsuja B2B-tilassa, avaa kontakti tai valitse useita "
"listanäkymässä, ja klikkaa \"Portaalin käyttöoikeuksien hallinta\" "
"\"Toiminto\"-pudotusvalikosta."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Top"
msgstr "Ylin"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Top Bar"
msgstr "Yläpalkki"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
msgid "Total"
msgstr "Yhteensä"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_website_visitor__product_count
msgid "Total number of product viewed"
msgstr "Katsottujen tuotteiden kokonaismäärä"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_website_visitor__visitor_product_count
msgid "Total number of views on products"
msgstr "Tuotteiden katselukertojen kokonaismäärä"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/combo_configurator_dialog/combo_configurator_dialog.js:0
#: code:addons/website_sale/static/src/js/product_list/product_list.js:0
msgid "Total: %s"
msgstr "Loppusumma: %s"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_lamps_touch
msgid "Touch Lamps"
msgstr "Kosketuslamput"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_bins_toy
msgid "Toy Bins"
msgstr "Lelulaatikot"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_website_snippet_filter__product_cross_selling
msgid ""
"True only for product filters that require a product_id because they relate "
"to cross selling"
msgstr ""
"Totta vain tuotesuodattimille, jotka vaativat product_id:n, koska ne "
"liittyvät ristiinmyyntiin"

#. module: website_sale
#: model:res.groups,name:website_sale.group_show_uom_price
msgid "UOM Price Display for eCommerce"
msgstr "Mittayksikön hintanäyttö verkkokaupassa"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "UPS"
msgstr "UPS"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_image__video_url
msgid "URL of a video for showcasing your product."
msgstr "Tuotettasi esittelevän videon URL-osoite."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "USPS"
msgstr "USPS"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "USPS Shipping Methods"
msgstr "USPS Toimitusmenetelmät"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_drawers_underbed
msgid "Under-bed Drawers"
msgstr "Sängyn alla olevat laatikot"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_base_unit
msgid "Unit of Measure for price per unit on eCommerce products."
msgstr "Verkkokaupan tuotteiden yksikköhinnan mittayksikkö."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
msgid "Unpaid"
msgstr "Maksamaton"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.action_unpaid_orders_ecommerce
#: model:ir.actions.act_window,name:website_sale.action_view_unpaid_quotation_tree
#: model:ir.ui.menu,name:website_sale.menu_orders_unpaid_orders
msgid "Unpaid Orders"
msgstr "Maksamattomat tilaukset"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_view_form_normalized
#: model_terms:ir.ui.view,arch_db:website_sale.products_item
msgid "Unpublished"
msgstr "Julkaisematon"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
msgid "Upload a file from your local library."
msgstr "Lataa tiedosto paikallisesta kirjastostasi."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Use Google Places API to validate addresses entered by your visitors"
msgstr ""
"Käytä Google Places API:ta kävijöiden syöttämien osoitteiden varmistamieen"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
msgid "VAT"
msgstr "ALV"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_add_to_cart_options
msgid "Variant"
msgstr "Muunnelma"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__variant_ribbon_id
msgid "Variant Ribbon"
msgstr "Varianttinauha"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Variants"
msgstr "Variaatiot"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Vertical (2/3)"
msgstr "Pystysuora (2/3)"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__video_url
#: model_terms:ir.ui.view,arch_db:website_sale.view_product_image_form
msgid "Video URL"
msgstr "Videon URL"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_banner
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_centered
msgid "View Product"
msgstr "Näytä tuote"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/notification/add_to_cart_notification/add_to_cart_notification.xml:0
msgid "View cart"
msgstr "Näytä ostoskori"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_view_detail
msgid "View product"
msgstr "Näytä tuote"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_image_viewer.xml:0
msgid "Viewer"
msgstr "Lukija"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_boxes_vintage
msgid "Vintage Boxes"
msgstr "Vintage laatikot"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_multimedia_virtual_design
msgid "Virtual Design Tools"
msgstr "Virtuaaliset suunnittelutyökalut"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_attribute__visibility
msgid "Visibility"
msgstr "Näkyvyys"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__product_attribute__visibility__visible
msgid "Visible"
msgstr "Näytetään"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_delivery_carrier__website_published
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_published
msgid "Visible on current website"
msgstr "Näkyy nykysellä verkkosivulla"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_tag__visible_on_ecommerce
msgid "Visible on eCommerce"
msgstr "Näkyvissä verkkokaupassa"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_track
msgid "Visited Pages"
msgstr "Vieraillut sivut"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_visitor__product_ids
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_view_kanban
msgid "Visited Products"
msgstr "Vieraillut tuotteet"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_page_view_graph
msgid "Visitor Product Views"
msgstr "Vierailijan tuotteen katselukerrat"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_page_view_tree
msgid "Visitor Product Views History"
msgstr "Vierailijan tuotteen katseluhistoria"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_furnitures_wardrobes
msgid "Wardrobes"
msgstr "Vaatekaapit"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_utils.js:0
#: model:ir.model.fields,field_description:website_sale.field_sale_order__shop_warning
#: model:ir.model.fields,field_description:website_sale.field_sale_order_line__shop_warning
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.checkout_layout
msgid "Warning"
msgstr "Varoitus"

#. module: website_sale
#: model:product.template,name:website_sale.product_product_1_product_template
msgid "Warranty"
msgstr "Takuu"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_preview_data
msgid "Warranty (2 year)"
msgstr "Takuu (2 vuotta)"

#. module: website_sale
#: model:product.template,description_sale:website_sale.product_product_1_product_template
msgid ""
"Warranty, issued to the purchaser of an article by its manufacturer, "
"promising to repair or replace it if necessary within a specified period of "
"time."
msgstr ""
"Takuu, jonka valmistaja myöntää tavaran ostajalle ja lupaa korjata tai "
"vaihtaa sen tarvittaessa tietyn ajan kuluessa."

#. module: website_sale
#: model:ir.model,name:website_sale.model_website
#: model:ir.model.fields,field_description:website_sale.field_account_bank_statement_line__website_id
#: model:ir.model.fields,field_description:website_sale.field_account_move__website_id
#: model:ir.model.fields,field_description:website_sale.field_delivery_carrier__website_id
#: model:ir.model.fields,field_description:website_sale.field_product_pricelist__website_id
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_id
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_id
#: model:ir.model.fields,field_description:website_sale.field_product_tag__website_id
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_id
#: model:ir.model.fields,field_description:website_sale.field_sale_order__website_id
#: model:ir.model.fields,field_description:website_sale.field_sale_report__website_id
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__website_id
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Website"
msgstr "Verkkosivu"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_view_form_normalized
msgid "Website Category"
msgstr "Verkkosivun luokka"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_menu
msgid "Website Menu"
msgstr "Verkkosivuston valikko"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_message_ids
msgid "Website Messages"
msgstr "Verkkosivun ilmoitukset"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_public_category
#: model:ir.model.fields,field_description:website_sale.field_product_product__public_categ_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template__public_categ_ids
msgid "Website Product Category"
msgstr "Verkkosivun Tuote Kategoria"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_public_category_form_view
msgid "Website Public Categories"
msgstr "Verkkosivuston kategoriat"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_sequence
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_sequence
msgid "Website Sequence"
msgstr "Verkkokaupan järjestys"

#. module: website_sale
#: model:ir.actions.act_url,name:website_sale.action_open_website
msgid "Website Shop"
msgstr "Verkkokauppa"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_snippet_filter
msgid "Website Snippet Filter"
msgstr "Verkkosivuston pätkäsuodatin"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_delivery_carrier__website_url
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_url
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_url
msgid "Website URL"
msgstr "Verkkosivuston osoite"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_visitor
msgid "Website Visitor"
msgstr "Verkkosivun vierailija"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_template__website_message_ids
msgid "Website communication history"
msgstr "Verkkosivun viestihistoria"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_meta_description
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_meta_description
msgid "Website meta description"
msgstr "Verkkosivuston metakuvaus"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_meta_keywords
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_meta_keywords
msgid "Website meta keywords"
msgstr "Verkkosivuston meta-avainsanat"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_meta_title
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_meta_title
msgid "Website meta title"
msgstr "Verkkosivuston metaotsikko"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_meta_og_img
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_meta_og_img
msgid "Website opengraph image"
msgstr "Verkkosivun opengraph kuva"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_account_bank_statement_line__website_id
#: model:ir.model.fields,help:website_sale.field_account_move__website_id
msgid "Website through which this invoice was created for eCommerce orders."
msgstr ""
"Verkkosivusto, jonka kautta tämä lasku on luotu verkkokauppatilauksia "
"varten."

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_sale_order__website_id
msgid "Website through which this order was placed for eCommerce orders."
msgstr ""
"Verkkokaupan tilausten osalta verkkosivusto, jonka kautta tilaus tehtiin."

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_crm_team__website_ids
msgid "Websites"
msgstr "Verkkosivut"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "What should be done on \"Add to Cart\"?"
msgstr "Mitä pitäisi tehdä \"Lisää ostoskoriin\" -kohdassa?"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_share_buttons
msgid "WhatsApp"
msgstr "WhatsApp"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_tag__visible_on_ecommerce
msgid "Whether the tag is displayed on the eCommerce."
msgstr "Näytetäänkö tunniste verkkokaupassa."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
msgid "Whiteboard"
msgstr "Valkotaulu"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Wide (16/9)"
msgstr "Leveä (16/9)"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Wider (21/9)"
msgstr "Leveämpi (21/9)"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_website_sale_wishlist
msgid "Wishlists"
msgstr "Toivelistat"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
msgid "With three feet"
msgstr "Kolmella jalalla"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
msgid "Yes"
msgstr "Kyllä"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid ""
"You are editing your <b>delivery and billing</b> addresses\n"
"                                        at the same time!<br/>\n"
"                                        If you want to modify your billing address, create a"
msgstr ""
"Muokkaat <b>toimitus- ja laskutusosoitteita</b> \n"
"                                        samaan aikaan!<br/>\n"
"                                        Jos haluat muuttaa laskutusosoitettasi, luo laskutusosoite"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
msgid "You can't use a video as the product's main image."
msgstr "Et voi käyttää videota tuotteen pääkuvana."

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.sale_report_action_carts
#: model_terms:ir.actions.act_window,help:website_sale.sale_report_action_dashboard
msgid "You don't have any order from the website"
msgstr "Sinulla ei ole tilausta verkkosivustolta"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.sale_order_action_to_invoice
msgid "You don't have any order to invoice from the website"
msgstr "Sinulla ei ole laskutustilausta verkkosivustolta"

#. module: website_sale
#: model:mail.template,subject:website_sale.mail_template_sale_cart_recovery
msgid "You left items in your cart!"
msgstr "Jätit tuotteita ostoskoriin!"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_view_abandoned_tree
msgid ""
"You'll find here all the carts abandoned by your visitors.\n"
"                If they completed their address, you should send them a recovery email!"
msgstr ""
"Täältä löydät kaikki kävijöiden hylkäämät ostoskorit.\n"
"                Jos he ovat täyttäneet osoitteensa, lähetä heille palautussähköposti!"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.alternative_products
msgid ""
"Your Dynamic Snippet will be displayed here...\n"
"                                This message is displayed because youy did not provide both a filter and a template to use."
msgstr ""
"Dynaaminen pätkäsi näytetään tässä...\n"
"                                Tämä viesti näytetään, koska et ole antanut sekä suodatinta että mallia käytettäväksi."

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_form_editor.js:0
msgid "Your Email"
msgstr "Sähköpostisi"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_form_editor.js:0
msgid "Your Name"
msgstr "Nimesi"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Your address"
msgstr "Osoitteesi"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.checkout_layout
msgid "Your cart is empty!"
msgstr "Ostoskorisi on tyhjä!"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/delivery.py:0
msgid "Your cart is empty."
msgstr "Ostoskorisi on tyhjä."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/sale_order.py:0
msgid "Your cart is not ready to be paid, please verify previous steps."
msgstr "Ostoskorisi ei ole valmis maksettavaksi, tarkista edelliset vaiheet."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
msgid "Your payment has been authorized."
msgstr "Maksusuorituksesi on hyväksytty."

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/location_selector/location_selector_dialog/location_selector_dialog.js:0
msgid "Your postal code"
msgstr "Postinumerosi"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "Your previous cart has already been completed."
msgstr "Edellinen ostotapahtumasi on jo viety loppuun."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Zip Code"
msgstr "Postinumero"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_delivery_zip_prefix
msgid "Zip Prefix"
msgstr "Postinumeron etuliite"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "bpost"
msgstr "bpost"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "bpost Shipping Methods"
msgstr "bpost Toimitusmenetelmät"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "e.g. lamp,bin"
msgstr "esim. lamppu, roskakori"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#: model:ir.ui.menu,name:website_sale.menu_ecommerce
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_settings
msgid "eCommerce"
msgstr "Verkkokauppa"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.product_public_category_action
#: model:ir.ui.menu,name:website_sale.menu_catalog_categories
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "eCommerce Categories"
msgstr "Verkkokaupan tuotekategoriat"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__description_ecommerce
#: model:ir.model.fields,field_description:website_sale.field_product_template__description_ecommerce
msgid "eCommerce Description"
msgstr "verkkokaupan kuvaus"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_attribute_view_form
msgid "eCommerce Filter"
msgstr "Verkkokaupan suodatin"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.attribute_tree_view
msgid "eCommerce Filter Visibility"
msgstr "Verkkokaupan suodattimen näkyvyys"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "eCommerce Media"
msgstr "verkkokaupan Media"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_digest_digest__kpi_website_sale_total
msgid "eCommerce Sales"
msgstr "Verkkokauppamyynnit"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.header_cart_link
msgid "eCommerce cart"
msgstr "verkkokaupan ostoskori"

#. module: website_sale
#: model:ir.actions.server,name:website_sale.ir_cron_send_availability_email_ir_actions_server
msgid "eCommerce: send email to customers about their abandoned cart"
msgstr "verkkokauppa: lähetä asiakkaalle sähköpostia hylätystä ostoskorista"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "if you want to merge your previous cart into current cart."
msgstr "jos haluat yhdistää edellisen ostoskorisi nykyiseen ostoskoriin."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid ""
"if you want to restore your previous cart. Your current cart will be "
"replaced with your previous cart."
msgstr ""
"jos halaut palauttaa edellisen ostoskorisi. Nykyinen ostoskorisi korvataan "
"edellisellä ostoskorillasi."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "in category \""
msgstr "kategoriassa \""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "new address"
msgstr "uusi osoite"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
msgid "remove"
msgstr "poista"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.accept_terms_and_conditions
msgid "terms &amp; conditions"
msgstr "tilaus- ja toimitusehdot"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "to follow your order."
msgstr "seurataksesi lähetystäsi."
