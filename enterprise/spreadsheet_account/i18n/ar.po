# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* spreadsheet_account
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/plugins/accounting_plugin.js:0
msgid "%s is not a valid year."
msgstr "%s ليست سنة صحيحة. "

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid ""
"'%s' is not a valid period. Supported formats are \"21/12/2022\", "
"\"Q1/2022\", \"12/2022\", and \"2022\"."
msgstr ""
"'%s' ليست فترة صحيحة. الصيغ المدعومة هي \"21/12/2022\"، \"Q1/2022\" "
"،\"12/2022\"، و \"2022\". "

#. module: spreadsheet_account
#: model:ir.model,name:spreadsheet_account.model_account_account
msgid "Account"
msgstr "الحساب "

#. module: spreadsheet_account
#: model:ir.model,name:spreadsheet_account.model_res_company
msgid "Companies"
msgstr "الشركات"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "Get the total balance for the specified account(s) and period."
msgstr "الحصول على إجمالي رصيد الحساب (الحسابات) المحددة والفترة. "

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "Get the total credit for the specified account(s) and period."
msgstr "الحصول على إجمالي الائتمان للحساب (الحسابات) المحددة والفترة. "

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "Get the total debit for the specified account(s) and period."
msgstr "الحصول على إجمالي الخصم للحساب (الحسابات) المحددة والفترة. "

#. module: spreadsheet_account
#. odoo-python
#: code:addons/spreadsheet_account/models/account.py:0
msgid "Journal items for account prefix %s"
msgstr "عناصر اليومية لبادئة الحساب %s "

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "Returns the account ids of a given group."
msgstr "يُحضر معرفات الحسابات لمجموعة محددة. "

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid ""
"Returns the ending date of the fiscal year encompassing the provided date."
msgstr "يُحضر تاريخ نهاية السنة المالية شاملة التاريخ المحدد. "

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid ""
"Returns the starting date of the fiscal year encompassing the provided date."
msgstr "يُحضر تاريخ بداية السنة المالية شاملة التاريخ المحدد. "

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/index.js:0
msgid "See records"
msgstr "تفقد السجلات "

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "Set to TRUE to include unposted entries."
msgstr "التعيين كقيمة صحيحة لتشمل القيود غير المُرحلة. "

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "The account type (income, expense, asset_current,...)."
msgstr ""
"نوع الحساب (حساب دخل، حساب نفقات، حساب asset_current \"الأصول المتداولة\"، "
"...). "

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/plugins/accounting_plugin.js:0
msgid "The company fiscal year could not be found."
msgstr "لم يتم العثور على السنة المالية للشركة. "

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "The company to target (Advanced)."
msgstr "الشركة لاستهدافها (متقدم). "

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "The company."
msgstr "الشركة. "

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid ""
"The date range. Supported formats are \"21/12/2022\", \"Q1/2022\", "
"\"12/2022\", and \"2022\"."
msgstr ""
"نطاق التاريخ. الصيغ المدعومة هي \"21/12/2022\"، \"Q1/2022\" ،\"12/2022\"، و "
"\"2022\". "

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "The day from which to extract the fiscal year end."
msgstr "اليوم الذي يجب استخراج نهاية العام المالي منه. "

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "The day from which to extract the fiscal year start."
msgstr "اليوم الذي يجب استخراج بداية العام المالي منه. "

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "The prefix of the accounts."
msgstr "بادئات الحسابات. "

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "Year offset applied to date_range."
msgstr "إزاحة السنة مطبقة على date_range. "
