# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* spreadsheet_account
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/plugins/accounting_plugin.js:0
msgid "%s is not a valid year."
msgstr ""

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid ""
"'%s' is not a valid period. Supported formats are \"21/12/2022\", "
"\"Q1/2022\", \"12/2022\", and \"2022\"."
msgstr ""

#. module: spreadsheet_account
#: model:ir.model,name:spreadsheet_account.model_account_account
msgid "Account"
msgstr "Hesap"

#. module: spreadsheet_account
#: model:ir.model,name:spreadsheet_account.model_res_company
msgid "Companies"
msgstr "Şirketler"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "Get the total balance for the specified account(s) and period."
msgstr ""

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "Get the total credit for the specified account(s) and period."
msgstr ""

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "Get the total debit for the specified account(s) and period."
msgstr ""

#. module: spreadsheet_account
#. odoo-python
#: code:addons/spreadsheet_account/models/account.py:0
msgid "Journal items for account prefix %s"
msgstr ""

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "Returns the account ids of a given group."
msgstr ""

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid ""
"Returns the ending date of the fiscal year encompassing the provided date."
msgstr ""

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid ""
"Returns the starting date of the fiscal year encompassing the provided date."
msgstr ""

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/index.js:0
msgid "See records"
msgstr "Kayıtlara bakın"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "Set to TRUE to include unposted entries."
msgstr ""

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "The account type (income, expense, asset_current,...)."
msgstr ""

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/plugins/accounting_plugin.js:0
msgid "The company fiscal year could not be found."
msgstr ""

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "The company to target (Advanced)."
msgstr ""

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "The company."
msgstr ""

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid ""
"The date range. Supported formats are \"21/12/2022\", \"Q1/2022\", "
"\"12/2022\", and \"2022\"."
msgstr ""

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "The day from which to extract the fiscal year end."
msgstr ""

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "The day from which to extract the fiscal year start."
msgstr ""

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "The prefix of the accounts."
msgstr ""

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "Year offset applied to date_range."
msgstr ""
