# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* spreadsheet_account
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/plugins/accounting_plugin.js:0
msgid "%s is not a valid year."
msgstr "%s no es un año validado"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid ""
"'%s' is not a valid period. Supported formats are \"21/12/2022\", "
"\"Q1/2022\", \"12/2022\", and \"2022\"."
msgstr ""
"'%s' no es un periodo validado. Los formatos de soportes son \"21/12/2022\","
"  \"Q1/2022\", \"12/2022\", y \"2022\""

#. module: spreadsheet_account
#: model:ir.model,name:spreadsheet_account.model_account_account
msgid "Account"
msgstr "Cuenta"

#. module: spreadsheet_account
#: model:ir.model,name:spreadsheet_account.model_res_company
msgid "Companies"
msgstr "Compañías"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "Get the total balance for the specified account(s) and period."
msgstr ""
"Obtenga el saldo total para la(s) cuenta(s) especificada(s) y el periodo."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "Get the total credit for the specified account(s) and period."
msgstr ""
"Obtenga el crédito total para la(s) cuenta(s) especificada(s) y el periodo."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "Get the total debit for the specified account(s) and period."
msgstr ""
"Obtenga el débito total para la(s) cuenta(s) especificada(s) y el periodo."

#. module: spreadsheet_account
#. odoo-python
#: code:addons/spreadsheet_account/models/account.py:0
msgid "Journal items for account prefix %s"
msgstr "Apuntes contables para el prefijo de la cuenta %s"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "Returns the account ids of a given group."
msgstr "Devuelve los id de cuenta del grupo indicado."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid ""
"Returns the ending date of the fiscal year encompassing the provided date."
msgstr ""
"Devuelve la fecha final del ejercicio fiscal englobando la fecha prevista."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid ""
"Returns the starting date of the fiscal year encompassing the provided date."
msgstr ""
"Devolver la fecha inical del ejercicio fiscal englobando la fecha prevista."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/index.js:0
msgid "See records"
msgstr "Ver registros"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "Set to TRUE to include unposted entries."
msgstr "Establecer como TRUE para incluir las entradas no publicadas."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "The account type (income, expense, asset_current,...)."
msgstr "El tipo de cuenta (ingresos, gastos, asset_current y más)."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/plugins/accounting_plugin.js:0
msgid "The company fiscal year could not be found."
msgstr "No se pudo encontrar el ejercicio fiscal de la compañia."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "The company to target (Advanced)."
msgstr "La compañia objetivo (avanzado)."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "The company."
msgstr "La compañía."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid ""
"The date range. Supported formats are \"21/12/2022\", \"Q1/2022\", "
"\"12/2022\", and \"2022\"."
msgstr ""
"El intervalo de fechas. Los formatos compatibles son \"21/12/2022\", "
"\"Q1/2022\", \"12/2022\" y \"2022\"."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "The day from which to extract the fiscal year end."
msgstr "El día a partir del cual extraer el fin del ejercicio fiscal."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "The day from which to extract the fiscal year start."
msgstr "El día a partir del cual extraer el inicio del ejercicio fiscal."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "The prefix of the accounts."
msgstr "El prefijo de las cuentas."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "Year offset applied to date_range."
msgstr "Desplazamiento anual aplicado a date_range."
