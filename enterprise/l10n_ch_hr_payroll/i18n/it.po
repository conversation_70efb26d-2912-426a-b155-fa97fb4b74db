# Translation of Odoo Server.
# This file contains the translation of the following modules:
#   * l10n_ch_hr_payroll
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.5alpha1+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-11-20 08:49+0000\n"
"PO-Revision-Date: 2024-06-24 12:27+0000\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? "
"1 : 2;\n"

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,help:l10n_ch_hr_payroll.field_l10n_ch_accident_insurance_line__solution_number
msgid ""
"\n"
"0: Not UVG insured (e.g. member of the board of directors not working in the "
"company)\n"
"1: AAP and AANP insured, with AANP deduction\n"
"2: Insured AAP and AANP, without AANP deduction\n"
"3: Only AAP insured, so no AANP deduction (for employees whose weekly work "
"is < 8 h))"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/l10n_ch_individual_account.py:0
msgid "%(company)s-ch-global-account-%(year)s"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/l10n_ch_individual_account.py:0
msgid "%(employee)s-ch-individual-account-%(year)s"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.actions.report,print_report_name:l10n_ch_hr_payroll.action_report_light_payslip_ch
#: model:ir.actions.report,print_report_name:l10n_ch_hr_payroll.action_report_payslip_ch
msgid "'Payslip - %s' % (object.employee_id.name)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.actions.report,print_report_name:l10n_ch_hr_payroll.action_report_individual_account
msgid "(object._get_report_base_filename())"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_accident_insurance_line_rate__employer_non_occupational_part__100
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_accident_insurance_line_rate__employer_occupational_part__100
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_additional_accident_insurance_line_rate__employer_part__100
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_sickness_insurance_line_rate__employer_part__100
msgid "100 %"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule.category,name:l10n_ch_hr_payroll.hr_salary_rule_category_thirteen_month
msgid "13th Month"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_salary_rule__l10n_ch_13th_month_included
msgid "13th Month Included"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_14th_month
msgid "14th Month"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_salary_certificate_report_xml
msgid "2022-01-01"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_salary_certificate_report_xml
msgid "2022-12-31"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_salary_rule__l10n_ch_5_cents_rounding
msgid "5 cents rounding"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_accident_insurance_line_rate__employer_non_occupational_part__50
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_accident_insurance_line_rate__employer_occupational_part__50
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_additional_accident_insurance_line_rate__employer_part__50
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_sickness_insurance_line_rate__employer_part__50
msgid "50 %"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_be_individual_account_view_form
msgid ""
"<i class=\"oi oi-fw oi-arrow-right\"/>The following XML file can be imported "
"here for PDF Generation"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_individual_account_view_form
msgid "<span class=\"o_stat_text\">Eligible Employees</span>"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_tax_rate_import_wizard_view_form
msgid ""
"<span class=\"text-muted\">Tax files (.txt format) can be found at https://"
"www.estv.admin.ch/estv/fr/accueil/impot-federal-direct/impot-a-la-source/"
"baremes-cantonaux.html</span>"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.hr_contract_view_form
msgid "<span>%</span>"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.hr_contract_view_form
msgid "<span>CFH / lesson</span>"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_accident_insurance_line_view_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_additional_accident_insurance_line_view_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_sickness_insurance_line_view_form
msgid "<span>CHF</span>"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.report_individual_account
msgid "<span>Individual Account Report for year </span>"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.report_individual_account
msgid "<strong>Address</strong>"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.report_individual_account
msgid "<strong>Bank Account</strong>"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.report_light_payslip_ch
msgid "<strong>CODE</strong>"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.report_individual_account
msgid "<strong>Company Information</strong>"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.report_monthly_summary
msgid "<strong>Company:</strong> <br/>"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.report_individual_account
msgid "<strong>Designation</strong>"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.report_individual_account
msgid "<strong>Email</strong>"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.report_monthly_summary
msgid "<strong>Employee:</strong> <br/>"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.report_monthly_summary
msgid "<strong>From:</strong>"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.report_individual_account
msgid "<strong>Identification No</strong>"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.report_individual_account
msgid "<strong>Name</strong>"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.report_monthly_summary
msgid "<strong>Nr.</strong>"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.report_individual_account
msgid "<strong>Registration Number</strong>"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.report_individual_account
msgid "<strong>Salary Computation</strong>"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.report_monthly_summary
msgid "<strong>To:</strong>"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.constraint,message:l10n_ch_hr_payroll.constraint_l10n_ch_location_unit__unique
msgid ""
"A work location cannot be set more than once for the same company and "
"partner."
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_aanp
msgid "AANP"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_aanp_comp
msgid "AANP (Comp. Part.)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_salary_rule__l10n_ch_aanp_included
msgid "AANP Included"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule.category,name:l10n_ch_hr_payroll.hr_salary_rule_category_aanp_salary
msgid "AANP Salary"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_aap_comp
msgid "AAP (Comp. Part.)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_contract__l10n_ch_accident_insurance_line_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_payslip__l10n_ch_accident_insurance_line_id
msgid "AAP/AANP Insurance"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.actions.act_window,name:l10n_ch_hr_payroll.action_l10n_ch_accident_insurance
#: model:ir.ui.menu,name:l10n_ch_hr_payroll.menu_l10n_ch_accident_insurance
msgid "AAP/AANP Insurances"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_ac
msgid "AC"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_ac_comp
msgid "AC (Comp. Part.)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_ac_base
#: model:hr.salary.rule.category,name:l10n_ch_hr_payroll.hr_salary_rule_category_ac_base
msgid "AC Base"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_ac_open
msgid "AC OPEN"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/l10n_ch_insurance_report.py:0
#: model:hr.salary.rule.category,name:l10n_ch_hr_payroll.hr_salary_rule_category_ac_open
msgid "AC Open"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/l10n_ch_insurance_report.py:0
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_ac_salary
#: model:hr.salary.rule.category,name:l10n_ch_hr_payroll.hr_salary_rule_category_ac_salary
msgid "AC Salary"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule.category,name:l10n_ch_hr_payroll.hr_salary_rule_category_ac_threshold
msgid "AC Salary Maximum"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_ac_salary_max
msgid "AC Salary Threshold"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_social_insurance_view_form
msgid "AC Thresholds"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_compl_ac
msgid "ACC"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/l10n_ch_insurance_report.py:0
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_acc_salary
#: model:hr.salary.rule.category,name:l10n_ch_hr_payroll.hr_salary_rule_category_acc_salary
msgid "ACC Salary"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_acc_salary_max
#: model:hr.salary.rule.category,name:l10n_ch_hr_payroll.hr_salary_rule_category_acc_threshold
msgid "ACC Salary Maximum"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_social_insurance_view_form
msgid "ACC Thresholds"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.report_individual_account
msgid "APR"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_as_days
#: model:hr.salary.rule.category,name:l10n_ch_hr_payroll.hr_salary_rule_category_as_days
msgid "AS Days"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.report_individual_account
msgid "AUG"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_avs
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_insurance_report_view_form
msgid "AVS"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_avs_comp
msgid "AVS (Comp. Part.)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model,name:l10n_ch_hr_payroll.model_ch_yearly_report
msgid "AVS / LAA / LAAC / IJM Yearly Report"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_avs_base
#: model:hr.salary.rule.category,name:l10n_ch_hr_payroll.hr_salary_rule_category_avs_base
msgid "AVS Base"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__ch_yearly_report_line__report_type__avs_open
msgid "AVS Exempted Statement"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_avs_franchise
msgid "AVS Franchise IMPUTEE"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule.category,name:l10n_ch_hr_payroll.hr_salary_rule_category_avs_franchise
msgid "AVS Franchise Imputée"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule.category,name:l10n_ch_hr_payroll.hr_salary_rule_category_avs_franchise_non_imp
msgid "AVS Franchise Non Imputée"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_avs_open
msgid "AVS OPEN"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/l10n_ch_insurance_report.py:0
#: model:hr.salary.rule.category,name:l10n_ch_hr_payroll.hr_salary_rule_category_avs_open
msgid "AVS Open"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/l10n_ch_insurance_report.py:0
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_avs_salary
#: model:hr.salary.rule.category,name:l10n_ch_hr_payroll.hr_salary_rule_category_avs_salary
msgid "AVS Salary"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_avs_salary_retired
#: model:hr.salary.rule.category,name:l10n_ch_hr_payroll.hr_salary_rule_category_avs_salary_retired
msgid "AVS Salary Retired"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_contract__l10n_ch_avs_status
msgid "AVS Special Status"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__ch_yearly_report_line__report_type__avs
msgid "AVS Statement"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_payslip__l10n_ch_avs_status
msgid "AVS Status"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_salary_rule__l10n_ch_ac_included
msgid "AVS/AC Included"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_contract__l10n_ch_social_insurance_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_payslip__l10n_ch_social_insurance_id
msgid "AVS/AC Insurance"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.actions.act_window,name:l10n_ch_hr_payroll.action_l10n_ch_social_insurance
#: model:ir.ui.menu,name:l10n_ch_hr_payroll.menu_l10n_ch_social_insurance
msgid "AVS/AC Insurances"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_avs_franchise_non_imp
msgid "Abattement Non Imputé (Cumul)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance__ac_line_ids
msgid "Ac Line"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_indemnity_accident
msgid "Accident Indemnity"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:hr.salary.rule,note:l10n_ch_hr_payroll.l10n_ch_employees_aanp
#: model_terms:hr.salary.rule,note:l10n_ch_hr_payroll.l10n_ch_employees_aanp_comp
msgid "Accident Insurance (Non Occupational Rates)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:hr.salary.rule,note:l10n_ch_hr_payroll.l10n_ch_employees_aap_comp
msgid "Accident Insurance (Occupational Rates)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_accident_insurance_view_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_accident_insurance_view_tree
msgid "Accident Insurances"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_accident_insurance_line_view_form
msgid "Accident Insurances Solutions"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_action_collab
msgid "Actions de collaborateurs"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:hr.salary.rule,note:l10n_ch_hr_payroll.l10n_ch_employees_laac_comp_1
#: model_terms:hr.salary.rule,note:l10n_ch_hr_payroll.l10n_ch_employees_laac_comp_2
msgid "Additional Accident Insurance (LAAC)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.actions.act_window,name:l10n_ch_hr_payroll.action_l10n_ch_additional_accident_insurance
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_additional_accident_insurance_view_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_additional_accident_insurance_view_tree
msgid "Additional Accident Insurances"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_additional_accident_insurance_line_view_form
msgid "Additional Accident Insurances Solutions"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:hr.salary.rule,note:l10n_ch_hr_payroll.l10n_ch_employees_compl_ac
#: model_terms:hr.salary.rule,note:l10n_ch_hr_payroll.l10n_ch_employees_compl_ac_comp
msgid "Additional Unemployment Insurance"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,help:l10n_ch_hr_payroll.field_hr_payslip__l10n_ch_after_departure_payment
msgid ""
"Additional payment after leaving in the current year with maximum salary and "
"third-party benefits"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_advance
msgid "Advance"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_payslip__l10n_ch_after_departure_payment__n
msgid "After Departure Payment"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_payslip__l10n_ch_after_departure_payment__nk
msgid "After Departure Payment with correction"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_monthly_summary__aggregation_type
msgid "Aggregation Type"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_payslip_is_log_line__amount
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance_avs_ac_threshold__amount
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance_avs_acc_threshold__amount
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance_avs_retirement_rente__amount
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.report_monthly_summary
msgid "Amount"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_residence_category__annual-b
msgid "Annual (Cat. B)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_annuities_ai
msgid "Annuities AI"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_canton__ar
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_spouse_work_canton__ar
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_location_unit__canton__ar
msgid "Appenzell Rhodes-Extérieures"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_canton__ai
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_spouse_work_canton__ai
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_location_unit__canton__ai
msgid "Appenzell Rhodes-Intérieures"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.contract.type,name:l10n_ch_hr_payroll.l10n_ch_contract_type_apprentice
msgid "Apprenticeship contract"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_is_report__month__4
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_monthly_summary__month__4
msgid "April"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_canton__ag
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_spouse_work_canton__ag
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_location_unit__canton__ag
msgid "Argovie"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_residence_category__asylumseeker-n
msgid "Asylum Seeker (Cat. N)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_is_report__month__8
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_monthly_summary__month__8
msgid "August"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_ch_yearly_report__avs_institution_ids
msgid "Avs Institution"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance__avs_line_ids
msgid "Avs Line"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_location_unit__bur_ree_number
msgid "BUR-REE-Number"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/l10n_ch_location_unit.py:0
msgid "BUR-REE-Number checksum is not correct"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/l10n_ch_location_unit.py:0
msgid "BUR-REE-Number does not match the right format"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_basic_hourly
msgid "Basic Hourly Salary"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,help:l10n_ch_hr_payroll.field_l10n_ch_hr_employee_children__deduction_start
msgid "Beginning of the right to the child deduction"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_canton__be
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_spouse_work_canton__be
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_location_unit__canton__be
msgid "Berne"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_birth_allowance
msgid "Birth Allowance"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_hr_employee_children__birthdate
msgid "Birthdate"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/l10n_ch_insurance_report.py:0
msgid "Birthday"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_bonus
msgid "Bonus"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_imp_bonus
msgid "Bonus for improvement proposal"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_monthly_summary__aggregation_type__company
msgid "By Company"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_monthly_summary__aggregation_type__employee
msgid "By Employee"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_canton__bl
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_spouse_work_canton__bl
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_location_unit__canton__bl
msgid "Bâle-Campagne"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_canton__bs
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_spouse_work_canton__bs
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_location_unit__canton__bs
msgid "Bâle-Ville"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_ca_fee
msgid "CA Fee"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_fak
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_insurance_report_view_form
msgid "CAF"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_fak_company
msgid "CAF (Comp. Part.)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:hr.salary.rule,note:l10n_ch_hr_payroll.l10n_ch_employees_fak
msgid "CAF Contribution (Family Allowances)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:hr.salary.rule,note:l10n_ch_hr_payroll.l10n_ch_employees_fak_company
msgid "CAF Contribution (Family Allowances) Company"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_salary_rule__l10n_ch_caf_statement
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__ch_yearly_report_line__report_type__caf
msgid "CAF Statement"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule.category,name:l10n_ch_hr_payroll.hr_salary_rule_category_ch_days
msgid "CH Days"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.actions.act_window,name:l10n_ch_hr_payroll.action_l10n_ch_compensation_fund
msgid "CP Family"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_ch_yearly_report__caf_institution_ids
msgid "Caf Institution"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_compensation_fund__caf_line_ids
msgid "Caf Line"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_hr_payroll_employee_lang_view_form
msgid "Cancel"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_employee__l10n_ch_canton
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_is_report_line__canton
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_location_unit__canton
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_res_users__l10n_ch_canton
msgid "Canton"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_pension_capital
msgid "Capital benefit of a pension nature"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_car_expense
msgid "Car Expense"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_car_fees
msgid "Car Fees"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_cash_advantage_correction
msgid "Cash Advantage Correction"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_employee__certificate
msgid "Certificate Level"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.actions.act_window,name:l10n_ch_hr_payroll.l10n_ch_hr_payroll_employee_lang_wizard_action
#: model:ir.model,name:l10n_ch_hr_payroll.model_l10n_ch_hr_payroll_employee_lang_wizard
msgid "Change Employee Language"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model,name:l10n_ch_hr_payroll.model_l10n_ch_hr_payroll_employee_lang_wizard_line
msgid "Change Employee Language Line"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_child_allowance
msgid "Child Allowance"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_child_allowance_payment
msgid "Child Allowance Payment"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/l10n_ch_insurance_report.py:0
msgid "Child Allowances"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_hr_employee_children_view_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_hr_employee_children_view_tree
msgid "Children"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_religious_denomination__christiancatholic
msgid "Christian Catholic"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_tax_rate_import_wizard_view_form
msgid "Close"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_payslip_is_log_line__code
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.report_individual_account
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.report_monthly_summary
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.report_payslip_ch
msgid "Code"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_payslip__l10n_ch_after_departure_payment
msgid "Code change, correction-payment after departure"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,help:l10n_ch_hr_payroll.field_hr_salary_rule__l10n_ch_wage_statement
msgid ""
"Code meaning:\n"
"- I: Gross base salary and regular allowances\n"
"    - Ordinary salary paid, such as monthly, hourly, piece rate, working "
"from home, etc.\n"
"    - Regular allowances paid, such as allowances for position or for length "
"of service,\n"
"      residence, housing, travel or cost of living allowances.\n"
"    - Tips paid subject to AVS contributions.\n"
"    - Regular payments (at each pay) of a commission, turnover contribution "
"or other bonus\n"
"      paid regularly.\n"
"- J: Gross amount of compensation for shift work, Sunday or night work and "
"other arduousness\n"
"     bonuses (compensation for on-call duty, dirty work, etc.).\n"
"- K: Total amount of family allowances paid by the employer in the form of "
"child allowances,\n"
"     vocational training allowances, household allowances or care "
"allowances.\n"
"- Y: Benefits provided by insurance or similar institutions and which have "
"an impact on\n"
"     employee contributions\n"
"- L: Amount of AVS/AI/APG/AC/ (1st pillar) and AANP (employee’s share) "
"contributions.\n"
"     Not included:\n"
"        - the employer’s share,\n"
"        - the Parifonds,\n"
"        - daily allowance insurance in the event of IJM illness,\n"
"        - LAAC supplementary accident insurance\n"
"        - Social contributions must in principle be transmitted in the form "
"of negative values.\n"
"- M: Amount of ordinary contributions (employee's share) to LPP professional "
"pension provision or\n"
"     the 2nd pillar, in accordance with legal, statutory or regulatory "
"provisions.\n"
"     The amount indicated should not include redemption contributions.\n"
"     Regular BVG-LPP contributions must in principle be transmitted as "
"negative values."
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,help:l10n_ch_hr_payroll.field_hr_salary_rule__l10n_ch_yearly_statement
msgid ""
"Code meaning:\n"
"- O: 13th salary paid (with the 14th and following) provided that it is not "
"in the form of a bonus\n"
"- P: Overtime pay\n"
"- Q: Sporadic Benefits, e.g.\n"
"        - bonuses,\n"
"        - merit-based rewards,\n"
"        - participation in profit or turnover,\n"
"        - engagement bonuses and severance pay,\n"
"        - loyalty bonuses, bonuses and gifts for length of service,\n"
"        - fixed moving compensation,\n"
"        - Christmas bonuses,\n"
"        - compensation for members of the board of directors (attendance "
"fees, fees, etc.).\n"
"- R: Fringe Benefits\n"
"        - board and lodging (section 2.1 of the salary certificate);\n"
"      - the private share of the company car (section 2.2 of the salary "
"certificate);\n"
"      - other ancillary salary benefits (section 2.3 of the salary "
"certificate);\n"
"      - participation rights (section 5 of the salary certificate).\n"
"- S: Capital Payment: Capital benefits of a pension nature paid by the "
"employer directly to the\n"
"     employee and likely to be taxed at a reduced rate.\n"
"      - severance pay of a provident nature;\n"
"      - capital benefits of a pension nature;\n"
"      - salary payments after death.\n"
"- T: Other Benefits: All other services covered on an optional basis by the "
"employer, although\n"
"     they are generally due by the employee.\n"
"        - partial or total coverage of contributions owed by the employee to "
"LPP professional\n"
"          insurance, including executive insurance;\n"
"        - payments to occupational pension institutions (2nd pillar) made by "
"the employer in\n"
"          favor of the employee (purchase contributions);\n"
"        - payment of insurance contributions in favor of the employee and "
"members of his family\n"
"          (health insurance, optional 3rd pillar b insurance, life "
"insurance, etc.);\n"
"        - payment of contributions to recognized forms of linked individual "
"pension provision (3rd pillar a)."
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_commission
msgid "Commission"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model,name:l10n_ch_hr_payroll.model_res_company
msgid "Companies"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_ch_yearly_report__company_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_individual_account__company_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_is_report__company_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_location_unit__company_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_monthly_summary__company_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_salary_certificate__company_id
msgid "Company"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance_ac_line__employer_additional_rate
msgid "Company Additional Rate (%)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_company_car_employee
msgid "Company Car (Employee Part)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_compensation_fund_view_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.res_config_settings_view_form
msgid "Company Information"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance__laa_insurance_id
msgid "Company LAA Insurance"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance__lpp_insurance_id
msgid "Company LPP Insurance"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_accident_insurance_line_rate__employer_non_occupational_part
msgid "Company Non Occupational Part"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_accident_insurance_line_rate__employer_occupational_part
msgid "Company Occupational Part"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule.category,name:l10n_ch_hr_payroll.hr_salary_rule_category_comp_part
msgid "Company Part"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_compensation_fund_line__company_rate
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance_ac_line__employer_rate
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance_avs_line__employer_rate
msgid "Company Rate (%)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_employer_lpp_compensation
msgid "Compensation for employer LPP contributions"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_employer_lpp_compensation_rachat
msgid "Compensation rachat LPP employeur"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_compl_ac_comp
msgid "Compl. AC (Comp. Part.)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_hr_employee_children__name
msgid "Complete Name"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model,name:l10n_ch_hr_payroll.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_hr_payroll_employee_lang_view_form
msgid "Confirm"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_accident_insurance__contract_number
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_additional_accident_insurance__contract_number
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_lpp_insurance__contract_number
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_sickness_insurance__contract_number
msgid "Contract Number"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_contract__contract_type_id
msgid "Contract Type"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_contract__l10n_ch_contractual_13th_month_rate
msgid "Contractual allowances for 13th month"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_payslip_is_log_line__corrected_slip_id
msgid "Corrected Slip"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_employee_is_line__correction_date
msgid "Correction Date"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_benefits_in_kind_correction
msgid "Correction of benefits in kind"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_third_party_correction
msgid "Correction of third party compensation"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_monthly_summary_view_form
msgid "Create PDF"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_monthly_summary_view_form
msgid "Create XLSX"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_ch_yearly_report__create_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_ch_yearly_report_line__create_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_employee_is_line__create_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_payslip_is_log_line__create_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_accident_insurance__create_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_accident_insurance_line__create_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_accident_insurance_line_rate__create_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_additional_accident_insurance__create_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_additional_accident_insurance_line__create_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_additional_accident_insurance_line_rate__create_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_compensation_fund__create_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_compensation_fund_line__create_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_hr_employee_children__create_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_hr_payroll_employee_lang_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_hr_payroll_employee_lang_wizard_line__create_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_individual_account__create_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_is_report__create_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_is_report_line__create_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_location_unit__create_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_lpp_insurance__create_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_monthly_summary__create_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_salary_certificate__create_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_sickness_insurance__create_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_sickness_insurance_line__create_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_sickness_insurance_line_rate__create_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance__create_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance_ac_line__create_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance_avs_ac_threshold__create_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance_avs_acc_threshold__create_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance_avs_line__create_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance_avs_retirement_rente__create_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_tax_rate_import_wizard__create_uid
msgid "Created by"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_ch_yearly_report__create_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_ch_yearly_report_line__create_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_employee_is_line__create_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_payslip_is_log_line__create_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_accident_insurance__create_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_accident_insurance_line__create_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_accident_insurance_line_rate__create_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_additional_accident_insurance__create_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_additional_accident_insurance_line__create_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_additional_accident_insurance_line_rate__create_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_compensation_fund__create_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_compensation_fund_line__create_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_hr_employee_children__create_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_hr_payroll_employee_lang_wizard__create_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_hr_payroll_employee_lang_wizard_line__create_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_individual_account__create_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_is_report__create_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_is_report_line__create_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_location_unit__create_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_lpp_insurance__create_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_monthly_summary__create_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_salary_certificate__create_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_sickness_insurance__create_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_sickness_insurance_line__create_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_sickness_insurance_line_rate__create_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance__create_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance_ac_line__create_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance_avs_ac_threshold__create_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance_avs_acc_threshold__create_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance_avs_line__create_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance_avs_retirement_rente__create_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_tax_rate_import_wizard__create_date
msgid "Created on"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_residence_category__crossborder-g
msgid "Cross Border (Cat. G)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_ch_yearly_report__currency_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_is_report__currency_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_monthly_summary__currency_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_salary_certificate__currency_id
msgid "Currency"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_contract__l10n_ch_current_occupation_rate
msgid "Current Occupation rate"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_accident_insurance__customer_number
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_additional_accident_insurance__customer_number
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_lpp_insurance__customer_number
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_sickness_insurance__customer_number
msgid "Customer Number"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.report_individual_account
msgid "DEC"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_location_unit__dpi_number
msgid "DPI Number"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:hr.salary.rule,note:l10n_ch_hr_payroll.l10n_ch_employees_ijm_1
#: model_terms:hr.salary.rule,note:l10n_ch_hr_payroll.l10n_ch_employees_ijm_2
#: model_terms:hr.salary.rule,note:l10n_ch_hr_payroll.l10n_ch_employees_ijm_comp_1
#: model_terms:hr.salary.rule,note:l10n_ch_hr_payroll.l10n_ch_employees_ijm_comp_2
msgid "Daily Sickness Insurance"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_payslip_is_log_line__date
msgid "Date"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_death_allowance
msgid "Death Allowance"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_is_report__month__12
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_monthly_summary__month__12
msgid "December"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_individual_account__line_ids
msgid "Declarations"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_hr_employee_children__deduction_start
msgid "Deduction Start"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,help:l10n_ch_hr_payroll.field_l10n_ch_location_unit__bur_ree_number
msgid ""
"Depending on the structure of the company and the number of workplaces, "
"there are one or more REE numbers."
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,help:l10n_ch_hr_payroll.field_hr_contract__l10n_ch_is_predefined_category
msgid ""
"Des barèmes fixes sont appliqués pour l'impôt à la source retenu sur les "
"honoraires des administrateurs (art. 93 LIFD) et certaines participations de "
"collaborateur (art. 97a LIFD). Pour ces impôts, aucun enfant n'est pris en "
"compte et un seul taux en %% est appliqué. À cela s'ajoutent des catégories "
"prédéfinies pour les annonces rectificatives et pour l'annonce des salaires "
"bruts des frontaliers français pour lesquels l'accord spécial entre les "
"cantons BE, BS, BL, JU, NE, SO, VD et VS et la France s'applique."
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_individual_account__name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_salary_certificate__name
msgid "Description"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_social_insurance_view_form
msgid "Details"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_ch_yearly_report__display_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_ch_yearly_report_line__display_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_employee_is_line__display_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_payslip_is_log_line__display_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_accident_insurance__display_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_accident_insurance_line__display_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_accident_insurance_line_rate__display_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_additional_accident_insurance__display_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_additional_accident_insurance_line__display_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_additional_accident_insurance_line_rate__display_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_compensation_fund__display_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_compensation_fund_line__display_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_hr_employee_children__display_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_hr_payroll_employee_lang_wizard__display_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_hr_payroll_employee_lang_wizard_line__display_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_individual_account__display_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_is_report__display_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_is_report_line__display_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_location_unit__display_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_lpp_insurance__display_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_monthly_summary__display_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_salary_certificate__display_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_sickness_insurance__display_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_sickness_insurance_line__display_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_sickness_insurance_line_rate__display_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance__display_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance_ac_line__display_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance_avs_ac_threshold__display_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance_avs_acc_threshold__display_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance_avs_line__display_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance_avs_retirement_rente__display_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_tax_rate_import_wizard__display_name
msgid "Display Name"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_individual_account__documents_count
msgid "Documents Count"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_individual_account__documents_enabled
msgid "Documents Enabled"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_monthly_summary_view_form
msgid "Download the Monthly Summary PDF file:"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_monthly_summary_view_form
msgid "Download the Monthly Summary XLSX file:"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_rht_itp_ded
msgid "Déduction RHT/ITP (SM)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_carence_rht_itp
msgid "Délai de carence RHT/ITP"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_effective_expatriate_costs
msgid "Effective expatriate costs"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.hr_employee_view_form
msgid "Eligible children for deduction"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model,name:l10n_ch_hr_payroll.model_hr_employee
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_employee_is_line__employee_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_hr_employee_children__employee_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_hr_payroll_employee_lang_wizard_line__employee_id
msgid "Employee"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance_ac_line__employee_additional_rate
msgid "Employee Additional Rate (%)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model,name:l10n_ch_hr_payroll.model_l10n_ch_hr_employee_children
msgid "Employee Children"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model,name:l10n_ch_hr_payroll.model_hr_contract
msgid "Employee Contract"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/l10n_ch_insurance_report.py:0
msgid "Employee Number"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_compensation_fund_line__employee_rate
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance_ac_line__employee_rate
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance_avs_line__employee_rate
msgid "Employee Rate (%)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_compensation_fund_view_form
msgid "Employee Rates"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,help:l10n_ch_hr_payroll.field_hr_contract__l10n_ch_lesson_wage
msgid "Employee's gross wage by lesson."
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_additional_accident_insurance_line_rate__employer_part
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_sickness_insurance_line_rate__employer_part
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_accident_insurance_line_view_form
msgid "Employer Part"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_monthly_summary__date_end
msgid "End Period"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_contract__l10n_ch_avs_status__exempted
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_payslip__l10n_ch_avs_status__exempted
msgid "Exempted"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_salary_rule__l10n_ch_code
msgid "External Code"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.report_individual_account
msgid "FEB"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_fee
msgid "FEE"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_family_allowance
msgid "Family Allowance"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule.category,name:l10n_ch_hr_payroll.hr_salary_rule_category_caf
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_contract__l10n_ch_compensation_fund_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_payslip__l10n_ch_compensation_fund_id
msgid "Family Allowance (CAF)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.ui.menu,name:l10n_ch_hr_payroll.menu_l10n_ch_compensation_fund
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_compensation_fund_view_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_compensation_fund_view_tree
msgid "Family Allowances"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_is_report__month__2
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_monthly_summary__month__2
msgid "February"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_additional_accident_insurance_line_rate__female_rate
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_sickness_insurance_line_rate__female_rate
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_accident_insurance_line_view_form
msgid "Female Rate (%)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.contract.type,name:l10n_ch_hr_payroll.l10n_ch_contract_type_fixedSalaryMth
msgid "Fixed term contract with monthly salary"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.contract.type,name:l10n_ch_hr_payroll.l10n_ch_contract_type_fixedSalaryHrs
msgid "Fixed-term contract with salary hourly"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.contract.type,name:l10n_ch_hr_payroll.l10n_ch_contract_type_fixedSalaryNoTimeConstraint
msgid "Fixed-term contract, with commission, packages, piece rate salary, etc."
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_flat_expatriate_costs
msgid "Flat rate fees for expatriates"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_canton__ex
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_spouse_work_canton__ex
msgid "Foreign"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_free_housing
msgid "Free Housing"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_free_meals
msgid "Free Meals"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_free_room
msgid "Free Room"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_canton__fr
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_spouse_work_canton__fr
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_location_unit__canton__fr
msgid "Fribourg"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/l10n_ch_insurance_report.py:0
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_accident_insurance_line_rate__date_from
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_additional_accident_insurance_line_rate__date_from
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_compensation_fund_line__date_from
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_sickness_insurance_line_rate__date_from
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance_ac_line__date_from
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance_avs_ac_threshold__date_from
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance_avs_acc_threshold__date_from
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance_avs_line__date_from
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance_avs_retirement_rente__date_from
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_monthly_summary_view_form
msgid "From"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_func_allowance
msgid "Functional allowance"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_lpp_insurance__fund_number
msgid "Fund Number"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_be_individual_account_view_form
msgid "Generate Certificates"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_insurance_report_view_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_is_report_view_form
msgid "Generate Reports"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_monthly_summary_view_form
msgid "Generation Complete"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_canton__ge
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_spouse_work_canton__ge
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_location_unit__canton__ge
msgid "Genève"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_canton__gl
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_spouse_work_canton__gl
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_location_unit__canton__gl
msgid "Glaris"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_gratification
msgid "Gratification"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_canton__gr
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_spouse_work_canton__gr
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_location_unit__canton__gr
msgid "Grisons"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_gross_comp
msgid "Gross Compensation"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_salary_rule__l10n_ch_gross_included
msgid "Gross Included"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/l10n_ch_insurance_report.py:0
msgid "Gross Salary"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_contract__l10n_ch_thirteen_month
msgid "Has 13th Month"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_holiday_allowance
msgid "Holidays allowance"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_departure_time_off
msgid "Holidays payment after departure"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_house_allowance
msgid "House Allowance"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_ch_yearly_report__id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_ch_yearly_report_line__id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_employee_is_line__id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_payslip_is_log_line__id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_accident_insurance__id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_accident_insurance_line__id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_accident_insurance_line_rate__id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_additional_accident_insurance__id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_additional_accident_insurance_line__id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_additional_accident_insurance_line_rate__id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_compensation_fund__id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_compensation_fund_line__id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_hr_employee_children__id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_hr_payroll_employee_lang_wizard__id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_hr_payroll_employee_lang_wizard_line__id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_individual_account__id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_is_report__id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_is_report_line__id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_location_unit__id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_lpp_insurance__id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_monthly_summary__id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_salary_certificate__id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_sickness_insurance__id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_sickness_insurance_line__id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_sickness_insurance_line_rate__id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance__id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance_ac_line__id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance_avs_ac_threshold__id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance_avs_acc_threshold__id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance_avs_line__id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance_avs_retirement_rente__id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_tax_rate_import_wizard__id
msgid "ID"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_insurance_report_view_form
msgid "IJM"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/l10n_ch_insurance_report.py:0
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_ijm_base
#: model:hr.salary.rule.category,name:l10n_ch_hr_payroll.hr_salary_rule_category_ijm_base
msgid "IJM Base"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/l10n_ch_insurance_report.py:0
msgid "IJM Code"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_salary_rule__l10n_ch_ijm_included
msgid "IJM Included"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_contract__l10n_ch_sickness_insurance_line_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_payslip__l10n_ch_sickness_insurance_line_ids
#: model:ir.ui.menu,name:l10n_ch_hr_payroll.menu_l10n_ch_sickness_insurance
msgid "IJM Insurances"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/l10n_ch_insurance_report.py:0
#: model:hr.salary.rule.category,name:l10n_ch_hr_payroll.hr_salary_rule_category_ijm_salary
msgid "IJM Salary"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_ijm_salary_1
#: model:hr.salary.rule.category,name:l10n_ch_hr_payroll.hr_salary_rule_category_ijm_salary_1
msgid "IJM Salary Main Insurance"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_ijm_salary_max_1
#: model:hr.salary.rule.category,name:l10n_ch_hr_payroll.hr_salary_rule_category_ijm_salary_max_1
msgid "IJM Salary Maximum Main Insurance"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_ijm_salary_max_2
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_ijm_salary_min_2
#: model:hr.salary.rule.category,name:l10n_ch_hr_payroll.hr_salary_rule_category_ijm_salary_max_2
msgid "IJM Salary Maximum Second Insurance"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_ijm_salary_min_1
#: model:hr.salary.rule.category,name:l10n_ch_hr_payroll.hr_salary_rule_category_ijm_salary_min_1
msgid "IJM Salary Minimum Main Insurance"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule.category,name:l10n_ch_hr_payroll.hr_salary_rule_category_ijm_salary_min_2
msgid "IJM Salary Minimum Second Insurance"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_ijm_salary_2
#: model:hr.salary.rule.category,name:l10n_ch_hr_payroll.hr_salary_rule_category_ijm_salary_2
msgid "IJM Salary Second Insurance"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_sickness_insurance_view_form
msgid "IJM Solution"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__ch_yearly_report_line__report_type__ijm
msgid "IJM Statement"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_ijm_1
msgid "IJM1"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_ijm_comp_1
msgid "IJM1 (Comp. Part.)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_ijm_2
msgid "IJM2"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_ijm_comp_2
msgid "IJM2 (Comp. Part.)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_withholding_tax
msgid "IS"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.hr_contract_view_form
msgid "IS Additional Info"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_is_base
#: model:hr.salary.rule.category,name:l10n_ch_hr_payroll.hr_salary_rule_category_is_base
msgid "IS Base"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_payslip__l10n_ch_is_code
msgid "IS Code"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.ui.menu,name:l10n_ch_hr_payroll.menu_l10n_ch_employee_is_line
msgid "IS Corrections"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_is_salary_dt_aperiodic
#: model:hr.salary.rule.category,name:l10n_ch_hr_payroll.hr_salary_rule_category_is_dt_aperiodic_salary
msgid "IS DT Aperiodic Salary"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_is_salary_dt_periodic
#: model:hr.salary.rule.category,name:l10n_ch_hr_payroll.hr_salary_rule_category_is_dt_periodic_salary
msgid "IS DT Periodic Salary"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_is_salary_dt
#: model:hr.salary.rule.category,name:l10n_ch_hr_payroll.hr_salary_rule_category_is_dt_salary
msgid "IS DT Salary"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model,name:l10n_ch_hr_payroll.model_hr_employee_is_line
msgid "IS Entry / Withdrawals / Mutations"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.actions.act_window,name:l10n_ch_hr_payroll.action_l10n_ch_is_line
msgid "IS Line"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_employee_is_line_view_tree
msgid "IS Lines"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.hr_payslip_view_form_inherit
msgid "IS Log Lines"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model,name:l10n_ch_hr_payroll.model_hr_payslip_is_log_line
msgid "IS Log lines"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_contract__l10n_ch_is_model
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_payslip__l10n_ch_is_model
msgid "IS Model"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_contract__l10n_ch_is_predefined_category
msgid "IS Predefined Category"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_is_rate
#: model:hr.salary.rule.category,name:l10n_ch_hr_payroll.hr_salary_rule_category_is_rate
msgid "IS Rate"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_is_report_view_form
msgid "IS Report"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_is_salary
#: model:hr.salary.rule.category,name:l10n_ch_hr_payroll.hr_salary_rule_category_is_salary
msgid "IS Salary"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_is_salary_dt_ap
#: model:hr.salary.rule.category,name:l10n_ch_hr_payroll.hr_salary_rule_category_is_salary_dt_ap
msgid "IS Salary DT AP"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_is_salary_dt_p
#: model:hr.salary.rule.category,name:l10n_ch_hr_payroll.hr_salary_rule_category_is_salary_dt_p
msgid "IS Salary DT P"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_is_correction
msgid "IS: Correction"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_res_company__l10n_ch_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_res_config_settings__l10n_ch_uid
msgid "Identification Number (IDE-OFS)"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/res_company.py:0
msgid "Identification Number (IDE-OFS) checksum is not correct"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/res_company.py:0
msgid "Identification Number (IDE-OFS) does not match the right format"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_ch_yearly_report__ijm_institution_ids
msgid "Ijm Institution"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_indemnity_illness
msgid "Illness Indemnity"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_tax_rate_import_wizard_view_form
msgid "Import Tax Rate File"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.actions.act_window,name:l10n_ch_hr_payroll.l10n_ch_tax_rate_import_wizard_action
#: model:ir.ui.menu,name:l10n_ch_hr_payroll.menu_l10n_ch_tax_rate_import_wizard
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_tax_rate_import_wizard_view_form
msgid "Import Tax Rates"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_perfecting
msgid "Improvement"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule.category,name:l10n_ch_hr_payroll.hr_salary_rule_category_indemnite_perte_gain
msgid "Indemnite Perte de Gain"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_indemnity_ai
msgid "Indemnity AI"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_indemnity_apg
msgid "Indemnity APG"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_indemnite_chomage
msgid "Indemnité de chômage"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.actions.report,name:l10n_ch_hr_payroll.action_report_individual_account
msgid "Individual Account"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.actions.act_window,name:l10n_ch_hr_payroll.l10n_ch_individual_account_action
#: model:ir.ui.menu,name:l10n_ch_hr_payroll.menu_l10n_ch_individual_account
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_individual_account_view_form
msgid "Individual Accounts"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/l10n_ch_individual_account.py:0
msgid "Individual Accounts - Year %s"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_insurance_report_view_form
msgid "Institutions"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_accident_insurance_line__insurance_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_additional_accident_insurance_line__insurance_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_compensation_fund_line__insurance_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_sickness_insurance_line__insurance_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance_ac_line__insurance_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance_avs_ac_threshold__insurance_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance_avs_acc_threshold__insurance_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance_avs_line__insurance_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance_avs_retirement_rente__insurance_id
msgid "Insurance"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_accident_insurance__insurance_code
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_additional_accident_insurance__insurance_code
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_compensation_fund__insurance_code
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_lpp_insurance__insurance_code
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_sickness_insurance__insurance_code
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance__insurance_code
msgid "Insurance Code"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_accident_insurance__insurance_company
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_additional_accident_insurance__insurance_company
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_compensation_fund__insurance_company
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_lpp_insurance__insurance_company
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_sickness_insurance__insurance_company
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance__insurance_company
msgid "Insurance Company"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_accident_insurance__insurance_company_address_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_additional_accident_insurance__insurance_company_address_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_lpp_insurance__insurance_company_address_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_sickness_insurance__insurance_company_address_id
msgid "Insurance Company Address"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model,name:l10n_ch_hr_payroll.model_ch_yearly_report_line
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_insurance_report_view_tree
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_is_report_view_tree
msgid "Insurance Reports"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.actions.report,name:l10n_ch_hr_payroll.action_insurance_yearly_report
msgid "Insurance Yearly Report PDF"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.hr_contract_view_form
msgid "Insurances"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_accident_insurance_view_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_additional_accident_insurance_view_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_sickness_insurance_view_form
msgid "Insurer Information"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.contract.type,name:l10n_ch_hr_payroll.l10n_ch_contract_type_internshipContract
msgid "Internship contract"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/hr_employee.py:0
msgid "Invalid marital status for employee %s"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_payslip_is_log_line__is_code
msgid "Is Code"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_payslip_is_log_line__is_correction
msgid "Is Correction"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.actions.report,name:l10n_ch_hr_payroll.action_is_report
msgid "Is Report PDF"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.report_individual_account
msgid "JAN"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.report_individual_account
msgid "JUL"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.report_individual_account
msgid "JUN"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_is_report__month__1
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_monthly_summary__month__1
msgid "January"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_religious_denomination__jewishcommunity
msgid "Jewish Community"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_contract__l10n_ch_job_type
msgid "Job Type"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_is_report__month__7
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_monthly_summary__month__7
msgid "July"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_is_report__month__6
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_monthly_summary__month__6
msgid "June"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_canton__ju
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_spouse_work_canton__ju
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_location_unit__canton__ju
msgid "Jura"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:hr.salary.rule,note:l10n_ch_hr_payroll.l10n_ch_employees_withholding_tax
msgid ""
"Kept tax in the source\n"
"See: https://www.estv.admin.ch/estv/fr/accueil/impot-federal-direct/impot-a-"
"la-source/baremes-cantonaux.html"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance__l10n_ch_avs_ac_threshold_ids
msgid "L10N Ch Avs Ac Threshold"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance__l10n_ch_avs_acc_threshold_ids
msgid "L10N Ch Avs Acc Threshold"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance__l10n_ch_avs_rente_ids
msgid "L10N Ch Avs Rente"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_employee__l10n_ch_children
msgid "L10N Ch Children"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_payslip__l10n_ch_is_log_line_ids
msgid "L10N Ch Is Log Line"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_payslip__l10n_ch_lpp_not_insured
msgid "L10N Ch Lpp Not Insured"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_insurance_report_view_form
msgid "LAA"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/l10n_ch_insurance_report.py:0
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_laa_base
#: model:hr.salary.rule.category,name:l10n_ch_hr_payroll.hr_salary_rule_category_laa_base
msgid "LAA Base"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/l10n_ch_insurance_report.py:0
msgid "LAA Code"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/l10n_ch_insurance_report.py:0
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_laa_salary_2
#: model:hr.salary.rule.category,name:l10n_ch_hr_payroll.hr_salary_rule_category_laa_salary
msgid "LAA Salary"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_laa_salary_max
#: model:hr.salary.rule.category,name:l10n_ch_hr_payroll.hr_salary_rule_category_laa_threshold
msgid "LAA Salary Maximum"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_accident_insurance_view_form
msgid "LAA Solution"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__ch_yearly_report_line__report_type__laa
msgid "LAA Statement"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance__laa_insurance_from
msgid "LAA: Valid as of"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_insurance_report_view_form
msgid "LAAC"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/l10n_ch_insurance_report.py:0
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_laac_base
#: model:hr.salary.rule.category,name:l10n_ch_hr_payroll.hr_salary_rule_category_laac_base
msgid "LAAC Base"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/l10n_ch_insurance_report.py:0
msgid "LAAC Code"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_salary_rule__l10n_ch_laac_included
msgid "LAAC Included"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_contract__l10n_ch_additional_accident_insurance_line_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_payslip__l10n_ch_additional_accident_insurance_line_ids
#: model:ir.ui.menu,name:l10n_ch_hr_payroll.menu_l10n_ch_additional_accident_insurance
msgid "LAAC Insurances"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/l10n_ch_insurance_report.py:0
#: model:hr.salary.rule.category,name:l10n_ch_hr_payroll.hr_salary_rule_category_laac_salary
msgid "LAAC Salary"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_laac_salary_1
#: model:hr.salary.rule.category,name:l10n_ch_hr_payroll.hr_salary_rule_category_laac_salary_1
msgid "LAAC Salary Main Insurance"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_laac_salary_max_1
#: model:hr.salary.rule.category,name:l10n_ch_hr_payroll.hr_salary_rule_category_laac_salary_max_1
msgid "LAAC Salary Maximum Main Insurance"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_laac_salary_max_2
#: model:hr.salary.rule.category,name:l10n_ch_hr_payroll.hr_salary_rule_category_laac_salary_max_2
msgid "LAAC Salary Maximum Second Insurance"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_laac_salary_min_1
#: model:hr.salary.rule.category,name:l10n_ch_hr_payroll.hr_salary_rule_category_laac_salary_min_1
msgid "LAAC Salary Minimum Main Insurance"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_laac_salary_min_2
#: model:hr.salary.rule.category,name:l10n_ch_hr_payroll.hr_salary_rule_category_laac_salary_min_2
msgid "LAAC Salary Minimum Second Insurance"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_laac_salary_2
#: model:hr.salary.rule.category,name:l10n_ch_hr_payroll.hr_salary_rule_category_laac_salary_2
msgid "LAAC Salary Second Insurance"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_additional_accident_insurance_view_form
msgid "LAAC Solution"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__ch_yearly_report_line__report_type__laac
msgid "LAAC Statement"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_laac
msgid "LAAC1"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_laac_comp_1
msgid "LAAC1 (Comp. Part.)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_laac_2
msgid "LAAC2"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_laac_comp_2
msgid "LAAC2 (Comp. Part.)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_lpp
msgid "LPP"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_lpp_comp
msgid "LPP (Comp. Part.)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_salary_rule__l10n_ch_lpp_factor
msgid "LPP Factor"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_salary_rule__l10n_ch_lpp_forecast
msgid "LPP Forecast"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_contract__l10n_ch_lpp_insurance_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_payslip__l10n_ch_lpp_insurance_id
msgid "LPP Insurance"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.actions.act_window,name:l10n_ch_hr_payroll.action_l10n_ch_lpp_insurance
#: model:ir.ui.menu,name:l10n_ch_hr_payroll.menu_l10n_ch_lpp_insurance
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_lpp_insurance_view_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_lpp_insurance_view_tree
msgid "LPP Insurances"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_salary_rule__l10n_ch_lpp_retroactive
msgid "LPP Retroactive"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_lpp_redemption
msgid "LPP: Redemption"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance__lpp_insurance_from
msgid "LPP: Valid as of"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_ch_yearly_report__laa_institution_ids
msgid "Laa Institution"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_ch_yearly_report__laac_institution_ids
msgid "Laac Institution"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_hr_payroll_employee_lang_wizard_line__lang
msgid "Language"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_ch_yearly_report__write_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_ch_yearly_report_line__write_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_employee_is_line__write_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_payslip_is_log_line__write_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_accident_insurance__write_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_accident_insurance_line__write_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_accident_insurance_line_rate__write_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_additional_accident_insurance__write_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_additional_accident_insurance_line__write_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_additional_accident_insurance_line_rate__write_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_compensation_fund__write_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_compensation_fund_line__write_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_hr_employee_children__write_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_hr_payroll_employee_lang_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_hr_payroll_employee_lang_wizard_line__write_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_individual_account__write_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_is_report__write_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_is_report_line__write_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_location_unit__write_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_lpp_insurance__write_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_monthly_summary__write_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_salary_certificate__write_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_sickness_insurance__write_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_sickness_insurance_line__write_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_sickness_insurance_line_rate__write_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance__write_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance_ac_line__write_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance_avs_ac_threshold__write_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance_avs_acc_threshold__write_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance_avs_line__write_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance_avs_retirement_rente__write_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_tax_rate_import_wizard__write_uid
msgid "Last Updated by"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_ch_yearly_report__write_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_ch_yearly_report_line__write_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_employee_is_line__write_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_payslip_is_log_line__write_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_accident_insurance__write_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_accident_insurance_line__write_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_accident_insurance_line_rate__write_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_additional_accident_insurance__write_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_additional_accident_insurance_line__write_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_additional_accident_insurance_line_rate__write_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_compensation_fund__write_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_compensation_fund_line__write_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_hr_employee_children__write_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_hr_payroll_employee_lang_wizard__write_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_hr_payroll_employee_lang_wizard_line__write_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_individual_account__write_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_is_report__write_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_is_report_line__write_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_location_unit__write_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_lpp_insurance__write_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_monthly_summary__write_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_salary_certificate__write_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_sickness_insurance__write_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_sickness_insurance_line__write_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_sickness_insurance_line_rate__write_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance__write_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance_ac_line__write_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance_avs_ac_threshold__write_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance_avs_acc_threshold__write_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance_avs_line__write_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance_avs_retirement_rente__write_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_tax_rate_import_wizard__write_date
msgid "Last Updated on"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_contract__l10n_ch_lesson_wage
msgid "Lesson Wage"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_accident_insurance__line_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_accident_insurance_line_rate__line_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_additional_accident_insurance__line_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_additional_accident_insurance_line_rate__line_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_sickness_insurance__line_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_sickness_insurance_line_rate__line_id
msgid "Line"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_hr_payroll_employee_lang_wizard__line_ids
msgid "Lines"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_individual_account__lines_count
msgid "Lines Count"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_contract__l10n_ch_location_unit_id
msgid "Location Unit"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_contract__l10n_ch_job_type__lowercadre
msgid "Lower Management"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_canton__lu
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_spouse_work_canton__lu
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_location_unit__canton__lu
msgid "Lucerne"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_lunch_expense
msgid "Lunch Expense"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/l10n_ch_insurance_report.py:0
msgid "M/F"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.report_individual_account
msgid "MAR"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.report_individual_account
msgid "MAY"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:hr.salary.rule,note:l10n_ch_hr_payroll.l10n_ch_employees_laac
msgid "Main Additional Accident Insurance (LAAC)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_additional_accident_insurance_line_rate__male_rate
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_sickness_insurance_line_rate__male_rate
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_accident_insurance_line_view_form
msgid "Male Rate (%)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_is_report__month__3
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_monthly_summary__month__3
msgid "March"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_employee__marital
msgid "Marital Status"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_employee__l10n_ch_marital_from
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_res_users__l10n_ch_marital_from
msgid "Marital Status Start Date"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_marriage_allowance
msgid "Marriage Allowance"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_indemnity_maternity
msgid "Maternity Indemnity"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_is_report__month__5
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_monthly_summary__month__5
msgid "May"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_compensation_fund__member_number
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance__member_number
msgid "Member Number"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_compensation_fund__member_subnumber
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance__member_subnumber
msgid "Member Subnumber"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.contract.type,name:l10n_ch_hr_payroll.l10n_ch_contract_type_administrativeBoard
msgid "Member of a board of directors"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_contract__l10n_ch_job_type__middlecadre
msgid "Middle Management"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_military_wage
msgid "Military compensation benefit (CCM)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_is_report__month
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_monthly_summary__month
msgid "Month"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_contract__l10n_ch_is_model__monthly
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_payslip__l10n_ch_is_model__monthly
msgid "Monthly"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_contract__l10n_ch_monthly_effective_days
msgid "Monthly Effective Working Days"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.actions.act_window,name:l10n_ch_hr_payroll.l10n_ch_is_report_action
msgid "Monthly IS Report"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_salary_rule__l10n_ch_wage_statement
msgid "Monthly Statistics"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.actions.act_window,name:l10n_ch_hr_payroll.l10n_ch_monthly_summary_action
#: model:ir.ui.menu,name:l10n_ch_hr_payroll.menu_l10n_ch_monthly_summary
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_monthly_summary_view_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_monthly_summary_view_tree
msgid "Monthly Summary"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_monthly_summary__monthly_summary_pdf_file
msgid "Monthly Summary PDF"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_monthly_summary__monthly_summary_pdf_filename
msgid "Monthly Summary Pdf Filename"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_monthly_summary__monthly_summary_xls_file
msgid "Monthly Summary XLS"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_monthly_summary__monthly_summary_xls_filename
msgid "Monthly Summary Xls Filename"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.ui.menu,name:l10n_ch_hr_payroll.menu_l10n_ch_is_report
msgid "Monthly Tax-at-Source Report"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.report_monthly_summary
msgid "Monthly Wage Type Summary."
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_employee__l10n_ch_municipality
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_location_unit__municipality
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_res_users__l10n_ch_municipality
msgid "Municipality ID"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.report_individual_account
msgid "NOV"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/l10n_ch_insurance_report.py:0
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_ch_yearly_report__name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_accident_insurance__name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_additional_accident_insurance__name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_compensation_fund__name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_is_report__name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_lpp_insurance__name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_sickness_insurance__name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance__name
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.report_individual_account
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.report_monthly_summary
msgid "Name"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_residence_category__needforprotection-s
msgid "Need For Protection (Cat. S)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_canton__ne
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_spouse_work_canton__ne
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_location_unit__canton__ne
msgid "Neuchâtel"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_canton__nw
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_spouse_work_canton__nw
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_location_unit__canton__nw
msgid "Nidwald"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_night_allowance
msgid "Night Allowance"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_nightly_expense
msgid "Nightly Expense"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/l10n_ch_accident_insurance.py:0
msgid "No AANP rates found for date %s"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/l10n_ch_accident_insurance.py:0
msgid "No AAP rates found for date %s"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/l10n_ch_accident_insurance.py:0
msgid "No AAP/AANP threshold found for date %s"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/l10n_ch_social_insurance.py:0
msgid "No AC rates found for date %s"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/l10n_ch_social_insurance.py:0
msgid "No AC threshold rates found for date %s"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/l10n_ch_social_insurance.py:0
msgid "No ACC threshold rates found for date %s"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/l10n_ch_social_insurance.py:0
msgid "No AVS rates found for date %s"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/l10n_ch_compensation_fund.py:0
msgid "No CAF rates found for date %s"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/l10n_ch_sickness_insurance.py:0
msgid "No IJM rates found for date %s"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/l10n_ch_sickness_insurance.py:0
msgid "No IJM threshold found for date %s"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/l10n_ch_additional_accident_insurance.py:0
msgid "No LAAC rates found for date %s"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/l10n_ch_additional_accident_insurance.py:0
msgid "No LAAC threshold found for date %s"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/l10n_ch_accident_insurance.py:0
#: code:addons/l10n_ch_hr_payroll/models/l10n_ch_additional_accident_insurance.py:0
#: code:addons/l10n_ch_hr_payroll/models/l10n_ch_sickness_insurance.py:0
msgid "No found rate for gender %s"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/l10n_ch_social_insurance.py:0
msgid "No retirement exoneration amounts found for date date %s"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/hr_payslip.py:0
msgid ""
"No specified canton for employees:\n"
"%s"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/hr_payslip.py:0
msgid ""
"No specified tax scale for foreign employees:\n"
"%s"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/hr_rule_parameter.py:0
msgid ""
"No tax rates found for the employee canton. Make sure you've actually "
"imported using the wizard under Configuration -> Swiss -> Import Tax Rates"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_accident_insurance_line_view_form
msgid "Non-occupational"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_accident_insurance_line_rate__non_occupational_female_rate
msgid "Non-occupational Female Rate (%)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_accident_insurance_line_rate__non_occupational_male_rate
msgid "Non-occupational Male Rate (%)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_contract__l10n_ch_lpp_not_insured
msgid "Not LPP Insured"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_residence_category__notificationprocedureforshorttermwork120days
msgid "Notification Procedure for Short Term Work (120 days)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_residence_category__notificationprocedureforshorttermwork90days
msgid "Notification Procedure for Short Term Work (90 days)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_is_report__month__11
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_monthly_summary__month__11
msgid "November"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.report_individual_account
msgid "OCT"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_canton__ow
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_spouse_work_canton__ow
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_location_unit__canton__ow
msgid "Obwald"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_contract__l10n_ch_other_employers_occupation_rate
msgid "Occupation rate other employers"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_accident_insurance_line_view_form
msgid "Occupational"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_accident_insurance_line_rate__occupational_female_rate
msgid "Occupational Female Rate (%)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_accident_insurance_line_rate__occupational_male_rate
msgid "Occupational Male Rate (%)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_is_report__month__10
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_monthly_summary__month__10
msgid "October"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.res_config_settings_view_form
msgid "Offical Company Information"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:hr.salary.rule,note:l10n_ch_hr_payroll.l10n_ch_employees_avs
#: model_terms:hr.salary.rule,note:l10n_ch_hr_payroll.l10n_ch_employees_avs_comp
msgid ""
"Old Age &amp; Survivor's Insurance, Disability Insurance &amp; Loss of "
"Earnings"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_social_insurance_view_form
msgid "Old-age and survivors' insurance (AVS)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_on_call_alw
msgid "On-call service allowance"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_optional_aanp
msgid "Optional employer part AANP"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_optional_cm
msgid "Optional employer part CM"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_optional_ijm
msgid "Optional employer part IJM"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_optional_laac
msgid "Optional employer part LAAC"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_optional_lpp
msgid "Optional employer part LPP"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_optional_lpp_redemption
msgid "Optional employer part LPP Redemption"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_option_collab
msgid "Options de collaborateurs"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_residence_category__othersnotswiss
msgid "Other (Without Swiss)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_contract__l10n_ch_other_employers
msgid "Other Employers"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_other_expense
msgid "Other Expense"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_other_fees
msgid "Other Fees"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_religious_denomination__otherornone
msgid "Other or None"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_overtime_100
msgid "Overtime 100%"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_overtime_125
msgid "Overtime 125%"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_overtime_after_departure
msgid "Overtime after departure"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_individual_account__pdf_error
msgid "PDF Error Message"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_ch_yearly_report_line__pdf_file
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_is_report_line__pdf_file
msgid "PDF File"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_ch_yearly_report_line__pdf_filename
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_is_report_line__pdf_filename
msgid "PDF Filename"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/hr_employee.py:0
msgid "Partnership Dissolved By Death"
msgstr "Unione domestica sciolta per decesso"

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/hr_employee.py:0
msgid "Partnership Dissolved By Declaration of Lost"
msgstr "Unione domestica sciolta a seguito di una dichiarazione di assenza"

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/hr_employee.py:0
msgid "Partnership Dissolved By Law"
msgstr "Unione domestica sciolta giuridicamente"

#. module: l10n_ch_hr_payroll
#: model:ir.model,name:l10n_ch_hr_payroll.model_hr_payslip
msgid "Pay Slip"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_payslip__l10n_ch_pay_13th_month
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_payslip_run__l10n_ch_pay_13th_month
msgid "Pay Thirteen Month"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_contract__l10n_ch_has_withholding_tax
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_employee__l10n_ch_has_withholding_tax
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_res_users__l10n_ch_has_withholding_tax
msgid "Pay Withholding Taxes"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_payslip_is_log_line__payslip_id
msgid "Payslip"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model,name:l10n_ch_hr_payroll.model_hr_payslip_run
msgid "Payslip Batches"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_employee_is_line__payslips_to_correct
msgid "Payslips To Correct"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:hr.salary.rule,note:l10n_ch_hr_payroll.l10n_ch_employees_lpp
#: model_terms:hr.salary.rule,note:l10n_ch_hr_payroll.l10n_ch_employees_lpp_comp
msgid ""
"Pension\n"
"Source:\n"
"-https://hellosafe.ch/prevoyance/lpp/cotisation\n"
"-https://finpension.ch/fr/glossaire/salaire-coordonne/"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.contract.type,name:l10n_ch_hr_payroll.l10n_ch_contract_type_indefiniteSalaryMth
msgid "Permanent contract with monthly salary"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.contract.type,name:l10n_ch_hr_payroll.l10n_ch_contract_type_indefiniteSalaryMthAWT
msgid "Permanent contract with monthly salary and annualized working time"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.contract.type,name:l10n_ch_hr_payroll.l10n_ch_contract_type_indefiniteSalaryHrs
msgid "Permanent contract with salary hourly"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.contract.type,name:l10n_ch_hr_payroll.l10n_ch_contract_type_indefiniteSalaryNoTimeConstraint
msgid "Permanent contract, with commission, packages, piece rate pay, etc."
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_indemnite_perte_gain
msgid "Perte de gain RHT/ITP (SH)"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/wizard/l10n_ch_tax_rate_import.py:0
msgid "Please upload a tax file first."
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_individual_account_view_form
msgid "Populate"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_res_company__l10n_ch_post_box
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_res_config_settings__l10n_ch_post_box
msgid "Post Box"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_previous_year_bonus
msgid "Previous Year Bonus"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_professional_training_allowance
msgid "Professional Training Allowance"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_residence_category__provisionallyadmittedforeigners-f
msgid "Provisionally Admitted Foreigners (Cat. F)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.work.entry.type,name:l10n_ch_hr_payroll.work_entry_type_bank_holiday
msgid "Public Holiday"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_public_holiday_allowance
msgid "Public holiday allowance"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_accident_insurance_line__rate_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_additional_accident_insurance_line__rate_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_sickness_insurance_line__rate_ids
msgid "Rate"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_employee_is_line__reason
msgid "Reason"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_monthly_summary_view_form
msgid "Reference Period"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_religious_denomination__reformedevangelical
msgid "Reformed Evangelical"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/hr_employee.py:0
msgid "Registered Partnership"
msgstr "Unione domestica registrata"

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_employee__l10n_ch_religious_denomination
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_res_users__l10n_ch_religious_denomination
msgid "Religious Denomination"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_rental_housing
msgid "Rental housing rent reduction"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_ch_yearly_report_line__report_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_is_report_line__report_id
msgid "Report"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_ch_yearly_report__report_line_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_is_report__report_line_ids
msgid "Report Line"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_ch_yearly_report_line__report_type
msgid "Report Type"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_representation_fees
msgid "Representation Fees"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_employee__l10n_ch_residence_category
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_res_users__l10n_ch_residence_category
msgid "Residence Category"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_residence_category__residentforeignnationalwithgainfulemployment-ci
msgid "Residence Permit with Gainful Employment (Ci)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_contract__l10n_ch_job_type__lowestcadre
msgid "Responsible for carrying out the work"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_restreint_private_share_service_car
msgid "Restraint Private share service car"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_contract__l10n_ch_avs_status__retired
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_payslip__l10n_ch_avs_status__retired
msgid "Retired"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_social_insurance_view_form
msgid "Retired Employees AVS/AC Exoneration"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_employee__l10n_ch_retirement_insurance_number
msgid "Retirement insurance number"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_religious_denomination__romancatholic
msgid "Roman Catholic"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.report_individual_account
msgid "SEP"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/l10n_ch_insurance_report.py:0
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_employee__l10n_ch_sv_as_number
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_res_users__l10n_ch_sv_as_number
msgid "SV-AS Number"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_canton__sg
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_spouse_work_canton__sg
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_location_unit__canton__sg
msgid "Saint-Gall"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_lpp_insurance_view_form
msgid "Salary Attachments"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model,name:l10n_ch_hr_payroll.model_l10n_ch_salary_certificate
msgid "Salary Certificate By Employee"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.actions.act_window,name:l10n_ch_hr_payroll.l10n_ch_salary_certificate_action
#: model:ir.ui.menu,name:l10n_ch_hr_payroll.menu_l10n_ch_salary_certificate
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_be_individual_account_view_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_salary_certificate_view_tree
msgid "Salary Certificates"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/l10n_ch_salary_certificate_report.py:0
msgid "Salary Certificates - Year %s"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_basic_correction
msgid "Salary Correction"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.hr_contract_view_form
msgid "Salary Information"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model,name:l10n_ch_hr_payroll.model_hr_salary_rule
msgid "Salary Rule"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model,name:l10n_ch_hr_payroll.model_hr_rule_parameter
msgid "Salary Rule Parameter"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_salary_rule__l10n_ch_salary_certificate
msgid "Salary certificate"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_accident_wage
msgid "Salary in case of accident"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_sick_wage
msgid "Salary in case of illness"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_basic_lesson
msgid "Salary per Lesson"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_tax_scale__l
msgid ""
"Scale for German cross-border commuters who fulfill the conditions of the "
"scale A"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_tax_scale__m
msgid ""
"Scale for German cross-border commuters who fulfill the conditions of the "
"scale B"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_tax_scale__n
msgid ""
"Scale for German cross-border commuters who fulfill the conditions of the "
"scale C"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_tax_scale__q
msgid ""
"Scale for German cross-border commuters who fulfill the conditions of the "
"scale G"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_tax_scale__p
msgid ""
"Scale for German cross-border commuters who fulfill the conditions of the "
"scale H"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_tax_scale__r
msgid ""
"Scale for Italian cross-border commuters who fulfill the conditions of the "
"scale A"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_tax_scale__s
msgid ""
"Scale for Italian cross-border commuters who fulfill the conditions of the "
"scale B"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_tax_scale__t
msgid ""
"Scale for Italian cross-border commuters who fulfill the conditions of the "
"scale C"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_tax_scale__u
msgid ""
"Scale for Italian cross-border commuters who fulfill the conditions of the "
"scale H"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_tax_scale__f
msgid ""
"Scale for Italian cross-border commuters whose spouse is working lucrative "
"outside Switzerland"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_tax_scale__g
msgid ""
"Scale for income acquired as compensation which is paid to persons subject "
"to withholding tax by a person other than that the employer"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_tax_scale__e
msgid "Scale for income taxed under the procedure of simplified count"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_tax_scale__b
msgid ""
"Scale for married couples living in a common household with only one spouse "
"is gainfully employed"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_tax_scale__c
msgid "Scale for married couples with two incomes"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_tax_scale__d
msgid "Scale for people whose AVS contributions are reimbursed"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_tax_scale__a
msgid "Scale for single people"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_tax_scale__h
msgid ""
"Scale for single people living together with children or needy persons whom "
"they take on maintenance essentials"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_canton__sh
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_spouse_work_canton__sh
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_location_unit__canton__sh
msgid "Schaffhouse"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_canton__sz
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_spouse_work_canton__sz
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_location_unit__canton__sz
msgid "Schwytz"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:hr.salary.rule,note:l10n_ch_hr_payroll.l10n_ch_employees_laac_2
msgid "Second Additional Accident Insurance (LAAC)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_jubilee_gift
msgid "Seniority Gift"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/hr_employee.py:0
msgid "Separated"
msgstr "Separato/a"

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_is_report__month__9
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_monthly_summary__month__9
msgid "September"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_residence_category__settled-c
msgid "Settled (Cat. C)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_sev_pay
msgid "Severance pay (subject to AVS)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_residence_category__shortterm-l
msgid "Short Term (Cat. L)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.actions.act_window,name:l10n_ch_hr_payroll.action_l10n_ch_sickness_insurance
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_sickness_insurance_view_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_sickness_insurance_view_tree
msgid "Sickness Insurances"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_sickness_insurance_line_view_form
msgid "Sickness Insurances Solutions"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_hr_payroll_employee_lang_wizard__slip_ids
msgid "Slip"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_social_insurance_view_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_social_insurance_view_tree
msgid "Social Insurances"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_canton__so
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_spouse_work_canton__so
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_location_unit__canton__so
msgid "Soleure"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_accident_insurance_line__solution_code
msgid "Solution Code"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_accident_insurance_line__solution_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_additional_accident_insurance_line__solution_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_sickness_insurance_line__solution_name
msgid "Solution Name"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_accident_insurance_line__solution_number
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_additional_accident_insurance_line__solution_number
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_sickness_insurance_line__solution_number
msgid "Solution Number"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_accident_insurance_line__solution_type
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_additional_accident_insurance_line__solution_type
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_sickness_insurance_line__solution_type
msgid "Solution Type"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_salary_rule__l10n_ch_source_tax_included
msgid "Source Tax Included"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_special_indemnity
msgid "Special Indemnity"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_employee__l10n_ch_spouse_sv_as_number
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_res_users__l10n_ch_spouse_sv_as_number
msgid "Spouse SV-AS-Number"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_employee__l10n_ch_spouse_work_canton
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_res_users__l10n_ch_spouse_work_canton
msgid "Spouse Work Canton"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_employee__l10n_ch_spouse_work_start_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_res_users__l10n_ch_spouse_work_start_date
msgid "Spouse Work Start Date"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_monthly_summary__date_start
msgid "Start Period"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance__age_stop_male
msgid "Start of retirement age for men"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance__age_stop_female
msgid "Start of retirement age for women"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance__age_start
msgid "Start of the obligation to contribute to the AVS"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_sunday_allowance
msgid "Sunday Allowance"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_employee__l10n_ch_church_tax
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_res_users__l10n_ch_church_tax
msgid "Swiss Church Tax"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.hr_payslip_view_form_inherit
msgid "Swiss Insurances"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.res_config_settings_view_form
msgid "Swiss Localization"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model,name:l10n_ch_hr_payroll.model_l10n_ch_individual_account
msgid "Swiss Payroll: Individual Account"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model,name:l10n_ch_hr_payroll.model_l10n_ch_monthly_summary
msgid "Swiss Payroll: Monthly Summary"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model,name:l10n_ch_hr_payroll.model_l10n_ch_tax_rate_import_wizard
msgid "Swiss Payroll: Tax rate import wizard"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_employee__l10n_ch_tax_scale
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_res_users__l10n_ch_tax_scale
msgid "Swiss Tax Scale"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model,name:l10n_ch_hr_payroll.model_l10n_ch_social_insurance_avs_ac_threshold
msgid "Swiss: AC: Rate Threshold"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model,name:l10n_ch_hr_payroll.model_l10n_ch_social_insurance_avs_acc_threshold
msgid "Swiss: ACC: Rate Threshold"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model,name:l10n_ch_hr_payroll.model_l10n_ch_additional_accident_insurance_line_rate
msgid "Swiss: Accident Additional Insurances Line Rate (LAAC)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model,name:l10n_ch_hr_payroll.model_l10n_ch_accident_insurance
msgid "Swiss: Accident Insurances (AAP/AANP)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model,name:l10n_ch_hr_payroll.model_l10n_ch_accident_insurance_line
msgid "Swiss: Accident Insurances Line (AAP/AANP)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model,name:l10n_ch_hr_payroll.model_l10n_ch_accident_insurance_line_rate
msgid "Swiss: Accident Insurances Line Rate (AAP/AANP)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model,name:l10n_ch_hr_payroll.model_l10n_ch_additional_accident_insurance
msgid "Swiss: Additional Accident Insurances (LAAC)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model,name:l10n_ch_hr_payroll.model_l10n_ch_additional_accident_insurance_line
msgid "Swiss: Additional Accident Insurances Line (LAAC)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__certificate__voceducationcompl
msgid ""
"Swiss: Complete learning attested by a federal certificate of capacity (CFC)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__certificate__mandatoryschoolonly
msgid "Swiss: Compulsory schooling, without full vocational training"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__certificate__doctorate
msgid "Swiss: Doctorate, habilitation"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model,name:l10n_ch_hr_payroll.model_l10n_ch_compensation_fund
msgid "Swiss: Family Allowance (CAF)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model,name:l10n_ch_hr_payroll.model_l10n_ch_compensation_fund_line
msgid "Swiss: Family Allowance Rate (CAF)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__certificate__highervoceducation
msgid "Swiss: Higher Vocational Education"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__certificate__highervoceducationbachelor
msgid "Swiss: Higher Vocational Education Bachelor"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__certificate__highervoceducationmaster
msgid "Swiss: Higher Vocational Education Master"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__certificate__enterpriseeducation
msgid "Swiss: In-company training only"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model,name:l10n_ch_hr_payroll.model_l10n_ch_lpp_insurance
msgid "Swiss: LPP Insurances"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__certificate__universityentrancecertificate
msgid "Swiss: Matura"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model,name:l10n_ch_hr_payroll.model_l10n_ch_social_insurance_avs_retirement_rente
msgid "Swiss: Retired Employees Exoneration"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model,name:l10n_ch_hr_payroll.model_l10n_ch_sickness_insurance
msgid "Swiss: Sickness Insurances (IJM)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model,name:l10n_ch_hr_payroll.model_l10n_ch_sickness_insurance_line
msgid "Swiss: Sickness Insurances Line (IJM)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model,name:l10n_ch_hr_payroll.model_l10n_ch_sickness_insurance_line_rate
msgid "Swiss: Sickness Insurances Line Rate (IJM)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model,name:l10n_ch_hr_payroll.model_l10n_ch_social_insurance
msgid "Swiss: Social Insurances (AVS, AC)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model,name:l10n_ch_hr_payroll.model_l10n_ch_social_insurance_ac_line
msgid "Swiss: Social Insurances - AC Line"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model,name:l10n_ch_hr_payroll.model_l10n_ch_social_insurance_avs_line
msgid "Swiss: Social Insurances - AVS Line"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__certificate__teachercertificate
msgid "Swiss: Teaching certificate at different levels"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__certificate__universitybachelor
msgid "Swiss: University College Bachelor (university, ETH)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__certificate__universitymaster
msgid "Swiss: University College Master (university, ETH)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__certificate__highereducationbachelor
msgid "Swiss: University of Applied Sciences Bachelor"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__certificate__highereducationmaster
msgid "Swiss: University of Applied Sciences Master"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.ui.menu,name:l10n_ch_hr_payroll.menu_configuration_l10n_ch
#: model:ir.ui.menu,name:l10n_ch_hr_payroll.menu_reporting_l10n_ch
msgid "Switzerland"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.actions.report,name:l10n_ch_hr_payroll.action_report_monthly_summary
msgid "Switzerland: Monthly Summary"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.actions.report,name:l10n_ch_hr_payroll.action_report_payslip_ch
msgid "Switzerland: Payslip"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.actions.report,name:l10n_ch_hr_payroll.action_report_light_payslip_ch
msgid "Switzerland: Payslip (Light)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_tax_rate_import_wizard__tax_file_ids
msgid "Tax Files"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model,name:l10n_ch_hr_payroll.model_l10n_ch_is_report
msgid "Tax at Source Monthly Report"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model,name:l10n_ch_hr_payroll.model_l10n_ch_is_report_line
msgid "Tax at Source Monthly Report Line"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_tax_part_fees
msgid "Taxable participation fees"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_team_work
msgid "Team Work"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_canton__ti
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_spouse_work_canton__ti
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_location_unit__canton__ti
msgid "Tessin"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,help:l10n_ch_hr_payroll.field_hr_employee__l10n_ch_retirement_insurance_number
msgid ""
"The Central Compensation Office in Geneva assigns a retirement insurance "
"(RI) number to all newborns and immigrants, which is valid for the rest of "
"your life and remains the same even after a name change. Further information "
"on the structure of the RI number can be found on theSwiss Federal Social "
"Insurance Office website (in Germnan)."
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_lpp_insurance_view_form
msgid ""
"The LPP amounts to pay (by the employee or the employer) are to be defined "
"by hand from the"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/hr_employee.py:0
msgid ""
"The SV-AS number should be a thirteen-digit number, comma-separated (eg: "
"756.1848.4786.64)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_individual_account_view_form
msgid ""
"The files won't be posted in the employee portal if you don't have the "
"Documents app."
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_hr_payroll_employee_lang_view_form
msgid ""
"The following employees have an invalid language for the selected salary "
"structure.\n"
"                        <br/>\n"
"                        Please assign them a language below before "
"continuing."
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/wizard/l10n_ch_tax_rate_import.py:0
msgid "The tax file has been successfully imported."
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/l10n_ch_monthly_summary.py:0
msgid "There is no paid or done payslips over the selected period."
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_third_pill_a
msgid "Third pillar A paid by employer"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_third_pill_b
msgid "Third pillar B paid by employer"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_thirteen_month
msgid "Thirteen Month"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_thirteen_month_provision
msgid "Thirteen Month Provision"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,help:l10n_ch_hr_payroll.field_hr_employee__l10n_ch_sv_as_number
#: model:ir.model.fields,help:l10n_ch_hr_payroll.field_res_users__l10n_ch_sv_as_number
msgid ""
"Thirteen-digit AS number assigned by the Central Compensation Office (CdC)"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/hr_payslip.py:0
msgid "This document is a translation. This is not a legal document."
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_accident_insurance_line_rate__threshold
msgid "Threshold"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_canton__tg
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_spouse_work_canton__tg
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_location_unit__canton__tg
msgid "Thurgovie"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/l10n_ch_insurance_report.py:0
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_accident_insurance_line_rate__date_to
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_additional_accident_insurance_line_rate__date_to
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_compensation_fund_line__date_to
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_sickness_insurance_line_rate__date_to
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance_ac_line__date_to
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance_avs_ac_threshold__date_to
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance_avs_acc_threshold__date_to
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance_avs_line__date_to
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_social_insurance_avs_retirement_rente__date_to
msgid "To"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_contract__l10n_ch_job_type__highestcadre
msgid "Top Management"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.report_individual_account
msgid "Total Year"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_contract__l10n_ch_total_occupation_rate
msgid "Total occupation rate"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_travel_expense
msgid "Travel Expense"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:hr.salary.rule,note:l10n_ch_hr_payroll.l10n_ch_employees_ac
#: model_terms:hr.salary.rule,note:l10n_ch_hr_payroll.l10n_ch_employees_ac_comp
msgid ""
"Unemployment Insurance\n"
"Source: https://www.cvcicaisseavs.ch/en/employer/unemployment-insurance-ac."
"html"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_social_insurance_view_form
msgid "Unemployment insurance (AC)"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/wizard/l10n_ch_tax_rate_import.py:0
msgid "Unmanaged transaction type 02: %(line)"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/wizard/l10n_ch_tax_rate_import.py:0
msgid "Unmanaged transaction type 03: %(line)"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/wizard/l10n_ch_tax_rate_import.py:0
msgid "Unrecognized canton code %(canton): %(line)"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/wizard/l10n_ch_tax_rate_import.py:0
msgid "Unrecognized line format: %s"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/wizard/l10n_ch_tax_rate_import.py:0
msgid "Unrecognized tax scale %(tax_scale): %(line)"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/wizard/l10n_ch_tax_rate_import.py:0
msgid "Unrecognized transaction type %(t_type): %(line)"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_canton__ur
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_spouse_work_canton__ur
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_location_unit__canton__ur
msgid "Uri"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model,name:l10n_ch_hr_payroll.model_res_users
msgid "User"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_vacation_payment
msgid "Vacation Payment"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_canton__vs
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_spouse_work_canton__vs
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_location_unit__canton__vs
msgid "Valais"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_employee_is_line__valid_as_of
msgid "Valid As Of"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_canton__vd
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_spouse_work_canton__vd
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_location_unit__canton__vd
msgid "Vaud"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_additional_accident_insurance_line_rate__wage_from
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_sickness_insurance_line_rate__wage_from
msgid "Wage From"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_additional_accident_insurance_line_rate__wage_to
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_sickness_insurance_line_rate__wage_to
msgid "Wage To"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.actions.act_window,name:l10n_ch_hr_payroll.action_hr_salary_rule_l10n_ch_wage_types
#: model:ir.ui.menu,name:l10n_ch_hr_payroll.menu_l10n_ch_wage_types
msgid "Wage Types"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_location_unit__weekly_hours
msgid "Weekly Hours"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_location_unit__weekly_lessons
msgid "Weekly Lessons"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,help:l10n_ch_hr_payroll.field_hr_salary_rule__l10n_ch_aanp_included
msgid ""
"Whether the amount is included in the basis to compute the accident "
"insurance deduction"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,help:l10n_ch_hr_payroll.field_hr_salary_rule__l10n_ch_laac_included
msgid ""
"Whether the amount is included in the basis to compute the additional "
"accident insurance deduction"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,help:l10n_ch_hr_payroll.field_hr_salary_rule__l10n_ch_ijm_included
#: model:ir.model.fields,help:l10n_ch_hr_payroll.field_hr_salary_rule__l10n_ch_source_tax_included
msgid ""
"Whether the amount is included in the basis to compute the daily sick pay "
"deduction"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,help:l10n_ch_hr_payroll.field_hr_salary_rule__l10n_ch_ac_included
msgid ""
"Whether the amount is included in the basis to compute the retirement/"
"unemployement deduction"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.hr_employee_view_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.res_users_view_form
msgid "Withholding Taxes"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_contract__l10n_ch_job_type__nocadre
msgid "Without management function"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_hr_payroll_employee_lang_wizard_line__wizard_id
msgid "Wizard"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_is_report__work_location_ids
msgid "Work Location"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.ui.menu,name:l10n_ch_hr_payroll.menu_l10n_ch_location_unit
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_is_report_view_form
msgid "Work Locations"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model,name:l10n_ch_hr_payroll.model_l10n_ch_location_unit
msgid "Work Place - Swiss Payroll"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_employee_is_line_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_location_unit_view_form
msgid "Work address"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.actions.act_window,name:l10n_ch_hr_payroll.action_l10n_ch_location_unit
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_location_unit_view_tree
msgid "Work addresses"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_worked_days
#: model:hr.salary.rule.category,name:l10n_ch_hr_payroll.hr_salary_rule_category_worked_days
msgid "Worked Days Total"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:hr.salary.rule,name:l10n_ch_hr_payroll.l10n_ch_employees_ch_days
msgid "Worked Days in CH"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_location_unit__partner_id
msgid "Working Address"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_salary_certificate__xml_file
msgid "Xml File"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_salary_certificate__xml_filename
msgid "Xml Filename"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_ch_yearly_report__year
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_individual_account__year
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_is_report__year
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_monthly_summary__year
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_l10n_ch_salary_certificate__year
msgid "Year"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_contract__l10n_ch_is_model__yearly
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_payslip__l10n_ch_is_model__yearly
msgid "Yearly"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_contract__l10n_ch_yearly_holidays
msgid "Yearly Holidays Count"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.actions.act_window,name:l10n_ch_hr_payroll.l10n_ch_insurance_report_action
#: model:ir.ui.menu,name:l10n_ch_hr_payroll.menu_l10n_ch_insurance_report
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_insurance_report_view_form
msgid "Yearly Institution Reports"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_contract__l10n_ch_yearly_paid_public_holidays
msgid "Yearly Paid Public Holidays Count"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll.field_hr_salary_rule__l10n_ch_yearly_statement
msgid "Yearly Statistics"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/models/l10n_ch_insurance_report.py:0
#: code:addons/l10n_ch_hr_payroll/models/l10n_ch_monthly_summary.py:0
#: code:addons/l10n_ch_hr_payroll/models/l10n_ch_salary_certificate_report.py:0
#: code:addons/l10n_ch_hr_payroll/models/l10n_ch_tax_at_source_report.py:0
msgid "You must be logged in a Swiss company to use this feature"
msgstr ""

#. module: l10n_ch_hr_payroll
#. odoo-python
#: code:addons/l10n_ch_hr_payroll/wizard/l10n_ch_tax_rate_import.py:0
msgid "You must be logged into a Swiss company to use this feature"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_contract__l10n_ch_avs_status__youth
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_payslip__l10n_ch_avs_status__youth
msgid "Youth"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_canton__zg
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_spouse_work_canton__zg
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_location_unit__canton__zg
msgid "Zoug"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_canton__zh
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__hr_employee__l10n_ch_spouse_work_canton__zh
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll.selection__l10n_ch_location_unit__canton__zh
msgid "Zurich"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_accident_insurance_view_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_additional_accident_insurance_view_form
msgid "e.g. \"Accident Insurance Gastrosocial\""
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_compensation_fund_view_form
msgid "e.g. \"Family Allowance AK Bern\""
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_lpp_insurance_view_form
msgid "e.g. \"LPP Insurance AK Bern\""
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_sickness_insurance_view_form
msgid "e.g. \"Sickness Insurance Gastrosocial\""
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_social_insurance_view_form
msgid "e.g. \"Social Insurance AK Bern\""
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_is_report_view_form
msgid "e.g. 12/2024 Monthly Report"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_insurance_report_view_form
msgid "e.g. 2024 Yearly Report"
msgstr ""

#. module: l10n_ch_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll.l10n_ch_monthly_summary_view_form
msgid "to"
msgstr ""
