# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* partner_commission
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON> <george_ta<PERSON><PERSON><PERSON>@yahoo.com>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:27+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Greek (https://app.transifex.com/odoo/teams/41243/el/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: el\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: partner_commission
#. odoo-python
#: code:addons/partner_commission/models/account_move.py:0
msgid ""
"\n"
"%(product)s: from %(start_date)s to %(end_date)s"
msgstr ""

#. module: partner_commission
#. odoo-python
#: code:addons/partner_commission/models/account_move.py:0
msgid " (%d month(s))"
msgstr ""

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_plan__active
msgid "Active"
msgstr "Σε Ισχύ"

#. module: partner_commission
#: model:res.groups,name:partner_commission.group_commission_manager
msgid "All Documents"
msgstr ""

#. module: partner_commission
#: model_terms:ir.ui.view,arch_db:partner_commission.commission_plan_search_view
msgid "Archived"
msgstr "Αρχειοθετημένα"

#. module: partner_commission
#: model_terms:ir.ui.view,arch_db:partner_commission.res_config_settings_view_form
msgid "Automatic PO frequency"
msgstr ""

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_rule__is_capped
msgid "Capped"
msgstr ""

#. module: partner_commission
#: model:ir.model.fields.selection,name:partner_commission.selection__purchase_order__purchase_type__commission
#: model:product.template,name:partner_commission.product_commission_product_template
msgid "Commission"
msgstr ""

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_res_company__commission_automatic_po_frequency
#: model:ir.model.fields,field_description:partner_commission.field_res_config_settings__commission_automatic_po_frequency
msgid "Commission Automatic Po Frequency"
msgstr ""

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_rule__plan_id
#: model:ir.model.fields,field_description:partner_commission.field_res_partner__commission_plan_id
#: model:ir.model.fields,field_description:partner_commission.field_res_users__commission_plan_id
#: model:ir.model.fields,field_description:partner_commission.field_sale_order__commission_plan_id
#: model:ir.model.fields,field_description:partner_commission.field_sale_order_log_report__commission_plan_id
#: model:ir.model.fields,field_description:partner_commission.field_sale_report__commission_plan_id
#: model:ir.model.fields,field_description:partner_commission.field_sale_subscription_report__commission_plan_id
msgid "Commission Plan"
msgstr ""

#. module: partner_commission
#: model:ir.actions.act_window,name:partner_commission.action_commission_plans
#: model:ir.ui.menu,name:partner_commission.menu_commission_plans
msgid "Commission Plans"
msgstr ""

#. module: partner_commission
#. odoo-python
#: code:addons/partner_commission/models/account_move.py:0
msgid "Commission on %(invoice)s, %(partner)s, %(amount)s"
msgstr ""

#. module: partner_commission
#: model:ir.model,name:partner_commission.model_commission_plan
msgid "Commission plan"
msgstr ""

#. module: partner_commission
#. odoo-python
#: code:addons/partner_commission/models/account_move.py:0
msgid "Commission refunded. Invoice: %(link)s. Amount: %(amount)s."
msgstr ""

#. module: partner_commission
#: model:ir.model,name:partner_commission.model_commission_rule
msgid "Commission rules management."
msgstr ""

#. module: partner_commission
#: model:ir.model,name:partner_commission.model_res_company
msgid "Companies"
msgstr "Εταιρίες"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_plan__company_id
msgid "Company"
msgstr "Εταιρία"

#. module: partner_commission
#: model:ir.model,name:partner_commission.model_res_config_settings
msgid "Config Settings"
msgstr "Ρυθμίσεις διαμόρφωσης"

#. module: partner_commission
#: model:ir.model,name:partner_commission.model_res_partner
msgid "Contact"
msgstr "Επαφή"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_plan__create_uid
#: model:ir.model.fields,field_description:partner_commission.field_commission_rule__create_uid
msgid "Created by"
msgstr "Δημιουργήθηκε από"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_plan__create_date
#: model:ir.model.fields,field_description:partner_commission.field_commission_rule__create_date
msgid "Created on"
msgstr "Δημιουργήθηκε στις"

#. module: partner_commission
#: model:ir.actions.act_window,name:partner_commission.action_view_customer_invoices
msgid "Customer Invoices"
msgstr "Τιμολόγια Πελάτη"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_res_partner_grade__default_commission_plan_id
msgid "Default Commission Plan"
msgstr ""

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_plan__display_name
#: model:ir.model.fields,field_description:partner_commission.field_commission_rule__display_name
msgid "Display Name"
msgstr "Εμφάνιση Ονόματος"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_sale_order__commission_plan_frozen
msgid "Freeze Plan"
msgstr ""

#. module: partner_commission
#: model_terms:ir.ui.view,arch_db:partner_commission.res_config_settings_view_form
msgid "Frequency at which purchase orders will be automatically confirmed"
msgstr ""

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_plan__id
#: model:ir.model.fields,field_description:partner_commission.field_commission_rule__id
msgid "ID"
msgstr "Κωδικός"

#. module: partner_commission
#: model:ir.model.fields,help:partner_commission.field_commission_rule__product_id
msgid ""
"If set, the rule does not apply to the whole category but only on the given product.\n"
"The product must belong to the selected category.\n"
"Use several rules if you need to match multiple products within a category."
msgstr ""

#. module: partner_commission
#: model:ir.model.fields,help:partner_commission.field_purchase_order__invoice_commission_count
msgid "Invoices that have generated commissions included in this order"
msgstr ""

#. module: partner_commission
#: model:ir.model,name:partner_commission.model_account_move
msgid "Journal Entry"
msgstr "Εγγραφή Ημερολογίου"

#. module: partner_commission
#: model:ir.model,name:partner_commission.model_account_move_line
msgid "Journal Item"
msgstr "Στοιχείο Ημερολογίου"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_plan__write_uid
#: model:ir.model.fields,field_description:partner_commission.field_commission_rule__write_uid
msgid "Last Updated by"
msgstr "Τελευταία Ενημέρωση από"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_plan__write_date
#: model:ir.model.fields,field_description:partner_commission.field_commission_rule__write_date
msgid "Last Updated on"
msgstr "Τελευταία Ενημέρωση στις"

#. module: partner_commission
#: model:ir.model,name:partner_commission.model_crm_lead
msgid "Lead/Opportunity"
msgstr "Σύσταση/Ευκαιρία"

#. module: partner_commission
#: model:ir.model.fields.selection,name:partner_commission.selection__res_company__commission_automatic_po_frequency__manually
msgid "Manually"
msgstr "Χειροκίνητα"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_rule__max_commission
msgid "Max Commission"
msgstr ""

#. module: partner_commission
#: model:ir.model.fields,help:partner_commission.field_commission_rule__max_commission
msgid "Maximum amount, specified in the currency of the pricelist, if given."
msgstr ""

#. module: partner_commission
#: model_terms:ir.ui.view,arch_db:partner_commission.res_config_settings_view_form
msgid "Minimum PO amount total"
msgstr ""

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_res_company__commission_po_minimum
#: model:ir.model.fields,field_description:partner_commission.field_res_config_settings__commission_po_minimum
msgid "Minimum Total Amount for PO commission"
msgstr ""

#. module: partner_commission
#: model:ir.model.fields.selection,name:partner_commission.selection__res_company__commission_automatic_po_frequency__monthly
msgid "Monthly"
msgstr "Μηνιαία"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_plan__name
msgid "Name"
msgstr "Περιγραφή"

#. module: partner_commission
#. odoo-python
#: code:addons/partner_commission/models/account_move.py:0
msgid "New commission. Invoice: %(link)s. Amount: %(amount)s."
msgstr ""

#. module: partner_commission
#: model:res.groups,name:partner_commission.group_commission_user
msgid "Own Documents Only"
msgstr ""

#. module: partner_commission
#: model:ir.actions.server,name:partner_commission.cron_confirm_purchase_orders_ir_actions_server
msgid "Partner Commission: confirm purchase orders"
msgstr ""

#. module: partner_commission
#: model:ir.model,name:partner_commission.model_res_partner_grade
msgid "Partner Grade"
msgstr ""

#. module: partner_commission
#: model_terms:ir.ui.view,arch_db:partner_commission.res_config_settings_view_form
msgid "Partners Commissions"
msgstr ""

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_rule__pricelist_id
msgid "Pricelist"
msgstr "Τιμοκατάλογος"

#. module: partner_commission
#: model:ir.model.fields.selection,name:partner_commission.selection__purchase_order__purchase_type__procurement
msgid "Procurement"
msgstr "Προμήθειες"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_rule__product_id
msgid "Product"
msgstr "Είδος"

#. module: partner_commission
#. odoo-python
#: code:addons/partner_commission/models/commission_plan.py:0
msgid "Product %(product)s does not belong to category %(category)s"
msgstr ""

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_rule__category_id
msgid "Product Category"
msgstr "Κατηγορία Είδους"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_plan__product_id
msgid "Purchase Default Product"
msgstr ""

#. module: partner_commission
#: model:ir.model,name:partner_commission.model_purchase_order
msgid "Purchase Order"
msgstr "Παραγγελία Αγοράς"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_purchase_order__purchase_type
msgid "Purchase Type"
msgstr ""

#. module: partner_commission
#: model:ir.model.fields.selection,name:partner_commission.selection__res_company__commission_automatic_po_frequency__quarterly
msgid "Quarterly"
msgstr ""

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_rule__rate
msgid "Rate"
msgstr "Ισοτιμία"

#. module: partner_commission
#: model:ir.model.constraint,message:partner_commission.constraint_commission_rule_check_rate
msgid "Rate should be between 0 and 100."
msgstr ""

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_account_bank_statement_line__referrer_id
#: model:ir.model.fields,field_description:partner_commission.field_account_move__referrer_id
#: model:ir.model.fields,field_description:partner_commission.field_sale_order__referrer_id
#: model:ir.model.fields,field_description:partner_commission.field_sale_order_log_report__referrer_id
#: model:ir.model.fields,field_description:partner_commission.field_sale_report__referrer_id
#: model:ir.model.fields,field_description:partner_commission.field_sale_subscription_report__referrer_id
#: model_terms:ir.ui.view,arch_db:partner_commission.account_move_view_search_inherit_partner_commission
#: model_terms:ir.ui.view,arch_db:partner_commission.sale_order_log_search
#: model_terms:ir.ui.view,arch_db:partner_commission.sale_order_subsciption_view_search_inherit_partner_commission
#: model_terms:ir.ui.view,arch_db:partner_commission.sale_order_view_search_inherit_partner_commission
#: model_terms:ir.ui.view,arch_db:partner_commission.sale_report_search
#: model_terms:ir.ui.view,arch_db:partner_commission.sale_subscription_report_search
msgid "Referrer"
msgstr ""

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_sale_order__commission
msgid "Referrer Commission"
msgstr ""

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_account_bank_statement_line__commission_po_line_id
#: model:ir.model.fields,field_description:partner_commission.field_account_move__commission_po_line_id
msgid "Referrer Purchase Order line"
msgstr ""

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_plan__commission_rule_ids
#: model_terms:ir.ui.view,arch_db:partner_commission.commission_plan_form_view
msgid "Rules"
msgstr "Κανόνες"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_rule__template_id
msgid "Sale Order Template"
msgstr ""

#. module: partner_commission
#: model:ir.model,name:partner_commission.model_sale_report
msgid "Sales Analysis Report"
msgstr ""

#. module: partner_commission
#: model:ir.model,name:partner_commission.model_sale_order_log_report
msgid "Sales Log Analysis Report"
msgstr ""

#. module: partner_commission
#: model:ir.model,name:partner_commission.model_sale_order
msgid "Sales Order"
msgstr "Παραγγελία"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_rule__sequence
msgid "Sequence"
msgstr "Ακολουθία"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_purchase_order__invoice_commission_count
#: model_terms:ir.ui.view,arch_db:partner_commission.purchase_order_form_inherit_partner_commission
msgid "Source Invoices"
msgstr ""

#. module: partner_commission
#: model:ir.model,name:partner_commission.model_sale_subscription_report
msgid "Subscription Analysis"
msgstr ""

#. module: partner_commission
#: model:ir.model.fields,help:partner_commission.field_sale_order__commission_plan_id
msgid "Takes precedence over the Referrer's commission plan."
msgstr ""

#. module: partner_commission
#. odoo-python
#: code:addons/partner_commission/models/account_move.py:0
msgid ""
"The commission partner order %s must be checked manually (especially refund "
"lines which can be duplicated)."
msgstr ""

#. module: partner_commission
#: model:ir.model.fields,help:partner_commission.field_res_partner_grade__default_commission_plan_id
msgid ""
"The default commission plan used for this grade. Can be overwritten on the "
"partner form."
msgstr ""

#. module: partner_commission
#: model_terms:ir.ui.view,arch_db:partner_commission.res_config_settings_view_form
msgid ""
"The required minimum amount total needed to automatically confirm purchase "
"orders"
msgstr ""

#. module: partner_commission
#: model:ir.model.fields.selection,name:partner_commission.selection__res_company__commission_automatic_po_frequency__weekly
msgid "Weekly"
msgstr "Εβδομαδιαία"

#. module: partner_commission
#: model:ir.model.fields,help:partner_commission.field_commission_rule__is_capped
msgid "Whether the commission is capped."
msgstr ""

#. module: partner_commission
#: model:ir.model.fields,help:partner_commission.field_sale_order__commission_plan_frozen
msgid ""
"Whether the commission plan is frozen. When checked, the commission plan "
"won't automatically be updated according to the partner level."
msgstr ""
