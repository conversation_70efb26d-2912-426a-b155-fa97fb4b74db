# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_l10n_be_hr_payroll
# 
# Translators:
# Wil Odoo, 2024
# <PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-29 14:41+0000\n"
"PO-Revision-Date: 2024-01-30 08:20+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2024\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: documents_l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:documents_l10n_be_hr_payroll.l10n_be_281_10_form_wizard
#: model_terms:ir.ui.view,arch_db:documents_l10n_be_hr_payroll.l10n_be_281_45_form_wizard
#: model_terms:ir.ui.view,arch_db:documents_l10n_be_hr_payroll.l10n_be_individual_account_view_form
msgid "\"Documents\" app settings"
msgstr "Konfiguration der \"Dokumenten\"-App"

#. module: documents_l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:documents_l10n_be_hr_payroll.l10n_be_individual_account_view_form
msgid "<span class=\"o_stat_text\">Documents</span>"
msgstr "<span class=\"o_stat_text\">Dokumente</span>"

#. module: documents_l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:documents_l10n_be_hr_payroll.l10n_be_281_10_form_wizard
msgid ""
"<strong>Warning: </strong>In order to post the 281.10 sheets in the employee"
" portal, you have to Enable \"Human Resources\" in the"
msgstr ""
"<strong>Warnung: </strong> Um die 281.10 Bögen in das Mitarbeiterportal "
"einstellen zu können, müssen Sie die Option \"Personalwesen\" aktivieren in "
"der"

#. module: documents_l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:documents_l10n_be_hr_payroll.l10n_be_281_45_form_wizard
msgid ""
"<strong>Warning: </strong>In order to post the 281.45 sheets in the employee"
" portal, you have to Enable \"Human Resources\" in the"
msgstr ""
"<strong>Warnung: </strong> Um die 281.45 Bögen in das Mitarbeiterportal "
"einstellen zu können, müssen Sie die Option \"Personalwesen\" aktivieren in "
"der"

#. module: documents_l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:documents_l10n_be_hr_payroll.l10n_be_individual_account_view_form
msgid ""
"<strong>Warning: </strong>In order to post the individual account sheets in "
"the employee portal, you have to Enable \"Human Resources\" in the"
msgstr ""
"<strong>Warnung: </strong>Um die einzelnen Kontoblätter im Mitarbeiterportal"
" zu verbuchen, müssen Sie im Menü \"Personal\" die Option \"Personal\" "
"aktivieren."

#. module: documents_l10n_be_hr_payroll
#: model:ir.model,name:documents_l10n_be_hr_payroll.model_l10n_be_social_balance_sheet
msgid "Belgium: Social Balance Sheet"
msgstr "Belgien: Sozialbilanz"

#. module: documents_l10n_be_hr_payroll
#: model:ir.model,name:documents_l10n_be_hr_payroll.model_l10n_be_social_security_certificate
msgid "Belgium: Social Security Certificate"
msgstr "Belgien: Sozialversicherungsausweis"
