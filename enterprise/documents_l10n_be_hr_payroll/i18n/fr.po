# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_l10n_be_hr_payroll
# 
# Translators:
# Wil Odoo, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-29 14:41+0000\n"
"PO-Revision-Date: 2024-01-30 08:20+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: documents_l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:documents_l10n_be_hr_payroll.l10n_be_281_10_form_wizard
#: model_terms:ir.ui.view,arch_db:documents_l10n_be_hr_payroll.l10n_be_281_45_form_wizard
#: model_terms:ir.ui.view,arch_db:documents_l10n_be_hr_payroll.l10n_be_individual_account_view_form
msgid "\"Documents\" app settings"
msgstr "configuration de l'application \"Documents\""

#. module: documents_l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:documents_l10n_be_hr_payroll.l10n_be_individual_account_view_form
msgid "<span class=\"o_stat_text\">Documents</span>"
msgstr "<span class=\"o_stat_text\">Documents</span>"

#. module: documents_l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:documents_l10n_be_hr_payroll.l10n_be_281_10_form_wizard
msgid ""
"<strong>Warning: </strong>In order to post the 281.10 sheets in the employee"
" portal, you have to Enable \"Human Resources\" in the"
msgstr ""
"<strong>Attention :</strong> Afin de publier les fiches 281.10 dans le "
"portail des employés, vous devez Activer \"Ressources Humaines\" dans la"

#. module: documents_l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:documents_l10n_be_hr_payroll.l10n_be_281_45_form_wizard
msgid ""
"<strong>Warning: </strong>In order to post the 281.45 sheets in the employee"
" portal, you have to Enable \"Human Resources\" in the"
msgstr ""
"<strong>Attention : </strong>Afin d'afficher les fiches 281.45 dans le "
"portail des employés, vous devez Activer \"Ressources Humaines\" dans la"

#. module: documents_l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:documents_l10n_be_hr_payroll.l10n_be_individual_account_view_form
msgid ""
"<strong>Warning: </strong>In order to post the individual account sheets in "
"the employee portal, you have to Enable \"Human Resources\" in the"
msgstr ""
"<strong>Attention : </strong>Afin d'afficher les fiches de compte individuel"
" dans le portail des employés, vous devez Activer \"Ressources Humaines\" "
"dans la"

#. module: documents_l10n_be_hr_payroll
#: model:ir.model,name:documents_l10n_be_hr_payroll.model_l10n_be_social_balance_sheet
msgid "Belgium: Social Balance Sheet"
msgstr "Belgique: Bilan social"

#. module: documents_l10n_be_hr_payroll
#: model:ir.model,name:documents_l10n_be_hr_payroll.model_l10n_be_social_security_certificate
msgid "Belgium: Social Security Certificate"
msgstr "Belgique : Attestation des charges sociales"
