# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* delivery_sendcloud
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:25+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid ""
"\n"
"Additionally, some individual product(s) are too heavy for the heaviest available shipping method.\n"
"                             \n"
"Divide the quantity of the following product(s) across your packages if possible or choose another carrier:\n"
"\t%s"
msgstr ""
"\n"
"Außerdem haben einige Produkte ein Übergewicht bei der schwersten verfügbaren Versandmethode.\n"
"                             \n"
"Teilen Sie die Menge des/der folgenden Produkts/Produkte nach Möglichkeit auf mehrere Pakete auf oder wählen Sie ein anderes Transportunternehmen:\n"
"\t%s"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid ""
"\n"
"Try to distribute your products across your packages so that they weigh less than %(max_weight)s %(unit)s or choose another carrier."
msgstr ""
"\n"
"Versuchen Sie, Ihre Produkte so auf Ihre Pakete zu verteilen, dass sie weniger als %(max_weight)s %(unit)s wiegen oder wählen Sie ein anderes Transportunternehmen."

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid "%(partner_name)s email required"
msgstr "E-Mail von %(partner_name)s erforderlich"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid "%(partner_name)s phone required"
msgstr "Telefon von %(partner_name)s erforderlich"

#. module: delivery_sendcloud
#: model_terms:ir.ui.view,arch_db:delivery_sendcloud.view_delivery_carrier_form
msgid ""
"<i class=\"fa fa-exclamation-triangle\"/> Do not forget to load your "
"SendCloud shipping products for a valid configuration."
msgstr ""
"<i class=\"fa fa-exclamation-triangle\"/> Vergessen Sie nicht, Ihre "
"SendCloud-Versandprodukte für eine gültige Konfiguration zu laden."

#. module: delivery_sendcloud
#: model_terms:ir.ui.view,arch_db:delivery_sendcloud.view_delivery_carrier_form
msgid ""
"<i class=\"fa fa-exclamation-triangle\"/> In test environment, to avoid "
"charges, your shippings are automatically <b>cancelled</b> after the label "
"creation."
msgstr ""
"<i class=\"fa fa-exclamation-triangle\"/> In der Testumgebung werden Ihre "
"Sendungen nach der Erstellung des Etiketts automatisch <b>storniert</b>, um "
"Kosten zu vermeiden."

#. module: delivery_sendcloud
#: model_terms:ir.ui.view,arch_db:delivery_sendcloud.view_delivery_carrier_form
msgid ""
"<i class=\"fa fa-exclamation-triangle\"/> Only administrators can configure "
"the public and private keys."
msgstr ""
"<i class=\"fa fa-exclamation-triangle\"/> Nur Administratoren können die "
"öffentlichen und privaten Schlüssel konfigurieren."

#. module: delivery_sendcloud
#: model_terms:ir.ui.view,arch_db:delivery_sendcloud.view_shipping_method_sendcloud
msgid ""
"<i class=\"fa fa-info-circle\"/> Available shipping products depend on "
"enabled carriers in your Sendcloud account."
msgstr ""
"<i class=\"fa fa-info-circle\"/> Die verfügbaren Versandprodukte hängen von "
"den in Ihrem Sendcloud-Konto aktivierten Versandunternehmen ab."

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_wizard__sendcloud_products_code
msgid "Active Products Code"
msgstr "Aktive Produktcodes"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_product__functionalities
msgid "Available Functionalities"
msgstr "Verfügbare Funktionen"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_delivery_carrier__sendcloud_has_custom_functionalities
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_product__can_customize_functionalities
msgid "Can Customize Functionalities"
msgstr "Kann Funktionen anpassen"

#. module: delivery_sendcloud
#: model_terms:ir.ui.view,arch_db:delivery_sendcloud.view_shipping_method_sendcloud
msgid "Cancel"
msgstr "Abbrechen"

#. module: delivery_sendcloud
#. odoo-javascript
#: code:addons/delivery_sendcloud/static/src/views/sendcloud_product_selection_widget.xml:0
#: model:ir.model.fields,field_description:delivery_sendcloud.field_stock_package_type__package_carrier_type
msgid "Carrier"
msgstr "Transportunternehmen"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/delivery_carrier.py:0
msgid "Choose Sendcloud Shipping Products"
msgstr "Sendcloud-Versandprodukte auswählen"

#. module: delivery_sendcloud
#: model:ir.model,name:delivery_sendcloud.model_sendcloud_shipping_wizard
msgid "Choose from the available sendcloud shipping methods"
msgstr "Aus den verfügbaren Sendcloud-Versandmethoden auswählen"

#. module: delivery_sendcloud
#: model:ir.model,name:delivery_sendcloud.model_sendcloud_shipping_product
msgid "Choose from the available sendcloud shipping products"
msgstr "Aus den verfügbaren Sendcloud-Versandprodukten auswählen"

#. module: delivery_sendcloud
#: model_terms:ir.ui.view,arch_db:delivery_sendcloud.view_shipping_method_sendcloud
msgid "Confirm"
msgstr "Bestätigen"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid "Could not find currency %s"
msgstr "Währung %s konnte nicht gefunden werden"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid "Could not get document!"
msgstr "Dokument konnte nicht erhalten werden!"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_product__create_uid
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_wizard__create_uid
msgid "Created by"
msgstr "Erstellt von"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_product__create_date
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_wizard__create_date
msgid "Created on"
msgstr "Erstellt am"

#. module: delivery_sendcloud
#: model_terms:ir.ui.view,arch_db:delivery_sendcloud.view_delivery_carrier_form
msgid "Default Package Type"
msgstr "Standard-Pakettyp"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_delivery_carrier__sendcloud_default_package_type_id
msgid "Default Package Type for Sendcloud"
msgstr "Standard-Pakettyp für Sendcloud"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_wizard__carrier_id
msgid "Delivery"
msgstr "Lieferung"

#. module: delivery_sendcloud
#: model_terms:ir.ui.view,arch_db:delivery_sendcloud.view_shipping_method_sendcloud
msgid "Delivery Product"
msgstr "Lieferprodukt"

#. module: delivery_sendcloud
#: model:ir.model.fields,help:delivery_sendcloud.field_delivery_carrier__sendcloud_shipping_rules
msgid ""
"Depending your Sendcloud account type, through rules you can define the shipping method to use depending on different conditions like destination, weight, value, etc.\n"
"Rules can override shipping product selected in Odoo"
msgstr ""
"Je nach Sendcloud-Kontotyp können Sie mithilfe von Regeln die Versandmethode entsprechend unterschiedlicher Bedingungen wie Ziel, Gewicht, Wert usw. bestimmen.\n"
"Regeln können das in Odoo ausgewählte Versandprodukt außer Kraft setzen"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_product__display_name
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_wizard__display_name
msgid "Display Name"
msgstr "Anzeigename"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid ""
"Each address line can only contain a maximum of 75 characters. You can split"
" the address into multiple lines to try to avoid this limitation."
msgstr ""
"Jede Adresszeile kann nur maximal 75 Zeichen enthalten. Sie können die "
"Adresse auf mehrere Zeilen aufteilen, um zu versuchen, diese Begrenzung zu "
"umgehen."

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid ""
"Ensure picking has shipping weight, if using packages, each package should "
"have a shipping weight"
msgstr ""
"Vergewissern Sie sich, dass die Kommissionierung ein Versandgewicht hat; "
"wenn Sie Pakete verwenden, sollte jedes Paket ein Versandgewicht haben."

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/delivery_carrier.py:0
msgid "Failed to create the return label!"
msgstr "Erstellung des Rücksendeetiketts fehlgeschlagen!"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/delivery_carrier.py:0
msgid "Failed to get the actual price!"
msgstr "Der tatsächliche Preis wurde nicht ermittelt!"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_delivery_carrier__sendcloud_product_functionalities
msgid "Functionalities"
msgstr "Funktionen"

#. module: delivery_sendcloud
#: model_terms:ir.ui.view,arch_db:delivery_sendcloud.view_delivery_carrier_form
msgid "Functionality Filters"
msgstr "Funktionalitätsfilter"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/delivery_carrier.py:0
msgid "Go to the shipping product"
msgstr "Zum Versandprodukt"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_delivery_carrier__sendcloud_can_batch_shipping
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_product__has_multicollo
msgid "Has Multicollo"
msgstr "Hat Multicollo"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_product__id
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_wizard__id
msgid "ID"
msgstr "ID"

#. module: delivery_sendcloud
#: model_terms:ir.ui.view,arch_db:delivery_sendcloud.view_delivery_carrier_form
msgid "Integration"
msgstr "Integration"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_product__write_uid
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_wizard__write_uid
msgid "Last Updated by"
msgstr "Zuletzt aktualisiert von"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_product__write_date
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_wizard__write_date
msgid "Last Updated on"
msgstr "Zuletzt aktualisiert am"

#. module: delivery_sendcloud
#: model_terms:ir.ui.view,arch_db:delivery_sendcloud.view_delivery_carrier_form
msgid "Load your products"
msgstr "Ihre Produkte laden"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid ""
"Make sure country codes are set in partner country and warehouse country"
msgstr ""
"Stellen Sie sicher, dass die Ländercodes im Partnerland und im Lagerland "
"eingestellt sind."

#. module: delivery_sendcloud
#. odoo-javascript
#: code:addons/delivery_sendcloud/static/src/views/sendcloud_product_selection_widget.xml:0
msgid "Max height"
msgstr "Max. Höhe"

#. module: delivery_sendcloud
#. odoo-javascript
#: code:addons/delivery_sendcloud/static/src/views/sendcloud_product_selection_widget.xml:0
msgid "Max length"
msgstr "Max. Länge"

#. module: delivery_sendcloud
#. odoo-javascript
#: code:addons/delivery_sendcloud/static/src/views/sendcloud_product_selection_widget.xml:0
msgid "Max width"
msgstr "Max. Breite"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_product__max_weight
msgid "Maximum Weight"
msgstr "Maximalgewicht"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_product__min_weight
msgid "Minimum Weight"
msgstr "Mindestgewicht"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/delivery_carrier.py:0
msgid "Must be a Sendcloud carrier!"
msgstr "Muss ein Sendcloud-Zusteller sein!"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid "No address found with contact name %s on your sendcloud account."
msgstr ""
"Keine Adresse mit Name des Kontakts %s in Ihrem Sendcloud-Konto gefunden."

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid "No picking or order provided"
msgstr "Keine Kommissionierung oder Auftrag verfügbar"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/delivery_carrier.py:0
msgid ""
"Note that a unit of the product '%s' is heavier than the maximum weight "
"allowed by the shipping method."
msgstr ""
"Beachten Sie, dass eine Einheit des Produkts „%s“ schwerer ist als das für "
"die Versandmethode zulässige Höchstgewicht."

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/delivery_carrier.py:0
msgid ""
"Note that this price is for %s packages since the order weight is more than "
"the maximum weight allowed by the shipping method."
msgstr ""
"Beachten Sie, dass dieser Preis für %s-Pakete ist, da das Auftragsgewicht "
"das zulässige Maximalgewicht der Versandmethode überschreitet."

#. module: delivery_sendcloud
#: model_terms:ir.ui.view,arch_db:delivery_sendcloud.view_delivery_carrier_form
msgid "Options"
msgstr "Optionen"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid "Order below minimum weight of carrier"
msgstr "Auftrag unter Mindestgewicht des Transportunternehmens"

#. module: delivery_sendcloud
#. odoo-javascript
#: code:addons/delivery_sendcloud/static/src/views/sendcloud_product_selection_widget.xml:0
msgid "Other Functionalities"
msgstr "Andere Funktionen"

#. module: delivery_sendcloud
#. odoo-javascript
#: code:addons/delivery_sendcloud/static/src/views/sendcloud_product_selection_widget.xml:0
msgid ""
"Please check SendCloud return product documentation before selecting return "
"product. For some products, you can be charged for return labels printed but"
" not used."
msgstr ""
"Bitte prüfen Sie die SendCloud-Retourenunterlagen, bevor Sie ein "
"Retourenprodukt auswählen. Bei einigen Produkten können Ihnen gedruckte, "
"aber nicht verwendete Rücksendeetiketten in Rechnung gestellt werden."

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_delivery_carrier__delivery_type
msgid "Provider"
msgstr "Anbieter"

#. module: delivery_sendcloud
#: model_terms:ir.ui.view,arch_db:delivery_sendcloud.view_shipping_method_sendcloud
msgid "Return Product"
msgstr "Produkt retournieren"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_wizard__return_products
msgid "Return Products"
msgstr "Produkte retournieren"

#. module: delivery_sendcloud
#: model_terms:ir.ui.view,arch_db:delivery_sendcloud.view_delivery_carrier_form
msgid "SendCloud Configuration"
msgstr "SendCloud-Konfiguration"

#. module: delivery_sendcloud
#: model_terms:ir.ui.view,arch_db:delivery_sendcloud.res_config_settings_view_form_sale
msgid "SendCloud Shipping Methods"
msgstr "SendCloud-Versandmethoden"

#. module: delivery_sendcloud
#: model:ir.model.fields.selection,name:delivery_sendcloud.selection__delivery_carrier__delivery_type__sendcloud
#: model:ir.model.fields.selection,name:delivery_sendcloud.selection__stock_package_type__package_carrier_type__sendcloud
msgid "Sendcloud"
msgstr "Sendcloud"

#. module: delivery_sendcloud
#: model:ir.model.fields,help:delivery_sendcloud.field_delivery_carrier__sendcloud_public_key
msgid "Sendcloud API Integration Public key"
msgstr "Öffentlicher Schlüssel für Sencloud-API-Integration"

#. module: delivery_sendcloud
#: model:ir.model.fields,help:delivery_sendcloud.field_delivery_carrier__sendcloud_secret_key
msgid "Sendcloud API Integration Secret key"
msgstr "Geheimer Schlüssel der Sendcloud-API-Integration"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_stock_picking__sendcloud_parcel_ref
msgid "Sendcloud Parcel Reference"
msgstr "Sendcloud-Paketreferenz"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_product__sendcloud_code
msgid "Sendcloud Product Identifier"
msgstr "Sendcloud-Produkt-ID"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_delivery_carrier__sendcloud_public_key
msgid "Sendcloud Public Key"
msgstr "Öffentlicher Schlüssel von Sendcloud"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_delivery_carrier__sendcloud_return_id
msgid "Sendcloud Return"
msgstr "Sendcloud-Retoure"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_stock_picking__sendcloud_return_parcel_ref
msgid "Sendcloud Return Parcel Ref"
msgstr "Sendcloud-Ref. des Retourenpakets"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_delivery_carrier__sendcloud_return_name
msgid "Sendcloud Return Shipping Product"
msgstr "Sendcloud-Retourenversandprodukt"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_delivery_carrier__sendcloud_secret_key
msgid "Sendcloud Secret Key"
msgstr "Geheimer Schlüssel von Sendcloud"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_delivery_carrier__sendcloud_shipping_id
msgid "Sendcloud Shipping"
msgstr "Sendcloud-Versand"

#. module: delivery_sendcloud
#: model_terms:ir.ui.view,arch_db:delivery_sendcloud.res_config_settings_view_form_stock
msgid "Sendcloud Shipping Methods"
msgstr "Sendcloud-Versandmethoden"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_delivery_carrier__sendcloud_shipping_name
msgid "Sendcloud Shipping Product"
msgstr "Sendcloud-Versandprodukt"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid ""
"Sendcloud is not responding. Check your credentials and/or retry later."
msgstr ""
"Sendcloud antwortet nicht. Überprüfen Sie Ihre Anmeldedaten und/oder "
"versuchen Sie es später noch einmal."

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_delivery_carrier__country_id
msgid "Ship From"
msgstr "Versand von"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/delivery_carrier.py:0
msgid "Shipment %s cancelled"
msgstr "Sendung %s storniert"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_product__carrier
msgid "Shipping Carrier"
msgstr "Versandunternehmen"

#. module: delivery_sendcloud
#: model:ir.model,name:delivery_sendcloud.model_delivery_carrier
msgid "Shipping Methods"
msgstr "Versandmethoden"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_product__name
#: model_terms:ir.ui.view,arch_db:delivery_sendcloud.view_shipping_method_sendcloud
msgid "Shipping Product"
msgstr "Versandprodukt"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_wizard__shipping_products
msgid "Shipping Products"
msgstr "Versandprodukte"

#. module: delivery_sendcloud
#: model:ir.model.fields,help:delivery_sendcloud.field_delivery_carrier__sendcloud_default_package_type_id
msgid ""
"Some carriers require package dimensions, you can define these in a package "
"type that you set as default"
msgstr ""
"Einige Versandunternehmen erfordern Paketabmessungen. Sie können diese in "
"einem Pakettyp, das Sie als Standard einstellen, festlegen."

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid ""
"Some packages in your transfer are too heavy for the heaviest available "
"shipping method."
msgstr ""
"Einige Pakete in Ihrer Sendung sind zu schwer für die schwerste verfügbare "
"Versandmethode."

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid ""
"Something went wrong, parcel not returned from Sendcloud:\n"
" %s'."
msgstr ""
"Etwas ist schiefgelaufen. Das Paket wurde nicht von Sendcloud retourniert:\n"
" %s'."

#. module: delivery_sendcloud
#: model:ir.model,name:delivery_sendcloud.model_stock_package_type
msgid "Stock package type"
msgstr "Typ des Lagerpakets"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid "The %s address needs to have the street, city, zip, and country"
msgstr ""
"Die Adresse %s muss die Straße, den Ort, die Postleitzahl und das Land "
"enthalten."

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid ""
"The delivery address of the customer has been removed from the pickup "
"location. This information is required by Sendcloud. Please go to the "
"delivery partner via the delivery order and make sure the parent of the "
"delivery partner is the partner you want to ship to."
msgstr ""
"Die Lieferadresse des Kunden wurde aus dem Abholort entfernt. Sendcloud "
"benötigt diese Informationen. Bitte gehen Sie über den Lieferauftrag zum "
"Lieferpartner und stellen Sie sicher, dass der übergeordnete Lieferpartner "
"der Partner ist, mit dem Sie versenden möchten."

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid ""
"The estimated shipping price cannot be computed because the weight is missing for the following product(s): \n"
" %s"
msgstr ""
"Der geschätzte Versandpreis kann nicht berechnet werden, da das Gewicht für folgende(s) Produkt(e) fehlt: \n"
"%s"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/delivery_carrier.py:0
msgid "The shipping product actually configured can't handle this delivery"
msgstr ""
"Das tatsächlich konfigurierte Versandprodukt kann diese Lieferung nicht "
"verarbeiten"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid ""
"The total weight of your transfer is too heavy for the heaviest available "
"shipping method."
msgstr ""
"Das Gesamtgewicht Ihres Transfers ist zu schwer für die schwerste verfügbare"
" Versandmethode."

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/delivery_carrier.py:0
msgid ""
"There are no shipping products available, please update the 'Shipping From' "
"field or activate suitable carriers in your sendcloud account"
msgstr ""
"Es sind keine Versandprodukte verfügbar. Bitte aktualisieren Sie das Feld "
"„Versand von“ oder aktivieren Sie geeignete Versandunternehmen in Ihrem "
"Sendcloud-Konto."

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/delivery_carrier.py:0
msgid ""
"There is no rate available for this order with the selected shipping "
"product."
msgstr ""
"Für diesen Auftrag ist kein Tarif für das gewählte Versandprodukt verfügbar."

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid ""
"There is no shipping method available for this order with the selected "
"carrier"
msgstr ""
"Für diesen Auftrag mit dem ausgewählten Transportunternehmen ist keine "
"Versandmethode verfügbar"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid ""
"There is no shipping method available for this picking with the selected "
"carrier"
msgstr ""
"Für diese Kommissionierung mit dem ausgewählten Transportunternehmen ist "
"keine Versandmethode verfügbar"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid ""
"There's no method with matching weight range for packages :\n"
"%s\n"
"You can either choose another carrier, change your filters or redefine the content of your package(s)."
msgstr ""
"Es gibt keine Methode mit einem passenden Gewichtsbereich für Pakete:\n"
"%s\n"
"Sie können entweder ein anderes Transportunternehmen wählen, Ihre Filter ändern oder den Inhalt Ihrer Pakete neu definieren."

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid ""
"There's no shipping method matching all your selected filters for this "
"picking/order."
msgstr ""
"Für diese Kommissionierung/Bestellung gibt es keine Versandart, die allen "
"von Ihnen gewählten Filtern entspricht."

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/delivery_carrier.py:0
msgid "There's no unit of measure with the name \"%s\"."
msgstr "Es gibt keine Maßeinheit mit dem Namen „%s“."

#. module: delivery_sendcloud
#: model:ir.model,name:delivery_sendcloud.model_stock_picking
msgid "Transfer"
msgstr "Transfer"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_delivery_carrier__sendcloud_use_batch_shipping
msgid "Use Batch Shipping"
msgstr "Stapelversand verwenden"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_delivery_carrier__sendcloud_shipping_rules
msgid "Use Sendcloud shipping rules"
msgstr "Sendcloud-Versandregeln verwenden"

#. module: delivery_sendcloud
#. odoo-javascript
#: code:addons/delivery_sendcloud/static/src/views/sendcloud_product_selection_widget.xml:0
msgid "Weight range"
msgstr "Gewichtsbereich"

#. module: delivery_sendcloud
#: model:ir.model.fields,help:delivery_sendcloud.field_delivery_carrier__sendcloud_use_batch_shipping
msgid ""
"When sending multiple parcels, combine them in one shipment. Not supported "
"for international shipping requiring customs' documentation"
msgstr ""
"Wenn Sie mehrere Pakete versenden, fassen Sie diese in einer Sendung "
"zusammen. Nicht unterstützt für internationalen Versand, der Zolldokumente "
"erfordert"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/delivery_carrier.py:0
msgid "You must add your public and secret key for sendcloud delivery type!"
msgstr ""
"Sie müssen Ihren öffentlichen und geheimen Schlüssel für die Sendcloud-"
"Versandart hinzufügen!"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/delivery_carrier.py:0
msgid ""
"You must assign the required 'Shipping From' field in order to search for "
"available products"
msgstr ""
"Sie müssen das erforderliche Feld „Versand von“ zuweisen, um nach "
"verfügbaren Produkten suchen zu können."

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/delivery_carrier.py:0
msgid "You must have a shipping product configured!"
msgstr "Sie müssen ein Versandprodukt konfiguriert haben!"

#. module: delivery_sendcloud
#. odoo-javascript
#: code:addons/delivery_sendcloud/static/src/views/sendcloud_product_selection_widget.xml:0
msgid "cm"
msgstr "cm"

#. module: delivery_sendcloud
#: model_terms:ir.ui.view,arch_db:delivery_sendcloud.sendcloud_label_tracking
msgid ""
"created in Sendcloud. <br/>\n"
"            <b>Tracking Numbers:</b>"
msgstr ""
"erstellt in Sendcloud. <br/>\n"
"            <b>Sendungsnummern:</b>"
