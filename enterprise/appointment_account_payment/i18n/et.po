# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* appointment_account_payment
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# JanaAvalah, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:26+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Estonian (https://app.transifex.com/odoo/teams/41243/et/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: et\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: appointment_account_payment
#. odoo-python
#: code:addons/appointment_account_payment/models/calendar_booking.py:0
msgid ""
"%(name)s\n"
"%(date_start)s at %(time_start)s to\n"
"%(date_end)s at %(time_end)s (%(timezone)s)"
msgstr ""
"%(name)s\n"
"%(date_start)s kell %(time_start)s kuni\n"
"%(date_end)s kell %(time_end)s (%(timezone)s)"

#. module: appointment_account_payment
#. odoo-python
#: code:addons/appointment_account_payment/models/calendar_booking.py:0
msgid ""
"%(name)s with %(staff_user)s\n"
"%(date_start)s at %(time_start)s to\n"
"%(date_end)s at %(time_end)s (%(timezone)s)"
msgstr ""
"%(name)s koos %(staff_user)s\n"
"%(date_start)s kell %(time_start)s kuni \n"
"%(date_end)s kell %(time_end)s (%(timezone)s)"

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.appointment_validated
msgid "<i class=\"fa fa-check-circle text-success me-3\"/>Appointment Scheduled!"
msgstr ""
"<i class=\"fa fa-check-circle text-success me-3\"/>Kohtumine on planeeritud!"

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.appointment_payment
msgid "<i class=\"fa fa-pencil fa-fw me-1\"/>Edit my appointment"
msgstr "<i class=\"fa fa-pencil fa-fw me-1\"/>Muuda minu kohtumist"

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.calendar_booking_view
msgid ""
"<i class=\"fa fa-pencil-square-o text-warning me-3\"/>Appointment waiting "
"for payment"
msgstr ""

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.calendar_booking_view
msgid "<i class=\"fa fa-times-circle text-danger me-3\"/>Appointment Unavailable!"
msgstr ""

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.appointment_type_view_form
msgid ""
"<i class=\"oi oi-fw o_button_icon oi-arrow-right\"/> Configure Payment "
"Providers"
msgstr ""
"<i class=\"oi oi-fw o_button_icon oi-arrow-right\"/> Seadista makseteenuse "
"pakkujad"

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.appointment_type_view_form
msgid ""
"<span invisible=\"resource_manage_capacity\" class=\"ms-1\">per booking</span>\n"
"                            <span invisible=\"not resource_manage_capacity\" class=\"ms-1\">per seat</span>"
msgstr ""

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.calendar_booking_view
msgid "<span>Online</span>"
msgstr "<span>Online</span>"

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.appointment_progress_bar
msgid ""
"<span>Payment</span>\n"
"            <span class=\"d-inline-block mx-sm-3 fa fa-angle-right text-muted fs-5\"/>"
msgstr ""
"<span>Makse</span>\n"
"            <span class=\"d-inline-block mx-sm-3 fa fa-angle-right text-muted fs-5\"/>"

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.appointment_payment
msgid ""
"<strong>Warning</strong> Make sure you are logged in as the right partner "
"before making this payment."
msgstr ""
"<strong>Hoiatus</strong> Enne makse sooritamist veenduge, et olete õige "
"partnerina sisse logitud."

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.appointment_payment
msgid "<strong>Warning</strong> The currency is missing or incorrect."
msgstr "<strong>Hoiatus</strong> Valuuta puudub või on vale."

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.appointment_payment
msgid "<strong>Warning</strong> You must be logged in to pay."
msgstr "<strong>Hoiatus</strong> Maksmiseks peate olema sisse logitud."

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking__booking_token
msgid "Access Token"
msgstr "Ligipääsu võti"

#. module: appointment_account_payment
#: model:ir.model.constraint,message:appointment_account_payment.constraint_appointment_type_check_product_and_payment_step
msgid "Activating the payment step requires a product"
msgstr "Maksmise aktiveerimine nõuab toote olemasolu"

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.appointment_payment
msgid "Amount"
msgstr "Summa"

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.calendar_booking_view_form
msgid "Answers"
msgstr "Vastused"

#. module: appointment_account_payment
#: model:ir.model,name:appointment_account_payment.model_appointment_answer_input
msgid "Appointment Answer Inputs"
msgstr "Kohtumise Vastuse Sisend"

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking__appointment_answer_input_ids
msgid "Appointment Answers"
msgstr "Kohtumise vastused"

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking__appointment_invite_id
msgid "Appointment Invite"
msgstr "Kohtumise kutse"

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking__account_move_id
msgid "Appointment Invoice"
msgstr "Kohtumise arve"

#. module: appointment_account_payment
#: model:ir.model,name:appointment_account_payment.model_appointment_type
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking__appointment_type_id
msgid "Appointment Type"
msgstr "Kohtumise tüüp"

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking__asked_capacity
msgid "Asked Capacity"
msgstr "Küsitud mahutavus"

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.calendar_booking_view
msgid "Attendees"
msgstr "Osalejad"

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.calendar_booking_view_form
msgid "Booking"
msgstr "Broneering"

#. module: appointment_account_payment
#: model:product.template,name:appointment_account_payment.default_booking_product_product_template
msgid "Booking Fees"
msgstr "Broneerimistasud"

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking__booking_line_ids
msgid "Booking Lines"
msgstr "Broneerimis read"

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_appointment_type__product_id
msgid "Booking Product"
msgstr ""

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_appointment_answer_input__calendar_event_id
msgid "Calendar Event"
msgstr "Kohtumine"

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking_line__capacity_reserved
msgid "Capacity Reserved"
msgstr "Mahutavus reserveeritud"

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking_line__capacity_used
msgid "Capacity Used"
msgstr "Kasutatud mahutavus"

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.appointment_form
msgid "Confirm Appointment"
msgstr "Kinnita kohtumine"

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking__partner_id
msgid "Contact"
msgstr "Kontakt"

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking__create_uid
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking_line__create_uid
msgid "Created by"
msgstr "Loonud"

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking__create_date
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking_line__create_date
msgid "Created on"
msgstr "Loodud"

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_appointment_type__product_currency_id
msgid "Currency"
msgstr "Valuuta"

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking__name
msgid "Customer Name"
msgstr "Kliendi nimi"

#. module: appointment_account_payment
#: model_terms:appointment.type,message_intro:appointment_account_payment.appointment_type_online_cooking_lesson
msgid ""
"Discover the secrets kept in high-end kitchens with one of our starred "
"chefs, from the comfort of your own home."
msgstr ""
"Avasta saladused mida hoitakse tippköökides koos meie tippkokaga, otse oma "
"kodu mugavustest."

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking__display_name
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking_line__display_name
msgid "Display Name"
msgstr "Kuvatav nimi"

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking__duration
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.calendar_booking_view
msgid "Duration"
msgstr "Kestus"

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.calendar_booking_view
msgid "For"
msgstr ".. "

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking__guest_ids
msgid "Guests"
msgstr "Külalised"

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking__id
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking_line__id
msgid "ID"
msgstr "ID"

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking__not_available
msgid "Is Not Available"
msgstr "Ei ole saadaval"

#. module: appointment_account_payment
#: model:ir.model,name:appointment_account_payment.model_account_move
msgid "Journal Entry"
msgstr "Andmiku kanne"

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking__write_uid
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking_line__write_uid
msgid "Last Updated by"
msgstr "Viimati uuendatud"

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking__write_date
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking_line__write_date
msgid "Last Updated on"
msgstr "Viimati uuendatud"

#. module: appointment_account_payment
#. odoo-python
#: code:addons/appointment_account_payment/models/templates/appointment_type.py:0
msgid "Make customers pay a fee per person when booking your resources"
msgstr ""

#. module: appointment_account_payment
#. odoo-python
#: code:addons/appointment_account_payment/models/templates/appointment_type.py:0
msgid "Make sure customers pay before they can take a slot in your calendar"
msgstr ""

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking__calendar_event_id
msgid "Meeting"
msgstr "Koosolek"

#. module: appointment_account_payment
#: model:ir.model,name:appointment_account_payment.model_calendar_booking
#: model:ir.model.fields,field_description:appointment_account_payment.field_account_bank_statement_line__calendar_booking_ids
#: model:ir.model.fields,field_description:appointment_account_payment.field_account_move__calendar_booking_ids
#: model:ir.model.fields,field_description:appointment_account_payment.field_appointment_answer_input__calendar_booking_id
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking_line__calendar_booking_id
msgid "Meeting Booking"
msgstr "Kohtumise broneering"

#. module: appointment_account_payment
#: model:ir.model,name:appointment_account_payment.model_calendar_booking_line
msgid "Meeting Resource Booking"
msgstr "Kohtumise ressursi broneerimine"

#. module: appointment_account_payment
#: model:appointment.type,name:appointment_account_payment.appointment_type_online_cooking_lesson
#: model:product.template,name:appointment_account_payment.product_appointment_type_online_cooking_lesson_product_template
msgid "Online Cooking Lesson"
msgstr "Veebis kokanduskoolitus"

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.appointment_validated
msgid "Oops! Paid appointment cannot be cancelled via our website.<br/>"
msgstr ""

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking__staff_user_id
msgid "Operator"
msgstr "Operaator"

#. module: appointment_account_payment
#. odoo-python
#: code:addons/appointment_account_payment/models/templates/appointment_type.py:0
msgid "Paid Consultation"
msgstr ""

#. module: appointment_account_payment
#. odoo-python
#: code:addons/appointment_account_payment/models/templates/appointment_type.py:0
msgid "Paid Seats"
msgstr ""

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.appointment_payment
msgid "Payment for \""
msgstr "Makse \""

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.appointment_type_view_form
msgid "Pick a Product"
msgstr "Vali toode"

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.appointment_form
msgid "Proceed to Payment"
msgstr "Mine maksma"

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking__product_id
msgid "Product"
msgstr "Toode"

#. module: appointment_account_payment
#: model:ir.model.fields,help:appointment_account_payment.field_appointment_type__has_payment_step
msgid "Require visitors to pay to confirm their booking"
msgstr "Nõu külastajatelt maksmist enne broneerimist"

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking_line__appointment_resource_id
msgid "Resource"
msgstr "Ressurss"

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.calendar_booking_view
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.calendar_booking_view_form
msgid "Resources"
msgstr "Ressursid"

#. module: appointment_account_payment
#. odoo-python
#: code:addons/appointment_account_payment/models/templates/appointment_type.py:0
msgid "Room %s"
msgstr ""

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_appointment_type__product_lst_price
msgid "Sales Price"
msgstr "Müügihind"

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.appointment_payment
msgid "Service"
msgstr "Teenus"

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking__start
msgid "Start"
msgstr "Alusta"

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking__stop
msgid "Stop"
msgstr "Peata"

#. module: appointment_account_payment
#: model_terms:appointment.type,message_confirmation:appointment_account_payment.appointment_type_online_cooking_lesson
msgid ""
"Thank you for your reservation. We will soon contact you to discuss menu "
"options."
msgstr ""
"Täname teid reserveeringu eest. Me võtame teiega ühendust menüü asjus."

#. module: appointment_account_payment
#: model:ir.model.constraint,message:appointment_account_payment.constraint_appointment_answer_input_check_event_or_booking
msgid "The answer inputs must be linked to a meeting or to a booking"
msgstr "Vastus peab olema seotud koosoleku või broneeringuga."

#. module: appointment_account_payment
#. odoo-python
#: code:addons/appointment_account_payment/models/calendar_booking.py:0
msgid ""
"The following booking was not confirmed due to insufficient availability or "
"configuration changes: %s"
msgstr ""
"Broneeringud polnud kinnitatud puuduva mahutavuse või seadete muudatuse "
"tõttu: %s"

#. module: appointment_account_payment
#: model:ir.model.fields,help:appointment_account_payment.field_appointment_type__product_lst_price
msgid ""
"The sale price is managed from the product template. Click on the 'Configure"
" Variants' button to set the extra attribute prices."
msgstr ""
"Müügihinda hallatakse tootemallist. Täiendavate atribuutide hindade "
"määramiseks klõpsake nuppu Konfigureeri variandid."

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.appointment_payment
msgid "There is nothing to pay."
msgstr "Pole midagi maksta."

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.appointment_payment
msgid "This invoice is already paid for."
msgstr "See arve on juba makstud."

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.appointment_payment
msgid "This is a preview of the customer appointment payment form."
msgstr "See on eelvaade kliendi kohtumise maksmisvormist"

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.appointment_validated
msgid "To make any changes, please contact"
msgstr "Muudatuste tegemiseks võtke ühendust"

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.appointment_validated
msgid "To make any changes, please contact us."
msgstr "Muudatuste tegemiseks palun võtke ühendust."

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.calendar_booking_view
msgid ""
"Unfortunately, it looks like this booking is not possible anymore. Please "
"contact us to find an alternative."
msgstr ""
"Kahjuks ei ole see broneering enam võimalik. Palun kontakteeru, et leida "
"alternatiiv. "

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_appointment_type__has_payment_step
msgid "Up-front Payment"
msgstr "Ettemakse"

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.calendar_booking_view
msgid "When"
msgstr "Millal"

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.calendar_booking_view
msgid "Where"
msgstr ""

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.appointment_validated
msgid "at"
msgstr "kell"

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.appointment_validated
msgid "or"
msgstr "või"

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.calendar_booking_view
msgid "people"
msgstr "inimesed"

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.appointment_info
msgid "per seat"
msgstr ""
