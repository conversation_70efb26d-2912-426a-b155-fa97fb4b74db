<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="oss_sales_report" model="account.report">
        <field name="name">OSS Sales</field>
        <field name="root_report_id" ref="account.generic_tax_report"/>
        <field name="custom_handler_model_id" ref="model_l10n_eu_oss_sales_tax_report_handler"/>
        <field name="availability_condition">oss</field>
        <field name="column_ids">
            <record id="oss_sales_report_net" model="account.report.column">
                <field name="name">NET</field>
                <field name="expression_label">net</field>
            </record>
            <record id="oss_sales_report_tax" model="account.report.column">
                <field name="name">TAX</field>
                <field name="expression_label">tax</field>
            </record>
        </field>
    </record>

    <record id="oss_imports_report" model="account.report">
        <field name="name">OSS Imports</field>
        <field name="root_report_id" ref="account.generic_tax_report"/>
        <field name="custom_handler_model_id" ref="model_l10n_eu_oss_imports_tax_report_handler"/>
        <field name="availability_condition">oss</field>
        <field name="column_ids">
            <record id="oss_imports_report_net" model="account.report.column">
                <field name="name">NET</field>
                <field name="expression_label">net</field>
            </record>
            <record id="oss_imports_report_tax" model="account.report.column">
                <field name="name">TAX</field>
                <field name="expression_label">tax</field>
            </record>
        </field>
    </record>

</odoo>
