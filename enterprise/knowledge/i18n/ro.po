# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* knowledge
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 09:12+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "\" (Selection)"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "\" (date and time)"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "\" (date)"
msgstr ""

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"\"%(article_item_name)s\" is an Article Item from \"%(article_name)s\" and "
"cannot be restored on its own. Contact the owner of \"%(article_name)s\" to "
"have it restored instead."
msgstr ""

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"\"%(article_name)s\" is a template and can not be a child of an article "
"(\"%(parent_article_name)s\")."
msgstr ""

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"\"%(article_name)s\" is an article and can not be a child of a template "
"(\"%(parent_article_name)s\").\""
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "\"About Us\" Template"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "# Employees:"
msgstr ""

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__favorite_count
msgid "#Is Favorite"
msgstr "#Este favorit"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "%(article_name)s (copy)"
msgstr ""

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "%s has been sent to Trash"
msgstr "%s a fost trimis în Coșul de gunoi"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_mail_invite
msgid ".<br/>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid ""
"250 Executive Park Blvd, Suite 3400 94134\n"
"                        <br>\n"
"                        San Francisco California (US)\n"
"                        <br>\n"
"                        United States\n"
"                        <br>\n"
"                        <EMAIL>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_example_branding
msgid "85x200 or 100x200, depending on the content"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid ""
"<br>\n"
"                            Subscribe here to make sure you will not miss an episode!"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_campaign_brief
msgid ""
"<em>\n"
"                    <font class=\"text-600\">\n"
"                        Describe your campaign in just a few words.\n"
"                    </font>\n"
"                </em>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_campaign_brief
msgid ""
"<em>\n"
"                    <font class=\"text-600\">\n"
"                        How will your measure progress?\n"
"                    </font>\n"
"                </em>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_campaign_brief
msgid ""
"<em>\n"
"                    <font class=\"text-600\">\n"
"                        What are you trying to accomplish with this campaign?\n"
"                    </font>\n"
"                </em>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_campaign_brief
msgid ""
"<em>\n"
"                    <font class=\"text-600\">\n"
"                        What are you trying to convince them of?\n"
"                    </font>\n"
"                </em>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_campaign_brief
msgid ""
"<em>\n"
"                    <font class=\"text-600\">\n"
"                        Who are you trying to reach?\n"
"                    </font>\n"
"                </em>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid ""
"<em>\n"
"                    <font class=\"text-600\">Ask a Senior Engineer to fill this part before launching the task.</font>\n"
"                </em>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid ""
"<em>\n"
"                    <font class=\"text-600\">Ask the developer to go through this checklist before asking for a review.</font>\n"
"                </em>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid ""
"<em>\n"
"                    <font class=\"text-600\">Explain in a few words the problem that this change is going to solve.</font>\n"
"                </em>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid ""
"<em>\n"
"                    <font class=\"text-600\">Explain in a single sentence what has been changed.</font>\n"
"                </em>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid ""
"<em>\n"
"                    <font class=\"text-600\">Explain the consequences of this issue.</font>\n"
"                </em>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid ""
"<em>\n"
"                    <font class=\"text-600\">Explain what went wrong in just a few words.</font>\n"
"                </em>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid ""
"<em>\n"
"                    <font class=\"text-600\">How did it get to happen?</font>\n"
"                </em>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid ""
"<em>\n"
"                    <font class=\"text-600\">How was it solved?</font>\n"
"                </em>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid ""
"<em>\n"
"                    <font class=\"text-600\">Lay here any remaining question or doubt.</font>\n"
"                </em>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid ""
"<em>\n"
"                    <font class=\"text-600\">List here all the changes to implement.</font>\n"
"                </em>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid ""
"<em>\n"
"                    <font class=\"text-600\">List here all the material and documentation related to the task.</font>\n"
"                </em>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<em>\n"
"                    <font class=\"text-600\">List here all the text you could not do this week. These shall be postponed in the next weekly schedule.</font>\n"
"                </em>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid ""
"<font class=\"text-400\">\n"
"                            <em>How to best reach this customer type.</em>\n"
"                        </font>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
msgid ""
"<font class=\"text-400\">\n"
"                    <em>How to best reach this customer type.<br></em>\n"
"                </font>\n"
"                Abigail works a lot and never watches TV. Better reach her on her phone or on her to work."
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
msgid ""
"<font class=\"text-400\">\n"
"                    <em>How to best reach this customer type.<br></em>\n"
"                </font>\n"
"                As a classic Gen Z member, Vittorio never watches TV and never listens to the radio. For him to see our message, we need to get to his Instagram feed."
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
msgid ""
"<font class=\"text-400\">\n"
"                    <em>How to best reach this customer type.<br></em>\n"
"                </font>\n"
"                As an avid music listener, the best way to reach Sonya is through the radio since hers is always on."
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
msgid ""
"<font class=\"text-400\">\n"
"                    <em>How to best reach this customer type.<br></em>\n"
"                </font>\n"
"                Julius follows politics very tightly, and can best be reached with TV ads."
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "<font class=\"text-400\">To Read 1</font>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "<font class=\"text-400\">To Read 2</font>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "<font class=\"text-600\">Action A</font>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "<font class=\"text-600\">Action B</font>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "<font class=\"text-600\">Action C</font>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<font class=\"text-600\">Backlog 1</font>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<font class=\"text-600\">Backlog 2</font>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<font class=\"text-600\">Backlog 3</font>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">Blue/Green/Red/Yellow</font>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "<font class=\"text-600\">Change 1</font>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "<font class=\"text-600\">Change 2</font>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "<font class=\"text-600\">Change 3</font>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">Color</font>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">Competitor 1</font>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">Competitor 2</font>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">Competitor 3</font>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_release_note
msgid ""
"<font class=\"text-600\">Detailed Explanation of the feature, with "
"screenshots or a GIF</font>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">Email</font>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_release_note
msgid "<font class=\"text-600\">Fixed a bug where...</font>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_release_note
msgid "<font class=\"text-600\">From now on, ...</font>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid ""
"<font class=\"text-600\">How do stakeholders interact regarding "
"offers?</font>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">How do they compare and evaluate offers?</font>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid ""
"<font class=\"text-600\">I made sure any visual change is responsive</font>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid ""
"<font class=\"text-600\">I made sure it did not introduce any obvious "
"regression</font>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid ""
"<font class=\"text-600\">I made sure it did not introduce any security "
"flaw</font>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">Job Title 1</font>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">Job Title 2</font>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">Job Title</font>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "<font class=\"text-600\">Lesson A</font>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "<font class=\"text-600\">Lesson B</font>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "<font class=\"text-600\">Lesson C</font>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">Name 1</font>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">Name 2</font>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">Name</font>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_release_note
msgid "<font class=\"text-600\">New Feature 2</font>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">Phone</font>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<font class=\"text-600\">Priority A</font>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<font class=\"text-600\">Priority B</font>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<font class=\"text-600\">Priority C</font>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<font class=\"text-600\">Reminder 1</font>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<font class=\"text-600\">Reminder 2</font>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<font class=\"text-600\">Reminder 3</font>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<font class=\"text-600\">Task A</font>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<font class=\"text-600\">Task B</font>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<font class=\"text-600\">Task C</font>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<font class=\"text-600\">Task D</font>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">What is their buyer's journey?</font>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">What is their key decision criteria?</font>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid ""
"<font class=\"text-600\">What is your action plan to move forward with this account?</font><br>\n"
"                <font class=\"text-600\">Which KPI will be used to measure this progress?</font>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid ""
"<font class=\"text-o-color-2\">\n"
"                    <strong>Planned Next Step:</strong>\n"
"                </font>\n"
"                <br>\n"
"                <br>"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_invite_view_form
msgid ""
"<i class=\"fa fa-w fa-info-circle\"/> All external users you selected won't "
"be added to the members."
msgstr ""
"<i class=\"fa fa-w fa-info-circle\"/> Toți utilizatorii externi selectați nu"
" vor fi adăugați la membri."

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_kanban_items
msgid ""
"<i invisible=\"not is_user_favorite\" class=\"fa fa-star\" title=\"Remove from favorites\"/>\n"
"                                    <i invisible=\"is_user_favorite\" class=\"fa fa-star-o\" title=\"Add to favorites\"/>"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_kanban
msgid ""
"<i invisible=\"not is_user_favorite\" class=\"fa fa-star\" title=\"Remove from favorites\"/>\n"
"                                <i invisible=\"is_user_favorite\" class=\"fa fa-star-o\" title=\"Add to favorites\"/>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid ""
"<i>\n"
"                        <font style=\"color: rgb(0, 255, 0);\"><strong>Green: Values trust above all</strong></font><br>\n"
"                        <font style=\"color: rgb(255, 0, 0);\"><strong>Red: Values results above all</strong></font><br>\n"
"                        <strong>\n"
"                            <font style=\"color: rgb(239, 198, 49);\">\n"
"                                Yellow: Values creativity and enthusiasm above results\n"
"                            </font>\n"
"                        </strong>\n"
"                    </i>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid ""
"<i>\n"
"                    <font style=\"color: rgb(8, 82, 148);\">\n"
"                        <strong>Blue: Expects accurate and rigorous results</strong>\n"
"                    </font>\n"
"                </i>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_release_note
msgid "<span class=\"text-600\">New Feature 1</span>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_example_branding
msgid ""
"<span style=\"color: rgb(255, 255, 255);font-size: 16px;font-style: normal;font-weight: 400;background-color: rgb(113, 75, 103)\">\n"
"                                        5125C\n"
"                                    </span>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<span style=\"font-size: 12px;\">\n"
"                    <font class=\"text-600\">*💡 tick the box when the task is scheduled in the agenda</font>\n"
"                </span>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<span style=\"font-size: 14px\">\n"
"                                    <font class=\"text-600\">Task A</font>\n"
"                                </span>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<span style=\"font-size: 14px\">\n"
"                                    <font class=\"text-600\">Task B</font>\n"
"                                </span>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<span style=\"font-size: 14px\">\n"
"                                    <font class=\"text-600\">Task C</font>\n"
"                                </span>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<span style=\"font-size: 14px;\">\n"
"                                    <font class=\"text-600\">Task A</font>\n"
"                                </span>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<span style=\"font-size: 14px;\">\n"
"                                    <font class=\"text-600\">Task B</font>\n"
"                                </span>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<span style=\"font-size: 14px;\">\n"
"                                    <font class=\"text-600\">Task C</font>\n"
"                                </span>"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"                    Add a checklist\n"
"                    (/<span style=\"font-style: italic;\">checklist</span>)\n"
"                </span>"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"                    Add a separator\n"
"                    (/<span style=\"font-style: italic;\">separator</span>)\n"
"                </span>"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"                    Use\n"
"                    /<span style=\"font-style: italic;\">heading</span>\n"
"                    to convert a text into a title\n"
"                </span>"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">Favorites</font>\n"
"            </span>\n"
"            — Those are\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">shortcuts</font>\n"
"            </span>\n"
"            you create for yourself.\n"
"            Unstar ⭐ this page at the top to remove it from your favorites.\n"
"        </span>"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">Private</font>\n"
"            </span>\n"
"        </span>\n"
"        <span style=\"font-size: 14px;\">\n"
"            — This is\n"
"            <span style=\"font-weight: bolder;\"><font class=\"text-o-color-2\">your stuff</font></span>,\n"
"            the things you keep for yourself\n"
"            (<span style=\"font-style: italic;\">Drafts, Todo lists, ...</span>)\n"
"        </span>"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">Shared</font>\n"
"            </span>\n"
"        </span>\n"
"        <span style=\"font-size: 14px;\">\n"
"            — Those are the ones you\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">have invited someone or been invited to</font>\n"
"            </span>\n"
"        </span>"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">Workspace</font>\n"
"            </span>\n"
"        </span>\n"
"        <span style=\"font-size: 14px;\">\n"
"            — Articles there can be accessed by\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">your team</font>\n"
"        </span>\n"
"    </span>"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"            Below this list, try\n"
"            <span style=\"font-weight: bolder;\">commands</span>\n"
"            by\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">typing</font>\n"
"            </span>\n"
"            \"<span style=\"font-weight: bolder;\">/</span>\"\n"
"        </span>"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"            Content — Just click and\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">start typing</font>\n"
"            </span>\n"
"            (<span style=\"font-style: italic;\">documentation, tips, reports, ...</span>)\n"
"        </span>"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"            Folders —\n"
"        </span>\n"
"        <span style=\"font-size: 14px;\">\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">Nest other Articles</font>\n"
"            </span>\n"
"            under it to regroup them\n"
"            (<span style=\"font-style: italic;\">per team, topic, project, ...</span>)\n"
"        </span>"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"            Select text to\n"
"            <font class=\"bg-o-color-2\">Highlight</font>,\n"
"            <span style=\"text-decoration-line: line-through;\">strikethrough</span>\n"
"            or\n"
"            <span style=\"font-weight: bolder;\">style</span>\n"
"            <span style=\"font-style: italic; text-decoration-line: underline;\">it</span>\n"
"        </span>"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"            Use\n"
"            /<span style=\"font-weight: bolder;\"><font class=\"text-o-color-2\">clipboard</font></span>\n"
"            to insert a\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">clipboard</font>\n"
"            </span>\n"
"            box. Need to re-use its content?\n"
"        </span>"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"            Use\n"
"            /<span style=\"font-weight: bolder;\"><font class=\"text-o-color-2\">file</font></span>\n"
"            to share documents that are frequently needed\n"
"        </span>"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Articles</font>\n"
"        </span>\n"
"        are stored into different\n"
"        <span style=\"font-weight: bolder;\"><font class=\"text-o-color-2\">Sections</font></span>:\n"
"    </span>"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        A good workflow is to write your drafts in\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Private</font>\n"
"        </span>\n"
"        and, once done, move it from\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Private</font>\n"
"        </span>\n"
"        to\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Workspace</font>\n"
"        </span>\n"
"        to share it with everyone.\n"
"    </span>"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        And again, to move an\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Article</font>\n"
"        </span>\n"
"        from a\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Section</font>\n"
"        </span>\n"
"        to another, just\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Drag &amp; Drop</font>\n"
"        </span>\n"
"        it.\n"
"    </span>"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        This is where you and your team can centralize your\n"
"        <font class=\"text-o-color-2\" style=\"font-weight: bolder;\">Knowledge</font>\n"
"        and best practices! 🚀\n"
"    </span>"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        Those are your\n"
"        <span style=\"font-weight: bolder;\"><font class=\"text-o-color-2\">Articles</font></span>.\n"
"    </span>"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        To change the way\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Articles</font>\n"
"        </span>\n"
"        are organized, you can simply\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Drag &amp; Drop</font>\n"
"        </span>\n"
"        them\n"
"    </span>"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        Want to go\n"
"        <span style=\"font-style: italic;\">even</span>\n"
"        faster? ⚡️\n"
"    </span>\n"
"    <br/>\n"
"    <span style=\"font-size: 14px;\">\n"
"        Access\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Articles</font>\n"
"        </span>\n"
"        by opening the\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Command Palette</font>\n"
"        </span>\n"
"        (Ctrl+k/⌘+k) then search through articles by starting your query with\n"
"        \"<span style=\"font-weight: bolder;\">?</span>\".\n"
"    </span>"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        👈 See the\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Menu</font>\n"
"        </span>\n"
"        there, on the left?\n"
"    </span>"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid "<span style=\"font-size: 14px;\">And voilà, it is that simple.</span>"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">Check this box to indicate it's done</span>"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">Click anywhere, and just start "
"typing</span>"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid "<span style=\"font-size: 14px;\">Each of them can be used both as:</span>"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">From any Odoo document, find this article "
"by clicking on the 📗 icon in the chatter.</span>"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">From this box, files can be previewed, "
"forwarded and downloaded. 📁</span>"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">Got it? Now let's try advanced features "
"🧠</span>"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">Need this document somewhere? Come back "
"here by clicking on the 📗 icon in the chatter.</span>"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">Not sure how to do it? Check the video "
"below 👇</span>"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">Press Ctrl+Z/⌘+Z to undo any change</span>"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">This private page is for you to play around with.</span>\n"
"    <br/>\n"
"    <span style=\"font-size: 14px;\">Ready to give it a spin?</span>"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid "<span style=\"font-size: 14px;\">Try the following 👇</span>"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">You can use the clipboard as a description,"
" a message or simply copy it to your clipboard! 👌</span>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid ""
"<span style=\"font-size: 18px;\">\n"
"                                <strong>Color</strong>\n"
"                            </span>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid ""
"<span style=\"font-size: 18px;\">\n"
"                                <strong>Name</strong>\n"
"                            </span>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid ""
"<span style=\"font-size: 18px;\">\n"
"                                <strong>Role</strong>\n"
"                            </span>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid ""
"<span style=\"font-size: 18px;\">\n"
"                    1. How many people will live in this house?\n"
"                </span>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid ""
"<span style=\"font-size: 18px;\">\n"
"                    2. What is your budget?\n"
"                </span>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid ""
"<span style=\"font-size: 18px;\">\n"
"                    3. Which style do you prefer: Natural or Industrial?\n"
"                </span>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid ""
"<span style=\"font-size: 18px;\">\n"
"                    4. What can I do if I haven't found exactly what I wanted?\"\n"
"                </span>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<span style=\"font-size: 18px;\">\n"
"                    Productivity is never an accident. It is always the result of a commitment to excellence, intelligent planning, and focused effort.\n"
"                </span>​\n"
"                - Paul J. Meyer"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid ""
"<span style=\"font-size: 18px;\">\n"
"                    To add more, use the clipboard below 👇🏼\n"
"                </span>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid ""
"<span style=\"font-size: 24px;\">\n"
"                                Make sure to comply as <strong>you will represent <u>our</u> brand</strong>\n"
"                            </span>\n"
"                            <br>\n"
"                            <span style=\"font-size: 24px;\">If in doubt, get in touch with us.</span>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid ""
"<span style=\"font-size: 36px;\">And that's all for this month, folks!<br>\n"
"                    Thanks for reading, see you soon.👋\n"
"                </span>"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid "<span style=\"font-size: 48px;\">🚀</span>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid ""
"<span style=\"font-weight: bolder;\">\n"
"                    Company Newsletter\n"
"                </span>: <font class=\"text-400\">Month</font>"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-1\">YouTube</font>\n"
"        </span>"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-weight: bolder;\"><font class=\"text-o-"
"color-1\">Facebook</font></span>"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-weight: bolder;\"><font class=\"text-o-"
"color-1\">X</font></span>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid ""
"<strong style=\"font-weight: 500\"><span style=\"font-size: 14px\">\n"
"                            Early bird alert</span></strong>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<strong style=\"font-weight: 500\">Optional Tasks</strong>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<strong style=\"font-weight: 500\">⚡ TOP 3 PRIORITIES</strong>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid ""
"<strong>\n"
"                                    <font style=\"color: rgb(148, 189, 123);\">Do</font>\n"
"                                </strong>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">FRIDAY 🏠 @home</font>\n"
"                                </span>\n"
"                            </strong>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">MONDAY 🏢 @office</font>\n"
"                                </span>\n"
"                            </strong>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">Reminders</font>\n"
"                                </span>\n"
"                            </strong>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">SATURDAY 🏠 @home</font>\n"
"                                </span>\n"
"                            </strong>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">SUNDAY 🏠 @home</font>\n"
"                                </span>\n"
"                            </strong>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">THURSDAY 🏢 @office</font>\n"
"                                </span>\n"
"                            </strong>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">TUESDAY 🏢 @office</font>\n"
"                                </span>\n"
"                            </strong>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">WEDNESDAY 🏠 @home</font>\n"
"                                </span>\n"
"                            </strong>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">Name</span>\n"
"                            </strong>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">Strengths</span>\n"
"                            </strong>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">Weaknesses</span>\n"
"                            </strong>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid ""
"<strong><font class=\"text-o-color-2\">PRO TIP</font></strong>: From a lead,"
" use the book button in the chatter to find this article and autocomplete "
"your description with this qualification template."
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid ""
"<strong><font class=\"text-o-color-2\">PRO TIP</font></strong>: From a lead,"
" use the book button in the chatter to find this article and autocomplete "
"your email with this template."
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "<strong><font style=\"color: rgb(231, 99, 99);\">Do Not</font></strong>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "<strong><span style=\"font-size: 18px;\">Change</span></strong>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "<strong><span style=\"font-size: 18px;\">Complexity</span></strong>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "<strong>Actions Taken:</strong>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "<strong>Company</strong>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "<strong>Contact</strong>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "<strong>Do not recreate the Logo from scratch, use these ones</strong>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<strong>Extra Notes:</strong>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<strong>Fast Facts</strong>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "<strong>Lessons Learnt:</strong>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid ""
"<strong>M</strong>onthly\n"
"                            <strong>R</strong>ecurring\n"
"                            <strong>R</strong>evenues\n"
"                            (<em>subscriptions, ...</em>)"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "<strong>MRR</strong>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<strong>Main Point of Contact:</strong>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid ""
"<strong>N</strong>on-<strong>R</strong>ecurring <strong>R</strong>evenues\n"
"                            (<em>consultancy services, ...</em>)"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "<strong>NRR</strong>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<strong>Optional Tasks</strong>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "<strong>Q1,2,3,4</strong>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_release_note
msgid ""
"<strong>Summary</strong>: <font class=\"text-600\">What an exciting release!"
" This time the focus was on...</font>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid ""
"<strong>U</strong>nique <strong>S</strong>elling <strong>P</strong>roposition:\n"
"                            Advantage that makes you stand out from the competition."
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "<strong>USP</strong>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<strong>⚡ TOP 3 PRIORITIES</strong>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid "<u>Demographics</u>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid "<u>Key Decision Factors</u>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid "<u>Strategy</u>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "A day or less"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid ""
"A must listen for all developers out there and anyone interested in "
"Javascript!"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
msgid "Abigail"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid ""
"About YourCompany: YourCompany is a team of passionate people whose goal is to improve everyone's life\n"
"                        through disruptive products.\n"
"                        We build great products to solve your business problems.\n"
"                        <br>\n"
"                        Our products are designed for small to medium size companies willing to optimize their performance."
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "Absent for more than a day"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/js/knowledge_controller.js:0
msgid "Access Denied"
msgstr "Acces interzis"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "Access Restricted. May not be shared with everyone from"
msgstr "Acces restricționat. Nu poate fi partajat cu toată lumea de la"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_account_management
msgid "Account Management"
msgstr "Management Cont"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Account Management Cheat Sheet"
msgstr ""

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__message_needaction
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__message_needaction
msgid "Action Needed"
msgstr "Acțiune necesară"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Action Plan"
msgstr ""

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__active
msgid "Active"
msgstr "Activ"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__activity_ids
msgid "Activities"
msgstr "Activități"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Decorator Excepție Activitate"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__activity_state
msgid "Activity State"
msgstr "Stare activitate"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__activity_type_icon
msgid "Activity Type Icon"
msgstr "Pictograma tipului de activitate"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Add Cover"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Add Icon"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Add Properties"
msgstr "Adaugă proprietăți"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/properties_panel/properties_panel.xml:0
msgid "Add Property Fields"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comment/comment.xml:0
msgid "Add a Comment..."
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/embedded_clipboard_plugin/embedded_clipboard_plugin.js:0
msgid "Add a clipboard section"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/plugins/comments_plugin/comments_plugin.js:0
msgid "Add a comment to an image"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/plugins/comments_plugin/comments_plugin.js:0
msgid "Add a comment to selection"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comments_popover/comments_popover.xml:0
msgid "Add a comment..."
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_icon/knowledge_icon.xml:0
msgid "Add a random icon"
msgstr ""

#. module: knowledge
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_stage_action
msgid ""
"Add an embed kanban view of article items in the body of an article by using"
" '/kanban' command."
msgstr ""

#. module: knowledge
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_favorite_action
msgid ""
"Add articles in your list of favorites by clicking on the <i class=\"fa fa-"
"star-o\"></i> next to the article name."
msgstr ""
"Adăugați articole în lista dvs. de favorite apăsând pe <i class=\"fa fa-"
"star-o\"></i> lângă numele articolului."

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_invite_view_form
msgid "Add people or email addresses"
msgstr "Adăugați persoane sau adrese de e-mail"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "Add people or email addresses..."
msgstr "Adăugați persoane sau adrese de e-mail..."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.js:0
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_hierarchy
msgid "Add to favorites"
msgstr ""

#. module: knowledge
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_member_action
msgid ""
"Adding members allows you to share Articles while granting specific access "
"rights<br>(can write, can read, ...)."
msgstr ""
"Adăugarea membrilor vă permite să partajați articole în timp ce acordați "
"acces la drepturi specifice <br> (pot scrie, pot citi, ...)."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "Admin"
msgstr "Admin"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "Administrator"
msgstr "Administrator"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/webclient/commands/knowledge_providers.js:0
msgid "Advanced Search"
msgstr "Căutare avansată"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "Advertising that would appear online or on TV"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
msgid ""
"After-Sales Service <span class=\"o_stars o_five_stars\" id=\"checkId-5\"><i"
" class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i></span>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
msgid ""
"After-Sales Service <span class=\"o_stars o_five_stars\" id=\"checkId-5\"><i"
" class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
msgid ""
"After-Sales Service <span class=\"o_stars o_five_stars\" id=\"checkId-5\"><i"
" class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
msgid ""
"After-Sales Service​ <span class=\"o_stars o_five_stars\" "
"id=\"checkId-5\"><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid ""
"After-Sales Service​ <span class=\"o_stars o_five_stars\" "
"id=\"checkId-5\"><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i "
"class=\"fa fa-star-o\"></i></span>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid "Age:"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
msgid "Age: 24"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
msgid "Age: 29"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
msgid "Age: 42"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comments_panel/comments_panel.xml:0
msgid "All Discussions"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid "All Models 📖"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/form_status_indicator/form_status_indicator.xml:0
msgid "All changes saved"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "All my activities will always be encoded in our CRM"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "Always include sufficient clear space around the logo"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid ""
"Always type <strong>YourCompany</strong> in the same font size and style as "
"the content of the text"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "An hour or two (medical appointment, ...)"
msgstr ""

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_post_mortem
msgid ""
"Analyze what went wrong and the underlying causes. Extract insights to avoid"
" making similar mistakes in the future."
msgstr ""

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__article_anchor_text
msgid "Anchor Text"
msgstr ""

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_hr_faq
msgid "Answer questions frequently asked by your employees"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search_items
msgid "Archived"
msgstr "Arhivat"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover_dialog.js:0
msgid ""
"Are you sure you want to delete this cover? It will be removed from every "
"article it is used in."
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid ""
"Are you sure you want to leave your private Article? As you are its last "
"member, it will be moved to the Trash."
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.js:0
msgid ""
"Are you sure you want to move \"%(icon)s%(title)s\" to private? Only you "
"will be able to access it."
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.js:0
msgid ""
"Are you sure you want to move \"%(icon)s%(title)s\" to the Shared section? "
"It will be shared with all listed members."
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.js:0
msgid ""
"Are you sure you want to move \"%(icon)s%(title)s\" to the Workspace? It "
"will be shared with all internal users."
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.js:0
msgid ""
"Are you sure you want to move \"%(icon)s%(title)s\" under "
"\"%(parentIcon)s%(parentTitle)s\"? It will be shared with the same persons."
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid ""
"Are you sure you want to remove your member? By leaving an article, you may "
"lose access to it."
msgstr ""
"Sunteți sigur că doriți să eliminați membrul dvs.? Prin părăsirea unui "
"articol, puteți pierde accesul la acesta."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid "Are you sure you want to remove your own \"Write\" access?"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid ""
"Are you sure you want to restore access? This means this article will now "
"inherit any access set on its parent articles."
msgstr ""
"Sunteți sigur că doriți să restaurați accesul? Acest lucru înseamnă că acest"
" articol va moșteni acum orice acces setat pe articolele părinte."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid ""
"Are you sure you want to restrict access to this article? This means it will"
" no longer inherit access rights from its parents."
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/views/item_calendar/item_calendar_view.js:0
msgid "Are you sure you want to send this article to the trash?"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid ""
"Are you sure you want to set the internal permission to \"none\"? If you do,"
" you will no longer have access to the article."
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid ""
"Are you sure you want to set your permission to \"none\"? If you do, you "
"will no longer have access to the article."
msgstr ""
"Sunteți sigur că doriți să setați permisiunea dvs. la \"niciunul\"? Dacă "
"faceți asta, nu veți mai avea acces la articol."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/plugins/article_plugin/article_plugin.js:0
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_favorite__article_id
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_member__article_id
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__article_id
#: model:ir.model.fields,field_description:knowledge.field_knowledge_invite__article_id
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_favorite_view_search
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_member_view_search
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_stage_view_search
msgid "Article"
msgstr "Articol"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article_member.py:0
msgid ""
"Article '%s' should always have a writer: inherit write permission, or have "
"a member with write access"
msgstr ""
"Articolul '%s' ar trebui să aibă întotdeauna un autor: moșteniți permisiunea"
" de scriere sau aveți un membru cu acces de scriere"

#. module: knowledge
#: model:ir.model,name:knowledge.model_knowledge_article_thread
msgid "Article Discussion Thread"
msgstr ""

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__article_properties_definition
msgid "Article Item Properties"
msgstr "Proprietăți ale elementului articolului"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.js:0
#: model:ir.actions.act_window,name:knowledge.knowledge_article_action_item_calendar
#: model:ir.actions.act_window,name:knowledge.knowledge_article_item_action
#: model:ir.actions.act_window,name:knowledge.knowledge_article_item_action_stages
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search
msgid "Article Items"
msgstr "Elemente de articol"

#. module: knowledge
#: model:ir.model,name:knowledge.model_knowledge_article_member
msgid "Article Member"
msgstr "Membru al articolului"

#. module: knowledge
#: model:ir.model,name:knowledge.model_knowledge_article_template_category
msgid "Article Template Category"
msgstr ""

#. module: knowledge
#: model:ir.actions.act_window,name:knowledge.knowledge_article_template_action
msgid "Article Templates"
msgstr ""

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__article_url
msgid "Article URL"
msgstr "URL-ul articolului"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__cover_image_id
msgid "Article cover"
msgstr "Copertă articol"

#. module: knowledge
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_action_item_calendar
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_item_action
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_item_action_stages
msgid ""
"Article items are articles that exist inside their parents but are not displayed in the menu.\n"
"                They can be used to handle lists (Buildings, Tasks, ...)."
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid ""
"Article items are not showed in the left-side menu\n"
"but are shown in inserted kanban/list views"
msgstr ""

#. module: knowledge
#: model:ir.model.constraint,message:knowledge.constraint_knowledge_article_check_article_item_parent
msgid "Article items must have a parent."
msgstr "Elementele de articol trebuie să aibă un părinte."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "Article shared with you: %s"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/move_article_dialog/move_article_dialog.js:0
#: model:ir.actions.act_window,name:knowledge.knowledge_article_action
#: model:ir.actions.act_window,name:knowledge.knowledge_article_action_form
#: model:ir.actions.act_window,name:knowledge.knowledge_article_action_form_show_resolved
#: model:ir.actions.server,name:knowledge.ir_actions_server_knowledge_home_page
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search
msgid "Articles"
msgstr "Articole"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"Articles %s cannot be updated as this would create a recursive hierarchy."
msgstr ""
"Articolele %s nu pot fi actualizate deoarece ar crea o ierarhie recursivă."

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_cover__article_ids
msgid "Articles using cover"
msgstr "Articole care folosesc coperta"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "As a reader, you can't leave a Workspace article"
msgstr "Ca cititor, nu puteți părăsi un articol Workspace"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid ""
"As an administrator, you can always modify this article and its members."
msgstr ""
"Ca administrator, puteți modifica întotdeauna acest articol și membrii săi."

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__message_attachment_count
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__message_attachment_count
msgid "Attachment Count"
msgstr "Număr atașamente"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid "Available Models ✅"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid "BOOK NOW"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Background:"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "Backlog"
msgstr "Comenzi Nerezolvate"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "Base Color On"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "Based on"
msgstr "Bazat pe"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Be assertive but listen to what is being said"
msgstr ""

#. module: knowledge
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_action
msgid "Be the first one to unleash the power of Knowledge!"
msgstr ""

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__body
msgid "Body"
msgstr "Conținut"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "Booklets for a total price &gt; $1000"
msgstr ""

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_brand_assets
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "Brand Assets"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
msgid ""
"Brand Attraction <span class=\"o_stars o_five_stars\" id=\"checkId-3\"><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star-o\"></i></span>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
msgid ""
"Brand Attraction <span class=\"o_stars o_five_stars\" id=\"checkId-3\"><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
msgid ""
"Brand Attraction <span class=\"o_stars o_five_stars\" id=\"checkId-3\"><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid ""
"Brand Attraction <span class=\"o_stars o_five_stars\" id=\"checkId-3\"><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "Brand Name Rules"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.xml:0
msgid "Browse Templates"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_release_note
msgid "Bug Fixes 🔨"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.xml:0
msgid "Build an Item Calendar"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.xml:0
msgid "Build an Item Kanban"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.xml:0
msgid "Build an Item List"
msgstr ""

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_marketing_customer_personas
msgid ""
"Build fictional representation of your customers to better tailor your "
"advertising messages for them. "
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_release_note
msgid "But Also..."
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Buying Process"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.js:0
msgid "Calendar of %s"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.js:0
msgid "Calendar of Article Items"
msgstr ""

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__user_can_write
msgid "Can Edit"
msgstr "Poate edita"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__user_can_read
msgid "Can Read"
msgstr "Poate citi"

#. module: knowledge
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article__inherited_permission__write
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article__internal_permission__write
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article_member__permission__write
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_invite__permission__write
msgid "Can edit"
msgstr ""

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__is_article_visible_by_everyone
msgid "Can everyone see the Article?"
msgstr ""

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article_member.py:0
msgid "Can not update the article or partner of a member."
msgstr "Nu se poate actualiza articolul sau partenerul unui membru."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article_favorite.py:0
msgid "Can not update the article or user of a favorite."
msgstr "Nu se poate actualiza articolul sau utilizatorul unui favorit."

#. module: knowledge
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article__inherited_permission__read
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article__internal_permission__read
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article_member__permission__read
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_invite__permission__read
msgid "Can read"
msgstr "Poate citi"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__user_has_access_parent_path
msgid "Can the user join?"
msgstr ""

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__is_article_visible
msgid "Can the user see the article?"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/article_selection_dialog/article_selection_dialog.xml:0
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover.xml:0
#: code:addons/knowledge/static/src/components/move_article_dialog/move_article_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_invite_view_form
msgid "Cancel"
msgstr "Anulează"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"Cannot create an article under article %(parent_name)s which is a non-"
"private parent"
msgstr ""
"Nu se poate crea un articol sub articolul %(parent_name)s care este un "
"parinte non-privat"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid ""
"Capitalize the word <strong>YourCompany</strong>, except if it's part of an "
"URL e.g. website/company"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "Car Policy"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/move_article_dialog/move_article_dialog.js:0
msgid "Categories"
msgstr "Categorii"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_template_category__sequence
msgid "Category Sequence"
msgstr "Secvență categorie"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "Causes"
msgstr ""

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_account_management
msgid ""
"Centralize account insights in one document for comprehensive monitoring and"
" follow-up."
msgstr ""

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_meeting_minutes
msgid ""
"Centralize team meetings in a single article, while making sure notes are "
"handled efficiently."
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Challenges &amp; Competitive Landscape<br>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "Change 1"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "Change 2"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "Change 3"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid "Change Permission"
msgstr "Schimbă permisiunea"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover.xml:0
msgid "Change cover"
msgstr ""

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__child_ids
msgid "Child Articles and Items"
msgstr "Articole și elemente copil"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover_dialog.xml:0
msgid "Choose a nice cover"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/article_selection_dialog/article_selection_dialog.js:0
#: code:addons/knowledge/static/src/components/move_article_dialog/move_article_dialog.js:0
msgid "Choose an Article..."
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover.xml:0
msgid "Click and hold to reposition"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/core/clipboard/embedded_clipboard.xml:0
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/embedded_clipboard_plugin/embedded_clipboard_plugin.js:0
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Clipboard"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.js:0
#: code:addons/knowledge/static/src/js/knowledge_controller.js:0
#: code:addons/knowledge/static/src/macros/abstract_macro.js:0
msgid "Close"
msgstr "Închide"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/plugins/comments_plugin/comments_plugin.js:0
msgid "Comment"
msgstr "Comentariu"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Company Abbreviations"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Company Details"
msgstr "Detalii companie"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Company Location:"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Company Name:"
msgstr "Numele companiei:"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_company_newsletter
msgid "Company Newsletter"
msgstr ""

#. module: knowledge
#: model:knowledge.article.template.category,name:knowledge.knowledge_article_template_category_company_organization
msgid "Company Organization"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Company Structure:"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid ""
"Compiled below are tips &amp; tricks collected among our veterans to help "
"newcomers get started. We hope it will help you sign deals."
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Complicated buying process"
msgstr ""

#. module: knowledge
#: model:ir.ui.menu,name:knowledge.knowledge_menu_configuration
msgid "Configuration"
msgstr "Configurare"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
#: code:addons/knowledge/static/src/views/item_calendar/item_calendar_view.js:0
msgid "Confirmation"
msgstr "Confirmare"

#. module: knowledge
#: model:ir.model,name:knowledge.model_res_partner
msgid "Contact"
msgstr "Contactați"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid "Contact Us"
msgstr "Contactați-ne"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_shared_todos_contact_our_lawyer
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_shared_todos_contact_our_lawyer
msgid "Contact our Lawyer"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/core/clipboard/embedded_clipboard.js:0
msgid "Content copied to clipboard."
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Contract Due Date:"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Convert into Article"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Convert into Article Item"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/core/clipboard/embedded_clipboard.xml:0
msgid "Copy"
msgstr "Copie"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/embedded_view_link/embedded_view_link_popover.xml:0
msgid "Copy Link"
msgstr "Copiază link"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/core/clipboard/embedded_clipboard.xml:0
msgid "Copy to Clipboard"
msgstr "Copiați în Clipboard"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Costing too much money"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.js:0
msgid ""
"Could not move \"%(icon)s%(title)s\" under "
"\"%(parentIcon)s%(parentTitle)s\", because you do not have write permission "
"on the latter."
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid ""
"Could you please let me know on which number I could reach you so that we could get in touch?<br>\n"
"                        It should not take longer than 15 minutes."
msgstr ""

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_cover__attachment_url
msgid "Cover URL"
msgstr "Copertă URL"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_cover__attachment_id
msgid "Cover attachment"
msgstr "Copertă atașament"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__cover_image_url
msgid "Cover url"
msgstr "Copertă url"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__cover_image_position
msgid "Cover vertical offset"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
#: code:addons/knowledge/static/src/xml/knowledge_command_palette.xml:0
msgid "Create \""
msgstr "Creează \""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/article_selection_dialog/article_selection_dialog.js:0
msgid "Create \"%s\""
msgstr "Creează \"%s\""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Create a Copy"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar_row.xml:0
msgid "Create a nested article"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar_section.xml:0
msgid "Create a new article in workspace"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar_section.xml:0
msgid "Create a new private article"
msgstr ""

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_product_catalog
msgid ""
"Create a simple catalog to provide technical details about your products."
msgstr ""

#. module: knowledge
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_action_item_calendar
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_item_action
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_item_action_stages
msgid "Create an Article Item"
msgstr ""

#. module: knowledge
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_action
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_form
msgid "Create an article"
msgstr "Creează un articol"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Created"
msgstr "Creat(ă)"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__create_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_favorite__create_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_member__create_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_stage__create_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_template_category__create_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__create_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_cover__create_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_invite__create_uid
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_tree
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_tree_items
msgid "Created by"
msgstr "Creat de"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__create_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_favorite__create_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_member__create_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_stage__create_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_template_category__create_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__create_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_cover__create_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_invite__create_date
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_tree
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_tree_items
msgid "Created on"
msgstr "Creat pe"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Current Contract:"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Current Satisfaction:"
msgstr ""

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_marketing_customer_personas
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid "Customer Personas"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "DESIGN PROTOTYPE"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.js:0
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "Date Properties"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.js:0
msgid "Date and Time Properties"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_trash_notification
msgid "Dear"
msgstr "Dragă"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "Decision 1"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "Decision 2"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "Decision 3"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "Decision 4"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "Default Access Rights"
msgstr "Drepturi de acces implicite"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "Default Scale"
msgstr "Scara Implicită"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__internal_permission
msgid ""
"Default permission for all internal users. (External users can still have "
"access to this article if they are added to its members)"
msgstr ""
"Permisiune implicită pentru toți utilizatorii interni. (Utilizatorii externi"
" pot totuși să aibă acces la acest articol dacă sunt adăugați la membrii "
"săi)"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"Deleted articles are stored in Trash an extra <b>%(threshold)s</b> days\n"
"                 before being permanently removed for your database"
msgstr ""

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__deletion_date
msgid "Deletion Date"
msgstr "Data ștergerii"

#. module: knowledge
#: model:ir.model.constraint,message:knowledge.constraint_knowledge_article_check_permission_on_desync
msgid "Desynchronized articles must have internal permission."
msgstr "Articolele desincronizate trebuie să aibă permisiune internă."

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__is_desynchronized
msgid "Desyncronized with parents"
msgstr "Desincronizat cu părinții"

#. module: knowledge
#: model_terms:digest.tip,tip_description:knowledge.digest_tip_knowledge_0
msgid ""
"Did you know that access rights can be defined per user on any Knowledge "
"Article?"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/article_template_picker_dialog/article_template_picker_dialog.xml:0
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover_dialog.xml:0
#: code:addons/knowledge/static/src/components/prompt_embedded_view_name_dialog/prompt_embedded_view_name_dialog.xml:0
#: code:addons/knowledge/static/src/editor/embedded_components/backend/embedded_view_link/embedded_view_link_edit_dialog.xml:0
msgid "Discard"
msgstr "Abandonează"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__display_name
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_favorite__display_name
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_member__display_name
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_stage__display_name
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_template_category__display_name
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__display_name
#: model:ir.model.fields,field_description:knowledge.field_knowledge_cover__display_name
#: model:ir.model.fields,field_description:knowledge.field_knowledge_invite__display_name
msgid "Display Name"
msgstr "Nume afișat"

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_brand_assets
msgid ""
"Distribute brand digital assets while ensuring compliance with company "
"policies and guidelines."
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid "Diving in"
msgstr ""

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_marketing_campaign_brief
msgid ""
"Document your Marketing Campaigns to prioritize key objectives and outcomes."
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid ""
"Don't forget to spread the word, we're <em>so</em> looking forward to "
"unveiling this new Odoo version! 🥳"
msgstr ""

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
#: model:knowledge.article.stage,name:knowledge.knowledge_article_template_stage_done
msgid "Done"
msgstr "Efectuat"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.xml:0
msgid "Drop here to delete this article"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/views/list_view.js:0
msgid "Duplicate"
msgstr "Duplicare"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_mail_invite
msgid "EDIT"
msgstr "EDITAȚI "

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/embedded_view_actions_menu/embedded_view_actions_menu.xml:0
msgid "Edit"
msgstr "Editare"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/embedded_view_link/embedded_view_link_edit_dialog.xml:0
#: code:addons/knowledge/static/src/editor/embedded_components/backend/embedded_view_link/embedded_view_link_popover.xml:0
msgid "Edit Link"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid "Education:"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
msgid "Education: Bachelor's degree in Marketing"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
msgid "Education: High school diploma"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
msgid "Education: PhD."
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
msgid "Education: Student"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid "Egg'cellent run"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Email:"
msgstr "Email:"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/prompt_embedded_view_name_dialog/prompt_embedded_view_name_dialog.js:0
msgid "Embed a View"
msgstr "Încorporați o vizualizar"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__icon
msgid "Emoji"
msgstr "Emoticon"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.js:0
msgid "End Date Time"
msgstr ""

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_software_specification
msgid ""
"Ensure all stakeholders of a product change are aligned by clearly "
"communicating the requirements."
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
#: code:addons/knowledge/static/src/macros/abstract_macro.js:0
msgid "Error"
msgstr "Eroare"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Estimated Revenues:"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid "Events 🌍"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "Everyone"
msgstr "Toți"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "Expense Policy"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Export"
msgstr "Export"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_invite_view_form
msgid "Extra Comment"
msgstr "Comentarii suplimentare"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "Extra Technical Instructions:"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_favorite_view_form
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search
msgid "Favorite"
msgstr "Favorite"

#. module: knowledge
#: model:ir.model,name:knowledge.model_knowledge_article_favorite
msgid "Favorite Article"
msgstr "Articol favorit"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__favorite_ids
msgid "Favorite Articles"
msgstr "Articole favorite"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar_section.xml:0
#: model:ir.actions.act_window,name:knowledge.knowledge_article_favorite_action
#: model:ir.ui.menu,name:knowledge.knowledge_article_favorite_menu
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_favorite_view_tree
msgid "Favorites"
msgstr "Favorite"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.portal_my_home_knowledge
msgid "Find all articles shared with you"
msgstr ""

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_stage__fold
msgid "Folded in kanban view"
msgstr "Pliat în Kanban"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__message_follower_ids
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__message_follower_ids
msgid "Followers"
msgstr "Urmăritori"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__message_partner_ids
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__message_partner_ids
msgid "Followers (Partners)"
msgstr "Urmăritori (Parteneri)"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Pictogramă Font awesome, de ex. fa-sarcini"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid ""
"For all other categories, we simply require you to follow the rules listed "
"below."
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Full Width"
msgstr "Lățime Maximă"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__full_width
msgid "Full width"
msgstr "Lățime completă"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid "Gender(s):"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
msgid "Gender(s): M"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
msgid "Gender(s): W"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.xml:0
msgid "Generate an Article with AI"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_favorite_view_search
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_member_view_search
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_stage_view_search
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_template_view_search
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search_items
msgid "Group By"
msgstr "Grupează după"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "Guest"
msgstr "Oaspete"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_hr_faq
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "HR FAQ"
msgstr ""

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__user_has_access
msgid "Has Access"
msgstr "Are acces"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__has_message
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__has_message
msgid "Has Message"
msgstr "Are mesaj"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__user_has_write_access
msgid "Has Write Access"
msgstr "Are acces de scriere"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__has_item_children
msgid "Has article item children?"
msgstr "Are copil articolul?"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__has_article_children
msgid "Has normal article children?"
msgstr "Are copil articolul?"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__user_has_access_parent_path
msgid ""
"Has the user access to each parent from current article until its root?"
msgstr ""

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_invite__have_share_partners
msgid "Have Share Partners"
msgstr "Au parteneri de partajare"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid "Hello there"
msgstr "Salut"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"Hello there, I am a template 👋\n"
"            <br/>\n"
"            Use the buttons at the top-right of this box to re-use my content.\n"
"            <br/>\n"
"            No more time wasted! 🔥"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid ""
"Here are logos that you can use at your own convenience.\n"
"                <br>\n"
"                They can also be shared with customers, journalists and resellers."
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid ""
"Here is a short guide that will help you pick the right tiny house for you."
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Hey ProspectName,"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/xml/knowledge_command_palette.xml:0
msgid "Hidden"
msgstr "Ascuns"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comments_panel/comments_panel.xml:0
msgid "Highlight content and use the"
msgstr ""

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__html_field_history
msgid "History data"
msgstr ""

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__html_field_history_metadata
msgid "History metadata"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.xml:0
#: model:ir.ui.menu,name:knowledge.knowledge_menu_home
msgid "Home"
msgstr "Acasă"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "Hours Display"
msgstr ""

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_product_catalog_house_cielo
msgid "House Model \"Cielo\""
msgstr ""

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_product_catalog_house_dolcezza
msgid "House Model \"Dolcezza\""
msgstr ""

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_product_catalog_house_incanto
msgid "House Model \"Incanto\""
msgstr ""

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_product_catalog_house_serenita
msgid "House Model \"Serenità\""
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog_house_cielo
msgid "House Model - Cielo"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog_house_dolcezza
msgid "House Model - Dolcezza"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog_house_incanto
msgid "House Model - Incanto"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog_house_serenita
msgid "House Model - Serenità"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "How do I know which model I can order or not?"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "How they measure success:"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid "How to find the perfect model for your needs 😍"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid ""
"I was doing some research online and found your company.<br>\n"
"                        Considering we just launched ProductName, I was thinking you would be interested."
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "I will not steal prospects from colleagues"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "I will not waste time and energy bad-mouthing competitors"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "I will only sell a project if I am convinced it can be a success"
msgstr ""

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__id
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_favorite__id
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_member__id
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_stage__id
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_template_category__id
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__id
#: model:ir.model.fields,field_description:knowledge.field_knowledge_cover__id
#: model:ir.model.fields,field_description:knowledge.field_knowledge_invite__id
msgid "ID"
msgstr "ID"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__activity_exception_icon
msgid "Icon"
msgstr "Pictogramă"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Pictograma care indică o activitate de excepție."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Identify the pain points and offer clear solutions"
msgstr ""

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__message_needaction
#: model:ir.model.fields,help:knowledge.field_knowledge_article_thread__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Dacă este bifat, mesaje noi necesită atenția dumneavoastră."

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__message_has_error
#: model:ir.model.fields,help:knowledge.field_knowledge_article__message_has_sms_error
#: model:ir.model.fields,help:knowledge.field_knowledge_article_thread__message_has_error
#: model:ir.model.fields,help:knowledge.field_knowledge_article_thread__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Dacă este bifată, există mesaje cu eroare de livrare."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid ""
"If none of those offers convinced you, just get in touch with our "
"team.<br>At MyCompany, your happiness is our utmost priority, and we'll go "
"the extra mile to make sure you find what you're looking for!"
msgstr ""

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__is_desynchronized
msgid ""
"If set, this article won't inherit access rules from its parents anymore."
msgstr ""
"Dacă este setat, acest articol nu va moșteni mai mult regulile de acces de "
"la "

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "Impact"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_release_note
msgid "Improvements 🔬"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid "Income:"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
msgid "Income: $109,160"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
msgid "Income: $142,170"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
msgid "Income: $293,650"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
msgid "Income: $68,170"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Inconsistent customer experience"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/article_index_plugin/article_index_plugin.js:0
msgid "Index"
msgstr "Index"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid "Industrial ⚙-"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Industry:"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "Inform HR and your Team Leader."
msgstr ""

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_release_note
msgid "Inform users about your latest software updates and improvements."
msgstr ""

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__inherited_permission
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_member__article_permission
msgid "Inherited Permission"
msgstr "Permisiune moștenită"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__inherited_permission_parent_id
msgid "Inherited Permission Parent Article"
msgstr "Articolul părinte cu permisiune moștenită   "

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
#: code:addons/knowledge/static/src/components/prompt_embedded_view_name_dialog/prompt_embedded_view_name_dialog.xml:0
msgid "Insert"
msgstr "Introduceți"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/plugins/article_plugin/article_plugin.js:0
msgid "Insert Link"
msgstr "Introduceți un link"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "Insert a Calendar View"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/embedded_view_plugin/embedded_view_plugin.js:0
msgid "Insert a Calendar view of article items"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/embedded_view_plugin/embedded_view_plugin.js:0
msgid "Insert a Card view of article items"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/prompt_embedded_view_name_dialog/prompt_embedded_view_name_dialog.js:0
msgid "Insert a Kanban View"
msgstr "Introduceți o vizualizare Kanban"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/embedded_view_plugin/embedded_view_plugin.js:0
msgid "Insert a Kanban view of article items"
msgstr "Introduceți o vizualizare Kanban a elementelor articolului"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/prompt_embedded_view_name_dialog/prompt_embedded_view_name_dialog.js:0
msgid "Insert a List View"
msgstr "Introduceți o vizualizare Listă"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/embedded_view_plugin/embedded_view_plugin.js:0
msgid "Insert a List view of article items"
msgstr "Introduceți o vizualizare Listă a elementelor articolului"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/plugins/article_plugin/article_plugin.js:0
msgid "Insert an Article shortcut"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/external_embedded_view_favorite_menu/insert_embedded_view.xml:0
msgid "Insert link in article"
msgstr "Introduceți un link în articol"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/external_embedded_view_favorite_menu/insert_embedded_view.xml:0
msgid "Insert view in article"
msgstr "Introduceți o vizualizare în articol"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid ""
"Instead of setting up complicated processes, <strong>we prefer to let our "
"employees buy whatever they need</strong>. Just fill in an expense and we "
"will reimburse you."
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid "Interests:"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
msgid "Interests: Cooking"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
msgid "Interests: Music"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
msgid "Interests: Politics"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
msgid "Interests: Science"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__internal_permission
msgid "Internal Permission"
msgstr "Permisiune internă"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "Invitation to access an article"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_invite_view_form
msgid "Invite"
msgstr "Invită"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "Invite People"
msgstr "Invitați persoane"

#. module: knowledge
#: model:ir.actions.act_window,name:knowledge.knowledge_invite_action_from_article
msgid "Invite people"
msgstr "Invită persoane"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_favorite__is_article_active
msgid "Is Article Active"
msgstr "Este articolul activ"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__is_user_favorite
msgid "Is Favorited"
msgstr "Este favorit"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__message_is_follower
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__message_is_follower
msgid "Is Follower"
msgstr "Este urmăritor"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__is_article_item
msgid "Is Item?"
msgstr "Este element?"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__is_template
msgid "Is Template"
msgstr ""

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__has_item_parent
msgid "Is the parent an Item?"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "Issue Summary"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Issues they are trying to solve:"
msgstr ""

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__template_category_sequence
#: model:ir.model.fields,help:knowledge.field_knowledge_article_template_category__sequence
msgid "It determines the display order of the category"
msgstr ""

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__template_sequence
msgid "It determines the display order of the template within its category"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "Item 1"
msgstr "Articol 1"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "Item 2"
msgstr "Articol 2"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "Item 3"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "Item 4"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/embedded_view_plugin/embedded_view_plugin.js:0
msgid "Item Calendar"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/embedded_view_plugin/embedded_view_plugin.js:0
msgid "Item Cards"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/embedded_view_plugin/embedded_view_plugin.js:0
msgid "Item Kanban"
msgstr "Element Kanban"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/embedded_view_plugin/embedded_view_plugin.js:0
msgid "Item List"
msgstr "Element Listă"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__stage_id
msgid "Item Stage"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_calendar_items
msgid "Items"
msgstr "Elemente"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Job Position:"
msgstr "Funcție:"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_form
msgid "Join"
msgstr "Aderă"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar_section.xml:0
msgid "Join a hidden article"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
msgid "Julius"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.js:0
msgid "Kanban of %s"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.js:0
msgid "Kanban of Article Items"
msgstr ""

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_shared_todos
msgid "Keep track of your company to-dos and share them with your colleagues."
msgstr ""

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_company_newsletter
msgid ""
"Keep your colleagues informed about the company's latest developments and "
"activities through periodic updates."
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/external_embedded_view_favorite_menu/insert_embedded_view.xml:0
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/article_index_plugin/article_index_plugin.js:0
#: model:ir.ui.menu,name:knowledge.knowledge_menu_root
#: model:ir.ui.menu,name:knowledge.knowledge_menu_technical
#: model_terms:ir.ui.view,arch_db:knowledge.portal_my_home_knowledge
msgid "Knowledge"
msgstr "Cunoștințe"

#. module: knowledge
#: model:ir.model,name:knowledge.model_knowledge_article
msgid "Knowledge Article"
msgstr "Articol de cunoștințe"

#. module: knowledge
#: model:ir.model,name:knowledge.model_knowledge_cover
msgid "Knowledge Cover"
msgstr "Copertă de cunoștințe"

#. module: knowledge
#: model:ir.model,name:knowledge.model_knowledge_invite
msgid "Knowledge Invite Wizard"
msgstr "Invitație de cunoștințe"

#. module: knowledge
#: model:ir.model,name:knowledge.model_knowledge_article_stage
msgid "Knowledge Stage"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_tree
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_tree_items
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_tree_trash
msgid "Last Edit Date"
msgstr "Data ultimei modificări"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Last Edited"
msgstr ""

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__last_edition_uid
msgid "Last Edited by"
msgstr "Ultima modificare de către"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__last_edition_date
msgid "Last Edited on"
msgstr "Ultima modificare la"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__write_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_favorite__write_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_member__write_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_stage__write_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_template_category__write_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__write_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_cover__write_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_invite__write_uid
msgid "Last Updated by"
msgstr "Ultima actualizare făcută de"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__write_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_favorite__write_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_member__write_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_stage__write_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_template_category__write_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__write_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_cover__write_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_invite__write_date
msgid "Last Updated on"
msgstr "Ultima actualizare pe"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid "Leave"
msgstr "Părăsește"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "Leave Article"
msgstr "Părăsiți articolul"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid "Leave Private Article"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "Leaves &amp; Time Off"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "Let your Team Leader know in advance."
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/core/embedded_view_link/embedded_view_link_style.js:0
msgid "Link"
msgstr "Link"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/plugins/article_plugin/article_plugin.js:0
msgid "Link an Article"
msgstr "Legați un articol"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/embedded_view_link_plugin/embedded_view_link_plugin.js:0
msgid "Link copied to clipboard."
msgstr "Linkul a fost copiat în clipboard!"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.js:0
msgid "List of %s"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.js:0
msgid "List of Article Items"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comments_panel/comments_panel.xml:0
msgid "Load More Discussions"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/article_template_picker_dialog/article_template_picker_dialog.xml:0
msgid "Load Template"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.xml:0
msgid "Load a Template"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Lock Content"
msgstr ""

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__is_locked
msgid "Locked"
msgstr "Blocat"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"Logging changes from %(partner_name)s without write access on article "
"%(article_name)s due to hierarchy tree update"
msgstr ""
"Înregistrarea modificărilor de la %(partner_name)s fără acces de scriere la "
"articolul %(article_name)s din cauza actualizării arborelui de ierarhie"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "Logo"
msgstr "Sigla"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "Logos should only be used in the colors provided"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid "Lose Access"
msgstr ""

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_personal_organizer
msgid ""
"Make every week a success by proactively organizing your priority and "
"optional tasks."
msgstr ""

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_sprint_calendar
msgid "Manage your team schedule for the upcoming sprint."
msgstr ""

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article_thread.py:0
msgid "Mark Comment as Closed"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/mail/message/message_actions.js:0
msgid "Mark the discussion as resolved"
msgstr ""

#. module: knowledge
#: model:knowledge.article.template.category,name:knowledge.knowledge_article_template_category_marketing
msgid "Marketing"
msgstr "Marketing"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_marketing_campaign_brief
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_campaign_brief
msgid "Marketing Campaign Brief"
msgstr ""

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_sprint_calendar_meeting
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sprint_calendar_meeting
msgid "Meeting"
msgstr "Întâlnire"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_meeting_example_branding
msgid "Meeting Example"
msgstr ""

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_meeting_minutes
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_meeting_minutes_template
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes
msgid "Meeting Minutes"
msgstr "Minută sedință"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_example_branding
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "Meeting Minutes Template"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_member_view_form
msgid "Member"
msgstr "Membru"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
#: model:ir.actions.act_window,name:knowledge.knowledge_article_member_action
#: model:ir.ui.menu,name:knowledge.knowledge_article_member_menu
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_member_view_tree
msgid "Members"
msgstr "Membri"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__article_member_ids
msgid "Members Information"
msgstr "Informații membri"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__root_article_id
msgid "Menu Article"
msgstr "Meniu articol"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_invite__message
msgid "Message"
msgstr "Mesaj"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__message_has_error
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__message_has_error
msgid "Message Delivery error"
msgstr "Eroare de livrare a mesajului"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/mail/message/message_model_patch.js:0
msgid "Message Link Copied!"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/mail/message/message_model_patch.js:0
msgid "Message Link Copy Failed (Permission denied?)!"
msgstr ""

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__message_ids
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__message_ids
msgid "Messages"
msgstr "Mesaje"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/views/item_calendar/item_calendar_views.xml:0
msgid "Missing Calendar configuration."
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "More actions"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid ""
"More than 150 Odooers participated in this years' edition of the run of 5 or 11\n"
"                            kilometers.<br>Starting from the office, they enjoyed a great tour in the countryside before\n"
"                            coming back to Grand-Rosière, where they were welcomed with a drink and a burger."
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/move_article_dialog/move_article_dialog.js:0
msgid "Move \"%s\" under:"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/move_article_dialog/move_article_dialog.xml:0
msgid "Move Article"
msgstr "Mută articolul"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Move To"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/move_article_dialog/move_article_dialog.xml:0
msgid "Move an Article"
msgstr "Mută un articol"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.js:0
msgid "Move cancelled"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/move_article_dialog/move_article_dialog.xml:0
msgid "Move the untitled article under:"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.js:0
msgid "Move to Private"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.js:0
msgid "Move to Shared"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid "Move to Trash"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.js:0
msgid "Move to Workspace"
msgstr "Mutați-vă în spațiul de lucru"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Data limită a activității mele"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search_items
msgid "My Favorites"
msgstr "Favorite mele"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "My Forecast will always be accurate and up-to-date"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search_items
msgid "My Items"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_form_item_quick_create
msgid "My New Item"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/embedded_view_link/embedded_view_link_edit_dialog.xml:0
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_stage__name
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_template_view_tree
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_tree
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_tree_items
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_tree_trash
msgid "Name"
msgstr "Nume"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid "Natural Style ☘ -"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid "Navigation Basics 🐣"
msgstr ""

#. module: knowledge
#. odoo-javascript
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
#: code:addons/knowledge/static/src/js/knowledge_controller.js:0
#: code:addons/knowledge/static/src/views/item_calendar/item_calendar_views.xml:0
#: model:knowledge.article.stage,name:knowledge.knowledge_article_template_stage_new
msgid "New"
msgstr "Nou"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_release_note
msgid "New Features 🎉"
msgstr ""

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article_thread.py:0
msgid "New Mention in %s"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.js:0
msgid "New property could not be created."
msgstr ""

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Următoarea activitate din calendar"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Data limită pentru următoarea activitate"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__activity_summary
msgid "Next Activity Summary"
msgstr "Sumarul următoarei activități"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__activity_type_id
msgid "Next Activity Type"
msgstr "Următorul tip de activitate"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "Next Meeting: <font class=\"text-400\">@Date</font>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_example_branding
msgid "Next Meeting: <u>6th May, @John's Office</u>"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/xml/knowledge_command_palette.xml:0
msgid "No Article found."
msgstr "Nu a fost găsit niciun articol."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/webclient/commands/knowledge_providers.js:0
msgid "No Article found. Create \"%s\""
msgstr "Nu a fost găsit niciun articol. Creează \"%s\""

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "No Article in Trash"
msgstr "Niciun articol în coșul de gunoi"

#. module: knowledge
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_favorite_action
msgid "No Favorites yet!"
msgstr "Nu există favorite încă!"

#. module: knowledge
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_member_action
msgid "No Members yet!"
msgstr "Nu există membri încă!"

#. module: knowledge
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article__inherited_permission__none
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article__internal_permission__none
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article_member__permission__none
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_invite__permission__none
msgid "No access"
msgstr "Fără acces"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/webclient/commands/knowledge_providers.js:0
msgid "No article found."
msgstr "Nu a fost găsit niciun articol."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/core/article_index/readonly_article_index.xml:0
msgid "No article to display"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_form
msgid "No article yet."
msgstr "Nu există articole încă."

#. module: knowledge
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_stage_action
msgid "No stage yet!"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/article_template_picker_dialog/article_template_picker_dialog.xml:0
msgid "No template yet."
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comments_panel/comments_panel.xml:0
msgid "Nothing going on!"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid ""
"Nth <strong>Q</strong>uarter of the fiscal year.<br>\n"
"                            <em>E.g. Q4 starts on Oct. 1 and ends on Dec. 31.</em>"
msgstr ""

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__message_needaction_counter
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__message_needaction_counter
msgid "Number of Actions"
msgstr "Număr de acțiuni"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__message_has_error_counter
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__message_has_error_counter
msgid "Number of errors"
msgstr "Număr de erori"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__message_needaction_counter
#: model:ir.model.fields,help:knowledge.field_knowledge_article_thread__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Numărul de mesaje care necesită acțiune"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__message_has_error_counter
#: model:ir.model.fields,help:knowledge.field_knowledge_article_thread__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Numărul de mesaje cu eroare de livrare"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Objectives with our Collaboration"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_example_branding
msgid "Odoo Brand Assets"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid "Odoo Experience 🎉"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_example_branding
msgid "Odoo: Manage your SME online"
msgstr ""

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
#: model:knowledge.article.stage,name:knowledge.knowledge_article_template_stage_ongoing
msgid "Ongoing"
msgstr "În curs de desfășurare"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "Only internal users are allowed to alter memberships."
msgstr ""

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "Only internal users are allowed to create workspace root articles."
msgstr ""

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "Only internal users are allowed to modify this information."
msgstr ""

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "Only internal users are allowed to remove memberships."
msgstr ""

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"Only internal users are allowed to restore the original article access "
"information."
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/webclient/commands/knowledge_providers.js:0
msgid "Oops, there's nothing here. Try another search."
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/embedded_view_actions_menu/embedded_view_actions_menu.xml:0
#: code:addons/knowledge/static/src/components/sidebar/sidebar.js:0
#: code:addons/knowledge/static/src/views/item_calendar/item_calendar_views.xml:0
msgid "Open"
msgstr "Afișare"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comments_panel/comments_panel.xml:0
msgid "Open Discussions"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Open comments panel"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Open history"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.xml:0
msgid "Open the Trash"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_example_branding
msgid "OpenERP becomes Odoo"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/properties_panel/properties_panel.xml:0
msgid ""
"Organize your database with custom fields\n"
"                        (Text, Selection, ...)."
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_shared_todos
msgid "Otherwise, feel free to handle others listed below:"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid ""
"Our catalog can be found here and is updated every 2 years. If you do not "
"manage to find the specific model you are looking for,"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "Outcome"
msgstr ""

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_stage__parent_id
msgid "Owner Article"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid "PLANET ODOO"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Pain Points (<em>tick the relevant ones</em>)"
msgstr ""

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__parent_id
msgid "Parent Article"
msgstr "Articolul părinte"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__parent_path
msgid "Parent Path"
msgstr "Cale părinte"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_template_view_form
msgid "Parent Template"
msgstr ""

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_member__partner_id
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_member_view_search
msgid "Partner"
msgstr "Partener"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_shared_todos_pay_the_electricity_bill
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_shared_todos_pay_the_electricity_bill
msgid "Pay the Electricity Bill"
msgstr ""

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_member__permission
#: model:ir.model.fields,field_description:knowledge.field_knowledge_invite__permission
msgid "Permission"
msgstr "Permisiune"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_marketing_customer_persona_1
msgid "Persona 1"
msgstr ""

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_marketing_customer_persona_2
msgid "Persona 2"
msgstr ""

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_marketing_customer_persona_3
msgid "Persona 3"
msgstr ""

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_marketing_customer_persona_4
msgid "Persona 4"
msgstr ""

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_personal_organizer
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "Personal Organizer"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Phone:"
msgstr "Telefon:"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_shared_todos
msgid "Please pick the following tasks first:"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "Please refer to the chart below."
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid ""
"Please submit a request for assistance to the Marketing Team if you fall "
"into one of the following:"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "Pluralize the trademark (e.g.<em>YourCompanies</em>)"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid "Podcast updates 📻"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "Post-Mortem Analysis"
msgstr ""

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_post_mortem
msgid "Post-mortem"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Potential Risks:"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid ""
"Prepare your demos in advance and integrate the prospect's use case into it"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/embedded_view_link/embedded_view_link_edit_dialog.xml:0
msgid "Preview"
msgstr "Previzualizare"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
msgid ""
"Price <span class=\"o_stars o_five_stars\" id=\"checkId-1\"><i class=\"fa "
"fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i></span>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
msgid ""
"Price <span class=\"o_stars o_five_stars\" id=\"checkId-1\"><i class=\"fa "
"fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star-o\"></i></span>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
msgid ""
"Price <span class=\"o_stars o_five_stars\" id=\"checkId-1\"><i class=\"fa "
"fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i></span>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
msgid ""
"Price <span class=\"o_stars o_five_stars\" id=\"checkId-1\"><i class=\"fa "
"fa-star\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i"
" class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i></span>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid ""
"Price <span class=\"o_stars o_five_stars\" id=\"checkId-1\"><i class=\"fa "
"fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>​"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/core/embedded_view_link/embedded_view_link_style.js:0
msgid "Primary"
msgstr "Primar"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/move_article_dialog/move_article_dialog.js:0
#: code:addons/knowledge/static/src/components/sidebar/sidebar_section.xml:0
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article__category__private
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search
msgid "Private"
msgstr "Personal"

#. module: knowledge
#: model:knowledge.article.template.category,name:knowledge.knowledge_article_template_category_product_management
msgid "Product Management"
msgstr ""

#. module: knowledge
#: model:knowledge.article.template.category,name:knowledge.knowledge_article_template_category_productivity
msgid "Productivity"
msgstr "Productivitate"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__article_properties
msgid "Properties"
msgstr "Proprietăți"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid ""
"Properties are fields that can only be added on articles that have a parent."
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "Property Field"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Prospect Qualification"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Prospection Templates"
msgstr ""

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_sales_playbook
msgid ""
"Provide salespeople with tips, lexicon and templates to help them sell "
"faster."
msgstr ""

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_sprint_calendar_public_holiday
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sprint_calendar_public_holiday
msgid "Public Holidays"
msgstr "Sărbători legale"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "Q1: <font class=\"text-600\">Would it be possible to...</font>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "Q2: <font class=\"text-600\">Would there be an issue if...</font>"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_mail_invite
msgid "READ"
msgstr ""

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__rating_ids
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__rating_ids
msgid "Ratings"
msgstr "Ratings"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/mail/message/message_actions.js:0
msgid "Re-open the discussion"
msgstr ""

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "Read"
msgstr "Citiți"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_invite__partner_ids
msgid "Recipients"
msgstr "Destinatari"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "Recovery"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/article_index/article_index.xml:0
msgid "Refresh"
msgstr "Actualizare"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_release_note
msgid "Release Notes"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_release_note
msgid "Release Notes 🎉"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover.xml:0
msgid "Remove"
msgstr "Elimină"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Remove Cover"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
#: code:addons/knowledge/static/src/mail/emoji_picker/emoji_picker_patch.xml:0
msgid "Remove Icon"
msgstr "Eliminare pictogramă"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/embedded_view_link/embedded_view_link_popover.xml:0
msgid "Remove Link"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "Remove Member"
msgstr "Eliminare membru"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover.xml:0
msgid "Remove cover"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.js:0
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_hierarchy
msgid "Remove from favorites"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/prompt_embedded_view_name_dialog/prompt_embedded_view_name_dialog.xml:0
msgid "Rename"
msgstr "Redenumire"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover.xml:0
msgid "Replace"
msgstr "Înlocuire"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover.xml:0
msgid "Replace cover"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover.xml:0
msgid "Reposition"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover.xml:0
msgid "Reposition cover"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comments_panel/comments_panel.xml:0
msgid "Resolved Discussions"
msgstr ""

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__activity_user_id
msgid "Responsible User"
msgstr "Utilizator responsabil"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_form
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_tree_trash
msgid "Restore"
msgstr "Restabilire"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid "Restore Access"
msgstr "Restabiliți accesul"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Restore from Trash"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid "Restrict Access"
msgstr "Restricționați accesul"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid "Restrict own access"
msgstr ""

#. module: knowledge
#: model:ir.model.constraint,message:knowledge.constraint_knowledge_article_check_desync_on_root
msgid "Root articles cannot be desynchronized."
msgstr "Articolele rădăcină nu pot fi desincronizate."

#. module: knowledge
#: model:ir.model.constraint,message:knowledge.constraint_knowledge_article_check_permission_on_root
msgid "Root articles must have internal permission."
msgstr "Articolele rădăcină trebuie să aibă permisiune internă."

#. module: knowledge
#: model:ir.model.constraint,message:knowledge.constraint_knowledge_article_check_template_category_on_root
msgid "Root templates must have a category."
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "SEO &amp; SEA projects (betting on keywords, ...)"
msgstr ""

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__message_has_sms_error
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Eroare livrare SMS"

#. module: knowledge
#: model:knowledge.article.template.category,name:knowledge.knowledge_article_template_category_sales
msgid "Sales"
msgstr "Vânzări"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Sales Details"
msgstr "Detalii vânzări"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_sales_playbook
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Sales Playbook"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
#: code:addons/knowledge/static/src/editor/embedded_components/backend/embedded_view_link/embedded_view_link_edit_dialog.xml:0
msgid "Save"
msgstr "Salvează"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover.xml:0
msgid "Save Position"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover.xml:0
msgid "Save position"
msgstr ""

#. module: knowledge
#: model:ir.ui.menu,name:knowledge.knowledge_menu_article
msgid "Search"
msgstr "Căutare"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_favorite_view_search
msgid "Search Favorites"
msgstr "Căutați favorite"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/xml/form_controller.xml:0
msgid "Search Knowledge Articles"
msgstr "Căutați articole de cunoștințe"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_member_view_search
msgid "Search Members"
msgstr "Căutați membri"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_stage_view_search
msgid "Search Stages"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.js:0
msgid "Search an Article..."
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.xml:0
msgid "Search an article..."
msgstr "Căutați un articol..."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/webclient/commands/knowledge_providers.js:0
msgid "Search for an article..."
msgstr "Căutați un articol..."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/webclient/commands/knowledge_providers.js:0
msgid "Search hidden Articles..."
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search
msgid "Search results"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/core/embedded_view_link/embedded_view_link_style.js:0
msgid "Secondary"
msgstr "Secundar"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__category
msgid "Section"
msgstr "Secțiune"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "See a doctor and send us the sick note."
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/article_template_picker_dialog/article_template_picker_dialog.xml:0
msgid "Select a Template"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/external_embedded_view_favorite_menu/embedded_view_favorite_menu.js:0
msgid "Select an article"
msgstr "Selectați un articol"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/clipboard/macros_embedded_clipboard.xml:0
#: code:addons/knowledge/static/src/editor/embedded_components/backend/file/macros_file_mixin.xml:0
msgid "Send as Message"
msgstr "Trimiteți ca mesaj"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_tree
msgid "Send to Trash"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/views/item_calendar/item_calendar_view.js:0
msgid "Send to trash"
msgstr ""

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__sequence
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_favorite__sequence
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_stage__sequence
msgid "Sequence"
msgstr "Secvență"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Share"
msgstr "Partajează"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/move_article_dialog/move_article_dialog.js:0
#: code:addons/knowledge/static/src/components/sidebar/sidebar_section.xml:0
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article__category__shared
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search
msgid "Shared"
msgstr "Partajat"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_shared_todos
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_shared_todos
msgid "Shared To-Do List"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/article_index/article_index.xml:0
msgid "Show All"
msgstr "Afișează tot"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comment/comment.xml:0
#: code:addons/knowledge/static/src/editor/embedded_components/backend/article_index/article_index.xml:0
msgid "Show Less"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comment/comment.xml:0
msgid "Show More"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Show Properties"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "Show Weekends?"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/article_index_plugin/article_index_plugin.js:0
msgid "Show nested articles"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Size:"
msgstr "Dimensiune:"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
msgid ""
"Social <span class=\"o_stars o_five_stars\" id=\"checkId-4\"><i class=\"fa "
"fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i></span>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
msgid ""
"Social Status <span class=\"o_stars o_five_stars\" id=\"checkId-4\"><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star-o\"></i></span>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
msgid ""
"Social Status <span class=\"o_stars o_five_stars\" id=\"checkId-4\"><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid ""
"Social Status <span class=\"o_stars o_five_stars\" id=\"checkId-4\"><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
msgid ""
"Social Status ​<span class=\"o_stars o_five_stars\" id=\"checkId-4\"><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star-o\"></i></span>"
msgstr ""

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_software_specification
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "Software Specification"
msgstr ""

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "Some articles have been sent to Trash"
msgstr "Unele articole au fost trimise la coșul de gunoi"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/view/embedded_view.xml:0
msgid "Something went wrong!"
msgstr "Ceva nu a mers bine"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
msgid "Sonya"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "Source Feedback"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
msgid ""
"Speed of Service <span class=\"o_stars o_five_stars\" id=\"checkId-2\"><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star-o\"></i></span>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
msgid ""
"Speed of Service <span class=\"o_stars o_five_stars\" id=\"checkId-2\"><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
msgid ""
"Speed of Service <span class=\"o_stars o_five_stars\" id=\"checkId-2\"><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid ""
"Speed of Service ​<span class=\"o_stars o_five_stars\" id=\"checkId-2\"><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"
msgstr ""

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_sprint_calendar
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sprint_calendar
msgid "Sprint Calendar"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_stage_view_form
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search_items
msgid "Stage"
msgstr "Etapă"

#. module: knowledge
#: model:ir.actions.act_window,name:knowledge.knowledge_article_stage_action
#: model:ir.ui.menu,name:knowledge.knowledge_article_stage_menu
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_stage_view_tree
msgid "Stages"
msgstr "Etape"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article_stage__parent_id
msgid "Stages are shared among acommon parent and its children articles."
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Stakeholders Analysis"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "Start Date"
msgstr "Dată început"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.js:0
msgid "Start Date Time"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "Start typing"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.xml:0
msgid ""
"Start typing to continue with an empty page or pick an option below to get "
"started."
msgstr ""

#. module: knowledge
#: model_terms:digest.tip,tip_description:knowledge.digest_tip_knowledge_2
msgid ""
"Start working together on any Knowledge Article by sharing your article with"
" others."
msgstr ""

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Stare bazată pe activități\n"
"Întârziată: data activitații este deja trecută\n"
"Astăzi: data activității este astăzi\n"
"Planificate: activități viitoare."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "Stop Date"
msgstr "Dată de oprire"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/article_index/article_index.xml:0
msgid "Switch Mode"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid ""
"Talk to you soon,<br>\n"
"                        YourName"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_template_view_search
msgid "Template"
msgstr "Șablon"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__template_body
msgid "Template Body"
msgstr ""

#. module: knowledge
#: model:ir.actions.act_window,name:knowledge.knowledge_article_template_category_action
#: model:ir.ui.menu,name:knowledge.knowledge_article_template_category_menu
msgid "Template Categories"
msgstr ""

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__template_category_id
msgid "Template Category"
msgstr "Categorie șablon"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__template_category_sequence
msgid "Template Category Sequence"
msgstr ""

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__template_description
msgid "Template Description"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_template_view_search
msgid "Template Items"
msgstr ""

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__template_preview
msgid "Template Preview"
msgstr "Previzualizare Sablon"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__template_sequence
msgid "Template Sequence"
msgstr ""

#. module: knowledge
#: model:ir.actions.act_window,name:knowledge.knowledge_article_template_stage_action
#: model:ir.ui.menu,name:knowledge.knowledge_article_template_stage_menu
msgid "Template Stages"
msgstr ""

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__template_name
msgid "Template Title"
msgstr ""

#. module: knowledge
#: model:ir.ui.menu,name:knowledge.knowledge_article_template_menu
msgid "Templates"
msgstr "Șabloane"

#. module: knowledge
#: model:ir.model.constraint,message:knowledge.constraint_knowledge_article_check_template_name_required
msgid "Templates should have a name."
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "Test Environment"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid ""
"Thank you to everyone involved in the organization of this edition of the\n"
"                            <font class=\"text-o-color-2\">\n"
"                                <strong>Odoo Run</strong>\n"
"                            </font>!"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "The 5 Commandments"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid ""
"The Accounting team handles all payments on Fridays afternoon.\n"
"                <br>\n"
"                If 2 weeks have passed and you are still waiting to be paid, get in touch with them."
msgstr ""

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "The Article you are trying to access has been deleted"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid "The Future Entrepreneurship Fair"
msgstr ""

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "The article '%s' needs at least one member with 'Write' access."
msgstr "Articolul '%s' are nevoie de cel puțin un membru cu acces 'Scriere'."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/js/knowledge_controller.js:0
msgid ""
"The article you are trying to open has either been removed or is "
"inaccessible."
msgstr ""

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"The destination placement of %(article_name)s is ambiguous, you should "
"specify the category."
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/macros/abstract_macro.js:0
msgid "The operation could not be completed."
msgstr ""

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article_thread__article_anchor_text
msgid ""
"The original highlighted anchor text, giving initial context if that text is"
" modified or removed afterwards."
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid ""
"The podcast launches its new technical talks, for all tech-savvy listeners out there! This\n"
"                            new episode of the series features Géry, the mastermind behind OWL, the world fastest JS\n"
"                            framework. 🚀"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/macros/abstract_macro.js:0
msgid "The record that this macro is targeting could not be found."
msgstr ""

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/controllers/main.py:0
msgid "The selected member does not exists or has been already deleted."
msgstr "Membrii selectați nu există sau au fost deja șterși."

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__sequence
msgid ""
"The sequence is computed only among the articles that have the same parent."
msgstr ""
"Secvența este calculată doar printre articolele care au același părinte."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.js:0
msgid "The start date property is required."
msgstr ""

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__root_article_id
msgid ""
"The subject is the title of the highest parent in the article hierarchy."
msgstr ""
"Subiectul este titlul părintelui cel mai înalt în ierarhia articolului."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid ""
"The tickets to participate to Odoo Experience 23 are now available and they "
"are cheaper if you book them now!"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/view/embedded_view.xml:0
msgid "The view does not exist or you are not allowed to access to it."
msgstr "Vizualizarea nu există sau nu aveți permisiunea să o accesați."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar_section.xml:0
msgid "There are no Articles in your Workspace."
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "Things to handle this week*"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_form
msgid "This Article is in Trash and will be deleted on the"
msgstr "Acest articol este în coșul de gunoi și va fi șters pe"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_form
msgid "This article is archived."
msgstr "Acest articol este arhivat."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "This article is locked"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_form
msgid "This article is only displayed to its members."
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid ""
"This year again, Odoo was present at the <em>Tech and Innovation festival for students</em> in\n"
"                            Antwerp, ready to promote our company!<br>Fabien was also there and gave an interview on the main\n"
"                            stage."
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/properties_panel/properties_panel.xml:0
msgid ""
"Those fields will be available on all articles that share the same parent."
msgstr ""

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__is_resolved
msgid "Thread Closed"
msgstr ""

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_product_catalog
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid "Tiny House Catalog"
msgstr ""

#. module: knowledge
#: model:digest.tip,name:knowledge.digest_tip_knowledge_0
#: model_terms:digest.tip,tip_description:knowledge.digest_tip_knowledge_0
msgid "Tip: A Knowledge well kept"
msgstr ""

#. module: knowledge
#: model:digest.tip,name:knowledge.digest_tip_knowledge_2
#: model_terms:digest.tip,tip_description:knowledge.digest_tip_knowledge_2
msgid "Tip: Be on the same page"
msgstr ""

#. module: knowledge
#: model:digest.tip,name:knowledge.digest_tip_knowledge_1
#: model_terms:digest.tip,tip_description:knowledge.digest_tip_knowledge_1
msgid "Tip: Use Clipboards to easily inject repetitive content"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Tips to close more deals"
msgstr ""

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__name
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_template_category__name
msgid "Title"
msgstr "Titlu"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid "To be sure to stay updated, follow us on"
msgstr "Pentru a fi sigur că rămâneți actualizați, urmăriți-ne pe"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.xml:0
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Toggle aside menu"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Toggle chatter"
msgstr ""

#. module: knowledge
#: model:ir.actions.act_window,name:knowledge.knowledge_article_action_trashed
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_tree_items
msgid "Trash"
msgstr "Gunoi"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__to_delete
#: model:ir.ui.menu,name:knowledge.knowledge_article_menu_trashed
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search
msgid "Trashed"
msgstr "În coșul de gunoi"

#. module: knowledge
#: model:ir.model.constraint,message:knowledge.constraint_knowledge_article_check_trash
msgid "Trashed articles must be archived."
msgstr "Articolele în coșul de gunoi trebuie să fie arhivate."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "Trying to remove wrong member."
msgstr "Încercați să eliminați un membru greșit."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/embedded_view_link/embedded_view_link_edit_dialog.xml:0
msgid "Type"
msgstr "Tip"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipul de activitate de excepție înregistrată."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_form
msgid "Unarchive"
msgstr "Dezarhivează"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Unlock"
msgstr "Deblocați"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "Unsupported search operation"
msgstr ""

#. module: knowledge
#. odoo-javascript
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
#: code:addons/knowledge/static/src/components/hierarchy/hierarchy.xml:0
#: code:addons/knowledge/static/src/components/sidebar/sidebar_row.xml:0
#: code:addons/knowledge/static/src/editor/html_migrations/migration-1.0.js:0
#: code:addons/knowledge/static/src/webclient/commands/knowledge_providers.js:0
#: code:addons/knowledge/static/src/xml/knowledge_command_palette.xml:0
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_hierarchy
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_kanban_items
msgid "Untitled"
msgstr "Fără titlu"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/article_index/article_index.xml:0
msgid "Update"
msgstr "Actualizare"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "Usage"
msgstr "Utilizare"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/clipboard/macros_embedded_clipboard.js:0
msgid "Use as %s"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/file/macros_file_mixin.xml:0
msgid "Use as Attachment"
msgstr "Utilizare ca atașament"

#. module: knowledge
#: model_terms:digest.tip,tip_description:knowledge.digest_tip_knowledge_1
msgid "Use the /clipboard command on a Knowledge Article and get going."
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "Use the expression \"YourCompanions\" to refer to our community"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "Use the logo instead of the word inside sentences"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "Use this template whenever you have an announcement to make."
msgstr ""

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__category
msgid ""
"Used to categorize articles in UI, depending on their main permission "
"definitions."
msgstr ""

#. module: knowledge
#: model:ir.model,name:knowledge.model_res_users
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_favorite__user_id
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_favorite_view_search
msgid "User"
msgstr "Utilizator"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__user_favorite_sequence
msgid "User Favorite Sequence"
msgstr "Secvență utilizator preferat"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "User Permission"
msgstr "Permisiune utilizator"

#. module: knowledge
#: model:ir.model.constraint,message:knowledge.constraint_knowledge_article_favorite_unique_article_user
msgid "User already has this article in favorites."
msgstr "Utilizatorul are deja acest articol în favorite."

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__user_permission
msgid "User permission"
msgstr "Permisiune utilizator"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_release_note
msgid "VersionName - ReleaseDate"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "Visibility"
msgstr "Vizibilitate"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
msgid "Vittorio"
msgstr ""

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_shared_todos_water_the_plants
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_shared_todos_water_the_plants
msgid "Water the Plants"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid "We already cannot wait for the next one🏃"
msgstr ""

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__website_message_ids
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__website_message_ids
msgid "Website Messages"
msgstr "Mesaje de pe site-ul web"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__website_message_ids
#: model:ir.model.fields,help:knowledge.field_knowledge_article_thread__website_message_ids
msgid "Website communication history"
msgstr "Istoricul comunicării pe site-ul web"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/res_users.py:0
msgid "Welcome %s"
msgstr "Bine ați venit %s"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid ""
"Welcome to the last edition of the MyCompany Newsletter!<br>\n"
"                We are very excited to share with you the hottest updates and news! 🤩"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "What do I have to do if I cannot work?"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "What do you want to manage?"
msgstr "Ce doriți să gestionați?"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/prompt_embedded_view_name_dialog/prompt_embedded_view_name_dialog.xml:0
msgid "What items do you want to manage?"
msgstr ""

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__is_locked
msgid ""
"When locked, users cannot write on the body or change the title, even if "
"they have write access on the article."
msgstr ""
"Când este blocat, utilizatorii nu pot scrie în corp sau schimba titlul, "
"chiar dacă au acces de scriere la articol."

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__to_delete
msgid ""
"When sent to trash, articles are flagged to be deleted\n"
"                days after last edit. knowledge_article_trash_limit_days config\n"
"                parameter can be used to modify the number of days. \n"
"                (default is 30)"
msgstr ""
"Când sunt trimise la coș, articolele sunt marcate pentru a fi șterse\n"
"                zile după ultima editare. Parametrul de configurare\n"
"                knowledge_article_trash_limit_days poate fi folosit pentru a modifica numărul de zile. \n"
"                (implicit este 30)"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__full_width
msgid ""
"When set, the article body will take the full width available on the article"
" page. Otherwise, the body will have large horizontal margins."
msgstr ""
"Când este setat, corpul articolului va lua întreaga lățime disponibilă pe "
"pagina articolului. În caz contrar, corpul va avea margini orizontale mari."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid ""
"Whether your heart leans towards the rustic charm of a wooden hut or the "
"intricate beauty of Victorian steelworks, we have you covered. Desiring a "
"specific option? Rest assured, we are here to fulfill your wishes! Just get "
"in touch with our architects and we will make sure to provide any specific "
"choice you desire."
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_example_branding
msgid "Which color should we pick for the logo?"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_example_branding
msgid "Which size should the rollups be?"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_example_branding
msgid "Which text should we print on the billboards?"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid ""
"While tiny houses are inherently small, it's important to prioritize comfort"
" when sharing such limited space. To ensure everyone's happiness, we "
"recommend allowing at least 9m² per person. If you plan to live with "
"multiple roommates, our Serenità model is highly recommended, providing "
"ample space for each individual to enjoy<br>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid ""
"While tiny houses do offer a more cost-effective alternative to traditional "
"houses, at MyCompany, we prioritize durability, which comes with a price. "
"However, if you're on a tight budget, we have the perfect solution: our "
"Cielo model.<br>It provides everything you need while allowing you to "
"save.<br>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "Who can drive my car?"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "Who do I need to address if I need office supplies?"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid "Who has access to what? 🕵️"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "Why has my expense not been reimbursed? It has been accepted."
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/move_article_dialog/move_article_dialog.js:0
#: code:addons/knowledge/static/src/components/sidebar/sidebar_section.xml:0
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article__category__workspace
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search
msgid "Workspace"
msgstr "Spațiu de lucru"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "Write"
msgstr "Write"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.js:0
msgid "Write an article about"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid ""
"Write it without any article(<em><s>the</s> YourCompany, <s>a</s> "
"YourCompany, ...</em>)"
msgstr ""

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_shared_todos_write_the_next_newsletter
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_shared_todos_write_the_next_newsletter
msgid "Write the next Newsletter"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "You"
msgstr "Dumneavoastră"

#. module: knowledge
#: model:ir.model.constraint,message:knowledge.constraint_knowledge_article_member_unique_article_partner
msgid "You already added this partner on this article."
msgstr "Ați adăugat deja acest partener la acest articol."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "You are not allowed to create a new template."
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/view/readonly_embedded_view.js:0
msgid "You are not allowed to delete a favorite filter in this article."
msgstr ""

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "You are not allowed to delete a template."
msgstr ""

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "You are not allowed to make '%(article_name)s' private."
msgstr "Nu aveți permisiunea de a face '%(article_name)s' privat."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"You are not allowed to move '%(article_name)s' under '%(parent_name)s'."
msgstr ""
"Nu aveți permisiunea de a muta '%(article_name)s' sub '%(parent_name)s'."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "You are not allowed to move '%(article_name)s'."
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/view/readonly_embedded_view.js:0
msgid "You are not allowed to save a favorite filter in this article."
msgstr ""

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "You are not allowed to update a template."
msgstr ""

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "You are not allowed to update the type of a article or a template."
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "You can't leave an article for which you are the last writer"
msgstr "Nu puteți lăsa un articol pentru care sunteți ultimul autor"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"You can't move %(article_name)s under %(item_name)s, as %(item_name)s is an "
"Article Item. Convert %(item_name)s into an Article first."
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "You can't remove the last writer of the article"
msgstr "Nu puteți elimina ultimul autor al articolului"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "You cannot add or remove this article to your favorites"
msgstr "Nu puteți adăuga sau elimina acest articol la favorite"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/controllers/main.py:0
msgid "You cannot change the internal permission of this article."
msgstr "Nu puteți schimba permisiunea internă a acestui articol."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/controllers/main.py:0
msgid "You cannot change the permission of this member."
msgstr "Nu puteți schimba permisiunea acestui membru."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_cover.py:0
msgid "You cannot create a new Knowledge Cover from here."
msgstr ""

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "You cannot create an article under articles on which you cannot write"
msgstr "Nu puteți crea un articol sub articole pe care nu puteți scrie"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"You cannot move an article under %(parent_name)s as you cannot write on it"
msgstr ""
"Nu puteți muta un articol sub %(parent_name)s deoarece nu puteți scrie pe el"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/hierarchy/hierarchy.xml:0
msgid "You do not have access to this article"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar_section.xml:0
msgid "You do not have any private Article."
msgstr ""

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "You have to be editor on %(article_name)s to add members."
msgstr "Trebuie să fiți editor pe %(article_name)s pentru a adăuga membri."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"You have to be editor on %(article_name)s to change its internal permission."
msgstr ""
"Trebuie să fiți editor pe %(article_name)s pentru a schimba permisiunea "

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"You have to be editor on %(article_name)s to modify members permissions."
msgstr ""
"Trebuie să fiți editor pe %(article_name)s pentru a modifica permisiunile "

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"You have to be editor on %(article_name)s to remove or exclude member "
"%(member_name)s."
msgstr ""
"Trebuie să fiți editor pe %(article_name)s pentru a elimina sau exclude "

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "You have to be editor on %(article_name)s to restore it."
msgstr "Trebuie să fiți editor pe %(article_name)s pentru a-l restaura."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid ""
"You may have heard those a million times and yet you are not entirely sure of what it means.<br>\n"
"                Here is a quick recap for you to shine during the next meeting."
msgstr ""

#. module: knowledge
#. odoo-javascript
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
#: code:addons/knowledge/static/src/components/sidebar/sidebar.js:0
msgid "You need at least 2 members for the Article to be shared."
msgstr ""

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "You need to have access to this article in order to join its members."
msgstr ""

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"You need to have access to this article's root in order to join its members."
msgstr ""

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "Your Access: %s"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid ""
"Your car can be driven by <strong>you, your spouse and by any person living "
"under the same roof</strong> as long as they have a valid permit."
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "YourCompany is proud to announce that..."
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid ""
"YourCompany is very proud of its brand image.\n"
"                <br>\n"
"                When representing the brand, we thus ask you to be very cautious in how you refer to the company."
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comments_panel/comments_panel.xml:0
msgid "all"
msgstr "toate"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_trash_notification
msgid "and the following child article(s) have"
msgstr "și următorul articol copil a fost"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/webclient/commands/knowledge_providers.js:0
msgid "articles"
msgstr "articole"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_trash_notification
msgid "been sent to Trash.<br/><br/>"
msgstr "trimis la Coșul de Gunoi.<br/><br/>"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comments_panel/comments_panel.xml:0
msgid "button to add comments"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "contact our FleetOfficer"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover.xml:0
msgid "cover"
msgstr "copertă"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/prompt_embedded_view_name_dialog/prompt_embedded_view_name_dialog.js:0
msgid "e.g. Buildings"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "e.g. Meetings"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_stage_view_form
msgid "e.g. Ongoing"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/prompt_embedded_view_name_dialog/prompt_embedded_view_name_dialog.js:0
msgid "e.g. Todos"
msgstr "ex. De făcut"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_trash_notification
msgid "has"
msgstr "are"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_trash_notification
msgid "have"
msgstr "au"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_mail_invite
msgid "invited you to"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_mail_invite
msgid "invited you to access an article.<br/>"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
#: code:addons/knowledge/static/src/components/sidebar/sidebar.xml:0
#: code:addons/knowledge/static/src/components/sidebar/sidebar_row.xml:0
#: code:addons/knowledge/static/src/components/with_lazy_loading/with_lazy_loading.xml:0
msgid "loader"
msgstr "încărcător"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_mail_notification_layout
msgid "mentioned you in a comment:"
msgstr ""

#. module: knowledge
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article__user_permission__none
msgid "none"
msgstr "niciunul"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid "or"
msgstr "sau"

#. module: knowledge
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article__user_permission__read
msgid "read"
msgstr "citit"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comments_panel/comments_panel.xml:0
msgid "resolved"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.xml:0
msgid "search"
msgstr "căutare"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_mail_invite
msgid "the article"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_form
msgid "to unleash the power of Knowledge !"
msgstr "pentru a dezvălui puterea cunoștințelor!"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comments_panel/comments_panel.xml:0
msgid "unresolved"
msgstr ""

#. module: knowledge
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article__user_permission__write
msgid "write"
msgstr "scrie"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid ""
"​<span class=\"o_stars o_five_stars\" id=\"checkId-1\"><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i></span>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid ""
"​<span class=\"o_stars o_five_stars\" id=\"checkId-2\"><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i></span>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid ""
"​<span class=\"o_stars o_five_stars\" id=\"checkId-3\"><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i></span>"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_campaign_brief
msgid "⌚ Elevator Pitch"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_example_branding
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "☝🏼 Please prepare the following before the meeting:"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "⚙ Technical Specifications"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "❓ Open Questions"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "⭐ Release Notes"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_campaign_brief
msgid "🎯 Target Audience"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid "🏘️ House Model - Incanto"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid "🏠 House Model - Cielo"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid "🏢 House Model - Serenità"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_campaign_brief
msgid "📊 KPIs"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "📝 Purpose"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_campaign_brief
msgid "📣 Message"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "🔍 Review Checklist"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "🔗 Links"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "🗓 WEEKLY AGENDA"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_example_branding
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "🗣 Meeting Agenda"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_example_branding
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "🙌🏼 Decisions Taken"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_campaign_brief
msgid "🚀 Objective"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid "🛕 House Model - Dolcezza"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "🧠 Functional Specifications"
msgstr ""
