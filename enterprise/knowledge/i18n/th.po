# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* knowledge
# 
# Translators:
# <PERSON>il <PERSON>, 2024
# <PERSON><PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 09:12+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON>, 2025\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "\" (Selection)"
msgstr "\" (คัดเลือก)"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "\" (date and time)"
msgstr "\" (วันและเวลา)"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "\" (date)"
msgstr "\" (วันที่)"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"\"%(article_item_name)s\" is an Article Item from \"%(article_name)s\" and "
"cannot be restored on its own. Contact the owner of \"%(article_name)s\" to "
"have it restored instead."
msgstr ""
"\"%(article_item_name)s\" เป็นรายการบทความจาก \"%(article_name)s\" "
"และไม่สามารถกู้คืนได้ด้วยตนเอง ติดต่อเจ้าของ \"%(article_name)s\" "
"เพื่อขอให้กู้คืนแทน"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"\"%(article_name)s\" is a template and can not be a child of an article "
"(\"%(parent_article_name)s\")."
msgstr ""
"\"%(article_name)s\" เป็นเทมเพลตและไม่สามารถเป็นรายการย่อยของบทความได้ "
"(\"%(parent_article_name)s\")"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"\"%(article_name)s\" is an article and can not be a child of a template "
"(\"%(parent_article_name)s\").\""
msgstr ""
"\"%(article_name)s\" เป็นบทความและไม่สามารถเป็นรายการย่อยของเทมเพลตได้ "
"(\"%(parent_article_name)s\")\""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "\"About Us\" Template"
msgstr "เทมเพลต \"เกี่ยวกับเรา\""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "# Employees:"
msgstr "# พนักงาน:"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__favorite_count
msgid "#Is Favorite"
msgstr "#เป็นรายการโปรด"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "%(article_name)s (copy)"
msgstr "%(article_name)s (สำเนา)"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "%s has been sent to Trash"
msgstr "%s ถูกส่งไปยังถังขยะแล้ว"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_mail_invite
msgid ".<br/>"
msgstr "<br/>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid ""
"250 Executive Park Blvd, Suite 3400 94134\n"
"                        <br>\n"
"                        San Francisco California (US)\n"
"                        <br>\n"
"                        United States\n"
"                        <br>\n"
"                        <EMAIL>"
msgstr ""
"250 Executive Park Blvd, Suite 3400 94134\n"
"                        <br>\n"
"                        ซานฟรานซิสโก แคลิฟอร์เนีย (สหรัฐอเมริกา)\n"
"                        <br>\n"
"                        สหรัฐอเมริกา\n"
"                        <br>\n"
"                        <EMAIL>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_example_branding
msgid "85x200 or 100x200, depending on the content"
msgstr "85x200 หรือ 100x200 ขึ้นอยู่กับเนื้อหา"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid ""
"<br>\n"
"                            Subscribe here to make sure you will not miss an episode!"
msgstr ""
"<br>\n"
"                            สมัครสมาชิกที่นี่เพื่อให้แน่ใจว่าคุณจะไม่พลาดสักตอน!"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_campaign_brief
msgid ""
"<em>\n"
"                    <font class=\"text-600\">\n"
"                        Describe your campaign in just a few words.\n"
"                    </font>\n"
"                </em>"
msgstr ""
"<em>\n"
"                    <font class=\"text-600\">\n"
"                        อธิบายแคมเปญของคุณโดยใช้คำเพียงไม่กี่คำ\n"
"                    </font>\n"
"                </em>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_campaign_brief
msgid ""
"<em>\n"
"                    <font class=\"text-600\">\n"
"                        How will your measure progress?\n"
"                    </font>\n"
"                </em>"
msgstr ""
"<em>\n"
"                    <font class=\"text-600\">\n"
"                        คุณจะวัดผลความคืบหน้าของคุณอย่างไร?\n"
"                    </font>\n"
"                </em>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_campaign_brief
msgid ""
"<em>\n"
"                    <font class=\"text-600\">\n"
"                        What are you trying to accomplish with this campaign?\n"
"                    </font>\n"
"                </em>"
msgstr ""
"<em>\n"
"                    <font class=\"text-600\">\n"
"                        คุณพยายามทำอะไรให้สำเร็จด้วยแคมเปญนี้?\n"
"                    </font>\n"
"                </em>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_campaign_brief
msgid ""
"<em>\n"
"                    <font class=\"text-600\">\n"
"                        What are you trying to convince them of?\n"
"                    </font>\n"
"                </em>"
msgstr ""
"<em>\n"
"                    <font class=\"text-600\">\n"
"                        คุณกำลังพยายามโน้มน้าวพวกเขาเกี่ยวกับเรื่องอะไร?\n"
"                    </font>\n"
"                </em>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_campaign_brief
msgid ""
"<em>\n"
"                    <font class=\"text-600\">\n"
"                        Who are you trying to reach?\n"
"                    </font>\n"
"                </em>"
msgstr ""
"<em>\n"
"                    <font class=\"text-600\">\n"
"                        คุณกำลังพยายามเข้าถึงใคร?\n"
"                    </font>\n"
"                </em>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid ""
"<em>\n"
"                    <font class=\"text-600\">Ask a Senior Engineer to fill this part before launching the task.</font>\n"
"                </em>"
msgstr ""
"<em>\n"
"                    <font class=\"text-600\">ขอให้วิศวกรผู้อาวุโสกรอกข้อมูลในส่วนนี้ก่อนเริ่มงาน</font>\n"
"                </em>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid ""
"<em>\n"
"                    <font class=\"text-600\">Ask the developer to go through this checklist before asking for a review.</font>\n"
"                </em>"
msgstr ""
"<em>\n"
"                    <font class=\"text-600\">ขอให้นักพัฒนาอ่านรายการตรวจสอบนี้ก่อนที่จะขอให้มีการตรวจสอบ</font>\n"
"                </em>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid ""
"<em>\n"
"                    <font class=\"text-600\">Explain in a few words the problem that this change is going to solve.</font>\n"
"                </em>"
msgstr ""
"<em>\n"
"                    <font class=\"text-600\">อธิบายแบบสั้นถึงปัญหาที่การเปลี่ยนแปลงครั้งนี้กำลังจะแก้ไข</font>\n"
"                </em>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid ""
"<em>\n"
"                    <font class=\"text-600\">Explain in a single sentence what has been changed.</font>\n"
"                </em>"
msgstr ""
"<em>\n"
"                    <font class=\"text-600\">อธิบายในประโยคเดียวว่ามีการเปลี่ยนแปลงอะไรบ้าง</font>\n"
"                </em>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid ""
"<em>\n"
"                    <font class=\"text-600\">Explain the consequences of this issue.</font>\n"
"                </em>"
msgstr ""
"<em>\n"
"                    <font class=\"text-600\">อธิบายผลที่ตามมาของปัญหานี้</font>\n"
"                </em>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid ""
"<em>\n"
"                    <font class=\"text-600\">Explain what went wrong in just a few words.</font>\n"
"                </em>"
msgstr ""
"<em>\n"
"                    <font class=\"text-600\">อธิบายสิ่งที่ผิดพลาดแบบสั้น</font>\n"
"                </em>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid ""
"<em>\n"
"                    <font class=\"text-600\">How did it get to happen?</font>\n"
"                </em>"
msgstr ""
"<em>\n"
"                    <font class=\"text-600\">มันเกิดขึ้นได้อย่างไร?</font>\n"
"                </em>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid ""
"<em>\n"
"                    <font class=\"text-600\">How was it solved?</font>\n"
"                </em>"
msgstr ""
"<em>\n"
"                    <font class=\"text-600\">มันแก้ไขได้อย่างไร?</font>\n"
"                </em>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid ""
"<em>\n"
"                    <font class=\"text-600\">Lay here any remaining question or doubt.</font>\n"
"                </em>"
msgstr ""
"<em>\n"
"                    <font class=\"text-600\">ทิ้งคำถามหรือข้อสงสัยไว้ที่นี่</font>\n"
"                </em>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid ""
"<em>\n"
"                    <font class=\"text-600\">List here all the changes to implement.</font>\n"
"                </em>"
msgstr ""
"<em>\n"
"                    <font class=\"text-600\">แสดงรายการการเปลี่ยนแปลงทั้งหมดที่จะนำไปใช้ที่นี่</font>\n"
"                </em>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid ""
"<em>\n"
"                    <font class=\"text-600\">List here all the material and documentation related to the task.</font>\n"
"                </em>"
msgstr ""
"<em>\n"
"                    <font class=\"text-600\">แสดงรายการวัสดุและเอกสารทั้งหมดที่เกี่ยวข้องกับงานที่นี่</font>\n"
"                </em>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<em>\n"
"                    <font class=\"text-600\">List here all the text you could not do this week. These shall be postponed in the next weekly schedule.</font>\n"
"                </em>"
msgstr ""
"<em>\n"
"                    <font class=\"text-600\">เขียนข้อความทั้งหมดที่คุณไม่สามารถทำได้ในสัปดาห์นี้ที่นี่ สิ่งเหล่านี้จะถูกเลื่อนออกไปในตารางรายสัปดาห์ถัดไป</font>\n"
"                </em>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid ""
"<font class=\"text-400\">\n"
"                            <em>How to best reach this customer type.</em>\n"
"                        </font>"
msgstr ""
"<font class=\"text-400\">\n"
"                            <em>วิธีเข้าถึงลูกค้าประเภทนี้ได้ดีที่สุด</em>\n"
"                        </font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
msgid ""
"<font class=\"text-400\">\n"
"                    <em>How to best reach this customer type.<br></em>\n"
"                </font>\n"
"                Abigail works a lot and never watches TV. Better reach her on her phone or on her to work."
msgstr ""
"<font class=\"text-400\">\n"
"                    <em>วิธีเข้าถึงลูกค้าประเภทนี้ได้ดีที่สุด<br></em>\n"
"                </font>\n"
"                Abigail ทำงานหนักมากและไม่เคยพักดูทีวีเลย ติดต่อเธอทางโทรศัพท์หรือไปหาเธอที่ทำงานจะดีกว่า"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
msgid ""
"<font class=\"text-400\">\n"
"                    <em>How to best reach this customer type.<br></em>\n"
"                </font>\n"
"                As a classic Gen Z member, Vittorio never watches TV and never listens to the radio. For him to see our message, we need to get to his Instagram feed."
msgstr ""
"<font class=\"text-400\">\n"
"                    <em>วิธีเข้าถึงลูกค้าประเภทนี้ได้ดีที่สุด<br></em>\n"
"                </font>\n"
"                ในฐานะสมาชิก Gen Z  คนนึง Vittorio ไม่เคยพักดูทีวีและไม่เคยนั่งฟังวิทยุเลย เพื่อให้เขาเห็นข้อความของเรา เราต้องไปที่ฟีด Instagram ของเขา"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
msgid ""
"<font class=\"text-400\">\n"
"                    <em>How to best reach this customer type.<br></em>\n"
"                </font>\n"
"                As an avid music listener, the best way to reach Sonya is through the radio since hers is always on."
msgstr ""
"<font class=\"text-400\">\n"
"                    <em>วิธีเข้าถึงลูกค้าประเภทนี้ได้ดีที่สุด<br></em>\n"
"                </font>\n"
"                ในฐานะผู้ฟังเพลงตัวยง วิธีที่ดีที่สุดในการเข้าถึง Sonya คือทางวิทยุ เนื่องจากวิทยุของเธอเปิดอยู่ตลอดเวลา"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
msgid ""
"<font class=\"text-400\">\n"
"                    <em>How to best reach this customer type.<br></em>\n"
"                </font>\n"
"                Julius follows politics very tightly, and can best be reached with TV ads."
msgstr ""
"<font class=\"text-400\">\n"
"                    <em>วิธีเข้าถึงลูกค้าประเภทนี้ได้ดีที่สุด<br></em>\n"
"                </font>\n"
"                Julius ติดตามการเมืองอย่างเข้มงวด และเข้าถึงได้ดีที่สุดด้วยโฆษณาทางทีวี"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "<font class=\"text-400\">To Read 1</font>"
msgstr "<font class=\"text-400\">เพื่ออ่าน 1</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "<font class=\"text-400\">To Read 2</font>"
msgstr "<font class=\"text-400\">เพื่ออ่าน 2</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "<font class=\"text-600\">Action A</font>"
msgstr "<font class=\"text-600\">การดำเนินการ A</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "<font class=\"text-600\">Action B</font>"
msgstr "<font class=\"text-600\">การดำเนินการ B</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "<font class=\"text-600\">Action C</font>"
msgstr "<font class=\"text-600\">การดำเนินการ C</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<font class=\"text-600\">Backlog 1</font>"
msgstr "<font class=\"text-600\">งานค้าง 1</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<font class=\"text-600\">Backlog 2</font>"
msgstr "<font class=\"text-600\">งานค้าง 2</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<font class=\"text-600\">Backlog 3</font>"
msgstr "<font class=\"text-600\">งานค้าง 3</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">Blue/Green/Red/Yellow</font>"
msgstr "<font class=\"text-600\">น้ำเงิน/เขียว/แดง/เหลือง</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "<font class=\"text-600\">Change 1</font>"
msgstr "<font class=\"text-600\">เปลี่ยน 1</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "<font class=\"text-600\">Change 2</font>"
msgstr "<font class=\"text-600\">เปลี่ยน 2</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "<font class=\"text-600\">Change 3</font>"
msgstr "<font class=\"text-600\">เปลี่ยน 3</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">Color</font>"
msgstr "<font class=\"text-600\">สี</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">Competitor 1</font>"
msgstr "<font class=\"text-600\">คู่แข่ง 1</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">Competitor 2</font>"
msgstr "<font class=\"text-600\">คู่แข่ง 2</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">Competitor 3</font>"
msgstr "<font class=\"text-600\">คู่แข่ง 3</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_release_note
msgid ""
"<font class=\"text-600\">Detailed Explanation of the feature, with "
"screenshots or a GIF</font>"
msgstr ""
"<font class=\"text-600\">คำอธิบายโดยละเอียดเกี่ยวกับฟีเจอร์นี้ "
"พร้อมภาพหน้าจอหรือ GIF</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">Email</font>"
msgstr "<font class=\"text-600\">อีเมล</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_release_note
msgid "<font class=\"text-600\">Fixed a bug where...</font>"
msgstr "<font class=\"text-600\">แก้ไขข้อผิดพลาดที่...</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_release_note
msgid "<font class=\"text-600\">From now on, ...</font>"
msgstr "<font class=\"text-600\">จากนี้ไป ...</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid ""
"<font class=\"text-600\">How do stakeholders interact regarding "
"offers?</font>"
msgstr ""
"<font "
"class=\"text-600\">ผู้มีส่วนได้ส่วนเสียโต้ตอบเกี่ยวกับข้อเสนออย่างไร?</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">How do they compare and evaluate offers?</font>"
msgstr "<font class=\"text-600\">พวกเขาเปรียบเทียบและประเมินข้อเสนออย่างไร</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid ""
"<font class=\"text-600\">I made sure any visual change is responsive</font>"
msgstr ""
"<font "
"class=\"text-600\">ฉันตรวจสอบให้แน่ใจว่าการเปลี่ยนแปลงทางภาพตอบสนองได้</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid ""
"<font class=\"text-600\">I made sure it did not introduce any obvious "
"regression</font>"
msgstr "<font class=\"text-600\">ฉันแน่ใจว่ามันไม่ได้ทำให้เกิดการถดถอย</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid ""
"<font class=\"text-600\">I made sure it did not introduce any security "
"flaw</font>"
msgstr ""
"<font "
"class=\"text-600\">ฉันแน่ใจว่ามันไม่ได้ทำให้เกิดข้อบกพร่องด้านความปลอดภัย</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">Job Title 1</font>"
msgstr "<font class=\"text-600\">ตำแหน่งงาน 1</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">Job Title 2</font>"
msgstr "<font class=\"text-600\">ตำแหน่งงาน 2</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">Job Title</font>"
msgstr "<font class=\"text-600\">ตำแหน่งงาน</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "<font class=\"text-600\">Lesson A</font>"
msgstr "<font class=\"text-600\">บทเรียน A</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "<font class=\"text-600\">Lesson B</font>"
msgstr "<font class=\"text-600\">บทเรียน B</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "<font class=\"text-600\">Lesson C</font>"
msgstr "<font class=\"text-600\">บทเรียน C</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">Name 1</font>"
msgstr "<font class=\"text-600\">ชื่อ 1</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">Name 2</font>"
msgstr "<font class=\"text-600\">ชื่อ 2</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">Name</font>"
msgstr "<font class=\"text-600\">ชื่อ</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_release_note
msgid "<font class=\"text-600\">New Feature 2</font>"
msgstr "<font class=\"text-600\">ฟีเจอร์ใหม่ 2</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">Phone</font>"
msgstr "<font class=\"text-600\">โทรศัพท์</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<font class=\"text-600\">Priority A</font>"
msgstr "<font class=\"text-600\">ลำดับความสำคัญ A</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<font class=\"text-600\">Priority B</font>"
msgstr "<font class=\"text-600\">ลำดับความสำคัญ B</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<font class=\"text-600\">Priority C</font>"
msgstr "<font class=\"text-600\">ลำดับความสำคัญ C</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<font class=\"text-600\">Reminder 1</font>"
msgstr "<font class=\"text-600\">เตือนความจำ 1</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<font class=\"text-600\">Reminder 2</font>"
msgstr "<font class=\"text-600\">เตือนความจำ 2</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<font class=\"text-600\">Reminder 3</font>"
msgstr "<font class=\"text-600\">เตือนความจำ 3</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<font class=\"text-600\">Task A</font>"
msgstr "<font class=\"text-600\">งาน A</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<font class=\"text-600\">Task B</font>"
msgstr "<font class=\"text-600\">งาน B</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<font class=\"text-600\">Task C</font>"
msgstr "<font class=\"text-600\">งาน C</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<font class=\"text-600\">Task D</font>"
msgstr "<font class=\"text-600\">งาน D</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">What is their buyer's journey?</font>"
msgstr "<font class=\"text-600\">ประสบการณ์ของผู้ซื้อของพวกเขาเป็นอย่างไร?</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">What is their key decision criteria?</font>"
msgstr "<font class=\"text-600\">เกณฑ์การตัดสินใจที่สำคัญของพวกเขาคืออะไร?</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid ""
"<font class=\"text-600\">What is your action plan to move forward with this account?</font><br>\n"
"                <font class=\"text-600\">Which KPI will be used to measure this progress?</font>"
msgstr ""
"<font class=\"text-600\">แผนการดำเนินการของคุณเพื่อสร้างความคืบหน้ากับบัญชีนี้คืออะไร?</font><br>\n"
"                <font class=\"text-600\">KPI ใดที่จะใช้วัดความคืบหน้านี้?</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid ""
"<font class=\"text-o-color-2\">\n"
"                    <strong>Planned Next Step:</strong>\n"
"                </font>\n"
"                <br>\n"
"                <br>"
msgstr ""
"<font class=\"text-o-color-2\">\n"
"                    <strong>ขั้นต่อไปที่วางแผนไว้:</strong>\n"
"                </font>\n"
"                <br>\n"
"                <br>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_invite_view_form
msgid ""
"<i class=\"fa fa-w fa-info-circle\"/> All external users you selected won't "
"be added to the members."
msgstr ""
"<i class=\"fa fa-w fa-info-circle\"/> "
"ผู้ใช้ภายนอกทั้งหมดที่คุณเลือกจะไม่ถูกเพิ่มเข้าเป็นสมาชิก"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_kanban_items
msgid ""
"<i invisible=\"not is_user_favorite\" class=\"fa fa-star\" title=\"Remove from favorites\"/>\n"
"                                    <i invisible=\"is_user_favorite\" class=\"fa fa-star-o\" title=\"Add to favorites\"/>"
msgstr ""
"<i invisible=\"not is_user_favorite\" class=\"fa fa-star\" title=\"นำออกจากรายการโปรด\"/>\n"
"                                    <i invisible=\"is_user_favorite\" class=\"fa fa-star-o\" title=\"เพิ่มไปที่รายการโปรด\"/>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_kanban
msgid ""
"<i invisible=\"not is_user_favorite\" class=\"fa fa-star\" title=\"Remove from favorites\"/>\n"
"                                <i invisible=\"is_user_favorite\" class=\"fa fa-star-o\" title=\"Add to favorites\"/>"
msgstr ""
"<i invisible=\"not is_user_favorite\" class=\"fa fa-star\" title=\"ลบออกจากรายการโปรด\"/>\n"
"                                <i invisible=\"is_user_favorite\" class=\"fa fa-star-o\" title=\"เพิ่มไปยังรายการโปรด\"/>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid ""
"<i>\n"
"                        <font style=\"color: rgb(0, 255, 0);\"><strong>Green: Values trust above all</strong></font><br>\n"
"                        <font style=\"color: rgb(255, 0, 0);\"><strong>Red: Values results above all</strong></font><br>\n"
"                        <strong>\n"
"                            <font style=\"color: rgb(239, 198, 49);\">\n"
"                                Yellow: Values creativity and enthusiasm above results\n"
"                            </font>\n"
"                        </strong>\n"
"                    </i>"
msgstr ""
"<i>\n"
"                        <font style=\"color: rgb(0, 255, 0);\"><strong>สีเขียว: ให้ความสำคัญกับคุณค่าที่ไว้วางใจเหนือสิ่งอื่นใด</strong></font><br>\n"
"                        <font style=\"color: rgb(255, 0, 0);\"><strong>สีแดง: ให้ความสำคัญกับผลลัพธ์เหนือสิ่งอื่นใด</strong></font><br>\n"
"                        <strong>\n"
"                            <font style=\"color: rgb(239, 198, 49);\">\n"
"                                สีเหลือง: ให้ความสำคัญกับความคิดสร้างสรรค์และความกระตือรือร้นเหนือผลลัพธ์\n"
"                            </font>\n"
"                        </strong>\n"
"                    </i>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid ""
"<i>\n"
"                    <font style=\"color: rgb(8, 82, 148);\">\n"
"                        <strong>Blue: Expects accurate and rigorous results</strong>\n"
"                    </font>\n"
"                </i>"
msgstr ""
"<i>\n"
"                    <font style=\"color: rgb(8, 82, 148);\">\n"
"                        <strong>สีฟ้า: คาดหวังผลลัพธ์ที่แม่นยำและเข้มงวด</strong>\n"
"                    </font>\n"
"                </i>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_release_note
msgid "<span class=\"text-600\">New Feature 1</span>"
msgstr "<span class=\"text-600\">ฟีเจอร์ใหม่ 1</span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_example_branding
msgid ""
"<span style=\"color: rgb(255, 255, 255);font-size: 16px;font-style: normal;font-weight: 400;background-color: rgb(113, 75, 103)\">\n"
"                                        5125C\n"
"                                    </span>"
msgstr ""
"<span style=\"color: rgb(255, 255, 255);font-size: 16px;font-style: normal;font-weight: 400;background-color: rgb(113, 75, 103)\">\n"
"                                        5125C\n"
"                                    </span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<span style=\"font-size: 12px;\">\n"
"                    <font class=\"text-600\">*💡 tick the box when the task is scheduled in the agenda</font>\n"
"                </span>"
msgstr ""
"<span style=\"font-size: 12px;\">\n"
"                    <font class=\"text-600\">*💡 ทำเครื่องหมายในช่องเมื่อมีการกำหนดเวลางานในกำหนดการประชุม</font>\n"
"                </span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<span style=\"font-size: 14px\">\n"
"                                    <font class=\"text-600\">Task A</font>\n"
"                                </span>"
msgstr ""
"<span style=\"font-size: 14px\">\n"
"                                    <font class=\"text-600\">งาน A</font>\n"
"                                </span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<span style=\"font-size: 14px\">\n"
"                                    <font class=\"text-600\">Task B</font>\n"
"                                </span>"
msgstr ""
"<span style=\"font-size: 14px\">\n"
"                                    <font class=\"text-600\">งาน B</font>\n"
"                                </span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<span style=\"font-size: 14px\">\n"
"                                    <font class=\"text-600\">Task C</font>\n"
"                                </span>"
msgstr ""
"<span style=\"font-size: 14px\">\n"
"                                    <font class=\"text-600\">งาน C</font>\n"
"                                </span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<span style=\"font-size: 14px;\">\n"
"                                    <font class=\"text-600\">Task A</font>\n"
"                                </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"                                    <font class=\"text-600\">งาน A</font>\n"
"                                </span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<span style=\"font-size: 14px;\">\n"
"                                    <font class=\"text-600\">Task B</font>\n"
"                                </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"                                    <font class=\"text-600\">งาน B</font>\n"
"                                </span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<span style=\"font-size: 14px;\">\n"
"                                    <font class=\"text-600\">Task C</font>\n"
"                                </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"                                    <font class=\"text-600\">งาน C</font>\n"
"                                </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"                    Add a checklist\n"
"                    (/<span style=\"font-style: italic;\">checklist</span>)\n"
"                </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"                    เพิ่มเช็คลิสต์\n"
"                    (/<span style=\"font-style: italic;\">เช็คลิสต์</span>)\n"
"                </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"                    Add a separator\n"
"                    (/<span style=\"font-style: italic;\">separator</span>)\n"
"                </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"                    เพิ่มตัวคั่น\n"
"                    (/<span style=\"font-style: italic;\">ตัวคั่น</span>)\n"
"                </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"                    Use\n"
"                    /<span style=\"font-style: italic;\">heading</span>\n"
"                    to convert a text into a title\n"
"                </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"                    ใช้\n"
"                    /<span style=\"font-style: italic;\">หัวเรื่อง</span>\n"
"                    เพื่อแปลงข้อความให้เป็นชื่อเรื่อง\n"
"                </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">Favorites</font>\n"
"            </span>\n"
"            — Those are\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">shortcuts</font>\n"
"            </span>\n"
"            you create for yourself.\n"
"            Unstar ⭐ this page at the top to remove it from your favorites.\n"
"        </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">รายการโปรด</font>\n"
"            </span>\n"
"            — นั่นคือ\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">ทางลัด</font>\n"
"            </span>\n"
"            ที่คุณสร้างเพื่อคุณเอง\n"
"            ยกเลิกการติดดาว ⭐ หน้านี้ที่ด้านบนเพื่อลบออกจากรายการโปรดของคุณ\n"
"        </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">Private</font>\n"
"            </span>\n"
"        </span>\n"
"        <span style=\"font-size: 14px;\">\n"
"            — This is\n"
"            <span style=\"font-weight: bolder;\"><font class=\"text-o-color-2\">your stuff</font></span>,\n"
"            the things you keep for yourself\n"
"            (<span style=\"font-style: italic;\">Drafts, Todo lists, ...</span>)\n"
"        </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">ส่วนตัว</font>\n"
"            </span>\n"
"        </span>\n"
"        <span style=\"font-size: 14px;\">\n"
"            — นี่คือ\n"
"            <span style=\"font-weight: bolder;\"><font class=\"text-o-color-2\">สิ่งของของคุณ</font></span>\n"
"            สิ่งที่คุณเก็บไว้ใช้เอง\n"
"            (<span style=\"font-style: italic;\">ฉบับร่าง รายการสิ่งที่ต้องทำ ...</span>)\n"
"        </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">Shared</font>\n"
"            </span>\n"
"        </span>\n"
"        <span style=\"font-size: 14px;\">\n"
"            — Those are the ones you\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">have invited someone or been invited to</font>\n"
"            </span>\n"
"        </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">แชร์แล้ว</font>\n"
"            </span>\n"
"        </span>\n"
"        <span style=\"font-size: 14px;\">\n"
"            — สิ่งเหล่านี้คือสิ่งที่คุณ\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">ได้เชิญใครบางคนหรือได้รับเชิญให้เข้าร่วม</font>\n"
"            </span>\n"
"        </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">Workspace</font>\n"
"            </span>\n"
"        </span>\n"
"        <span style=\"font-size: 14px;\">\n"
"            — Articles there can be accessed by\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">your team</font>\n"
"        </span>\n"
"    </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">เวิร์กสเปซ</font>\n"
"            </span>\n"
"        </span>\n"
"        <span style=\"font-size: 14px;\">\n"
"            — บทความที่นั่นสามารถเข้าถึงได้โดย\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">ทีมของคุณ</font>\n"
"        </span>\n"
"    </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"            Below this list, try\n"
"            <span style=\"font-weight: bolder;\">commands</span>\n"
"            by\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">typing</font>\n"
"            </span>\n"
"            \"<span style=\"font-weight: bolder;\">/</span>\"\n"
"        </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"            ด้านล่างรายการนี้ ให้ลองใช้\n"
"            <span style=\"font-weight: bolder;\">commands</span>\n"
"            โดย\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">การพิมพ์</font>\n"
"            </span>\n"
"            \"<span style=\"font-weight: bolder;\">/</span>\"\n"
"        </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"            Content — Just click and\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">start typing</font>\n"
"            </span>\n"
"            (<span style=\"font-style: italic;\">documentation, tips, reports, ...</span>)\n"
"        </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"            เนื้อหา - แค่คลิกและ\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">เริ่มพิมพ์</font>\n"
"            </span>\n"
"            (<span style=\"font-style: italic;\">เอกสารประกอบการสอน เคล็ดลับ รายงาน ...</span>)\n"
"        </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"            Folders —\n"
"        </span>\n"
"        <span style=\"font-size: 14px;\">\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">Nest other Articles</font>\n"
"            </span>\n"
"            under it to regroup them\n"
"            (<span style=\"font-style: italic;\">per team, topic, project, ...</span>)\n"
"        </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"            โฟลเดอร์ —\n"
"        </span>\n"
"        <span style=\"font-size: 14px;\">\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">ซ้อนบทความอื่นๆ</font>\n"
"            </span>\n"
"            ไว้ข้างใต้เพื่อจัดกลุ่มใหม่\n"
"            (<span style=\"font-style: italic;\">ต่อทีม หัวข้อ โปรเจ็กต์ ...</span>)\n"
"        </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"            Select text to\n"
"            <font class=\"bg-o-color-2\">Highlight</font>,\n"
"            <span style=\"text-decoration-line: line-through;\">strikethrough</span>\n"
"            or\n"
"            <span style=\"font-weight: bolder;\">style</span>\n"
"            <span style=\"font-style: italic; text-decoration-line: underline;\">it</span>\n"
"        </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"            เลือกข้อความเพื่อ\n"
"            <font class=\"bg-o-color-2\">ไฮไลท์</font>,\n"
"            <span style=\"text-decoration-line: line-through;\">ขีดเส้น</span>\n"
"            หรือ\n"
"            <span style=\"font-weight: bolder;\">จัดรูปแบบ</span>\n"
"            <span style=\"font-style: italic; text-decoration-line: underline;\">มัน</span>\n"
"        </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"            Use\n"
"            /<span style=\"font-weight: bolder;\"><font class=\"text-o-color-2\">clipboard</font></span>\n"
"            to insert a\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">clipboard</font>\n"
"            </span>\n"
"            box. Need to re-use its content?\n"
"        </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"            ใช้\n"
"            /<span style=\"font-weight: bolder;\"><font class=\"text-o-color-2\">clipboard</font></span>\n"
"            เพื่อแทรกกล่อง\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">clipboard</font>\n"
"            </span>\n"
"            ต้องการใช้เนื้อหานี้อยู่หรือไม่?\n"
"        </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"            Use\n"
"            /<span style=\"font-weight: bolder;\"><font class=\"text-o-color-2\">file</font></span>\n"
"            to share documents that are frequently needed\n"
"        </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"            ใช้\n"
"            /<span style=\"font-weight: bolder;\"><font class=\"text-o-color-2\">file</font></span>\n"
"            เพื่อแชร์เอกสารที่จำเป็นต้องใช้บ่อยๆ\n"
"        </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Articles</font>\n"
"        </span>\n"
"        are stored into different\n"
"        <span style=\"font-weight: bolder;\"><font class=\"text-o-color-2\">Sections</font></span>:\n"
"    </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">บทความ</font>\n"
"        </span>\n"
"        จะถูกจัดเก็บไว้ใน\n"
"        <span style=\"font-weight: bolder;\"><font class=\"text-o-color-2\">หมวดต่างๆ</font></span>:\n"
"    </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        A good workflow is to write your drafts in\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Private</font>\n"
"        </span>\n"
"        and, once done, move it from\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Private</font>\n"
"        </span>\n"
"        to\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Workspace</font>\n"
"        </span>\n"
"        to share it with everyone.\n"
"    </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"        ขั้นตอนการทำงานที่ดีคือการเขียนฉบับร่างของคุณใน\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">ส่วนตัว</font>\n"
"        </span>\n"
"        และเมื่อเสร็จแล้วให้ย้ายออกจาก\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">ส่วนตัว</font>\n"
"        </span>\n"
"        to\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">เวิร์กสเปซ</font>\n"
"        </span>\n"
"        เพื่อแชร์กับทุกคน\n"
"    </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        And again, to move an\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Article</font>\n"
"        </span>\n"
"        from a\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Section</font>\n"
"        </span>\n"
"        to another, just\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Drag &amp; Drop</font>\n"
"        </span>\n"
"        it.\n"
"    </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"        และอีกครั้งเพื่อย้าย\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">บทความ</font>\n"
"        </span>\n"
"        จาก\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">หมวด</font>\n"
"        </span>\n"
"        หนึ่งไปยังอีกที่หนึ่งเพียงแค่\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">ลาก &amp; วาง</font>\n"
"        </span>\n"
"        \n"
"    </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        This is where you and your team can centralize your\n"
"        <font class=\"text-o-color-2\" style=\"font-weight: bolder;\">Knowledge</font>\n"
"        and best practices! 🚀\n"
"    </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"        นี่คือที่ที่คุณและทีมสามารถรวมศูนย์\n"
"        <font class=\"text-o-color-2\" style=\"font-weight: bolder;\">คลังข้อมูล</font>\n"
"        และแนวทางปฏิบัติที่ดีที่สุดของคุณได้! 🚀\n"
"    </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        Those are your\n"
"        <span style=\"font-weight: bolder;\"><font class=\"text-o-color-2\">Articles</font></span>.\n"
"    </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"        เหล่านั้นคือ\n"
"        <span style=\"font-weight: bolder;\"><font class=\"text-o-color-2\">บทความ</font></span>\n"
"    </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        To change the way\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Articles</font>\n"
"        </span>\n"
"        are organized, you can simply\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Drag &amp; Drop</font>\n"
"        </span>\n"
"        them\n"
"    </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"        หากต้องการเปลี่ยนวิธีการจัดระเบียบ\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">บทความ</font>\n"
"        </span>\n"
"        คุณสามารถทำได้ง่ายๆด้วยการ\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">ลาก &amp; วาง</font>\n"
"        </span>\n"
"        \n"
"    </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        Want to go\n"
"        <span style=\"font-style: italic;\">even</span>\n"
"        faster? ⚡️\n"
"    </span>\n"
"    <br/>\n"
"    <span style=\"font-size: 14px;\">\n"
"        Access\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Articles</font>\n"
"        </span>\n"
"        by opening the\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Command Palette</font>\n"
"        </span>\n"
"        (Ctrl+k/⌘+k) then search through articles by starting your query with\n"
"        \"<span style=\"font-weight: bolder;\">?</span>\".\n"
"    </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"        ต้องการที่จะไปให้\n"
"        <span style=\"font-style: italic;\">เร็ว</span>\n"
"        ยิ่งขึ้น? ⚡️\n"
"    </span>\n"
"    <br/>\n"
"    <span style=\"font-size: 14px;\">\n"
"        เข้าถึง\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">บทความ</font>\n"
"        </span>\n"
"        โดยการเปิด\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Command Palette</font>\n"
"        </span>\n"
"        (Ctrl+k/⌘+k)  จากนั้นค้นหาบทความต่างๆ โดยเริ่มค้นหาด้วย\n"
"        \"<span style=\"font-weight: bolder;\">?</span>\"\n"
"    </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        👈 See the\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Menu</font>\n"
"        </span>\n"
"        there, on the left?\n"
"    </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"        👈 ดูที่\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">เมนู</font>\n"
"        </span>\n"
"       ที่นั่น ทางซ้าย?\n"
"    </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid "<span style=\"font-size: 14px;\">And voilà, it is that simple.</span>"
msgstr "<span style=\"font-size: 14px;\">และนั่นก็ง่ายมาก</span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">Check this box to indicate it's done</span>"
msgstr ""
"<span style=\"font-size: "
"14px;\">ทำเครื่องหมายที่ช่องนี้เพื่อระบุว่าเสร็จสิ้นแล้ว</span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">Click anywhere, and just start "
"typing</span>"
msgstr "<span style=\"font-size: 14px;\">คลิกที่ใดก็ได้แล้วเริ่มพิมพ์</span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid "<span style=\"font-size: 14px;\">Each of them can be used both as:</span>"
msgstr "<span style=\"font-size: 14px;\">แต่ละอันสามารถใช้ได้ทั้งสองอย่าง:</span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">From any Odoo document, find this article "
"by clicking on the 📗 icon in the chatter.</span>"
msgstr ""
"<span style=\"font-size: 14px;\">จากเอกสาร Odoo "
"ให้ค้นหาบทความนี้โดยคลิกที่ไอคอน 📗 ในแชท</span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">From this box, files can be previewed, "
"forwarded and downloaded. 📁</span>"
msgstr ""
"<span style=\"font-size: 14px;\">จากกล่องนี้ คุณสามารถดูตัวอย่าง ส่งต่อ "
"และดาวน์โหลดไฟล์ได้ 📁</span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">Got it? Now let's try advanced features "
"🧠</span>"
msgstr ""
"<span style=\"font-size: 14px;\">เข้าใจแล้วใช่ไหม? "
"ตอนนี้เรามาลองใช้ฟีเจอร์ขั้นสูงไปด้วยกัน 🧠</span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">Need this document somewhere? Come back "
"here by clicking on the 📗 icon in the chatter.</span>"
msgstr ""
"<span style=\"font-size: 14px;\">ต้องการใช้เอกสารนี้ที่ไหนสักแห่งหรือไม่? "
"กลับมาที่นี่โดยคลิกที่ไอคอน 📗 ในแชท</span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">Not sure how to do it? Check the video "
"below 👇</span>"
msgstr ""
"<span style=\"font-size: 14px;\">ไม่แน่ใจว่าต้องทำอย่างไร? "
"ตรวจสอบวิดีโอด้านล่างนี้ 👇</span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">Press Ctrl+Z/⌘+Z to undo any change</span>"
msgstr ""
"<span style=\"font-size: 14px;\">กด Ctrl+Z/⌘+Z "
"เพื่อเลิกทำการเปลี่ยนแปลง</span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">This private page is for you to play around with.</span>\n"
"    <br/>\n"
"    <span style=\"font-size: 14px;\">Ready to give it a spin?</span>"
msgstr ""
"<span style=\"font-size: 14px;\">เพจส่วนตัวนี้มีไว้ให้คุณลองทดสอบ</span>\n"
"    <br/>\n"
"    <span style=\"font-size: 14px;\">พร้อมที่จะลองหรือยัง?</span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid "<span style=\"font-size: 14px;\">Try the following 👇</span>"
msgstr "<span style=\"font-size: 14px;\">ลองทำตามขั้นตอนต่อไปนี้ 👇</span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">You can use the clipboard as a description,"
" a message or simply copy it to your clipboard! 👌</span>"
msgstr ""
"<span style=\"font-size: 14px;\">คุณสามารถใช้คลิปบอร์ดเป็นคำอธิบาย ข้อความ "
"หรือคัดลอกไปยังคลิปบอร์ดของคุณ! 👌</span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid ""
"<span style=\"font-size: 18px;\">\n"
"                                <strong>Color</strong>\n"
"                            </span>"
msgstr ""
"<span style=\"font-size: 18px;\">\n"
"                                <strong>สี</strong>\n"
"                            </span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid ""
"<span style=\"font-size: 18px;\">\n"
"                                <strong>Name</strong>\n"
"                            </span>"
msgstr ""
"<span style=\"font-size: 18px;\">\n"
"                                <strong>ชื่อ</strong>\n"
"                            </span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid ""
"<span style=\"font-size: 18px;\">\n"
"                                <strong>Role</strong>\n"
"                            </span>"
msgstr ""
"<span style=\"font-size: 18px;\">\n"
"                                <strong>หน้าที่</strong>\n"
"                            </span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid ""
"<span style=\"font-size: 18px;\">\n"
"                    1. How many people will live in this house?\n"
"                </span>"
msgstr ""
"<span style=\"font-size: 18px;\">\n"
"                    1. บ้านหลังนี้จะมีคนอยู่กี่คน?\n"
"                </span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid ""
"<span style=\"font-size: 18px;\">\n"
"                    2. What is your budget?\n"
"                </span>"
msgstr ""
"<span style=\"font-size: 18px;\">\n"
"                    2. คุณมีงบประมาณเท่าไหร่?\n"
"                </span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid ""
"<span style=\"font-size: 18px;\">\n"
"                    3. Which style do you prefer: Natural or Industrial?\n"
"                </span>"
msgstr ""
"<span style=\"font-size: 18px;\">\n"
"                    3. คุณชอบสไตล์ไหน: แบบธรรมชาติหรือแบบอุตสาหกรรม?\n"
"                </span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid ""
"<span style=\"font-size: 18px;\">\n"
"                    4. What can I do if I haven't found exactly what I wanted?\"\n"
"                </span>"
msgstr ""
"<span style=\"font-size: 18px;\">\n"
"                    4. ฉันจะทำอย่างไรหากไม่พบสิ่งที่ฉันต้องการอย่างแท้จริง?\"\n"
"                </span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<span style=\"font-size: 18px;\">\n"
"                    Productivity is never an accident. It is always the result of a commitment to excellence, intelligent planning, and focused effort.\n"
"                </span>​\n"
"                - Paul J. Meyer"
msgstr ""
"<span style=\"font-size: 18px;\">\n"
"                    ผลผลิตไม่เคยเป็นเรื่องบังเอิญ มันเป็นผลที่มาจากความมุ่งมั่น การวางแผนที่ชาญฉลาด และความพยายามที่ไม่มีที่สิ้นสุดเสมอ\n"
"                </span>​\n"
"                - Paul J. Meyer"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid ""
"<span style=\"font-size: 18px;\">\n"
"                    To add more, use the clipboard below 👇🏼\n"
"                </span>"
msgstr ""
"<span style=\"font-size: 18px;\">\n"
"                    หากต้องการเพิ่ม ให้ใช้คลิปบอร์ดด้านล่าง👇🏼\n"
"                </span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid ""
"<span style=\"font-size: 24px;\">\n"
"                                Make sure to comply as <strong>you will represent <u>our</u> brand</strong>\n"
"                            </span>\n"
"                            <br>\n"
"                            <span style=\"font-size: 24px;\">If in doubt, get in touch with us.</span>"
msgstr ""
"<span style=\"font-size: 24px;\">\n"
"                                ตรวจสอบให้แน่ใจว่าได้ปฏิบัติตามเนื่องจาก<strong>คุณจะเป็นตัวแทนของ <u>แบรนด์ของเรา</u></strong>\n"
"                            </span>\n"
"                            <br>\n"
"                            <span style=\"font-size: 24px;\">หากมีข้อสงสัยโปรดติดต่อเรา</span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid ""
"<span style=\"font-size: 36px;\">And that's all for this month, folks!<br>\n"
"                    Thanks for reading, see you soon.👋\n"
"                </span>"
msgstr ""
"<span style=\"font-size: 36px;\">และนั่นคือทั้งหมดสำหรับเดือนนี้ทุกคน!<br>\n"
"                    ขอบคุณที่อ่าน แล้วพบกันใหม่ 👋\n"
"                </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid "<span style=\"font-size: 48px;\">🚀</span>"
msgstr "<span style=\"font-size: 48px;\">🚀</span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid ""
"<span style=\"font-weight: bolder;\">\n"
"                    Company Newsletter\n"
"                </span>: <font class=\"text-400\">Month</font>"
msgstr ""
"<span style=\"font-weight: bolder;\">\n"
"                    จดหมายข่าวบริษัท\n"
"                </span>: <font class=\"text-400\">เดือน</font>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-1\">YouTube</font>\n"
"        </span>"
msgstr ""
"<span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-1\">YouTube</font>\n"
"        </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-weight: bolder;\"><font class=\"text-o-"
"color-1\">Facebook</font></span>"
msgstr ""
"<span style=\"font-weight: bolder;\"><font class=\"text-o-"
"color-1\">Facebook</font></span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-weight: bolder;\"><font class=\"text-o-"
"color-1\">X</font></span>"
msgstr ""
"<span style=\"font-weight: bolder;\"><font class=\"text-o-"
"color-1\">X</font></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid ""
"<strong style=\"font-weight: 500\"><span style=\"font-size: 14px\">\n"
"                            Early bird alert</span></strong>"
msgstr ""
"<strong style=\"font-weight: 500\"><span style=\"font-size: 14px\">\n"
"                            การแจ้งเตือนล่วงหน้า</span></strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<strong style=\"font-weight: 500\">Optional Tasks</strong>"
msgstr "<strong style=\"font-weight: 500\">งานทางเลือก</strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<strong style=\"font-weight: 500\">⚡ TOP 3 PRIORITIES</strong>"
msgstr "<strong style=\"font-weight: 500\">⚡ ลำดับความสำคัญ 3 อันดับแรก</strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid ""
"<strong>\n"
"                                    <font style=\"color: rgb(148, 189, 123);\">Do</font>\n"
"                                </strong>"
msgstr ""
"<strong>\n"
"                                    <font style=\"color: rgb(148, 189, 123);\">ทำ</font>\n"
"                                </strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">FRIDAY 🏠 @home</font>\n"
"                                </span>\n"
"                            </strong>"
msgstr ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">วันศุกร์ 🏠 @home</font>\n"
"                                </span>\n"
"                            </strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">MONDAY 🏢 @office</font>\n"
"                                </span>\n"
"                            </strong>"
msgstr ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">วันจันทร์ 🏢 @office</font>\n"
"                                </span>\n"
"                            </strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">Reminders</font>\n"
"                                </span>\n"
"                            </strong>"
msgstr ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">เตือนความจำ</font>\n"
"                                </span>\n"
"                            </strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">SATURDAY 🏠 @home</font>\n"
"                                </span>\n"
"                            </strong>"
msgstr ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">วันเสาร์ 🏠 @home</font>\n"
"                                </span>\n"
"                            </strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">SUNDAY 🏠 @home</font>\n"
"                                </span>\n"
"                            </strong>"
msgstr ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">วันอาทิตย์ 🏠 @home</font>\n"
"                                </span>\n"
"                            </strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">THURSDAY 🏢 @office</font>\n"
"                                </span>\n"
"                            </strong>"
msgstr ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">วันพฤหัสบดี 🏢 @office</font>\n"
"                                </span>\n"
"                            </strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">TUESDAY 🏢 @office</font>\n"
"                                </span>\n"
"                            </strong>"
msgstr ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">วันอังคาร 🏢 @office</font>\n"
"                                </span>\n"
"                            </strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">WEDNESDAY 🏠 @home</font>\n"
"                                </span>\n"
"                            </strong>"
msgstr ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">วันพุธ 🏠 @home</font>\n"
"                                </span>\n"
"                            </strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">Name</span>\n"
"                            </strong>"
msgstr ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">ชื่อ</span>\n"
"                            </strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">Strengths</span>\n"
"                            </strong>"
msgstr ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">จุดแข็ง</span>\n"
"                            </strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">Weaknesses</span>\n"
"                            </strong>"
msgstr ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">จุดอ่อน</span>\n"
"                            </strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid ""
"<strong><font class=\"text-o-color-2\">PRO TIP</font></strong>: From a lead,"
" use the book button in the chatter to find this article and autocomplete "
"your description with this qualification template."
msgstr ""
"<strong><font class=\"text-o-"
"color-2\">เคล็ดลับระดับมืออาชีพ</font></strong>: จากลูกค้าเป้าหมาย "
"ให้ใช้ปุ่มหนังสือในการพูดคุยเพื่อค้นหาบทความนี้และเติมคำอธิบายของคุณโดยอัตโนมัติด้วยเทมเพลตของคุณสมบัตินี้"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid ""
"<strong><font class=\"text-o-color-2\">PRO TIP</font></strong>: From a lead,"
" use the book button in the chatter to find this article and autocomplete "
"your email with this template."
msgstr ""
"<strong><font class=\"text-o-"
"color-2\">เคล็ดลับระดับมืออาชีพ</font></strong>: จากลูกค้าเป้าหมาย "
"ให้ใช้ปุ่มหนังสือในการแชทเพื่อค้นหาบทความนี้และเติมอีเมลของคุณอัตโนมัติด้วยเทมเพลตนี้"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "<strong><font style=\"color: rgb(231, 99, 99);\">Do Not</font></strong>"
msgstr "<strong><font style=\"color: rgb(231, 99, 99);\">ไม่ต้อง</font></strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "<strong><span style=\"font-size: 18px;\">Change</span></strong>"
msgstr "<strong><span style=\"font-size: 18px;\">เปลี่ยน</span></strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "<strong><span style=\"font-size: 18px;\">Complexity</span></strong>"
msgstr "<strong><span style=\"font-size: 18px;\">ความซับซ้อน</span></strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "<strong>Actions Taken:</strong>"
msgstr "<strong>การกระทำที่ดำเนินการแล้ว:</strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "<strong>Company</strong>"
msgstr "<strong>บริษัท</strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "<strong>Contact</strong>"
msgstr "<strong>รายชื่อผู้ติดต่อ</strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "<strong>Do not recreate the Logo from scratch, use these ones</strong>"
msgstr ""
"<strong>ไม่ต้องสร้างโลโก้ขึ้นมาใหม่ตั้งแต่ต้น ให้ใช้โลโก้เหล่านี้</strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<strong>Extra Notes:</strong>"
msgstr "<strong>หมายเหตุเพิ่มเติม:</strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<strong>Fast Facts</strong>"
msgstr "<strong>ข้อมูลด่วน</strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "<strong>Lessons Learnt:</strong>"
msgstr "<strong>บทเรียนที่ได้รับ:</strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid ""
"<strong>M</strong>onthly\n"
"                            <strong>R</strong>ecurring\n"
"                            <strong>R</strong>evenues\n"
"                            (<em>subscriptions, ...</em>)"
msgstr ""
"<strong>รายเดือน\n"
"                            <strong>เกิดซ้ำ\n"
"                            <strong>รายได้\n"
"                            (<em>การสมัครรับข้อมูล ...</em>)"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "<strong>MRR</strong>"
msgstr "<strong>MRR</strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<strong>Main Point of Contact:</strong>"
msgstr "<strong>จุดติดต่อหลัก:</strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid ""
"<strong>N</strong>on-<strong>R</strong>ecurring <strong>R</strong>evenues\n"
"                            (<em>consultancy services, ...</em>)"
msgstr ""
"<strong>N</strong>บน -<strong>การเกิดซ้ำ <strong>รายได้\n"
"                            (<em>บริการให้คำปรึกษา ...</em>)"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "<strong>NRR</strong>"
msgstr "<strong>NRR</strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<strong>Optional Tasks</strong>"
msgstr "<strong>งานทางเลือก</strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "<strong>Q1,2,3,4</strong>"
msgstr "<strong>Q1,2,3,4</strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_release_note
msgid ""
"<strong>Summary</strong>: <font class=\"text-600\">What an exciting release!"
" This time the focus was on...</font>"
msgstr ""
"<strong>สรุป</strong>: <font "
"class=\"text-600\">เป็นการเปิดตัวที่น่าตื่นเต้นมาก! "
"คราวนี้โฟกัสไปที่...</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid ""
"<strong>U</strong>nique <strong>S</strong>elling <strong>P</strong>roposition:\n"
"                            Advantage that makes you stand out from the competition."
msgstr ""
"<strong>มีเอกลักษณ์ <strong>การขาย <strong>ข้อเสนอ:\n"
"                            ความได้เปรียบที่ทำให้คุณโดดเด่นเหนือคู่แข่ง"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "<strong>USP</strong>"
msgstr "<strong>USP</strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<strong>⚡ TOP 3 PRIORITIES</strong>"
msgstr "<strong>⚡ ลำดับความสำคัญ 3 อันดับแรก</strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid "<u>Demographics</u>"
msgstr "<u>ข้อมูลประชากร</u>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid "<u>Key Decision Factors</u>"
msgstr "<u>ปัจจัยการตัดสินใจที่สำคัญ</u>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid "<u>Strategy</u>"
msgstr "<u>กลยุทธ์</u>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "A day or less"
msgstr "หนึ่งวันหรือน้อยกว่านั้น"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid ""
"A must listen for all developers out there and anyone interested in "
"Javascript!"
msgstr "นักพัฒนาทุกคนและทุกคนที่สนใจ Javascript จะต้องรับฟัง!"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
msgid "Abigail"
msgstr "Abigail"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid ""
"About YourCompany: YourCompany is a team of passionate people whose goal is to improve everyone's life\n"
"                        through disruptive products.\n"
"                        We build great products to solve your business problems.\n"
"                        <br>\n"
"                        Our products are designed for small to medium size companies willing to optimize their performance."
msgstr ""
"เกี่ยวกับ YourCompany: YourCompany เป็นทีมคนที่หลงใหลในการช่วยเพิ่มคุณภาพชีวิตของทุกคนผ่าน\n"
"                        ผลิตภัณฑ์ที่สร้างความเปลี่ยนแปลงได้\n"
"                        เราสร้างผลิตภัณฑ์ที่ยอดเยี่ยมเพื่อแก้ไขปัญหาที่ธุรจกิจของคุณมี\n"
"                        <br>\n"
"                        ผลิตภัณฑ์ของเราถูกออกแบบสำหรับบริษัทขนาดเล็กถึงกลางที่ต้องการเพิ่มประสิทธิภาพการทำงานของพวกเขา"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "Absent for more than a day"
msgstr "ขาดไปมากกว่าหนึ่งวัน"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/js/knowledge_controller.js:0
msgid "Access Denied"
msgstr "การเข้าถึงถูกปฏิเสธ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "Access Restricted. May not be shared with everyone from"
msgstr "การเข้าถึงถูกจำกัด อาจจะไม่สามารถแชร์กับทุกคนได้ตั้งแต่"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_account_management
msgid "Account Management"
msgstr "การจัดการบัญชี"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Account Management Cheat Sheet"
msgstr "โพยการจัดการบัญชี"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__message_needaction
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__message_needaction
msgid "Action Needed"
msgstr "จำเป็นต้องดำเนินการ"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Action Plan"
msgstr "แผนปฏิบัติการ"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__active
msgid "Active"
msgstr "เปิดใช้งาน"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__activity_ids
msgid "Activities"
msgstr "กิจกรรม"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "การตกแต่งข้อยกเว้นกิจกรรม"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__activity_state
msgid "Activity State"
msgstr "สถานะกิจกรรม"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__activity_type_icon
msgid "Activity Type Icon"
msgstr "ไอคอนประเภทกิจกรรม"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Add Cover"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Add Icon"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Add Properties"
msgstr "เพิ่มคุณสมบัติ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/properties_panel/properties_panel.xml:0
msgid "Add Property Fields"
msgstr "เพิ่มฟิลด์คุณสมบัติ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comment/comment.xml:0
msgid "Add a Comment..."
msgstr "เพิ่มความคิดเห็น..."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/embedded_clipboard_plugin/embedded_clipboard_plugin.js:0
msgid "Add a clipboard section"
msgstr "เพิ่มส่วนคลิปบอร์ด"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/plugins/comments_plugin/comments_plugin.js:0
msgid "Add a comment to an image"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/plugins/comments_plugin/comments_plugin.js:0
msgid "Add a comment to selection"
msgstr "เพิ่มความคิดเห็นในการเลือก"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comments_popover/comments_popover.xml:0
msgid "Add a comment..."
msgstr "เพิ่มความคิดเห็น..."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_icon/knowledge_icon.xml:0
msgid "Add a random icon"
msgstr "เพิ่มไอคอนแบบสุ่ม"

#. module: knowledge
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_stage_action
msgid ""
"Add an embed kanban view of article items in the body of an article by using"
" '/kanban' command."
msgstr ""
"เพิ่มมุมมองคัมบังแบบฝังของรายการบทความในเนื้อความของบทความโดยใช้คำสั่ง "
"'/kanban'"

#. module: knowledge
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_favorite_action
msgid ""
"Add articles in your list of favorites by clicking on the <i class=\"fa fa-"
"star-o\"></i> next to the article name."
msgstr ""
"เพิ่มบทความในรายการโปรดของคุณโดยคลิก<i class=\"fa fa-"
"star-o\"></i>ที่ถัดจากชื่อบทความ"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_invite_view_form
msgid "Add people or email addresses"
msgstr "เพิ่มบุคคลหรือที่อยู่อีเมล"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "Add people or email addresses..."
msgstr "เพิ่มบุคคลหรือที่อยู่อีเมล..."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.js:0
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_hierarchy
msgid "Add to favorites"
msgstr "เพิ่มในรายการโปรด"

#. module: knowledge
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_member_action
msgid ""
"Adding members allows you to share Articles while granting specific access "
"rights<br>(can write, can read, ...)."
msgstr ""
"การเพิ่มสมาชิกทำให้คุณสามารถแชร์บทความในขณะที่ให้สิทธิ์การเข้าถึงเฉพาะได้<br>(เขียนได้"
" อ่านได้ ...)"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "Admin"
msgstr "แอดมิน"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "Administrator"
msgstr "ผู้ดูแลระบบ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/webclient/commands/knowledge_providers.js:0
msgid "Advanced Search"
msgstr "การค้นหาขั้นสูง"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "Advertising that would appear online or on TV"
msgstr "การโฆษณาที่จะปรากฏทางออนไลน์หรือทางทีวี"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
msgid ""
"After-Sales Service <span class=\"o_stars o_five_stars\" id=\"checkId-5\"><i"
" class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i></span>"
msgstr ""
"บริการหลังการขาย<span class=\"o_stars o_five_stars\" id=\"checkId-5\"><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
msgid ""
"After-Sales Service <span class=\"o_stars o_five_stars\" id=\"checkId-5\"><i"
" class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"
msgstr ""
"บริการหลังการขาย <span class=\"o_stars o_five_stars\" id=\"checkId-5\"><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
msgid ""
"After-Sales Service <span class=\"o_stars o_five_stars\" id=\"checkId-5\"><i"
" class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"
msgstr ""
"บริการหลังการขาย <span class=\"o_stars o_five_stars\" id=\"checkId-5\"><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
msgid ""
"After-Sales Service​ <span class=\"o_stars o_five_stars\" "
"id=\"checkId-5\"><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"
msgstr ""
"บริการหลังการขาย​ <span class=\"o_stars o_five_stars\" id=\"checkId-5\"><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid ""
"After-Sales Service​ <span class=\"o_stars o_five_stars\" "
"id=\"checkId-5\"><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i "
"class=\"fa fa-star-o\"></i></span>"
msgstr ""
"บริการหลังการขาย​ <span class=\"o_stars o_five_stars\" id=\"checkId-5\"><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid "Age:"
msgstr "อายุ:"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
msgid "Age: 24"
msgstr "อายุ: 24"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
msgid "Age: 29"
msgstr "อายุ: 29"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
msgid "Age: 42"
msgstr "อายุ: 42"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comments_panel/comments_panel.xml:0
msgid "All Discussions"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid "All Models 📖"
msgstr "ทุกรุ่น 📖"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/form_status_indicator/form_status_indicator.xml:0
msgid "All changes saved"
msgstr "บันทึกการเปลี่ยนแปลงทั้งหมดแล้ว"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "All my activities will always be encoded in our CRM"
msgstr "กิจกรรมทั้งหมดของฉันจะถูกเข้ารหัสใน CRM ของเราเสมอ"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "Always include sufficient clear space around the logo"
msgstr "เว้นพื้นที่ว่างรอบโลโก้ให้เพียงพอเสมอ"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid ""
"Always type <strong>YourCompany</strong> in the same font size and style as "
"the content of the text"
msgstr ""
"พิมพ์ <strong>YourCompany</strong> "
"ด้วยขนาดฟอนต์และสไตล์เดียวกันกับเนื้อหาของข้อความเสมอ"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "An hour or two (medical appointment, ...)"
msgstr "หนึ่งหรือสองชั่วโมง (นัดหมายแพทย์ ... )"

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_post_mortem
msgid ""
"Analyze what went wrong and the underlying causes. Extract insights to avoid"
" making similar mistakes in the future."
msgstr ""
"วิเคราะห์สิ่งที่ผิดพลาดและสาเหตุที่แท้จริง "
"ดึงข้อมูลเชิงลึกเพื่อหลีกเลี่ยงไม่ให้เกิดข้อผิดพลาดที่คล้ายกันอีกในอนาคต"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__article_anchor_text
msgid "Anchor Text"
msgstr ""

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_hr_faq
msgid "Answer questions frequently asked by your employees"
msgstr "ตอบคำถามที่ถามบ่อยโดยพนักงานของคุณ"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search_items
msgid "Archived"
msgstr "เก็บถาวรแล้ว"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover_dialog.js:0
msgid ""
"Are you sure you want to delete this cover? It will be removed from every "
"article it is used in."
msgstr "คุณแน่ใจหรือไม่ว่าต้องการลบหน้าปกนี้ มันจะถูกลบออกจากทุกบทความที่ใช้"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid ""
"Are you sure you want to leave your private Article? As you are its last "
"member, it will be moved to the Trash."
msgstr ""
"คุณแน่ใจหรือไม่ว่าต้องการออกจากบทความส่วนตัวของคุณ? "
"เนื่องจากคุณเป็นสมาชิกคนสุดท้าย มันจะถูกย้ายไปที่ถังขยะ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.js:0
msgid ""
"Are you sure you want to move \"%(icon)s%(title)s\" to private? Only you "
"will be able to access it."
msgstr ""
"คุณแน่ใจหรือไม่ว่าต้องการย้าย \"%(icon)s%(title)s\" ไปเป็นแบบส่วนตัว "
"มีเพียงคุณเท่านั้นที่จะสามารถเข้าถึงได้"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.js:0
msgid ""
"Are you sure you want to move \"%(icon)s%(title)s\" to the Shared section? "
"It will be shared with all listed members."
msgstr ""
"คุณแน่ใจหรือไม่ว่าต้องการย้าย \"%(icon)s%(title)s\" ไปยังส่วนที่ใช้ร่วมกัน "
"มันจะถูกแชร์กับสมาชิกที่มีรายชื่อทั้งหมด"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.js:0
msgid ""
"Are you sure you want to move \"%(icon)s%(title)s\" to the Workspace? It "
"will be shared with all internal users."
msgstr ""
"คุณแน่ใจหรือไม่ว่าต้องการย้าย \"%(icon)s%(title)s\" ไปยังพื้นที่ทำงาน "
"มันจะถูกแชร์กับผู้ใช้ภายในทั้งหมด"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.js:0
msgid ""
"Are you sure you want to move \"%(icon)s%(title)s\" under "
"\"%(parentIcon)s%(parentTitle)s\"? It will be shared with the same persons."
msgstr ""
"คุณแน่ใจหรือไม่ว่าต้องการย้าย \"%(icon)s%(title)s\" ใต้ "
"\"%(parentIcon)s%(parentTitle)s\" จะถูกแชร์ให้กับบุคคลเดียวกัน"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid ""
"Are you sure you want to remove your member? By leaving an article, you may "
"lose access to it."
msgstr ""
"คุณแน่ใจหรือไม่ว่าต้องการลบสมาชิกของคุณ? "
"การออกจากบทความอาจทำให้คุณสูญเสียการเข้าถึงบทความนั้น"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid "Are you sure you want to remove your own \"Write\" access?"
msgstr "คุณแน่ใจหรือไม่ว่าต้องการลบสิทธิ์การเข้าถึง \"การเขียน\" ของคุณเอง"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid ""
"Are you sure you want to restore access? This means this article will now "
"inherit any access set on its parent articles."
msgstr ""
"คุณแน่ใจหรือไม่ว่าต้องการคืนค่าการเข้าถึง? "
"ซึ่งหมายความว่าบทความนี้จะสืบทอดสิทธิ์การเข้าถึงใดๆ ที่กำหนดไว้ในบทความหลัก"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid ""
"Are you sure you want to restrict access to this article? This means it will"
" no longer inherit access rights from its parents."
msgstr ""
"คุณแน่ใจหรือไม่ว่าต้องการจำกัดการเข้าถึงบทความนี้ "
"ซึ่งหมายความว่าจะไม่สืบทอดสิทธิ์การเข้าถึงหลักอีกต่อไป"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/views/item_calendar/item_calendar_view.js:0
msgid "Are you sure you want to send this article to the trash?"
msgstr "คุณแน่ใจหรือไม่ว่าต้องการส่งบทความนี้ลงถังขยะ?"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid ""
"Are you sure you want to set the internal permission to \"none\"? If you do,"
" you will no longer have access to the article."
msgstr ""
"คุณแน่ใจหรือไม่ว่าต้องการตั้งค่าการอนุญาตภายในเป็น \"ไม่มี\" "
"หากคุณทำเช่นนั้น คุณจะไม่สามารถเข้าถึงบทความได้อีกต่อไป"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid ""
"Are you sure you want to set your permission to \"none\"? If you do, you "
"will no longer have access to the article."
msgstr ""
"คุณแน่ใจหรือไม่ว่าต้องการตั้งค่าการอนุญาตของคุณเป็น \"ไม่มี\"? "
"หากคุณทำเช่นนั้น คุณจะไม่สามารถเข้าถึงบทความได้อีกต่อไป"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/plugins/article_plugin/article_plugin.js:0
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_favorite__article_id
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_member__article_id
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__article_id
#: model:ir.model.fields,field_description:knowledge.field_knowledge_invite__article_id
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_favorite_view_search
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_member_view_search
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_stage_view_search
msgid "Article"
msgstr "บทความ"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article_member.py:0
msgid ""
"Article '%s' should always have a writer: inherit write permission, or have "
"a member with write access"
msgstr ""
"บทความ '%s' ควรมีผู้เขียนเสมอ: สืบทอดสิทธิ์การเขียน "
"หรือมีสมาชิกที่มีสิทธิ์เขียน"

#. module: knowledge
#: model:ir.model,name:knowledge.model_knowledge_article_thread
msgid "Article Discussion Thread"
msgstr "หัวข้อสนทนาบทความ"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__article_properties_definition
msgid "Article Item Properties"
msgstr "คุณสมบัติรายการบทความ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.js:0
#: model:ir.actions.act_window,name:knowledge.knowledge_article_action_item_calendar
#: model:ir.actions.act_window,name:knowledge.knowledge_article_item_action
#: model:ir.actions.act_window,name:knowledge.knowledge_article_item_action_stages
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search
msgid "Article Items"
msgstr "รายการบทความ"

#. module: knowledge
#: model:ir.model,name:knowledge.model_knowledge_article_member
msgid "Article Member"
msgstr "สมาชิกบทความ"

#. module: knowledge
#: model:ir.model,name:knowledge.model_knowledge_article_template_category
msgid "Article Template Category"
msgstr "หมวดหมู่เทมเพลตบทความ"

#. module: knowledge
#: model:ir.actions.act_window,name:knowledge.knowledge_article_template_action
msgid "Article Templates"
msgstr "เทมเพลตบทความ"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__article_url
msgid "Article URL"
msgstr "URL ของบทความ"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__cover_image_id
msgid "Article cover"
msgstr "ปกของบทความ"

#. module: knowledge
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_action_item_calendar
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_item_action
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_item_action_stages
msgid ""
"Article items are articles that exist inside their parents but are not displayed in the menu.\n"
"                They can be used to handle lists (Buildings, Tasks, ...)."
msgstr ""
"รายการบทความคือบทความที่มีอยู่ภายในรายการหลักแต่ไม่ได้แสดงในเมนู\n"
"                สามารถใช้เพื่อจัดการรายการ (อาคาร งาน ...)"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid ""
"Article items are not showed in the left-side menu\n"
"but are shown in inserted kanban/list views"
msgstr ""
"รายการบทความจะไม่แสดงในเมนูด้านซ้าย\n"
"แต่จะแสดงในมุมมองคัมบัง/รายการที่ถูกแทรก"

#. module: knowledge
#: model:ir.model.constraint,message:knowledge.constraint_knowledge_article_check_article_item_parent
msgid "Article items must have a parent."
msgstr "รายการบทความต้องมีพาเรนต์"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "Article shared with you: %s"
msgstr "บทความที่แชร์กับคุณ: %s"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/move_article_dialog/move_article_dialog.js:0
#: model:ir.actions.act_window,name:knowledge.knowledge_article_action
#: model:ir.actions.act_window,name:knowledge.knowledge_article_action_form
#: model:ir.actions.act_window,name:knowledge.knowledge_article_action_form_show_resolved
#: model:ir.actions.server,name:knowledge.ir_actions_server_knowledge_home_page
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search
msgid "Articles"
msgstr "บทความ"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"Articles %s cannot be updated as this would create a recursive hierarchy."
msgstr "ไม่สามารถอัปเดตบทความ %s ได้ เนื่องจากจะสร้างลำดับชั้นแบบเรียกซ้ำ"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_cover__article_ids
msgid "Articles using cover"
msgstr "บทความที่ใช้ปก"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "As a reader, you can't leave a Workspace article"
msgstr "ในฐานะผู้อ่าน คุณไม่สามารถออกจากบทความใน เวิร์กสเปซได้"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid ""
"As an administrator, you can always modify this article and its members."
msgstr "ในฐานะผู้ดูแลระบบ คุณสามารถแก้ไขบทความนี้และสมาชิกได้ตลอดเวลา"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__message_attachment_count
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__message_attachment_count
msgid "Attachment Count"
msgstr "จำนวนสิ่งที่แนบมา"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid "Available Models ✅"
msgstr "รุ่นที่พร้อมใช้งาน ✅"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid "BOOK NOW"
msgstr "จองตอนนี้"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Background:"
msgstr "พื้นหลัง:"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "Backlog"
msgstr "งานค้าง"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "Base Color On"
msgstr "สีพื้นฐานบน"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "Based on"
msgstr "อิงตาม"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Be assertive but listen to what is being said"
msgstr "กล้าแสดงออกแต่ฟังสิ่งที่คนอื่นกำลังพูดด้วยเช่นกัน"

#. module: knowledge
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_action
msgid "Be the first one to unleash the power of Knowledge!"
msgstr "เป็นคนแรกที่จะปลดปล่อยพลังแห่งความรู้!"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__body
msgid "Body"
msgstr "เนื้อความ"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "Booklets for a total price &gt; $1000"
msgstr "หนังสือเล่มเล็กราคารวม &gt; 1,000 ดอลลาร์"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_brand_assets
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "Brand Assets"
msgstr "ทรัพย์สินของแบรนด์"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
msgid ""
"Brand Attraction <span class=\"o_stars o_five_stars\" id=\"checkId-3\"><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star-o\"></i></span>"
msgstr ""
"ความน่าสนใจของแบรนด์ <span class=\"o_stars o_five_stars\" "
"id=\"checkId-3\"><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
msgid ""
"Brand Attraction <span class=\"o_stars o_five_stars\" id=\"checkId-3\"><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"
msgstr ""
"ความน่าสนใจของแบรนด์ <span class=\"o_stars o_five_stars\" "
"id=\"checkId-3\"><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
msgid ""
"Brand Attraction <span class=\"o_stars o_five_stars\" id=\"checkId-3\"><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"
msgstr ""
"ความน่าสนใจของแบรนด์ <span class=\"o_stars o_five_stars\" "
"id=\"checkId-3\"><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid ""
"Brand Attraction <span class=\"o_stars o_five_stars\" id=\"checkId-3\"><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"
msgstr ""
"ความน่าสนใจของแบรนด์ <span class=\"o_stars o_five_stars\" "
"id=\"checkId-3\"><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i "
"class=\"fa fa-star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "Brand Name Rules"
msgstr "กฎเกี่ยวกับชื่อแบรนด์"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.xml:0
msgid "Browse Templates"
msgstr "เรียกดูเทมเพลต"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_release_note
msgid "Bug Fixes 🔨"
msgstr "แก้ไขข้อผิดพลาด 🔨"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.xml:0
msgid "Build an Item Calendar"
msgstr "สร้างปฏิทินรายการ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.xml:0
msgid "Build an Item Kanban"
msgstr "สร้างรายการคัมบัง"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.xml:0
msgid "Build an Item List"
msgstr "สร้างรายการสินค้า"

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_marketing_customer_personas
msgid ""
"Build fictional representation of your customers to better tailor your "
"advertising messages for them. "
msgstr ""
"สร้างการนำเสนอของลูกค้าของคุณเพื่อปรับแต่งข้อความโฆษณาให้เหมาะกับพวกเขาได้ดียิ่งขึ้น"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_release_note
msgid "But Also..."
msgstr "แต่ยัง..."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Buying Process"
msgstr "ขั้นตอนการซื้อ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.js:0
msgid "Calendar of %s"
msgstr "ปฏิทินของ %s"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.js:0
msgid "Calendar of Article Items"
msgstr "ปฏิทินรายการบทความ"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__user_can_write
msgid "Can Edit"
msgstr "สามารถแก้ไข"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__user_can_read
msgid "Can Read"
msgstr "อ่านได้"

#. module: knowledge
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article__inherited_permission__write
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article__internal_permission__write
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article_member__permission__write
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_invite__permission__write
msgid "Can edit"
msgstr "แก้ไขได้"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__is_article_visible_by_everyone
msgid "Can everyone see the Article?"
msgstr "ทุกคนสามารถเห็นบทความได้หรือไม่?"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article_member.py:0
msgid "Can not update the article or partner of a member."
msgstr "ไม่สามารถอัปเดตบทความหรือพาร์ทเนอร์ของสมาชิกได้"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article_favorite.py:0
msgid "Can not update the article or user of a favorite."
msgstr "ไม่สามารถอัปเดตบทความหรือผู้ใช้รายการโปรดได้"

#. module: knowledge
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article__inherited_permission__read
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article__internal_permission__read
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article_member__permission__read
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_invite__permission__read
msgid "Can read"
msgstr "อ่านได้"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__user_has_access_parent_path
msgid "Can the user join?"
msgstr "ผู้ใช้สามารถเข้าร่วมได้หรือไม่?"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__is_article_visible
msgid "Can the user see the article?"
msgstr "ผู้ใช้สามารถดูบทความได้หรือไม่?"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/article_selection_dialog/article_selection_dialog.xml:0
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover.xml:0
#: code:addons/knowledge/static/src/components/move_article_dialog/move_article_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_invite_view_form
msgid "Cancel"
msgstr "ยกเลิก"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"Cannot create an article under article %(parent_name)s which is a non-"
"private parent"
msgstr ""
"ไม่สามารถสร้างบทความภายใต้บทความ %(parent_name)s "
"ซึ่งเป็นพาเรนต์ที่ไม่ใช่ส่วนตัวได้"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid ""
"Capitalize the word <strong>YourCompany</strong>, except if it's part of an "
"URL e.g. website/company"
msgstr ""
"ใช้คำว่า <strong>YourCompany</strong> ให้เป็นตัวพิมพ์ใหญ่ "
"ยกเว้นว่าเป็นส่วนหนึ่งของ URL เช่น เว็บไซต์/บริษัท"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "Car Policy"
msgstr "นโยบายเกี่ยวกับรถยนต์"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/move_article_dialog/move_article_dialog.js:0
msgid "Categories"
msgstr "หมวดหมู่"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_template_category__sequence
msgid "Category Sequence"
msgstr "ลำดับหมวดหมู่"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "Causes"
msgstr "สาเหตุ"

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_account_management
msgid ""
"Centralize account insights in one document for comprehensive monitoring and"
" follow-up."
msgstr ""
"รวมข้อมูลเชิงลึกของบัญชีไว้ในเอกสารเดียวเพื่อการติดตามและติดตามผลที่ครอบคลุม"

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_meeting_minutes
msgid ""
"Centralize team meetings in a single article, while making sure notes are "
"handled efficiently."
msgstr ""
"รวมการประชุมทีมไว้ในบทความเดียว "
"ขณะเดียวกันก็ดูแลให้บันทึกย่อได้รับการจัดการอย่างมีประสิทธิภาพ"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Challenges &amp; Competitive Landscape<br>"
msgstr "ความท้าทาย &amp; การแข่งขันของภูมิประเทศ<br>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "Change 1"
msgstr "การเปลี่ยนแปลง 1"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "Change 2"
msgstr "การเปลี่ยนแปลง 2"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "Change 3"
msgstr "การเปลี่ยนแปลง 3"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid "Change Permission"
msgstr "เปลี่ยนการอนุญาต"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover.xml:0
msgid "Change cover"
msgstr "เปลี่ยนปก"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__child_ids
msgid "Child Articles and Items"
msgstr "บทความและรายการสำหรับเด็ก"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover_dialog.xml:0
msgid "Choose a nice cover"
msgstr "เลือกปกสวยๆ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/article_selection_dialog/article_selection_dialog.js:0
#: code:addons/knowledge/static/src/components/move_article_dialog/move_article_dialog.js:0
msgid "Choose an Article..."
msgstr "เลือกบทความ..."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover.xml:0
msgid "Click and hold to reposition"
msgstr "คลิกค้างไว้เพื่อเปลี่ยนตำแหน่ง"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/core/clipboard/embedded_clipboard.xml:0
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/embedded_clipboard_plugin/embedded_clipboard_plugin.js:0
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Clipboard"
msgstr "คลิปบอร์ด"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.js:0
#: code:addons/knowledge/static/src/js/knowledge_controller.js:0
#: code:addons/knowledge/static/src/macros/abstract_macro.js:0
msgid "Close"
msgstr "ปิด"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/plugins/comments_plugin/comments_plugin.js:0
msgid "Comment"
msgstr "ความคิดเห็น"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Company Abbreviations"
msgstr "ชื่อย่อบริษัท"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Company Details"
msgstr "รายละเอียดบริษัท"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Company Location:"
msgstr "ที่ตั้งบริษัท:"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Company Name:"
msgstr "ชื่อบริษัท:"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_company_newsletter
msgid "Company Newsletter"
msgstr "จดหมายข่าวบริษัท"

#. module: knowledge
#: model:knowledge.article.template.category,name:knowledge.knowledge_article_template_category_company_organization
msgid "Company Organization"
msgstr "องค์กรบริษัท"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Company Structure:"
msgstr "โครงสร้างบริษัท:"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid ""
"Compiled below are tips &amp; tricks collected among our veterans to help "
"newcomers get started. We hope it will help you sign deals."
msgstr ""
"ด้านล่างนี้คือ เคล็ดลับ &amp; เทคนิค "
"ที่รวบรวมไว้ในหมู่ผู้เชี่ยวชาญของเราเพื่อช่วยให้ผู้มาใหม่เริ่มต้นได้ "
"เราหวังว่ามันจะช่วยให้คุณทำข้อตกลงได้"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Complicated buying process"
msgstr "ขั้นตอนการซื้อที่ซับซ้อน"

#. module: knowledge
#: model:ir.ui.menu,name:knowledge.knowledge_menu_configuration
msgid "Configuration"
msgstr "การกำหนดค่า"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
#: code:addons/knowledge/static/src/views/item_calendar/item_calendar_view.js:0
msgid "Confirmation"
msgstr "การยืนยัน"

#. module: knowledge
#: model:ir.model,name:knowledge.model_res_partner
msgid "Contact"
msgstr "ติดต่อ"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid "Contact Us"
msgstr "ติดต่อเรา"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_shared_todos_contact_our_lawyer
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_shared_todos_contact_our_lawyer
msgid "Contact our Lawyer"
msgstr "ติดต่อทนายความของเรา"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/core/clipboard/embedded_clipboard.js:0
msgid "Content copied to clipboard."
msgstr "คัดลอกเนื้อหาไปยังคลิปบอร์ดแล้ว"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Contract Due Date:"
msgstr "วันครบกำหนดสัญญา:"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Convert into Article"
msgstr "แปลงเป็นบทความ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Convert into Article Item"
msgstr "แปลงเป็นรายการบทความ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/core/clipboard/embedded_clipboard.xml:0
msgid "Copy"
msgstr "คัดลอก"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/embedded_view_link/embedded_view_link_popover.xml:0
msgid "Copy Link"
msgstr "คัดลอกลิงก์"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/core/clipboard/embedded_clipboard.xml:0
msgid "Copy to Clipboard"
msgstr "คัดลอกไปที่คลิปบอร์ด"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Costing too much money"
msgstr "มีค่าใช้จ่ายที่สูงเกินไป"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.js:0
msgid ""
"Could not move \"%(icon)s%(title)s\" under "
"\"%(parentIcon)s%(parentTitle)s\", because you do not have write permission "
"on the latter."
msgstr ""
"ไม่สามารถย้าย \"%(icon)s%(title)s\" ใต้ \"%(parentIcon)s%(parentTitle)s\" "
"ได้ เนื่องจากคุณไม่ได้รับอนุญาตให้เขียนในส่วนหลัง"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid ""
"Could you please let me know on which number I could reach you so that we could get in touch?<br>\n"
"                        It should not take longer than 15 minutes."
msgstr ""
"โปรดแจ้งให้เราทราบว่าหมายเลขใดที่ฉันสามารถติดต่อคุณได้เพื่อที่เราจะได้ติดต่อกลับ<br>\n"
"                        ใช้เวลาไม่เกิน 15 นาที"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_cover__attachment_url
msgid "Cover URL"
msgstr "URL หน้าปก"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_cover__attachment_id
msgid "Cover attachment"
msgstr "สิ่งที่แนบมากับปก"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__cover_image_url
msgid "Cover url"
msgstr "URL หน้าปก"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__cover_image_position
msgid "Cover vertical offset"
msgstr "ครอบคลุมออฟเซ็ตแนวตั้ง"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
#: code:addons/knowledge/static/src/xml/knowledge_command_palette.xml:0
msgid "Create \""
msgstr "สร้าง \""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/article_selection_dialog/article_selection_dialog.js:0
msgid "Create \"%s\""
msgstr "สร้าง \"%s\""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Create a Copy"
msgstr "สร้างสำเนา"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar_row.xml:0
msgid "Create a nested article"
msgstr "สร้างบทความแบบซ้อน"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar_section.xml:0
msgid "Create a new article in workspace"
msgstr "สร้างบทความใหม่ในเวิร์กสเปซ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar_section.xml:0
msgid "Create a new private article"
msgstr "สร้างบทความส่วนตัวใหม่"

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_product_catalog
msgid ""
"Create a simple catalog to provide technical details about your products."
msgstr ""
"สร้างแค็ตตาล็อกแบบง่ายๆ เพื่อให้รายละเอียดทางเทคนิคเกี่ยวกับผลิตภัณฑ์ของคุณ"

#. module: knowledge
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_action_item_calendar
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_item_action
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_item_action_stages
msgid "Create an Article Item"
msgstr "สร้างรายการบทความ"

#. module: knowledge
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_action
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_form
msgid "Create an article"
msgstr "สร้างบทความ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Created"
msgstr "สร้างแล้ว"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__create_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_favorite__create_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_member__create_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_stage__create_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_template_category__create_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__create_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_cover__create_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_invite__create_uid
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_tree
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_tree_items
msgid "Created by"
msgstr "สร้างโดย"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__create_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_favorite__create_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_member__create_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_stage__create_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_template_category__create_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__create_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_cover__create_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_invite__create_date
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_tree
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_tree_items
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Current Contract:"
msgstr "สัญญาปัจจุบัน:"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Current Satisfaction:"
msgstr "ความพอใจในปัจจุบัน:"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_marketing_customer_personas
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid "Customer Personas"
msgstr "บุคลิกของลูกค้า"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "DESIGN PROTOTYPE"
msgstr "ต้นแบบการออกแบบ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.js:0
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "Date Properties"
msgstr "คุณสมบัติวันที่"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.js:0
msgid "Date and Time Properties"
msgstr "คุณสมบัติวันที่และเวลา"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_trash_notification
msgid "Dear"
msgstr "เรียน"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "Decision 1"
msgstr "การตัดสินใจ 1"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "Decision 2"
msgstr "การตัดสินใจ 2"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "Decision 3"
msgstr "การตัดสินใจ 3"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "Decision 4"
msgstr "การตัดสินใจ 4"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "Default Access Rights"
msgstr "สิทธิการเข้าถึงเริ่มต้น"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "Default Scale"
msgstr "ขนาดเริ่มต้น"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__internal_permission
msgid ""
"Default permission for all internal users. (External users can still have "
"access to this article if they are added to its members)"
msgstr ""
"สิทธิ์เริ่มต้นสำหรับผู้ใช้ภายในทั้งหมด "
"(ผู้ใช้ภายนอกยังคงสามารถเข้าถึงบทความนี้ได้หากมีการเพิ่มสมาชิก)"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"Deleted articles are stored in Trash an extra <b>%(threshold)s</b> days\n"
"                 before being permanently removed for your database"
msgstr ""
"บทความที่ถูกลบจะถูกเก็บไว้ในถังขยะเพิ่มอีก <b>%(threshold)s</b> วัน\n"
"                 ก่อนที่จะถูกลบออกจากฐานข้อมูลของคุณอย่างถาวร"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__deletion_date
msgid "Deletion Date"
msgstr "วันที่ลบ"

#. module: knowledge
#: model:ir.model.constraint,message:knowledge.constraint_knowledge_article_check_permission_on_desync
msgid "Desynchronized articles must have internal permission."
msgstr "บทความที่ไม่ซิงโครไนซ์ต้องได้รับอนุญาตจากภายใน"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__is_desynchronized
msgid "Desyncronized with parents"
msgstr "ไม่ซิงโครไนซ์กับพาเรนต์"

#. module: knowledge
#: model_terms:digest.tip,tip_description:knowledge.digest_tip_knowledge_0
msgid ""
"Did you know that access rights can be defined per user on any Knowledge "
"Article?"
msgstr ""
"คุณทราบหรือไม่ว่าสิทธิ์การเข้าถึงสามารถกำหนดได้ต่อผู้ใช้ในบทความคลังข้อมูล"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/article_template_picker_dialog/article_template_picker_dialog.xml:0
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover_dialog.xml:0
#: code:addons/knowledge/static/src/components/prompt_embedded_view_name_dialog/prompt_embedded_view_name_dialog.xml:0
#: code:addons/knowledge/static/src/editor/embedded_components/backend/embedded_view_link/embedded_view_link_edit_dialog.xml:0
msgid "Discard"
msgstr "ละทิ้ง"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__display_name
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_favorite__display_name
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_member__display_name
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_stage__display_name
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_template_category__display_name
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__display_name
#: model:ir.model.fields,field_description:knowledge.field_knowledge_cover__display_name
#: model:ir.model.fields,field_description:knowledge.field_knowledge_invite__display_name
msgid "Display Name"
msgstr "แสดงชื่อ"

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_brand_assets
msgid ""
"Distribute brand digital assets while ensuring compliance with company "
"policies and guidelines."
msgstr ""
"เผยแพร่สินทรัพย์ดิจิทัลของแบรนด์ "
"ในขณะเดียวกันก็รับประกันการปฏิบัติตามนโยบายและแนวปฏิบัติของบริษัท"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid "Diving in"
msgstr "ลงลึกเข้าไป"

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_marketing_campaign_brief
msgid ""
"Document your Marketing Campaigns to prioritize key objectives and outcomes."
msgstr ""
"จัดทำเอกสารแคมเปญการตลาดของคุณ "
"เพื่อจัดลำดับความสำคัญของวัตถุประสงค์และผลลัพธ์หลัก"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid ""
"Don't forget to spread the word, we're <em>so</em> looking forward to "
"unveiling this new Odoo version! 🥳"
msgstr ""
"อย่าลืมบอกต่อ เรากำลัง<em>ตั้งตารอ</em>ที่จะเปิดตัว Odoo เวอร์ชันใหม่นี้!  🥳"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
#: model:knowledge.article.stage,name:knowledge.knowledge_article_template_stage_done
msgid "Done"
msgstr "เสร็จสิ้น"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.xml:0
msgid "Drop here to delete this article"
msgstr "วางที่นี่เพื่อลบบทความนี้"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/views/list_view.js:0
msgid "Duplicate"
msgstr "ทำซ้ำ"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_mail_invite
msgid "EDIT"
msgstr "แก้ไข"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/embedded_view_actions_menu/embedded_view_actions_menu.xml:0
msgid "Edit"
msgstr "แก้ไข"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/embedded_view_link/embedded_view_link_edit_dialog.xml:0
#: code:addons/knowledge/static/src/editor/embedded_components/backend/embedded_view_link/embedded_view_link_popover.xml:0
msgid "Edit Link"
msgstr "แก้ไขลิงก์"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid "Education:"
msgstr "การศึกษา:"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
msgid "Education: Bachelor's degree in Marketing"
msgstr "การศึกษา: ปริญญาตรีสาขาการตลาด"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
msgid "Education: High school diploma"
msgstr "การศึกษา: ประกาศนียบัตรมัธยมปลาย"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
msgid "Education: PhD."
msgstr "การศึกษา: ปริญญาเอก"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
msgid "Education: Student"
msgstr "การศึกษา: นักเรียน"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid "Egg'cellent run"
msgstr "การวิ่งครั้งสุดยอด"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Email:"
msgstr "อีเมล:"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/prompt_embedded_view_name_dialog/prompt_embedded_view_name_dialog.js:0
msgid "Embed a View"
msgstr "ฝังมุมมอง"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__icon
msgid "Emoji"
msgstr "อิโมจิ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.js:0
msgid "End Date Time"
msgstr "วันเวลาสิ้นสุด"

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_software_specification
msgid ""
"Ensure all stakeholders of a product change are aligned by clearly "
"communicating the requirements."
msgstr ""
"ตรวจสอบให้แน่ใจว่าผู้มีส่วนได้ส่วนเสียทั้งหมดของการเปลี่ยนแปลงผลิตภัณฑ์มีความสอดคล้องโดยการสื่อสารข้อกำหนดอย่างชัดเจน"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
#: code:addons/knowledge/static/src/macros/abstract_macro.js:0
msgid "Error"
msgstr "ผิดพลาด"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Estimated Revenues:"
msgstr "รายได้โดยประมาณ:"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid "Events 🌍"
msgstr "กิจกรรม 🌍"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "Everyone"
msgstr "ทุกคน"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "Expense Policy"
msgstr "นโยบายค่าใช้จ่าย"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Export"
msgstr "ส่งออก"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_invite_view_form
msgid "Extra Comment"
msgstr "ความเห็นเพิ่มเติม"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "Extra Technical Instructions:"
msgstr "คำแนะนำทางเทคนิคเพิ่มเติม:"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_favorite_view_form
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search
msgid "Favorite"
msgstr "รายการโปรด"

#. module: knowledge
#: model:ir.model,name:knowledge.model_knowledge_article_favorite
msgid "Favorite Article"
msgstr "บทความโปรด"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__favorite_ids
msgid "Favorite Articles"
msgstr "บทความที่ชื่นชอบ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar_section.xml:0
#: model:ir.actions.act_window,name:knowledge.knowledge_article_favorite_action
#: model:ir.ui.menu,name:knowledge.knowledge_article_favorite_menu
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_favorite_view_tree
msgid "Favorites"
msgstr "รายการโปรด"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.portal_my_home_knowledge
msgid "Find all articles shared with you"
msgstr "ค้นหาบทความทั้งหมดที่แชร์กับคุณ"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_stage__fold
msgid "Folded in kanban view"
msgstr "พับในมุมมองคัมบัง"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__message_follower_ids
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__message_follower_ids
msgid "Followers"
msgstr "ผู้ติดตาม"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__message_partner_ids
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__message_partner_ids
msgid "Followers (Partners)"
msgstr "ผู้ติดตาม (พาร์ทเนอร์)"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "ไอคอนแบบฟอนต์ที่ยอดเยี่ยมเช่น fa-tasks"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid ""
"For all other categories, we simply require you to follow the rules listed "
"below."
msgstr ""
"สำหรับหมวดหมู่อื่นๆ ทั้งหมด "
"เราเพียงแต่ขอให้คุณปฏิบัติตามกฎที่ระบุไว้ด้านล่างนี้"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Full Width"
msgstr "เต็มความกว้าง"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__full_width
msgid "Full width"
msgstr "ความกว้างเต็ม"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid "Gender(s):"
msgstr "เพศ:"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
msgid "Gender(s): M"
msgstr "เพศ: ชาย"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
msgid "Gender(s): W"
msgstr "เพศ: หญิง"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.xml:0
msgid "Generate an Article with AI"
msgstr "สร้างบทความด้วย AI"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_favorite_view_search
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_member_view_search
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_stage_view_search
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_template_view_search
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search_items
msgid "Group By"
msgstr "กลุ่มโดย"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "Guest"
msgstr "ลูกค้า"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_hr_faq
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "HR FAQ"
msgstr "คำถามที่พบบ่อยเกี่ยวกับทรัพยากรบุคคล"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__user_has_access
msgid "Has Access"
msgstr "มีการเข้าถึง"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__has_message
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__has_message
msgid "Has Message"
msgstr "มีข้อความ"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__user_has_write_access
msgid "Has Write Access"
msgstr "มีการเข้าถึงการเขียน"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__has_item_children
msgid "Has article item children?"
msgstr "มีรายการบทความย่อยหรือไม่?"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__has_article_children
msgid "Has normal article children?"
msgstr "มีบทความย่อยปกติหรือไม่?"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__user_has_access_parent_path
msgid ""
"Has the user access to each parent from current article until its root?"
msgstr "ผู้ใช้สามารถเข้าถึงพาเรนต์จากบทความปัจจุบันจนกระทั่งถึงรูทหรือไม่"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_invite__have_share_partners
msgid "Have Share Partners"
msgstr "มีหุ้นส่วนแบ่งปัน"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid "Hello there"
msgstr "สวัสดี"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"Hello there, I am a template 👋\n"
"            <br/>\n"
"            Use the buttons at the top-right of this box to re-use my content.\n"
"            <br/>\n"
"            No more time wasted! 🔥"
msgstr ""
"สวัสดี ฉันคือเทมเพลต 👋\n"
"            <br/>\n"
"            ใช้ปุ่มที่ด้านบนขวาของช่องนี้เพื่อนำเนื้อหาของฉันกลับมาใช้ใหม่\n"
"            <br/>\n"
"            ไม่ต้องเสียเวลาอีกต่อไป! 🔥"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid ""
"Here are logos that you can use at your own convenience.\n"
"                <br>\n"
"                They can also be shared with customers, journalists and resellers."
msgstr ""
"นี่คือโลโก้ที่คุณสามารถใช้ตามความสะดวกของคุณเอง\n"
"                <br>\n"
"                นอกจากนี้ยังสามารถแชร์กับลูกค้า นักข่าว และผู้ค้าปลีกได้อีกด้วย"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid ""
"Here is a short guide that will help you pick the right tiny house for you."
msgstr "คำแนะนำที่จะช่วยคุณเลือกบ้านที่เหมาะกับคุณมีดังนี้"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Hey ProspectName,"
msgstr "สวัสดี ผู้มีแนวโน้มจะเป็นลูกค้า"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/xml/knowledge_command_palette.xml:0
msgid "Hidden"
msgstr "ซ่อนแล้ว"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comments_panel/comments_panel.xml:0
msgid "Highlight content and use the"
msgstr "ไฮไลท์เนื้อหาและใช้"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__html_field_history
msgid "History data"
msgstr "ข้อมูลประวัติ"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__html_field_history_metadata
msgid "History metadata"
msgstr "ข้อมูลเมตาของประวัติ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.xml:0
#: model:ir.ui.menu,name:knowledge.knowledge_menu_home
msgid "Home"
msgstr "หน้าแรก"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "Hours Display"
msgstr ""

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_product_catalog_house_cielo
msgid "House Model \"Cielo\""
msgstr "แบบบ้าน \"Cielo\""

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_product_catalog_house_dolcezza
msgid "House Model \"Dolcezza\""
msgstr "แบบบ้าน \"Dolcezza\""

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_product_catalog_house_incanto
msgid "House Model \"Incanto\""
msgstr "แบบบ้าน \"Incanto\""

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_product_catalog_house_serenita
msgid "House Model \"Serenità\""
msgstr "แบบบ้าน \"Serenità\""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog_house_cielo
msgid "House Model - Cielo"
msgstr "แบบบ้าน - เซียโล"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog_house_dolcezza
msgid "House Model - Dolcezza"
msgstr "แบบบ้าน - Dolcezza"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog_house_incanto
msgid "House Model - Incanto"
msgstr "แบบบ้าน - Incanto"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog_house_serenita
msgid "House Model - Serenità"
msgstr "แบบบ้าน - Serenità"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "How do I know which model I can order or not?"
msgstr "จะทราบได้อย่างไรว่าสามารถสั่งซื้อรุ่นใดได้บ้าง?"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "How they measure success:"
msgstr "วิธีวัดความสำเร็จ:"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid "How to find the perfect model for your needs 😍"
msgstr "วิธีค้นหาโมเดลที่เหมาะกับความต้องการของคุณ 😍"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid ""
"I was doing some research online and found your company.<br>\n"
"                        Considering we just launched ProductName, I was thinking you would be interested."
msgstr ""
"ฉันกำลังหาข้อมูลทางออนไลน์และพบบริษัทของคุณ<br>\n"
"                        เมื่อพิจารณาว่าเราเพิ่งเปิดตัว ProductName ฉันคิดว่าคุณอาจจะสนใจ"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "I will not steal prospects from colleagues"
msgstr "ฉันจะไม่ขโมยโอกาสจากเพื่อนร่วมงาน"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "I will not waste time and energy bad-mouthing competitors"
msgstr "ฉันจะไม่เสียเวลาและพลังงานกับคู่แข่งที่ปากร้าย"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "I will only sell a project if I am convinced it can be a success"
msgstr "ฉันจะขายโปรเจ็กต์ก็ต่อเมื่อฉันมั่นใจว่ามันสามารถประสบความสำเร็จได้"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__id
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_favorite__id
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_member__id
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_stage__id
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_template_category__id
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__id
#: model:ir.model.fields,field_description:knowledge.field_knowledge_cover__id
#: model:ir.model.fields,field_description:knowledge.field_knowledge_invite__id
msgid "ID"
msgstr "ไอดี"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__activity_exception_icon
msgid "Icon"
msgstr "ไอคอน"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "ไอคอนเพื่อระบุการยกเว้นกิจกรรม"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Identify the pain points and offer clear solutions"
msgstr "ระบุปัญหาและเสนอแนวทางแก้ไขที่ชัดเจน"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__message_needaction
#: model:ir.model.fields,help:knowledge.field_knowledge_article_thread__message_needaction
msgid "If checked, new messages require your attention."
msgstr "ถ้าเลือก ข้อความใหม่จะต้องการความสนใจจากคุณ"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__message_has_error
#: model:ir.model.fields,help:knowledge.field_knowledge_article__message_has_sms_error
#: model:ir.model.fields,help:knowledge.field_knowledge_article_thread__message_has_error
#: model:ir.model.fields,help:knowledge.field_knowledge_article_thread__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "ถ้าเลือก ข้อความบางข้อความมีข้อผิดพลาดในการส่ง"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid ""
"If none of those offers convinced you, just get in touch with our "
"team.<br>At MyCompany, your happiness is our utmost priority, and we'll go "
"the extra mile to make sure you find what you're looking for!"
msgstr ""
"หากไม่มีข้อเสนอใดที่ทำให้คุณมั่นใจ โปรดติดต่อทีมงานของเรา<br>ที่ MyCompany "
"ความสุขของคุณคือสิ่งสำคัญที่สุดของเรา "
"และเราจะพยายามอย่างเต็มที่เพื่อให้แน่ใจว่าคุณจะพบสิ่งที่คุณกำลังมองหา!"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__is_desynchronized
msgid ""
"If set, this article won't inherit access rules from its parents anymore."
msgstr "หากตั้งค่าไว้ บทความนี้จะไม่สืบทอดกฎการเข้าถึงจากพาเรนต์อีกต่อไป"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "Impact"
msgstr "ผลกระทบ"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_release_note
msgid "Improvements 🔬"
msgstr "การปรับปรุง 🔬"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid "Income:"
msgstr "รายได้:"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
msgid "Income: $109,160"
msgstr "รายได้: $109,160"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
msgid "Income: $142,170"
msgstr "รายได้: $142,170"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
msgid "Income: $293,650"
msgstr "รายได้: $293,650"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
msgid "Income: $68,170"
msgstr "รายได้: $68,170"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Inconsistent customer experience"
msgstr "ประสบการณ์ของลูกค้าที่ไม่สอดคล้องกัน"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/article_index_plugin/article_index_plugin.js:0
msgid "Index"
msgstr "ดัชนี"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid "Industrial ⚙-"
msgstr "ทางอุตสาหกรรม ⚙-"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Industry:"
msgstr "อุตสาหกรรม:"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "Inform HR and your Team Leader."
msgstr "แจ้ง HR และหัวหน้าทีมของคุณ"

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_release_note
msgid "Inform users about your latest software updates and improvements."
msgstr ""
"แจ้งให้ผู้ใช้ทราบเกี่ยวกับการอัปเดตและการปรับปรุงซอฟต์แวร์ล่าสุดของคุณ"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__inherited_permission
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_member__article_permission
msgid "Inherited Permission"
msgstr "สิทธิ์ที่สืบทอดมา"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__inherited_permission_parent_id
msgid "Inherited Permission Parent Article"
msgstr "บทความหลักการอนุญาตที่สืบทอดมา"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
#: code:addons/knowledge/static/src/components/prompt_embedded_view_name_dialog/prompt_embedded_view_name_dialog.xml:0
msgid "Insert"
msgstr "แทรก"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/plugins/article_plugin/article_plugin.js:0
msgid "Insert Link"
msgstr "แทรกลิงค์"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "Insert a Calendar View"
msgstr "แทรกมุมมองปฏิทิน"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/embedded_view_plugin/embedded_view_plugin.js:0
msgid "Insert a Calendar view of article items"
msgstr "แทรกมุมมองปฏิทินของรายการบทความ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/embedded_view_plugin/embedded_view_plugin.js:0
msgid "Insert a Card view of article items"
msgstr "แทรกมุมมองการ์ดของรายการบทความ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/prompt_embedded_view_name_dialog/prompt_embedded_view_name_dialog.js:0
msgid "Insert a Kanban View"
msgstr "แทรกมุมมองคัมบัง"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/embedded_view_plugin/embedded_view_plugin.js:0
msgid "Insert a Kanban view of article items"
msgstr "แทรกมุมมองคัมบังของรายการบทความ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/prompt_embedded_view_name_dialog/prompt_embedded_view_name_dialog.js:0
msgid "Insert a List View"
msgstr "แทรกมุมมองรายการ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/embedded_view_plugin/embedded_view_plugin.js:0
msgid "Insert a List view of article items"
msgstr "แทรกรายการมุมมองของบทความ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/plugins/article_plugin/article_plugin.js:0
msgid "Insert an Article shortcut"
msgstr "แทรกทางลัดบทความ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/external_embedded_view_favorite_menu/insert_embedded_view.xml:0
msgid "Insert link in article"
msgstr "แทรกลิงค์ในบทความ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/external_embedded_view_favorite_menu/insert_embedded_view.xml:0
msgid "Insert view in article"
msgstr "แทรกมุมมองในบทความ"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid ""
"Instead of setting up complicated processes, <strong>we prefer to let our "
"employees buy whatever they need</strong>. Just fill in an expense and we "
"will reimburse you."
msgstr ""
"แทนที่จะตั้งกระบวนการที่ซับซ้อน "
"<strong>เราอยากให้พนักงานของเราซื้อสิ่งที่พวกเขาต้องการ</strong> "
"เพียงกรอกค่าใช้จ่ายแล้วเราจะคืนเงินให้กับคุณ"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid "Interests:"
msgstr "ความสนใจ:"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
msgid "Interests: Cooking"
msgstr "ความสนใจ: การทำอาหาร"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
msgid "Interests: Music"
msgstr "ความสนใจ: ดนตรี"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
msgid "Interests: Politics"
msgstr "ความสนใจ: การเมือง"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
msgid "Interests: Science"
msgstr "ความสนใจ: วิทยาศาสตร์"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__internal_permission
msgid "Internal Permission"
msgstr "การอนุญาตภายใน"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "Invitation to access an article"
msgstr "ขอเชิญเข้าสู่บทความ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_invite_view_form
msgid "Invite"
msgstr "เชิญ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "Invite People"
msgstr "เชิญผู้คน"

#. module: knowledge
#: model:ir.actions.act_window,name:knowledge.knowledge_invite_action_from_article
msgid "Invite people"
msgstr "เชิญบุคคล"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_favorite__is_article_active
msgid "Is Article Active"
msgstr "บทความใช้งานอยู่หรือไม่"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__is_user_favorite
msgid "Is Favorited"
msgstr "เป็นรายการโปรด"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__message_is_follower
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__message_is_follower
msgid "Is Follower"
msgstr "เป็นผู้ติดตาม"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__is_article_item
msgid "Is Item?"
msgstr "ไอเทมใช่ไหม?"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__is_template
msgid "Is Template"
msgstr "เป็นเทมเพลต"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__has_item_parent
msgid "Is the parent an Item?"
msgstr "พาเรนต์เป็นรายการหรือไม่?"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "Issue Summary"
msgstr "สรุปปัญหา"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Issues they are trying to solve:"
msgstr "ปัญหาที่พวกเขาพยายามแก้ไข:"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__template_category_sequence
#: model:ir.model.fields,help:knowledge.field_knowledge_article_template_category__sequence
msgid "It determines the display order of the category"
msgstr "จะกำหนดลำดับการแสดงของหมวดหมู่"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__template_sequence
msgid "It determines the display order of the template within its category"
msgstr "จะกำหนดลำดับการแสดงของเทมเพลตภายในหมวดหมู่"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "Item 1"
msgstr "รายการ 1"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "Item 2"
msgstr "รายการ  2"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "Item 3"
msgstr "รายการ 3"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "Item 4"
msgstr "รายการ 4"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/embedded_view_plugin/embedded_view_plugin.js:0
msgid "Item Calendar"
msgstr "ปฏิทินรายการ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/embedded_view_plugin/embedded_view_plugin.js:0
msgid "Item Cards"
msgstr "บัตรรายการ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/embedded_view_plugin/embedded_view_plugin.js:0
msgid "Item Kanban"
msgstr "รายการคัมบัง"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/embedded_view_plugin/embedded_view_plugin.js:0
msgid "Item List"
msgstr "รายการไอเท็ม"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__stage_id
msgid "Item Stage"
msgstr "รายการขั้นตอน"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_calendar_items
msgid "Items"
msgstr "รายการ"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Job Position:"
msgstr "ตำแหน่งงาน:"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_form
msgid "Join"
msgstr "เข้าร่วม"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar_section.xml:0
msgid "Join a hidden article"
msgstr "เข้าร่วมบทความที่ซ่อนอยู่"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
msgid "Julius"
msgstr "Julius"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.js:0
msgid "Kanban of %s"
msgstr "คัมบังของ %s"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.js:0
msgid "Kanban of Article Items"
msgstr "คัมบังของรายการบทความ"

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_shared_todos
msgid "Keep track of your company to-dos and share them with your colleagues."
msgstr "ติดตามสิ่งที่ต้องทำของบริษัทของคุณและแชร์กับเพื่อนร่วมงานของคุณ"

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_company_newsletter
msgid ""
"Keep your colleagues informed about the company's latest developments and "
"activities through periodic updates."
msgstr ""
"แจ้งให้เพื่อนร่วมงานของคุณทราบเกี่ยวกับการพัฒนาและกิจกรรมล่าสุดของบริษัทผ่านการอัปเดตเป็นระยะ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/external_embedded_view_favorite_menu/insert_embedded_view.xml:0
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/article_index_plugin/article_index_plugin.js:0
#: model:ir.ui.menu,name:knowledge.knowledge_menu_root
#: model:ir.ui.menu,name:knowledge.knowledge_menu_technical
#: model_terms:ir.ui.view,arch_db:knowledge.portal_my_home_knowledge
msgid "Knowledge"
msgstr "ความรู้"

#. module: knowledge
#: model:ir.model,name:knowledge.model_knowledge_article
msgid "Knowledge Article"
msgstr "บทความความรู้"

#. module: knowledge
#: model:ir.model,name:knowledge.model_knowledge_cover
msgid "Knowledge Cover"
msgstr "ปกคลังข้อมูล"

#. module: knowledge
#: model:ir.model,name:knowledge.model_knowledge_invite
msgid "Knowledge Invite Wizard"
msgstr "โปรแกรมการเชิญสำหรับคลังข้อมูล"

#. module: knowledge
#: model:ir.model,name:knowledge.model_knowledge_article_stage
msgid "Knowledge Stage"
msgstr "ขั้นตอนของคลังข้อมูล"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_tree
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_tree_items
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_tree_trash
msgid "Last Edit Date"
msgstr "วันที่แก้ไขล่าสุด"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Last Edited"
msgstr "แก้ไขครั้งล่าสุด"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__last_edition_uid
msgid "Last Edited by"
msgstr "แก้ไขล่าสุดโดย"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__last_edition_date
msgid "Last Edited on"
msgstr "แก้ไขล่าสุดเมื่อ"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__write_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_favorite__write_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_member__write_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_stage__write_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_template_category__write_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__write_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_cover__write_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_invite__write_uid
msgid "Last Updated by"
msgstr "อัปเดตครั้งล่าสุดโดย"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__write_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_favorite__write_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_member__write_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_stage__write_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_template_category__write_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__write_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_cover__write_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_invite__write_date
msgid "Last Updated on"
msgstr "อัปเดตครั้งล่าสุดเมื่อ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid "Leave"
msgstr "ลา"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "Leave Article"
msgstr "บทความการลาหยุด"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid "Leave Private Article"
msgstr "ออกจากบทความส่วนตัว"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "Leaves &amp; Time Off"
msgstr "การลา &amp; ระบบการลา"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "Let your Team Leader know in advance."
msgstr "แจ้งให้หัวหน้าทีมของคุณทราบล่วงหน้า"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/core/embedded_view_link/embedded_view_link_style.js:0
msgid "Link"
msgstr "ลิงก์"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/plugins/article_plugin/article_plugin.js:0
msgid "Link an Article"
msgstr "เชื่อมโยงบทความ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/embedded_view_link_plugin/embedded_view_link_plugin.js:0
msgid "Link copied to clipboard."
msgstr "คัดลอกลิงก์ไปยังคลิปบอร์ดแล้ว"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.js:0
msgid "List of %s"
msgstr "รายการของ %s"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.js:0
msgid "List of Article Items"
msgstr "รายการบทความ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comments_panel/comments_panel.xml:0
msgid "Load More Discussions"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/article_template_picker_dialog/article_template_picker_dialog.xml:0
msgid "Load Template"
msgstr "โหลดเทมเพลต"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.xml:0
msgid "Load a Template"
msgstr "โหลดเทมเพลต"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Lock Content"
msgstr "ล็อคเนื้อหา"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__is_locked
msgid "Locked"
msgstr "ล็อก"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"Logging changes from %(partner_name)s without write access on article "
"%(article_name)s due to hierarchy tree update"
msgstr ""
"การบันทึกการเปลี่ยนแปลงจาก %(partner_name)s "
"โดยไม่มีการเข้าถึงการเขียนในบทความ %(article_name)s "
"เนื่องจากการอัปเดตโครงสร้างลำดับชั้น"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "Logo"
msgstr "โลโก้"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "Logos should only be used in the colors provided"
msgstr "ควรใช้โลโก้ตามสีที่ให้ไว้เท่านั้น"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid "Lose Access"
msgstr "สูญเสียการเข้าถึง"

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_personal_organizer
msgid ""
"Make every week a success by proactively organizing your priority and "
"optional tasks."
msgstr ""
"ทำให้ทุกสัปดาห์ประสบความสำเร็จด้วยการจัดระเบียบลำดับความสำคัญและงานเสริมของคุณในเชิงรุก"

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_sprint_calendar
msgid "Manage your team schedule for the upcoming sprint."
msgstr "จัดการกำหนดการของทีมสำหรับการวิ่งที่กำลังจะมาถึง"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article_thread.py:0
msgid "Mark Comment as Closed"
msgstr "ทำเครื่องหมายความคิดเห็นเป็นปิด"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/mail/message/message_actions.js:0
msgid "Mark the discussion as resolved"
msgstr ""

#. module: knowledge
#: model:knowledge.article.template.category,name:knowledge.knowledge_article_template_category_marketing
msgid "Marketing"
msgstr "การตลาด"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_marketing_campaign_brief
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_campaign_brief
msgid "Marketing Campaign Brief"
msgstr "บทสรุปแคมเปญการตลาด"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_sprint_calendar_meeting
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sprint_calendar_meeting
msgid "Meeting"
msgstr "การประชุม"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_meeting_example_branding
msgid "Meeting Example"
msgstr "ตัวอย่างการประชุม"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_meeting_minutes
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_meeting_minutes_template
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes
msgid "Meeting Minutes"
msgstr "บันทึกการประชุม"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_example_branding
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "Meeting Minutes Template"
msgstr "เทมเพลตรายงานการประชุม"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_member_view_form
msgid "Member"
msgstr "สมาชิก"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
#: model:ir.actions.act_window,name:knowledge.knowledge_article_member_action
#: model:ir.ui.menu,name:knowledge.knowledge_article_member_menu
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_member_view_tree
msgid "Members"
msgstr "สมาชิก"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__article_member_ids
msgid "Members Information"
msgstr "ข้อมูลสมาชิก"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__root_article_id
msgid "Menu Article"
msgstr "บทความเมนู"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_invite__message
msgid "Message"
msgstr "ข้อความ"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__message_has_error
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__message_has_error
msgid "Message Delivery error"
msgstr "เกิดข้อผิดพลาดในการส่งข้อความ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/mail/message/message_model_patch.js:0
msgid "Message Link Copied!"
msgstr "คัดลอกลิงก์ข้อความแล้ว!"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/mail/message/message_model_patch.js:0
msgid "Message Link Copy Failed (Permission denied?)!"
msgstr "การคัดลอกลิงก์ข้อความล้มเหลว (ไม่ได้รับอนุญาต?)!"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__message_ids
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__message_ids
msgid "Messages"
msgstr "ข้อความ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/views/item_calendar/item_calendar_views.xml:0
msgid "Missing Calendar configuration."
msgstr "ไม่มีการกำหนดค่าปฏิทิน"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "More actions"
msgstr "การดำเนินการเพิ่มเติม"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid ""
"More than 150 Odooers participated in this years' edition of the run of 5 or 11\n"
"                            kilometers.<br>Starting from the office, they enjoyed a great tour in the countryside before\n"
"                            coming back to Grand-Rosière, where they were welcomed with a drink and a burger."
msgstr ""
"Odooers มากกว่า 150 คนเข้าร่วมในการวิ่ง 5 หรือ 11 กิโลเมตร\n"
"                            ประจำปีนี้<br>เริ่มต้นจากสำนักงาน พวกเขาสนุกกับการท่องเที่ยวในชนบทก่อนกลับ\n"
"                            มาที่ Grand-Rosière ซึ่งพวกเขาได้รับการต้อนรับด้วยเครื่องดื่มและเบอร์เกอร์"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/move_article_dialog/move_article_dialog.js:0
msgid "Move \"%s\" under:"
msgstr "ย้าย \"%s\" ใต้:"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/move_article_dialog/move_article_dialog.xml:0
msgid "Move Article"
msgstr "บทความเพิ่มเติม"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Move To"
msgstr "ย้ายไปที่"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/move_article_dialog/move_article_dialog.xml:0
msgid "Move an Article"
msgstr "ย้ายบทความ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.js:0
msgid "Move cancelled"
msgstr "ย้ายไปที่ยกเลิกแแล้ว"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/move_article_dialog/move_article_dialog.xml:0
msgid "Move the untitled article under:"
msgstr "ย้ายบทความที่ไม่มีชื่อไปไว้ใต้:"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.js:0
msgid "Move to Private"
msgstr "ย้ายไปที่ส่วนตัว"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.js:0
msgid "Move to Shared"
msgstr "ย้ายไปที่แชร์แล้ว"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid "Move to Trash"
msgstr "ย้ายไปที่ถังขยะ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.js:0
msgid "Move to Workspace"
msgstr "ย้ายไปที่เวิร์กสเปซ"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "วันครบกำหนดกิจกรรมของฉัน"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search_items
msgid "My Favorites"
msgstr "รายการโปรดของฉัน"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "My Forecast will always be accurate and up-to-date"
msgstr "การคาดการณ์ของฉันจะแม่นยำและอัปเดตอยู่เสมอ"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search_items
msgid "My Items"
msgstr "รายการของฉัน"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_form_item_quick_create
msgid "My New Item"
msgstr "รายการใหม่ของฉัน"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/embedded_view_link/embedded_view_link_edit_dialog.xml:0
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_stage__name
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_template_view_tree
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_tree
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_tree_items
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_tree_trash
msgid "Name"
msgstr "ชื่อ"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid "Natural Style ☘ -"
msgstr "แบบธรรมชาติ ☘ -"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid "Navigation Basics 🐣"
msgstr "พื้นฐานการนำทาง 🐣"

#. module: knowledge
#. odoo-javascript
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
#: code:addons/knowledge/static/src/js/knowledge_controller.js:0
#: code:addons/knowledge/static/src/views/item_calendar/item_calendar_views.xml:0
#: model:knowledge.article.stage,name:knowledge.knowledge_article_template_stage_new
msgid "New"
msgstr "ใหม่"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_release_note
msgid "New Features 🎉"
msgstr "ฟีเจอร์ใหม่ 🎉"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article_thread.py:0
msgid "New Mention in %s"
msgstr "มีการกล่าวถึงใหม่ใน %s"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.js:0
msgid "New property could not be created."
msgstr "ไม่สามารถสร้างคุณสมบัติใหม่ได้"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "ปฏิทินอีเวนต์กิจกรรมถัดไป"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "วันครบกำหนดกิจกรรมถัดไป"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__activity_summary
msgid "Next Activity Summary"
msgstr "สรุปกิจกรรมถัดไป"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__activity_type_id
msgid "Next Activity Type"
msgstr "ประเภทกิจกรรมถัดไป"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "Next Meeting: <font class=\"text-400\">@Date</font>"
msgstr "การประชุมครั้งต่อไป: <font class=\"text-400\">@วันที่</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_example_branding
msgid "Next Meeting: <u>6th May, @John's Office</u>"
msgstr "การประชุมครั้งถัดไป: <u>วันที่ 6 พฤษภาคม @สำนักงาน John</u>"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/xml/knowledge_command_palette.xml:0
msgid "No Article found."
msgstr "ไม่พบบทความ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/webclient/commands/knowledge_providers.js:0
msgid "No Article found. Create \"%s\""
msgstr "ไม่พบบทความ สร้าง \"%s\""

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "No Article in Trash"
msgstr "ไม่มีบทความในถังขยะ"

#. module: knowledge
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_favorite_action
msgid "No Favorites yet!"
msgstr "ยังไม่มีรายการโปรด!"

#. module: knowledge
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_member_action
msgid "No Members yet!"
msgstr "ยังไม่มีสมาชิก!"

#. module: knowledge
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article__inherited_permission__none
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article__internal_permission__none
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article_member__permission__none
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_invite__permission__none
msgid "No access"
msgstr "ไม่มีการเข้าถึง"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/webclient/commands/knowledge_providers.js:0
msgid "No article found."
msgstr "ไม่พบบทความ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/core/article_index/readonly_article_index.xml:0
msgid "No article to display"
msgstr ""

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_form
msgid "No article yet."
msgstr "ยังไม่มีบทความ"

#. module: knowledge
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_stage_action
msgid "No stage yet!"
msgstr "ยังไม่มีขั้นตอน!"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/article_template_picker_dialog/article_template_picker_dialog.xml:0
msgid "No template yet."
msgstr "ยังไม่มีเทมเพลต"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comments_panel/comments_panel.xml:0
msgid "Nothing going on!"
msgstr "ไม่มีอะไรเกิดขึ้น!"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid ""
"Nth <strong>Q</strong>uarter of the fiscal year.<br>\n"
"                            <em>E.g. Q4 starts on Oct. 1 and ends on Dec. 31.</em>"
msgstr ""
"Nth <strong>ไตร</strong>มาสของปีงบประมาณ<br>\n"
"                            <em>เช่น ไตรมาสที่ 4 เริ่มวันที่ 1 ตุลาคม และสิ้นสุดในวันที่ 31 ธันวาคม</em>"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__message_needaction_counter
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__message_needaction_counter
msgid "Number of Actions"
msgstr "จํานวนการดําเนินการ"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__message_has_error_counter
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__message_has_error_counter
msgid "Number of errors"
msgstr "จํานวนข้อผิดพลาด"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__message_needaction_counter
#: model:ir.model.fields,help:knowledge.field_knowledge_article_thread__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "จำนวนข้อความที่ต้องดำเนินการ"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__message_has_error_counter
#: model:ir.model.fields,help:knowledge.field_knowledge_article_thread__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "จํานวนข้อความที่มีข้อผิดพลาดในการส่ง"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Objectives with our Collaboration"
msgstr "วัตถุประสงค์ในการทำงานร่วมกันของเรา"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_example_branding
msgid "Odoo Brand Assets"
msgstr "ทรัพย์สินของแบรนด์ Odoo"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid "Odoo Experience 🎉"
msgstr "ประสบการณ์ Odoo  🎉"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_example_branding
msgid "Odoo: Manage your SME online"
msgstr "Odoo: จัดการ SME ของคุณทางออนไลน์"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
#: model:knowledge.article.stage,name:knowledge.knowledge_article_template_stage_ongoing
msgid "Ongoing"
msgstr "กำลังดำเนินการ"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "Only internal users are allowed to alter memberships."
msgstr "เฉพาะผู้ใช้ภายในเท่านั้นที่ได้รับอนุญาตให้เปลี่ยนแปลงการเป็นสมาชิก"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "Only internal users are allowed to create workspace root articles."
msgstr "เฉพาะผู้ใช้ภายในเท่านั้นที่ได้รับอนุญาตให้สร้างบทความรูทของเวิร์กสเปซ"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "Only internal users are allowed to modify this information."
msgstr "เฉพาะผู้ใช้ภายในเท่านั้นที่ได้รับอนุญาตให้แก้ไขข้อมูลนี้"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "Only internal users are allowed to remove memberships."
msgstr "เฉพาะผู้ใช้ภายในเท่านั้นที่ได้รับอนุญาตให้ลบการเป็นสมาชิกได้"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"Only internal users are allowed to restore the original article access "
"information."
msgstr ""
"เฉพาะผู้ใช้ภายในเท่านั้นที่ได้รับอนุญาตให้กู้คืนข้อมูลการเข้าถึงบทความต้นฉบับ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/webclient/commands/knowledge_providers.js:0
msgid "Oops, there's nothing here. Try another search."
msgstr "อ๊ะ ไม่มีอะไรที่นี่ ลองค้นหาอีกครั้ง"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/embedded_view_actions_menu/embedded_view_actions_menu.xml:0
#: code:addons/knowledge/static/src/components/sidebar/sidebar.js:0
#: code:addons/knowledge/static/src/views/item_calendar/item_calendar_views.xml:0
msgid "Open"
msgstr "เปิด"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comments_panel/comments_panel.xml:0
msgid "Open Discussions"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Open comments panel"
msgstr "เปิดแผงความคิดเห็น"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Open history"
msgstr "เปิดประวัติ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.xml:0
msgid "Open the Trash"
msgstr "เปิดถังขยะ"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_example_branding
msgid "OpenERP becomes Odoo"
msgstr "OpenERP กลายเป็น Odoo"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/properties_panel/properties_panel.xml:0
msgid ""
"Organize your database with custom fields\n"
"                        (Text, Selection, ...)."
msgstr ""
"จัดระเบียบฐานข้อมูลของคุณด้วยฟิลด์ที่กำหนดเอง\n"
"                        (ข้อความ, ตัวเลือก, ...)"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_shared_todos
msgid "Otherwise, feel free to handle others listed below:"
msgstr "ไม่เช่นนั้น คุณสามารถจัดการกับผู้อื่นตามรายการด้านล่างนี้:"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid ""
"Our catalog can be found here and is updated every 2 years. If you do not "
"manage to find the specific model you are looking for,"
msgstr ""
"แคตตาล็อกของเราสามารถพบได้ที่นี่และอัปเดตทุก 2 ปี "
"หากคุณไม่สามารถค้นหารุ่นเฉพาะที่คุณกำลังมองหาได้"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "Outcome"
msgstr "ผลลัพธ์"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_stage__parent_id
msgid "Owner Article"
msgstr "บทความเจ้าของ"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid "PLANET ODOO"
msgstr "PLANET ODOO"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Pain Points (<em>tick the relevant ones</em>)"
msgstr "ปัญหาของลูกค้า (<em>ทำเครื่องหมายในช่องที่เกี่ยวข้อง</em>)"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__parent_id
msgid "Parent Article"
msgstr "บทความหลัก"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__parent_path
msgid "Parent Path"
msgstr "เส้นทางหลัก"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_template_view_form
msgid "Parent Template"
msgstr "เทมเพลตหลัก"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_member__partner_id
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_member_view_search
msgid "Partner"
msgstr "พาร์ทเนอร์"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_shared_todos_pay_the_electricity_bill
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_shared_todos_pay_the_electricity_bill
msgid "Pay the Electricity Bill"
msgstr "ชำระค่าไฟฟ้า"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_member__permission
#: model:ir.model.fields,field_description:knowledge.field_knowledge_invite__permission
msgid "Permission"
msgstr "การอนุญาต"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_marketing_customer_persona_1
msgid "Persona 1"
msgstr "บุคลิก 1"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_marketing_customer_persona_2
msgid "Persona 2"
msgstr "บุคลิก 2"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_marketing_customer_persona_3
msgid "Persona 3"
msgstr "บุคลิก 3"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_marketing_customer_persona_4
msgid "Persona 4"
msgstr "บุคลิก 4"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_personal_organizer
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "Personal Organizer"
msgstr "ผู้จัดงานส่วนตัว"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Phone:"
msgstr "โทรศัพท์:"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_shared_todos
msgid "Please pick the following tasks first:"
msgstr "โปรดเลือกงานต่อไปนี้ก่อน:"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "Please refer to the chart below."
msgstr "โปรดดูแผนภูมิด้านล่าง"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid ""
"Please submit a request for assistance to the Marketing Team if you fall "
"into one of the following:"
msgstr ""
"กรุณาส่งคำขอความช่วยเหลือไปยังทีมการตลาดหากคุณเข้าข่ายข้อใดข้อหนึ่งต่อไปนี้:"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "Pluralize the trademark (e.g.<em>YourCompanies</em>)"
msgstr "สร้างเครื่องหมายการค้าหลายรายการ (เช่น <em>YourCompanies</em>)"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid "Podcast updates 📻"
msgstr "อัปเดตพอดแคสต์ 📻"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "Post-Mortem Analysis"
msgstr "การวิเคราะห์หลังการชันสูตรพลิกศพ"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_post_mortem
msgid "Post-mortem"
msgstr "การชันสูตรศพ"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Potential Risks:"
msgstr "ความเสี่ยงที่อาจเกิดขึ้น:"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid ""
"Prepare your demos in advance and integrate the prospect's use case into it"
msgstr ""
"เตรียมการสาธิตของคุณล่วงหน้าและรวมกรณีการใช้งานของลูกค้าเป้าหมายเข้าไปด้วย"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/embedded_view_link/embedded_view_link_edit_dialog.xml:0
msgid "Preview"
msgstr "ตัวอย่าง"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
msgid ""
"Price <span class=\"o_stars o_five_stars\" id=\"checkId-1\"><i class=\"fa "
"fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i></span>"
msgstr ""
"ราคา <span class=\"o_stars o_five_stars\" id=\"checkId-1\"><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
msgid ""
"Price <span class=\"o_stars o_five_stars\" id=\"checkId-1\"><i class=\"fa "
"fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star-o\"></i></span>"
msgstr ""
"ราคา <span class=\"o_stars o_five_stars\" id=\"checkId-1\"><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
msgid ""
"Price <span class=\"o_stars o_five_stars\" id=\"checkId-1\"><i class=\"fa "
"fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i></span>"
msgstr ""
"ราคา <span class=\"o_stars o_five_stars\" id=\"checkId-1\"><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
msgid ""
"Price <span class=\"o_stars o_five_stars\" id=\"checkId-1\"><i class=\"fa "
"fa-star\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i"
" class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i></span>"
msgstr ""
"ราคา <span class=\"o_stars o_five_stars\" id=\"checkId-1\"><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid ""
"Price <span class=\"o_stars o_five_stars\" id=\"checkId-1\"><i class=\"fa "
"fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>​"
msgstr ""
"ราคา <span class=\"o_stars o_five_stars\" id=\"checkId-1\"><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i></span>​"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/core/embedded_view_link/embedded_view_link_style.js:0
msgid "Primary"
msgstr "หลัก"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/move_article_dialog/move_article_dialog.js:0
#: code:addons/knowledge/static/src/components/sidebar/sidebar_section.xml:0
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article__category__private
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search
msgid "Private"
msgstr "ส่วนตัว"

#. module: knowledge
#: model:knowledge.article.template.category,name:knowledge.knowledge_article_template_category_product_management
msgid "Product Management"
msgstr "การจัดการผลิตภัณฑ์"

#. module: knowledge
#: model:knowledge.article.template.category,name:knowledge.knowledge_article_template_category_productivity
msgid "Productivity"
msgstr "ผลิตผล"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__article_properties
msgid "Properties"
msgstr "คุณสมบัติ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid ""
"Properties are fields that can only be added on articles that have a parent."
msgstr "คุณสมบัติคือฟิลด์ที่สามารถเพิ่มได้เฉพาะในบทความที่มีพาเรนต์เท่านั้น"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "Property Field"
msgstr "ฟิลด์คุณสมบัติ"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Prospect Qualification"
msgstr "คุณสมบัติผู้มีแนวโน้มจะเป็นลูกค้า"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Prospection Templates"
msgstr "เทมเพลตการสำรวจ"

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_sales_playbook
msgid ""
"Provide salespeople with tips, lexicon and templates to help them sell "
"faster."
msgstr ""
"ให้คำแนะนำ พจนานุกรม และเทมเพลตกับพนักงานขายเพื่อช่วยให้พวกเขาขายได้เร็วขึ้น"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_sprint_calendar_public_holiday
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sprint_calendar_public_holiday
msgid "Public Holidays"
msgstr "วันหยุดตามกฎหมาย"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "Q1: <font class=\"text-600\">Would it be possible to...</font>"
msgstr "Q1: <font class=\"text-600\">เป็นไปได้ไหมที่จะ...</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "Q2: <font class=\"text-600\">Would there be an issue if...</font>"
msgstr "Q2: <font class=\"text-600\">จะมีปัญหาไหมถ้า...</font>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_mail_invite
msgid "READ"
msgstr "อ่าน"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__rating_ids
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__rating_ids
msgid "Ratings"
msgstr "การให้คะแนน"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/mail/message/message_actions.js:0
msgid "Re-open the discussion"
msgstr ""

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "Read"
msgstr "อ่าน"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_invite__partner_ids
msgid "Recipients"
msgstr "ผู้รับ"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "Recovery"
msgstr "การกู้คืน"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/article_index/article_index.xml:0
msgid "Refresh"
msgstr "รีเฟรช"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_release_note
msgid "Release Notes"
msgstr "บันทึกประจำรุ่น"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_release_note
msgid "Release Notes 🎉"
msgstr "บันทึกประจำรุ่น 🎉"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover.xml:0
msgid "Remove"
msgstr "นำออก"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Remove Cover"
msgstr "นำหน้าปกออก"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
#: code:addons/knowledge/static/src/mail/emoji_picker/emoji_picker_patch.xml:0
msgid "Remove Icon"
msgstr "ลบไอคอน"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/embedded_view_link/embedded_view_link_popover.xml:0
msgid "Remove Link"
msgstr "นำลิงก์ออก"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "Remove Member"
msgstr "ลบสมาชิก"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover.xml:0
msgid "Remove cover"
msgstr "ลบปก"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.js:0
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_hierarchy
msgid "Remove from favorites"
msgstr "ลบออกจากรายการโปรด"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/prompt_embedded_view_name_dialog/prompt_embedded_view_name_dialog.xml:0
msgid "Rename"
msgstr "ตั้งชื่อใหม่"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover.xml:0
msgid "Replace"
msgstr "แทนที่"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover.xml:0
msgid "Replace cover"
msgstr "เปลี่ยนปก"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover.xml:0
msgid "Reposition"
msgstr "เปลี่ยนตำแหน่ง"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover.xml:0
msgid "Reposition cover"
msgstr "เปลี่ยนตำแหน่งปก"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comments_panel/comments_panel.xml:0
msgid "Resolved Discussions"
msgstr ""

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__activity_user_id
msgid "Responsible User"
msgstr "ผู้ใช้ที่รับผิดชอบ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_form
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_tree_trash
msgid "Restore"
msgstr "คืนค่า"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid "Restore Access"
msgstr "คืนค่าการเข้าถึง"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Restore from Trash"
msgstr "กู้คืนจากถังขยะ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid "Restrict Access"
msgstr "จำกัดการเข้าถึง"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid "Restrict own access"
msgstr "จำกัดการเข้าถึงของตนเอง"

#. module: knowledge
#: model:ir.model.constraint,message:knowledge.constraint_knowledge_article_check_desync_on_root
msgid "Root articles cannot be desynchronized."
msgstr "บทความหลักไม่สามารถยกเลิกการซิงโครไนซ์ได้"

#. module: knowledge
#: model:ir.model.constraint,message:knowledge.constraint_knowledge_article_check_permission_on_root
msgid "Root articles must have internal permission."
msgstr "บทความหลักต้องได้รับอนุญาตจากภายใน"

#. module: knowledge
#: model:ir.model.constraint,message:knowledge.constraint_knowledge_article_check_template_category_on_root
msgid "Root templates must have a category."
msgstr "เทมเพลตรูทต้องมีหมวดหมู่"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "SEO &amp; SEA projects (betting on keywords, ...)"
msgstr "SEO &amp; โปรเจ็กต์ SEA (การประมูลคีย์เวิร์ด ... )"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__message_has_sms_error
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__message_has_sms_error
msgid "SMS Delivery error"
msgstr "ข้อผิดพลาดในการส่ง SMS"

#. module: knowledge
#: model:knowledge.article.template.category,name:knowledge.knowledge_article_template_category_sales
msgid "Sales"
msgstr "การขาย"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Sales Details"
msgstr "รายละเอียดการขาย"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_sales_playbook
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Sales Playbook"
msgstr "คู่มือการขาย"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
#: code:addons/knowledge/static/src/editor/embedded_components/backend/embedded_view_link/embedded_view_link_edit_dialog.xml:0
msgid "Save"
msgstr "บันทึก"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover.xml:0
msgid "Save Position"
msgstr "บันทึกตำแหน่ง"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover.xml:0
msgid "Save position"
msgstr "บันทึกตำแหน่ง"

#. module: knowledge
#: model:ir.ui.menu,name:knowledge.knowledge_menu_article
msgid "Search"
msgstr "ค้นหา"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_favorite_view_search
msgid "Search Favorites"
msgstr "ค้นหารายการโปรด"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/xml/form_controller.xml:0
msgid "Search Knowledge Articles"
msgstr "ค้นหาบทความความรู้"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_member_view_search
msgid "Search Members"
msgstr "ค้นหาสมาชิก"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_stage_view_search
msgid "Search Stages"
msgstr "ค้นหาขั้นตอน"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.js:0
msgid "Search an Article..."
msgstr "ค้นหาบทความ..."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.xml:0
msgid "Search an article..."
msgstr "ค้นหาบทความ..."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/webclient/commands/knowledge_providers.js:0
msgid "Search for an article..."
msgstr "ค้นหาบทความ..."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/webclient/commands/knowledge_providers.js:0
msgid "Search hidden Articles..."
msgstr "ค้นหาบทความที่ซ่อนอยู่..."

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search
msgid "Search results"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/core/embedded_view_link/embedded_view_link_style.js:0
msgid "Secondary"
msgstr "รอง"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__category
msgid "Section"
msgstr "ส่วน"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "See a doctor and send us the sick note."
msgstr "พบแพทย์และส่งบันทึกลาป่วยให้เรา"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/article_template_picker_dialog/article_template_picker_dialog.xml:0
msgid "Select a Template"
msgstr "เลือกเทมเพลต"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/external_embedded_view_favorite_menu/embedded_view_favorite_menu.js:0
msgid "Select an article"
msgstr "เลือกบทความ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/clipboard/macros_embedded_clipboard.xml:0
#: code:addons/knowledge/static/src/editor/embedded_components/backend/file/macros_file_mixin.xml:0
msgid "Send as Message"
msgstr "ส่งเป็นข้อความ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_tree
msgid "Send to Trash"
msgstr "ส่งไปที่ถังขยะ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/views/item_calendar/item_calendar_view.js:0
msgid "Send to trash"
msgstr "ส่งไปที่ถังขยะ"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__sequence
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_favorite__sequence
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_stage__sequence
msgid "Sequence"
msgstr "ลำดับ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Share"
msgstr "แชร์"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/move_article_dialog/move_article_dialog.js:0
#: code:addons/knowledge/static/src/components/sidebar/sidebar_section.xml:0
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article__category__shared
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search
msgid "Shared"
msgstr "แบ่งปัน"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_shared_todos
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_shared_todos
msgid "Shared To-Do List"
msgstr "รายการสิ่งที่ต้องทำที่ใช้ร่วมกัน"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/article_index/article_index.xml:0
msgid "Show All"
msgstr "แสดงทั้งหมด"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comment/comment.xml:0
#: code:addons/knowledge/static/src/editor/embedded_components/backend/article_index/article_index.xml:0
msgid "Show Less"
msgstr "แสดงน้อยลง"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comment/comment.xml:0
msgid "Show More"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Show Properties"
msgstr "แสดงคุณสมบัติ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "Show Weekends?"
msgstr ""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/article_index_plugin/article_index_plugin.js:0
msgid "Show nested articles"
msgstr "แสดงบทความที่ซ้อนกัน"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Size:"
msgstr "ขนาด:"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
msgid ""
"Social <span class=\"o_stars o_five_stars\" id=\"checkId-4\"><i class=\"fa "
"fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i></span>"
msgstr ""
"โซเชียล <span class=\"o_stars o_five_stars\" id=\"checkId-4\"><i class=\"fa "
"fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
msgid ""
"Social Status <span class=\"o_stars o_five_stars\" id=\"checkId-4\"><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star-o\"></i></span>"
msgstr ""
"สถานะของโซเชียล <span class=\"o_stars o_five_stars\" id=\"checkId-4\"><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
msgid ""
"Social Status <span class=\"o_stars o_five_stars\" id=\"checkId-4\"><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"
msgstr ""
"สถานะของโซเชียล <span class=\"o_stars o_five_stars\" id=\"checkId-4\"><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid ""
"Social Status <span class=\"o_stars o_five_stars\" id=\"checkId-4\"><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"
msgstr ""
"สถานะของโซเชียล <span class=\"o_stars o_five_stars\" id=\"checkId-4\"><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
msgid ""
"Social Status ​<span class=\"o_stars o_five_stars\" id=\"checkId-4\"><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star-o\"></i></span>"
msgstr ""
"สถานะของโซเชียล ​<span class=\"o_stars o_five_stars\" id=\"checkId-4\"><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star-o\"></i></span>"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_software_specification
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "Software Specification"
msgstr "ข้อมูลจำเพาะของซอฟต์แวร์"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "Some articles have been sent to Trash"
msgstr "บทความบางส่วนได้ถูกส่งไปยังถังขยะแล้ว"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/view/embedded_view.xml:0
msgid "Something went wrong!"
msgstr "มีบางอย่างผิดพลาด!"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
msgid "Sonya"
msgstr "Sonya"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "Source Feedback"
msgstr "ที่มาคำติชม"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
msgid ""
"Speed of Service <span class=\"o_stars o_five_stars\" id=\"checkId-2\"><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star-o\"></i></span>"
msgstr ""
"ความเร็วในการให้บริการ <span class=\"o_stars o_five_stars\" "
"id=\"checkId-2\"><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
msgid ""
"Speed of Service <span class=\"o_stars o_five_stars\" id=\"checkId-2\"><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"
msgstr ""
"ความเร็วในการให้บริการ <span class=\"o_stars o_five_stars\" "
"id=\"checkId-2\"><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
msgid ""
"Speed of Service <span class=\"o_stars o_five_stars\" id=\"checkId-2\"><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"
msgstr ""
"ความเร็วในการให้บริการ <span class=\"o_stars o_five_stars\" "
"id=\"checkId-2\"><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid ""
"Speed of Service ​<span class=\"o_stars o_five_stars\" id=\"checkId-2\"><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"
msgstr ""
"ความเร็วในการให้บริการ ​<span class=\"o_stars o_five_stars\" "
"id=\"checkId-2\"><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i "
"class=\"fa fa-star-o\"></i></span>"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_sprint_calendar
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sprint_calendar
msgid "Sprint Calendar"
msgstr "ปฏิทินสปรินต์"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_stage_view_form
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search_items
msgid "Stage"
msgstr "ขั้นตอน"

#. module: knowledge
#: model:ir.actions.act_window,name:knowledge.knowledge_article_stage_action
#: model:ir.ui.menu,name:knowledge.knowledge_article_stage_menu
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_stage_view_tree
msgid "Stages"
msgstr "สถานะ"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article_stage__parent_id
msgid "Stages are shared among acommon parent and its children articles."
msgstr "ขั้นตอนต่างๆ จะถูกแชร์ระหว่างบทความหลักและบทความย่อย"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Stakeholders Analysis"
msgstr "การวิเคราะห์ผู้มีส่วนได้เสีย"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "Start Date"
msgstr "วันที่เริ่ม"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.js:0
msgid "Start Date Time"
msgstr "วันที่และเวลาเริ่มต้น"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "Start typing"
msgstr "เริ่มพิมพ์"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.xml:0
msgid ""
"Start typing to continue with an empty page or pick an option below to get "
"started."
msgstr ""
"เริ่มพิมพ์เพื่อดำเนินการต่อด้วยหน้าเปล่าหรือเลือกตัวเลือกด้านล่างเพื่อเริ่มต้น"

#. module: knowledge
#: model_terms:digest.tip,tip_description:knowledge.digest_tip_knowledge_2
msgid ""
"Start working together on any Knowledge Article by sharing your article with"
" others."
msgstr "เริ่มทำงานร่วมกันในบทความคลังข้อมูล โดยการแชร์บทความของคุณกับผู้อื่น"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"สถานะตามกิจกรรม\n"
"เกินกำหนด: วันที่ครบกำหนดผ่านไปแล้ว\n"
"วันนี้: วันที่จัดกิจกรรมคือวันนี้\n"
"วางแผน: กิจกรรมในอนาคต"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "Stop Date"
msgstr "วันที่หยุด"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/article_index/article_index.xml:0
msgid "Switch Mode"
msgstr "สลับโหมด"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid ""
"Talk to you soon,<br>\n"
"                        YourName"
msgstr ""
"ไว้คุยกันเร็ว ๆ นี้ <br>\n"
"                        ชื่อคุณ"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_template_view_search
msgid "Template"
msgstr "เทมเพลต"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__template_body
msgid "Template Body"
msgstr "เนื้อหาเทมเพลต"

#. module: knowledge
#: model:ir.actions.act_window,name:knowledge.knowledge_article_template_category_action
#: model:ir.ui.menu,name:knowledge.knowledge_article_template_category_menu
msgid "Template Categories"
msgstr "หมวดหมู่เทมเพลต"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__template_category_id
msgid "Template Category"
msgstr "หมวดหมู่เทมเพลต"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__template_category_sequence
msgid "Template Category Sequence"
msgstr "ลำดับหมวดหมู่เทมเพลต"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__template_description
msgid "Template Description"
msgstr "คำอธิบายเทมเพลต"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_template_view_search
msgid "Template Items"
msgstr "รายการเทมเพลต"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__template_preview
msgid "Template Preview"
msgstr "ตัวอย่างแม่แบบ"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__template_sequence
msgid "Template Sequence"
msgstr "ลำดับเทมเพลต"

#. module: knowledge
#: model:ir.actions.act_window,name:knowledge.knowledge_article_template_stage_action
#: model:ir.ui.menu,name:knowledge.knowledge_article_template_stage_menu
msgid "Template Stages"
msgstr "ขั้นตอนเทมเพลต"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__template_name
msgid "Template Title"
msgstr "ชื่อเทมเพลต"

#. module: knowledge
#: model:ir.ui.menu,name:knowledge.knowledge_article_template_menu
msgid "Templates"
msgstr "เทมเพลต"

#. module: knowledge
#: model:ir.model.constraint,message:knowledge.constraint_knowledge_article_check_template_name_required
msgid "Templates should have a name."
msgstr "เทมเพลตควรมีชื่อ"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "Test Environment"
msgstr "สภาพแวดล้อมการทดสอบ"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid ""
"Thank you to everyone involved in the organization of this edition of the\n"
"                            <font class=\"text-o-color-2\">\n"
"                                <strong>Odoo Run</strong>\n"
"                            </font>!"
msgstr ""
"ขอขอบคุณทุกท่านที่เกี่ยวข้องในการจัดครั้งนี้\n"
"                            <font class=\"text-o-color-2\">\n"
"                                <strong>Odoo Run</strong>\n"
"                            </font>!"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "The 5 Commandments"
msgstr "บัญญัติ 5 ประการ"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid ""
"The Accounting team handles all payments on Fridays afternoon.\n"
"                <br>\n"
"                If 2 weeks have passed and you are still waiting to be paid, get in touch with them."
msgstr ""
"ทีมงานบัญชีจะจัดการการชำระเงินทั้งหมดในช่วงบ่ายวันศุกร์\n"
"                <br>\n"
"                หากผ่านไป 2 สัปดาห์แล้วและคุณยังคงรอการชำระเงินอยู่ โปรดติดต่อพวกเขา"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "The Article you are trying to access has been deleted"
msgstr "บทความที่คุณพยายามเข้าถึงถูกลบแล้ว"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid "The Future Entrepreneurship Fair"
msgstr "มหกรรมผู้ประกอบการแห่งอนาคต"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "The article '%s' needs at least one member with 'Write' access."
msgstr "บทความ '%s' ต้องมีสมาชิกอย่างน้อยหนึ่งคนที่มีสิทธิ์เข้าถึง 'การเขียน'"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/js/knowledge_controller.js:0
msgid ""
"The article you are trying to open has either been removed or is "
"inaccessible."
msgstr "บทความที่คุณพยายามเปิดถูกลบออกหรือไม่สามารถเข้าถึงได้"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"The destination placement of %(article_name)s is ambiguous, you should "
"specify the category."
msgstr "ตำแหน่งปลายทางของ %(article_name)s ไม่ชัดเจน คุณควรระบุหมวดหมู่"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/macros/abstract_macro.js:0
msgid "The operation could not be completed."
msgstr "ไม่สามารถดำเนินการให้เสร็จสิ้นได้"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article_thread__article_anchor_text
msgid ""
"The original highlighted anchor text, giving initial context if that text is"
" modified or removed afterwards."
msgstr ""
"Anchor Text ที่ไฮไลต์แบบเดิม "
"ให้บริบทเริ่มต้นหากข้อความนั้นได้รับการแก้ไขหรือลบออกในภายหลัง"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid ""
"The podcast launches its new technical talks, for all tech-savvy listeners out there! This\n"
"                            new episode of the series features Géry, the mastermind behind OWL, the world fastest JS\n"
"                            framework. 🚀"
msgstr ""
"พอดแคสต์เปิดตัวการพูดคุยทางเทคนิคใหม่ สำหรับผู้ฟังที่เชี่ยวชาญด้านเทคโนโลยีทุกคน!\n"
"                            ตอนใหม่ของซีรีส์นี้นำเสนอ Géry ผู้บงการเบื้องหลัง OWL ซึ่งเป็นเฟรมเวิร์ก JS\n"
"                            ที่เร็วที่สุดในโลก 🚀"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/macros/abstract_macro.js:0
msgid "The record that this macro is targeting could not be found."
msgstr "ไม่พบบันทึกที่มาโครนี้กำหนดเป้าหมายอยู่"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/controllers/main.py:0
msgid "The selected member does not exists or has been already deleted."
msgstr "สมาชิกที่เลือกไม่มีอยู่หรือถูกลบไปแล้ว"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__sequence
msgid ""
"The sequence is computed only among the articles that have the same parent."
msgstr "ลำดับจะคำนวณเฉพาะในบทความที่มีพาเรนต์เดียวกันเท่านั้น"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.js:0
msgid "The start date property is required."
msgstr "ต้องระบุคุณสมบัติวันที่เริ่มต้น"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__root_article_id
msgid ""
"The subject is the title of the highest parent in the article hierarchy."
msgstr "หัวเรื่องคือชื่อเรื่องของระดับบนสุดในลำดับชั้นของบทความ"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid ""
"The tickets to participate to Odoo Experience 23 are now available and they "
"are cheaper if you book them now!"
msgstr ""
"ตั๋วเข้าร่วม Odoo Experience 23 มีจำหน่ายแล้ว และราคาถูกกว่าหากคุณจองตอนนี้!"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/view/embedded_view.xml:0
msgid "The view does not exist or you are not allowed to access to it."
msgstr "ไม่มีมุมมองนี้หรือคุณไม่ได้รับอนุญาตให้เข้าถึงมุมมองดังกล่าว"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar_section.xml:0
msgid "There are no Articles in your Workspace."
msgstr "ไม่มีบทความในเวิร์กสเปซของคุณ"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "Things to handle this week*"
msgstr "สิ่งที่ต้องจัดการในสัปดาห์นี้*"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_form
msgid "This Article is in Trash and will be deleted on the"
msgstr "บทความนี้อยู่ในถังขยะ และจะถูกลบทิ้งใน"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_form
msgid "This article is archived."
msgstr "บทความนี้ถูกเก็บถาวร"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "This article is locked"
msgstr "บทความนี้ถูกล็อค"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_form
msgid "This article is only displayed to its members."
msgstr "บทความนี้จะแสดงเฉพาะสมาชิกเท่านั้น"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid ""
"This year again, Odoo was present at the <em>Tech and Innovation festival for students</em> in\n"
"                            Antwerp, ready to promote our company!<br>Fabien was also there and gave an interview on the main\n"
"                            stage."
msgstr ""
"ในปีนี้อีกครั้ง Odoo ได้เข้าร่วมงานเทศกาล<em>เทคโนโลยีและนวัตกรรมสำหรับนักศึกษา</em> ในเมือง\n"
"                            แอนต์เวิร์ป พร้อมที่จะโปรโมทบริษัทของเรา!<br>Fabien ก็อยู่ที่นั่นด้วยและให้สัมภาษณ์บนเวทีหลัก\n"
"                            "

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/properties_panel/properties_panel.xml:0
msgid ""
"Those fields will be available on all articles that share the same parent."
msgstr "ช่องเหล่านั้นจะมีอยู่ในบทความทั้งหมดที่มีเนื้อหาหลักเดียวกัน"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__is_resolved
msgid "Thread Closed"
msgstr "ปิดกระทู้แล้ว"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_product_catalog
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid "Tiny House Catalog"
msgstr "แคตตาล็อกบ้านแบบเล็ก"

#. module: knowledge
#: model:digest.tip,name:knowledge.digest_tip_knowledge_0
#: model_terms:digest.tip,tip_description:knowledge.digest_tip_knowledge_0
msgid "Tip: A Knowledge well kept"
msgstr ""

#. module: knowledge
#: model:digest.tip,name:knowledge.digest_tip_knowledge_2
#: model_terms:digest.tip,tip_description:knowledge.digest_tip_knowledge_2
msgid "Tip: Be on the same page"
msgstr ""

#. module: knowledge
#: model:digest.tip,name:knowledge.digest_tip_knowledge_1
#: model_terms:digest.tip,tip_description:knowledge.digest_tip_knowledge_1
msgid "Tip: Use Clipboards to easily inject repetitive content"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Tips to close more deals"
msgstr "เคล็ดลับในการปิดการขายเพิ่มเติม"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__name
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_template_category__name
msgid "Title"
msgstr "คำนำหน้าชื่อ"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid "To be sure to stay updated, follow us on"
msgstr "เพื่อให้แน่ใจว่าจะได้รับการอัปเดต ติดตามเราบน"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.xml:0
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Toggle aside menu"
msgstr "สลับเมนูด้านข้าง"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Toggle chatter"
msgstr "สลับการแชท"

#. module: knowledge
#: model:ir.actions.act_window,name:knowledge.knowledge_article_action_trashed
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_tree_items
msgid "Trash"
msgstr "ขยะ"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__to_delete
#: model:ir.ui.menu,name:knowledge.knowledge_article_menu_trashed
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search
msgid "Trashed"
msgstr "ถังขยะ"

#. module: knowledge
#: model:ir.model.constraint,message:knowledge.constraint_knowledge_article_check_trash
msgid "Trashed articles must be archived."
msgstr "บทความที่ทิ้งแล้วจะต้องถูกเก็บถาวร"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "Trying to remove wrong member."
msgstr "กำลังพยายามลบสมาชิกที่ไม่ถูกต้อง"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/embedded_view_link/embedded_view_link_edit_dialog.xml:0
msgid "Type"
msgstr "ประเภท"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "ประเภทกิจกรรมข้อยกเว้นบนบันทึก"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_form
msgid "Unarchive"
msgstr "ยกเลิกการเก็บถาวร"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Unlock"
msgstr "ปลดล็อก"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "Unsupported search operation"
msgstr "การดำเนินการค้นหาที่ไม่รองรับ"

#. module: knowledge
#. odoo-javascript
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
#: code:addons/knowledge/static/src/components/hierarchy/hierarchy.xml:0
#: code:addons/knowledge/static/src/components/sidebar/sidebar_row.xml:0
#: code:addons/knowledge/static/src/editor/html_migrations/migration-1.0.js:0
#: code:addons/knowledge/static/src/webclient/commands/knowledge_providers.js:0
#: code:addons/knowledge/static/src/xml/knowledge_command_palette.xml:0
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_hierarchy
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_kanban_items
msgid "Untitled"
msgstr "ไม่มีชื่อ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/article_index/article_index.xml:0
msgid "Update"
msgstr "อัปเดต"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "Usage"
msgstr "การใช้งาน"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/clipboard/macros_embedded_clipboard.js:0
msgid "Use as %s"
msgstr "ใช้เป็น %s"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/file/macros_file_mixin.xml:0
msgid "Use as Attachment"
msgstr "ใช้เป็นเอกสารแนบ"

#. module: knowledge
#: model_terms:digest.tip,tip_description:knowledge.digest_tip_knowledge_1
msgid "Use the /clipboard command on a Knowledge Article and get going."
msgstr "ใช้คำสั่ง /clipboard บนบทความคลังข้อมูลและเริ่มต้นได้เลย"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "Use the expression \"YourCompanions\" to refer to our community"
msgstr "ใช้สำนวน \"YourCompanions\" เพื่ออ้างถึงคอมมูนิตี้ของเรา"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "Use the logo instead of the word inside sentences"
msgstr "ใช้โลโก้แทนคำในประโยค"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "Use this template whenever you have an announcement to make."
msgstr "ใช้เทมเพลตนี้ทุกครั้งที่คุณต้องประกาศ"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__category
msgid ""
"Used to categorize articles in UI, depending on their main permission "
"definitions."
msgstr "ใช้เพื่อจัดหมวดหมู่บทความใน UI ขึ้นอยู่กับคำจำกัดความของการอนุญาตหลัก"

#. module: knowledge
#: model:ir.model,name:knowledge.model_res_users
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_favorite__user_id
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_favorite_view_search
msgid "User"
msgstr "ผู้ใช้"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__user_favorite_sequence
msgid "User Favorite Sequence"
msgstr "ลำดับรายการโปรดของผู้ใช้"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "User Permission"
msgstr "การอนุญาตของผู้ใช้"

#. module: knowledge
#: model:ir.model.constraint,message:knowledge.constraint_knowledge_article_favorite_unique_article_user
msgid "User already has this article in favorites."
msgstr "ผู้ใช้มีบทความนี้อยู่ในรายการโปรดแล้ว"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__user_permission
msgid "User permission"
msgstr "การอนุญาตของผู้ใช้"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_release_note
msgid "VersionName - ReleaseDate"
msgstr "ชื่อเวอร์ชัน - วันที่วางจำหน่าย"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "Visibility"
msgstr "การมองเห็น"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
msgid "Vittorio"
msgstr "Vittorio"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_shared_todos_water_the_plants
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_shared_todos_water_the_plants
msgid "Water the Plants"
msgstr "รดน้ำต้นไม้"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid "We already cannot wait for the next one🏃"
msgstr "เราแทบรอไม่ไหวแล้ว 🏃"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__website_message_ids
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__website_message_ids
msgid "Website Messages"
msgstr "ข้อความเว็บไซต์"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__website_message_ids
#: model:ir.model.fields,help:knowledge.field_knowledge_article_thread__website_message_ids
msgid "Website communication history"
msgstr "ประวัติการสื่อสารของเว็บไซต์"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/res_users.py:0
msgid "Welcome %s"
msgstr "ยินดีต้อนรับ %s"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid ""
"Welcome to the last edition of the MyCompany Newsletter!<br>\n"
"                We are very excited to share with you the hottest updates and news! 🤩"
msgstr ""
"ยินดีต้อนรับสู่จดหมายข่าว MyCompany ฉบับล่าสุด!<br>\n"
"                เรารู้สึกตื่นเต้นมากที่จะแชร์ข้อมูลอัปเดตและข่าวสารที่ร้อนแรงที่สุดให้กับคุณ! 🤩"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "What do I have to do if I cannot work?"
msgstr "ฉันต้องทำอย่างไรหากฉันไม่สามารถทำงานได้?"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "What do you want to manage?"
msgstr "คุณต้องการจัดการอะไร?"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/prompt_embedded_view_name_dialog/prompt_embedded_view_name_dialog.xml:0
msgid "What items do you want to manage?"
msgstr "คุณต้องการจัดการรายการอะไรบ้าง?"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__is_locked
msgid ""
"When locked, users cannot write on the body or change the title, even if "
"they have write access on the article."
msgstr ""
"เมื่อล็อค ผู้ใช้จะไม่สามารถเขียนบนเนื้อหาหรือเปลี่ยนชื่อเรื่องได้ "
"แม้ว่าพวกเขาจะมีสิทธิ์ในการเขียนบทความก็ตาม"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__to_delete
msgid ""
"When sent to trash, articles are flagged to be deleted\n"
"                days after last edit. knowledge_article_trash_limit_days config\n"
"                parameter can be used to modify the number of days. \n"
"                (default is 30)"
msgstr ""
"เมื่อส่งไปที่ถังขยะ บทความจะถูกตั้งค่าสถานะให้ลบไปหลายวัน\n"
"                จากการแก้ไขครั้งล่าสุด พารามิเตอร์การกำหนดค่า knowledge_article_trash_limit_days\n"
"                สามารถใช้เพื่อแก้ไขจำนวนวันได้ \n"
"                (ค่าเริ่มต้นคือ 30)"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__full_width
msgid ""
"When set, the article body will take the full width available on the article"
" page. Otherwise, the body will have large horizontal margins."
msgstr ""
"เมื่อตั้งค่าแล้ว เนื้อหาของบทความจะใช้ความกว้างเต็มที่มีอยู่ในหน้าบทความ "
"ไม่เช่นนั้น เนื้อหาจะมีระยะขอบแนวนอนขนาดใหญ่"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid ""
"Whether your heart leans towards the rustic charm of a wooden hut or the "
"intricate beauty of Victorian steelworks, we have you covered. Desiring a "
"specific option? Rest assured, we are here to fulfill your wishes! Just get "
"in touch with our architects and we will make sure to provide any specific "
"choice you desire."
msgstr ""
"ไม่ว่าใจของคุณจะเอนเอียงไปทางเสน่ห์แบบชนบทของกระท่อมไม้หรือความงามอันประณีตของโรงงานเหล็กสไตล์วิคตอเรียน"
" เราก็พร้อมครอบคลุมให้กับคุณ ต้องการตัวเลือกเฉพาะหรือไม่? "
"มั่นใจได้ว่าเราอยู่ที่นี่เพื่อเติมเต็มความต้องการของคุณ! "
"เพียงติดต่อกับสถาปนิกของเรา แล้วเราจะจัดเตรียมทางเลือกเฉพาะที่คุณต้องการ"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_example_branding
msgid "Which color should we pick for the logo?"
msgstr "เราควรเลือกสีอะไรสำหรับโลโก้?"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_example_branding
msgid "Which size should the rollups be?"
msgstr "Rollups ควรมีขนาดเท่าใด"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_example_branding
msgid "Which text should we print on the billboards?"
msgstr "เราควรพิมพ์ข้อความอะไรบนป้ายโฆษณา?"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid ""
"While tiny houses are inherently small, it's important to prioritize comfort"
" when sharing such limited space. To ensure everyone's happiness, we "
"recommend allowing at least 9m² per person. If you plan to live with "
"multiple roommates, our Serenità model is highly recommended, providing "
"ample space for each individual to enjoy<br>"
msgstr ""
"แม้ว่าบ้านหลังเล็กจะเล็กอยู่แล้ว "
"แต่สิ่งสำคัญคือต้องให้ความสำคัญกับความสะดวกสบายเมื่อแชร์พื้นที่ที่จำกัดเช่นนี้"
" เพื่อให้ทุกคนมีความสุข เราขอแนะนำให้จัดสรรพื้นที่อย่างน้อย 9 ตร.ม. ต่อคน "
"หากคุณวางแผนที่จะอาศัยอยู่ร่วมกับเพื่อนร่วมห้องหลายคน ขอแนะนำรุ่น Serenità "
"ของเรา โดยให้พื้นที่กว้างขวางให้แต่ละคนได้เพลิดเพลิน<br>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid ""
"While tiny houses do offer a more cost-effective alternative to traditional "
"houses, at MyCompany, we prioritize durability, which comes with a price. "
"However, if you're on a tight budget, we have the perfect solution: our "
"Cielo model.<br>It provides everything you need while allowing you to "
"save.<br>"
msgstr ""
"แม้ว่าบ้านหลังเล็กๆ จะเป็นทางเลือกที่คุ้มค่ากว่าบ้านแบบดั้งเดิม แต่ที่ "
"MyCompany เราให้ความสำคัญกับความทนทานซึ่งมาพร้อมกับราคา อย่างไรก็ตาม "
"หากคุณมีงบจำกัด เรามีทางออกที่สมบูรณ์แบบ: โมเดล Cielo "
"ของเรา<br>มีทุกสิ่งที่คุณต้องการในขณะที่ช่วยให้คุณประหยัดมากขึ้นได้<br>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "Who can drive my car?"
msgstr "ใครสามารถขับรถของฉันได้บ้าง?"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "Who do I need to address if I need office supplies?"
msgstr "ฉันต้องติดต่อใครหากฉันต้องการใช้อุปกรณ์ของสำนักงาน?"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid "Who has access to what? 🕵️"
msgstr "ใครเข้าถึงอะไรได้บ้าง? 🕵️"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "Why has my expense not been reimbursed? It has been accepted."
msgstr "ทำไมค่าใช้จ่ายของฉันจึงไม่ได้รับการคืนเงิน? แต่ได้รับการยอมรับแล้ว"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/move_article_dialog/move_article_dialog.js:0
#: code:addons/knowledge/static/src/components/sidebar/sidebar_section.xml:0
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article__category__workspace
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search
msgid "Workspace"
msgstr "พื้นที่ทำงาน"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "Write"
msgstr "เขียน"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.js:0
msgid "Write an article about"
msgstr "เขียนบทความเกี่ยวกับ"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid ""
"Write it without any article(<em><s>the</s> YourCompany, <s>a</s> "
"YourCompany, ...</em>)"
msgstr ""
"เขียนโดยไม่มีบทความ (<em><s></s>YourCompany, <s></s>YourCompany, ...</em>)"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_shared_todos_write_the_next_newsletter
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_shared_todos_write_the_next_newsletter
msgid "Write the next Newsletter"
msgstr "เขียนจดหมายข่าวฉบับถัดไป"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "You"
msgstr "คุณ"

#. module: knowledge
#: model:ir.model.constraint,message:knowledge.constraint_knowledge_article_member_unique_article_partner
msgid "You already added this partner on this article."
msgstr "คุณได้เพิ่มพาร์ทเนอร์รายนี้ในบทความนี้แล้ว"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "You are not allowed to create a new template."
msgstr "คุณไม่ได้รับอนุญาตให้สร้างเทมเพลตใหม่"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/view/readonly_embedded_view.js:0
msgid "You are not allowed to delete a favorite filter in this article."
msgstr "คุณไม่มีสิทธิ์ลบตัวกรองที่ชื่นชอบในบทความนี้"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "You are not allowed to delete a template."
msgstr "คุณไม่ได้รับอนุญาตให้ลบเทมเพลต"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "You are not allowed to make '%(article_name)s' private."
msgstr "คุณไม่ได้รับอนุญาตให้ทำให้ '%(article_name)s' เป็นส่วนตัว"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"You are not allowed to move '%(article_name)s' under '%(parent_name)s'."
msgstr "คุณไม่ได้รับอนุญาตให้ย้าย '%(article_name)s' ภายใต้ '%(parent_name)s'"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "You are not allowed to move '%(article_name)s'."
msgstr "คุณไม่ได้รับอนุญาตให้ย้าย '%(article_name)s'."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/view/readonly_embedded_view.js:0
msgid "You are not allowed to save a favorite filter in this article."
msgstr "คุณไม่ได้รับอนุญาตให้บันทึกตัวกรองที่ชื่นชอบในบทความนี้"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "You are not allowed to update a template."
msgstr "คุณไม่ได้รับอนุญาตให้อัปเดตเทมเพลต"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "You are not allowed to update the type of a article or a template."
msgstr "คุณไม่ได้รับอนุญาตให้อัปเดตประเภทของบทความหรือเทมเพลต"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "You can't leave an article for which you are the last writer"
msgstr "คุณไม่สามารถออกจากบทความที่คุณเป็นผู้เขียนคนสุดท้ายได้"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"You can't move %(article_name)s under %(item_name)s, as %(item_name)s is an "
"Article Item. Convert %(item_name)s into an Article first."
msgstr ""
"คุณไม่สามารถย้าย %(article_name)s ไปไว้ใต้ %(item_name)s ได้ เนื่องจาก "
"%(item_name)s เป็นรายการบทความ แปลง %(item_name)s เป็นบทความก่อน"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "You can't remove the last writer of the article"
msgstr "คุณไม่สามารถลบผู้เขียนบทความคนสุดท้ายได้"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "You cannot add or remove this article to your favorites"
msgstr "You cannot add or remove this article to your favorites"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/controllers/main.py:0
msgid "You cannot change the internal permission of this article."
msgstr "คุณไม่สามารถเปลี่ยนการอนุญาตภายในของบทความนี้ได้"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/controllers/main.py:0
msgid "You cannot change the permission of this member."
msgstr "คุณไม่สามารถเปลี่ยนการอนุญาตของสมาชิกรายนี้ได้"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_cover.py:0
msgid "You cannot create a new Knowledge Cover from here."
msgstr "คุณไม่สามารถสร้างปกคลังข้อมูลใหม่ได้จากที่นี่"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "You cannot create an article under articles on which you cannot write"
msgstr "คุณไม่สามารถสร้างบทความภายใต้บทความที่คุณไม่สามารถเขียนได้"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"You cannot move an article under %(parent_name)s as you cannot write on it"
msgstr ""
"คุณไม่สามารถย้ายบทความภายใต้ %(parent_name)s ได้ "
"เนื่องจากคุณไม่สามารถเขียนบทความได้"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/hierarchy/hierarchy.xml:0
msgid "You do not have access to this article"
msgstr "คุณไม่มีสิทธิ์เข้าถึงบทความนี้"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar_section.xml:0
msgid "You do not have any private Article."
msgstr "คุณไม่มีบทความส่วนตัว"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "You have to be editor on %(article_name)s to add members."
msgstr "คุณต้องเป็นผู้แก้ไข %(article_name)s จึงจะเพิ่มสมาชิกได้"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"You have to be editor on %(article_name)s to change its internal permission."
msgstr ""
"คุณต้องเป็นผู้แก้ไข %(article_name)s จึงจะสามารถเปลี่ยนแปลงการอนุญาตภายในได้"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"You have to be editor on %(article_name)s to modify members permissions."
msgstr "คุณต้องเป็นผู้แก้ไข %(article_name)s จึงจะแก้ไขการอนุญาตของสมาชิกได้"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"You have to be editor on %(article_name)s to remove or exclude member "
"%(member_name)s."
msgstr ""
"คุณต้องเป็นผู้แก้ไข %(article_name)s จึงจะลบหรือยกเว้นสมาชิก %(member_name)s"
" ได้"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "You have to be editor on %(article_name)s to restore it."
msgstr "คุณต้องเป็นผู้แก้ไข %(article_name)s จึงจะกู้คืนได้"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid ""
"You may have heard those a million times and yet you are not entirely sure of what it means.<br>\n"
"                Here is a quick recap for you to shine during the next meeting."
msgstr ""
"คุณอาจเคยได้ยินคำเหล่านั้นมานับล้านครั้งแล้ว แต่คุณก็ยังไม่แน่ใจนักว่ามันหมายถึงอะไร<br>\n"
"                นี่เป็นบทสรุปสั้นๆ เพื่อให้คุณเตรียมพร้อมในการประชุมครั้งถัดไป"

#. module: knowledge
#. odoo-javascript
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
#: code:addons/knowledge/static/src/components/sidebar/sidebar.js:0
msgid "You need at least 2 members for the Article to be shared."
msgstr "คุณต้องมีสมาชิกอย่างน้อย 2 คนจึงจะแชร์บทความได้"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "You need to have access to this article in order to join its members."
msgstr "คุณต้องมีสิทธิ์เข้าถึงบทความนี้จึงจะเข้าร่วมเป็นสมาชิกได้"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"You need to have access to this article's root in order to join its members."
msgstr "คุณต้องมีสิทธิ์เข้าถึงฐานของบทความนี้จึงจะเข้าร่วมเป็นสมาชิกได้"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "Your Access: %s"
msgstr "การเข้าถึงของคุณ: %s"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid ""
"Your car can be driven by <strong>you, your spouse and by any person living "
"under the same roof</strong> as long as they have a valid permit."
msgstr ""
"รถของคุณสามารถขับได้โดย คุณ คู่สมรสของคุณ และโดยบุคคลใดๆ "
"ที่อาศัยอยู่ในบ้านเดียวกัน ตราบใดที่พวกเขามีใบอนุญาตที่ถูกต้อง"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "YourCompany is proud to announce that..."
msgstr "YourCompany ภูมิใจที่จะแจ้งให้ทราบว่า..."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid ""
"YourCompany is very proud of its brand image.\n"
"                <br>\n"
"                When representing the brand, we thus ask you to be very cautious in how you refer to the company."
msgstr ""
"YourCompany มีความภาคภูมิใจในภาพลักษณ์ของแบรนด์\n"
"                <br>\n"
"                เมื่อเป็นตัวแทนของแบรนด์ เราขอให้คุณใช้ความระมัดระวังอย่างมากในการอ้างถึงบริษัท"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comments_panel/comments_panel.xml:0
msgid "all"
msgstr "ทั้งหมด"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_trash_notification
msgid "and the following child article(s) have"
msgstr "และบทความย่อยต่อไปนี้มี"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/webclient/commands/knowledge_providers.js:0
msgid "articles"
msgstr "บทความ"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_trash_notification
msgid "been sent to Trash.<br/><br/>"
msgstr "ถูกส่งไปยังถังขยะ <br/><br/>"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comments_panel/comments_panel.xml:0
msgid "button to add comments"
msgstr "ปุ่มเพิ่มความคิดเห็น"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "contact our FleetOfficer"
msgstr "ติดต่อเจ้าหน้าที่ขนส่งของเรา"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover.xml:0
msgid "cover"
msgstr "ปก"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/prompt_embedded_view_name_dialog/prompt_embedded_view_name_dialog.js:0
msgid "e.g. Buildings"
msgstr "เช่น อาคาร"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "e.g. Meetings"
msgstr "เช่น การประชุม"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_stage_view_form
msgid "e.g. Ongoing"
msgstr "เช่น กำลังดำเนินการอยู่"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/prompt_embedded_view_name_dialog/prompt_embedded_view_name_dialog.js:0
msgid "e.g. Todos"
msgstr "เช่น สิ่งที่ต้องทำ"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_trash_notification
msgid "has"
msgstr "มี"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_trash_notification
msgid "have"
msgstr "มี"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_mail_invite
msgid "invited you to"
msgstr "เชิญคุณไป"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_mail_invite
msgid "invited you to access an article.<br/>"
msgstr "เชิญคุณเข้าสู่บทความ <br/>"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
#: code:addons/knowledge/static/src/components/sidebar/sidebar.xml:0
#: code:addons/knowledge/static/src/components/sidebar/sidebar_row.xml:0
#: code:addons/knowledge/static/src/components/with_lazy_loading/with_lazy_loading.xml:0
msgid "loader"
msgstr "ตัวโหลด"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_mail_notification_layout
msgid "mentioned you in a comment:"
msgstr "กล่าวถึงคุณในความคิดเห็น:"

#. module: knowledge
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article__user_permission__none
msgid "none"
msgstr "ไม่มี"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid "or"
msgstr "หรือ"

#. module: knowledge
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article__user_permission__read
msgid "read"
msgstr "อ่าน"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comments_panel/comments_panel.xml:0
msgid "resolved"
msgstr "ได้รับการแก้ไขแล้ว"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.xml:0
msgid "search"
msgstr "ค้นหา"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_mail_invite
msgid "the article"
msgstr "บทความ"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_form
msgid "to unleash the power of Knowledge !"
msgstr "เพื่อปลดปล่อยพลังแห่งความรู้!"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comments_panel/comments_panel.xml:0
msgid "unresolved"
msgstr "ยังไม่ได้รับการแก้ไข"

#. module: knowledge
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article__user_permission__write
msgid "write"
msgstr "เขียน"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid ""
"​<span class=\"o_stars o_five_stars\" id=\"checkId-1\"><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i></span>"
msgstr ""
"​<span class=\"o_stars o_five_stars\" id=\"checkId-1\"><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid ""
"​<span class=\"o_stars o_five_stars\" id=\"checkId-2\"><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i></span>"
msgstr ""
"​<span class=\"o_stars o_five_stars\" id=\"checkId-2\"><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid ""
"​<span class=\"o_stars o_five_stars\" id=\"checkId-3\"><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i></span>"
msgstr ""
"​<span class=\"o_stars o_five_stars\" id=\"checkId-3\"><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_campaign_brief
msgid "⌚ Elevator Pitch"
msgstr "⌚ ลานลิฟท์"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_example_branding
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "☝🏼 Please prepare the following before the meeting:"
msgstr "☝🏼 โปรดเตรียมสิ่งต่อไปนี้ก่อนการประชุม:"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "⚙ Technical Specifications"
msgstr "⚙ ข้อมูลจำเพาะทางเทคนิค"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "❓ Open Questions"
msgstr "❓ เปิดคำถาม"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "⭐ Release Notes"
msgstr "⭐ บันทึกประจำรุ่น"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_campaign_brief
msgid "🎯 Target Audience"
msgstr "🎯 กลุ่มเป้าหมาย"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid "🏘️ House Model - Incanto"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid "🏠 House Model - Cielo"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid "🏢 House Model - Serenità"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_campaign_brief
msgid "📊 KPIs"
msgstr "📊 KPIs"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "📝 Purpose"
msgstr "📝 วัตถุประสงค์"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_campaign_brief
msgid "📣 Message"
msgstr "📣 ข้อความ"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "🔍 Review Checklist"
msgstr "🔍 ทบทวนรายการตรวจสอบ"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "🔗 Links"
msgstr "🔗 ลิงค์"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "🗓 WEEKLY AGENDA"
msgstr "🗓 วาระประจำสัปดาห์"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_example_branding
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "🗣 Meeting Agenda"
msgstr "🗣 กำหนดการประชุม"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_example_branding
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "🙌🏼 Decisions Taken"
msgstr "🙌🏼 การตัดสินใจ"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_campaign_brief
msgid "🚀 Objective"
msgstr "🚀 เป้าหาย"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid "🛕 House Model - Dolcezza"
msgstr ""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "🧠 Functional Specifications"
msgstr "🧠 ข้อมูลจำเพาะด้านการทำงาน"
