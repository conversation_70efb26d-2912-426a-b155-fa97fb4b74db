# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* knowledge
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 09:12+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "\" (Selection)"
msgstr "\" (<PERSON><PERSON><PERSON>)"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "\" (date and time)"
msgstr "\" (ngày và giờ)"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "\" (date)"
msgstr "\" (ngày)"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"\"%(article_item_name)s\" is an Article Item from \"%(article_name)s\" and "
"cannot be restored on its own. Contact the owner of \"%(article_name)s\" to "
"have it restored instead."
msgstr ""
"\"%(article_item_name)s\" là một Hạng mục bài viết từ \"%(article_name)s\" "
"và không thể tự khôi phục được. Hãy liên hệ với chủ sở hữu của "
"\"%(article_name)s\" để khôi phục nó."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"\"%(article_name)s\" is a template and can not be a child of an article "
"(\"%(parent_article_name)s\")."
msgstr ""
"\"%(article_name)s\" là mẫu và không thể là phần phụ của một bài viết "
"(\"%(parent_article_name)s\")."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"\"%(article_name)s\" is an article and can not be a child of a template "
"(\"%(parent_article_name)s\").\""
msgstr ""
"\"%(article_name)s\" là bài viết và không thể là phần phụ của một mẫu "
"(\"%(parent_article_name)s\").\""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "\"About Us\" Template"
msgstr "Mẫu \"Giới thiệu\""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "# Employees:"
msgstr "# nhân viên:"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__favorite_count
msgid "#Is Favorite"
msgstr "#Là yêu thích"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "%(article_name)s (copy)"
msgstr "%(article_name)s (bản sao)"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "%s has been sent to Trash"
msgstr "%s đã được đưa vào Thùng rác"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_mail_invite
msgid ".<br/>"
msgstr ".<br/>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid ""
"250 Executive Park Blvd, Suite 3400 94134\n"
"                        <br>\n"
"                        San Francisco California (US)\n"
"                        <br>\n"
"                        United States\n"
"                        <br>\n"
"                        <EMAIL>"
msgstr ""
"250 Executive Park Blvd, Suite 3400 94134\n"
"                        <br>\n"
"                        San Francisco California (HK)\n"
"                        <br>\n"
"                        Hoa Kỳ\n"
"                        <br>\n"
"                        <EMAIL>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_example_branding
msgid "85x200 or 100x200, depending on the content"
msgstr "85x200 hoặc 100x200, tuỳ vào nội dung"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid ""
"<br>\n"
"                            Subscribe here to make sure you will not miss an episode!"
msgstr ""
"<br>\n"
"                            Đăng ký tại đây để đảm bảo bạn sẽ không bỏ lỡ tập nào!"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_campaign_brief
msgid ""
"<em>\n"
"                    <font class=\"text-600\">\n"
"                        Describe your campaign in just a few words.\n"
"                    </font>\n"
"                </em>"
msgstr ""
"<em>\n"
"                    <font class=\"text-600\">\n"
"                        Mô tả ngắn gọn chiến dịch của bạn.\n"
"                    </font>\n"
"                </em>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_campaign_brief
msgid ""
"<em>\n"
"                    <font class=\"text-600\">\n"
"                        How will your measure progress?\n"
"                    </font>\n"
"                </em>"
msgstr ""
"<em>\n"
"                    <font class=\"text-600\">\n"
"                        Bạn đo lường tiến trình như thế nào?\n"
"                    </font>\n"
"                </em>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_campaign_brief
msgid ""
"<em>\n"
"                    <font class=\"text-600\">\n"
"                        What are you trying to accomplish with this campaign?\n"
"                    </font>\n"
"                </em>"
msgstr ""
"<em>\n"
"                    <font class=\"text-600\">\n"
"                        Bạn đang cố gắng đạt được điều gì với chiến dịch này?\n"
"                    </font>\n"
"                </em>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_campaign_brief
msgid ""
"<em>\n"
"                    <font class=\"text-600\">\n"
"                        What are you trying to convince them of?\n"
"                    </font>\n"
"                </em>"
msgstr ""
"<em>\n"
"                    <font class=\"text-600\">\n"
"                        Bạn đang cố gắng thuyết phục họ về điều gì?\n"
"                    </font>\n"
"                </em>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_campaign_brief
msgid ""
"<em>\n"
"                    <font class=\"text-600\">\n"
"                        Who are you trying to reach?\n"
"                    </font>\n"
"                </em>"
msgstr ""
"<em>\n"
"                    <font class=\"text-600\">\n"
"                        Bạn đang cố gắng tiếp cận đối tượng nào?\n"
"                    </font>\n"
"                </em>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid ""
"<em>\n"
"                    <font class=\"text-600\">Ask a Senior Engineer to fill this part before launching the task.</font>\n"
"                </em>"
msgstr ""
"<em>\n"
"                    <font class=\"text-600\">Kỹ sư cấp cao phải điền phần này trước khi bắt đầu nhiệm vụ.</font>\n"
"                </em>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid ""
"<em>\n"
"                    <font class=\"text-600\">Ask the developer to go through this checklist before asking for a review.</font>\n"
"                </em>"
msgstr ""
"<em>\n"
"                    <font class=\"text-600\">Nhà phát triển phải xem qua danh sách này trước khi yêu cầu kiểm duyệt.</font>\n"
"                </em>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid ""
"<em>\n"
"                    <font class=\"text-600\">Explain in a few words the problem that this change is going to solve.</font>\n"
"                </em>"
msgstr ""
"<em>\n"
"                    <font class=\"text-600\">Giải thích ngắn gọn về vấn đề mà thay đổi này sẽ giải quyết.</font>\n"
"                </em>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid ""
"<em>\n"
"                    <font class=\"text-600\">Explain in a single sentence what has been changed.</font>\n"
"                </em>"
msgstr ""
"<em>\n"
"                    <font class=\"text-600\">Giải thích về các thay đổi trong một câu duy nhất.</font>\n"
"                </em>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid ""
"<em>\n"
"                    <font class=\"text-600\">Explain the consequences of this issue.</font>\n"
"                </em>"
msgstr ""
"<em>\n"
"                    <font class=\"text-600\">Giải thích hậu quả của vấn đề này.</font>\n"
"                </em>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid ""
"<em>\n"
"                    <font class=\"text-600\">Explain what went wrong in just a few words.</font>\n"
"                </em>"
msgstr ""
"<em>\n"
"                    <font class=\"text-600\">Giải thích ngắn gọn những điều đã xảy ra.</font>\n"
"                </em>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid ""
"<em>\n"
"                    <font class=\"text-600\">How did it get to happen?</font>\n"
"                </em>"
msgstr ""
"<em>\n"
"                    <font class=\"text-600\">Điều này xảy ra như thế nào?</font>\n"
"                </em>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid ""
"<em>\n"
"                    <font class=\"text-600\">How was it solved?</font>\n"
"                </em>"
msgstr ""
"<em>\n"
"                    <font class=\"text-600\">Nó đã được xử lý ra sao?</font>\n"
"                </em>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid ""
"<em>\n"
"                    <font class=\"text-600\">Lay here any remaining question or doubt.</font>\n"
"                </em>"
msgstr ""
"<em>\n"
"                    <font class=\"text-600\">Ghi ra bất kỳ câu hỏi hoặc thắc mắc chưa được giải đáp.</font>\n"
"                </em>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid ""
"<em>\n"
"                    <font class=\"text-600\">List here all the changes to implement.</font>\n"
"                </em>"
msgstr ""
"<em>\n"
"                    <font class=\"text-600\">Liệt kê tất cả những thay đổi cần thực hiện.</font>\n"
"                </em>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid ""
"<em>\n"
"                    <font class=\"text-600\">List here all the material and documentation related to the task.</font>\n"
"                </em>"
msgstr ""
"<em>\n"
"                    <font class=\"text-600\">Liệt kê tất cả các tài nguyên và tài liệu liên quan đến nhiệm vụ.</font>\n"
"                </em>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<em>\n"
"                    <font class=\"text-600\">List here all the text you could not do this week. These shall be postponed in the next weekly schedule.</font>\n"
"                </em>"
msgstr ""
"<em>\n"
"                    <font class=\"text-600\">Liệt kê tất cả nhiệm vụ bạn không thể làm trong tuần này. Chúng sẽ được hoãn sang thời gian biểu hàng tuần tiếp theo.</font>\n"
"                </em>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid ""
"<font class=\"text-400\">\n"
"                            <em>How to best reach this customer type.</em>\n"
"                        </font>"
msgstr ""
"<font class=\"text-400\">\n"
"                            <em>Cách tốt nhất để tiếp cận phân khúc khách hàng này.</em>\n"
"                        </font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
msgid ""
"<font class=\"text-400\">\n"
"                    <em>How to best reach this customer type.<br></em>\n"
"                </font>\n"
"                Abigail works a lot and never watches TV. Better reach her on her phone or on her to work."
msgstr ""
"<font class=\"text-400\">\n"
"                    <em>Cách tốt nhất để tiếp cận phân khúc khách hàng này.<br></em>\n"
"                </font>\n"
"                Abigail làm việc rất bận rộn và không bao giờ xem TV. Tốt hơn nên liên hệ với cô ấy qua điện thoại hoặc khi cô ấy đi làm."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
msgid ""
"<font class=\"text-400\">\n"
"                    <em>How to best reach this customer type.<br></em>\n"
"                </font>\n"
"                As a classic Gen Z member, Vittorio never watches TV and never listens to the radio. For him to see our message, we need to get to his Instagram feed."
msgstr ""
"<font class=\"text-400\">\n"
"                    <em>Cách tốt nhất để tiếp cận phân khúc khách hàng này.<br></em>\n"
"                </font>\n"
"                Là một thành viên Gen Z chính hiệu, Vittorio không bao giờ xem TV hay nghe radio. Để anh ấy xem được thông điệp của chúng ta, chúng ta cần hiện diện trên trang chủ Instagram của anh ấy."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
msgid ""
"<font class=\"text-400\">\n"
"                    <em>How to best reach this customer type.<br></em>\n"
"                </font>\n"
"                As an avid music listener, the best way to reach Sonya is through the radio since hers is always on."
msgstr ""
"<font class=\"text-400\">\n"
"                    <em>Cách tốt nhất để tiếp cận phân khúc khách hàng này.<br></em>\n"
"                </font>\n"
"                Là một người đam mê nghe nhạc, cách tốt nhất để tiếp cận Sonya là qua radio vì radio của cô ấy luôn bật."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
msgid ""
"<font class=\"text-400\">\n"
"                    <em>How to best reach this customer type.<br></em>\n"
"                </font>\n"
"                Julius follows politics very tightly, and can best be reached with TV ads."
msgstr ""
"<font class=\"text-400\">\n"
"                    <em>Cách tốt nhất để tiếp cận phân khúc khách hàng này.<br></em>\n"
"                </font>\n"
"                Julius theo dõi sát sao các vấn đề chính trị, và cách tiếp cận tốt nhất là bằng quảng cáo truyền hình."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "<font class=\"text-400\">To Read 1</font>"
msgstr "<font class=\"text-400\">Cần đọc 1</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "<font class=\"text-400\">To Read 2</font>"
msgstr "<font class=\"text-400\">Cần đọc 2</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "<font class=\"text-600\">Action A</font>"
msgstr "<font class=\"text-600\">Tác vụ A</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "<font class=\"text-600\">Action B</font>"
msgstr "<font class=\"text-600\">Tác vụ B</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "<font class=\"text-600\">Action C</font>"
msgstr "<font class=\"text-600\">Tác vụ C</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<font class=\"text-600\">Backlog 1</font>"
msgstr "<font class=\"text-600\">Việc tồn đọng 1</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<font class=\"text-600\">Backlog 2</font>"
msgstr "<font class=\"text-600\">Việc tồn đọng 2</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<font class=\"text-600\">Backlog 3</font>"
msgstr "<font class=\"text-600\">Việc tồn đọng 3</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">Blue/Green/Red/Yellow</font>"
msgstr "<font class=\"text-600\">Xanh lam/Xanh lục/Đỏ/Vàng</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "<font class=\"text-600\">Change 1</font>"
msgstr "<font class=\"text-600\">Thay đổi 1</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "<font class=\"text-600\">Change 2</font>"
msgstr "<font class=\"text-600\">Thay đổi 2</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "<font class=\"text-600\">Change 3</font>"
msgstr "<font class=\"text-600\">Thay đổi 3</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">Color</font>"
msgstr "<font class=\"text-600\">Màu sắc</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">Competitor 1</font>"
msgstr "<font class=\"text-600\">Đối thủ 1</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">Competitor 2</font>"
msgstr "<font class=\"text-600\">Đối thủ 2</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">Competitor 3</font>"
msgstr "<font class=\"text-600\">Đối thủ 3</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_release_note
msgid ""
"<font class=\"text-600\">Detailed Explanation of the feature, with "
"screenshots or a GIF</font>"
msgstr ""
"<font class=\"text-600\">Giải thích chi tiết về tính năng, kèm theo ảnh chụp"
" màn hình hoặc ảnh GIF</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">Email</font>"
msgstr "<font class=\"text-600\">Email</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_release_note
msgid "<font class=\"text-600\">Fixed a bug where...</font>"
msgstr "<font class=\"text-600\">Đã sửa lỗi ở...</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_release_note
msgid "<font class=\"text-600\">From now on, ...</font>"
msgstr "<font class=\"text-600\">Từ giờ trở đi, ...</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid ""
"<font class=\"text-600\">How do stakeholders interact regarding "
"offers?</font>"
msgstr ""
"<font class=\"text-600\">Các bên liên quan tương tác với đề xuất như thế "
"nào?</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">How do they compare and evaluate offers?</font>"
msgstr ""
"<font class=\"text-600\">Họ so sánh và đánh giá đề xuất như thế nào?</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid ""
"<font class=\"text-600\">I made sure any visual change is responsive</font>"
msgstr ""
"<font class=\"text-600\">Tôi chắc chắn mọi thay đổi trực quan đều phản "
"hồi</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid ""
"<font class=\"text-600\">I made sure it did not introduce any obvious "
"regression</font>"
msgstr ""
"<font class=\"text-600\">Tôi chắc chắn thay đổi không gây ra bất kỳ hồi quy "
"rõ ràng nào</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid ""
"<font class=\"text-600\">I made sure it did not introduce any security "
"flaw</font>"
msgstr ""
"<font class=\"text-600\">Tôi chắc chắn thay đổi không gây ra bất kỳ lỗ hổng "
"bảo mật nào</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">Job Title 1</font>"
msgstr "<font class=\"text-600\">Chức danh 1</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">Job Title 2</font>"
msgstr "<font class=\"text-600\">Chức danh 2</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">Job Title</font>"
msgstr "<font class=\"text-600\">Chức danh</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "<font class=\"text-600\">Lesson A</font>"
msgstr "<font class=\"text-600\">Bài học A</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "<font class=\"text-600\">Lesson B</font>"
msgstr "<font class=\"text-600\">Bài học B</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "<font class=\"text-600\">Lesson C</font>"
msgstr "<font class=\"text-600\">Bài học C</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">Name 1</font>"
msgstr "<font class=\"text-600\">Tên 1</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">Name 2</font>"
msgstr "<font class=\"text-600\">Tên 2</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">Name</font>"
msgstr "<font class=\"text-600\">Tên</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_release_note
msgid "<font class=\"text-600\">New Feature 2</font>"
msgstr "<font class=\"text-600\">Tính năng mới 2</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">Phone</font>"
msgstr "<font class=\"text-600\">Điện thoại</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<font class=\"text-600\">Priority A</font>"
msgstr "<font class=\"text-600\">Ưu tiên A</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<font class=\"text-600\">Priority B</font>"
msgstr "<font class=\"text-600\">Ưu tiên B</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<font class=\"text-600\">Priority C</font>"
msgstr "<font class=\"text-600\">Ưu tiên C</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<font class=\"text-600\">Reminder 1</font>"
msgstr "<font class=\"text-600\">Nhắc nhở 1</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<font class=\"text-600\">Reminder 2</font>"
msgstr "<font class=\"text-600\">Nhắc nhở 2</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<font class=\"text-600\">Reminder 3</font>"
msgstr "<font class=\"text-600\">Nhắc nhở 3</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<font class=\"text-600\">Task A</font>"
msgstr "<font class=\"text-600\">Nhiệm vụ A</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<font class=\"text-600\">Task B</font>"
msgstr "<font class=\"text-600\">Nhiệm vụ B</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<font class=\"text-600\">Task C</font>"
msgstr "<font class=\"text-600\">Nhiệm vụ C</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<font class=\"text-600\">Task D</font>"
msgstr "<font class=\"text-600\">Nhiệm vụ D</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">What is their buyer's journey?</font>"
msgstr "<font class=\"text-600\">Lộ trình người mua của họ là gì?</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">What is their key decision criteria?</font>"
msgstr "<font class=\"text-600\">Tiêu chí quyết định chính của họ là gì?</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid ""
"<font class=\"text-600\">What is your action plan to move forward with this account?</font><br>\n"
"                <font class=\"text-600\">Which KPI will be used to measure this progress?</font>"
msgstr ""
"<font class=\"text-600\">Bạn có kế hoạch hành động gì để đi tiếp với tài khoản này?</font><br>\n"
"                <font class=\"text-600\">KPI nào sẽ được sử dụng để đo lường tiến trình này?</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid ""
"<font class=\"text-o-color-2\">\n"
"                    <strong>Planned Next Step:</strong>\n"
"                </font>\n"
"                <br>\n"
"                <br>"
msgstr ""
"<font class=\"text-o-color-2\">\n"
"                    <strong>Bước kế tiếp theo kế hoạch:</strong>\n"
"                </font>\n"
"                <br>\n"
"                <br>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_invite_view_form
msgid ""
"<i class=\"fa fa-w fa-info-circle\"/> All external users you selected won't "
"be added to the members."
msgstr ""
"<i class=\"fa fa-w fa-info-circle\"/> Tất cả người dùng bên ngoài mà bạn đã "
"chọn sẽ không được thêm vào thành viên."

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_kanban_items
msgid ""
"<i invisible=\"not is_user_favorite\" class=\"fa fa-star\" title=\"Remove from favorites\"/>\n"
"                                    <i invisible=\"is_user_favorite\" class=\"fa fa-star-o\" title=\"Add to favorites\"/>"
msgstr ""
"<i invisible=\"not is_user_favorite\" class=\"fa fa-star\" title=\"Xoá khỏi danh sách yêu thích\"/>\n"
"                                    <i invisible=\"is_user_favorite\" class=\"fa fa-star-o\" title=\"Thêm vào danh sách yêu thích\"/>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_kanban
msgid ""
"<i invisible=\"not is_user_favorite\" class=\"fa fa-star\" title=\"Remove from favorites\"/>\n"
"                                <i invisible=\"is_user_favorite\" class=\"fa fa-star-o\" title=\"Add to favorites\"/>"
msgstr ""
"<i invisible=\"not is_user_favorite\" class=\"fa fa-star\" title=\"Xoá khỏi Danh sách yêu thích\"/>\n"
"                                <i invisible=\"is_user_favorite\" class=\"fa fa-star-o\" title=\"Thêm vào Danh sách yêu thích\"/>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid ""
"<i>\n"
"                        <font style=\"color: rgb(0, 255, 0);\"><strong>Green: Values trust above all</strong></font><br>\n"
"                        <font style=\"color: rgb(255, 0, 0);\"><strong>Red: Values results above all</strong></font><br>\n"
"                        <strong>\n"
"                            <font style=\"color: rgb(239, 198, 49);\">\n"
"                                Yellow: Values creativity and enthusiasm above results\n"
"                            </font>\n"
"                        </strong>\n"
"                    </i>"
msgstr ""
"<i>\n"
"                        <font style=\"color: rgb(0, 255, 0);\"><strong>Xanh lục: Đặt niềm tin lên hàng đầu</strong></font><br>\n"
"                        <font style=\"color: rgb(255, 0, 0);\"><strong>Đỏ: Đặt kết quả lên hàng đầu</strong></font><br>\n"
"                        <strong>\n"
"                            <font style=\"color: rgb(239, 198, 49);\">\n"
"                                Vàng: Đặt sự sáng tạo và nhiệt tình lên trên kết quả\n"
"                            </font>\n"
"                        </strong>\n"
"                    </i>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid ""
"<i>\n"
"                    <font style=\"color: rgb(8, 82, 148);\">\n"
"                        <strong>Blue: Expects accurate and rigorous results</strong>\n"
"                    </font>\n"
"                </i>"
msgstr ""
"<i>\n"
"                    <font style=\"color: rgb(8, 82, 148);\">\n"
"                        <strong>Xanh lam: Mong đợi kết quả chính xác và tuyệt đối</strong>\n"
"                    </font>\n"
"                </i>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_release_note
msgid "<span class=\"text-600\">New Feature 1</span>"
msgstr "<span class=\"text-600\">Tính năng mớ 1</span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_example_branding
msgid ""
"<span style=\"color: rgb(255, 255, 255);font-size: 16px;font-style: normal;font-weight: 400;background-color: rgb(113, 75, 103)\">\n"
"                                        5125C\n"
"                                    </span>"
msgstr ""
"<span style=\"color: rgb(255, 255, 255);font-size: 16px;font-style: normal;font-weight: 400;background-color: rgb(113, 75, 103)\">\n"
"                                        5125C\n"
"                                    </span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<span style=\"font-size: 12px;\">\n"
"                    <font class=\"text-600\">*💡 tick the box when the task is scheduled in the agenda</font>\n"
"                </span>"
msgstr ""
"<span style=\"font-size: 12px;\">\n"
"                    <font class=\"text-600\">*💡 đánh dấu vào ô khi nhiệm vụ được lên lịch trong thời gian biểu</font>\n"
"                </span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<span style=\"font-size: 14px\">\n"
"                                    <font class=\"text-600\">Task A</font>\n"
"                                </span>"
msgstr ""
"<span style=\"font-size: 14px\">\n"
"                                    <font class=\"text-600\">Nhiệm vụ A</font>\n"
"                                </span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<span style=\"font-size: 14px\">\n"
"                                    <font class=\"text-600\">Task B</font>\n"
"                                </span>"
msgstr ""
"<span style=\"font-size: 14px\">\n"
"                                    <font class=\"text-600\">Nhiệm vụ B</font>\n"
"                                </span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<span style=\"font-size: 14px\">\n"
"                                    <font class=\"text-600\">Task C</font>\n"
"                                </span>"
msgstr ""
"<span style=\"font-size: 14px\">\n"
"                                    <font class=\"text-600\">Nhiệm vụ C</font>\n"
"                                </span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<span style=\"font-size: 14px;\">\n"
"                                    <font class=\"text-600\">Task A</font>\n"
"                                </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"                                    <font class=\"text-600\">Nhiệm vụ A</font>\n"
"                                </span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<span style=\"font-size: 14px;\">\n"
"                                    <font class=\"text-600\">Task B</font>\n"
"                                </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"                                    <font class=\"text-600\">Nhiệm vụ B</font>\n"
"                                </span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<span style=\"font-size: 14px;\">\n"
"                                    <font class=\"text-600\">Task C</font>\n"
"                                </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"                                    <font class=\"text-600\">Nhiệm vụ C</font>\n"
"                                </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"                    Add a checklist\n"
"                    (/<span style=\"font-style: italic;\">checklist</span>)\n"
"                </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"                    Thêm một danh sách\n"
"                    (/<span style=\"font-style: italic;\">danh sách</span>)\n"
"                </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"                    Add a separator\n"
"                    (/<span style=\"font-style: italic;\">separator</span>)\n"
"                </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"                    Thêm một dấu phân cách\n"
"                    (/<span style=\"font-style: italic;\">dấu phân cách</span>)\n"
"                </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"                    Use\n"
"                    /<span style=\"font-style: italic;\">heading</span>\n"
"                    to convert a text into a title\n"
"                </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"                    Sử dụng\n"
"                    /<span style=\"font-style: italic;\">thẻ tiêu đề</span>\n"
"                    để chuyển văn bản thành tiêu đề\n"
"                </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">Favorites</font>\n"
"            </span>\n"
"            — Those are\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">shortcuts</font>\n"
"            </span>\n"
"            you create for yourself.\n"
"            Unstar ⭐ this page at the top to remove it from your favorites.\n"
"        </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">Danh sách yêu thích</font>\n"
"            </span>\n"
"            — Đó là\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">những lối tắt</font>\n"
"            </span>\n"
"            bạn tự tạo cho mình.\n"
"            Bỏ gắn sao ⭐ trên đầu trang này để xoá trang khỏi danh sách yêu thích.\n"
"        </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">Private</font>\n"
"            </span>\n"
"        </span>\n"
"        <span style=\"font-size: 14px;\">\n"
"            — This is\n"
"            <span style=\"font-weight: bolder;\"><font class=\"text-o-color-2\">your stuff</font></span>,\n"
"            the things you keep for yourself\n"
"            (<span style=\"font-style: italic;\">Drafts, Todo lists, ...</span>)\n"
"        </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">Riêng tư</font>\n"
"            </span>\n"
"        </span>\n"
"        <span style=\"font-size: 14px;\">\n"
"            — Đây là\n"
"            <span style=\"font-weight: bolder;\"><font class=\"text-o-color-2\">tài liệu của bạn</font></span>,\n"
"            những thứ mà bạn muốn lưu cho riêng mình\n"
"            (<span style=\"font-style: italic;\">Bản nháp, danh sách việc cần làm, ...</span>)\n"
"        </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">Shared</font>\n"
"            </span>\n"
"        </span>\n"
"        <span style=\"font-size: 14px;\">\n"
"            — Those are the ones you\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">have invited someone or been invited to</font>\n"
"            </span>\n"
"        </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">Được chia sẻ</font>\n"
"            </span>\n"
"        </span>\n"
"        <span style=\"font-size: 14px;\">\n"
"            — Đó là những tài liệu mà bạn \n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">đã mời ai đó hoặc được mời</font>\n"
"            </span>\n"
"        </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">Workspace</font>\n"
"            </span>\n"
"        </span>\n"
"        <span style=\"font-size: 14px;\">\n"
"            — Articles there can be accessed by\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">your team</font>\n"
"        </span>\n"
"    </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">Không gian làm việc</font>\n"
"            </span>\n"
"        </span>\n"
"        <span style=\"font-size: 14px;\">\n"
"            — Các bài viết ở đây có thể được\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">cả bộ phận của bạn</font> truy cập\n"
"        </span>\n"
"    </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"            Below this list, try\n"
"            <span style=\"font-weight: bolder;\">commands</span>\n"
"            by\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">typing</font>\n"
"            </span>\n"
"            \"<span style=\"font-weight: bolder;\">/</span>\"\n"
"        </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"            Dưới danh sách này, hãy thử sử dụng\n"
"            <span style=\"font-weight: bolder;\">lệnh</span>\n"
"            bằng cách\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">nhập</font>\n"
"            </span>\n"
"            \"<span style=\"font-weight: bolder;\">/</span>\"\n"
"        </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"            Content — Just click and\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">start typing</font>\n"
"            </span>\n"
"            (<span style=\"font-style: italic;\">documentation, tips, reports, ...</span>)\n"
"        </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"            Nội dung — Chỉ cần nhấp và\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">bắt đầu nhập</font>\n"
"            </span>\n"
"            (<span style=\"font-style: italic;\">tài liệu, mẹo vặt, báo cáo,...</span>)\n"
"        </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"            Folders —\n"
"        </span>\n"
"        <span style=\"font-size: 14px;\">\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">Nest other Articles</font>\n"
"            </span>\n"
"            under it to regroup them\n"
"            (<span style=\"font-style: italic;\">per team, topic, project, ...</span>)\n"
"        </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"            Tập tin —\n"
"        </span>\n"
"        <span style=\"font-size: 14px;\">\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">Lồng các bài viết khác</font>\n"
"            </span>\n"
"            vào bên dưới để nhóm lại chúng\n"
"            (<span style=\"font-style: italic;\">theo bộ phận, chủ đề, dự án, ...</span>)\n"
"        </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"            Select text to\n"
"            <font class=\"bg-o-color-2\">Highlight</font>,\n"
"            <span style=\"text-decoration-line: line-through;\">strikethrough</span>\n"
"            or\n"
"            <span style=\"font-weight: bolder;\">style</span>\n"
"            <span style=\"font-style: italic; text-decoration-line: underline;\">it</span>\n"
"        </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"            Chọn văn bản để\n"
"            <font class=\"bg-o-color-2\">in đậm</font>,\n"
"            <span style=\"text-decoration-line: line-through;\">gạch ngang</span>\n"
"            hoặc\n"
"            <span style=\"font-weight: bolder;\">tạo kiểu</span>\n"
"            <span style=\"font-style: italic; text-decoration-line: underline;\">cho văn bản đó</span>\n"
"        </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"            Use\n"
"            /<span style=\"font-weight: bolder;\"><font class=\"text-o-color-2\">clipboard</font></span>\n"
"            to insert a\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">clipboard</font>\n"
"            </span>\n"
"            box. Need to re-use its content?\n"
"        </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"            Sử dụng\n"
"            /<span style=\"font-weight: bolder;\"><font class=\"text-o-color-2\">bộ nhớ tạm</font></span>\n"
"            để chèn một ô\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">bộ nhớ tạm</font>\n"
"            </span>\n"
"            Bạn cần sử dụng lại nội dung của bộ nhớ tạm?\n"
"        </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"            Use\n"
"            /<span style=\"font-weight: bolder;\"><font class=\"text-o-color-2\">file</font></span>\n"
"            to share documents that are frequently needed\n"
"        </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"            Sử dụng\n"
"            /<span style=\"font-weight: bolder;\"><font class=\"text-o-color-2\">tệp</font></span>\n"
"            để chia sẻ các tài liệu thường xuyên cần đến\n"
"        </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Articles</font>\n"
"        </span>\n"
"        are stored into different\n"
"        <span style=\"font-weight: bolder;\"><font class=\"text-o-color-2\">Sections</font></span>:\n"
"    </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Bài viết</font>\n"
"        </span>\n"
"        được lưu trong các\n"
"        <span style=\"font-weight: bolder;\"><font class=\"text-o-color-2\">Phần</font></span> khác nhau:\n"
"    </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        A good workflow is to write your drafts in\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Private</font>\n"
"        </span>\n"
"        and, once done, move it from\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Private</font>\n"
"        </span>\n"
"        to\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Workspace</font>\n"
"        </span>\n"
"        to share it with everyone.\n"
"    </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"        Một quy trình làm việc tối ưu là viết bản nháp của bạn trong chế độ\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Riêng tư</font>\n"
"        </span>\n"
"        và khi hoàn tất, di chuyển nó từ\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Riêng tư</font>\n"
"        </span>\n"
"        sang\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Không gian làm việc</font>\n"
"        </span>\n"
"        để chia sẻ với mọi người.\n"
"    </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        And again, to move an\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Article</font>\n"
"        </span>\n"
"        from a\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Section</font>\n"
"        </span>\n"
"        to another, just\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Drag &amp; Drop</font>\n"
"        </span>\n"
"        it.\n"
"    </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"        Và xin nhắc lại là để di chuyển một\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Bài viết</font>\n"
"        </span>\n"
"        từ\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Phần</font> này\n"
"        </span>\n"
"        sang phần khác, bạn chỉ cần\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Kéo &amp; Thả</font>\n"
"        </span>\n"
"        it.\n"
"    </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        This is where you and your team can centralize your\n"
"        <font class=\"text-o-color-2\" style=\"font-weight: bolder;\">Knowledge</font>\n"
"        and best practices! 🚀\n"
"    </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"        Đây là nơi mà bạn và cả bộ phận có thể tập trung\n"
"        <font class=\"text-o-color-2\" style=\"font-weight: bolder;\">Kiến thức</font>\n"
"        cũng như các phương pháp tối ưu nhất của mình! 🚀\n"
"    </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        Those are your\n"
"        <span style=\"font-weight: bolder;\"><font class=\"text-o-color-2\">Articles</font></span>.\n"
"    </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"        Đó là các\n"
"        <span style=\"font-weight: bolder;\"><font class=\"text-o-color-2\">Bài viết</font></span> của bạn.\n"
"    </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        To change the way\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Articles</font>\n"
"        </span>\n"
"        are organized, you can simply\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Drag &amp; Drop</font>\n"
"        </span>\n"
"        them\n"
"    </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"        Để thay đổi cách tổ chức\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Bài viết</font>\n"
"        </span>\n"
"        bạn có thể\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Kéo &amp; Thả</font>\n"
"        </span>\n"
"        chúng một cách đơn giản\n"
"    </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        Want to go\n"
"        <span style=\"font-style: italic;\">even</span>\n"
"        faster? ⚡️\n"
"    </span>\n"
"    <br/>\n"
"    <span style=\"font-size: 14px;\">\n"
"        Access\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Articles</font>\n"
"        </span>\n"
"        by opening the\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Command Palette</font>\n"
"        </span>\n"
"        (Ctrl+k/⌘+k) then search through articles by starting your query with\n"
"        \"<span style=\"font-weight: bolder;\">?</span>\".\n"
"    </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"        Bạn\n"
"        <span style=\"font-style: italic;\">thậm chí</span>\n"
"        muốn thao tác nhanh hơn? ⚡️\n"
"    </span>\n"
"    <br/>\n"
"    <span style=\"font-size: 14px;\">\n"
"        Truy cập\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Bài viết</font>\n"
"        </span>\n"
"        bằng cách mở\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Bảng lệnh</font>\n"
"        </span>\n"
"        (Ctrl+k/⌘+k) sau đó tìm kiếm các bài viết bằng cách bắt đầu truy vấn với\n"
"        \"<span style=\"font-weight: bolder;\">?</span>\".\n"
"    </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        👈 See the\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Menu</font>\n"
"        </span>\n"
"        there, on the left?\n"
"    </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"        👈 Bạn có thấy\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Menu</font>\n"
"        </span>\n"
"        ở đó, phía bên trái không\n"
"    </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid "<span style=\"font-size: 14px;\">And voilà, it is that simple.</span>"
msgstr "<span style=\"font-size: 14px;\">Và vậy đó, chỉ đơn giản thế thôi.</span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">Check this box to indicate it's done</span>"
msgstr ""
"<span style=\"font-size: 14px;\">Đánh dấu ô này để cho biết đã hoàn "
"tất</span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">Click anywhere, and just start "
"typing</span>"
msgstr ""
"<span style=\"font-size: 14px;\">Nhấp vào bất kỳ đâu, rồi chỉ cần bắt đầu "
"nhập</span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid "<span style=\"font-size: 14px;\">Each of them can be used both as:</span>"
msgstr "<span style=\"font-size: 14px;\">Bạn đều có thể sử dụng chúngnhư:</span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">From any Odoo document, find this article "
"by clicking on the 📗 icon in the chatter.</span>"
msgstr ""
"<span style=\"font-size: 14px;\">Từ bất kỳ tài liệu Odoo nào, hãy tìm bài "
"viết này bằng cách nhấp vào biểu tượng 📗 trong cửa sổ trò chuyện.</span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">From this box, files can be previewed, "
"forwarded and downloaded. 📁</span>"
msgstr ""
"<span style=\"font-size: 14px;\">Từ ô này, các tập tin có thể được xem "
"trước, chuyển tiếp và tải xuống. 📁</span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">Got it? Now let's try advanced features "
"🧠</span>"
msgstr ""
"<span style=\"font-size: 14px;\">Bạn đã hiểu rồi chứ? Tiếp theo, hãy thử các"
" tính năng nâng cao nhé 🧠</span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">Need this document somewhere? Come back "
"here by clicking on the 📗 icon in the chatter.</span>"
msgstr ""
"<span style=\"font-size: 14px;\">Bạn cần tài liệu này ở đâu đó? Hãy quay lại"
" đây bằng cách nhấp vào biểu tượng 📗 trong cửa sổ trò chuyện.</span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">Not sure how to do it? Check the video "
"below 👇</span>"
msgstr ""
"<span style=\"font-size: 14px;\">Bạn không rõ cách làm? Hãy xem video dưới "
"đây 👇</span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">Press Ctrl+Z/⌘+Z to undo any change</span>"
msgstr ""
"<span style=\"font-size: 14px;\">Bấm Ctrl+Z/⌘+Z để hoàn tác mọi thay "
"đổi</span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">This private page is for you to play around with.</span>\n"
"    <br/>\n"
"    <span style=\"font-size: 14px;\">Ready to give it a spin?</span>"
msgstr ""
"<span style=\"font-size: 14px;\">Trang riêng tư này là nơi bạn được thoả sức sử dụng.</span>\n"
"    <br/>\n"
"    <span style=\"font-size: 14px;\">Bạn đã sẵn sàng thử chưa?</span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid "<span style=\"font-size: 14px;\">Try the following 👇</span>"
msgstr "<span style=\"font-size: 14px;\">Thử các tính năng sau 👇</span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">You can use the clipboard as a description,"
" a message or simply copy it to your clipboard! 👌</span>"
msgstr ""
"<span style=\"font-size: 14px;\">Bạn có thể sử dụng bộ nhớ tạm dưới dạng mô "
"tả, tin nhắn hoặc chỉ cần sao chép nó vào bộ nhớ tạm của mình!👌</span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid ""
"<span style=\"font-size: 18px;\">\n"
"                                <strong>Color</strong>\n"
"                            </span>"
msgstr ""
"<span style=\"font-size: 18px;\">\n"
"                                <strong>Màu sắc</strong>\n"
"                            </span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid ""
"<span style=\"font-size: 18px;\">\n"
"                                <strong>Name</strong>\n"
"                            </span>"
msgstr ""
"<span style=\"font-size: 18px;\">\n"
"                                <strong>Tên</strong>\n"
"                            </span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid ""
"<span style=\"font-size: 18px;\">\n"
"                                <strong>Role</strong>\n"
"                            </span>"
msgstr ""
"<span style=\"font-size: 18px;\">\n"
"                                <strong>Vai trò</strong>\n"
"                            </span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid ""
"<span style=\"font-size: 18px;\">\n"
"                    1. How many people will live in this house?\n"
"                </span>"
msgstr ""
"<span style=\"font-size: 18px;\">\n"
"                    1. Có bao nhiêu người sẽ sống trong ngôi nhà này?\n"
"                </span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid ""
"<span style=\"font-size: 18px;\">\n"
"                    2. What is your budget?\n"
"                </span>"
msgstr ""
"<span style=\"font-size: 18px;\">\n"
"                    2. Ngân sách của bạn là bao nhiêu?\n"
"                </span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid ""
"<span style=\"font-size: 18px;\">\n"
"                    3. Which style do you prefer: Natural or Industrial?\n"
"                </span>"
msgstr ""
"<span style=\"font-size: 18px;\">\n"
"                    3. Bạn thích phong cách nào: thiên nhiên hay công nghiệp?\n"
"                </span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid ""
"<span style=\"font-size: 18px;\">\n"
"                    4. What can I do if I haven't found exactly what I wanted?\"\n"
"                </span>"
msgstr ""
"<span style=\"font-size: 18px;\">\n"
"                    4. Tôi có thể làm gì nếu chưa tìm thấy đúng điều mình muốn?\"\n"
"                </span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<span style=\"font-size: 18px;\">\n"
"                    Productivity is never an accident. It is always the result of a commitment to excellence, intelligent planning, and focused effort.\n"
"                </span>​\n"
"                - Paul J. Meyer"
msgstr ""
"<span style=\"font-size: 18px;\">\n"
"                    Năng suất không bao giờ xảy đến một cách ngẫu nhiên mà luôn là kết quả của cam kết hướng tới sự xuất sắc, kế hoạch hành động thông minh, cùng với nỗ lực cao độ.\n"
"                </span>​\n"
"                - Paul J. Meyer"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid ""
"<span style=\"font-size: 18px;\">\n"
"                    To add more, use the clipboard below 👇🏼\n"
"                </span>"
msgstr ""
"<span style=\"font-size: 18px;\">\n"
"                    Để thêm nhiều hơn, hãy sử dụng bộ nhớ tạm dưới đây👇🏼\n"
"                </span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid ""
"<span style=\"font-size: 24px;\">\n"
"                                Make sure to comply as <strong>you will represent <u>our</u> brand</strong>\n"
"                            </span>\n"
"                            <br>\n"
"                            <span style=\"font-size: 24px;\">If in doubt, get in touch with us.</span>"
msgstr ""
"<span style=\"font-size: 24px;\">\n"
"                                Hãy đảm bảo tuân thủ vì <strong>bạn sẽ đại diện cho thương hiệu của <u>chúng tôi</u></strong>\n"
"                            </span>\n"
"                            <br>\n"
"                            <span style=\"font-size: 24px;\">Nếu bạn có điểm nào còn thắc mắc, hãy liên hệ với chúng tôi.</span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid ""
"<span style=\"font-size: 36px;\">And that's all for this month, folks!<br>\n"
"                    Thanks for reading, see you soon.👋\n"
"                </span>"
msgstr ""
"<span style=\"font-size: 36px;\">Và đó là mọi thứ trong tháng này!<br>\n"
"                    Cảm ơn bạn đã đón đọc và hẹn gặp lại.👋\n"
"                </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid "<span style=\"font-size: 48px;\">🚀</span>"
msgstr "<span style=\"font-size: 48px;\">🚀</span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid ""
"<span style=\"font-weight: bolder;\">\n"
"                    Company Newsletter\n"
"                </span>: <font class=\"text-400\">Month</font>"
msgstr ""
"<span style=\"font-weight: bolder;\">\n"
"                    Bản tin email công ty\n"
"                </span>: <font class=\"text-400\">Tháng</font>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-1\">YouTube</font>\n"
"        </span>"
msgstr ""
"<span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-1\">YouTube</font>\n"
"        </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-weight: bolder;\"><font class=\"text-o-"
"color-1\">Facebook</font></span>"
msgstr ""
"<span style=\"font-weight: bolder;\"><font class=\"text-o-"
"color-1\">Facebook</font></span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-weight: bolder;\"><font class=\"text-o-"
"color-1\">X</font></span>"
msgstr ""
"<span style=\"font-weight: bolder;\"><font class=\"text-o-"
"color-1\">X</font></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid ""
"<strong style=\"font-weight: 500\"><span style=\"font-size: 14px\">\n"
"                            Early bird alert</span></strong>"
msgstr ""
"<strong style=\"font-weight: 500\"><span style=\"font-size: 14px\">\n"
"                            Thông báo đặt chỗ sớm</span></strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<strong style=\"font-weight: 500\">Optional Tasks</strong>"
msgstr "<strong style=\"font-weight: 500\">Nhiệm vụ không bắt buộc</strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<strong style=\"font-weight: 500\">⚡ TOP 3 PRIORITIES</strong>"
msgstr "<strong style=\"font-weight: 500\">⚡ 3 ƯU TIÊN HÀNG ĐẦU</strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid ""
"<strong>\n"
"                                    <font style=\"color: rgb(148, 189, 123);\">Do</font>\n"
"                                </strong>"
msgstr ""
"<strong>\n"
"                                    <font style=\"color: rgb(148, 189, 123);\">Nên làm</font>\n"
"                                </strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">FRIDAY 🏠 @home</font>\n"
"                                </span>\n"
"                            </strong>"
msgstr ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">THỨ 6 🏠 @nhà</font>\n"
"                                </span>\n"
"                            </strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">MONDAY 🏢 @office</font>\n"
"                                </span>\n"
"                            </strong>"
msgstr ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">THỨ 2 🏢 @văn_phòng</font>\n"
"                                </span>\n"
"                            </strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">Reminders</font>\n"
"                                </span>\n"
"                            </strong>"
msgstr ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">Nhắc nhở</font>\n"
"                                </span>\n"
"                            </strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">SATURDAY 🏠 @home</font>\n"
"                                </span>\n"
"                            </strong>"
msgstr ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">THỨ 7 🏠 @nhà</font>\n"
"                                </span>\n"
"                            </strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">SUNDAY 🏠 @home</font>\n"
"                                </span>\n"
"                            </strong>"
msgstr ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">CHỦ NHẬT 🏠 @nhà</font>\n"
"                                </span>\n"
"                            </strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">THURSDAY 🏢 @office</font>\n"
"                                </span>\n"
"                            </strong>"
msgstr ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">THỨ 5 🏢 @văn_phòng</font>\n"
"                                </span>\n"
"                            </strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">TUESDAY 🏢 @office</font>\n"
"                                </span>\n"
"                            </strong>"
msgstr ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">THỨ 3 🏢 @văn_phòng</font>\n"
"                                </span>\n"
"                            </strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">WEDNESDAY 🏠 @home</font>\n"
"                                </span>\n"
"                            </strong>"
msgstr ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">THỨ 4 🏠 @nhà</font>\n"
"                                </span>\n"
"                            </strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">Name</span>\n"
"                            </strong>"
msgstr ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">Tên</span>\n"
"                            </strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">Strengths</span>\n"
"                            </strong>"
msgstr ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">Điểm mạnh</span>\n"
"                            </strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">Weaknesses</span>\n"
"                            </strong>"
msgstr ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">Điểm yếu</span>\n"
"                            </strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid ""
"<strong><font class=\"text-o-color-2\">PRO TIP</font></strong>: From a lead,"
" use the book button in the chatter to find this article and autocomplete "
"your description with this qualification template."
msgstr ""
"<strong><font class=\"text-o-color-2\">MẸO</font></strong>: Từ lead, hãy sử "
"dụng nút hình cuốn sách trong cửa sổ trò chuyện để tìm bài viết này và tự "
"động hoàn thành phần mô tả bằng mẫu hồ sơ này."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid ""
"<strong><font class=\"text-o-color-2\">PRO TIP</font></strong>: From a lead,"
" use the book button in the chatter to find this article and autocomplete "
"your email with this template."
msgstr ""
"<strong><font class=\"text-o-color-2\">MẸO</font></strong>: Từ lead, hãy sử "
"dụng nút hình cuốn sách trong cửa sổ trò chuyện để tìm bài viết này và tự "
"động hoàn thành email bằng mẫu này."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "<strong><font style=\"color: rgb(231, 99, 99);\">Do Not</font></strong>"
msgstr ""
"<strong><font style=\"color: rgb(231, 99, 99);\">Không nên "
"làm</font></strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "<strong><span style=\"font-size: 18px;\">Change</span></strong>"
msgstr "<strong><span style=\"font-size: 18px;\">Thay đổi</span></strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "<strong><span style=\"font-size: 18px;\">Complexity</span></strong>"
msgstr "<strong><span style=\"font-size: 18px;\">Độ phức tạp</span></strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "<strong>Actions Taken:</strong>"
msgstr "<strong>Tác vụ cải thiện:</strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "<strong>Company</strong>"
msgstr "<strong>Công ty</strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "<strong>Contact</strong>"
msgstr "<strong>Liên hệ</strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "<strong>Do not recreate the Logo from scratch, use these ones</strong>"
msgstr "<strong>Đừng tạo lại logo từ đầu, hãy sử dụng những logo này</strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<strong>Extra Notes:</strong>"
msgstr "<strong>Ghi chú bổ sung:</strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<strong>Fast Facts</strong>"
msgstr "<strong>Thông tin sơ bộ</strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "<strong>Lessons Learnt:</strong>"
msgstr "<strong>Bài học kinh nghiệm:</strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid ""
"<strong>M</strong>onthly\n"
"                            <strong>R</strong>ecurring\n"
"                            <strong>R</strong>evenues\n"
"                            (<em>subscriptions, ...</em>)"
msgstr ""
"<strong>D</strong>oanh thu\n"
"                            <strong>Đ</strong>ịnh kỳ\n"
"                            <strong>H</strong>àng tháng\n"
"                            (<em>gói đăng ký, ...</em>)"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "<strong>MRR</strong>"
msgstr "<strong>MRR</strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<strong>Main Point of Contact:</strong>"
msgstr "<strong>Người liên hệ chính:</strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid ""
"<strong>N</strong>on-<strong>R</strong>ecurring <strong>R</strong>evenues\n"
"                            (<em>consultancy services, ...</em>)"
msgstr ""
"<strong>D</strong>oanh thu <strong>K</strong>hông <strong>T</strong>hường xuyên\n"
"                            (<em>dịch vụ tư vấn, ...</em>)"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "<strong>NRR</strong>"
msgstr "<strong>NRR</strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<strong>Optional Tasks</strong>"
msgstr "<strong>Nhiệm vụ không bắt buộc</strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "<strong>Q1,2,3,4</strong>"
msgstr "<strong>Q1,2,3,4</strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_release_note
msgid ""
"<strong>Summary</strong>: <font class=\"text-600\">What an exciting release!"
" This time the focus was on...</font>"
msgstr ""
"<strong>Tóm tắt</strong>: <font class=\"text-600\">Thật là một phiên bản thú"
" vị! Trọng tâm của phiên bản này là...</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid ""
"<strong>U</strong>nique <strong>S</strong>elling <strong>P</strong>roposition:\n"
"                            Advantage that makes you stand out from the competition."
msgstr ""
"<strong>L</strong>ợi thế <strong>B</strong>án hàng <strong>Đ</strong>ộc nhất:\n"
"                            Lợi thế khiến bạn trở nên vượt trội so với đối thủ cạnh tranh."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "<strong>USP</strong>"
msgstr "<strong>USP</strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<strong>⚡ TOP 3 PRIORITIES</strong>"
msgstr "<strong>⚡ 3 ƯU TIÊN HÀNG ĐẦU</strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid "<u>Demographics</u>"
msgstr "<u>Thông tin cá nhân</u>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid "<u>Key Decision Factors</u>"
msgstr "<u>Yếu tố quyết định chính</u>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid "<u>Strategy</u>"
msgstr "<u>Chiến lược</u>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "A day or less"
msgstr "Một hoặc dưới một ngày"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid ""
"A must listen for all developers out there and anyone interested in "
"Javascript!"
msgstr ""
"Đây là tập mà tất cả các nhà phát triển trên toàn cầu và bất kỳ ai quan tâm "
"đến Javascript nên nghe!"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
msgid "Abigail"
msgstr "Abigail"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid ""
"About YourCompany: YourCompany is a team of passionate people whose goal is to improve everyone's life\n"
"                        through disruptive products.\n"
"                        We build great products to solve your business problems.\n"
"                        <br>\n"
"                        Our products are designed for small to medium size companies willing to optimize their performance."
msgstr ""
"Giới thiệu về YourCompany: YourCompany là một đội ngũ gồm những cá nhân đam mê với mục tiêu cải thiện cuộc sống của mọi người\n"
"                        thông qua các sản phẩm đột phá.\n"
"                        Chúng tôi tạo ra những sản phẩm tuyệt vời để giải quyết các vấn đề kinh doanh của bạn.\n"
"                        <br>\n"
"                        Sản phẩm của chúng tôi được thiết kế cho các công ty vừa và nhỏ sẵn sàng tối ưu hóa hiệu suất làm việc của họ."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "Absent for more than a day"
msgstr "Vắng mặt trên một ngày"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/js/knowledge_controller.js:0
msgid "Access Denied"
msgstr "Truy cập bị từ chối"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "Access Restricted. May not be shared with everyone from"
msgstr "Hạn chế truy cập. Có thể không được chia sẻ với mọi người từ"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_account_management
msgid "Account Management"
msgstr "Quản lý tài khoản"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Account Management Cheat Sheet"
msgstr "Kế hoạch quản lý tài khoản"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__message_needaction
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__message_needaction
msgid "Action Needed"
msgstr "Tác vụ cần thiết"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Action Plan"
msgstr "Kế hoạch tác vụ"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__active
msgid "Active"
msgstr "Đang hoạt động"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__activity_ids
msgid "Activities"
msgstr "Hoạt động"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Hoạt động ngoại lệ"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__activity_state
msgid "Activity State"
msgstr "Trạng thái hoạt động"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__activity_type_icon
msgid "Activity Type Icon"
msgstr "Biểu tượng loại hoạt động"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Add Cover"
msgstr "Thêm ảnh bìa"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Add Icon"
msgstr "Thêm biểu tượng"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Add Properties"
msgstr "Thêm thuộc tính"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/properties_panel/properties_panel.xml:0
msgid "Add Property Fields"
msgstr "Thêm trường thuộc tính"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comment/comment.xml:0
msgid "Add a Comment..."
msgstr "Thêm một bình luận..."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/embedded_clipboard_plugin/embedded_clipboard_plugin.js:0
msgid "Add a clipboard section"
msgstr "Thêm một phần bộ nhớ tạm"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/plugins/comments_plugin/comments_plugin.js:0
msgid "Add a comment to an image"
msgstr "Thêm một bình luận vào hình ảnh"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/plugins/comments_plugin/comments_plugin.js:0
msgid "Add a comment to selection"
msgstr "Thêm một bình luận vào lựa chọn"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comments_popover/comments_popover.xml:0
msgid "Add a comment..."
msgstr "Thêm nhận xét..."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_icon/knowledge_icon.xml:0
msgid "Add a random icon"
msgstr "Thêm một biểu tượng ngẫu nhiên"

#. module: knowledge
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_stage_action
msgid ""
"Add an embed kanban view of article items in the body of an article by using"
" '/kanban' command."
msgstr ""
"Thêm chế độ xem kanban nhúng của các hạng mục bài viết trong nội dung bài "
"viết bằng cách sử dụng lệnh '/kanban'."

#. module: knowledge
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_favorite_action
msgid ""
"Add articles in your list of favorites by clicking on the <i class=\"fa fa-"
"star-o\"></i> next to the article name."
msgstr ""
"Thêm bài viết vào danh sách yêu thích của bạn bằng cách nhấp vào <i "
"class=\"fa fa-star-o\"></i> bên cạnh tên bài viết. "

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_invite_view_form
msgid "Add people or email addresses"
msgstr "Thêm người hoặc địa chỉ email"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "Add people or email addresses..."
msgstr "Thêm người hoặc địa chỉ email..."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.js:0
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_hierarchy
msgid "Add to favorites"
msgstr "Thêm vào danh sách yêu thích"

#. module: knowledge
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_member_action
msgid ""
"Adding members allows you to share Articles while granting specific access "
"rights<br>(can write, can read, ...)."
msgstr ""
"Việc thêm thành viên cho phép bạn chia sẻ Bài viết và đồng thời cấp các "
"quyền truy cập cụ thể<br>(có thể viết, có thể đọc,...)."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "Admin"
msgstr "Quản trị viên"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "Administrator"
msgstr "Quản trị viên"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/webclient/commands/knowledge_providers.js:0
msgid "Advanced Search"
msgstr "Tìm kiếm nâng cao"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "Advertising that would appear online or on TV"
msgstr "Quảng cáo sẽ xuất hiện online hoặc trên TV"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
msgid ""
"After-Sales Service <span class=\"o_stars o_five_stars\" id=\"checkId-5\"><i"
" class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i></span>"
msgstr ""
"Dịch vụ hậu mãi​ <span class=\"o_stars o_five_stars\" id=\"checkId-5\"><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
msgid ""
"After-Sales Service <span class=\"o_stars o_five_stars\" id=\"checkId-5\"><i"
" class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"
msgstr ""
"Dịch vụ hậu mãi​ <span class=\"o_stars o_five_stars\" id=\"checkId-5\"><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
msgid ""
"After-Sales Service <span class=\"o_stars o_five_stars\" id=\"checkId-5\"><i"
" class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"
msgstr ""
"Dịch vụ hậu mãi​ <span class=\"o_stars o_five_stars\" id=\"checkId-5\"><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
msgid ""
"After-Sales Service​ <span class=\"o_stars o_five_stars\" "
"id=\"checkId-5\"><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"
msgstr ""
"Dịch vụ hậu mãi​ <span class=\"o_stars o_five_stars\" id=\"checkId-5\"><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid ""
"After-Sales Service​ <span class=\"o_stars o_five_stars\" "
"id=\"checkId-5\"><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i "
"class=\"fa fa-star-o\"></i></span>"
msgstr ""
"Dịch vụ hậu mãi​ <span class=\"o_stars o_five_stars\" id=\"checkId-5\"><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid "Age:"
msgstr "Tuổi:"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
msgid "Age: 24"
msgstr "Tuổi: 24"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
msgid "Age: 29"
msgstr "Tuổi: 29"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
msgid "Age: 42"
msgstr "Tuổi: 42"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comments_panel/comments_panel.xml:0
msgid "All Discussions"
msgstr "Tất cả cuộc thảo luận"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid "All Models 📖"
msgstr "Tất cả mẫu 📖"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/form_status_indicator/form_status_indicator.xml:0
msgid "All changes saved"
msgstr "Mọi thay đổi đã được lưu"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "All my activities will always be encoded in our CRM"
msgstr "Tất cả hoạt động của tôi sẽ luôn được ghi nhận vào CRM của chúng tôi"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "Always include sufficient clear space around the logo"
msgstr "Luôn lấy đủ khoảng trống xung quanh logo"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid ""
"Always type <strong>YourCompany</strong> in the same font size and style as "
"the content of the text"
msgstr ""
"Luôn nhập <strong>YourCompany</strong> cùng cỡ chữ và kiểu chữ với nội dung "
"của văn bản"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "An hour or two (medical appointment, ...)"
msgstr "Một hoặc hai giờ (lịch hẹn khám bệnh,...)"

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_post_mortem
msgid ""
"Analyze what went wrong and the underlying causes. Extract insights to avoid"
" making similar mistakes in the future."
msgstr ""
"Phân tích những gì đã đi chệch hướng và nguyên nhân cơ bản dẫn đến điều đó. "
"Rút ra các bài học kinh nghiệm để tránh mắc phải những sai lầm tương tự "
"trong tương lai."

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__article_anchor_text
msgid "Anchor Text"
msgstr "Anchor Text"

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_hr_faq
msgid "Answer questions frequently asked by your employees"
msgstr "Trả lời các câu hỏi thường gặp của nhân viên"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search_items
msgid "Archived"
msgstr "Đã lưu trữ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover_dialog.js:0
msgid ""
"Are you sure you want to delete this cover? It will be removed from every "
"article it is used in."
msgstr ""
"Bạn có chắc chắn muốn xóa ảnh bìa này không? Nó sẽ bị xóa khỏi mọi bài viết "
"mà nó được sử dụng."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid ""
"Are you sure you want to leave your private Article? As you are its last "
"member, it will be moved to the Trash."
msgstr ""
"Bạn có chắc chắn muốn rời khỏi bài viết riêng tư này không? Vì bạn là thành "
"viên cuối cùng của bài viết, nên nó sẽ được chuyển vào Thùng rác."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.js:0
msgid ""
"Are you sure you want to move \"%(icon)s%(title)s\" to private? Only you "
"will be able to access it."
msgstr ""
"Bạn có chắc chắn muốn chuyển \"%(icon)s%(title)s\" vào mục riêng tư không? "
"Chỉ có bạn mới có thể truy cập nó."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.js:0
msgid ""
"Are you sure you want to move \"%(icon)s%(title)s\" to the Shared section? "
"It will be shared with all listed members."
msgstr ""
"Bạn có chắc chắn muốn chuyển \"%(icon)s%(title)s\" vào phần Chia sẻ không? "
"Nó sẽ được chia sẻ với tất cả người dùng được liệt kê."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.js:0
msgid ""
"Are you sure you want to move \"%(icon)s%(title)s\" to the Workspace? It "
"will be shared with all internal users."
msgstr ""
"Bạn có chắc chắn muốn chuyển \"%(icon)s%(title)s\" vào Không gian làm việc "
"không? Nó sẽ được chia sẻ với tất cả người dùng nội bộ."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.js:0
msgid ""
"Are you sure you want to move \"%(icon)s%(title)s\" under "
"\"%(parentIcon)s%(parentTitle)s\"? It will be shared with the same persons."
msgstr ""
"Bạn có chắc chắn muốn chuyển \"%(icon)s%(title)s\" vào "
"\"%(parentIcon)s%(parentTitle)s\" không? Nó sẽ được chia sẻ với những người "
"trong đó."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid ""
"Are you sure you want to remove your member? By leaving an article, you may "
"lose access to it."
msgstr ""
"Bạn có chắc chắn muốn xóa quyền thành viên của mình không? Khi rời khỏi một "
"bài viết, bạn có thể mất quyền truy cập vào bài viết đó."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid "Are you sure you want to remove your own \"Write\" access?"
msgstr "Bạn có chắc chắn muốn xóa quyền \"Viết\" của chính mình không?"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid ""
"Are you sure you want to restore access? This means this article will now "
"inherit any access set on its parent articles."
msgstr ""
"Bạn có chắc chắn muốn khôi phục quyền truy cập không? Điều này đồng nghĩa "
"với việc bài viết này giờ đây sẽ kế thừa mọi quyền truy cập được thiết lập "
"trên các bài viết chính của nó."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid ""
"Are you sure you want to restrict access to this article? This means it will"
" no longer inherit access rights from its parents."
msgstr ""
"Bạn có chắc chắn muốn hạn chế quyền truy cập vào bài viết này không? Điều "
"này đồng nghĩa với việc bài viết sẽ không còn kế thừa quyền truy cập từ các "
"bài viết chính của nó nữa."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/views/item_calendar/item_calendar_view.js:0
msgid "Are you sure you want to send this article to the trash?"
msgstr "Bạn có chắc chắn muốn đưa bài viết này vào thùng rác không?"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid ""
"Are you sure you want to set the internal permission to \"none\"? If you do,"
" you will no longer have access to the article."
msgstr ""
"Bạn có chắc chắn muốn đặt quyền nội bộ thành \"không\" không? Nếu thực hiện "
"thao tác này, bạn sẽ không còn quyền truy cập vào bài viết."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid ""
"Are you sure you want to set your permission to \"none\"? If you do, you "
"will no longer have access to the article."
msgstr ""
"Bạn có chắc chắn muốn đặt quyền của mình thành \"không\" không? Nếu thực "
"hiện thao tác này, bạn sẽ không còn quyền truy cập vào bài viết."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/plugins/article_plugin/article_plugin.js:0
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_favorite__article_id
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_member__article_id
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__article_id
#: model:ir.model.fields,field_description:knowledge.field_knowledge_invite__article_id
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_favorite_view_search
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_member_view_search
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_stage_view_search
msgid "Article"
msgstr "Bài viết"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article_member.py:0
msgid ""
"Article '%s' should always have a writer: inherit write permission, or have "
"a member with write access"
msgstr ""
"Bài viết '%s' phải luôn có người viết: kế thừa quyền viết hoặc có thành viên"
" có quyền viết"

#. module: knowledge
#: model:ir.model,name:knowledge.model_knowledge_article_thread
msgid "Article Discussion Thread"
msgstr "Chủ đề thảo luận bài viết"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__article_properties_definition
msgid "Article Item Properties"
msgstr "Thuộc tính hạng mục bài viết"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.js:0
#: model:ir.actions.act_window,name:knowledge.knowledge_article_action_item_calendar
#: model:ir.actions.act_window,name:knowledge.knowledge_article_item_action
#: model:ir.actions.act_window,name:knowledge.knowledge_article_item_action_stages
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search
msgid "Article Items"
msgstr "Hạng mục bài viết"

#. module: knowledge
#: model:ir.model,name:knowledge.model_knowledge_article_member
msgid "Article Member"
msgstr "Thành viên bài viết"

#. module: knowledge
#: model:ir.model,name:knowledge.model_knowledge_article_template_category
msgid "Article Template Category"
msgstr "Danh mục mẫu bài viết"

#. module: knowledge
#: model:ir.actions.act_window,name:knowledge.knowledge_article_template_action
msgid "Article Templates"
msgstr "Mẫu bài viết"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__article_url
msgid "Article URL"
msgstr "URL bài viết"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__cover_image_id
msgid "Article cover"
msgstr "Ảnh bìa bài viết"

#. module: knowledge
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_action_item_calendar
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_item_action
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_item_action_stages
msgid ""
"Article items are articles that exist inside their parents but are not displayed in the menu.\n"
"                They can be used to handle lists (Buildings, Tasks, ...)."
msgstr ""
"Hạng mục bài viết là các bài viết tồn tại trong bài viết chính của chúng nhưng không được hiển thị trong menu.\n"
"                 Chúng có thể được sử dụng để xử lý các danh sách (VD: Nhiệm vụ,...)."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid ""
"Article items are not showed in the left-side menu\n"
"but are shown in inserted kanban/list views"
msgstr ""
"Các hạng mục bài viết không được hiển thị trong menu bên trái\n"
"mà hiển thị trong chế độ xem danh sách/kanban được chèn"

#. module: knowledge
#: model:ir.model.constraint,message:knowledge.constraint_knowledge_article_check_article_item_parent
msgid "Article items must have a parent."
msgstr "Các hạng mục bài viết phải có hạng mục chính."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "Article shared with you: %s"
msgstr "Bài viết được chia sẻ với bạn: %s"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/move_article_dialog/move_article_dialog.js:0
#: model:ir.actions.act_window,name:knowledge.knowledge_article_action
#: model:ir.actions.act_window,name:knowledge.knowledge_article_action_form
#: model:ir.actions.act_window,name:knowledge.knowledge_article_action_form_show_resolved
#: model:ir.actions.server,name:knowledge.ir_actions_server_knowledge_home_page
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search
msgid "Articles"
msgstr "Bài viết"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"Articles %s cannot be updated as this would create a recursive hierarchy."
msgstr ""
"Không thể cập nhật các bài viết %s vì điều này sẽ tạo ra một hệ thống phân "
"cấp đệ quy."

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_cover__article_ids
msgid "Articles using cover"
msgstr "Bài viết sử dụng ảnh bìa"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "As a reader, you can't leave a Workspace article"
msgstr ""
"Với vai trò người đọc, bạn không thể rời khỏi bài viết trên Không gian làm "
"việc"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid ""
"As an administrator, you can always modify this article and its members."
msgstr ""
"Với vai trò quản trị viên, bạn luôn có thể sửa đổi bài viết này và các thành"
" viên của bài viết."

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__message_attachment_count
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__message_attachment_count
msgid "Attachment Count"
msgstr "Số tệp đính kèm"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid "Available Models ✅"
msgstr "Mẫu có sẵn ✅"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid "BOOK NOW"
msgstr "ĐẶT NGAY"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Background:"
msgstr "Thông tin cơ bản:"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "Backlog"
msgstr "Việc tồn đọng"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "Base Color On"
msgstr "Màu cơ bản trên"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "Based on"
msgstr "Dựa trên"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Be assertive but listen to what is being said"
msgstr "Hãy quyết đoán nhưng luôn chú ý lắng nghe"

#. module: knowledge
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_action
msgid "Be the first one to unleash the power of Knowledge!"
msgstr "Hãy là người đầu tiên giải phóng tiềm lực Kiến thức!"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__body
msgid "Body"
msgstr "Thân"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "Booklets for a total price &gt; $1000"
msgstr "Tổng giá booklet &gt; $1000"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_brand_assets
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "Brand Assets"
msgstr "Tài sản thương hiệu"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
msgid ""
"Brand Attraction <span class=\"o_stars o_five_stars\" id=\"checkId-3\"><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star-o\"></i></span>"
msgstr ""
"Tên tuổi thương hiệu <span class=\"o_stars o_five_stars\" "
"id=\"checkId-3\"><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
msgid ""
"Brand Attraction <span class=\"o_stars o_five_stars\" id=\"checkId-3\"><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"
msgstr ""
"Tên tuổi thương hiệu <span class=\"o_stars o_five_stars\" "
"id=\"checkId-3\"><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
msgid ""
"Brand Attraction <span class=\"o_stars o_five_stars\" id=\"checkId-3\"><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"
msgstr ""
"Tên tuổi thương hiệu <span class=\"o_stars o_five_stars\" "
"id=\"checkId-3\"><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid ""
"Brand Attraction <span class=\"o_stars o_five_stars\" id=\"checkId-3\"><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"
msgstr ""
"Tên tuổi thương hiệu <span class=\"o_stars o_five_stars\" "
"id=\"checkId-3\"><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i "
"class=\"fa fa-star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "Brand Name Rules"
msgstr "Quy tắc tên thương hiệu"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.xml:0
msgid "Browse Templates"
msgstr "Tìm mẫu"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_release_note
msgid "Bug Fixes 🔨"
msgstr "Sửa lỗi 🔨"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.xml:0
msgid "Build an Item Calendar"
msgstr "Tạo lịch hạng mục"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.xml:0
msgid "Build an Item Kanban"
msgstr "Tạo kanban hạng mục"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.xml:0
msgid "Build an Item List"
msgstr "Tạo danh sách hạng mục"

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_marketing_customer_personas
msgid ""
"Build fictional representation of your customers to better tailor your "
"advertising messages for them. "
msgstr ""
"Mường tượng hình ảnh khách hàng để điều chỉnh thông điệp quảng cáo của bạn "
"cho phù hợp với họ hơn."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_release_note
msgid "But Also..."
msgstr "Và cả..."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Buying Process"
msgstr "Quá trình mua hàng"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.js:0
msgid "Calendar of %s"
msgstr "Lịch %s"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.js:0
msgid "Calendar of Article Items"
msgstr "Lịch hạng mục bài viết"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__user_can_write
msgid "Can Edit"
msgstr "Có thể sửa"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__user_can_read
msgid "Can Read"
msgstr "Có thể đọc"

#. module: knowledge
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article__inherited_permission__write
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article__internal_permission__write
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article_member__permission__write
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_invite__permission__write
msgid "Can edit"
msgstr "Có thể sửa"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__is_article_visible_by_everyone
msgid "Can everyone see the Article?"
msgstr "Có phải mọi người đều có thể xem bài viết không?"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article_member.py:0
msgid "Can not update the article or partner of a member."
msgstr "Không thể cập nhật bài viết hoặc đối tác của thành viên."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article_favorite.py:0
msgid "Can not update the article or user of a favorite."
msgstr "Không thể cập nhật bài viết hoặc người dùng của mục yêu thích."

#. module: knowledge
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article__inherited_permission__read
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article__internal_permission__read
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article_member__permission__read
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_invite__permission__read
msgid "Can read"
msgstr "Có thể đọc"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__user_has_access_parent_path
msgid "Can the user join?"
msgstr "Người dùng có thể tham gia không?"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__is_article_visible
msgid "Can the user see the article?"
msgstr "Người dùng có thể xem bài viết không?"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/article_selection_dialog/article_selection_dialog.xml:0
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover.xml:0
#: code:addons/knowledge/static/src/components/move_article_dialog/move_article_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_invite_view_form
msgid "Cancel"
msgstr "Hủy"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"Cannot create an article under article %(parent_name)s which is a non-"
"private parent"
msgstr ""
"Không thể tạo một bài viết thuộc bài viết %(parent_name)s không phải bài "
"viết riêng tư"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid ""
"Capitalize the word <strong>YourCompany</strong>, except if it's part of an "
"URL e.g. website/company"
msgstr ""
"Viết hoa từ <strong>YourCompany</strong>, trừ khi nó là một phần của URL. "
"VD: trang web/công ty"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "Car Policy"
msgstr "Chính sách xe"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/move_article_dialog/move_article_dialog.js:0
msgid "Categories"
msgstr "Danh mục"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_template_category__sequence
msgid "Category Sequence"
msgstr "Trình tự danh mục"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "Causes"
msgstr "Nguyên nhân"

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_account_management
msgid ""
"Centralize account insights in one document for comprehensive monitoring and"
" follow-up."
msgstr ""
"Tập trung thông tin tài khoản vào một tài liệu duy nhất để theo dõi và cập "
"nhật hiệu quả."

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_meeting_minutes
msgid ""
"Centralize team meetings in a single article, while making sure notes are "
"handled efficiently."
msgstr ""
"Tập trung các cuộc họp vào một bài viết duy nhất, đồng thời đảm bảo nội dung"
" ghi chú được xử lý hiệu quả."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Challenges &amp; Competitive Landscape<br>"
msgstr " Thách thức &amp; bối cảnh cạnh tranh<br>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "Change 1"
msgstr "Thay đổi 1"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "Change 2"
msgstr "Thay đổi 2"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "Change 3"
msgstr "Thay đổi 3"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid "Change Permission"
msgstr "Thay đổi quyền"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover.xml:0
msgid "Change cover"
msgstr "Thay đổi ảnh bìa"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__child_ids
msgid "Child Articles and Items"
msgstr "Hạng mục và bài viết phụ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover_dialog.xml:0
msgid "Choose a nice cover"
msgstr "Chọn một ảnh bìa đẹp mắt"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/article_selection_dialog/article_selection_dialog.js:0
#: code:addons/knowledge/static/src/components/move_article_dialog/move_article_dialog.js:0
msgid "Choose an Article..."
msgstr "Chọn một bài viết..."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover.xml:0
msgid "Click and hold to reposition"
msgstr "Bấm và giữ để căn chỉnh"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/core/clipboard/embedded_clipboard.xml:0
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/embedded_clipboard_plugin/embedded_clipboard_plugin.js:0
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Clipboard"
msgstr "Bộ nhớ tạm"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.js:0
#: code:addons/knowledge/static/src/js/knowledge_controller.js:0
#: code:addons/knowledge/static/src/macros/abstract_macro.js:0
msgid "Close"
msgstr "Đóng"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/plugins/comments_plugin/comments_plugin.js:0
msgid "Comment"
msgstr "Bình luận"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Company Abbreviations"
msgstr "Chữ viết tắt thông dụng"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Company Details"
msgstr "Thông tin công ty"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Company Location:"
msgstr "Địa chỉ công ty:"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Company Name:"
msgstr "Tên công ty:"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_company_newsletter
msgid "Company Newsletter"
msgstr "Bản tin email công ty"

#. module: knowledge
#: model:knowledge.article.template.category,name:knowledge.knowledge_article_template_category_company_organization
msgid "Company Organization"
msgstr "Tổ chức công ty"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Company Structure:"
msgstr "Cơ cấu công ty:"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid ""
"Compiled below are tips &amp; tricks collected among our veterans to help "
"newcomers get started. We hope it will help you sign deals."
msgstr ""
"Dưới đây là thông tin tổng hợp về các mẹo &amp; thủ thuật được thu thập từ "
"những nhân viên kỳ cựu để hỗ trợ các nhân viên mới. Chúng tôi hy vọng chúng "
"sẽ giúp bạn ký kết giao dịch thành công."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Complicated buying process"
msgstr "Quy trình mua hàng phức tạp"

#. module: knowledge
#: model:ir.ui.menu,name:knowledge.knowledge_menu_configuration
msgid "Configuration"
msgstr "Cấu hình"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
#: code:addons/knowledge/static/src/views/item_calendar/item_calendar_view.js:0
msgid "Confirmation"
msgstr "Xác nhận"

#. module: knowledge
#: model:ir.model,name:knowledge.model_res_partner
msgid "Contact"
msgstr "Liên hệ"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid "Contact Us"
msgstr "Liên hệ với chúng tôi"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_shared_todos_contact_our_lawyer
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_shared_todos_contact_our_lawyer
msgid "Contact our Lawyer"
msgstr "Liên hệ với luật sư của chúng tôi"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/core/clipboard/embedded_clipboard.js:0
msgid "Content copied to clipboard."
msgstr "Nội dung đã được sao chép vào bộ nhớ tạm."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Contract Due Date:"
msgstr "Ngày hết hạn hợp đồng:"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Convert into Article"
msgstr "Chuyển thành bài viết"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Convert into Article Item"
msgstr "Chuyển thành hạng mục bài viết"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/core/clipboard/embedded_clipboard.xml:0
msgid "Copy"
msgstr "Sao chép"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/embedded_view_link/embedded_view_link_popover.xml:0
msgid "Copy Link"
msgstr "Copy Link"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/core/clipboard/embedded_clipboard.xml:0
msgid "Copy to Clipboard"
msgstr "Sao chép vào bộ nhớ tạm"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Costing too much money"
msgstr "Tốn quá nhiều tiền"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.js:0
msgid ""
"Could not move \"%(icon)s%(title)s\" under "
"\"%(parentIcon)s%(parentTitle)s\", because you do not have write permission "
"on the latter."
msgstr ""
"Không thể di chuyển \"%(icon)s%(title)s\" vào dưới "
"\"%(parentIcon)s%(parentTitle)s\", bởi vì bạn không có quyền viết trên bài "
"viết chính"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid ""
"Could you please let me know on which number I could reach you so that we could get in touch?<br>\n"
"                        It should not take longer than 15 minutes."
msgstr ""
"Bạn có thể vui lòng cho tôi biết tôi có thể liên hệ với bạn qua số điện thoại nào được không?<br>\n"
"                        Hãy yên tâm là cuộc trò chuyện này sẽ không tốn quá 15 phút."

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_cover__attachment_url
msgid "Cover URL"
msgstr "URL ảnh bìa"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_cover__attachment_id
msgid "Cover attachment"
msgstr "Tệp đính kèm ảnh bìa"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__cover_image_url
msgid "Cover url"
msgstr "URL ảnh bìa"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__cover_image_position
msgid "Cover vertical offset"
msgstr "Offset dọc ảnh bìa"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
#: code:addons/knowledge/static/src/xml/knowledge_command_palette.xml:0
msgid "Create \""
msgstr "Tạo \""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/article_selection_dialog/article_selection_dialog.js:0
msgid "Create \"%s\""
msgstr "Tạo \"%s\""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Create a Copy"
msgstr "Tạo một bản sao"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar_row.xml:0
msgid "Create a nested article"
msgstr "Tạo một bài viết lồng nhau"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar_section.xml:0
msgid "Create a new article in workspace"
msgstr "Tạo một bài viết mới trong không gian làm việc"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar_section.xml:0
msgid "Create a new private article"
msgstr "Tạo một bài viết riêng tư mới"

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_product_catalog
msgid ""
"Create a simple catalog to provide technical details about your products."
msgstr ""
"Tạo một danh mục đơn giản để cung cấp chi tiết kỹ thuật về sản phẩm của bạn."

#. module: knowledge
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_action_item_calendar
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_item_action
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_item_action_stages
msgid "Create an Article Item"
msgstr "Tạo một hạng mục bài viết"

#. module: knowledge
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_action
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_form
msgid "Create an article"
msgstr "Tạo một bài viết"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Created"
msgstr "Đã tạo"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__create_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_favorite__create_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_member__create_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_stage__create_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_template_category__create_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__create_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_cover__create_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_invite__create_uid
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_tree
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_tree_items
msgid "Created by"
msgstr "Được tạo bởi"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__create_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_favorite__create_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_member__create_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_stage__create_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_template_category__create_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__create_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_cover__create_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_invite__create_date
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_tree
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_tree_items
msgid "Created on"
msgstr "Được tạo vào"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Current Contract:"
msgstr "Hợp đồng hiện tại:"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Current Satisfaction:"
msgstr "Mức độ hài lòng hiện tại:"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_marketing_customer_personas
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid "Customer Personas"
msgstr "Hình ảnh khách hàng"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "DESIGN PROTOTYPE"
msgstr "MẪU THIẾT KẾ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.js:0
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "Date Properties"
msgstr "Thuộc tính ngày"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.js:0
msgid "Date and Time Properties"
msgstr "Thuộc tính ngày và giờ"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_trash_notification
msgid "Dear"
msgstr "Thân gửi"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "Decision 1"
msgstr "Quyết định 1"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "Decision 2"
msgstr "Quyết định 2"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "Decision 3"
msgstr "Quyết định 3"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "Decision 4"
msgstr "Quyết định 4"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "Default Access Rights"
msgstr "Quyền truy cập mặc định"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "Default Scale"
msgstr "Khung thời gian mặc định"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__internal_permission
msgid ""
"Default permission for all internal users. (External users can still have "
"access to this article if they are added to its members)"
msgstr ""
"Quyền mặc định cho tất cả người dùng nội bộ. (Người dùng bên ngoài vẫn có "
"thể truy cập vào bài viết này nếu họ được thêm làm thành viên của bài viết)"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"Deleted articles are stored in Trash an extra <b>%(threshold)s</b> days\n"
"                 before being permanently removed for your database"
msgstr ""
"Các bài viết đã xóa được lưu trữ trong Thùng rác thêm <b>%(threshold)s</b> ngày\n"
"                 trước khi bị xóa vĩnh viễn khỏi cơ sở dữ liệu của bạn"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__deletion_date
msgid "Deletion Date"
msgstr "Ngày xoá"

#. module: knowledge
#: model:ir.model.constraint,message:knowledge.constraint_knowledge_article_check_permission_on_desync
msgid "Desynchronized articles must have internal permission."
msgstr "Các bài viết huỷ đồng bộ phải có quyền nội bộ."

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__is_desynchronized
msgid "Desyncronized with parents"
msgstr "Huỷ đồng bộ hóa với phần chính"

#. module: knowledge
#: model_terms:digest.tip,tip_description:knowledge.digest_tip_knowledge_0
msgid ""
"Did you know that access rights can be defined per user on any Knowledge "
"Article?"
msgstr ""
"Bạn có biết rằng bạn có thể xác định quyền truy cập cho từng người dùng trên"
" bất kỳ Bài viết kiến ​​thức nào không?"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/article_template_picker_dialog/article_template_picker_dialog.xml:0
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover_dialog.xml:0
#: code:addons/knowledge/static/src/components/prompt_embedded_view_name_dialog/prompt_embedded_view_name_dialog.xml:0
#: code:addons/knowledge/static/src/editor/embedded_components/backend/embedded_view_link/embedded_view_link_edit_dialog.xml:0
msgid "Discard"
msgstr "Huỷ bỏ"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__display_name
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_favorite__display_name
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_member__display_name
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_stage__display_name
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_template_category__display_name
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__display_name
#: model:ir.model.fields,field_description:knowledge.field_knowledge_cover__display_name
#: model:ir.model.fields,field_description:knowledge.field_knowledge_invite__display_name
msgid "Display Name"
msgstr "Tên hiển thị"

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_brand_assets
msgid ""
"Distribute brand digital assets while ensuring compliance with company "
"policies and guidelines."
msgstr ""
"Phân phối tài sản kỹ thuật số của thương hiệu đồng thời đảm bảo tuân thủ các"
" chính sách và nguyên tắc của công ty."

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid "Diving in"
msgstr "Bước đầu tìm hiểu"

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_marketing_campaign_brief
msgid ""
"Document your Marketing Campaigns to prioritize key objectives and outcomes."
msgstr ""
"Lưu lại chiến dịch marketing của bạn để ưu tiên các mục tiêu chính và kết "
"quả."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid ""
"Don't forget to spread the word, we're <em>so</em> looking forward to "
"unveiling this new Odoo version! 🥳"
msgstr ""
"Đừng quên chia sẻ thông tin này nhé! Chúng tôi <em>vô cùng</em> mong đợi đến"
" ngày ra mắt phiên bản Odoo mới này! 🥳"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
#: model:knowledge.article.stage,name:knowledge.knowledge_article_template_stage_done
msgid "Done"
msgstr "Hoàn tất"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.xml:0
msgid "Drop here to delete this article"
msgstr "Thả vào đây để xóa bài viết này"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/views/list_view.js:0
msgid "Duplicate"
msgstr "Nhân bản"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_mail_invite
msgid "EDIT"
msgstr "CHỈNH SỬA"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/embedded_view_actions_menu/embedded_view_actions_menu.xml:0
msgid "Edit"
msgstr "Chỉnh sửa"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/embedded_view_link/embedded_view_link_edit_dialog.xml:0
#: code:addons/knowledge/static/src/editor/embedded_components/backend/embedded_view_link/embedded_view_link_popover.xml:0
msgid "Edit Link"
msgstr "Chỉnh sửa liên kết"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid "Education:"
msgstr "Học vấn:"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
msgid "Education: Bachelor's degree in Marketing"
msgstr "Học vấn: Cử nhân Marketing"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
msgid "Education: High school diploma"
msgstr "Học vấn: Tốt nghiệp cấp 3"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
msgid "Education: PhD."
msgstr "Học vấn: Tiến sĩ"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
msgid "Education: Student"
msgstr "Học vấn: Sinh viên"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid "Egg'cellent run"
msgstr "Giải chạy hấp dẫn"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Email:"
msgstr "Email:"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/prompt_embedded_view_name_dialog/prompt_embedded_view_name_dialog.js:0
msgid "Embed a View"
msgstr "Nhúng một chế độ xem"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__icon
msgid "Emoji"
msgstr "Emoji"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.js:0
msgid "End Date Time"
msgstr "Ngày giờ kết thúc"

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_software_specification
msgid ""
"Ensure all stakeholders of a product change are aligned by clearly "
"communicating the requirements."
msgstr ""
"Đảm bảo tất cả các bên liên quan đến việc thay đổi sản phẩm đều thống nhất "
"với nhau bằng cách truyền đạt rõ ràng các yêu cầu."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
#: code:addons/knowledge/static/src/macros/abstract_macro.js:0
msgid "Error"
msgstr "Lỗi"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Estimated Revenues:"
msgstr "Doanh thu ước tính"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid "Events 🌍"
msgstr "Sự kiện 🌍"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "Everyone"
msgstr "Mọi người"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "Expense Policy"
msgstr "Chính sách chi phí"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Export"
msgstr "Xuất"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_invite_view_form
msgid "Extra Comment"
msgstr "Bình luận bổ sung"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "Extra Technical Instructions:"
msgstr "Chỉ dẫn kỹ thuật bổ sung:"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_favorite_view_form
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search
msgid "Favorite"
msgstr "Yêu thích"

#. module: knowledge
#: model:ir.model,name:knowledge.model_knowledge_article_favorite
msgid "Favorite Article"
msgstr "Bài viết yêu thích"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__favorite_ids
msgid "Favorite Articles"
msgstr "Bài viết yêu thích"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar_section.xml:0
#: model:ir.actions.act_window,name:knowledge.knowledge_article_favorite_action
#: model:ir.ui.menu,name:knowledge.knowledge_article_favorite_menu
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_favorite_view_tree
msgid "Favorites"
msgstr "Danh sách yêu thích"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.portal_my_home_knowledge
msgid "Find all articles shared with you"
msgstr "Tìm tất cả các bài viết được chia sẻ với bạn"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_stage__fold
msgid "Folded in kanban view"
msgstr "Thu gọn trong kanban"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__message_follower_ids
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__message_follower_ids
msgid "Followers"
msgstr "Người theo dõi"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__message_partner_ids
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__message_partner_ids
msgid "Followers (Partners)"
msgstr "Người theo dõi (Đối tác)"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font biểu tượng, ví dụ: fa-tasks"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid ""
"For all other categories, we simply require you to follow the rules listed "
"below."
msgstr ""
"Đối với tất cả hạng mục, chúng tôi chỉ cần bạn tuân theo các quy tắc được "
"liệt kê dưới đây."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Full Width"
msgstr "Trải hết chiều rộng"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__full_width
msgid "Full width"
msgstr "Trải hết chiều rộng"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid "Gender(s):"
msgstr "Giới tính:"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
msgid "Gender(s): M"
msgstr "Giới tính: Nam"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
msgid "Gender(s): W"
msgstr "Giới tính: Nữ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.xml:0
msgid "Generate an Article with AI"
msgstr "Tạo một bài viết bằng AI"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_favorite_view_search
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_member_view_search
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_stage_view_search
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_template_view_search
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search_items
msgid "Group By"
msgstr "Nhóm theo"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "Guest"
msgstr "Khách"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_hr_faq
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "HR FAQ"
msgstr "Câu hỏi thường gặp về nhân sự"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__user_has_access
msgid "Has Access"
msgstr "Có quyền truy cập"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__has_message
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__has_message
msgid "Has Message"
msgstr "Có tin nhắn"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__user_has_write_access
msgid "Has Write Access"
msgstr "Có quyền viết"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__has_item_children
msgid "Has article item children?"
msgstr "Có phần phụ của hạng mục bài viết?"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__has_article_children
msgid "Has normal article children?"
msgstr "Có phần phụ của bài viết bình thường?"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__user_has_access_parent_path
msgid ""
"Has the user access to each parent from current article until its root?"
msgstr ""
"Người dùng có quyền truy cập vào từng bài viết chính tính từ bài viết hiện "
"tại cho đến bài viết gốc của nó?"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_invite__have_share_partners
msgid "Have Share Partners"
msgstr "Có đối tác chia sẻ"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid "Hello there"
msgstr "Xin chào"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"Hello there, I am a template 👋\n"
"            <br/>\n"
"            Use the buttons at the top-right of this box to re-use my content.\n"
"            <br/>\n"
"            No more time wasted! 🔥"
msgstr ""
"Xin chào! Mình là một bản mẫu. 👋\n"
"            <br/>\n"
"            Hãy sử dụng các nút ở phía trên bên phải ô này để sử dụng lại nội dung của mình nhé.\n"
"            <br/>\n"
"            Bạn sẽ tiết kiệm được nhiều thời gian đó! 🔥"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid ""
"Here are logos that you can use at your own convenience.\n"
"                <br>\n"
"                They can also be shared with customers, journalists and resellers."
msgstr ""
"Đây là những logo mà bạn có thể thoải mái sử dụng.\n"
"                <br>\n"
"                Chúng cũng có thể được chia sẻ với khách hàng, cánh nhà báo, và các đơn vị bán lại."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid ""
"Here is a short guide that will help you pick the right tiny house for you."
msgstr ""
"Dưới đây là hướng dẫn ngắn có thể giúp bạn chọn được ngôi nhà nhỏ phù hợp "
"với mình."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Hey ProspectName,"
msgstr "Xin chào TênKháchhàngTiềmnăng,"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/xml/knowledge_command_palette.xml:0
msgid "Hidden"
msgstr "Ẩn"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comments_panel/comments_panel.xml:0
msgid "Highlight content and use the"
msgstr "Tô màu nội dung và sử dụng"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__html_field_history
msgid "History data"
msgstr "Dữ liệu lịch sử"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__html_field_history_metadata
msgid "History metadata"
msgstr "Siêu dữ liệu lịch sử"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.xml:0
#: model:ir.ui.menu,name:knowledge.knowledge_menu_home
msgid "Home"
msgstr "Trang chủ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "Hours Display"
msgstr "Hiển thị giờ"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_product_catalog_house_cielo
msgid "House Model \"Cielo\""
msgstr "Mẫu nhà \"Cielo\""

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_product_catalog_house_dolcezza
msgid "House Model \"Dolcezza\""
msgstr "Mẫu nhà \"Dolcezza\""

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_product_catalog_house_incanto
msgid "House Model \"Incanto\""
msgstr "Mẫu nhà \"Incanto\""

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_product_catalog_house_serenita
msgid "House Model \"Serenità\""
msgstr "Mẫu nhà \"Serenità\""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog_house_cielo
msgid "House Model - Cielo"
msgstr "Mẫu nhà - Cielo"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog_house_dolcezza
msgid "House Model - Dolcezza"
msgstr "Mẫu nhà - Dolcezza"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog_house_incanto
msgid "House Model - Incanto"
msgstr "Mẫu nhà - Incanto"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog_house_serenita
msgid "House Model - Serenità"
msgstr "Mẫu nhà - Serenità"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "How do I know which model I can order or not?"
msgstr "Làm sao để biết tôi có thể đặt mẫu xe nào?"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "How they measure success:"
msgstr "Cách họ đo lường thành công:"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid "How to find the perfect model for your needs 😍"
msgstr "Làm thế nào để tìm thấy mẫu hoàn toàn phù hợp với nhu cầu của bạn 😍"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid ""
"I was doing some research online and found your company.<br>\n"
"                        Considering we just launched ProductName, I was thinking you would be interested."
msgstr ""
"Tôi đã thấy công ty của bạn trong khi thực hiện một số nghiên cứu online.<br>\n"
"                        Chúng tôi vừa ra mắt TênSảnphẩm, và tôi cho rằng bạn sẽ quan tâm đến sản phẩm này."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "I will not steal prospects from colleagues"
msgstr "Tôi sẽ không đánh cắp khách hàng tiềm năng từ đồng nghiệp"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "I will not waste time and energy bad-mouthing competitors"
msgstr "Tôi sẽ không lãng phí thời gian và sức lực để nói xấu đối thủ"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "I will only sell a project if I am convinced it can be a success"
msgstr "Tôi sẽ chỉ bán một dự án nếu tôi tin rằng nó có thể thành công"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__id
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_favorite__id
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_member__id
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_stage__id
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_template_category__id
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__id
#: model:ir.model.fields,field_description:knowledge.field_knowledge_cover__id
#: model:ir.model.fields,field_description:knowledge.field_knowledge_invite__id
msgid "ID"
msgstr "ID"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__activity_exception_icon
msgid "Icon"
msgstr "Biểu tượng"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Biểu tượng cho thấy một hoạt động ngoại lệ."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Identify the pain points and offer clear solutions"
msgstr "Xác định các điểm yếu và đưa ra giải pháp rõ ràng"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__message_needaction
#: model:ir.model.fields,help:knowledge.field_knowledge_article_thread__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Nếu chọn, bạn cần chú ý tới các tin nhắn mới."

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__message_has_error
#: model:ir.model.fields,help:knowledge.field_knowledge_article__message_has_sms_error
#: model:ir.model.fields,help:knowledge.field_knowledge_article_thread__message_has_error
#: model:ir.model.fields,help:knowledge.field_knowledge_article_thread__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Nếu chọn, một số tin nhắn sẽ có lỗi gửi."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid ""
"If none of those offers convinced you, just get in touch with our "
"team.<br>At MyCompany, your happiness is our utmost priority, and we'll go "
"the extra mile to make sure you find what you're looking for!"
msgstr ""
"Nếu bạn chưa hài lòng với bất kỳ đề xuất nào trong số đó, chỉ cần liên hệ "
"với chúng tôi.<br>Tại MyCompany, chúng tôi đặt hạnh phúc của bạn lên làm ưu "
"tiên hàng đầu và sẽ nỗ lực hơn nữa để đảm bảo bạn tìm ra những gì mình đang "
"tìm kiếm!"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__is_desynchronized
msgid ""
"If set, this article won't inherit access rules from its parents anymore."
msgstr ""
"Nếu cài đặt, bài viết này sẽ không kế thừa các quy tắc truy cập từ bài viết "
"chính của nó."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "Impact"
msgstr "Hậu quả"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_release_note
msgid "Improvements 🔬"
msgstr "Cải tiến 🔬"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid "Income:"
msgstr "Thu nhập:"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
msgid "Income: $109,160"
msgstr "Thu nhập: 109.160$"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
msgid "Income: $142,170"
msgstr "Thu nhập: 142.170$"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
msgid "Income: $293,650"
msgstr "Thu nhập: 293.650$"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
msgid "Income: $68,170"
msgstr "Thu nhập: 68.170$"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Inconsistent customer experience"
msgstr "Trải nghiệm khách hàng không nhất quán"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/article_index_plugin/article_index_plugin.js:0
msgid "Index"
msgstr "Chỉ số"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid "Industrial ⚙-"
msgstr "Công nghiệp ⚙-"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Industry:"
msgstr "Ngành:"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "Inform HR and your Team Leader."
msgstr "Thông báo cho bộ phận nhân sự và Trưởng nhóm của bạn."

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_release_note
msgid "Inform users about your latest software updates and improvements."
msgstr ""
"Thông báo cho người dùng về các bản cập nhật và cải tiến phần mềm mới nhất "
"của bạn."

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__inherited_permission
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_member__article_permission
msgid "Inherited Permission"
msgstr "Quyền được kế thừa"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__inherited_permission_parent_id
msgid "Inherited Permission Parent Article"
msgstr "Bài viết chính có quyền được kế thừa"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
#: code:addons/knowledge/static/src/components/prompt_embedded_view_name_dialog/prompt_embedded_view_name_dialog.xml:0
msgid "Insert"
msgstr "Chèn"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/plugins/article_plugin/article_plugin.js:0
msgid "Insert Link"
msgstr "Chèn liên kết"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "Insert a Calendar View"
msgstr "Chèn chế độ xem lịch"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/embedded_view_plugin/embedded_view_plugin.js:0
msgid "Insert a Calendar view of article items"
msgstr "Chèn chế độ xem lịch của các hạng mục bài viết"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/embedded_view_plugin/embedded_view_plugin.js:0
msgid "Insert a Card view of article items"
msgstr "Chèn chế độ xem thẻ của các hạng mục bài viết"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/prompt_embedded_view_name_dialog/prompt_embedded_view_name_dialog.js:0
msgid "Insert a Kanban View"
msgstr "Chèn chế độ xem Kanban"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/embedded_view_plugin/embedded_view_plugin.js:0
msgid "Insert a Kanban view of article items"
msgstr "Chèn chế độ xem kanban của các hạng mục bài viết"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/prompt_embedded_view_name_dialog/prompt_embedded_view_name_dialog.js:0
msgid "Insert a List View"
msgstr "Chèn chế độ xem danh sách"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/embedded_view_plugin/embedded_view_plugin.js:0
msgid "Insert a List view of article items"
msgstr "Chèn chế độ xem danh sách của các hạng mục bài viết"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/plugins/article_plugin/article_plugin.js:0
msgid "Insert an Article shortcut"
msgstr "Chèn lối tắt bài viết"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/external_embedded_view_favorite_menu/insert_embedded_view.xml:0
msgid "Insert link in article"
msgstr "Chèn liên kết vào bài viết"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/external_embedded_view_favorite_menu/insert_embedded_view.xml:0
msgid "Insert view in article"
msgstr "Chèn chế độ xem vào bài viết"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid ""
"Instead of setting up complicated processes, <strong>we prefer to let our "
"employees buy whatever they need</strong>. Just fill in an expense and we "
"will reimburse you."
msgstr ""
"Thay vì tạo ra các quy trình phức tạp, <strong>chúng tôi muốn để nhân viên "
"mua bất cứ thứ gì họ cần.</strong> Sau đó, chỉ cần điền vào một phiếu chi "
"phí và chúng tôi sẽ hoàn trả cho bạn."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid "Interests:"
msgstr "Sở thích:"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
msgid "Interests: Cooking"
msgstr "Sở thích: Nấu ăn"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
msgid "Interests: Music"
msgstr "Sở thích: Âm nhạc"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
msgid "Interests: Politics"
msgstr "Sở thích: Chính trị"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
msgid "Interests: Science"
msgstr "Sở thích: Khoa học"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__internal_permission
msgid "Internal Permission"
msgstr "Quyền nội bộ"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "Invitation to access an article"
msgstr "Lời mời truy cập một bài viết"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_invite_view_form
msgid "Invite"
msgstr "Mời"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "Invite People"
msgstr "Mời người"

#. module: knowledge
#: model:ir.actions.act_window,name:knowledge.knowledge_invite_action_from_article
msgid "Invite people"
msgstr "Mời người"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_favorite__is_article_active
msgid "Is Article Active"
msgstr "Là bài viết đang hoạt động"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__is_user_favorite
msgid "Is Favorited"
msgstr "Được yêu thích"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__message_is_follower
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__message_is_follower
msgid "Is Follower"
msgstr "Là người theo dõi"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__is_article_item
msgid "Is Item?"
msgstr "Là hạng mục?"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__is_template
msgid "Is Template"
msgstr "Là mẫu"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__has_item_parent
msgid "Is the parent an Item?"
msgstr "Phần chính có phải là một hạng mục không?"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "Issue Summary"
msgstr "Tóm tắt vấn đề"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Issues they are trying to solve:"
msgstr "Vấn đề họ đang cố gắng giải quyết:"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__template_category_sequence
#: model:ir.model.fields,help:knowledge.field_knowledge_article_template_category__sequence
msgid "It determines the display order of the category"
msgstr "Nó xác định thứ tự hiển thị của danh mục"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__template_sequence
msgid "It determines the display order of the template within its category"
msgstr "Nó xác định thứ tự hiển thị của mẫu trong danh mục"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "Item 1"
msgstr "Mục 1"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "Item 2"
msgstr "Mục 2"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "Item 3"
msgstr "Mục 3"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "Item 4"
msgstr "Mục 4"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/embedded_view_plugin/embedded_view_plugin.js:0
msgid "Item Calendar"
msgstr "Lịch hạng mục"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/embedded_view_plugin/embedded_view_plugin.js:0
msgid "Item Cards"
msgstr "Thẻ hạng mục"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/embedded_view_plugin/embedded_view_plugin.js:0
msgid "Item Kanban"
msgstr "Kanban hạng mục"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/embedded_view_plugin/embedded_view_plugin.js:0
msgid "Item List"
msgstr "Danh sách hạng mục"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__stage_id
msgid "Item Stage"
msgstr "Giai đoạn hạng mục"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_calendar_items
msgid "Items"
msgstr "Hạng mục"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Job Position:"
msgstr "Chức vụ:"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_form
msgid "Join"
msgstr "Tham gia"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar_section.xml:0
msgid "Join a hidden article"
msgstr "Tham gia một bài viết ẩn"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
msgid "Julius"
msgstr "Julius"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.js:0
msgid "Kanban of %s"
msgstr "Kanban %s"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.js:0
msgid "Kanban of Article Items"
msgstr "Kanban hạng mục bài viết"

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_shared_todos
msgid "Keep track of your company to-dos and share them with your colleagues."
msgstr ""
"Theo dõi việc cần làm của công ty và chia sẻ chúng với đồng nghiệp của bạn."

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_company_newsletter
msgid ""
"Keep your colleagues informed about the company's latest developments and "
"activities through periodic updates."
msgstr ""
"Thông báo cho đồng nghiệp của bạn về những phát triển và hoạt động mới nhất "
"của công ty thông qua các bản cập nhật định kỳ."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/external_embedded_view_favorite_menu/insert_embedded_view.xml:0
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/article_index_plugin/article_index_plugin.js:0
#: model:ir.ui.menu,name:knowledge.knowledge_menu_root
#: model:ir.ui.menu,name:knowledge.knowledge_menu_technical
#: model_terms:ir.ui.view,arch_db:knowledge.portal_my_home_knowledge
msgid "Knowledge"
msgstr "Kiến thức"

#. module: knowledge
#: model:ir.model,name:knowledge.model_knowledge_article
msgid "Knowledge Article"
msgstr "Bài viết kiến thức"

#. module: knowledge
#: model:ir.model,name:knowledge.model_knowledge_cover
msgid "Knowledge Cover"
msgstr "Ảnh bìa Kiến thức"

#. module: knowledge
#: model:ir.model,name:knowledge.model_knowledge_invite
msgid "Knowledge Invite Wizard"
msgstr "Công cụ mời Kiến thức"

#. module: knowledge
#: model:ir.model,name:knowledge.model_knowledge_article_stage
msgid "Knowledge Stage"
msgstr "Giai đoạn Kiến thức"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_tree
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_tree_items
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_tree_trash
msgid "Last Edit Date"
msgstr "Ngày chỉnh sửa lần cuối"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Last Edited"
msgstr "Chỉnh sửa lần cuối"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__last_edition_uid
msgid "Last Edited by"
msgstr "Chỉnh sửa lần cuối bởi"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__last_edition_date
msgid "Last Edited on"
msgstr "Chỉnh sửa lần cuối vào"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__write_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_favorite__write_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_member__write_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_stage__write_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_template_category__write_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__write_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_cover__write_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_invite__write_uid
msgid "Last Updated by"
msgstr "Cập nhật lần cuối bởi"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__write_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_favorite__write_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_member__write_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_stage__write_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_template_category__write_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__write_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_cover__write_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_invite__write_date
msgid "Last Updated on"
msgstr "Cập nhật lần cuối vào"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid "Leave"
msgstr "Rời khỏi"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "Leave Article"
msgstr "Rời khỏi bài viết"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid "Leave Private Article"
msgstr "Rời khỏi bài viết riêng tư"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "Leaves &amp; Time Off"
msgstr "Nghỉ phép"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "Let your Team Leader know in advance."
msgstr "Hãy báo trước cho Trưởng nhóm của bạn."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/core/embedded_view_link/embedded_view_link_style.js:0
msgid "Link"
msgstr "Liên kết"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/plugins/article_plugin/article_plugin.js:0
msgid "Link an Article"
msgstr "Liên kết một bài viết"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/embedded_view_link_plugin/embedded_view_link_plugin.js:0
msgid "Link copied to clipboard."
msgstr "Liên kết được sao chép vào bộ nhớ tạm."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.js:0
msgid "List of %s"
msgstr "Danh sách %s"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.js:0
msgid "List of Article Items"
msgstr "Danh sách của hạng mục bài viết"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comments_panel/comments_panel.xml:0
msgid "Load More Discussions"
msgstr "Tải thêm cuộc thảo luận"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/article_template_picker_dialog/article_template_picker_dialog.xml:0
msgid "Load Template"
msgstr "Tải mẫu"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.xml:0
msgid "Load a Template"
msgstr "Tải một mẫu"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Lock Content"
msgstr "Khoá nội dung"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__is_locked
msgid "Locked"
msgstr "Đã khoá"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"Logging changes from %(partner_name)s without write access on article "
"%(article_name)s due to hierarchy tree update"
msgstr ""
"Ghi nhật ký thay đổi từ %(partner_name)s không có quyền viết trên bài viết "
"%(article_name)s do cập nhật cây phân cấp"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "Logo"
msgstr "Logo"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "Logos should only be used in the colors provided"
msgstr "Logo chỉ được sử dụng ở các màu sắc được cung cấp"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid "Lose Access"
msgstr "Mất quyền truy cập"

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_personal_organizer
msgid ""
"Make every week a success by proactively organizing your priority and "
"optional tasks."
msgstr ""
"Hãy biến mỗi tuần thành một trái ngọt bằng cách chủ động sắp xếp các nhiệm "
"vụ ưu tiên và nhiệm vụ không bắt buộc."

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_sprint_calendar
msgid "Manage your team schedule for the upcoming sprint."
msgstr "Quản lý lịch làm việc của bộ phận cho cuộc chạy nước rút sắp tới."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article_thread.py:0
msgid "Mark Comment as Closed"
msgstr "Đánh dấu bình luận là đã đóng"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/mail/message/message_actions.js:0
msgid "Mark the discussion as resolved"
msgstr "Đánh dấu cuộc thảo luận là đã xử lý"

#. module: knowledge
#: model:knowledge.article.template.category,name:knowledge.knowledge_article_template_category_marketing
msgid "Marketing"
msgstr "Marketing"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_marketing_campaign_brief
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_campaign_brief
msgid "Marketing Campaign Brief"
msgstr "Tóm tắt chiến dịch marketing"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_sprint_calendar_meeting
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sprint_calendar_meeting
msgid "Meeting"
msgstr "Họp"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_meeting_example_branding
msgid "Meeting Example"
msgstr "Ví dụ cuộc họp"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_meeting_minutes
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_meeting_minutes_template
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes
msgid "Meeting Minutes"
msgstr "Biên bản cuộc họp"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_example_branding
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "Meeting Minutes Template"
msgstr "Mẫu biên bản cuộc họp"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_member_view_form
msgid "Member"
msgstr "Thành viên"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
#: model:ir.actions.act_window,name:knowledge.knowledge_article_member_action
#: model:ir.ui.menu,name:knowledge.knowledge_article_member_menu
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_member_view_tree
msgid "Members"
msgstr "Thành viên"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__article_member_ids
msgid "Members Information"
msgstr "Thông tin thành viên"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__root_article_id
msgid "Menu Article"
msgstr "Bài viết menu"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_invite__message
msgid "Message"
msgstr "Tin nhắn"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__message_has_error
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__message_has_error
msgid "Message Delivery error"
msgstr "Lỗi gửi tin nhắn"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/mail/message/message_model_patch.js:0
msgid "Message Link Copied!"
msgstr "Liên kết tin nhắn đã được sao chép!"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/mail/message/message_model_patch.js:0
msgid "Message Link Copy Failed (Permission denied?)!"
msgstr "Sao chép liên kết tin nhắn không thành công (Quyền bị từ chối?)!"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__message_ids
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__message_ids
msgid "Messages"
msgstr "Tin nhắn"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/views/item_calendar/item_calendar_views.xml:0
msgid "Missing Calendar configuration."
msgstr "Thiếu cấu hình lịch."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "More actions"
msgstr "Thêm tác vụ"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid ""
"More than 150 Odooers participated in this years' edition of the run of 5 or 11\n"
"                            kilometers.<br>Starting from the office, they enjoyed a great tour in the countryside before\n"
"                            coming back to Grand-Rosière, where they were welcomed with a drink and a burger."
msgstr ""
"Hơn 150 Odooer đã tham gia giải chạy 5 hoặc 11 km của năm nay.\n"
"                            <br>Bắt đầu từ văn phòng công ty, các Odooer đã tận hưởng cung đường tuyệt vời qua vùng nông thôn trước khi \n"
"                            quay trở lại Grand-Rosière, nơi đồ uống và bánh mì kẹp thịt chào đón họ."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/move_article_dialog/move_article_dialog.js:0
msgid "Move \"%s\" under:"
msgstr "Chuyển \"%s\" vào dưới:"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/move_article_dialog/move_article_dialog.xml:0
msgid "Move Article"
msgstr "Chuyển bài viết"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Move To"
msgstr "Chuyển đến"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/move_article_dialog/move_article_dialog.xml:0
msgid "Move an Article"
msgstr "Chuyển một bài viết"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.js:0
msgid "Move cancelled"
msgstr "Di chuyển đã bị huỷ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/move_article_dialog/move_article_dialog.xml:0
msgid "Move the untitled article under:"
msgstr "Chuyển bài viết không có tiêu đề vào dưới:"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.js:0
msgid "Move to Private"
msgstr "Chuyển vào mục riêng tư"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.js:0
msgid "Move to Shared"
msgstr "Chuyển tới Chia sẻ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid "Move to Trash"
msgstr "Chuyển vào thùng rác"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.js:0
msgid "Move to Workspace"
msgstr "Chuyển vào không gian làm việc"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Hạn chót hoạt động của tôi"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search_items
msgid "My Favorites"
msgstr "Danh sách yêu thích của tôi"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "My Forecast will always be accurate and up-to-date"
msgstr "Dự báo của tôi sẽ luôn chính xác và được cập nhật kịp thời"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search_items
msgid "My Items"
msgstr "Hạng mục của tôi"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_form_item_quick_create
msgid "My New Item"
msgstr "Hạng mục mới của tôi"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/embedded_view_link/embedded_view_link_edit_dialog.xml:0
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_stage__name
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_template_view_tree
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_tree
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_tree_items
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_tree_trash
msgid "Name"
msgstr "Tên"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid "Natural Style ☘ -"
msgstr "Phong cách thiên nhiên ☘ -"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid "Navigation Basics 🐣"
msgstr "Điều hướng cơ bản 🐣"

#. module: knowledge
#. odoo-javascript
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
#: code:addons/knowledge/static/src/js/knowledge_controller.js:0
#: code:addons/knowledge/static/src/views/item_calendar/item_calendar_views.xml:0
#: model:knowledge.article.stage,name:knowledge.knowledge_article_template_stage_new
msgid "New"
msgstr "Mới"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_release_note
msgid "New Features 🎉"
msgstr "Tính năng mới 🎉"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article_thread.py:0
msgid "New Mention in %s"
msgstr "Đề cập mới trong %s"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.js:0
msgid "New property could not be created."
msgstr "Không thể tạo thuộc tính mới."

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Sự kiện trên lịch cho hoạt động tiếp theo"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Hạn chót cho hoạt động tiếp theo"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__activity_summary
msgid "Next Activity Summary"
msgstr "Tóm tắt hoạt động tiếp theo"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__activity_type_id
msgid "Next Activity Type"
msgstr "Loại hoạt động tiếp theo"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "Next Meeting: <font class=\"text-400\">@Date</font>"
msgstr "Cuộc họp tiếp theo: <font class=\"text-400\">@Ngày</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_example_branding
msgid "Next Meeting: <u>6th May, @John's Office</u>"
msgstr "Cuộc họp tiếp theo: <u>Ngày 6/5, @Văn phòng của John</u>"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/xml/knowledge_command_palette.xml:0
msgid "No Article found."
msgstr "Không tìm thấy bài viết nào."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/webclient/commands/knowledge_providers.js:0
msgid "No Article found. Create \"%s\""
msgstr "Không tìm thấy bài viết nào. Tạo \"%s\""

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "No Article in Trash"
msgstr "Không có bài viết nào trong thùng rác"

#. module: knowledge
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_favorite_action
msgid "No Favorites yet!"
msgstr "Chưa có danh sách yêu thích nào!"

#. module: knowledge
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_member_action
msgid "No Members yet!"
msgstr "Chưa có thành viên nào!"

#. module: knowledge
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article__inherited_permission__none
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article__internal_permission__none
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article_member__permission__none
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_invite__permission__none
msgid "No access"
msgstr "Không có quyền truy cập"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/webclient/commands/knowledge_providers.js:0
msgid "No article found."
msgstr "Không tìm thấy bài viết nào."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/core/article_index/readonly_article_index.xml:0
msgid "No article to display"
msgstr "Không có bài viết để hiển thị"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_form
msgid "No article yet."
msgstr "Chưa có bài viết nào."

#. module: knowledge
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_stage_action
msgid "No stage yet!"
msgstr "Chưa có giai đoạn nào!"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/article_template_picker_dialog/article_template_picker_dialog.xml:0
msgid "No template yet."
msgstr "Chưa có mẫu nào."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comments_panel/comments_panel.xml:0
msgid "Nothing going on!"
msgstr "Không có gì xảy ra!"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid ""
"Nth <strong>Q</strong>uarter of the fiscal year.<br>\n"
"                            <em>E.g. Q4 starts on Oct. 1 and ends on Dec. 31.</em>"
msgstr ""
"<strong>Q</strong>ý của năm tài chính.<br>\n"
"                            <em>VD: Q4 bắt đầu vào ngày 01/10 và kết thúc vào ngày 31/12.</em>"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__message_needaction_counter
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__message_needaction_counter
msgid "Number of Actions"
msgstr "Số lượng tác vụ"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__message_has_error_counter
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__message_has_error_counter
msgid "Number of errors"
msgstr "Số lượng lỗi"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__message_needaction_counter
#: model:ir.model.fields,help:knowledge.field_knowledge_article_thread__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Số tin nhắn cần xử lý"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__message_has_error_counter
#: model:ir.model.fields,help:knowledge.field_knowledge_article_thread__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Số tin nhắn bị gửi lỗi"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Objectives with our Collaboration"
msgstr "Mục tiêu với sự hợp tác của chúng ta"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_example_branding
msgid "Odoo Brand Assets"
msgstr "Tài sản thương hiệu Odoo"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid "Odoo Experience 🎉"
msgstr "Odoo Experience 🎉"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_example_branding
msgid "Odoo: Manage your SME online"
msgstr "Odoo: Quản lý online SME của bạn"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
#: model:knowledge.article.stage,name:knowledge.knowledge_article_template_stage_ongoing
msgid "Ongoing"
msgstr "Đang diễn ra"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "Only internal users are allowed to alter memberships."
msgstr "Chỉ người dùng nội bộ mới được phép thay đổi tư cách thành viên."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "Only internal users are allowed to create workspace root articles."
msgstr ""
"Chỉ người dùng nội bộ mới được phép tạo các bài viết gốc trong không gian "
"làm việc."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "Only internal users are allowed to modify this information."
msgstr "Chỉ người dùng nội bộ mới được phép sửa đổi thông tin này."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "Only internal users are allowed to remove memberships."
msgstr "Chỉ người dùng nội bộ mới được phép gỡ tư cách thành viên."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"Only internal users are allowed to restore the original article access "
"information."
msgstr ""
"Chỉ người dùng nội bộ mới được phép khôi phục thông tin quyền truy cập bài "
"viết gốc."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/webclient/commands/knowledge_providers.js:0
msgid "Oops, there's nothing here. Try another search."
msgstr "Rất tiếc, không có gì ở đây cả. Hãy thử từ khoá tìm kiếm khác."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/embedded_view_actions_menu/embedded_view_actions_menu.xml:0
#: code:addons/knowledge/static/src/components/sidebar/sidebar.js:0
#: code:addons/knowledge/static/src/views/item_calendar/item_calendar_views.xml:0
msgid "Open"
msgstr "Mở"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comments_panel/comments_panel.xml:0
msgid "Open Discussions"
msgstr "Mở cuộc thảo luận"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Open comments panel"
msgstr "Mở bảng bình luận"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Open history"
msgstr "Mở lịch sử"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.xml:0
msgid "Open the Trash"
msgstr "Mở thùng rác"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_example_branding
msgid "OpenERP becomes Odoo"
msgstr "OpenERP trở thành Odoo"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/properties_panel/properties_panel.xml:0
msgid ""
"Organize your database with custom fields\n"
"                        (Text, Selection, ...)."
msgstr ""
"Sắp xếp cơ sở dữ liệu của bạn với các trường tùy chỉnh\n"
"                        (Văn bản, Lựa chọn, ...)."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_shared_todos
msgid "Otherwise, feel free to handle others listed below:"
msgstr ""
"Nếu không, vui lòng thực hiện các nhiệm vụ khác được liệt kê dưới đây:"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid ""
"Our catalog can be found here and is updated every 2 years. If you do not "
"manage to find the specific model you are looking for,"
msgstr ""
"Danh mục của chúng tôi nằm ở đây và được cập nhật 2 năm một lần. Nếu không "
"thấy mẫu cụ thể mà bạn đang tìm kiếm,"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "Outcome"
msgstr "Kết quả"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_stage__parent_id
msgid "Owner Article"
msgstr "Bài viết sở hữu"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid "PLANET ODOO"
msgstr "HÀNH TINH ODOO"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Pain Points (<em>tick the relevant ones</em>)"
msgstr "Điểm yếu (<em>đánh dấu vào mục liên quan</em>)"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__parent_id
msgid "Parent Article"
msgstr "Bài viết chính"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__parent_path
msgid "Parent Path"
msgstr "Tuyến chính"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_template_view_form
msgid "Parent Template"
msgstr "Mẫu chính"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_member__partner_id
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_member_view_search
msgid "Partner"
msgstr "Đối tác"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_shared_todos_pay_the_electricity_bill
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_shared_todos_pay_the_electricity_bill
msgid "Pay the Electricity Bill"
msgstr "Thanh toán hóa đơn tiền điện"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_member__permission
#: model:ir.model.fields,field_description:knowledge.field_knowledge_invite__permission
msgid "Permission"
msgstr "Quyền"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_marketing_customer_persona_1
msgid "Persona 1"
msgstr "Khách hàng 1"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_marketing_customer_persona_2
msgid "Persona 2"
msgstr "Khách hàng 2"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_marketing_customer_persona_3
msgid "Persona 3"
msgstr "Khách hàng 3"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_marketing_customer_persona_4
msgid "Persona 4"
msgstr "Khách hàng 4"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_personal_organizer
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "Personal Organizer"
msgstr "Kế hoạch cá nhân"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Phone:"
msgstr "Điện thoại:"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_shared_todos
msgid "Please pick the following tasks first:"
msgstr "Trước tiên, vui lòng chọn các nhiệm vụ sau:"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "Please refer to the chart below."
msgstr "Vui lòng tham khảo bảng sau."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid ""
"Please submit a request for assistance to the Marketing Team if you fall "
"into one of the following:"
msgstr ""
"Vui lòng gửi yêu cầu hỗ trợ tới Bộ phận Marketing nếu bạn gặp một trong các "
"trường hợp sau:"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "Pluralize the trademark (e.g.<em>YourCompanies</em>)"
msgstr "Viết nhãn hiệu dưới dạng số nhiều (VD: <em>Những/Các TênCôngTy</em>)"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid "Podcast updates 📻"
msgstr "Cập nhật podcast 📻"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "Post-Mortem Analysis"
msgstr "Phân tích thất bại"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_post_mortem
msgid "Post-mortem"
msgstr "Thất bại"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Potential Risks:"
msgstr "Rủi ro tiềm tàng:"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid ""
"Prepare your demos in advance and integrate the prospect's use case into it"
msgstr ""
"Chuẩn bị trước các bản demo của bạn và chỉnh sửa dựa theo trường hợp sử dụng"
" của khách hàng tiềm năng"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/embedded_view_link/embedded_view_link_edit_dialog.xml:0
msgid "Preview"
msgstr "Xem trước"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
msgid ""
"Price <span class=\"o_stars o_five_stars\" id=\"checkId-1\"><i class=\"fa "
"fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i></span>"
msgstr ""
"Giá cả <span class=\"o_stars o_five_stars\" id=\"checkId-1\"><i class=\"fa "
"fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
msgid ""
"Price <span class=\"o_stars o_five_stars\" id=\"checkId-1\"><i class=\"fa "
"fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star-o\"></i></span>"
msgstr ""
"Giá cả <span class=\"o_stars o_five_stars\" id=\"checkId-1\"><i class=\"fa "
"fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
msgid ""
"Price <span class=\"o_stars o_five_stars\" id=\"checkId-1\"><i class=\"fa "
"fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i></span>"
msgstr ""
"Giá cả <span class=\"o_stars o_five_stars\" id=\"checkId-1\"><i class=\"fa "
"fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
msgid ""
"Price <span class=\"o_stars o_five_stars\" id=\"checkId-1\"><i class=\"fa "
"fa-star\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i"
" class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i></span>"
msgstr ""
"Giá cả <span class=\"o_stars o_five_stars\" id=\"checkId-1\"><i class=\"fa "
"fa-star\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i"
" class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid ""
"Price <span class=\"o_stars o_five_stars\" id=\"checkId-1\"><i class=\"fa "
"fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>​"
msgstr ""
"Giá cả <span class=\"o_stars o_five_stars\" id=\"checkId-1\"><i class=\"fa "
"fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>​"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/core/embedded_view_link/embedded_view_link_style.js:0
msgid "Primary"
msgstr "Chủ đạo"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/move_article_dialog/move_article_dialog.js:0
#: code:addons/knowledge/static/src/components/sidebar/sidebar_section.xml:0
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article__category__private
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search
msgid "Private"
msgstr "Riêng tư"

#. module: knowledge
#: model:knowledge.article.template.category,name:knowledge.knowledge_article_template_category_product_management
msgid "Product Management"
msgstr "Quản lý sản phẩm"

#. module: knowledge
#: model:knowledge.article.template.category,name:knowledge.knowledge_article_template_category_productivity
msgid "Productivity"
msgstr "Năng suất"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__article_properties
msgid "Properties"
msgstr "Thuộc tính"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid ""
"Properties are fields that can only be added on articles that have a parent."
msgstr ""
"Thuộc tính là những trường chỉ có thể được thêm vào các bài viết có bài viết"
" chính."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "Property Field"
msgstr "Trường thuộc tính"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Prospect Qualification"
msgstr "Hồ sơ khách hàng tiềm năng"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Prospection Templates"
msgstr "Mẫu liên hệ khách hàng tiềm năng"

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_sales_playbook
msgid ""
"Provide salespeople with tips, lexicon and templates to help them sell "
"faster."
msgstr ""
"Cung cấp cho nhân viên bán hàng các mẹo, vốn từ vựng và mẫu để giúp họ bán "
"hàng nhanh hơn."

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_sprint_calendar_public_holiday
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sprint_calendar_public_holiday
msgid "Public Holidays"
msgstr "Ngày lễ"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "Q1: <font class=\"text-600\">Would it be possible to...</font>"
msgstr "Câu hỏi 1: <font class=\"text-600\">Liệu có thể...</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "Q2: <font class=\"text-600\">Would there be an issue if...</font>"
msgstr ""
"Câu hỏi 2: <font class=\"text-600\">Liệu có vấn đề nào xảy ra không "
"nếu...</font>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_mail_invite
msgid "READ"
msgstr "ĐỌC"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__rating_ids
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__rating_ids
msgid "Ratings"
msgstr "Đánh giá"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/mail/message/message_actions.js:0
msgid "Re-open the discussion"
msgstr "Mở lại cuộc thảo luận"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "Read"
msgstr "Đọc"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_invite__partner_ids
msgid "Recipients"
msgstr "Người nhận"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "Recovery"
msgstr "Khắc phục"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/article_index/article_index.xml:0
msgid "Refresh"
msgstr "Làm mới"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_release_note
msgid "Release Notes"
msgstr "Ghi chú phát hành"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_release_note
msgid "Release Notes 🎉"
msgstr "Ghi chú phát hành 🎉"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover.xml:0
msgid "Remove"
msgstr "Xóa"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Remove Cover"
msgstr "Gỡ ảnh bìa"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
#: code:addons/knowledge/static/src/mail/emoji_picker/emoji_picker_patch.xml:0
msgid "Remove Icon"
msgstr "Xoá biểu tượng"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/embedded_view_link/embedded_view_link_popover.xml:0
msgid "Remove Link"
msgstr "Xoá liên kết"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "Remove Member"
msgstr "Gỡ thành viên"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover.xml:0
msgid "Remove cover"
msgstr "Xoá ảnh bìa"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.js:0
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_hierarchy
msgid "Remove from favorites"
msgstr "Xoá khỏi danh sách yêu thích"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/prompt_embedded_view_name_dialog/prompt_embedded_view_name_dialog.xml:0
msgid "Rename"
msgstr "Đổi tên"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover.xml:0
msgid "Replace"
msgstr "Thay thế"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover.xml:0
msgid "Replace cover"
msgstr "Thay đổi ảnh bìa"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover.xml:0
msgid "Reposition"
msgstr "Căn chỉnh"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover.xml:0
msgid "Reposition cover"
msgstr "Căn chỉnh ảnh bìa"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comments_panel/comments_panel.xml:0
msgid "Resolved Discussions"
msgstr "Cuộc thảo luận đã xử lý"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__activity_user_id
msgid "Responsible User"
msgstr "Người phụ trách"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_form
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_tree_trash
msgid "Restore"
msgstr "Khôi phục"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid "Restore Access"
msgstr "Khôi phục quyền truy cập"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Restore from Trash"
msgstr "Khôi phục từ thùng rác"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid "Restrict Access"
msgstr "Hạn chế quyền truy cập"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid "Restrict own access"
msgstr "Tự hạn chế quyền truy cập"

#. module: knowledge
#: model:ir.model.constraint,message:knowledge.constraint_knowledge_article_check_desync_on_root
msgid "Root articles cannot be desynchronized."
msgstr "Không thể huỷ đồng bộ bài viết gốc."

#. module: knowledge
#: model:ir.model.constraint,message:knowledge.constraint_knowledge_article_check_permission_on_root
msgid "Root articles must have internal permission."
msgstr "Bài viết gốc phải có quyền nội bộ."

#. module: knowledge
#: model:ir.model.constraint,message:knowledge.constraint_knowledge_article_check_template_category_on_root
msgid "Root templates must have a category."
msgstr "Mẫu gốc phải có một danh mục."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "SEO &amp; SEA projects (betting on keywords, ...)"
msgstr "Dự án SEO &amp; SEA (dựa vào từ khóa,...)"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__message_has_sms_error
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Lỗi gửi SMS"

#. module: knowledge
#: model:knowledge.article.template.category,name:knowledge.knowledge_article_template_category_sales
msgid "Sales"
msgstr "Bán hàng"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Sales Details"
msgstr "Thông tin bán hàng"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_sales_playbook
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Sales Playbook"
msgstr "Cẩm nang kinh doanh"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
#: code:addons/knowledge/static/src/editor/embedded_components/backend/embedded_view_link/embedded_view_link_edit_dialog.xml:0
msgid "Save"
msgstr "Lưu"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover.xml:0
msgid "Save Position"
msgstr "Lưu vị trí"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover.xml:0
msgid "Save position"
msgstr "Lưu vị trí"

#. module: knowledge
#: model:ir.ui.menu,name:knowledge.knowledge_menu_article
msgid "Search"
msgstr "Tìm kiếm"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_favorite_view_search
msgid "Search Favorites"
msgstr "Tìm kiếm danh sách yêu thích"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/xml/form_controller.xml:0
msgid "Search Knowledge Articles"
msgstr "Tìm kiếm bài viết kiến thức"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_member_view_search
msgid "Search Members"
msgstr "Tìm kiếm thành viên"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_stage_view_search
msgid "Search Stages"
msgstr "Tìm kiếm giai đoạn"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.js:0
msgid "Search an Article..."
msgstr "Tìm kiếm một bài viết..."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.xml:0
msgid "Search an article..."
msgstr "Tìm kiếm một bài viết..."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/webclient/commands/knowledge_providers.js:0
msgid "Search for an article..."
msgstr "Tìm kiếm một bài viết..."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/webclient/commands/knowledge_providers.js:0
msgid "Search hidden Articles..."
msgstr "Tìm kiếm bài viết đã bị ẩn..."

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search
msgid "Search results"
msgstr "Kết quả tìm kiếm"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/core/embedded_view_link/embedded_view_link_style.js:0
msgid "Secondary"
msgstr "Secondary"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__category
msgid "Section"
msgstr "Phần"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "See a doctor and send us the sick note."
msgstr "Hãy đến gặp bác sĩ và gửi cho chúng tôi phiếu khám bệnh."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/article_template_picker_dialog/article_template_picker_dialog.xml:0
msgid "Select a Template"
msgstr "Chọn một mẫu"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/external_embedded_view_favorite_menu/embedded_view_favorite_menu.js:0
msgid "Select an article"
msgstr "Chọn một bài viết"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/clipboard/macros_embedded_clipboard.xml:0
#: code:addons/knowledge/static/src/editor/embedded_components/backend/file/macros_file_mixin.xml:0
msgid "Send as Message"
msgstr "Gửi dưới dạng tin nhắn"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_tree
msgid "Send to Trash"
msgstr "Đưa vào thùng rác"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/views/item_calendar/item_calendar_view.js:0
msgid "Send to trash"
msgstr "Đưa vào thùng rác"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__sequence
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_favorite__sequence
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_stage__sequence
msgid "Sequence"
msgstr "Trình tự"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Share"
msgstr "Chia sẻ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/move_article_dialog/move_article_dialog.js:0
#: code:addons/knowledge/static/src/components/sidebar/sidebar_section.xml:0
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article__category__shared
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search
msgid "Shared"
msgstr "Đã chia sẻ"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_shared_todos
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_shared_todos
msgid "Shared To-Do List"
msgstr "Danh sách việc cần làm chung"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/article_index/article_index.xml:0
msgid "Show All"
msgstr "Hiện thị tất cả"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comment/comment.xml:0
#: code:addons/knowledge/static/src/editor/embedded_components/backend/article_index/article_index.xml:0
msgid "Show Less"
msgstr "Hiển thị ít hơn"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comment/comment.xml:0
msgid "Show More"
msgstr "Hiển thị thêm"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Show Properties"
msgstr "Hiển thị thuộc tính"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "Show Weekends?"
msgstr "Hiển thị cuối tuần?"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/article_index_plugin/article_index_plugin.js:0
msgid "Show nested articles"
msgstr "Hiển thị bài viết lồng nhau"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Size:"
msgstr "Kích thước:"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
msgid ""
"Social <span class=\"o_stars o_five_stars\" id=\"checkId-4\"><i class=\"fa "
"fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i></span>"
msgstr ""
"Xã hội <span class=\"o_stars o_five_stars\" id=\"checkId-4\"><i class=\"fa "
"fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
msgid ""
"Social Status <span class=\"o_stars o_five_stars\" id=\"checkId-4\"><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star-o\"></i></span>"
msgstr ""
"Vị thế <span class=\"o_stars o_five_stars\" id=\"checkId-4\"><i class=\"fa "
"fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
msgid ""
"Social Status <span class=\"o_stars o_five_stars\" id=\"checkId-4\"><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"
msgstr ""
"Vị thế <span class=\"o_stars o_five_stars\" id=\"checkId-4\"><i class=\"fa "
"fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid ""
"Social Status <span class=\"o_stars o_five_stars\" id=\"checkId-4\"><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"
msgstr ""
"Vị thế <span class=\"o_stars o_five_stars\" id=\"checkId-4\"><i class=\"fa "
"fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
msgid ""
"Social Status ​<span class=\"o_stars o_five_stars\" id=\"checkId-4\"><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star-o\"></i></span>"
msgstr ""
"Vị thế ​<span class=\"o_stars o_five_stars\" id=\"checkId-4\"><i class=\"fa "
"fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star-o\"></i></span>"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_software_specification
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "Software Specification"
msgstr "Thông số kỹ thuật phần mềm"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "Some articles have been sent to Trash"
msgstr "Một số bài viết đã được đưa vào thùng rác"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/view/embedded_view.xml:0
msgid "Something went wrong!"
msgstr "Đã xảy ra lỗi!"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
msgid "Sonya"
msgstr "Sonya"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "Source Feedback"
msgstr "Phản hồi nguồn"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
msgid ""
"Speed of Service <span class=\"o_stars o_five_stars\" id=\"checkId-2\"><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star-o\"></i></span>"
msgstr ""
"Tốc độ dịch vụ <span class=\"o_stars o_five_stars\" id=\"checkId-2\"><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
msgid ""
"Speed of Service <span class=\"o_stars o_five_stars\" id=\"checkId-2\"><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"
msgstr ""
"Tốc độ dịch vụ <span class=\"o_stars o_five_stars\" id=\"checkId-2\"><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
msgid ""
"Speed of Service <span class=\"o_stars o_five_stars\" id=\"checkId-2\"><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"
msgstr ""
"Tốc độ dịch vụ <span class=\"o_stars o_five_stars\" id=\"checkId-2\"><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid ""
"Speed of Service ​<span class=\"o_stars o_five_stars\" id=\"checkId-2\"><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"
msgstr ""
"Tốc độ dịch vụ ​<span class=\"o_stars o_five_stars\" id=\"checkId-2\"><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_sprint_calendar
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sprint_calendar
msgid "Sprint Calendar"
msgstr "Lịch sprint"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_stage_view_form
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search_items
msgid "Stage"
msgstr "Giai đoạn"

#. module: knowledge
#: model:ir.actions.act_window,name:knowledge.knowledge_article_stage_action
#: model:ir.ui.menu,name:knowledge.knowledge_article_stage_menu
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_stage_view_tree
msgid "Stages"
msgstr "Giai đoạn"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article_stage__parent_id
msgid "Stages are shared among acommon parent and its children articles."
msgstr ""
"Giai đoạn được chia sẻ giữa bài viết chính và các bài viết phụ của nó."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Stakeholders Analysis"
msgstr "Phân tích bên liên quan"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "Start Date"
msgstr "Ngày bắt đầu"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.js:0
msgid "Start Date Time"
msgstr "Ngày giờ bắt đầu"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "Start typing"
msgstr "Bắt đầu nhập"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.xml:0
msgid ""
"Start typing to continue with an empty page or pick an option below to get "
"started."
msgstr ""
"Nhập để tiếp tục làm việc với một trang trống hoặc chọn một tùy chọn dưới "
"đây để bắt đầu."

#. module: knowledge
#: model_terms:digest.tip,tip_description:knowledge.digest_tip_knowledge_2
msgid ""
"Start working together on any Knowledge Article by sharing your article with"
" others."
msgstr ""
"Bắt đầu cùng làm việc trên bất kỳ Bài viết Kiến ​​thức nào bằng cách chia sẻ"
" bài viết của bạn với người khác."

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Trạng thái dựa trên hoạt động\n"
"Quá hạn: Hạn chót hạn đã qua\n"
"Hôm nay: Hôm nay là ngày phải thực hiện\n"
"Kế hoạch: Cần thực hiện trong tương lai."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "Stop Date"
msgstr "Ngày dừng"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/article_index/article_index.xml:0
msgid "Switch Mode"
msgstr "Đổi chế độ"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid ""
"Talk to you soon,<br>\n"
"                        YourName"
msgstr ""
"Hi vọng có thể sớm trò chuyện cùng bạn!<br>\n"
"                        TênBạn"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_template_view_search
msgid "Template"
msgstr "Mẫu"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__template_body
msgid "Template Body"
msgstr "Nội dung mẫu"

#. module: knowledge
#: model:ir.actions.act_window,name:knowledge.knowledge_article_template_category_action
#: model:ir.ui.menu,name:knowledge.knowledge_article_template_category_menu
msgid "Template Categories"
msgstr "Danh mục mẫu"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__template_category_id
msgid "Template Category"
msgstr "Danh mục mẫu"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__template_category_sequence
msgid "Template Category Sequence"
msgstr "Trình tự danh mục mẫu"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__template_description
msgid "Template Description"
msgstr "Mô tả mẫu"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_template_view_search
msgid "Template Items"
msgstr "Hạng mục mẫu"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__template_preview
msgid "Template Preview"
msgstr "Xem trước mẫu"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__template_sequence
msgid "Template Sequence"
msgstr "Trình tự mẫu"

#. module: knowledge
#: model:ir.actions.act_window,name:knowledge.knowledge_article_template_stage_action
#: model:ir.ui.menu,name:knowledge.knowledge_article_template_stage_menu
msgid "Template Stages"
msgstr "Giai đoạn mẫu"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__template_name
msgid "Template Title"
msgstr "Tiêu đề mẫu"

#. module: knowledge
#: model:ir.ui.menu,name:knowledge.knowledge_article_template_menu
msgid "Templates"
msgstr "Mẫu"

#. module: knowledge
#: model:ir.model.constraint,message:knowledge.constraint_knowledge_article_check_template_name_required
msgid "Templates should have a name."
msgstr "Mẫu phải có tên."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "Test Environment"
msgstr "Môi trường kiểm thử"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid ""
"Thank you to everyone involved in the organization of this edition of the\n"
"                            <font class=\"text-o-color-2\">\n"
"                                <strong>Odoo Run</strong>\n"
"                            </font>!"
msgstr ""
"Cảm ơn tất cả thành viên đã tham gia tổ chức đường đua\n"
"                            <font class=\"text-o-color-2\">\n"
"                                <strong>Odoo Run</strong>\n"
"                            </font>lần này!"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "The 5 Commandments"
msgstr "5 nguyên tắc làm việc"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid ""
"The Accounting team handles all payments on Fridays afternoon.\n"
"                <br>\n"
"                If 2 weeks have passed and you are still waiting to be paid, get in touch with them."
msgstr ""
"Bộ phận Kế toán chịu trách nhiệm xử lý tất cả các khoản thanh toán vào chiều thứ Sáu.\n"
"                <br>\n"
"                Nếu đã 2 tuần trôi qua mà bạn vẫn chưa nhận được thanh toán, vui lòng liên hệ với họ."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "The Article you are trying to access has been deleted"
msgstr "Bài viết bạn đang cố truy cập đã bị xóa"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid "The Future Entrepreneurship Fair"
msgstr "Hội chợ khởi nghiệp tương lai"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "The article '%s' needs at least one member with 'Write' access."
msgstr "Bài viết '%s' cần ít nhất một thành viên có quyền 'Viết'."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/js/knowledge_controller.js:0
msgid ""
"The article you are trying to open has either been removed or is "
"inaccessible."
msgstr "Bài viết bạn đang cố mở đã bị gỡ hoặc không thể truy cập được."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"The destination placement of %(article_name)s is ambiguous, you should "
"specify the category."
msgstr ""
"Vị trí đích của %(article_name)s không rõ ràng, bạn cần xác định danh mục."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/macros/abstract_macro.js:0
msgid "The operation could not be completed."
msgstr "Không thể hoàn tất thao tác này."

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article_thread__article_anchor_text
msgid ""
"The original highlighted anchor text, giving initial context if that text is"
" modified or removed afterwards."
msgstr ""
"Anchor text gốc được tô màu, cung cấp ngữ cảnh ban đầu nếu sau này văn bản "
"đó được sửa đổi hoặc xóa bỏ."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid ""
"The podcast launches its new technical talks, for all tech-savvy listeners out there! This\n"
"                            new episode of the series features Géry, the mastermind behind OWL, the world fastest JS\n"
"                            framework. 🚀"
msgstr ""
"Podcast cho ra mắt phần trao đổi kỹ thuật mới dành cho tất cả người nghe am hiểu công nghệ!\n"
"                            Tập mới này có sự góp mặt của Géry, người sáng tạo OWL, khung JS nhanh nhất\n"
"                            thế giới.  🚀"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/macros/abstract_macro.js:0
msgid "The record that this macro is targeting could not be found."
msgstr "Không thể tìm thấy bản ghi mà macro này đang nhắm mục tiêu."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/controllers/main.py:0
msgid "The selected member does not exists or has been already deleted."
msgstr "Thành viên được chọn không tồn tại hoặc đã bị xóa."

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__sequence
msgid ""
"The sequence is computed only among the articles that have the same parent."
msgstr "Trình tự chỉ được tính trong số các bài viết có cùng bài viết chính."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.js:0
msgid "The start date property is required."
msgstr "Thuộc tính ngày bắt đầu là bắt buộc."

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__root_article_id
msgid ""
"The subject is the title of the highest parent in the article hierarchy."
msgstr ""
"Chủ đề là tiêu đề của bài viết chính có thứ tự cao nhất trong hệ thống phân "
"cấp bài viết."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid ""
"The tickets to participate to Odoo Experience 23 are now available and they "
"are cheaper if you book them now!"
msgstr ""
"Vé tham gia Odoo Experience 23 hiện đã có sẵn và sẽ được giảm giá nếu bạn "
"đặt ngay bây giờ!"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/view/embedded_view.xml:0
msgid "The view does not exist or you are not allowed to access to it."
msgstr "Chế độ xem không tồn tại hoặc bạn không được phép truy cập vào nó."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar_section.xml:0
msgid "There are no Articles in your Workspace."
msgstr "Không có bài viết nào trong Không gian làm việc của bạn."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "Things to handle this week*"
msgstr "Những việc cần thực hiện trong tuần này*"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_form
msgid "This Article is in Trash and will be deleted on the"
msgstr "Bài viết này đang nằm trong thùng rác và sẽ bị xóa vào ngày"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_form
msgid "This article is archived."
msgstr "Bài viết này đã được lưu trữ."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "This article is locked"
msgstr "Bài viết này đã bị khóa"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_form
msgid "This article is only displayed to its members."
msgstr "Bài viết này chỉ được hiển thị cho các thành viên của bài viết."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid ""
"This year again, Odoo was present at the <em>Tech and Innovation festival for students</em> in\n"
"                            Antwerp, ready to promote our company!<br>Fabien was also there and gave an interview on the main\n"
"                            stage."
msgstr ""
"Năm nay, Odoo tiếp tục góp mặt tại <em>lễ hội Công nghệ và Đổi mới dành cho sinh viên</em> ở\n"
"                            Antwerp để quảng bá công ty của chúng tôi!<br>Fabien cũng có mặt ở lễ hội này và trả lời phỏng vấn trên sân khấu\n"
"                            chính."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/properties_panel/properties_panel.xml:0
msgid ""
"Those fields will be available on all articles that share the same parent."
msgstr ""
"Những trường đó sẽ có sẵn trên tất cả bài viết có cùng bài viết chính."

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__is_resolved
msgid "Thread Closed"
msgstr "Chủ đề đã đóng"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_product_catalog
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid "Tiny House Catalog"
msgstr "Danh mục nhà nhỏ"

#. module: knowledge
#: model:digest.tip,name:knowledge.digest_tip_knowledge_0
#: model_terms:digest.tip,tip_description:knowledge.digest_tip_knowledge_0
msgid "Tip: A Knowledge well kept"
msgstr "Mẹo: Bảo vệ Kiến thức"

#. module: knowledge
#: model:digest.tip,name:knowledge.digest_tip_knowledge_2
#: model_terms:digest.tip,tip_description:knowledge.digest_tip_knowledge_2
msgid "Tip: Be on the same page"
msgstr "Mẹo: Cùng chung lộ trình"

#. module: knowledge
#: model:digest.tip,name:knowledge.digest_tip_knowledge_1
#: model_terms:digest.tip,tip_description:knowledge.digest_tip_knowledge_1
msgid "Tip: Use Clipboards to easily inject repetitive content"
msgstr "Mẹo: Sử dụng Bộ nhớ tạm để dễ dàng chèn nội dung trùng lặp"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Tips to close more deals"
msgstr "Mẹo để ký kết nhiều giao dịch hơn"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__name
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_template_category__name
msgid "Title"
msgstr "Tiêu đề"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid "To be sure to stay updated, follow us on"
msgstr ""
"Để chắc chắn bạn luôn cập nhật thông tin kịp thời, hãy theo dõi chúng tôi "
"trên"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.xml:0
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Toggle aside menu"
msgstr "Chuyển đổi menu sang bên"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Toggle chatter"
msgstr "Bật/tắt cửa sổ trò chuyện"

#. module: knowledge
#: model:ir.actions.act_window,name:knowledge.knowledge_article_action_trashed
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_tree_items
msgid "Trash"
msgstr "Thùng rác"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__to_delete
#: model:ir.ui.menu,name:knowledge.knowledge_article_menu_trashed
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search
msgid "Trashed"
msgstr "Đã chuyển vào thùng rác"

#. module: knowledge
#: model:ir.model.constraint,message:knowledge.constraint_knowledge_article_check_trash
msgid "Trashed articles must be archived."
msgstr "Các bài viết trong thùng rác phải được lưu trữ."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "Trying to remove wrong member."
msgstr "Đang cố gắng gỡ thành viên không phù hợp."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/embedded_view_link/embedded_view_link_edit_dialog.xml:0
msgid "Type"
msgstr "Loại"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Loại hoạt động ngoại lệ trong bản ghi."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_form
msgid "Unarchive"
msgstr "Bỏ lưu trữ"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Unlock"
msgstr "Mở khoá"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "Unsupported search operation"
msgstr "Thao tác tìm kiếm không được hỗ trợ"

#. module: knowledge
#. odoo-javascript
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
#: code:addons/knowledge/static/src/components/hierarchy/hierarchy.xml:0
#: code:addons/knowledge/static/src/components/sidebar/sidebar_row.xml:0
#: code:addons/knowledge/static/src/editor/html_migrations/migration-1.0.js:0
#: code:addons/knowledge/static/src/webclient/commands/knowledge_providers.js:0
#: code:addons/knowledge/static/src/xml/knowledge_command_palette.xml:0
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_hierarchy
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_kanban_items
msgid "Untitled"
msgstr "Không có tiêu đề"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/article_index/article_index.xml:0
msgid "Update"
msgstr "Cập nhật"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "Usage"
msgstr "Cách dùng"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/clipboard/macros_embedded_clipboard.js:0
msgid "Use as %s"
msgstr "Sử dụng làm %s"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/file/macros_file_mixin.xml:0
msgid "Use as Attachment"
msgstr "Dùng làm tệp đính kèm"

#. module: knowledge
#: model_terms:digest.tip,tip_description:knowledge.digest_tip_knowledge_1
msgid "Use the /clipboard command on a Knowledge Article and get going."
msgstr "Sử dụng lệnh /clipboard trên Bài viết Kiến ​​thức và bắt đầu."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "Use the expression \"YourCompanions\" to refer to our community"
msgstr "Sử dụng cụm từ \"YourCompanions\" để chỉ cộng đồng của chúng tôi"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "Use the logo instead of the word inside sentences"
msgstr "Sử dụng logo thay vì từ ngữ trong câu"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "Use this template whenever you have an announcement to make."
msgstr "Hãy sử dụng mẫu này bất cứ khi nào bạn cần đưa ra thông báo."

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__category
msgid ""
"Used to categorize articles in UI, depending on their main permission "
"definitions."
msgstr ""
"Được sử dụng để phân loại các bài viết trong giao diện người dùng, tùy thuộc"
" vào định nghĩa về quyền chính của chúng."

#. module: knowledge
#: model:ir.model,name:knowledge.model_res_users
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_favorite__user_id
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_favorite_view_search
msgid "User"
msgstr "Người dùng"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__user_favorite_sequence
msgid "User Favorite Sequence"
msgstr "Trình tự yêu thích của người dùng"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "User Permission"
msgstr "Quyền của người dùng"

#. module: knowledge
#: model:ir.model.constraint,message:knowledge.constraint_knowledge_article_favorite_unique_article_user
msgid "User already has this article in favorites."
msgstr "Người dùng đã có bài viết này trong danh sách yêu thích."

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__user_permission
msgid "User permission"
msgstr "Quyền của người dùng"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_release_note
msgid "VersionName - ReleaseDate"
msgstr "Tênphiênbản - Ngàypháthành"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "Visibility"
msgstr "Hiển thị"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
msgid "Vittorio"
msgstr "Vittorio"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_shared_todos_water_the_plants
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_shared_todos_water_the_plants
msgid "Water the Plants"
msgstr "Tưới cây"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid "We already cannot wait for the next one🏃"
msgstr "Chúng tôi rất mong đợi tới lần tiếp theo! 🏃"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__website_message_ids
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__website_message_ids
msgid "Website Messages"
msgstr "Thông báo trên trang web"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__website_message_ids
#: model:ir.model.fields,help:knowledge.field_knowledge_article_thread__website_message_ids
msgid "Website communication history"
msgstr "Lịch sử trao đổi qua trang web"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/res_users.py:0
msgid "Welcome %s"
msgstr "Chào mừng %s"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid ""
"Welcome to the last edition of the MyCompany Newsletter!<br>\n"
"                We are very excited to share with you the hottest updates and news! 🤩"
msgstr ""
"Chào mừng bạn đến với ấn bản cuối cùng của Bản tin email MyCompany!<br>\n"
"                Chúng tôi rất vui mừng được chia sẻ với bạn những cập nhật và tin tức nóng hổi nhất! 🤩"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "What do I have to do if I cannot work?"
msgstr "Tôi cần làm gì nếu tôi không thể làm việc?"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "What do you want to manage?"
msgstr "Bạn muốn quản lý thứ gì?"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/prompt_embedded_view_name_dialog/prompt_embedded_view_name_dialog.xml:0
msgid "What items do you want to manage?"
msgstr "Bạn muốn quản lý hạng mục nào?"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__is_locked
msgid ""
"When locked, users cannot write on the body or change the title, even if "
"they have write access on the article."
msgstr ""
"Khi bị khóa, người dùng không thể viết vào phần nội dung hoặc thay đổi tiêu "
"đề, ngay cả khi họ có quyền viết đối với bài viết đó."

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__to_delete
msgid ""
"When sent to trash, articles are flagged to be deleted\n"
"                days after last edit. knowledge_article_trash_limit_days config\n"
"                parameter can be used to modify the number of days. \n"
"                (default is 30)"
msgstr ""
"Khi bị đưa vào thùng rác, các bài viết sẽ được gắn cờ để xóa\n"
"                sau vài ngày kể từ lần chỉnh sửa cuối cùng. Bạn có thể sử dụng tham số\n"
"                cấu hình knowledge_article_trash_limit_days để sửa đổi số ngày.\n"
"                (mặc định là 30)"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__full_width
msgid ""
"When set, the article body will take the full width available on the article"
" page. Otherwise, the body will have large horizontal margins."
msgstr ""
"Khi cài đặt, nội dung bài viết sẽ trải hết chiều rộng có sẵn trên trang bài "
"viết. Nếu không, phần nội dung sẽ có lề ngang lớn."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid ""
"Whether your heart leans towards the rustic charm of a wooden hut or the "
"intricate beauty of Victorian steelworks, we have you covered. Desiring a "
"specific option? Rest assured, we are here to fulfill your wishes! Just get "
"in touch with our architects and we will make sure to provide any specific "
"choice you desire."
msgstr ""
"Cho dù trái tim bạn nghiêng về vẻ quyến rũ mộc mạc của một túp lều gỗ hay vẻ"
" đẹp đầy phức tạp của các nhà máy thép thời Victoria, chúng tôi đều đáp ứng "
"được nhu cầu của bạn. Bạn muốn một phong cách riêng biệt? Hãy yên tâm, chúng"
" tôi ở đây để thực hiện mong muốn của bạn! Chỉ cần liên hệ với các kiến trúc"
" sư của chúng tôi và chúng tôi sẽ đảm bảo cung cấp mọi lựa chọn riêng mà bạn"
" muốn."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_example_branding
msgid "Which color should we pick for the logo?"
msgstr "Nên chọn màu nào cho logo?"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_example_branding
msgid "Which size should the rollups be?"
msgstr "Các roll-up nên có kích thước nào?"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_example_branding
msgid "Which text should we print on the billboards?"
msgstr "Nên in nội dung nào trên biển quảng cáo?"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid ""
"While tiny houses are inherently small, it's important to prioritize comfort"
" when sharing such limited space. To ensure everyone's happiness, we "
"recommend allowing at least 9m² per person. If you plan to live with "
"multiple roommates, our Serenità model is highly recommended, providing "
"ample space for each individual to enjoy<br>"
msgstr ""
"Mặc dù những ngôi nhà nhỏ vốn đã có diện tích khiêm tốn, nhưng điều quan "
"trọng là phải ưu tiên sự thoải mái khi chia sẻ không gian hạn chế như vậy. "
"Để đảm bảo hạnh phúc của mọi thành viên, chúng tôi khuyên bạn nên tính toán "
"diện tích tối thiểu là 9m2/người. Nếu bạn dự định sống cùng nhiều thành "
"viên, chúng tôi đặc biệt đề xuất mẫu Serenità, vì nó cung cấp không gian "
"rộng rãi cho mỗi cá nhân<br>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid ""
"While tiny houses do offer a more cost-effective alternative to traditional "
"houses, at MyCompany, we prioritize durability, which comes with a price. "
"However, if you're on a tight budget, we have the perfect solution: our "
"Cielo model.<br>It provides everything you need while allowing you to "
"save.<br>"
msgstr ""
"Mặc dù những ngôi nhà nhỏ là lựa chọn thay thế tiết kiệm hơn cho những ngôi "
"nhà truyền thống, nhưng tại MyCompany, chúng tôi ưu tiên độ bền đi kèm với "
"giá cả. Dù vậy, nếu bạn có ngân sách eo hẹp, thì chúng tôi có một giải pháp "
"hoàn hảo là mẫu Cielo.<br>Mẫu nhà này cung cấp mọi thứ bạn cần trong khi cho"
" phép bạn tiết kiệm ngân sách.<br>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "Who can drive my car?"
msgstr "Ai có thể lái xe của tôi?"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "Who do I need to address if I need office supplies?"
msgstr "Tôi cần liên hệ với ai nếu tôi cần văn phòng phẩm?"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid "Who has access to what? 🕵️"
msgstr "Ai có thể truy cập vào những bài viết nào? 🕵️"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "Why has my expense not been reimbursed? It has been accepted."
msgstr ""
"Tại sao khoản chi phí của tôi chưa được hoàn trả? Nó đã được chấp nhận rồi."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/move_article_dialog/move_article_dialog.js:0
#: code:addons/knowledge/static/src/components/sidebar/sidebar_section.xml:0
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article__category__workspace
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search
msgid "Workspace"
msgstr "Không gian làm việc"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "Write"
msgstr "Viết"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.js:0
msgid "Write an article about"
msgstr "Viết một bài viết về"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid ""
"Write it without any article(<em><s>the</s> YourCompany, <s>a</s> "
"YourCompany, ...</em>)"
msgstr ""
"Không thêm mạo từ (<em><s>the</s> YourCompany, <s>a</s> YourCompany, "
"...</em>) (áp dụng trong tiếng Anh)"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_shared_todos_write_the_next_newsletter
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_shared_todos_write_the_next_newsletter
msgid "Write the next Newsletter"
msgstr "Viết bản tin email tiếp theo"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "You"
msgstr "Bạn"

#. module: knowledge
#: model:ir.model.constraint,message:knowledge.constraint_knowledge_article_member_unique_article_partner
msgid "You already added this partner on this article."
msgstr "Bạn đã thêm đối tác này vào bài viết."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "You are not allowed to create a new template."
msgstr "Bạn không được phép tạo mẫu mới."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/view/readonly_embedded_view.js:0
msgid "You are not allowed to delete a favorite filter in this article."
msgstr "Bạn không được phép xóa bộ lọc yêu thích trong bài viết này."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "You are not allowed to delete a template."
msgstr "Bạn không được phép xóa mẫu."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "You are not allowed to make '%(article_name)s' private."
msgstr "Bạn không được phép chuyển '%(article_name)s' thành riêng tư."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"You are not allowed to move '%(article_name)s' under '%(parent_name)s'."
msgstr ""
"Bạn không được phép chuyển '%(article_name)s' vào dưới '%(parent_name)s'."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "You are not allowed to move '%(article_name)s'."
msgstr "Bạn không được phép chuyển '%(article_name)s'."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/view/readonly_embedded_view.js:0
msgid "You are not allowed to save a favorite filter in this article."
msgstr "Bạn không được phép lưu bộ lọc yêu thích trong bài viết này."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "You are not allowed to update a template."
msgstr "Bạn không được phép cập nhật mẫu."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "You are not allowed to update the type of a article or a template."
msgstr "Bạn không được phép cập nhật loại bài viết hoặc mẫu."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "You can't leave an article for which you are the last writer"
msgstr "Bạn không thể rời khỏi bài viết mà bạn là người viết cuối cùng"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"You can't move %(article_name)s under %(item_name)s, as %(item_name)s is an "
"Article Item. Convert %(item_name)s into an Article first."
msgstr ""
"Bạn không thể di chuyển %(article_name)s dưới %(item_name)s, vì "
"%(item_name)s là một Hạng mục bài viết. Trước tiên hãy chuyển %(item_name)s "
"thành một Bài viết."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "You can't remove the last writer of the article"
msgstr "Bạn không thể gỡ người viết cuối cùng của bài viết"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "You cannot add or remove this article to your favorites"
msgstr ""
"Bạn không thể thêm hoặc xóa bài viết này vào/khỏi danh sách yêu thích của "
"bạn"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/controllers/main.py:0
msgid "You cannot change the internal permission of this article."
msgstr "Bạn không thể thay đổi quyền nội bộ của bài viết này."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/controllers/main.py:0
msgid "You cannot change the permission of this member."
msgstr "Bạn không thể thay đổi quyền của thành viên này."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_cover.py:0
msgid "You cannot create a new Knowledge Cover from here."
msgstr "Bạn không thể tạo Ảnh bìa Kiến thức mới từ đây."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "You cannot create an article under articles on which you cannot write"
msgstr ""
"Bạn không thể tạo một bài viết thuộc về những bài viết mà bạn không thể viết"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"You cannot move an article under %(parent_name)s as you cannot write on it"
msgstr ""
"Bạn không thể chuyển một bài viết vào %(parent_name)s vì bạn không thể viết "
"trên bài viết đó"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/hierarchy/hierarchy.xml:0
msgid "You do not have access to this article"
msgstr "Bạn không có quyền truy cập bài viết này. "

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar_section.xml:0
msgid "You do not have any private Article."
msgstr "Bạn không có bài viết riêng tư nào."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "You have to be editor on %(article_name)s to add members."
msgstr "Bạn phải là biên tập viên trên %(article_name)s để thêm thành viên."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"You have to be editor on %(article_name)s to change its internal permission."
msgstr ""
"Bạn phải là biên tập viên trên %(article_name)s để thay đổi quyền nội bộ của"
" bài viết đó."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"You have to be editor on %(article_name)s to modify members permissions."
msgstr ""
"Bạn phải là biên tập viên trên %(article_name)s để chỉnh sửa quyền của thành"
" viên."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"You have to be editor on %(article_name)s to remove or exclude member "
"%(member_name)s."
msgstr ""
"Bạn phải là biên tập viên trên %(article_name)s để gỡ hoặc loại thành viên "
"%(member_name)s."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "You have to be editor on %(article_name)s to restore it."
msgstr ""
"Bạn phải là biên tập viên trên %(article_name)s để khôi phục bài viết đó."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid ""
"You may have heard those a million times and yet you are not entirely sure of what it means.<br>\n"
"                Here is a quick recap for you to shine during the next meeting."
msgstr ""
"Bạn có thể đã nghe thấy chúng cả triệu lần nhưng vẫn chưa hoàn toàn hiểu rõ nghĩa của chúng là gì.<br>\n"
"                Dưới đây là bản tóm tắt nhanh để bạn tỏa sáng trong cuộc họp tiếp theo."

#. module: knowledge
#. odoo-javascript
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
#: code:addons/knowledge/static/src/components/sidebar/sidebar.js:0
msgid "You need at least 2 members for the Article to be shared."
msgstr "Bạn cần ít nhất 2 thành viên để chia sẻ bài viết."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "You need to have access to this article in order to join its members."
msgstr ""
"Bạn cần có quyền truy cập vào bài viết này để tham gia làm thành viên của "
"bài viết."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"You need to have access to this article's root in order to join its members."
msgstr ""
"Bạn cần có quyền truy cập vào bài viết gốc của bài viết này để tham gia làm "
"thành viên của bài viết."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "Your Access: %s"
msgstr "Quyền truy cập của bạn: %s"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid ""
"Your car can be driven by <strong>you, your spouse and by any person living "
"under the same roof</strong> as long as they have a valid permit."
msgstr ""
"Xe của bạn có thể được lái bởi <strong>bạn, vợ/chồng của bạn và bất kỳ người"
" nào sống cùng nhà với bạn</strong> miễn là họ có giấy phép hợp lệ."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "YourCompany is proud to announce that..."
msgstr "YourCompany hân hạnh thông báo rằng..."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid ""
"YourCompany is very proud of its brand image.\n"
"                <br>\n"
"                When representing the brand, we thus ask you to be very cautious in how you refer to the company."
msgstr ""
"YourCompany rất tự hào về hình ảnh thương hiệu của mình.\n"
"                <br>\n"
"                Do đó, nếu bạn đại diện cho thương hiệu, chúng tôi yêu cầu bạn hết sức thận trọng khi đề cập tới công ty."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comments_panel/comments_panel.xml:0
msgid "all"
msgstr "tất cả"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_trash_notification
msgid "and the following child article(s) have"
msgstr "và (các) bài viết phụ sau đây có"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/webclient/commands/knowledge_providers.js:0
msgid "articles"
msgstr "bài viết"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_trash_notification
msgid "been sent to Trash.<br/><br/>"
msgstr "đã được đưa vào thùng rác.<br/><br/>"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comments_panel/comments_panel.xml:0
msgid "button to add comments"
msgstr "để thêm bình luận"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "contact our FleetOfficer"
msgstr "hãy liên hệ Chuyên viên đội xe"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover.xml:0
msgid "cover"
msgstr "ảnh bìa"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/prompt_embedded_view_name_dialog/prompt_embedded_view_name_dialog.js:0
msgid "e.g. Buildings"
msgstr "VD: Toà nhà"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "e.g. Meetings"
msgstr "VD: Cuộc họp"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_stage_view_form
msgid "e.g. Ongoing"
msgstr "VD: Đang diễn ra"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/prompt_embedded_view_name_dialog/prompt_embedded_view_name_dialog.js:0
msgid "e.g. Todos"
msgstr "VD: Việc cần làm"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_trash_notification
msgid "has"
msgstr "has"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_trash_notification
msgid "have"
msgstr "have"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_mail_invite
msgid "invited you to"
msgstr "đã mời bạn cho"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_mail_invite
msgid "invited you to access an article.<br/>"
msgstr "mời bạn truy cập một bài viết.<br/>"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
#: code:addons/knowledge/static/src/components/sidebar/sidebar.xml:0
#: code:addons/knowledge/static/src/components/sidebar/sidebar_row.xml:0
#: code:addons/knowledge/static/src/components/with_lazy_loading/with_lazy_loading.xml:0
msgid "loader"
msgstr "bộ tải"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_mail_notification_layout
msgid "mentioned you in a comment:"
msgstr "đã nhắc đến bạn trong một bình luận:"

#. module: knowledge
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article__user_permission__none
msgid "none"
msgstr "không"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid "or"
msgstr "hoặc"

#. module: knowledge
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article__user_permission__read
msgid "read"
msgstr "đọc"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comments_panel/comments_panel.xml:0
msgid "resolved"
msgstr "đã được xử lý"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.xml:0
msgid "search"
msgstr "tìm kiếm"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_mail_invite
msgid "the article"
msgstr "bài viết"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_form
msgid "to unleash the power of Knowledge !"
msgstr "giải phóng tiềm lực Kiến thức!"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comments_panel/comments_panel.xml:0
msgid "unresolved"
msgstr "chưa được xử lý"

#. module: knowledge
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article__user_permission__write
msgid "write"
msgstr "viết"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid ""
"​<span class=\"o_stars o_five_stars\" id=\"checkId-1\"><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i></span>"
msgstr ""
"​<span class=\"o_stars o_five_stars\" id=\"checkId-1\"><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid ""
"​<span class=\"o_stars o_five_stars\" id=\"checkId-2\"><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i></span>"
msgstr ""
"​<span class=\"o_stars o_five_stars\" id=\"checkId-2\"><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid ""
"​<span class=\"o_stars o_five_stars\" id=\"checkId-3\"><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i></span>"
msgstr ""
"​<span class=\"o_stars o_five_stars\" id=\"checkId-3\"><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_campaign_brief
msgid "⌚ Elevator Pitch"
msgstr "⌚ Giới thiệu ngắn gọn"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_example_branding
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "☝🏼 Please prepare the following before the meeting:"
msgstr "☝🏼 Vui lòng chuẩn bị những điều sau trước cuộc họp:"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "⚙ Technical Specifications"
msgstr "⚙ Thông số kỹ thuật"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "❓ Open Questions"
msgstr "❓ Câu hỏi bỏ ngỏ"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "⭐ Release Notes"
msgstr "⭐ Ghi chú phát hành"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_campaign_brief
msgid "🎯 Target Audience"
msgstr "🎯 Đối tượng mục tiêu"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid "🏘️ House Model - Incanto"
msgstr "🏘️ Mẫu nhà - Incanto"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid "🏠 House Model - Cielo"
msgstr "🏠 Mẫu nhà - Cielo"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid "🏢 House Model - Serenità"
msgstr "🏢 Mẫu nhà - Serenità"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_campaign_brief
msgid "📊 KPIs"
msgstr "📊 KPI"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "📝 Purpose"
msgstr "📝 Mục đích"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_campaign_brief
msgid "📣 Message"
msgstr "📣 Thông điệp"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "🔍 Review Checklist"
msgstr "🔍 Danh sách kiểm duyệt"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "🔗 Links"
msgstr "🔗 Liên kết"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "🗓 WEEKLY AGENDA"
msgstr "🗓 THỜI GIAN BIỂU HÀNG TUẦN"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_example_branding
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "🗣 Meeting Agenda"
msgstr "🗣 Chương trình cuộc họp"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_example_branding
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "🙌🏼 Decisions Taken"
msgstr "🙌🏼 Quyết định đã ra"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_campaign_brief
msgid "🚀 Objective"
msgstr "🚀 Mục tiêu"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid "🛕 House Model - Dolcezza"
msgstr "🛕 Mẫu nhà - Dolcezza"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "🧠 Functional Specifications"
msgstr "🧠 Thông số chức năng"
