# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_barcode_product_expiry
# 
# Translators:
# <AUTHOR> <EMAIL>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:28+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: erpgo translator <<EMAIL>>, 2024\n"
"Language-Team: Azerbaijani (https://app.transifex.com/odoo/teams/41243/az/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: az\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: stock_barcode_product_expiry
#: model_terms:ir.ui.view,arch_db:stock_barcode_product_expiry.stock_move_line_product_selector
msgid "<i class=\"fa fa-fw fa-lg fa-calendar me-3\" title=\"Expiration Date\"/>"
msgstr ""

#. module: stock_barcode_product_expiry
#: model_terms:ir.ui.view,arch_db:stock_barcode_product_expiry.stock_move_line_product_selector
msgid ""
"<span class=\"fa fa-exclamation-triangle text-danger ms-4\" invisible=\"not is_expired or not picking_type_use_existing_lots\">\n"
"                        This lot is expired\n"
"                    </span>"
msgstr ""

#. module: stock_barcode_product_expiry
#: model:ir.model,name:stock_barcode_product_expiry.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "Məhsul Hərəkətləri (Ehtiyat Keçid Xətti)"

#. module: stock_barcode_product_expiry
#: model:ir.model,name:stock_barcode_product_expiry.model_product_product
msgid "Product Variant"
msgstr "Məhsul Çeşidi"
