# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale_comparison
# 
# Translators:
# Wil Odoo, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: website_sale_comparison
#: model:product.attribute.value,name:website_sale_comparison.product_attribute_value_8
msgid "134.7 x 200 x 7.2 mm"
msgstr "134.7 * 200 * 7.2 مم"

#. module: website_sale_comparison
#: model:product.attribute.value,name:website_sale_comparison.product_attribute_value_7
msgid "308 g"
msgstr "308 جرام "

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid ""
"<i class=\"fa fa-chevron-circle-down o_product_comparison_collpase\" "
"role=\"img\" aria-label=\"Collapse\" title=\"Collapse\"/>"
msgstr ""
"<i class=\"fa fa-chevron-circle-down o_product_comparison_collpase\" "
"role=\"img\" aria-label=\"Collapse\" title=\"طوي \"/>"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid ""
"<i class=\"fa fa-chevron-circle-down o_product_comparison_collpase\" "
"role=\"img\" aria-label=\"Collapse\" title=\"Collapse\"/><span>Tags</span>"
msgstr ""
"<i class=\"fa fa-chevron-circle-down o_product_comparison_collpase\" "
"role=\"img\" aria-label=\"Collapse\" title=\"تصغير \"/><span>علامات "
"التصنيف</span> "

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid "<i class=\"fa fa-shopping-cart me-2\"/>Add to Cart"
msgstr "<i class=\"fa fa-shopping-cart me-2\"/>إضافة إلى عربة التسوق "

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_product
msgid "<i class=\"fa fa-trash\" role=\"img\" aria-label=\"Remove\"/>"
msgstr "<i class=\"fa fa-trash\" role=\"img\" aria-label=\"Remove\"/>"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_add_to_compare
msgid "<span class=\"fa fa-exchange me-2\"/>Compare"
msgstr "<span class=\"fa fa-exchange me-2\"/> مقارنة "

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_attributes_body
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid "<span>Tags</span>"
msgstr "<span>علامات التصنيف</span> "

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid "<strong>Price:</strong>"
msgstr "<strong>السعر:</strong>"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid "<strong>x</strong>"
msgstr "<strong>x</strong>"

#. module: website_sale_comparison
#: model:product.attribute.value,name:website_sale_comparison.product_attribute_value_1
msgid "Apple"
msgstr "Apple"

#. module: website_sale_comparison
#: model:ir.actions.act_window,name:website_sale_comparison.product_attribute_category_action
#: model:ir.ui.menu,name:website_sale_comparison.menu_attribute_category_action
msgid "Attribute Categories"
msgstr "فئات الخاصية"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.snippet_options
msgid "Bottom of Page"
msgstr "أسفل الصفحة "

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__name
msgid "Category Name"
msgstr "اسم الفئة"

#. module: website_sale_comparison
#. odoo-javascript
#: code:addons/website_sale_comparison/static/src/xml/comparison.xml:0
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.add_to_compare
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_add_to_compare
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.snippet_options
msgid "Compare"
msgstr "المقارنة"

#. module: website_sale_comparison
#. odoo-javascript
#: code:addons/website_sale_comparison/static/src/js/website_sale_comparison.js:0
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid "Compare Products"
msgstr "المقارنة بين المنتجات"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid "Contact Us"
msgstr "تواصل معنا"

#. module: website_sale_comparison
#: model_terms:ir.actions.act_window,help:website_sale_comparison.product_attribute_category_action
msgid "Create a new attribute category"
msgstr "إنشاء فئة خاصية جديدة "

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: website_sale_comparison
#: model:product.attribute,name:website_sale_comparison.product_attribute_8
#: model:product.attribute.category,name:website_sale_comparison.product_attribute_category_2
msgid "Dimensions"
msgstr "الأبعاد"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: website_sale_comparison
#: model:product.attribute.category,name:website_sale_comparison.product_attribute_category_duration
msgid "Duration"
msgstr "المدة"

#. module: website_sale_comparison
#: model:product.attribute.category,name:website_sale_comparison.product_attribute_category_general_features
msgid "General Features"
msgstr "الخواص العامة "

#. module: website_sale_comparison
#: model_terms:ir.actions.act_window,help:website_sale_comparison.product_attribute_category_action
msgid ""
"Group attributes by category that will appear in the specification\n"
"                part of a product page."
msgstr ""
"قم بتجميع الخصائص حسب الفئة التي ستظهر في جزء المواصفات\n"
"                في صفحة الإنتاج. "

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__id
msgid "ID"
msgstr "المُعرف"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.snippet_options
msgid "In accordion"
msgstr ""

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.snippet_options
msgid "None"
msgstr "لا شيء"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.accordion_specs_item
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.specifications_table
msgid "Others"
msgstr "غير ذلك"

#. module: website_sale_comparison
#. odoo-javascript
#: code:addons/website_sale_comparison/static/src/xml/comparison.xml:0
msgid "Product"
msgstr "المنتج"

#. module: website_sale_comparison
#: model:ir.model,name:website_sale_comparison.model_product_attribute
msgid "Product Attribute"
msgstr "خاصية المنتج"

#. module: website_sale_comparison
#: model:ir.model,name:website_sale_comparison.model_product_attribute_category
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_attribute_category_tree_view
msgid "Product Attribute Category"
msgstr "فئة خاصية المنتج"

#. module: website_sale_comparison
#: model:ir.model,name:website_sale_comparison.model_product_template_attribute_line
msgid "Product Template Attribute Line"
msgstr "بند خاصية قالب المنتج"

#. module: website_sale_comparison
#: model:ir.model,name:website_sale_comparison.model_product_product
msgid "Product Variant"
msgstr "متغير المنتج "

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_product
msgid "Product image"
msgstr "صورة المنتج"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__attribute_ids
msgid "Related Attributes"
msgstr "الخواص ذات الصلة "

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_product
msgid "Remove"
msgstr "إزالة"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__sequence
msgid "Sequence"
msgstr "تسلسل "

#. module: website_sale_comparison
#: model:ir.model.fields,help:website_sale_comparison.field_product_attribute__category_id
msgid ""
"Set a category to regroup similar attributes under the same section in the "
"Comparison page of eCommerce."
msgstr ""
"قم بضبط فئة لإعادة تجميع الخصائص المتشابهة في قسم واحد في صفحة المقارنة "
"بالمتجر الإلكتروني. "

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid "Shop Comparator"
msgstr "مقارن المتجر"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.snippet_options
msgid "Specification"
msgstr "المواصفات"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.accordion_specs_item
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_attributes_body
msgid "Specifications"
msgstr "المواصفات"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid "Uncategorized"
msgstr "غير مصنف"

#. module: website_sale_comparison
#. odoo-javascript
#: code:addons/website_sale_comparison/static/src/xml/comparison.xml:0
msgid "Warning"
msgstr "تحذير"

#. module: website_sale_comparison
#: model:product.attribute,name:website_sale_comparison.product_attribute_7
msgid "Weight"
msgstr "الوزن"

#. module: website_sale_comparison
#. odoo-javascript
#: code:addons/website_sale_comparison/static/src/xml/comparison.xml:0
msgid "You can compare max 4 products."
msgstr "يمكنك المقارنة بين 4 منتجات كحد أقصى. "

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute__category_id
msgid "eCommerce Category"
msgstr "فئة المتجر الإلكتروني "
