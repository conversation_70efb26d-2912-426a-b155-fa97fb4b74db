<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="account_balance_report_l10n_dk_balance" model="account.report">
        <field name="name">Balance</field>
        <field name="root_report_id" ref="account_reports.balance_sheet"/>
        <field name="filter_date_range" eval="False"/>
        <field name="filter_unfold_all" eval="True"/>
        <field name="filter_journals" eval="True"/>
        <field name="filter_analytic_groupby" eval="True"/>
        <field name="country_id" ref="base.dk"/>
        <field name="filter_multi_company">selector</field>
        <field name="column_ids">
            <record id="account_balance_report_l10n_dk_balance_column" model="account.report.column">
                <field name="name">Balance</field>
                <field name="expression_label">balance</field>
            </record>
        </field>
        <field name="line_ids">
            <record id="account_balance_report_l10n_dk_active" model="account.report.line">
                <field name="name">Active</field>
                <field name="code">DK_active</field>
                <field name="aggregation_formula">DK_fixed.balance + DK_current.balance</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="account_balance_report_l10n_dk_active_1" model="account.report.line">
                        <field name="name">1. Fixed assets</field>
                        <field name="code">DK_fixed</field>
                        <field name="aggregation_formula">DK_intagible_fixed.balance + DK_tangible_fixed.balance + DK_financial_fixed.balance</field>
                        <field name="children_ids">
                            <record id="account_balance_report_l10n_dk_active_1_1" model="account.report.line">
                                <field name="name">a) Intangible fixed assets</field>
                                <field name="code">DK_intagible_fixed</field>
                                <field name="aggregation_formula">DK_goodwill.balance + DK_aquired_intangible.balance</field>
                                <field name="children_ids">
                                    <record id="account_balance_report_l10n_dk_active_1_1_1" model="account.report.line">
                                        <field name="name">1. Goodwill</field>
                                        <field name="code">DK_goodwill</field>
                                        <field name="aggregation_formula">DK_goodwill_book_begin.balance + DK_goodwill_additions.balance + DK_goodwill_departures.balance + DK_goodwill_other_value.balance + DK_goodwill_amortization_impair_year.balance + DK_goodwill_amortization_impair_revers.balance</field>
                                        <field name="children_ids">
                                            <record id="account_balance_report_l10n_dk_active_1_1_1_1" model="account.report.line">
                                                <field name="name">a) Goodwill, book value at beginning of period</field>
                                                <field name="code">DK_goodwill_book_begin</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_1_1_1_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5010)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_1_1_2" model="account.report.line">
                                                <field name="name">b) Goodwill, additions during the year</field>
                                                <field name="code">DK_goodwill_additions</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_1_1_2_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5020)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_1_1_3" model="account.report.line">
                                                <field name="name">c) Goodwill, departures during the year</field>
                                                <field name="code">DK_goodwill_departures</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_1_1_3_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5030)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_1_1_4" model="account.report.line">
                                                <field name="name">d) Goodwill, other value adjustments</field>
                                                <field name="code">DK_goodwill_other_value</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_1_1_4_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5040)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_1_1_5" model="account.report.line">
                                                <field name="name">e) Goodwill, amortization and impairment for the year</field>
                                                <field name="code">DK_goodwill_amortization_impair_year</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_1_1_5_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5050)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_1_1_6" model="account.report.line">
                                                <field name="name">f) Goodwill, amortization and impairment reversals</field>
                                                <field name="code">DK_goodwill_amortization_impair_revers</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_1_1_6_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5060)</field>
                                                    </record>
                                                </field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_active_1_1_2" model="account.report.line">
                                        <field name="name">2. Acquired intangible fixed assets</field>
                                        <field name="code">DK_aquired_intangible</field>
                                        <field name="aggregation_formula">DK_acquired_intangible_book.balance + DK_acquired_intangible_addition.balance + DK_acquired_intangible_disposals.balance + DK_acquired_intangible_other.balance + DK_acquired_intangible_amort.balance + DK_acquired_intangible_reversed.balance</field>
                                        <field name="children_ids">
                                            <record id="account_balance_report_l10n_dk_active_1_1_2_1" model="account.report.line">
                                                <field name="name">a) Acquired intangible fixed assets, book value at beginning of period</field>
                                                <field name="code">DK_acquired_intangible_book</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_1_2_1_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5080)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_1_2_2" model="account.report.line">
                                                <field name="name">b) Acquired intangible fixed assets, additions during the year</field>
                                                <field name="code">DK_acquired_intangible_addition</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_1_2_2_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5090)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_1_2_3" model="account.report.line">
                                                <field name="name">c) Acquired intangible fixed assets, disposals during the year</field>
                                                <field name="code">DK_acquired_intangible_disposals</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_1_2_3_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5100)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_1_2_4" model="account.report.line">
                                                <field name="name">d) Acquired intangible fixed assets, other value adjustments</field>
                                                <field name="code">DK_acquired_intangible_other</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_1_2_4_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5110)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_1_2_5" model="account.report.line">
                                                <field name="name">e) Acquired intangible assets, amortization and impairment losses for the year</field>
                                                <field name="code">DK_acquired_intangible_amort</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_1_2_5_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5120)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_1_2_6" model="account.report.line">
                                                <field name="name">f) Acquired intangible assets, amortization and impairment losses reversed</field>
                                                <field name="code">DK_acquired_intangible_reversed</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_1_2_6_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5130)</field>
                                                    </record>
                                                </field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_balance_report_l10n_dk_active_1_2" model="account.report.line">
                                <field name="name">b) Tangible fixed assets</field>
                                <field name="code">DK_tangible_fixed</field>
                                <field name="aggregation_formula">DK_investement_properties.balance + DK_investement_properties_const.balance + DK_land_buildings.balance + DK_prod_plant_machin.balance + DK_furnishing_rented.balance + DK_other_fixtures.balance + DK_tangible_fixes_constr.balance + DK_assets_under_leases.balance</field>
                                <field name="children_ids">
                                    <record id="account_balance_report_l10n_dk_active_1_2_1" model="account.report.line">
                                        <field name="name">1. Investment properties</field>
                                        <field name="code">DK_investement_properties</field>
                                        <field name="aggregation_formula">DK_invest_book_begin.balance + DK_invest_additions.balance + DK_invest_dispo.balance + DK_invest_improvement.balance + DK_invest_other.balance + DK_invest_amort.balance + DK_invest_revers.balance</field>
                                        <field name="children_ids">
                                            <record id="account_balance_report_l10n_dk_active_1_2_1_1" model="account.report.line">
                                                <field name="name">a) Investment properties, book value at beginning of period</field>
                                                <field name="code">DK_invest_book_begin</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_2_1_1_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5160)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_2_1_2" model="account.report.line">
                                                <field name="name">b) Investment properties, additions during the year</field>
                                                <field name="code">DK_invest_additions</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_2_1_2_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5170)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_2_1_3" model="account.report.line">
                                                <field name="name">c) Investment properties, disposals during the year</field>
                                                <field name="code">DK_invest_dispo</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_2_1_3_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5180)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_2_1_4" model="account.report.line">
                                                <field name="name">d) Investment properties, improvements during the year</field>
                                                <field name="code">DK_invest_improvement</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_2_1_4_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5190)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_2_1_5" model="account.report.line">
                                                <field name="name">e) Investment properties, other value adjustments</field>
                                                <field name="code">DK_invest_other</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_2_1_5_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5200)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_2_1_6" model="account.report.line">
                                                <field name="name">f) Investment property, depreciation, amortization and impairment for the year</field>
                                                <field name="code">DK_invest_amort</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_2_1_6_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5210)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_2_1_7" model="account.report.line">
                                                <field name="name">g) Investment properties, reversal of depreciation and impairment losses</field>
                                                <field name="code">DK_invest_revers</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_2_1_7_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5220)</field>
                                                    </record>
                                                </field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_active_1_2_2" model="account.report.line">
                                        <field name="name">2. Investment properties under construction</field>
                                        <field name="code">DK_investement_properties_const</field>
                                        <field name="aggregation_formula">DK_invest_constr_book_begin.balance + DK_invest_constr_additions.balance + DK_invest_disposals.balance + DK_invest_improvements.balance + DK_invest_other_value.balance + DK_invest_impair.balance + DK_invest_revers_losses.balance</field>
                                        <field name="children_ids">
                                            <record id="account_balance_report_l10n_dk_active_1_2_2_1" model="account.report.line">
                                                <field name="name">a) Investment properties under construction, book value at beginning of period</field>
                                                <field name="code">DK_invest_constr_book_begin</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_2_2_1_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5240)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_2_2_2" model="account.report.line">
                                                <field name="name">b) Investment properties under construction, additions during the year</field>
                                                <field name="code">DK_invest_constr_additions</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_2_2_2_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5250)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_2_2_3" model="account.report.line">
                                                <field name="name">c) Investment properties under construction, disposals during the year</field>
                                                <field name="code">DK_invest_disposals</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_2_2_3_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5260)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_2_2_4" model="account.report.line">
                                                <field name="name">d) Investment properties under construction, improvements during the year</field>
                                                <field name="code">DK_invest_improvements</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_2_2_4_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5270)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_2_2_5" model="account.report.line">
                                                <field name="name">e) Investment properties under construction, other value adjustments</field>
                                                <field name="code">DK_invest_other_value</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_2_2_5_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5280)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_2_2_6" model="account.report.line">
                                                <field name="name">f) Investment properties under construction, impairment losses for the year</field>
                                                <field name="code">DK_invest_impair</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_2_2_6_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5290)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_2_2_7" model="account.report.line">
                                                <field name="name">g) Investment properties under construction, reversal of impairment losses</field>
                                                <field name="code">DK_invest_revers_losses</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_2_2_7_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5300)</field>
                                                    </record>
                                                </field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_active_1_2_3" model="account.report.line">
                                        <field name="name">3. Land and buildings</field>
                                        <field name="code">DK_land_buildings</field>
                                        <field name="aggregation_formula">DK_land_buildings_book.balance + DK_land_buildings_addition.balance + DK_land_buildings_end.balance + DK_land_buildings_improvements.balance +DK_land_buildings_other.balance + DK_land_buildings_depreciation.balance + DK_land_buildings_reveral.balance</field>
                                        <field name="children_ids">
                                            <record id="account_balance_report_l10n_dk_active_1_2_3_1" model="account.report.line">
                                                <field name="name">a) Land and buildings, book value at beginning of period</field>
                                                <field name="code">DK_land_buildings_book</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_2_3_1_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5320)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_2_3_2" model="account.report.line">
                                                <field name="name">b) Land and buildings, additions during the year</field>
                                                <field name="code">DK_land_buildings_addition</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_2_3_2_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5330)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_2_3_3" model="account.report.line">
                                                <field name="name">c) Land and buildings, end of year</field>
                                                <field name="code">DK_land_buildings_end</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_2_3_3_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5340)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_2_3_4" model="account.report.line">
                                                <field name="name">d) Land and buildings, improvements during the year</field>
                                                <field name="code">DK_land_buildings_improvements</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_2_3_4_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5350)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_2_3_5" model="account.report.line">
                                                <field name="name">e) Land and buildings, other value adjustments</field>
                                                <field name="code">DK_land_buildings_other</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_2_3_5_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5370)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_2_3_6" model="account.report.line">
                                                <field name="name">f) Land and buildings, depreciation and impairment for the year</field>
                                                <field name="code">DK_land_buildings_depreciation</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_2_3_6_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5390)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_2_3_7" model="account.report.line">
                                                <field name="name">g) Land and buildings, depreciation and impairment reversals</field>
                                                <field name="code">DK_land_buildings_reveral</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_2_3_7_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5400)</field>
                                                    </record>
                                                </field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_active_1_2_4" model="account.report.line">
                                        <field name="name">4. Production plant and machinery</field>
                                        <field name="code">DK_prod_plant_machin</field>
                                        <field name="aggregation_formula">DK_plant_machinery_book.balance + DK_plant_machinery_annual.balance + DK_plant_machinery_end.balance + DK_plant_machinery_other.balance + DK_plant_machinery_depreciation.balance + DK_plant_machinery_revers.balance</field>
                                        <field name="children_ids">
                                            <record id="account_balance_report_l10n_dk_active_1_2_4_1" model="account.report.line">
                                                <field name="name">a) Plant and machinery, book value at beginning of period</field>
                                                <field name="code">DK_plant_machinery_book</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_2_4_1_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5420)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_2_4_2" model="account.report.line">
                                                <field name="name">b) Plant and machinery, annual additions</field>
                                                <field name="code">DK_plant_machinery_annual</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_2_4_2_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5430)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_2_4_3" model="account.report.line">
                                                <field name="name">c) Plant and machinery, end of year</field>
                                                <field name="code">DK_plant_machinery_end</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_2_4_3_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5440)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_2_4_4" model="account.report.line">
                                                <field name="name">d) Plant and machinery, other value adjustments</field>
                                                <field name="code">DK_plant_machinery_other</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_2_4_4_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5450)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_2_4_5" model="account.report.line">
                                                <field name="name">e) Plant and machinery, depreciation and amortization for the year</field>
                                                <field name="code">DK_plant_machinery_depreciation</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_2_4_5_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5470)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_2_4_6" model="account.report.line">
                                                <field name="name">f) Plant and machinery, depreciation and write-offs reversed</field>
                                                <field name="code">DK_plant_machinery_revers</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_2_4_6_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5480)</field>
                                                    </record>
                                                </field>
                                            </record>

                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_active_1_2_5" model="account.report.line">
                                        <field name="name">5. Furnishing of rented premises</field>
                                        <field name="code">DK_furnishing_rented</field>
                                        <field name="aggregation_formula">DK_furnishing_rented_book.balance + DK_furnishing_rented_annual.balance + DK_furnishing_rented_yearly.balance + DK_furnishing_rented_other.balance + DK_furnishing_rented_depreciation.balance + DK_furnishing_rented_reversed.balance</field>
                                        <field name="children_ids">
                                            <record id="account_balance_report_l10n_dk_active_1_2_5_1" model="account.report.line">
                                                <field name="name">a) Furnishing of rented premises, book value at beginning of period</field>
                                                <field name="code">DK_furnishing_rented_book</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_2_5_1_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5500)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_2_5_2" model="account.report.line">
                                                <field name="name">b) Furnishing of rented premises, annual approaches</field>
                                                <field name="code">DK_furnishing_rented_annual</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_2_5_2_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5510)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_2_5_3" model="account.report.line">
                                                <field name="name">c) Furnishing of rented premises, yearly departures</field>
                                                <field name="code">DK_furnishing_rented_yearly</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_2_5_3_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5520)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_2_5_4" model="account.report.line">
                                                <field name="name">d) Furnishing of rented premises, other value adjustments</field>
                                                <field name="code">DK_furnishing_rented_other</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_2_5_4_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5530)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_2_5_5" model="account.report.line">
                                                <field name="name">e) Furnishing of rented premises, depreciation and amortization for the year</field>
                                                <field name="code">DK_furnishing_rented_depreciation</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_2_5_5_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5540)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_2_5_6" model="account.report.line">
                                                <field name="name">f) Furnishing of rented premises, depreciation and write-offs reversed</field>
                                                <field name="code">DK_furnishing_rented_reversed</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_2_5_6_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5550)</field>
                                                    </record>
                                                </field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_active_1_2_6" model="account.report.line">
                                        <field name="name">6. Other fixtures and fittings, tools and equipment</field>
                                        <field name="code">DK_other_fixtures</field>
                                        <field name="aggregation_formula">DK_other_fixtures_book.balance + DK_other_fixtures_additions.balance + DK_other_fixtures_disposal.balance + DK_other_fixtures_other.balance + DK_other_fixtures_depreciation.balance + DK_other_fixtures_reversed.balance</field>
                                        <field name="children_ids">
                                            <record id="account_balance_report_l10n_dk_active_1_2_6_1" model="account.report.line">
                                                <field name="name">a) Other fixtures and fittings, tools and equipment, book value at beginning of period</field>
                                                <field name="code">DK_other_fixtures_book</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_2_6_1_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5570)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_2_6_2" model="account.report.line">
                                                <field name="name">b) Other fixtures and fittings, tools and equipment, additions during the year</field>
                                                <field name="code">DK_other_fixtures_additions</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_2_6_2_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5580)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_2_6_3" model="account.report.line">
                                                <field name="name">c) Other fixtures and fittings, tools and equipment, disposals during the year</field>
                                                <field name="code">DK_other_fixtures_disposal</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_2_6_3_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5590)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_2_6_4" model="account.report.line">
                                                <field name="name">d) Other fixtures and fittings, tools and equipment, other value adjustments</field>
                                                <field name="code">DK_other_fixtures_other</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_2_6_4_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5600)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_2_6_5" model="account.report.line">
                                                <field name="name">e) Other fixtures and fittings, tools and equipment, depreciation and amortization for the year</field>
                                                <field name="code">DK_other_fixtures_depreciation</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_2_6_5_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5610)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_2_6_6" model="account.report.line">
                                                <field name="name">f) Other fixtures and fittings, furniture and equipment, depreciation and write-offs reversed</field>
                                                <field name="code">DK_other_fixtures_reversed</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_2_6_6_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5620)</field>
                                                    </record>
                                                </field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_active_1_2_7" model="account.report.line">
                                        <field name="name">7. Tangible fixed assets under construction and prepayments for tangible fixed assets</field>
                                        <field name="code">DK_tangible_fixes_constr</field>
                                        <field name="aggregation_formula">DK_tangible_fixes_constr_book.balance + DK_tangible_fixes_constr_additions.balance + DK_tangible_fixes_constr_other.balance + DK_tangible_fixes_constr_disposal.balance + DK_tangible_fixes_constr_impairment.balance +DK_tangible_fixes_constr_reversal.balance </field>
                                        <field name="children_ids">
                                            <record id="account_balance_report_l10n_dk_active_1_2_7_1" model="account.report.line">
                                                <field name="name">a) Tangible fixed assets under construction and prepayments for tangible fixed assets, book value at beginning of period</field>
                                                <field name="code">DK_tangible_fixes_constr_book</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_2_7_1_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5640)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_2_7_2" model="account.report.line">
                                                <field name="name">b) Tangible fixed assets under construction and prepayments for tangible fixed assets, additions during the year</field>
                                                <field name="code">DK_tangible_fixes_constr_additions</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_2_7_2_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5650)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_2_7_3" model="account.report.line">
                                                <field name="name">c) Tangible fixed assets under construction and prepayments for tangible fixed assets, disposals during the year</field>
                                                <field name="code">DK_tangible_fixes_constr_disposal</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_2_7_3_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5660)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_2_7_4" model="account.report.line">
                                                <field name="name">d) Tangible fixed assets under construction and prepayments for tangible fixed assets, other value adjustments</field>
                                                <field name="code">DK_tangible_fixes_constr_other</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_2_7_4_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5670)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_2_7_5" model="account.report.line">
                                                <field name="name">e) Tangible fixed assets under construction and prepayments for tangible fixed assets, impairment losses for the year</field>
                                                <field name="code">DK_tangible_fixes_constr_impairment</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_2_7_5_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5680)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_2_7_6" model="account.report.line">
                                                <field name="name">f) Tangible fixed assets under construction and prepayments for tangible fixed assets, reversal of impairment losses</field>
                                                <field name="code">DK_tangible_fixes_constr_reversal</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_2_7_6_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5690)</field>
                                                    </record>
                                                </field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_active_1_2_8" model="account.report.line">
                                        <field name="name">8. Assets held under finance leases</field>
                                        <field name="code">DK_assets_under_leases</field>
                                        <field name="aggregation_formula">DK_assets_under_leases_book.balance + DK_assets_under_leases_additions.balance + DK_assets_under_leases_disposal.balance + DK_assets_under_leases_other.balance + DK_assets_under_leases_depreciation.balance + DK_assets_under_leases_reversal.balance</field>
                                        <field name="children_ids">
                                            <record id="account_balance_report_l10n_dk_active_1_2_8_1" model="account.report.line">
                                                <field name="name">a) Assets held under finance leases, book value at beginning of period</field>
                                                <field name="code">DK_assets_under_leases_book</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_2_8_1_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5710)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_2_8_2" model="account.report.line">
                                                <field name="name">b) Assets held under finance leases, additions during the year</field>
                                                <field name="code">DK_assets_under_leases_additions</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_2_8_2_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5720)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_2_8_3" model="account.report.line">
                                                <field name="name">c) Assets held under finance leases, disposals during the year</field>
                                                <field name="code">DK_assets_under_leases_disposal</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_2_8_3_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5730)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_2_8_4" model="account.report.line">
                                                <field name="name">d) Assets held under finance leases, other value adjustments</field>
                                                <field name="code">DK_assets_under_leases_other</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_2_8_4_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5740)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_2_8_5" model="account.report.line">
                                                <field name="name">e) Assets held under finance leases, depreciation and amortization for the year</field>
                                                <field name="code">DK_assets_under_leases_depreciation</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_2_8_5_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5750)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_2_8_6" model="account.report.line">
                                                <field name="name">f) Assets held under finance leases, reversal of depreciation and impairment losses</field>
                                                <field name="code">DK_assets_under_leases_reversal</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_2_8_6_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5760)</field>
                                                    </record>
                                                </field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_balance_report_l10n_dk_active_1_3" model="account.report.line">
                                <field name="name">c) Financial fixed assets</field>
                                <field name="code">DK_financial_fixed</field>
                                <field name="aggregation_formula">DK_invest_affiliated_financial.balance + DK_long_receivables_affiliated.balance + DK_invest_participating_interests.balance + DK_long_receivables_participating_interests.balance + DK_other_secu_equity.balance + DK_other_non_current_receivables.balance + DK_receivables_participants.balance</field>
                                <field name="children_ids">
                                    <record id="account_balance_report_l10n_dk_active_1_3_1" model="account.report.line">
                                        <field name="name">1. Investments in affiliated enterprises</field>
                                        <field name="code">DK_invest_affiliated_financial</field>
                                        <field name="aggregation_formula">DK_invest_affiliated_book.balance + DK_invest_affiliated_addition.balance + DK_invest_affiliated_disposals.balance + DK_invest_affiliated_other.balance + DK_invest_affiliated_impairment.balance + DK_invest_affiliated_reversal.balance</field>
                                        <field name="children_ids">
                                            <record id="account_balance_report_l10n_dk_active_1_3_1_1" model="account.report.line">
                                                <field name="name">a) Investments in group enterprises, book value at beginning of period</field>
                                                <field name="code">DK_invest_affiliated_book</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_3_1_1_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5800)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_3_1_2" model="account.report.line">
                                                <field name="name">b) Investments in affiliated enterprises, additions during the year</field>
                                                <field name="code">DK_invest_affiliated_addition</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_3_1_2_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5810)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_3_1_3" model="account.report.line">
                                                <field name="name">c) Investments in affiliated enterprises, disposals during the year</field>
                                                <field name="code">DK_invest_affiliated_disposals</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_3_1_3_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5820)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_3_1_4" model="account.report.line">
                                                <field name="name">d) Investments in group enterprises, other value adjustments</field>
                                                <field name="code">DK_invest_affiliated_other</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_3_1_4_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5830)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_3_1_5" model="account.report.line">
                                                <field name="name">e) Investments in affiliated enterprises, impairment losses for the year</field>
                                                <field name="code">DK_invest_affiliated_impairment</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_3_1_5_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5840)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_3_1_6" model="account.report.line">
                                                <field name="name">f) Investments in affiliated enterprises, reversal of impairment losses</field>
                                                <field name="code">DK_invest_affiliated_reversal</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_3_1_6_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5850)</field>
                                                    </record>
                                                </field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_active_1_3_2" model="account.report.line">
                                        <field name="name">2. Long-term receivables from affiliated enterprises</field>
                                        <field name="code">DK_long_receivables_affiliated</field>
                                        <field name="aggregation_formula">DK_long_receivables_affiliated_b.balance + DK_long_receivables_affiliated_loss.balance</field>
                                        <field name="children_ids">
                                            <record id="account_balance_report_l10n_dk_active_1_3_2_1" model="account.report.line">
                                                <field name="name">a) Long-term receivables from affiliated enterprises</field>
                                                <field name="code">DK_long_receivables_affiliated_b</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_3_2_1_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5870)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_3_2_2" model="account.report.line">
                                                <field name="name">b) Impairment losses on long-term receivables from affiliated enterprises</field>
                                                <field name="code">DK_long_receivables_affiliated_loss</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_3_2_2_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5880)</field>
                                                    </record>
                                                </field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_active_1_3_3" model="account.report.line">
                                        <field name="name">3. Investments in participating interests</field>
                                        <field name="code">DK_invest_participating_interests</field>
                                        <field name="aggregation_formula">DK_invest_participating_interests_book.balance + DK_invest_participating_interests_addition.balance + DK_invest_participating_interests_disposals.balance + DK_invest_participating_interests_other.balance + DK_invest_participating_interests_impairment.balance + DK_invest_participating_interests_reversed.balance</field>
                                        <field name="children_ids">
                                            <record id="account_balance_report_l10n_dk_active_1_3_3_1" model="account.report.line">
                                                <field name="name">a) Investments in participating interests, book value at beginning of period</field>
                                                <field name="code">DK_invest_participating_interests_book</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_3_3_1_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5900)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_3_3_2" model="account.report.line">
                                                <field name="name">b) Investments in participating interests, book value at beginning of period</field>
                                                <field name="code">DK_invest_participating_interests_addition</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_3_3_2_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5910)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_3_3_3" model="account.report.line">
                                                <field name="name">c) Investments in participating interests, disposals during the year</field>
                                                <field name="code">DK_invest_participating_interests_disposals</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_3_3_3_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5920)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_3_3_4" model="account.report.line">
                                                <field name="name">d) Investments in participating interests, other value adjustments</field>
                                                <field name="code">DK_invest_participating_interests_other</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_3_3_4_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5930)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_3_3_5" model="account.report.line">
                                                <field name="name">e) Participating interests, impairment losses for the year</field>
                                                <field name="code">DK_invest_participating_interests_impairment</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_3_3_5_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5940)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_3_3_6" model="account.report.line">
                                                <field name="name">f) Equity investments, opening book value, impairment losses reversed</field>
                                                <field name="code">DK_invest_participating_interests_reversed</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_3_3_6_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5950)</field>
                                                    </record>
                                                </field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_active_1_3_4" model="account.report.line">
                                        <field name="name">4. Long-term receivables from participating interests</field>
                                        <field name="code">DK_long_receivables_participating_interests</field>
                                        <field name="aggregation_formula">DK_long_receivables_participating_interests_b.balance + DK_long_receivables_participating_loss.balance</field>
                                        <field name="children_ids">
                                            <record id="account_balance_report_l10n_dk_active_1_3_4_1" model="account.report.line">
                                                <field name="name">a) Long-term receivables from participating interests</field>
                                                <field name="code">DK_long_receivables_participating_interests_b</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_3_4_1_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5970)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_3_4_2" model="account.report.line">
                                                <field name="name">b) Impairment loss on long-term receivables from participating interests</field>
                                                <field name="code">DK_long_receivables_participating_loss</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_3_4_2_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_5980)</field>
                                                    </record>
                                                </field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_active_1_3_5" model="account.report.line">
                                        <field name="name">5. Other securities and equity</field>
                                        <field name="code">DK_other_secu_equity</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_active_1_3_5_1_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">tag(l10n_dk.account_tag_6000)</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_active_1_3_6" model="account.report.line">
                                        <field name="name">6. Other (non-current) receivables</field>
                                        <field name="code">DK_other_non_current_receivables</field>
                                        <field name="aggregation_formula">DK_deffered_tax.balance + DK_other_non_current_receivables_b.balance + DK_deposits.balance</field>
                                        <field name="children_ids">
                                            <record id="account_balance_report_l10n_dk_active_1_3_6_1" model="account.report.line">
                                                <field name="name">a) Deferred tax assets</field>
                                                <field name="code">DK_deffered_tax</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_3_6_1_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_6020)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_3_6_2" model="account.report.line">
                                                <field name="name">b) Other (non-current) receivables</field>
                                                <field name="code">DK_other_non_current_receivables_b</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_3_6_2_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_6030)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_1_3_6_3" model="account.report.line">
                                                <field name="name">c) Deposits</field>
                                                <field name="code">DK_deposits</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_1_3_6_3_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_6040)</field>
                                                    </record>
                                                </field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_active_1_3_7" model="account.report.line">
                                        <field name="name">7. Receivables from enterprise participants and management</field>
                                        <field name="code">DK_receivables_participants</field>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_active_1_3_7_1_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">tag(l10n_dk.account_tag_6060)</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="account_balance_report_l10n_dk_active_2" model="account.report.line">
                        <field name="name">2. Current assets</field>
                        <field name="code">DK_current</field>
                        <field name="aggregation_formula">DK_inventories.balance + DK_receivable.balance + DK_secu_equity_invest.balance + DK_cash.balance</field>
                        <field name="children_ids">
                            <record id="account_balance_report_l10n_dk_active_2_1" model="account.report.line">
                                <field name="name">a) Inventories</field>
                                <field name="code">DK_inventories</field>
                                <field name="aggregation_formula">DK_raw_mat_consu_write_down.balance + DK_goods_manu_write_down.balance + DK_manu_goods_impairment.balance + DK_prepayments_goods.balance</field>
                                <field name="children_ids">
                                    <record id="account_balance_report_l10n_dk_active_2_1_1" model="account.report.line">
                                        <field name="name">1. Raw materials, consumables and write-down</field>
                                        <field name="code">DK_raw_mat_consu_write_down</field>
                                        <field name="aggregation_formula">DK_raw_mat_consu.balance + DK_write_down_raw_mat_consu.balance</field>
                                        <field name="children_ids">
                                            <record id="account_balance_report_l10n_dk_active_2_1_1_1" model="account.report.line">
                                                <field name="name">a) Raw materials and consumables</field>
                                                <field name="code">DK_raw_mat_consu</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_2_1_1_1_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_6080)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_2_1_1_2" model="account.report.line">
                                                <field name="name">b) Write-down of raw materials and consumables</field>
                                                <field name="code">DK_write_down_raw_mat_consu</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_2_1_1_2_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_6090)</field>
                                                    </record>
                                                </field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_active_2_1_2" model="account.report.line">
                                        <field name="name">2. Goods in the course of manufacture and write-down</field>
                                        <field name="code">DK_goods_manu_write_down</field>
                                        <field name="aggregation_formula">DK_goods_manu.balance + DK_write_down_work.balance</field>
                                        <field name="children_ids">
                                            <record id="account_balance_report_l10n_dk_active_2_1_2_1" model="account.report.line">
                                                <field name="name">a) Goods in the course of manufacture</field>
                                                <field name="code">DK_goods_manu</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_2_1_2_1_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_6110)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_2_1_2_2" model="account.report.line">
                                                <field name="name">b) Write-down of work in progress</field>
                                                <field name="code">DK_write_down_work</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_2_1_2_2_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_6120)</field>
                                                    </record>
                                                </field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_active_2_1_3" model="account.report.line">
                                        <field name="name">3. Manufactured and traded goods and impairment losses</field>
                                        <field name="code">DK_manu_goods_impairment</field>
                                        <field name="aggregation_formula">DK_goods_manu_traded.balance + DK_impairment_losses_manu_goods.balance</field>
                                        <field name="children_ids">
                                            <record id="account_balance_report_l10n_dk_active_2_1_3_1" model="account.report.line">
                                                <field name="name">a) Manufactured and traded goods</field>
                                                <field name="code">DK_goods_manu_traded</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_2_1_3_1_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_6140) + tag(l10n_dk.account_tag_6180)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_2_1_3_2" model="account.report.line">
                                                <field name="name">b) Impairment losses on manufactured goods and goods for resale</field>
                                                <field name="code">DK_impairment_losses_manu_goods</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_2_1_3_2_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_6150)</field>
                                                    </record>
                                                </field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_active_2_1_4" model="account.report.line">
                                        <field name="name">4. Prepayments for goods</field>
                                        <field name="code">DK_prepayments_goods</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_active_2_1_4_1_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">tag(l10n_dk.account_tag_6170) + tag(l10n_dk.account_tag_6483) + tag(l10n_dk.account_tag_6484)</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_balance_report_l10n_dk_active_2_2" model="account.report.line">
                                <field name="name">b) Receivables</field>
                                <field name="code">DK_receivable</field>
                                <field name="aggregation_formula">DK_trade_other_receiv_accumul.balance + DK_current_receivables_affiliated_other.balance + DK_work_in_progress_third.balance + DK_other_receivables_current.balance + DK_claims_payment_company.balance + DK_current_receivables.balance + DK_prepayment_accrued_income.balance</field>
                                <field name="children_ids">
                                    <record id="account_balance_report_l10n_dk_active_2_2_1" model="account.report.line">
                                        <field name="name">1. Trade and other receivables and accumulated provision</field>
                                        <field name="code">DK_trade_other_receiv_accumul</field>
                                        <field name="aggregation_formula">DK_trade_other_receiv.balance + DK_accumulated_provision.balance</field>
                                        <field name="children_ids">
                                            <record id="account_balance_report_l10n_dk_active_2_2_1_1" model="account.report.line">
                                                <field name="name">a) Trade and other receivables</field>
                                                <field name="code">DK_trade_other_receiv</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_2_2_1_1_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_6190)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_2_2_1_2" model="account.report.line">
                                                <field name="name">b) Accumulated provision for impairment losses on trade receivables</field>
                                                <field name="code">DK_accumulated_provision</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_2_2_1_2_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_6200)</field>
                                                    </record>
                                                </field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_active_2_2_2" model="account.report.line">
                                        <field name="name">2. Current receivables from affiliated enterprises and other</field>
                                        <field name="code">DK_current_receivables_affiliated_other</field>
                                        <field name="aggregation_formula">DK_current_receivable_affiliated.balance + DK_accumulated_impairment_loss_receivables.balance + DK_short_receiv_participating.balance + DK_accumulated_impairment_losses_loans.balance</field>
                                        <field name="children_ids">
                                            <record id="account_balance_report_l10n_dk_active_2_2_2_1" model="account.report.line">
                                                <field name="name">a) Current receivables from affiliated enterprises</field>
                                                <field name="code">DK_current_receivable_affiliated</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_2_2_2_1_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_6220)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_2_2_2_2" model="account.report.line">
                                                <field name="name">b) Accumulated impairment losses for losses on receivables from group undertakings</field>
                                                <field name="code">DK_accumulated_impairment_loss_receivables</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_2_2_2_2_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_6230)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_2_2_2_3" model="account.report.line">
                                                <field name="name">d) Short-term receivables from participating interests</field>
                                                <field name="code">DK_short_receiv_participating</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_2_2_2_3_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_6240)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_2_2_2_4" model="account.report.line">
                                                <field name="name">e) Accumulated impairment losses for losses on loans and advances from participating interests</field>
                                                <field name="code">DK_accumulated_impairment_losses_loans</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_2_2_2_4_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_6250)</field>
                                                    </record>
                                                </field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_active_2_2_3" model="account.report.line">
                                        <field name="name">3. Work in progress on behalf of third parties</field>
                                        <field name="code">DK_work_in_progress_third</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_active_2_2_3_1_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">tag(l10n_dk.account_tag_6270)</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_active_2_2_4" model="account.report.line">
                                        <field name="name">4. Other receivables (current)</field>
                                        <field name="code">DK_other_receivables_current</field>
                                        <field name="aggregation_formula">DK_deferred_tax.balance + DK_corporation_tax_receivable.balance +DK_vat_receivable.balance + DK_withholding_tax.balance + DK_other_receivables_current_b.balance</field>
                                        <field name="children_ids">
                                            <record id="account_balance_report_l10n_dk_active_2_2_4_1" model="account.report.line">
                                                <field name="name">a) Deferred tax assets</field>
                                                <field name="code">DK_deferred_tax</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_2_2_4_1_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_6290)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_2_2_4_2" model="account.report.line">
                                                <field name="name">b) Corporation tax receivable (current)</field>
                                                <field name="code">DK_corporation_tax_receivable</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_2_2_4_2_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_6300)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_2_2_4_3" model="account.report.line">
                                                <field name="name">c) Withholding tax receivable</field>
                                                <field name="code">DK_withholding_tax</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_2_2_4_3_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_6310)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_2_2_4_4" model="account.report.line">
                                                <field name="name">d) VAT receivable (current)</field>
                                                <field name="code">DK_vat_receivable</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_2_2_4_4_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_6320)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_2_2_4_5" model="account.report.line">
                                                <field name="name">e) Other receivables (current)</field>
                                                <field name="code">DK_other_receivables_current_b</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_2_2_4_5_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_6330) + tag(l10n_dk.account_tag_5960) + tag(l10n_dk.account_tag_5961) + tag(l10n_dk.account_tag_6070)</field>
                                                    </record>
                                                </field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_active_2_2_5" model="account.report.line">
                                        <field name="name">5. Claims for payment of company capital and share premium</field>
                                        <field name="code">DK_claims_payment_company</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_active_2_2_5_1_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">tag(l10n_dk.account_tag_6350)</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_active_2_2_6" model="account.report.line">
                                        <field name="name">6. Current receivables from enterprise participants and management</field>
                                        <field name="code">DK_current_receivables</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_active_2_2_6_1_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">tag(l10n_dk.account_tag_6370)</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_active_2_2_7" model="account.report.line">
                                        <field name="name">7. Prepayments and accrued income</field>
                                        <field name="code">DK_prepayment_accrued_income</field>
                                        <field name="aggregation_formula">DK_prepayments_can_maintained.balance + DK_prepayments_cannot_maintained.balance</field>
                                        <field name="children_ids">
                                            <record id="account_balance_report_l10n_dk_active_2_2_7_1" model="account.report.line">
                                                <field name="name">a) Prepayments and accrued income that can be maintained for tax purposes</field>
                                                <field name="code">DK_prepayments_can_maintained</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_2_2_7_1_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_6390)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_balance_report_l10n_dk_active_2_2_7_2" model="account.report.line">
                                                <field name="name">b) Prepayments and accrued income that cannot be maintained for tax purposes</field>
                                                <field name="code">DK_prepayments_cannot_maintained</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_balance_report_l10n_dk_active_2_2_7_2_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_6400)</field>
                                                    </record>
                                                </field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_balance_report_l10n_dk_active_2_3" model="account.report.line">
                                <field name="name">c) Securities and equity investments</field>
                                <field name="code">DK_secu_equity_invest</field>
                                <field name="aggregation_formula">DK_invest_affiliated_secu.balance + DK_other_secu.balance</field>
                                <field name="children_ids">
                                    <record id="account_balance_report_l10n_dk_active_2_3_1" model="account.report.line">
                                        <field name="name">1. Investments in affiliated enterprises</field>
                                        <field name="code">DK_invest_affiliated_secu</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_active_2_3_1_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">tag(l10n_dk.account_tag_6420)</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_active_2_3_2" model="account.report.line">
                                        <field name="name">2. Other securities and equity</field>
                                        <field name="code">DK_other_secu</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_active_2_3_2_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">tag(l10n_dk.account_tag_6450)</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_balance_report_l10n_dk_active_2_4" model="account.report.line">
                                <field name="name">d) Cash, cash equivalents and bank account</field>
                                <field name="code">DK_cash</field>
                                <field name="aggregation_formula">DK_cash_cash_equival.balance + DK_bank_account.balance</field>
                                <field name="children_ids">
                                    <record id="account_balance_report_l10n_dk_active_2_4_1" model="account.report.line">
                                        <field name="name">a) Cash and cash equivalents</field>
                                        <field name="code">DK_cash_cash_equival</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_active_2_4_1_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">tag(l10n_dk.account_tag_6470) + tag(l10n_dk.account_tag_6471) + tag(l10n_dk.account_tag_6831)</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_active_2_4_2" model="account.report.line">
                                        <field name="name">b) Bank account</field>
                                        <field name="code">DK_bank_account</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_active_2_4_2_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">tag(l10n_dk.account_tag_6480) + tag(l10n_dk.account_tag_6481) + tag(l10n_dk.account_tag_6482)</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="account_balance_report_l10n_dk_passiv" model="account.report.line">
                <field name="name">Passive</field>
                <field name="code">DK_passive</field>
                <field name="aggregation_formula">DK_equity_capital.balance + DK_provisions.balance + DK_long_term_debt.balance + DK_short_term_debt.balance</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="account_balance_report_l10n_dk_passiv_1_1" model="account.report.line">
                        <field name="name">1. Equity capital</field>
                        <field name="code">DK_equity_capital</field>
                        <field name="aggregation_formula">DK_working_capital.balance + DK_share_premium.balance + DK_reval_reserve.balance + DK_reserve_reval.balance + DK_other_reserves_equity.balance + DK_retained_earnings.balance + DK_proposed_dividend.balance</field>
                        <field name="children_ids">
                            <record id="account_balance_report_l10n_dk_passiv_1_1_1" model="account.report.line">
                                <field name="name">a) Working capital</field>
                                <field name="code">DK_working_capital</field>
                                <field name="aggregation_formula">DK_registered_capital.balance + DK_paid_up_capital.balance </field>
                                <field name="children_ids">
                                    <record id="account_balance_report_l10n_dk_passiv_1_1_1_1" model="account.report.line">
                                        <field name="name">1. Registered capital, etc.</field>
                                        <field name="code">DK_registered_capital</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_passiv_1_1_1_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_dk.account_tag_6510)</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_passiv_1_1_1_2" model="account.report.line">
                                        <field name="name">2. Paid-up registered capital, etc.</field>
                                        <field name="code">DK_paid_up_capital</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_passiv_1_1_1_2_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_dk.account_tag_6520)</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_balance_report_l10n_dk_passiv_1_1_2" model="account.report.line">
                                <field name="name">b) Share premium on issue</field>
                                <field name="code">DK_share_premium</field>
                                <field name="groupby">account_id</field>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_balance_report_l10n_dk_passiv_1_1_2_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-tag(l10n_dk.account_tag_6540)</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_balance_report_l10n_dk_passiv_1_1_3" model="account.report.line">
                                <field name="name">c) Revaluation reserve</field>
                                <field name="code">DK_reval_reserve</field>
                                <field name="groupby">account_id</field>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_balance_report_l10n_dk_passiv_1_1_3_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-tag(l10n_dk.account_tag_6560)</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_balance_report_l10n_dk_passiv_1_1_4" model="account.report.line">
                                <field name="name">d) Reserve for net revaluation under the equity method</field>
                                <field name="code">DK_reserve_reval</field>
                                <field name="groupby">account_id</field>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_balance_report_l10n_dk_passiv_1_1_4_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-tag(l10n_dk.account_tag_6580)</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_balance_report_l10n_dk_passiv_1_1_5" model="account.report.line">
                                <field name="name">e) Other reserves</field>
                                <field name="code">DK_other_reserves_equity</field>
                                <field name="aggregation_formula">DK_reserve_loans.balance + DK_reserve_unpaid_working.balance + DK_other_statutory_reserves.balance + DK_statutory_reserves.balance + DK_other_reserves.balance</field>
                                <field name="children_ids">
                                    <record id="account_balance_report_l10n_dk_passiv_1_1_5_1" model="account.report.line">
                                        <field name="name">1. Reserve for loans and collateral</field>
                                        <field name="code">DK_reserve_loans</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_passiv_1_1_5_1_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_dk.account_tag_6810)</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_passiv_1_1_5_2" model="account.report.line">
                                        <field name="name">2. Reserve for unpaid working capital and share premium account</field>
                                        <field name="code">DK_reserve_unpaid_working</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_passiv_1_1_5_2_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_dk.account_tag_6830)</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_passiv_1_1_5_3" model="account.report.line">
                                        <field name="name">3. Other statutory reserves</field>
                                        <field name="code">DK_other_statutory_reserves</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_passiv_1_1_5_3_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_dk.account_tag_6870)</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_passiv_1_1_5_4" model="account.report.line">
                                        <field name="name">3. Statutory reserves</field>
                                        <field name="code">DK_statutory_reserves</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_passiv_1_1_5_4_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_dk.account_tag_6890)</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_passiv_1_1_5_5" model="account.report.line">
                                        <field name="name">4. Other reserves</field>
                                        <field name="code">DK_other_reserves</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_passiv_1_1_5_5_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_dk.account_tag_6910)</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_balance_report_l10n_dk_passiv_1_1_5_6" model="account.report.line">
                                <field name="name">f) Retained earnings</field>
                                <field name="code">DK_retained_earnings</field>
                                <field name="aggregation_formula">DK_result_pnl.balance + DK_brought_forward.balance</field>
                                <field name="children_ids">
                                    <record id="account_balance_report_l10n_dk_passiv_1_1_5_6_1" model="account.report.line">
                                        <field name="name">1. Profit brought forward</field>
                                        <field name="code">DK_brought_forward</field>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_passiv_1_1_5_6_1_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_dk.account_tag_6940)</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_dk_profit_loss0" model="account.report.line">
                                        <field name="name">2. Profit or loss for the year</field>
                                        <field name="code">DK_result_pnl</field>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_dk_profit_loss0_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">aggregation</field>
                                                <field name="formula">DK_result.balance</field>
                                                <field name="subformula">cross_report</field>
                                                <field name="date_scope">from_fiscalyear</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_balance_report_l10n_dk_passiv_1_1_7" model="account.report.line">
                                <field name="name">g) Proposed dividend recognized in equity</field>
                                <field name="code">DK_proposed_dividend</field>
                                <field name="groupby">account_id</field>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_balance_report_l10n_dk_passiv_1_1_7_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-tag(l10n_dk.account_tag_6960)</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="account_balance_report_l10n_dk_passiv_1_2" model="account.report.line">
                        <field name="name">2. Provisions for liabilities and charges</field>
                        <field name="code">DK_provisions</field>
                        <field name="aggregation_formula">DK_provisions_deferred_pensions.balance + DK_other_provisions.balance </field>
                        <field name="children_ids">
                            <record id="account_balance_report_l10n_dk_passiv_1_2_1" model="account.report.line">
                                <field name="name">a) Provision for deferred taxes and pensions</field>
                                <field name="code">DK_provisions_deferred_pensions</field>
                                <field name="aggregation_formula">DK_provisions_deferred.balance + DK_provisions_pensions.balance</field>
                                <field name="children_ids">
                                    <record id="account_balance_report_l10n_dk_passiv_1_2_1_1" model="account.report.line">
                                        <field name="name">1. Provisions for deferred taxes</field>
                                        <field name="code">DK_provisions_deferred</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_passiv_1_2_1_1_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_dk.account_tag_7010)</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_passiv_1_2_1_2" model="account.report.line">
                                        <field name="name">2. Provisions for pensions and similar obligations</field>
                                        <field name="code">DK_provisions_pensions</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_passiv_1_2_1_2_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_dk.account_tag_7020)</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_balance_report_l10n_dk_passiv_1_2_2" model="account.report.line">
                                <field name="name">b) Other Provisions</field>
                                <field name="code">DK_other_provisions</field>
                                <field name="groupby">account_id</field>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_balance_report_l10n_dk_passiv_1_2_2_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-tag(l10n_dk.account_tag_7040)</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="account_balance_report_l10n_dk_passiv_1_3" model="account.report.line">
                        <field name="name">3. Long-term debt</field>
                        <field name="code">DK_long_term_debt</field>
                        <field name="aggregation_formula">DK_credit_banks.balance + DK_owed_payable_affiliated.balance + DK_other_long_term_debt.balance </field>
                        <field name="children_ids">
                            <record id="account_balance_report_l10n_dk_passiv_1_3_1" model="account.report.line">
                                <field name="name">a) Amounts owed to credit institutions and banks</field>
                                <field name="code">DK_credit_banks</field>
                                <field name="aggregation_formula">DK_credit.balance + DK_banks.balance</field>
                                <field name="children_ids">
                                    <record id="account_balance_report_l10n_dk_passiv_1_3_1_1" model="account.report.line">
                                        <field name="name">1. Amounts owed to credit institutions - long-term debt</field>
                                        <field name="code">DK_credit</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_passiv_1_3_1_1_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_dk.account_tag_7110)</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_passiv_1_3_1_2" model="account.report.line">
                                        <field name="name">2. Amounts owed to banks - long-term debt</field>
                                        <field name="code">DK_banks</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_passiv_1_3_1_2_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_dk.account_tag_7120)</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_balance_report_l10n_dk_passiv_1_3_2" model="account.report.line">
                                <field name="name">b) Amounts owed to affiliated enterprises and payable to participating interests</field>
                                <field name="code">DK_owed_payable_affiliated</field>
                                <field name="aggregation_formula">DK_owed.balance + DK_payable.balance </field>
                                <field name="children_ids">
                                    <record id="account_balance_report_l10n_dk_passiv_1_3_2_1" model="account.report.line">
                                        <field name="name">1. Amounts owed to affiliated enterprises - long-term debt</field>
                                        <field name="code">DK_owed</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_passiv_1_3_2_1_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_dk.account_tag_7160)</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_passiv_1_3_2_2" model="account.report.line">
                                        <field name="name">2. Amounts payable to participating interests - long-term debt</field>
                                        <field name="code">DK_payable</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_passiv_1_3_2_2_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_dk.account_tag_7170)</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_balance_report_l10n_dk_passiv_1_3_3" model="account.report.line">
                                <field name="name">c) Other debts, including taxes and social security contributions payable</field>
                                <field name="code">DK_other_long_term_debt</field>
                                <field name="aggregation_formula">DK_other_payables_debts.balance + DK_owed_shareholders.balance + DK_other_deposits.balance + DK_leasing_obligation.balance + DK_corporation_tax.balance</field>
                                <field name="children_ids">
                                    <record id="account_balance_report_l10n_dk_passiv_1_3_3_1" model="account.report.line">
                                        <field name="name">1. Other payables</field>
                                        <field name="code">DK_other_payables_debts</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_passiv_1_3_3_1_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_dk.account_tag_7190) - tag(l10n_dk.account_tag_7180)</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_passiv_1_3_3_2" model="account.report.line">
                                        <field name="name">2. Amounts owed to shareholders and management</field>
                                        <field name="code">DK_owed_shareholders</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_passiv_1_3_3_2_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_dk.account_tag_7210)</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_passiv_1_3_3_3" model="account.report.line">
                                        <field name="name">3. Deposits</field>
                                        <field name="code">DK_other_deposits</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_passiv_1_3_3_3_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_dk.account_tag_7230)</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_passiv_1_3_3_4" model="account.report.line">
                                        <field name="name">4. Leasing obligation</field>
                                        <field name="code">DK_leasing_obligation</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_passiv_1_3_3_4_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_dk.account_tag_7240)</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_passiv_1_3_3_5" model="account.report.line">
                                        <field name="name">4. Corporation tax</field>
                                        <field name="code">DK_corporation_tax</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_passiv_1_3_3_5_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_dk.account_tag_7250)</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="account_balance_report_l10n_dk_passiv_1_4" model="account.report.line">
                        <field name="name">4. Short-term debt</field>
                        <field name="code">DK_short_term_debt</field>
                        <field name="aggregation_formula">DK_owed_credit_institutions_bank_other.balance + DK_prepayments_custo.balance + DK_suppliers_goods_services.balance + DK_owed_payables_short_term.balance + DK_other_debts_taxes.balance + DK_prepayments_accrued_income.balance</field>
                        <field name="children_ids">
                            <record id="account_balance_report_l10n_dk_passiv_1_4_1" model="account.report.line">
                                <field name="name">a) Amounts owed to credit institutions, banks and other</field>
                                <field name="code">DK_owed_credit_institutions_bank_other</field>
                                <field name="aggregation_formula">DK_owed_credit_institutions.balance + DK_owed_bank.balance + DK_other_credit_institutions.balance</field>
                                <field name="children_ids">
                                    <record id="account_balance_report_l10n_dk_passiv_1_4_1_1" model="account.report.line">
                                        <field name="name">1) Amounts owed to credit institutions</field>
                                        <field name="code">DK_owed_credit_institutions</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_passiv_1_4_1_1_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_dk.account_tag_7310)</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_passiv_1_4_1_2" model="account.report.line">
                                        <field name="name">2) Amounts owed to banks</field>
                                        <field name="code">DK_owed_bank</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_passiv_1_4_1_2_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_dk.account_tag_7330)</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_passiv_1_4_1_3" model="account.report.line">
                                        <field name="name">3) Other credit institutions</field>
                                        <field name="code">DK_other_credit_institutions</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_passiv_1_4_1_3_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_dk.account_tag_7350)</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_balance_report_l10n_dk_passiv_1_4_2" model="account.report.line">
                                <field name="name">b) Prepayments received from customers</field>
                                <field name="code">DK_prepayments_custo</field>
                                <field name="groupby">account_id</field>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_balance_report_l10n_dk_passiv_1_4_2_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-tag(l10n_dk.account_tag_7410)</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_balance_report_l10n_dk_passiv_1_4_3" model="account.report.line">
                                <field name="name">c) Suppliers of goods and services</field>
                                <field name="code">DK_suppliers_goods_services</field>
                                <field name="groupby">account_id</field>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_balance_report_l10n_dk_passiv_1_4_3_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-tag(l10n_dk.account_tag_7440)</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_balance_report_l10n_dk_passiv_1_4_4" model="account.report.line">
                                <field name="name">d) Amounts owed to affiliated enterprises and payable to participating interests</field>
                                <field name="code">DK_owed_payables_short_term</field>
                                <field name="aggregation_formula">DK_owed_short_term.balance + DK_payable_short_term.balance</field>
                                <field name="children_ids">
                                    <record id="account_balance_report_l10n_dk_passiv_1_4_4_1" model="account.report.line">
                                        <field name="name">1) Amounts owed to affiliated enterprises</field>
                                        <field name="code">DK_owed_short_term</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_passiv_1_4_4_1_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_dk.account_tag_7510)</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_passiv_1_4_4_2" model="account.report.line">
                                        <field name="name">2) Amounts payable to participating interests</field>
                                        <field name="code">DK_payable_short_term</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_passiv_1_4_4_2_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_dk.account_tag_7520)</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_balance_report_l10n_dk_passiv_1_4_5" model="account.report.line">
                                <field name="name">e) Other debts, including taxes and social security contributions payable</field>
                                <field name="code">DK_other_debts_taxes</field>
                                <field name="aggregation_formula">DK_owed_shareholders_management.balance + DK_deposits_short_debt.balance + DK_leasing_short_debt.balance + DK_sales_vat.balance + DK_vat_purchases_goods.balance + DK_vat_purchases_service.balance + DK_vat_purchase.balance + DK_oil_lpg_tax.balance + DK_elec_tax.balance + DK_gas_tax.balance + DK_coal_tax.balance + DK_water_tax.balance + DK_co2_tax.balance + DK_vat_due.balance + DK_wages_salaries_due.balance + DK_bonuses.balance + DK_holiday.balance + DK_tax_due.balance + DK_am_contrib.balance + DK_atp_contrib.balance + DK_amp_contrib.balance + DK_other_pension.balance + DK_other_payables.balance</field>
                                <field name="children_ids">
                                    <record id="account_balance_report_l10n_dk_passiv_1_4_5_1" model="account.report.line">
                                        <field name="name">1. Amounts owed to shareholders and management</field>
                                        <field name="code">DK_owed_shareholders_management</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_passiv_1_4_5_1_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_dk.account_tag_7590)</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_passiv_1_4_5_2" model="account.report.line">
                                        <field name="name">2. Deposits</field>
                                        <field name="code">DK_deposits_short_debt</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_passiv_1_4_5_2_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_dk.account_tag_7610)</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_passiv_1_4_5_3" model="account.report.line">
                                        <field name="name">3. Leasing liability</field>
                                        <field name="code">DK_leasing_short_debt</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_passiv_1_4_5_3_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_dk.account_tag_7630)</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_passiv_1_4_5_4" model="account.report.line">
                                        <field name="name">4. Sales VAT</field>
                                        <field name="code">DK_sales_vat</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_passiv_1_4_5_4_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_dk.account_tag_7680) - tag(l10n_dk.account_tag_7681)</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_passiv_1_4_5_5" model="account.report.line">
                                        <field name="name">5. VAT on purchases of goods abroad, EU and non-EU</field>
                                        <field name="code">DK_vat_purchases_goods</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_passiv_1_4_5_5_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_dk.account_tag_7700) - tag(l10n_dk.account_tag_7742)</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_passiv_1_4_5_6" model="account.report.line">
                                        <field name="name">6. VAT on purchases of services abroad, EU and non-EU</field>
                                        <field name="code">DK_vat_purchases_service</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_passiv_1_4_5_6_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_dk.account_tag_7720) - tag(l10n_dk.account_tag_7741)</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_passiv_1_4_5_7" model="account.report.line">
                                        <field name="name">7. Purchase VAT</field>
                                        <field name="code">DK_vat_purchase</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_passiv_1_4_5_7_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_dk.account_tag_7740)</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_passiv_1_4_5_8" model="account.report.line">
                                        <field name="name">8. Oil and LPG tax</field>
                                        <field name="code">DK_oil_lpg_tax</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_passiv_1_4_5_8_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_dk.account_tag_7760)</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_passiv_1_4_5_9" model="account.report.line">
                                        <field name="name">9. Electricity tax</field>
                                        <field name="code">DK_elec_tax</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_passiv_1_4_5_9_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_dk.account_tag_7780)</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_passiv_1_4_5_10" model="account.report.line">
                                        <field name="name">10. Natural and town gas tax</field>
                                        <field name="code">DK_gas_tax</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_passiv_1_4_5_10_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_dk.account_tag_7800)</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_passiv_1_4_5_11" model="account.report.line">
                                        <field name="name">11. Coal tax</field>
                                        <field name="code">DK_coal_tax</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_passiv_1_4_5_11_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_dk.account_tag_7810)</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_passiv_1_4_5_12" model="account.report.line">
                                        <field name="name">12. Water tax</field>
                                        <field name="code">DK_water_tax</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_passiv_1_4_5_12_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_dk.account_tag_7820)</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_passiv_1_4_5_13" model="account.report.line">
                                        <field name="name">13. CO2 tax</field>
                                        <field name="code">DK_co2_tax</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_passiv_1_4_5_13_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_dk.account_tag_7830)</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_passiv_1_4_5_14" model="account.report.line">
                                        <field name="name">14. VAT due</field>
                                        <field name="code">DK_vat_due</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_passiv_1_4_5_14_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_dk.account_tag_7840)</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_passiv_1_4_5_15" model="account.report.line">
                                        <field name="name">15. Wages and salaries due</field>
                                        <field name="code">DK_wages_salaries_due</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_passiv_1_4_5_15_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_dk.account_tag_7860)</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_passiv_1_4_5_16" model="account.report.line">
                                        <field name="name">16. Bonuses and bonuses payable</field>
                                        <field name="code">DK_bonuses</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_passiv_1_4_5_16_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_dk.account_tag_7880)</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_passiv_1_4_5_17" model="account.report.line">
                                        <field name="name">17. Holiday pay due</field>
                                        <field name="code">DK_holiday</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_passiv_1_4_5_17_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_dk.account_tag_7900)</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_passiv_1_4_5_18" model="account.report.line">
                                        <field name="name">18. Tax due</field>
                                        <field name="code">DK_tax_due</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_passiv_1_4_5_18_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_dk.account_tag_7920)</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_passiv_1_4_5_19" model="account.report.line">
                                        <field name="name">19. AM contribution due</field>
                                        <field name="code">DK_am_contrib</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_passiv_1_4_5_19_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_dk.account_tag_7940)</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_passiv_1_4_5_20" model="account.report.line">
                                        <field name="name">20. ATP contribution due</field>
                                        <field name="code">DK_atp_contrib</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_passiv_1_4_5_20_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_dk.account_tag_7960)</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_passiv_1_4_5_21" model="account.report.line">
                                        <field name="name">21. AMP contribution due</field>
                                        <field name="code">DK_amp_contrib</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_passiv_1_4_5_21_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_dk.account_tag_7980)</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_passiv_1_4_5_22" model="account.report.line">
                                        <field name="name">22. Other pension due</field>
                                        <field name="code">DK_other_pension</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_passiv_1_4_5_22_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_dk.account_tag_8000)</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_balance_report_l10n_dk_passiv_1_4_5_23" model="account.report.line">
                                        <field name="name">23. Other payables</field>
                                        <field name="code">DK_other_payables</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_balance_report_l10n_dk_passiv_1_4_5_23_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_dk.account_tag_8040)</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_balance_report_l10n_dk_passiv_1_4_6" model="account.report.line">
                                <field name="name">f) Prepayments and accrued income</field>
                                <field name="code">DK_prepayments_accrued_income</field>
                                <field name="groupby">account_id</field>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_balance_report_l10n_dk_passiv_1_4_6_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-tag(l10n_dk.account_tag_8070)</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
        </field>
    </record>
</odoo>
