<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="account_financial_report_dk_pnl" model="account.report">
        <field name="name">Profit and loss</field>
        <field name="root_report_id" ref="account_reports.profit_and_loss"/>
        <field name="filter_unfold_all" eval="True"/>
        <field name="filter_journals" eval="True"/>
        <field name="country_id" ref="base.dk"/>
        <field name="filter_multi_company">selector</field>
        <field name="column_ids">
            <record id="account_financial_report_dk_pnl_column" model="account.report.column">
                <field name="name">Balance</field>
                <field name="expression_label">balance</field>
            </record>
        </field>
        <field name="line_ids">
            <record id="account_pnl_report_l10n_dk_1" model="account.report.line">
                <field name="name">Result for the year</field>
                <field name="code">DK_result</field>
                <field name="aggregation_formula">DK_result_before_tax.balance - DK_tax_profit.balance - DK_other_tax.balance </field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="account_pnl_report_l10n_dk_1_1" model="account.report.line">
                        <field name="name">a) Result before tax</field>
                        <field name="code">DK_result_before_tax</field>
                        <field name="foldable" eval="True"/>
                        <field name="aggregation_formula">DK_gross.balance - DK_personnel_costs.balance - DK_depreciation.balance - DK_impairment.balance - DK_other_operating_costs.balance + DK_income_invest.balance + DK_income_other_invest.balance + DK_other_financial_income_affiliated.balance + DK_other_financial_income.balance + DK_impairment_financial_assets.balance - DK_financial_expenses.balance - DK_other_financial_costs.balance</field>
                        <field name="children_ids">
                            <record id="account_pnl_report_l10n_dk_1_1_1" model="account.report.line">
                                <field name="name">1. Gross profit/gross loss</field>
                                <field name="code">DK_gross</field>
                                <field name="aggregation_formula">DK_net_turnover.balance + DK_change_in_stocks.balance + DK_other_operating_income.balance - DK_cost_materials.balance - DK_other_external_costs.balance</field>
                                <field name="hierarchy_level">5</field>
                                <field name="children_ids">
                                    <record id="account_pnl_report_l10n_dk_1_1_1_1" model="account.report.line">
                                        <field name="name">1. Net turnover</field>
                                        <field name="code">DK_net_turnover</field>
                                        <field name="aggregation_formula">DK_sale_goods_services.balance + DK_sale_goods_eu.balance + DK_sale_goods_non_eu.balance + DK_sale_service_eu.balance + DK_sale_service_non_eu.balance + DK_regulation_work_progress.balance + DK_value_adjustments_property.balance</field>
                                        <field name="children_ids">
                                            <record id="account_pnl_report_l10n_dk_1_1_1_1_1" model="account.report.line">
                                                <field name="name">a) Sale of goods and services</field>
                                                <field name="code">DK_sale_goods_services</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_1_1_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">-tag(l10n_dk.account_tag_1010) - tag(l10n_dk.account_tag_1011)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_pnl_report_l10n_dk_1_1_1_1_2" model="account.report.line">
                                                <field name="name">b) Sales of goods abroad, EU</field>
                                                <field name="code">DK_sale_goods_eu</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_1_2_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">-tag(l10n_dk.account_tag_1050)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_pnl_report_l10n_dk_1_1_1_1_3" model="account.report.line">
                                                <field name="name">c) Sales of goods abroad, non-EU</field>
                                                <field name="code">DK_sale_goods_non_eu</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_1_3_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">-tag(l10n_dk.account_tag_1100)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_pnl_report_l10n_dk_1_1_1_1_4" model="account.report.line">
                                                <field name="name">d) Sales of services abroad, EU</field>
                                                <field name="code">DK_sale_service_eu</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_1_4_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">-tag(l10n_dk.account_tag_1150)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_pnl_report_l10n_dk_1_1_1_1_5" model="account.report.line">
                                                <field name="name">e) Sales of services abroad, non-EU</field>
                                                <field name="code">DK_sale_service_non_eu</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_1_5_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">-tag(l10n_dk.account_tag_1200)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_pnl_report_l10n_dk_1_1_1_1_6" model="account.report.line">
                                                <field name="name">f) Regulation of work in progress</field>
                                                <field name="code">DK_regulation_work_progress</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_1_6_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">-tag(l10n_dk.account_tag_1300)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_pnl_report_l10n_dk_1_1_1_1_7" model="account.report.line">
                                                <field name="name">g) Value adjustments on investment property</field>
                                                <field name="code">DK_value_adjustments_property</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_1_7_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">-tag(l10n_dk.account_tag_1350)</field>
                                                    </record>
                                                </field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_pnl_report_l10n_dk_1_1_1_2" model="account.report.line">
                                        <field name="name">2. Change in stocks of finished goods and work in progress</field>
                                        <field name="code">DK_change_in_stocks</field>
                                        <field name="aggregation_formula">DK_inventory_adjustment.balance + DK_write_down_stocks.balance + DK_other_changes_stocks.balance</field>
                                        <field name="children_ids">
                                            <record id="account_pnl_report_l10n_dk_1_1_1_2_1" model="account.report.line">
                                                <field name="name">a) Inventory adjustment on stocks of finished goods and work in progress</field>
                                                <field name="code">DK_inventory_adjustment</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_2_1_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">-tag(l10n_dk.account_tag_1410)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_pnl_report_l10n_dk_1_1_1_2_2" model="account.report.line">
                                                <field name="name">b) Write-down of stocks of finished goods and work in progress</field>
                                                <field name="code">DK_write_down_stocks</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_2_2_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">-tag(l10n_dk.account_tag_1430)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_pnl_report_l10n_dk_1_1_1_2_3" model="account.report.line">
                                                <field name="name">c) Other changes in stocks of finished goods and work in progress</field>
                                                <field name="code">DK_other_changes_stocks</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_2_3_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">-tag(l10n_dk.account_tag_1460)</field>
                                                    </record>
                                                </field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_pnl_report_l10n_dk_1_1_1_3" model="account.report.line">
                                        <field name="name">3. Other operating income</field>
                                        <field name="code">DK_other_operating_income</field>
                                        <field name="aggregation_formula">DK_gain_sale_intangible.balance + DK_gain_sale_tangible.balance + DK_gain_sale_financial.balance + DK_other_misc.balance</field>
                                        <field name="children_ids">
                                            <record id="account_pnl_report_l10n_dk_1_1_1_3_1" model="account.report.line">
                                                <field name="name">a) Gain on sale of intangible fixed assets</field>
                                                <field name="code">DK_gain_sale_intangible</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_3_1_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">-tag(l10n_dk.account_tag_1510)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_pnl_report_l10n_dk_1_1_1_3_2" model="account.report.line">
                                                <field name="name">b) Gain on sale of tangible fixed assets</field>
                                                <field name="code">DK_gain_sale_tangible</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_3_2_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">-tag(l10n_dk.account_tag_1530)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_pnl_report_l10n_dk_1_1_1_3_3" model="account.report.line">
                                                <field name="name">c) Gain on sale of financial fixed assets</field>
                                                <field name="code">DK_gain_sale_financial</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_3_3_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">-tag(l10n_dk.account_tag_1540)</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_pnl_report_l10n_dk_1_1_1_3_4" model="account.report.line">
                                                <field name="name">d) Other miscellaneous operating income</field>
                                                <field name="code">DK_other_misc</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_3_4_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">-tag(l10n_dk.account_tag_1550)</field>
                                                    </record>
                                                </field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_pnl_report_l10n_dk_1_1_1_4" model="account.report.line">
                                        <field name="name">4. Cost of raw materials, consumables and services</field>
                                        <field name="code">DK_cost_materials</field>
                                        <field name="aggregation_formula" eval="False"/>
                                        <field name="expression_ids">
                                            <record id="account_pnl_report_l10n_dk_1_1_1_4_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">aggregation</field>
                                                <field name="formula">
                                                    DK_purchase_goods.balance +
                                                    DK_purchase_goods_eu.balance +
                                                    DK_purchase_goods_non_eu.balance +
                                                    DK_benefit_purchases.balance +
                                                    DK_benefit_purchases_eu.balance +
                                                    DK_benefit_purchases_non_eu.balance +
                                                    DK_inventory_adjustment_stocks.balance +
                                                    DK_write_down_inventories.balance
                                                </field>
                                                <field name="green_on_positive" eval="False"/>
                                            </record>
                                        </field>
                                        <field name="children_ids">
                                            <record id="account_pnl_report_l10n_dk_1_1_1_4_1" model="account.report.line">
                                                <field name="name">a) Purchase of goods and services</field>
                                                <field name="code">DK_purchase_goods</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_4_1_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_1610) + tag(l10n_dk.account_tag_1611)</field>
                                                        <field name="green_on_positive" eval="False"/>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_pnl_report_l10n_dk_1_1_1_4_2" model="account.report.line">
                                                <field name="name">b) Purchases of goods and services abroad, EU</field>
                                                <field name="code">DK_purchase_goods_eu</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_4_2_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_1630) + tag(l10n_dk.account_tag_1631)</field>
                                                        <field name="green_on_positive" eval="False"/>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_pnl_report_l10n_dk_1_1_1_4_3" model="account.report.line">
                                                <field name="name">c) Purchases of goods and services from abroad, non-EU</field>
                                                <field name="code">DK_purchase_goods_non_eu</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_4_3_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_1650) + tag(l10n_dk.account_tag_1651)</field>
                                                        <field name="green_on_positive" eval="False"/>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_pnl_report_l10n_dk_1_1_1_4_4" model="account.report.line">
                                                <field name="name">d) Benefit purchases</field>
                                                <field name="code">DK_benefit_purchases</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_4_4_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_1660)</field>
                                                        <field name="green_on_positive" eval="False"/>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_pnl_report_l10n_dk_1_1_1_4_5" model="account.report.line">
                                                <field name="name">e) Benefit purchases abroad, EU</field>
                                                <field name="code">DK_benefit_purchases_eu</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_4_5_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_1710)</field>
                                                        <field name="green_on_positive" eval="False"/>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_pnl_report_l10n_dk_1_1_1_4_6" model="account.report.line">
                                                <field name="name">f) Benefit purchases abroad, non-EU</field>
                                                <field name="code">DK_benefit_purchases_non_eu</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_4_6_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_1740)</field>
                                                        <field name="green_on_positive" eval="False"/>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_pnl_report_l10n_dk_1_1_1_4_7" model="account.report.line">
                                                <field name="name">g) Inventory adjustment on stocks of raw materials and consumables</field>
                                                <field name="code">DK_inventory_adjustment_stocks</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_4_7_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_1770)</field>
                                                        <field name="green_on_positive" eval="False"/>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_pnl_report_l10n_dk_1_1_1_4_8" model="account.report.line">
                                                <field name="name">h) Write-down of inventories</field>
                                                <field name="code">DK_write_down_inventories</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_4_8_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">tag(l10n_dk.account_tag_1800)</field>
                                                        <field name="green_on_positive" eval="False"/>
                                                    </record>
                                                </field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_pnl_report_l10n_dk_1_1_1_5" model="account.report.line">
                                        <field name="name">5. Other external costs</field>
                                        <field name="code">DK_other_external_costs</field>
                                        <field name="aggregation_formula" eval="False"/>
                                        <field name="expression_ids">
                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">aggregation</field>
                                                <field name="formula">DK_sales_costs.balance + DK_premises_costs.balance + DK_administrative_costs.balance</field>
                                                <field name="green_on_positive" eval="False"/>
                                            </record>
                                        </field>
                                        <field name="children_ids">
                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_1" model="account.report.line">
                                                <field name="name">a) Sales costs</field>
                                                <field name="code">DK_sales_costs</field>
                                                <field name="aggregation_formula" eval="False"/>
                                                <field name="expression_ids">
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_1_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">aggregation</field>
                                                        <field name="formula">
                                                            DK_freight_costs.balance +
                                                            DK_advertising_pub.balance +
                                                            DK_exhibitions_decoration.balance +
                                                            DK_restoration_visits.balance +
                                                            DK_representation_expenses.balance +
                                                            DK_representation_expenses_fully.balance +
                                                            DK_other_selling_expenses.balance +
                                                            DK_newspapers_magazines.balance +
                                                            DK_gifts_flowers.balance</field>
                                                        <field name="green_on_positive" eval="False"/>
                                                    </record>
                                                </field>
                                                <field name="children_ids">
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_1_1" model="account.report.line">
                                                        <field name="name">1. Freight costs</field>
                                                        <field name="code">DK_freight_costs</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_1_1_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_1830)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_1_2" model="account.report.line">
                                                        <field name="name">2. Advertising and publicity</field>
                                                        <field name="code">DK_advertising_pub</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_1_2_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_1850)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_1_3" model="account.report.line">
                                                        <field name="name">3. Exhibitions and decoration</field>
                                                        <field name="code">DK_exhibitions_decoration</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_1_3_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_1870)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_1_4" model="account.report.line">
                                                        <field name="name">4. Restoration visits</field>
                                                        <field name="code">DK_restoration_visits</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_1_4_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_1890)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_1_5" model="account.report.line">
                                                        <field name="name">5. Representation expenses, tax deduction limited</field>
                                                        <field name="code">DK_representation_expenses</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_1_5_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_1910)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_1_6" model="account.report.line">
                                                        <field name="name">6. Representation expenses, fully deductible for tax purposes</field>
                                                        <field name="code">DK_representation_expenses_fully</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_1_6_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_1930)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_1_7" model="account.report.line">
                                                        <field name="name">7. Other selling expenses</field>
                                                        <field name="code">DK_other_selling_expenses</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_1_7_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_1950)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_1_8" model="account.report.line">
                                                        <field name="name">8. Newspapers and magazines</field>
                                                        <field name="code">DK_newspapers_magazines</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_1_8_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_1970)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_1_9" model="account.report.line">
                                                        <field name="name">9. Gifts and flowers</field>
                                                        <field name="code">DK_gifts_flowers</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_1_9_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_1990)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_2" model="account.report.line">
                                                <field name="name">b) Premises costs</field>
                                                <field name="code">DK_premises_costs</field>
                                                <field name="aggregation_formula" eval="False"/>
                                                <field name="expression_ids">
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_2_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">aggregation</field>
                                                        <field name="formula">DK_rent.balance + DK_el.balance + DK_electricity.balance +
                                                            DK_water.balance + DK_heat.balance + DK_water_tax_pnl.balance +
                                                            DK_oil_lpg_tax_pnl.balance + DK_coal_tax_pnl.balance + DK_natural_gas.balance +
                                                            DK_co2_tax_pnl.balance + DK_other_taxes.balance + DK_cleaning_sanitation.balance +
                                                            DK_repair_maintenance.balance + DK_repair_maintenance_tax.balance +
                                                            DK_insurances.balance + DK_property_taxes.balance + DK_other_premises.balance</field>
                                                        <field name="green_on_positive" eval="False"/>
                                                    </record>
                                                </field>
                                                <field name="children_ids">
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_2_1" model="account.report.line">
                                                        <field name="name">1. Rent, excluding electricity, water and heating</field>
                                                        <field name="code">DK_rent</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_2_1_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_2030)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_2_2" model="account.report.line">
                                                        <field name="name">2. El</field>
                                                        <field name="code">DK_el</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_2_2_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_2050)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_2_3" model="account.report.line">
                                                        <field name="name">3. Electricity tax</field>
                                                        <field name="code">DK_electricity</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_2_3_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_2060)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_2_4" model="account.report.line">
                                                        <field name="name">4. Water</field>
                                                        <field name="code">DK_water</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_2_4_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_2070)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_2_5" model="account.report.line">
                                                        <field name="name">5. Heat</field>
                                                        <field name="code">DK_heat</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_2_5_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_2080)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_2_6" model="account.report.line">
                                                        <field name="name">6. Water tax</field>
                                                        <field name="code">DK_water_tax_pnl</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_2_6_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_2090)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_2_7" model="account.report.line">
                                                        <field name="name">7. Oil and LPG tax</field>
                                                        <field name="code">DK_oil_lpg_tax_pnl</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_2_7_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_2100)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_2_8" model="account.report.line">
                                                        <field name="name">8. Coal tax</field>
                                                        <field name="code">DK_coal_tax_pnl</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_2_8_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_2110)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_2_9" model="account.report.line">
                                                        <field name="name">9. Natural and town gas tax</field>
                                                        <field name="code">DK_natural_gas</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_2_9_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_2120)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_2_10" model="account.report.line">
                                                        <field name="name">10. CO2 tax</field>
                                                        <field name="code">DK_co2_tax_pnl</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_2_10_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_2130)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_2_11" model="account.report.line">
                                                        <field name="name">11. Other taxes</field>
                                                        <field name="code">DK_other_taxes</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_2_11_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_2140)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_2_12" model="account.report.line">
                                                        <field name="name">12. Cleaning and sanitation (waste management)</field>
                                                        <field name="code">DK_cleaning_sanitation</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_2_12_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_2150)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_2_13" model="account.report.line">
                                                        <field name="name">13. Repair and maintenance</field>
                                                        <field name="code">DK_repair_maintenance</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_2_13_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_2160)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_2_14" model="account.report.line">
                                                        <field name="name">14. Repair and maintenance, tax depreciable property, building 1</field>
                                                        <field name="code">DK_repair_maintenance_tax</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_2_14_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_2170)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_2_15" model="account.report.line">
                                                        <field name="name">15. Insurances</field>
                                                        <field name="code">DK_insurances</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_2_15_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_2180)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_2_16" model="account.report.line">
                                                        <field name="name">16. Property taxes</field>
                                                        <field name="code">DK_property_taxes</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_2_16_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_2190)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_2_17" model="account.report.line">
                                                        <field name="name">17. Other premises costs</field>
                                                        <field name="code">DK_other_premises</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_2_17_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_2200)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_3" model="account.report.line">
                                                <field name="name">c) Administrative costs</field>
                                                <field name="code">DK_administrative_costs</field>
                                                <field name="aggregation_formula" eval="False"/>
                                                <field name="expression_ids">
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_3_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">aggregation</field>
                                                        <field name="formula">DK_acquisitions_below.balance + DK_acquisitions_above.balance +
                                                            DK_subcontractors.balance + DK_research_and_dev.balance +
                                                            DK_other_production_costs.balance + DK_recognized_losses.balance +
                                                            DK_adjustment_impairment_loss.balance + DK_adjustment_receivables.balance +
                                                            DK_it_equipment.balance + DK_tax_free_travel.balance +
                                                            DK_canteen.balance + DK_contigents.balance + DK_non_fiction.balance +
                                                            DK_postage_charges.balance + DK_telephone_internet_business.balance +
                                                            DK_telephone_internet_private.balance + DK_office_supplies.balance +
                                                            DK_rent_operating.balance + DK_travel_expenses.balance +
                                                            DK_temporary_assistance.balance + DK_consulting_services.balance +
                                                            DK_course_costs.balance + DK_leasing_costs_passenger.balance +
                                                            DK_operating_costs.balance + DK_operating_costs_vans.balance +
                                                            DK_parking_costs.balance + DK_car_expenses_gov.balance +
                                                            DK_free_car.balance + DK_employment_injury_insurance.balance +
                                                            DK_government_fees.balance + DK_audit_accounting.balance +
                                                            DK_legal_assistance.balance + DK_other_consultancy.balance +
                                                            DK_non_tax_deductible.balance + DK_administration_management.balance +
                                                            DK_penny_rounding.balance + DK_other_external.balance
                                                        </field>
                                                        <field name="green_on_positive" eval="False"/>
                                                    </record>
                                                </field>
                                                <field name="children_ids">
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_3_1" model="account.report.line">
                                                        <field name="name">1. Small acquisitions below the tax threshold for small assets</field>
                                                        <field name="code">DK_acquisitions_below</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_3_1_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_2230)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_3_2" model="account.report.line">
                                                        <field name="name">2. Small acquisitions above the tax threshold for small assets</field>
                                                        <field name="code">DK_acquisitions_above</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_3_2_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_2240)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_3_3" model="account.report.line">
                                                        <field name="name">3. Subcontractors</field>
                                                        <field name="code">DK_subcontractors</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_3_3_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_2250)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_3_4" model="account.report.line">
                                                        <field name="name">4. Research and development costs</field>
                                                        <field name="code">DK_research_and_dev</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_3_4_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_2260)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_3_5" model="account.report.line">
                                                        <field name="name">5. Other production costs</field>
                                                        <field name="code">DK_other_production_costs</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_3_5_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_2270)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_3_6" model="account.report.line">
                                                        <field name="name">6. Recognized losses on trade receivables</field>
                                                        <field name="code">DK_recognized_losses</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_3_6_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_2280)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_3_7" model="account.report.line">
                                                        <field name="name">7. Adjustment of impairment loss on trade receivables</field>
                                                        <field name="code">DK_adjustment_impairment_loss</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_3_7_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_2290)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_3_8" model="account.report.line">
                                                        <field name="name">8. Adjustment of receivables from group and associated enterprises</field>
                                                        <field name="code">DK_adjustment_receivables</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_3_8_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_2300)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_3_9" model="account.report.line">
                                                        <field name="name">9. IT equipment, etc.</field>
                                                        <field name="code">DK_it_equipment</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_3_9_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_2310)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_3_10" model="account.report.line">
                                                        <field name="name">10. Tax-free travel and transport allowances</field>
                                                        <field name="code">DK_tax_free_travel</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_3_10_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_2330)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_3_11" model="account.report.line">
                                                        <field name="name">11. Canteen costs</field>
                                                        <field name="code">DK_canteen</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_3_11_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_2350)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_3_12" model="account.report.line">
                                                        <field name="name">12. Contingents</field>
                                                        <field name="code">DK_contigents</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_3_12_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_2370)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_3_13" model="account.report.line">
                                                        <field name="name">13. Non-fiction</field>
                                                        <field name="code">DK_non_fiction</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_3_13_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_2380)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_3_14" model="account.report.line">
                                                        <field name="name">14. Postage and charges</field>
                                                        <field name="code">DK_postage_charges</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_3_14_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_2390)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_3_15" model="account.report.line">
                                                        <field name="name">15. Telephone and internet etc. (business only)</field>
                                                        <field name="code">DK_telephone_internet_business</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_3_15_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_2410)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_3_16" model="account.report.line">
                                                        <field name="name">16. Telephone and internet etc. (partly private)</field>
                                                        <field name="code">DK_telephone_internet_private</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_3_16_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_2420)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_3_17" model="account.report.line">
                                                        <field name="name">17. Office supplies</field>
                                                        <field name="code">DK_office_supplies</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_3_17_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_2450)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_3_18" model="account.report.line">
                                                        <field name="name">18. Rent and operating lease payments (excluding rentals)</field>
                                                        <field name="code">DK_rent_operating</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_3_18_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_2460)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_3_19" model="account.report.line">
                                                        <field name="name">19. Travel expenses</field>
                                                        <field name="code">DK_travel_expenses</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_3_19_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_2470)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_3_20" model="account.report.line">
                                                        <field name="name">20. Temporary assistance</field>
                                                        <field name="code">DK_temporary_assistance</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_3_20_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_2480)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_3_21" model="account.report.line">
                                                        <field name="name">21. Consulting services</field>
                                                        <field name="code">DK_consulting_services</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_3_21_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_2510)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_3_22" model="account.report.line">
                                                        <field name="name">22. Course costs</field>
                                                        <field name="code">DK_course_costs</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_3_22_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_2520)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_3_23" model="account.report.line">
                                                        <field name="name">23. Leasing costs, passenger cars</field>
                                                        <field name="code">DK_leasing_costs_passenger</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_3_23_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_2530)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_3_24" model="account.report.line">
                                                        <field name="name">24. Operating costs, passenger cars</field>
                                                        <field name="code">DK_operating_costs</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_3_24_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_2540)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_3_25" model="account.report.line">
                                                        <field name="name">25. Operating costs, vans</field>
                                                        <field name="code">DK_operating_costs_vans</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_3_25_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_2560)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_3_26" model="account.report.line">
                                                        <field name="name">26. Parking costs</field>
                                                        <field name="code">DK_parking_costs</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_3_26_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_2620)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_3_27" model="account.report.line">
                                                        <field name="name">27. Car expenses according to government tariffs</field>
                                                        <field name="code">DK_car_expenses_gov</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_3_27_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_2630)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_3_28" model="account.report.line">
                                                        <field name="name">28. Free car</field>
                                                        <field name="code">DK_free_car</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_3_28_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_2640)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_3_29" model="account.report.line">
                                                        <field name="name">29. Employment injury insurance</field>
                                                        <field name="code">DK_employment_injury_insurance</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_3_29_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_2650)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_3_30" model="account.report.line">
                                                        <field name="name">30. Government fees and fines (not tax deductible)</field>
                                                        <field name="code">DK_government_fees</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_3_30_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_2660)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_3_31" model="account.report.line">
                                                        <field name="name">31. Audit and accounting assistance</field>
                                                        <field name="code">DK_audit_accounting</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_3_31_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_2670)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_3_32" model="account.report.line">
                                                        <field name="name">32. Legal assistance</field>
                                                        <field name="code">DK_legal_assistance</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_3_32_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_2680)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_3_33" model="account.report.line">
                                                        <field name="name">33. Other consultancy fees</field>
                                                        <field name="code">DK_other_consultancy</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_3_33_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_2690)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_3_34" model="account.report.line">
                                                        <field name="name">34. Non-tax-deductible consultancy fees</field>
                                                        <field name="code">DK_non_tax_deductible</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_3_34_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_2700)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_3_35" model="account.report.line">
                                                        <field name="name">35. Administration/management fee</field>
                                                        <field name="code">DK_administration_management</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_3_35_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_2710)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_3_36" model="account.report.line">
                                                        <field name="name">36. Penny rounding/cash differences</field>
                                                        <field name="code">DK_penny_rounding</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_3_36_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_2720)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                    <record id="account_pnl_report_l10n_dk_1_1_1_5_3_37" model="account.report.line">
                                                        <field name="name">37. Other external costs</field>
                                                        <field name="code">DK_other_external</field>
                                                        <field name="groupby">account_id</field>
                                                        <field name="foldable" eval="True"/>
                                                        <field name="expression_ids">
                                                            <record id="account_pnl_report_l10n_dk_1_1_1_5_3_37_balance" model="account.report.expression">
                                                                <field name="label">balance</field>
                                                                <field name="engine">account_codes</field>
                                                                <field name="formula">tag(l10n_dk.account_tag_2810)</field>
                                                                <field name="green_on_positive" eval="False"/>
                                                            </record>
                                                        </field>
                                                    </record>
                                                </field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_pnl_report_l10n_dk_1_1_2" model="account.report.line">
                                <field name="name">2. Personnel costs</field>
                                <field name="code">DK_personnel_costs</field>
                                <field name="aggregation_formula" eval="False"/>
                                <field name="expression_ids">
                                    <record id="account_pnl_report_l10n_dk_1_1_2_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">
                                            DK_wages_salaries.balance + DK_holiday_obligation.balance + DK_long_service_bonuses.balance +
                                            DK_directors_fees.balance + DK_am_contributory.balance + DK_am_non_contributory.balance +
                                            DK_pensions.balance + DK_remuneration_pension.balance + DK_social_security.balance +
                                            DK_aer.balance +DK_atp.balance + DK_other_staff_costs.balance + DK_employee_benefits.balance +
                                            DK_salary_reinbursements.balance + DK_tax_free.balance + DK_payroll_tax.balance
                                        </field>
                                        <field name="green_on_positive" eval="False"/>
                                    </record>
                                </field>
                                <field name="children_ids">
                                    <record id="account_pnl_report_l10n_dk_1_1_2_1" model="account.report.line">
                                        <field name="name">a) Wages and salaries</field>
                                        <field name="code">DK_wages_salaries</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_pnl_report_l10n_dk_1_1_2_1_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">tag(l10n_dk.account_tag_2850)</field>
                                                <field name="green_on_positive" eval="False"/>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_pnl_report_l10n_dk_1_1_2_2" model="account.report.line">
                                        <field name="name">b) Holiday pay obligation</field>
                                        <field name="code">DK_holiday_obligation</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_pnl_report_l10n_dk_1_1_2_2_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">tag(l10n_dk.account_tag_2860)</field>
                                                <field name="green_on_positive" eval="False"/>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_pnl_report_l10n_dk_1_1_2_3" model="account.report.line">
                                        <field name="name">c) Long-service bonuses and severance pay</field>
                                        <field name="code">DK_long_service_bonuses</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_pnl_report_l10n_dk_1_1_2_3_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">tag(l10n_dk.account_tag_2870)</field>
                                                <field name="green_on_positive" eval="False"/>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_pnl_report_l10n_dk_1_1_2_4" model="account.report.line">
                                        <field name="name">d) Directors' fees</field>
                                        <field name="code">DK_directors_fees</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_pnl_report_l10n_dk_1_1_2_4_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">tag(l10n_dk.account_tag_2880)</field>
                                                <field name="green_on_positive" eval="False"/>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_pnl_report_l10n_dk_1_1_2_5" model="account.report.line">
                                        <field name="name">e) AM Contributory A-Income</field>
                                        <field name="code">DK_am_contributory</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_pnl_report_l10n_dk_1_1_2_5_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">tag(l10n_dk.account_tag_2890)</field>
                                                <field name="green_on_positive" eval="False"/>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_pnl_report_l10n_dk_1_1_2_6" model="account.report.line">
                                        <field name="name">f) AM Non-contributory A-Income</field>
                                        <field name="code">DK_am_non_contributory</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_pnl_report_l10n_dk_1_1_2_6_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">tag(l10n_dk.account_tag_2900)</field>
                                                <field name="green_on_positive" eval="False"/>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_pnl_report_l10n_dk_1_1_2_7" model="account.report.line">
                                        <field name="name">g) Pensions</field>
                                        <field name="code">DK_pensions</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_pnl_report_l10n_dk_1_1_2_7_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">tag(l10n_dk.account_tag_2910)</field>
                                                <field name="green_on_positive" eval="False"/>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_pnl_report_l10n_dk_1_1_2_8" model="account.report.line">
                                        <field name="name">h) Remuneration in lieu of pension promises</field>
                                        <field name="code">DK_remuneration_pension</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_pnl_report_l10n_dk_1_1_2_8_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">tag(l10n_dk.account_tag_2920)</field>
                                                <field name="green_on_positive" eval="False"/>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_pnl_report_l10n_dk_1_1_2_9" model="account.report.line">
                                        <field name="name">i) Social security costs</field>
                                        <field name="code">DK_social_security</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_pnl_report_l10n_dk_1_1_2_9_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">tag(l10n_dk.account_tag_2930)</field>
                                                <field name="green_on_positive" eval="False"/>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_pnl_report_l10n_dk_1_1_2_10" model="account.report.line">
                                        <field name="name">j) AER/ AUB</field>
                                        <field name="code">DK_aer</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_pnl_report_l10n_dk_1_1_2_10_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">tag(l10n_dk.account_tag_2940)</field>
                                                <field name="green_on_positive" eval="False"/>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_pnl_report_l10n_dk_1_1_2_11" model="account.report.line">
                                        <field name="name">k) ATP</field>
                                        <field name="code">DK_atp</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_pnl_report_l10n_dk_1_1_2_11_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">tag(l10n_dk.account_tag_2950)</field>
                                                <field name="green_on_positive" eval="False"/>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_pnl_report_l10n_dk_1_1_2_12" model="account.report.line">
                                        <field name="name">l) Other staff costs</field>
                                        <field name="code">DK_other_staff_costs</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_pnl_report_l10n_dk_1_1_2_12_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">tag(l10n_dk.account_tag_2960)</field>
                                                <field name="green_on_positive" eval="False"/>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_pnl_report_l10n_dk_1_1_2_13" model="account.report.line">
                                        <field name="name">m) Employee benefits</field>
                                        <field name="code">DK_employee_benefits</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_pnl_report_l10n_dk_1_1_2_13_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">tag(l10n_dk.account_tag_2965)</field>
                                                <field name="green_on_positive" eval="False"/>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_pnl_report_l10n_dk_1_1_2_14" model="account.report.line">
                                        <field name="name">n) Salary reimbursements</field>
                                        <field name="code">DK_salary_reinbursements</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_pnl_report_l10n_dk_1_1_2_14_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">tag(l10n_dk.account_tag_2968)</field>
                                                <field name="green_on_positive" eval="False"/>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_pnl_report_l10n_dk_1_1_2_15" model="account.report.line">
                                        <field name="name">o) Tax-free allowances paid in the form of mileage allowances and subsistence allowances</field>
                                        <field name="code">DK_tax_free</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_pnl_report_l10n_dk_1_1_2_15_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">tag(l10n_dk.account_tag_2970)</field>
                                                <field name="green_on_positive" eval="False"/>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_pnl_report_l10n_dk_1_1_2_16" model="account.report.line">
                                        <field name="name">p) Payroll tax</field>
                                        <field name="code">DK_payroll_tax</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_pnl_report_l10n_dk_1_1_2_16_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">tag(l10n_dk.account_tag_2980)</field>
                                                <field name="green_on_positive" eval="False"/>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_pnl_report_l10n_dk_1_1_3" model="account.report.line">
                                <field name="name">3. Depreciation, amortization and impairment of tangible and intangible fixed assets</field>
                                <field name="code">DK_depreciation</field>
                                <field name="aggregation_formula" eval="False"/>
                                <field name="expression_ids">
                                    <record id="account_pnl_report_l10n_dk_1_1_3_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">
                                            DK_amort_intangible.balance + DK_amort_goodwill.balance + DK_depreciation_land_build.balance +
                                            DK_depreciation_plant_machinery.balance + DK_depreciation_lease.balance +
                                            DK_depreciation_write_downs.balance + DK_depreciation_software.balance +
                                            DK_depreciation_impairment_land.balance + DK_depreciation_amort_plant.balance +
                                            DK_depreciation_amort_other.balance
                                        </field>
                                        <field name="green_on_positive" eval="False"/>
                                    </record>
                                </field>
                                <field name="children_ids">
                                    <record id="account_pnl_report_l10n_dk_1_1_3_1" model="account.report.line">
                                        <field name="name">a) Amortization and impairment of acquired intangible assets</field>
                                        <field name="code">DK_amort_intangible</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_pnl_report_l10n_dk_1_1_3_1_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">tag(l10n_dk.account_tag_3000)</field>
                                                <field name="green_on_positive" eval="False"/>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_pnl_report_l10n_dk_1_1_3_2" model="account.report.line">
                                        <field name="name">b) Amortization and impairment of goodwill</field>
                                        <field name="code">DK_amort_goodwill</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_pnl_report_l10n_dk_1_1_3_2_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">tag(l10n_dk.account_tag_3010)</field>
                                                <field name="green_on_positive" eval="False"/>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_pnl_report_l10n_dk_1_1_3_3" model="account.report.line">
                                        <field name="name">c) Depreciation and impairment of land and buildings</field>
                                        <field name="code">DK_depreciation_land_build</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_pnl_report_l10n_dk_1_1_3_3_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">tag(l10n_dk.account_tag_3020)</field>
                                                <field name="green_on_positive" eval="False"/>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_pnl_report_l10n_dk_1_1_3_4" model="account.report.line">
                                        <field name="name">d) Depreciation and amortization of plant and machinery</field>
                                        <field name="code">DK_depreciation_plant_machinery</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_pnl_report_l10n_dk_1_1_3_4_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">tag(l10n_dk.account_tag_3030)</field>
                                                <field name="green_on_positive" eval="False"/>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_pnl_report_l10n_dk_1_1_3_5" model="account.report.line">
                                        <field name="name">e) Depreciation and amortization of leasehold improvements</field>
                                        <field name="code">DK_depreciation_lease</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_pnl_report_l10n_dk_1_1_3_5_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">tag(l10n_dk.account_tag_3040)</field>
                                                <field name="green_on_positive" eval="False"/>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_pnl_report_l10n_dk_1_1_3_6" model="account.report.line">
                                        <field name="name">f) Depreciation and write-downs of other fixtures and fittings, tools and equipment</field>
                                        <field name="code">DK_depreciation_write_downs</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_pnl_report_l10n_dk_1_1_3_6_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">tag(l10n_dk.account_tag_3050)</field>
                                                <field name="green_on_positive" eval="False"/>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_pnl_report_l10n_dk_1_1_3_7" model="account.report.line">
                                        <field name="name">g) Depreciation and amortization of software</field>
                                        <field name="code">DK_depreciation_software</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_pnl_report_l10n_dk_1_1_3_7_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">tag(l10n_dk.account_tag_3060)</field>
                                                <field name="green_on_positive" eval="False"/>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_pnl_report_l10n_dk_1_1_3_8" model="account.report.line">
                                        <field name="name">h) Depreciation and impairment of land and buildings held under finance leases</field>
                                        <field name="code">DK_depreciation_impairment_land</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_pnl_report_l10n_dk_1_1_3_8_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">tag(l10n_dk.account_tag_3070)</field>
                                                <field name="green_on_positive" eval="False"/>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_pnl_report_l10n_dk_1_1_3_9" model="account.report.line">
                                        <field name="name">i) Depreciation and amortization of plant and machinery held under finance leases</field>
                                        <field name="code">DK_depreciation_amort_plant</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_pnl_report_l10n_dk_1_1_3_9_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">tag(l10n_dk.account_tag_3080)</field>
                                                <field name="green_on_positive" eval="False"/>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_pnl_report_l10n_dk_1_1_3_10" model="account.report.line">
                                        <field name="name">j) Depreciation, amortization and write-downs of other plant, machinery and equipment held under finance leases</field>
                                        <field name="code">DK_depreciation_amort_other</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_pnl_report_l10n_dk_1_1_3_10_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">tag(l10n_dk.account_tag_3090)</field>
                                                <field name="green_on_positive" eval="False"/>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_pnl_report_l10n_dk_1_1_4" model="account.report.line">
                                <field name="name">4. Impairment losses on current assets in excess of normal write-downs</field>
                                <field name="code">DK_impairment</field>
                                <field name="groupby">account_id</field>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_pnl_report_l10n_dk_1_1_4_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">tag(l10n_dk.account_tag_3130)</field>
                                        <field name="green_on_positive" eval="False"/>
                                    </record>
                                </field>
                            </record>
                            <record id="account_pnl_report_l10n_dk_1_1_5" model="account.report.line">
                                <field name="name">5. Other operating costs</field>
                                <field name="code">DK_other_operating_costs</field>
                                <field name="aggregation_formula" eval="False"/>
                                <field name="expression_ids">
                                    <record id="account_pnl_report_l10n_dk_1_1_5_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">
                                            DK_loss_disposal_intangible.balance +
                                            DK_loss_disposal_tangible.balance +
                                            DK_other_operating_costs_b.balance
                                        </field>
                                        <field name="green_on_positive" eval="False"/>
                                    </record>
                                </field>
                                <field name="children_ids">
                                    <record id="account_pnl_report_l10n_dk_1_1_5_1" model="account.report.line">
                                        <field name="name">a) Loss on disposal of intangible assets</field>
                                        <field name="code">DK_loss_disposal_intangible</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_pnl_report_l10n_dk_1_1_5_1_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">tag(l10n_dk.account_tag_3160)</field>
                                                <field name="green_on_positive" eval="False"/>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_pnl_report_l10n_dk_1_1_5_2" model="account.report.line">
                                        <field name="name">b) Loss on disposal of tangible assets</field>
                                        <field name="code">DK_loss_disposal_tangible</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_pnl_report_l10n_dk_1_1_5_2_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">tag(l10n_dk.account_tag_3170)</field>
                                                <field name="green_on_positive" eval="False"/>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_pnl_report_l10n_dk_1_1_5_3" model="account.report.line">
                                        <field name="name">c) Other operating costs</field>
                                        <field name="code">DK_other_operating_costs_b</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_pnl_report_l10n_dk_1_1_5_3_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">tag(l10n_dk.account_tag_3180) + tag(l10n_dk.account_tag_3790)</field>
                                                <field name="green_on_positive" eval="False"/>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_pnl_report_l10n_dk_1_1_6" model="account.report.line">
                                <field name="name">6. Income from investments</field>
                                <field name="code">DK_income_invest</field>
                                <field name="aggregation_formula">DK_income_invest_affiliated.balance + DK_income_invest_participating.balance </field>
                                <field name="children_ids">
                                    <record id="account_pnl_report_l10n_dk_1_1_6_1" model="account.report.line">
                                        <field name="name">a) Income from investments in affiliated enterprises</field>
                                        <field name="code">DK_income_invest_affiliated</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_pnl_report_l10n_dk_1_1_6_1_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_dk.account_tag_3200)</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_pnl_report_l10n_dk_1_1_6_2" model="account.report.line">
                                        <field name="name">b) Income from participating interests</field>
                                        <field name="code">DK_income_invest_participating</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_pnl_report_l10n_dk_1_1_6_2_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_dk.account_tag_3230)</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_pnl_report_l10n_dk_1_1_7" model="account.report.line">
                                <field name="name">7. Income from other investments, securities and receivables that are fixed assets</field>
                                <field name="code">DK_income_other_invest</field>
                                <field name="aggregation_formula">DK_dividends_unlisted.balance + DK_other_income_other_invest.balance</field>
                                <field name="children_ids">
                                    <record id="account_pnl_report_l10n_dk_1_1_7_1" model="account.report.line">
                                        <field name="name">a) Dividends from unlisted portfolio shares (gross dividend)</field>
                                        <field name="code">DK_dividends_unlisted</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_pnl_report_l10n_dk_1_1_7_1_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_dk.account_tag_3380)</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_pnl_report_l10n_dk_1_1_7_2" model="account.report.line">
                                        <field name="name">b) Other income from other investments, securities and receivables that are fixed assets</field>
                                        <field name="code">DK_other_income_other_invest</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_pnl_report_l10n_dk_1_1_7_2_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_dk.account_tag_3400)</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_pnl_report_l10n_dk_1_1_8" model="account.report.line">
                                <field name="name">8. Other financial income from affiliated enterprises</field>
                                <field name="code">DK_other_financial_income_affiliated</field>
                                <field name="groupby">account_id</field>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_pnl_report_l10n_dk_1_1_8_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-tag(l10n_dk.account_tag_3440)</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_pnl_report_l10n_dk_1_1_9" model="account.report.line">
                                <field name="name">9. Other financial income</field>
                                <field name="code">DK_other_financial_income</field>
                                <field name="aggregation_formula">DK_interest_banks.balance + DK_interest_trade_receivables.balance + DK_interest_supplements.balance +DK_other_financial_income_b.balance</field>
                                <field name="children_ids">
                                    <record id="account_pnl_report_l10n_dk_1_1_9_1" model="account.report.line">
                                        <field name="name">a) Interest from banks</field>
                                        <field name="code">DK_interest_banks</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_pnl_report_l10n_dk_1_1_9_1_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_dk.account_tag_3470)</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_pnl_report_l10n_dk_1_1_9_2" model="account.report.line">
                                        <field name="name">b) Interest on trade receivables</field>
                                        <field name="code">DK_interest_trade_receivables</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_pnl_report_l10n_dk_1_1_9_2_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_dk.account_tag_3490)</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_pnl_report_l10n_dk_1_1_9_3" model="account.report.line">
                                        <field name="name">c) Interest supplements etc. from the government (non-taxable)</field>
                                        <field name="code">DK_interest_supplements</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_pnl_report_l10n_dk_1_1_9_3_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_dk.account_tag_3510)</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_pnl_report_l10n_dk_1_1_9_4" model="account.report.line">
                                        <field name="name">d) Other financial income</field>
                                        <field name="code">DK_other_financial_income_b</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_pnl_report_l10n_dk_1_1_9_4_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_dk.account_tag_3530) - tag(l10n_dk.account_tag_3190) - tag(l10n_dk.account_tag_3570)</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_pnl_report_l10n_dk_1_1_10" model="account.report.line">
                                <field name="name">10. Impairment of financial assets</field>
                                <field name="code">DK_impairment_financial_assets</field>
                                <field name="groupby">account_id</field>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_pnl_report_l10n_dk_1_1_10_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-tag(l10n_dk.account_tag_3560)</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_pnl_report_l10n_dk_1_1_11" model="account.report.line">
                                <field name="name">11. Financial expenses arising from affiliated enterprises</field>
                                <field name="code">DK_financial_expenses</field>
                                <field name="groupby">account_id</field>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_pnl_report_l10n_dk_1_1_11_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">tag(l10n_dk.account_tag_3590)</field>
                                        <field name="green_on_positive" eval="False"/>
                                    </record>
                                </field>
                            </record>
                            <record id="account_pnl_report_l10n_dk_1_1_12" model="account.report.line">
                                <field name="name">12. Other financial costs</field>
                                <field name="code">DK_other_financial_costs</field>
                                <field name="aggregation_formula" eval="False"/>
                                <field name="expression_ids">
                                    <record id="account_pnl_report_l10n_dk_1_1_12_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">
                                            DK_exchange_rate.balance + DK_exchange_rate_foreign.balance +
                                            DK_exchange_losses.balance + DK_interest_finance.balance + DK_interest_suppliers.balance +
                                            DK_interest_payable.balance + DK_interest_government.balance +
                                            DK_value_adjustments.balance + DK_other_charges.balance
                                        </field>
                                        <field name="green_on_positive" eval="False"/>
                                    </record>
                                </field>
                                <field name="children_ids">
                                    <record id="account_pnl_report_l10n_dk_1_1_12_1" model="account.report.line">
                                        <field name="name">a) Exchange rate adjustments</field>
                                        <field name="code">DK_exchange_rate</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_pnl_report_l10n_dk_1_1_12_1_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">tag(l10n_dk.account_tag_3610)</field>
                                                <field name="green_on_positive" eval="False"/>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_pnl_report_l10n_dk_1_1_12_2" model="account.report.line">
                                        <field name="name">b) Exchange rate adjustments, foreign subsidiaries</field>
                                        <field name="code">DK_exchange_rate_foreign</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_pnl_report_l10n_dk_1_1_12_2_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">tag(l10n_dk.account_tag_3620)</field>
                                                <field name="green_on_positive" eval="False"/>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_pnl_report_l10n_dk_1_1_12_3" model="account.report.line">
                                        <field name="name">c) Exchange losses on cash and cash equivalents, bank and mortgage debt</field>
                                        <field name="code">DK_exchange_losses</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_pnl_report_l10n_dk_1_1_12_3_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">tag(l10n_dk.account_tag_3630)</field>
                                                <field name="green_on_positive" eval="False"/>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_pnl_report_l10n_dk_1_1_12_4" model="account.report.line">
                                        <field name="name">d) Interest on finance lease debt</field>
                                        <field name="code">DK_interest_finance</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_pnl_report_l10n_dk_1_1_12_4_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">tag(l10n_dk.account_tag_3640)</field>
                                                <field name="green_on_positive" eval="False"/>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_pnl_report_l10n_dk_1_1_12_5" model="account.report.line">
                                        <field name="name">e) Interest on suppliers of goods and services</field>
                                        <field name="code">DK_interest_suppliers</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_pnl_report_l10n_dk_1_1_12_5_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">tag(l10n_dk.account_tag_3650)</field>
                                                <field name="green_on_positive" eval="False"/>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_pnl_report_l10n_dk_1_1_12_6" model="account.report.line">
                                        <field name="name">f) Interest payable to banks and mortgage credit institutions</field>
                                        <field name="code">DK_interest_payable</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_pnl_report_l10n_dk_1_1_12_6_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">tag(l10n_dk.account_tag_3670)</field>
                                                <field name="green_on_positive" eval="False"/>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_pnl_report_l10n_dk_1_1_12_7" model="account.report.line">
                                        <field name="name">g) Interest to the government (not tax deductible)</field>
                                        <field name="code">DK_interest_government</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_pnl_report_l10n_dk_1_1_12_7_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">tag(l10n_dk.account_tag_3675)</field>
                                                <field name="green_on_positive" eval="False"/>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_pnl_report_l10n_dk_1_1_12_8" model="account.report.line">
                                        <field name="name">h) Value adjustments on investment property</field>
                                        <field name="code">DK_value_adjustments</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_pnl_report_l10n_dk_1_1_12_8_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">tag(l10n_dk.account_tag_3680)</field>
                                                <field name="green_on_positive" eval="False"/>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_pnl_report_l10n_dk_1_1_12_9" model="account.report.line">
                                        <field name="name">i) Other financial charges</field>
                                        <field name="code">DK_other_charges</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_pnl_report_l10n_dk_1_1_12_9_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">tag(l10n_dk.account_tag_3690)</field>
                                                <field name="green_on_positive" eval="False"/>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="account_pnl_report_l10n_dk_1_2" model="account.report.line">
                        <field name="name">b) Tax on profit for the year</field>
                        <field name="code">DK_tax_profit</field>
                        <field name="aggregation_formula" eval="False"/>
                        <field name="expression_ids">
                            <record id="account_pnl_report_l10n_dk_1_2_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">DK_current_tax.balance + DK_change_deffered.balance + DK_adjustment.balance</field>
                                <field name="green_on_positive" eval="False"/>
                            </record>
                        </field>
                        <field name="children_ids">
                            <record id="account_pnl_report_l10n_dk_1_2_1" model="account.report.line">
                                <field name="name">a) Current tax</field>
                                <field name="code">DK_current_tax</field>
                                <field name="groupby">account_id</field>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_pnl_report_l10n_dk_1_2_1_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">tag(l10n_dk.account_tag_3740)</field>
                                        <field name="green_on_positive" eval="False"/>
                                    </record>
                                </field>
                            </record>
                            <record id="account_pnl_report_l10n_dk_1_2_2" model="account.report.line">
                                <field name="name">b) Change in deferred tax</field>
                                <field name="code">DK_change_deffered</field>
                                <field name="groupby">account_id</field>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_pnl_report_l10n_dk_1_2_2_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">tag(l10n_dk.account_tag_3760)</field>
                                        <field name="green_on_positive" eval="False"/>
                                    </record>
                                </field>
                            </record>
                            <record id="account_pnl_report_l10n_dk_1_2_3" model="account.report.line">
                                <field name="name">c) Adjustment relating to previous years</field>
                                <field name="code">DK_adjustment</field>
                                <field name="groupby">account_id</field>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_pnl_report_l10n_dk_1_2_3_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">tag(l10n_dk.account_tag_3780)</field>
                                        <field name="green_on_positive" eval="False"/>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="account_pnl_report_l10n_dk_1_3" model="account.report.line">
                        <field name="name">c) Other tax</field>
                        <field name="code">DK_other_tax</field>
                        <field name="groupby">account_id</field>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="account_pnl_report_l10n_dk_1_3_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">tag(l10n_dk.account_tag_3810)</field>
                                <field name="green_on_positive" eval="False"/>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
        </field>
    </record>

    <record id="action_account_report_dk_pnl" model="ir.actions.client">
        <field name="name">Profit and Loss</field>
        <field name="tag">account_report</field>
        <field name="context" eval="{'report_id': ref('account_financial_report_dk_pnl')}"/>
    </record>

    <record id="account_financial_report_dk_profit_loss0" model="account.report.line">
        <field name="action_id" ref="action_account_report_dk_pnl"/>
    </record>

</odoo>
