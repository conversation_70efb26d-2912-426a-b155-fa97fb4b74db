# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_sepa_direct_debit
# 
# Translators:
# Wil <PERSON>doo, 2024
# <PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:52+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "(if applicable)"
msgstr "(indien van toepassing)"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "(optional)"
msgstr "(optioneel)"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "*********"
msgstr "*********"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "2023-08-10"
msgstr "2023-08-10"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "2023-09-10"
msgstr "2023-09-10"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "380055"
msgstr "380055"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "*************"
msgstr "*************"

#. module: account_sepa_direct_debit
#: model:mail.template,body_html:account_sepa_direct_debit.email_template_sdd_new_mandate
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Dear <t t-out=\"object.partner_id.display_name or ''\">Azure Interior</t>\n"
"                        <br/>\n"
"                        <br/>\n"
"                        Here is your SEPA Direct Debit Mandate to sign to authorize\n"
"                        <t t-out=\"object.company_id.display_name or ''\">YourCompany</t>\n"
"                        to send instructions to your bank to debit your account in accordance with the instructions from\n"
"                        <t t-out=\"object.company_id.display_name or ''\">YourCompany</t>\n"
"                        Do not hesitate to contact us if you have any questions.\n"
"                        <br/>\n"
"                        Best regards,\n"
"                        <t t-if=\"not is_html_empty(user.signature)\" data-o-mail-quote-container=\"1\">\n"
"                            <br/><br/>\n"
"                            <t t-out=\"user.signature\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"                        </t>\n"
"                    </p>\n"
"                </div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Beste <t t-out=\"object.partner_id.display_name or ''\">Azure Interieur</t>\n"
"                       <br/>\n"
"                        <br/>\n"
"                        Hier is je SEPA Automatische Incasso Mandaat om te ondertekenen om te autoriseren\n"
"                       <t t-out=\"object.company_id.display_name or ''\">UwBedrijf</t>\n"
"                        instructies te sturen naar uw bank om je rekening te debiteren in overeenstemming met de instructies van\n"
"                       <t t-out=\"object.company_id.display_name or ''\">YourCompany</t>\n"
"                        Aarzel niet om contact met ons op te nemen als je vragen hebt.\n"
"                       <br/>\n"
"                        Met vriendelijke groeten,\n"
"                        <t t-if=\"not is_html_empty(user.signature)\" data-o-mail-quote-container=\"1\">\n"
"                            <br/><br/>\n"
"                            <t t-out=\"user.signature\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"                        </t>\n"
"                    </p>\n"
"                </div>\n"
"            "

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
msgid "<span> day(s)</span>"
msgstr "<span> dag(en)</span>"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>Address:</strong>"
msgstr "<strong>Adres</strong>"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>City: </strong>"
msgstr "<strong>Plaats:</strong>"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>Country: </strong>"
msgstr "<strong>Land:</strong>"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>Creditor identifier:</strong>"
msgstr "<strong>Crediteur identificatie:</strong>"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid ""
"<strong>Date and place of signature:</strong> "
"......................................"
msgstr ""
"<strong>Datum en plaats van handtekening:</strong> "
"......................................"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>Email:</strong>"
msgstr "<strong>E-mail:</strong>"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>IBAN:</strong>"
msgstr "<strong>IBAN:</strong>"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>Mandate identifier:</strong>"
msgstr "<strong>Identificatiecode Mandaat:</strong>"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid ""
"<strong>Name of the reference party:</strong> "
"......................................"
msgstr ""
"<strong>Naam van de referentie partij:</strong> "
"......................................"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>Phone:</strong>"
msgstr "<strong>Telefoon:</strong>"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>Signature:</strong>"
msgstr "<strong>Handtekening:</strong>"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>Start date:</strong>"
msgstr "<strong>Startdatum:</strong>"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>Transaction type:</strong> recurrent"
msgstr "<strong>Tranactiesoort:</strong> herhalend"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>Zip: </strong>"
msgstr "<strong>Postcode: </strong>"

#. module: account_sepa_direct_debit
#: model:mail.template,body_html:account_sepa_direct_debit.email_template_sdd_pre_notification
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <t t-set=\"company\" t-value=\"object.company_id\"/>\n"
"                    <span style=\"font-size: 10px;\">Your Direct Debit Payment Notification</span><br/>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"right\" t-if=\"not company.uses_default_logo\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ company.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"company.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                    <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    Dear <t t-out=\"object.partner_id.name or ''\">Azure Interior</t><br/>\n"
"                    <br/>\n"
"                    A Direct Debit payment request amounting to\n"
"                    <span style=\"font-weight:bold;\" t-out=\"format_amount(object.amount, object.currency_id) or ''\">$ 0.00</span>\n"
"                    will be sent to your bank.<br/>\n"
"                    Your account ending with <t t-out=\"ctx.get('iban_last_4') or ''\">1234</t> will be automatically debited on the\n"
"                    <span style=\"font-weight:bold;\" t-out=\"format_date(ctx.get('collection_date'))\">2020-04-18</span>,\n"
"                    or shortly thereafter.<br/>\n"
"                    please make sure you have the requested funds.<br/>\n"
"                    <br/>\n"
"                    <t t-if=\"ctx.get('creditor_iban') or ctx.get('mandate_ref')\">\n"
"                        Merchant data:<br/>\n"
"                        <ul>\n"
"                            <t t-if=\"ctx.get('creditor_iban')\">\n"
"                                <li>IBAN: <t t-out=\"ctx['creditor_iban'] or ''\">NO 93 8601 1117947</t></li>\n"
"                            </t>\n"
"                            <t t-if=\"ctx.get('mandate_ref')\">\n"
"                                <li>SEPA DIRECT DEBIT MANDATE REFERENCE: <t t-out=\"ctx['mandate_ref'] or ''\"/></li>\n"
"                            </t>\n"
"                        </ul>\n"
"                    </t>\n"
"                    <br/>\n"
"                    Do not hesitate to contact us if you have any question.\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"company.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-if=\"company.phone\">\n"
"                        <t t-out=\"company.phone or ''\">******-123-4567</t> |\n"
"                    </t>\n"
"                    <t t-if=\"company.email\">\n"
"                        <a t-attf-href=\"'mailto:%s' % {{ company.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"company.email or ''\"><EMAIL></a> |\n"
"                    </t>\n"
"                    <t t-if=\"company.website\">\n"
"                        <a t-attf-href=\"'%s' % {{ company.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"company.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=mail\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <t t-set=\"company\" t-value=\"object.company_id\"/>\n"
"                    <span style=\"font-size: 10px;\">Kennisgeving van je automatische incasso</span><br/>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"right\" t-if=\"not company.uses_default_logo\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ company.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"company.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                    <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    Geachte <t t-out=\"object.partner_id.name or ''\">Azure Interieur</t><br/>\n"
"                    <br/>\n"
"                    Een verzoek tot automatische incasso van\n"
"                   <span style=\"font-weight:bold;\" t-out=\"format_amount(object.amount, object.currency_id) or ''\">$ 0.00</span>\n"
"                    zal naar je bank worden gestuurd.<br/>\n"
"                    Je rekening eindigend op <t t-out=\"ctx.get('iban_last_4') or ''\">1234</t> zal automatisch worden gedebiteerd op de\n"
"                   <span style=\"font-weight:bold;\" t-out=\"format_date(ctx.get('collection_date'))\">2020-04-18</span>,\n"
"                    of kort daarna.<br/>\n"
"                    zorg ervoor dat je het gevraagde bedrag hebt.<br/>\n"
"                    <br/>\n"
"                   <t t-if=\"ctx.get('creditor_iban') or ctx.get('mandate_ref')\">\n"
"                        Gegevens verkoper:<br/>\n"
"                       <ul>\n"
"                           <t t-if=\"ctx.get('creditor_iban')\">\n"
"                               <li>IBAN: <t t-out=\"ctx['creditor_iban'] or ''\">NR 93 8601 1117947</t></li>\n"
"                           </t>\n"
"                           <t t-if=\"ctx.get('mandate_ref')\">\n"
"                               <li>SEPA MANDAATREFERENTIE:</li> </t> </ul> </t> <t t-out=\"ctx['mandate_ref'] or ''\"/> \n"
" <t t-if=\"ctx.get('creditor_iban') or ctx.get('mandate_ref')\"> <ul> <t t-if=\"ctx.get('mandate_ref')\">                          </t>\n"
"                       </ul>\n"
"                   </t>\n"
"                   <br/>\n"
"                    Aarzel niet om contact met ons op te nemen als je een vraag hebt.\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"company.name or ''\">UwBedrijf</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-if=\"company.phone\">\n"
"                       <t t-out=\"company.phone or ''\">******-123-4567</t> |\n"
"                    </t>\n"
"                    <t t-if=\"company.email\">\n"
"                        i <a t-attf-href=\"'mailto:%s' % {{ company.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"company.email or ''\">nfo@you</a> rcompany.com\n"
"                    </t>\n"
"                    <t t-if=\"company.website\">\n"
"                        <a t-attf-href=\"'%s' % {{ company.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"company.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Aangedreven door <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=mail\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "

#. module: account_sepa_direct_debit
#: model:mail.template,body_html:account_sepa_direct_debit.email_template_sdd_mandate_expiring
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #FFFFFF; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\">\n"
"                    <tr>\n"
"                        <td align=\"center\">\n"
"                            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: #FFFFFF; color: #454748; border-collapse:separate;\">\n"
"                                <tbody>\n"
"                                    <!-- CONTENT -->\n"
"                                    <tr>\n"
"                                        <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                                <tr>\n"
"                                                    <td valign=\"top\" style=\"font-size: 13px;\">\n"
"                                                        <div>\n"
"                                                            Hello,<br/><br/>\n"
"                                                            A SEPA Direct Debit mandate will reach its automatic expiration date on <span t-out=\"format_date(object._get_expiry_date_per_mandate()[object])\">2020-04-18</span><br/>\n"
"                                                            This can be caused by one of the following reason:\n"
"                                                            <ul>\n"
"                                                                <li>\n"
"                                                                    That date is the mandate end date agreed upon signature.\n"
"                                                                </li>\n"
"                                                                <li>\n"
"                                                                    It would then be more than 36 months since the last time this mandate was used.\n"
"                                                                </li>\n"
"                                                            </ul>\n"
"                                                            <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                                                                <a t-attf-href=\"{{ website_url }}/odoo/sdd-mandates/{{ object.id }}\" style=\"background-color: #4caf50; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                                                    Go to Mandate\n"
"                                                                </a>\n"
"                                                            </div>\n"
"                                                            Security Tip: Check that the domain name you are redirected to is: <b><a t-att-href=\"object.get_base_url()\" t-out=\"object.get_base_url() or ''\">\n"
"                                                            https://yourcompany.odoo.com</a></b>\n"
"                                                        </div>\n"
"                                                    </td>\n"
"                                                </tr>\n"
"                                                <tr>\n"
"                                                    <td style=\"text-align:center;\">\n"
"                                                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                                    </td>\n"
"                                                </tr>\n"
"                                            </table>\n"
"                                        </td>\n"
"                                    </tr>\n"
"                                </tbody>\n"
"                            </table>\n"
"                        </td>\n"
"                    </tr>\n"
"                    <!-- POWERED BY -->\n"
"                    <tr>\n"
"                        <td align=\"center\" style=\"min-width: 590px;\">\n"
"                            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"                                <tr>\n"
"                                    <td style=\"text-align: center; font-size: 13px;\">\n"
"                                        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=auth\" style=\"color: #875A7B;\">Odoo</a>\n"
"                                    </td>\n"
"                                </tr>\n"
"                            </table>\n"
"                            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"                                <tr>\n"
"                                    <td style=\"text-align: center; font-size: 11px;\">\n"
"                                        PS: This is an automated email sent by Odoo Accounting to notify you a SEPA Direct Debit mandate is going to get automatically closed.\n"
"                                    </td>\n"
"                                </tr>\n"
"                            </table>\n"
"                        </td>\n"
"                    </tr>\n"
"                </table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #FFFFFF; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\">\n"
"                    <tr>\n"
"                        <td align=\"center\">\n"
"                            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: #FFFFFF; color: #454748; border-collapse:separate;\">\n"
"                                <tbody>\n"
"                                    <!-- CONTENT -->\n"
"                                    <tr>\n"
"                                        <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                                <tr>\n"
"                                                    <td valign=\"top\" style=\"font-size: 13px;\">\n"
"                                                        <div>\n"
"                                                            Hallo,<br/><br/>\n"
"                                                            Een SEPA Automatische Incasso mandaat zal zijn automatische vervaldatum op <span t-out=\"format_date(object._get_expiry_date_per_mandate()[object])\">2020-04-18</span> bereiken<br/>\n"
"                                                            Dit kan worden veroorzaakt door een van de volgende redenen:\n"
"                                                            <ul>\n"
"                                                                <li>\n"
"                                                                    Die datum is de einddatum van het mandaat die is overeengekomen bij ondertekening.\n"
"                                                                </li>\n"
"                                                                <li>\n"
"                                                                    Het zou dan meer dan 36 maanden geleden zijn dat dit mandaat voor het laatst is gebruikt.\n"
"                                                                </li>\n"
"                                                            </ul>\n"
"                                                            <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                                                                <a t-attf-href=\"{{ website_url }}/odoo/sdd-mandates/{{ object.id }}\" style=\"background-color: #4caf50; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                                                    Ga naar mandaat\n"
"                                                                </a>\n"
"                                                            </div>\n"
"                                                            Beveiligingstip: Controleer of de domeinnaam waar je naar wordt doorverwezen: <b><a t-att-href=\"object.get_base_url()\" t-out=\"object.get_base_url() or ''\">\n"
"                                                            https://yourcompany.odoo.com</a></b>\n"
"                                                        </div>\n"
"                                                    </td>\n"
"                                                </tr>\n"
"                                                <tr>\n"
"                                                    <td style=\"text-align:center;\">\n"
"                                                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                                    </td>\n"
"                                                </tr>\n"
"                                            </table>\n"
"                                        </td>\n"
"                                    </tr>\n"
"                                </tbody>\n"
"                            </table>\n"
"                        </td>\n"
"                    </tr>\n"
"                    <!-- POWERED BY -->\n"
"                    <tr>\n"
"                        <td align=\"center\" style=\"min-width: 590px;\">\n"
"                            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"                                <tr>\n"
"                                    <td style=\"text-align: center; font-size: 13px;\">\n"
"                                        Aangedreven door <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=auth\" style=\"color: #875A7B;\">Odoo</a>\n"
"                                    </td>\n"
"                                </tr>\n"
"                            </table>\n"
"                            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"                                <tr>\n"
"                                    <td style=\"text-align: center; font-size: 11px;\">\n"
"                                        PS: Dit is een geautomatiseerde e-mail die wordt verzonden door Odoo Accounting om je te informeren dat een SEPA-incassomachtiging automatisch wordt afgesloten.\n"
"                                    </td>\n"
"                                </tr>\n"
"                            </table>\n"
"                        </td>\n"
"                    </tr>\n"
"                </table>\n"
"            "

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_payment.py:0
msgid ""
"A SEPA direct debit version should be selected to generate the addresses in "
"the export file."
msgstr ""
"Om de adressen in het exportbestand te genereren, moet een SEPA-automatische"
" incassoversie worden geselecteerd."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_payment.py:0
msgid ""
"A SEPA direct debit version should be selected to generate the export file."
msgstr ""
"Om het exportbestand te genereren moet een SEPA-versie voor automatische "
"incasso worden geselecteerd."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid ""
"A customer account is required to validate a SEPA Direct Debit mandate."
msgstr ""
"Een klantrekening is vereist om een SEPA-incassomachtiging te valideren."

#. module: account_sepa_direct_debit
#: model_terms:ir.actions.act_window,help:account_sepa_direct_debit.account_sepa_direct_debit_mandate_tree_act
msgid ""
"A mandate represents the authorization you receive from a customer\n"
"                    to automatically collect money on her account."
msgstr ""
"Een mandaat vertegenwoordigt de autorisatie die je van een klant krijgt\n"
"om automatisch geld op haar rekening te incasseren."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__partner_bank_id
msgid "Account of the customer to collect payments from."
msgstr "Bankrekening van de klant om betalingen van af te houden."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_needaction
msgid "Action Needed"
msgstr "Actie gevraagd"

#. module: account_sepa_direct_debit
#: model:ir.model.fields.selection,name:account_sepa_direct_debit.selection__sdd_mandate__state__active
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sdd_mandate_search_view
msgid "Active"
msgstr "Actief"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__activity_ids
msgid "Activities"
msgstr "Activiteiten"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Activiteit uitzondering decoratie"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__activity_state
msgid "Activity State"
msgstr "Activiteitsfase"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__activity_type_icon
msgid "Activity Type Icon"
msgstr "Activiteitensoort icoon"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid "All the payments in the batch must have the same SDD scheme."
msgstr "Alle betalingen in de batch moeten hetzelfde SDD-schema hebben."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "Antwerp"
msgstr "Antwerpen"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid ""
"As part of your rights, you are entitled to a refund from your bank under "
"the terms and conditions of your agreement with your bank. Your rights are "
"explained in a statement that you can obtain from your bank. A refund must "
"be claimed within 8 weeks starting from the date on which your account was "
"debited."
msgstr ""
"Als onderdeel van je rechten heb je recht op terugbetaling door je bank "
"onder de voorwaarden van de overeenkomst met je bank. Je rechten staan "
"beschreven in een verklaring die je kunt opvragen bij je bank. Een "
"terugbetaling moet worden aangevraagd binnen 8 weken vanaf de datum waarop "
"je rekening is afgeschreven."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_attachment_count
msgid "Attachment Count"
msgstr "Aantal bijlagen"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__author_id
msgid "Author"
msgstr "Auteur"

#. module: account_sepa_direct_debit
#: model:ir.model.fields.selection,name:account_sepa_direct_debit.selection__account_batch_payment__sdd_scheme__b2b
#: model:ir.model.fields.selection,name:account_sepa_direct_debit.selection__sdd_mandate__sdd_scheme__b2b
msgid "B2B"
msgstr "B2B"

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_res_partner_bank
msgid "Bank Accounts"
msgstr "Bankrekeningen"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.view_batch_payment_form_inherit
msgid "Batch Booking"
msgstr "Batchboeking"

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_account_batch_payment
msgid "Batch Payment"
msgstr "Batchbetaling"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "Belgium"
msgstr "België"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__body_has_template_value
msgid "Body content is the same as the template"
msgstr "De inhoud is hetzelfde als de sjabloon"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "Building C"
msgstr "Gebouw C"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "By signing this mandate form, you authorise (A)"
msgstr "Door dit mandaatformulier te ondertekenen geeft je machtiging tot (A)"

#. module: account_sepa_direct_debit
#: model:ir.model.fields.selection,name:account_sepa_direct_debit.selection__account_batch_payment__sdd_scheme__core
#: model:ir.model.fields.selection,name:account_sepa_direct_debit.selection__sdd_mandate__sdd_scheme__core
msgid "CORE"
msgstr "CORE"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "CREDIT-1234"
msgstr "KREDIET-1234"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__can_edit_body
msgid "Can Edit Body"
msgstr "Kan tekst bewerken"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_send_form
msgid "Cancel"
msgstr "Annuleren"

#. module: account_sepa_direct_debit
#: model:ir.model.fields.selection,name:account_sepa_direct_debit.selection__sdd_mandate__state__cancelled
msgid "Cancelled"
msgstr "Geannuleerd"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/wizard/sdd_mandate_send.py:0
msgid "Check Partner(s) Email(s)"
msgstr "Controleer het e-mailadres van het contact(s)"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
msgid "Close"
msgstr "Afsluiten"

#. module: account_sepa_direct_debit
#: model:ir.model.fields.selection,name:account_sepa_direct_debit.selection__sdd_mandate__state__closed
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sdd_mandate_search_view
msgid "Closed"
msgstr "Gesloten"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
msgid "Collections"
msgstr "Inningen"

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_res_company
msgid "Companies"
msgstr "Bedrijven"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__company_id
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__company_id
msgid "Company"
msgstr "Bedrijf"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__company_id
msgid "Company for whose invoices the mandate can be used."
msgstr "Bedrijf waarvoor het mandaat gebruikt kan worden."

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_res_config_settings
msgid "Config Settings"
msgstr "Configuratie instellingen"

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_res_partner
msgid "Contact"
msgstr "Contact"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__body
msgid "Contents"
msgstr "Inhoud"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_payment__sdd_mandate_usable
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_payment_register__sdd_mandate_usable
msgid "Could a SDD mandate be used?"
msgstr "Kan een SDD mandaat worden gebruikt?"

#. module: account_sepa_direct_debit
#: model_terms:ir.actions.act_window,help:account_sepa_direct_debit.account_sepa_direct_debit_mandate_tree_act
msgid "Create a new direct debit customer mandate"
msgstr "Maak een nieuw automatisch incasso mandaat aan"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_account_payment_form
msgid "Create it."
msgstr "Maak het aan."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__create_uid
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__create_uid
msgid "Created by"
msgstr "Aangemaakt door"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__create_date
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__create_date
msgid "Created on"
msgstr "Aangemaakt op"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "Creditor"
msgstr "Crediteur"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_res_config_settings__sdd_creditor_identifier
msgid "Creditor Identifier"
msgstr "Identificator crediteur"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_res_config_settings__sdd_creditor_identifier
msgid "Creditor identifier of your company within SEPA scheme."
msgstr "Identificator crediteur van je bedrijf binnen de SEPA-regeling."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__partner_id
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__partner_id
msgid "Customer"
msgstr "Klant"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
msgid "Customer mandate"
msgstr "Klant mandaat"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__partner_id
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate_send__partner_id
msgid "Customer whose payments are to be managed by this mandate."
msgstr "Klanten wiens betaling die door dit mandaat beheerd moet worden."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "DEBT1234"
msgstr "SCHULD1234"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__start_date
msgid "Date from which the mandate can be used (inclusive)."
msgstr "Datum vanaf wanneer het mandaat gebruikt kan worden (inclusief)."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__end_date
msgid ""
"Date until which the mandate can be used. It will automatically be closed "
"after this date."
msgstr ""
"Datum tot wanneer het mandaat kan worden gebruikt. Het wordt automatisch "
"gesloten na deze datum."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_account_batch_payment__sdd_required_collection_date
msgid ""
"Date when the company expects to receive the payments of this batch. It "
"can't be inferior to the sending day + the longest pre-notification period "
"defined in the mandates linked to this batch."
msgstr ""
"Datum waarop het bedrijf de betalingen van deze batch verwacht te ontvangen."
" Deze datum kan niet lager zijn dan de verzenddag + de langste "
"vooraankondigingstermijn die is gedefinieerd in de mandaten die aan deze "
"batch zijn gekoppeld."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "Debtor"
msgstr "Debiteur"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__debtor_id_code
msgid "Debtor Identifier"
msgstr "Identificator debiteur"

#. module: account_sepa_direct_debit
#: model:ir.model.fields.selection,name:account_sepa_direct_debit.selection__account_journal__debit_sepa_pain_version__pain_008_001_02
msgid "Default (Pain 008.001.02)"
msgstr "Standaard (Pijn 008.001.02)"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: account_sepa_direct_debit
#: model:ir.actions.act_window,name:account_sepa_direct_debit.account_sepa_direct_debit_mandate_tree_act
#: model:ir.actions.act_window,name:account_sepa_direct_debit.account_sepa_direct_debit_partner_mandates
#: model:ir.ui.menu,name:account_sepa_direct_debit.account_sepa_direct_debit_customer_mandates_menu
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sdd_res_partner_view
msgid "Direct Debit Mandates"
msgstr "Incasso mandaten"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_account_journal_dashboard_kanban_view
msgid "Direct Debit Payment to Collect"
msgstr "Incassobetaling te innen"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__payments_to_collect_nber
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_account_journal_dashboard_kanban_view
msgid "Direct Debit Payments to Collect"
msgstr "Incassobetalingen te innen"

#. module: account_sepa_direct_debit
#: model:ir.actions.act_window,name:account_sepa_direct_debit.action_sdd_payments_to_collect
msgid "Direct debit payments to collect"
msgstr "Incassobetalingen te innen"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__display_name
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__display_name
msgid "Display Name"
msgstr "Schermnaam"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__checkbox_download
msgid "Download"
msgstr "Downloaden"

#. module: account_sepa_direct_debit
#: model:ir.model.fields.selection,name:account_sepa_direct_debit.selection__sdd_mandate__state__draft
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sdd_mandate_search_view
msgid "Draft"
msgstr "Concept"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__state
msgid ""
"Draft: Validate before use.\n"
"Active: Valid mandates to collect payments.\n"
"Cancelled: Mandates never validated.\n"
"Closed: Expired or manually closed mandates. Previous transactions remain valid.\n"
"Revoked: Fraudulent mandates. Previous invoices might need reimbursement.\n"
msgstr ""
"Concept: Valideren voor gebruik.\n"
"Actief: Geldige mandaten om betalingen te innen.\n"
"Geannuleerd: Mandaten nooit gevalideerd.\n"
"Gesloten: Vervallen of handmatig gesloten mandaten. Eerdere transacties blijven geldig.\n"
"Ingetrokken: Frauduleuze mandaten. Eerdere facturen moeten mogelijk worden terugbetaald.\n"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__checkbox_send_mail
msgid "Email"
msgstr "E-mail"

#. module: account_sepa_direct_debit
#: model:mail.template,description:account_sepa_direct_debit.email_template_sdd_new_mandate
msgid "Email requesting the customer to sign the mandate attached"
msgstr ""
"E-mail met het verzoek aan de klant om het bijgevoegde mandaat te "
"ondertekenen"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__template_id
msgid "Email template"
msgstr "E-mailsjabloon"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__end_date
msgid "End Date"
msgstr "Einddatum"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "End date"
msgstr "Einddatum"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_res_partner__sdd_mandate_ids
#: model:ir.model.fields,help:account_sepa_direct_debit.field_res_users__sdd_mandate_ids
msgid "Every mandate belonging to this partner."
msgstr "Elk mandaat van deze contact."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__expiration_warning_already_sent
msgid "Expiration warning sent"
msgstr "Vervalwaarschuwing verzonden"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_follower_ids
msgid "Followers"
msgstr "Volgers"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_partner_ids
msgid "Followers (Partners)"
msgstr "Volgers (Contacten)"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesome icoon bijv. fa-tasks"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__debtor_id_code
msgid "Free reference identifying the debtor in your company."
msgstr ""
"Vrije referentie waarmee de debiteur van je bedrijf wordt geïdentificeerd."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid "Go to journal"
msgstr "Ga naar dagboek"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid "Go to mandates"
msgstr "Ga naar mandaten"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid "Go to payments"
msgstr "Ga naar betalingen"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid "Go to settings"
msgstr "Ga naar instellingen"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_payment_register_form_inherit_account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_account_payment_form
msgid "Good news! A valid SEPA Mandate is available."
msgstr "Goed nieuws! Er is een geldig SEPA-mandaat beschikbaar."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__has_message
msgid "Has Message"
msgstr "Heeft bericht"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__partner_bank_id
msgid "IBAN"
msgstr "IBAN"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__id
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__id
msgid "ID"
msgstr "ID"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__activity_exception_icon
msgid "Icon"
msgstr "Icoon"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icoon om uitzondering op activiteit aan te geven."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "Identification code"
msgstr "Identificatiecode"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__name
msgid "Identifier"
msgstr "Identifier"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Indien aangevinkt vragen nieuwe berichten je aandacht."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__message_has_error
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Indien aangevinkt hebben sommige berichten een leveringsfout."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/res_company.py:0
msgid "Invalid creditor identifier. Make sure you made no typo."
msgstr ""
"Ongeldige identificator crediteur. Controleer of je geen typefout heeft "
"gemaakt."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/res_company.py:0
msgid "Invalid creditor identifier. Wrong format."
msgstr "Ongeldige identifier crediteur: Verkeerd formaat."

#. module: account_sepa_direct_debit
#: model:mail.message.subtype,description:account_sepa_direct_debit.sdd_mt_invoice_paid_with_mandate
msgid "Invoice paid via direct debit."
msgstr "Factuur betaald via auto incasso."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__paid_invoice_ids
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
msgid "Invoices Paid"
msgstr "Betaalde facturen"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.view_account_invoice_filter
msgid "Invoices matching a valid SEPA Direct Debit Mandate"
msgstr "Facturen die overeenkomen met een geldig SEPA-incassomachtiging"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__paid_invoice_ids
msgid "Invoices paid using this mandate."
msgstr "Facturen betaald met dit mandaat."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
msgid "Invoices paid with this mandate."
msgstr "Facturen betaald met dit mandaat."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__is_mail_template_editor
msgid "Is Editor"
msgstr "Is bewerker"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_is_follower
msgid "Is Follower"
msgstr "Is een volger"

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_account_journal
msgid "Journal"
msgstr "Dagboek"

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_account_move
msgid "Journal Entry"
msgstr "Boeking"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__lang
msgid "Language"
msgstr "Taal"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__write_uid
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__write_date
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__write_date
msgid "Last Updated on"
msgstr "Laatst bijgewerkt op"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_main_attachment_id
msgid "Main Attachment"
msgstr "Hoofdbijlage"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__mandate_id
msgid "Mandate"
msgstr "Mandaat"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__mandate_pdf_file
msgid "Mandate Form PDF"
msgstr "Mandaatformulier PDF"

#. module: account_sepa_direct_debit
#: model:ir.actions.report,name:account_sepa_direct_debit.sdd_mandate_form_report_main
msgid "Mandate form"
msgstr "Mandaat formulier"

#. module: account_sepa_direct_debit
#: model:ir.model.constraint,message:account_sepa_direct_debit.constraint_sdd_mandate_name_unique
msgid "Mandate identifier must be unique! Please choose another one."
msgstr ""
"De identificatiecode van het mandaat moet uniek zijn! Kies een andere."

#. module: account_sepa_direct_debit
#: model:ir.actions.server,name:account_sepa_direct_debit.sdd_mandate_state_cron_ir_actions_server
msgid "Mandate state updater"
msgstr "Mandaat status updater"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "Marc Demo"
msgstr "Marc Demo"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_has_error
msgid "Message Delivery error"
msgstr "Bericht afleverfout"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_ids
msgid "Messages"
msgstr "Berichten"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Mijn activiteit deadline"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Volgende activiteitenafspraak"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Volgende activiteit deadline"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__activity_summary
msgid "Next Activity Summary"
msgstr "Volgende activiteit overzicht"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__activity_type_id
msgid "Next Activity Type"
msgstr "Volgende activiteit type"

#. module: account_sepa_direct_debit
#: model_terms:ir.actions.act_window,help:account_sepa_direct_debit.action_sdd_payments_to_collect
msgid "No direct debit payment to collect"
msgstr "Geen incassobetaling te innen"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_needaction_counter
msgid "Number of Actions"
msgstr "Aantal acties"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__payments_to_collect_nber
msgid ""
"Number of Direct Debit payments to be collected for this mandate, that is, "
"the number of payments that have been generated and posted thanks to this "
"mandate and still needs their XML file to be generated and sent to the bank "
"to debit the customer's account."
msgstr ""
"Aantal incasso betalingen welke geïncasseerd moeten worden voor dit mandaat,"
" dit is het aantal betalingen dat gegenereerd en geboekt zijn dankzij dit "
"mandaat en waar nog geen XML bestand voor gegenereerd en verstuurd is naar "
"de bank van het debet bankrekening nummer."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_has_error_counter
msgid "Number of errors"
msgstr "Aantal fouten"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__paid_invoices_nber
msgid "Number of invoices paid with this mandate."
msgstr "Aantal facturen betaald met dit mandaat."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Aantal berichten die actie vereisen"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Aantal berichten met leveringsfout"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "Odoo PVT LTD"
msgstr "Odoo PVT LTD"

#. module: account_sepa_direct_debit
#: model_terms:ir.actions.act_window,help:account_sepa_direct_debit.account_sepa_direct_debit_mandate_tree_act
msgid ""
"Once an invoice is made\n"
"                    in Odoo for a customer having a mandate active on the invoice date,\n"
"                    its validation will trigger its automatic payment, and you will\n"
"                    then only have to generate a SEPA Direct Debit (SDD) XML file containing this operation\n"
"                    and send it to your bank to effectively get paid."
msgstr ""
"Als een factuur is gemaakt\n"
"                    in Odoo voor een klant met een actief mandaat op de factuurdatum,\n"
"                    zal bij de bevestiging automatisch de betalingen gemaakt worden en zal je\n"
"                    vervolgens een SEPA Direct Debit (SDD) XML-bestand moeten genereren die deze betaling bevat\n"
"                    en naar je bank moeten versturen om betaald te krijgen."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_account_bank_statement_line__sdd_mandate_id
#: model:ir.model.fields,help:account_sepa_direct_debit.field_account_move__sdd_mandate_id
#: model:ir.model.fields,help:account_sepa_direct_debit.field_account_payment__sdd_mandate_id
msgid ""
"Once this invoice has been paid with Direct Debit, contains the mandate that"
" allowed the payment."
msgstr ""
"Wanneer de factuur is betaald met auto incasso bevat deze de mandaat dat de "
"betaling toestond."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__one_off
msgid "One-off Mandate"
msgstr "Eenmalig mandaat"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid ""
"Only IBAN account numbers can receive SEPA Direct Debit payments. Please "
"select a journal associated to one or add an IBAN bank account to the "
"current journal"
msgstr ""
"Alleen IBAN-rekeningnummers kunnen SEPA Automatische Incasso-betalingen "
"ontvangen. Selecteer een dagboek dat gekoppeld is aan één of voeg een IBAN-"
"bankrekening toe aan het huidige dagboek"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid "Only mandates in draft state can be deleted."
msgstr "Alleen mandaten in concept kunnen worden verwijderd."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_payment_register_form_inherit_account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_account_payment_form
msgid "Oops! No valid SEPA mandate for this customer."
msgstr "Oeps! Geen geldige SEPA-machtiging voor deze klant."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid "Open customer"
msgstr "Open klant"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid "Open customers"
msgstr "Open klanten"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sdd_res_partner_view
msgid "Open this partner's mandates"
msgstr "Open de mandaten van deze contact"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate_send__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"Optionele vertaaltaal (ISO-code) om te selecteren bij het verzenden van een "
"e-mail. Indien niet ingesteld, wordt de Engelse versie gebruikt. Dit moet "
"meestal een placeholder zijn die de juiste taal biedt, bijv. {{ "
"object.partner_id.lang }}."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_account_payment_form
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_account_payment_with_mandates_tree
msgid "Originating SEPA mandate"
msgstr "Originele SEPA mandaten"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid "Paid Invoices"
msgstr "Betaalde facturen"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__paid_invoices_nber
msgid "Paid Invoices Number"
msgstr "Aantal betaalde facturen"

#. module: account_sepa_direct_debit
#: model:mail.message.subtype,name:account_sepa_direct_debit.sdd_mt_invoice_paid_with_mandate
msgid "Paid via direct debit"
msgstr "Betaald via auto incasso"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/wizard/sdd_mandate_send.py:0
msgid "Partner should have an email address."
msgstr "Contact moet een e-mailadres hebben."

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_account_payment_register
msgid "Pay"
msgstr "Betaal"

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_account_payment_method
msgid "Payment Methods"
msgstr "Betaalwijzes"

#. module: account_sepa_direct_debit
#: model:mail.template,subject:account_sepa_direct_debit.email_template_sdd_pre_notification
msgid "Payment notification {{ object.memo }}"
msgstr "Kennisgeving betaling {{ object.memo }}"

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_account_payment
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__payment_ids
msgid "Payments"
msgstr "Betalingen"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
msgid "Payments generated for this mandate that have not yet been collected."
msgstr ""
"Betalingen gegenereerd door dit mandaat die nog niet geïncasseerd zijn."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__payment_ids
msgid "Payments generated thanks to this mandate."
msgstr "Betalingen aangemaakt dankzij dit mandaat."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.view_account_move_line_payment_filter
msgid "Payments matching a valid SEPA Direct Debit Mandate"
msgstr "Betalingen die overeenkomen met een geldig SEPA-incassomachtiging"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid "Payments to Collect"
msgstr "Te incasseren betalingen"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid "Payments without mandate"
msgstr "Betalingen zonder mandaat"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_report_invoice_document
msgid ""
"Please do not pay it manually, the payment will be asked to your bank to be processed\n"
"                        automatically."
msgstr ""
"Betaal niet handmatig, de betaling wordt verstuurd naar je bank om automatisch uitgevoerd te \n"
"worden."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__pre_notification_period
msgid "Pre-notification"
msgstr "Kennisgeving vooraf"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid "Problematic mandates"
msgstr "Problematische mandaten"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__rating_ids
msgid "Ratings"
msgstr "Beoordelingen"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__recipient_ids
msgid "Recipients"
msgstr "Ontvangers"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__render_model
msgid "Rendering Model"
msgstr "Weergavemodel"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_account_batch_payment__sdd_batch_booking
msgid "Request batch booking from the bank for the related bank statements."
msgstr ""
"Vraag batch boekingen van de bank voor de gerelateerde rekeningafschriften."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_batch_payment__sdd_required_collection_date
msgid "Required collection date"
msgstr "Gewenste incassodatum"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__activity_user_id
msgid "Responsible User"
msgstr "Verantwoordelijke gebruiker"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
msgid "Revoke"
msgstr "Intrekken"

#. module: account_sepa_direct_debit
#: model:ir.model.fields.selection,name:account_sepa_direct_debit.selection__sdd_mandate__state__revoked
msgid "Revoked"
msgstr "Ingetrokken"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_view_account_payment_search
msgid "Revoked SDD Mandate"
msgstr "SDD mandaat intrekken"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "Rue de la Loi, 16"
msgstr "Wetstraat, 16"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.view_account_payment_search
msgid "SDD B2B"
msgstr "SDD B2B"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_batch_payment__sdd_batch_booking
msgid "SDD Batch Booking"
msgstr "SDD Batchboeking"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.view_account_payment_search
msgid "SDD CORE"
msgstr "SDD CORE"

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_sdd_mandate
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.view_account_move_line_payment_filter
msgid "SDD Mandate"
msgstr "Incasso mandaat"

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_sdd_mandate_send
msgid "SDD Mandate Send"
msgstr "SDD mandaat verzenden"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_batch_payment__sdd_scheme
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_payment__sdd_mandate_scheme
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__sdd_scheme
msgid "SDD Scheme"
msgstr "SDD schema"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_res_partner__sdd_count
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_res_users__sdd_count
msgid "SDD count"
msgstr "SDD aantal"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_res_company__sdd_creditor_identifier
msgid "SDD creditor identifier"
msgstr "SSD crediteur Identificator"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid "SDD scheme is set on the customer mandate."
msgstr "SDD-regeling is ingesteld op het mandaat van de klant."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "SEPA Business-to-Business Direct Debit Mandate"
msgstr "SEPA Business-to-Business Incasso Machtiging"

#. module: account_sepa_direct_debit
#: model:account.payment.method,name:account_sepa_direct_debit.payment_method_sdd
msgid "SEPA Direct Debit"
msgstr "SEPA automatische incasso"

#. module: account_sepa_direct_debit
#: model:mail.template,name:account_sepa_direct_debit.email_template_sdd_pre_notification
msgid "SEPA Direct Debit Customer Pre-Notification mail"
msgstr "SEPA Automatische Incasso Pre-Notificatie e-mail"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "SEPA Direct Debit Mandate"
msgstr "SEPA automatische incasso mandaat"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_journal__debit_sepa_pain_version
msgid "SEPA Direct Debit Pain Version"
msgstr "SEPA-automatische incasso Pain-versie"

#. module: account_sepa_direct_debit
#: model:mail.template,name:account_sepa_direct_debit.email_template_sdd_new_mandate
msgid "SEPA Direct Debit Sending"
msgstr "SEPA incasso versturen"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_res_company__sdd_creditor_identifier
msgid ""
"SEPA Direct Debit creditor identifier of the company, given by the bank."
msgstr ""
"SEPA automatische incasso creditor indentifier van het bedrijf, wordt "
"gegeven door de bank."

#. module: account_sepa_direct_debit
#: model:mail.template,name:account_sepa_direct_debit.email_template_sdd_mandate_expiring
#: model:mail.template,subject:account_sepa_direct_debit.email_template_sdd_mandate_expiring
msgid "SEPA Direct Debit mandate expiration warning"
msgstr "Waarschuwing vervaldatum SEPA-incassomachtiging"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid ""
"SEPA Direct Debit scheme only accepts IBAN account numbers. Please select an"
" IBAN-compliant debtor account for this mandate."
msgstr ""
"SEPA automatische incasso schema accepteert alleen IBAN rekeningnummers. "
"Selecteer een debiteur met een correct IBAN nummer voor dit mandaat."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.view_account_journal_form
msgid "SEPA Pain Version"
msgstr "SEPA Pain Versie"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.view_account_journal_form
msgid "SEPA Pain version"
msgstr "SEPA Pain-versie"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_payment_register_form_inherit_account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_account_payment_form
msgid "SEPA Scheme operates in Euro."
msgstr "Het SEPA-systeem werkt in euro."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid "SEPA direct debit stateless customer"
msgstr "SEPA domiciliëring stateloze klant"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid ""
"SEPA regulations set the minimum pre-notification period to a minimum of 2 "
"days to allow enough time for the customer to check that their account is "
"adequately funded."
msgstr ""
"In de SEPA-voorschriften is de minimale periode voorafgaand aan de "
"kennisgeving vastgesteld op minimaal 2 dagen, zodat de klant genoeg tijd "
"heeft om te controleren of zijn rekening voldoende is gefinancierd."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "SEPA-CUST-001"
msgstr "SEPA-CUST-001"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "SEPA1234"
msgstr "SEPA1234"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS fout bij versturen"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_bank_statement_line__sdd_has_usable_mandate
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_move__sdd_has_usable_mandate
msgid "Sdd Has Usable Mandate"
msgstr "SDD heeft een bruikbaar mandaat"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_bank_statement_line__sdd_mandate_id
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_move__sdd_mandate_id
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_payment__sdd_mandate_id
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_res_partner__sdd_mandate_ids
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_res_users__sdd_mandate_ids
msgid "Sdd Mandate"
msgstr "SDD mandaat"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_payment.py:0
msgid "Select a SEPA Direct Debit version before generating the XML."
msgstr "Selecteer een SEPA-incassoversie voordat je de XML genereert."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid "Send"
msgstr "Verzenden"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_send_form
msgid "Send & Print"
msgstr "Verzend & afdrukken"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__is_sent
msgid "Sent to the customer"
msgstr "Verzonden naar de klant"

#. module: account_sepa_direct_debit
#: model:mail.template,description:account_sepa_direct_debit.email_template_sdd_pre_notification
msgid "Sent to the customer to indicate their account will be charged"
msgstr ""
"Verzonden naar de klant om hem te informeren dat zijn rekening zal worden "
"gedebiteerd"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "Signature"
msgstr "Handtekening"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid ""
"Some draft payments could not be posted because of the lack of any active "
"mandate."
msgstr ""
"Sommige conceptbetalingen konden niet worden gepost vanwege het ontbreken "
"van een actief mandaat."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid "Some payments are linked to an inactive mandate."
msgstr "Sommige betalingen zijn gekoppeld aan een inactief mandaat."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid "Some payments are not linked to any mandate."
msgstr "Sommige betalingen zijn niet gekoppeld aan een mandaat."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__start_date
msgid "Start Date"
msgstr "Begindatum"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__state
msgid "State"
msgstr "Status"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status gebaseerd op activiteiten\n"
"Te laat: Datum is al gepasseerd\n"
"Vandaag: Activiteit datum is vandaag\n"
"Gepland: Toekomstige activiteiten."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__subject
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_send_form
msgid "Subject"
msgstr "Onderwerp"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_account_batch_payment__sdd_scheme
#: model:ir.model.fields,help:account_sepa_direct_debit.field_account_payment__sdd_mandate_scheme
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__sdd_scheme
msgid ""
"The B2B scheme is an optional scheme,\n"
"offered exclusively to business payers.\n"
"Some banks/businesses might not accept B2B SDD."
msgstr ""
"Het B2B-schema is een optioneel schema,\n"
"exclusief aangeboden aan zakelijke betalers.\n"
"Sommige banken / bedrijven accepteren mogelijk geen B2B SDD."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_payment.py:0
msgid ""
"The SEPA Direct Debit mandate associated to the payment has been revoked and"
" cannot be used anymore."
msgstr ""
"De SEPA-incassomachtiging die aan de betaling is gekoppeld, is ingetrokken "
"en kan niet meer worden gebruikt."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid ""
"The bank needs to be informed at least 5 days in advance for collections "
"related to a new mandate and 2 days in advance when the mandate is already "
"known by them. In this case, the minimum collection date must be the "
"%(date)s"
msgstr ""
"De bank moet ten minste 5 dagen van tevoren op de hoogte worden gesteld voor"
" incasso's met betrekking tot een nieuw mandaat en 2 dagen van tevoren als "
"het mandaat al bij hen bekend is. In dit geval moet de minimale incassodatum"
" de %(date)s zijn"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/res_company.py:0
msgid "The creditor identifier exceeds the maximum length of 35 characters."
msgstr ""
"De crediteur identificator is langer dan de maximale lengte van 35 "
"karakters."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid "The customer must have a country"
msgstr "De klant moet een land hebben"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_payment.py:0
msgid ""
"The debtor and creditor city name is a compulsary information when "
"generating the SDD XML."
msgstr ""
"De plaatsnaam van de debiteur en crediteur is een verplichte informatie bij "
"het genereren van de SDD XML."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_payment.py:0
msgid ""
"The debtor and creditor country is a compulsary information when generating "
"the SDD XML."
msgstr ""
"Het land van de debiteur en crediteur is een verplichte informatie bij het "
"genereren van de SDD XML."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid ""
"The debtor identifier you specified exceeds the limitation of 35 characters "
"imposed by SEPA regulation"
msgstr ""
"De debiteur Identifier die je hebt ingegeven heeft te veel karakters, het "
"SEPA limiet is 35 karakters"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid ""
"The end date of the mandate must be posterior or equal to its start date."
msgstr "De einddatum van het mandaat moet na of tijdens de startdatum vallen."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid ""
"The mandate will only be used to pay invoices into the specified time range. If no end date is specified,\n"
"                    you will have to contact us to stop its use. The minimum notification period for creditors\n"
"                    to inform debtors about an upcoming collection is"
msgstr ""
"Het mandaat wordt alleen gebruikt om facturen in het opgegeven tijdsbereik te betalen. Als er geen einddatum is opgegeven,\n"
"                    moet je contact met ons opnemen om het gebruik te stoppen. De minimale kennisgevingstermijn voor schuldeisers\n"
"                    om debiteuren te informeren over een aanstaande incasso is"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__pre_notification_period
msgid ""
"The minimum notice period in days, used to inform the customer prior to "
"collection."
msgstr ""
"De minimale opzegtermijn in dagen die wordt gebruikt om de klant te "
"informeren voordat hij wordt opgehaald."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_payment.py:0
msgid ""
"The payment must be linked to a SEPA Direct Debit mandate in order to "
"generate a Direct Debit XML."
msgstr ""
"De betaling moet worden gekoppeld aan een SEPA Direct Debit-machtiging om "
"een Direct Debit XML te genereren."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__name
msgid "The unique identifier of this mandate."
msgstr "Unieke identificator van dit mandaat."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_report_invoice_document
msgid ""
"This invoice will be paid using direct debit and is only\n"
"                        sent for informative purposes."
msgstr ""
"Deze factuur wordt betaald door gebruik te maken van automatische incasso\n"
"en is alleen verstuurd ter informatie."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid ""
"This mandate is only intended for business-to-business transactions. You are"
" not entitled to a refund from your bank after your account has been "
"debited, but you are entitled to request your bank not to debit your account"
" up until the day on which the payment is due."
msgstr ""
"Deze machtiging is alleen bedoeld voor business-to-business transacties. Je "
"hebt geen recht op terugbetaling door de bank nadat je rekening is "
"afgeschreven, maar je kunt je bank wel verzoeken om geen afschrijving van je"
" rekening te doen tot de dag waarop de betaling verschuldigd is."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid ""
"To solve that, you should create a mandate for each of the involved "
"customers, valid at the moment of the payment date."
msgstr ""
"Om dat op te lossen, dien je voor elk van de betrokken klanten een mandaat "
"aan te maken, geldig op het moment van de betalingsdatum."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__one_off
msgid ""
"True if and only if this mandate can be used for only one transaction. It "
"will automatically go from 'active' to 'closed' after its first use in "
"payment if this option is set.\n"
msgstr ""
"Aangevinkt als alleen dit mandaat kan worden gebruikt voor één transactie. "
"Het mandaat zal automatisch van 'actief' naar 'gesloten' worden gezet na de "
"eerste betaling.\n"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_payment.py:0
msgid ""
"Trying to generate a Direct Debit XML file containing payments from another "
"company than that file's creditor."
msgstr ""
"Proberen een automatische incasso XML bestand te maken die betalingen van "
"een bedrijf bevatten."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_payment.py:0
msgid ""
"Trying to generate a Direct Debit XML for payments coming from another "
"payment method than SEPA Direct Debit."
msgstr ""
"Proberen een automatisch incasso XML bestand te maken voor betalingen met "
"een andere betaalmethode dan SEPA automatische incasso."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_payment.py:0
msgid ""
"Trying to register a payment on a mandate belonging to a different partner."
msgstr ""
"Proberen een betaling te registreren op een mandaat van een andere contact."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Type van de geregistreerde uitzonderingsactiviteit."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid "Under B2B SDD Scheme, the customer must be a company."
msgstr "In het B2B SDD-programma moet de klant een bedrijf zijn."

#. module: account_sepa_direct_debit
#: model:ir.model.fields.selection,name:account_sepa_direct_debit.selection__account_journal__debit_sepa_pain_version__pain_008_001_08
msgid "Updated 2023 (Pain 008.001.08)"
msgstr "Bijgewerkt 2023 (Pijn 008.001.08)"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
msgid "Validate"
msgstr "Bevestigen"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "Validity"
msgstr "Geldig t/m"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/wizard/sdd_mandate_send.py:0
msgid "View Partner(s)"
msgstr "Bekijk contact(s)"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__warnings
msgid "Warnings"
msgstr "Waarschuwingen"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__website_message_ids
msgid "Website Messages"
msgstr "Websiteberichten"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__website_message_ids
msgid "Website communication history"
msgstr "Website communicatie geschiedenis"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/wizard/account_payment_register.py:0
msgid ""
"You can't pay any of the selected invoices using the SEPA Direct Debit "
"method, as no valid mandate is available"
msgstr ""
"Je kunt geen van de geselecteerde facturen betalen met de SEPA Automatische "
"Incasso methode, omdat er geen geldig mandaat beschikbaar is"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/res_partner_bank.py:0
msgid ""
"You cannot delete a bank account linked to an active SEPA Direct Debit "
"mandate."
msgstr ""
"Een bankrekening gekoppeld aan een actief SEPA incasso mandaat kan niet "
"verwijderd worden."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid ""
"You cannot generate a SEPA Direct Debit file with a required collection date inferior to the sending day + the longest pre-notification period defined in the mandates linked to this batch.\n"
"According to these payments mandates, the minimum required date should be the %(minimum_date)s"
msgstr ""
"Je kunt geen SEPA Incassobestand genereren met een vereiste incassodatum die lager is dan de verzenddag + de langste vooraankondigingstermijn die is gedefinieerd in de mandaten die aan deze batch zijn gekoppeld.\n"
"Volgens deze betalingsmandaten moet de minimaal vereiste datum de %(minimum_date)s zijn"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid ""
"Your company must have a creditor identifier in order to issue SEPA Direct "
"Debit payments requests. It can be defined in accounting module's settings."
msgstr ""
"Je bedrijf moet een crediteur identificator hebben om SEPA automatische "
"incasso betalingen te versturen. Het kan ingesteld worden in het "
"instellingen van de boekhouding module."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid ""
"day(s).\n"
"                    It will be sent to the debtor's email."
msgstr ""
"dag(en).\n"
"                    Het wordt naar het e-mailadres van de debiteur gestuurd."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid ""
"to send instructions to your bank to debit your account and (B) your bank to"
" debit your account in accordance with the instructions from"
msgstr ""
"om instructies naar je bank te sturen om je rekening te debiteren en (B) je "
"bank om je rekening te debiteren in overeenstemming met de instructies van"

#. module: account_sepa_direct_debit
#: model:mail.template,subject:account_sepa_direct_debit.email_template_sdd_new_mandate
msgid "{{ object.partner_id.display_name }} SEPA Direct Debit Mandate"
msgstr ""
"{{ object.partner_id.display_name }} SEPA Automatische Incasso Mandaat"
