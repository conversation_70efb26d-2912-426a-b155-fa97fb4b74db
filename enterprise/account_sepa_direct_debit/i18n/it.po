# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_sepa_direct_debit
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON>-Nesselbosch, 2024
# Wil Odoo, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:52+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "(if applicable)"
msgstr "(se applicabile)"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "(optional)"
msgstr "(facoltativo)"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "*********"
msgstr "*********"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "2023-08-10"
msgstr "10-08-2023"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "2023-09-10"
msgstr "10-09-2023"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "380055"
msgstr "380055"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "*************"
msgstr "*************"

#. module: account_sepa_direct_debit
#: model:mail.template,body_html:account_sepa_direct_debit.email_template_sdd_new_mandate
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Dear <t t-out=\"object.partner_id.display_name or ''\">Azure Interior</t>\n"
"                        <br/>\n"
"                        <br/>\n"
"                        Here is your SEPA Direct Debit Mandate to sign to authorize\n"
"                        <t t-out=\"object.company_id.display_name or ''\">YourCompany</t>\n"
"                        to send instructions to your bank to debit your account in accordance with the instructions from\n"
"                        <t t-out=\"object.company_id.display_name or ''\">YourCompany</t>\n"
"                        Do not hesitate to contact us if you have any questions.\n"
"                        <br/>\n"
"                        Best regards,\n"
"                        <t t-if=\"not is_html_empty(user.signature)\" data-o-mail-quote-container=\"1\">\n"
"                            <br/><br/>\n"
"                            <t t-out=\"user.signature\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"                        </t>\n"
"                    </p>\n"
"                </div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Gentile <t t-out=\"object.partner_id.display_name or ''\">Azure Interior</t>\n"
"                        <br/>\n"
"                        <br/>\n"
"                        di seguito il mandato di addebito diretto SEPA da firmare per autorizzare\n"
"                        <t t-out=\"object.company_id.display_name or ''\">YourCompany</t>\n"
"                        a inviare istruzioni alla banca per addebitare il tuo conto secondo le istruzioni di\n"
"                        <t t-out=\"object.company_id.display_name or ''\">YourCompany</t>\n"
"                        Non esitare a contattarci se hai domande.\n"
"                        <br/>\n"
"                        Cordiali saluti,\n"
"                        <t t-if=\"not is_html_empty(user.signature)\" data-o-mail-quote-container=\"1\">\n"
"                            <br/><br/>\n"
"                            <t t-out=\"user.signature\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"                        </t>\n"
"                    </p>\n"
"                </div>\n"
"            "

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
msgid "<span> day(s)</span>"
msgstr "<span> giorno/i</span>"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>Address:</strong>"
msgstr "<strong>Indirizzo:</strong>"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>City: </strong>"
msgstr "<strong>Città: </strong>"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>Country: </strong>"
msgstr "<strong>Nazione: </strong>"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>Creditor identifier:</strong>"
msgstr "<strong>Identificativo creditore:</strong>"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid ""
"<strong>Date and place of signature:</strong> "
"......................................"
msgstr ""
"<strong>Data e luogo della firma:</strong> "
"......................................"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>Email:</strong>"
msgstr "<strong>E-mail:</strong>"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>IBAN:</strong>"
msgstr "<strong>IBAN:</strong>"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>Mandate identifier:</strong>"
msgstr "<strong>Identificativo mandato:</strong>"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid ""
"<strong>Name of the reference party:</strong> "
"......................................"
msgstr ""
"<strong>Nome parte di riferimento:</strong> "
"......................................"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>Phone:</strong>"
msgstr "<strong>Telefono:</strong>"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>Signature:</strong>"
msgstr "<strong>Firma:</strong>"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>Start date:</strong>"
msgstr "<strong>Data inizio:</strong>"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>Transaction type:</strong> recurrent"
msgstr "<strong>Tipo di transazione:</strong> ricorrente"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>Zip: </strong>"
msgstr "<strong>CAP: </strong>"

#. module: account_sepa_direct_debit
#: model:mail.template,body_html:account_sepa_direct_debit.email_template_sdd_pre_notification
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <t t-set=\"company\" t-value=\"object.company_id\"/>\n"
"                    <span style=\"font-size: 10px;\">Your Direct Debit Payment Notification</span><br/>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"right\" t-if=\"not company.uses_default_logo\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ company.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"company.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                    <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    Dear <t t-out=\"object.partner_id.name or ''\">Azure Interior</t><br/>\n"
"                    <br/>\n"
"                    A Direct Debit payment request amounting to\n"
"                    <span style=\"font-weight:bold;\" t-out=\"format_amount(object.amount, object.currency_id) or ''\">$ 0.00</span>\n"
"                    will be sent to your bank.<br/>\n"
"                    Your account ending with <t t-out=\"ctx.get('iban_last_4') or ''\">1234</t> will be automatically debited on the\n"
"                    <span style=\"font-weight:bold;\" t-out=\"format_date(ctx.get('collection_date'))\">2020-04-18</span>,\n"
"                    or shortly thereafter.<br/>\n"
"                    please make sure you have the requested funds.<br/>\n"
"                    <br/>\n"
"                    <t t-if=\"ctx.get('creditor_iban') or ctx.get('mandate_ref')\">\n"
"                        Merchant data:<br/>\n"
"                        <ul>\n"
"                            <t t-if=\"ctx.get('creditor_iban')\">\n"
"                                <li>IBAN: <t t-out=\"ctx['creditor_iban'] or ''\">NO 93 8601 1117947</t></li>\n"
"                            </t>\n"
"                            <t t-if=\"ctx.get('mandate_ref')\">\n"
"                                <li>SEPA DIRECT DEBIT MANDATE REFERENCE: <t t-out=\"ctx['mandate_ref'] or ''\"/></li>\n"
"                            </t>\n"
"                        </ul>\n"
"                    </t>\n"
"                    <br/>\n"
"                    Do not hesitate to contact us if you have any question.\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"company.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-if=\"company.phone\">\n"
"                        <t t-out=\"company.phone or ''\">******-123-4567</t> |\n"
"                    </t>\n"
"                    <t t-if=\"company.email\">\n"
"                        <a t-attf-href=\"'mailto:%s' % {{ company.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"company.email or ''\"><EMAIL></a> |\n"
"                    </t>\n"
"                    <t t-if=\"company.website\">\n"
"                        <a t-attf-href=\"'%s' % {{ company.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"company.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=mail\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <t t-set=\"company\" t-value=\"object.company_id\"/>\n"
"                    <span style=\"font-size: 10px;\">Notifica pagamento addebito diretto</span><br/>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"right\" t-if=\"not company.uses_default_logo\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ company.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"company.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                    <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    Dear <t t-out=\"object.partner_id.name or ''\">Azure Interior</t><br/>\n"
"                    <br/>\n"
"                    alla tua banca verrà inviata una richiesta di pagamento tramite addebito diretto pari a\n"
"                    <span style=\"font-weight:bold;\" t-out=\"format_amount(object.amount, object.currency_id) or ''\">0,00 $</span>\n"
"                    .<br/>\n"
"                    L'importo verrà addebitato automaticamente sul conto, che termina con <t t-out=\"ctx.get('iban_last_4') or ''\">1234</t> il\n"
"                    <span style=\"font-weight:bold;\" t-out=\"format_date(ctx.get('collection_date'))\">18-04-2020</span>,\n"
"                    o poco dopo.<br/>\n"
"                    Assicurati di avere il denaro richiesto.<br/>\n"
"                    <br/>\n"
"                    <t t-if=\"ctx.get('creditor_iban') or ctx.get('mandate_ref')\">\n"
"                        Dati esercente:<br/>\n"
"                        <ul>\n"
"                            <t t-if=\"ctx.get('creditor_iban')\">\n"
"                                <li>IBAN: <t t-out=\"ctx['creditor_iban'] or ''\">NO 93 8601 1117947</t></li>\n"
"                            </t>\n"
"                            <t t-if=\"ctx.get('mandate_ref')\">\n"
"                                <li>RIFERIMENTO MANDATO ADDEBITO DIRETTO SEPA: <t t-out=\"ctx['mandate_ref'] or ''\"/></li>\n"
"                            </t>\n"
"                        </ul>\n"
"                    </t>\n"
"                    <br/>\n"
"                    Se hai domande, non esitare a contattarci.\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"company.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-if=\"company.phone\">\n"
"                        <t t-out=\"company.phone or ''\">******-123-4567</t> |\n"
"                    </t>\n"
"                    <t t-if=\"company.email\">\n"
"                        <a t-attf-href=\"'mailto:%s' % {{ company.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"company.email or ''\"><EMAIL></a> |\n"
"                    </t>\n"
"                    <t t-if=\"company.website\">\n"
"                        <a t-attf-href=\"'%s' % {{ company.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"company.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=mail\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "

#. module: account_sepa_direct_debit
#: model:mail.template,body_html:account_sepa_direct_debit.email_template_sdd_mandate_expiring
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #FFFFFF; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\">\n"
"                    <tr>\n"
"                        <td align=\"center\">\n"
"                            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: #FFFFFF; color: #454748; border-collapse:separate;\">\n"
"                                <tbody>\n"
"                                    <!-- CONTENT -->\n"
"                                    <tr>\n"
"                                        <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                                <tr>\n"
"                                                    <td valign=\"top\" style=\"font-size: 13px;\">\n"
"                                                        <div>\n"
"                                                            Hello,<br/><br/>\n"
"                                                            A SEPA Direct Debit mandate will reach its automatic expiration date on <span t-out=\"format_date(object._get_expiry_date_per_mandate()[object])\">2020-04-18</span><br/>\n"
"                                                            This can be caused by one of the following reason:\n"
"                                                            <ul>\n"
"                                                                <li>\n"
"                                                                    That date is the mandate end date agreed upon signature.\n"
"                                                                </li>\n"
"                                                                <li>\n"
"                                                                    It would then be more than 36 months since the last time this mandate was used.\n"
"                                                                </li>\n"
"                                                            </ul>\n"
"                                                            <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                                                                <a t-attf-href=\"{{ website_url }}/odoo/sdd-mandates/{{ object.id }}\" style=\"background-color: #4caf50; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                                                    Go to Mandate\n"
"                                                                </a>\n"
"                                                            </div>\n"
"                                                            Security Tip: Check that the domain name you are redirected to is: <b><a t-att-href=\"object.get_base_url()\" t-out=\"object.get_base_url() or ''\">\n"
"                                                            https://yourcompany.odoo.com</a></b>\n"
"                                                        </div>\n"
"                                                    </td>\n"
"                                                </tr>\n"
"                                                <tr>\n"
"                                                    <td style=\"text-align:center;\">\n"
"                                                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                                    </td>\n"
"                                                </tr>\n"
"                                            </table>\n"
"                                        </td>\n"
"                                    </tr>\n"
"                                </tbody>\n"
"                            </table>\n"
"                        </td>\n"
"                    </tr>\n"
"                    <!-- POWERED BY -->\n"
"                    <tr>\n"
"                        <td align=\"center\" style=\"min-width: 590px;\">\n"
"                            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"                                <tr>\n"
"                                    <td style=\"text-align: center; font-size: 13px;\">\n"
"                                        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=auth\" style=\"color: #875A7B;\">Odoo</a>\n"
"                                    </td>\n"
"                                </tr>\n"
"                            </table>\n"
"                            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"                                <tr>\n"
"                                    <td style=\"text-align: center; font-size: 11px;\">\n"
"                                        PS: This is an automated email sent by Odoo Accounting to notify you a SEPA Direct Debit mandate is going to get automatically closed.\n"
"                                    </td>\n"
"                                </tr>\n"
"                            </table>\n"
"                        </td>\n"
"                    </tr>\n"
"                </table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #FFFFFF; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\">\n"
"                    <tr>\n"
"                        <td align=\"center\">\n"
"                            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: #FFFFFF; color: #454748; border-collapse:separate;\">\n"
"                                <tbody>\n"
"                                    <!-- CONTENT -->\n"
"                                    <tr>\n"
"                                        <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                                <tr>\n"
"                                                    <td valign=\"top\" style=\"font-size: 13px;\">\n"
"                                                        <div>\n"
"                                                            Ciao,<br/><br/>\n"
"                                                           Un mandato di addebito diretto SEPA scadrà automaticamente il <span t-out=\"format_date(object._get_expiry_date_per_mandate()[object])\">18-04-2020</span><br/>\n"
"                                                            Ciò può essere causato da uno dei seguenti motivi:\n"
"                                                            <ul>\n"
"                                                                <li>\n"
"                                                                    Tale data è la data di scadenza del mandato concordata al momento della firma.\n"
"                                                                </li>\n"
"                                                                <li>\n"
"                                                                    Sarebbero trascorsi più di 36 mesi dall'ultima volta che è stato utilizzato questo mandato.\n"
"                                                                </li>\n"
"                                                            </ul>\n"
"                                                            <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                                                                <a t-attf-href=\"{{ website_url }}/odoo/sdd-mandates/{{ object.id }}\" style=\"background-color: #4caf50; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                                                    Apri il mandato\n"
"                                                                </a>\n"
"                                                            </div>\n"
"                                                            Consiglio sicurezza: verifica che il nome di dominio a cui sei reindirizzato sia: <b><a t-att-href=\"object.get_base_url()\" t-out=\"object.get_base_url() or ''\">\n"
"                                                            https://yourcompany.odoo.com</a></b>\n"
"                                                        </div>\n"
"                                                    </td>\n"
"                                                </tr>\n"
"                                                <tr>\n"
"                                                    <td style=\"text-align:center;\">\n"
"                                                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                                                    </td>\n"
"                                                </tr>\n"
"                                            </table>\n"
"                                        </td>\n"
"                                    </tr>\n"
"                                </tbody>\n"
"                            </table>\n"
"                        </td>\n"
"                    </tr>\n"
"                    <!-- POWERED BY -->\n"
"                    <tr>\n"
"                        <td align=\"center\" style=\"min-width: 590px;\">\n"
"                            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"                                <tr>\n"
"                                    <td style=\"text-align: center; font-size: 13px;\">\n"
"                                        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=auth\" style=\"color: #875A7B;\">Odoo</a>\n"
"                                    </td>\n"
"                                </tr>\n"
"                            </table>\n"
"                            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"                                <tr>\n"
"                                    <td style=\"text-align: center; font-size: 11px;\">\n"
"                                        PS: e-mail automatica inviata da Odoo Contabilità per comunicarti la chiusura automatica di un mandato di addebito diretto SEPA.\n"
"                                    </td>\n"
"                                </tr>\n"
"                            </table>\n"
"                        </td>\n"
"                    </tr>\n"
"                </table>\n"
"            "

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_payment.py:0
msgid ""
"A SEPA direct debit version should be selected to generate the addresses in "
"the export file."
msgstr ""
"È necessario selezionare una versione di addebito diretto SEPA per generare "
"gli indirizzi nel file di esportazione."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_payment.py:0
msgid ""
"A SEPA direct debit version should be selected to generate the export file."
msgstr ""
"È necessario selezionare una versione di addebito diretto SEPA per generare "
"il file di esportazione."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid ""
"A customer account is required to validate a SEPA Direct Debit mandate."
msgstr ""
"Per convalidare un mandato di addebito diretto SEPA è necessario un conto "
"cliente."

#. module: account_sepa_direct_debit
#: model_terms:ir.actions.act_window,help:account_sepa_direct_debit.account_sepa_direct_debit_mandate_tree_act
msgid ""
"A mandate represents the authorization you receive from a customer\n"
"                    to automatically collect money on her account."
msgstr ""
"Un mandato rappresenta l'autorizzazione ricevuta da un cliente\n"
"                    a riscuotere denaro dal proprio conto in modo automatico."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__partner_bank_id
msgid "Account of the customer to collect payments from."
msgstr "Conto del cliente dal quale riscuotere i pagamenti."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_needaction
msgid "Action Needed"
msgstr "Azione richiesta"

#. module: account_sepa_direct_debit
#: model:ir.model.fields.selection,name:account_sepa_direct_debit.selection__sdd_mandate__state__active
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sdd_mandate_search_view
msgid "Active"
msgstr "Attivo"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__activity_ids
msgid "Activities"
msgstr "Attività"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Decorazione eccezione attività"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__activity_state
msgid "Activity State"
msgstr "Stato attività"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icona tipo di attività"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid "All the payments in the batch must have the same SDD scheme."
msgstr ""
"Tutti i pagamenti del raggruppamento devono possedere lo stesso schema SDD."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "Antwerp"
msgstr "Anversa"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid ""
"As part of your rights, you are entitled to a refund from your bank under "
"the terms and conditions of your agreement with your bank. Your rights are "
"explained in a statement that you can obtain from your bank. A refund must "
"be claimed within 8 weeks starting from the date on which your account was "
"debited."
msgstr ""
"Hai diritto ad un rimborso da parte della banca secondo i termini e le "
"condizioni stabiliti nell'accordo siglato con la banca stessa. I tuoi "
"diritti sono spiegati in una dichiarazione che puoi ottenere dalla banca. Un"
" rimborso deve essere richiesto in 8 settimane a partire dalla data nella "
"quale l'importo è stato addebitato sul tuo conto."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_attachment_count
msgid "Attachment Count"
msgstr "Numero allegati"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__author_id
msgid "Author"
msgstr "Autore"

#. module: account_sepa_direct_debit
#: model:ir.model.fields.selection,name:account_sepa_direct_debit.selection__account_batch_payment__sdd_scheme__b2b
#: model:ir.model.fields.selection,name:account_sepa_direct_debit.selection__sdd_mandate__sdd_scheme__b2b
msgid "B2B"
msgstr "B2B"

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_res_partner_bank
msgid "Bank Accounts"
msgstr "Conti bancari"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.view_batch_payment_form_inherit
msgid "Batch Booking"
msgstr "Prenotazione per lotti"

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_account_batch_payment
msgid "Batch Payment"
msgstr "Pagamento raggruppato"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "Belgium"
msgstr "Belgio"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__body_has_template_value
msgid "Body content is the same as the template"
msgstr "Il contenuto del corpo è lo stesso del modello"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "Building C"
msgstr "Edificio C"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "By signing this mandate form, you authorise (A)"
msgstr "Firmando questo modulo di mandato, autorizzi (A)"

#. module: account_sepa_direct_debit
#: model:ir.model.fields.selection,name:account_sepa_direct_debit.selection__account_batch_payment__sdd_scheme__core
#: model:ir.model.fields.selection,name:account_sepa_direct_debit.selection__sdd_mandate__sdd_scheme__core
msgid "CORE"
msgstr "CORE"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "CREDIT-1234"
msgstr "CREDITO-1234"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__can_edit_body
msgid "Can Edit Body"
msgstr "Può modificare corpo"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_send_form
msgid "Cancel"
msgstr "Annulla"

#. module: account_sepa_direct_debit
#: model:ir.model.fields.selection,name:account_sepa_direct_debit.selection__sdd_mandate__state__cancelled
msgid "Cancelled"
msgstr "Annullato"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/wizard/sdd_mandate_send.py:0
msgid "Check Partner(s) Email(s)"
msgstr "Verifica e-mail partner"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
msgid "Close"
msgstr "Chiudi"

#. module: account_sepa_direct_debit
#: model:ir.model.fields.selection,name:account_sepa_direct_debit.selection__sdd_mandate__state__closed
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sdd_mandate_search_view
msgid "Closed"
msgstr "Chiuso"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
msgid "Collections"
msgstr "Riscossioni"

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_res_company
msgid "Companies"
msgstr "Aziende"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__company_id
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__company_id
msgid "Company"
msgstr "Azienda"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__company_id
msgid "Company for whose invoices the mandate can be used."
msgstr "Azienda per le cui fatture può essere utilizzato il mandato."

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_res_config_settings
msgid "Config Settings"
msgstr "Impostazioni di configurazione"

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_res_partner
msgid "Contact"
msgstr "Contatto"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__body
msgid "Contents"
msgstr "Contenuti"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_payment__sdd_mandate_usable
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_payment_register__sdd_mandate_usable
msgid "Could a SDD mandate be used?"
msgstr "È possibile usare un mandato di addebito diretto?"

#. module: account_sepa_direct_debit
#: model_terms:ir.actions.act_window,help:account_sepa_direct_debit.account_sepa_direct_debit_mandate_tree_act
msgid "Create a new direct debit customer mandate"
msgstr "Crea un nuovo mandato di addebito diretto per il cliente"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_account_payment_form
msgid "Create it."
msgstr "Crealo."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__create_uid
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__create_uid
msgid "Created by"
msgstr "Creato da"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__create_date
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__create_date
msgid "Created on"
msgstr "Data creazione"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "Creditor"
msgstr "Creditore"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_res_config_settings__sdd_creditor_identifier
msgid "Creditor Identifier"
msgstr "Identificativo mandato"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_res_config_settings__sdd_creditor_identifier
msgid "Creditor identifier of your company within SEPA scheme."
msgstr "Identificativo creditore dell'azienda all'interno dello schema SEPA."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__partner_id
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__partner_id
msgid "Customer"
msgstr "Cliente"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
msgid "Customer mandate"
msgstr "Mandato del cliente"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__partner_id
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate_send__partner_id
msgid "Customer whose payments are to be managed by this mandate."
msgstr "Cliente i cui pagamenti devono essere gestiti con questo mandato."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "DEBT1234"
msgstr "DEBITO1234"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__start_date
msgid "Date from which the mandate can be used (inclusive)."
msgstr "Data dalla quale può essere utilizzato il mandato (inclusa)."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__end_date
msgid ""
"Date until which the mandate can be used. It will automatically be closed "
"after this date."
msgstr ""
"Data fino alla quale può essere utilizzato il mandato. Dopo questa data "
"viene chiuso in modo automatico."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_account_batch_payment__sdd_required_collection_date
msgid ""
"Date when the company expects to receive the payments of this batch. It "
"can't be inferior to the sending day + the longest pre-notification period "
"defined in the mandates linked to this batch."
msgstr ""
"Data in cui l'azienda si aspetta di ricevere i pagamenti di questo lotto. "
"Non può essere inferiore al giorno di invio + il periodo di pre-notifica più"
" lungo definito nei mandati collegati a questo lotto."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "Debtor"
msgstr "Debitore"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__debtor_id_code
msgid "Debtor Identifier"
msgstr "Identificativo debitore"

#. module: account_sepa_direct_debit
#: model:ir.model.fields.selection,name:account_sepa_direct_debit.selection__account_journal__debit_sepa_pain_version__pain_008_001_02
msgid "Default (Pain 008.001.02)"
msgstr "Predefinito (Pain 008.001.02)"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: account_sepa_direct_debit
#: model:ir.actions.act_window,name:account_sepa_direct_debit.account_sepa_direct_debit_mandate_tree_act
#: model:ir.actions.act_window,name:account_sepa_direct_debit.account_sepa_direct_debit_partner_mandates
#: model:ir.ui.menu,name:account_sepa_direct_debit.account_sepa_direct_debit_customer_mandates_menu
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sdd_res_partner_view
msgid "Direct Debit Mandates"
msgstr "Mandati di addebito diretto"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_account_journal_dashboard_kanban_view
msgid "Direct Debit Payment to Collect"
msgstr "Pagamento con addebito diretto da riscuotere"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__payments_to_collect_nber
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_account_journal_dashboard_kanban_view
msgid "Direct Debit Payments to Collect"
msgstr "Pagamenti con addebito diretto da riscuotere"

#. module: account_sepa_direct_debit
#: model:ir.actions.act_window,name:account_sepa_direct_debit.action_sdd_payments_to_collect
msgid "Direct debit payments to collect"
msgstr "Pagamenti con addebito diretto da incassare"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__display_name
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__display_name
msgid "Display Name"
msgstr "Nome visualizzato"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__checkbox_download
msgid "Download"
msgstr "Scarica"

#. module: account_sepa_direct_debit
#: model:ir.model.fields.selection,name:account_sepa_direct_debit.selection__sdd_mandate__state__draft
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sdd_mandate_search_view
msgid "Draft"
msgstr "Bozza"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__state
msgid ""
"Draft: Validate before use.\n"
"Active: Valid mandates to collect payments.\n"
"Cancelled: Mandates never validated.\n"
"Closed: Expired or manually closed mandates. Previous transactions remain valid.\n"
"Revoked: Fraudulent mandates. Previous invoices might need reimbursement.\n"
msgstr ""
"Bozza: convalida prima dell'uso.\n"
"Attivo: mandati validi per riscuotere i pagamenti.\n"
"Annullato: mandati mai convalidati.\n"
"Chiuso: mandati scaduti o chiusi manualmente. Le transazioni precedenti restano valide.\n"
"Revocato: mandati fraudolenti. Potrebbe essere necessario rimborsare fatture precedenti.\n"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__checkbox_send_mail
msgid "Email"
msgstr "E-mail"

#. module: account_sepa_direct_debit
#: model:mail.template,description:account_sepa_direct_debit.email_template_sdd_new_mandate
msgid "Email requesting the customer to sign the mandate attached"
msgstr "E-mail che chiede la firma del mandato allegato al cliente"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__template_id
msgid "Email template"
msgstr "Modello e-mail"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__end_date
msgid "End Date"
msgstr "Data fine"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "End date"
msgstr "Data fine"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_res_partner__sdd_mandate_ids
#: model:ir.model.fields,help:account_sepa_direct_debit.field_res_users__sdd_mandate_ids
msgid "Every mandate belonging to this partner."
msgstr "Tutti i mandati che appartengono a questo partner."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__expiration_warning_already_sent
msgid "Expiration warning sent"
msgstr "Avviso scadenza inviato"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_follower_ids
msgid "Followers"
msgstr "Seguito da"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguito da (partner)"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icona Font Awesome es. fa-tasks"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__debtor_id_code
msgid "Free reference identifying the debtor in your company."
msgstr "Riferimento libero che identifica il debitore dell'azienda."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid "Go to journal"
msgstr "Vai al registro"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid "Go to mandates"
msgstr "Vai ai mandati"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid "Go to payments"
msgstr "Vai ai pagamenti"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid "Go to settings"
msgstr "Vai alle impostazioni"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_payment_register_form_inherit_account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_account_payment_form
msgid "Good news! A valid SEPA Mandate is available."
msgstr "Buone notizie! È disponibile un mandato SEPA valido."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__has_message
msgid "Has Message"
msgstr "Contiene messaggio"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__partner_bank_id
msgid "IBAN"
msgstr "IBAN"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__id
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__id
msgid "ID"
msgstr "ID"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__activity_exception_icon
msgid "Icon"
msgstr "Icona"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icona per indicare un'attività eccezione."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "Identification code"
msgstr "Codice identificativo"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__name
msgid "Identifier"
msgstr "Identificatore"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Se selezionata, nuovi messaggi richiedono attenzione."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__message_has_error
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Se selezionata, alcuni messaggi presentano un errore di consegna."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/res_company.py:0
msgid "Invalid creditor identifier. Make sure you made no typo."
msgstr ""
"Identificativo creditore non valido, controllare che non ci siano refusi."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/res_company.py:0
msgid "Invalid creditor identifier. Wrong format."
msgstr "Identificativo creditore non valido, formato errato."

#. module: account_sepa_direct_debit
#: model:mail.message.subtype,description:account_sepa_direct_debit.sdd_mt_invoice_paid_with_mandate
msgid "Invoice paid via direct debit."
msgstr "Fattura pagata con addebito diretto."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__paid_invoice_ids
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
msgid "Invoices Paid"
msgstr "Fatture pagate"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.view_account_invoice_filter
msgid "Invoices matching a valid SEPA Direct Debit Mandate"
msgstr ""
"Fatture che corrispondono a un mandato di addebito diretto SEPA valido"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__paid_invoice_ids
msgid "Invoices paid using this mandate."
msgstr "Fatture pagate con questo mandato."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
msgid "Invoices paid with this mandate."
msgstr "Fatture pagate con questo mandato."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__is_mail_template_editor
msgid "Is Editor"
msgstr "È editor"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_is_follower
msgid "Is Follower"
msgstr "Sta seguendo"

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_account_journal
msgid "Journal"
msgstr "Registro"

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_account_move
msgid "Journal Entry"
msgstr "Registrazione contabile"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__lang
msgid "Language"
msgstr "Lingua"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__write_uid
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__write_uid
msgid "Last Updated by"
msgstr "Ultimo aggiornamento di"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__write_date
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__write_date
msgid "Last Updated on"
msgstr "Ultimo aggiornamento il"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_main_attachment_id
msgid "Main Attachment"
msgstr "Allegato principale"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__mandate_id
msgid "Mandate"
msgstr "Mandato"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__mandate_pdf_file
msgid "Mandate Form PDF"
msgstr "Modulo mandato PDF"

#. module: account_sepa_direct_debit
#: model:ir.actions.report,name:account_sepa_direct_debit.sdd_mandate_form_report_main
msgid "Mandate form"
msgstr "Modulo di mandato"

#. module: account_sepa_direct_debit
#: model:ir.model.constraint,message:account_sepa_direct_debit.constraint_sdd_mandate_name_unique
msgid "Mandate identifier must be unique! Please choose another one."
msgstr "L'identificatore del mandato deve essere unico! Scegline un altro."

#. module: account_sepa_direct_debit
#: model:ir.actions.server,name:account_sepa_direct_debit.sdd_mandate_state_cron_ir_actions_server
msgid "Mandate state updater"
msgstr "Aggiornatore stato del mandato"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "Marc Demo"
msgstr "Marc Demo"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_has_error
msgid "Message Delivery error"
msgstr "Errore di consegna messaggio"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_ids
msgid "Messages"
msgstr "Messaggi"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Scadenza mie attività"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Prossimo evento del calendario delle attività"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Scadenza prossima attività"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__activity_summary
msgid "Next Activity Summary"
msgstr "Riepilogo prossima attività"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__activity_type_id
msgid "Next Activity Type"
msgstr "Tipologia prossima attività"

#. module: account_sepa_direct_debit
#: model_terms:ir.actions.act_window,help:account_sepa_direct_debit.action_sdd_payments_to_collect
msgid "No direct debit payment to collect"
msgstr "Nessun pagamento con addebito diretto da riscuotere"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_needaction_counter
msgid "Number of Actions"
msgstr "Numero di azioni"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__payments_to_collect_nber
msgid ""
"Number of Direct Debit payments to be collected for this mandate, that is, "
"the number of payments that have been generated and posted thanks to this "
"mandate and still needs their XML file to be generated and sent to the bank "
"to debit the customer's account."
msgstr ""
"Numero di pagamenti con addebito diretto da riscuotere per questo mandato. È"
" il numero di pagamenti generati e confermati grazie al mandato, il cui file"
" XML deve ancora essere generato e inviato alla banca per l'addebito sul "
"conto del cliente."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_has_error_counter
msgid "Number of errors"
msgstr "Numero di errori"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__paid_invoices_nber
msgid "Number of invoices paid with this mandate."
msgstr "Numero di fatture pagate con questo ordine di pagamento."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Numero di messaggi che richiedono un'azione"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Numero di messaggi con errore di consegna"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "Odoo PVT LTD"
msgstr "Odoo PVT LTD"

#. module: account_sepa_direct_debit
#: model_terms:ir.actions.act_window,help:account_sepa_direct_debit.account_sepa_direct_debit_mandate_tree_act
msgid ""
"Once an invoice is made\n"
"                    in Odoo for a customer having a mandate active on the invoice date,\n"
"                    its validation will trigger its automatic payment, and you will\n"
"                    then only have to generate a SEPA Direct Debit (SDD) XML file containing this operation\n"
"                    and send it to your bank to effectively get paid."
msgstr ""
"Una volta creata una fattura\n"
"                    in Odoo per un cliente avente un ordine di pagamento attivo alla data della fattura,\n"
"                    la convalida ne autorizzerà il pagamento automatico e dovrai\n"
"                    solo creare un file XML per l'addebito diretto SEPA contenente l'operazione\n"
"                    e invialo alla tua banca per ricevere il denaro."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_account_bank_statement_line__sdd_mandate_id
#: model:ir.model.fields,help:account_sepa_direct_debit.field_account_move__sdd_mandate_id
#: model:ir.model.fields,help:account_sepa_direct_debit.field_account_payment__sdd_mandate_id
msgid ""
"Once this invoice has been paid with Direct Debit, contains the mandate that"
" allowed the payment."
msgstr ""
"Dopo che la fattura è stata pagata con addebito diretto, contiene il mandato"
" che ha consentito il pagamento."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__one_off
msgid "One-off Mandate"
msgstr "Mandato unico"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid ""
"Only IBAN account numbers can receive SEPA Direct Debit payments. Please "
"select a journal associated to one or add an IBAN bank account to the "
"current journal"
msgstr ""
"Solo i numeri di conto IBAN possono ricevere pagamenti con addebito diretto "
"SEPA. Seleziona un registro associato a uno di essi o aggiungi un conto "
"bancario con IBAN al registro corrente."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid "Only mandates in draft state can be deleted."
msgstr "È possibile eliminare solo i mandati in stato di bozza."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_payment_register_form_inherit_account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_account_payment_form
msgid "Oops! No valid SEPA mandate for this customer."
msgstr "Oops Nessun mandato SEPA valido per questo cliente."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid "Open customer"
msgstr "Apri cliente"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid "Open customers"
msgstr "Apri clienti"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sdd_res_partner_view
msgid "Open this partner's mandates"
msgstr "Apri il mandato di questo partner"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate_send__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"Lingua di traduzione opzionale (codice ISO) da selezionare quando si invia "
"un'email. Se non impostato, verrà usata la versione inglese. Di solito "
"dovrebbe essere un'espressione segnaposto che fornisce la lingua "
"appropriata, ad esempio {{ object.partner_id.lang }}."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_account_payment_form
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_account_payment_with_mandates_tree
msgid "Originating SEPA mandate"
msgstr "Mandato SEPA iniziale"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid "Paid Invoices"
msgstr "Fatture pagate"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__paid_invoices_nber
msgid "Paid Invoices Number"
msgstr "Numero fatture pagate"

#. module: account_sepa_direct_debit
#: model:mail.message.subtype,name:account_sepa_direct_debit.sdd_mt_invoice_paid_with_mandate
msgid "Paid via direct debit"
msgstr "Pagata con addebito diretto"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/wizard/sdd_mandate_send.py:0
msgid "Partner should have an email address."
msgstr "Il partner deve avere un indirizzo e-mail."

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_account_payment_register
msgid "Pay"
msgstr "Paga"

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_account_payment_method
msgid "Payment Methods"
msgstr "Metodi di pagamento"

#. module: account_sepa_direct_debit
#: model:mail.template,subject:account_sepa_direct_debit.email_template_sdd_pre_notification
msgid "Payment notification {{ object.memo }}"
msgstr "Notifica pagamento {{ object.memo }}"

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_account_payment
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__payment_ids
msgid "Payments"
msgstr "Pagamenti"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
msgid "Payments generated for this mandate that have not yet been collected."
msgstr "Pagamenti generati, ma non ancora riscossi, per questo mandato."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__payment_ids
msgid "Payments generated thanks to this mandate."
msgstr "Pagamenti generati grazie al mandato."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.view_account_move_line_payment_filter
msgid "Payments matching a valid SEPA Direct Debit Mandate"
msgstr ""
"Pagamenti che corrispondono ad un mandato di addebito diretto SEPA valido"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid "Payments to Collect"
msgstr "Pagamenti da riscuotere"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid "Payments without mandate"
msgstr "Pagamenti senza mandato"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_report_invoice_document
msgid ""
"Please do not pay it manually, the payment will be asked to your bank to be processed\n"
"                        automatically."
msgstr ""
"Non effettuare il pagamento manuale, viene richiesta alla banca l'elaborazione\n"
"                        automatica."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__pre_notification_period
msgid "Pre-notification"
msgstr "Pre-notifica"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid "Problematic mandates"
msgstr "Mandati problematici"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__rating_ids
msgid "Ratings"
msgstr "Valutazioni"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__recipient_ids
msgid "Recipients"
msgstr "Destinatari"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__render_model
msgid "Rendering Model"
msgstr "Rendering Model"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_account_batch_payment__sdd_batch_booking
msgid "Request batch booking from the bank for the related bank statements."
msgstr ""
"Richiedi alla banca la registrazione del batch per i relativi estratti "
"conto."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_batch_payment__sdd_required_collection_date
msgid "Required collection date"
msgstr "Data di riscossione richiesta"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__activity_user_id
msgid "Responsible User"
msgstr "Utente responsabile"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
msgid "Revoke"
msgstr "Revoca"

#. module: account_sepa_direct_debit
#: model:ir.model.fields.selection,name:account_sepa_direct_debit.selection__sdd_mandate__state__revoked
msgid "Revoked"
msgstr "Revocato"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_view_account_payment_search
msgid "Revoked SDD Mandate"
msgstr "Mandato SDD revocato"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "Rue de la Loi, 16"
msgstr "Rue de la Loi, 16"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.view_account_payment_search
msgid "SDD B2B"
msgstr "SDD B2B"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_batch_payment__sdd_batch_booking
msgid "SDD Batch Booking"
msgstr "SDD Prenotazione lotti"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.view_account_payment_search
msgid "SDD CORE"
msgstr "SDD CORE"

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_sdd_mandate
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.view_account_move_line_payment_filter
msgid "SDD Mandate"
msgstr "Mandato SDD"

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_sdd_mandate_send
msgid "SDD Mandate Send"
msgstr "Mandato SSD inviato"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_batch_payment__sdd_scheme
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_payment__sdd_mandate_scheme
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__sdd_scheme
msgid "SDD Scheme"
msgstr "Schema SDD"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_res_partner__sdd_count
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_res_users__sdd_count
msgid "SDD count"
msgstr "Numero SDD"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_res_company__sdd_creditor_identifier
msgid "SDD creditor identifier"
msgstr "Identificativo creditore SDD"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid "SDD scheme is set on the customer mandate."
msgstr "Lo schema SDD è impostato sul mandato del cliente."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "SEPA Business-to-Business Direct Debit Mandate"
msgstr "Ordine di pagamento debito diretto tra aziende SEPA"

#. module: account_sepa_direct_debit
#: model:account.payment.method,name:account_sepa_direct_debit.payment_method_sdd
msgid "SEPA Direct Debit"
msgstr "Addebito diretto SEPA"

#. module: account_sepa_direct_debit
#: model:mail.template,name:account_sepa_direct_debit.email_template_sdd_pre_notification
msgid "SEPA Direct Debit Customer Pre-Notification mail"
msgstr "E-mail preavviso addebito diretto SEPA cliente"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "SEPA Direct Debit Mandate"
msgstr "Mandato di addebito diretto SEPA"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_journal__debit_sepa_pain_version
msgid "SEPA Direct Debit Pain Version"
msgstr "Versione Pain addebito diretto SEPA"

#. module: account_sepa_direct_debit
#: model:mail.template,name:account_sepa_direct_debit.email_template_sdd_new_mandate
msgid "SEPA Direct Debit Sending"
msgstr "Invio addebito diretto SEPA"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_res_company__sdd_creditor_identifier
msgid ""
"SEPA Direct Debit creditor identifier of the company, given by the bank."
msgstr ""
"Identificativo creditore per l'addebito diretto SEPA dell'azienda (fornito "
"dalla banca)."

#. module: account_sepa_direct_debit
#: model:mail.template,name:account_sepa_direct_debit.email_template_sdd_mandate_expiring
#: model:mail.template,subject:account_sepa_direct_debit.email_template_sdd_mandate_expiring
msgid "SEPA Direct Debit mandate expiration warning"
msgstr "Avviso scadenza mandato addebito diretto SEPA"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid ""
"SEPA Direct Debit scheme only accepts IBAN account numbers. Please select an"
" IBAN-compliant debtor account for this mandate."
msgstr ""
"Lo schema di addebito diretto SEPA accetta solo numeri di conto IBAN. Per "
"questo mandato selezionare un conto debitore conforme IBAN."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.view_account_journal_form
msgid "SEPA Pain Version"
msgstr "Versione Pain SEPA"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.view_account_journal_form
msgid "SEPA Pain version"
msgstr "Versione Pain SEPA"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_payment_register_form_inherit_account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_account_payment_form
msgid "SEPA Scheme operates in Euro."
msgstr "Schema SEPA opera in Euro."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid "SEPA direct debit stateless customer"
msgstr "Cliente addebito diretto SEPA senza stato"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid ""
"SEPA regulations set the minimum pre-notification period to a minimum of 2 "
"days to allow enough time for the customer to check that their account is "
"adequately funded."
msgstr ""
"Le regole SEPA stabiliscono un periodo minimo di pre-notifica di 2 giorni "
"per consentire al cliente di verificare che il suo conto sia adeguatamente "
"finanziato."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "SEPA-CUST-001"
msgstr "SEPA-CUST-001"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "SEPA1234"
msgstr "SEPA1234"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Errore di consegna SMS"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_bank_statement_line__sdd_has_usable_mandate
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_move__sdd_has_usable_mandate
msgid "Sdd Has Usable Mandate"
msgstr "SDD ha un mandato utilizzabile"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_bank_statement_line__sdd_mandate_id
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_move__sdd_mandate_id
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_payment__sdd_mandate_id
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_res_partner__sdd_mandate_ids
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_res_users__sdd_mandate_ids
msgid "Sdd Mandate"
msgstr "Mandato SDD"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_payment.py:0
msgid "Select a SEPA Direct Debit version before generating the XML."
msgstr ""
"Seleziona una versione di addebito diretto SEPA prima di generare l'XML."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid "Send"
msgstr "Invia"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_send_form
msgid "Send & Print"
msgstr "Invia e stampa"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__is_sent
msgid "Sent to the customer"
msgstr "Inviato al cliente"

#. module: account_sepa_direct_debit
#: model:mail.template,description:account_sepa_direct_debit.email_template_sdd_pre_notification
msgid "Sent to the customer to indicate their account will be charged"
msgstr "E-mail inviata ai clienti per notificare l'addebito"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "Signature"
msgstr "Firma"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid ""
"Some draft payments could not be posted because of the lack of any active "
"mandate."
msgstr ""
"Non è stato possibile confermare alcuni pagamenti in bozza, non è presente "
"alcun mandato attivo."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid "Some payments are linked to an inactive mandate."
msgstr "Alcuni pagamenti sono collegati a mandati non attivi."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid "Some payments are not linked to any mandate."
msgstr "Alcuni pagamenti non sono collegati a nessun mandato."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__start_date
msgid "Start Date"
msgstr "Data inizio"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__state
msgid "State"
msgstr "Provincia"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Stato basato sulle attività\n"
"In ritardo: scadenza già superata\n"
"Oggi: attività in data odierna\n"
"Pianificato: attività future."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__subject
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_send_form
msgid "Subject"
msgstr "Oggetto"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_account_batch_payment__sdd_scheme
#: model:ir.model.fields,help:account_sepa_direct_debit.field_account_payment__sdd_mandate_scheme
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__sdd_scheme
msgid ""
"The B2B scheme is an optional scheme,\n"
"offered exclusively to business payers.\n"
"Some banks/businesses might not accept B2B SDD."
msgstr ""
"Lo schema B2B è opzionale,\n"
"offerto esclusivamente ai pagatori commerciali.\n"
"Alcune banche/attività potrebbero non accettare SDD per B2B."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_payment.py:0
msgid ""
"The SEPA Direct Debit mandate associated to the payment has been revoked and"
" cannot be used anymore."
msgstr ""
"Il mandato di addebito diretto SEPA associato al pagamento è stato revocato "
"e non può più essere utilizzato."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid ""
"The bank needs to be informed at least 5 days in advance for collections "
"related to a new mandate and 2 days in advance when the mandate is already "
"known by them. In this case, the minimum collection date must be the "
"%(date)s"
msgstr ""
"La banca deve essere informata con almeno 5 giorni di anticipo per gli "
"incassi relativi a un nuovo mandato e con 2 giorni di anticipo quando il "
"mandato è già noto. In questo caso, la data minima di incasso deve essere il"
" %(date)s"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/res_company.py:0
msgid "The creditor identifier exceeds the maximum length of 35 characters."
msgstr ""
"Superata la lunghezza massima di 35 caratteri per l'identificativo del "
"creditore."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid "The customer must have a country"
msgstr "Indica un Paese per il cliente"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_payment.py:0
msgid ""
"The debtor and creditor city name is a compulsary information when "
"generating the SDD XML."
msgstr ""
"Il nome della città del debitore e del creditore è un'informazione "
"obbligatoria quando viene creato l'XML SDD."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_payment.py:0
msgid ""
"The debtor and creditor country is a compulsary information when generating "
"the SDD XML."
msgstr ""
"La nazione del debitore e creditore è un'informazione obbligatoria quando "
"viene creato l'XML SDD."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid ""
"The debtor identifier you specified exceeds the limitation of 35 characters "
"imposed by SEPA regulation"
msgstr ""
"L'identificativo specificato per il debitore supera il limite massimo di 35 "
"caratteri imposto dalla norma SEPA."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid ""
"The end date of the mandate must be posterior or equal to its start date."
msgstr ""
"La data di fine del mandato deve essere posteriore o uguale alla data di "
"inizio."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid ""
"The mandate will only be used to pay invoices into the specified time range. If no end date is specified,\n"
"                    you will have to contact us to stop its use. The minimum notification period for creditors\n"
"                    to inform debtors about an upcoming collection is"
msgstr ""
"Il mandato sarà utilizzato solo per pagare le fatture nell'intervallo di tempo specificato. Se non viene indicata nessuna data di fine,\n"
"                    dovrai contattarci per bloccarlo. Il periodo minimo di notifica per i creditori\n"
"                    al fine di informare i debitori sull'incasso imminente è il"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__pre_notification_period
msgid ""
"The minimum notice period in days, used to inform the customer prior to "
"collection."
msgstr ""
"Il periodo minimo di preavviso in giorni, utilizzato per informare il "
"cliente prima della riscossione."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_payment.py:0
msgid ""
"The payment must be linked to a SEPA Direct Debit mandate in order to "
"generate a Direct Debit XML."
msgstr ""
"Per generare un XML di addebito diretto, il pagamento deve essere collegato "
"a un mandato SEPA."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__name
msgid "The unique identifier of this mandate."
msgstr "Identificativo univoco del mandato."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_report_invoice_document
msgid ""
"This invoice will be paid using direct debit and is only\n"
"                        sent for informative purposes."
msgstr ""
"Questa fattura verrà pagata con addebito diretto e viene\n"
"                        inviata solo a scopo informativo."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid ""
"This mandate is only intended for business-to-business transactions. You are"
" not entitled to a refund from your bank after your account has been "
"debited, but you are entitled to request your bank not to debit your account"
" up until the day on which the payment is due."
msgstr ""
"Questo ordine di pagamento è rivolto esclusivamente alle transazioni fra "
"aziende. Non hai diritto ad un rimborso una volta che l'importo è stato "
"addebitato sul tuo conto ma hai il diritto di chiedere alla tua banca di non"
" effettuare ulteriori addebiti fino al giorno in cui bisogna saldare il "
"pagamento."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid ""
"To solve that, you should create a mandate for each of the involved "
"customers, valid at the moment of the payment date."
msgstr ""
"Per risolvere, deve essere creato un mandato per ciascuno dei clienti "
"coinvolti, valido al momento della data di pagamento."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__one_off
msgid ""
"True if and only if this mandate can be used for only one transaction. It "
"will automatically go from 'active' to 'closed' after its first use in "
"payment if this option is set.\n"
msgstr ""
"Vero se e solo se questo mandato può essere utilizzato per una sola "
"operazione. Se l'opzione è impostata, passa in modo automatico da \"Attivo\""
" a \"Chiuso\" dopo il primo utilizzo nel pagamento.\n"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_payment.py:0
msgid ""
"Trying to generate a Direct Debit XML file containing payments from another "
"company than that file's creditor."
msgstr ""
"Tentativo di generare un file XML di addebito diretto che contiene pagamenti"
" da un'azienda diversa da quella del creditore del file."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_payment.py:0
msgid ""
"Trying to generate a Direct Debit XML for payments coming from another "
"payment method than SEPA Direct Debit."
msgstr ""
"Tentativo di generare un file XML di addebito diretto per pagamenti "
"provenienti da metodi diversi da quelli SEPA."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_payment.py:0
msgid ""
"Trying to register a payment on a mandate belonging to a different partner."
msgstr ""
"Tentativo di registrare un pagamento per un mandato che appartiene a un "
"altro partner."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipo di attività eccezione sul record."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid "Under B2B SDD Scheme, the customer must be a company."
msgstr "Se si usa lo schema SDD B2B, il cliente deve essere un'azienda."

#. module: account_sepa_direct_debit
#: model:ir.model.fields.selection,name:account_sepa_direct_debit.selection__account_journal__debit_sepa_pain_version__pain_008_001_08
msgid "Updated 2023 (Pain 008.001.08)"
msgstr "Aggiornato 2023 (Pain 008.001.08)"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
msgid "Validate"
msgstr "Convalida"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "Validity"
msgstr "Validità"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/wizard/sdd_mandate_send.py:0
msgid "View Partner(s)"
msgstr "Mostra partner"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__warnings
msgid "Warnings"
msgstr "Avvisi"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__website_message_ids
msgid "Website Messages"
msgstr "Messaggi sito web"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__website_message_ids
msgid "Website communication history"
msgstr "Cronologia comunicazioni sito web"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/wizard/account_payment_register.py:0
msgid ""
"You can't pay any of the selected invoices using the SEPA Direct Debit "
"method, as no valid mandate is available"
msgstr ""
"Non è possibile pagare nessuna delle fatture selezionate utilizzando il "
"metodo di addebito diretto SEPA perché non è disponibile nessun mandato "
"valido"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/res_partner_bank.py:0
msgid ""
"You cannot delete a bank account linked to an active SEPA Direct Debit "
"mandate."
msgstr ""
"Impossibile eliminare un conto bancario collegato a un mandato di addebito "
"diretto SEPA attivo."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid ""
"You cannot generate a SEPA Direct Debit file with a required collection date inferior to the sending day + the longest pre-notification period defined in the mandates linked to this batch.\n"
"According to these payments mandates, the minimum required date should be the %(minimum_date)s"
msgstr ""
"Non è possibile generare un file di addebito diretto SEPA con una data di incasso richiesta inferiore al giorno di invio + il periodo di pre-notifica più lungo definito nei mandati collegati a questo lotto.\n"
"Secondo i mandati di pagamento, la data minima richiesta deve corrispondere al %(minimum_date)s"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid ""
"Your company must have a creditor identifier in order to issue SEPA Direct "
"Debit payments requests. It can be defined in accounting module's settings."
msgstr ""
"Per emettere richieste di pagamento con addebito diretto SEPA, l'azienda "
"deve possedere un identificativo creditore. Può essere definito nelle "
"impostazioni del modulo di contabilità."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid ""
"day(s).\n"
"                    It will be sent to the debtor's email."
msgstr ""
"giorno/i.\n"
"                    Verrà inviato all'e-mail del debitore."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid ""
"to send instructions to your bank to debit your account and (B) your bank to"
" debit your account in accordance with the instructions from"
msgstr ""
"l'invio di istruzioni alla tua banca per addebitare il tuo conto e (B) la "
"tua banca ad addebitare il tuo conto secondo le istruzioni di"

#. module: account_sepa_direct_debit
#: model:mail.template,subject:account_sepa_direct_debit.email_template_sdd_new_mandate
msgid "{{ object.partner_id.display_name }} SEPA Direct Debit Mandate"
msgstr "{{ object.partner_id.display_name }} Mandato addebito diretto SEPA"
