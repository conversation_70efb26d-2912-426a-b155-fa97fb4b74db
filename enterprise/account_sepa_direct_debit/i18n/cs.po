# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_sepa_direct_debit
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:52+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Czech (https://app.transifex.com/odoo/teams/41243/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n <= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "(if applicable)"
msgstr "(případně)"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "(optional)"
msgstr "(volitelný)"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "*********"
msgstr "*********"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "2023-08-10"
msgstr "10. 8. 2023"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "2023-09-10"
msgstr "10. 9. 2023"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "380055"
msgstr "380055"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "*************"
msgstr "*************"

#. module: account_sepa_direct_debit
#: model:mail.template,body_html:account_sepa_direct_debit.email_template_sdd_new_mandate
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Dear <t t-out=\"object.partner_id.display_name or ''\">Azure Interior</t>\n"
"                        <br/>\n"
"                        <br/>\n"
"                        Here is your SEPA Direct Debit Mandate to sign to authorize\n"
"                        <t t-out=\"object.company_id.display_name or ''\">YourCompany</t>\n"
"                        to send instructions to your bank to debit your account in accordance with the instructions from\n"
"                        <t t-out=\"object.company_id.display_name or ''\">YourCompany</t>\n"
"                        Do not hesitate to contact us if you have any questions.\n"
"                        <br/>\n"
"                        Best regards,\n"
"                        <t t-if=\"not is_html_empty(user.signature)\" data-o-mail-quote-container=\"1\">\n"
"                            <br/><br/>\n"
"                            <t t-out=\"user.signature\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"                        </t>\n"
"                    </p>\n"
"                </div>\n"
"            "
msgstr ""

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
msgid "<span> day(s)</span>"
msgstr ""

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>Address:</strong>"
msgstr "<strong>Adresa:</strong>"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>City: </strong>"
msgstr "<strong>Město: </strong>"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>Country: </strong>"
msgstr "<strong>Země: </strong>"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>Creditor identifier:</strong>"
msgstr "<strong>Identifikátor věřitele:</strong>"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid ""
"<strong>Date and place of signature:</strong> "
"......................................"
msgstr ""
"<strong>Datum a místo podpisu:</strong> "
"......................................"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>Email:</strong>"
msgstr "<strong>E-mail:</strong>"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>IBAN:</strong>"
msgstr "<strong>IBAN:</strong>"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>Mandate identifier:</strong>"
msgstr "<strong>Identifikátor mandátu:</strong>"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid ""
"<strong>Name of the reference party:</strong> "
"......................................"
msgstr ""
"<strong>Název referenční strany:</strong> "
"......................................"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>Phone:</strong>"
msgstr "<strong>Telefon:</strong>"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>Signature:</strong>"
msgstr "<strong>Podpis:</strong>"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>Start date:</strong>"
msgstr "<strong>Počáteční datum:</strong>"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>Transaction type:</strong> recurrent"
msgstr "<strong>Typ transakce:</strong> opakující se"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>Zip: </strong>"
msgstr "<strong>PSČ: </strong>"

#. module: account_sepa_direct_debit
#: model:mail.template,body_html:account_sepa_direct_debit.email_template_sdd_pre_notification
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <t t-set=\"company\" t-value=\"object.company_id\"/>\n"
"                    <span style=\"font-size: 10px;\">Your Direct Debit Payment Notification</span><br/>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"right\" t-if=\"not company.uses_default_logo\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ company.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"company.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                    <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    Dear <t t-out=\"object.partner_id.name or ''\">Azure Interior</t><br/>\n"
"                    <br/>\n"
"                    A Direct Debit payment request amounting to\n"
"                    <span style=\"font-weight:bold;\" t-out=\"format_amount(object.amount, object.currency_id) or ''\">$ 0.00</span>\n"
"                    will be sent to your bank.<br/>\n"
"                    Your account ending with <t t-out=\"ctx.get('iban_last_4') or ''\">1234</t> will be automatically debited on the\n"
"                    <span style=\"font-weight:bold;\" t-out=\"format_date(ctx.get('collection_date'))\">2020-04-18</span>,\n"
"                    or shortly thereafter.<br/>\n"
"                    please make sure you have the requested funds.<br/>\n"
"                    <br/>\n"
"                    <t t-if=\"ctx.get('creditor_iban') or ctx.get('mandate_ref')\">\n"
"                        Merchant data:<br/>\n"
"                        <ul>\n"
"                            <t t-if=\"ctx.get('creditor_iban')\">\n"
"                                <li>IBAN: <t t-out=\"ctx['creditor_iban'] or ''\">NO 93 8601 1117947</t></li>\n"
"                            </t>\n"
"                            <t t-if=\"ctx.get('mandate_ref')\">\n"
"                                <li>SEPA DIRECT DEBIT MANDATE REFERENCE: <t t-out=\"ctx['mandate_ref'] or ''\"/></li>\n"
"                            </t>\n"
"                        </ul>\n"
"                    </t>\n"
"                    <br/>\n"
"                    Do not hesitate to contact us if you have any question.\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"company.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-if=\"company.phone\">\n"
"                        <t t-out=\"company.phone or ''\">******-123-4567</t> |\n"
"                    </t>\n"
"                    <t t-if=\"company.email\">\n"
"                        <a t-attf-href=\"'mailto:%s' % {{ company.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"company.email or ''\"><EMAIL></a> |\n"
"                    </t>\n"
"                    <t t-if=\"company.website\">\n"
"                        <a t-attf-href=\"'%s' % {{ company.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"company.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=mail\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""

#. module: account_sepa_direct_debit
#: model:mail.template,body_html:account_sepa_direct_debit.email_template_sdd_mandate_expiring
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #FFFFFF; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\">\n"
"                    <tr>\n"
"                        <td align=\"center\">\n"
"                            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: #FFFFFF; color: #454748; border-collapse:separate;\">\n"
"                                <tbody>\n"
"                                    <!-- CONTENT -->\n"
"                                    <tr>\n"
"                                        <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                                <tr>\n"
"                                                    <td valign=\"top\" style=\"font-size: 13px;\">\n"
"                                                        <div>\n"
"                                                            Hello,<br/><br/>\n"
"                                                            A SEPA Direct Debit mandate will reach its automatic expiration date on <span t-out=\"format_date(object._get_expiry_date_per_mandate()[object])\">2020-04-18</span><br/>\n"
"                                                            This can be caused by one of the following reason:\n"
"                                                            <ul>\n"
"                                                                <li>\n"
"                                                                    That date is the mandate end date agreed upon signature.\n"
"                                                                </li>\n"
"                                                                <li>\n"
"                                                                    It would then be more than 36 months since the last time this mandate was used.\n"
"                                                                </li>\n"
"                                                            </ul>\n"
"                                                            <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                                                                <a t-attf-href=\"{{ website_url }}/odoo/sdd-mandates/{{ object.id }}\" style=\"background-color: #4caf50; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                                                    Go to Mandate\n"
"                                                                </a>\n"
"                                                            </div>\n"
"                                                            Security Tip: Check that the domain name you are redirected to is: <b><a t-att-href=\"object.get_base_url()\" t-out=\"object.get_base_url() or ''\">\n"
"                                                            https://yourcompany.odoo.com</a></b>\n"
"                                                        </div>\n"
"                                                    </td>\n"
"                                                </tr>\n"
"                                                <tr>\n"
"                                                    <td style=\"text-align:center;\">\n"
"                                                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                                    </td>\n"
"                                                </tr>\n"
"                                            </table>\n"
"                                        </td>\n"
"                                    </tr>\n"
"                                </tbody>\n"
"                            </table>\n"
"                        </td>\n"
"                    </tr>\n"
"                    <!-- POWERED BY -->\n"
"                    <tr>\n"
"                        <td align=\"center\" style=\"min-width: 590px;\">\n"
"                            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"                                <tr>\n"
"                                    <td style=\"text-align: center; font-size: 13px;\">\n"
"                                        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=auth\" style=\"color: #875A7B;\">Odoo</a>\n"
"                                    </td>\n"
"                                </tr>\n"
"                            </table>\n"
"                            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"                                <tr>\n"
"                                    <td style=\"text-align: center; font-size: 11px;\">\n"
"                                        PS: This is an automated email sent by Odoo Accounting to notify you a SEPA Direct Debit mandate is going to get automatically closed.\n"
"                                    </td>\n"
"                                </tr>\n"
"                            </table>\n"
"                        </td>\n"
"                    </tr>\n"
"                </table>\n"
"            "
msgstr ""

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_payment.py:0
msgid ""
"A SEPA direct debit version should be selected to generate the addresses in "
"the export file."
msgstr ""
"Pro generování adres v exportním souboru byste měli zvolit verzi SEPA "
"inkasa."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_payment.py:0
msgid ""
"A SEPA direct debit version should be selected to generate the export file."
msgstr ""
"Pro generování exportního souboru byste měli zvolit verzi SEPA inkasa."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid ""
"A customer account is required to validate a SEPA Direct Debit mandate."
msgstr ""

#. module: account_sepa_direct_debit
#: model_terms:ir.actions.act_window,help:account_sepa_direct_debit.account_sepa_direct_debit_mandate_tree_act
msgid ""
"A mandate represents the authorization you receive from a customer\n"
"                    to automatically collect money on her account."
msgstr ""
"Mandát představuje oprávnění, které obdržíte od zákazníka\n"
"                   automaticky shromažďovat peníze na jejím účtu."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__partner_bank_id
msgid "Account of the customer to collect payments from."
msgstr "Účet zákazníka, od kterého se mají vybírat platby."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_needaction
msgid "Action Needed"
msgstr "Vyžadována akce"

#. module: account_sepa_direct_debit
#: model:ir.model.fields.selection,name:account_sepa_direct_debit.selection__sdd_mandate__state__active
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sdd_mandate_search_view
msgid "Active"
msgstr "Aktivní"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__activity_ids
msgid "Activities"
msgstr "Aktivity"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Typ výjimečné aktivity"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__activity_state
msgid "Activity State"
msgstr "Stav aktivity"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ikona typu aktivity"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid "All the payments in the batch must have the same SDD scheme."
msgstr "Všechny platby v dávce musí mít stejné schéma SDD."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "Antwerp"
msgstr "Antwerpy"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid ""
"As part of your rights, you are entitled to a refund from your bank under "
"the terms and conditions of your agreement with your bank. Your rights are "
"explained in a statement that you can obtain from your bank. A refund must "
"be claimed within 8 weeks starting from the date on which your account was "
"debited."
msgstr ""
"V rámci svých práv máte nárok na vrácení peněz od své banky podle podmínek "
"smlouvy s bankou. Vaše práva jsou vysvětlena ve výpisu, který můžete získat "
"ve své bance. O vrácení peněz je třeba požádat do 8 týdnů ode dne, kdy vám "
"byla částka odepsána z účtu."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_attachment_count
msgid "Attachment Count"
msgstr "Počet příloh"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__author_id
msgid "Author"
msgstr "Autor"

#. module: account_sepa_direct_debit
#: model:ir.model.fields.selection,name:account_sepa_direct_debit.selection__account_batch_payment__sdd_scheme__b2b
#: model:ir.model.fields.selection,name:account_sepa_direct_debit.selection__sdd_mandate__sdd_scheme__b2b
msgid "B2B"
msgstr "B2B"

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_res_partner_bank
msgid "Bank Accounts"
msgstr "Bankovní účty"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.view_batch_payment_form_inherit
msgid "Batch Booking"
msgstr "Dávková rezervace"

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_account_batch_payment
msgid "Batch Payment"
msgstr "Hromadná platba"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "Belgium"
msgstr "Belgie"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__body_has_template_value
msgid "Body content is the same as the template"
msgstr "Tělo obsahu se shoduje se šablonou"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "Building C"
msgstr "Budova C"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "By signing this mandate form, you authorise (A)"
msgstr "Podepsáním tohoto formuláře zmocnění udělujete oprávnění (A)"

#. module: account_sepa_direct_debit
#: model:ir.model.fields.selection,name:account_sepa_direct_debit.selection__account_batch_payment__sdd_scheme__core
#: model:ir.model.fields.selection,name:account_sepa_direct_debit.selection__sdd_mandate__sdd_scheme__core
msgid "CORE"
msgstr "CORE"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "CREDIT-1234"
msgstr "CREDIT-1234"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__can_edit_body
msgid "Can Edit Body"
msgstr "Může upravovat tělo"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_send_form
msgid "Cancel"
msgstr "Zrušit"

#. module: account_sepa_direct_debit
#: model:ir.model.fields.selection,name:account_sepa_direct_debit.selection__sdd_mandate__state__cancelled
msgid "Cancelled"
msgstr "Zrušeno"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/wizard/sdd_mandate_send.py:0
msgid "Check Partner(s) Email(s)"
msgstr ""

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
msgid "Close"
msgstr "Zavřít"

#. module: account_sepa_direct_debit
#: model:ir.model.fields.selection,name:account_sepa_direct_debit.selection__sdd_mandate__state__closed
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sdd_mandate_search_view
msgid "Closed"
msgstr "Uzavřeno"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
msgid "Collections"
msgstr "Výběry"

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_res_company
msgid "Companies"
msgstr "Společnosti"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__company_id
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__company_id
msgid "Company"
msgstr "Společnost"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__company_id
msgid "Company for whose invoices the mandate can be used."
msgstr "Společnost, na jejíž faktury může být mandát použit."

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurační nastavení"

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__body
msgid "Contents"
msgstr "Obsah"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_payment__sdd_mandate_usable
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_payment_register__sdd_mandate_usable
msgid "Could a SDD mandate be used?"
msgstr "Lze použít mandát SDD?"

#. module: account_sepa_direct_debit
#: model_terms:ir.actions.act_window,help:account_sepa_direct_debit.account_sepa_direct_debit_mandate_tree_act
msgid "Create a new direct debit customer mandate"
msgstr "Vytvořte nový mandát pro inkaso"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_account_payment_form
msgid "Create it."
msgstr "Vytvořit."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__create_uid
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__create_uid
msgid "Created by"
msgstr "Vytvořeno uživatelem"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__create_date
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__create_date
msgid "Created on"
msgstr "Vytvořeno dne"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "Creditor"
msgstr "Věřitel"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_res_config_settings__sdd_creditor_identifier
msgid "Creditor Identifier"
msgstr "Identifikátor věřitele"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_res_config_settings__sdd_creditor_identifier
msgid "Creditor identifier of your company within SEPA scheme."
msgstr "Identifikátor věřitele vaší společnosti v rámci systému SEPA."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__partner_id
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__partner_id
msgid "Customer"
msgstr "Zákazník"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
msgid "Customer mandate"
msgstr "Mandát zákazníka"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__partner_id
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate_send__partner_id
msgid "Customer whose payments are to be managed by this mandate."
msgstr "Zákazník, jehož platby mají být spravovány tímto mandátem."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "DEBT1234"
msgstr "DEBT1234"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__start_date
msgid "Date from which the mandate can be used (inclusive)."
msgstr "Datum, od kterého lze mandát použít (včetně)."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__end_date
msgid ""
"Date until which the mandate can be used. It will automatically be closed "
"after this date."
msgstr ""
"Datum, kdy může být mandát použit. Po tomto datu bude automaticky uzavřen."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_account_batch_payment__sdd_required_collection_date
msgid ""
"Date when the company expects to receive the payments of this batch. It "
"can't be inferior to the sending day + the longest pre-notification period "
"defined in the mandates linked to this batch."
msgstr ""

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "Debtor"
msgstr "Dlužník"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__debtor_id_code
msgid "Debtor Identifier"
msgstr "Identifikátor dlužníka"

#. module: account_sepa_direct_debit
#: model:ir.model.fields.selection,name:account_sepa_direct_debit.selection__account_journal__debit_sepa_pain_version__pain_008_001_02
msgid "Default (Pain 008.001.02)"
msgstr "Výchozí (Pain 008.001.02)"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: account_sepa_direct_debit
#: model:ir.actions.act_window,name:account_sepa_direct_debit.account_sepa_direct_debit_mandate_tree_act
#: model:ir.actions.act_window,name:account_sepa_direct_debit.account_sepa_direct_debit_partner_mandates
#: model:ir.ui.menu,name:account_sepa_direct_debit.account_sepa_direct_debit_customer_mandates_menu
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sdd_res_partner_view
msgid "Direct Debit Mandates"
msgstr "Příkazy k inkasu"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_account_journal_dashboard_kanban_view
msgid "Direct Debit Payment to Collect"
msgstr "Platba inkasem k inkasu"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__payments_to_collect_nber
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_account_journal_dashboard_kanban_view
msgid "Direct Debit Payments to Collect"
msgstr "Platby inkasem k inkasu"

#. module: account_sepa_direct_debit
#: model:ir.actions.act_window,name:account_sepa_direct_debit.action_sdd_payments_to_collect
msgid "Direct debit payments to collect"
msgstr "Inkaso inkasních plateb"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__display_name
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__display_name
msgid "Display Name"
msgstr "Zobrazovací název"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__checkbox_download
msgid "Download"
msgstr "Stáhnout"

#. module: account_sepa_direct_debit
#: model:ir.model.fields.selection,name:account_sepa_direct_debit.selection__sdd_mandate__state__draft
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sdd_mandate_search_view
msgid "Draft"
msgstr "Návrh"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__state
msgid ""
"Draft: Validate before use.\n"
"Active: Valid mandates to collect payments.\n"
"Cancelled: Mandates never validated.\n"
"Closed: Expired or manually closed mandates. Previous transactions remain valid.\n"
"Revoked: Fraudulent mandates. Previous invoices might need reimbursement.\n"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__checkbox_send_mail
msgid "Email"
msgstr "Email "

#. module: account_sepa_direct_debit
#: model:mail.template,description:account_sepa_direct_debit.email_template_sdd_new_mandate
msgid "Email requesting the customer to sign the mandate attached"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__template_id
msgid "Email template"
msgstr "Emailová šablona"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__end_date
msgid "End Date"
msgstr "Datum do"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "End date"
msgstr "Konečné datum"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_res_partner__sdd_mandate_ids
#: model:ir.model.fields,help:account_sepa_direct_debit.field_res_users__sdd_mandate_ids
msgid "Every mandate belonging to this partner."
msgstr "Každý příkaz tohoto partnera."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__expiration_warning_already_sent
msgid "Expiration warning sent"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_follower_ids
msgid "Followers"
msgstr "Odběratelé"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_partner_ids
msgid "Followers (Partners)"
msgstr "Odběratelé (partneři)"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Ikona v rámci awesome font, např. fa-tasks"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__debtor_id_code
msgid "Free reference identifying the debtor in your company."
msgstr "Bezplatná reference k identifikaci dlužníka ve vaší společnosti."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid "Go to journal"
msgstr ""

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid "Go to mandates"
msgstr ""

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid "Go to payments"
msgstr ""

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid "Go to settings"
msgstr ""

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_payment_register_form_inherit_account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_account_payment_form
msgid "Good news! A valid SEPA Mandate is available."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__has_message
msgid "Has Message"
msgstr "Má zprávu"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__partner_bank_id
msgid "IBAN"
msgstr "IBAN"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__id
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__id
msgid "ID"
msgstr "ID"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__activity_exception_icon
msgid "Icon"
msgstr "Ikona"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikona, která označuje výjimečnou aktivitu."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "Identification code"
msgstr "Identifikační kód"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__name
msgid "Identifier"
msgstr "Identifikátor"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Pokud zaškrtnuto, nové zprávy vyžadují vaši pozornost."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__message_has_error
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Pokud zaškrtnuto, některé zprávy mají chybu při doručení."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/res_company.py:0
msgid "Invalid creditor identifier. Make sure you made no typo."
msgstr ""
"Neplatný identifikátor věřitele. Ujistěte se, že jste neudělali žádnou "
"chybu."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/res_company.py:0
msgid "Invalid creditor identifier. Wrong format."
msgstr "Neplatný identifikátor věřitele. Špatný formát."

#. module: account_sepa_direct_debit
#: model:mail.message.subtype,description:account_sepa_direct_debit.sdd_mt_invoice_paid_with_mandate
msgid "Invoice paid via direct debit."
msgstr "Faktura zaplacená prostřednictvím inkasa."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__paid_invoice_ids
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
msgid "Invoices Paid"
msgstr "Faktury placené"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.view_account_invoice_filter
msgid "Invoices matching a valid SEPA Direct Debit Mandate"
msgstr "Faktury odpovídající platnému pověření SEPA inkasa"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__paid_invoice_ids
msgid "Invoices paid using this mandate."
msgstr "Faktury placené pomocí tohoto pověření."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
msgid "Invoices paid with this mandate."
msgstr "Faktury placené tímto mandátem."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__is_mail_template_editor
msgid "Is Editor"
msgstr "Je editor"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_is_follower
msgid "Is Follower"
msgstr "Je odběratel"

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_account_journal
msgid "Journal"
msgstr "Účetní deník"

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_account_move
msgid "Journal Entry"
msgstr "Účetní záznam"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__lang
msgid "Language"
msgstr "Jazyk"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__write_uid
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__write_uid
msgid "Last Updated by"
msgstr "Naposledy upraveno uživatelem"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__write_date
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__write_date
msgid "Last Updated on"
msgstr "Naposledy upraveno dne"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_main_attachment_id
msgid "Main Attachment"
msgstr "Hlavní příloha"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__mandate_id
msgid "Mandate"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__mandate_pdf_file
msgid "Mandate Form PDF"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.actions.report,name:account_sepa_direct_debit.sdd_mandate_form_report_main
msgid "Mandate form"
msgstr "Mandátový formulář"

#. module: account_sepa_direct_debit
#: model:ir.model.constraint,message:account_sepa_direct_debit.constraint_sdd_mandate_name_unique
msgid "Mandate identifier must be unique! Please choose another one."
msgstr "Identifikátor zmocnění musí být unikátní. Prosím zvolte jiný."

#. module: account_sepa_direct_debit
#: model:ir.actions.server,name:account_sepa_direct_debit.sdd_mandate_state_cron_ir_actions_server
msgid "Mandate state updater"
msgstr "Aktualizátor stavu mandátu"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "Marc Demo"
msgstr "Marc Demo"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_has_error
msgid "Message Delivery error"
msgstr "Chyba při doručování zprávy"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_ids
msgid "Messages"
msgstr "Zprávy"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Termín mé aktivity"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Další aktivita z kalendáře"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Termín další aktivity"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__activity_summary
msgid "Next Activity Summary"
msgstr "Popis další aktivity"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__activity_type_id
msgid "Next Activity Type"
msgstr "Typ další aktivity"

#. module: account_sepa_direct_debit
#: model_terms:ir.actions.act_window,help:account_sepa_direct_debit.action_sdd_payments_to_collect
msgid "No direct debit payment to collect"
msgstr "Žádná inkasní platba k inkasu"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_needaction_counter
msgid "Number of Actions"
msgstr "Počet akcí"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__payments_to_collect_nber
msgid ""
"Number of Direct Debit payments to be collected for this mandate, that is, "
"the number of payments that have been generated and posted thanks to this "
"mandate and still needs their XML file to be generated and sent to the bank "
"to debit the customer's account."
msgstr ""
"Počet plateb přímým debetem, které mají být shromážděny pro tento mandát, "
"tj. Počet plateb, které byly vygenerovány a zaúčtovány díky tomuto mandátu a"
" stále je třeba vygenerovat a odeslat jejich soubor XML a odeslat do banky k"
" odepsání z účtu zákazníka."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_has_error_counter
msgid "Number of errors"
msgstr "Počet chyb"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__paid_invoices_nber
msgid "Number of invoices paid with this mandate."
msgstr "Počet faktur uhrazených tímto svolením."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Počet zpráv vyžadujících akci"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Počet zpráv s chybou při doručení"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "Odoo PVT LTD"
msgstr "Odoo PVT LTD"

#. module: account_sepa_direct_debit
#: model_terms:ir.actions.act_window,help:account_sepa_direct_debit.account_sepa_direct_debit_mandate_tree_act
msgid ""
"Once an invoice is made\n"
"                    in Odoo for a customer having a mandate active on the invoice date,\n"
"                    its validation will trigger its automatic payment, and you will\n"
"                    then only have to generate a SEPA Direct Debit (SDD) XML file containing this operation\n"
"                    and send it to your bank to effectively get paid."
msgstr ""
"Jakmile je v Odoo pro zákazníka vytvořena faktura,\n"
"                    který má aktivní pověření k inkasu ke dni faktury,\n"
"                    její zaúčtování spustí automatickou úhradu, a vy následně\n"
"                    musíte vytvořit SEPA Příkaz k inkasu (SDD) XML soubor, obsahující příslušnou operaci,\n"
"                    a poslat jej do banky, abyste skutečně dostali zaplaceno."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_account_bank_statement_line__sdd_mandate_id
#: model:ir.model.fields,help:account_sepa_direct_debit.field_account_move__sdd_mandate_id
#: model:ir.model.fields,help:account_sepa_direct_debit.field_account_payment__sdd_mandate_id
msgid ""
"Once this invoice has been paid with Direct Debit, contains the mandate that"
" allowed the payment."
msgstr ""
"Jakmile byla tato faktura zaplacena inkasem, obsahuje mandát umožňující "
"platbu."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__one_off
msgid "One-off Mandate"
msgstr "Jednorázový mandát"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid ""
"Only IBAN account numbers can receive SEPA Direct Debit payments. Please "
"select a journal associated to one or add an IBAN bank account to the "
"current journal"
msgstr ""

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid "Only mandates in draft state can be deleted."
msgstr ""

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_payment_register_form_inherit_account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_account_payment_form
msgid "Oops! No valid SEPA mandate for this customer."
msgstr ""
"Jejda! Pro tohoto zákazníka neexistuje ověřené pověření k SEPA inkasu."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid "Open customer"
msgstr ""

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid "Open customers"
msgstr ""

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sdd_res_partner_view
msgid "Open this partner's mandates"
msgstr "Otevřít mandáty tohoto partnera"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate_send__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"Volitelný překladový jazyk (kód ISO), který lze vybrat při odesílání "
"e-mailu. Pokud není nastavena, bude použita anglická verze. To by měl být "
"obvykle zástupný výraz, který poskytuje vhodný jazyk, např. "
"{{object.partner_id.lang}}."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_account_payment_form
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_account_payment_with_mandates_tree
msgid "Originating SEPA mandate"
msgstr "Původní mandát SEPA"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid "Paid Invoices"
msgstr "Zaplacené faktury"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__paid_invoices_nber
msgid "Paid Invoices Number"
msgstr "Číslo placených faktur"

#. module: account_sepa_direct_debit
#: model:mail.message.subtype,name:account_sepa_direct_debit.sdd_mt_invoice_paid_with_mandate
msgid "Paid via direct debit"
msgstr "Placené prostřednictvím inkasa"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/wizard/sdd_mandate_send.py:0
msgid "Partner should have an email address."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_account_payment_register
msgid "Pay"
msgstr "Zaplatit"

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_account_payment_method
msgid "Payment Methods"
msgstr "Platební metody"

#. module: account_sepa_direct_debit
#: model:mail.template,subject:account_sepa_direct_debit.email_template_sdd_pre_notification
msgid "Payment notification {{ object.memo }}"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_account_payment
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__payment_ids
msgid "Payments"
msgstr "Platby"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
msgid "Payments generated for this mandate that have not yet been collected."
msgstr "Platby generované za tento mandát, které dosud nebyly vybrány."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__payment_ids
msgid "Payments generated thanks to this mandate."
msgstr "Platby generované díky tomuto mandátu."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.view_account_move_line_payment_filter
msgid "Payments matching a valid SEPA Direct Debit Mandate"
msgstr ""

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid "Payments to Collect"
msgstr "Platby k inkasu"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid "Payments without mandate"
msgstr ""

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_report_invoice_document
msgid ""
"Please do not pay it manually, the payment will be asked to your bank to be processed\n"
"                        automatically."
msgstr ""
"Neplaťte to prosím ručně, platba bude odeslána ke zpracování vaší bance\n"
"                       automaticky."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__pre_notification_period
msgid "Pre-notification"
msgstr ""

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid "Problematic mandates"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__rating_ids
msgid "Ratings"
msgstr "Hodnocení"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__recipient_ids
msgid "Recipients"
msgstr "Příjemci"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__render_model
msgid "Rendering Model"
msgstr "Vykreslování modelu"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_account_batch_payment__sdd_batch_booking
msgid "Request batch booking from the bank for the related bank statements."
msgstr ""
"Vyžádejte si od banky dávkovou rezervaci souvisejících bankovních výpisů."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_batch_payment__sdd_required_collection_date
msgid "Required collection date"
msgstr "Požadované datum vyzvednutí"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__activity_user_id
msgid "Responsible User"
msgstr "Zodpovědný uživatel"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
msgid "Revoke"
msgstr "Zrušit"

#. module: account_sepa_direct_debit
#: model:ir.model.fields.selection,name:account_sepa_direct_debit.selection__sdd_mandate__state__revoked
msgid "Revoked"
msgstr "Zrušeno"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_view_account_payment_search
msgid "Revoked SDD Mandate"
msgstr "Zrušený mandát SDD"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "Rue de la Loi, 16"
msgstr "Rue de la Loi, 16"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.view_account_payment_search
msgid "SDD B2B"
msgstr "SDD B2B"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_batch_payment__sdd_batch_booking
msgid "SDD Batch Booking"
msgstr "Dávková rezervace SDD"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.view_account_payment_search
msgid "SDD CORE"
msgstr "SDD CORE"

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_sdd_mandate
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.view_account_move_line_payment_filter
msgid "SDD Mandate"
msgstr "SDD Mandát"

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_sdd_mandate_send
msgid "SDD Mandate Send"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_batch_payment__sdd_scheme
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_payment__sdd_mandate_scheme
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__sdd_scheme
msgid "SDD Scheme"
msgstr "Schéma SDD"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_res_partner__sdd_count
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_res_users__sdd_count
msgid "SDD count"
msgstr "Počet SDD"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_res_company__sdd_creditor_identifier
msgid "SDD creditor identifier"
msgstr "Identifikátor věřitele SDD"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid "SDD scheme is set on the customer mandate."
msgstr "Schéma SEPA inkasa je nasatveno na pověření zákazníka."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "SEPA Business-to-Business Direct Debit Mandate"
msgstr "SEPA 'B2B' pověření k inkasu"

#. module: account_sepa_direct_debit
#: model:account.payment.method,name:account_sepa_direct_debit.payment_method_sdd
msgid "SEPA Direct Debit"
msgstr "Inkaso SEPA"

#. module: account_sepa_direct_debit
#: model:mail.template,name:account_sepa_direct_debit.email_template_sdd_pre_notification
msgid "SEPA Direct Debit Customer Pre-Notification mail"
msgstr ""

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "SEPA Direct Debit Mandate"
msgstr "Příkaz k inkasu SEPA"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_journal__debit_sepa_pain_version
msgid "SEPA Direct Debit Pain Version"
msgstr "SEPA Direct Debit Pain verze"

#. module: account_sepa_direct_debit
#: model:mail.template,name:account_sepa_direct_debit.email_template_sdd_new_mandate
msgid "SEPA Direct Debit Sending"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_res_company__sdd_creditor_identifier
msgid ""
"SEPA Direct Debit creditor identifier of the company, given by the bank."
msgstr "Identifikátor věřitele SEPA Direct Debit poskytnutý bankou."

#. module: account_sepa_direct_debit
#: model:mail.template,name:account_sepa_direct_debit.email_template_sdd_mandate_expiring
#: model:mail.template,subject:account_sepa_direct_debit.email_template_sdd_mandate_expiring
msgid "SEPA Direct Debit mandate expiration warning"
msgstr ""

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid ""
"SEPA Direct Debit scheme only accepts IBAN account numbers. Please select an"
" IBAN-compliant debtor account for this mandate."
msgstr ""
"Schéma SEPA inkasa přijímá pouze čísla účtů IBAN. Pro tento mandát vyberte "
"prosím účet dlužníka, který vyhovuje IBAN."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.view_account_journal_form
msgid "SEPA Pain Version"
msgstr "SEPA Pain Version"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.view_account_journal_form
msgid "SEPA Pain version"
msgstr "SEPA Pain verze"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_payment_register_form_inherit_account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_account_payment_form
msgid "SEPA Scheme operates in Euro."
msgstr ""

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid "SEPA direct debit stateless customer"
msgstr ""

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid ""
"SEPA regulations set the minimum pre-notification period to a minimum of 2 "
"days to allow enough time for the customer to check that their account is "
"adequately funded."
msgstr ""

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "SEPA-CUST-001"
msgstr "SEPA-CUST-001"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "SEPA1234"
msgstr "SEPA1234"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Chyba doručení SMS"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_bank_statement_line__sdd_has_usable_mandate
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_move__sdd_has_usable_mandate
msgid "Sdd Has Usable Mandate"
msgstr "Sdd má použitelný mandát"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_bank_statement_line__sdd_mandate_id
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_move__sdd_mandate_id
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_payment__sdd_mandate_id
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_res_partner__sdd_mandate_ids
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_res_users__sdd_mandate_ids
msgid "Sdd Mandate"
msgstr "SDD zplnomocnění"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_payment.py:0
msgid "Select a SEPA Direct Debit version before generating the XML."
msgstr "Před generováním XML zvolte verzi SEPA inkasa."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid "Send"
msgstr "Odchozí"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_send_form
msgid "Send & Print"
msgstr "Odeslat a vytisknout"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__is_sent
msgid "Sent to the customer"
msgstr ""

#. module: account_sepa_direct_debit
#: model:mail.template,description:account_sepa_direct_debit.email_template_sdd_pre_notification
msgid "Sent to the customer to indicate their account will be charged"
msgstr "Odesláno zákazníkovi, aby uvedl, že jeho účet bude účtován"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "Signature"
msgstr "Podpis"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid ""
"Some draft payments could not be posted because of the lack of any active "
"mandate."
msgstr ""
"Některé koncepty plateb nebylo možné zaúčtovat kvůli nedostatku aktivního "
"mandátu."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid "Some payments are linked to an inactive mandate."
msgstr ""

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid "Some payments are not linked to any mandate."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__start_date
msgid "Start Date"
msgstr "Počáteční datum"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__state
msgid "State"
msgstr "Stát"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Stav na základě aktivit\n"
"Po splatnosti: Datum již uplynul\n"
"Dnes: Datum aktivity je dnes\n"
"Plánováno: Budoucí aktivity."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__subject
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_send_form
msgid "Subject"
msgstr "Předmět"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_account_batch_payment__sdd_scheme
#: model:ir.model.fields,help:account_sepa_direct_debit.field_account_payment__sdd_mandate_scheme
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__sdd_scheme
msgid ""
"The B2B scheme is an optional scheme,\n"
"offered exclusively to business payers.\n"
"Some banks/businesses might not accept B2B SDD."
msgstr ""
"Režim B2B je volitelný režim,\n"
"nabízeny výhradně podnikatelským plátcům.\n"
"Některé banky / podniky nemusí akceptovat B2B SDD."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_payment.py:0
msgid ""
"The SEPA Direct Debit mandate associated to the payment has been revoked and"
" cannot be used anymore."
msgstr ""
"Mandát SEPA inkasa spojený s platbou byl zrušen a již jej nelze použít."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid ""
"The bank needs to be informed at least 5 days in advance for collections "
"related to a new mandate and 2 days in advance when the mandate is already "
"known by them. In this case, the minimum collection date must be the "
"%(date)s"
msgstr ""

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/res_company.py:0
msgid "The creditor identifier exceeds the maximum length of 35 characters."
msgstr "Identifikátor věřitele překračuje maximální délku 35 znaků."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid "The customer must have a country"
msgstr ""

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_payment.py:0
msgid ""
"The debtor and creditor city name is a compulsary information when "
"generating the SDD XML."
msgstr ""
"Název města dlužníka i věřitele je povinným údajem pro generování XML SEPA "
"inkasa."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_payment.py:0
msgid ""
"The debtor and creditor country is a compulsary information when generating "
"the SDD XML."
msgstr ""
"Země dlužníka i věřitele je povinným údajem pro generování XML SEPA inkasa."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid ""
"The debtor identifier you specified exceeds the limitation of 35 characters "
"imposed by SEPA regulation"
msgstr ""
"Zadaný identifikátor dlužníka překračuje omezení 35 znaků uložených "
"nařízením SEPA"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid ""
"The end date of the mandate must be posterior or equal to its start date."
msgstr "Konec mandátu musí být po datu, nebo roven počátečnímu datu."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid ""
"The mandate will only be used to pay invoices into the specified time range. If no end date is specified,\n"
"                    you will have to contact us to stop its use. The minimum notification period for creditors\n"
"                    to inform debtors about an upcoming collection is"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__pre_notification_period
msgid ""
"The minimum notice period in days, used to inform the customer prior to "
"collection."
msgstr ""

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_payment.py:0
msgid ""
"The payment must be linked to a SEPA Direct Debit mandate in order to "
"generate a Direct Debit XML."
msgstr ""
"Platba musí být spojena s mandátem SEPA inkasa, aby bylo možné vygenerovat "
"XML inkaso."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__name
msgid "The unique identifier of this mandate."
msgstr "Jedinečný identifikátor tohoto pověření."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_report_invoice_document
msgid ""
"This invoice will be paid using direct debit and is only\n"
"                        sent for informative purposes."
msgstr ""
"Tato faktura bude zaplacena prostřednictvím inkasa a je pouze\n"
"                       zasíláno pro informativní účely."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid ""
"This mandate is only intended for business-to-business transactions. You are"
" not entitled to a refund from your bank after your account has been "
"debited, but you are entitled to request your bank not to debit your account"
" up until the day on which the payment is due."
msgstr ""
"Toto pověření je určeno pouze pro transakce mezi podniky. Po odepsání peněz "
"z vašeho účtu nemáte nárok na vrácení peněz, ale máte právo požádat banku, "
"aby vám peníze z účtu neodepisovala až do dne splatnosti platby."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid ""
"To solve that, you should create a mandate for each of the involved "
"customers, valid at the moment of the payment date."
msgstr ""
"Chcete-li to vyřešit, měli byste vytvořit mandát pro každého ze zúčastněných"
" zákazníků, platný v okamžiku data platby."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__one_off
msgid ""
"True if and only if this mandate can be used for only one transaction. It "
"will automatically go from 'active' to 'closed' after its first use in "
"payment if this option is set.\n"
msgstr ""
"Pravda pouze tehdy, pokud lze tento mandát použít pouze pro jednu transakci."
" Je-li tato možnost nastavena, automaticky přejde z prvního na placené z "
"aktivního na uzavřené.\n"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_payment.py:0
msgid ""
"Trying to generate a Direct Debit XML file containing payments from another "
"company than that file's creditor."
msgstr ""
"Snažím se generovat XML soubor přímého inkasa obsahující platby od jiné "
"společnosti než od věřitele daného souboru."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_payment.py:0
msgid ""
"Trying to generate a Direct Debit XML for payments coming from another "
"payment method than SEPA Direct Debit."
msgstr ""
"Pokoušíme se vygenerovat XML přímého debetu pro platby pocházející z jiné "
"platební metody než SEPA inkaso."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_payment.py:0
msgid ""
"Trying to register a payment on a mandate belonging to a different partner."
msgstr "Snažíte se zaregistrovat platbu na základě mandátu jiného partnera."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Typ výjimečné aktivity na záznamu."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid "Under B2B SDD Scheme, the customer must be a company."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields.selection,name:account_sepa_direct_debit.selection__account_journal__debit_sepa_pain_version__pain_008_001_08
msgid "Updated 2023 (Pain 008.001.08)"
msgstr "Aktualizováno 2023 (Pain 008.001.08)"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
msgid "Validate"
msgstr "Potvrdit"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "Validity"
msgstr "Platnost"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/wizard/sdd_mandate_send.py:0
msgid "View Partner(s)"
msgstr "Zobrazit Partner(y)"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__warnings
msgid "Warnings"
msgstr "Varování"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__website_message_ids
msgid "Website Messages"
msgstr "Webové zprávy"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__website_message_ids
msgid "Website communication history"
msgstr "Webová historie komunikace"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/wizard/account_payment_register.py:0
msgid ""
"You can't pay any of the selected invoices using the SEPA Direct Debit "
"method, as no valid mandate is available"
msgstr ""

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/res_partner_bank.py:0
msgid ""
"You cannot delete a bank account linked to an active SEPA Direct Debit "
"mandate."
msgstr ""
"Bankovní účet propojený s aktivním příkazem k inkasu SEPA nelze smazat."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid ""
"You cannot generate a SEPA Direct Debit file with a required collection date inferior to the sending day + the longest pre-notification period defined in the mandates linked to this batch.\n"
"According to these payments mandates, the minimum required date should be the %(minimum_date)s"
msgstr ""

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid ""
"Your company must have a creditor identifier in order to issue SEPA Direct "
"Debit payments requests. It can be defined in accounting module's settings."
msgstr ""
"Vaše společnost musí mít k vydání žádostí o platby SEPA inkasa identifikátor"
" věřitele. Může být definován v nastavení účetního modulu."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid ""
"day(s).\n"
"                    It will be sent to the debtor's email."
msgstr ""

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid ""
"to send instructions to your bank to debit your account and (B) your bank to"
" debit your account in accordance with the instructions from"
msgstr ""
"zaslat pokyny vaší bance k odepsání částky z vašeho účtu a (B) vaší bance k "
"odepsání částky z vašeho účtu v souladu s pokyny od"

#. module: account_sepa_direct_debit
#: model:mail.template,subject:account_sepa_direct_debit.email_template_sdd_new_mandate
msgid "{{ object.partner_id.display_name }} SEPA Direct Debit Mandate"
msgstr ""
