# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_sepa_direct_debit
# 
# Translators:
# <PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# eriiikgt, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# Jonatan Gk, 2024
# jabiri7, 2024
# <PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# ericrolo, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON> <carles<PERSON>@hotmail.com>, 2024
# <AUTHOR> <EMAIL>, 2024
# ma<PERSON><PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:52+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Xavier, 2024\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "(if applicable)"
msgstr "(si aplicable)"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "(optional)"
msgstr "(opcional)"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "*********"
msgstr "*********"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "2023-08-10"
msgstr "2023-08-10"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "2023-09-10"
msgstr "2023-09-10"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "380055"
msgstr "380055"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "*************"
msgstr "*************"

#. module: account_sepa_direct_debit
#: model:mail.template,body_html:account_sepa_direct_debit.email_template_sdd_new_mandate
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Dear <t t-out=\"object.partner_id.display_name or ''\">Azure Interior</t>\n"
"                        <br/>\n"
"                        <br/>\n"
"                        Here is your SEPA Direct Debit Mandate to sign to authorize\n"
"                        <t t-out=\"object.company_id.display_name or ''\">YourCompany</t>\n"
"                        to send instructions to your bank to debit your account in accordance with the instructions from\n"
"                        <t t-out=\"object.company_id.display_name or ''\">YourCompany</t>\n"
"                        Do not hesitate to contact us if you have any questions.\n"
"                        <br/>\n"
"                        Best regards,\n"
"                        <t t-if=\"not is_html_empty(user.signature)\" data-o-mail-quote-container=\"1\">\n"
"                            <br/><br/>\n"
"                            <t t-out=\"user.signature\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"                        </t>\n"
"                    </p>\n"
"                </div>\n"
"            "
msgstr ""

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
msgid "<span> day(s)</span>"
msgstr ""

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>Address:</strong>"
msgstr "<strong>Adreça:</strong>"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>City: </strong>"
msgstr "<strong>Ciutat: </strong>"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>Country: </strong>"
msgstr "<strong>País: </strong>"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>Creditor identifier:</strong>"
msgstr "<strong>Identificador de creditor:</strong>"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid ""
"<strong>Date and place of signature:</strong> "
"......................................"
msgstr ""
"<strong>Data i lloc de signatura:</strong> "
"......................................"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>Email:</strong>"
msgstr "<strong>Email:</strong>"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>IBAN:</strong>"
msgstr "<strong>IBAN:</strong>"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>Mandate identifier:</strong>"
msgstr "<strong>Identificador del mandat:</strong>"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid ""
"<strong>Name of the reference party:</strong> "
"......................................"
msgstr ""
"<strong>Nom de la part de referència:</strong> "
"......................................"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>Phone:</strong>"
msgstr "<strong>Telèfon:</strong>"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>Signature:</strong>"
msgstr "<strong>Signatura:</strong>"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>Start date:</strong>"
msgstr "<strong>Data inicial:</strong>"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>Transaction type:</strong> recurrent"
msgstr "<strong>Tipus de transacció:</strong> recurrent"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>Zip: </strong>"
msgstr "<strong>CP: </strong>"

#. module: account_sepa_direct_debit
#: model:mail.template,body_html:account_sepa_direct_debit.email_template_sdd_pre_notification
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <t t-set=\"company\" t-value=\"object.company_id\"/>\n"
"                    <span style=\"font-size: 10px;\">Your Direct Debit Payment Notification</span><br/>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"right\" t-if=\"not company.uses_default_logo\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ company.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"company.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                    <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    Dear <t t-out=\"object.partner_id.name or ''\">Azure Interior</t><br/>\n"
"                    <br/>\n"
"                    A Direct Debit payment request amounting to\n"
"                    <span style=\"font-weight:bold;\" t-out=\"format_amount(object.amount, object.currency_id) or ''\">$ 0.00</span>\n"
"                    will be sent to your bank.<br/>\n"
"                    Your account ending with <t t-out=\"ctx.get('iban_last_4') or ''\">1234</t> will be automatically debited on the\n"
"                    <span style=\"font-weight:bold;\" t-out=\"format_date(ctx.get('collection_date'))\">2020-04-18</span>,\n"
"                    or shortly thereafter.<br/>\n"
"                    please make sure you have the requested funds.<br/>\n"
"                    <br/>\n"
"                    <t t-if=\"ctx.get('creditor_iban') or ctx.get('mandate_ref')\">\n"
"                        Merchant data:<br/>\n"
"                        <ul>\n"
"                            <t t-if=\"ctx.get('creditor_iban')\">\n"
"                                <li>IBAN: <t t-out=\"ctx['creditor_iban'] or ''\">NO 93 8601 1117947</t></li>\n"
"                            </t>\n"
"                            <t t-if=\"ctx.get('mandate_ref')\">\n"
"                                <li>SEPA DIRECT DEBIT MANDATE REFERENCE: <t t-out=\"ctx['mandate_ref'] or ''\"/></li>\n"
"                            </t>\n"
"                        </ul>\n"
"                    </t>\n"
"                    <br/>\n"
"                    Do not hesitate to contact us if you have any question.\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"company.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-if=\"company.phone\">\n"
"                        <t t-out=\"company.phone or ''\">******-123-4567</t> |\n"
"                    </t>\n"
"                    <t t-if=\"company.email\">\n"
"                        <a t-attf-href=\"'mailto:%s' % {{ company.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"company.email or ''\"><EMAIL></a> |\n"
"                    </t>\n"
"                    <t t-if=\"company.website\">\n"
"                        <a t-attf-href=\"'%s' % {{ company.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"company.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=mail\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""

#. module: account_sepa_direct_debit
#: model:mail.template,body_html:account_sepa_direct_debit.email_template_sdd_mandate_expiring
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #FFFFFF; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\">\n"
"                    <tr>\n"
"                        <td align=\"center\">\n"
"                            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: #FFFFFF; color: #454748; border-collapse:separate;\">\n"
"                                <tbody>\n"
"                                    <!-- CONTENT -->\n"
"                                    <tr>\n"
"                                        <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                                <tr>\n"
"                                                    <td valign=\"top\" style=\"font-size: 13px;\">\n"
"                                                        <div>\n"
"                                                            Hello,<br/><br/>\n"
"                                                            A SEPA Direct Debit mandate will reach its automatic expiration date on <span t-out=\"format_date(object._get_expiry_date_per_mandate()[object])\">2020-04-18</span><br/>\n"
"                                                            This can be caused by one of the following reason:\n"
"                                                            <ul>\n"
"                                                                <li>\n"
"                                                                    That date is the mandate end date agreed upon signature.\n"
"                                                                </li>\n"
"                                                                <li>\n"
"                                                                    It would then be more than 36 months since the last time this mandate was used.\n"
"                                                                </li>\n"
"                                                            </ul>\n"
"                                                            <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                                                                <a t-attf-href=\"{{ website_url }}/odoo/sdd-mandates/{{ object.id }}\" style=\"background-color: #4caf50; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                                                    Go to Mandate\n"
"                                                                </a>\n"
"                                                            </div>\n"
"                                                            Security Tip: Check that the domain name you are redirected to is: <b><a t-att-href=\"object.get_base_url()\" t-out=\"object.get_base_url() or ''\">\n"
"                                                            https://yourcompany.odoo.com</a></b>\n"
"                                                        </div>\n"
"                                                    </td>\n"
"                                                </tr>\n"
"                                                <tr>\n"
"                                                    <td style=\"text-align:center;\">\n"
"                                                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                                    </td>\n"
"                                                </tr>\n"
"                                            </table>\n"
"                                        </td>\n"
"                                    </tr>\n"
"                                </tbody>\n"
"                            </table>\n"
"                        </td>\n"
"                    </tr>\n"
"                    <!-- POWERED BY -->\n"
"                    <tr>\n"
"                        <td align=\"center\" style=\"min-width: 590px;\">\n"
"                            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"                                <tr>\n"
"                                    <td style=\"text-align: center; font-size: 13px;\">\n"
"                                        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=auth\" style=\"color: #875A7B;\">Odoo</a>\n"
"                                    </td>\n"
"                                </tr>\n"
"                            </table>\n"
"                            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"                                <tr>\n"
"                                    <td style=\"text-align: center; font-size: 11px;\">\n"
"                                        PS: This is an automated email sent by Odoo Accounting to notify you a SEPA Direct Debit mandate is going to get automatically closed.\n"
"                                    </td>\n"
"                                </tr>\n"
"                            </table>\n"
"                        </td>\n"
"                    </tr>\n"
"                </table>\n"
"            "
msgstr ""

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_payment.py:0
msgid ""
"A SEPA direct debit version should be selected to generate the addresses in "
"the export file."
msgstr ""

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_payment.py:0
msgid ""
"A SEPA direct debit version should be selected to generate the export file."
msgstr ""

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid ""
"A customer account is required to validate a SEPA Direct Debit mandate."
msgstr ""

#. module: account_sepa_direct_debit
#: model_terms:ir.actions.act_window,help:account_sepa_direct_debit.account_sepa_direct_debit_mandate_tree_act
msgid ""
"A mandate represents the authorization you receive from a customer\n"
"                    to automatically collect money on her account."
msgstr ""
"Un mandat representa l'autorizació que rebeu d'un client\n"
"                    per a fer càrrecs automàtics al seu compte."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__partner_bank_id
msgid "Account of the customer to collect payments from."
msgstr "Compte del client per a fer-hi càrrecs."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_needaction
msgid "Action Needed"
msgstr "Acció necessària"

#. module: account_sepa_direct_debit
#: model:ir.model.fields.selection,name:account_sepa_direct_debit.selection__sdd_mandate__state__active
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sdd_mandate_search_view
msgid "Active"
msgstr "Actiu"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__activity_ids
msgid "Activities"
msgstr "Activitats"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Activitat d'excepció de decoració"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__activity_state
msgid "Activity State"
msgstr "Estat de l'activitat"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icona de tipus d'activitat"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid "All the payments in the batch must have the same SDD scheme."
msgstr "Tots els pagaments en el lot han de tenir el mateix esquema SDD."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "Antwerp"
msgstr "Anvers"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid ""
"As part of your rights, you are entitled to a refund from your bank under "
"the terms and conditions of your agreement with your bank. Your rights are "
"explained in a statement that you can obtain from your bank. A refund must "
"be claimed within 8 weeks starting from the date on which your account was "
"debited."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_attachment_count
msgid "Attachment Count"
msgstr "Nombre d'adjunts"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__author_id
msgid "Author"
msgstr "Autor"

#. module: account_sepa_direct_debit
#: model:ir.model.fields.selection,name:account_sepa_direct_debit.selection__account_batch_payment__sdd_scheme__b2b
#: model:ir.model.fields.selection,name:account_sepa_direct_debit.selection__sdd_mandate__sdd_scheme__b2b
msgid "B2B"
msgstr "B2B"

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_res_partner_bank
msgid "Bank Accounts"
msgstr "Comptes bancaris"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.view_batch_payment_form_inherit
msgid "Batch Booking"
msgstr "Comptabilització per lots"

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_account_batch_payment
msgid "Batch Payment"
msgstr "Remesa de pagaments"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "Belgium"
msgstr "Bèlgica"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__body_has_template_value
msgid "Body content is the same as the template"
msgstr ""

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "Building C"
msgstr "Edifici C"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "By signing this mandate form, you authorise (A)"
msgstr "Tot signant aquest formulari de mandat, autoritzeu (A)"

#. module: account_sepa_direct_debit
#: model:ir.model.fields.selection,name:account_sepa_direct_debit.selection__account_batch_payment__sdd_scheme__core
#: model:ir.model.fields.selection,name:account_sepa_direct_debit.selection__sdd_mandate__sdd_scheme__core
msgid "CORE"
msgstr "NUCLI"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "CREDIT-1234"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__can_edit_body
msgid "Can Edit Body"
msgstr "Pot modificar el cos del email"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_send_form
msgid "Cancel"
msgstr "Cancel·la"

#. module: account_sepa_direct_debit
#: model:ir.model.fields.selection,name:account_sepa_direct_debit.selection__sdd_mandate__state__cancelled
msgid "Cancelled"
msgstr "Cancel·lat"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/wizard/sdd_mandate_send.py:0
msgid "Check Partner(s) Email(s)"
msgstr "Comproveu el(s) correu(s) electrònic(s) de contacte(s)"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
msgid "Close"
msgstr "Tancar"

#. module: account_sepa_direct_debit
#: model:ir.model.fields.selection,name:account_sepa_direct_debit.selection__sdd_mandate__state__closed
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sdd_mandate_search_view
msgid "Closed"
msgstr "Tancat"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
msgid "Collections"
msgstr "Col·leccions"

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_res_company
msgid "Companies"
msgstr "Empreses"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__company_id
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__company_id
msgid "Company"
msgstr "Empresa"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__company_id
msgid "Company for whose invoices the mandate can be used."
msgstr "Empresa per a les factures de la qual es pot usar el mandat."

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_res_config_settings
msgid "Config Settings"
msgstr "Paràmetres de configuració"

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_res_partner
msgid "Contact"
msgstr "Contacte"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__body
msgid "Contents"
msgstr "Continguts"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_payment__sdd_mandate_usable
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_payment_register__sdd_mandate_usable
msgid "Could a SDD mandate be used?"
msgstr "Pot usar-se un mandat SEPA SDD?"

#. module: account_sepa_direct_debit
#: model_terms:ir.actions.act_window,help:account_sepa_direct_debit.account_sepa_direct_debit_mandate_tree_act
msgid "Create a new direct debit customer mandate"
msgstr "Crear a new mandat de càrrec directe"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_account_payment_form
msgid "Create it."
msgstr "Crea-ho."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__create_uid
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__create_uid
msgid "Created by"
msgstr "Creat per"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__create_date
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__create_date
msgid "Created on"
msgstr "Creat el"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "Creditor"
msgstr "Creditor"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_res_config_settings__sdd_creditor_identifier
msgid "Creditor Identifier"
msgstr "Identificador de creditor"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_res_config_settings__sdd_creditor_identifier
msgid "Creditor identifier of your company within SEPA scheme."
msgstr "Identificador de creditor de la vostra empresa dins l'esquema SEPA."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__partner_id
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__partner_id
msgid "Customer"
msgstr "Client/a"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
msgid "Customer mandate"
msgstr "Mandat de client"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__partner_id
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate_send__partner_id
msgid "Customer whose payments are to be managed by this mandate."
msgstr "Client els pagaments del qual es regeixen per aquest mandat."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "DEBT1234"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__start_date
msgid "Date from which the mandate can be used (inclusive)."
msgstr "Data des de la qual el mandat pot usar-se (inclusiu)."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__end_date
msgid ""
"Date until which the mandate can be used. It will automatically be closed "
"after this date."
msgstr ""
"Data fins a la qual es pot fer servir el mandat. Desprès es tancarà "
"automàticament."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_account_batch_payment__sdd_required_collection_date
msgid ""
"Date when the company expects to receive the payments of this batch. It "
"can't be inferior to the sending day + the longest pre-notification period "
"defined in the mandates linked to this batch."
msgstr ""

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "Debtor"
msgstr "Deutor"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__debtor_id_code
msgid "Debtor Identifier"
msgstr "Identificador del deutor"

#. module: account_sepa_direct_debit
#: model:ir.model.fields.selection,name:account_sepa_direct_debit.selection__account_journal__debit_sepa_pain_version__pain_008_001_02
msgid "Default (Pain 008.001.02)"
msgstr "Per defecte (Pain 008.001.02)"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: account_sepa_direct_debit
#: model:ir.actions.act_window,name:account_sepa_direct_debit.account_sepa_direct_debit_mandate_tree_act
#: model:ir.actions.act_window,name:account_sepa_direct_debit.account_sepa_direct_debit_partner_mandates
#: model:ir.ui.menu,name:account_sepa_direct_debit.account_sepa_direct_debit_customer_mandates_menu
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sdd_res_partner_view
msgid "Direct Debit Mandates"
msgstr "Mandats de càrrec directe"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_account_journal_dashboard_kanban_view
msgid "Direct Debit Payment to Collect"
msgstr "Pagaments de càrrec directe a recollir"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__payments_to_collect_nber
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_account_journal_dashboard_kanban_view
msgid "Direct Debit Payments to Collect"
msgstr "Pagaments de càrrec directe a recollir"

#. module: account_sepa_direct_debit
#: model:ir.actions.act_window,name:account_sepa_direct_debit.action_sdd_payments_to_collect
msgid "Direct debit payments to collect"
msgstr "Pagaments de càrrec directe a recollir"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__display_name
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__display_name
msgid "Display Name"
msgstr "Nom mostrat"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__checkbox_download
msgid "Download"
msgstr "Descarregar"

#. module: account_sepa_direct_debit
#: model:ir.model.fields.selection,name:account_sepa_direct_debit.selection__sdd_mandate__state__draft
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sdd_mandate_search_view
msgid "Draft"
msgstr "Esborrany"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__state
msgid ""
"Draft: Validate before use.\n"
"Active: Valid mandates to collect payments.\n"
"Cancelled: Mandates never validated.\n"
"Closed: Expired or manually closed mandates. Previous transactions remain valid.\n"
"Revoked: Fraudulent mandates. Previous invoices might need reimbursement.\n"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__checkbox_send_mail
msgid "Email"
msgstr "Correu electrònic"

#. module: account_sepa_direct_debit
#: model:mail.template,description:account_sepa_direct_debit.email_template_sdd_new_mandate
msgid "Email requesting the customer to sign the mandate attached"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__template_id
msgid "Email template"
msgstr "Plantilla de correu electrònic"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__end_date
msgid "End Date"
msgstr "Data de finalització"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "End date"
msgstr "Data de finalització"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_res_partner__sdd_mandate_ids
#: model:ir.model.fields,help:account_sepa_direct_debit.field_res_users__sdd_mandate_ids
msgid "Every mandate belonging to this partner."
msgstr "Tots els mandats pertanyents a aquest partner."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__expiration_warning_already_sent
msgid "Expiration warning sent"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_follower_ids
msgid "Followers"
msgstr "Seguidors"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidors (Partners)"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icona Font Awesome p.e. fa-tasks"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__debtor_id_code
msgid "Free reference identifying the debtor in your company."
msgstr "Referència lliure que identifica el deutor a la vostra empresa."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid "Go to journal"
msgstr ""

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid "Go to mandates"
msgstr ""

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid "Go to payments"
msgstr ""

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid "Go to settings"
msgstr ""

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_payment_register_form_inherit_account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_account_payment_form
msgid "Good news! A valid SEPA Mandate is available."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__has_message
msgid "Has Message"
msgstr "Té un missatge"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__partner_bank_id
msgid "IBAN"
msgstr "IBAN"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__id
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__id
msgid "ID"
msgstr "ID"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__activity_exception_icon
msgid "Icon"
msgstr "Icona"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icona que indica una activitat d'excepció."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "Identification code"
msgstr "Codi d'identificació"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__name
msgid "Identifier"
msgstr "Identificador"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""
"Si està marcat, hi ha nous missatges que requereixen la vostra atenció."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__message_has_error
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Si està marcat, alguns missatges tenen un error d'entrega."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/res_company.py:0
msgid "Invalid creditor identifier. Make sure you made no typo."
msgstr ""
"Identificador de creditor invàlid. Assegureu-vos de que no heu fet cap error"
" tipogràfic."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/res_company.py:0
msgid "Invalid creditor identifier. Wrong format."
msgstr "Identificador de creditor invàlid. Format incorrecte."

#. module: account_sepa_direct_debit
#: model:mail.message.subtype,description:account_sepa_direct_debit.sdd_mt_invoice_paid_with_mandate
msgid "Invoice paid via direct debit."
msgstr "Factura pagada via càrrec directe SEPA."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__paid_invoice_ids
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
msgid "Invoices Paid"
msgstr "Factures pagades"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.view_account_invoice_filter
msgid "Invoices matching a valid SEPA Direct Debit Mandate"
msgstr ""
"Factures que coincideixen amb SEPA Direct de Debit amb un mandat de "
"domiciliació"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__paid_invoice_ids
msgid "Invoices paid using this mandate."
msgstr "Factures pagades amb aquest mandat."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
msgid "Invoices paid with this mandate."
msgstr "Factures pagades amb aquest mandat."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__is_mail_template_editor
msgid "Is Editor"
msgstr "És Editor"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_is_follower
msgid "Is Follower"
msgstr "És un seguidor"

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_account_journal
msgid "Journal"
msgstr "Diari"

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_account_move
msgid "Journal Entry"
msgstr "Assentament comptable"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__lang
msgid "Language"
msgstr "Idioma"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__write_uid
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__write_uid
msgid "Last Updated by"
msgstr "Última actualització per"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__write_date
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__write_date
msgid "Last Updated on"
msgstr "Última actualització el"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_main_attachment_id
msgid "Main Attachment"
msgstr "Adjunt principal"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__mandate_id
msgid "Mandate"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__mandate_pdf_file
msgid "Mandate Form PDF"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.actions.report,name:account_sepa_direct_debit.sdd_mandate_form_report_main
msgid "Mandate form"
msgstr "Formulari de mandat"

#. module: account_sepa_direct_debit
#: model:ir.model.constraint,message:account_sepa_direct_debit.constraint_sdd_mandate_name_unique
msgid "Mandate identifier must be unique! Please choose another one."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.actions.server,name:account_sepa_direct_debit.sdd_mandate_state_cron_ir_actions_server
msgid "Mandate state updater"
msgstr "Actualitzador d'estat del mandat"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "Marc Demo"
msgstr "Marc Demo"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_has_error
msgid "Message Delivery error"
msgstr "Error d'entrega del missatge"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_ids
msgid "Messages"
msgstr "Missatges"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Venciment de l'activitat"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Proper esdeveniment del calendari d'activitats"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Data límit de la següent activitat"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__activity_summary
msgid "Next Activity Summary"
msgstr "Resum de la següent activitat"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__activity_type_id
msgid "Next Activity Type"
msgstr "Tipus de la següent activitat"

#. module: account_sepa_direct_debit
#: model_terms:ir.actions.act_window,help:account_sepa_direct_debit.action_sdd_payments_to_collect
msgid "No direct debit payment to collect"
msgstr "No hi ha pagaments SEPA a recollir"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_needaction_counter
msgid "Number of Actions"
msgstr "Nombre d'accions"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__payments_to_collect_nber
msgid ""
"Number of Direct Debit payments to be collected for this mandate, that is, "
"the number of payments that have been generated and posted thanks to this "
"mandate and still needs their XML file to be generated and sent to the bank "
"to debit the customer's account."
msgstr ""
"Nombre de pagaments SEPA a recollir per aquest mandat, es a dir, el nombre "
"de pagaments que s'han generat i confirmat gràcies a aquest mandat i estan "
"pendents de generar i enviar al banc el fitxer XML per tal fer el càrrec al "
"compte del client."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_has_error_counter
msgid "Number of errors"
msgstr "Nombre d'errors"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__paid_invoices_nber
msgid "Number of invoices paid with this mandate."
msgstr "Nombre de factures pagades amb aquest mandat."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Nombre de missatges que requereixen una acció"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Nombre de missatges amb error d'entrega"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "Odoo PVT LTD"
msgstr ""

#. module: account_sepa_direct_debit
#: model_terms:ir.actions.act_window,help:account_sepa_direct_debit.account_sepa_direct_debit_mandate_tree_act
msgid ""
"Once an invoice is made\n"
"                    in Odoo for a customer having a mandate active on the invoice date,\n"
"                    its validation will trigger its automatic payment, and you will\n"
"                    then only have to generate a SEPA Direct Debit (SDD) XML file containing this operation\n"
"                    and send it to your bank to effectively get paid."
msgstr ""
"Un cop feta una factura\n"
"                    a Odoo per a un client que tingui un mandat actiu en la data de la factura,\n"
"                    la seva validació activarà el pagament automàtic, i tu ho faràs\n"
"                    només cal generar un fitxer XML SEPA Direct Debit (SDD) que contingui aquesta operació\n"
"                    i enviar-la al banc per cobrar-la efectivament."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_account_bank_statement_line__sdd_mandate_id
#: model:ir.model.fields,help:account_sepa_direct_debit.field_account_move__sdd_mandate_id
#: model:ir.model.fields,help:account_sepa_direct_debit.field_account_payment__sdd_mandate_id
msgid ""
"Once this invoice has been paid with Direct Debit, contains the mandate that"
" allowed the payment."
msgstr ""
"Un cop la factura s'ha pagat amb SEPA, conté el mandat que permet el seu "
"pagament."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__one_off
msgid "One-off Mandate"
msgstr "Mandat únic"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid ""
"Only IBAN account numbers can receive SEPA Direct Debit payments. Please "
"select a journal associated to one or add an IBAN bank account to the "
"current journal"
msgstr ""

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid "Only mandates in draft state can be deleted."
msgstr ""

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_payment_register_form_inherit_account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_account_payment_form
msgid "Oops! No valid SEPA mandate for this customer."
msgstr "Oops! No hi ha un mandat de SEPA vàlid per a aquest client."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid "Open customer"
msgstr ""

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid "Open customers"
msgstr ""

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sdd_res_partner_view
msgid "Open this partner's mandates"
msgstr "Obrir els mandats d'aquest client"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate_send__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"Idioma opcional de traducció (codi ISO) per a seleccionar en enviar un "
"correu electrònic. Si no està establert, s'usarà la versió anglesa. Això "
"normalment hauria de ser una expressió de substitució que proveeixi l'idioma"
" apropiat, p. ex. {{ object.partner_id.lang }}."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_account_payment_form
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_account_payment_with_mandates_tree
msgid "Originating SEPA mandate"
msgstr "Originant mandat SEPA"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid "Paid Invoices"
msgstr "Factures pagades"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__paid_invoices_nber
msgid "Paid Invoices Number"
msgstr "Nombre de factures pagades"

#. module: account_sepa_direct_debit
#: model:mail.message.subtype,name:account_sepa_direct_debit.sdd_mt_invoice_paid_with_mandate
msgid "Paid via direct debit"
msgstr "Pagat per SEPA"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/wizard/sdd_mandate_send.py:0
msgid "Partner should have an email address."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_account_payment_register
msgid "Pay"
msgstr "Paga"

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_account_payment_method
msgid "Payment Methods"
msgstr "Formes de pagament"

#. module: account_sepa_direct_debit
#: model:mail.template,subject:account_sepa_direct_debit.email_template_sdd_pre_notification
msgid "Payment notification {{ object.memo }}"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_account_payment
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__payment_ids
msgid "Payments"
msgstr "Pagaments"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
msgid "Payments generated for this mandate that have not yet been collected."
msgstr "Pagaments generats per aquest mandat i que encara no s'han recollit."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__payment_ids
msgid "Payments generated thanks to this mandate."
msgstr "Pagaments generats gràcies a aquest mandat."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.view_account_move_line_payment_filter
msgid "Payments matching a valid SEPA Direct Debit Mandate"
msgstr ""

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid "Payments to Collect"
msgstr "Pagaments a recollir"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid "Payments without mandate"
msgstr ""

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_report_invoice_document
msgid ""
"Please do not pay it manually, the payment will be asked to your bank to be processed\n"
"                        automatically."
msgstr ""
"Si us plau, no ho pagueu manualment, es demanarà al vostre banc que processi el pagament\n"
"                        automàticament."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__pre_notification_period
msgid "Pre-notification"
msgstr ""

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid "Problematic mandates"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__rating_ids
msgid "Ratings"
msgstr "Valoracions"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__recipient_ids
msgid "Recipients"
msgstr "Destinataris"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__render_model
msgid "Rendering Model"
msgstr "Model de renderització"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_account_batch_payment__sdd_batch_booking
msgid "Request batch booking from the bank for the related bank statements."
msgstr ""
"Sol·licitar al banc comptabilització en lot dels estats bancaris "
"relacionats. "

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_batch_payment__sdd_required_collection_date
msgid "Required collection date"
msgstr "Data de recollida obligatòria"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__activity_user_id
msgid "Responsible User"
msgstr "Usuari responsable"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
msgid "Revoke"
msgstr "Revocar"

#. module: account_sepa_direct_debit
#: model:ir.model.fields.selection,name:account_sepa_direct_debit.selection__sdd_mandate__state__revoked
msgid "Revoked"
msgstr "Revocat"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_view_account_payment_search
msgid "Revoked SDD Mandate"
msgstr "Mandat SEPA revocat"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "Rue de la Loi, 16"
msgstr "Rue de la Loi, 16"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.view_account_payment_search
msgid "SDD B2B"
msgstr "SDD B2B"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_batch_payment__sdd_batch_booking
msgid "SDD Batch Booking"
msgstr "SDD Reserva de lots"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.view_account_payment_search
msgid "SDD CORE"
msgstr "SDD CENTRE"

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_sdd_mandate
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.view_account_move_line_payment_filter
msgid "SDD Mandate"
msgstr "Mandat SEPA"

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_sdd_mandate_send
msgid "SDD Mandate Send"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_batch_payment__sdd_scheme
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_payment__sdd_mandate_scheme
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__sdd_scheme
msgid "SDD Scheme"
msgstr "Esquema SDD"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_res_partner__sdd_count
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_res_users__sdd_count
msgid "SDD count"
msgstr "Comptador SEPA"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_res_company__sdd_creditor_identifier
msgid "SDD creditor identifier"
msgstr "Identificador de creditor SEPA"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid "SDD scheme is set on the customer mandate."
msgstr "L'esquema SDD està establert en el mandat del client."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "SEPA Business-to-Business Direct Debit Mandate"
msgstr ""

#. module: account_sepa_direct_debit
#: model:account.payment.method,name:account_sepa_direct_debit.payment_method_sdd
msgid "SEPA Direct Debit"
msgstr "Càrrec directe SEPA"

#. module: account_sepa_direct_debit
#: model:mail.template,name:account_sepa_direct_debit.email_template_sdd_pre_notification
msgid "SEPA Direct Debit Customer Pre-Notification mail"
msgstr ""

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "SEPA Direct Debit Mandate"
msgstr "Mandat de càrrec directe SEPA"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_journal__debit_sepa_pain_version
msgid "SEPA Direct Debit Pain Version"
msgstr ""

#. module: account_sepa_direct_debit
#: model:mail.template,name:account_sepa_direct_debit.email_template_sdd_new_mandate
msgid "SEPA Direct Debit Sending"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_res_company__sdd_creditor_identifier
msgid ""
"SEPA Direct Debit creditor identifier of the company, given by the bank."
msgstr "Identificador de creditor SEPA de l'empresa, facilitat pel banc."

#. module: account_sepa_direct_debit
#: model:mail.template,name:account_sepa_direct_debit.email_template_sdd_mandate_expiring
#: model:mail.template,subject:account_sepa_direct_debit.email_template_sdd_mandate_expiring
msgid "SEPA Direct Debit mandate expiration warning"
msgstr ""

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid ""
"SEPA Direct Debit scheme only accepts IBAN account numbers. Please select an"
" IBAN-compliant debtor account for this mandate."
msgstr ""
"L'esquema càrrec directe SEPA només accepta comptes IBAN. Si us plau, "
"seleccioneu un compte de deutor amb format IBAN per aquest mandat."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.view_account_journal_form
msgid "SEPA Pain Version"
msgstr "Versió Pain SEPA"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.view_account_journal_form
msgid "SEPA Pain version"
msgstr ""

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_payment_register_form_inherit_account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_account_payment_form
msgid "SEPA Scheme operates in Euro."
msgstr ""

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid "SEPA direct debit stateless customer"
msgstr ""

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid ""
"SEPA regulations set the minimum pre-notification period to a minimum of 2 "
"days to allow enough time for the customer to check that their account is "
"adequately funded."
msgstr ""

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "SEPA-CUST-001"
msgstr ""

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "SEPA1234"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Error de lliurament SMS"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_bank_statement_line__sdd_has_usable_mandate
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_move__sdd_has_usable_mandate
msgid "Sdd Has Usable Mandate"
msgstr "Sdd té un mandat utilitzable"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_bank_statement_line__sdd_mandate_id
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_move__sdd_mandate_id
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_payment__sdd_mandate_id
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_res_partner__sdd_mandate_ids
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_res_users__sdd_mandate_ids
msgid "Sdd Mandate"
msgstr "Mandat SEPA"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_payment.py:0
msgid "Select a SEPA Direct Debit version before generating the XML."
msgstr ""

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid "Send"
msgstr "Enviar"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_send_form
msgid "Send & Print"
msgstr "Envia i imprimeix"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__is_sent
msgid "Sent to the customer"
msgstr ""

#. module: account_sepa_direct_debit
#: model:mail.template,description:account_sepa_direct_debit.email_template_sdd_pre_notification
msgid "Sent to the customer to indicate their account will be charged"
msgstr "Enviat al client per indicar que es carregarà el seu compte"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "Signature"
msgstr "Signatura"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid ""
"Some draft payments could not be posted because of the lack of any active "
"mandate."
msgstr ""
"Alguns projectes de pagament no es van poder publicar a causa de la falta de"
" mandat actiu."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid "Some payments are linked to an inactive mandate."
msgstr ""

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid "Some payments are not linked to any mandate."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__start_date
msgid "Start Date"
msgstr "Data inicial"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__state
msgid "State"
msgstr "Estat"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Estat basat en activitats\n"
"Vençuda: La data límit ja ha passat\n"
"Avui: La data de l'activitat és avui\n"
"Planificat: Activitats futures."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__subject
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_send_form
msgid "Subject"
msgstr "Assumpte"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_account_batch_payment__sdd_scheme
#: model:ir.model.fields,help:account_sepa_direct_debit.field_account_payment__sdd_mandate_scheme
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__sdd_scheme
msgid ""
"The B2B scheme is an optional scheme,\n"
"offered exclusively to business payers.\n"
"Some banks/businesses might not accept B2B SDD."
msgstr ""
"L'esquema B2B és un esquema opcional,\n"
"ofert exclusivament als pagadors de les empreses.\n"
"És possible que alguns bancs/empreses no acceptin el SDD B2B."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_payment.py:0
msgid ""
"The SEPA Direct Debit mandate associated to the payment has been revoked and"
" cannot be used anymore."
msgstr ""
"El mandat SEPA associat al pagament s'ha revocat i ja no pot utilitzar-se."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid ""
"The bank needs to be informed at least 5 days in advance for collections "
"related to a new mandate and 2 days in advance when the mandate is already "
"known by them. In this case, the minimum collection date must be the "
"%(date)s"
msgstr ""

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/res_company.py:0
msgid "The creditor identifier exceeds the maximum length of 35 characters."
msgstr "L'identificador del creditor excedeix el màxim de 35 caràcters."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid "The customer must have a country"
msgstr ""

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_payment.py:0
msgid ""
"The debtor and creditor city name is a compulsary information when "
"generating the SDD XML."
msgstr ""

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_payment.py:0
msgid ""
"The debtor and creditor country is a compulsary information when generating "
"the SDD XML."
msgstr ""

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid ""
"The debtor identifier you specified exceeds the limitation of 35 characters "
"imposed by SEPA regulation"
msgstr ""
"L'identificador del deutor que heu especificat excedeix la limitació de 35 "
"caràcters imposada per la regulació SEPA "

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid ""
"The end date of the mandate must be posterior or equal to its start date."
msgstr ""
"La data final del mandat cal que sigui posterior o igual a la data inicial. "

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid ""
"The mandate will only be used to pay invoices into the specified time range. If no end date is specified,\n"
"                    you will have to contact us to stop its use. The minimum notification period for creditors\n"
"                    to inform debtors about an upcoming collection is"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__pre_notification_period
msgid ""
"The minimum notice period in days, used to inform the customer prior to "
"collection."
msgstr ""

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_payment.py:0
msgid ""
"The payment must be linked to a SEPA Direct Debit mandate in order to "
"generate a Direct Debit XML."
msgstr ""
"Cal que el pagament estigui enllaçat a un mandat SEPA per tal de generar un "
"fitxer SEPA XML. "

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__name
msgid "The unique identifier of this mandate."
msgstr "L'identificador únic d'aquest mandat."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_report_invoice_document
msgid ""
"This invoice will be paid using direct debit and is only\n"
"                        sent for informative purposes."
msgstr ""
"Aquesta factura es pagarà via SEPA i ha estat enviada únicament\n"
"                        amb propòsits informatius."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid ""
"This mandate is only intended for business-to-business transactions. You are"
" not entitled to a refund from your bank after your account has been "
"debited, but you are entitled to request your bank not to debit your account"
" up until the day on which the payment is due."
msgstr ""

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid ""
"To solve that, you should create a mandate for each of the involved "
"customers, valid at the moment of the payment date."
msgstr ""
"Per a resoldre això, ha de crear un mandat per a cadascun dels clients "
"implicats, vàlid en el moment de la data de pagament."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__one_off
msgid ""
"True if and only if this mandate can be used for only one transaction. It "
"will automatically go from 'active' to 'closed' after its first use in "
"payment if this option is set.\n"
msgstr ""
"Cert si, i solament si, aquest mandat només pot usar-se per a una única "
"transacció. Si aquesta opció està marcada, canviarà automàticament de "
"'actiu' a 'tancat' després del seu primer ús. \n"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_payment.py:0
msgid ""
"Trying to generate a Direct Debit XML file containing payments from another "
"company than that file's creditor."
msgstr ""
"S'està intentant generar un fitxer SEPA XML contenint pagaments d'una altra "
"empresa que el creditor del fitxer. "

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_payment.py:0
msgid ""
"Trying to generate a Direct Debit XML for payments coming from another "
"payment method than SEPA Direct Debit."
msgstr ""
"S'està intentant generar un fitxer SEPA XML contenint pagaments d'un mètode "
"de pagament diferent que càrrec directe SEPA. "

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_payment.py:0
msgid ""
"Trying to register a payment on a mandate belonging to a different partner."
msgstr ""
"S'està intentant registrar un pagament en un mandat que pertany a un altre "
"client. "

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipus d'activitat d'excepció registrada."

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
msgid "Under B2B SDD Scheme, the customer must be a company."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields.selection,name:account_sepa_direct_debit.selection__account_journal__debit_sepa_pain_version__pain_008_001_08
msgid "Updated 2023 (Pain 008.001.08)"
msgstr ""

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
msgid "Validate"
msgstr "Validar"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "Validity"
msgstr "Validesa"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/wizard/sdd_mandate_send.py:0
msgid "View Partner(s)"
msgstr "Veure contacte(s)"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_send__warnings
msgid "Warnings"
msgstr "Avisos"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__website_message_ids
msgid "Website Messages"
msgstr "Missatges del lloc web"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__website_message_ids
msgid "Website communication history"
msgstr "Historial de comunicacions del lloc web"

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/wizard/account_payment_register.py:0
msgid ""
"You can't pay any of the selected invoices using the SEPA Direct Debit "
"method, as no valid mandate is available"
msgstr ""

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/res_partner_bank.py:0
msgid ""
"You cannot delete a bank account linked to an active SEPA Direct Debit "
"mandate."
msgstr "No podeu eliminar un compte bancari enllaçat a un mandat SEPA actiu. "

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid ""
"You cannot generate a SEPA Direct Debit file with a required collection date inferior to the sending day + the longest pre-notification period defined in the mandates linked to this batch.\n"
"According to these payments mandates, the minimum required date should be the %(minimum_date)s"
msgstr ""

#. module: account_sepa_direct_debit
#. odoo-python
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
msgid ""
"Your company must have a creditor identifier in order to issue SEPA Direct "
"Debit payments requests. It can be defined in accounting module's settings."
msgstr ""
"Cal que la vostra empresa tingui un identificador de creditor per tal de "
"generar peticions de càrrec directe SEPA. Es pot definir a la configuració "
"del mòdul de comptabilitat. "

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid ""
"day(s).\n"
"                    It will be sent to the debtor's email."
msgstr ""

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid ""
"to send instructions to your bank to debit your account and (B) your bank to"
" debit your account in accordance with the instructions from"
msgstr ""
"per a enviar instruccions al vostre banc per a fer càrrecs al vostre compte "
"i (B) al vostre banc per a fer càrrecs al vostre compte d'acord amb les "
"instruccions de "

#. module: account_sepa_direct_debit
#: model:mail.template,subject:account_sepa_direct_debit.email_template_sdd_new_mandate
msgid "{{ object.partner_id.display_name }} SEPA Direct Debit Mandate"
msgstr ""
