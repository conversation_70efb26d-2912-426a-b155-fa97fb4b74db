# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_followup
# 
# Translators:
# emre oktem, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# Enes <PERSON>ç, 2024
# <PERSON>rt<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:52+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
msgid "%(company)s Payment Reminder - %(partner)s"
msgstr ""

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup.py:0
msgid "%(delay)s days (copy of %(name)s)"
msgstr ""

#. module: account_followup
#: model:ir.actions.report,print_report_name:account_followup.action_report_followup
msgid "'Follow-up ' + object.display_name"
msgstr ""

#. module: account_followup
#: model:account_followup.followup.line,name:account_followup.demo_followup_line1
msgid "15 Days"
msgstr "15 Gün"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.followup_filter_info_template
msgid "2023-09-06"
msgstr ""

#. module: account_followup
#: model:account_followup.followup.line,name:account_followup.demo_followup_line2
msgid "30 Days"
msgstr "30 Gün"

#. module: account_followup
#: model:account_followup.followup.line,name:account_followup.demo_followup_line3
msgid "40 Days"
msgstr ""

#. module: account_followup
#: model:account_followup.followup.line,name:account_followup.demo_followup_line4
msgid "50 Days"
msgstr ""

#. module: account_followup
#: model:account_followup.followup.line,name:account_followup.demo_followup_line5
msgid "60 Days"
msgstr ""

#. module: account_followup
#: model:mail.template,body_html:account_followup.email_template_followup_1
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px;\">\n"
"                        <t t-if=\"object.id != object.commercial_partner_id.id\">Dear <t t-out=\"object.name or ''\"/> (<t t-out=\"object.commercial_partner_id.name or ''\"/>),</t>\n"
"                        <t t-else=\"\">Dear <t t-out=\"object.name or ''\"/>,</t>\n"
"                        <br/>\n"
"                        It has come to our attention that you have&nbsp;an outstanding balance of <t t-out=\"format_amount(object.total_overdue, object.currency_id) or ''\"/>\n"
"                        <br/>\n"
"                        We kindly request that you take necessary action to settle this amount within the next 8 days.\n"
"                        <br/>\n"
"                        </p><div t-if=\"object._show_pay_now_button()\" class=\"d-flex\">\n"
"                            <a t-att-href=\"'/my/invoices/overdue'\" class=\"btn btn-primary\">Pay now</a>\n"
"                        </div>\n"
"                        If you have already made the payment after receiving this message, please disregard it.\n"
"                        Our accounting department is available if you require any assistance or have any questions.\n"
"                        <br/>\n"
"                        Thank you for your cooperation.\n"
"                        <br/>\n"
"                        Sincerely,\n"
"                        <t t-if=\"not is_html_empty(object._get_followup_responsible().signature)\">\n"
"                            <br/>\n"
"                            <t t-out=\"object._get_followup_responsible().signature\"/>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <br/>\n"
"                            --\n"
"                            <br/>\n"
"                            <t t-out=\"object._get_followup_responsible().name\"/>\n"
"                        </t>\n"
"                    \n"
"                </div>\n"
"            "
msgstr ""

#. module: account_followup
#: model:mail.template,body_html:account_followup.demo_followup_email_template_4
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p>\n"
"                        <t t-if=\"object.id != object.commercial_partner_id.id\">Dear <t t-out=\"object.name or ''\"/> (<t t-out=\"object.commercial_partner_id.name or ''\"/>),</t>\n"
"                        <t t-else=\"\">Dear <t t-out=\"object.name or ''\"/>,</t>\n"
"                        <br/>\n"
"                        Despite several reminders, your account is still not settled.\n"
"                        Unless full payment is made in next 8 days, then legal action for the recovery of the debt will be taken without further notice.\n"
"                        I trust that this action will prove unnecessary and details of due payments is printed below.\n"
"                        In case of any queries concerning this matter, do not hesitate to contact our accounting department.\n"
"                        <br/>\n"
"                        Best Regards,\n"
"                        <br/>\n"
"                        <t t-if=\"not is_html_empty(object._get_followup_responsible().signature)\">\n"
"                            <br/>\n"
"                            <t t-out=\"object._get_followup_responsible().signature\"/>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <br/>\n"
"                            --\n"
"                            <br/>\n"
"                            <t t-out=\"object._get_followup_responsible().name\"/>\n"
"                        </t>\n"
"                    </p>\n"
"                </div>\n"
"            "
msgstr ""

#. module: account_followup
#: model:mail.template,body_html:account_followup.demo_followup_email_template_2
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p>\n"
"                        <t t-if=\"object.id != object.commercial_partner_id.id\">Dear <t t-out=\"object.name or ''\"/> (<t t-out=\"object.commercial_partner_id.name or ''\"/>),</t>\n"
"                        <t t-else=\"\">Dear <t t-out=\"object.name or ''\"/>,</t>\n"
"                        <br/>\n"
"                        We are disappointed to see that despite sending a reminder, that your account is now seriously overdue.\n"
"                        <br/>\n"
"                        It is essential that immediate payment is made, otherwise we will have to consider placing a stop on your account which means that we will no longer be able to supply your company with (goods/services). Please, take appropriate measures in order to carry out this payment in the next 8 days.\n"
"                        <br/>\n"
"                        If there is a problem with paying invoice that we are not aware of, do not hesitate to contact our accounting department, so that we can resolve the matter quickly.\n"
"                        <br/>\n"
"                        Details of due payments is printed below.\n"
"                        <br/>\n"
"                        Best Regards,\n"
"                        <t t-if=\"not is_html_empty(object._get_followup_responsible().signature)\">\n"
"                            <br/>\n"
"                            <t t-out=\"object._get_followup_responsible().signature\"/>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <br/>\n"
"                            --\n"
"                            <br/>\n"
"                            <t t-out=\"object._get_followup_responsible().name\"/>\n"
"                        </t>\n"
"                    </p>\n"
"                </div>\n"
"            "
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.res_partner_view_form
msgid "<span class=\"o_stat_text\">Customer Statement</span>"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.res_partner_view_form
msgid ""
"<span>Preferred address for follow-up reports. Selected by default when you "
"send reminders about overdue invoices.</span>"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid ""
"<strong>\n"
"                                Pending Invoices\n"
"                            </strong>"
msgstr ""

#. module: account_followup
#: model:ir.model.constraint,message:account_followup.constraint_account_followup_followup_line_uniq_name
msgid ""
"A follow-up action name must be unique. This name is already set to another "
"action."
msgstr ""
"Bir takip eylemi adı benzersiz olmalıdır. Bu ad zaten başka bir eyleme "
"ayarlanmış."

#. module: account_followup
#: model:ir.model.fields.selection,name:account_followup.selection__account_followup_followup_line__activity_default_responsible_type__account_manager
msgid "Account Manager"
msgstr "Hesap Yöneticisi"

#. module: account_followup
#: model:ir.actions.server,name:account_followup.ir_cron_auto_post_draft_entry_ir_actions_server
msgid "Account Report Followup; Execute followup"
msgstr "Hesap Takibi Raporu ; Takip gerçekleştir"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "Actions"
msgstr "Aksiyonlar"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_tree
msgid "Activity"
msgstr "Aktivite"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "Activity Notes"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__activity_type_id
msgid "Activity Type"
msgstr "Aktivite Türü"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "Add a note"
msgstr "Bir Not Ekle"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "Add contacts to notify..."
msgstr "Bilgilendirilecek kişi ekle..."

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__additional_follower_ids
msgid "Add followers"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.followup_filter_info_template
msgid "Address"
msgstr "Adres"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__type
#: model:ir.model.fields,field_description:account_followup.field_res_users__type
msgid "Address Type"
msgstr "Adres Türü"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__join_invoices
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__join_invoices
msgid "Attach Invoices"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__attachment_ids
msgid "Attachment"
msgstr "Ek"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__auto_execute
#: model:ir.model.fields.selection,name:account_followup.selection__res_partner__followup_reminder_type__automatic
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_line_filter
msgid "Automatic"
msgstr "Otomatik"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__body_has_template_value
msgid "Body content is the same as the template"
msgstr "Gövde içeriği şablonla aynı"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__can_edit_body
msgid "Can Edit Body"
msgstr "Gövdeyi Düzenleyebilir"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "Cancel"
msgstr "İptal"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.missing_information_view_form
msgid "Close"
msgstr "Kapat"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
msgid "Communication"
msgstr "İletişim"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__company_id
msgid "Company"
msgstr "Firma"

#. module: account_followup
#: model:ir.model,name:account_followup.model_res_partner
msgid "Contact"
msgstr "Kontak"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "Content Template"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__body
msgid "Contents"
msgstr "İçerikler"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__create_uid
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__create_uid
#: model:ir.model.fields,field_description:account_followup.field_account_followup_missing_information_wizard__create_uid
msgid "Created by"
msgstr "Tarafından oluşturuldu"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__create_date
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__create_date
#: model:ir.model.fields,field_description:account_followup.field_account_followup_missing_information_wizard__create_date
msgid "Created on"
msgstr "Oluşturuldu"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.followup_filter_info_template
msgid "Customer ref:"
msgstr "Müşteri ref:"

#. module: account_followup
#: model:ir.actions.client,name:account_followup.action_account_followup
msgid "Customers Statement"
msgstr "Müşteri Hesap Ekstreleri"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
msgid "Date"
msgstr "Tarih"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.followup_filter_info_template
msgid "Date:"
msgstr "Tarih:"

#. module: account_followup
#: model:ir.model.constraint,message:account_followup.constraint_account_followup_followup_line_days_uniq
msgid "Days of the follow-up lines must be different per company"
msgstr ""

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
msgid ""
"Dear %s,\n"
"\n"
"\n"
"Exception made if there was a mistake of ours, it seems that the following amount stays unpaid. Please, take appropriate measures in order to carry out this payment in the next 8 days.\n"
"\n"
"Would your payment have been carried out after this mail was sent, please ignore this message. Do not hesitate to contact our accounting department.\n"
"\n"
"Best Regards,\n"
"\n"
msgstr ""

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
msgid ""
"Dear client, we kindly remind you that you still have unpaid invoices. "
"Please check them and take appropriate action. %s"
msgstr ""

#. module: account_followup
#: model_terms:ir.actions.act_window,help:account_followup.action_account_followup_line_definition_form
msgid "Define follow-up levels and their related actions"
msgstr "Takip düzeylerini ve onlarla ilgili eylemleri tanımlayın."

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.followup_filter_info_template
msgid "Demo Ref"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__name
msgid "Description"
msgstr "Açıklama"

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_account_followup_followup_line__activity_default_responsible_type
msgid ""
"Determine who will be assigned to the activity:\n"
"- Follow-up Responsible (default)\n"
"- Salesperson: Sales Person defined on the invoice\n"
"- Account Manager: Sales Person defined on the customer"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__display_name
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__display_name
#: model:ir.model.fields,field_description:account_followup.field_account_followup_missing_information_wizard__display_name
msgid "Display Name"
msgstr "İsim Göster"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
msgid "Due Date"
msgstr "Vade Tarihi"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__delay
msgid "Due Days"
msgstr "Vade Tarihleri"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__email
msgid "Email"
msgstr "E-Posta"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "Email Recipients"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "Email Subject"
msgstr "E-posta Konusu"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__email_recipient_ids
msgid "Extra Recipients"
msgstr ""

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
msgid "Follow-up %(partner)s - %(date)s"
msgstr ""

#. module: account_followup
#: model:ir.model.fields.selection,name:account_followup.selection__res_partner__type__followup
msgid "Follow-up Address"
msgstr ""

#. module: account_followup
#: model:ir.model,name:account_followup.model_account_followup_followup_line
msgid "Follow-up Criteria"
msgstr "Takip Kriteri"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_move_line__followup_line_id
#: model:ir.model.fields,field_description:account_followup.field_res_partner__followup_line_id
#: model:ir.model.fields,field_description:account_followup.field_res_users__followup_line_id
msgid "Follow-up Level"
msgstr "Takip Düzeyi"

#. module: account_followup
#: model:ir.actions.act_window,name:account_followup.action_account_followup_line_definition_form
#: model:ir.ui.menu,name:account_followup.account_followup_menu
msgid "Follow-up Levels"
msgstr "Takip Düzeyleri"

#. module: account_followup
#: model:ir.model,name:account_followup.model_account_followup_report
msgid "Follow-up Report"
msgstr "Alacak Takip Raporu"

#. module: account_followup
#: model:ir.model.fields.selection,name:account_followup.selection__account_followup_followup_line__activity_default_responsible_type__followup
msgid "Follow-up Responsible"
msgstr "Takip Sorumlusu"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__followup_status
#: model:ir.model.fields,field_description:account_followup.field_res_users__followup_status
msgid "Follow-up Status"
msgstr "Takip Durumu"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_tree
msgid "Follow-up Steps"
msgstr "Takip Adımları"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.report_followup_print_all
msgid "Follow-up details"
msgstr ""

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
msgid "Follow-up letter generated"
msgstr ""

#. module: account_followup
#: model:ir.model,name:account_followup.model_account_followup_missing_information_wizard
msgid "Followup missing information wizard"
msgstr ""

#. module: account_followup
#: model_terms:ir.actions.act_window,help:account_followup.action_account_followup_line_definition_form
msgid ""
"For each step, specify the actions to be taken and delay in days. It is\n"
"            possible to use print and e-mail templates to send specific messages to\n"
"            the customer."
msgstr ""

#. module: account_followup
#: model:mail.template,name:account_followup.demo_followup_email_template_4
msgid "Fourth reminder followup"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__id
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__id
#: model:ir.model.fields,field_description:account_followup.field_account_followup_missing_information_wizard__id
msgid "ID"
msgstr "ID"

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_account_followup_followup_line__additional_follower_ids
msgid ""
"If set, those users will be added as followers on the partner and receive "
"notifications about any email reply made by the partner on the reminder "
"email."
msgstr ""

#. module: account_followup
#: model:ir.model.fields.selection,name:account_followup.selection__res_partner__followup_status__in_need_of_action
msgid "In need of action"
msgstr "Eylem gerektiriyor"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_partner_property_form_followup
msgid "Invoice Follow-ups"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "Invoices Analysis"
msgstr "Faturaların Analizi"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__is_mail_template_editor
msgid "Is Editor"
msgstr "İş Düzenleyicisi"

#. module: account_followup
#: model:ir.model,name:account_followup.model_account_move_line
msgid "Journal Item"
msgstr "Yevmiye Kalemi"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "Kind reminder!"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__lang
msgid "Language"
msgstr "Dil"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__write_uid
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__write_uid
#: model:ir.model.fields,field_description:account_followup.field_account_followup_missing_information_wizard__write_uid
msgid "Last Updated by"
msgstr "Son Güncelleyen"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__write_date
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__write_date
#: model:ir.model.fields,field_description:account_followup.field_account_followup_missing_information_wizard__write_date
msgid "Last Updated on"
msgstr "Son Güncelleme"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "Letter/Email Content"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__mail_template_id
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__template_id
msgid "Mail Template"
msgstr "E-Posta Şablonu"

#. module: account_followup
#: model:ir.model.fields.selection,name:account_followup.selection__res_partner__followup_reminder_type__manual
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_line_filter
msgid "Manual"
msgstr "Manuel"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/res_partner.py:0
#: code:addons/account_followup/wizard/followup_missing_information.py:0
msgid "Missing information"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.line_template
msgid "Name"
msgstr "Adı"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/res_partner.py:0
msgid "Next Reminder Date set to %s"
msgstr "Sonraki Hatırlatma Tarihi %s"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__followup_next_action_date
#: model:ir.model.fields,field_description:account_followup.field_res_users__followup_next_action_date
msgid "Next reminder"
msgstr ""

#. module: account_followup
#: model:ir.model.fields.selection,name:account_followup.selection__res_partner__followup_status__no_action_needed
msgid "No action needed"
msgstr "Eylem gerekmiyor"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__activity_note
msgid "Note"
msgstr "Not"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "Notification"
msgstr "Bildirimler"

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_account_followup_manual_reminder__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"E-posta gönderirken seçmek için isteğe bağlı çeviri dili (ISO kodu). "
"Ayarlanmazsa, İngilizce sürüm kullanılacaktır. Bu genellikle uygun dili "
"sağlayan bir yer tutucu ifadesi olmalıdır, örn. {{ object.partner_id.lang "
"}}."

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "Options"
msgstr "Seçenekler"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
#: model:ir.model.fields,field_description:account_followup.field_account_move_line__invoice_origin
msgid "Origin"
msgstr "Kaynak"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/res_partner.py:0
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_search_view
#: model_terms:ir.ui.view,arch_db:account_followup.view_partner_property_form_followup
msgid "Overdue Invoices"
msgstr "Gecikmiş Faturalar"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__partner_id
msgid "Partner"
msgstr "İş Ortağı"

#. module: account_followup
#: model:mail.template,name:account_followup.email_template_followup_1
msgid "Payment Reminder"
msgstr "Ödeme Hatırlatıcı"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__print
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "Print"
msgstr "Yazdır"

#. module: account_followup
#: model:ir.actions.report,name:account_followup.action_report_followup
msgid "Print Follow-up Letter"
msgstr "Takip Mektubu Yazdır"

#. module: account_followup
#: model:ir.actions.server,name:account_followup.action_account_reports_customer_statements_do_followup
msgid "Process Follow-ups"
msgstr ""

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
msgid "Reference"
msgstr "Referans"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "Remind"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__followup_reminder_type
#: model:ir.model.fields,field_description:account_followup.field_res_users__followup_reminder_type
msgid "Reminders"
msgstr "Hatırlatmalar"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__render_model
msgid "Rendering Model"
msgstr "İşleme Modeli"

#. module: account_followup
#: model:ir.model,name:account_followup.model_ir_actions_report
msgid "Report Action"
msgstr "Rapor işlemi"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_search_view
msgid "Requires Follow-up"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__activity_default_responsible_type
#: model:ir.model.fields,field_description:account_followup.field_res_partner__followup_responsible_id
#: model:ir.model.fields,field_description:account_followup.field_res_users__followup_responsible_id
msgid "Responsible"
msgstr "Sorumlu"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "SMS"
msgstr "SMS"

#. module: account_followup
#: model:ir.model.fields.selection,name:account_followup.selection__account_followup_followup_line__activity_default_responsible_type__salesperson
msgid "Salesperson"
msgstr "Satış Temsilcisi"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__create_activity
msgid "Schedule Activity"
msgstr "Aktivite Planla"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_line_filter
msgid "Search Follow-up"
msgstr "Takip Ara"

#. module: account_followup
#: model:mail.template,name:account_followup.demo_followup_email_template_2
msgid "Second reminder followup"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
#: model_terms:ir.ui.view,arch_db:account_followup.view_partner_property_form_followup
msgid "Send"
msgstr "Gönder"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "Send & Print"
msgstr "Gönder & Yazdır"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__send_email
msgid "Send Email"
msgstr "E-posta Gönder"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__send_sms
msgid "Send SMS Message"
msgstr ""

#. module: account_followup
#: model:ir.actions.act_window,name:account_followup.manual_reminder_action
msgid "Send and Print"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__sms
msgid "Sms"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__sms_body
msgid "Sms Body"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "Sms Content"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__sms_template_id
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__sms_template_id
msgid "Sms Template"
msgstr "SMS Şablonu"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__subject
msgid "Subject"
msgstr "Konu"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__activity_summary
msgid "Summary"
msgstr "Özet"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.table_header_template_followup_report
msgid "Table Header"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.line_template
msgid "Table Value"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_res_partner__followup_next_action_date
#: model:ir.model.fields,help:account_followup.field_res_users__followup_next_action_date
msgid ""
"The date before which no follow-up action should be taken.\n"
"                You can set it manually if desired but it is automatically set when follow-ups are processed.\n"
"                The date is computed according to the following rules (depending on the follow-up levels):\n"
"                - default -> next date set in {next level delay - current level delay} days\n"
"                - if no next level -> next date set in {current level delay - previous level delay} days\n"
"                - if no next level AND no previous level -> next date set in {current level delay} days\n"
"                - if no level defined at all -> next date never automatically set"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_account_move_line__invoice_origin
msgid "The document(s) that generated the invoice."
msgstr "Faturayı oluşturan belge(ler)."

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_account_followup_followup_line__delay
msgid ""
"The number of days after the due date of the invoice to wait before sending "
"the reminder. Can be negative if you want to send the reminder before the "
"invoice due date."
msgstr ""

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_res_partner__followup_responsible_id
#: model:ir.model.fields,help:account_followup.field_res_users__followup_responsible_id
msgid ""
"The responsible assigned to manual followup activities, if defined in the "
"level."
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_tree_view
msgid "Total"
msgstr "Toplam"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
#: model:ir.model.fields,field_description:account_followup.field_res_partner__total_due
#: model:ir.model.fields,field_description:account_followup.field_res_users__total_due
msgid "Total Due"
msgstr "Toplam Vade"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
#: model:ir.model.fields,field_description:account_followup.field_res_partner__total_overdue
#: model:ir.model.fields,field_description:account_followup.field_res_users__total_overdue
msgid "Total Overdue"
msgstr "Toplam Geciken"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__unpaid_invoice_ids
#: model:ir.model.fields,field_description:account_followup.field_res_users__unpaid_invoice_ids
msgid "Unpaid Invoice"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__unpaid_invoices_count
#: model:ir.model.fields,field_description:account_followup.field_res_users__unpaid_invoices_count
msgid "Unpaid Invoices Count"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__unreconciled_aml_ids
#: model:ir.model.fields,field_description:account_followup.field_res_users__unreconciled_aml_ids
msgid "Unreconciled Aml"
msgstr "Denkleştirilmemiş Aml"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.missing_information_view_form
msgid "View partners"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.missing_information_view_form
msgid ""
"We were not able to process some of the automated follow-up actions due to "
"missing information on the partners."
msgstr ""

#. module: account_followup
#: model:ir.model.fields.selection,name:account_followup.selection__res_partner__followup_status__with_overdue_invoices
msgid "With overdue invoices"
msgstr "Vadesi geçmiş faturalarla"

#. module: account_followup
#: model:ir.model,name:account_followup.model_account_followup_manual_reminder
msgid "Wizard for sending manual reminders to clients"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "Write your message here..."
msgstr "Mesajını buraya yaz..."

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
msgid ""
"You are trying to send an Email, but no follow-up contact has any email "
"address set for customer '%s'"
msgstr ""

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
msgid ""
"You are trying to send an SMS, but no follow-up contact has any mobile/phone"
" number set for customer '%s'"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "days after due date"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "e.g. First Reminder Email"
msgstr "Örneğin. İlk Hatırlatma E-postası"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
msgid "payment reminder"
msgstr "ödeme hatırlatıcısı"

#. module: account_followup
#: model:mail.template,subject:account_followup.demo_followup_email_template_2
#: model:mail.template,subject:account_followup.demo_followup_email_template_4
#: model:mail.template,subject:account_followup.email_template_followup_1
msgid ""
"{{ (object.company_id or object._get_followup_responsible().company_id).name"
" }} Payment Reminder - {{ object.commercial_company_name }}"
msgstr ""
"{{ (object.company_id or object._get_followup_responsible().company_id).name"
" }} Ödeme Hatırlatması - {{ object.commercial_company_name }}"
