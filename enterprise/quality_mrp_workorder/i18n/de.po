# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* quality_mrp_workorder
# 
# Translators:
# <PERSON><PERSON>, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:52+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.worksheet_page
msgid "<strong>Lot/Serial Number: </strong>"
msgstr "<strong>Los-/Seriennummer:</strong>"

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.worksheet_page
msgid "<strong>Work Order: </strong>"
msgstr "<strong>Arbeitsauftrag: </strong>"

#. module: quality_mrp_workorder
#: model:ir.model.fields,field_description:quality_mrp_workorder.field_mrp_production__check_ids
msgid "Checks"
msgstr "Prüfungen"

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_check_view_form_inherit_mrp_workorder
msgid "Component Lot/Serial"
msgstr "Los/Serie der Komponente"

#. module: quality_mrp_workorder
#. odoo-javascript
#: code:addons/quality_mrp_workorder/static/src/components/menuPopup.xml:0
msgid "Create a Quality Alert"
msgstr "Einen Qualitätsalarm erstellen"

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_alert_view_form_inherit_mrp
msgid "Discard"
msgstr "Verwerfen"

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.mrp_production_workorder_tree_editable_view_inherit_quality
msgid "Done"
msgstr "Erledigt"

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_check_view_form_tablet_inherit_quality
msgid "Fail"
msgstr "Fehlgeschlagen"

#. module: quality_mrp_workorder
#. odoo-python
#: code:addons/quality_mrp_workorder/models/quality.py:0
msgid "Failure"
msgstr "Fehlschlag"

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_check_view_search_inherit_quality_mrp_workorder
msgid "Finished Lot/Serial"
msgstr "Fertige(s) Los/Serie"

#. module: quality_mrp_workorder
#: model:ir.model,name:quality_mrp_workorder.model_stock_lot
msgid "Lot/Serial"
msgstr "Los/Serie"

#. module: quality_mrp_workorder
#: model:ir.model,name:quality_mrp_workorder.model_mrp_production
msgid "Manufacturing Order"
msgstr "Fertigungsauftrag"

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_check_view_search_inherit_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_point_view_search_inherit_mrp_workorder
msgid "Manufacturing Steps"
msgstr "Fertigungsschritte"

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_check_view_form_tablet_inherit_quality
msgid "Measure:"
msgstr "Wert:"

#. module: quality_mrp_workorder
#. odoo-javascript
#: code:addons/quality_mrp_workorder/static/src/mrp_display/mrp_quality_check_confirmation_dialog/mrp_quality_check_confirmation_dialog.xml:0
msgid "Open spreadsheet"
msgstr "Tabellenkalkulation öffnen"

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_alert_view_search_inherit_quality_mrp_workorder
msgid "Operation"
msgstr "Vorgang"

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_check_view_form_tablet_inherit_quality
msgid "Pass"
msgstr "Bestanden"

#. module: quality_mrp_workorder
#: model:ir.model,name:quality_mrp_workorder.model_product_template
msgid "Product"
msgstr "Produkt"

#. module: quality_mrp_workorder
#: model:ir.model,name:quality_mrp_workorder.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "Produktbewegungen (Lagerbuchung)"

#. module: quality_mrp_workorder
#: model:ir.model,name:quality_mrp_workorder.model_product_product
msgid "Product Variant"
msgstr "Produktvariante"

#. module: quality_mrp_workorder
#: model:ir.model,name:quality_mrp_workorder.model_quality_check
msgid "Quality Check"
msgstr "Qualitätsprüfung"

#. module: quality_mrp_workorder
#. odoo-python
#: code:addons/quality_mrp_workorder/models/quality.py:0
msgid "Quality Check Failed"
msgstr "Qualitätsprüfung fehlgeschlagen"

#. module: quality_mrp_workorder
#. odoo-javascript
#: code:addons/quality_mrp_workorder/static/src/mrp_display/mrp_display_record.xml:0
#: model:ir.actions.act_window,name:quality_mrp_workorder.quality_check_action_wo
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_check_view_search_inherit_mrp_workorder
msgid "Quality Checks"
msgstr "Qualitätsprüfungen"

#. module: quality_mrp_workorder
#: model:ir.model,name:quality_mrp_workorder.model_quality_point
msgid "Quality Control Point"
msgstr "Qualitätskontrollpunkt"

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_point_view_search_inherit_mrp_workorder
msgid "Quality Points"
msgstr "Qualitätspunkte"

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_alert_view_form_inherit_mrp
msgid "Save"
msgstr "Speichern"

#. module: quality_mrp_workorder
#: model:ir.model.fields,field_description:quality_mrp_workorder.field_quality_check__operation_id
msgid "Step"
msgstr "Schritt"

#. module: quality_mrp_workorder
#. odoo-python
#: code:addons/quality_mrp_workorder/models/quality.py:0
msgid "Success"
msgstr "Erfolgreich"

#. module: quality_mrp_workorder
#. odoo-python
#: code:addons/quality_mrp_workorder/models/quality.py:0
msgid ""
"The On-demand frequency is not possible with work order quality points."
msgstr ""
"Die Häufigkeit auf Anfrage ist bei Qualitätspunkten für Arbeitsaufträge "
"nicht möglich."

#. module: quality_mrp_workorder
#. odoo-python
#: code:addons/quality_mrp_workorder/models/quality.py:0
msgid ""
"The Quantity quality check type is not possible with manufacturing operation"
" types."
msgstr ""
"Der Qualitätsprüfungstyp „Menge“ ist nicht mit Fertigungsvorgangsarten "
"möglich."

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_check_view_form_tablet_inherit_quality
msgid "Validate"
msgstr "Validieren"

#. module: quality_mrp_workorder
#: model:ir.model,name:quality_mrp_workorder.model_mrp_workorder
msgid "Work Order"
msgstr "Arbeitsauftrag"

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_check_view_form_inherit_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_check_view_tree_inherit_mrp_workorder
msgid "Work Order Operation"
msgstr "Arbeitsauftragsvorgang"
