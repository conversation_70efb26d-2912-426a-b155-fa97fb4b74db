# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_account
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
msgid ""
"\n"
"Affected valuation layers: %s"
msgstr ""
"\n"
"طبقات التقييم المتأثرة: %s "

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
msgid " Product cost updated from %(previous)s to %(new_cost)s."
msgstr "تم تحديث تكلفة المنتج من %(previous)s إلى %(new_cost)s. "

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_quant.py:0
msgid " [Accounted on %s]"
msgstr " [تمت المحاسبة في %s] "

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
msgid " lot/serial number cost updated from %(previous)s to %(new_cost)s."
msgstr ""
"تم تحديث تكلفة رقم المجموعة/الرقم التسلسلي من %(previous)s إلى %(new_cost)s."
" "

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_valuation_layer.py:0
msgid "%(user)s changed cost from %(previous)s to %(new_price)s - %(record)s"
msgstr ""

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
msgid ""
"%(user)s changed stock valuation from  %(previous)s to %(new_value)s - %(product)s\n"
"%(reason)s"
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid ""
")\n"
"                            <small class=\"mx-2 fst-italic\">Use a negative added value to record a decrease in the product value</small>"
msgstr ""
")\n"
"                            <small class=\"mx-2 fst-italic\">استخدم قيمة سالبة مضافة لتسجيل انخفاض في قيمة المنتج</small>"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_account_report_invoice_document
msgid "6.00"
msgstr "6.00"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_category_property_form
msgid "<b>Set other input/output accounts on specific </b>"
msgstr "<b>قم بتعيين حسابات مدخلات/مخرجات أخرى في </b>"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_picking
#: model_terms:ir.ui.view,arch_db:stock_account.view_production_lot_form_stock_account
msgid "<span class=\"o_stat_text\">Valuation</span>"
msgstr "<span class=\"o_stat_text\">التقييم</span> "

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_account_report_invoice_document
msgid "<span>Product</span>"
msgstr "<span>المنتج</span> "

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_account_report_invoice_document
msgid "<span>Quantity</span>"
msgstr "<span>الكمية</span>"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_account_report_invoice_document
msgid "<span>SN/LN</span>"
msgstr "<span>الرقم التسلسلي/رقم الدفعة</span> "

#. module: stock_account
#: model:ir.model,name:stock_account.model_account_chart_template
msgid "Account Chart Template"
msgstr "نموذج مخطط الحساب "

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_move__account_move_ids
msgid "Account Move"
msgstr "حركة الحساب"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_category_property_form
msgid "Account Stock Properties"
msgstr "خصائص حساب المخزون"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_quant__accounting_date
#: model:ir.model.fields,field_description:stock_account.field_stock_request_count__accounting_date
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__date
msgid "Accounting Date"
msgstr "تاريخ المحاسبة"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_move_form_inherit
msgid "Accounting Entries"
msgstr "القيود المحاسبية"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_location_form_inherit
msgid "Accounting Information"
msgstr "المعلومات المحاسبية"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_tree
msgid "Add Manual Valuation"
msgstr "إضافة تقييم يدوي "

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.res_config_settings_view_form
msgid ""
"Add additional cost (transport, customs, ...) in the value of the product."
msgstr "قم بإضافة تكلفة إضافية (المواصلات، الجمارك، ...) إلى قيمة المنتج. "

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid "Added Value"
msgstr "القيمة المضافة "

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__added_value
msgid "Added value"
msgstr "القيمة المضافة "

#. module: stock_account
#: model:ir.actions.act_window,name:stock_account.action_revalue_layers
msgid "Adjust Valuation"
msgstr "تعديل تقييم المخزون "

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_res_config_settings__module_stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_account.res_config_settings_view_form
msgid ""
"Affect landed costs on reception operations and split them among products to"
" update their cost price."
msgstr ""
"قم بتطبيق التكاليف المُضافة على عمليات الاستلام وقسمها بين المنتجات لتحديث "
"سعر تكلفتها. "

#. module: stock_account
#: model:ir.model,name:stock_account.model_account_analytic_account
msgid "Analytic Account"
msgstr "الحساب التحليلي"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_move__analytic_account_line_ids
msgid "Analytic Account Line"
msgstr "بند الحساب التحليلي "

#. module: stock_account
#: model:ir.model,name:stock_account.model_account_analytic_plan
msgid "Analytic Plans"
msgstr "الخطط التحليلية "

#. module: stock_account
#: model:ir.model.fields.selection,name:stock_account.selection__product_category__property_valuation__real_time
msgid "Automated"
msgstr "مؤتمت "

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_res_config_settings__group_stock_accounting_automatic
msgid "Automatic Stock Accounting"
msgstr "المحاسبة التلقائية للمخزون "

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product__avg_cost
#: model:ir.model.fields,field_description:stock_account.field_stock_lot__avg_cost
msgid "Average Cost"
msgstr "متوسط التكلفة "

#. module: stock_account
#: model:ir.model.fields.selection,name:stock_account.selection__product_category__property_cost_method__average
msgid "Average Cost (AVCO)"
msgstr "متوسط التكلفة (AVCO) "

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_account_report_invoice_document
msgid "BC46282798"
msgstr "BC46282798"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_account_report_invoice_document
msgid "Bacon"
msgstr "اللحم المقدد "

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid "Cancel"
msgstr "إلغاء"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_move.py:0
msgid ""
"Cannot find a stock input account for the product %s. You must define one on"
" the product category, or on the location, before processing this operation."
msgstr ""
"لم نستطع إيجاد حساب مدخلات مخزون للمنتج %s. عليك تحديد حساب في فئة المنتج أو"
" في الموقع قبل معالجة هذه العملية. "

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_move.py:0
msgid ""
"Cannot find a stock output account for the product %s. You must define one "
"on the product category, or on the location, before processing this "
"operation."
msgstr ""
"لم نستطع إيجاد حساب مخرجات مخزون للمنتج %s. عليك تحديد حساب في فئة المنتج أو"
" في الموقع قبل معالجة هذه العملية. "

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
msgid ""
"Changing your cost method is an important change that will impact your "
"inventory valuation. Are you sure you want to make that change?"
msgstr ""
"تغيير طريقة حساب تكاليفك هو تغيير هام سيؤثر على تقييم مخزونك. هل أنت متأكد "
"أنك تريد القيام بهذا التغيير؟"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_quantity_history_inherit_stock_account
msgid "Choose a date to get the valuation at that date"
msgstr "اختر تاريخاً للحصول عى التقييم في ذلك التاريخ "

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_account_move_line__cogs_origin_id
msgid "Cogs Origin"
msgstr "أصل التروس "

#. module: stock_account
#: model:ir.model,name:stock_account.model_res_company
msgid "Companies"
msgstr "الشركات"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__company_id
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__company_id
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
msgid "Company"
msgstr "الشركة "

#. module: stock_account
#: model:ir.model,name:stock_account.model_res_config_settings
msgid "Config Settings"
msgstr "تهيئة الإعدادات "

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_move.py:0
msgid ""
"Configuration error. Please configure the price difference account on the "
"product or its category to process this operation."
msgstr ""
"خطأ في التهيئة. يرجى تهيئة حساب فرق السعر على المنتج أو فئته لمعالجة هذه "
"العملية. "

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_move.py:0
msgid "Correction of %s (modification of past move)"
msgstr "تصحيح %s (تعديل حركة سابقة) "

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_lot__standard_price
msgid "Cost"
msgstr "التكلفة"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category__property_cost_method
#: model:ir.model.fields,field_description:stock_account.field_product_product__cost_method
#: model:ir.model.fields,field_description:stock_account.field_product_template__cost_method
#: model:ir.model.fields,field_description:stock_account.field_stock_quant__cost_method
msgid "Costing Method"
msgstr "طريقة حساب التكاليف"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
msgid ""
"Costing method change for product category %(category)s: from %(old_method)s"
" to %(new_method)s."
msgstr ""
"تغيرت طريقة حساب التكاليف لفئة المنتج %(category)s: من %(old_method)s إلى "
"%(new_method)s. "

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__account_id
msgid "Counterpart Account"
msgstr "حساب القيد المناظر"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category__property_stock_account_input_categ_id
msgid ""
"Counterpart journal items for all incoming stock moves will be posted in this account, unless there is a specific valuation account\n"
"                set on the source location. This is the default value for all products in this category. It can also directly be set on each product."
msgstr ""
"عناصر دفتر يومية حساب القيد المناظر لكافة حركات المخزون الواردة سيتم ترحيلها في هذا الحساب، إلا إذا كان هناك حساب تقييم محدد\n"
"                معين في الموقع المصدري. هذه هي القيمة الافتراضية لكافة المنتجات في هذه الفئة. يمكن تعيينه مباشرة أيضاً في كل منتج. "

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_picking__country_code
msgid "Country Code"
msgstr "رمز الدولة"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__create_uid
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__create_date
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_quant__currency_id
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__currency_id
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__currency_id
msgid "Currency"
msgstr "العملة"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__current_quantity_svl
msgid "Current Quantity"
msgstr "الكمية الحالية "

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__current_value_svl
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid "Current Value"
msgstr "القيمة الحالية"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_form
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_tree
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
msgid "Date"
msgstr "التاريخ"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_quant__accounting_date
msgid ""
"Date at which the accounting entries will be created in case of automated "
"inventory valuation. If empty, the inventory date will be used."
msgstr ""
"تاريخ إنشاء القيود المحاسبية في حالة التقييم التلقائي للمخزون. إذا تُرك هذا "
"الحقل فارغاً، سيتم استخدام تاريخ المخزون. "

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_valuation_layer__uom_id
msgid "Default unit of measure used for all stock operations."
msgstr "وحدة القياس الافتراضية المستخدمة لكافة عمليات المخزون. "

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__description
msgid "Description"
msgstr "الوصف"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_res_config_settings__group_lot_on_invoice
msgid "Display Lots & Serial Numbers on Invoices"
msgstr "عرض أرقام الدفعات والأرقام التسلسلية في الفواتير "

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__display_name
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: stock_account
#: model:res.groups,name:stock_account.group_lot_on_invoice
msgid "Display Serial & Lot Number on Invoices"
msgstr "عرض الأرقام التسلسلية وأرقام الدفعات في الفواتير "

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
msgid ""
"Due to a change of product category (from %(old_category)s to "
"%(new_category)s), the costing method has changed for product %(product)s: "
"from %(old_method)s to %(new_method)s."
msgstr ""
"بسبب تغير فئة المنتج (من %(old_category)s إلى %(new_category)s)، تغيرت طريقة"
" حساب التكاليف للمنتج %(product)s: من %(old_method)s إلى %(new_method)s. "

#. module: stock_account
#: model:ir.model.fields.selection,name:stock_account.selection__product_category__property_cost_method__fifo
msgid "First In First Out (FIFO)"
msgstr "الوارد أولاً يخرج أولاً (FIFO) "

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
msgid "Group by..."
msgstr "التجميع حسب... "

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
msgid "Has Remaining Qty"
msgstr "يحتوي على كميات متبقية "

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__id
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__id
msgid "ID"
msgstr "المُعرف"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_product__lot_valuated
#: model:ir.model.fields,help:stock_account.field_product_template__lot_valuated
msgid "If checked, the valuation will be specific by Lot/Serial number."
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
msgid "Incoming"
msgstr "واردة "

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_location
msgid "Inventory Locations"
msgstr "مواقع المخزون"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/account_chart_template.py:0
#: model:ir.model.fields,field_description:stock_account.field_product_category__property_valuation
#: model:ir.model.fields,field_description:stock_account.field_product_product__valuation
#: model:ir.model.fields,field_description:stock_account.field_product_template__valuation
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__property_valuation
#: model_terms:ir.ui.view,arch_db:stock_account.view_category_property_form_stock
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
msgid "Inventory Valuation"
msgstr "تقييم المخزون"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__account_move_line_id
msgid "Invoice Line"
msgstr "بند الفاتورة"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__price_diff_value
msgid "Invoice value correction with invoice currency"
msgstr "تصحيح قيمة الفاتورة مع عملة الفاتورة "

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__account_journal_id
msgid "Journal"
msgstr "دفتر اليومية"

#. module: stock_account
#: model:ir.model,name:stock_account.model_account_move
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__account_move_id
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_tree
msgid "Journal Entry"
msgstr "قيد اليومية"

#. module: stock_account
#: model:ir.model,name:stock_account.model_account_move_line
msgid "Journal Item"
msgstr "عنصر اليومية"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_res_config_settings__module_stock_landed_costs
msgid "Landed Costs"
msgstr "إجمالي التكاليف "

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__write_uid
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__write_date
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__stock_valuation_layer_id
msgid "Linked To"
msgstr "مرتبط بـ"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
msgid ""
"Lot %(lot)s has a negative quantity in stock. Correct this"
"                         quantity before enabling lot valuation"
msgstr ""

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_lot.py:0
msgid "Lot value manually modified (from %(old)s to %(new)s)"
msgstr "تم تعديل قيمة المجموعة يدوياً (من %(old)s إلى %(new)s) "

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_lot
msgid "Lot/Serial"
msgstr "رقم الدفعة/الرقم التسلسلي "

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__lot_id
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
msgid "Lot/Serial Number"
msgstr "رقم المجموعة/الرقم التسلسلي "

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_lot.py:0
msgid "Lot/Serial number Revaluation"
msgstr "إعاجة تقييم رقم المجموعة/الرقم التسلسلي "

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_move_line.py:0
msgid "Lot/Serial number is mandatory for product valuated by lot"
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.res_config_settings_view_form
msgid "Lots & Serial numbers will appear on the invoice"
msgstr "ستظهر أرقام المجموعات والأرقام التسلسلية في الفاتورة "

#. module: stock_account
#: model:ir.model.fields.selection,name:stock_account.selection__product_category__property_valuation__manual_periodic
msgid "Manual"
msgstr "يدوي"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
msgid "Manual Stock Valuation: %s."
msgstr "تقييم المخزون يدوياً: %s. "

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category__property_valuation
#: model:ir.model.fields,help:stock_account.field_product_product__valuation
#: model:ir.model.fields,help:stock_account.field_product_template__valuation
#: model:ir.model.fields,help:stock_account.field_stock_valuation_layer_revaluation__property_valuation
msgid ""
"Manual: The accounting entries to value the inventory are not posted automatically.\n"
"        Automated: An accounting entry is automatically created to value the inventory when a product enters or leaves the company.\n"
"        "
msgstr ""
"يدوي: لا يتم ترحيل القيود المحاسبية المستخدمة لتقييم المخزون تلقائياً.\n"
"        آلي: يتم إنشاء قيد محاسبي تلقائياً لتقييم المخزون عندما يدخل منتج إلى الشركة أو يخرج منها.\n"
"        "

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__new_value
msgid "New value"
msgstr "قيمة جديدة "

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__new_value_by_qty
msgid "New value by quantity"
msgstr "القيمة الجديدة حسب الكمية "

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
msgid "No Reason Given"
msgstr "لا يوجد سبب "

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_form
msgid "Other Info"
msgstr "معلومات أخرى"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
msgid "Outgoing"
msgstr "الصادرة "

#. module: stock_account
#: model:ir.model,name:stock_account.model_product_template
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__product_id
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
msgid "Product"
msgstr "المنتج"

#. module: stock_account
#: model:ir.model,name:stock_account.model_product_category
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__categ_id
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
msgid "Product Category"
msgstr "فئة المنتج"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "تحركات المنتج (بنود حركة المخزون)"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid "Product Revaluation"
msgstr "إعادة تقييم المنتج "

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__product_tmpl_id
msgid "Product Template"
msgstr "قالب المنتج"

#. module: stock_account
#: model:ir.model,name:stock_account.model_product_product
msgid "Product Variant"
msgstr "متغير المنتج "

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
msgid ""
"Product value manually modified (from %(original_price)s to %(new_price)s)"
msgstr ""
"تم تعديل قيمة المنتج يدوياً (من %(original_price)s إلى %(new_price)s) "

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_res_company__account_production_wip_account_id
msgid "Production WIP Account"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_res_company__account_production_wip_overhead_account_id
msgid "Production WIP Overhead Account"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__quantity
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_tree
msgid "Quantity"
msgstr "الكمية"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product__quantity_svl
#: model:ir.model.fields,field_description:stock_account.field_stock_lot__quantity_svl
msgid "Quantity Svl"
msgstr "طبقة تقييم المخزون (Svl) للكمية "

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_quant
msgid "Quants"
msgstr "سجلات الكميات "

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__reason
msgid "Reason"
msgstr "السبب"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_valuation_layer_revaluation__reason
msgid "Reason of the revaluation"
msgstr "سبب إعادة التقييم "

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__warehouse_id
msgid "Receipt WH"
msgstr "مستودع الاستلام "

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__reference
msgid "Reference"
msgstr "الرقم المرجعي "

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__lot_id
msgid "Related lot/serial number"
msgstr "رقم المجموعة/الرقم التسلسلي ذو الصلة "

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__product_id
msgid "Related product"
msgstr "المنتج ذو الصلة "

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__remaining_qty
msgid "Remaining Qty"
msgstr "الكمية المتبقية"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__remaining_value
msgid "Remaining Value"
msgstr "القيمة المتبقية"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_return_picking_line
msgid "Return Picking Line"
msgstr "بند إرجاع الشحنة المنتقاة "

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
msgid "Revaluation of %s"
msgstr "إعادة تقييم %s "

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid "Revalue"
msgstr "إعادة تقييم "

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_lot.py:0
msgid "Select an existing lot/serial number to be reevaluated"
msgstr "قم بتحديد رقم مجموعة/رقم تسلسلي موجود لإعادة تقييمه "

#. module: stock_account
#: model:ir.model.fields.selection,name:stock_account.selection__product_category__property_cost_method__standard
msgid "Standard Price"
msgstr "السعر القياسي"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category__property_cost_method
#: model:ir.model.fields,help:stock_account.field_product_product__cost_method
#: model:ir.model.fields,help:stock_account.field_product_template__cost_method
#: model:ir.model.fields,help:stock_account.field_stock_quant__cost_method
msgid ""
"Standard Price: The products are valued at their standard cost defined on the product.\n"
"        Average Cost (AVCO): The products are valued at weighted average cost.\n"
"        First In First Out (FIFO): The products are valued supposing those that enter the company first will also leave it first.\n"
"        "
msgstr ""
"السعر القياسي: يتم تقييم المنتجات بتكاليفها القياسية المحددة في المنتج.\n"
"        متوسط التكلفة (AVCO): يتم تقييم المنتجات حسب التكلفة المتوسطة الموزونة.\n"
"        الوارد أولاً يخرج أولاً (FIFO): يتم تقييم المنتجات بافتراض أن المنتج الذي يدخل الشركة أولاً سيخرج منها أولاً.\n"
"        "

#. module: stock_account
#: model:res.groups,name:stock_account.group_stock_accounting_automatic
msgid "Stock Accounting Automatic"
msgstr "المحاسبة التلقائية للمخزون "

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category__property_stock_account_input_categ_id
msgid "Stock Input Account"
msgstr "حساب مدخلات المخزون "

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category__property_stock_journal
msgid "Stock Journal"
msgstr "دفتر يومية المخزون"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_move
#: model:ir.model.fields,field_description:stock_account.field_account_bank_statement_line__stock_move_id
#: model:ir.model.fields,field_description:stock_account.field_account_move__stock_move_id
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__stock_move_id
msgid "Stock Move"
msgstr "حركة المخزون"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category__property_stock_account_output_categ_id
msgid "Stock Output Account"
msgstr "حساب مخرجات المخزون "

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_quantity_history
msgid "Stock Quantity History"
msgstr "سجل كمية المخزون"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_forecasted_product_product
msgid "Stock Replenishment Report"
msgstr "تقرير تجديد المخزون "

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_request_count
msgid "Stock Request an Inventory Count"
msgstr "طلب تعداد المخزون "

#. module: stock_account
#. odoo-javascript
#: code:addons/stock_account/static/src/stock_account_forecasted/forecasted_header.js:0
#: model:ir.actions.act_window,name:stock_account.stock_valuation_layer_action
#: model:ir.actions.act_window,name:stock_account.stock_valuation_layer_report_action
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_quant_tree_editable_inherit
msgid "Stock Valuation"
msgstr "تقييم المخزون"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category__property_stock_valuation_account_id
msgid "Stock Valuation Account"
msgstr "حساب تقييم المخزون"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_location__valuation_in_account_id
msgid "Stock Valuation Account (Incoming)"
msgstr "حساب تقييم المخزون (الوارد)"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_location__valuation_out_account_id
msgid "Stock Valuation Account (Outgoing)"
msgstr "حساب تقييم المخزون (الصادر)"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_valuation_layer
#: model:ir.model.fields,field_description:stock_account.field_account_bank_statement_line__stock_valuation_layer_ids
#: model:ir.model.fields,field_description:stock_account.field_account_move__stock_valuation_layer_ids
#: model:ir.model.fields,field_description:stock_account.field_account_move_line__stock_valuation_layer_ids
#: model:ir.model.fields,field_description:stock_account.field_product_product__stock_valuation_layer_ids
#: model:ir.model.fields,field_description:stock_account.field_stock_lot__stock_valuation_layer_ids
#: model:ir.model.fields,field_description:stock_account.field_stock_move__stock_valuation_layer_ids
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__stock_valuation_layer_ids
msgid "Stock Valuation Layer"
msgstr "طبقة تقييم المخزون "

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_product__company_currency_id
msgid ""
"Technical field to correctly show the currently selected company's currency "
"that corresponds to the totaled value of the product's valuation layers"
msgstr ""
"حقل تقني لإظهار عملة الشركة المحددة حالياً والتي تتوافق مع إجمالي قيمة طبقات"
" تقييم المنتج "

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_picking__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"كود الدولة حسب المعيار الدولي أيزو المكون من حرفين.\n"
"يمكنك استخدام هذا الحقل لإجراء بحث سريع."

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
msgid ""
"The Stock Input and/or Output accounts cannot be the same as the Stock "
"Valuation account."
msgstr ""
"لا يمكن أن تكون حسابات مدخلات و/أو مخرجات المخزون مطابقة لحساب تقييم "
"المخزون. "

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
msgid ""
"The action leads to the creation of a journal entry, for which you don't "
"have the access rights."
msgstr "يؤدي الإجراء إلى إنشاء قيد يومية، ولا تمك حق الوصول إليه. "

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
msgid "The added value doesn't have any impact on the stock valuation"
msgstr "ليس للقيمة المضافة أي تأثير على تقييم المخزون "

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_move.py:0
msgid ""
"The move lines are not in a consistent state: some are entering and other "
"are leaving the company."
msgstr ""
"بنود الحركات ليست متسقة: بعض البنود واردة للشركة وبعضها صادرة من الشركة. "

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_move.py:0
msgid ""
"The move lines are not in a consistent states: they are doing an "
"intercompany in a single step while they should go through the intercompany "
"transit location."
msgstr ""
"بنود الحركات ليست متسقة: تقوم البنود بإجراءات ما بين الشركات في خطوة واحدة "
"بينما يجب أن تمر عبر موقع التبادل بين الشركات. "

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_move.py:0
msgid ""
"The move lines are not in a consistent states: they do not share the same "
"origin or destination company."
msgstr ""
"بنود الحركات ليست متسقة: ليس  للبنود نفس الشركة المصدر أو الشركة الوجهة. "

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
msgid ""
"The stock accounts should be set in order to use the automatic valuation."
msgstr ""

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_move_line.py:0
msgid ""
"The stock valuation of a move is based on the type of the source and "
"destination locations. As the move is already processed, you cannot modify "
"the locations in a way that changes the valuation logic defined during the "
"initial processing."
msgstr ""
"يعتمد تقييم المخزون الخاص بالحركة على نوع الموقعين المصدر والوجهة. وبما أن "
"الحركة قد تمت معالجتها بالفعل، لا يمكنك تعديل المواقع بطريقة تغير منطق "
"التقييم الذي تم تحديده أثناء المعالجة الأولية. "

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
msgid ""
"The value of a stock valuation layer cannot be negative. Landed cost could "
"be use to correct a specific transfer."
msgstr ""
"لا يمكن أن يكون لطبقة تقييم المخزون قيمة سالبة. يجب استخدام إجمالي التكاليف "
"لتصحيح تحويل محدد. "

#. module: stock_account
#: model_terms:ir.actions.act_window,help:stock_account.stock_valuation_layer_action
#: model_terms:ir.actions.act_window,help:stock_account.stock_valuation_layer_report_action
msgid ""
"There are no valuation layers. Valuation layers are created when there are "
"product moves that impact the valuation of the stock."
msgstr ""
"لا توجد طبقات تقييم. يتم إنشاء طبقات التقييم عندما يكون لبعض حركات المنتج "
"تأثير على تقييم المخزون. "

#. module: stock_account
#. odoo-javascript
#: code:addons/stock_account/static/src/fields/boolean_confirm.js:0
msgid ""
"This operation might lead in a loss of data. Valuation will be identical for"
" all lots/SN. Do you want to proceed ? "
msgstr ""

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
msgid ""
"This product is valuated by lot/serial number. Changing the cost will update"
" the cost of every lot/serial number in stock."
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_tree
msgid "Total Moved Quantity"
msgstr "إجمالي الكمية التي تم نقلها "

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_report_tree
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_tree
msgid "Total Remaining Quantity"
msgstr "إجمالي الكمية المتبقية "

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_report_tree
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_tree
msgid "Total Remaining Value"
msgstr "إجمالي القيمة المتبقية "

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product__total_value
#: model:ir.model.fields,field_description:stock_account.field_stock_lot__total_value
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__value
#: model_terms:ir.ui.view,arch_db:stock_account.product_product_stock_tree_inherit_stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_tree
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_quant_tree_editable_inherit
msgid "Total Value"
msgstr "القيمة الكلية"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_picking
msgid "Transfer"
msgstr "تحويل "

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_move__to_refund
#: model:ir.model.fields,help:stock_account.field_stock_return_picking_line__to_refund
msgid ""
"Trigger a decrease of the delivered/received quantity in the associated Sale"
" Order/Purchase Order"
msgstr ""
"تشغيل تخفيض الكمية التي تم إيصالها/الكمية المستلمة في أمر البيع/الشراء "
"المرتبط "

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_tree
msgid "Unit"
msgstr "الوحدة"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.product_product_stock_tree_inherit_stock_account
msgid "Unit Cost"
msgstr "تكلفة الوحدة"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__unit_cost
msgid "Unit Value"
msgstr "قيمة الوحدة "

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__uom_id
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__product_uom_name
msgid "Unit of Measure"
msgstr "وحدة القياس"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_move__to_refund
#: model:ir.model.fields,field_description:stock_account.field_stock_return_picking_line__to_refund
msgid "Update quantities on SO/PO"
msgstr "تحديث الكميات في أمر البيع/أمر الشراء  "

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
msgid "Updating lot valuation for product %s."
msgstr "جاري تحديث تقييم المجموعة للمنتج %s. "

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_location__valuation_in_account_id
msgid ""
"Used for real-time inventory valuation. When set on a virtual location (non "
"internal type), this account will be used to hold the value of products "
"being moved from an internal location into this location, instead of the "
"generic Stock Output Account set on the product. This has no effect for "
"internal locations."
msgstr ""
"يُستخدم لتقييم المخزون في الوقت الفعلي. عند تعيينه في موقع افتراضي (نوع غير "
"داخلي)، سيُستخدم هذا الحساب لحفظ قيمة المنتجات التي يتم نقلها من موقع داخلي "
"إلى هذا الموقع، عوضاً عن حساب مخرجات المخزون العام المعين في المنتج. لا يؤثر"
" ذلك على المواقع الداخلية. "

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_location__valuation_out_account_id
msgid ""
"Used for real-time inventory valuation. When set on a virtual location (non "
"internal type), this account will be used to hold the value of products "
"being moved out of this location and into an internal location, instead of "
"the generic Stock Output Account set on the product. This has no effect for "
"internal locations."
msgstr ""
"يُستخدم لتقييم المخزون في الوقت الفعلي. عند تعيينه في موقع افتراضي (نوع غير "
"داخلي)، سيُستخدم هذا الحساب لحفظ قيمة المنتجات التي يتم نقلها خارج هذا "
"الموقع إلى موقع داخلي، عوضاً عن حساب مخرجات المخزون العام المعين في المنتج. "
"لا يؤثر ذلك على المواقع الداخلية. "

#. module: stock_account
#: model:ir.ui.menu,name:stock_account.menu_valuation
#: model_terms:ir.ui.view,arch_db:stock_account.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_form
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_quant_tree_editable_inherit
msgid "Valuation"
msgstr "التقييم "

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product__company_currency_id
#: model:ir.model.fields,field_description:stock_account.field_stock_lot__company_currency_id
msgid "Valuation Currency"
msgstr "عملة التقييم "

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__adjusted_layer_ids
msgid "Valuation Layers"
msgstr "طبقات تعديل المخزون "

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.product_product_stock_tree_inherit_stock_account
msgid "Valuation Report"
msgstr "تقرير التقييم "

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_tree
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_quantity_history_inherit_stock_account
msgid "Valuation at Date"
msgstr "القييم بتاريخ "

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product__lot_valuated
#: model:ir.model.fields,field_description:stock_account.field_product_template__lot_valuated
msgid "Valuation by Lot/Serial number"
msgstr "التقييم حسب رقم المجموعة / الرقم التسلسلي "

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
msgid ""
"Valuation method change for product category %(category)s: from "
"%(old_method)s to %(new_method)s."
msgstr ""
"تغيرت طريقة التقييم لفئة المنتج %(category)s: من %(old_method)s إلى "
"%(new_method)s. "

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_valuation_layer_revaluation__adjusted_layer_ids
msgid "Valuations layers being adjusted"
msgstr "يتم تعديل طبقات التقييم "

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_quant__value
msgid "Value"
msgstr "القيمة"

#. module: stock_account
#. odoo-javascript
#: code:addons/stock_account/static/src/stock_account_forecasted/forecasted_header.xml:0
msgid "Value On Hand:"
msgstr "القيمة في اليد: "

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product__value_svl
#: model:ir.model.fields,field_description:stock_account.field_stock_lot__value_svl
msgid "Value Svl"
msgstr "طبقة تقييم المخزون (Svl) للقيمة "

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_lot__standard_price
msgid ""
"Value of the lot (automatically computed in AVCO).\n"
"        Used to value the product when the purchase cost is not known (e.g. inventory adjustment).\n"
"        Used to compute margins on sale orders."
msgstr ""

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
msgid "Warning"
msgstr "تحذير"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category__property_stock_valuation_account_id
msgid ""
"When automated inventory valuation is enabled on a product, this account "
"will hold the current value of the products."
msgstr ""
"عند تمكين تقييم المخزون المؤتمت لمنتج ما، سيحتوي  هذا الحساب على القيمة "
"الحالية للمنتجات. "

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category__property_stock_account_output_categ_id
msgid ""
"When doing automated inventory valuation, counterpart journal items for all outgoing stock moves will be posted in this account,\n"
"                unless there is a specific valuation account set on the destination location. This is the default value for all products in this category.\n"
"                It can also directly be set on each product."
msgstr ""
"عند إجراء تقييم مؤتمت للمخزون، عناصر دفتر يومية حساب القيد المناظر لكافة حركات المخزون الصادرة سيتم ترحيلها في هذا الحساب،\n"
"                إلا إذا كان هناك حساب تقييم محدد معين في الموقع الوجهة. هذه هي القيمة الافتراضية لكافة المنتجات في هذه الفئة.\n"
"                يمكن تعيينها مباشرة أيضاً في كل منتج. "

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category__property_stock_journal
msgid ""
"When doing automated inventory valuation, this is the Accounting Journal in "
"which entries will be automatically posted when stock moves are processed."
msgstr ""
"عند إجراء تقييم مؤتمت للمخزون، هذا هو دفتر اليومية المحاسبي الذي سيتم ترحيل "
"القيود فيه عند معالجة حركات المخزون. "

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_valuation_layer_revaluation
msgid "Wizard model to reavaluate a stock inventory for a product"
msgstr "نموذج المعالج لإعادة تقييم بضاعة المخزون لمنتج ما "

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
msgid "You cannot adjust the valuation of a layer with zero quantity"
msgstr ""

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
msgid "You cannot adjust valuation without a product"
msgstr "لا يمكنك تعديل التقييم دون وجود منتج "

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
msgid ""
"You cannot change the costing method of product valuated by lot/serial "
"number."
msgstr ""

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
msgid ""
"You cannot change the product category of a product valuated by lot/serial "
"number."
msgstr ""

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
msgid "You cannot revalue a product with a standard cost method."
msgstr "لا يمكنك إعادة تحديد قيمة منتج له طريقة تكلفة قياسية. "

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
msgid "You cannot revalue a product with an empty or negative stock."
msgstr "لا يمكنك إعادة تحديد قيمة منتج ذو مخزون فارغ أو كميته سالبة. "

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
msgid "You cannot revalue multiple products at once"
msgstr "لا يمكنك إعادة تقييم عدو منتجات في آن واحد "

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
#: code:addons/stock_account/models/stock_lot.py:0
msgid ""
"You cannot update the cost of a product in automated valuation as it leads "
"to the creation of a journal entry, for which you don't have the access "
"rights."
msgstr ""
"لا يمكنك تحديث تكلفة منتج في التقييم المؤتمت، حيث أن ذلك يؤدي إلى إنشاء قيد "
"يومية، ولا تملك حق الوصول لذلك. "

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
msgid ""
"You don't have any input valuation account defined on your product category."
" You must define one before processing this operation."
msgstr ""
"لا تملك أي حساب تقييم المدخلات في فئة منتجك. عليك تحديد واحد قبل معالجة هذه "
"العملية. "

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
msgid ""
"You don't have any output valuation account defined on your product "
"category. You must define one before processing this operation."
msgstr ""
"لا تملك أي حساب لتقييم المخرجات في فئة منتجك. عليك تحديد واحد قبل معالجة هذه"
" العملية. "

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
msgid ""
"You don't have any stock input account defined on your product category. You"
" must define one before processing this operation."
msgstr ""
"لا تملك أي حساب تقييم لمدخلات المخزون في فئة منتجك. عليك تحديد واحد قبل "
"معالجة هذه العملية. "

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_move.py:0
msgid ""
"You don't have any stock journal defined on your product category, check if "
"you have installed a chart of accounts."
msgstr ""
"لا توجد أية قيود مخزون يومية معرفة لفئة منتجك، تأكد من تثبيتك لمخطط "
"الحسابات. "

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
#: code:addons/stock_account/models/stock_move.py:0
#: code:addons/stock_account/models/stock_valuation_layer.py:0
msgid ""
"You don't have any stock valuation account defined on your product category."
" You must define one before processing this operation."
msgstr ""
"لا يوجد حساب لتقييم المخزون محدد لفئة منتجك. عليك تحديد واحد قبل معالجة هذه "
"العملية. "

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_valuation_layer.py:0
msgid "You must set a counterpart account on your product category."
msgstr "عليك تعيين حساب قيد مقابل في فئة منتجك. "

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid "by"
msgstr "بواسطة"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid "for"
msgstr "لـ "

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_category_property_form
msgid "locations"
msgstr "المواقع "

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_account_report_invoice_document
msgid "units"
msgstr "وحدات "

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_graph
msgid "valuation graph"
msgstr "الرسم البياني للتقييم "
