# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_subscription
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:51+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"\n"
"- You are trying to invoice recurring orders. Please verify the delivered quantity of product based on invoicing policy, next invoice date and end date of the contract."
msgstr ""
"\n"
"- 您正在嘗試為定期訂單開立發票。請根據發票政策、下次發票日期及合約結束日期，核實產品的交付數量。"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__nbr
msgid "# of Lines"
msgstr "資料行數目"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__percentage_satisfaction
msgid "% Happy"
msgstr "% 高興"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/product.py:0
msgid "%(price)s %(billing_period_display_sentence)s"
msgstr "%(price)s %(billing_period_display_sentence)s"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order_line.py:0
msgid "%(start)s to %(next)s"
msgstr "%(start)s 至 %(next)s"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order_line.py:0
#: code:addons/sale_subscription/models/sale_subscription_plan.py:0
msgid "%s days"
msgstr "%s 天"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order_line.py:0
msgid ""
"(*) These recurring products are discounted according to the prorated period"
" from %(start)s to %(end)s"
msgstr "* 這些經常性產品的折扣，是基於以下期間按比例計算：%(start)s 至 %(end)s"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order_line.py:0
msgid ""
"(*) These recurring products are entirely discounted as the next period has "
"not been invoiced yet."
msgstr "* 這些經常性產品已全數折扣，因為下一期尚未開立發票。"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "(Change Plan)"
msgstr "（更改計劃）"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "- You delivered %(delivered)s %(name)s and invoiced %(invoiced)s"
msgstr "- 你已交付 %(delivered)s %(name)s 及已為 %(invoiced)s 開立發票"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__mrr_change_period__1month
msgid "1 Month"
msgstr "1 個月"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__mrr_change_period__3months
msgid "3 Months"
msgstr "3 個月"

#. module: sale_subscription
#: model:sale.subscription.plan,name:sale_subscription.subscription_plan_6_month
msgid "6 Months"
msgstr "6 個月"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_sidebar
msgid "<b>Payment Method</b>"
msgstr "<b>付款方式</b>"

#. module: sale_subscription
#: model:mail.template,body_html:sale_subscription.mail_template_subscription_alert
msgid ""
"<div style=\"background:#F0F0F0;color:#515166;padding:10px 0px;font-family:Arial,Helvetica,sans-serif;font-size:14px;\">\n"
"                <table style=\"width:600px;margin:5px auto;\">\n"
"                    <tbody>\n"
"                        <tr><td t-if=\"not object.company_id.uses_default_logo\">\n"
"                            <a href=\"/\"><img t-attf-src=\"/logo.png?company={{ object.company_id.id }}\" style=\"vertical-align:baseline;max-width:100px;\"/></a>\n"
"                        </td><td style=\"text-align:right;vertical-align:middle;\">\n"
"                                Subscription Renewal\n"
"                        </td></tr>\n"
"                    </tbody>\n"
"                </table>\n"
"                <table style=\"width:600px;margin:0px auto;background:white;border:1px solid #e1e1e1;\">\n"
"                    <tbody>\n"
"                        <tr><td style=\"padding:15px 20px 10px 20px;\">\n"
"                            <p>Dear <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,</p>\n"
"                            <p>Your subscription <strong t-out=\"object.name or ''\">Office Cleaning Service\"</strong> needs your attention.</p>\n"
"                            <p>We invite you to renew it by clicking on the following link.</p>\n"
"                            <p>Kind regards.</p>\n"
"                        </td></tr>\n"
"                        <tr data-o-mail-quote-container=\"1\"><td style=\"padding:15px 20px 10px 20px;\" t-out=\"object.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</td></tr>\n"
"                    </tbody>\n"
"                </table>\n"
"            </div>\n"
"        "
msgstr ""

#. module: sale_subscription
#: model:mail.template,body_html:sale_subscription.mail_template_subscription_rating
msgid ""
"<div style=\"background:#F0F0F0;color:#515166;padding:10px 0px;font-family:Arial,Helvetica,sans-serif;font-size:14px;\">\n"
"            <t t-set=\"access_token\" t-value=\"object._rating_get_access_token()\"/>\n"
"                <table style=\"width:600px;margin:5px auto;\">\n"
"                    <tbody>\n"
"                        <tr><td t-if=\"not object.company_id.uses_default_logo\">\n"
"                            <a href=\"/\"><img t-attf-src=\"/logo.png?company={{ object.company_id.id }}\" style=\"vertical-align:baseline;max-width:100px;\"/></a>\n"
"                        </td><td style=\"text-align:right;vertical-align:middle;\">\n"
"                                Satisfaction Survey\n"
"                        </td></tr>\n"
"                    </tbody>\n"
"                </table>\n"
"                <table style=\"width:600px;margin:0px auto;background:white;border:1px solid #e1e1e1;\">\n"
"                    <tbody>\n"
"                        <tr><td style=\"padding:15px 20px 10px 20px;\">\n"
"                            <p>Hello,</p>\n"
"                            <p>Please take a moment to rate our services related to your subscription \"<strong t-out=\"object.name or ''\">Office Cleaning Service\"</strong>\"\n"
"                               assigned to <strong t-out=\"object._rating_get_operator().name or ''\">Mitchell Admin</strong>.</p>\n"
"                            <p>We appreciate your feedback. It helps us to improve continuously.</p>\n"
"                        </td></tr>\n"
"                        <tr><td style=\"padding:10px 20px\">\n"
"                            <table summary=\"o_mail_notification\" style=\"width:100%;border-top:1px solid #e1e1e1;\">\n"
"                                <tr>\n"
"                                    <td style=\"text-align:center;\">\n"
"                                        <h2 style=\"font-weight:300;font-size:18px;\">\n"
"                                            Tell us how you feel about our services:\n"
"                                        </h2>\n"
"                                        <div style=\"text-color: #888888\">(click on one of these smileys)</div>\n"
"                                    </td>\n"
"                                </tr>\n"
"                                <tr>\n"
"                                    <td style=\"padding:10px 10px;\">\n"
"                                        <table style=\"width:100%;text-align:center;\">\n"
"                                            <tr>\n"
"                                                <td>\n"
"                                                    <a t-attf-href=\"/rate/{{ access_token }}/5\">\n"
"                                                        <img alt=\"Satisfied\" src=\"/rating/static/src/img/rating_5.png\" title=\"Satisfied\"/>\n"
"                                                    </a>\n"
"                                                </td>\n"
"                                                <td>\n"
"                                                    <a t-attf-href=\"/rate/{{ access_token }}/3\">\n"
"                                                        <img alt=\"Okay\" src=\"/rating/static/src/img/rating_3.png\" title=\"Okay\"/>\n"
"                                                    </a>\n"
"                                                </td>\n"
"                                                <td>\n"
"                                                    <a t-attf-href=\"/rate/{{ access_token }}/1\">\n"
"                                                        <img alt=\"Dissatisfied\" src=\"/rating/static/src/img/rating_1.png\" title=\"Dissatisfied\"/>\n"
"                                                    </a>\n"
"                                                </td>\n"
"                                            </tr>\n"
"                                        </table>\n"
"                                    </td>\n"
"                                </tr>\n"
"                            </table>\n"
"                        </td></tr>\n"
"                        <tr data-o-mail-quote-container=\"1\"><td style=\"padding:15px 20px 10px 20px;\" t-out=\"object.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</td></tr>\n"
"                    </tbody>\n"
"                </table>\n"
"                <table style=\"width:600px;margin:auto;text-align:center;font-size:12px;\">\n"
"                    <tbody>\n"
"                        <tr><td style=\"padding-top:10px;color:#afafaf;\">\n"
"                            <p>Email automatically sent by <a target=\"_blank\" href=\"https://www.odoo.com/app/subscriptions\" style=\"color:#875A7B;text-decoration:none;\">Odoo Subscription</a> for <a t-att-href=\"object.company_id.website\" style=\"color:#875A7B;text-decoration:none;\" t-out=\"object.company_id.name or ''\">YourCompany</a></p>\n"
"                        </td></tr>\n"
"                    </tbody>\n"
"                </table>\n"
"            </div>\n"
"        "
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid ""
"<em class=\"text-muted\" colspan=\"2\" invisible=\"not id\">\n"
"                                Action data can not be updated to avoid unexpected behaviors. Create a new action instead.\n"
"                            </em>"
msgstr ""
"<em class=\"text-muted\" colspan=\"2\" invisible=\"not id\">\n"
"                                未能更新操作數據以避免意外行為。請改為建立一個新動作。\n"
"                            </em>"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_base_automation_form
msgid ""
"<em class=\"text-muted\">\n"
"                        Action data can not be updated to avoid unexpected behaviors. Create a new automation rule instead.\n"
"                    </em>"
msgstr ""
"<em class=\"text-muted\">\n"
"                        操作數據無法更新，以避免意外行為。請改為建立新的自動化規則。\n"
"                    </em>"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_sidebar
msgid "<i class=\"fa fa-download me-1\"/> Download"
msgstr "<i class=\"fa fa-download me-1\"/> 下載"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "<i class=\"fa fa-fw fa-check\"/> In Progress"
msgstr "<i class=\"fa fa-fw fa-check\"/> 進行中"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "<i class=\"fa fa-fw fa-check\"/> Paused"
msgstr "<i class=\"fa fa-fw fa-check\"/> 已暫停"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "<i class=\"fa fa-fw fa-check\"/> Renewed"
msgstr "<i class=\"fa fa-fw fa-check\"/> 已續訂"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "<i class=\"fa fa-fw fa-remove\"/> Closed"
msgstr "<i class=\"fa fa-fw fa-remove\"/> 已關閉"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "<i class=\"fa fa-fw fa-remove\"/> Closing"
msgstr "<i class=\"fa fa-fw fa-remove\"/> 結算"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "<i class=\"fa fa-fw fa-repeat\"/> To Renew"
msgstr "<i class=\"fa fa-fw fa-repeat\"/> 需續期"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_so_portal_template
msgid "<i class=\"oi oi-arrow-right me-1\"/>Back to your subscription"
msgstr "<i class=\"oi oi-arrow-right me-1\"/> 返回你的定期訂購"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_template
msgid "<option value=\"\">Choose a reason...</option>"
msgstr "<option value=\"\">選擇一個原因⋯</option>"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.payment_method_form
#: model_terms:ir.ui.view,arch_db:sale_subscription.payment_token_form
msgid ""
"<small class=\"text-600\">Automate payments for the linked "
"subscriptions</small>"
msgstr "<small class=\"text-600\">自動支付已連結定期訂購計劃的費用</small>"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.portal_my_subscriptions
msgid ""
"<span class=\"badge rounded-pill text-bg-danger\"><i class=\"fa fa-fw fa-"
"repeat\"/> To Renew</span>"
msgstr ""
"<span class=\"badge rounded-pill text-bg-danger\"><i class=\"fa fa-fw fa-"
"repeat\"/> 需續期</span>"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.portal_my_subscriptions
msgid ""
"<span class=\"badge rounded-pill text-bg-dark\"><i class=\"fa fa-fw fa-"
"remove\"/> Closed</span>"
msgstr ""
"<span class=\"badge rounded-pill text-bg-dark\"><i class=\"fa fa-fw fa-"
"remove\"/> 已關閉</span>"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.portal_my_subscriptions
msgid "<span class=\"badge rounded-pill text-bg-info\"> Renewal Quotation </span>"
msgstr "<span class=\"badge rounded-pill text-bg-info\"> 續訂報價</span>"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.portal_my_subscriptions
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_portal_content_inherit
msgid ""
"<span class=\"badge rounded-pill text-bg-primary\"><i class=\"fa fa-fw fa-"
"check\"/> Upsell</span>"
msgstr ""
"<span class=\"badge rounded-pill text-bg-primary\"><i class=\"fa fa-fw fa-"
"check\"/> 追加銷售</span>"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.portal_my_subscriptions
msgid ""
"<span class=\"badge rounded-pill text-bg-secondary\"><i class=\"fa fa-fw fa-"
"check\"/> Renewed</span>"
msgstr ""
"<span class=\"badge rounded-pill text-bg-secondary\"><i class=\"fa fa-fw fa-"
"check\"/> 已續訂</span>"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.portal_my_subscriptions
msgid ""
"<span class=\"badge rounded-pill text-bg-success\"><i class=\"fa fa-fw fa-"
"check\"/> In Progress</span>"
msgstr ""
"<span class=\"badge rounded-pill text-bg-success\"><i class=\"fa fa-fw fa-"
"check\"/> 進行中</span>"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.portal_my_subscriptions
msgid ""
"<span class=\"badge rounded-pill text-bg-success\"><i class=\"fa fa-fw fa-"
"check\"/> Paused</span>"
msgstr ""
"<span class=\"badge rounded-pill text-bg-success\"><i class=\"fa fa-fw fa-"
"check\"/> 已暫停</span>"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
msgid "<span class=\"badge rounded-pill text-bg-warning\">Closing</span>"
msgstr "<span class=\"badge rounded-pill text-bg-warning\">結算</span>"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
msgid "<span class=\"o_stat_text\">MRR</span>"
msgstr "<span class=\"o_stat_text\">每月經常收入（MRR）</span>"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_plan_view_form
msgid "<span class=\"o_stat_text\">Subscription Items</span>"
msgstr "<span class=\"o_stat_text\">訂閱項目</span>"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_plan_view_form
msgid "<span class=\"o_stat_text\">Subscriptions</span>"
msgstr "<span class=\"o_stat_text\">定期訂購</span>"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_sidebar
msgid "<span class=\"text-muted\">Next Billing</span>"
msgstr "<span class=\"text-muted\">下期賬單</span>"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.recurring_details
msgid "<span class=\"text-start\">Non Recurring</span>:"
msgstr "<span class=\"text-start\">非重複性</span>："

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.recurring_details
msgid "<span class=\"text-start\">Recurring</span>:"
msgstr "<span class=\"text-start\">經常性</span>："

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
msgid ""
"<span invisible=\"not plan_id or subscription_state == "
"'7_upsell'\">until</span>"
msgstr ""
"<span invisible=\"not plan_id or subscription_state == "
"'7_upsell'\">直至</span>"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid ""
"<span>\n"
"                                There is already a pending payment for this subscription.\n"
"                            </span>"
msgstr ""
"<span>\n"
"                                此定期訂購計劃已有一項正待完成的付款。\n"
"                            </span>"

#. module: sale_subscription
#: model_terms:web_tour.tour,rainbow_man_message:sale_subscription.sale_subscription_tour
msgid ""
"<span><b>Congratulations</b>, your first subscription quotation is ready to be sent!\n"
"        </span>"
msgstr ""
"<span><b>恭喜！</b>你的首份訂閱報價單，已準備好發送！\n"
"        </span>"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "<strong invisible=\"not rating_operator\">%</strong>"
msgstr "<strong invisible=\"not rating_operator\">%</strong>"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "<strong> and </strong>"
msgstr "<strong> 和 </strong>"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.report_saleorder_document
msgid "<strong>Recurring Plan</strong>"
msgstr "<strong>定期計劃</strong>"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.report_saleorder_document
msgid "<strong>Start Date</strong>"
msgstr "<strong>開始日期</strong>"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_close_reason_view_form
msgid ""
"<strong>Warning:</strong> the survey can only be shown if all information is"
" set. Please complete:"
msgstr "<strong>警告：</strong>只有在設定所有資訊後，才會顯示調查。請完成填寫："

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "<strong>subscriptions</strong>"
msgstr "<strong>定期訂購計劃</strong>"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "<strong>to</strong>"
msgstr "<strong>到</strong>"

#. module: sale_subscription
#: model:mail.template,body_html:sale_subscription.email_payment_close
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <t t-set=\"company\" t-value=\"object.company_id or object.user_id.company_id or user.company_id\"/>\n"
"                    <span style=\"font-size: 10px;\">Your Subscription</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">Office Cleaning Service</span>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"right\" t-if=\"not company.uses_default_logo\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ company.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"company.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                    <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    Hello <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,<br/><br/>\n"
"                    <t t-if=\"ctx.get('payment_token') and ctx.get('total_amount')\">\n"
"                        Our final attempt to process a payment for your subscription using your payment method\n"
"                        <t t-out=\"ctx.get('payment_token') or ''\">TOKEN</t>\n"
"                        for <t t-out=\"ctx['total_amount'] or ''\">100</t> <t t-out=\"ctx.get('currency') or ''\">$</t> failed.\n"
"                        <t t-if=\"ctx.get('error')\">\n"
"                            Your bank or credit institution gave the following details about the issue: <pre t-out=\"ctx['error'] or ''\"/>.\n"
"                        </t>\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        Our final attempt to process a payment for your subscription failed because we have no payment method recorded for you.\n"
"                    </t>\n"
"                    <br/><br/>\n"
"                    As your payment should have been made <strong><t t-out=\"ctx.get('auto_close_limit') or ''\">5</t> days ago</strong>, your subscription has been terminated.\n"
"                    Should you wish to resolve this issue, do not hesitate to contact us.<br/><br/>\n"
"                    Thank you for choosing <t t-out=\"company.name or ''\">YourCompany</t>!\n"
"                    <div t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"                        <br/><br/>\n"
"                        <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"company.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-if=\"company.phone\">\n"
"                        <t t-out=\"company.phone or ''\">******-123-4567</t> |\n"
"                    </t>\n"
"                    <t t-if=\"company.email\">\n"
"                        <a t-attf-href=\"'mailto:%s' % {{ company.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"company.email or ''\"><EMAIL></a> |\n"
"                    </t>\n"
"                    <t t-if=\"company.website\">\n"
"                        <a t-attf-href=\"'%s' % {{ company.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"company.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=mail\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"        "
msgstr ""

#. module: sale_subscription
#: model:mail.template,body_html:sale_subscription.email_payment_reminder
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <t t-set=\"company\" t-value=\"object.company_id or object.user_id.company_id or user.company_id\"/>\n"
"                    <span style=\"font-size: 10px;\">Your Subscription</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">Office Cleaning Service</span>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"right\" t-if=\"not company.uses_default_logo\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ company.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"company.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                    <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    Hello <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,<br/><br/>\n"
"                    <t t-if=\"ctx.get('payment_token') and ctx.get('total_amount')\">\n"
"                        We were unable to process a payment for your subscription using your payment method\n"
"                        <t t-out=\"ctx['payment_token'] or ''\">TOKEN</t>\n"
"                        for <t t-out=\"ctx['total_amount'] or ''\">10</t> <t t-out=\"ctx.get('currency_name') or ''\">$</t>.\n"
"                        <t t-if=\"ctx.get('error')\">\n"
"                            Your bank or credit institution gave the following details about the issue: <pre t-out=\"ctx['error'] or ''\"/>.\n"
"                        </t>\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        Your subscription <t t-out=\"ctx.get('code') or ''\">CODE</t> is due for renewal but we haven’t received your payment yet. To keep your subscription active, please make your payment as soon as possible.\n"
"                    </t>\n"
"                    <br/><br/>\n"
"                    Your subscription <t t-out=\"ctx.get('code') or ''\">CODE</t> is still valid but will be <strong>suspended</strong>\n"
"                    on <t t-out=\"format_date(ctx.get('date_close')) or ''\">05/05/2021</t><br/>\n"
"                    <div style=\"margin: 16px 0px; text-align: center;\">\n"
"                        <a t-attf-href=\"{{ ctx.get('payment_link', object.get_portal_url()) }}\" style=\"display: inline-block; padding: 10px 30px; text-decoration: none; background-color: #875A7B; color: #fff; border-radius: 5px;\">\n"
"                            Pay Now\n"
"                        </a>\n"
"                    </div>\n"
"                    <br/>\n"
"                    If you have any questions, do not hesitate to contact us.<br/><br/>\n"
"                    Thank you for choosing <t t-out=\"company.name or ''\">YourCompany</t>!\n"
"                    <div>\n"
"                    <t t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"                        <br/><br/>\n"
"                        <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"                    </t>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"company.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-if=\"company.phone\">\n"
"                        <t t-out=\"company.phone or ''\">******-123-4567</t> |\n"
"                    </t>\n"
"                    <t t-if=\"company.email\">\n"
"                        <a t-attf-href=\"'mailto:%s' % {{ company.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"company.email or ''\"><EMAIL></a> |\n"
"                    </t>\n"
"                    <t t-if=\"company.website\">\n"
"                        <a t-attf-href=\"'%s' % {{ company.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"company.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=mail\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"        "
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"A cancelled SO cannot be in progress. You should close %s before cancelling "
"it."
msgstr "已取消的銷售單，不可設為進行中。你應該先關閉 %s，然後取消它。"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/controllers/portal.py:0
msgid "A renewal has been created by the client."
msgstr "續訂已由客戶建立。"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "A renewal quotation %s has been created"
msgstr "已建立續訂報價 %s"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/product.py:0
msgid "A subscription combo product can only contain subscription products."
msgstr "訂閱組合產品只可包含訂閱產品。"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.view_sale_order_log_analysis_graph
#: model_terms:ir.ui.view,arch_db:sale_subscription.view_sale_order_log_growth_graph
#: model_terms:ir.ui.view,arch_db:sale_subscription.view_sale_order_log_growth_pivot
msgid "ARR Change"
msgstr "ARR 變動"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__arr_change_normalized
msgid "ARR Change (normalized)"
msgstr "ARR 每年經常收入變化（正常化）"

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_account_move_send
msgid "Account Move Send"
msgstr "賬戶分錄傳送"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "Action"
msgstr "動作"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "Action Name"
msgstr "動作名稱"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__message_needaction
msgid "Action Needed"
msgstr "需要採取行動"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__action
msgid "Action To Do"
msgstr "待辦的行動"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__action_server_ids
msgid "Actions"
msgstr "動作"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__active
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__active
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_pricing__active
msgid "Active"
msgstr "啟用"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__contract_number
msgid "Active Subscriptions Change"
msgstr "生效訂購計劃變更"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "Activity"
msgstr "活動"

#. module: sale_subscription
#: model:ir.actions.act_window,name:sale_subscription.mail_activity_plan_action_subscription
#: model:ir.ui.menu,name:sale_subscription.mail_activity_plan_menu_config_subscription
msgid "Activity Plans"
msgstr "活動計劃"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__activity_type_id
msgid "Activity Type"
msgstr "活動類型"

#. module: sale_subscription
#: model:ir.actions.act_window,name:sale_subscription.mail_activity_type_action_config_subscription
#: model:ir.ui.menu,name:sale_subscription.subscription_menu_config_activity_type
msgid "Activity Types"
msgstr "活動類型"

#. module: sale_subscription
#: model_terms:ir.actions.act_window,help:sale_subscription.mail_activity_plan_action_subscription
msgid ""
"Activity plans are used to assign a list of activities in just a few clicks\n"
"                (e.g. \"Renewal Offer\", \"Yearly Satisfaction Review\", ...)"
msgstr ""
"「活動計劃」用於簡易分配一系列活動，按幾下便完成。\n"
"                （例如：續訂優惠、每年滿意度審核等）"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__user_quantity
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__user_quantity
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_plan_search
msgid "Add Products"
msgstr "加入產品"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_sidebar
msgid "Add Quantity"
msgstr "新增數量"

#. module: sale_subscription
#. odoo-javascript
#: code:addons/sale_subscription/static/src/js/tours/sale_subscription.js:0
msgid "Add a new rule"
msgstr "加入新規則"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.product_pricelist_view
#: model_terms:ir.ui.view,arch_db:sale_subscription.product_template_view_form_recurring
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_plan_view_form
msgid "Add a price rule"
msgstr "新增價格規則"

#. module: sale_subscription
#. odoo-javascript
#: code:addons/sale_subscription/static/src/js/tours/sale_subscription.js:0
msgid ""
"Add a recurring plan for this product, or create a new one with the desired "
"recurrence (e.g., Monthly)"
msgstr "為此產品加入重複性計劃，或按所需的重複周期（例如：每月）建立新計劃"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_subscription_plan__billing_first_day
msgid ""
"Align all subscription invoices on the first day of each billing period."
msgstr "將所有訂閱發票協調至每個賬單周期的第一天。"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__billing_first_day
msgid "Align to Period Start"
msgstr "協調至期間開首"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/controllers/portal.py:0
msgid "All"
msgstr "所有"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__user_quantity
#: model:ir.model.fields,help:sale_subscription.field_sale_subscription_plan__user_quantity
msgid ""
"Allow customers to create an Upsell quote to adjust the quantity of products"
" in their subscription.Only products that are listed as \"optional "
"products\" can be modified."
msgstr "允許客戶建立追加銷售報價，以調整服務計劃訂購產品的數量。只可修改列為「可選產品」的產品。"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_subscription_plan__related_plan_id
msgid ""
"Allow your customers to switch from this plan to another on quotation (new "
"subscription or renewal)"
msgstr "允許客戶由此計劃切換至報價單上的另一計劃（新訂購或續訂）"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_close_reason_view_form
msgid ""
"Allowing to be selected by customers in the portal when they are closing "
"their subscriptions."
msgstr "允許客戶在關閉訂購計劃時，在門戶網站中進行選擇。"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/controllers/portal.py:0
msgid "An upsell has been created by the client."
msgstr "客戶已建立一項追加銷售。"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "An upsell quotation %s has been created"
msgstr "已建立追加銷售報價 %s"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__recurring_yearly
msgid "Annual Recurring Revenue"
msgstr "年度經常收入"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "Anticipate payment"
msgstr "預期付款"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__filter_domain
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "Apply on"
msgstr "套用於"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_template_view_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_plan_view_form
msgid "Archived"
msgstr "已封存"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__activity_user
msgid "Assign To"
msgstr "指派給"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__payment_transaction__subscription_action__assign_token
msgid "Assign Token"
msgstr "指派代碼"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_subscription_plan__user_closable_options__at_date
msgid "At date"
msgstr "於指定日期"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__message_attachment_count
msgid "Attachment Count"
msgstr "附件數目"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__payment_transaction__renewal_state__authorized
msgid "Authorized"
msgstr "授權"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_sidebar
msgid "Automate Payment"
msgstr "自動化付款"

#. module: sale_subscription
#: model_terms:ir.actions.act_window,help:sale_subscription.sale_subscription_action
#: model_terms:ir.actions.act_window,help:sale_subscription.sale_subscription_action_filtered
msgid ""
"Automate invoices and payments, simplify upsell quotes, reduce churn and get"
" insights (MRR, churn, CLTV, ...)"
msgstr "自動化發票及付款、簡化追加銷售報價、減少客戶流失，並獲得有用數據（例如：MRR 每月經常收入、客戶流失情況、CLTV 客戶終身價值等）。"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__auto_close_limit
msgid "Automatic Closing"
msgstr "自動關閉"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__auto_close_limit_display
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_plan_search
msgid "Automatic Closing After"
msgstr "自動關閉（在此期限後）"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"Automatic payment failed after multiple attempts. Contract closed "
"automatically."
msgstr "多次嘗試自動付款均失敗。合約已自動關閉。"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"Automatic payment failed. Check the corresponding invoice %s. We can't "
"automatically process negative payment"
msgstr "自動付款失敗。請檢查對應的發票 %s。我們不能自動處理負數金額的付款"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "Automatic payment failed. Email sent to customer. Error: %s"
msgstr "自動付款失敗。電郵已發送給客戶。錯誤： %s"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"Automatic payment failed. No country specified on payment_token's partner"
msgstr "自動付款失敗：payment_token 的合作夥伴未指定國家/地區"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "Automatic payment failed. No email sent this time. Error: %s"
msgstr "自動付款失敗。今次沒有發送電郵。錯誤： %s"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"Automatic payment succeeded. Payment reference: %(ref)s. Amount: %(amount)s."
" Contract set to: In Progress, Next Invoice: %(inv)s. Email sent to "
"customer."
msgstr "自動付款成功。付款參考：%(ref)s，金額：%(amount)s。合約已設為：進行中。下次發票：%(inv)s。電郵已發送給客戶。"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__payment_exception
msgid ""
"Automatic payment with token failed. The payment provider configuration and "
"token should be checked"
msgstr "使用代碼自動付款失敗。請檢查付款服務商配置及代碼"

#. module: sale_subscription
#: model:sale.order.close.reason,name:sale_subscription.close_reason_auto_close_limit_reached
msgid "Automatic renewal failed"
msgstr "自動續訂失敗"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"Automatic renewal succeeded. Free subscription. Next Invoice: %(inv)s. No "
"email sent."
msgstr "自動續訂成功。免費訂購。下次發票：%(inv)s。沒有發送電郵。"

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_base_automation
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__automation_id
msgid "Automation Rule"
msgstr "自動化規則"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__name
msgid "Automation Rule Name"
msgstr "自動化規則名稱"

#. module: sale_subscription
#: model:ir.actions.act_window,name:sale_subscription.sale_subscription_alert_action
#: model:ir.ui.menu,name:sale_subscription.menu_sale_subscription_alert
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_tree
msgid "Automation Rules"
msgstr "自動化規則"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__rating_avg
msgid "Average Rating"
msgstr "平均評分"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order__health__bad
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__health__bad
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__health__bad
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_subscription_report__health__bad
msgid "Bad"
msgstr "欠佳"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Bad Health"
msgstr "健康度欠佳"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__filter_pre_domain
msgid "Before Update Domain"
msgstr "更新網域前"

#. module: sale_subscription
#: model_terms:sale.order.close.reason,retention_message:sale_subscription.close_reason_1
msgid ""
"Before closing your subscription, we'd like to offer you to schedule a call "
"with Marc Demo, your account manager."
msgstr "在你關閉定期訂購計劃前，我們想邀請你安排與你的客戶經理 Marc Demo 通話。"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__billing_period_display
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_pricing__name
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_plan_view_form
msgid "Billing Period"
msgstr "賬單期"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__billing_period_value
msgid "Billing Period "
msgstr "賬單期"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__billing_period_display_sentence
msgid "Billing Period Display"
msgstr "賬單期顯示"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_close_reason__retention_button_link
msgid "Button Link"
msgstr "按鈕連結"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_close_reason__retention_button_text
msgid "Button Text"
msgstr "按鈕文字"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__percentage_satisfaction
msgid ""
"Calculate the ratio between the number of the best ('great') ratings and the"
" total number of ratings"
msgstr "計算最佳（'高分' ）評分數與評分總數之間的比率"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__campaign_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__campaign_id
msgid "Campaign"
msgstr "行銷活動"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_change_customer_view_form
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_close_reason_wizard_view_form
msgid "Cancel"
msgstr "取消"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__order_state__cancel
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__state__cancel
#: model:sale.order.close.reason,name:sale_subscription.close_reason_cancel
msgid "Cancelled"
msgstr "已取消"

#. module: sale_subscription
#: model:product.template,name:sale_subscription.product_car_leasing_product_template
msgid "Car Leasing (SUB)"
msgstr "租車（定期訂購）"

#. module: sale_subscription
#: model:ir.actions.act_window,name:sale_subscription.sale_subscription_change_customer_wizard_action
msgid "Change Customer"
msgstr "更改客戶"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_sidebar
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_so_portal_template
msgid "Change Plan"
msgstr "更改規劃"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_so_portal_template
msgid "Change Plan for"
msgstr "更改規劃："

#. module: sale_subscription
#: model:ir.actions.server,name:sale_subscription.model_sale_order_subscription_change_customer
msgid "Change customer"
msgstr "更改客戶"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "Check reopened subscription"
msgstr "檢查重新開放的訂購計劃"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_template
msgid "Choose a closing reason before submitting"
msgstr "提交前，請選擇關閉原因"

#. module: sale_subscription
#. odoo-javascript
#: code:addons/sale_subscription/static/src/js/tours/sale_subscription.js:0
msgid "Choose a product name.<br/><i>(e.g. eLearning Access)</i>"
msgstr "選擇產品名稱。<br/><small>（例：存取網上學習平台）</small>"

#. module: sale_subscription
#. odoo-javascript
#: code:addons/sale_subscription/static/src/js/tours/sale_subscription.js:0
msgid "Choose the invoice duration for your subscription"
msgstr "為定期訂購計劃選擇發票期有多長"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log__event_type__2_churn
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__event_type__2_churn
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
msgid "Churn"
msgstr "流失"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order__subscription_state__6_churn
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__subscription_state__6_churn
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__subscription_state_from__6_churn
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log__subscription_state__6_churn
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__subscription_state__6_churn
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_subscription_report__subscription_state__6_churn
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Churned"
msgstr "已流失"

#. module: sale_subscription
#. odoo-javascript
#: code:addons/sale_subscription/static/src/js/tours/sale_subscription.js:0
msgid "Click here to add some products or services"
msgstr "按此處以加入一些產品或服務"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__user_closable
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__user_closable
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_plan_search
msgid "Closable"
msgstr "可關閉"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_so_portal_template
msgid "Close"
msgstr "關閉"

#. module: sale_subscription
#: model:ir.actions.act_window,name:sale_subscription.sale_subscription_close_reason_wizard_action
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__close_reason_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__close_reason_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_close_reason_wizard__close_reason_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__close_reason_id
msgid "Close Reason"
msgstr "關閉原因"

#. module: sale_subscription
#: model:ir.actions.act_window,name:sale_subscription.sale_subscription_close_reason_action
#: model:ir.ui.menu,name:sale_subscription.menu_sale_subscription_close_reason_action
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_close_reason_view_tree
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_close_reason_wizard_view_form
msgid "Close Reasons"
msgstr "關閉原因"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_sidebar
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_template
msgid "Close Subscription"
msgstr "關閉定期訂購"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__user_closable_options
msgid "Closeable Plan Options"
msgstr "可關閉計劃選項"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/controllers/portal.py:0
msgid "Closed"
msgstr "已關閉"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
msgid "Closed subscriptions"
msgstr "封閉的訂閱"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/controllers/portal.py:0
msgid "Closing text: %s"
msgstr "收結文字：%s"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__commercial_partner_id
msgid "Commercial Entity"
msgstr "商業實體"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__company_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log__company_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__company_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__company_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_pricing__company_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__company_id
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
msgid "Company"
msgstr "公司"

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_res_config_settings
msgid "Config Settings"
msgstr "配置設定"

#. module: sale_subscription
#: model:ir.ui.menu,name:sale_subscription.menu_sale_subscription_config
msgid "Configuration"
msgstr "配置"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_res_config_settings__invoice_consolidation
#: model_terms:ir.ui.view,arch_db:sale_subscription.res_config_settings_view_form
msgid ""
"Consolidate all of a customer's subscriptions that are due to be billed on "
"the same day onto a single invoice."
msgstr "將所有應在同一天開立收費賬單的客戶服務計劃，合併至單一張發票。"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_res_config_settings__invoice_consolidation
msgid "Consolidate subscriptions billing"
msgstr "合併定期訂購賬單"

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_res_partner
msgid "Contact"
msgstr "聯絡人"

#. module: sale_subscription
#: model:sale.order.close.reason,retention_button_text:sale_subscription.close_reason_1
msgid "Contact Marc"
msgstr "聯絡 Marc"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_template__duration_unit
msgid "Contract duration"
msgstr "合約期"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__payment_exception
msgid "Contract in exception"
msgstr "例外合約"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log__event_type__15_contraction
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__event_type__15_contraction
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
msgid "Contraction"
msgstr "縮減"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Contracts whose payment has failed"
msgstr "付款失敗的合約"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__country_id
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
msgid "Country"
msgstr "國家/地區"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
msgid "Create Alternative"
msgstr "建立替代方案"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
msgid "Create Invoice"
msgstr "建立發票"

#. module: sale_subscription
#: model_terms:ir.actions.act_window,help:sale_subscription.mail_activity_plan_action_subscription
msgid "Create a Subscription Activity Plan"
msgstr "建立定期訂購活動計劃"

#. module: sale_subscription
#: model_terms:ir.actions.act_window,help:sale_subscription.sale_subscription_plan_action
msgid "Create a new plan"
msgstr "建立新計劃"

#. module: sale_subscription
#: model_terms:ir.actions.act_window,help:sale_subscription.product_action_subscription
msgid "Create a new product"
msgstr "建立新產品"

#. module: sale_subscription
#: model_terms:ir.actions.act_window,help:sale_subscription.sale_subscription_action
#: model_terms:ir.actions.act_window,help:sale_subscription.sale_subscription_action_filtered
msgid "Create a new subscription"
msgstr "建立新定期訂購"

#. module: sale_subscription
#: model_terms:ir.actions.act_window,help:sale_subscription.sale_subscription_alert_action
msgid "Create a new subscription alert"
msgstr "建立新定期訂購提醒"

#. module: sale_subscription
#: model_terms:ir.actions.act_window,help:sale_subscription.sale_subscription_template_action
msgid "Create a new template of subscription"
msgstr "建立新定期訂購範本"

#. module: sale_subscription
#: model_terms:ir.actions.act_window,help:sale_subscription.sale_subscription_action_quotes
msgid "Create a subscription quotation"
msgstr "建立定期訂購報價"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__action__next_activity
msgid "Create next activity"
msgstr "建立下一個活動"

#. module: sale_subscription
#: model_terms:ir.actions.act_window,help:sale_subscription.sale_subscription_action_quotes
msgid ""
"Create subscriptions to manage recurring invoicing and payments from your "
"customers."
msgstr "建立定期訂購以管理客戶的經常發票及付款。"

#. module: sale_subscription
#. odoo-javascript
#: code:addons/sale_subscription/static/src/js/tours/sale_subscription.js:0
msgid "Create your first subscription product here"
msgstr "在此建立你的第一個定期訂購產品"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__create_uid
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_close_reason__create_uid
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log__create_uid
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_change_customer_wizard__create_uid
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_close_reason_wizard__create_uid
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__create_uid
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_pricing__create_uid
msgid "Created by"
msgstr "建立人員"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__create_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_close_reason__create_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log__create_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_change_customer_wizard__create_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_close_reason_wizard__create_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__create_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_pricing__create_date
msgid "Created on"
msgstr "建立於"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__cron_nextcall
msgid "Cron Nextcall"
msgstr "Cron 下次通話"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__currency_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__currency_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log__currency_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__currency_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_pricing__currency_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__currency_id
#: model_terms:ir.ui.view,arch_db:sale_subscription.view_sale_order_log_growth_tree
msgid "Currency"
msgstr "貨幣"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_product_product__product_subscription_pricing_ids
#: model:ir.model.fields,field_description:sale_subscription.field_product_template__product_subscription_pricing_ids
msgid "Custom Subscription Pricings"
msgstr "自訂定期訂購價格"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__partner_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__partner_id
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_sales_order_line_filter
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Customer"
msgstr "客戶"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__commercial_partner_id
msgid "Customer Company"
msgstr "客戶公司"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__country_id
msgid "Customer Country"
msgstr "客戶國家"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__commercial_partner_id
msgid "Customer Entity"
msgstr "客戶實體"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__industry_id
msgid "Customer Industry"
msgstr "客戶的行業"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__client_order_ref
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__client_order_ref
msgid "Customer Reference"
msgstr "客戶參考"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__state_id
msgid "Customer State"
msgstr "客戶州/省"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__partner_zip
msgid "Customer ZIP"
msgstr "客戶郵遞區號"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__user_closable
#: model:ir.model.fields,help:sale_subscription.field_sale_subscription_plan__user_closable
msgid "Customer can close their subscriptions."
msgstr "客戶可以關閉他們的訂購計劃。"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__user_extend
#: model:ir.model.fields,help:sale_subscription.field_sale_subscription_plan__user_extend
msgid "Customer can create a renewal quotation for their subscription."
msgstr "客戶可為其服務計劃建立續訂報價。"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__customer_ids
#: model:ir.ui.menu,name:sale_subscription.menu_orders_customers
msgid "Customers"
msgstr "客戶"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "Date:"
msgstr "日期:"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_plan_view_form
msgid "Days"
msgstr "天內"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "Delay After Trigger"
msgstr "觸發後延遲"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_alert__trg_date_range
msgid ""
"Delay after the trigger date. You can put a negative number if you need a "
"delay before the trigger date, like sending a reminder 15 minutes before a "
"meeting."
msgstr "觸發日期之後延遲多久才執行操作。若想在觸發日期之前執行操作，可以輸入負數，例如在會議開始前 15 分鐘發送提醒。"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__trg_date_range
msgid "Delay after trigger date"
msgstr "觸發日期後延遲"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__trg_date_range_type
msgid "Delay type"
msgstr "延遲類型"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__description
msgid "Description"
msgstr "說明"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "Destination"
msgstr "目的地"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_plan_view_form
msgid "Details"
msgstr "詳細資訊"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_template
msgid "Discard, I want to stay"
msgstr "算了，我想保留"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__discount
msgid "Discount %"
msgstr "折扣百分比"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__discount_amount
msgid "Discount Amount"
msgstr "折扣金額"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__display_late
msgid "Display Late"
msgstr "顯示遲到"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__display_name
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_close_reason__display_name
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log__display_name
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__display_name
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_change_customer_wizard__display_name
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_close_reason_wizard__display_name
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__display_name
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_pricing__display_name
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_product_product__display_subscription_pricing
#: model:ir.model.fields,field_description:sale_subscription.field_product_template__display_subscription_pricing
msgid "Display Price"
msgstr "顯示價錢"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_sidebar
msgid "Download"
msgstr "下載"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__payment_transaction__renewal_state__draft
msgid "Draft"
msgstr "草稿"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__activity_date_deadline_range
msgid "Due Date In"
msgstr "截止日期至"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__activity_date_deadline_range_type
msgid "Due type"
msgstr "截止類型"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_template__duration_unit
msgid "Duration Unit"
msgstr "持續時長單位"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__template_id
msgid "Email Template"
msgstr "電郵範本"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_subscription_plan__invoice_mail_template_id
msgid ""
"Email template used to send invoicing email automatically.\n"
"Leave it empty if you don't want to send email automatically."
msgstr ""
"用於自動發送發票電郵的電子郵件範本。\n"
"如果不想自動發送電子郵件，請留空。"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_close_reason__empty_retention_message
msgid "Empty Retention Message"
msgstr "空白挽留訊息"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_template__duration_value
msgid "End After"
msgstr "此後結束"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__end_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_line__subscription_end_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__end_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__end_date
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_sales_order_line_filter
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "End Date"
msgstr "結束日期"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "End Date:"
msgstr "結束日期："

#. module: sale_subscription
#: model:sale.order.close.reason,name:sale_subscription.close_reason_end_of_contract
msgid "End of contract"
msgstr "合約期終結"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_subscription_plan__user_closable_options__end_of_period
msgid "End of period"
msgstr "服務期完結時"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"Error during renewal of contract %(order_ids)s %(order_refs)s "
"%(payment_state)s"
msgstr "合約續訂時發生錯誤：%(order_ids)s %(order_refs)s %(payment_state)s"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "Error during renewal of contract %s (Payment not recorded)"
msgstr "合約 %s 續約時發生錯誤（未有收到付款記錄）"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log__event_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__event_date
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
msgid "Event Date"
msgstr "活動日期"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
msgid "Event Type"
msgstr "活動類型"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log__event_type__1_expansion
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__event_type__1_expansion
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
msgid "Expansion"
msgstr "加大"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "Expiration Date:"
msgstr "到期日："

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Failed Payments"
msgstr "失敗付款"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_alert__on_change_field_ids
msgid "Fields that trigger the onchange."
msgstr "觸發 onchange 的欄位。"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
msgid "First Contract"
msgstr "首份合約"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__first_contract_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__first_contract_date
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "First Contract Date"
msgstr "首次合約日期"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__origin_order_id
msgid "First Order"
msgstr "首份訂單"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__origin_order_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__origin_order_id
msgid "First contract"
msgstr "首份合約"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__first_contract_date
msgid "First contract date"
msgstr "首次合約日期"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__message_follower_ids
msgid "Followers"
msgstr "關注人"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__message_partner_ids
msgid "Followers (Partners)"
msgstr "關注人（業務夥伴）"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_kanban
msgid "Future"
msgstr "未來"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Future Activities"
msgstr "未來活動"

#. module: sale_subscription
#. odoo-javascript
#: code:addons/sale_subscription/static/src/js/tours/sale_subscription.js:0
msgid "Go ahead and create a new product"
msgstr "去建立一個新產品吧！"

#. module: sale_subscription
#. odoo-javascript
#: code:addons/sale_subscription/static/src/js/tours/sale_subscription.js:0
msgid "Go ahead and create a new subscription"
msgstr "去建立一個新的定期訂購吧！"

#. module: sale_subscription
#. odoo-javascript
#: code:addons/sale_subscription/static/src/js/tours/sale_subscription.js:0
msgid "Go back to the subscription view"
msgstr "返回定期訂購檢視畫面"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order__health__done
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__health__done
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__health__done
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_subscription_report__health__done
msgid "Good"
msgstr "良好"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Good Health"
msgstr "健康度良好"

#. module: sale_subscription
#: model_terms:ir.actions.act_window,help:sale_subscription.sale_subscription_action_pending
msgid "Great, there are no subscriptions to renew!"
msgstr "太好了，沒有續訂需要處理！"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__weight
msgid "Gross Weight"
msgstr "毛重"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_sales_order_line_filter
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Group By"
msgstr "分組依據"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_kanban
msgid "Happy face"
msgstr "笑臉"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__model_is_mail_thread
msgid "Has Mail Thread"
msgstr "有郵件對話串"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__has_message
msgid "Has Message"
msgstr "有訊息"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__has_recurring_line
msgid "Has Recurring Line"
msgstr "有經常性資料行"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__health
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__health
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__health
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__health
msgid "Health"
msgstr "健康度"

#. module: sale_subscription
#: model:ir.module.category,description:sale_subscription.module_category_subscription_management
msgid "Helps you handle subscriptions and recurring invoicing."
msgstr "幫助你處理定期訂購計劃及經常性發票。"

#. module: sale_subscription
#: model_terms:ir.actions.act_window,help:sale_subscription.sale_subscription_action_pending
msgid "Here you will find subscriptions overdue for renewal."
msgstr "此處會顯示過期未續訂的定期訂購計劃。"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_sidebar
msgid "History"
msgstr "過往訂單"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__history_count
msgid "History Count"
msgstr "過往訂單數目"

#. module: sale_subscription
#: model:sale.order.close.reason,name:sale_subscription.close_reason_4
msgid "I don't use it"
msgstr "我不使用它"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_close_reason__id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log__id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_change_customer_wizard__id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_close_reason_wizard__id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_pricing__id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__id
msgid "ID"
msgstr "識別號"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__message_needaction
msgid "If checked, new messages require your attention."
msgstr "勾選代表有新訊息需要您留意。"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__message_has_error
#: model:ir.model.fields,help:sale_subscription.field_sale_order__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "勾選代表有訊息發生傳送錯誤。"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__payment_token_id
msgid "If not set, the automatic payment will fail."
msgstr "若不設置，自動付款將會失敗。"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_alert__filter_domain
msgid ""
"If present, this condition must be satisfied before executing the automation"
" rule."
msgstr "如果存在，則在執行自動化規則前必須滿足該條件。"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_alert__filter_pre_domain
msgid ""
"If present, this condition must be satisfied before the update of the "
"record. Not checked on record creation."
msgstr "如果存在，此條件必須在更新記錄之前滿足。建立記錄時未有檢查。"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__end_date
#: model:ir.model.fields,help:sale_subscription.field_sale_order_line__subscription_end_date
msgid ""
"If set in advance, the subscription will be set to renew 1 month before the "
"date and will be closed on the date set in this field."
msgstr "若提前設定，定期訂購計劃將設為在該日期前 1 個月續訂，並會在此欄位設定的日期關閉。"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_product_product__recurring_invoice
#: model:ir.model.fields,help:sale_subscription.field_product_template__recurring_invoice
#: model:ir.model.fields,help:sale_subscription.field_sale_order_line__recurring_invoice
#: model:ir.model.fields,help:sale_subscription.field_sale_order_template_line__recurring_invoice
#: model:ir.model.fields,help:sale_subscription.field_sale_order_template_option__recurring_invoice
msgid ""
"If set, confirming a sale order with this product will create a subscription"
msgstr "如果設定了，在確認帶有這個產品的訂單的時候會建立一個訂閱"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_line__pricelist_id
msgid "If you change the pricelist, only newly added lines will be affected."
msgstr "如果更改價目表，則只會影響新增加的產品明細."

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "If you wish to reopen it, the"
msgstr "如果您希望重新打開它，則"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid ""
"If you wish to reopen it, you can pay your invoice for the current invoicing"
" period."
msgstr "如果您希望重新打開它，您可以為目前的發票期支付您的發票。"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/controllers/portal.py:0
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order__subscription_state__3_progress
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__subscription_state__3_progress
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__subscription_state_from__3_progress
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log__subscription_state__3_progress
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__subscription_state__3_progress
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_subscription_report__subscription_state__3_progress
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "In Progress"
msgstr "進行中"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__industry_id
msgid "Industry"
msgstr "行業"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "Initial"
msgstr "簡簽"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__internal_note
msgid "Internal Note"
msgstr "內部備註"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__internal_note_display
msgid "Internal Note Display"
msgstr "內部備註顯示"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
msgid "Internal notes"
msgstr "內部備註"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/controllers/portal.py:0
msgid "Invalid access token."
msgstr "存取權杖無效。"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__invoice_mail_template_id
msgid "Invoice Email Template"
msgstr "發票電郵範本"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__line_invoice_status
msgid "Invoice Status"
msgstr "發票狀態"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/controllers/portal.py:0
msgid "Invoice not found."
msgstr "找不到發票。"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__is_batch
msgid "Is Batch"
msgstr "是大批處理"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__message_is_follower
msgid "Is Follower"
msgstr "是關注人"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_close_reason__is_protected
msgid "Is Protected"
msgstr "受保護"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__is_renewing
msgid "Is Renewing"
msgstr "正在續期"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_base_automation__is_sale_order_alert
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__is_sale_order_alert
msgid "Is Sale Order Alert"
msgstr "是銷售單警報"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_template__is_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__is_subscription
msgid "Is Subscription"
msgstr "是定期訂購"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__is_upselling
msgid "Is Upselling"
msgstr "正在追加銷售"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__is_invoice_cron
msgid "Is a Subscription invoiced in cron"
msgstr "是在 cron 開立發票的定期訂購"

#. module: sale_subscription
#: model_terms:ir.actions.act_window,help:sale_subscription.sale_subscription_action_pending
msgid ""
"It could be due to the fail of the automatic payment\n"
"            or because the new invoice has not yet been issued."
msgstr ""
"可能是由於自動付款失敗，\n"
"            或新發票尚未開立而引致。"

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_account_move
msgid "Journal Entry"
msgstr "日記賬記項"

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_account_move_line
msgid "Journal Item"
msgstr "日記賬項目"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__kpi_1month_mrr_delta
msgid "KPI 1 Month MRR Delta"
msgstr "KPI 1 個月 MRR 變動差額"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__kpi_1month_mrr_percentage
msgid "KPI 1 Month MRR Percentage"
msgstr "KPI 1 個月 MRR 變動百分比"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__kpi_3months_mrr_percentage
msgid "KPI 3 Months MRR Percentage"
msgstr "KPI 3 個月 MRR 變動百分比"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__kpi_3months_mrr_delta
msgid "KPI 3 months MRR Delta"
msgstr "KPI 3 個月 MRR 變動差額"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_template__is_unlimited
msgid "Last Forever"
msgstr "永遠持續"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_line__last_invoiced_date
msgid "Last Invoiced Date"
msgstr "最後開立發票日期"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__last_reminder_date
msgid "Last Reminder Date"
msgstr "上次提醒日期"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__last_run
msgid "Last Run"
msgstr "最後執行"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__write_uid
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_close_reason__write_uid
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log__write_uid
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_change_customer_wizard__write_uid
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_close_reason_wizard__write_uid
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__write_uid
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_pricing__write_uid
msgid "Last Updated by"
msgstr "最後更新者"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__write_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_close_reason__write_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log__write_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_change_customer_wizard__write_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_close_reason_wizard__write_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__write_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_pricing__write_date
msgid "Last Updated on"
msgstr "最後更新於"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__last_invoice_date
msgid "Last invoice date"
msgstr "最後發票日期"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__last_reminder_date
msgid "Last time when we sent a payment reminder"
msgstr "上次發送付款提醒的時間"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Late Activities"
msgstr "逾期活動"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_kanban
msgid "Latest Rating: Dissatisfied"
msgstr "最新評價：不滿意"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_kanban
msgid "Latest Rating: Okay"
msgstr "最新評價：良好"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_kanban
msgid "Latest Rating: Satisfied"
msgstr "最近評分：滿意"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__least_delay_msg
msgid "Least Delay Msg"
msgstr "最少延遲消息"

#. module: sale_subscription
#. odoo-javascript
#: code:addons/sale_subscription/static/src/js/tours/sale_subscription.js:0
msgid "Let's add a pricing with a recurrence"
msgstr "讓我們加入一個有重複周期的定價"

#. module: sale_subscription
#. odoo-javascript
#: code:addons/sale_subscription/static/src/js/tours/sale_subscription.js:0
msgid "Let's add price for selected recurrence"
msgstr "讓我們為所選重複周期加入價格"

#. module: sale_subscription
#. odoo-javascript
#: code:addons/sale_subscription/static/src/js/tours/sale_subscription.js:0
msgid "Let's choose the customer for your subscription"
msgstr "讓我們為你的定期訂購計劃選擇客戶"

#. module: sale_subscription
#. odoo-javascript
#: code:addons/sale_subscription/static/src/js/tours/sale_subscription.js:0
msgid "Let's go to the catalog to create our first subscription product"
msgstr "讓我們前往產品目錄，建立我們的第一個定期訂購產品。"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__log_webhook_calls
msgid "Log Calls"
msgstr "通話記錄"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__log_currency_id
msgid "Log Currency"
msgstr "系統日誌貨幣"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "Log a note..."
msgstr "寫下備註⋯"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_tree
#: model_terms:ir.ui.view,arch_db:sale_subscription.view_sale_order_log_growth_tree
msgid "MRR"
msgstr "MRR（每月經常收入）"

#. module: sale_subscription
#: model:ir.actions.act_window,name:sale_subscription.sale_order_log_analysis_action
msgid "MRR Analysis"
msgstr "MRR 分析"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "MRR Between"
msgstr "MRR 期間"

#. module: sale_subscription
#: model:ir.actions.act_window,name:sale_subscription.sale_order_log_growth_action
#: model:ir.ui.menu,name:sale_subscription.menu_sale_order_log_growth_report
msgid "MRR Breakdown"
msgstr "MRR 細分"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__amount_signed
#: model_terms:ir.ui.view,arch_db:sale_subscription.view_sale_order_log_analysis_graph
#: model_terms:ir.ui.view,arch_db:sale_subscription.view_sale_order_log_growth_graph
#: model_terms:ir.ui.view,arch_db:sale_subscription.view_sale_order_log_growth_pivot
msgid "MRR Change"
msgstr "MRR 變動"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__mrr_change_normalized
msgid "MRR Change (normalized)"
msgstr "MRR 每月經常收入變化（正常化）"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__mrr_change_amount
msgid "MRR Change Amount"
msgstr "MRR 變動金額"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "MRR Change More"
msgstr "MRR 變動更多"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__mrr_change_period
msgid "MRR Change Period"
msgstr "MRR 變動周期"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__mrr_change_unit
msgid "MRR Change Unit"
msgstr "MRR 變動單位"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__mrr_max
msgid "MRR Range Max"
msgstr "MRR 最大範圍"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__mrr_min
msgid "MRR Range Min"
msgstr "MRR 最小範圍"

#. module: sale_subscription
#: model:ir.ui.menu,name:sale_subscription.menu_sale_order_log_analysis_report
msgid "MRR Timeline"
msgstr "MRR 時間線"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log__amount_signed
#: model_terms:ir.ui.view,arch_db:sale_subscription.view_sale_order_log_growth_tree
msgid "MRR change"
msgstr "MRR 變動"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "MRR changes"
msgstr "MRR 變動"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_log__recurring_monthly
msgid "MRR, after applying the changes of that particular event"
msgstr "MRR 每月經常收入，在套用該特定事件的變更後"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_digest
msgid "MRR:"
msgstr "MRR 每月經常收入："

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.portal_my_home_subscription
msgid "Manage your subscriptions"
msgstr "管理定期訂購"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_sidebar
msgid ""
"Managing payment methods requires to be logged in under the customer order."
msgstr "要管理付款方法，須在客戶訂單下登入。"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"Manual payment succeeded. Payment reference: %(ref)s. Amount: %(amount)s. "
"Contract set to: In Progress, Next Invoice: %(inv)s. Email sent to customer."
msgstr "手動付款成功。付款參考：%(ref)s，金額：%(amount)s。合約已設為：進行中。下次發票：%(inv)s。電郵已發送給客戶。"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__margin
msgid "Margin"
msgstr "毛利"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__medium_id
msgid "Medium"
msgstr "媒介"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__starred_user_ids
msgid "Members"
msgstr "成員"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_close_reason__retention_message
msgid "Message"
msgstr "訊息"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__message_has_error
msgid "Message Delivery error"
msgstr "訊息傳遞錯誤"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__message_ids
msgid "Messages"
msgstr "訊息"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"Mix of negative recurring lines and non-recurring line. The contract should "
"be fixed manually"
msgstr "負數經常性資料行與非重複性資料行混合。合約應該手動修復"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__model_id
msgid "Model"
msgstr "模型"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__model_name
msgid "Model Name"
msgstr "模型名稱"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_alert__model_id
msgid "Model on which the automation rule runs."
msgstr "運行自動化規則的模型。"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__trigger_condition__on_create_or_write
msgid "Modification"
msgstr "修改"

#. module: sale_subscription
#: model:sale.subscription.plan,name:sale_subscription.subscription_plan_month
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_plan_view_form
msgid "Monthly"
msgstr "每月"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__recurring_monthly
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__recurring_monthly
msgid "Monthly Recurring"
msgstr "每月重複"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_account_move_line__subscription_mrr
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_line__recurring_monthly
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__recurring_monthly
msgid "Monthly Recurring Revenue"
msgstr "每月經常收入"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_template__duration_unit__month
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_subscription_plan__billing_period_unit__month
msgid "Months"
msgstr "月"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "My Subscriptions"
msgstr "我的定期訂購"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/controllers/portal.py:0
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__name
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_tree
msgid "Name"
msgstr "名稱"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order__health__normal
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__health__normal
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__health__normal
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_subscription_report__health__normal
msgid "Neutral"
msgstr "中立"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_kanban
msgid "Neutral face"
msgstr "平淡的表情"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log__event_type__0_creation
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__event_type__0_creation
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
msgid "New"
msgstr "新增"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_change_customer_wizard__partner_id
msgid "New Customer"
msgstr "新客戶"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_change_customer_view_form
msgid "New Customer Information"
msgstr "新客戶資料"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_change_customer_wizard__partner_shipping_id
msgid "New Delivery Address"
msgstr "新送貨地址"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_change_customer_wizard__partner_invoice_id
msgid "New Invoice Address"
msgstr "新發票地址"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log__recurring_monthly
#: model_terms:ir.ui.view,arch_db:sale_subscription.view_sale_order_log_growth_tree
msgid "New MRR"
msgstr "新增 MRR"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/controllers/portal.py:0
msgid "Newest"
msgstr "最新"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.portal_my_subscriptions
msgid "Next Billing Date"
msgstr "下次賬單日期"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__next_invoice_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_line__next_invoice_date
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_tree
msgid "Next Invoice"
msgstr "下次發票"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__next_invoice_date
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_sales_order_line_filter
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Next Invoice Date"
msgstr "下次發票日期"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_digest
msgid "Next Invoice:"
msgstr "下次發票："

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Next invoice Date"
msgstr "下次發票日期"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "No Payment Method"
msgstr "沒有付款方法"

#. module: sale_subscription
#: model_terms:ir.actions.act_window,help:sale_subscription.sale_subscription_action_upsell
msgid "No Upsell Found"
msgstr "未找到追加銷售"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_plan_view_form
msgid "No automatic mail"
msgstr "沒有自動郵件"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_template
msgid "No thanks, close my account"
msgstr "不用了，取消我的帳戶"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "No valid Payment Method"
msgstr "沒有有效的付款方法"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_tree
msgid "Non Recurring"
msgstr "非重複性"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.view_order_product_search_inherit
msgid "Non-recurring"
msgstr "非重複性"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_template_view_form
msgid "None"
msgstr "無"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.view_sales_order_filter_subscription
msgid "Not Recurring"
msgstr "不是經常性"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__activity_note
msgid "Note"
msgstr "備註"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__note_order
msgid "Note Order"
msgstr "備註訂單"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
msgid "Notes"
msgstr "備註"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_tree
msgid "Number"
msgstr "號碼"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__message_needaction_counter
msgid "Number of Actions"
msgstr "動作數量"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__message_has_error_counter
msgid "Number of errors"
msgstr "錯誤數量"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "需要採取行動的訊息數目"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "有發送錯誤的郵件數量"

#. module: sale_subscription
#: model:product.template,name:sale_subscription.product_office_cleaning_product_template
#: model:sale.order.template.line,name:sale_subscription.montly_template_line
#: model:sale.order.template.line,name:sale_subscription.yearly_template_line
msgid "Office Cleaning Service (SUB)"
msgstr "辦公室清潔服務（定期訂購）"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__on_change_field_ids
msgid "On Change Fields Trigger"
msgstr "變化欄位的觸發器"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order_template.py:0
msgid "Operation not supported"
msgstr "不支援該操作"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__related_plan_id
msgid "Optional Plans"
msgstr "可選計劃"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__order_reference
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Order"
msgstr "訂單"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__date
msgid "Order Date"
msgstr "報價日期"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__invoice_status
msgid "Order Invoice Status"
msgstr "訂單發票狀態"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__name
msgid "Order Reference"
msgstr "訂單關聯"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_tree
msgid "Order Status"
msgstr "訂單狀態"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_sidebar
msgid "Orders"
msgstr "訂單"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log__origin_order_id
msgid "Origin Contract"
msgstr "原始合約"

#. module: sale_subscription
#: model:sale.order.close.reason,name:sale_subscription.close_reason_5
msgid "Other"
msgstr "其他"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "Over"
msgstr "超出"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_sidebar
msgid "Overdue"
msgstr "逾期"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__subscription_id
msgid "Parent Contract"
msgstr "母項合約"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_line__parent_line_id
msgid "Parent Line"
msgstr "母項資料行"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_tree
msgid "Parent Subscription"
msgstr "母項定期訂購"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/res_partner.py:0
msgid "Partner Subscription"
msgstr "合作夥伴定期訂購"

#. module: sale_subscription
#: model:ir.actions.server,name:sale_subscription.model_sale_order_subscription_pause_record
msgid "Pause Subscription"
msgstr "暫停定期訂購"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order__subscription_state__4_paused
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__subscription_state__4_paused
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__subscription_state_from__4_paused
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log__subscription_state__4_paused
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__subscription_state__4_paused
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_subscription_report__subscription_state__4_paused
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Paused"
msgstr "已暫停"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "Pay now"
msgstr "立即付款"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_kanban
msgid "Payment Failure"
msgstr "付款失敗"

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_payment_provider
msgid "Payment Provider"
msgstr "付款服務商"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__payment_term_id
msgid "Payment Terms"
msgstr "付款條件"

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_payment_token
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__payment_token_id
msgid "Payment Token"
msgstr "付款代碼(token)"

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_payment_transaction
msgid "Payment Transaction"
msgstr "付款交易"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "Payment not recorded"
msgstr "付款未能記錄"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "Payment recorded: %s"
msgstr "已記錄付款：%s"

#. module: sale_subscription
#: model:mail.template,subject:sale_subscription.email_payment_reminder
msgid ""
"Payment reminder for subscription {{ object.client_order_ref or object.name "
"}}"
msgstr "{{ object.client_order_ref or object.name }} 定期訂購付款提醒"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__payment_transaction__renewal_state__pending
msgid "Pending"
msgstr "等待付款"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__pending_transaction
msgid "Pending Transaction"
msgstr "等待中交易"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
msgid "Pending transaction"
msgstr "等待交易"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_alert__mrr_change_period
msgid "Period over which the KPI is calculated"
msgstr "計算 KPI 的期間"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__plan_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__plan_id
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
msgid "Plan"
msgstr "計劃"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "Plan:"
msgstr "計劃："

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"Please add a recurring plan on the subscription or remove the recurring "
"product."
msgstr "請在訂購中加入定期計劃，或刪除定期產品。"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"Please add a recurring product in the subscription or remove the recurring "
"plan."
msgstr "請在訂購中加入定期產品，或刪除定期計劃。"

#. module: sale_subscription
#. odoo-javascript
#: code:addons/sale_subscription/static/src/xml/payment_form_templates.xml:0
msgid "Please provide another payment method for these subscriptions first."
msgstr "請先為這些定期訂購提供另一種付款方式。"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"Please remove the recurring plan on the subscription before sending the "
"email."
msgstr "請在發送電子郵件之前，取消訂閱的定期計劃。"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"Please set a recurring plan on the subscription before sending the email."
msgstr "請在發送電子郵件之前，設置訂閱的定期計劃。"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_close_reason_view_form
msgid "Portal: retention step"
msgstr "客戶頁面：挽留步驟"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_sale_order_line_list
msgid "Price"
msgstr "價格"

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_product_pricelist
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_line__pricelist_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__pricelist_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_pricing__pricelist_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__pricelist_id
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_sales_order_line_filter
msgid "Pricelist"
msgstr "價格表"

#. module: sale_subscription
#: model:ir.ui.menu,name:sale_subscription.menu_sale_subscription_pricelist
msgid "Pricelists"
msgstr "價格表"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_plan_view_form
msgid "Pricing"
msgstr "定價"

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_sale_subscription_pricing
msgid "Pricing rule of subscription products"
msgstr "定期訂購產品定價規則"

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_product_template
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__product_tmpl_id
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_sale_order_line_list
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_sales_order_line_filter
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Product"
msgstr "產品"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__categ_id
msgid "Product Category"
msgstr "產品分類"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__product_id
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_sales_order_line_filter
msgid "Product Variant"
msgstr "產品款式"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_pricing__product_variant_ids
msgid "Product Variants"
msgstr "產品款式"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_sale_order_line_list
msgid "Product Varient"
msgstr "產品款式變體"

#. module: sale_subscription
#: model:ir.actions.act_window,name:sale_subscription.product_action_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_pricing__product_template_id
#: model:ir.ui.menu,name:sale_subscription.menu_sale_subscription_product
#: model:ir.ui.menu,name:sale_subscription.product_menu_catalog
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "Products"
msgstr "產品"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__qty_delivered
msgid "Qty Delivered"
msgstr "已送貨數量"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__qty_invoiced
msgid "Qty Invoiced"
msgstr "已開立發票數量"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__product_uom_qty
msgid "Qty Ordered"
msgstr "訂購數量"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__qty_to_deliver
msgid "Qty To Deliver"
msgstr "要交付的數量"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__qty_to_invoice
msgid "Qty To Invoice"
msgstr "待開立發票數量"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_analysis_view_graph
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_analysis_view_pivot
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_analysis_view_tree
msgid "Quantity"
msgstr "數量"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order__subscription_state__1_draft
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__order_state__draft
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__subscription_state__1_draft
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__subscription_state_from__1_draft
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log__subscription_state__1_draft
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__state__draft
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__subscription_state__1_draft
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_subscription_report__subscription_state__1_draft
msgid "Quotation"
msgstr "報價"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__order_state__sent
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__state__sent
msgid "Quotation Sent"
msgstr "已送出報價單"

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_sale_order_template
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_tree
msgid "Quotation Template"
msgstr "報價單範本"

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_sale_order_template_line
msgid "Quotation Template Line"
msgstr "報價單範本資料行"

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_sale_order_template_option
msgid "Quotation Template Option"
msgstr "報價單範本選項"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_digest
msgid "Quotation Template:"
msgstr "報價單範本："

#. module: sale_subscription
#: model:ir.actions.act_window,name:sale_subscription.sale_subscription_template_action
#: model:ir.ui.menu,name:sale_subscription.menu_template_of_subscription
msgid "Quotation Templates"
msgstr "報價單範本"

#. module: sale_subscription
#: model:ir.actions.act_window,name:sale_subscription.sale_subscription_action_quotes
#: model:ir.ui.menu,name:sale_subscription.menu_sale_subscription_quotes
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Quotations"
msgstr "報價單"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__rating_avg_text
msgid "Rating Avg Text"
msgstr "平均評分文字"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "最新意見評分"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__rating_last_image
msgid "Rating Last Image"
msgstr "最新評分圖像"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__rating_last_value
msgid "Rating Last Value"
msgstr "最新評分值"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__rating_operator
msgid "Rating Operator"
msgstr "評分操作員"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__rating_percentage
msgid "Rating Percentage"
msgstr "評分百分比"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__rating_percentage_satisfaction
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "Rating Satisfaction"
msgstr "評分滿意度"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_alert__rating_percentage
msgid ""
"Rating Satisfaction is the ratio of positive rating to total number of "
"rating."
msgstr "評分滿意度是正面評分佔評分總數的比率。"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__rating_last_text
msgid "Rating Text"
msgstr "評分文字"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__rating_count
msgid "Rating count"
msgstr "評分數目"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__rating_ids
msgid "Ratings"
msgstr "評分"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_close_reason__name
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_close_reason_wizard_view_form
msgid "Reason"
msgstr "原因"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__record_getter
msgid "Record Getter"
msgstr "記錄獲取器"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__is_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_tree
#: model_terms:ir.ui.view,arch_db:sale_subscription.view_order_product_search_inherit
#: model_terms:ir.ui.view,arch_db:sale_subscription.view_sales_order_filter_subscription
msgid "Recurring"
msgstr "經常性"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__recurring_details
msgid "Recurring Details"
msgstr "重複詳情"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__plan_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_line__subscription_plan_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log__plan_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_template__plan_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_pricing__plan_id
#: model_terms:ir.ui.view,arch_db:sale_subscription.portal_my_subscriptions
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_plan_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_plan_view_form
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Recurring Plan"
msgstr "循環計劃"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_portal_content_inherit
msgid "Recurring Plan:"
msgstr "重複計劃："

#. module: sale_subscription
#: model:ir.actions.act_window,name:sale_subscription.sale_subscription_plan_action
#: model:ir.ui.menu,name:sale_subscription.menu_sale_subscription_plans
msgid "Recurring Plans"
msgstr "循環計劃"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_pricing__price
msgid "Recurring Price"
msgstr "經常性價格"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.product_pricelist_view
#: model_terms:ir.ui.view,arch_db:sale_subscription.product_template_view_form_recurring
msgid "Recurring Prices"
msgstr "經常價格"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_product_pricelist__product_subscription_pricing_ids
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__product_subscription_pricing_ids
msgid "Recurring Pricing"
msgstr "經常性價錢"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__recurring_total
msgid "Recurring Revenue"
msgstr "每月遞延收入"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
#: model:ir.model.constraint,message:sale_subscription.constraint_sale_subscription_plan_check_for_valid_billing_period_value
msgid ""
"Recurring period must be a positive number. Please ensure the input is a "
"valid positive numeric value."
msgstr "重複周期必須是正數。請確保輸入有效的正數數值。"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "Reference:"
msgstr "編號："

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__payment_transaction__renewal_state__cancel
msgid "Refused"
msgstr "已拒絕"

#. module: sale_subscription
#: model:product.template,name:sale_subscription.product_fee_product_template
#: model:sale.order.template.line,name:sale_subscription.montly_template_line2
msgid "Registration fee"
msgstr "註冊費"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__user_extend
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__user_extend
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_plan_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_sidebar
msgid "Renew"
msgstr "續訂"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__renewal_count
msgid "Renewal Count"
msgstr "續訂數目"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order__subscription_state__2_renewal
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__subscription_state__2_renewal
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__subscription_state_from__2_renewal
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log__subscription_state__2_renewal
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__subscription_state__2_renewal
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_subscription_report__subscription_state__2_renewal
msgid "Renewal Quotation"
msgstr "續訂報價"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "Renewal Quotations"
msgstr "續訂報價單"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
msgid "Renewal Quote"
msgstr "續訂報價"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_payment_transaction__renewal_state
msgid "Renewal State"
msgstr "續訂狀態"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order__subscription_state__5_renewed
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__subscription_state__5_renewed
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__subscription_state_from__5_renewed
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log__subscription_state__5_renewed
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__subscription_state__5_renewed
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_subscription_report__subscription_state__5_renewed
msgid "Renewed"
msgstr "已續訂"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
msgid "Reopen"
msgstr "重新打開"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "Reopen your subscription"
msgstr "重新啟用訂購計劃"

#. module: sale_subscription
#: model:ir.ui.menu,name:sale_subscription.menu_sale_subscription_report
msgid "Reporting"
msgstr "報表"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
msgid ""
"Request a online payment from the customer to confirm the order. For a "
"subscription, a payment will be required also before each renewal"
msgstr "要求客戶在線付款，以確認訂單。至於訂閱計劃，每次續訂前也需要付款。"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__activity_user_id
msgid "Responsible"
msgstr "負責人"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
msgid "Resume"
msgstr "恢復"

#. module: sale_subscription
#: model:ir.ui.menu,name:sale_subscription.menu_sale_subscription_report_cohort
msgid "Retention"
msgstr "保留"

#. module: sale_subscription
#: model:ir.actions.act_window,name:sale_subscription.sale_subscription_report_cohort_action
msgid "Retention Analysis"
msgstr "保留分析"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__message_has_sms_error
msgid "SMS Delivery error"
msgstr "簡訊發送錯誤"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__sms_template_id
msgid "SMS Template"
msgstr "簡訊範本"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_kanban
msgid "Sad face"
msgstr "悲傷的表情"

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_sale_order_option
msgid "Sale Options"
msgstr "銷售選項"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log__order_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__order_id
msgid "Sale Order"
msgstr "銷售訂單"

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_sale_order_alert
msgid "Sale Order Alert"
msgstr "銷售訂單警示"

#. module: sale_subscription
#: model:ir.actions.act_window,name:sale_subscription.action_sale_order_lines
msgid "Sale Order Lines"
msgstr "銷售訂單資料行"

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_sale_order_log
msgid "Sale Order Log"
msgstr "銷售訂單記錄"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.view_sale_order_log_analysis_graph
#: model_terms:ir.ui.view,arch_db:sale_subscription.view_sale_order_log_growth_graph
#: model_terms:ir.ui.view,arch_db:sale_subscription.view_sale_order_log_growth_pivot
msgid "Sale Order Log Analysis"
msgstr "銷售訂單記錄分析"

#. module: sale_subscription
#: model:ir.actions.server,name:sale_subscription.ir_cron_sale_subscription_update_kpi_ir_actions_server
msgid "Sale Subscription: Update KPI"
msgstr "銷售定期訂購：更新 KPI"

#. module: sale_subscription
#: model:ir.actions.server,name:sale_subscription.account_analytic_cron_for_invoice_ir_actions_server
msgid "Sale Subscription: generate recurring invoices and payments"
msgstr "銷售定期訂購：產生經常性發票及付款"

#. module: sale_subscription
#: model:ir.actions.server,name:sale_subscription.send_payment_reminder_ir_actions_server
msgid "Sale Subscription: send reminder for subscriptions with no token"
msgstr "銷售訂閱：為沒有代碼的訂閱發送提醒"

#. module: sale_subscription
#: model:ir.actions.server,name:sale_subscription.account_analytic_cron_ir_actions_server
msgid "Sale Subscription: subscriptions expiration"
msgstr "銷售定期訂購：訂購計劃過期"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_sales_order_line_filter
msgid "Sale order"
msgstr "銷售單"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order_log.py:0
msgid "Sale order log: %s"
msgstr "銷售單日誌：%s"

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_sale_advance_payment_inv
msgid "Sales Advance Payment Invoice"
msgstr "銷售預先付款發票"

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_sale_report
msgid "Sales Analysis Report"
msgstr "銷售分析報告"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
msgid "Sales History"
msgstr "銷售歷程"

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_sale_order_log_report
msgid "Sales Log Analysis Report"
msgstr "銷售記錄分析報表"

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_sale_order
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__order_state__sale
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__state__sale
msgid "Sales Order"
msgstr "銷售訂單"

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_sale_order_line
msgid "Sales Order Line"
msgstr "銷售訂單資料行"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__team_ids
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log__team_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__team_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__team_id
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Sales Team"
msgstr "銷售團隊"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__activity_user__channel_leader
msgid "Sales Team Leader"
msgstr "銷售團隊隊長"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log__user_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__user_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__user_id
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Salesperson"
msgstr "銷售員"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_sales_order_line_filter
msgid "Search Sales Order"
msgstr "搜尋銷售訂單"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_template_view_search
msgid "Search Template"
msgstr "搜尋範本"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_subscription_pricing__product_variant_ids
msgid ""
"Select Variants of the Product for which this rule applies. Leave empty if "
"this rule applies for any variant of this template."
msgstr "選擇套用此規則的產品款式。若想規則套用至此範本的任何款式，請留空。"

#. module: sale_subscription
#. odoo-javascript
#: code:addons/sale_subscription/static/src/js/tours/sale_subscription.js:0
msgid "Select a recurring product"
msgstr "選擇一個經常性產品"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_subscription_pricing__product_template_id
msgid "Select products on which this pricing will be applied."
msgstr "選擇套用此定價的產品。"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_close_reason_view_form
msgid "Selectable in Portal"
msgstr "在客戶頁面可選取"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_plan_view_form
msgid "Self-Service"
msgstr "自助服務"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__payment_transaction__subscription_action__automatic_send_mail
msgid "Send Mail (automatic payment)"
msgstr "傳送郵件（自動付款）"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__payment_transaction__subscription_action__manual_send_mail
msgid "Send Mail (manual payment)"
msgstr "傳送郵件（手動付款）"

#. module: sale_subscription
#: model:ir.actions.act_window,name:sale_subscription.sale_subscription_act_window_sms_composer_multi
#: model:ir.actions.act_window,name:sale_subscription.sale_subscription_act_window_sms_composer_single
msgid "Send an SMS Text Message"
msgstr "傳送 SMS 文字訊息"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__action__sms
msgid "Send an SMS Text Message to the customer"
msgstr "傳送 SMS 文字訊息給客戶"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__action__mail_post
msgid "Send an email to the customer"
msgstr "傳送電郵給客戶"

#. module: sale_subscription
#: model:mail.template,description:sale_subscription.email_payment_close
msgid ""
"Sent to customer to indicate that subscription is automatically terminated"
msgstr "發送給客戶以表明定期訂購會自動終止"

#. module: sale_subscription
#: model:mail.template,description:sale_subscription.email_payment_reminder
msgid ""
"Sent to customer when payment failed but subscription is not yet cancelled"
msgstr "付款失敗但定期訂購尚未取消時，發送給客戶"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_close_reason__sequence
msgid "Sequence"
msgstr "序號"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__action_id
msgid "Server Action"
msgstr "伺服器動作"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__action__set_health_value
msgid "Set Contract Health value"
msgstr "設定合約健康度"

#. module: sale_subscription
#: model:mail.template,description:sale_subscription.mail_template_subscription_rating
msgid ""
"Set on subscription's stage (e.g. Closed, Upsell) to ask a rating to "
"customers"
msgstr "在定期訂購的階段設定（例如已關閉、追加銷售）以向客戶詢問評分"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.payment_form_inherit
msgid "Set payment method for subscription"
msgstr "為訂購計劃設定付款方式"

#. module: sale_subscription
#: model:ir.ui.menu,name:sale_subscription.menu_sale_subscription_settings
msgid "Settings"
msgstr "設定"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__starred
msgid "Show Subscription on dashboard"
msgstr "顯示定期訂購Dashboard"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Show all records which has next action date is before today"
msgstr "顯示在今天之前的下一個行動日期的所有記錄"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__health
#: model:ir.model.fields,help:sale_subscription.field_sale_order_alert__health
msgid "Show the health status"
msgstr "顯示健康度狀態"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_alert__trg_selection_field_id
msgid ""
"Some triggers need a reference to a selection field. This field is used to "
"store it."
msgstr "某些觸發器需要選擇欄位的引用。這個欄位就是用來儲存的。"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_alert__trg_field_ref
msgid ""
"Some triggers need a reference to another field. This field is used to store"
" it."
msgstr "某些觸發器需要另一個欄位的引用。這個欄位就是用來儲存它的。"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__source_id
msgid "Source"
msgstr "來源"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__product_ids
msgid "Specific Products"
msgstr "特殊產品"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__activity_user_ids
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__activity_user__users
msgid "Specific Users"
msgstr "特定使用者"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__subscription_state
msgid "Stage"
msgstr "階段"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__subscription_state_from
msgid "Stage from"
msgstr "階段由"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "Stage goes from"
msgstr "階段從"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__start_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_line__subscription_start_date
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_sales_order_line_filter
msgid "Start Date"
msgstr "開始日期"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_digest
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "Start Date:"
msgstr "開始日期："

#. module: sale_subscription
#: model:mail.message.subtype,name:sale_subscription.subtype_state_change
msgid "State Change"
msgstr "狀態變更"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/controllers/portal.py:0
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__order_state
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__state
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__state
#: model_terms:ir.ui.view,arch_db:sale_subscription.portal_my_subscriptions
msgid "Status"
msgstr "狀態"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_change_customer_view_form
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_close_reason_wizard_view_form
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_template
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_so_portal_template
msgid "Submit"
msgstr "提交"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
#: model:ir.model.fields,field_description:sale_subscription.field_account_move_line__subscription_id
#: model_terms:ir.ui.view,arch_db:sale_subscription.portal_my_subscriptions
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_template_view_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_view_cohort
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_view_cohort
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_sale_order_line_list
msgid "Subscription"
msgstr "定期訂購"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"Subscription %(link)s has been cancelled. The parent order %(parent_link)s has been reopened.\n"
"                                                You should close %(parent_link)s if the customer churned, or renew it if the customer continue the service.\n"
"                                                Note: if you already created a new subscription instead of renewing it, please cancel your newly\n"
"                                                created subscription and renew %(parent_link)s instead"
msgstr ""
"定期訂購計劃 %(link)s 已被取消。其母項訂單 %(parent_link)s 已重新開啟。\n"
"                                                若屬客戶流失，你應該關閉 %(parent_link)s；如果客戶繼續使用服務，你應該續訂它。\n"
"                                                請注意：若已建立新的訂購計劃而不是續訂，請取消\n"
"                                                新建立的訂購計劃，並改為續訂 %(parent_link)s"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "Subscription %s has been reopened. The end date has been removed"
msgstr "定期訂購計劃 %s 已重新開啟。結束日期已移除"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_payment_transaction__subscription_action
msgid "Subscription Action"
msgstr "定期訂購操作"

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_sale_subscription_report
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_analysis_view_graph
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_analysis_view_pivot
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_analysis_view_tree
msgid "Subscription Analysis"
msgstr "定期訂購分析"

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_sale_subscription_change_customer_wizard
msgid "Subscription Change Customer Wizard"
msgstr "定期訂購更改客戶精靈"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__subscription_child_ids
msgid "Subscription Child"
msgstr "定期訂購子項"

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_sale_order_close_reason
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_close_reason_view_form
msgid "Subscription Close Reason"
msgstr "定期訂購關閉原因"

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_sale_subscription_close_reason_wizard
msgid "Subscription Close Reason Wizard"
msgstr "定期訂購關閉原因精靈"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__subscription_count
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__subscription_line_count
msgid "Subscription Count"
msgstr "定期訂購數目"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_subscription_plan.py:0
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_sale_order_line_list
msgid "Subscription Items"
msgstr "訂閱項目"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.view_sale_order_log_growth_tree
msgid "Subscription Log Analysis"
msgstr "定期訂購記錄分析"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__order_log_ids
msgid "Subscription Logs"
msgstr "定期訂購記錄"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_sidebar
msgid "Subscription Manager:"
msgstr "定期訂購經理："

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_sale_subscription_plan
msgid "Subscription Plan"
msgstr "定期訂購計劃"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__subscription_plan_ids
msgid "Subscription Plans"
msgstr "定期訂購計劃"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_product_product__recurring_invoice
#: model:ir.model.fields,field_description:sale_subscription.field_product_template__recurring_invoice
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_line__recurring_invoice
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_template_line__recurring_invoice
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_template_option__recurring_invoice
msgid "Subscription Product"
msgstr "定期訂購產品"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
msgid "Subscription Quotation"
msgstr "定期訂購報價"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__activity_user__contract
msgid "Subscription Salesperson"
msgstr "定期訂購銷售員"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log__subscription_state
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__subscription_state
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__subscription_state
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
msgid "Subscription State"
msgstr "定期訂購狀態"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__subscription_state
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Subscription Status"
msgstr "定期訂購的現狀況"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__template_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__template_id
msgid "Subscription Template"
msgstr "定期訂購範本"

#. module: sale_subscription
#: model:sale.order.close.reason,name:sale_subscription.close_reason_2
msgid "Subscription does not meet my requirements"
msgstr "定期訂購計劃未能滿足我的要求"

#. module: sale_subscription
#: model:sale.order.close.reason,name:sale_subscription.close_reason_1
msgid "Subscription is too expensive"
msgstr "定期訂購計劃太貴"

#. module: sale_subscription
#: model:sale.order.close.reason,name:sale_subscription.close_reason_3
msgid "Subscription reached its end date"
msgstr "定期訂購計劃已達結束日期"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_log__subscription_state
msgid "Subscription stage when the change occurred"
msgstr "發生變更時的定期訂購階段"

#. module: sale_subscription
#: model:mail.message.subtype,description:sale_subscription.subtype_state_change
msgid "Subscription state has changed"
msgstr "定期訂購狀態已變更"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_portal_content_inherit
msgid "Subscription:"
msgstr "定期訂購計劃："

#. module: sale_subscription
#: model:mail.template,name:sale_subscription.mail_template_subscription_alert
msgid "Subscription: Default Email Alert"
msgstr "定期訂購：預設電郵警示"

#. module: sale_subscription
#: model:sms.template,name:sale_subscription.sms_template_data_default_alert
msgid "Subscription: Default SMS Alert"
msgstr "定期訂購：預設 SMS 短訊警示"

#. module: sale_subscription
#: model:mail.template,name:sale_subscription.email_payment_close
msgid "Subscription: Payment Failure"
msgstr "定期訂購：付款失敗"

#. module: sale_subscription
#: model:mail.template,name:sale_subscription.email_payment_reminder
msgid "Subscription: Payment Reminder"
msgstr "定期訂購：付款提醒"

#. module: sale_subscription
#: model:sms.template,name:sale_subscription.sms_template_data_payment_failure
msgid "Subscription: Payment failure"
msgstr "定期訂購：付款失敗"

#. module: sale_subscription
#: model:sms.template,name:sale_subscription.sms_template_data_payment_reminder
msgid "Subscription: Payment reminder"
msgstr "定期訂購：付款提醒"

#. module: sale_subscription
#: model:mail.template,name:sale_subscription.mail_template_subscription_rating
msgid "Subscription: Rating Request"
msgstr "定期訂購：評分請求"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order_alert.py:0
#: code:addons/sale_subscription/models/sale_subscription_plan.py:0
#: model:ir.actions.act_window,name:sale_subscription.sale_subscription_action
#: model:ir.actions.act_window,name:sale_subscription.sale_subscription_action_filtered
#: model:ir.model.fields,field_description:sale_subscription.field_res_partner__subscription_count
#: model:ir.model.fields,field_description:sale_subscription.field_res_users__subscription_count
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__active_subs_count
#: model:ir.module.category,name:sale_subscription.module_category_subscription_management
#: model:ir.ui.menu,name:sale_subscription.menu_sale_subscription
#: model:ir.ui.menu,name:sale_subscription.menu_sale_subscription_action
#: model:ir.ui.menu,name:sale_subscription.menu_sale_subscription_analysis
#: model:ir.ui.menu,name:sale_subscription.menu_sale_subscription_root
#: model_terms:ir.ui.view,arch_db:sale_subscription.portal_my_home_menu_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.portal_my_home_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.portal_my_subscriptions
#: model_terms:ir.ui.view,arch_db:sale_subscription.product_template_search_view_inherit_sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.product_template_view_form_recurring
#: model_terms:ir.ui.view,arch_db:sale_subscription.res_partner_view_inherit_sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_activity
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Subscriptions"
msgstr "定期訂購"

#. module: sale_subscription
#: model:ir.actions.act_window,name:sale_subscription.sale_subscription_report_analysis_action
msgid "Subscriptions Analysis"
msgstr "定期訂購分析"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Subscriptions that are not assigned to an account manager."
msgstr "尚未指派給客戶經理的定期訂購。"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_analysis_view_tree
msgid "Sum of Monthly Recurring"
msgstr "每月經常收入總和"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_analysis_view_tree
msgid "Sum of Yearly Recurring Revenue"
msgstr "每年經常收入總和"

#. module: sale_subscription
#: model:base.automation,name:sale_subscription.subscription_alert_percent_revenue_base_automation
msgid "Take action on less satisfied clients"
msgstr "就不太滿意的客戶採取行動"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__team_user_id
msgid "Team Leader"
msgstr "團隊隊長"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_template
msgid "Tell us, why are you leaving?"
msgstr "告訴我們，為什麼要離開？"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Template"
msgstr "範本文字"

#. module: sale_subscription
#: model:mail.template,description:sale_subscription.mail_template_subscription_alert
msgid ""
"Template to be used on customized alerts for subscriptions requiring "
"attention"
msgstr "警報範本，用於需要關注的定期訂購的自訂警報"

#. module: sale_subscription
#: model_terms:ir.actions.act_window,help:sale_subscription.sale_subscription_template_action
msgid ""
"Templates are used to prefigure subscription that\n"
"                can be selected by the salespeople to quickly configure the\n"
"                terms and conditions of the subscription."
msgstr ""
"可使用模版來預示訂閱\n"
" 銷售人員可選擇此模版來快速設定\n"
" 訂閱的條款"

#. module: sale_subscription
#: model:mail.template,subject:sale_subscription.email_payment_close
msgid ""
"Termination of subscription {{ object.client_order_ref or object.name }}"
msgstr "終止定期訂購 {{ object.client_order_ref or object.name }}"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_account_move_line__subscription_mrr
msgid ""
"The MRR is computed by dividing the signed amount (in company currency) by the amount of time between the start and end dates converted in months.\n"
"This allows comparison of invoice lines created by subscriptions with different temporalities.\n"
"The computation assumes that 1 month is comprised of exactly 30 days, regardless  of the actual length of the month."
msgstr ""
"MRR 每月經常收入的計算方法，是將已簽約成交金額（以公司貨幣計價）除以開始日期至結束日期之間的時間（以月為單位）。\n"
"這樣可以比較不同時間長度的定期訂購所建立的發票資料行。\n"
"此計算方法假設每個月都由 30 天組成，不論該月的實際日數。"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_alert__trigger_field_ids
msgid ""
"The automation rule will be triggered if and only if one of these fields is "
"updated.If empty, all fields are watched."
msgstr "如果為空，則所有欄位都會被監視。"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_subscription_pricing.py:0
msgid "The company of the plan is different from the company of the pricelist"
msgstr "服務計劃的公司與價目表的公司不同"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"The deferred settings are not properly set. Please complete them to generate"
" subscription deferred revenues"
msgstr "遞延設定未正確設置。請完成設定，以產生訂購計劃遞延收入"

#. module: sale_subscription
#: model:ir.model.constraint,message:sale_subscription.constraint_sale_order_template_check_duration_value
msgid "The duration can't be negative or 0."
msgstr "持續時長不可為負數或 0。"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__first_contract_date
msgid ""
"The first contract date is the start date of the first contract of the "
"sequence. It is common across a subscription and its renewals."
msgstr "首份合約日期是序列中第一個合約的開始日期。在同一個定期訂購及其續訂安排中，此資料維持相同。"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"The following recurring orders have draft invoices. Please Confirm them or "
"cancel them before creating new invoices. %s."
msgstr "以下經常性訂單有草稿發票。請在建立新發票之前，確認或取消它們。 %s。"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/account_move.py:0
msgid ""
"The following refund %s has been made on this contract. Please check the "
"next invoice date if necessary."
msgstr "已就此合約完成以下退款 %s 。如有需要，請檢查下次發票日期。"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"The last invoice (%s) of this subscription is unpaid after the due date."
msgstr "此定期訂購的最新一張發票 (%s) 在到期日後尚未付款。"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__pending_transaction
msgid "The last transaction of the order is currently pending"
msgstr "訂單的最後一筆交易目前正在等待處理"

#. module: sale_subscription
#: model:ir.model.constraint,message:sale_subscription.constraint_sale_order_check_start_date_lower_next_invoice_date
msgid "The next invoice date of a sale order should be after its start date."
msgstr "銷售訂單的下次發票日期，應在開始日期之後。"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__next_invoice_date
#: model:ir.model.fields,help:sale_subscription.field_sale_order_line__next_invoice_date
msgid ""
"The next invoice will be created on this date then the period will be "
"extended."
msgstr "將在此日期上建立下一張發票，那麼期間將會延長。"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/controllers/portal.py:0
msgid ""
"The payment method you selected can only pay amounts up to %s. Please create"
" or select another one."
msgstr "你選擇的付款方式最多只可支付金額為 %s 的交易。請建立或選擇其他付款方式。"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order_close_reason.py:0
msgid ""
"The reason %s is required by the Subscription application and cannot be "
"deleted."
msgstr "原因 %s 是定期訂購應用程式所需的，不可刪除。"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_close_reason_view_form
msgid "The reason for closing a subscription"
msgstr "停止訂購原因"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_close_reason_view_form
msgid "The redirect link of the call to action"
msgstr "行動呼籲的重新導向連結"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "The renewal %s has been cancelled."
msgstr "續訂「%s」已被取消。"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid ""
"The scheduled action for alerts has been deleted. Update the Subscriptions "
"module to re-create it."
msgstr "警報的預先排程操作已被刪除。請更新定期訂購模組，以重新建立它。"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__start_date
#: model:ir.model.fields,help:sale_subscription.field_sale_order_line__subscription_start_date
msgid "The start date indicate when the subscription periods begin."
msgstr "開始日期表示定期訂購期間開始的日期。"

#. module: sale_subscription
#: model:sale.order.close.reason,name:sale_subscription.close_reason_renew
msgid "The subscription was renewed with a new plan"
msgstr "此定期訂購計劃已使用新的服務計劃續訂"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_close_reason_view_form
msgid "The text to display on the call to action"
msgstr "行動呼籲的顯示文字"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "The upsell %s has been cancelled."
msgstr "追加銷售「%s」已被取消。"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"The upsell %s has been cancelled. Please recheck the quantities as they may "
"have been affected by this cancellation."
msgstr "追加銷售 %s 已取消。請重新檢查數量，因為此次取消可能會影響它們。"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "The upsell %s has been confirmed."
msgstr "追加銷售「%s」已獲確認。"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_subscription_pricing.py:0
msgid "There are multiple pricings for an unique product, plan and pricelist."
msgstr "一項獨特的產品、計劃和價目表存有多種定價。"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_template
msgid "There is a"
msgstr "有一個"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_kanban
msgid ""
"This bar allows to filter the opportunities based on scheduled activities."
msgstr "這個工具條允許您根據安排的事件來過濾商機。"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_alert__record_getter
msgid ""
"This code will be run to find on which record the automation rule should be "
"run."
msgstr "該代碼將用於查找應在哪條記錄上運行自動化規則。"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_close_reason_view_form
msgid ""
"This message will be displayed to convince the customer to stay (e.g., We "
"don't want you to leave, can we offer to schedule a meeting with your "
"account manager?)"
msgstr "將顯示此訊息以挽留客戶（例如，我們不想讓您離開，我們可以安排與您的客戶經理會面嗎？）"

#. module: sale_subscription
#. odoo-javascript
#: code:addons/sale_subscription/static/src/xml/payment_form_templates.xml:0
msgid ""
"This payment method cannot be deleted, because it is currently linked to the\n"
"                following active subscriptions:"
msgstr ""
"此付款方式無法刪除，因它目前連結至\n"
"                以下有效訂購計劃："

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "This subscription is renewed in %s with a change of plan."
msgstr "此定期訂購計劃已於 %s 改用新的服務計劃續訂。"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_digest
msgid "This subscription is the renewal of subscription"
msgstr "此次定期訂購是續訂"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_digest
msgid "This upsell order has been created from the subscription"
msgstr "此追加銷售訂單是從該定期訂購建立的"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid ""
"This will trigger the action on all linked subsccriptions, regardless of "
"possible timed conditions. Are you sure you want to proceed?"
msgstr "這會對所有已連結定期訂購觸發操作，不論其有否已設定按時觸發條件。確定要繼續？"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__trigger_condition__on_time
msgid "Timed Condition"
msgstr "按時觸發條件"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__activity_summary
msgid "Title"
msgstr "標題"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "To Invoice"
msgstr "待開立發票"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/controllers/portal.py:0
#: model:ir.actions.act_window,name:sale_subscription.sale_subscription_action_pending
#: model:ir.ui.menu,name:sale_subscription.menu_sale_subscription_pending
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
msgid "To Renew"
msgstr "待續約"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
msgid "To renew"
msgstr "待續約"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Today Activities"
msgstr "今天活動"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__price_total
#: model_terms:ir.ui.view,arch_db:sale_subscription.portal_my_subscriptions
msgid "Total"
msgstr "總計"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__non_recurring_total
msgid "Total Non Recurring Revenue"
msgstr "非重複性收入總計"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_portal_content_inherit
msgid "Total Quantity"
msgstr "數量總計"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__recurring_total
msgid "Total Recurring"
msgstr "經常收入總計"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log__event_type__3_transfer
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__event_type__3_transfer
msgid "Transfer"
msgstr "轉移"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__trigger
msgid "Trigger"
msgstr "觸發"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__trg_date_id
msgid "Trigger Date"
msgstr "觸發日期"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__trg_selection_field_id
msgid "Trigger Field"
msgstr "觸發欄位"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__trg_field_ref_model_name
msgid "Trigger Field Model"
msgstr "觸發欄位模型"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__trigger_field_ids
msgid "Trigger Fields"
msgstr "觸發欄位"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "Trigger Now"
msgstr "立即觸發"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__trigger_condition
msgid "Trigger On"
msgstr "觸發時間"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__trg_field_ref
msgid "Trigger Reference"
msgstr "觸發參考"

#. module: sale_subscription
#: model_terms:ir.actions.act_window,help:sale_subscription.sale_subscription_alert_action
msgid ""
"Trigger alerts for salespersons or customers: churn, invoice not paid, "
"upsell, etc."
msgstr "向銷售人員或客戶觸發警報：流失、發票未付款、追加銷售，等等。"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_close_reason__retention_message
msgid ""
"Try to prevent customers from leaving and closing their subscriptions, "
"thanks to a catchy message and a call to action."
msgstr "透過吸引人的訊息和行動呼籲，嘗試避免客戶離開和關閉訂購。"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log__event_type
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__event_type
msgid "Type of event"
msgstr "事件類型"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Unassigned"
msgstr "未分派"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__billing_period_unit
msgid "Unit"
msgstr "單位"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__price_unit
msgid "Unit Price"
msgstr "單價"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__product_uom
msgid "Unit of Measure"
msgstr "量度單位"

#. module: sale_subscription
#: model:sale.order.close.reason,name:sale_subscription.close_reason_unknown
msgid "Unknown"
msgstr "未知"

#. module: sale_subscription
#: model:sale.order.close.reason,name:sale_subscription.close_reason_unpaid_subscription
msgid "Unpaid subscription"
msgstr "未付款定期訂購"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_subscription_plan__auto_close_limit
msgid ""
"Unpaid subscription after the due date majored by this number of days will be automatically closed by the subscriptions expiration scheduled action. \n"
"If the chosen payment method has failed to renew the subscription after this time, the subscription is automatically closed."
msgstr ""
"若定期訂購在到期日之後仍未付款多達此天數，將以定期訂購到期預排操作，自動關閉訂購計劃。\n"
"如果在此時間之後，所選的付款方式未能續訂，定期訂購計劃將自動關閉。"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__untaxed_amount_invoiced
msgid "Untaxed Amount Invoiced"
msgstr "未連稅已開立發票金額"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__untaxed_amount_to_invoice
msgid "Untaxed Amount To Invoice"
msgstr "未連稅待開立發票金額"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__price_subtotal
msgid "Untaxed Total"
msgstr "未連稅總額"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_digest
msgid "Untaxed Total:"
msgstr "未連稅總額："

#. module: sale_subscription
#: model:base.automation,name:sale_subscription.subscription_alert_bad_health_base_automation
msgid "Update health value according to MRR"
msgstr "根據 MRR 每月經常收入更新健康值"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order__subscription_state__7_upsell
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__subscription_state__7_upsell
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__subscription_state_from__7_upsell
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log__subscription_state__7_upsell
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__subscription_state__7_upsell
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_subscription_report__subscription_state__7_upsell
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
msgid "Upsell"
msgstr "追加銷售"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"Upsell %(order)s for customer %(customer)s for the period %(date_start)s to "
"%(date_end)s %(nl)s%(lines)s"
msgstr ""
"追加銷售：訂單 %(order)s，客戶：%(customer)s，期間：%(date_start)s 至 %(date_end)s "
"%(nl)s%(lines)s"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__upsell_count
msgid "Upsell Count"
msgstr "追加銷售數目"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "Upsell Quotations"
msgstr "追加銷售報價單"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
msgid "Upsell Quote"
msgstr "追加銷售報價"

#. module: sale_subscription
#: model_terms:ir.actions.act_window,help:sale_subscription.sale_subscription_action_upsell
msgid ""
"Upsell orders allow you to add or remove products to ongoing subscriptions.\n"
"                It can be created by clicking on the Upsell button from an active subscription."
msgstr ""
"追加銷售訂單可讓你向持續的定期訂購計劃，新增或刪除產品。\n"
"                若要建立，可在生效的訂購計劃內，按「追加銷售」按鈕。"

#. module: sale_subscription
#: model:ir.actions.act_window,name:sale_subscription.sale_subscription_action_upsell
#: model:ir.ui.menu,name:sale_subscription.menu_sale_subscription_upsell
msgid "Upsells"
msgstr "追加銷售"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__url
msgid "Url"
msgstr "網址"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__trg_date_calendar_id
msgid "Use Calendar"
msgstr "使用日曆"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__trg_date_resource_field_id
msgid "Use employee work schedule"
msgstr "使用員工工作計劃"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_alert__trg_date_resource_field_id
msgid "Use the user's working schedule."
msgstr "使用使用者的工作計劃。"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_line__product_template_variant_value_ids
msgid "Variant Values"
msgstr "變體值"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_close_reason__visible_in_portal
msgid "Visible In Portal"
msgstr "客戶頁面可見"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__volume
msgid "Volume"
msgstr "貨量"

#. module: sale_subscription
#. odoo-javascript
#: code:addons/sale_subscription/static/src/js/tours/sale_subscription.js:0
msgid ""
"Want a recurring billing through subscription management? Get started by "
"clicking here"
msgstr "想透過管理定期訂購計劃，進行重複定期收費嗎？請按此處開始"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__warehouse_id
msgid "Warehouse"
msgstr "倉庫"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/product.py:0
msgid "Warning"
msgstr "警告"

#. module: sale_subscription
#. odoo-javascript
#: code:addons/sale_subscription/static/src/js/payment_form.js:0
msgid "Warning!"
msgstr "警告！"

#. module: sale_subscription
#: model_terms:sale.order.close.reason,retention_message:sale_subscription.close_reason_1
msgid "We are sorry to hear that."
msgstr "我們很抱歉聽到這個消息。"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_template
msgid "We are sorry to see you go."
msgstr "很遺憾看見你走。"

#. module: sale_subscription
#: model_terms:sale.order.close.reason,retention_message:sale_subscription.close_reason_1
msgid "We don't want you to leave us like this."
msgstr "我們不希望你就這樣離開我們。"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__webhook_uuid
msgid "Webhook UUID"
msgstr "Webhook 唯一識別碼UUID"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__website_message_ids
msgid "Website Messages"
msgstr "網站資訊"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__website_message_ids
msgid "Website communication history"
msgstr "網站溝通記錄"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_subscription_plan__billing_period_unit__week
msgid "Weeks"
msgstr "星期"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_alert__trg_date_calendar_id
msgid ""
"When calculating a day-based timed condition, it is possibleto use a "
"calendar to compute the date based on working days."
msgstr "在計算以天為單位的定時條件時，可以使用日曆來計算以工作日為單位的日期。"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_alert__trg_date_id
msgid ""
"When should the condition be triggered.\n"
"                If present, will be checked by the scheduler. If empty, will be checked at creation and update."
msgstr ""
"應在何時觸發該條件。\n"
"                如果存在，將由排期程式檢查。如果留空，將在建立和更新時檢查。"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_alert__active
msgid "When unchecked, the rule is hidden and will not be executed."
msgstr "如果不選中，此規則被隱藏且不被執行"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__starred
msgid "Whether this subscription should be displayed on the dashboard or not"
msgstr "是否應該在儀表板上顯示此訂閱"

#. module: sale_subscription
#: model:sale.subscription.plan,name:sale_subscription.subscription_plan_year
msgid "Yearly"
msgstr "每年"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__recurring_yearly
msgid "Yearly Recurring"
msgstr "每年重複"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_template__duration_unit__year
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_subscription_plan__billing_period_unit__year
msgid "Years"
msgstr "年"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_portal_templates_warnings
msgid ""
"You are about to permanently close a subscription that is valid until\n"
"                    &amp;nbsp;"
msgstr ""
"你即將永久關閉一項訂閱，該訂閱的有效期直至：\n"
"                    &amp;nbsp;"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/product.py:0
msgid ""
"You can not change the recurring property of this product because it has "
"been sold already."
msgstr "你無法變更此產品的經常屬性，因為它已售出。"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/wizard/sale_subscription_close_reason_wizard.py:0
msgid ""
"You can not churn a contract that has not been invoiced. Please cancel the "
"contract instead."
msgstr "未開立發票的合約，不可設為流失。請改為取消合約。"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"You can not delete a confirmed subscription. You must first close and cancel"
" it before you can delete it."
msgstr "不可刪除已確認的定期訂購。必須先關閉並取消它，然後才可將其刪除。"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"You can not upsell or renew a subscription that has not been invoiced yet. "
"Please, update directly the %s contract or invoice it first."
msgstr "尚未開立發票的定期訂購，不可續訂或進行追加銷售。請直接更新 %s 合約，或先行開立發票。"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/res_partner.py:0
msgid ""
"You can't archive the partner as it is used in the following recurring "
"orders: %s"
msgstr "不可封存該合作夥伴，因為它仍用於以下經常訂單： %s"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "You cannot cancel a churned renewed subscription."
msgstr "已流失的定期訂購續訂，不可取消。"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "You cannot cancel a subscription that has been invoiced."
msgstr "不可取消已開立發票的定期訂購。"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/wizard/sale_subscription_change_customer_wizard.py:0
msgid "You cannot change the customer of non recurring sale order."
msgstr "非重複性的銷售訂單，不可更改客戶。"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"You cannot create an upsell for this subscription because it :\n"
" - Has not started yet.\n"
" - Has no invoiced period in the future."
msgstr ""
"未能為此定期訂購建立追加銷售，因為它：\n"
"- 還未開始。\n"
"- 未有將來的開立發票期間。"

#. module: sale_subscription
#: model:ir.model.constraint,message:sale_subscription.constraint_sale_order_sale_subscription_state_coherence_2
msgid "You cannot have a draft SO be a confirmed subscription."
msgstr "草稿銷售單不可當作已確認定期訂購。"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "You cannot renew a contract that already has an active subscription. "
msgstr "合約已有正在生效的訂購計劃，不可續訂。"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "You cannot renew a subscription that has been renewed. "
msgstr "不可續訂已經續訂的定期訂購計劃。"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "You cannot reopen a subscription that isn't closed."
msgstr "無法重新打開未關閉的定期訂購。"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"You cannot set to draft a cancelled quotation linked to invoiced "
"subscriptions. Please create a new quotation."
msgstr "若報價單連結至的定期訂購計劃已開立發票，當報價單被取消後，便不可再設為草稿。請建立新的報價單。"

#. module: sale_subscription
#: model:ir.model.constraint,message:sale_subscription.constraint_sale_order_sale_subscription_state_coherence
msgid ""
"You cannot set to draft a confirmed subscription. Please create a new "
"quotation"
msgstr "已確認的定期訂購，不可設為草稿。請建立新的報價。"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "You cannot upsell a subscription using a different currency."
msgstr "不可使用不同的貨幣對定期訂購進行追加銷售。"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"You cannot upsell a subscription whose next invoice date is in the past.\n"
"Please, invoice directly the %s contract."
msgstr ""
"若定期訂購的下次發票日期已過去，便不可進行追加銷售。\n"
"請直接為 %s 合約開立發票。"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"You cannot validate a renewal quotation starting before the next invoice "
"date of the parent contract. Please update the start date after the %s."
msgstr "續訂報價的開始日期若先於母項合約的下次發票日期，便無法驗證。請將開始日期更新為 %s 之後。"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.portal_my_subscriptions
msgid "You don't have any subscriptions yet."
msgstr "你尚未有任何定期訂購。"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_sidebar
msgid "You must be logged in to manage your payment methods."
msgstr "必須登入才可管理你的付款方法。"

#. module: sale_subscription
#: model_terms:ir.actions.act_window,help:sale_subscription.product_action_subscription
msgid ""
"You must define a product for everything you sell or purchase,\n"
"                whether it's a storable product, a consumable or a service."
msgstr ""
"您必須為您銷售或採購的所有產品定義產品，\n"
"                無論是可庫存產品、消耗品還是服務。"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "Your Subscription"
msgstr "你的訂購計劃"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.payment_method_form
msgid "Your payment details will be saved for automatic renewals."
msgstr "你的付款資料會被儲存，供日後自動續訂。"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "Your subscription is about to end on the"
msgstr "你的訂閱即將於以下時間關閉："

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "Your subscription is closed."
msgstr "你的定期訂購已關閉。"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "Your subscription is expired, will be closed soon."
msgstr "你的定期訂購已過期，即將關閉。"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_portal_templates_warnings
msgid "Your subscription will be closed on the"
msgstr "你的訂閱將於以下時間關閉："

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_template
msgid "and"
msgstr "及"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_close_reason_view_form
msgid "button link"
msgstr "按鈕連結"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_close_reason_view_form
msgid "button text"
msgstr "按鈕文字"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "e.g. Closed Subscription"
msgstr "例：已關閉的定期訂購"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "e.g. Discuss proposal"
msgstr "例：討論提案"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_template
msgid "existing for this subscription, please confirm it or reject it."
msgstr "已存在於此訂購計劃內，請確認或拒絕。"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__rating_operator__>
msgid "greater than"
msgstr "大於"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__rating_operator__<
msgid "less than"
msgstr "少於"

#. module: sale_subscription
#: model:sale.order.close.reason,retention_button_link:sale_subscription.close_reason_1
msgid "mailto:<EMAIL>?subject=Close contract: too expensive"
msgstr "mailto:<EMAIL>?subject=終止合約：太昂貴"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_close_reason_view_form
msgid "message"
msgstr "訊息"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "missing payments (from"
msgstr "缺失的付款（來自"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_sidebar
msgid "on"
msgstr "於"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_subscription_plan.py:0
msgid "per %d months"
msgstr "每 %d 個月"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_subscription_plan.py:0
msgid "per %d weeks"
msgstr "每 %d 週"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_subscription_plan.py:0
msgid "per %d years"
msgstr "每 %d 年"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_subscription_plan.py:0
msgid "per month"
msgstr "每月"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_subscription_plan.py:0
msgid "per week"
msgstr "每週"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_subscription_plan.py:0
msgid "per year"
msgstr "每年"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_template
msgid "quotation"
msgstr "報價單"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_template
msgid ""
"quotation existing for this subscription, please confirm it or reject it."
msgstr "此訂購計劃已有報價，請確認或拒絕。"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_template
msgid ""
"quotation existing for this subscription, please confirm them or reject "
"them."
msgstr "此訂購計劃已有報價，請確認或拒絕它們。"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_template
msgid "renewal"
msgstr "續訂"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "to this day) will be automatically processed."
msgstr "至今天）將自動處理。"

#. module: sale_subscription
#: model:sms.template,body:sale_subscription.sms_template_data_payment_failure
msgid ""
"{{ object.company_id.name }}: Our final attempt to process a payment for "
"your subscription failed. As your payment should have been made on {{ "
"object.next_invoice_date }}, your subscription has been terminated."
msgstr ""
"{{ object.company_id.name }}：我們最後一次嘗試就你的服務計劃收取付款，仍未能成功。由於有關款項本應在 {{ "
"object.next_invoice_date }} 到期及完成繳付，因此你的服務計劃已被終止。"

#. module: sale_subscription
#: model:mail.template,subject:sale_subscription.mail_template_subscription_alert
msgid ""
"{{ object.company_id.name }}: Please check the subscription {{ object.name "
"}}"
msgstr "{{ object.company_id.name }}：請檢查定期訂購計劃 {{ object.name }} 的狀況。"

#. module: sale_subscription
#: model:mail.template,subject:sale_subscription.mail_template_subscription_rating
msgid "{{ object.company_id.name }}: Service Rating Request"
msgstr "{{ object.company_id.name }}：服務評分請求"

#. module: sale_subscription
#: model:sms.template,body:sale_subscription.sms_template_data_payment_reminder
msgid ""
"{{ object.company_id.name }}: We were unable to process a payment for your "
"subscription. Your subscription {{ object.name }} is still valid but will be"
" suspended on {{ object.next_invoice_date }} unless the payment succeeds in "
"the meantime."
msgstr ""
"{{ object.company_id.name }}：我們未能就你的服務計劃收取付款。你的服務計劃 {{ object.name }} "
"目前仍然生效，但將會在 {{ object.next_invoice_date }} 暫停運作，除非你在該日之前成功付款。"

#. module: sale_subscription
#: model:sms.template,body:sale_subscription.sms_template_data_default_alert
msgid ""
"{{ object.company_id.name }}: Your subscription {{ object.name }} needs your"
" attention. If you have some concerns about it, please contact {{ "
"object.user_id.name }}, your contact person."
msgstr ""
"{{ object.company_id.name }}：你的服務計劃「{{ object.name "
"}}」需要你注意。如果你對服務計劃有任何疑問，請聯絡你的聯絡人 {{ object.user_id.name }}。"
