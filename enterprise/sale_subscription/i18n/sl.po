# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_subscription
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:51+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Slovenian (https://app.transifex.com/odoo/teams/41243/sl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sl\n"
"Plural-Forms: nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n%100==4 ? 2 : 3);\n"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"\n"
"- You are trying to invoice recurring orders. Please verify the delivered quantity of product based on invoicing policy, next invoice date and end date of the contract."
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__nbr
msgid "# of Lines"
msgstr "št. postavk"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__percentage_satisfaction
msgid "% Happy"
msgstr "% Zadovoljstva"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/product.py:0
msgid "%(price)s %(billing_period_display_sentence)s"
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order_line.py:0
msgid "%(start)s to %(next)s"
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order_line.py:0
#: code:addons/sale_subscription/models/sale_subscription_plan.py:0
msgid "%s days"
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order_line.py:0
msgid ""
"(*) These recurring products are discounted according to the prorated period"
" from %(start)s to %(end)s"
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order_line.py:0
msgid ""
"(*) These recurring products are entirely discounted as the next period has "
"not been invoiced yet."
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "(Change Plan)"
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "- You delivered %(delivered)s %(name)s and invoiced %(invoiced)s"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__mrr_change_period__1month
msgid "1 Month"
msgstr "1 Mesec"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__mrr_change_period__3months
msgid "3 Months"
msgstr "3 mesece"

#. module: sale_subscription
#: model:sale.subscription.plan,name:sale_subscription.subscription_plan_6_month
msgid "6 Months"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_sidebar
msgid "<b>Payment Method</b>"
msgstr ""

#. module: sale_subscription
#: model:mail.template,body_html:sale_subscription.mail_template_subscription_alert
msgid ""
"<div style=\"background:#F0F0F0;color:#515166;padding:10px 0px;font-family:Arial,Helvetica,sans-serif;font-size:14px;\">\n"
"                <table style=\"width:600px;margin:5px auto;\">\n"
"                    <tbody>\n"
"                        <tr><td t-if=\"not object.company_id.uses_default_logo\">\n"
"                            <a href=\"/\"><img t-attf-src=\"/logo.png?company={{ object.company_id.id }}\" style=\"vertical-align:baseline;max-width:100px;\"/></a>\n"
"                        </td><td style=\"text-align:right;vertical-align:middle;\">\n"
"                                Subscription Renewal\n"
"                        </td></tr>\n"
"                    </tbody>\n"
"                </table>\n"
"                <table style=\"width:600px;margin:0px auto;background:white;border:1px solid #e1e1e1;\">\n"
"                    <tbody>\n"
"                        <tr><td style=\"padding:15px 20px 10px 20px;\">\n"
"                            <p>Dear <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,</p>\n"
"                            <p>Your subscription <strong t-out=\"object.name or ''\">Office Cleaning Service\"</strong> needs your attention.</p>\n"
"                            <p>We invite you to renew it by clicking on the following link.</p>\n"
"                            <p>Kind regards.</p>\n"
"                        </td></tr>\n"
"                        <tr data-o-mail-quote-container=\"1\"><td style=\"padding:15px 20px 10px 20px;\" t-out=\"object.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</td></tr>\n"
"                    </tbody>\n"
"                </table>\n"
"            </div>\n"
"        "
msgstr ""

#. module: sale_subscription
#: model:mail.template,body_html:sale_subscription.mail_template_subscription_rating
msgid ""
"<div style=\"background:#F0F0F0;color:#515166;padding:10px 0px;font-family:Arial,Helvetica,sans-serif;font-size:14px;\">\n"
"            <t t-set=\"access_token\" t-value=\"object._rating_get_access_token()\"/>\n"
"                <table style=\"width:600px;margin:5px auto;\">\n"
"                    <tbody>\n"
"                        <tr><td t-if=\"not object.company_id.uses_default_logo\">\n"
"                            <a href=\"/\"><img t-attf-src=\"/logo.png?company={{ object.company_id.id }}\" style=\"vertical-align:baseline;max-width:100px;\"/></a>\n"
"                        </td><td style=\"text-align:right;vertical-align:middle;\">\n"
"                                Satisfaction Survey\n"
"                        </td></tr>\n"
"                    </tbody>\n"
"                </table>\n"
"                <table style=\"width:600px;margin:0px auto;background:white;border:1px solid #e1e1e1;\">\n"
"                    <tbody>\n"
"                        <tr><td style=\"padding:15px 20px 10px 20px;\">\n"
"                            <p>Hello,</p>\n"
"                            <p>Please take a moment to rate our services related to your subscription \"<strong t-out=\"object.name or ''\">Office Cleaning Service\"</strong>\"\n"
"                               assigned to <strong t-out=\"object._rating_get_operator().name or ''\">Mitchell Admin</strong>.</p>\n"
"                            <p>We appreciate your feedback. It helps us to improve continuously.</p>\n"
"                        </td></tr>\n"
"                        <tr><td style=\"padding:10px 20px\">\n"
"                            <table summary=\"o_mail_notification\" style=\"width:100%;border-top:1px solid #e1e1e1;\">\n"
"                                <tr>\n"
"                                    <td style=\"text-align:center;\">\n"
"                                        <h2 style=\"font-weight:300;font-size:18px;\">\n"
"                                            Tell us how you feel about our services:\n"
"                                        </h2>\n"
"                                        <div style=\"text-color: #888888\">(click on one of these smileys)</div>\n"
"                                    </td>\n"
"                                </tr>\n"
"                                <tr>\n"
"                                    <td style=\"padding:10px 10px;\">\n"
"                                        <table style=\"width:100%;text-align:center;\">\n"
"                                            <tr>\n"
"                                                <td>\n"
"                                                    <a t-attf-href=\"/rate/{{ access_token }}/5\">\n"
"                                                        <img alt=\"Satisfied\" src=\"/rating/static/src/img/rating_5.png\" title=\"Satisfied\"/>\n"
"                                                    </a>\n"
"                                                </td>\n"
"                                                <td>\n"
"                                                    <a t-attf-href=\"/rate/{{ access_token }}/3\">\n"
"                                                        <img alt=\"Okay\" src=\"/rating/static/src/img/rating_3.png\" title=\"Okay\"/>\n"
"                                                    </a>\n"
"                                                </td>\n"
"                                                <td>\n"
"                                                    <a t-attf-href=\"/rate/{{ access_token }}/1\">\n"
"                                                        <img alt=\"Dissatisfied\" src=\"/rating/static/src/img/rating_1.png\" title=\"Dissatisfied\"/>\n"
"                                                    </a>\n"
"                                                </td>\n"
"                                            </tr>\n"
"                                        </table>\n"
"                                    </td>\n"
"                                </tr>\n"
"                            </table>\n"
"                        </td></tr>\n"
"                        <tr data-o-mail-quote-container=\"1\"><td style=\"padding:15px 20px 10px 20px;\" t-out=\"object.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</td></tr>\n"
"                    </tbody>\n"
"                </table>\n"
"                <table style=\"width:600px;margin:auto;text-align:center;font-size:12px;\">\n"
"                    <tbody>\n"
"                        <tr><td style=\"padding-top:10px;color:#afafaf;\">\n"
"                            <p>Email automatically sent by <a target=\"_blank\" href=\"https://www.odoo.com/app/subscriptions\" style=\"color:#875A7B;text-decoration:none;\">Odoo Subscription</a> for <a t-att-href=\"object.company_id.website\" style=\"color:#875A7B;text-decoration:none;\" t-out=\"object.company_id.name or ''\">YourCompany</a></p>\n"
"                        </td></tr>\n"
"                    </tbody>\n"
"                </table>\n"
"            </div>\n"
"        "
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid ""
"<em class=\"text-muted\" colspan=\"2\" invisible=\"not id\">\n"
"                                Action data can not be updated to avoid unexpected behaviors. Create a new action instead.\n"
"                            </em>"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_base_automation_form
msgid ""
"<em class=\"text-muted\">\n"
"                        Action data can not be updated to avoid unexpected behaviors. Create a new automation rule instead.\n"
"                    </em>"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_sidebar
msgid "<i class=\"fa fa-download me-1\"/> Download"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "<i class=\"fa fa-fw fa-check\"/> In Progress"
msgstr "<i class=\"fa fa-fw fa-check\"/> V teku"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "<i class=\"fa fa-fw fa-check\"/> Paused"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "<i class=\"fa fa-fw fa-check\"/> Renewed"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "<i class=\"fa fa-fw fa-remove\"/> Closed"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "<i class=\"fa fa-fw fa-remove\"/> Closing"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "<i class=\"fa fa-fw fa-repeat\"/> To Renew"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_so_portal_template
msgid "<i class=\"oi oi-arrow-right me-1\"/>Back to your subscription"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_template
msgid "<option value=\"\">Choose a reason...</option>"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.payment_method_form
#: model_terms:ir.ui.view,arch_db:sale_subscription.payment_token_form
msgid ""
"<small class=\"text-600\">Automate payments for the linked "
"subscriptions</small>"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.portal_my_subscriptions
msgid ""
"<span class=\"badge rounded-pill text-bg-danger\"><i class=\"fa fa-fw fa-"
"repeat\"/> To Renew</span>"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.portal_my_subscriptions
msgid ""
"<span class=\"badge rounded-pill text-bg-dark\"><i class=\"fa fa-fw fa-"
"remove\"/> Closed</span>"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.portal_my_subscriptions
msgid "<span class=\"badge rounded-pill text-bg-info\"> Renewal Quotation </span>"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.portal_my_subscriptions
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_portal_content_inherit
msgid ""
"<span class=\"badge rounded-pill text-bg-primary\"><i class=\"fa fa-fw fa-"
"check\"/> Upsell</span>"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.portal_my_subscriptions
msgid ""
"<span class=\"badge rounded-pill text-bg-secondary\"><i class=\"fa fa-fw fa-"
"check\"/> Renewed</span>"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.portal_my_subscriptions
msgid ""
"<span class=\"badge rounded-pill text-bg-success\"><i class=\"fa fa-fw fa-"
"check\"/> In Progress</span>"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.portal_my_subscriptions
msgid ""
"<span class=\"badge rounded-pill text-bg-success\"><i class=\"fa fa-fw fa-"
"check\"/> Paused</span>"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
msgid "<span class=\"badge rounded-pill text-bg-warning\">Closing</span>"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
msgid "<span class=\"o_stat_text\">MRR</span>"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_plan_view_form
msgid "<span class=\"o_stat_text\">Subscription Items</span>"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_plan_view_form
msgid "<span class=\"o_stat_text\">Subscriptions</span>"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_sidebar
msgid "<span class=\"text-muted\">Next Billing</span>"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.recurring_details
msgid "<span class=\"text-start\">Non Recurring</span>:"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.recurring_details
msgid "<span class=\"text-start\">Recurring</span>:"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
msgid ""
"<span invisible=\"not plan_id or subscription_state == "
"'7_upsell'\">until</span>"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid ""
"<span>\n"
"                                There is already a pending payment for this subscription.\n"
"                            </span>"
msgstr ""

#. module: sale_subscription
#: model_terms:web_tour.tour,rainbow_man_message:sale_subscription.sale_subscription_tour
msgid ""
"<span><b>Congratulations</b>, your first subscription quotation is ready to be sent!\n"
"        </span>"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "<strong invisible=\"not rating_operator\">%</strong>"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "<strong> and </strong>"
msgstr "<strong> in</strong>"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.report_saleorder_document
msgid "<strong>Recurring Plan</strong>"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.report_saleorder_document
msgid "<strong>Start Date</strong>"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_close_reason_view_form
msgid ""
"<strong>Warning:</strong> the survey can only be shown if all information is"
" set. Please complete:"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "<strong>subscriptions</strong>"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "<strong>to</strong>"
msgstr "<strong>do</strong>"

#. module: sale_subscription
#: model:mail.template,body_html:sale_subscription.email_payment_close
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <t t-set=\"company\" t-value=\"object.company_id or object.user_id.company_id or user.company_id\"/>\n"
"                    <span style=\"font-size: 10px;\">Your Subscription</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">Office Cleaning Service</span>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"right\" t-if=\"not company.uses_default_logo\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ company.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"company.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                    <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    Hello <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,<br/><br/>\n"
"                    <t t-if=\"ctx.get('payment_token') and ctx.get('total_amount')\">\n"
"                        Our final attempt to process a payment for your subscription using your payment method\n"
"                        <t t-out=\"ctx.get('payment_token') or ''\">TOKEN</t>\n"
"                        for <t t-out=\"ctx['total_amount'] or ''\">100</t> <t t-out=\"ctx.get('currency') or ''\">$</t> failed.\n"
"                        <t t-if=\"ctx.get('error')\">\n"
"                            Your bank or credit institution gave the following details about the issue: <pre t-out=\"ctx['error'] or ''\"/>.\n"
"                        </t>\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        Our final attempt to process a payment for your subscription failed because we have no payment method recorded for you.\n"
"                    </t>\n"
"                    <br/><br/>\n"
"                    As your payment should have been made <strong><t t-out=\"ctx.get('auto_close_limit') or ''\">5</t> days ago</strong>, your subscription has been terminated.\n"
"                    Should you wish to resolve this issue, do not hesitate to contact us.<br/><br/>\n"
"                    Thank you for choosing <t t-out=\"company.name or ''\">YourCompany</t>!\n"
"                    <div t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"                        <br/><br/>\n"
"                        <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"company.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-if=\"company.phone\">\n"
"                        <t t-out=\"company.phone or ''\">******-123-4567</t> |\n"
"                    </t>\n"
"                    <t t-if=\"company.email\">\n"
"                        <a t-attf-href=\"'mailto:%s' % {{ company.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"company.email or ''\"><EMAIL></a> |\n"
"                    </t>\n"
"                    <t t-if=\"company.website\">\n"
"                        <a t-attf-href=\"'%s' % {{ company.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"company.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=mail\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"        "
msgstr ""

#. module: sale_subscription
#: model:mail.template,body_html:sale_subscription.email_payment_reminder
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <t t-set=\"company\" t-value=\"object.company_id or object.user_id.company_id or user.company_id\"/>\n"
"                    <span style=\"font-size: 10px;\">Your Subscription</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">Office Cleaning Service</span>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"right\" t-if=\"not company.uses_default_logo\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ company.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"company.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                    <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    Hello <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,<br/><br/>\n"
"                    <t t-if=\"ctx.get('payment_token') and ctx.get('total_amount')\">\n"
"                        We were unable to process a payment for your subscription using your payment method\n"
"                        <t t-out=\"ctx['payment_token'] or ''\">TOKEN</t>\n"
"                        for <t t-out=\"ctx['total_amount'] or ''\">10</t> <t t-out=\"ctx.get('currency_name') or ''\">$</t>.\n"
"                        <t t-if=\"ctx.get('error')\">\n"
"                            Your bank or credit institution gave the following details about the issue: <pre t-out=\"ctx['error'] or ''\"/>.\n"
"                        </t>\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        Your subscription <t t-out=\"ctx.get('code') or ''\">CODE</t> is due for renewal but we haven’t received your payment yet. To keep your subscription active, please make your payment as soon as possible.\n"
"                    </t>\n"
"                    <br/><br/>\n"
"                    Your subscription <t t-out=\"ctx.get('code') or ''\">CODE</t> is still valid but will be <strong>suspended</strong>\n"
"                    on <t t-out=\"format_date(ctx.get('date_close')) or ''\">05/05/2021</t><br/>\n"
"                    <div style=\"margin: 16px 0px; text-align: center;\">\n"
"                        <a t-attf-href=\"{{ ctx.get('payment_link', object.get_portal_url()) }}\" style=\"display: inline-block; padding: 10px 30px; text-decoration: none; background-color: #875A7B; color: #fff; border-radius: 5px;\">\n"
"                            Pay Now\n"
"                        </a>\n"
"                    </div>\n"
"                    <br/>\n"
"                    If you have any questions, do not hesitate to contact us.<br/><br/>\n"
"                    Thank you for choosing <t t-out=\"company.name or ''\">YourCompany</t>!\n"
"                    <div>\n"
"                    <t t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"                        <br/><br/>\n"
"                        <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"                    </t>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"company.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-if=\"company.phone\">\n"
"                        <t t-out=\"company.phone or ''\">******-123-4567</t> |\n"
"                    </t>\n"
"                    <t t-if=\"company.email\">\n"
"                        <a t-attf-href=\"'mailto:%s' % {{ company.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"company.email or ''\"><EMAIL></a> |\n"
"                    </t>\n"
"                    <t t-if=\"company.website\">\n"
"                        <a t-attf-href=\"'%s' % {{ company.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"company.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=mail\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"        "
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"A cancelled SO cannot be in progress. You should close %s before cancelling "
"it."
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/controllers/portal.py:0
msgid "A renewal has been created by the client."
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "A renewal quotation %s has been created"
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/product.py:0
msgid "A subscription combo product can only contain subscription products."
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.view_sale_order_log_analysis_graph
#: model_terms:ir.ui.view,arch_db:sale_subscription.view_sale_order_log_growth_graph
#: model_terms:ir.ui.view,arch_db:sale_subscription.view_sale_order_log_growth_pivot
msgid "ARR Change"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__arr_change_normalized
msgid "ARR Change (normalized)"
msgstr ""

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_account_move_send
msgid "Account Move Send"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "Action"
msgstr "Dejanje"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "Action Name"
msgstr "Naziv dejanja"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__message_needaction
msgid "Action Needed"
msgstr "Potreben je ukrep"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__action
msgid "Action To Do"
msgstr "Načrtovana dejanja"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__action_server_ids
msgid "Actions"
msgstr "Dejanja"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__active
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__active
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_pricing__active
msgid "Active"
msgstr "Aktivno"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__contract_number
msgid "Active Subscriptions Change"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "Activity"
msgstr "Aktivnost"

#. module: sale_subscription
#: model:ir.actions.act_window,name:sale_subscription.mail_activity_plan_action_subscription
#: model:ir.ui.menu,name:sale_subscription.mail_activity_plan_menu_config_subscription
msgid "Activity Plans"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__activity_type_id
msgid "Activity Type"
msgstr "Tip aktivnosti"

#. module: sale_subscription
#: model:ir.actions.act_window,name:sale_subscription.mail_activity_type_action_config_subscription
#: model:ir.ui.menu,name:sale_subscription.subscription_menu_config_activity_type
msgid "Activity Types"
msgstr "Tipi aktivnosti"

#. module: sale_subscription
#: model_terms:ir.actions.act_window,help:sale_subscription.mail_activity_plan_action_subscription
msgid ""
"Activity plans are used to assign a list of activities in just a few clicks\n"
"                (e.g. \"Renewal Offer\", \"Yearly Satisfaction Review\", ...)"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__user_quantity
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__user_quantity
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_plan_search
msgid "Add Products"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_sidebar
msgid "Add Quantity"
msgstr ""

#. module: sale_subscription
#. odoo-javascript
#: code:addons/sale_subscription/static/src/js/tours/sale_subscription.js:0
msgid "Add a new rule"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.product_pricelist_view
#: model_terms:ir.ui.view,arch_db:sale_subscription.product_template_view_form_recurring
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_plan_view_form
msgid "Add a price rule"
msgstr ""

#. module: sale_subscription
#. odoo-javascript
#: code:addons/sale_subscription/static/src/js/tours/sale_subscription.js:0
msgid ""
"Add a recurring plan for this product, or create a new one with the desired "
"recurrence (e.g., Monthly)"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_subscription_plan__billing_first_day
msgid ""
"Align all subscription invoices on the first day of each billing period."
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__billing_first_day
msgid "Align to Period Start"
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/controllers/portal.py:0
msgid "All"
msgstr "Vse"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__user_quantity
#: model:ir.model.fields,help:sale_subscription.field_sale_subscription_plan__user_quantity
msgid ""
"Allow customers to create an Upsell quote to adjust the quantity of products"
" in their subscription.Only products that are listed as \"optional "
"products\" can be modified."
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_subscription_plan__related_plan_id
msgid ""
"Allow your customers to switch from this plan to another on quotation (new "
"subscription or renewal)"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_close_reason_view_form
msgid ""
"Allowing to be selected by customers in the portal when they are closing "
"their subscriptions."
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/controllers/portal.py:0
msgid "An upsell has been created by the client."
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "An upsell quotation %s has been created"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__recurring_yearly
msgid "Annual Recurring Revenue"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "Anticipate payment"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__filter_domain
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "Apply on"
msgstr "Uporabi na"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_template_view_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_plan_view_form
msgid "Archived"
msgstr "Arhivirano"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__activity_user
msgid "Assign To"
msgstr "Dodeljeno k"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__payment_transaction__subscription_action__assign_token
msgid "Assign Token"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_subscription_plan__user_closable_options__at_date
msgid "At date"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__message_attachment_count
msgid "Attachment Count"
msgstr "Število prilog"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__payment_transaction__renewal_state__authorized
msgid "Authorized"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_sidebar
msgid "Automate Payment"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.actions.act_window,help:sale_subscription.sale_subscription_action
#: model_terms:ir.actions.act_window,help:sale_subscription.sale_subscription_action_filtered
msgid ""
"Automate invoices and payments, simplify upsell quotes, reduce churn and get"
" insights (MRR, churn, CLTV, ...)"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__auto_close_limit
msgid "Automatic Closing"
msgstr "Samodejno zapiranje"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__auto_close_limit_display
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_plan_search
msgid "Automatic Closing After"
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"Automatic payment failed after multiple attempts. Contract closed "
"automatically."
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"Automatic payment failed. Check the corresponding invoice %s. We can't "
"automatically process negative payment"
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "Automatic payment failed. Email sent to customer. Error: %s"
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"Automatic payment failed. No country specified on payment_token's partner"
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "Automatic payment failed. No email sent this time. Error: %s"
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"Automatic payment succeeded. Payment reference: %(ref)s. Amount: %(amount)s."
" Contract set to: In Progress, Next Invoice: %(inv)s. Email sent to "
"customer."
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__payment_exception
msgid ""
"Automatic payment with token failed. The payment provider configuration and "
"token should be checked"
msgstr ""

#. module: sale_subscription
#: model:sale.order.close.reason,name:sale_subscription.close_reason_auto_close_limit_reached
msgid "Automatic renewal failed"
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"Automatic renewal succeeded. Free subscription. Next Invoice: %(inv)s. No "
"email sent."
msgstr ""

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_base_automation
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__automation_id
msgid "Automation Rule"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__name
msgid "Automation Rule Name"
msgstr ""

#. module: sale_subscription
#: model:ir.actions.act_window,name:sale_subscription.sale_subscription_alert_action
#: model:ir.ui.menu,name:sale_subscription.menu_sale_subscription_alert
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_tree
msgid "Automation Rules"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__rating_avg
msgid "Average Rating"
msgstr "Povprečna ocena"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order__health__bad
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__health__bad
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__health__bad
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_subscription_report__health__bad
msgid "Bad"
msgstr "Slaba"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Bad Health"
msgstr "Slabo zdravstveno stanje"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__filter_pre_domain
msgid "Before Update Domain"
msgstr "Pred posodobitvijo domene"

#. module: sale_subscription
#: model_terms:sale.order.close.reason,retention_message:sale_subscription.close_reason_1
msgid ""
"Before closing your subscription, we'd like to offer you to schedule a call "
"with Marc Demo, your account manager."
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__billing_period_display
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_pricing__name
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_plan_view_form
msgid "Billing Period"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__billing_period_value
msgid "Billing Period "
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__billing_period_display_sentence
msgid "Billing Period Display"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_close_reason__retention_button_link
msgid "Button Link"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_close_reason__retention_button_text
msgid "Button Text"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__percentage_satisfaction
msgid ""
"Calculate the ratio between the number of the best ('great') ratings and the"
" total number of ratings"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__campaign_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__campaign_id
msgid "Campaign"
msgstr "Kampanja"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_change_customer_view_form
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_close_reason_wizard_view_form
msgid "Cancel"
msgstr "Prekliči"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__order_state__cancel
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__state__cancel
#: model:sale.order.close.reason,name:sale_subscription.close_reason_cancel
msgid "Cancelled"
msgstr "Preklicano"

#. module: sale_subscription
#: model:product.template,name:sale_subscription.product_car_leasing_product_template
msgid "Car Leasing (SUB)"
msgstr ""

#. module: sale_subscription
#: model:ir.actions.act_window,name:sale_subscription.sale_subscription_change_customer_wizard_action
msgid "Change Customer"
msgstr "Sprememba kupca"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_sidebar
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_so_portal_template
msgid "Change Plan"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_so_portal_template
msgid "Change Plan for"
msgstr ""

#. module: sale_subscription
#: model:ir.actions.server,name:sale_subscription.model_sale_order_subscription_change_customer
msgid "Change customer"
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "Check reopened subscription"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_template
msgid "Choose a closing reason before submitting"
msgstr ""

#. module: sale_subscription
#. odoo-javascript
#: code:addons/sale_subscription/static/src/js/tours/sale_subscription.js:0
msgid "Choose a product name.<br/><i>(e.g. eLearning Access)</i>"
msgstr ""

#. module: sale_subscription
#. odoo-javascript
#: code:addons/sale_subscription/static/src/js/tours/sale_subscription.js:0
msgid "Choose the invoice duration for your subscription"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log__event_type__2_churn
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__event_type__2_churn
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
msgid "Churn"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order__subscription_state__6_churn
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__subscription_state__6_churn
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__subscription_state_from__6_churn
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log__subscription_state__6_churn
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__subscription_state__6_churn
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_subscription_report__subscription_state__6_churn
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Churned"
msgstr ""

#. module: sale_subscription
#. odoo-javascript
#: code:addons/sale_subscription/static/src/js/tours/sale_subscription.js:0
msgid "Click here to add some products or services"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__user_closable
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__user_closable
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_plan_search
msgid "Closable"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_so_portal_template
msgid "Close"
msgstr "Zaključi"

#. module: sale_subscription
#: model:ir.actions.act_window,name:sale_subscription.sale_subscription_close_reason_wizard_action
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__close_reason_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__close_reason_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_close_reason_wizard__close_reason_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__close_reason_id
msgid "Close Reason"
msgstr "Razlog zaprtja"

#. module: sale_subscription
#: model:ir.actions.act_window,name:sale_subscription.sale_subscription_close_reason_action
#: model:ir.ui.menu,name:sale_subscription.menu_sale_subscription_close_reason_action
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_close_reason_view_tree
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_close_reason_wizard_view_form
msgid "Close Reasons"
msgstr "Razlogi zaprtja"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_sidebar
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_template
msgid "Close Subscription"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__user_closable_options
msgid "Closeable Plan Options"
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/controllers/portal.py:0
msgid "Closed"
msgstr "Neaktivni"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
msgid "Closed subscriptions"
msgstr "Zaprte naročnine"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/controllers/portal.py:0
msgid "Closing text: %s"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__commercial_partner_id
msgid "Commercial Entity"
msgstr "Poslovni subjekt"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__company_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log__company_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__company_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__company_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_pricing__company_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__company_id
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
msgid "Company"
msgstr "Podjetje"

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_res_config_settings
msgid "Config Settings"
msgstr "Uredi nastavitve"

#. module: sale_subscription
#: model:ir.ui.menu,name:sale_subscription.menu_sale_subscription_config
msgid "Configuration"
msgstr "Nastavitve"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_res_config_settings__invoice_consolidation
#: model_terms:ir.ui.view,arch_db:sale_subscription.res_config_settings_view_form
msgid ""
"Consolidate all of a customer's subscriptions that are due to be billed on "
"the same day onto a single invoice."
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_res_config_settings__invoice_consolidation
msgid "Consolidate subscriptions billing"
msgstr ""

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_res_partner
msgid "Contact"
msgstr "Stik"

#. module: sale_subscription
#: model:sale.order.close.reason,retention_button_text:sale_subscription.close_reason_1
msgid "Contact Marc"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_template__duration_unit
msgid "Contract duration"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__payment_exception
msgid "Contract in exception"
msgstr "Pogodba v izjemi"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log__event_type__15_contraction
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__event_type__15_contraction
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
msgid "Contraction"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Contracts whose payment has failed"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__country_id
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
msgid "Country"
msgstr "Država"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
msgid "Create Alternative"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
msgid "Create Invoice"
msgstr "Ustvari račun"

#. module: sale_subscription
#: model_terms:ir.actions.act_window,help:sale_subscription.mail_activity_plan_action_subscription
msgid "Create a Subscription Activity Plan"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.actions.act_window,help:sale_subscription.sale_subscription_plan_action
msgid "Create a new plan"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.actions.act_window,help:sale_subscription.product_action_subscription
msgid "Create a new product"
msgstr "Ustvari nov proizvod"

#. module: sale_subscription
#: model_terms:ir.actions.act_window,help:sale_subscription.sale_subscription_action
#: model_terms:ir.actions.act_window,help:sale_subscription.sale_subscription_action_filtered
msgid "Create a new subscription"
msgstr "Ustvari novo naročnino"

#. module: sale_subscription
#: model_terms:ir.actions.act_window,help:sale_subscription.sale_subscription_alert_action
msgid "Create a new subscription alert"
msgstr "Ustvari novo opozorilo o naročnini"

#. module: sale_subscription
#: model_terms:ir.actions.act_window,help:sale_subscription.sale_subscription_template_action
msgid "Create a new template of subscription"
msgstr "Ustvari novo predlogo za naročnino"

#. module: sale_subscription
#: model_terms:ir.actions.act_window,help:sale_subscription.sale_subscription_action_quotes
msgid "Create a subscription quotation"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__action__next_activity
msgid "Create next activity"
msgstr "Ustvari naslednjo aktivnost"

#. module: sale_subscription
#: model_terms:ir.actions.act_window,help:sale_subscription.sale_subscription_action_quotes
msgid ""
"Create subscriptions to manage recurring invoicing and payments from your "
"customers."
msgstr ""

#. module: sale_subscription
#. odoo-javascript
#: code:addons/sale_subscription/static/src/js/tours/sale_subscription.js:0
msgid "Create your first subscription product here"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__create_uid
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_close_reason__create_uid
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log__create_uid
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_change_customer_wizard__create_uid
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_close_reason_wizard__create_uid
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__create_uid
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_pricing__create_uid
msgid "Created by"
msgstr "Ustvaril"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__create_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_close_reason__create_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log__create_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_change_customer_wizard__create_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_close_reason_wizard__create_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__create_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_pricing__create_date
msgid "Created on"
msgstr "Ustvarjeno"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__cron_nextcall
msgid "Cron Nextcall"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__currency_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__currency_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log__currency_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__currency_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_pricing__currency_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__currency_id
#: model_terms:ir.ui.view,arch_db:sale_subscription.view_sale_order_log_growth_tree
msgid "Currency"
msgstr "Valuta"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_product_product__product_subscription_pricing_ids
#: model:ir.model.fields,field_description:sale_subscription.field_product_template__product_subscription_pricing_ids
msgid "Custom Subscription Pricings"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__partner_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__partner_id
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_sales_order_line_filter
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Customer"
msgstr "Stranka"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__commercial_partner_id
msgid "Customer Company"
msgstr "Podjetje stranke"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__country_id
msgid "Customer Country"
msgstr "Država kupca"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__commercial_partner_id
msgid "Customer Entity"
msgstr "Entiteta stranke"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__industry_id
msgid "Customer Industry"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__client_order_ref
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__client_order_ref
msgid "Customer Reference"
msgstr "Sklic stranke"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__state_id
msgid "Customer State"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__partner_zip
msgid "Customer ZIP"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__user_closable
#: model:ir.model.fields,help:sale_subscription.field_sale_subscription_plan__user_closable
msgid "Customer can close their subscriptions."
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__user_extend
#: model:ir.model.fields,help:sale_subscription.field_sale_subscription_plan__user_extend
msgid "Customer can create a renewal quotation for their subscription."
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__customer_ids
#: model:ir.ui.menu,name:sale_subscription.menu_orders_customers
msgid "Customers"
msgstr "Kupci"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "Date:"
msgstr "Datum:"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_plan_view_form
msgid "Days"
msgstr "Dni"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "Delay After Trigger"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_alert__trg_date_range
msgid ""
"Delay after the trigger date. You can put a negative number if you need a "
"delay before the trigger date, like sending a reminder 15 minutes before a "
"meeting."
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__trg_date_range
msgid "Delay after trigger date"
msgstr "Zamik po datumu izvedbe"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__trg_date_range_type
msgid "Delay type"
msgstr "Tip zamika"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__description
msgid "Description"
msgstr "Opis"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "Destination"
msgstr "Destinacija"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_plan_view_form
msgid "Details"
msgstr "Podrobnosti"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_template
msgid "Discard, I want to stay"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__discount
msgid "Discount %"
msgstr "Popust %"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__discount_amount
msgid "Discount Amount"
msgstr "Znesek popusta"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__display_late
msgid "Display Late"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__display_name
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_close_reason__display_name
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log__display_name
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__display_name
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_change_customer_wizard__display_name
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_close_reason_wizard__display_name
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__display_name
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_pricing__display_name
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__display_name
msgid "Display Name"
msgstr "Prikazani naziv"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_product_product__display_subscription_pricing
#: model:ir.model.fields,field_description:sale_subscription.field_product_template__display_subscription_pricing
msgid "Display Price"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_sidebar
msgid "Download"
msgstr "Prenesi"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__payment_transaction__renewal_state__draft
msgid "Draft"
msgstr "Osnutek"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__activity_date_deadline_range
msgid "Due Date In"
msgstr "Datum zapadlosti v"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__activity_date_deadline_range_type
msgid "Due type"
msgstr "Vrsta zapadlosti"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_template__duration_unit
msgid "Duration Unit"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__template_id
msgid "Email Template"
msgstr "Predloga e-pošte"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_subscription_plan__invoice_mail_template_id
msgid ""
"Email template used to send invoicing email automatically.\n"
"Leave it empty if you don't want to send email automatically."
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_close_reason__empty_retention_message
msgid "Empty Retention Message"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_template__duration_value
msgid "End After"
msgstr "Konec po"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__end_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_line__subscription_end_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__end_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__end_date
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_sales_order_line_filter
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "End Date"
msgstr "Končni datum"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "End Date:"
msgstr ""

#. module: sale_subscription
#: model:sale.order.close.reason,name:sale_subscription.close_reason_end_of_contract
msgid "End of contract"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_subscription_plan__user_closable_options__end_of_period
msgid "End of period"
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"Error during renewal of contract %(order_ids)s %(order_refs)s "
"%(payment_state)s"
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "Error during renewal of contract %s (Payment not recorded)"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log__event_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__event_date
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
msgid "Event Date"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
msgid "Event Type"
msgstr "Vrsta dogodka"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log__event_type__1_expansion
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__event_type__1_expansion
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
msgid "Expansion"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "Expiration Date:"
msgstr "Datum izteka:"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Failed Payments"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_alert__on_change_field_ids
msgid "Fields that trigger the onchange."
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
msgid "First Contract"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__first_contract_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__first_contract_date
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "First Contract Date"
msgstr "Datum prve pogodbe"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__origin_order_id
msgid "First Order"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__origin_order_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__origin_order_id
msgid "First contract"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__first_contract_date
msgid "First contract date"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__message_follower_ids
msgid "Followers"
msgstr "Sledilci"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__message_partner_ids
msgid "Followers (Partners)"
msgstr "Sledilci (partnerji)"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_kanban
msgid "Future"
msgstr "V prihodnje"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Future Activities"
msgstr "Bodoče aktivnosti"

#. module: sale_subscription
#. odoo-javascript
#: code:addons/sale_subscription/static/src/js/tours/sale_subscription.js:0
msgid "Go ahead and create a new product"
msgstr "Pojdi naprej in ustvari nov izdelek"

#. module: sale_subscription
#. odoo-javascript
#: code:addons/sale_subscription/static/src/js/tours/sale_subscription.js:0
msgid "Go ahead and create a new subscription"
msgstr ""

#. module: sale_subscription
#. odoo-javascript
#: code:addons/sale_subscription/static/src/js/tours/sale_subscription.js:0
msgid "Go back to the subscription view"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order__health__done
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__health__done
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__health__done
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_subscription_report__health__done
msgid "Good"
msgstr "Dobro"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Good Health"
msgstr "Dobro zdravstveno stanje"

#. module: sale_subscription
#: model_terms:ir.actions.act_window,help:sale_subscription.sale_subscription_action_pending
msgid "Great, there are no subscriptions to renew!"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__weight
msgid "Gross Weight"
msgstr "Bruto masa"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_sales_order_line_filter
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Group By"
msgstr "Združi po"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_kanban
msgid "Happy face"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__model_is_mail_thread
msgid "Has Mail Thread"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__has_message
msgid "Has Message"
msgstr "Ima sporočilo"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__has_recurring_line
msgid "Has Recurring Line"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__health
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__health
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__health
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__health
msgid "Health"
msgstr ""

#. module: sale_subscription
#: model:ir.module.category,description:sale_subscription.module_category_subscription_management
msgid "Helps you handle subscriptions and recurring invoicing."
msgstr ""

#. module: sale_subscription
#: model_terms:ir.actions.act_window,help:sale_subscription.sale_subscription_action_pending
msgid "Here you will find subscriptions overdue for renewal."
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_sidebar
msgid "History"
msgstr "Zgodovina"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__history_count
msgid "History Count"
msgstr ""

#. module: sale_subscription
#: model:sale.order.close.reason,name:sale_subscription.close_reason_4
msgid "I don't use it"
msgstr "Ne uporabljam ga"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_close_reason__id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log__id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_change_customer_wizard__id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_close_reason_wizard__id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_pricing__id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__id
msgid "ID"
msgstr "ID"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Če je označeno, zahtevajo nova sporočila vašo pozornost."

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__message_has_error
#: model:ir.model.fields,help:sale_subscription.field_sale_order__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Če je označeno, nekatera sporočila vsebujejo napako pri dostavi."

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__payment_token_id
msgid "If not set, the automatic payment will fail."
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_alert__filter_domain
msgid ""
"If present, this condition must be satisfied before executing the automation"
" rule."
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_alert__filter_pre_domain
msgid ""
"If present, this condition must be satisfied before the update of the "
"record. Not checked on record creation."
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__end_date
#: model:ir.model.fields,help:sale_subscription.field_sale_order_line__subscription_end_date
msgid ""
"If set in advance, the subscription will be set to renew 1 month before the "
"date and will be closed on the date set in this field."
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_product_product__recurring_invoice
#: model:ir.model.fields,help:sale_subscription.field_product_template__recurring_invoice
#: model:ir.model.fields,help:sale_subscription.field_sale_order_line__recurring_invoice
#: model:ir.model.fields,help:sale_subscription.field_sale_order_template_line__recurring_invoice
#: model:ir.model.fields,help:sale_subscription.field_sale_order_template_option__recurring_invoice
msgid ""
"If set, confirming a sale order with this product will create a subscription"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_line__pricelist_id
msgid "If you change the pricelist, only newly added lines will be affected."
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "If you wish to reopen it, the"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid ""
"If you wish to reopen it, you can pay your invoice for the current invoicing"
" period."
msgstr ""
"Če ga želite ponovno odpreti, lahko plačate račun za tekoče obdobje "
"izdajanja računov."

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/controllers/portal.py:0
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order__subscription_state__3_progress
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__subscription_state__3_progress
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__subscription_state_from__3_progress
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log__subscription_state__3_progress
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__subscription_state__3_progress
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_subscription_report__subscription_state__3_progress
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "In Progress"
msgstr "V teku"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__industry_id
msgid "Industry"
msgstr "Panoga"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "Initial"
msgstr "Začetnica"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__internal_note
msgid "Internal Note"
msgstr "Interni Zaznamek"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__internal_note_display
msgid "Internal Note Display"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
msgid "Internal notes"
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/controllers/portal.py:0
msgid "Invalid access token."
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__invoice_mail_template_id
msgid "Invoice Email Template"
msgstr "E-poštna predloga računa"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__line_invoice_status
msgid "Invoice Status"
msgstr "Status računa"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/controllers/portal.py:0
msgid "Invoice not found."
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__is_batch
msgid "Is Batch"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__message_is_follower
msgid "Is Follower"
msgstr "Je sledilec"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_close_reason__is_protected
msgid "Is Protected"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__is_renewing
msgid "Is Renewing"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_base_automation__is_sale_order_alert
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__is_sale_order_alert
msgid "Is Sale Order Alert"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_template__is_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__is_subscription
msgid "Is Subscription"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__is_upselling
msgid "Is Upselling"
msgstr "Nadgradna prodaja"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__is_invoice_cron
msgid "Is a Subscription invoiced in cron"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.actions.act_window,help:sale_subscription.sale_subscription_action_pending
msgid ""
"It could be due to the fail of the automatic payment\n"
"            or because the new invoice has not yet been issued."
msgstr ""

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_account_move
msgid "Journal Entry"
msgstr "Temeljnica"

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_account_move_line
msgid "Journal Item"
msgstr "Postavka"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__kpi_1month_mrr_delta
msgid "KPI 1 Month MRR Delta"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__kpi_1month_mrr_percentage
msgid "KPI 1 Month MRR Percentage"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__kpi_3months_mrr_percentage
msgid "KPI 3 Months MRR Percentage"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__kpi_3months_mrr_delta
msgid "KPI 3 months MRR Delta"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_template__is_unlimited
msgid "Last Forever"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_line__last_invoiced_date
msgid "Last Invoiced Date"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__last_reminder_date
msgid "Last Reminder Date"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__last_run
msgid "Last Run"
msgstr "Zadnji zagon"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__write_uid
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_close_reason__write_uid
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log__write_uid
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_change_customer_wizard__write_uid
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_close_reason_wizard__write_uid
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__write_uid
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_pricing__write_uid
msgid "Last Updated by"
msgstr "Zadnji posodobil"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__write_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_close_reason__write_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log__write_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_change_customer_wizard__write_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_close_reason_wizard__write_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__write_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_pricing__write_date
msgid "Last Updated on"
msgstr "Zadnjič posodobljeno"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__last_invoice_date
msgid "Last invoice date"
msgstr "Datum zadnjega računa"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__last_reminder_date
msgid "Last time when we sent a payment reminder"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Late Activities"
msgstr "Aktivnosti z zamudo"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_kanban
msgid "Latest Rating: Dissatisfied"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_kanban
msgid "Latest Rating: Okay"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_kanban
msgid "Latest Rating: Satisfied"
msgstr "Zadnja ocena: Zadovoljen"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__least_delay_msg
msgid "Least Delay Msg"
msgstr "Sporočilo z najmanjšo zamudo"

#. module: sale_subscription
#. odoo-javascript
#: code:addons/sale_subscription/static/src/js/tours/sale_subscription.js:0
msgid "Let's add a pricing with a recurrence"
msgstr ""

#. module: sale_subscription
#. odoo-javascript
#: code:addons/sale_subscription/static/src/js/tours/sale_subscription.js:0
msgid "Let's add price for selected recurrence"
msgstr ""

#. module: sale_subscription
#. odoo-javascript
#: code:addons/sale_subscription/static/src/js/tours/sale_subscription.js:0
msgid "Let's choose the customer for your subscription"
msgstr ""

#. module: sale_subscription
#. odoo-javascript
#: code:addons/sale_subscription/static/src/js/tours/sale_subscription.js:0
msgid "Let's go to the catalog to create our first subscription product"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__log_webhook_calls
msgid "Log Calls"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__log_currency_id
msgid "Log Currency"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "Log a note..."
msgstr "Zabeleži opombo..."

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_tree
#: model_terms:ir.ui.view,arch_db:sale_subscription.view_sale_order_log_growth_tree
msgid "MRR"
msgstr ""

#. module: sale_subscription
#: model:ir.actions.act_window,name:sale_subscription.sale_order_log_analysis_action
msgid "MRR Analysis"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "MRR Between"
msgstr "MRR med"

#. module: sale_subscription
#: model:ir.actions.act_window,name:sale_subscription.sale_order_log_growth_action
#: model:ir.ui.menu,name:sale_subscription.menu_sale_order_log_growth_report
msgid "MRR Breakdown"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__amount_signed
#: model_terms:ir.ui.view,arch_db:sale_subscription.view_sale_order_log_analysis_graph
#: model_terms:ir.ui.view,arch_db:sale_subscription.view_sale_order_log_growth_graph
#: model_terms:ir.ui.view,arch_db:sale_subscription.view_sale_order_log_growth_pivot
msgid "MRR Change"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__mrr_change_normalized
msgid "MRR Change (normalized)"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__mrr_change_amount
msgid "MRR Change Amount"
msgstr "Sprememba zneska MRR"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "MRR Change More"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__mrr_change_period
msgid "MRR Change Period"
msgstr "Obdobje spremembe MRR"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__mrr_change_unit
msgid "MRR Change Unit"
msgstr "Enota za spremembo MRR"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__mrr_max
msgid "MRR Range Max"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__mrr_min
msgid "MRR Range Min"
msgstr ""

#. module: sale_subscription
#: model:ir.ui.menu,name:sale_subscription.menu_sale_order_log_analysis_report
msgid "MRR Timeline"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log__amount_signed
#: model_terms:ir.ui.view,arch_db:sale_subscription.view_sale_order_log_growth_tree
msgid "MRR change"
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "MRR changes"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_log__recurring_monthly
msgid "MRR, after applying the changes of that particular event"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_digest
msgid "MRR:"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.portal_my_home_subscription
msgid "Manage your subscriptions"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_sidebar
msgid ""
"Managing payment methods requires to be logged in under the customer order."
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"Manual payment succeeded. Payment reference: %(ref)s. Amount: %(amount)s. "
"Contract set to: In Progress, Next Invoice: %(inv)s. Email sent to customer."
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__margin
msgid "Margin"
msgstr "Razlika v ceni"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__medium_id
msgid "Medium"
msgstr "Medij"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__starred_user_ids
msgid "Members"
msgstr "Člani"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_close_reason__retention_message
msgid "Message"
msgstr "Sporočilo"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__message_has_error
msgid "Message Delivery error"
msgstr "Napaka pri dostavi sporočila"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__message_ids
msgid "Messages"
msgstr "Sporočila"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"Mix of negative recurring lines and non-recurring line. The contract should "
"be fixed manually"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__model_id
msgid "Model"
msgstr "Model"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__model_name
msgid "Model Name"
msgstr "Naziv modela"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_alert__model_id
msgid "Model on which the automation rule runs."
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__trigger_condition__on_create_or_write
msgid "Modification"
msgstr "Sprememba"

#. module: sale_subscription
#: model:sale.subscription.plan,name:sale_subscription.subscription_plan_month
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_plan_view_form
msgid "Monthly"
msgstr "Mesečno"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__recurring_monthly
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__recurring_monthly
msgid "Monthly Recurring"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_account_move_line__subscription_mrr
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_line__recurring_monthly
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__recurring_monthly
msgid "Monthly Recurring Revenue"
msgstr "Mesečni ponavljajoči se prihodek"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_template__duration_unit__month
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_subscription_plan__billing_period_unit__month
msgid "Months"
msgstr "Meseci"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "My Subscriptions"
msgstr "Moje naročnine"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/controllers/portal.py:0
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__name
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_tree
msgid "Name"
msgstr "Naziv"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order__health__normal
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__health__normal
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__health__normal
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_subscription_report__health__normal
msgid "Neutral"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_kanban
msgid "Neutral face"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log__event_type__0_creation
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__event_type__0_creation
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
msgid "New"
msgstr "Novo"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_change_customer_wizard__partner_id
msgid "New Customer"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_change_customer_view_form
msgid "New Customer Information"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_change_customer_wizard__partner_shipping_id
msgid "New Delivery Address"
msgstr "Novi naslov za dostavo"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_change_customer_wizard__partner_invoice_id
msgid "New Invoice Address"
msgstr "Novi naslov za račun"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log__recurring_monthly
#: model_terms:ir.ui.view,arch_db:sale_subscription.view_sale_order_log_growth_tree
msgid "New MRR"
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/controllers/portal.py:0
msgid "Newest"
msgstr "Najnovejše"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.portal_my_subscriptions
msgid "Next Billing Date"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__next_invoice_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_line__next_invoice_date
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_tree
msgid "Next Invoice"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__next_invoice_date
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_sales_order_line_filter
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Next Invoice Date"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_digest
msgid "Next Invoice:"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Next invoice Date"
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "No Payment Method"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.actions.act_window,help:sale_subscription.sale_subscription_action_upsell
msgid "No Upsell Found"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_plan_view_form
msgid "No automatic mail"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_template
msgid "No thanks, close my account"
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "No valid Payment Method"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_tree
msgid "Non Recurring"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.view_order_product_search_inherit
msgid "Non-recurring"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_template_view_form
msgid "None"
msgstr "Brez"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.view_sales_order_filter_subscription
msgid "Not Recurring"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__activity_note
msgid "Note"
msgstr "Opomba"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__note_order
msgid "Note Order"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
msgid "Notes"
msgstr "Beležke"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_tree
msgid "Number"
msgstr "Številka"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__message_needaction_counter
msgid "Number of Actions"
msgstr "Število aktivnosti"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__message_has_error_counter
msgid "Number of errors"
msgstr "Število napak"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Število sporočil, ki zahtevajo ukrepanje"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Število sporočil, ki niso bila pravilno dostavljena."

#. module: sale_subscription
#: model:product.template,name:sale_subscription.product_office_cleaning_product_template
#: model:sale.order.template.line,name:sale_subscription.montly_template_line
#: model:sale.order.template.line,name:sale_subscription.yearly_template_line
msgid "Office Cleaning Service (SUB)"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__on_change_field_ids
msgid "On Change Fields Trigger"
msgstr "Sprožitev ob spremembi polj"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order_template.py:0
msgid "Operation not supported"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__related_plan_id
msgid "Optional Plans"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__order_reference
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Order"
msgstr "Naroči"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__date
msgid "Order Date"
msgstr "Datum naročila"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__invoice_status
msgid "Order Invoice Status"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__name
msgid "Order Reference"
msgstr "Sklic naročila"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_tree
msgid "Order Status"
msgstr "Status naloga"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_sidebar
msgid "Orders"
msgstr "Naročila"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log__origin_order_id
msgid "Origin Contract"
msgstr ""

#. module: sale_subscription
#: model:sale.order.close.reason,name:sale_subscription.close_reason_5
msgid "Other"
msgstr "Drugo"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "Over"
msgstr "Čez"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_sidebar
msgid "Overdue"
msgstr "Zapadlo"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__subscription_id
msgid "Parent Contract"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_line__parent_line_id
msgid "Parent Line"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_tree
msgid "Parent Subscription"
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/res_partner.py:0
msgid "Partner Subscription"
msgstr ""

#. module: sale_subscription
#: model:ir.actions.server,name:sale_subscription.model_sale_order_subscription_pause_record
msgid "Pause Subscription"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order__subscription_state__4_paused
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__subscription_state__4_paused
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__subscription_state_from__4_paused
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log__subscription_state__4_paused
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__subscription_state__4_paused
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_subscription_report__subscription_state__4_paused
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Paused"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "Pay now"
msgstr "Plačaj takoj"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_kanban
msgid "Payment Failure"
msgstr ""

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_payment_provider
msgid "Payment Provider"
msgstr "Ponudnik plačil"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__payment_term_id
msgid "Payment Terms"
msgstr "Plačilni pogoji"

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_payment_token
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__payment_token_id
msgid "Payment Token"
msgstr "Plačilni žeton"

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_payment_transaction
msgid "Payment Transaction"
msgstr "Plačilna transakcija"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "Payment not recorded"
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "Payment recorded: %s"
msgstr ""

#. module: sale_subscription
#: model:mail.template,subject:sale_subscription.email_payment_reminder
msgid ""
"Payment reminder for subscription {{ object.client_order_ref or object.name "
"}}"
msgstr "Opomnik za plačilo za naročnino"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__payment_transaction__renewal_state__pending
msgid "Pending"
msgstr "Nerešeno"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__pending_transaction
msgid "Pending Transaction"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
msgid "Pending transaction"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_alert__mrr_change_period
msgid "Period over which the KPI is calculated"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__plan_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__plan_id
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
msgid "Plan"
msgstr "Plan"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "Plan:"
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"Please add a recurring plan on the subscription or remove the recurring "
"product."
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"Please add a recurring product in the subscription or remove the recurring "
"plan."
msgstr ""

#. module: sale_subscription
#. odoo-javascript
#: code:addons/sale_subscription/static/src/xml/payment_form_templates.xml:0
msgid "Please provide another payment method for these subscriptions first."
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"Please remove the recurring plan on the subscription before sending the "
"email."
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"Please set a recurring plan on the subscription before sending the email."
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_close_reason_view_form
msgid "Portal: retention step"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_sale_order_line_list
msgid "Price"
msgstr "Cena"

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_product_pricelist
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_line__pricelist_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__pricelist_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_pricing__pricelist_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__pricelist_id
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_sales_order_line_filter
msgid "Pricelist"
msgstr "Cenik"

#. module: sale_subscription
#: model:ir.ui.menu,name:sale_subscription.menu_sale_subscription_pricelist
msgid "Pricelists"
msgstr "Ceniki"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_plan_view_form
msgid "Pricing"
msgstr "Določanje cen"

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_sale_subscription_pricing
msgid "Pricing rule of subscription products"
msgstr ""

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_product_template
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__product_tmpl_id
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_sale_order_line_list
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_sales_order_line_filter
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Product"
msgstr "Izdelek"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__categ_id
msgid "Product Category"
msgstr "Kategorija proizvoda"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__product_id
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_sales_order_line_filter
msgid "Product Variant"
msgstr "Različica izdelka"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_pricing__product_variant_ids
msgid "Product Variants"
msgstr "Različice izdelka"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_sale_order_line_list
msgid "Product Varient"
msgstr ""

#. module: sale_subscription
#: model:ir.actions.act_window,name:sale_subscription.product_action_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_pricing__product_template_id
#: model:ir.ui.menu,name:sale_subscription.menu_sale_subscription_product
#: model:ir.ui.menu,name:sale_subscription.product_menu_catalog
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "Products"
msgstr "Izdelki"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__qty_delivered
msgid "Qty Delivered"
msgstr "Kol dobavljena"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__qty_invoiced
msgid "Qty Invoiced"
msgstr "Kol obračunana"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__product_uom_qty
msgid "Qty Ordered"
msgstr "Kol naročena"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__qty_to_deliver
msgid "Qty To Deliver"
msgstr "Kol. za dostavo"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__qty_to_invoice
msgid "Qty To Invoice"
msgstr "Kol za obračun"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_analysis_view_graph
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_analysis_view_pivot
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_analysis_view_tree
msgid "Quantity"
msgstr "Količina"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order__subscription_state__1_draft
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__order_state__draft
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__subscription_state__1_draft
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__subscription_state_from__1_draft
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log__subscription_state__1_draft
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__state__draft
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__subscription_state__1_draft
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_subscription_report__subscription_state__1_draft
msgid "Quotation"
msgstr "Ponudba"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__order_state__sent
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__state__sent
msgid "Quotation Sent"
msgstr "Ponudba poslana"

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_sale_order_template
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_tree
msgid "Quotation Template"
msgstr "Predloga ponudbe"

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_sale_order_template_line
msgid "Quotation Template Line"
msgstr ""

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_sale_order_template_option
msgid "Quotation Template Option"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_digest
msgid "Quotation Template:"
msgstr ""

#. module: sale_subscription
#: model:ir.actions.act_window,name:sale_subscription.sale_subscription_template_action
#: model:ir.ui.menu,name:sale_subscription.menu_template_of_subscription
msgid "Quotation Templates"
msgstr "Predloge ponudb"

#. module: sale_subscription
#: model:ir.actions.act_window,name:sale_subscription.sale_subscription_action_quotes
#: model:ir.ui.menu,name:sale_subscription.menu_sale_subscription_quotes
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Quotations"
msgstr "Ponudbe"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__rating_avg_text
msgid "Rating Avg Text"
msgstr "Ocena Povprečno besedilo"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "Zadnje povratne informacije"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__rating_last_image
msgid "Rating Last Image"
msgstr "Ocena zadnje slike"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__rating_last_value
msgid "Rating Last Value"
msgstr "Ocena zadnje vrednosti"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__rating_operator
msgid "Rating Operator"
msgstr "Ocena operaterja"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__rating_percentage
msgid "Rating Percentage"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__rating_percentage_satisfaction
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "Rating Satisfaction"
msgstr "Zadovoljstvo z oceno"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_alert__rating_percentage
msgid ""
"Rating Satisfaction is the ratio of positive rating to total number of "
"rating."
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__rating_last_text
msgid "Rating Text"
msgstr "Besedilo ocene"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__rating_count
msgid "Rating count"
msgstr "Število ocen"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__rating_ids
msgid "Ratings"
msgstr "Ocene"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_close_reason__name
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_close_reason_wizard_view_form
msgid "Reason"
msgstr "Razlog"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__record_getter
msgid "Record Getter"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__is_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_tree
#: model_terms:ir.ui.view,arch_db:sale_subscription.view_order_product_search_inherit
#: model_terms:ir.ui.view,arch_db:sale_subscription.view_sales_order_filter_subscription
msgid "Recurring"
msgstr "Ponavljajoče"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__recurring_details
msgid "Recurring Details"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__plan_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_line__subscription_plan_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log__plan_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_template__plan_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_pricing__plan_id
#: model_terms:ir.ui.view,arch_db:sale_subscription.portal_my_subscriptions
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_plan_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_plan_view_form
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Recurring Plan"
msgstr "Recurring Plan"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_portal_content_inherit
msgid "Recurring Plan:"
msgstr ""

#. module: sale_subscription
#: model:ir.actions.act_window,name:sale_subscription.sale_subscription_plan_action
#: model:ir.ui.menu,name:sale_subscription.menu_sale_subscription_plans
msgid "Recurring Plans"
msgstr "Ponavljajoči načrti"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_pricing__price
msgid "Recurring Price"
msgstr "Ponavljajoča se cena"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.product_pricelist_view
#: model_terms:ir.ui.view,arch_db:sale_subscription.product_template_view_form_recurring
msgid "Recurring Prices"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_product_pricelist__product_subscription_pricing_ids
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__product_subscription_pricing_ids
msgid "Recurring Pricing"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__recurring_total
msgid "Recurring Revenue"
msgstr "Recurring Revenue"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
#: model:ir.model.constraint,message:sale_subscription.constraint_sale_subscription_plan_check_for_valid_billing_period_value
msgid ""
"Recurring period must be a positive number. Please ensure the input is a "
"valid positive numeric value."
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "Reference:"
msgstr "Sklic:"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__payment_transaction__renewal_state__cancel
msgid "Refused"
msgstr "Zavrnjeno"

#. module: sale_subscription
#: model:product.template,name:sale_subscription.product_fee_product_template
#: model:sale.order.template.line,name:sale_subscription.montly_template_line2
msgid "Registration fee"
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__user_extend
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__user_extend
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_plan_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_sidebar
msgid "Renew"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__renewal_count
msgid "Renewal Count"
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order__subscription_state__2_renewal
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__subscription_state__2_renewal
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__subscription_state_from__2_renewal
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log__subscription_state__2_renewal
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__subscription_state__2_renewal
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_subscription_report__subscription_state__2_renewal
msgid "Renewal Quotation"
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "Renewal Quotations"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
msgid "Renewal Quote"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_payment_transaction__renewal_state
msgid "Renewal State"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order__subscription_state__5_renewed
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__subscription_state__5_renewed
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__subscription_state_from__5_renewed
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log__subscription_state__5_renewed
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__subscription_state__5_renewed
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_subscription_report__subscription_state__5_renewed
msgid "Renewed"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
msgid "Reopen"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "Reopen your subscription"
msgstr ""

#. module: sale_subscription
#: model:ir.ui.menu,name:sale_subscription.menu_sale_subscription_report
msgid "Reporting"
msgstr "Poročanje"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
msgid ""
"Request a online payment from the customer to confirm the order. For a "
"subscription, a payment will be required also before each renewal"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__activity_user_id
msgid "Responsible"
msgstr "Odgovoren"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
msgid "Resume"
msgstr "Nadaljevanje"

#. module: sale_subscription
#: model:ir.ui.menu,name:sale_subscription.menu_sale_subscription_report_cohort
msgid "Retention"
msgstr "Zadrževanje"

#. module: sale_subscription
#: model:ir.actions.act_window,name:sale_subscription.sale_subscription_report_cohort_action
msgid "Retention Analysis"
msgstr "Analiza zadrževanja"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Napaka pri dostavi SMS "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__sms_template_id
msgid "SMS Template"
msgstr "SMS predloga"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_kanban
msgid "Sad face"
msgstr ""

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_sale_order_option
msgid "Sale Options"
msgstr "Prodajne opcije"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log__order_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__order_id
msgid "Sale Order"
msgstr "Prodajni nalog"

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_sale_order_alert
msgid "Sale Order Alert"
msgstr ""

#. module: sale_subscription
#: model:ir.actions.act_window,name:sale_subscription.action_sale_order_lines
msgid "Sale Order Lines"
msgstr ""

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_sale_order_log
msgid "Sale Order Log"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.view_sale_order_log_analysis_graph
#: model_terms:ir.ui.view,arch_db:sale_subscription.view_sale_order_log_growth_graph
#: model_terms:ir.ui.view,arch_db:sale_subscription.view_sale_order_log_growth_pivot
msgid "Sale Order Log Analysis"
msgstr ""

#. module: sale_subscription
#: model:ir.actions.server,name:sale_subscription.ir_cron_sale_subscription_update_kpi_ir_actions_server
msgid "Sale Subscription: Update KPI"
msgstr ""

#. module: sale_subscription
#: model:ir.actions.server,name:sale_subscription.account_analytic_cron_for_invoice_ir_actions_server
msgid "Sale Subscription: generate recurring invoices and payments"
msgstr ""

#. module: sale_subscription
#: model:ir.actions.server,name:sale_subscription.send_payment_reminder_ir_actions_server
msgid "Sale Subscription: send reminder for subscriptions with no token"
msgstr ""

#. module: sale_subscription
#: model:ir.actions.server,name:sale_subscription.account_analytic_cron_ir_actions_server
msgid "Sale Subscription: subscriptions expiration"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_sales_order_line_filter
msgid "Sale order"
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order_log.py:0
msgid "Sale order log: %s"
msgstr ""

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_sale_advance_payment_inv
msgid "Sales Advance Payment Invoice"
msgstr "Prodaja predplačilo računa"

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_sale_report
msgid "Sales Analysis Report"
msgstr "Poročilo o analizi prodaje"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
msgid "Sales History"
msgstr "Zgodovina prodaje"

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_sale_order_log_report
msgid "Sales Log Analysis Report"
msgstr ""

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_sale_order
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__order_state__sale
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__state__sale
msgid "Sales Order"
msgstr "Prodajni nalog"

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_sale_order_line
msgid "Sales Order Line"
msgstr "Postavka naročila"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__team_ids
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log__team_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__team_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__team_id
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Sales Team"
msgstr "Prodajna ekipa"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__activity_user__channel_leader
msgid "Sales Team Leader"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log__user_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__user_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__user_id
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Salesperson"
msgstr "Prodajalec"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_sales_order_line_filter
msgid "Search Sales Order"
msgstr "Iskanje prodajnih nalogov"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_template_view_search
msgid "Search Template"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_subscription_pricing__product_variant_ids
msgid ""
"Select Variants of the Product for which this rule applies. Leave empty if "
"this rule applies for any variant of this template."
msgstr ""

#. module: sale_subscription
#. odoo-javascript
#: code:addons/sale_subscription/static/src/js/tours/sale_subscription.js:0
msgid "Select a recurring product"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_subscription_pricing__product_template_id
msgid "Select products on which this pricing will be applied."
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_close_reason_view_form
msgid "Selectable in Portal"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_plan_view_form
msgid "Self-Service"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__payment_transaction__subscription_action__automatic_send_mail
msgid "Send Mail (automatic payment)"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__payment_transaction__subscription_action__manual_send_mail
msgid "Send Mail (manual payment)"
msgstr ""

#. module: sale_subscription
#: model:ir.actions.act_window,name:sale_subscription.sale_subscription_act_window_sms_composer_multi
#: model:ir.actions.act_window,name:sale_subscription.sale_subscription_act_window_sms_composer_single
msgid "Send an SMS Text Message"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__action__sms
msgid "Send an SMS Text Message to the customer"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__action__mail_post
msgid "Send an email to the customer"
msgstr "Stranki pošljite elektronsko sporočilo"

#. module: sale_subscription
#: model:mail.template,description:sale_subscription.email_payment_close
msgid ""
"Sent to customer to indicate that subscription is automatically terminated"
msgstr ""

#. module: sale_subscription
#: model:mail.template,description:sale_subscription.email_payment_reminder
msgid ""
"Sent to customer when payment failed but subscription is not yet cancelled"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_close_reason__sequence
msgid "Sequence"
msgstr "Zaporedje"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__action_id
msgid "Server Action"
msgstr "Strežniško dejanje"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__action__set_health_value
msgid "Set Contract Health value"
msgstr ""

#. module: sale_subscription
#: model:mail.template,description:sale_subscription.mail_template_subscription_rating
msgid ""
"Set on subscription's stage (e.g. Closed, Upsell) to ask a rating to "
"customers"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.payment_form_inherit
msgid "Set payment method for subscription"
msgstr ""

#. module: sale_subscription
#: model:ir.ui.menu,name:sale_subscription.menu_sale_subscription_settings
msgid "Settings"
msgstr "Nastavitve"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__starred
msgid "Show Subscription on dashboard"
msgstr "Prikaži naročnino na nadzorni plošči"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Show all records which has next action date is before today"
msgstr ""
"Prikaži vse zapise z datumom naslednje aktivnosti pred današnjim datumom."

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__health
#: model:ir.model.fields,help:sale_subscription.field_sale_order_alert__health
msgid "Show the health status"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_alert__trg_selection_field_id
msgid ""
"Some triggers need a reference to a selection field. This field is used to "
"store it."
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_alert__trg_field_ref
msgid ""
"Some triggers need a reference to another field. This field is used to store"
" it."
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__source_id
msgid "Source"
msgstr "Vir"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__product_ids
msgid "Specific Products"
msgstr "Specifični izdelki"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__activity_user_ids
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__activity_user__users
msgid "Specific Users"
msgstr "Posebni uporabniki"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__subscription_state
msgid "Stage"
msgstr "Stopnja"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__subscription_state_from
msgid "Stage from"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "Stage goes from"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__start_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_line__subscription_start_date
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_sales_order_line_filter
msgid "Start Date"
msgstr "Začetni datum"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_digest
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "Start Date:"
msgstr "Začetni datum:"

#. module: sale_subscription
#: model:mail.message.subtype,name:sale_subscription.subtype_state_change
msgid "State Change"
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/controllers/portal.py:0
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__order_state
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__state
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__state
#: model_terms:ir.ui.view,arch_db:sale_subscription.portal_my_subscriptions
msgid "Status"
msgstr "Status"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_change_customer_view_form
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_close_reason_wizard_view_form
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_template
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_so_portal_template
msgid "Submit"
msgstr "Pošlji"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
#: model:ir.model.fields,field_description:sale_subscription.field_account_move_line__subscription_id
#: model_terms:ir.ui.view,arch_db:sale_subscription.portal_my_subscriptions
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_template_view_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_view_cohort
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_view_cohort
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_sale_order_line_list
msgid "Subscription"
msgstr "Naročnina"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"Subscription %(link)s has been cancelled. The parent order %(parent_link)s has been reopened.\n"
"                                                You should close %(parent_link)s if the customer churned, or renew it if the customer continue the service.\n"
"                                                Note: if you already created a new subscription instead of renewing it, please cancel your newly\n"
"                                                created subscription and renew %(parent_link)s instead"
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "Subscription %s has been reopened. The end date has been removed"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_payment_transaction__subscription_action
msgid "Subscription Action"
msgstr ""

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_sale_subscription_report
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_analysis_view_graph
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_analysis_view_pivot
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_analysis_view_tree
msgid "Subscription Analysis"
msgstr ""

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_sale_subscription_change_customer_wizard
msgid "Subscription Change Customer Wizard"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__subscription_child_ids
msgid "Subscription Child"
msgstr ""

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_sale_order_close_reason
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_close_reason_view_form
msgid "Subscription Close Reason"
msgstr "Naročnina za razlog zaprtja"

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_sale_subscription_close_reason_wizard
msgid "Subscription Close Reason Wizard"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__subscription_count
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__subscription_line_count
msgid "Subscription Count"
msgstr "Število naročnin"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_subscription_plan.py:0
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_sale_order_line_list
msgid "Subscription Items"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.view_sale_order_log_growth_tree
msgid "Subscription Log Analysis"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__order_log_ids
msgid "Subscription Logs"
msgstr "Dnevniki naročnin"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_sidebar
msgid "Subscription Manager:"
msgstr ""

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_sale_subscription_plan
msgid "Subscription Plan"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__subscription_plan_ids
msgid "Subscription Plans"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_product_product__recurring_invoice
#: model:ir.model.fields,field_description:sale_subscription.field_product_template__recurring_invoice
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_line__recurring_invoice
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_template_line__recurring_invoice
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_template_option__recurring_invoice
msgid "Subscription Product"
msgstr "Naročniški izdelek"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
msgid "Subscription Quotation"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__activity_user__contract
msgid "Subscription Salesperson"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log__subscription_state
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__subscription_state
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__subscription_state
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
msgid "Subscription State"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__subscription_state
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Subscription Status"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__template_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__template_id
msgid "Subscription Template"
msgstr "Predloga za naročnino"

#. module: sale_subscription
#: model:sale.order.close.reason,name:sale_subscription.close_reason_2
msgid "Subscription does not meet my requirements"
msgstr "Naročnina ne ustreza mojim zahtevam"

#. module: sale_subscription
#: model:sale.order.close.reason,name:sale_subscription.close_reason_1
msgid "Subscription is too expensive"
msgstr "Naročnina je predraga"

#. module: sale_subscription
#: model:sale.order.close.reason,name:sale_subscription.close_reason_3
msgid "Subscription reached its end date"
msgstr "Naročnina je dosegla svoj končni datum"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_log__subscription_state
msgid "Subscription stage when the change occurred"
msgstr ""

#. module: sale_subscription
#: model:mail.message.subtype,description:sale_subscription.subtype_state_change
msgid "Subscription state has changed"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_portal_content_inherit
msgid "Subscription:"
msgstr ""

#. module: sale_subscription
#: model:mail.template,name:sale_subscription.mail_template_subscription_alert
msgid "Subscription: Default Email Alert"
msgstr ""

#. module: sale_subscription
#: model:sms.template,name:sale_subscription.sms_template_data_default_alert
msgid "Subscription: Default SMS Alert"
msgstr ""

#. module: sale_subscription
#: model:mail.template,name:sale_subscription.email_payment_close
msgid "Subscription: Payment Failure"
msgstr ""

#. module: sale_subscription
#: model:mail.template,name:sale_subscription.email_payment_reminder
msgid "Subscription: Payment Reminder"
msgstr "Naročnina: Opomnik za plačilo"

#. module: sale_subscription
#: model:sms.template,name:sale_subscription.sms_template_data_payment_failure
msgid "Subscription: Payment failure"
msgstr ""

#. module: sale_subscription
#: model:sms.template,name:sale_subscription.sms_template_data_payment_reminder
msgid "Subscription: Payment reminder"
msgstr "Naročnina: Opomnik za plačilo"

#. module: sale_subscription
#: model:mail.template,name:sale_subscription.mail_template_subscription_rating
msgid "Subscription: Rating Request"
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order_alert.py:0
#: code:addons/sale_subscription/models/sale_subscription_plan.py:0
#: model:ir.actions.act_window,name:sale_subscription.sale_subscription_action
#: model:ir.actions.act_window,name:sale_subscription.sale_subscription_action_filtered
#: model:ir.model.fields,field_description:sale_subscription.field_res_partner__subscription_count
#: model:ir.model.fields,field_description:sale_subscription.field_res_users__subscription_count
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__active_subs_count
#: model:ir.module.category,name:sale_subscription.module_category_subscription_management
#: model:ir.ui.menu,name:sale_subscription.menu_sale_subscription
#: model:ir.ui.menu,name:sale_subscription.menu_sale_subscription_action
#: model:ir.ui.menu,name:sale_subscription.menu_sale_subscription_analysis
#: model:ir.ui.menu,name:sale_subscription.menu_sale_subscription_root
#: model_terms:ir.ui.view,arch_db:sale_subscription.portal_my_home_menu_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.portal_my_home_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.portal_my_subscriptions
#: model_terms:ir.ui.view,arch_db:sale_subscription.product_template_search_view_inherit_sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.product_template_view_form_recurring
#: model_terms:ir.ui.view,arch_db:sale_subscription.res_partner_view_inherit_sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_activity
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Subscriptions"
msgstr "Naročnine"

#. module: sale_subscription
#: model:ir.actions.act_window,name:sale_subscription.sale_subscription_report_analysis_action
msgid "Subscriptions Analysis"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Subscriptions that are not assigned to an account manager."
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_analysis_view_tree
msgid "Sum of Monthly Recurring"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_analysis_view_tree
msgid "Sum of Yearly Recurring Revenue"
msgstr ""

#. module: sale_subscription
#: model:base.automation,name:sale_subscription.subscription_alert_percent_revenue_base_automation
msgid "Take action on less satisfied clients"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__team_user_id
msgid "Team Leader"
msgstr "Vodja ekipe"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_template
msgid "Tell us, why are you leaving?"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Template"
msgstr "Predloga"

#. module: sale_subscription
#: model:mail.template,description:sale_subscription.mail_template_subscription_alert
msgid ""
"Template to be used on customized alerts for subscriptions requiring "
"attention"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.actions.act_window,help:sale_subscription.sale_subscription_template_action
msgid ""
"Templates are used to prefigure subscription that\n"
"                can be selected by the salespeople to quickly configure the\n"
"                terms and conditions of the subscription."
msgstr ""

#. module: sale_subscription
#: model:mail.template,subject:sale_subscription.email_payment_close
msgid ""
"Termination of subscription {{ object.client_order_ref or object.name }}"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_account_move_line__subscription_mrr
msgid ""
"The MRR is computed by dividing the signed amount (in company currency) by the amount of time between the start and end dates converted in months.\n"
"This allows comparison of invoice lines created by subscriptions with different temporalities.\n"
"The computation assumes that 1 month is comprised of exactly 30 days, regardless  of the actual length of the month."
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_alert__trigger_field_ids
msgid ""
"The automation rule will be triggered if and only if one of these fields is "
"updated.If empty, all fields are watched."
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_subscription_pricing.py:0
msgid "The company of the plan is different from the company of the pricelist"
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"The deferred settings are not properly set. Please complete them to generate"
" subscription deferred revenues"
msgstr ""

#. module: sale_subscription
#: model:ir.model.constraint,message:sale_subscription.constraint_sale_order_template_check_duration_value
msgid "The duration can't be negative or 0."
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__first_contract_date
msgid ""
"The first contract date is the start date of the first contract of the "
"sequence. It is common across a subscription and its renewals."
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"The following recurring orders have draft invoices. Please Confirm them or "
"cancel them before creating new invoices. %s."
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/account_move.py:0
msgid ""
"The following refund %s has been made on this contract. Please check the "
"next invoice date if necessary."
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"The last invoice (%s) of this subscription is unpaid after the due date."
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__pending_transaction
msgid "The last transaction of the order is currently pending"
msgstr ""

#. module: sale_subscription
#: model:ir.model.constraint,message:sale_subscription.constraint_sale_order_check_start_date_lower_next_invoice_date
msgid "The next invoice date of a sale order should be after its start date."
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__next_invoice_date
#: model:ir.model.fields,help:sale_subscription.field_sale_order_line__next_invoice_date
msgid ""
"The next invoice will be created on this date then the period will be "
"extended."
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/controllers/portal.py:0
msgid ""
"The payment method you selected can only pay amounts up to %s. Please create"
" or select another one."
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order_close_reason.py:0
msgid ""
"The reason %s is required by the Subscription application and cannot be "
"deleted."
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_close_reason_view_form
msgid "The reason for closing a subscription"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_close_reason_view_form
msgid "The redirect link of the call to action"
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "The renewal %s has been cancelled."
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid ""
"The scheduled action for alerts has been deleted. Update the Subscriptions "
"module to re-create it."
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__start_date
#: model:ir.model.fields,help:sale_subscription.field_sale_order_line__subscription_start_date
msgid "The start date indicate when the subscription periods begin."
msgstr ""

#. module: sale_subscription
#: model:sale.order.close.reason,name:sale_subscription.close_reason_renew
msgid "The subscription was renewed with a new plan"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_close_reason_view_form
msgid "The text to display on the call to action"
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "The upsell %s has been cancelled."
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"The upsell %s has been cancelled. Please recheck the quantities as they may "
"have been affected by this cancellation."
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "The upsell %s has been confirmed."
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_subscription_pricing.py:0
msgid "There are multiple pricings for an unique product, plan and pricelist."
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_template
msgid "There is a"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_kanban
msgid ""
"This bar allows to filter the opportunities based on scheduled activities."
msgstr ""
"This bar allows to filter the opportunities based on scheduled activities."

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_alert__record_getter
msgid ""
"This code will be run to find on which record the automation rule should be "
"run."
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_close_reason_view_form
msgid ""
"This message will be displayed to convince the customer to stay (e.g., We "
"don't want you to leave, can we offer to schedule a meeting with your "
"account manager?)"
msgstr ""

#. module: sale_subscription
#. odoo-javascript
#: code:addons/sale_subscription/static/src/xml/payment_form_templates.xml:0
msgid ""
"This payment method cannot be deleted, because it is currently linked to the\n"
"                following active subscriptions:"
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "This subscription is renewed in %s with a change of plan."
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_digest
msgid "This subscription is the renewal of subscription"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_digest
msgid "This upsell order has been created from the subscription"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid ""
"This will trigger the action on all linked subsccriptions, regardless of "
"possible timed conditions. Are you sure you want to proceed?"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__trigger_condition__on_time
msgid "Timed Condition"
msgstr "Časovno stanje"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__activity_summary
msgid "Title"
msgstr "Naslov"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "To Invoice"
msgstr "Za obračun"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/controllers/portal.py:0
#: model:ir.actions.act_window,name:sale_subscription.sale_subscription_action_pending
#: model:ir.ui.menu,name:sale_subscription.menu_sale_subscription_pending
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
msgid "To Renew"
msgstr "Za obnovitev"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
msgid "To renew"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Today Activities"
msgstr "Današnje aktivnosti"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__price_total
#: model_terms:ir.ui.view,arch_db:sale_subscription.portal_my_subscriptions
msgid "Total"
msgstr "Skupaj"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__non_recurring_total
msgid "Total Non Recurring Revenue"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_portal_content_inherit
msgid "Total Quantity"
msgstr "Skupna količina"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__recurring_total
msgid "Total Recurring"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log__event_type__3_transfer
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__event_type__3_transfer
msgid "Transfer"
msgstr "Prenos"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__trigger
msgid "Trigger"
msgstr "Sprožilec"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__trg_date_id
msgid "Trigger Date"
msgstr "Sproženo dne"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__trg_selection_field_id
msgid "Trigger Field"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__trg_field_ref_model_name
msgid "Trigger Field Model"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__trigger_field_ids
msgid "Trigger Fields"
msgstr "Sprožilna polja"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "Trigger Now"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__trigger_condition
msgid "Trigger On"
msgstr "Sprožilec vklopljen"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__trg_field_ref
msgid "Trigger Reference"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.actions.act_window,help:sale_subscription.sale_subscription_alert_action
msgid ""
"Trigger alerts for salespersons or customers: churn, invoice not paid, "
"upsell, etc."
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_close_reason__retention_message
msgid ""
"Try to prevent customers from leaving and closing their subscriptions, "
"thanks to a catchy message and a call to action."
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log__event_type
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__event_type
msgid "Type of event"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Unassigned"
msgstr "Nedodeljeno"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__billing_period_unit
msgid "Unit"
msgstr "Enota"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__price_unit
msgid "Unit Price"
msgstr "Cena enote"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__product_uom
msgid "Unit of Measure"
msgstr "Enota mere"

#. module: sale_subscription
#: model:sale.order.close.reason,name:sale_subscription.close_reason_unknown
msgid "Unknown"
msgstr "Neznano"

#. module: sale_subscription
#: model:sale.order.close.reason,name:sale_subscription.close_reason_unpaid_subscription
msgid "Unpaid subscription"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_subscription_plan__auto_close_limit
msgid ""
"Unpaid subscription after the due date majored by this number of days will be automatically closed by the subscriptions expiration scheduled action. \n"
"If the chosen payment method has failed to renew the subscription after this time, the subscription is automatically closed."
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__untaxed_amount_invoiced
msgid "Untaxed Amount Invoiced"
msgstr "Znesek brez davka - zaračunano"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__untaxed_amount_to_invoice
msgid "Untaxed Amount To Invoice"
msgstr "Znesek brez davka - za obračun"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__price_subtotal
msgid "Untaxed Total"
msgstr "Skupaj brez davka"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_digest
msgid "Untaxed Total:"
msgstr ""

#. module: sale_subscription
#: model:base.automation,name:sale_subscription.subscription_alert_bad_health_base_automation
msgid "Update health value according to MRR"
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order__subscription_state__7_upsell
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__subscription_state__7_upsell
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__subscription_state_from__7_upsell
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log__subscription_state__7_upsell
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__subscription_state__7_upsell
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_subscription_report__subscription_state__7_upsell
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
msgid "Upsell"
msgstr "Nadgradna prodaja"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"Upsell %(order)s for customer %(customer)s for the period %(date_start)s to "
"%(date_end)s %(nl)s%(lines)s"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__upsell_count
msgid "Upsell Count"
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "Upsell Quotations"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
msgid "Upsell Quote"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.actions.act_window,help:sale_subscription.sale_subscription_action_upsell
msgid ""
"Upsell orders allow you to add or remove products to ongoing subscriptions.\n"
"                It can be created by clicking on the Upsell button from an active subscription."
msgstr ""

#. module: sale_subscription
#: model:ir.actions.act_window,name:sale_subscription.sale_subscription_action_upsell
#: model:ir.ui.menu,name:sale_subscription.menu_sale_subscription_upsell
msgid "Upsells"
msgstr "Nadgradna prodaja"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__url
msgid "Url"
msgstr "Url"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__trg_date_calendar_id
msgid "Use Calendar"
msgstr "Uporabi koledar"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__trg_date_resource_field_id
msgid "Use employee work schedule"
msgstr "Uporabi delovni urnik kadra"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_alert__trg_date_resource_field_id
msgid "Use the user's working schedule."
msgstr "Uporabi delovni urnik uporabnika."

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_line__product_template_variant_value_ids
msgid "Variant Values"
msgstr "Vrednosti različic"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_close_reason__visible_in_portal
msgid "Visible In Portal"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__volume
msgid "Volume"
msgstr "Prostornina"

#. module: sale_subscription
#. odoo-javascript
#: code:addons/sale_subscription/static/src/js/tours/sale_subscription.js:0
msgid ""
"Want a recurring billing through subscription management? Get started by "
"clicking here"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__warehouse_id
msgid "Warehouse"
msgstr "Skladišče"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/product.py:0
msgid "Warning"
msgstr "Opozorilo"

#. module: sale_subscription
#. odoo-javascript
#: code:addons/sale_subscription/static/src/js/payment_form.js:0
msgid "Warning!"
msgstr "Opozorilo!"

#. module: sale_subscription
#: model_terms:sale.order.close.reason,retention_message:sale_subscription.close_reason_1
msgid "We are sorry to hear that."
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_template
msgid "We are sorry to see you go."
msgstr ""

#. module: sale_subscription
#: model_terms:sale.order.close.reason,retention_message:sale_subscription.close_reason_1
msgid "We don't want you to leave us like this."
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__webhook_uuid
msgid "Webhook UUID"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__website_message_ids
msgid "Website Messages"
msgstr "Sporočila iz spletne strani"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__website_message_ids
msgid "Website communication history"
msgstr "Kronologija komunikacij spletne strani"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_subscription_plan__billing_period_unit__week
msgid "Weeks"
msgstr "Tedni"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_alert__trg_date_calendar_id
msgid ""
"When calculating a day-based timed condition, it is possibleto use a "
"calendar to compute the date based on working days."
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_alert__trg_date_id
msgid ""
"When should the condition be triggered.\n"
"                If present, will be checked by the scheduler. If empty, will be checked at creation and update."
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_alert__active
msgid "When unchecked, the rule is hidden and will not be executed."
msgstr "Če ni označeno, je pravilo skrito in ne bo izvedeno."

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__starred
msgid "Whether this subscription should be displayed on the dashboard or not"
msgstr ""

#. module: sale_subscription
#: model:sale.subscription.plan,name:sale_subscription.subscription_plan_year
msgid "Yearly"
msgstr "Letno"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__recurring_yearly
msgid "Yearly Recurring"
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_template__duration_unit__year
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_subscription_plan__billing_period_unit__year
msgid "Years"
msgstr "Leta"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_portal_templates_warnings
msgid ""
"You are about to permanently close a subscription that is valid until\n"
"                    &amp;nbsp;"
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/product.py:0
msgid ""
"You can not change the recurring property of this product because it has "
"been sold already."
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/wizard/sale_subscription_close_reason_wizard.py:0
msgid ""
"You can not churn a contract that has not been invoiced. Please cancel the "
"contract instead."
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"You can not delete a confirmed subscription. You must first close and cancel"
" it before you can delete it."
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"You can not upsell or renew a subscription that has not been invoiced yet. "
"Please, update directly the %s contract or invoice it first."
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/res_partner.py:0
msgid ""
"You can't archive the partner as it is used in the following recurring "
"orders: %s"
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "You cannot cancel a churned renewed subscription."
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "You cannot cancel a subscription that has been invoiced."
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/wizard/sale_subscription_change_customer_wizard.py:0
msgid "You cannot change the customer of non recurring sale order."
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"You cannot create an upsell for this subscription because it :\n"
" - Has not started yet.\n"
" - Has no invoiced period in the future."
msgstr ""

#. module: sale_subscription
#: model:ir.model.constraint,message:sale_subscription.constraint_sale_order_sale_subscription_state_coherence_2
msgid "You cannot have a draft SO be a confirmed subscription."
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "You cannot renew a contract that already has an active subscription. "
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "You cannot renew a subscription that has been renewed. "
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "You cannot reopen a subscription that isn't closed."
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"You cannot set to draft a cancelled quotation linked to invoiced "
"subscriptions. Please create a new quotation."
msgstr ""

#. module: sale_subscription
#: model:ir.model.constraint,message:sale_subscription.constraint_sale_order_sale_subscription_state_coherence
msgid ""
"You cannot set to draft a confirmed subscription. Please create a new "
"quotation"
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "You cannot upsell a subscription using a different currency."
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"You cannot upsell a subscription whose next invoice date is in the past.\n"
"Please, invoice directly the %s contract."
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"You cannot validate a renewal quotation starting before the next invoice "
"date of the parent contract. Please update the start date after the %s."
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.portal_my_subscriptions
msgid "You don't have any subscriptions yet."
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_sidebar
msgid "You must be logged in to manage your payment methods."
msgstr ""

#. module: sale_subscription
#: model_terms:ir.actions.act_window,help:sale_subscription.product_action_subscription
msgid ""
"You must define a product for everything you sell or purchase,\n"
"                whether it's a storable product, a consumable or a service."
msgstr ""
"Definirate morate vsak izdelek, katerega prodajate ali kupujete,\n"
"                ne glede na to, ali gre za izdelek z zalogo, potrošni material ali storitev. "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "Your Subscription"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.payment_method_form
msgid "Your payment details will be saved for automatic renewals."
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "Your subscription is about to end on the"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "Your subscription is closed."
msgstr "Vaša naročnina je zaključena."

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "Your subscription is expired, will be closed soon."
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_portal_templates_warnings
msgid "Your subscription will be closed on the"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_template
msgid "and"
msgstr "in"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_close_reason_view_form
msgid "button link"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_close_reason_view_form
msgid "button text"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "e.g. Closed Subscription"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "e.g. Discuss proposal"
msgstr "npr. Pogovor o predlogu"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_template
msgid "existing for this subscription, please confirm it or reject it."
msgstr ""

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__rating_operator__>
msgid "greater than"
msgstr "večje kot"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__rating_operator__<
msgid "less than"
msgstr "manjše kot"

#. module: sale_subscription
#: model:sale.order.close.reason,retention_button_link:sale_subscription.close_reason_1
msgid "mailto:<EMAIL>?subject=Close contract: too expensive"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_close_reason_view_form
msgid "message"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "missing payments (from"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_sidebar
msgid "on"
msgstr "na"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_subscription_plan.py:0
msgid "per %d months"
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_subscription_plan.py:0
msgid "per %d weeks"
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_subscription_plan.py:0
msgid "per %d years"
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_subscription_plan.py:0
msgid "per month"
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_subscription_plan.py:0
msgid "per week"
msgstr ""

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_subscription_plan.py:0
msgid "per year"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_template
msgid "quotation"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_template
msgid ""
"quotation existing for this subscription, please confirm it or reject it."
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_template
msgid ""
"quotation existing for this subscription, please confirm them or reject "
"them."
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_template
msgid "renewal"
msgstr ""

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "to this day) will be automatically processed."
msgstr ""

#. module: sale_subscription
#: model:sms.template,body:sale_subscription.sms_template_data_payment_failure
msgid ""
"{{ object.company_id.name }}: Our final attempt to process a payment for "
"your subscription failed. As your payment should have been made on {{ "
"object.next_invoice_date }}, your subscription has been terminated."
msgstr ""

#. module: sale_subscription
#: model:mail.template,subject:sale_subscription.mail_template_subscription_alert
msgid ""
"{{ object.company_id.name }}: Please check the subscription {{ object.name "
"}}"
msgstr ""

#. module: sale_subscription
#: model:mail.template,subject:sale_subscription.mail_template_subscription_rating
msgid "{{ object.company_id.name }}: Service Rating Request"
msgstr ""

#. module: sale_subscription
#: model:sms.template,body:sale_subscription.sms_template_data_payment_reminder
msgid ""
"{{ object.company_id.name }}: We were unable to process a payment for your "
"subscription. Your subscription {{ object.name }} is still valid but will be"
" suspended on {{ object.next_invoice_date }} unless the payment succeeds in "
"the meantime."
msgstr ""

#. module: sale_subscription
#: model:sms.template,body:sale_subscription.sms_template_data_default_alert
msgid ""
"{{ object.company_id.name }}: Your subscription {{ object.name }} needs your"
" attention. If you have some concerns about it, please contact {{ "
"object.user_id.name }}, your contact person."
msgstr ""
