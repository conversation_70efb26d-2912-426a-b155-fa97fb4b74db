# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_payroll
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:52+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Polish (https://app.transifex.com/odoo/teams/41243/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip_worked_days.py:0
msgid " (Half-Day)"
msgstr "(Pół dnia)"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__count
msgid "# Payslip"
msgstr "# Pasek wypłaty"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__payslips_count
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract_history__payslips_count
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__payslip_count
msgid "# Payslips"
msgstr "# Paski wypłaty"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payroll_declaration_mixin.py:0
msgid "%(employee_name)s-declaration-%(year)s"
msgstr "%(employee_name)s-deklaracja-%(year)s"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_salary_rule.py:0
msgid ""
"%(error_type)s\n"
"- Employee: %(employee)s\n"
"- Contract: %(contract)s\n"
"- Payslip: %(payslip)s\n"
"- Salary rule: %(name)s (%(code)s)\n"
"- Error: %(error_message)s"
msgstr ""
"%(error_type)s\n"
"- Pracownik: %(employee)s\n"
"- Umowa: %(contract)s\n"
"- Odcinek wypłaty: %(payslip)s\n"
"- Reguła wypłaty: %(name)s (%(code)s)\n"
"- Błąd: %(error_message)s"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payroll_headcount.py:0
msgid "%(rate)s Hours/week"
msgstr "%(rate)s Godzin/tydzień"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "%(start_date_string)s and %(end_date_string)s"
msgstr "%(start_date_string)s i %(end_date_string)s"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_contract.py:0
#: code:addons/hr_payroll/models/hr_payroll_structure.py:0
#: code:addons/hr_payroll/models/hr_salary_rule.py:0
msgid "%s (copy)"
msgstr "%s (kopiuj)"

#. module: hr_payroll
#: model:ir.actions.report,print_report_name:hr_payroll.action_report_light_payslip
#: model:ir.actions.report,print_report_name:hr_payroll.action_report_payslip
msgid "'Payslip - %s' % (object.name)"
msgstr "'Pasek wypłaty - %s' % (object.name)"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "(%s Payslips)"
msgstr "(%s Paski wypłaty)"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__state
msgid ""
"* When the payslip is created the status is 'Draft'\n"
"                \n"
"* If the payslip is under verification, the status is 'Waiting'.\n"
"                \n"
"* If the payslip is confirmed then status is set to 'Done'.\n"
"                \n"
"* When the user cancels a payslip, the status is 'Canceled'."
msgstr ""
"* Po utworzeniu paska wypłaty jego status to \"Wersja robocza\".\n"
"\n"
"* Jeśli odcinek wypłaty jest w trakcie weryfikacji, jego status to \"Oczekujący.\n"
"\n"
"* Jeśli odcinek wypłaty zostanie potwierdzony, status zostanie ustawiony na \"Gotowy\".\n"
"\n"
"* Gdy użytkownik anuluje odcinek wypłaty, jego status to \"Anulowany\"."

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "-> Report"
msgstr "-> Raport"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_view_kanban
msgid "/ Hour"
msgstr "/ Godzina"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_view_kanban
msgid "/ Month"
msgstr "/ miesiąc"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
msgid "/ day"
msgstr "/ dzień"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
msgid "/ half-month"
msgstr "/ pół-miesiąc"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
msgid "/ half-year"
msgstr "/ półrocze"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
msgid "/ quarter"
msgstr "/ kwartał"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
msgid "/ two months"
msgstr "/ dwa miesiące"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
msgid "/ two weeks"
msgstr "/ dwa tygodnie"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
msgid "/ week"
msgstr "/ tydzień"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
msgid "/ year"
msgstr "/ rok"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_note_demo_content
msgid "1. Save documents to terminated employees"
msgstr "1. Zapisuj dokumenty dla zwolnionych pracowników"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
msgid "100"
msgstr "100"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_13th_month_salary
msgid "13th pay salary"
msgstr "13. pensja"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "1st semester of %s"
msgstr "1-szy semestr %s"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_note_demo_content
msgid "2. Index salaries for Marketing department"
msgstr "2. Wynagrodzenia indeksowe dla działu marketingu"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
msgid "2023 Payroll"
msgstr "Lista płac 2023"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "2nd semester of %s"
msgstr "2. semestr %s"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_note_demo_content
msgid "3. Create a new contract for Marc Demo with his new position"
msgstr "3. Stwórz nowy kontrakt dla Marca Demo z jego nowym stanowiskiem."

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_rule_parameter_view_form
msgid ""
"<span class=\"bg-white opacity-75 w-100 h-100 o_salary_rule_overlay position-absolute top-0 end-0 w-100 h-100 text-center d-flex justify-content-center flex-column fs-3\">\n"
"                            Save your Salary Rule in order to add Parameter Values.\n"
"                        </span>"
msgstr ""
"<span class=\"bg-white opacity-75 w-100 h-100 o_salary_rule_overlay position-absolute top-0 end-0 w-100 h-100 text-center d-flex justify-content-center flex-column fs-3\">\n"
"Zapisz regułę wynagrodzeń, aby dodać wartości parametrów.\n"
"</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_resource_calendar_view_form
msgid "<span class=\"ms-2\"> hours/week</span>"
msgstr "<span class=\"ms-2\">godzin/tydzień</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                            Work Entries\n"
"                            </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"Wpisy pracy\n"
"</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
msgid ""
"<span class=\"o_stat_text\"> Payslips </span>\n"
"                            <span class=\"o_stat_value\"> New </span>"
msgstr ""
"<span class=\"o_stat_text\"> Listy płac </span>\n"
"                            <span class=\"o_stat_value\"> Nowe </span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_export_mixin_form_view
msgid "<span class=\"o_stat_text\">Eligible Employees</span>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_headcount_view_form
msgid "<span class=\"o_stat_text\">Employees</span>"
msgstr "<span class=\"o_stat_text\">Pracownicy</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
msgid "<span class=\"o_stat_text\">Payslips</span>"
msgstr "<span class=\"o_stat_text\">Paski wypłaty</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
msgid ""
"<span class=\"o_stat_text\">Salary Attachments</span>\n"
"                            <span class=\"o_stat_value\">New</span>"
msgstr ""
"<span class=\"o_stat_text\">Załączniki do wynagrodzenia</span>\n"
"                            <span class=\"o_stat_value\">Nowe</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
msgid ""
"<span nolabel=\"1\" colspan=\"2\">This wizard will generate payslips for all"
" selected employee(s) based on the dates and credit note specified on "
"Payslips Run.</span>"
msgstr ""
"<span nolabel=\"1\" colspan=\"2\">Ten kreator wygeneruje paski wypłaty dla "
"wszystkich wybranych pracowników na podstawie dat i not kredytowych "
"określonych w Payslips Run.</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_index_form_view
msgid ""
"<span role=\"status\" class=\"alert alert-warning d-block\" invisible=\"not "
"display_warning\">You have selected contracts that are not running, this "
"wizard can only index running contracts.</span>"
msgstr ""
"<span role=\"status\" class=\"alert alert-warning d-block\" invisible=\"not "
"display_warning\">Wybrałeś kontrakty, które nie są uruchomione, ten kreator "
"może indeksować tylko uruchomione kontrakty.</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_resource_calendar_view_form
msgid "<span> %</span>"
msgstr "<span>%</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
msgid "<span>/ hour</span>"
msgstr "<span>/ godzinę</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_edit_payslip_lines_form_wizard
msgid ""
"<span><strong>Tip:</strong> Each time you edit the quantity or the amount on"
" a line, we recompute the following lines. We recommend that you edit from "
"top to bottom to prevent your edition from being overwritten by the "
"automatic recalculation. Be careful that reordering the lines doesn't "
"recompute them.</span>"
msgstr ""
"<span><strong>Wskazówka:</strong> Za każdym razem, gdy edytujesz ilość lub "
"kwotę w wierszu, ponownie obliczamy kolejne wiersze. Zalecamy edytowanie od "
"góry do dołu, aby zapobiec nadpisaniu edycji przez automatyczne "
"przeliczanie. Należy uważać, aby zmiana kolejności wierszy nie spowodowała "
"ich ponownego obliczenia.</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "<span>Days</span>"
msgstr "<span>Dni</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_form
msgid "<span>Payslips</span>"
msgstr "<span>Paski wypłat</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<span>Total</span>"
msgstr "<span>Wszystkie</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_rule_parameter_view_form
msgid "<span>Versions</span>"
msgstr "<span>Wersje</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong class=\"me-2\">Address:</strong>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong class=\"me-2\">Children:</strong>"
msgstr "<strong class=\"me-2\">Dzieci:</strong>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong class=\"me-2\">Computed On:</strong>"
msgstr "<strong class=\"me-2\">Obliczono dnia:</strong>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong class=\"me-2\">Contract Start Date:</strong>"
msgstr "<strong class=\"me-2\">Data rozpoczęcia umowy:</strong>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong class=\"me-2\">Contract Type:</strong>"
msgstr "<strong class=\"me-2\">Rodzaj umowy:</strong>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong class=\"me-2\">Department:</strong>"
msgstr "<strong class=\"me-2\">Dział:</strong>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong class=\"me-2\">Email:</strong>"
msgstr "<strong class=\"me-2\">Email:</strong>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong class=\"me-2\">ID:</strong>"
msgstr "<strong class=\"me-2\">ID:</strong>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong class=\"me-2\">Job Position:</strong>"
msgstr "<strong class=\"me-2\">Stanowisko pracy:</strong>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong class=\"me-2\">Marital Status:</strong>"
msgstr "<strong class=\"me-2\">Stan cywilny:</strong>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong class=\"me-2\">Name:</strong>"
msgstr "<strong class=\"me-2\">Imię:</strong>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong class=\"me-2\">Pay Period:</strong>"
msgstr "<strong class=\"me-2\">Okres płatności:</strong>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong class=\"me-2\">Working Schedule:</strong>"
msgstr "<strong class=\"me-2\">Harmonogram pracy:</strong>"

#. module: hr_payroll
#: model_terms:web_tour.tour,rainbow_man_message:hr_payroll.payroll_tours
msgid ""
"<strong>Congrats, Your first payslip is now finished. It's time for you to "
"explore the Payroll app by yourself.</strong>"
msgstr ""
"<strong>Gratulacje, Twój pierwszy pasek wypłaty został ukończony. Teraz "
"pora, abyś zapoznał się osobiście z aplikacją Lista płac.</strong>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
msgid "<strong>Register Name:</strong>"
msgstr "<strong>Nazwa funduszu:</strong>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
msgid "<strong>Total</strong>"
msgstr "<strong>Suma</strong>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_light_payslip
msgid "<strong>YTD</strong>"
msgstr "<strong>YTD</strong>"

#. module: hr_payroll
#: model:mail.template,body_html:hr_payroll.mail_template_new_payslip
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto;\"><tbody>\n"
"    <tr><td valign=\"top\" style=\"text-align: left; font-size: 14px;\">\n"
"        Dear <t t-esc=\"object.employee_id.name\"/>, a new payslip is available for you.<br/><br/>\n"
"        Please find the PDF in your employee portal.<br/><br/>\n"
"        Have a nice day,<br/>\n"
"        The HR Team\n"
"    </td></tr>\n"
"</tbody></table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto;\"><tbody>\n"
"    <tr><td valign=\"top\" style=\"text-align: left; font-size: 14px;\">\n"
"        Drogi/a <t t-esc=\"object.employee_id.name\"/>, nowy odcinek wynagrodzenia jest dla Ciebie dostępny.<br/><br/>\n"
"        Plik PDF znajdziesz w portalu pracownika.<br/><br/>\n"
"        Miłego dnia,<br/>\n"
"        Zespół HR\n"
"    </td></tr>\n"
"</tbody></table>\n"
"            "

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_work_entry_type_is_unforeseen_is_leave
msgid "A unforeseen absence must be a leave."
msgstr "Nieprzewidziana nieobecność musi być urlopem."

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Accounting"
msgstr "Księgowość"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_needaction
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__message_needaction
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_needaction
msgid "Action Needed"
msgstr "Wymagane działanie"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_dashboard_warning__active
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__active
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__active
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__active
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_search
msgid "Active"
msgstr "Aktywne"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__active_amount
msgid "Active Amount"
msgstr "Aktywna kwota"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__activity_ids
msgid "Activities"
msgstr "Czynności"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_exception_decoration
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Dekoracja wyjątku aktywności"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_state
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__activity_state
msgid "Activity State"
msgstr "Stan aktywności"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_type_icon
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ikona typu aktywności"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/views/add_payslips_hook.js:0
#: code:addons/hr_payroll/static/src/views/payslip_batch_form/payslip_batch_form.js:0
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
msgid "Add Payslips"
msgstr "Dodaj paski wypłaty"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid "Add a <strong>name</strong> to the contract."
msgstr "Dodaj <strong>nazwę</strong> do umowy."

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid "Add a employee to your contract"
msgstr "Dodaj pracownika do umowy"

#. module: hr_payroll
#: model_terms:ir.actions.act_window,help:hr_payroll.action_view_hr_payroll_structure_from_type
msgid "Add a new salary structure"
msgstr "Dodanie nowej struktury wynagrodzeń"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Add an internal note..."
msgstr "Dodaj notatkę wewnetrzną ..."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__ytd_computation
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_structure__ytd_computation
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__ytd_computation
msgid ""
"Adds a column in the payslip that shows the accumulated amount paid for "
"different rules during the year"
msgstr ""
"Dodaje kolumnę do paska wypłaty, która pokazuje skumulowaną ilość zapłaconą "
"za różne reguły podczas roku."

#. module: hr_payroll
#: model:res.groups,name:hr_payroll.group_hr_payroll_manager
msgid "Administrator"
msgstr "Administrator"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/components/dashboard/payslip_batch/payslip_batch.xml:0
msgid "All"
msgstr "Wszystko"

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_employee_payslips
msgid "All Payslips"
msgstr "Wszystkie paski wypłaty"

#. module: hr_payroll
#: model:hr.salary.rule.category,name:hr_payroll.ALW
msgid "Allowance"
msgstr "Dodatek"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_rule__condition_select__none
msgid "Always True"
msgstr "Zawsze prawda"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__amount
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__amount
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__amount
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__amount
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Amount"
msgstr "Kwota"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__amount_other_input_id
msgid "Amount Other Input"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__amount_select
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__amount_select
msgid "Amount Type"
msgstr "Rodzaj kwoty"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payment_report_wizard.py:0
msgid "Amount to pay"
msgstr "Kwota do zapłaty"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__monthly_amount
msgid "Amount to pay each payslip."
msgstr "Kwota do zapłaty na każdym pasku wypłaty"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__active_amount
msgid ""
"Amount to pay for this payslip, Payslip Amount or less depending on the "
"Remaining Amount."
msgstr ""
"Kwota do zapłaty za ten odcinek wypłaty, Kwota odcinka wypłaty lub mniej w "
"zależności od pozostałej kwoty."

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_payroll_employee_declaration_unique_employee_sheet
msgid "An employee can only have one declaration per sheet."
msgstr "Pracownik może mieć tylko jedną deklarację na arkusz."

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/components/dashboard/payroll_stats/payroll_stats.xml:0
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_contract__schedule_pay__annually
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__default_schedule_pay__annually
msgid "Annually"
msgstr "Rocznie"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid ""
"Another refund payslip with the same amount has been found. Do you want to "
"create a new one?"
msgstr ""
"Znaleziono inny pasek wypłaty z taką samą kwotą. Czy chcesz utworzyć nowy?"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__appears_on_payslip
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__appears_on_payslip
msgid "Appears on Payslip"
msgstr "Pojawi się w pasku"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__condition_python
msgid ""
"Applied this rule for calculation if condition is true. You can specify "
"condition like basic > 1000."
msgstr ""
"Ta reguła będzie zastosowana, jeśli warunek będzie True (prawda). Warunek "
"może wyglądać tak: basic > 1000."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__date_estimated_end
msgid "Approximated end date."
msgstr "Przybliżona data zakończenia."

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_export_mixin__reference_month__4
#: model:ir.model.fields.selection,name:hr_payroll.selection__res_company__ytd_reset_month__4
msgid "April"
msgstr "Kwiecień"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_dashboard_warning_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_dashboard_warning_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_input_type_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_input_type_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_employee_grade_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payroll_structure_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_rule_filter
msgid "Archived"
msgstr "Zarchiwizowane"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/components/dashboard/todo_list/todo_list.js:0
msgid ""
"Are you sure you want to delete this note? All content will be definitely "
"lost."
msgstr ""
"Czy na pewno chcesz usunąć tę notatkę? Cała zawartość zostanie bezpowrotnie "
"utracona."

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.default_assignment_of_salary_rule
msgid "Assignment of Salary"
msgstr "Przypisanie wynagrodzenia"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid ""
"At least one previous negative net could be reported on this payslip for %s"
msgstr ""
"Co najmniej jeden poprzedni ujemny wynik netto mógł zostać odnotowany na tym"
" pasku wypłaty dla %s"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_attachment_count
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__message_attachment_count
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_attachment_count
msgid "Attachment Count"
msgstr "Liczba załączników"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__attachment_name
msgid "Attachment Name"
msgstr "Nazwa załącznika"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.default_attachment_of_salary_rule
msgid "Attachment of Salary"
msgstr "Załącznik do wynagrodzenia"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_report__work_entry_source__attendance
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_report__work_entry_source__attendance
msgid "Attendances"
msgstr "Obecności"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_export_mixin__reference_month__8
#: model:ir.model.fields.selection,name:hr_payroll.selection__res_company__ytd_reset_month__8
msgid "August"
msgstr "Sierpień"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "August 2023 Payslip"
msgstr "Pasek wypłaty sierpień 2023"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__struct_ids
msgid "Availability in Structure"
msgstr "Dostępność w strukturze"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__available_in_attachments
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_input_type_view_search
msgid "Available in attachments"
msgstr "Dostępne w załącznikach"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__avatar_1920
msgid "Avatar"
msgstr "Awatar"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__avatar_128
msgid "Avatar 128"
msgstr "Awatar 128"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_report_view_tree
msgid "Average of Basic Wage"
msgstr "Średnia płaca podstawowa"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_report_view_tree
msgid "Average of Net Wage"
msgstr "Średnia płaca netto"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payment_report_wizard.py:0
msgid "Bank account"
msgstr "Konto bankowe"

#. module: hr_payroll
#: model:hr.salary.rule.category,name:hr_payroll.BASIC
msgid "Basic"
msgstr "Podstawowe"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
#: model:hr.salary.rule,name:hr_payroll.default_basic_salary_rule
msgid "Basic Salary"
msgstr "Wynagrodzenie podstawowe"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__basic_wage
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__basic_wage
msgid "Basic Wage"
msgstr "Płaca podstawowa"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__leave_basic_wage
msgid "Basic Wage for Time Off"
msgstr "Wynagrodzenie podstawowe za czas wolny"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Batch"
msgstr "Partia"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__payslip_run_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
msgid "Batch Name"
msgstr "Nazwa partii"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/components/dashboard/payslip_batch/payslip_batch.xml:0
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payslip_run
msgid "Batches"
msgstr "Partie"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_res_config_settings__module_l10n_be_hr_payroll
msgid "Belgium Payroll"
msgstr "Belgijska lista płac"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_contract__schedule_pay__bi-monthly
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__default_schedule_pay__bi-monthly
msgid "Bi-monthly"
msgstr "Co dwa miesiące"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_contract__schedule_pay__bi-weekly
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__default_schedule_pay__bi-weekly
msgid "Bi-weekly"
msgstr "Co dwa tygodnie"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_note_demo_content
msgid "Board meeting summary:"
msgstr "Podsumowanie spotkania zarządu:"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_payment_report_wizard__export_format__csv
msgid "CSV"
msgstr "CSV"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_form
msgid "Calculations"
msgstr "Obliczenia"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__calendar_changed
msgid "Calendar Changed"
msgstr "Zmiana kalendarza"

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.action_hr_payroll_cancel_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_index_form_view
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_payment_report_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Cancel"
msgstr "Anuluj"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payslip__state__cancel
msgid "Canceled"
msgstr "Anulowano"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_attachment__state__cancel
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_report__state__cancelled
msgid "Cancelled"
msgstr "Anulowano"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Cannot cancel a payslip that is done."
msgstr "Nie można anulować wykonanego paska wypłaty."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Cannot mark payslip as paid if not confirmed."
msgstr ""
"Nie można oznaczyć paska wypłaty jako zapłaconego, jeśli nie został "
"potwierdzony."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__code
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_worked_days__code
msgid ""
"Careful, the Code is used in many references, changing it could lead to "
"unwanted changes."
msgstr ""
"Ostrożnie, kod jest używany w wielu odniesieniach, jego zmiana może "
"prowadzić do niepożądanych zmian."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_headcount_line__employee_type
msgid ""
"Categorize your Employees by type. This field also has an impact on "
"contracts. Only Employees, Students and Trainee will have contract history."
msgstr ""
"Kategoryzuj swoich pracowników według typu. To pole ma również wpływ na "
"umowy. Historia umów będzie dotyczyć tylko pracowników, studentów i "
"stażystów."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__category_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__category_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__category_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_rule_filter
msgid "Category"
msgstr "Kategoria"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__is_refund
msgid ""
"Check if the value of the salary attachment must be taken into account as "
"negative (-X)"
msgstr ""
"Sprawdź, czy wartość potrącenia z wynagrodzenia powinna być uwzględniona "
"jako ujemna (-X)."

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid ""
"Check the <strong>Work Entries</strong> linked to your newly created "
"Contract."
msgstr ""
"Sprawdź <strong>wpisy prac</strong> powiązane z nowo utworzonym kontraktem."

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.default_child_support
msgid "Child Support"
msgstr "Alimenty na dzieci"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__children_ids
msgid "Children"
msgstr "Dzieci"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Choose a Payroll Localization"
msgstr "Wybierz lokalizację listy płac"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid "Click here to create a new <strong>Contract</strong>."
msgstr "Kliknij tutaj, aby utworzyć nową <strong>umowę</strong>."

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid ""
"Click here to generate a <strong>Batch</strong> for the displayed Employees."
msgstr ""
"Kliknij tutaj, aby wygenerować <strong>partię</strong> dla wyświetlonych "
"pracowników."

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid ""
"Click on <strong>Salary Information</strong> to access additional fields."
msgstr ""
"Kliknij <strong>Informacje o wynagrodzeniu</strong>, aby uzyskać dostęp do "
"dodatkowych pól."

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid "Click on Employees to pick one of your <strong>Employees</strong>."
msgstr ""
"Kliknij na Pracownicy, aby wybrać jednego ze swoich "
"<strong>pracowników</strong>."

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid ""
"Click on Payroll to manage your employee's <strong>Work Entries</strong>, "
"<strong>Contracts</strong> and <strong>Payslips</strong>."
msgstr ""
"Kliknij opcję Lista płac, aby zarządzać <strong>wpisami pracy</strong>, "
"<strong>umowami</strong> i <strong>paskami wypłaty</strong> pracowników."

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid "Click on the <strong>Payslip</strong>."
msgstr "Kliknij na <strong>pasek wypłaty</strong>."

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid "Click on the <strong>Work Entries</strong> menu."
msgstr "Kliknij menu <strong>Wpisy pracy</strong>."

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/components/dashboard/todo_list/todo_list.xml:0
msgid "Close"
msgstr "Zamknij"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__paid_date
msgid "Close Date"
msgstr "Data zamknięcia"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_type__round_days_type__half-up
msgid "Closest"
msgstr "Najbliżej"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__structure_code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__code
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
msgid "Code"
msgstr "Kod"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_view_kanban
msgid "Code:"
msgstr "Kod:"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_working_rate__color
msgid "Color"
msgstr "Kolor"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_res_company
msgid "Companies"
msgstr "Firmy"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_declaration_mixin__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_note__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_payment_report_wizard__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_mixin__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__company_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Company"
msgstr "Firma"

#. module: hr_payroll
#: model:hr.salary.rule.category,name:hr_payroll.COMP
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "Company Contribution"
msgstr "Składki firmowe"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__full_time_required_hours
msgid "Company Full Time"
msgstr "Pełny etat w firmie"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_attachment__state__close
msgid "Completed"
msgstr "Ukończona"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "Computation"
msgstr "Obliczenia"

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.action_hr_payroll_compute_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Compute Sheet"
msgstr "Oblicz listę"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__compute_date
msgid "Computed On"
msgstr "Obliczono na"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__condition_select
msgid "Condition Based on"
msgstr "Warunek oparty o"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__condition_other_input_id
msgid "Condition Other Input"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "Conditions"
msgstr "Warunki"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_res_config_settings
msgid "Config Settings"
msgstr "Ustawienia konfiguracji"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_index_form_view
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Confirm"
msgstr "Potwierdź"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid "Confirm the <strong>Payslip</strong>."
msgstr "Potwierdź <strong>pasek wypłaty</strong>."

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payslip_run__state__verify
msgid "Confirmed"
msgstr "Potwierdzone"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_report__state__conflict
msgid "Conflict"
msgstr "Konflikt"

#. module: hr_payroll
#: model:hr.payroll.dashboard.warning,name:hr_payroll.hr_payroll_dashboard_warning_work_entries_in_conflict
msgid "Conflicts"
msgstr "Konflikty"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__contract_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__contract_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__contract_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__contract_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__contract_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__contract_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_employee_mixin__contract_ids
msgid "Contract"
msgstr "Umowa"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__contract_domain_ids
msgid "Contract Domain"
msgstr "Domena umowy"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Contract Expiration Notice Period"
msgstr "Okres wypowiedzenia umowy"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__contract_names
msgid "Contract Names"
msgstr "Nazwy umów"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_structure_type_view_search
msgid "Contract Type"
msgstr "Typ umowy"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Contract Wage ("
msgstr "Wynagrodzenie według umowy ("

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_contract_history
msgid "Contract history"
msgstr "Historia zatrudnienia"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_index_form_view
msgid "Contract indexing"
msgstr "Indeksowanie umów"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_hr_contract_repository
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__contract_ids
#: model:ir.ui.menu,name:hr_payroll.hr_menu_all_contracts
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_employees_root
msgid "Contracts"
msgstr "Umowy"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_filter
msgid "Contribution Register"
msgstr "Rejestr składek"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_contribution_registers
#: model:ir.actions.report,name:hr_payroll.action_report_register
msgid "Contribution Registers"
msgstr "Rejestry składek"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_convanceallowance1
msgid "Conveyance Allowance"
msgstr "Dodatek transportowy"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_ca_gravie
msgid "Conveyance Allowance For Gravie"
msgstr "Dodatek transportowy dla Gravie"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__amount
msgid "Count"
msgstr "Liczba"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_dashboard_warning__country_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__country_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__country_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__country_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__country_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__country_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__country_id
msgid "Country"
msgstr "Kraj"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__country_code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__country_code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__country_code
msgid "Country Code"
msgstr "Kod kraju"

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.action_hr_payroll_confirm_payroll
msgid "Create Draft Entry"
msgstr "Utwórz wpis szkicu"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_form
msgid "Create Individual Attachments"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Create Payment Report"
msgstr "Utwórz raport płatności"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Create SEPA payment"
msgstr "Tworzenie płatności SEPA"

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.action_new_salary_attachment
msgid "Create Salary Attachment"
msgstr "Utwórz załącznik wynagrodzenia"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_mixin__create_uid
msgid "Create Uid"
msgstr "Utwórz Uid"

#. module: hr_payroll
#: model_terms:ir.actions.act_window,help:hr_payroll.action_hr_contract_repository
msgid "Create a new contract"
msgstr "Utwórz nową umowę"

#. module: hr_payroll
#: model_terms:ir.actions.act_window,help:hr_payroll.action_salary_rule_form
msgid "Create a new salary rule"
msgstr "Utwórz nową regułę wynagrodzenia"

#. module: hr_payroll
#: model_terms:ir.actions.act_window,help:hr_payroll.hr_rule_parameter_action
msgid "Create a new salary rule parameter"
msgstr "Utwórz nowy parametr reguły wynagrodzenia"

#. module: hr_payroll
#: model_terms:ir.actions.act_window,help:hr_payroll.action_view_hr_payroll_structure_list_form
msgid "Create a new salary structure"
msgstr "Utwórz nową strukturę wynagrodzenia"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/components/dashboard/todo_list/todo_list.xml:0
msgid "Create new todo note"
msgstr "Utwórz nową notatkę do zrobienia"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_dashboard_warning__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_working_rate__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_note__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_payment_report_wizard__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__create_uid
msgid "Created by"
msgstr "Utworzył(a)"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_dashboard_warning__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_working_rate__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_note__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_payment_report_wizard__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__create_date
msgid "Created on"
msgstr "Data utworzenia"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__credit_note
msgid "Credit Note"
msgstr "Nota Kredytowa"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Credit Notes"
msgstr "Noty Kredytowe"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__is_credit_time
msgid "Credit Time"
msgstr "Czas kredytowania"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract_history__time_credit
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry__is_credit_time
msgid "Credit time"
msgstr "Czas kredytowania"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__currency_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__currency_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__currency_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__currency_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__currency_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__currency_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__currency_id
msgid "Currency"
msgstr "Waluta"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_search
msgid "Current month"
msgstr "Bieżący miesiąc"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount__is_name_custom
msgid "Custom Name"
msgstr "Niestandardowa nazwa"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_contract__schedule_pay__daily
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__default_schedule_pay__daily
msgid "Daily"
msgstr "Codziennie"

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_dashboard_configuration
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_dashboard_root
msgid "Dashboard"
msgstr "Konsola"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Date"
msgstr "Data"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__date_start
msgid "Date From"
msgstr "Data początkowa"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__date_start
msgid "Date Start"
msgstr "Data rozpoczęcia"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__date_end
msgid "Date To"
msgstr "Data Końcowa"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__date_end
msgid "Date at which this assignment has been set as completed or cancelled."
msgstr ""
"Data, w której zadanie zostało ustawione jako zakończone lub anulowane."

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_type__round_days__full
msgid "Day"
msgstr "Dzień"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Day where the 'Year To Date' will be reset every year."
msgstr ""
"Dzień, w którym 'rok do tej pory' (Year To Date) będzie resetowany co roku."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_res_company__ytd_reset_day
#: model:ir.model.fields,help:hr_payroll.field_res_config_settings__ytd_reset_day
msgid ""
"Day where the YTD will be reset every year. If zero or negative, then the first day of the month will be selected instead.\n"
"        If greater than the last day of a month, then the last day of the month will be selected instead."
msgstr ""
"Dzień, w którym YTD (rok do tej pory) będzie resetowany co roku. Jeśli wartość będzie równa zero lub ujemna, zostanie wybrany pierwszy dzień miesiąca. \n"
"Jeśli będzie większa niż ostatni dzień miesiąca, zostanie wybrany ostatni dzień miesiąca."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__number_of_days
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Days"
msgstr "Dni"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__count_leave
msgid "Days of Paid Time Off"
msgstr "Dni płatnego czasu wolnego"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__count_unforeseen_absence
msgid "Days of Unforeseen Absence"
msgstr "Dni nieprzewidzianej nieobecności"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__count_leave_unpaid
msgid "Days of Unpaid Time Off"
msgstr "Dni bezpłatnego czasu wolnego"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_export_mixin__reference_month__12
#: model:ir.model.fields.selection,name:hr_payroll.selection__res_company__ytd_reset_month__12
msgid "December"
msgstr "Grudzień"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__res_id
msgid "Declaration Model Id"
msgstr "ID modelu deklaracji"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__res_model
msgid "Declaration Model Name"
msgstr "Nazwa modelu deklaracji"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_declaration_mixin__line_ids
msgid "Declarations"
msgstr "Deklaracje"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
#: model:hr.salary.rule,name:hr_payroll.default_deduction_salary_rule
#: model:hr.salary.rule.category,name:hr_payroll.DED
msgid "Deduction"
msgstr "Potrącenie"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__schedule_pay
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_type__default_schedule_pay
msgid "Default Scheduled Pay"
msgstr "Domyślne zaplanowane wynagrodzenie"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_type__wage_type
msgid "Default Wage Type"
msgstr "Domyślny typ wynagrodzenia"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_type__default_work_entry_type_id
msgid "Default Work Entry Type"
msgstr "Domyślny typ wpisu roboczego"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid "Define a <strong>Wage</strong>."
msgstr "Definicja <strong>wynagrodzenia</strong>."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_structure__schedule_pay
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_structure_type__default_schedule_pay
msgid "Defines the frequency of the wage payment."
msgstr "Określa częstotliwość wypłaty wynagrodzenia."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_edit_payslip_line__struct_id
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__struct_id
msgid ""
"Defines the rules that have to be applied to this payslip, according to the "
"contract chosen. If the contract is empty, this field isn't mandatory "
"anymore and all the valid rules of the structures of the employee's "
"contracts will be applied."
msgstr ""
"Określa reguły, które mają być stosowane do tego paska wypłaty, zgodnie z "
"wybraną umową. Jeśli umowa jest pusta, pole to nie jest już obowiązkowe i "
"zostaną zastosowane wszystkie obowiązujące zasady struktur umów pracownika."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__department_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__department_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__department_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__department_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__department_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_headcount_line_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Department"
msgstr "Dział"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_light_payslip
msgid "Department:"
msgstr "Dział:"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__description
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__note
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__description
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__description
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__note
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__note
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_rule_parameter_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "Description"
msgstr "Opis"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__disabled
msgid "Disabled"
msgstr "Wyłączone"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_edit_payslip_lines_form_wizard
msgid "Discard"
msgstr "Odrzuć"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_dashboard_warning__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_working_rate__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_note__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_payment_report_wizard__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__display_name
msgid "Display Name"
msgstr "Nazwa wyświetlana"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Display Payslip PDF File on a payslip form"
msgstr "Wyświetl plik PDF paska wypłaty na formularzu paska wypłaty"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_hr_work_entry_type_view_form_inherit
msgid "Display in Payslip"
msgstr "Wyświetlanie na pasku wypłaty"

#. module: hr_payroll
#: model:res.groups,name:hr_payroll.group_payslip_display
msgid "Display payslip PDF"
msgstr "Wyświetl pasek wypłaty PDF"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__attachment
msgid "Document"
msgstr "Dokument"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payslip__state__done
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payslip_run__state__close
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Done"
msgstr "Wykonano"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_filter
msgid "Done Payslip Batches"
msgstr "Wykonane listy płac"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Done Slip"
msgstr "Pasek wykonany"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Double Holiday Pay"
msgstr "Podwójne wynagrodzenie urlopowe"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_type__round_days_type__down
msgid "Down"
msgstr "W dół"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_employee_declaration__state__draft
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payslip__state__draft
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_report__state__draft
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_filter
msgid "Draft"
msgstr "Projekt"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_filter
msgid "Draft Payslip Batches"
msgstr "Projekty list płac"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Draft Slip"
msgstr "Projekt listy płac"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Earnings"
msgstr ""

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
#: model:ir.actions.server,name:hr_payroll.action_edit_payslip_lines
msgid "Edit Payslip Lines"
msgstr "Edycja pasków wypłaty"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__edit_payslip_lines_wizard_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__edit_payslip_lines_wizard_id
msgid "Edit Payslip Lines Wizard"
msgstr "Kreator edycji pasków wypłaty"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_edit_payslip_worked_days_line
msgid "Edit payslip line wizard worked days"
msgstr "Kreator linii edycji paska wypłaty przepracowane dni"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_edit_payslip_lines_wizard
msgid "Edit payslip lines wizard"
msgstr "Kreator edycji linii paska wypłaty"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_edit_payslip_line
msgid "Edit payslip lines wizard line"
msgstr "Edycja linii kreatora pasków wypłaty"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__edited
msgid "Edited"
msgstr "Edytowane"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_edit_payslip_lines_form_wizard
msgid "Edition of Payslip Lines in the Payslip"
msgstr "Edycja paska wypłaty Linie na pasku wypłaty"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_work_entry_export_mixin.py:0
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_mixin__eligible_employee_line_ids
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_export_employee_mixin_list_view
msgid "Eligible Employees"
msgstr "Uprawnieni pracownicy"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_mixin__eligible_employee_count
msgid "Eligible Employees Count"
msgstr "Liczba uprawnionych pracowników"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__email_cc
msgid "Email cc"
msgstr "Email DW"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_employee
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__employee_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__employee_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__employee_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__employee_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__employee_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__employee_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_employee_mixin__employee_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__employee_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Employee"
msgstr "Pracownik"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_search_inherit
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_hr_employee_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Employee Code"
msgstr "Kod pracownika"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_contract
msgid "Employee Contract"
msgstr "Umowa pracownika"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount__employee_count
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__employee_count
msgid "Employee Count"
msgstr "Liczba pracowników"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payroll_declaration_mixin.py:0
msgid "Employee Declarations"
msgstr "Deklaracje pracownika"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_employee_grade_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payroll_structure_list_view
msgid "Employee Function"
msgstr "Funkcja pracownika"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Employee Information"
msgstr "Informacje o pracowniku"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/components/dashboard/payslip_batch/payslip_batch.js:0
#: model:ir.actions.act_window,name:hr_payroll.action_view_hr_payslip_form
#: model:ir.actions.act_window,name:hr_payroll.action_view_hr_payslip_month_form
msgid "Employee Payslips"
msgstr "Paski wypłat"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Employee Trends"
msgstr "Trendy wśród pracowników"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__employee_type
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_headcount_line_search
msgid "Employee Type"
msgstr "Typ pracownika"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payment_report_wizard.py:0
msgid "Employee address"
msgstr "Adres pracownika"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payment_report_wizard.py:0
msgid "Employee name"
msgstr "Imię i nazwisko pracownika"

#. module: hr_payroll
#: model:hr.payroll.dashboard.warning,name:hr_payroll.hr_payroll_dashboard_warning_employee_with_different_company_on_contract
msgid "Employee whose contracts and company are differents"
msgstr "Pracownik, którego umowy i firma są różne"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_contract__hourly_wage
msgid "Employee's hourly gross wage."
msgstr "Godzinowe wynagrodzenie brutto pracownika."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_contract__resource_calendar_id
msgid ""
"Employee's working schedule.\n"
"        When left empty, the employee is considered to have a fully flexible schedule, allowing them to work without any time limit, anytime of the week.\n"
"        "
msgstr ""
"Harmonogram pracy pracownika.\n"
"      Jeśli pole jest puste, zakłada się, że pracownik ma w pełni elastyczne godziny pracy, pozwalające mu na pracę bez żadnego czasowego limitu, w dowolnym dniu tygodnia.\n"
"        "

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__employee_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__employee_ids
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_filter
msgid "Employees"
msgstr "Pracownicy"

#. module: hr_payroll
#: model:hr.payroll.dashboard.warning,name:hr_payroll.hr_payroll_dashboard_warning_employee_missing_from_open_batch
msgid "Employees (With Running Contracts) missing from open batches"
msgstr "Brak pracowników (z bieżącymi umowami) w otwartych partiach"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
msgid "Employees Selection"
msgstr "Selekcja pracowników"

#. module: hr_payroll
#: model:hr.payroll.dashboard.warning,name:hr_payroll.hr_payroll_dashboard_warning_employee_ambiguous_contract
msgid "Employees With Both New And Running Contracts"
msgstr "Pracownicy z nowymi i trwającymi umowami"

#. module: hr_payroll
#: model:hr.payroll.dashboard.warning,name:hr_payroll.hr_payroll_dashboard_warning_employees_multiple_payslips
msgid "Employees With Multiple Open Payslips of Same Type"
msgstr "Pracownicy z wieloma otwartymi paskami wypłaty tego samego typu"

#. module: hr_payroll
#: model:hr.payroll.dashboard.warning,name:hr_payroll.hr_payroll_dashboard_warning_employee_without_bank_account
msgid "Employees Without Bank account Number"
msgstr "Pracownicy bez numeru konta bankowego"

#. module: hr_payroll
#: model:hr.payroll.dashboard.warning,name:hr_payroll.hr_payroll_dashboard_warning_employee_without_identification
msgid "Employees Without Identification Number"
msgstr "Pracownicy bez numeru identyfikacyjnego"

#. module: hr_payroll
#: model:hr.payroll.dashboard.warning,name:hr_payroll.hr_payroll_dashboard_warning_employee_without_contract
msgid "Employees Without Running Contracts"
msgstr "Pracownicy bez obowiązujących umów"

#. module: hr_payroll
#: model:hr.payroll.dashboard.warning,name:hr_payroll.hr_payroll_dashboard_warning_nearly_expired_contracts
msgid "Employees with running contracts coming to an end"
msgstr "Pracownicy z bieżącymi umowami, które dobiegają końca"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Employer Cost"
msgstr "Koszt pracodawcy"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_structure__hide_basic_on_pdf
msgid ""
"Enable this option if you don't want to display the Basic Salary on the "
"printed pdf."
msgstr ""
"Włącz tę opcję, jeśli nie chcesz wyświetlać Podstawowej Płacy na "
"wydrukowanym pdf-ie."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__date_to
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__date_end
msgid "End Date"
msgstr "Data końcowa"

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_salary_attachment_check_dates
msgid "End date may not be before the starting date."
msgstr "Data końcowa nie może być wcześniejsza niż data początkowa."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__display_warning
msgid "Error"
msgstr "Błąd"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_salary_rule_category.py:0
msgid "Error! You cannot create recursive hierarchy of Salary Rule Category."
msgstr ""
"Błąd! Nie można utworzyć hierarchii rekurencyjnej kategorii reguł "
"wynagrodzeń."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__date_estimated_end
msgid "Estimated End Date"
msgstr "Przewidywana data zakończenia"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_line__partner_id
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__partner_id
msgid "Eventual third party involved in the salary payment of the employees."
msgstr "Ewentualny podmiot zewnętrzny związany z wynagrodzeniem pracownika."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_employee_mixin__export_id
msgid "Export"
msgstr "Eksport"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_run__payment_report
msgid "Export .csv file related to this batch"
msgstr "Eksportuj plik .csv powiązany z tą partią"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__payment_report
msgid "Export .csv file related to this payslip"
msgstr "Eksportuj plik .csv powiązany z tym paskiem wypłaty"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_export_mixin_form_view
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_export_mixin_list_view
msgid "Export Employee Work Entries"
msgstr "Eksportuj wpisy pracy pracownika"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_mixin__export_file
msgid "Export File"
msgstr "Eksportuj plik"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_mixin__export_filename
msgid "Export Filename"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_payment_report_wizard__export_format
msgid "Export Format"
msgstr "Eksportuj format"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Export Payslip"
msgstr "Eksport pasek wypłaty"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_export_mixin__reference_month__2
#: model:ir.model.fields.selection,name:hr_payroll.selection__res_company__ytd_reset_month__2
msgid "February"
msgstr "Luty"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid "First, we'll create a new <strong>Contract</strong>."
msgstr "Najpierw utworzymy nową <strong>umowę</strong>."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__amount_fix
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__amount_fix
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_rule__amount_select__fix
msgid "Fixed Amount"
msgstr "Kwota stała"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_contract__wage_type__monthly
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__wage_type__monthly
msgid "Fixed Wage"
msgstr "Stała pensja"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_follower_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__message_follower_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_follower_ids
msgid "Followers"
msgstr "Obserwatorzy"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_partner_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__message_partner_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_partner_ids
msgid "Followers (Partners)"
msgstr "Obserwatorzy (partnerzy)"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__activity_type_icon
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_run__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Ikona Font awesome np. fa-tasks"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_line__amount_percentage
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__amount_percentage
msgid "For example, enter 50.0 to apply a percentage of 50%"
msgstr "Na przykład, wprowadź 50.0 dla oprocentowania 50%"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_res_config_settings__module_l10n_fr_hr_payroll
msgid "French Payroll"
msgstr "Francuskie wynagrodzenie"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount__date_from
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__date_from
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__date_from
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__date_from
msgid "From"
msgstr "Od"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payslips_by_employees.py:0
msgid "From %(from_date)s to %(end_date)s"
msgstr "Od %(from_date)s do %(end_date)s"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "General"
msgstr "Ogólne"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_payment_report_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
msgid "Generate"
msgstr "Generuj"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_export_mixin_form_view
msgid "Generate Export File"
msgstr "Generuj plik do eksportu"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_employee_declaration_view_tree
msgid "Generate PDFs"
msgstr "Generuj PDF-y"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/hr_work_entries_gantt.xml:0
#: code:addons/hr_payroll/static/src/views/work_entry_calendar/work_entry_calendar.xml:0
#: model:ir.actions.act_window,name:hr_payroll.action_hr_payslip_by_employees
#: model:ir.actions.server,name:hr_payroll.action_hr_payslip_run_generate
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
msgid "Generate Payslips"
msgstr "Generuj paski wypłat"

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.action_generate_payslips_from_work_entries
msgid "Generate payslips"
msgstr "Generuj paski wypłaty"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payslip_employees
msgid "Generate payslips for all selected employees"
msgstr "Generuj odcinki wypłaty dla wybranych pracowników"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_employee_declaration__state__pdf_generated
msgid "Generated PDF"
msgstr "Wygenerowany PDF"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
msgid "Generated Payslips"
msgstr "Wygenerowane paski wypłaty"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_note_demo_content
msgid "Give insurance card to new registered employees"
msgstr "Przekazanie karty ubezpieczeniowej nowym zarejestrowanym pracownikom"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__gross_wage
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__gross_wage
msgid "Gross Wage"
msgstr "Wynagrodzenie brutto"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_employee_declaration_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_headcount_line_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payroll_structure_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_rule_filter
msgid "Group By"
msgstr "Grupuj wg"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_res_config_settings__group_payslip_display
msgid "Group Payslip Display"
msgstr "Wyświetlanie paska wypłaty grupy"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_payment_report_wizard
msgid "HR Payroll Payment Report Wizard"
msgstr "Kreator raportu płatności wynagrodzeń HR"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_work_entry
msgid "HR Work Entry"
msgstr "Wpis pracy HR"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_work_entry_type
msgid "HR Work Entry Type"
msgstr "Typ zapisu pracy"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_type__round_days__half
msgid "Half Day"
msgstr "Pół dnia"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__has_done_payslip
msgid "Has Done Payslip"
msgstr "Ma gotowy pasek wypłaty"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__has_message
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__has_message
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__has_message
msgid "Has Message"
msgstr "Ma wiadomość"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__has_negative_net_to_report
msgid "Has Negative Net To Report"
msgstr "Ma ujemną wartość netto do zaraportowania"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__has_refund_slip
msgid "Has Refund Slip"
msgstr "Posiada odcinek zwrotu pieniędzy"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__has_similar_attachment
msgid "Has Similar Attachment"
msgstr "Ma podobne przywiązanie"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__has_similar_attachment_warning
msgid "Has Similar Attachment Warning"
msgstr "Ma podobne ostrzeżenie o załączniku"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__has_total_amount
msgid "Has Total Amount"
msgstr "Ma łączną kwotę"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.hr_payroll_headcount_action
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_headcount_action
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_headcount_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_headcount_view_tree
msgid "Headcount"
msgstr "Liczba pracowników"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_headcount_line
msgid "Headcount Line"
msgstr "Linia liczby pracowników"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payroll_headcount.py:0
msgid "Headcount for %(company_name)s from %(date_from)s to %(date_to)s"
msgstr "Liczba pracowników w %(company_name)s od %(date_from)s do %(date_to)s"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payroll_headcount.py:0
msgid "Headcount for %(company_name)s on the %(date)s"
msgstr "Liczba pracowników w %(company_name)s dnia %(date)s"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_headcount_line_view_tree
msgid "Headcount's Employees"
msgstr ""

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payroll_headcount.py:0
msgid "Headcount's employees"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__hide_basic_on_pdf
msgid "Hide Basic On Pdf"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__hourly_wage
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_contract__wage_type__hourly
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__wage_type__hourly
msgid "Hourly Wage"
msgstr "Wynagrodzenie godzinowe"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Hours"
msgstr "godziny"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Hours / Week"
msgstr "Godzin / Tydzień"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__hours_per_week
#: model:ir.model.fields,field_description:hr_payroll.field_resource_calendar__hours_per_week
msgid "Hours per Week"
msgstr "Godziny tygodniowo"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_houserentallowance1
msgid "House Rent Allowance"
msgstr "Dodatek do czynszu za dom"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_dashboard_warning__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_working_rate__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_note__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_payment_report_wizard__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__id
msgid "ID"
msgstr "ID"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_exception_icon
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__activity_exception_icon
msgid "Icon"
msgstr "Ikona"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__activity_exception_icon
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_run__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikona wskazująca na wyjątek aktywności."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__message_needaction
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_run__message_needaction
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Jeśli zaznaczone, nowe wiadomości wymagają twojej uwagi."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__message_has_error
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__message_has_sms_error
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_run__message_has_error
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_run__message_has_sms_error
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__message_has_error
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""
"Jeśli zaznaczone, niektóre wiadomości napotkały błędy podczas doręczenia."

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
msgid ""
"If empty, the default salary structure from the Salary structure type of the"
" employee will be used"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_employee__is_non_resident
#: model:ir.model.fields,help:hr_payroll.field_res_users__is_non_resident
msgid "If recipient lives in a foreign country"
msgstr "Jeśli odbiorca mieszka za granicą"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_input_type__is_quantity
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__is_quantity
msgid ""
"If set, hide currency and consider the manual input as a quantity for every "
"rule computation using this input."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__active
msgid ""
"If the active field is set to false, it will allow you to hide the salary "
"rule without removing it."
msgstr ""
"Jeśli nie chcesz widzieć reguły płacowej na listach, ale nie chcesz jej "
"usuwać, to odznacz pole aktywne."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_employee__disabled
msgid "If the employee is declared disabled by law"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__image_1920
msgid "Image"
msgstr "Obraz"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__image_128
msgid "Image 128"
msgstr "Obraz 128"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_view_tree
msgid "Index Contracts"
msgstr "Indeks umów"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_hr_payroll_index
#: model:ir.actions.server,name:hr_payroll.action_index_contracts
msgid "Index contract(s)"
msgstr "Indeksy umów"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_index
msgid "Index contracts"
msgstr "Indeks umów"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_res_config_settings__module_l10n_in_hr_payroll
msgid "Indian Payroll"
msgstr "Indyjskie wynagrodzenie"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__credit_note
msgid "Indicates this payslip has a refund of another"
msgstr "Oznacza, ze ten pasek jest zwrotem z innego paska"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Input Data"
msgstr "Dane wejściowe"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__note
msgid "Internal Note"
msgstr "Notatka wewnętrzna"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__internet_invoice
msgid "Internet Subscription Invoice"
msgstr "Faktura za abonament internetowy"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_is_follower
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__message_is_follower
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_is_follower
msgid "Is Follower"
msgstr "Jest obserwatorem"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__is_fulltime
#: model:ir.model.fields,field_description:hr_payroll.field_resource_calendar__is_fulltime
msgid "Is Full Time"
msgstr "Jest w pełnym wymiarze godzin"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__is_paid
msgid "Is Paid"
msgstr "Jest płatny"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
msgid "Is Register"
msgstr "Jest rejestrem"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__is_regular
msgid "Is Regular"
msgstr "Jest zwyczajny"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__is_superuser
msgid "Is Superuser"
msgstr "Jest superużytkownikiem"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__is_wrong_duration
msgid "Is Wrong Duration"
msgstr "Błędny czas trwania"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__is_quantity
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__is_quantity
msgid "Is quantity?"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__quantity
msgid ""
"It is used in computation for percentage and fixed amount. E.g. a rule for "
"Meal Voucher having fixed amount of 1€ per worked day can have its quantity "
"defined in expression like worked_days['WORK100'].number_of_days."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_input__amount
msgid ""
"It is used in computation. E.g. a rule for salesmen having 1%% commission of"
" basic salary per product can defined in expression like: result = "
"inputs['SALEURO'].amount * contract.wage * 0.01."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_export_mixin__reference_month__1
#: model:ir.model.fields.selection,name:hr_payroll.selection__res_company__ytd_reset_month__1
msgid "January"
msgstr "Styczeń"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_headcount_line_search
msgid "Job"
msgstr "Stanowisko"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__job_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__job_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__job_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Job Position"
msgstr "Stanowisko pracy"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__job_id
msgid "Job Title"
msgstr "Stanowisko"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_light_payslip
msgid "Job Title:"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_export_mixin__reference_month__7
#: model:ir.model.fields.selection,name:hr_payroll.selection__res_company__ytd_reset_month__7
msgid "July"
msgstr "Lipiec"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_export_mixin__reference_month__6
#: model:ir.model.fields.selection,name:hr_payroll.selection__res_company__ytd_reset_month__6
msgid "June"
msgstr "Czerwiec"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_search
msgid "Last 365 Days Payslip"
msgstr "Pasek wypłaty z ostatnich 365 dni"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Last Departures"
msgstr "Ostatnie odloty"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
msgid "Last Month"
msgstr "Ostatni miesiąc"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
msgid "Last Quarter"
msgstr "Ostatni kwartał"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_dashboard_warning__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_working_rate__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_note__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_payment_report_wizard__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__write_uid
msgid "Last Updated by"
msgstr "Ostatnio aktualizowane przez"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_dashboard_warning__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_working_rate__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_note__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_payment_report_wizard__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__write_date
msgid "Last Updated on"
msgstr "Data ostatniej aktualizacji"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount__line_ids
msgid "Line"
msgstr "Pozycja"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_form
msgid "Line Name"
msgstr "Nazwa linii"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_declaration_mixin__lines_count
msgid "Lines Count"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule_category__parent_id
msgid ""
"Linking a salary category to its parent is used only for the reporting "
"purpose."
msgstr ""
"Łączenie kategorii wynagrodzenia do jego kategorii nadrzędnej jest stosowane"
" tylko do celów raportowych."

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_note_demo_content
msgid "Links:"
msgstr "Linki:"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__paid
msgid "Made Payment Order? "
msgstr "Dokonałeś zlecenia płatności?"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_main_attachment_id
msgid "Main Attachment"
msgstr "Główny załącznik"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_export_mixin__reference_month__3
#: model:ir.model.fields.selection,name:hr_payroll.selection__res_company__ytd_reset_month__3
msgid "March"
msgstr "Marzec"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_form
msgid "Mark as Completed"
msgstr "Oznacz jako ukończone"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Mark as paid"
msgstr "Oznacz jako opłacone"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__master_department_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_search
msgid "Master Department"
msgstr "Dział główny"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__condition_range_max
msgid "Maximum Range"
msgstr "Zakres maksymalny"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_export_mixin__reference_month__5
#: model:ir.model.fields.selection,name:hr_payroll.selection__res_company__ytd_reset_month__5
msgid "May"
msgstr "Maj"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_meal_voucher
msgid "Meal Voucher"
msgstr "Kupon na posiłek"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_has_error
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__message_has_error
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_has_error
msgid "Message Delivery error"
msgstr "Błąd doręczenia wiadomości"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__message_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_ids
msgid "Messages"
msgstr "Wiadomości"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__condition_range_min
msgid "Minimum Range"
msgstr "Zakres minimalny"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__mobile_invoice
msgid "Mobile Subscription Invoice"
msgstr "Faktura za abonament komórkowy"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_report_hr_payroll_contribution_register
msgid "Model for Printing hr.payslip.line grouped by register"
msgstr "Wzorzec wydruku hr.payslip.line pogrupowany według rejestru"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/components/dashboard/payroll_stats/payroll_stats.xml:0
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_contract__schedule_pay__monthly
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__default_schedule_pay__monthly
msgid "Monthly"
msgstr "Miesięcznie"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_tree
msgid "Monthly Amount"
msgstr "Kwota miesięczna"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__my_activity_date_deadline
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Ostateczny terminin moich aktywności"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_dashboard_warning__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_note__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__rule_parameter_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__name
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Name"
msgstr "Nazwa"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_structure__payslip_name
msgid ""
"Name to be set on a payslip. Example: 'End of the year bonus'. If not set, "
"the default value is 'Salary Slip'"
msgstr ""
"Nazwa do ustawienia na pasku wypłaty. Przykład: \"Premia na koniec roku\". "
"Jeśli nie ustawiono, wartością domyślną jest \"Odcinek wynagrodzenia\"."

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_light_payslip
msgid "Name:"
msgstr "Nazwa:"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__negative_net_to_report_amount
msgid "Negative Net To Report Amount"
msgstr "Ujemna kwota netto do zaraportowania"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__negative_net_to_report_display
msgid "Negative Net To Report Display"
msgstr "Ujemna wartość netto do wyświetlenia"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__negative_net_to_report_message
msgid "Negative Net To Report Message"
msgstr "Ujemna wartość netto do wiadomości"

#. module: hr_payroll
#: model:mail.activity.type,name:hr_payroll.mail_activity_data_hr_payslip_negative_net
msgid "Negative Net to Report"
msgstr "Ujemna wartość netto do zaraportowania"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__is_refund
msgid "Negative Value"
msgstr ""

#. module: hr_payroll
#: model:hr.salary.rule.category,name:hr_payroll.NET
msgid "Net"
msgstr "Netto"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_view_kanban
msgid "Net -"
msgstr "Netto -"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
#: model:hr.salary.rule,name:hr_payroll.default_net_salary
msgid "Net Salary"
msgstr "Wynagrodzenie netto"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__net_wage
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__net_wage
msgid "Net Wage"
msgstr "Wynagrodzenie netto"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/report/hr_contract_history.py:0
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payslip_run__state__draft
msgid "New"
msgstr "Nowe"

#. module: hr_payroll
#: model:hr.payroll.dashboard.warning,name:hr_payroll.hr_payroll_dashboard_warning_new_contracts
msgid "New Contracts"
msgstr "Nowe umowy"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "New Employees"
msgstr "Nowi pracownicy"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payslips_by_employees.py:0
msgid "New Payslip"
msgstr "Nowy pasek wypłaty"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_calendar_event_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Następna Czynność wydarzenia w kalendarzu"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_date_deadline
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Termin kolejnej czynności"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_summary
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__activity_summary
msgid "Next Activity Summary"
msgstr "Podsumowanie kolejnej czynności"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_type_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__activity_type_id
msgid "Next Activity Type"
msgstr "Typ następnej czynności"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__no_end_date
msgid "No End Date"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "No ID number on the employee !!!"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_type__round_days__no
msgid "No Rounding"
msgstr "Bez zaokrąglania"

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_employee_unique_registration_number
msgid "No duplication of registration numbers is allowed"
msgstr "Niedozwolone jest powielanie numerów rejestracyjnych"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__default_no_end_date
msgid "No end date by default"
msgstr ""

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_rule_parameter.py:0
msgid "No rule parameter with code \"%(code)s\" was found for %(date)s"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__is_non_resident
#: model:ir.model.fields,field_description:hr_payroll.field_res_users__is_non_resident
msgid "Non-resident"
msgstr "Nie-rezydent"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/res_company.py:0
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_note__note
msgid "Note"
msgstr "Notatka"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_rule_parameter_view_form
msgid ""
"Note a description of your parameter, when it's used, how is it computed, "
"what's the source, ..."
msgstr ""
"Zanotuj opis parametru, kiedy jest używany, jak jest obliczany, jakie jest "
"jego źródło, ..."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid ""
"Note: There are previous payslips with a negative amount for a total of %s "
"to report."
msgstr ""
"Uwaga: Istnieją poprzednie paski wypłat z ujemną kwotą %s do zaraportowania."

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_category_form
msgid "Notes"
msgstr "Notatki"

#. module: hr_payroll
#: model_terms:ir.actions.act_window,help:hr_payroll.action_contribution_registers
msgid "Nothing to show"
msgstr "Nic do pokazania"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_export_mixin__reference_month__11
#: model:ir.model.fields.selection,name:hr_payroll.selection__res_company__ytd_reset_month__11
msgid "November"
msgstr "Listopad"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_needaction_counter
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__message_needaction_counter
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_needaction_counter
msgid "Number of Actions"
msgstr "Liczba akcji"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__number_of_days
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__number_of_days
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__number_of_days
msgid "Number of Days"
msgstr "Liczba dni"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__number_of_hours
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__number_of_hours
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__number_of_hours
msgid "Number of Hours"
msgstr "Liczba godzin"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid ""
"Number of days prior to the contract end date that a contract expiration "
"warning is triggered."
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid ""
"Number of days prior to the work permit expiration date that a warning is "
"triggered."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_has_error_counter
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__message_has_error_counter
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_has_error_counter
msgid "Number of errors"
msgstr "Liczba błędów"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_contract__full_time_required_hours
msgid ""
"Number of hours to work on the company schedule to be considered as "
"fulltime."
msgstr ""
"Liczba godzin, które należy przepracować zgodnie z harmonogramem firmy, aby "
"zostać uznanym za pełnoetatowego."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__message_needaction_counter
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_run__message_needaction_counter
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Liczba wiadomości wymagających akcji"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__message_has_error_counter
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_run__message_has_error_counter
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Liczba wiadomości z błędami przy doręczeniu"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__occurrences
msgid "Number of times the salary attachment will appear on the payslip."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__occurrences
msgid "Occurrences"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_export_mixin__reference_month__10
#: model:ir.model.fields.selection,name:hr_payroll.selection__res_company__ytd_reset_month__10
msgid "October"
msgstr "Październik"

#. module: hr_payroll
#: model:res.groups,name:hr_payroll.group_hr_payroll_user
msgid "Officer: Manage all contracts"
msgstr "Urzędnik: Zarządzanie wszystkimi umowami"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_note_demo_content
msgid "On the"
msgstr "Na"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid ""
"On the first tab is the amount of worked time giving you a <strong>gross "
"amount</strong>."
msgstr ""
"W pierwszej zakładce znajduje się ilość przepracowanego czasu, co daje "
"<strong>kwotę brutto</strong>."

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid ""
"On the second tab is the computation of the rules linked to the Structure "
"resulting in a <strong>net amount</strong>."
msgstr ""
"Na drugiej karcie znajduje się obliczenie reguł powiązanych ze strukturą, co"
" daje <strong>kwotę netto</strong>."

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid ""
"On the smartbutton, you can find all the <strong>Payslips</strong> included "
"in the Batch."
msgstr ""
"Na przycisku smartbutton można znaleźć wszystkie <strong>paski "
"wypłaty</strong> zawarte w danej partii."

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.ir_actions_server_action_open_reporting
msgid "Open Payroll Reporting"
msgstr "Raportowanie otwartych list płac"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Other Info"
msgstr "Inne informacje"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Other Information"
msgstr "Inne informacje"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_rule__amount_select__input
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_rule__condition_select__input
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_employee_grade_form
msgid "Other Input"
msgstr "Inne wejście"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__input_line_type_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input___allowed_input_type_ids
msgid "Other Input Line"
msgstr "Inna linia wejściowa"

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payslip_entry_type_view
msgid "Other Input Types"
msgstr "Inne typy wejść"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Other Inputs"
msgstr "Inne parametry"

#. module: hr_payroll
#: model:hr.work.entry.type,name:hr_payroll.hr_work_entry_type_out_of_contract
msgid "Out of Contract"
msgstr "Poza umową"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_declaration_mixin__pdf_error
msgid "PDF Error Message"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__pdf_file
msgid "PDF File"
msgstr "Plik PDF"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payroll_employee_declaration.py:0
msgid "PDF generation started. It will be available shortly."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payslip__state__paid
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payslip_run__state__paid
msgid "Paid"
msgstr "Zapłacona"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__paid_amount
msgid "Paid Amount"
msgstr "Zapłacona kwota"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_report__work_type__2
msgid "Paid Time Off"
msgstr "Płatny urlop"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__parameter_value
msgid "Parameter Value"
msgstr "Wartość parametru"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__parent_id
msgid "Parent"
msgstr "Nadrzędny"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__time_credit
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_search_inherit
msgid "Part Time"
msgstr "Część etatu"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__time_credit_type_id
msgid "Part Time Work Entry Type"
msgstr "Praca w niepełnym wymiarze godzin"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
msgid "Part time"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__partner_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__partner_id
msgid "Partner"
msgstr "Kontrahent"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_light_payslip
msgid "Pay Period:"
msgstr ""

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payslip
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__slip_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__payslip_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__slip_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__payslip_id
msgid "Pay Slip"
msgstr "Pasek wypłaty"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__slip_id
msgid "PaySlip"
msgstr "Pasek wypłaty"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
msgid "PaySlip Lines by Contribution Register"
msgstr "Pozycje paska wypłaty wg Rejestru Składek"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
msgid "PaySlip Name"
msgstr "Nazwa paska"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__payment_report
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__payment_report
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_payment_report_view_form
msgid "Payment Report"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__payment_report_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__payment_report_date
msgid "Payment Report Date"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__payment_report_filename
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__payment_report_filename
msgid "Payment Report Filename"
msgstr ""

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.open_payroll_modules
#: model:ir.ui.menu,name:hr_payroll.menu_report_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_hr_employee_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Payroll"
msgstr "Wynagrodzenie"

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.action_reset_work_entries
msgid "Payroll - Technical: Reset Work Entries"
msgstr "Lista płac - Techniczne: Resetuj wpisy robocze"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.payroll_report_action
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_report_view_tree
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_graph
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_search
msgid "Payroll Analysis"
msgstr "Analiza listy płac"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_report
msgid "Payroll Analysis Report"
msgstr "Raport z analizy listy płac"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__code
msgid "Payroll Code"
msgstr "Kod wynagrodzenia"

#. module: hr_payroll
#: model:ir.actions.client,name:hr_payroll.hr_payroll_dashboard_open
msgid "Payroll Dashboard"
msgstr "Pulpit nawigacyjny listy płac"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_dashboard_warning
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_dashboard_warning_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_dashboard_warning_view_tree
msgid "Payroll Dashboard Warning"
msgstr ""

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_hr_payroll_dashboard_warning
msgid "Payroll Dashboard Warnings"
msgstr ""

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_declaration_mixin
msgid "Payroll Declaration Mixin"
msgstr "Mixin deklaracji płacowej"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_employee_declaration
msgid "Payroll Employee Declaration"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Payroll Entries"
msgstr "Zapisy wynagrodzenia"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_headcount
msgid "Payroll Headcount"
msgstr ""

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_note
msgid "Payroll Note"
msgstr "Nota dotycząca wynagrodzenia"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Payroll Rules"
msgstr "Zasady dotyczące wynagrodzenia"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Payroll SEPA"
msgstr "Lista płac SEPA"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payroll_structure_filter
msgid "Payroll Structures"
msgstr "Struktura wynagrodzenia"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Payroll rules that apply to your country"
msgstr "Zasady wynagradzania obowiązujące w danym kraju"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_note_demo_content
msgid "Payroll tips &amp; tricks:"
msgstr "Wskazówki &amp; triki dotyczące wynagrodzenia:"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_res_config_settings__module_hr_payroll_account
msgid "Payroll with Accounting"
msgstr "Lista płac z księgowością"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_res_config_settings__module_hr_payroll_account_iso20022
msgid "Payroll with SEPA payment"
msgstr "Lista płac z płatnością SEPA"

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.ir_cron_generate_payslip_pdfs_ir_actions_server
msgid "Payroll: Generate pdfs"
msgstr "Lista płac: Generowanie plików pdf"

#. module: hr_payroll
#: model:mail.template,name:hr_payroll.mail_template_new_payslip
msgid "Payroll: New Payslip"
msgstr "Lista płac: Nowy pasek wypłaty"

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.ir_cron_update_payroll_data_ir_actions_server
msgid "Payroll: Update data"
msgstr "Lista płac: Aktualizacja danych"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
#: model:ir.actions.act_window,name:hr_payroll.action_hr_payslip_new
#: model:ir.actions.report,name:hr_payroll.action_report_payslip
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__payslip_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_payment_report_wizard__payslip_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__payslip_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Payslip"
msgstr "Pasek wypłaty"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Payslip 'Date From' must be earlier than 'Date To'."
msgstr "Pasek wypłaty \"Data od\" musi być wcześniejsza niż \"Data do\"."

#. module: hr_payroll
#: model:ir.actions.report,name:hr_payroll.action_report_light_payslip
msgid "Payslip (Light)"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__monthly_amount
msgid "Payslip Amount"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_filter
msgid "Payslip Batch"
msgstr "Partia paska wypłaty"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payslip_run
msgid "Payslip Batches"
msgstr "Listy płac"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__payslip_count
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__payslip_count
msgid "Payslip Count"
msgstr "Liczba pasków wypłaty"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
msgid "Payslip Generation"
msgstr ""

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payslip_input
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_input_type_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_input_type_view_tree
msgid "Payslip Input"
msgstr "Wejścia Paska wypłaty"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_input_type_view_tree
msgid "Payslip Input Name"
msgstr "Pasek wypłaty Nazwa wejścia"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payslip_input_type
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_input_type_view_search
msgid "Payslip Input Type"
msgstr "Typ wejścia paska wypłaty"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__input_line_ids
msgid "Payslip Inputs"
msgstr "Dane do paska wynagrodzenia"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_hr_employee_view_form
msgid "Payslip Language"
msgstr ""

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payslip_line
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_form
msgid "Payslip Line"
msgstr "Pozycja paska wypłaty"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.act_contribution_reg_payslip_lines
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__line_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__line_ids
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_filter
msgid "Payslip Lines"
msgstr "Pozycje paska wypłaty"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__payslip_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__name
msgid "Payslip Name"
msgstr "Nazwa paska"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_view_hr_payslip_input_type
msgid "Payslip Other Input Types"
msgstr "Pasek wypłaty Inne typy danych wejściowych"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Payslip PDF Display"
msgstr "Wyświetlanie paska wypłaty PDF"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payment_report_wizard.py:0
msgid "Payslip Period"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_payment_report_wizard__payslip_run_id
msgid "Payslip Run"
msgstr ""

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payslip_worked_days
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__worked_days_line_ids
msgid "Payslip Worked Days"
msgstr "Dni robocze paska"

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_salary_attachment_check_monthly_amount
msgid "Payslip amount must be strictly positive."
msgstr ""

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_salary_attachment.py:0
#: model:ir.actions.act_window,name:hr_payroll.act_hr_employee_payslip_list
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__slip_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__slip_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__payslip_ids
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_payslips
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_history_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_pivot
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_tree
msgid "Payslips"
msgstr "Paski płac"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_hr_payslip_run_tree
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_tree
msgid "Payslips Batches"
msgstr "Generowanie Listy płac"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_tree
msgid "Payslips Count"
msgstr "Liczba pasków wypłaty"

#. module: hr_payroll
#: model:hr.payroll.dashboard.warning,name:hr_payroll.hr_payroll_dashboard_warning_payslips_previous_contract
msgid "Payslips Generated On Previous Contract"
msgstr "Paski wypłaty wygenerowane w ramach poprzedniej umowy"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.hr_payslip_action_view_to_pay
msgid "Payslips To Pay"
msgstr "Paski wypłaty do zapłaty"

#. module: hr_payroll
#: model:hr.payroll.dashboard.warning,name:hr_payroll.hr_payroll_dashboard_warning_payslips_negative_net
msgid "Payslips With Negative NET"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
msgid "Payslips by Employees"
msgstr "Listy płac wg pracowników"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__pdf_filename
msgid "Pdf Filename"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__pdf_to_generate
msgid "Pdf To Generate"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__percentage
msgid "Percentage"
msgstr "Procentowo"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__amount_percentage
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__amount_percentage
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_rule__amount_select__percentage
msgid "Percentage (%)"
msgstr "Procentowo (%)"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__amount_percentage_base
msgid "Percentage based on"
msgstr "Procentowo bazując na"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Period"
msgstr "Okres"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_mixin__period_start
msgid "Period Start"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_mixin__period_stop
msgid "Period Stop"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_report__work_entry_source__planning
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_report__work_entry_source__planning
msgid "Planning"
msgstr "Planowanie"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payroll_employee_declaration.py:0
msgid "Please select the declarations for which you want to generate a PDF."
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_headcount_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_export_mixin_form_view
msgid "Populate"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Post payroll slips in accounting"
msgstr "Księgowanie odcinków list płac w księgowości"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Previous Negative Payslip to Report"
msgstr "Poprzedni Ujemny pasek wypłaty do zgłoszenia"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_tree
msgid "Print"
msgstr "Drukuj"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_professionaltax1
msgid "Professional Tax"
msgstr "Podatek zawodowy"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Prorated end-of-year bonus"
msgstr "Proporcjonalna premia na koniec roku"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_providentfund1
msgid "Provident Fund"
msgstr "Fundusz emerytalny"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_dashboard_warning__evaluation_code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__amount_python_compute
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_rule__amount_select__code
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_rule_filter
msgid "Python Code"
msgstr "Kod Python"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__condition_python
msgid "Python Condition"
msgstr "Warunek Python"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_rule__condition_select__python
msgid "Python Expression"
msgstr "Wyrażenie Python"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_rule_parameter_value__parameter_value
msgid "Python data structure"
msgstr "Struktura danych Pythona"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__quantity
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__quantity
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__quantity
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Quantity"
msgstr "Ilość"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
msgid "Quantity/Rate"
msgstr "Ilość/Przelicznik"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Quarter %(quarter)s of %(year)s"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_contract__schedule_pay__quarterly
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__default_schedule_pay__quarterly
msgid "Quarterly"
msgstr "Kwartalnie"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__queued_for_pdf
msgid "Queued For Pdf"
msgstr "W kolejce do pliku Pdf"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_employee_declaration__state__pdf_to_generate
msgid "Queued PDF generation"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_rule__condition_select__range
msgid "Range"
msgstr "Zakres"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__condition_range
msgid "Range Based on"
msgstr "Zakres bazujący na"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_working_rate__rate
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Rate"
msgstr "Kurs"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__rate
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__rate
msgid "Rate (%)"
msgstr "Przelicznik (%)"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__rating_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__rating_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__rating_ids
msgid "Ratings"
msgstr "Oceny"

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.action_hr_payroll_recompute_whole_sheet
msgid "Recompute Whole Sheet"
msgstr "Przelicz cały arkusz"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Recompute the payslip lines only, not the worked days / input lines"
msgstr ""
"Ponownie oblicz tylko linie paska wypłaty, a nie przepracowane dni / linie "
"wejściowe"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_salary_attachment.py:0
msgid "Recorded a new payment of %s."
msgstr "Zarejestrowano nową płatność w wysokości %s."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__number
msgid "Reference"
msgstr "Odnośnik"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_mixin__reference_month
msgid "Reference Month"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_mixin__reference_year
msgid "Reference Year"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Refund"
msgstr "Korekta"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Refund: %(payslip)s"
msgstr "Zwrot: %(payslip)s"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_search
msgid "Refunds"
msgstr "Korekty"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__registration_number
msgid "Registration Number of the Employee"
msgstr "Numer rejestracyjny pracownika"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_type__default_struct_id
msgid "Regular Pay Structure"
msgstr "Regularna struktura wynagrodzeń"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_report__work_type__1
msgid "Regular Working Day"
msgstr "Zwykły dzień roboczy"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
#: model:hr.salary.rule,name:hr_payroll.default_reimbursement_salary_rule
msgid "Reimbursement"
msgstr "Zwrot kosztów"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_tree
msgid "Related Payslips"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__remaining_amount
msgid "Remaining Amount"
msgstr "Pozostała kwota"

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_salary_attachment_check_remaining_amount
msgid "Remaining amount must be positive."
msgstr "Pozostała kwota musi być dodatnia."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__remaining_amount
msgid "Remaining amount to be paid."
msgstr "Pozostała kwota do zapłaty."

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid "Remove \"Conflicting\" filter"
msgstr ""

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payment_report_wizard.py:0
msgid "Report Date"
msgstr ""

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_report
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_hr_work_entry_type_view_form_inherit_contract
msgid "Reporting"
msgstr "Raportowanie"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_work_entry_export_mixin.py:0
msgid "Resolve Conflicts"
msgstr ""

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_resource_calendar
msgid "Resource Working Time"
msgstr "Czas pracy w zasobach"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_user_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__activity_user_id
msgid "Responsible User"
msgstr "Użytkownik odpowiedzialny"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_type__round_days_type
msgid "Round Type"
msgstr "Typ zaokrąglenia"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_type__round_days
msgid "Rounding"
msgstr "Zaokrąglenie"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__salary_rule_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__salary_rule_id
msgid "Rule"
msgstr "Reguła"

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_hr_salary_rule_category
msgid "Rule Categories"
msgstr "Kategorie reguł"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "Rule Name"
msgstr "Nazwa reguły"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__rule_parameter_id
msgid "Rule Parameter"
msgstr "Parametr reguł"

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_action_hr_salary_rule_parameter
msgid "Rule Parameters"
msgstr "Parametry reguł"

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_action_hr_salary_rule_form
msgid "Rules"
msgstr "Reguły"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_attachment__state__open
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_form
msgid "Running"
msgstr "Uruchomione"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__sim_card
msgid "SIM Card Copy"
msgstr "Kopia karty SIM"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_has_sms_error
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__message_has_sms_error
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Błąd dostarczenia wiadomości SMS"

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_hr_salary_configuration
msgid "Salary"
msgstr "Wynagrodzenie"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_contract.py:0
#: code:addons/hr_payroll/report/hr_contract_history.py:0
#: model:ir.actions.act_window,name:hr_payroll.action_hr_salary_attachment_new
#: model:ir.actions.act_window,name:hr_payroll.hr_salary_attachment_action
#: model:ir.actions.act_window,name:hr_payroll.hr_salary_attachment_action_view_employee
#: model:ir.model,name:hr_payroll.model_hr_salary_attachment
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_form
msgid "Salary Attachment"
msgstr "Załącznik do wynagrodzenia"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__salary_attachments_count
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract_history__salary_attachment_count
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__salary_attachment_count
msgid "Salary Attachment Count"
msgstr "Wynagrodzenie Liczba załączników"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__salary_attachment_count
msgid "Salary Attachment count"
msgstr "Wynagrodzenie Liczba załączników"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
#: code:addons/hr_payroll/models/hr_salary_attachment.py:0
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__salary_attachment_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__salary_attachment_ids
#: model:ir.ui.menu,name:hr_payroll.hr_menu_salary_attachments
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_history_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_tree
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Salary Attachments"
msgstr "Załączniki płacowe"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_category_form
msgid "Salary Categories"
msgstr "Kategorie wynagrodzeń"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Salary Computation"
msgstr "Obliczanie wynagrodzenia"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_salary_rule
msgid "Salary Rule"
msgstr "Reguła wynagrodzenia"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_hr_salary_rule_category
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_category_tree
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_salary_rule_category_filter
msgid "Salary Rule Categories"
msgstr "Kategorie Reguł wynagrodzenia"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_salary_rule_category
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_filter
msgid "Salary Rule Category"
msgstr "Kategoria reguły wynagrodzenia"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_rule_parameter
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_rule_parameter_view_form
msgid "Salary Rule Parameter"
msgstr "Parametr reguły wynagrodzeń"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_rule_parameter_value
msgid "Salary Rule Parameter Value"
msgstr "Wartość parametru reguły wynagrodzeń"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.hr_rule_parameter_action
msgid "Salary Rule Parameters"
msgstr "Parametry reguły wynagrodzeń"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_salary_rule_form
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__rule_ids
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_list
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_employee_grade_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_rule_filter
msgid "Salary Rules"
msgstr "Reguły wynagrodzenia"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Salary Slip"
msgstr "Odcinek wypłaty"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_structure
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__structure_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__struct_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_edit_payslip_lines_form_wizard
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_tree
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_tree_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_rule_filter
msgid "Salary Structure"
msgstr "Struktura wynagrodzenia"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_structure_type
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__structure_type_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__structure_type_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_employee_tree_inherit
msgid "Salary Structure Type"
msgstr "Rodzaj struktury wynagrodzeń"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_view_hr_payroll_structure_from_type
#: model:ir.actions.act_window,name:hr_payroll.action_view_hr_payroll_structure_list_form
msgid "Salary Structures"
msgstr "Struktury wynagrodzeń"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__schedule_pay
msgid "Schedule Pay"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_employee_declaration_view_search
msgid "Search Declarations"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_headcount_line_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_headcount_view_search
msgid "Search Headcount"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_dashboard_warning_view_search
msgid "Search Payroll Dashboard Warning"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_filter
msgid "Search Payslip Batches"
msgstr "Szukaj list płac"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_filter
msgid "Search Payslip Lines"
msgstr "Szukaj pozycji pasków"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Search Payslips"
msgstr "Szukaj pasków"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_search
msgid "Search Salary Attachment"
msgstr "Wyszukiwanie załącznika wynagrodzenia"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_rule_filter
msgid "Search Salary Rule"
msgstr "Szukaj według reguł wynagrodzenia"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_structure_type_view_search
msgid "Search Structure Type"
msgstr "Typ struktury wyszukiwania"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_contract__schedule_pay__semi-annually
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__default_schedule_pay__semi-annually
msgid "Semi-annually"
msgstr "Półrocznie"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_contract__schedule_pay__semi-monthly
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__default_schedule_pay__semi-monthly
msgid "Semi-monthly"
msgstr ""

#. module: hr_payroll
#: model:mail.template,description:hr_payroll.mail_template_new_payslip
msgid "Sent to employee to notify them about their new payslip"
msgstr "Wysłane do pracownika w celu powiadomienia go o nowym pasku wypłaty"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_export_mixin__reference_month__9
#: model:ir.model.fields.selection,name:hr_payroll.selection__res_company__ytd_reset_month__9
msgid "September"
msgstr "Wrzesień"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_dashboard_warning__sequence
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__sequence
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__sequence
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_type__sequence
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__sequence
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__sequence
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__sequence
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__sequence
msgid "Sequence"
msgstr "Sekwencja"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
msgid ""
"Set a specific department if you wish to select all the employees from this "
"department (and subdepartments) at once."
msgstr ""
"Ustaw konkretny dział, jeśli chcesz wybrać wszystkich pracowników z tego "
"działu (i poddziałów) jednocześnie."

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
msgid ""
"Set a specific job if you wish to select all the employees from this job at "
"once."
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
msgid ""
"Set a specific structure type if you wish to select all the employees from "
"this structure type at once."
msgstr ""

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid "Set the Contract as <strong><q>Running</q></strong>."
msgstr "Ustaw umowę jako <strong><q>działającą</q></strong>."

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Set to Draft"
msgstr "Ustaw jako projekt"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_hr_payroll_configuration
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_global_settings
msgid "Settings"
msgstr "Ustawienia"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Slips to Confirm"
msgstr "Odcinki do potwierdzenia"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payment_report_wizard.py:0
msgid "Some employees (%s) don't have a bank account."
msgstr "Niektórzy pracownicy (%s) nie mają konta bankowego."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_work_entry_export_mixin.py:0
msgid ""
"Some work entries are in conflict. Please resolve the conflicts before "
"exporting."
msgstr ""

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payslips_by_employees.py:0
msgid "Some work entries could not be validated."
msgstr "Niektórych wpisów roboczych nie można było zweryfikować."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__standard_calendar_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract_history__standard_calendar_id
msgid "Standard Calendar"
msgstr "Standardowy kalendarz"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__date_from
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__date_start
msgid "Start Date"
msgstr "Data początkowa"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__state
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__state
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_employee_declaration_view_search
msgid "State"
msgstr "Stan"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__state
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__state
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__state
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Status"
msgstr "Status"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__activity_state
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_run__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status na podstawie czynności\n"
"Zaległe: Termin już minął\n"
"Dzisiaj: Data czynności przypada na dzisiaj\n"
"Zaplanowane: Przyszłe czynności."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__struct_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__struct_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Structure"
msgstr "Struktura"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_employee_grade_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payroll_structure_filter
msgid "Structure Name"
msgstr "Nazwa struktury"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_type__name
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_structure_type_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_structure_type_view_tree
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payroll_structure_filter
msgid "Structure Type"
msgstr "Typ struktury"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_type__struct_type_count
msgid "Structure Type Count"
msgstr "Typ struktury Liczba"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_hr_payroll_structure_type
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_structure_type
msgid "Structure Types"
msgstr "Typy struktur"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_type__struct_ids
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_structure_view
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_structure_type_view_form
msgid "Structures"
msgstr "Struktury"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__sum_worked_hours
msgid "Sum Worked Hours"
msgstr "Suma przepracowanych godzin"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_sum_alw_category
msgid "Sum of Allowance category"
msgstr "Suma kategorii uprawnień"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_tree
msgid "Sum of Days"
msgstr "Suma dni"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_dashboard_warning__color
msgid "Tag color. No color means black."
msgstr ""

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
#: model:hr.salary.rule,name:hr_payroll.default_gross_salary_rule
#: model:hr.salary.rule.category,name:hr_payroll.GROSS
msgid "Taxable Salary"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__report_id
msgid "Template"
msgstr "Szablon"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__country_code
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_input_type__country_code
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_run__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"Kod kraju ISO w dwóch znakach. \n"
"Możesz użyć tego pola do szybkiego wyszukiwania."

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
msgid ""
"The Standard Calendar is the calendar used by the people working at a 100% "
"rate. It's used here to compute your part-time percentage."
msgstr ""
"Kalendarz standardowy to kalendarz używany przez osoby pracujące na 100% "
"etatu. Jest on używany do obliczania procentowego udziału osób pracujących w"
" niepełnym wymiarze godzin."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_work_entry_type__is_unforeseen
msgid ""
"The Work Entry checked as Unforeseen Absence will be counted in absenteeism "
"at work report."
msgstr ""
"Wpis zaznaczony jako Nieprzewidziana nieobecność zostanie uwzględniony w "
"raporcie nieobecności w pracy."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/res_company.py:0
msgid ""
"The YTD reset day must be a valid day of the month : since the current month"
" is %(month)s, it should be between 1 and %(day)s."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_edit_payslip_line__code
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_line__code
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__code
msgid ""
"The code of salary rules can be used as reference in computation of other "
"rules. In that case, it is case sensitive."
msgstr ""
"Kod reguł wynagrodzeń może być użyty jako odniesienie przy obliczaniu innych"
" reguł. W takim przypadku rozróżniana jest wielkość liter."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_input__code
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_input_type__code
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_worked_days__work_entry_type_id
msgid "The code that can be used in the salary rules"
msgstr "Kod, który może być używany przy tworzeniu reguł płac"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_line__amount_select
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__amount_select
msgid "The computation method for the rule amount."
msgstr "Metoda obliczania dla wartości reguły."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_input__contract_id
msgid "The contract this input should be applied to"
msgstr "Umowa, do której należy zastosować te dane wejściowe"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_worked_days__contract_id
msgid "The contract this worked days should be applied to"
msgstr "Umowa, do której należy zastosować te przepracowane dni"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__paid_date
msgid "The date on which the payment is made to the employee."
msgstr ""

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid ""
"The duration of the payslip is not accurate according to the structure type."
msgstr ""
"Czas trwania paska wypłaty nie jest dokładny w zależności od typu struktury."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid ""
"The following employees have a contract outside of the payslip period:\n"
"%s"
msgstr ""
"Następujący pracownicy mają umowę poza okresem wypłaty:\n"
"%s"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid ""
"The following values are not valid:\n"
"%s"
msgstr ""
"Następujące wartości są nieprawidłowe:\n"
"%s"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__condition_range_max
msgid "The maximum amount, applied for this rule."
msgstr "Maksymalna wartość dla tej reguły"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__condition_range_min
msgid "The minimum amount, applied for this rule."
msgstr "Wartość minimalna dla tej reguły"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_light_payslip
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid ""
"The net amount will be recovered from the first positive remuneration "
"established after this."
msgstr ""
"Kwota netto zostanie odzyskana z pierwszego dodatniego wynagrodzenia "
"ustalonego po tym fakcie."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "The payslips should be in Draft or Waiting state."
msgstr "Paski wypłaty powinny być w stanie Draft lub Waiting."

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/views/add_payslips_hook.js:0
msgid "The payslips(s) are now added to the batch"
msgstr "Paski wypłaty są teraz dodawane do partii."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "The period selected does not match the contract validity period."
msgstr "Wybrany okres nie jest zgodny z okresem ważności umowy."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "The selected payslips should be linked to the same batch"
msgstr ""

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_payroll_headcount_date_range
msgid "The start date must be anterior to the end date."
msgstr "Data początkowa musi być przed datą końcową."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_contract__time_credit_type_id
msgid ""
"The work entry type used when generating work entries to fit full time "
"working schedule."
msgstr ""
"Typ wpisu pracy używany podczas generowania wpisów pracy w celu dopasowania "
"do harmonogramu pracy w pełnym wymiarze godzin."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_work_entry_type__unpaid_structure_ids
msgid "The work entry won’t grant any money to employee in payslip."
msgstr ""
"Wpis o pracy nie przyzna pracownikowi żadnych pieniędzy na pasku wypłaty."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payroll_declaration_mixin.py:0
msgid "There is no declaration to generate for the given period"
msgstr ""

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payment_report_wizard.py:0
msgid ""
"There is no valid payslip (done and net wage > 0) to generate the file."
msgstr ""

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payment_report_wizard.py:0
msgid "There should be at least one payslip to generate the file."
msgstr ""

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid ""
"There's no contract set on payslip %(payslip)s for %(employee)s. Check that "
"there is at least a contract set on the employee form."
msgstr ""

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "This action is forbidden on validated payslips."
msgstr "Działanie to jest zabronione na zatwierdzonych paskach wypłaty."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "This action is restricted to payroll managers only."
msgstr "Ta czynność jest dostępna tylko dla menedżerów ds. listy płac."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_rule_parameter__code
#: model:ir.model.fields,help:hr_payroll.field_hr_rule_parameter_value__code
msgid "This code is used in salary rules to refer to this parameter."
msgstr ""
"Ten kod jest używany w regułach wynagrodzeń, aby odnieść się do tego "
"parametru."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_input_type__struct_ids
msgid ""
"This input will be only available in those structure. If empty, it will be "
"available in all payslip."
msgstr ""
"To wejście będzie dostępne tylko w tej strukturze. Jeśli jest puste, będzie "
"dostępne we wszystkich paskach wypłaty."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_contract_history__time_credit
msgid "This is a credit time contract."
msgstr "Jest to umowa na czas kredytowania."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_work_entry__is_credit_time
msgid "This is a credit time work entry."
msgstr ""

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "This payslip can be erroneous :"
msgstr "Ten pasek wypłaty może być błędny :"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_edit_payslip_lines_wizard.py:0
msgid "This payslip has been manually edited by %s."
msgstr "Ten pasek wypłaty został ręcznie edytowany przez %s."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "This payslip is not validated. This is not a legal document."
msgstr "Ten pasek wypłaty nie jest zatwierdzony. Nie jest to dokument prawny."

#. module: hr_payroll
#: model_terms:ir.actions.act_window,help:hr_payroll.payroll_report_action
msgid "This report performs analysis on your payslip."
msgstr "Raport ten przeprowadza analizę paska wypłaty."

#. module: hr_payroll
#: model_terms:ir.actions.act_window,help:hr_payroll.hr_work_entry_report_action
msgid "This report performs analysis on your work entries."
msgstr "Ten raport przeprowadza analizę wpisów roboczych."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__condition_range
msgid ""
"This will be used to compute the % fields values; in general it is on basic,"
" but you can also use categories code fields in lowercase as a variable "
"names (hra, ma, lta, etc.) and the variable basic."
msgstr ""
"Będzie to używane do obliczania wartości pól %; ogólnie jest to podstawowe, "
"ale można również użyć pól kodu kategorii małymi literami jako nazw "
"zmiennych (hra, ma, lta itp.) i zmiennej podstawowej."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payslips_by_employees.py:0
msgid "Time intervals to look for:%s"
msgstr "Przedziały czasowe, których należy szukać: %s"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount__date_to
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__date_to
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__date_to
msgid "To"
msgstr "Do"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "To Compute"
msgstr "Do obliczenia"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "To Confirm"
msgstr "Do potwierdzenia"

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_employee_payslips_to_pay
msgid "To Pay"
msgstr "Do zapłacenia"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_light_payslip
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "To pay on"
msgstr "Do zapłaty w dniu"

#. module: hr_payroll
#: model_terms:ir.actions.act_window,help:hr_payroll.action_contribution_registers
msgid "To see something in this report, compute a payslip."
msgstr "Aby zobaczyć coś w tym raporcie, oblicz pasek wypłaty."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__total
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__total
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_resource_calendar_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Total"
msgstr "Suma"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__total_amount
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Total Amount"
msgstr "Łączna kwota"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Total Working Days"
msgstr "Suma dni roboczych"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Total Working Hours"
msgstr "Łączna liczba godzin pracy"

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_salary_attachment_check_total_amount
msgid ""
"Total amount must be strictly positive and greater than or equal to the "
"payslip amount."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__total_amount
msgid "Total amount to be paid."
msgstr "Całkowita kwota do zapłaty."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__sum_worked_hours
msgid "Total hours of attendance and time off (paid or not)"
msgstr "Łączna liczba godzin obecności i czasu wolnego (płatnego lub nie)"

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_rule_parameter__unique
msgid "Two rule parameters cannot have the same code."
msgstr "Dwa parametry reguły nie mogą mieć tego samego kodu."

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_rule_parameter_value__unique
msgid "Two rules with the same code cannot start the same day"
msgstr "Dwie reguły z tym samym kodem nie mogą rozpocząć się tego samego dnia"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__type
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__type_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__struct_type_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__input_type_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__work_entry_type_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__other_input_type_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_search
msgid "Type"
msgstr "Typ"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__activity_exception_decoration
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_run__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Typ wyjątku działania na rekordzie."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_type__is_unforeseen
msgid "Unforeseen Absence"
msgstr "Nieprzewidziana nieobecność"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Unknown State"
msgstr "Nieznany stan"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_hr_work_entry_type_view_form_inherit
msgid "Unpaid"
msgstr "Nieopłacone"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_report__work_type__3
msgid "Unpaid Time Off"
msgstr "Bezpłatny czas wolny"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__unpaid_work_entry_type_ids
msgid "Unpaid Work Entry Type"
msgstr "Praca nieodpłatna Typ wpisu"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_employee_grade_form
msgid "Unpaid Work Entry Types"
msgstr "Praca nieodpłatna Typy wpisu"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_type__unpaid_structure_ids
msgid "Unpaid in Structures Types"
msgstr "Nieopłacone w typach struktur"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payment_report_wizard.py:0
msgid ""
"Untrusted bank account for the following employees:\n"
"%s"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_type__round_days_type__up
msgid "Up"
msgstr "W górę"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__use_worked_day_lines
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__use_worked_day_lines
msgid "Use Worked Day Lines"
msgstr "Korzystanie z linii dni roboczych"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_line__sequence
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__sequence
msgid "Use to arrange calculation sequence"
msgstr "Stosuj do kolejności obliczeń"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_line__appears_on_payslip
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__appears_on_payslip
msgid "Used to display the salary rule on payslip."
msgstr "Stosowane do wyświetlenia reguły płacowej na pasku"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__appears_on_employee_cost_dashboard
msgid "Used to display the value in the employer cost dashboard."
msgstr "Służy do wyświetlania wartości w konsoli kosztów pracodawcy."

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_res_users
msgid "User"
msgstr "Użytkownik"

#. module: hr_payroll
#: model:res.groups,comment:hr_payroll.group_hr_payroll_user
msgid "User can manage all contracts, work entries and create payslips."
msgstr ""
"Użytkownik może zarządzać wszystkimi umowami, wpisami pracy i tworzyć paski "
"wypłat"

#. module: hr_payroll
#: model:res.groups,comment:hr_payroll.group_hr_payroll_manager
msgid "User have full access on the application."
msgstr "Użytkownik ma pełny dostęp do aplikacji."

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
msgid "Validate"
msgstr "Zatwierdź"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_edit_payslip_lines_form_wizard
msgid "Validate Edition"
msgstr "Walidacja edycji"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_report__state__validated
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_search
msgid "Validated"
msgstr "Zatwierdzone"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__parameter_version_ids
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_rule_parameter_view_form
msgid "Versions"
msgstr "Wersje"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__appears_on_employee_cost_dashboard
msgid "View on Employer Cost Dashboard"
msgstr "Widok na konsoli kosztów pracodawcy"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__appears_on_payroll_report
msgid "View on Payroll Reporting"
msgstr "Wyświetlanie raportów płacowych"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_view_kanban
msgid "Wage :"
msgstr "Płaca :"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__wage_on_payroll
msgid "Wage On Payroll"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__wage_type
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract_history__wage_type
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__wage_type
msgid "Wage Type"
msgstr "Rodzaj wynagrodzenia"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_index_wizard.py:0
msgid "Wage indexed by %(percentage).2f%% on %(date)s"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payslip__state__verify
msgid "Waiting"
msgstr "Oczekiwanie"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_dashboard_warning__color
msgid "Warning Color"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__warning_message
msgid "Warning Message"
msgstr "Wiadomość ostrzegawcza"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_salary_attachment.py:0
msgid "Warning, a similar attachment has been found."
msgstr "Ostrzeżenie, znaleziono podobny załącznik."

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/components/dashboard/action_box/action_box.xml:0
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_dashboard_warning
msgid "Warnings"
msgstr "Ostrzeżenia"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_work_entry.py:0
msgid ""
"Watch out for gaps in %(employee_name)s's calendar\n"
"\n"
"Please complete the missing work entries of %(employee_name)s:%(time_intervals_str)s \n"
"\n"
"Missing work entries are like the Bermuda Triangle for paychecks. Let's keep your colleague's earnings from vanishing into thin air!"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_work_entry_type__round_days_type
msgid "Way of rounding the work entry type."
msgstr "Sposób zaokrąglania typu wpisu roboczego."

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_note_demo_content
msgid "We have to improve our Payroll flow with the new Odoo process"
msgstr "Musimy usprawnić nasz przepływ płac dzięki nowemu procesowi Odoo"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__website_message_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__website_message_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__website_message_ids
msgid "Website Messages"
msgstr "Wiadomości"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__website_message_ids
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_run__website_message_ids
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__website_message_ids
msgid "Website communication history"
msgstr "Historia komunikacji"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Week %(week_number)s of %(year)s"
msgstr "Tydzień %(week_number)s z %(year)s"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_contract__schedule_pay__weekly
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__default_schedule_pay__weekly
msgid "Weekly"
msgstr "Tygodniowo"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Weeks %(week)s and %(week1)s of %(year)s"
msgstr "Tygodnie %(week)s i %(week1)s z%(year)s"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_work_entry_type__round_days
msgid ""
"When the work entry is displayed in the payslip, the value is rounded "
"accordingly."
msgstr ""
"Gdy wpis roboczy jest wyświetlany na pasku wypłaty, wartość jest odpowiednio"
" zaokrąglana."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_contract__calendar_changed
msgid "Whether the previous or next contract has a different schedule or not"
msgstr "Czy poprzednia lub następna umowa ma inny harmonogram, czy nie"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_index__description
msgid ""
"Will be used as the message specifying why the wage on the contract has been"
" modified"
msgstr ""
"Będzie używany jako komunikat określający, dlaczego wynagrodzenie w umowie "
"zostało zmodyfikowane"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__count_work
msgid "Work Days"
msgstr "Dni robocze"

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_work_entries_root
msgid "Work Entries"
msgstr "Zapisy pracy"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.hr_work_entry_report_action
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_tree
msgid "Work Entries Analysis"
msgstr "Analiza wpisów roboczych"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_work_entry_report
msgid "Work Entries Analysis Report"
msgstr "Raport z analizy wejść do pracy"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_work_entry_export_mixin.py:0
msgid "Work Entries Export"
msgstr ""

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid ""
"Work Entries are generated for each <strong>time period</strong> defined in "
"the Working Schedule of the Contract."
msgstr ""
"Wpisy Robocze są generowane dla każdego <strong>okresu </strong> "
"zdefiniowanego w Harmonogramie Roboczym Kontraktu."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_work_entry_export_mixin.py:0
msgid "Work Entries for %(employee)s"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_employee_mixin__work_entry_ids
msgid "Work Entry"
msgstr "Wpis pracy"

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_hr_work_entry_report
msgid "Work Entry Analysis"
msgstr "Analiza wejść do pracy"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_work_entry_export_employee_mixin
msgid "Work Entry Export Employee"
msgstr ""

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_work_entry_export_mixin
msgid "Work Entry Export Mixin"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__work_entry_source
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__work_entry_source
msgid "Work Entry Source"
msgstr "Źródło wpisu pracy"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__work_entry_type_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__work_entry_type_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_search
msgid "Work Entry Type"
msgstr "Typ zapisu pracy"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__count_work_hours
msgid "Work Hours"
msgstr "Godziny pracy"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Work Permit Expiration Notice Period"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_resource_calendar__work_time_rate
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_resource_calendar_view_form
msgid "Work Time Rate"
msgstr "Stawka za czas pracy"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid ""
"Work entries may not be generated for the period from %(start)s to %(end)s."
msgstr "Wpisy pracy nie mogą być generowane za okres od %(start)s do %(end)s."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_structure_type__default_work_entry_type_id
msgid "Work entry type for regular attendances."
msgstr "Typ wpisu roboczego dla regularnych obecności"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__work_time_rate
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract_history__work_time_rate
msgid "Work time rate"
msgstr "Stawka za czas pracy"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_resource_calendar__work_time_rate
msgid ""
"Work time rate versus full time working schedule, should be between 0 and "
"100 %."
msgstr ""
"Wskaźnik czasu pracy w stosunku do pełnego harmonogramu pracy powinien "
"wynosić od 0 do 100%."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_contract__work_time_rate
#: model:ir.model.fields,help:hr_payroll.field_hr_contract_history__work_time_rate
msgid "Work time rate versus full time working schedule."
msgstr "Stawka za czas pracy a harmonogram pracy w pełnym wymiarze godzin."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__work_code
msgid "Work type"
msgstr "Typ pracy"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__work_type
msgid "Work, (un)paid Time Off"
msgstr "Praca, (nie)płatny czas wolny"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Worked Day"
msgstr "Dzień roboczy"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_edit_payslip_lines_form_wizard
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Worked Days"
msgstr "Dni robocze"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Worked Days & Inputs"
msgstr "Dni robocze i dane do wprowadzenia"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__worked_days_line_ids
msgid "Worked Days Lines"
msgstr "Przepracowane dni Linie"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_structure__use_worked_day_lines
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__use_worked_day_lines
msgid "Worked days won't be computed/displayed in payslips."
msgstr "Przepracowane dni nie będą obliczane/wyświetlane na paskach wypłaty."

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_headcount_working_rate
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__working_rate_ids
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_headcount_line_search
msgid "Working Rate"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__resource_calendar_id
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_report__work_entry_source__calendar
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_report__work_entry_source__calendar
msgid "Working Schedule"
msgstr "Godziny pracy"

#. module: hr_payroll
#: model:hr.payroll.dashboard.warning,name:hr_payroll.hr_payroll_dashboard_warning_working_schedule_change
msgid "Working Schedule Changes"
msgstr "Zmiany w harmonogramie pracy"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_salary_rule.py:0
msgid "Wrong percentage base or quantity defined for:"
msgstr "Nieprawidłowa podstawa procentowa lub ilość zdefiniowana dla:"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_salary_rule.py:0
msgid "Wrong python code defined for:"
msgstr "Nieprawidłowy kod Pythona zdefiniowany dla:"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_salary_rule.py:0
msgid "Wrong python condition defined for:"
msgstr "Nieprawidłowy warunek Pythona zdefiniowany dla:"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_salary_rule.py:0
msgid "Wrong quantity defined for:"
msgstr "Nieprawidłowa ilość zdefiniowana dla:"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_salary_rule.py:0
msgid "Wrong range condition defined for:"
msgstr "Nieprawidłowy warunek zakresu zdefiniowany dla:"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_rule_parameter.py:0
msgid ""
"Wrong rule parameter value for %(rule_parameter_name)s at date %(date)s.\n"
"%(error)s"
msgstr ""

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid ""
"Wrong warning computation code defined for:\n"
"- Warning: %(warning)s\n"
"- Error: %(error)s"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__ytd
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__ytd
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__ytd
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__ytd
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "YTD"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "YTD Reset Date"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_res_company__ytd_reset_day
#: model:ir.model.fields,field_description:hr_payroll.field_res_config_settings__ytd_reset_day
msgid "YTD Reset Day of the month"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_res_company__ytd_reset_month
#: model:ir.model.fields,field_description:hr_payroll.field_res_config_settings__ytd_reset_month
msgid "YTD Reset Month"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_declaration_mixin__year
msgid "Year"
msgstr "Rok"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__ytd_computation
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__ytd_computation
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__ytd_computation
msgid "Year to Date Computation"
msgstr ""

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/res_users.py:0
msgid ""
"You are receiving this message because you are the HR Responsible of this "
"employee."
msgstr ""
"Otrzymujesz tę wiadomość, ponieważ jesteś osobą odpowiedzialną za sprawy "
"kadrowe tego pracownika."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip_run.py:0
msgid ""
"You can't delete a batch with payslips if they are not draft or cancelled."
msgstr ""

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "You can't validate a cancelled payslip."
msgstr "Nie można zatwierdzić anulowanego paska wypłaty."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip_input_type.py:0
msgid ""
"You cannot delete %s as it is used in another module but you can archive it "
"instead."
msgstr ""
"Nie można usunąć %s, ponieważ jest używany w innym module, ale można go "
"zarchiwizować."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "You cannot delete a payslip which is not draft or cancelled!"
msgstr ""
"Nie możesz usuwać paska, który nie jest w stanie Projekt lub Anulowane"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_salary_attachment.py:0
msgid "You cannot delete a running salary attachment!"
msgstr "Nie można usunąć działającego załącznika z wynagrodzeniem!"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_salary_attachment.py:0
msgid "You cannot record a payment on multi employees attachments."
msgstr ""

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip_run.py:0
msgid ""
"You cannot reset a batch to draft if some of the payslips have already been "
"paid."
msgstr ""
"Nie można zresetować partii do wersji roboczej, jeśli niektóre paski wypłaty"
" zostały już opłacone."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "You cannot validate a payslip on which the contract is cancelled"
msgstr ""
"Nie można zatwierdzić paska wypłaty, na którym umowa została anulowana"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_index_wizard.py:0
msgid ""
"You have selected non running contracts, if you really need to index them, "
"please do it by hand"
msgstr ""
"Wybrałeś niedziałające kontrakty, jeśli naprawdę musisz je zindeksować, zrób"
" to ręcznie"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_work_entry_export_mixin.py:0
msgid "You must be logged in a %(country_code)s company to use this feature"
msgstr ""

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payroll_declaration_mixin.py:0
msgid "You must be logged in a %s company to use this feature"
msgstr ""

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payslips_by_employees.py:0
msgid "You must select employee(s) to generate payslip(s)."
msgstr "Musisz wybrać pracowników do generacji pasków"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip_line.py:0
msgid "You must set a contract to create a payslip line."
msgstr "Aby utworzyć pasek wypłaty, należy ustawić umowę."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payroll_structure_type.py:0
msgid "You should also be logged into a company in %s to set this country."
msgstr ""
"Powinieneś być również zalogowany do firmy w %s, aby ustawić ten kraj."

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_export_employee_mixin_list_view
msgid "contracts"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_type__current_companies_country_codes
msgid "country codes"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
msgid "e.g. April 2021"
msgstr "np. kwiecień 2021 r."

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_input_type_view_form
msgid "e.g. Child Support"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_structure_type_view_form
msgid "e.g. Employee"
msgstr "np. pracownik"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_dashboard_warning_view_form
msgid "e.g. Employee Without Contracts"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "e.g. Net"
msgstr "np. Netto"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "e.g. Net Salary"
msgstr "np. Wynagrodzenie netto"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_employee_grade_form
msgid "e.g. Regular Pay"
msgstr "np. regularne wynagrodzenie"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__headcount_id
msgid "headcount_id"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_note_demo_content
msgid "https://www.odoo.com/fr_FR/slides/slide/manage-payroll-1002"
msgstr "https://www.odoo.com/fr_FR/slides/slide/manage-payroll-1002"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_light_payslip
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "of"
msgstr "z"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__amount_percentage_base
msgid "result will be affected to a variable"
msgstr "rezultat będzie zależny od zmiennej"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_light_payslip
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "xxxxxxxxxxxx"
msgstr ""

#. module: hr_payroll
#: model:mail.template,subject:hr_payroll.mail_template_new_payslip
msgid "{{ object.employee_id.name }}, a new payslip is available for you"
msgstr "{{ object.employee_id.name }}, dostępny jest nowy pasek wypłaty."
