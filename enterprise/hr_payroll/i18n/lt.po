# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_payroll
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# Nerijus, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON>. <<EMAIL>>, 2024
# <PERSON><PERSON> Grigoni<PERSON> <<EMAIL>>, 2024
# <PERSON>lvi<PERSON> <<EMAIL>>, 2024
# Šar<PERSON>nas Ažna <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# Martin Trigaux, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:52+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Martin Trigaux, 2024\n"
"Language-Team: Lithuanian (https://app.transifex.com/odoo/teams/41243/lt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lt\n"
"Plural-Forms: nplurals=4; plural=(n % 10 == 1 && (n % 100 > 19 || n % 100 < 11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < 11) ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip_worked_days.py:0
msgid " (Half-Day)"
msgstr " (Pusė dienos)"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__count
msgid "# Payslip"
msgstr "# Algalapis"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__payslips_count
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract_history__payslips_count
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__payslip_count
msgid "# Payslips"
msgstr "# Algalapiai"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payroll_declaration_mixin.py:0
msgid "%(employee_name)s-declaration-%(year)s"
msgstr ""

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_salary_rule.py:0
msgid ""
"%(error_type)s\n"
"- Employee: %(employee)s\n"
"- Contract: %(contract)s\n"
"- Payslip: %(payslip)s\n"
"- Salary rule: %(name)s (%(code)s)\n"
"- Error: %(error_message)s"
msgstr ""

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payroll_headcount.py:0
msgid "%(rate)s Hours/week"
msgstr ""

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "%(start_date_string)s and %(end_date_string)s"
msgstr ""

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_contract.py:0
#: code:addons/hr_payroll/models/hr_payroll_structure.py:0
#: code:addons/hr_payroll/models/hr_salary_rule.py:0
msgid "%s (copy)"
msgstr "%s (kopija)"

#. module: hr_payroll
#: model:ir.actions.report,print_report_name:hr_payroll.action_report_light_payslip
#: model:ir.actions.report,print_report_name:hr_payroll.action_report_payslip
msgid "'Payslip - %s' % (object.name)"
msgstr "'Algalapis - %s' % (object.name)"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "(%s Payslips)"
msgstr "(%s Algalapiai)"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__state
msgid ""
"* When the payslip is created the status is 'Draft'\n"
"                \n"
"* If the payslip is under verification, the status is 'Waiting'.\n"
"                \n"
"* If the payslip is confirmed then status is set to 'Done'.\n"
"                \n"
"* When the user cancels a payslip, the status is 'Canceled'."
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "-> Report"
msgstr "-> Ataskaita"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_view_kanban
msgid "/ Hour"
msgstr "/ Valandą"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_view_kanban
msgid "/ Month"
msgstr "/ mėnesį"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
msgid "/ day"
msgstr "/ dieną"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
msgid "/ half-month"
msgstr "/ pusė mėnesio"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
msgid "/ half-year"
msgstr "/ pusė metų"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
msgid "/ quarter"
msgstr "/ ketvirtis"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
msgid "/ two months"
msgstr "/ du mėnesiai"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
msgid "/ two weeks"
msgstr "/ dvi savaitės"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
msgid "/ week"
msgstr "/ savaitė"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
msgid "/ year"
msgstr "/ metai"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_note_demo_content
msgid "1. Save documents to terminated employees"
msgstr "1. Išsaugokite dokumentus atleistiems darbuotojams"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
msgid "100"
msgstr "100"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_13th_month_salary
msgid "13th pay salary"
msgstr "13-tas atlyginimas"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "1st semester of %s"
msgstr "1-as semestras iš %s"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_note_demo_content
msgid "2. Index salaries for Marketing department"
msgstr "2. Indeksuoti atlyginimus Rinkodaros skyriui"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
msgid "2023 Payroll"
msgstr "2023 metų atlyginimai"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "2nd semester of %s"
msgstr "2-as semestras iš %s"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_note_demo_content
msgid "3. Create a new contract for Marc Demo with his new position"
msgstr "3. Sukurti naują Marc Demo sutartį su jo naujomis pareigomis"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_rule_parameter_view_form
msgid ""
"<span class=\"bg-white opacity-75 w-100 h-100 o_salary_rule_overlay position-absolute top-0 end-0 w-100 h-100 text-center d-flex justify-content-center flex-column fs-3\">\n"
"                            Save your Salary Rule in order to add Parameter Values.\n"
"                        </span>"
msgstr ""
"<span class=\"bg-white opacity-75 w-100 h-100 o_salary_rule_overlay position-absolute top-0 end-0 w-100 h-100 text-center d-flex justify-content-center flex-column fs-3\">\n"
"                            Išsaugokite savo atlyginimo taisyklę, kad galėtumėte pridėti parametrų reikšmes.\n"
"                        </span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_resource_calendar_view_form
msgid "<span class=\"ms-2\"> hours/week</span>"
msgstr "<span class=\"ms-2\"> val./sav.</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                            Work Entries\n"
"                            </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                            Darbo įrašai\n"
"                            </span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
msgid ""
"<span class=\"o_stat_text\"> Payslips </span>\n"
"                            <span class=\"o_stat_value\"> New </span>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_export_mixin_form_view
msgid "<span class=\"o_stat_text\">Eligible Employees</span>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_headcount_view_form
msgid "<span class=\"o_stat_text\">Employees</span>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
msgid "<span class=\"o_stat_text\">Payslips</span>"
msgstr "<span class=\"o_stat_text\">Algalapiai</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
msgid ""
"<span class=\"o_stat_text\">Salary Attachments</span>\n"
"                            <span class=\"o_stat_value\">New</span>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
msgid ""
"<span nolabel=\"1\" colspan=\"2\">This wizard will generate payslips for all"
" selected employee(s) based on the dates and credit note specified on "
"Payslips Run.</span>"
msgstr ""
"<span nolabel=\"1\" colspan=\"2\">Šis vedlys sugeneruos algalapius visiems "
"pasirinktiems darbuotojams pagal datas ir algalapių veikime nurodytas "
"kreditines sąskaitas.</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_index_form_view
msgid ""
"<span role=\"status\" class=\"alert alert-warning d-block\" invisible=\"not "
"display_warning\">You have selected contracts that are not running, this "
"wizard can only index running contracts.</span>"
msgstr ""
"<span role=\"status\" class=\"alert alert-warning d-block\" invisible=\"not "
"display_warning\">Jūs pasirinkote sutartis, kurios nėra aktyvios, šis vedlys"
" gali indeksuoti tik veikiančias sutartis.</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_resource_calendar_view_form
msgid "<span> %</span>"
msgstr "<span> %</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
msgid "<span>/ hour</span>"
msgstr "<span>/ valandą</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_edit_payslip_lines_form_wizard
msgid ""
"<span><strong>Tip:</strong> Each time you edit the quantity or the amount on"
" a line, we recompute the following lines. We recommend that you edit from "
"top to bottom to prevent your edition from being overwritten by the "
"automatic recalculation. Be careful that reordering the lines doesn't "
"recompute them.</span>"
msgstr ""
"<span><strong>Patarimas:</strong> Kiekvieną kartą, kai redaguojate kiekį ar "
"sumą eilutėje, mes iš naujo apskaičiuojame šias eilutes. Rekomenduojame "
"redaguoti nuo viršaus į apačią, kad išvengtumėte jūsų redagavimo perrašymo "
"automatinio perskaičiavimo metu. Būkite atsargūs, nes eilutės pertvarkymas "
"jų neperkomplektuoja.</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "<span>Days</span>"
msgstr "<span>Dienos</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_form
msgid "<span>Payslips</span>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<span>Total</span>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_rule_parameter_view_form
msgid "<span>Versions</span>"
msgstr "<span>Versijos</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong class=\"me-2\">Address:</strong>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong class=\"me-2\">Children:</strong>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong class=\"me-2\">Computed On:</strong>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong class=\"me-2\">Contract Start Date:</strong>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong class=\"me-2\">Contract Type:</strong>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong class=\"me-2\">Department:</strong>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong class=\"me-2\">Email:</strong>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong class=\"me-2\">ID:</strong>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong class=\"me-2\">Job Position:</strong>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong class=\"me-2\">Marital Status:</strong>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong class=\"me-2\">Name:</strong>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong class=\"me-2\">Pay Period:</strong>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong class=\"me-2\">Working Schedule:</strong>"
msgstr ""

#. module: hr_payroll
#: model_terms:web_tour.tour,rainbow_man_message:hr_payroll.payroll_tours
msgid ""
"<strong>Congrats, Your first payslip is now finished. It's time for you to "
"explore the Payroll app by yourself.</strong>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
msgid "<strong>Register Name:</strong>"
msgstr "<strong>Registruoti vardą:</strong>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
msgid "<strong>Total</strong>"
msgstr "<strong>Suma</strong>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_light_payslip
msgid "<strong>YTD</strong>"
msgstr ""

#. module: hr_payroll
#: model:mail.template,body_html:hr_payroll.mail_template_new_payslip
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto;\"><tbody>\n"
"    <tr><td valign=\"top\" style=\"text-align: left; font-size: 14px;\">\n"
"        Dear <t t-esc=\"object.employee_id.name\"/>, a new payslip is available for you.<br/><br/>\n"
"        Please find the PDF in your employee portal.<br/><br/>\n"
"        Have a nice day,<br/>\n"
"        The HR Team\n"
"    </td></tr>\n"
"</tbody></table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto;\"><tbody>\n"
"    <tr><td valign=\"top\" style=\"text-align: left; font-size: 14px;\">\n"
"        Gerb. <t t-esc=\"object.employee_id.name\"/>, jums sugeneruotas naujas atlyginimo lapelis.<br/><br/>\n"
"        PDF failą rasite savo darbuotojų portale.<br/><br/>\n"
"        Geros dienos,<br/>\n"
"        HR komanda\n"
"    </td></tr>\n"
"</tbody></table>\n"
"           "

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_work_entry_type_is_unforeseen_is_leave
msgid "A unforeseen absence must be a leave."
msgstr "Nenumatytas nebuvimas darbe turi būti registruotas prie laisvadienių"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Accounting"
msgstr "Apskaita"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_needaction
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__message_needaction
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_needaction
msgid "Action Needed"
msgstr "Reikalingas veiksmas"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_dashboard_warning__active
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__active
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__active
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__active
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_search
msgid "Active"
msgstr "Aktyvus"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__active_amount
msgid "Active Amount"
msgstr "Aktyvi suma"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__activity_ids
msgid "Activities"
msgstr "Veiklos"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_exception_decoration
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Veiklos išimties žymėjimas"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_state
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__activity_state
msgid "Activity State"
msgstr "Veiklos būsena"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_type_icon
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__activity_type_icon
msgid "Activity Type Icon"
msgstr "Veiklos tipo ikona"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/views/add_payslips_hook.js:0
#: code:addons/hr_payroll/static/src/views/payslip_batch_form/payslip_batch_form.js:0
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
msgid "Add Payslips"
msgstr "Pridėti algalapius"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid "Add a <strong>name</strong> to the contract."
msgstr "Pridėti <strong>vardą</strong> į sutartį."

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid "Add a employee to your contract"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.actions.act_window,help:hr_payroll.action_view_hr_payroll_structure_from_type
msgid "Add a new salary structure"
msgstr "Pridėti naują atlyginimų struktūrą"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Add an internal note..."
msgstr "Pridėti vidinę pastabą..."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__ytd_computation
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_structure__ytd_computation
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__ytd_computation
msgid ""
"Adds a column in the payslip that shows the accumulated amount paid for "
"different rules during the year"
msgstr ""

#. module: hr_payroll
#: model:res.groups,name:hr_payroll.group_hr_payroll_manager
msgid "Administrator"
msgstr "Administratorius"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/components/dashboard/payslip_batch/payslip_batch.xml:0
msgid "All"
msgstr "Visi"

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_employee_payslips
msgid "All Payslips"
msgstr "Visi algalapiai"

#. module: hr_payroll
#: model:hr.salary.rule.category,name:hr_payroll.ALW
msgid "Allowance"
msgstr "Išlaidos"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_rule__condition_select__none
msgid "Always True"
msgstr "Visada teigiamas"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__amount
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__amount
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__amount
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__amount
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Amount"
msgstr "Suma"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__amount_other_input_id
msgid "Amount Other Input"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__amount_select
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__amount_select
msgid "Amount Type"
msgstr "Sumos tipas"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payment_report_wizard.py:0
msgid "Amount to pay"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__monthly_amount
msgid "Amount to pay each payslip."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__active_amount
msgid ""
"Amount to pay for this payslip, Payslip Amount or less depending on the "
"Remaining Amount."
msgstr ""

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_payroll_employee_declaration_unique_employee_sheet
msgid "An employee can only have one declaration per sheet."
msgstr "Darbuotojas gali turėti tik vieną deklaraciją viename lape."

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/components/dashboard/payroll_stats/payroll_stats.xml:0
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_contract__schedule_pay__annually
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__default_schedule_pay__annually
msgid "Annually"
msgstr "Kartą per metus"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid ""
"Another refund payslip with the same amount has been found. Do you want to "
"create a new one?"
msgstr ""
"Rasta kita grąžinimo algalapio kopija su ta pačia suma. Ar norite sukurti "
"naują?"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__appears_on_payslip
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__appears_on_payslip
msgid "Appears on Payslip"
msgstr "Rodomas algalapyje"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__condition_python
msgid ""
"Applied this rule for calculation if condition is true. You can specify "
"condition like basic > 1000."
msgstr ""
"Jei sąlyga teigiama, bus pritaikyta ši skaičiavimo taisyklė. Galite nurodyti"
" sąlyga, kaip pvz., bazinis > 1000."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__date_estimated_end
msgid "Approximated end date."
msgstr "Apytikslė pabaigos data."

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_export_mixin__reference_month__4
#: model:ir.model.fields.selection,name:hr_payroll.selection__res_company__ytd_reset_month__4
msgid "April"
msgstr "Balandis"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_dashboard_warning_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_dashboard_warning_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_input_type_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_input_type_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_employee_grade_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payroll_structure_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_rule_filter
msgid "Archived"
msgstr "Archyvuotas"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/components/dashboard/todo_list/todo_list.js:0
msgid ""
"Are you sure you want to delete this note? All content will be definitely "
"lost."
msgstr ""
"Ar tikrai norite ištrinti šią pastabą? Visi įrašai bus negrįžtamai prarasti."

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.default_assignment_of_salary_rule
msgid "Assignment of Salary"
msgstr "Atlyginimo paskyrimas"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid ""
"At least one previous negative net could be reported on this payslip for %s"
msgstr ""
"Šiame algalapyje gali būti nurodytas bent vienas ankstesnis neigiamas "
"grynasis uždarbis už %s"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_attachment_count
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__message_attachment_count
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_attachment_count
msgid "Attachment Count"
msgstr "Prisegtukų skaičius"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__attachment_name
msgid "Attachment Name"
msgstr "Prisegtuko pavadinimas"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.default_attachment_of_salary_rule
msgid "Attachment of Salary"
msgstr "Atlyginimo priedas"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_report__work_entry_source__attendance
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_report__work_entry_source__attendance
msgid "Attendances"
msgstr "Lankomumas"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_export_mixin__reference_month__8
#: model:ir.model.fields.selection,name:hr_payroll.selection__res_company__ytd_reset_month__8
msgid "August"
msgstr "Rugpjūtis"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "August 2023 Payslip"
msgstr "2023 m. rugpjūčio mėnesio algalapis"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__struct_ids
msgid "Availability in Structure"
msgstr "Galimas struktūroje"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__available_in_attachments
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_input_type_view_search
msgid "Available in attachments"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__avatar_1920
msgid "Avatar"
msgstr "Portretas"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__avatar_128
msgid "Avatar 128"
msgstr "Portretas 128"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_report_view_tree
msgid "Average of Basic Wage"
msgstr "Vidutinis bazinis atlyginimas"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_report_view_tree
msgid "Average of Net Wage"
msgstr "Vidutinis neto atlyginimas"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payment_report_wizard.py:0
msgid "Bank account"
msgstr "Banko sąskaita"

#. module: hr_payroll
#: model:hr.salary.rule.category,name:hr_payroll.BASIC
msgid "Basic"
msgstr "Bazinis"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
#: model:hr.salary.rule,name:hr_payroll.default_basic_salary_rule
msgid "Basic Salary"
msgstr "Pagrindinis atlyginimas"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__basic_wage
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__basic_wage
msgid "Basic Wage"
msgstr "Bazinis atlyginimas"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__leave_basic_wage
msgid "Basic Wage for Time Off"
msgstr "Bazinis atlyginimas už laisvadienius"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Batch"
msgstr "Grupiniai skaičiavimai"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__payslip_run_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
msgid "Batch Name"
msgstr "Grupės pavadinimas"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/components/dashboard/payslip_batch/payslip_batch.xml:0
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payslip_run
msgid "Batches"
msgstr "Grupiniai skaičiavimai"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_res_config_settings__module_l10n_be_hr_payroll
msgid "Belgium Payroll"
msgstr "Belgiškas algalapis"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_contract__schedule_pay__bi-monthly
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__default_schedule_pay__bi-monthly
msgid "Bi-monthly"
msgstr "Du kartus per mėnesį"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_contract__schedule_pay__bi-weekly
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__default_schedule_pay__bi-weekly
msgid "Bi-weekly"
msgstr "Du kartus per savaitę"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_note_demo_content
msgid "Board meeting summary:"
msgstr "Valdybos susirinkimo santrauka:"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_payment_report_wizard__export_format__csv
msgid "CSV"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_form
msgid "Calculations"
msgstr "Skaičiavimai"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__calendar_changed
msgid "Calendar Changed"
msgstr "Pakeistas kalendorius"

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.action_hr_payroll_cancel_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_index_form_view
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_payment_report_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Cancel"
msgstr "Atšaukti"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payslip__state__cancel
msgid "Canceled"
msgstr "Atšaukta"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_attachment__state__cancel
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_report__state__cancelled
msgid "Cancelled"
msgstr "Atšauktas"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Cannot cancel a payslip that is done."
msgstr "Negalima atšaukti algalapio, kuris jau yra atliktas."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Cannot mark payslip as paid if not confirmed."
msgstr "Nepatvirtinus algalapio negalima pažymėti jo, kaip apmokėto."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__code
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_worked_days__code
msgid ""
"Careful, the Code is used in many references, changing it could lead to "
"unwanted changes."
msgstr ""
"Atsargiai, kodas naudojamas daugelyje nuorodų, jo keitimas gali sukelti "
"nepageidaujamus pakeitimus."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_headcount_line__employee_type
msgid ""
"Categorize your Employees by type. This field also has an impact on "
"contracts. Only Employees, Students and Trainee will have contract history."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__category_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__category_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__category_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_rule_filter
msgid "Category"
msgstr "Kategorija"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__is_refund
msgid ""
"Check if the value of the salary attachment must be taken into account as "
"negative (-X)"
msgstr ""

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid ""
"Check the <strong>Work Entries</strong> linked to your newly created "
"Contract."
msgstr ""
"Patikrinkite <strong>Darbo įrašai</strong> susieti su jūsų naujai sukurta "
"sutartimi."

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.default_child_support
msgid "Child Support"
msgstr "Vaikų parama"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__children_ids
msgid "Children"
msgstr "Dukterinis"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Choose a Payroll Localization"
msgstr "Pasirinkti algalapio lokalizaciją"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid "Click here to create a new <strong>Contract</strong>."
msgstr "Spustelėkite čia, norėdami sukurti naują <strong>Sutartį</strong>."

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid ""
"Click here to generate a <strong>Batch</strong> for the displayed Employees."
msgstr ""
" Spustelėkite čia, norėdami sugeneruoti <strong>paketą</strong> rodomiems "
"darbuotojams."

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid ""
"Click on <strong>Salary Information</strong> to access additional fields."
msgstr ""
"Spustelėkite <strong>Atlyginimo informacija</strong>  kad pasiektumėte "
"papildomus laukus."

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid "Click on Employees to pick one of your <strong>Employees</strong>."
msgstr ""
" Spustelėkite „Darbuotojai“, kad pasirinktumėte vieną iš savo "
"<strong>Darbuotojų</strong>."

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid ""
"Click on Payroll to manage your employee's <strong>Work Entries</strong>, "
"<strong>Contracts</strong> and <strong>Payslips</strong>."
msgstr ""
" Spustelėkite „Atlyginimai“, kad tvarkytumėte savo darbuotojo <strong>Darbo "
"įrašus</strong>, <strong>Sutartis</strong> ir <strong>Atlyginimo "
"lapelius</strong>."

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid "Click on the <strong>Payslip</strong>."
msgstr " Spustelėkite ant <strong>Atlyginimo lapelio</strong>."

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid "Click on the <strong>Work Entries</strong> menu."
msgstr "Spustelėkite ant meniu <strong>Darbo įrašai</strong>."

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/components/dashboard/todo_list/todo_list.xml:0
msgid "Close"
msgstr "Uždaryti"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__paid_date
msgid "Close Date"
msgstr "Uždarymo data"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_type__round_days_type__half-up
msgid "Closest"
msgstr "Arčiausias"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__structure_code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__code
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
msgid "Code"
msgstr "Kodas"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_view_kanban
msgid "Code:"
msgstr "Kodas:"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_working_rate__color
msgid "Color"
msgstr "Spalva"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_res_company
msgid "Companies"
msgstr "Įmonės"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_declaration_mixin__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_note__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_payment_report_wizard__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_mixin__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__company_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Company"
msgstr "Įmonė"

#. module: hr_payroll
#: model:hr.salary.rule.category,name:hr_payroll.COMP
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "Company Contribution"
msgstr "Įmonės įmokos"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__full_time_required_hours
msgid "Company Full Time"
msgstr "Įmonės pilnas etatas"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_attachment__state__close
msgid "Completed"
msgstr "Atlikta"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "Computation"
msgstr "Apskaičiavimas"

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.action_hr_payroll_compute_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Compute Sheet"
msgstr "Skaičiuoti lapą"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__compute_date
msgid "Computed On"
msgstr "APskaičiuota"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__condition_select
msgid "Condition Based on"
msgstr "Sąlyga, paremta"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__condition_other_input_id
msgid "Condition Other Input"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "Conditions"
msgstr "Sąlygos"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigūracijos nustatymai"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_index_form_view
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Confirm"
msgstr "Patvirtinti"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid "Confirm the <strong>Payslip</strong>."
msgstr "Patvirtinkite <strong>algalapį</strong>."

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payslip_run__state__verify
msgid "Confirmed"
msgstr "Patvirtinti"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_report__state__conflict
msgid "Conflict"
msgstr "Konfliktas"

#. module: hr_payroll
#: model:hr.payroll.dashboard.warning,name:hr_payroll.hr_payroll_dashboard_warning_work_entries_in_conflict
msgid "Conflicts"
msgstr "Konfliktai"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__contract_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__contract_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__contract_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__contract_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__contract_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__contract_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_employee_mixin__contract_ids
msgid "Contract"
msgstr "Sutartis"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__contract_domain_ids
msgid "Contract Domain"
msgstr "Sutarties sritis"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Contract Expiration Notice Period"
msgstr "Sutarties galiojimo pabaigos įspėjimo laikotarpis"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__contract_names
msgid "Contract Names"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_structure_type_view_search
msgid "Contract Type"
msgstr "Sutarties tipas"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Contract Wage ("
msgstr ""

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_contract_history
msgid "Contract history"
msgstr "Sutartčių istorija"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_index_form_view
msgid "Contract indexing"
msgstr "Sutarčių indeksavimas"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_hr_contract_repository
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__contract_ids
#: model:ir.ui.menu,name:hr_payroll.hr_menu_all_contracts
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_employees_root
msgid "Contracts"
msgstr "Sutartys"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_filter
msgid "Contribution Register"
msgstr "Įmokų registras"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_contribution_registers
#: model:ir.actions.report,name:hr_payroll.action_report_register
msgid "Contribution Registers"
msgstr "Įmokų registrai"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_convanceallowance1
msgid "Conveyance Allowance"
msgstr "Transporto išlaidos"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_ca_gravie
msgid "Conveyance Allowance For Gravie"
msgstr "Transporto išlaidos Gravie"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__amount
msgid "Count"
msgstr "Skaičiuoti"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_dashboard_warning__country_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__country_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__country_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__country_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__country_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__country_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__country_id
msgid "Country"
msgstr "Valstybė"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__country_code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__country_code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__country_code
msgid "Country Code"
msgstr "Valstybės kodas"

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.action_hr_payroll_confirm_payroll
msgid "Create Draft Entry"
msgstr "Sukurti juodraštinį įrašą"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_form
msgid "Create Individual Attachments"
msgstr "Sukurti individualius priedus"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Create Payment Report"
msgstr "Sukurti mokėjimo ataskaitą"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Create SEPA payment"
msgstr "Sukurti SEPA mokėjimą"

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.action_new_salary_attachment
msgid "Create Salary Attachment"
msgstr "Sukurkite atlyginimo priedą"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_mixin__create_uid
msgid "Create Uid"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.actions.act_window,help:hr_payroll.action_hr_contract_repository
msgid "Create a new contract"
msgstr "Sukurkite naują sutartį"

#. module: hr_payroll
#: model_terms:ir.actions.act_window,help:hr_payroll.action_salary_rule_form
msgid "Create a new salary rule"
msgstr "Sukurti naują atlyginimo taisyklę"

#. module: hr_payroll
#: model_terms:ir.actions.act_window,help:hr_payroll.hr_rule_parameter_action
msgid "Create a new salary rule parameter"
msgstr "Sukurti naują atlyginimo taisyklės parametrą"

#. module: hr_payroll
#: model_terms:ir.actions.act_window,help:hr_payroll.action_view_hr_payroll_structure_list_form
msgid "Create a new salary structure"
msgstr "Sukurti naują atlyginimo struktūrą"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/components/dashboard/todo_list/todo_list.xml:0
msgid "Create new todo note"
msgstr "Sukurti naują darbų pastabą"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_dashboard_warning__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_working_rate__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_note__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_payment_report_wizard__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__create_uid
msgid "Created by"
msgstr "Sukūrė"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_dashboard_warning__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_working_rate__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_note__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_payment_report_wizard__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__create_date
msgid "Created on"
msgstr "Sukurta"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__credit_note
msgid "Credit Note"
msgstr "Kreditinė sąskaita"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Credit Notes"
msgstr "Kreditinės sąskaitos"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__is_credit_time
msgid "Credit Time"
msgstr "Kredito laikas"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract_history__time_credit
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry__is_credit_time
msgid "Credit time"
msgstr "Kredito laikas"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__currency_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__currency_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__currency_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__currency_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__currency_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__currency_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__currency_id
msgid "Currency"
msgstr "Valiuta"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_search
msgid "Current month"
msgstr "Einamas mėnuo"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount__is_name_custom
msgid "Custom Name"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_contract__schedule_pay__daily
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__default_schedule_pay__daily
msgid "Daily"
msgstr "Kasdien"

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_dashboard_configuration
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_dashboard_root
msgid "Dashboard"
msgstr "Valdymo skydelis"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Date"
msgstr "Data"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__date_start
msgid "Date From"
msgstr "Nuo"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__date_start
msgid "Date Start"
msgstr "Pradžios data"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__date_end
msgid "Date To"
msgstr "Iki"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__date_end
msgid "Date at which this assignment has been set as completed or cancelled."
msgstr "Data, kada ši užduotis buvo nustatyta kaip atlikta arba atšaukta."

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_type__round_days__full
msgid "Day"
msgstr "Diena"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Day where the 'Year To Date' will be reset every year."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_res_company__ytd_reset_day
#: model:ir.model.fields,help:hr_payroll.field_res_config_settings__ytd_reset_day
msgid ""
"Day where the YTD will be reset every year. If zero or negative, then the first day of the month will be selected instead.\n"
"        If greater than the last day of a month, then the last day of the month will be selected instead."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__number_of_days
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Days"
msgstr "Dienos"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__count_leave
msgid "Days of Paid Time Off"
msgstr "Apmokamos laisvadienių dienos"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__count_unforeseen_absence
msgid "Days of Unforeseen Absence"
msgstr "Nenumatytos nebuvimo darbe dienos"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__count_leave_unpaid
msgid "Days of Unpaid Time Off"
msgstr "Neapmokamos laisvadienių dienos"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_export_mixin__reference_month__12
#: model:ir.model.fields.selection,name:hr_payroll.selection__res_company__ytd_reset_month__12
msgid "December"
msgstr "Gruodis"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__res_id
msgid "Declaration Model Id"
msgstr "Deklaracijos Modelio Id"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__res_model
msgid "Declaration Model Name"
msgstr "Deklaracijos Modelio Pavadinimas"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_declaration_mixin__line_ids
msgid "Declarations"
msgstr "Deklaracijos"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
#: model:hr.salary.rule,name:hr_payroll.default_deduction_salary_rule
#: model:hr.salary.rule.category,name:hr_payroll.DED
msgid "Deduction"
msgstr "Išskaitymas"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__schedule_pay
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_type__default_schedule_pay
msgid "Default Scheduled Pay"
msgstr "Numatytasis suplanuotas atlyginimas"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_type__wage_type
msgid "Default Wage Type"
msgstr "Numatytasis darbo užmokesčio tipas"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_type__default_work_entry_type_id
msgid "Default Work Entry Type"
msgstr "Numatytasis darbo įrašo tipas"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid "Define a <strong>Wage</strong>."
msgstr "Apibrėžkite <strong>Atlyginimą</strong>."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_structure__schedule_pay
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_structure_type__default_schedule_pay
msgid "Defines the frequency of the wage payment."
msgstr "Nurodo atlyginimo išmokėjimo dažnumą."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_edit_payslip_line__struct_id
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__struct_id
msgid ""
"Defines the rules that have to be applied to this payslip, according to the "
"contract chosen. If the contract is empty, this field isn't mandatory "
"anymore and all the valid rules of the structures of the employee's "
"contracts will be applied."
msgstr ""
"Nustato taisykles, kurias reikia taikyti šiam algalapiui, atsižvelgiant į "
"pasirinktą sutartį. Jei sutarties laukas tuščias, šis laukas tampa "
"neprivalomas, ir bus taikomos visos galiojančios darbuotojo sutarčių "
"struktūrų taisyklės."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__department_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__department_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__department_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__department_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__department_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_headcount_line_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Department"
msgstr "Skyrius"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_light_payslip
msgid "Department:"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__description
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__note
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__description
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__description
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__note
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__note
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_rule_parameter_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "Description"
msgstr "Aprašymas"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__disabled
msgid "Disabled"
msgstr "Išjungta"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_edit_payslip_lines_form_wizard
msgid "Discard"
msgstr "Atmesti"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_dashboard_warning__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_working_rate__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_note__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_payment_report_wizard__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__display_name
msgid "Display Name"
msgstr "Rodomas pavadinimas"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Display Payslip PDF File on a payslip form"
msgstr "Rodyti algalapio PDF failą algalapio formoje"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_hr_work_entry_type_view_form_inherit
msgid "Display in Payslip"
msgstr "Rodyti atlyginimo lapelyje"

#. module: hr_payroll
#: model:res.groups,name:hr_payroll.group_payslip_display
msgid "Display payslip PDF"
msgstr "Rodyti algalapio PDF"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__attachment
msgid "Document"
msgstr "Dokumentas"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payslip__state__done
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payslip_run__state__close
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Done"
msgstr "Atlikta"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_filter
msgid "Done Payslip Batches"
msgstr "Patvirtintos algalapių suvestinės"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Done Slip"
msgstr "Atlikti algalapiai"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Double Holiday Pay"
msgstr "Dvigubas atostogų atlyginimas"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_type__round_days_type__down
msgid "Down"
msgstr "Žemyn"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_employee_declaration__state__draft
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payslip__state__draft
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_report__state__draft
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_filter
msgid "Draft"
msgstr "Juodraštis"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_filter
msgid "Draft Payslip Batches"
msgstr "Algalapių suvestinių juodraščiai"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Draft Slip"
msgstr "Algalapių juodraščiai"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Earnings"
msgstr ""

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
#: model:ir.actions.server,name:hr_payroll.action_edit_payslip_lines
msgid "Edit Payslip Lines"
msgstr "Redaguoti algalapio eilutes"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__edit_payslip_lines_wizard_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__edit_payslip_lines_wizard_id
msgid "Edit Payslip Lines Wizard"
msgstr "Algalapio eilučių redagavimo vedlys"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_edit_payslip_worked_days_line
msgid "Edit payslip line wizard worked days"
msgstr "Redaguoti algalapio eilutės vedlį dirbtų dienų skaičiui"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_edit_payslip_lines_wizard
msgid "Edit payslip lines wizard"
msgstr "Algalapio eilučių redagavimo vedlys"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_edit_payslip_line
msgid "Edit payslip lines wizard line"
msgstr "Redaguoti algalapio eilučių vedlio eilutę"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__edited
msgid "Edited"
msgstr "Redaguota"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_edit_payslip_lines_form_wizard
msgid "Edition of Payslip Lines in the Payslip"
msgstr "Algalapio eilučių redagavimas algalapyje"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_work_entry_export_mixin.py:0
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_mixin__eligible_employee_line_ids
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_export_employee_mixin_list_view
msgid "Eligible Employees"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_mixin__eligible_employee_count
msgid "Eligible Employees Count"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__email_cc
msgid "Email cc"
msgstr "El. Pašto CC"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_employee
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__employee_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__employee_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__employee_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__employee_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__employee_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__employee_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_employee_mixin__employee_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__employee_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Employee"
msgstr "Darbuotojas"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_search_inherit
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_hr_employee_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Employee Code"
msgstr "Darbuotojo kodas"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_contract
msgid "Employee Contract"
msgstr "Darbuotojo sutartis"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount__employee_count
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__employee_count
msgid "Employee Count"
msgstr "Darbuotojų skaičius"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payroll_declaration_mixin.py:0
msgid "Employee Declarations"
msgstr "Darbuotojo deklaracijos"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_employee_grade_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payroll_structure_list_view
msgid "Employee Function"
msgstr "Darbuotojo funkcija"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Employee Information"
msgstr "Darbuotojo informacija"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/components/dashboard/payslip_batch/payslip_batch.js:0
#: model:ir.actions.act_window,name:hr_payroll.action_view_hr_payslip_form
#: model:ir.actions.act_window,name:hr_payroll.action_view_hr_payslip_month_form
msgid "Employee Payslips"
msgstr "Darbuotojų algalapiai"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Employee Trends"
msgstr "Darbuotojų tendencijos"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__employee_type
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_headcount_line_search
msgid "Employee Type"
msgstr "Darbuotojo Tipas"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payment_report_wizard.py:0
msgid "Employee address"
msgstr ""

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payment_report_wizard.py:0
msgid "Employee name"
msgstr ""

#. module: hr_payroll
#: model:hr.payroll.dashboard.warning,name:hr_payroll.hr_payroll_dashboard_warning_employee_with_different_company_on_contract
msgid "Employee whose contracts and company are differents"
msgstr "Darbuotojas, kurio sutartys ir įmonė skiriasi"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_contract__hourly_wage
msgid "Employee's hourly gross wage."
msgstr "Darbuotojo valandinis bruto darbo užmokestis."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_contract__resource_calendar_id
msgid ""
"Employee's working schedule.\n"
"        When left empty, the employee is considered to have a fully flexible schedule, allowing them to work without any time limit, anytime of the week.\n"
"        "
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__employee_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__employee_ids
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_filter
msgid "Employees"
msgstr "Darbuotojai"

#. module: hr_payroll
#: model:hr.payroll.dashboard.warning,name:hr_payroll.hr_payroll_dashboard_warning_employee_missing_from_open_batch
msgid "Employees (With Running Contracts) missing from open batches"
msgstr ""
"Darbuotojai (su aktyviomis sutartimis), kurie nėra įtraukti į atidarytas "
"grupes."

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
msgid "Employees Selection"
msgstr ""

#. module: hr_payroll
#: model:hr.payroll.dashboard.warning,name:hr_payroll.hr_payroll_dashboard_warning_employee_ambiguous_contract
msgid "Employees With Both New And Running Contracts"
msgstr "Darbuotojai, turintys tiek naujas, tiek aktyvias sutartis"

#. module: hr_payroll
#: model:hr.payroll.dashboard.warning,name:hr_payroll.hr_payroll_dashboard_warning_employees_multiple_payslips
msgid "Employees With Multiple Open Payslips of Same Type"
msgstr "Darbuotojai, turintys kelis atvirus to paties tipo lgalapius"

#. module: hr_payroll
#: model:hr.payroll.dashboard.warning,name:hr_payroll.hr_payroll_dashboard_warning_employee_without_bank_account
msgid "Employees Without Bank account Number"
msgstr "Darbuotojai neturintys banko sąskaitos"

#. module: hr_payroll
#: model:hr.payroll.dashboard.warning,name:hr_payroll.hr_payroll_dashboard_warning_employee_without_identification
msgid "Employees Without Identification Number"
msgstr "Darbuotojai neturintys identifikavimo numerio"

#. module: hr_payroll
#: model:hr.payroll.dashboard.warning,name:hr_payroll.hr_payroll_dashboard_warning_employee_without_contract
msgid "Employees Without Running Contracts"
msgstr "Darbuotojai neturintys veikiančios sutarties"

#. module: hr_payroll
#: model:hr.payroll.dashboard.warning,name:hr_payroll.hr_payroll_dashboard_warning_nearly_expired_contracts
msgid "Employees with running contracts coming to an end"
msgstr ""

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Employer Cost"
msgstr "Darbdavio sąnaudos"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_structure__hide_basic_on_pdf
msgid ""
"Enable this option if you don't want to display the Basic Salary on the "
"printed pdf."
msgstr ""
"Įjunkite šią parinktį, jei nenorite, kad spausdintame PDF faile būtų rodomas"
" pagrindinis atlyginimas."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__date_to
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__date_end
msgid "End Date"
msgstr "Pabaigos data"

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_salary_attachment_check_dates
msgid "End date may not be before the starting date."
msgstr "Pabaigos data negali būti ankstesnė už pradžios datą."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__display_warning
msgid "Error"
msgstr "Klaida"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_salary_rule_category.py:0
msgid "Error! You cannot create recursive hierarchy of Salary Rule Category."
msgstr ""
"Klaida! Jūs negalite sukurti pasikartojančios atlyginimo taisyklių "
"kategorijų hierarchijos."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__date_estimated_end
msgid "Estimated End Date"
msgstr "Numatoma pabaigos data"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_line__partner_id
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__partner_id
msgid "Eventual third party involved in the salary payment of the employees."
msgstr "Trečiąjai šaliai mokama įmoka nuo darbuotojo atlyginimo."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_employee_mixin__export_id
msgid "Export"
msgstr "Eksportuoti"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_run__payment_report
msgid "Export .csv file related to this batch"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__payment_report
msgid "Export .csv file related to this payslip"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_export_mixin_form_view
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_export_mixin_list_view
msgid "Export Employee Work Entries"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_mixin__export_file
msgid "Export File"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_mixin__export_filename
msgid "Export Filename"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_payment_report_wizard__export_format
msgid "Export Format"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Export Payslip"
msgstr "Eksportuoti algalapį"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_export_mixin__reference_month__2
#: model:ir.model.fields.selection,name:hr_payroll.selection__res_company__ytd_reset_month__2
msgid "February"
msgstr "Vasaris"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid "First, we'll create a new <strong>Contract</strong>."
msgstr " Pirmiausia sukursime naują <strong>sutartį</strong>."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__amount_fix
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__amount_fix
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_rule__amount_select__fix
msgid "Fixed Amount"
msgstr "Fiksuota suma"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_contract__wage_type__monthly
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__wage_type__monthly
msgid "Fixed Wage"
msgstr "Fiksuotas darbo užmokestis"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_follower_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__message_follower_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_follower_ids
msgid "Followers"
msgstr "Sekėjai"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_partner_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__message_partner_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_partner_ids
msgid "Followers (Partners)"
msgstr "Sekėjai (partneriai)"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__activity_type_icon
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_run__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesome piktograma, pvz., fa-tasks"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_line__amount_percentage
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__amount_percentage
msgid "For example, enter 50.0 to apply a percentage of 50%"
msgstr "Pavyzdžiui, įrašykite 50.0, kad būtų skaičiuojama 50%"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_res_config_settings__module_l10n_fr_hr_payroll
msgid "French Payroll"
msgstr "Prancūziškas algalapis"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount__date_from
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__date_from
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__date_from
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__date_from
msgid "From"
msgstr "Iš"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payslips_by_employees.py:0
msgid "From %(from_date)s to %(end_date)s"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "General"
msgstr "Bendra"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_payment_report_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
msgid "Generate"
msgstr "Generuoti"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_export_mixin_form_view
msgid "Generate Export File"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_employee_declaration_view_tree
msgid "Generate PDFs"
msgstr "Generuoti PDF-us"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/hr_work_entries_gantt.xml:0
#: code:addons/hr_payroll/static/src/views/work_entry_calendar/work_entry_calendar.xml:0
#: model:ir.actions.act_window,name:hr_payroll.action_hr_payslip_by_employees
#: model:ir.actions.server,name:hr_payroll.action_hr_payslip_run_generate
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
msgid "Generate Payslips"
msgstr "Generuoti algalapius"

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.action_generate_payslips_from_work_entries
msgid "Generate payslips"
msgstr "Generuoti algalapius"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payslip_employees
msgid "Generate payslips for all selected employees"
msgstr "Visiems pasirinktiems darbuotojams generuoti algalapius"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_employee_declaration__state__pdf_generated
msgid "Generated PDF"
msgstr "Sugeneruotas PDF"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
msgid "Generated Payslips"
msgstr "Sugeneruoti algalapiai"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_note_demo_content
msgid "Give insurance card to new registered employees"
msgstr "Išduoti draudimo kortelę naujai registruotiems darbuotojams"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__gross_wage
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__gross_wage
msgid "Gross Wage"
msgstr "Bruto atlyginimas"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_employee_declaration_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_headcount_line_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payroll_structure_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_rule_filter
msgid "Group By"
msgstr "Grupuoti pagal"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_res_config_settings__group_payslip_display
msgid "Group Payslip Display"
msgstr "Grupės algalapių rodinys"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_payment_report_wizard
msgid "HR Payroll Payment Report Wizard"
msgstr ""

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_work_entry
msgid "HR Work Entry"
msgstr "Personalo darbo įrašas"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_work_entry_type
msgid "HR Work Entry Type"
msgstr "Personbalo darbo įrašo tipas"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_type__round_days__half
msgid "Half Day"
msgstr "Pusė dienos"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__has_done_payslip
msgid "Has Done Payslip"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__has_message
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__has_message
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__has_message
msgid "Has Message"
msgstr "Turi žinutę"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__has_negative_net_to_report
msgid "Has Negative Net To Report"
msgstr "Turi neigiamą neto, apie kurį reikia pranešti"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__has_refund_slip
msgid "Has Refund Slip"
msgstr "Turi grąžinta algalapį"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__has_similar_attachment
msgid "Has Similar Attachment"
msgstr "Turi panašų priedą"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__has_similar_attachment_warning
msgid "Has Similar Attachment Warning"
msgstr "Yra panašus priedo įspėjimas"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__has_total_amount
msgid "Has Total Amount"
msgstr "Turi bendrą sumą"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.hr_payroll_headcount_action
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_headcount_action
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_headcount_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_headcount_view_tree
msgid "Headcount"
msgstr ""

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_headcount_line
msgid "Headcount Line"
msgstr ""

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payroll_headcount.py:0
msgid "Headcount for %(company_name)s from %(date_from)s to %(date_to)s"
msgstr ""

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payroll_headcount.py:0
msgid "Headcount for %(company_name)s on the %(date)s"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_headcount_line_view_tree
msgid "Headcount's Employees"
msgstr ""

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payroll_headcount.py:0
msgid "Headcount's employees"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__hide_basic_on_pdf
msgid "Hide Basic On Pdf"
msgstr "Slėpti pagrindinę informaciją PDF faile"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__hourly_wage
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_contract__wage_type__hourly
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__wage_type__hourly
msgid "Hourly Wage"
msgstr "Valandinis atlyginimas"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Hours"
msgstr "Valandos"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Hours / Week"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__hours_per_week
#: model:ir.model.fields,field_description:hr_payroll.field_resource_calendar__hours_per_week
msgid "Hours per Week"
msgstr "Valandos per savaitę"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_houserentallowance1
msgid "House Rent Allowance"
msgstr "Nuomos išlaidos"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_dashboard_warning__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_working_rate__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_note__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_payment_report_wizard__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__id
msgid "ID"
msgstr "ID"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_exception_icon
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__activity_exception_icon
msgid "Icon"
msgstr "Piktograma"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__activity_exception_icon
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_run__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Išimties veiklą žyminti piktograma."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__message_needaction
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_run__message_needaction
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Jeigu pažymėta, naujiems pranešimams reikės jūsų dėmesio."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__message_has_error
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__message_has_sms_error
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_run__message_has_error
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_run__message_has_sms_error
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__message_has_error
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Jei pažymėta, yra žinučių, turinčių pristatymo klaidų."

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
msgid ""
"If empty, the default salary structure from the Salary structure type of the"
" employee will be used"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_employee__is_non_resident
#: model:ir.model.fields,help:hr_payroll.field_res_users__is_non_resident
msgid "If recipient lives in a foreign country"
msgstr "Jei gavėjas gyvena užsienio šalyje"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_input_type__is_quantity
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__is_quantity
msgid ""
"If set, hide currency and consider the manual input as a quantity for every "
"rule computation using this input."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__active
msgid ""
"If the active field is set to false, it will allow you to hide the salary "
"rule without removing it."
msgstr ""
"Jei aktyvus laukas yra nustatytas kaip neigiamas, tai leis jums paslėpti "
"atlyginimo taisyklę jos nepašalinant."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_employee__disabled
msgid "If the employee is declared disabled by law"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__image_1920
msgid "Image"
msgstr "Paveikslėlis"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__image_128
msgid "Image 128"
msgstr "Paveikslėlis 128"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_view_tree
msgid "Index Contracts"
msgstr "Indeksuoti sutartis"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_hr_payroll_index
#: model:ir.actions.server,name:hr_payroll.action_index_contracts
msgid "Index contract(s)"
msgstr "Indeksuoti sutartį(is)"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_index
msgid "Index contracts"
msgstr "Indeksuoti sutartis"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_res_config_settings__module_l10n_in_hr_payroll
msgid "Indian Payroll"
msgstr "Indiškas algalapis"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__credit_note
msgid "Indicates this payslip has a refund of another"
msgstr "Parodo, kad tai yra kito algalapio grąžinimas"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Input Data"
msgstr "Priskaitymų duomenys"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__note
msgid "Internal Note"
msgstr "Vidinė pastaba"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__internet_invoice
msgid "Internet Subscription Invoice"
msgstr "Internetinės prenumeratos sąskaita faktūra"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_is_follower
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__message_is_follower
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_is_follower
msgid "Is Follower"
msgstr "Yra sekėjas"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__is_fulltime
#: model:ir.model.fields,field_description:hr_payroll.field_resource_calendar__is_fulltime
msgid "Is Full Time"
msgstr "Visas etatas"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__is_paid
msgid "Is Paid"
msgstr "Apmokėta"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
msgid "Is Register"
msgstr "Yra registruotas"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__is_regular
msgid "Is Regular"
msgstr "Yra reguliarus"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__is_superuser
msgid "Is Superuser"
msgstr "Yra supervartotojas"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__is_wrong_duration
msgid "Is Wrong Duration"
msgstr "Neteisinga trukmė"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__is_quantity
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__is_quantity
msgid "Is quantity?"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__quantity
msgid ""
"It is used in computation for percentage and fixed amount. E.g. a rule for "
"Meal Voucher having fixed amount of 1€ per worked day can have its quantity "
"defined in expression like worked_days['WORK100'].number_of_days."
msgstr ""
"Tai naudojama procentinės ir fiksuotos sumos apskaičiavimui. Pavyzdžiui, "
"taisyklė dėl maitinimo kupono su fiksuota 1€ suma už dirbtą dieną gali "
"turėti jos kiekį apibrėžtą išraiškoje, pvz., "
"worked_days['WORK100'].number_of_days."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_input__amount
msgid ""
"It is used in computation. E.g. a rule for salesmen having 1%% commission of"
" basic salary per product can defined in expression like: result = "
"inputs['SALEURO'].amount * contract.wage * 0.01."
msgstr ""
"Tai naudojama skaičiavimams. Pavyzdžiui, taisyklė pardavėjams, kuriems "
"taikoma 1% komisija nuo pagrindinio atlyginimo už kiekvieną produktą, gali "
"būti apibrėžta išraiškoje, pvz.: result = inputs['SALEURO'].amount * "
"contract.wage * 0.01."

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_export_mixin__reference_month__1
#: model:ir.model.fields.selection,name:hr_payroll.selection__res_company__ytd_reset_month__1
msgid "January"
msgstr "Sausis"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_headcount_line_search
msgid "Job"
msgstr "Darbo pozicija"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__job_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__job_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__job_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Job Position"
msgstr "Pareigos"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__job_id
msgid "Job Title"
msgstr "Pareigos"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_light_payslip
msgid "Job Title:"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_export_mixin__reference_month__7
#: model:ir.model.fields.selection,name:hr_payroll.selection__res_company__ytd_reset_month__7
msgid "July"
msgstr "Liepa"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_export_mixin__reference_month__6
#: model:ir.model.fields.selection,name:hr_payroll.selection__res_company__ytd_reset_month__6
msgid "June"
msgstr "Birželis"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_search
msgid "Last 365 Days Payslip"
msgstr "Paskutinės 365 dienų algalapiai"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Last Departures"
msgstr "Paskutiniai išvykimai"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
msgid "Last Month"
msgstr "Paskutinis mėnuo"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
msgid "Last Quarter"
msgstr "Paskutinis ketvirtis"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_dashboard_warning__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_working_rate__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_note__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_payment_report_wizard__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__write_uid
msgid "Last Updated by"
msgstr "Paskutinį kartą atnaujino"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_dashboard_warning__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_working_rate__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_note__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_payment_report_wizard__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__write_date
msgid "Last Updated on"
msgstr "Paskutinį kartą atnaujinta"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount__line_ids
msgid "Line"
msgstr "Eilutė"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_form
msgid "Line Name"
msgstr "Eilutės pavadinimas"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_declaration_mixin__lines_count
msgid "Lines Count"
msgstr "Eilučių skaičius"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule_category__parent_id
msgid ""
"Linking a salary category to its parent is used only for the reporting "
"purpose."
msgstr ""
"Atlyginimo taisyklių kategorijos susiejimas su tėvine kategorija naudojimas "
"tik ataskaitose."

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_note_demo_content
msgid "Links:"
msgstr "Nuorodos:"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__paid
msgid "Made Payment Order? "
msgstr "Sukūrėte mokėjimo nurodymą?"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_main_attachment_id
msgid "Main Attachment"
msgstr "Pagrindinis prisegtukas"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_export_mixin__reference_month__3
#: model:ir.model.fields.selection,name:hr_payroll.selection__res_company__ytd_reset_month__3
msgid "March"
msgstr "Kovas"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_form
msgid "Mark as Completed"
msgstr "Pažymėti kaip užbaigtą"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Mark as paid"
msgstr "Pažymėti kaip apmokėtą"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__master_department_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_search
msgid "Master Department"
msgstr "Pagrindinis skyrius"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__condition_range_max
msgid "Maximum Range"
msgstr "Didžiausias intervalas"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_export_mixin__reference_month__5
#: model:ir.model.fields.selection,name:hr_payroll.selection__res_company__ytd_reset_month__5
msgid "May"
msgstr "Gegužė"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_meal_voucher
msgid "Meal Voucher"
msgstr "Maisto kuponas"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_has_error
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__message_has_error
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_has_error
msgid "Message Delivery error"
msgstr "Žinutės pristatymo klaida"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__message_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_ids
msgid "Messages"
msgstr "Žinutės"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__condition_range_min
msgid "Minimum Range"
msgstr "Mažiausias intervalas"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__mobile_invoice
msgid "Mobile Subscription Invoice"
msgstr "Mobiliojo ryšio prenumeratos sąskaita-faktūra"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_report_hr_payroll_contribution_register
msgid "Model for Printing hr.payslip.line grouped by register"
msgstr "Modelis spausdinti hr.payslip.line, suskirstytą pagal registrą"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/components/dashboard/payroll_stats/payroll_stats.xml:0
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_contract__schedule_pay__monthly
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__default_schedule_pay__monthly
msgid "Monthly"
msgstr "Kas mėnesį"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_tree
msgid "Monthly Amount"
msgstr "Mėnesio suma"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__my_activity_date_deadline
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Veiklos paskutinis terminas"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_dashboard_warning__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_note__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__rule_parameter_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__name
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Name"
msgstr "Pavadinimas"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_structure__payslip_name
msgid ""
"Name to be set on a payslip. Example: 'End of the year bonus'. If not set, "
"the default value is 'Salary Slip'"
msgstr ""
"Pavadinimas, kurį reikia nustatyti algalapyje. Pavyzdys: „Metų pabaigos "
"premija“. Jei nepateikta, numatytoji reikšmė yra 'Algalapis'."

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_light_payslip
msgid "Name:"
msgstr "Vardas:"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__negative_net_to_report_amount
msgid "Negative Net To Report Amount"
msgstr "Neigiama grynoji suma nurodyta ataskaitoje"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__negative_net_to_report_display
msgid "Negative Net To Report Display"
msgstr "Neigiama grynoji suma rodinyje"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__negative_net_to_report_message
msgid "Negative Net To Report Message"
msgstr "Pranešimas apie neigiamą grynąją sumą"

#. module: hr_payroll
#: model:mail.activity.type,name:hr_payroll.mail_activity_data_hr_payslip_negative_net
msgid "Negative Net to Report"
msgstr "Neigiama grynoji suma pranešimui"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__is_refund
msgid "Negative Value"
msgstr ""

#. module: hr_payroll
#: model:hr.salary.rule.category,name:hr_payroll.NET
msgid "Net"
msgstr "Atskaičius mokesčius"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_view_kanban
msgid "Net -"
msgstr "Grynoji suma -"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
#: model:hr.salary.rule,name:hr_payroll.default_net_salary
msgid "Net Salary"
msgstr "Į rankas"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__net_wage
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__net_wage
msgid "Net Wage"
msgstr "Neto užmokestis"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/report/hr_contract_history.py:0
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payslip_run__state__draft
msgid "New"
msgstr "Naujas"

#. module: hr_payroll
#: model:hr.payroll.dashboard.warning,name:hr_payroll.hr_payroll_dashboard_warning_new_contracts
msgid "New Contracts"
msgstr "Nauja sutartis"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "New Employees"
msgstr "Nauji darbuotojai"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payslips_by_employees.py:0
msgid "New Payslip"
msgstr "Naujas algalapis"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_calendar_event_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Kitas veiklos kalendoriaus įvykis"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_date_deadline
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Kito veiksmo terminas"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_summary
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__activity_summary
msgid "Next Activity Summary"
msgstr "Kito veiksmo santrauka"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_type_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__activity_type_id
msgid "Next Activity Type"
msgstr "Kito veiksmo tipas"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__no_end_date
msgid "No End Date"
msgstr "Nėra pabaigos datos"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "No ID number on the employee !!!"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_type__round_days__no
msgid "No Rounding"
msgstr "Jokio apvalinimo"

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_employee_unique_registration_number
msgid "No duplication of registration numbers is allowed"
msgstr "Neleidžiama dubliuoti registracijos numerių"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__default_no_end_date
msgid "No end date by default"
msgstr ""

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_rule_parameter.py:0
msgid "No rule parameter with code \"%(code)s\" was found for %(date)s"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__is_non_resident
#: model:ir.model.fields,field_description:hr_payroll.field_res_users__is_non_resident
msgid "Non-resident"
msgstr "Nerezidentas"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/res_company.py:0
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_note__note
msgid "Note"
msgstr "Pastaba"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_rule_parameter_view_form
msgid ""
"Note a description of your parameter, when it's used, how is it computed, "
"what's the source, ..."
msgstr ""
"Pažymėkite savo parametro aprašymą: kada jis naudojamas, kaip jis "
"apskaičiuojamas, koks yra jo šaltinis ir pan. ..."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid ""
"Note: There are previous payslips with a negative amount for a total of %s "
"to report."
msgstr "Pastaba: yra ankstesnių algalapių su neigiama suma. Iš viso %s."

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_category_form
msgid "Notes"
msgstr "Pastabos"

#. module: hr_payroll
#: model_terms:ir.actions.act_window,help:hr_payroll.action_contribution_registers
msgid "Nothing to show"
msgstr "Nėra ką rodyti"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_export_mixin__reference_month__11
#: model:ir.model.fields.selection,name:hr_payroll.selection__res_company__ytd_reset_month__11
msgid "November"
msgstr "Lapkritis"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_needaction_counter
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__message_needaction_counter
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_needaction_counter
msgid "Number of Actions"
msgstr "Veiksmų skaičius"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__number_of_days
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__number_of_days
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__number_of_days
msgid "Number of Days"
msgstr "Dienų skaičius"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__number_of_hours
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__number_of_hours
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__number_of_hours
msgid "Number of Hours"
msgstr "Valandų skaičius"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid ""
"Number of days prior to the contract end date that a contract expiration "
"warning is triggered."
msgstr ""
"Dienų skaičius iki sutarties pabaigos datos, kai suaktyvinamas įspėjimas "
"apie sutarties galiojimo pabaigą."

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid ""
"Number of days prior to the work permit expiration date that a warning is "
"triggered."
msgstr ""
"Dienų skaičius iki darbo leidimo galiojimo pabaigos datos, kada suveikia "
"įspėjimas."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_has_error_counter
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__message_has_error_counter
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_has_error_counter
msgid "Number of errors"
msgstr "Klaidų kiekis"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_contract__full_time_required_hours
msgid ""
"Number of hours to work on the company schedule to be considered as "
"fulltime."
msgstr ""
"Valandų skaičius, kurį reikia dirbti pagal įmonės grafiką, kad būtų laikoma "
"visu etatu."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__message_needaction_counter
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_run__message_needaction_counter
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Pranešimų, kuriems reikia imtis veiksmų, skaičius"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__message_has_error_counter
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_run__message_has_error_counter
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Žinučių su pristatymo klaida skaičius"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__occurrences
msgid "Number of times the salary attachment will appear on the payslip."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__occurrences
msgid "Occurrences"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_export_mixin__reference_month__10
#: model:ir.model.fields.selection,name:hr_payroll.selection__res_company__ytd_reset_month__10
msgid "October"
msgstr "Spalis"

#. module: hr_payroll
#: model:res.groups,name:hr_payroll.group_hr_payroll_user
msgid "Officer: Manage all contracts"
msgstr "Atsakingas darbuotojas: Tvarkykite visas sutartis"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_note_demo_content
msgid "On the"
msgstr "Ant"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid ""
"On the first tab is the amount of worked time giving you a <strong>gross "
"amount</strong>."
msgstr ""
"Pirmoje skirtuke rodoma dirbto laiko suma, suteikianti jums <strong> benrą "
"sumą</strong>."

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid ""
"On the second tab is the computation of the rules linked to the Structure "
"resulting in a <strong>net amount</strong>."
msgstr ""
"Antrame skirtuke pateikiamas su struktūra susietų taisyklių apskaičiavimas, "
"kuris lemia <strong>grynąją sumą</strong>."

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid ""
"On the smartbutton, you can find all the <strong>Payslips</strong> included "
"in the Batch."
msgstr ""
" Ant išmaniojo mygtuko galite rasti visus <strong>algalapius</strong> "
"įtrauktus į šią grupę."

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.ir_actions_server_action_open_reporting
msgid "Open Payroll Reporting"
msgstr "Atidarykite darbo užmokesčio ataskaitas"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Other Info"
msgstr "Kita informacija"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Other Information"
msgstr "Kita informacija"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_rule__amount_select__input
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_rule__condition_select__input
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_employee_grade_form
msgid "Other Input"
msgstr "Kiti priskaitymai"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__input_line_type_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input___allowed_input_type_ids
msgid "Other Input Line"
msgstr "Kitų priskaitymų eilutė"

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payslip_entry_type_view
msgid "Other Input Types"
msgstr "Kitų priskaitymų tipai"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Other Inputs"
msgstr "Kiti priskaitymai"

#. module: hr_payroll
#: model:hr.work.entry.type,name:hr_payroll.hr_work_entry_type_out_of_contract
msgid "Out of Contract"
msgstr "Be sutarties"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_declaration_mixin__pdf_error
msgid "PDF Error Message"
msgstr "PDF klaidos pranešimas"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__pdf_file
msgid "PDF File"
msgstr "PDF Failas"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payroll_employee_declaration.py:0
msgid "PDF generation started. It will be available shortly."
msgstr "PDF generavimas pradėtas. Netrukus bus atlikta."

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payslip__state__paid
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payslip_run__state__paid
msgid "Paid"
msgstr "Apmokėta"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__paid_amount
msgid "Paid Amount"
msgstr "Sumokėta suma"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_report__work_type__2
msgid "Paid Time Off"
msgstr "Apmokamos Atostogos"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__parameter_value
msgid "Parameter Value"
msgstr "Parametro vertė"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__parent_id
msgid "Parent"
msgstr "Tėvinis"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__time_credit
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_search_inherit
msgid "Part Time"
msgstr "Ne visą darbo dieną"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__time_credit_type_id
msgid "Part Time Work Entry Type"
msgstr "Darbo ne visą darbo dieną tipas"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
msgid "Part time"
msgstr "Ne visa darbo diena"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__partner_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__partner_id
msgid "Partner"
msgstr "Partneris"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_light_payslip
msgid "Pay Period:"
msgstr ""

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payslip
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__slip_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__payslip_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__slip_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__payslip_id
msgid "Pay Slip"
msgstr "Algalapis"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__slip_id
msgid "PaySlip"
msgstr "Algalapis"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
msgid "PaySlip Lines by Contribution Register"
msgstr "Algalapio eilutės pagal įmokų registrą"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
msgid "PaySlip Name"
msgstr "Algalapio pavadinimas"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__payment_report
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__payment_report
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_payment_report_view_form
msgid "Payment Report"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__payment_report_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__payment_report_date
msgid "Payment Report Date"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__payment_report_filename
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__payment_report_filename
msgid "Payment Report Filename"
msgstr ""

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.open_payroll_modules
#: model:ir.ui.menu,name:hr_payroll.menu_report_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_hr_employee_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Payroll"
msgstr "Atlyginimai"

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.action_reset_work_entries
msgid "Payroll - Technical: Reset Work Entries"
msgstr "Darbo užmokestis – Techniniai: iš naujo nustatyti darbo įrašus"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.payroll_report_action
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_report_view_tree
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_graph
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_search
msgid "Payroll Analysis"
msgstr "Darbo užmokesčio analizė"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_report
msgid "Payroll Analysis Report"
msgstr "Darbo užmokesčio analizės ataskaita"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__code
msgid "Payroll Code"
msgstr "Darbo užmokesčio kodas"

#. module: hr_payroll
#: model:ir.actions.client,name:hr_payroll.hr_payroll_dashboard_open
msgid "Payroll Dashboard"
msgstr "Darbo užmokesčio skydelis"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_dashboard_warning
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_dashboard_warning_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_dashboard_warning_view_tree
msgid "Payroll Dashboard Warning"
msgstr ""

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_hr_payroll_dashboard_warning
msgid "Payroll Dashboard Warnings"
msgstr ""

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_declaration_mixin
msgid "Payroll Declaration Mixin"
msgstr " Atlyginimų deklaracijos integracija"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_employee_declaration
msgid "Payroll Employee Declaration"
msgstr "Darbuotojo atlyginimų deklaracija"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Payroll Entries"
msgstr "Algalapio įrašai"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_headcount
msgid "Payroll Headcount"
msgstr ""

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_note
msgid "Payroll Note"
msgstr "Algalapio pastaba"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Payroll Rules"
msgstr "Atlyginimų taisyklės"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Payroll SEPA"
msgstr "Darbo užmokestis SEPA"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payroll_structure_filter
msgid "Payroll Structures"
msgstr "Atlyginimų struktūros"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Payroll rules that apply to your country"
msgstr "Algalapio taisyklės, kurios galioja jūsų šalyje"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_note_demo_content
msgid "Payroll tips &amp; tricks:"
msgstr "Darbo užmokesčių patarimai ir gudrybės:"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_res_config_settings__module_hr_payroll_account
msgid "Payroll with Accounting"
msgstr "Darbo užmokestis su apskaita"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_res_config_settings__module_hr_payroll_account_iso20022
msgid "Payroll with SEPA payment"
msgstr "Darbo užmokestis su SEPA mokėjimu"

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.ir_cron_generate_payslip_pdfs_ir_actions_server
msgid "Payroll: Generate pdfs"
msgstr "Darbo užmokestis: generuoti pdf failus"

#. module: hr_payroll
#: model:mail.template,name:hr_payroll.mail_template_new_payslip
msgid "Payroll: New Payslip"
msgstr "Darbo užmokestis: naujas algalapis"

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.ir_cron_update_payroll_data_ir_actions_server
msgid "Payroll: Update data"
msgstr "Darbo užmokestis: atnaujinti duomenis"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
#: model:ir.actions.act_window,name:hr_payroll.action_hr_payslip_new
#: model:ir.actions.report,name:hr_payroll.action_report_payslip
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__payslip_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_payment_report_wizard__payslip_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__payslip_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Payslip"
msgstr "Algalapis"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Payslip 'Date From' must be earlier than 'Date To'."
msgstr "Algalapio 'Data nuo' turi būti ankstesnė nei 'Data iki'."

#. module: hr_payroll
#: model:ir.actions.report,name:hr_payroll.action_report_light_payslip
msgid "Payslip (Light)"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__monthly_amount
msgid "Payslip Amount"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_filter
msgid "Payslip Batch"
msgstr "Algalapio suvestinė"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payslip_run
msgid "Payslip Batches"
msgstr "Algalapių suvestinės"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__payslip_count
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__payslip_count
msgid "Payslip Count"
msgstr "Algalapių skaičius"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
msgid "Payslip Generation"
msgstr ""

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payslip_input
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_input_type_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_input_type_view_tree
msgid "Payslip Input"
msgstr "Algalapio priskaitymas"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_input_type_view_tree
msgid "Payslip Input Name"
msgstr "Algalapio priskaitymo pavadinimas"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payslip_input_type
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_input_type_view_search
msgid "Payslip Input Type"
msgstr "Algalapio priskaitymo tipas"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__input_line_ids
msgid "Payslip Inputs"
msgstr "Algalapių priskaitymai"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_hr_employee_view_form
msgid "Payslip Language"
msgstr ""

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payslip_line
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_form
msgid "Payslip Line"
msgstr "Algalapio eilutė"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.act_contribution_reg_payslip_lines
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__line_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__line_ids
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_filter
msgid "Payslip Lines"
msgstr "Algalapio eilutės"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__payslip_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__name
msgid "Payslip Name"
msgstr "Algalapio pavadinimas"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_view_hr_payslip_input_type
msgid "Payslip Other Input Types"
msgstr "Kiti algalapio įvesties tipai"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Payslip PDF Display"
msgstr "Algalapio PDF rodinys"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payment_report_wizard.py:0
msgid "Payslip Period"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_payment_report_wizard__payslip_run_id
msgid "Payslip Run"
msgstr ""

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payslip_worked_days
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__worked_days_line_ids
msgid "Payslip Worked Days"
msgstr "Išdirbtų dienų algalapis"

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_salary_attachment_check_monthly_amount
msgid "Payslip amount must be strictly positive."
msgstr ""

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_salary_attachment.py:0
#: model:ir.actions.act_window,name:hr_payroll.act_hr_employee_payslip_list
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__slip_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__slip_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__payslip_ids
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_payslips
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_history_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_pivot
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_tree
msgid "Payslips"
msgstr "Algalapiai"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_hr_payslip_run_tree
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_tree
msgid "Payslips Batches"
msgstr "Algalapių suvestinės"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_tree
msgid "Payslips Count"
msgstr "Algalapių skaičius"

#. module: hr_payroll
#: model:hr.payroll.dashboard.warning,name:hr_payroll.hr_payroll_dashboard_warning_payslips_previous_contract
msgid "Payslips Generated On Previous Contract"
msgstr "Algalapiai, sudaryti pagal ankstesnę sutartį"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.hr_payslip_action_view_to_pay
msgid "Payslips To Pay"
msgstr "Mokėtini algalaiai"

#. module: hr_payroll
#: model:hr.payroll.dashboard.warning,name:hr_payroll.hr_payroll_dashboard_warning_payslips_negative_net
msgid "Payslips With Negative NET"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
msgid "Payslips by Employees"
msgstr "Algalapiai pagal darbuotojus"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__pdf_filename
msgid "Pdf Filename"
msgstr "PDF failo pavadinimas"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__pdf_to_generate
msgid "Pdf To Generate"
msgstr "PDF failas generavimui"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__percentage
msgid "Percentage"
msgstr "Procentinė dalis"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__amount_percentage
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__amount_percentage
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_rule__amount_select__percentage
msgid "Percentage (%)"
msgstr "Procentinė dalis (%)"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__amount_percentage_base
msgid "Percentage based on"
msgstr "Procentinė dalis nuo"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Period"
msgstr "Laikotarpis"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_mixin__period_start
msgid "Period Start"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_mixin__period_stop
msgid "Period Stop"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_report__work_entry_source__planning
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_report__work_entry_source__planning
msgid "Planning"
msgstr "Planavimas"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payroll_employee_declaration.py:0
msgid "Please select the declarations for which you want to generate a PDF."
msgstr "Pasirinkite deklaracijas, kurioms norite sukurti PDF."

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_headcount_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_export_mixin_form_view
msgid "Populate"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Post payroll slips in accounting"
msgstr "Skelbti algalapius apskaitoje"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Previous Negative Payslip to Report"
msgstr "Ankstesnis neigiamas algalapis ataskaitai."

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_tree
msgid "Print"
msgstr "Spausdinti"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_professionaltax1
msgid "Professional Tax"
msgstr "Profesiniai mokesčiai"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Prorated end-of-year bonus"
msgstr "Proporcingai apskaičiuota metų pabaigos premija"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_providentfund1
msgid "Provident Fund"
msgstr "Investicinis fondas"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_dashboard_warning__evaluation_code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__amount_python_compute
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_rule__amount_select__code
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_rule_filter
msgid "Python Code"
msgstr "Python kodas"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__condition_python
msgid "Python Condition"
msgstr "Python sąlyga"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_rule__condition_select__python
msgid "Python Expression"
msgstr "Python išraiška"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_rule_parameter_value__parameter_value
msgid "Python data structure"
msgstr "Python duomenų struktūra"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__quantity
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__quantity
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__quantity
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Quantity"
msgstr "Kiekis"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
msgid "Quantity/Rate"
msgstr "Kiekis / Tarifas"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Quarter %(quarter)s of %(year)s"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_contract__schedule_pay__quarterly
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__default_schedule_pay__quarterly
msgid "Quarterly"
msgstr "Kas ketvirtį"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__queued_for_pdf
msgid "Queued For Pdf"
msgstr "Eilėje dėl pdf"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_employee_declaration__state__pdf_to_generate
msgid "Queued PDF generation"
msgstr "PDF generavimas eilėje"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_rule__condition_select__range
msgid "Range"
msgstr "Intervalas"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__condition_range
msgid "Range Based on"
msgstr "Intervalas pagal"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_working_rate__rate
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Rate"
msgstr "Kursas"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__rate
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__rate
msgid "Rate (%)"
msgstr "Koeficientas (%)"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__rating_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__rating_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__rating_ids
msgid "Ratings"
msgstr "Įvertinimai"

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.action_hr_payroll_recompute_whole_sheet
msgid "Recompute Whole Sheet"
msgstr "Perskaičiuoti visą lapą"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Recompute the payslip lines only, not the worked days / input lines"
msgstr ""
"Perskaičiuokite tik algalapio eilutes, o ne darbo dienas / priskaitymo "
"eilutes"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_salary_attachment.py:0
msgid "Recorded a new payment of %s."
msgstr "Įrašytas naujas %s mokėjimas."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__number
msgid "Reference"
msgstr "Numeris"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_mixin__reference_month
msgid "Reference Month"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_mixin__reference_year
msgid "Reference Year"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Refund"
msgstr "Pinigų Grąžinimas"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Refund: %(payslip)s"
msgstr "Grąžinimas: %(payslip)s"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_search
msgid "Refunds"
msgstr "Kreditavimai"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__registration_number
msgid "Registration Number of the Employee"
msgstr "Darbuotojo registracijos numeris"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_type__default_struct_id
msgid "Regular Pay Structure"
msgstr "Įprasta darbo užmokesčio struktūra"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_report__work_type__1
msgid "Regular Working Day"
msgstr "Įprasta darbo diena"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
#: model:hr.salary.rule,name:hr_payroll.default_reimbursement_salary_rule
msgid "Reimbursement"
msgstr "Kompensacija"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_tree
msgid "Related Payslips"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__remaining_amount
msgid "Remaining Amount"
msgstr "Likusi suma"

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_salary_attachment_check_remaining_amount
msgid "Remaining amount must be positive."
msgstr "Likusi suma turi būti teigiama."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__remaining_amount
msgid "Remaining amount to be paid."
msgstr "Likusi suma, kurią reikia sumokėti."

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid "Remove \"Conflicting\" filter"
msgstr ""

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payment_report_wizard.py:0
msgid "Report Date"
msgstr ""

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_report
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_hr_work_entry_type_view_form_inherit_contract
msgid "Reporting"
msgstr "Ataskaitos"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_work_entry_export_mixin.py:0
msgid "Resolve Conflicts"
msgstr ""

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_resource_calendar
msgid "Resource Working Time"
msgstr "Ištekliaus darbo laikas"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_user_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__activity_user_id
msgid "Responsible User"
msgstr "Atsakingas vartotojas"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_type__round_days_type
msgid "Round Type"
msgstr "Apvalinimo tipas"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_type__round_days
msgid "Rounding"
msgstr "Apvalinimas"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__salary_rule_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__salary_rule_id
msgid "Rule"
msgstr "Taisyklė"

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_hr_salary_rule_category
msgid "Rule Categories"
msgstr "Taisyklių kategorijos"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "Rule Name"
msgstr "Taisyklės pavadinimas"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__rule_parameter_id
msgid "Rule Parameter"
msgstr "Taisyklės parametras"

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_action_hr_salary_rule_parameter
msgid "Rule Parameters"
msgstr "Taisyklės parametrai"

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_action_hr_salary_rule_form
msgid "Rules"
msgstr "Taisyklės"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_attachment__state__open
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_form
msgid "Running"
msgstr "Galiojanti"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__sim_card
msgid "SIM Card Copy"
msgstr "SIM kortelės kopija"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_has_sms_error
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__message_has_sms_error
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS pristatymo klaida"

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_hr_salary_configuration
msgid "Salary"
msgstr "Atlyginimas"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_contract.py:0
#: code:addons/hr_payroll/report/hr_contract_history.py:0
#: model:ir.actions.act_window,name:hr_payroll.action_hr_salary_attachment_new
#: model:ir.actions.act_window,name:hr_payroll.hr_salary_attachment_action
#: model:ir.actions.act_window,name:hr_payroll.hr_salary_attachment_action_view_employee
#: model:ir.model,name:hr_payroll.model_hr_salary_attachment
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_form
msgid "Salary Attachment"
msgstr "Atlyginimo priedas"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__salary_attachments_count
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract_history__salary_attachment_count
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__salary_attachment_count
msgid "Salary Attachment Count"
msgstr "Atlyginimo priedų skaičius"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__salary_attachment_count
msgid "Salary Attachment count"
msgstr "Atlyginimo priedų skaičius"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
#: code:addons/hr_payroll/models/hr_salary_attachment.py:0
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__salary_attachment_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__salary_attachment_ids
#: model:ir.ui.menu,name:hr_payroll.hr_menu_salary_attachments
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_history_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_tree
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Salary Attachments"
msgstr "Atlyginimo priedai"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_category_form
msgid "Salary Categories"
msgstr "Atlyginimo kategorijos"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Salary Computation"
msgstr "Atlyginimo skaičiavimas"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_salary_rule
msgid "Salary Rule"
msgstr "Atlyginimo taisyklė"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_hr_salary_rule_category
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_category_tree
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_salary_rule_category_filter
msgid "Salary Rule Categories"
msgstr "Atlyginimo taisyklių kategorijos"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_salary_rule_category
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_filter
msgid "Salary Rule Category"
msgstr "Atlyginimo taisyklių kategorija"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_rule_parameter
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_rule_parameter_view_form
msgid "Salary Rule Parameter"
msgstr "Atlyginimo taisyklių parametras"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_rule_parameter_value
msgid "Salary Rule Parameter Value"
msgstr "Atlyginimo taisyklių parametro vertė"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.hr_rule_parameter_action
msgid "Salary Rule Parameters"
msgstr "Atlyginimo taisyklių parametrai"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_salary_rule_form
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__rule_ids
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_list
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_employee_grade_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_rule_filter
msgid "Salary Rules"
msgstr "Atlyginimo taisyklės"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Salary Slip"
msgstr "Algalapis"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_structure
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__structure_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__struct_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_edit_payslip_lines_form_wizard
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_tree
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_tree_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_rule_filter
msgid "Salary Structure"
msgstr "Atlyginimo struktūra"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_structure_type
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__structure_type_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__structure_type_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_employee_tree_inherit
msgid "Salary Structure Type"
msgstr "Atlyginimo struktūros tipas"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_view_hr_payroll_structure_from_type
#: model:ir.actions.act_window,name:hr_payroll.action_view_hr_payroll_structure_list_form
msgid "Salary Structures"
msgstr "Atlyginimo struktūros"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__schedule_pay
msgid "Schedule Pay"
msgstr "Suplanuotas mokėjimas"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_employee_declaration_view_search
msgid "Search Declarations"
msgstr "Ieškoti deklaracijų"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_headcount_line_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_headcount_view_search
msgid "Search Headcount"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_dashboard_warning_view_search
msgid "Search Payroll Dashboard Warning"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_filter
msgid "Search Payslip Batches"
msgstr "Ieškoti algalapio suvestinių"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_filter
msgid "Search Payslip Lines"
msgstr "Ieškoti algalapio eilutėse"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Search Payslips"
msgstr "Ieškoti algalapių"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_search
msgid "Search Salary Attachment"
msgstr "Ieškoti atlyginimo priedų"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_rule_filter
msgid "Search Salary Rule"
msgstr "Ieškoti atlyginimo taisyklės"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_structure_type_view_search
msgid "Search Structure Type"
msgstr "Ieškoti struktūros tipo"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_contract__schedule_pay__semi-annually
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__default_schedule_pay__semi-annually
msgid "Semi-annually"
msgstr "Kas pusmetį"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_contract__schedule_pay__semi-monthly
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__default_schedule_pay__semi-monthly
msgid "Semi-monthly"
msgstr "kas pusmetį"

#. module: hr_payroll
#: model:mail.template,description:hr_payroll.mail_template_new_payslip
msgid "Sent to employee to notify them about their new payslip"
msgstr "Išsiųsta darbuotojui, kad praneštų jam apie naują algalapį"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_export_mixin__reference_month__9
#: model:ir.model.fields.selection,name:hr_payroll.selection__res_company__ytd_reset_month__9
msgid "September"
msgstr "Rugsėjis"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_dashboard_warning__sequence
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__sequence
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__sequence
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_type__sequence
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__sequence
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__sequence
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__sequence
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__sequence
msgid "Sequence"
msgstr "Seka"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
msgid ""
"Set a specific department if you wish to select all the employees from this "
"department (and subdepartments) at once."
msgstr ""
"Nustatykite konkretų skyrių, jei norite iš karto pasirinkti visus "
"darbuotojus iš šio skyriaus (ir poskyrių)."

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
msgid ""
"Set a specific job if you wish to select all the employees from this job at "
"once."
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
msgid ""
"Set a specific structure type if you wish to select all the employees from "
"this structure type at once."
msgstr ""

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid "Set the Contract as <strong><q>Running</q></strong>."
msgstr "Nustatyti sutartį kaip <strong><q>vykdomą</q></strong>."

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Set to Draft"
msgstr "Nustatyti kaip juodraštį"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_hr_payroll_configuration
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_global_settings
msgid "Settings"
msgstr "Nustatymai"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Slips to Confirm"
msgstr "Nepatvirtinti algalapiai"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payment_report_wizard.py:0
msgid "Some employees (%s) don't have a bank account."
msgstr "Kai kurie darbuotojai (%s) neturi nustatytos banko sąskaitos."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_work_entry_export_mixin.py:0
msgid ""
"Some work entries are in conflict. Please resolve the conflicts before "
"exporting."
msgstr ""

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payslips_by_employees.py:0
msgid "Some work entries could not be validated."
msgstr "Kai kurių darbo įrašų patvirtinti nepavyko."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__standard_calendar_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract_history__standard_calendar_id
msgid "Standard Calendar"
msgstr "Standartinis kalendorius"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__date_from
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__date_start
msgid "Start Date"
msgstr "Darbo pradžios data"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__state
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__state
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_employee_declaration_view_search
msgid "State"
msgstr "Būsena"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__state
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__state
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__state
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Status"
msgstr "Būsena"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__activity_state
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_run__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Būsena, paremta veiklomis\n"
"Vėluojantis: Termino data jau praėjo\n"
"Šiandien: Veikla turi būti baigta šiandien\n"
"Suplanuotas: Ateities veiklos."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__struct_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__struct_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Structure"
msgstr "Struktūra"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_employee_grade_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payroll_structure_filter
msgid "Structure Name"
msgstr "Struktūros pavadinimas"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_type__name
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_structure_type_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_structure_type_view_tree
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payroll_structure_filter
msgid "Structure Type"
msgstr "Struktūros tipas"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_type__struct_type_count
msgid "Structure Type Count"
msgstr "Struktūros tipų kiekis"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_hr_payroll_structure_type
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_structure_type
msgid "Structure Types"
msgstr "Struktūros tipai"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_type__struct_ids
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_structure_view
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_structure_type_view_form
msgid "Structures"
msgstr "Struktūros"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__sum_worked_hours
msgid "Sum Worked Hours"
msgstr "Darbo valandų suma"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_sum_alw_category
msgid "Sum of Allowance category"
msgstr "Pašalpos kategorijos suma"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_tree
msgid "Sum of Days"
msgstr "Dienų suma"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_dashboard_warning__color
msgid "Tag color. No color means black."
msgstr ""

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
#: model:hr.salary.rule,name:hr_payroll.default_gross_salary_rule
#: model:hr.salary.rule.category,name:hr_payroll.GROSS
msgid "Taxable Salary"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__report_id
msgid "Template"
msgstr "Šablonas"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__country_code
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_input_type__country_code
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_run__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"ISO šalies kodas dviem simboliais. \n"
"Galite naudoti šį lauką greitai paieškai."

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
msgid ""
"The Standard Calendar is the calendar used by the people working at a 100% "
"rate. It's used here to compute your part-time percentage."
msgstr ""
"Standartinis kalendorius yra naudojamas žmonėms, dirbantiems 100% darbo "
"laiku. Jis čia naudojamas ne viso jūsų darbo laiko procentui apskaičiuoti."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_work_entry_type__is_unforeseen
msgid ""
"The Work Entry checked as Unforeseen Absence will be counted in absenteeism "
"at work report."
msgstr ""
"Darbo įrašas, pažymėtas kaip nenumatytas neatvykimas, bus įskaičiuotas į "
"pravaikštų darbe ataskaitą."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/res_company.py:0
msgid ""
"The YTD reset day must be a valid day of the month : since the current month"
" is %(month)s, it should be between 1 and %(day)s."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_edit_payslip_line__code
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_line__code
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__code
msgid ""
"The code of salary rules can be used as reference in computation of other "
"rules. In that case, it is case sensitive."
msgstr ""
"Atlyginimo taisyklių kodas gali būti naudojamas kaip nuoroda kitų taisyklių "
"skaičiavimuose. Tokiu atveju, svarbu didžiosios ir mažosios raidės."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_input__code
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_input_type__code
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_worked_days__work_entry_type_id
msgid "The code that can be used in the salary rules"
msgstr "Kodas, kuris gali būti naudojamas atlyginimo taisyklėse"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_line__amount_select
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__amount_select
msgid "The computation method for the rule amount."
msgstr "Skaičiavimo būdas, naudojamas sumos apskaičiavimui."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_input__contract_id
msgid "The contract this input should be applied to"
msgstr "Sutartis, kuriai ši įvestis turėtų būti taikoma"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_worked_days__contract_id
msgid "The contract this worked days should be applied to"
msgstr "Sutartis, kuriai šios darbo dienos turėtų būti taikomos"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__paid_date
msgid "The date on which the payment is made to the employee."
msgstr "Mokėjimo darbuotojui data."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid ""
"The duration of the payslip is not accurate according to the structure type."
msgstr "Algalapio trukmė neatitinka struktūros tipo."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid ""
"The following employees have a contract outside of the payslip period:\n"
"%s"
msgstr ""
"Šie darbuotojai turi sutartį ne algalapio laikotarpiu:\n"
"%s"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid ""
"The following values are not valid:\n"
"%s"
msgstr ""
"Šios reikšmės negalioja:\n"
"%s"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__condition_range_max
msgid "The maximum amount, applied for this rule."
msgstr "Didžiausias kiekis, taikomas šiai taisyklei."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__condition_range_min
msgid "The minimum amount, applied for this rule."
msgstr "Didžiausias kiekis, taikomas šiai taisyklei."

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_light_payslip
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid ""
"The net amount will be recovered from the first positive remuneration "
"established after this."
msgstr ""
"Grynoji suma bus susigrąžinta iš pirmojo teigiamo atlygio, nustatyto po to."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "The payslips should be in Draft or Waiting state."
msgstr "Algalapio būsena turi būti juodraštis arba laukiama."

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/views/add_payslips_hook.js:0
msgid "The payslips(s) are now added to the batch"
msgstr " Algalapis(-iai) dabar pridėtas prie grupės."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "The period selected does not match the contract validity period."
msgstr "Pasirinktas laikotarpis nesutampa su sutarties galiojimo laikotarpiu."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "The selected payslips should be linked to the same batch"
msgstr ""

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_payroll_headcount_date_range
msgid "The start date must be anterior to the end date."
msgstr "Pradžios data turi būti ankstesnė už pabaigos datą."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_contract__time_credit_type_id
msgid ""
"The work entry type used when generating work entries to fit full time "
"working schedule."
msgstr ""
"Darbo įrašų tipas, naudojamas generuojant darbo įrašus, kad jie atitiktų "
"pilnos darbo dienos grafiką."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_work_entry_type__unpaid_structure_ids
msgid "The work entry won’t grant any money to employee in payslip."
msgstr "Darbo įrašas, kuris nesuteiks darbuotojui pinigų algalapyje."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payroll_declaration_mixin.py:0
msgid "There is no declaration to generate for the given period"
msgstr "Nurodytam laikotarpiui nereikia generuoti deklaracijos"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payment_report_wizard.py:0
msgid ""
"There is no valid payslip (done and net wage > 0) to generate the file."
msgstr ""

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payment_report_wizard.py:0
msgid "There should be at least one payslip to generate the file."
msgstr ""

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid ""
"There's no contract set on payslip %(payslip)s for %(employee)s. Check that "
"there is at least a contract set on the employee form."
msgstr ""

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "This action is forbidden on validated payslips."
msgstr "Šis veiksmas draudžiamas patvirtintuose algalapiuose."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "This action is restricted to payroll managers only."
msgstr "Šis veiksmas apribotas tik darbo užmokesčio vadybininkams."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_rule_parameter__code
#: model:ir.model.fields,help:hr_payroll.field_hr_rule_parameter_value__code
msgid "This code is used in salary rules to refer to this parameter."
msgstr ""
"Šis kodas naudojamas atlyginimo taisyklėse, norint nurodyti šį parametrą."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_input_type__struct_ids
msgid ""
"This input will be only available in those structure. If empty, it will be "
"available in all payslip."
msgstr ""
"Ši įvestis bus pasiekiama tik toje struktūroje. Jei jis tuščias, jis bus "
"pateiktas visuose algalapiuose."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_contract_history__time_credit
msgid "This is a credit time contract."
msgstr "Tai yra kompensacinio laiko sutartis."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_work_entry__is_credit_time
msgid "This is a credit time work entry."
msgstr "Tai yra kompensacinio laiko darbo įrašas."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "This payslip can be erroneous :"
msgstr "Šis algalapis gali būti klaidingas:"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_edit_payslip_lines_wizard.py:0
msgid "This payslip has been manually edited by %s."
msgstr "Šį algalapį rankiniu būdu redagavo %s."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "This payslip is not validated. This is not a legal document."
msgstr ""
"Šis atlyginimo lapelis nėra patvirtintas. Tai nėra teisinis dokumentas."

#. module: hr_payroll
#: model_terms:ir.actions.act_window,help:hr_payroll.payroll_report_action
msgid "This report performs analysis on your payslip."
msgstr "Šioje ataskaitoje atliekama jūsų algalapio analizė."

#. module: hr_payroll
#: model_terms:ir.actions.act_window,help:hr_payroll.hr_work_entry_report_action
msgid "This report performs analysis on your work entries."
msgstr "Šioje ataskaitoje atliekama jūsų darbo įrašų analizė."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__condition_range
msgid ""
"This will be used to compute the % fields values; in general it is on basic,"
" but you can also use categories code fields in lowercase as a variable "
"names (hra, ma, lta, etc.) and the variable basic."
msgstr ""
"Įrašykite lauko pavadinimą iš algalapio formos, kurio reikšmė bus tikrinama "
"ar atitinka intervalą. Vietoj algalapio formoje esančio lauko, galima "
"naudoti kategorijų kodus, įvedant juos mažosiomis raidėmis (hra, ma, lta, ir"
" t.t.)."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payslips_by_employees.py:0
msgid "Time intervals to look for:%s"
msgstr "Laiko intervalai, kurių reikia ieškoti: %s"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount__date_to
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__date_to
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__date_to
msgid "To"
msgstr "Iki"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "To Compute"
msgstr "Nepaskaičiuoti"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "To Confirm"
msgstr "Nepatvirtinti"

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_employee_payslips_to_pay
msgid "To Pay"
msgstr "Neapmokėti"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_light_payslip
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "To pay on"
msgstr "Sumokėti į"

#. module: hr_payroll
#: model_terms:ir.actions.act_window,help:hr_payroll.action_contribution_registers
msgid "To see something in this report, compute a payslip."
msgstr "Norėdami ką nors pamatyti šioje ataskaitoje, apskaičiuokite algalapį."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__total
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__total
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_resource_calendar_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Total"
msgstr "Suma"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__total_amount
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Total Amount"
msgstr "Visa suma"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Total Working Days"
msgstr "Viso darbo dienų"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Total Working Hours"
msgstr "Viso darbo valandų"

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_salary_attachment_check_total_amount
msgid ""
"Total amount must be strictly positive and greater than or equal to the "
"payslip amount."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__total_amount
msgid "Total amount to be paid."
msgstr "Visa mokėtina suma."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__sum_worked_hours
msgid "Total hours of attendance and time off (paid or not)"
msgstr "Bendra lankomumo ir laisvadienių trukmė (mokama ar ne)"

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_rule_parameter__unique
msgid "Two rule parameters cannot have the same code."
msgstr "Dvi taisyklės parametrai negali turėti to paties kodo."

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_rule_parameter_value__unique
msgid "Two rules with the same code cannot start the same day"
msgstr "Dvi taisyklės su tuo pačiu kodu negali prasidėti tą pačią dieną"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__type
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__type_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__struct_type_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__input_type_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__work_entry_type_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__other_input_type_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_search
msgid "Type"
msgstr "Tipas"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__activity_exception_decoration
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_run__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Įrašytos išimties veiklos tipas."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_type__is_unforeseen
msgid "Unforeseen Absence"
msgstr "Nenumatytas nedarbas"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Unknown State"
msgstr "Nežinoma valstija"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_hr_work_entry_type_view_form_inherit
msgid "Unpaid"
msgstr "Neapmokėta"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_report__work_type__3
msgid "Unpaid Time Off"
msgstr "Neapmokami laisvadieniai"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__unpaid_work_entry_type_ids
msgid "Unpaid Work Entry Type"
msgstr "Neapmokamas darbo įrašo tipas"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_employee_grade_form
msgid "Unpaid Work Entry Types"
msgstr "Neapmokami darbo įrašo tipai"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_type__unpaid_structure_ids
msgid "Unpaid in Structures Types"
msgstr "Neapmokami struktūros tipuose"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payment_report_wizard.py:0
msgid ""
"Untrusted bank account for the following employees:\n"
"%s"
msgstr ""
"Pas šiuos darbuotojus nustatyta nepatikima banko sąskaita :\n"
"%s"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_type__round_days_type__up
msgid "Up"
msgstr "Aukštyn"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__use_worked_day_lines
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__use_worked_day_lines
msgid "Use Worked Day Lines"
msgstr "Naudoti priskaičiuotų dienų eilutes"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_line__sequence
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__sequence
msgid "Use to arrange calculation sequence"
msgstr "Naudokite skaičiavimo sekos išdėstymui"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_line__appears_on_payslip
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__appears_on_payslip
msgid "Used to display the salary rule on payslip."
msgstr "Naudojama atlyginimo taisyklės rodymui algalapyje."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__appears_on_employee_cost_dashboard
msgid "Used to display the value in the employer cost dashboard."
msgstr "Naudojama vertės rodymui darbdavio sąnaudų suvestinėje."

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_res_users
msgid "User"
msgstr "Vartotojas"

#. module: hr_payroll
#: model:res.groups,comment:hr_payroll.group_hr_payroll_user
msgid "User can manage all contracts, work entries and create payslips."
msgstr ""
"Vartotojas gali tvarkyti visas sutartis, darbo įrašus ir sudaryti "
"algalapius."

#. module: hr_payroll
#: model:res.groups,comment:hr_payroll.group_hr_payroll_manager
msgid "User have full access on the application."
msgstr "Vartotojas turi visas teises DU aplikacijoje"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
msgid "Validate"
msgstr "Patvirtinti"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_edit_payslip_lines_form_wizard
msgid "Validate Edition"
msgstr "Patvirtinti redagavimą"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_report__state__validated
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_search
msgid "Validated"
msgstr "Patvirtinta"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__parameter_version_ids
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_rule_parameter_view_form
msgid "Versions"
msgstr "Versijos"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__appears_on_employee_cost_dashboard
msgid "View on Employer Cost Dashboard"
msgstr "Peržiūrėti darbdavio išlaidų informacijos skydelyje"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__appears_on_payroll_report
msgid "View on Payroll Reporting"
msgstr "Peržiūrėti darbo užmokesčio ataskaitas"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_view_kanban
msgid "Wage :"
msgstr "Darbo užmokestis:"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__wage_on_payroll
msgid "Wage On Payroll"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__wage_type
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract_history__wage_type
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__wage_type
msgid "Wage Type"
msgstr "Darbo užmokesčio rūšis"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_index_wizard.py:0
msgid "Wage indexed by %(percentage).2f%% on %(date)s"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payslip__state__verify
msgid "Waiting"
msgstr "Laukia"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_dashboard_warning__color
msgid "Warning Color"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__warning_message
msgid "Warning Message"
msgstr "Įspėjamasis pranešimas"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_salary_attachment.py:0
msgid "Warning, a similar attachment has been found."
msgstr "Įspėjimas, rastas panašus priedas."

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/components/dashboard/action_box/action_box.xml:0
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_dashboard_warning
msgid "Warnings"
msgstr "Įspėjimai"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_work_entry.py:0
msgid ""
"Watch out for gaps in %(employee_name)s's calendar\n"
"\n"
"Please complete the missing work entries of %(employee_name)s:%(time_intervals_str)s \n"
"\n"
"Missing work entries are like the Bermuda Triangle for paychecks. Let's keep your colleague's earnings from vanishing into thin air!"
msgstr ""
" Atkreipkite dėmesį į spragas %(employee_name)s kelendoriuje\n"
"\n"
" Prašome užpildyti trūkstamus %(employee_name)s darbo įrašus:%(time_intervals_str)s \n"
"\n"
" Trūkstami darbo įrašai yra kaip Bermudų trikampis algalapiams. Išsaugokime jūsų kolegos uždarbį nuo dingimo!"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_work_entry_type__round_days_type
msgid "Way of rounding the work entry type."
msgstr "Darbo įrašo tipo apvalinimo būdas."

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_note_demo_content
msgid "We have to improve our Payroll flow with the new Odoo process"
msgstr ""
"Turime pagerinti savo darbo užmokesčio skaičiavimo eigą naudodami naują Odoo"
" procesą"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__website_message_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__website_message_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__website_message_ids
msgid "Website Messages"
msgstr "Interneto svetainės žinutės"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__website_message_ids
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_run__website_message_ids
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__website_message_ids
msgid "Website communication history"
msgstr "Svetainės komunikacijos istorija"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Week %(week_number)s of %(year)s"
msgstr " %(week_number)s savaitė iš %(year)s"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_contract__schedule_pay__weekly
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__default_schedule_pay__weekly
msgid "Weekly"
msgstr "Kartą per savaitę"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Weeks %(week)s and %(week1)s of %(year)s"
msgstr "Savaitės %(week)s ir %(week1)s iš %(year)s"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_work_entry_type__round_days
msgid ""
"When the work entry is displayed in the payslip, the value is rounded "
"accordingly."
msgstr "Kai darbo įrašas rodomas algalapyje, vertė atitinkamai suapvalinama."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_contract__calendar_changed
msgid "Whether the previous or next contract has a different schedule or not"
msgstr "Nesvarbu ankstesnė ar kita sutartis turi skirtingą tvarkaraštį, ar ne"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_index__description
msgid ""
"Will be used as the message specifying why the wage on the contract has been"
" modified"
msgstr ""
"Bus naudojamas kaip pranešimas, nurodantis, kodėl sutartyje nurodytas darbo "
"užmokestis buvo pakeistas"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__count_work
msgid "Work Days"
msgstr "Darbo dienos"

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_work_entries_root
msgid "Work Entries"
msgstr "Darbo įrašai"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.hr_work_entry_report_action
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_tree
msgid "Work Entries Analysis"
msgstr "Darbo įrašų analizė"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_work_entry_report
msgid "Work Entries Analysis Report"
msgstr "Darbo įrašų analizės ataskaita"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_work_entry_export_mixin.py:0
msgid "Work Entries Export"
msgstr ""

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid ""
"Work Entries are generated for each <strong>time period</strong> defined in "
"the Working Schedule of the Contract."
msgstr ""
" Darbo įrašai generuojami kiekvienam <strong>laiko periodui</strong>, "
"apibrėžtam sutarties darbo grafike."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_work_entry_export_mixin.py:0
msgid "Work Entries for %(employee)s"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_employee_mixin__work_entry_ids
msgid "Work Entry"
msgstr ""

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_hr_work_entry_report
msgid "Work Entry Analysis"
msgstr "Darbo įrašo analizė"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_work_entry_export_employee_mixin
msgid "Work Entry Export Employee"
msgstr ""

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_work_entry_export_mixin
msgid "Work Entry Export Mixin"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__work_entry_source
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__work_entry_source
msgid "Work Entry Source"
msgstr "Darbo įrašo šaltinis"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__work_entry_type_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__work_entry_type_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_search
msgid "Work Entry Type"
msgstr "Darbo įrašo tipas"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__count_work_hours
msgid "Work Hours"
msgstr "Darbo valandos"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Work Permit Expiration Notice Period"
msgstr "Darbo leidimo galiojimo pabaigos įspėjimo laikotarpis"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_resource_calendar__work_time_rate
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_resource_calendar_view_form
msgid "Work Time Rate"
msgstr "Darbo laiko proporcija"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid ""
"Work entries may not be generated for the period from %(start)s to %(end)s."
msgstr ""
"Darbo įrašai negali būti generuojami laikotarpiui nuo %(start)s iki %(end)s."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_structure_type__default_work_entry_type_id
msgid "Work entry type for regular attendances."
msgstr "Darbo įrašo tipas nuolatiniam lankymui."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__work_time_rate
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract_history__work_time_rate
msgid "Work time rate"
msgstr "Darbo laiko norma"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_resource_calendar__work_time_rate
msgid ""
"Work time rate versus full time working schedule, should be between 0 and "
"100 %."
msgstr ""
"Darbo laiko norma, lyginant su pilnu etatu, turi būti nuo 0 iki 100 %."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_contract__work_time_rate
#: model:ir.model.fields,help:hr_payroll.field_hr_contract_history__work_time_rate
msgid "Work time rate versus full time working schedule."
msgstr "Darbo laiko norma, palyginti su viso etato darbo grafiku."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__work_code
msgid "Work type"
msgstr "Darbo tipas"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__work_type
msgid "Work, (un)paid Time Off"
msgstr "Darbas, (ne)apmokamos atostogos"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Worked Day"
msgstr "Dirbta diena"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_edit_payslip_lines_form_wizard
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Worked Days"
msgstr "Dirbtos dienos"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Worked Days & Inputs"
msgstr "Darbo dienų priskaitymai / atskaitymai"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__worked_days_line_ids
msgid "Worked Days Lines"
msgstr "Dirbtų dienų eilutės"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_structure__use_worked_day_lines
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__use_worked_day_lines
msgid "Worked days won't be computed/displayed in payslips."
msgstr "Priskaičiuotos dienos nebus skaičiuojamos/rodomos algalapyje."

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_headcount_working_rate
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__working_rate_ids
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_headcount_line_search
msgid "Working Rate"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__resource_calendar_id
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_report__work_entry_source__calendar
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_report__work_entry_source__calendar
msgid "Working Schedule"
msgstr "Darbo grafikas"

#. module: hr_payroll
#: model:hr.payroll.dashboard.warning,name:hr_payroll.hr_payroll_dashboard_warning_working_schedule_change
msgid "Working Schedule Changes"
msgstr "Darbo grafiko pakeitimai"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_salary_rule.py:0
msgid "Wrong percentage base or quantity defined for:"
msgstr "Neteisingai nustatyta procentinė bazė arba kiekis:"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_salary_rule.py:0
msgid "Wrong python code defined for:"
msgstr "Neteisingai nustatytas „Python“ kodas:"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_salary_rule.py:0
msgid "Wrong python condition defined for:"
msgstr "Neteisingai nustatyta „Python“ sąlyga:"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_salary_rule.py:0
msgid "Wrong quantity defined for:"
msgstr "Neteisingas kiekis nustatytas:"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_salary_rule.py:0
msgid "Wrong range condition defined for:"
msgstr "Neteisinga diapazono sąlyga nustatyta:"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_rule_parameter.py:0
msgid ""
"Wrong rule parameter value for %(rule_parameter_name)s at date %(date)s.\n"
"%(error)s"
msgstr ""

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid ""
"Wrong warning computation code defined for:\n"
"- Warning: %(warning)s\n"
"- Error: %(error)s"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__ytd
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__ytd
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__ytd
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__ytd
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "YTD"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "YTD Reset Date"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_res_company__ytd_reset_day
#: model:ir.model.fields,field_description:hr_payroll.field_res_config_settings__ytd_reset_day
msgid "YTD Reset Day of the month"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_res_company__ytd_reset_month
#: model:ir.model.fields,field_description:hr_payroll.field_res_config_settings__ytd_reset_month
msgid "YTD Reset Month"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_declaration_mixin__year
msgid "Year"
msgstr "Metai"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__ytd_computation
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__ytd_computation
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__ytd_computation
msgid "Year to Date Computation"
msgstr ""

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/res_users.py:0
msgid ""
"You are receiving this message because you are the HR Responsible of this "
"employee."
msgstr "Gavote šį pranešimą, nes esate persona atsakinga už šį darbuotoją."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip_run.py:0
msgid ""
"You can't delete a batch with payslips if they are not draft or cancelled."
msgstr ""

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "You can't validate a cancelled payslip."
msgstr "Negalite tvirtinti atšaukto algalapio"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip_input_type.py:0
msgid ""
"You cannot delete %s as it is used in another module but you can archive it "
"instead."
msgstr ""
"Negalite ištrinti %s, nes jis naudojamas kitame modulyje, bet galite jį "
"archyvuoti."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "You cannot delete a payslip which is not draft or cancelled!"
msgstr "Negalite ištrinti algalapio, kuris nėra juodraštis arba atšauktas!"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_salary_attachment.py:0
msgid "You cannot delete a running salary attachment!"
msgstr "Negalite ištrinti veikiančio atlyginimo priedo!"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_salary_attachment.py:0
msgid "You cannot record a payment on multi employees attachments."
msgstr "Negalite įrašyti mokėjimo iš kart kelių darbuotojų prieduose."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip_run.py:0
msgid ""
"You cannot reset a batch to draft if some of the payslips have already been "
"paid."
msgstr ""
"Negalite nustatyti visos grupės į juodraštį, jei kai kurie algalapiai jau "
"buvo apmokėti."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "You cannot validate a payslip on which the contract is cancelled"
msgstr ""
"Jūs negalite patvirtinti algalapio, jei sutartis su darbuotoju yra nutraukta"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_index_wizard.py:0
msgid ""
"You have selected non running contracts, if you really need to index them, "
"please do it by hand"
msgstr ""
"Pasirinkote neveikiančias sutartis, jei tikrai reikia jas indeksuoti, "
"padarykite tai ranka"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_work_entry_export_mixin.py:0
msgid "You must be logged in a %(country_code)s company to use this feature"
msgstr ""

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payroll_declaration_mixin.py:0
msgid "You must be logged in a %s company to use this feature"
msgstr ""
"Kad galėtumėte naudotis šia funkcija, turite būti prisijungę prie %s įmonės"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payslips_by_employees.py:0
msgid "You must select employee(s) to generate payslip(s)."
msgstr ""
"Norėdami generuoti algalapį (-ius), turite pasirinkti darbuotoją (-us)."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip_line.py:0
msgid "You must set a contract to create a payslip line."
msgstr "Norėdami sukurti algalapio eilutę, turite nustatyti sutartį."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payroll_structure_type.py:0
msgid "You should also be logged into a company in %s to set this country."
msgstr "Kad galėtumėte nustatyti šalį, turite būti prisijungę prie %s įmonės."

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_export_employee_mixin_list_view
msgid "contracts"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_type__current_companies_country_codes
msgid "country codes"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
msgid "e.g. April 2021"
msgstr "pvz. Balandis 2021"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_input_type_view_form
msgid "e.g. Child Support"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_structure_type_view_form
msgid "e.g. Employee"
msgstr "pvz. Darbuotojas"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_dashboard_warning_view_form
msgid "e.g. Employee Without Contracts"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "e.g. Net"
msgstr "pvz. Neto"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "e.g. Net Salary"
msgstr "pvz. Neto atlyginimas"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_employee_grade_form
msgid "e.g. Regular Pay"
msgstr "pvz. Reguliarus atlyginimas"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__headcount_id
msgid "headcount_id"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_note_demo_content
msgid "https://www.odoo.com/fr_FR/slides/slide/manage-payroll-1002"
msgstr "https://www.odoo.com/fr_FR/slides/slide/manage-payroll-1002"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_light_payslip
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "of"
msgstr "iš"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__amount_percentage_base
msgid "result will be affected to a variable"
msgstr "rezultatas bus paveiktas kintamojo"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_light_payslip
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "xxxxxxxxxxxx"
msgstr "xxxxxxxxxxxx"

#. module: hr_payroll
#: model:mail.template,subject:hr_payroll.mail_template_new_payslip
msgid "{{ object.employee_id.name }}, a new payslip is available for you"
msgstr ""
"{{ object.employee_id.name }}, jums prieinamas naujas atlyginimo lapelis"
