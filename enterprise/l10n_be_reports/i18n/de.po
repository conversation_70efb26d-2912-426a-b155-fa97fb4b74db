# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_be_reports
#
# Translators:
# <PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON><PERSON>Nesselbosch, 2023
# <PERSON>, 2023
# <PERSON><PERSON>, 2023
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.5alpha1+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-08 09:25+0000\n"
"PO-Revision-Date: 2024-08-08 09:25+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: \n"
"Language: de\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.4.1\n"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_a_plcf
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_a_plcf
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_a_plcf
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_a_plcf
msgid "(14) - Profit (Loss) to Be Carried Forward (+)/(-)"
msgstr "(14) - Vorzutragender Gewinn (Verlust) (+)/(-)"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_a_pltba_plpafa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_a_pltba_plpafa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_a_pltba_plpafa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_a_pltba_plpafa
msgid ""
"(9905) - Profit (Loss) of the Period Available for Appropriation (+)/(-)"
msgstr ""
"(9905) - Zu verwendender Gewinn (anzurechnender Verlust) des Geschäftsjahres "
"(+)/(-)"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_281_50_pdf
msgid "(including amounts that are related to other taxable periods):"
msgstr ""
"tatsächlich gezahlten Betrag ein (einschließlich der Beträge, die sich auf "
"andere Besteuerungszeiträume beziehen):"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_281_50_pdf
msgid ""
",\n"
"                                                enter the amount actually "
"paid in"
msgstr ""
",\n"
"tragen Sie hier den"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_281_50_pdf
msgid "0.00"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_281_50_pdf
msgid ""
"0.00\n"
"                                                <br/><br/>"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "1. NN ou NE:"
msgstr "1. NN oder UN:"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_281_50_pdf
msgid "1. N°"
msgstr "1. Nr."

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_e_aff
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_e_aff
msgid "10 - Association or Foundation Funds"
msgstr "10 - Vermögen der Vereinigung oder Stiftung"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_c_c
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_c_c
msgid "10 - Capital"
msgstr "10 - Kapital"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_c
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e_c
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_c
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e_c
msgid "10/11 - Contributions"
msgstr "10/11 - Einlage"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_e
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_e
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e
msgid "10/15 - Equity"
msgstr "10/15 - Eigenkapital"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_tot
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_tot
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_tot
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_tot
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_tot
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_tot
msgid "10/49 - TOTAL LIABILITIES"
msgstr "10/49 - SUMME DER PASSIVA"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_c_c_ic
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_c_c_ic
msgid "100 - Issued Capital"
msgstr "100 - Gezeichnetes Kapital"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_c_c_uc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_c_c_uc
msgid "101 - Uncalled Capital"
msgstr "101 - Nicht eingefordertes Kapital"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_c_bc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_c_bc
msgid "11 - Beyond Capital"
msgstr "11 - Sacheinlage"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e_c_a
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e_c_a
msgid "110 - Available"
msgstr "110 - Verfügbar"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_c_bc_spa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_c_bc_spa
msgid "1100/10 - Share Premium Account"
msgstr "1100/10 - Agio"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_c_bc_o
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_c_bc_o
msgid "1109/19 - Other"
msgstr "1109/19 - Sonstige"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e_c_na
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e_c_na
msgid "111 - Not Available"
msgstr "111 - Nicht verfügbar"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_e_rs
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_e_rs
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_rs
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e_rs
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_rs
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e_rs
msgid "12 - Revaluation Surpluses"
msgstr "12 - Neubewertungsrücklagen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_e_for
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_e_for
msgid "13 - Funds and Other Reserves"
msgstr "13 - Zweckgebundenes Vermögen und sonstige Rücklagen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_r
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e_r
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_r
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e_r
msgid "13 - Reserves"
msgstr "13 - Rücklagen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_r_rno_lr
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_r_rno_lr
msgid "130 - Legal Reserve"
msgstr "130 - Gesetzliche Rücklage"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_r_rno
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e_r_rno
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_r_rno
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e_r_rno
msgid "130/1 - Reserves Not Available"
msgstr "130/1 - Nicht verfügbare Rücklagen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_r_rno_rnos
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e_r_rno_rnos
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_r_rno_rnos
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e_r_rno_rnos
msgid "1311 - Reserves Not Available Statutorily"
msgstr "1311 - Satzungsgemäße nicht verfügbare Rücklagen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_r_rno_pos
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e_r_rno_pos
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_r_rno_pos
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e_r_rno_pos
msgid "1312 - Purchase of Own Shares"
msgstr "1312 - Erwerb eigener Aktien"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_r_rno_fs
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e_r_rno_fs
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_r_rno_fs
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e_r_rno_fs
msgid "1313 - Financial Support"
msgstr "1313 - Finanzielle Unterstützung"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_r_rno_o
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e_r_rno_o
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_r_rno_o
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e_r_rno_o
msgid "1319 - Other"
msgstr "1319 - Sonstige"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_r_ur
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e_r_ur
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_r_ur
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e_r_ur
msgid "132 - Untaxed Reserves"
msgstr "132 - Steuerfreie Rücklagen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_r_ar
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e_r_ar
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_r_ar
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e_r_ar
msgid "133 - Available Reserves"
msgstr "133 - Verfügbare Rücklagen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_e_apl
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_e_apl
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_apl
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e_apl
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_apl
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e_apl
msgid "14 - Accumulated Profits (Losses) (+)/(-)"
msgstr "14 - Gewinnvortrag (Verlustvortrag) (+)/(-)"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_a_pltba_plppbf
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_a_pltba_plppbf
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_a_pltba_plppbf
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_a_pltba_plppbf
msgid "14P - Profit (Loss) of the Preceding Period Brought Forward (+)/(-)"
msgstr "14P - Gewinnvortrag (Verlustvortrag) aus dem Vorjahr (+)/(-)"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_e_cs
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_e_cs
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_cs
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e_cs
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_cs
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e_cs
msgid "15 - Capital Subsidies"
msgstr "15 - Kapitalsubventionen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_pdt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_pdt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_pdt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_pdt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_pdt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_pdt
msgid "16 - Provisions and Deferred Taxes"
msgstr "16 - Rückstellungen und aufgeschobene Steuern"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_pdt_plc_pso
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_pdt_plc_pso
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_pdt_plc_pso
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_pdt_plc_pso
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_pdt_plc_pso
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_pdt_plc_pso
msgid "160 - Pensions and Similar Obligations"
msgstr "160 - Pensionen und ähnliche Verpflichtungen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_pdt_plc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_pdt_plc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_pdt_plc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_pdt_plc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_pdt_plc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_pdt_plc
msgid "160/5 - Provisions for Liabilities and Charges"
msgstr "160/5 - Rückstellungen für Risiken und Aufwendungen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_pdt_plc_t
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_pdt_plc_t
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_pdt_plc_t
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_pdt_plc_t
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_pdt_plc_t
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_pdt_plc_t
msgid "161 - Taxes"
msgstr "161 - Steuern"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_pdt_plc_mrm
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_pdt_plc_mrm
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_pdt_plc_mrm
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_pdt_plc_mrm
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_pdt_plc_mrm
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_pdt_plc_mrm
msgid "162 - Major Repairs and Maintenance"
msgstr "162 - Große Reparaturen und Instandhaltungsarbeiten"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_pdt_plc_eo
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_pdt_plc_eo
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_pdt_plc_eo
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_pdt_plc_eo
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_pdt_plc_eo
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_pdt_plc_eo
msgid "163 - Environmental Obligations"
msgstr "163 - Umweltschutzverpflichtungen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_pdt_plc_olc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_pdt_plc_olc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_pdt_plc_olc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_pdt_plc_olc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_pdt_plc_olc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_pdt_plc_olc
msgid "164/5 - Other Liabilities and Charges"
msgstr "164/5 - Sonstige Risiken und Aufwendungen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_pdt_plc_pslrgrr
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_pdt_plc_pslrgrr
msgid ""
"167 - Provisions for Subsidies and Legacies to Reimburse and Gifts With a "
"Recovery Right"
msgstr ""
"167 - Rückstellungen für zurückzuzahlende Subventionen und Legate und für "
"Schenkungen mit Rücknahmerecht"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_pdt_dt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_pdt_dt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_pdt_dt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_pdt_dt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_pdt_dt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_pdt_dt
msgid "168 - Deferred Taxes"
msgstr "168 - Aufgeschobene Steuern"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apamtoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apamtoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apamtoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apamtoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apamtoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apamtoy
msgid "17 - Amounts Payable After More Than One Year"
msgstr "17 - Verbindlichkeiten mit einer Restlaufzeit von mehr als einem Jahr"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap
msgid "17/49 - Amounts Payable"
msgstr "17/49 - Verbindlichkeiten"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apamtoy_fd_sl
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apamtoy_fd_sl
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apamtoy_fd_sl
msgid "170 - Subordinated Loans"
msgstr "170 - Nachrangige Anleihen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apamtoy_fd
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apamtoy_fd
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apamtoy_fd
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apamtoy_fd
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apamtoy_fd
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apamtoy_fd
msgid "170/4 - Financial Debts"
msgstr "170/4 - Finanzverbindlichkeiten"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apamtoy_fd_ud
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apamtoy_fd_ud
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apamtoy_fd_ud
msgid "171 - Unsubordinated Debentures"
msgstr "171 - Nicht nachrangige Anleihen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apamtoy_fd_loso
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apamtoy_fd_loso
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apamtoy_fd_loso
msgid "172 - Leasing and Other Similar Obligations"
msgstr "172 - Verbindlichkeiten aufgrund von Leasing- und ähnlichen Verträgen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apamtoy_fd_ciloso
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apamtoy_fd_ciloso
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apamtoy_fd_ciloso
msgid "172/3 - Credit Institutions, Leasing and Other Similar Obligations"
msgstr ""
"172/3 - Kreditinstitute, Verbindlichkeiten aufgrund von Leasing- und "
"ähnlichen Verträgen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apamtoy_fd_ci
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apamtoy_fd_ci
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apamtoy_fd_ci
msgid "173 - Credit Institutions"
msgstr "173 - Kreditinstitute"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apamtoy_fd_ol
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apamtoy_fd_ol
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apamtoy_fd_ol
msgid "174 - Other Loans"
msgstr "174 - Sonstige Anleihen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apamtoy_fd_ol
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apamtoy_fd_ol
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apamtoy_fd_ol
msgid "174/0 - Other Loans"
msgstr "174/0 - Sonstige Anleihen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apamtoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apamtoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apamtoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apamtoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apamtoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apamtoy_td
msgid "175 - Trade Debts"
msgstr "175 - Verbindlichkeiten aus Lieferungen und Leistungen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apamtoy_td_s
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apamtoy_td_s
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apamtoy_td_s
msgid "1750 - Suppliers"
msgstr "1750 - Lieferanten"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apamtoy_td_bep
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apamtoy_td_bep
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apamtoy_td_bep
msgid "1751 - Bills of Exchange Payable"
msgstr "1751 - Verbindlichkeiten aus Wechseln"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apamtoy_apcp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apamtoy_apcp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apamtoy_apcp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apamtoy_apcp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apamtoy_apcp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apamtoy_apcp
msgid "176 - Advance Payments on Contracts in Progress"
msgstr "176 - Anzahlungen auf Bestellungen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apamtoy_oap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apamtoy_oap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apamtoy_oap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apamtoy_oap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apamtoy_oap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apamtoy_oap
msgid "178/9 - Other Amounts Payable"
msgstr "178/9 - Sonstige Verbindlichkeiten"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_asdna
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e_asdna
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_asdna
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e_asdna
msgid "19 - Advance to Shareholders on the Distribution of Net Assets"
msgstr ""
"19 - Vorschuss an die Gesellschafter auf der Verteilung des Nettoaktiva"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_281_50_pdf
msgid "2. Name (or denomination) and address of the debtor of the income:"
msgstr "2. Name (oder Bezeichnung) und Anschrift des Schuldners der Einkünfte:"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "2. Phone number:"
msgstr "2. Telefonnummer:"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_fe
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fe
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_fe
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_fe
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fe
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fe
msgid "20 - Formation Expenses"
msgstr "20 - Errichtungs- und Erweiterungsaufwendungen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_tot
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_tot
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_tot
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_tot
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_tot
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_tot
msgid "20/58 - TOTAL ASSETS"
msgstr "20/58 - SUMME DER AKTIVA"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_fa_ifa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa_ifa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_fa_ifa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_fa_ifa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa_ifa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa_ifa
msgid "21 - Intangible Fixed Assets"
msgstr "21 - Immaterielle Anlagewerte"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_fa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_fa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_fa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa
msgid "21/28 - Fixed Assets"
msgstr "21/28 - Anlagevermögen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_fa_tfa_lb
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa_tfa_lb
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_fa_tfa_lb
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_fa_tfa_lb
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa_tfa_lb
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa_tfa_lb
msgid "22 - Land and Buildings"
msgstr "22 - Grundstücke und Bauten"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_fa_tfa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa_tfa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_fa_tfa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_fa_tfa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa_tfa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa_tfa
msgid "22/27 - Tangible Fixed Assets"
msgstr "22/27 - Sachanlagen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_fa_tfa_pme
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa_tfa_pme
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_fa_tfa_pme
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_fa_tfa_pme
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa_tfa_pme
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa_tfa_pme
msgid "23 - Plant, Machinery and Equipment"
msgstr "23 - Anlagen, Maschinen und Betriebsausstattung"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_fa_tfa_fv
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa_tfa_fv
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_fa_tfa_fv
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_fa_tfa_fv
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa_tfa_fv
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa_tfa_fv
msgid "24 - Furniture and Vehicles"
msgstr "24 - Geschäftsausstattung und Fuhrpark"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_fa_tfa_losr
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa_tfa_losr
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_fa_tfa_losr
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_fa_tfa_losr
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa_tfa_losr
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa_tfa_losr
msgid "25 - Leasing and Other Similar Rights"
msgstr "25 - Leasing und ähnliche Rechte"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_fa_tfa_otfa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa_tfa_otfa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_fa_tfa_otfa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_fa_tfa_otfa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa_tfa_otfa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa_tfa_otfa
msgid "26 - Other Tangible Fixed Assets"
msgstr "26 - Sonstige Sachanlagen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_fa_tfa_aucap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa_tfa_aucap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_fa_tfa_aucap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_fa_tfa_aucap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa_tfa_aucap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa_tfa_aucap
msgid "27 - Assets Under Construction and Advance Payments"
msgstr "27 - Anlagen im Bau und geleistete Anzahlungen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_fa_ffa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa_ffa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_fa_ffa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_fa_ffa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa_ffa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa_ffa
msgid "28 - Financial Fixed Assets"
msgstr "28 - Finanzanlagen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa_ffa_ac_pi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa_ffa_ac_pi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa_ffa_ac_pi
msgid "280 - Participating Interests"
msgstr "280 - Beteiligungen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa_ffa_ac
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa_ffa_ac
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa_ffa_ac
msgid "280/1 - Affiliated Companies"
msgstr "280/1 - Verbundene Unternehmen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa_ffa_ac_ar
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa_ffa_ac_ar
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa_ffa_ac_ar
msgid "281 - Amounts Receivable"
msgstr "281 - Forderungen"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "281.10"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "281.11"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "281.12"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "281.13"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "281.14"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "281.15"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "281.16"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "281.17"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "281.18"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "281.20"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "281.30"
msgstr ""

#. module: l10n_be_reports
#: model:res.partner.category,name:l10n_be_reports.res_partner_tag_281_50
msgid "281.50"
msgstr ""

#. module: l10n_be_reports
#: model:account.account.tag,name:l10n_be_reports.account_tag_281_50_atn
msgid "281.50 - ATN"
msgstr "281.50 - VJA"

#. module: l10n_be_reports
#: model:account.account.tag,name:l10n_be_reports.account_tag_281_50_commissions
msgid "281.50 - Commissions"
msgstr "281.50 - Provisionen"

#. module: l10n_be_reports
#: model:account.account.tag,name:l10n_be_reports.account_tag_281_50_exposed_expenses
msgid "281.50 - Exposed Expenses"
msgstr "281.50 - Ausgelegte Kosten"

#. module: l10n_be_reports
#: model:account.account.tag,name:l10n_be_reports.account_tag_281_50_fees
msgid "281.50 - Fees"
msgstr "281.50 - Honorare"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.form_281_50_view_form
msgid "281.50 Form"
msgstr "Karte 281.50"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.form_325_view_form
msgid "281.50 Forms"
msgstr "Karten 281.50"

#. module: l10n_be_reports
#: model:ir.actions.report,name:l10n_be_reports.action_report_partner_281_50_pdf
msgid "281.50 PDF"
msgstr "281.50 PDF"

#. module: l10n_be_reports
#: model:ir.actions.report,name:l10n_be_reports.action_report_partner_281_50_xml
msgid "281.50 XML"
msgstr "281.50 XML"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_res_partner__forms_281_50
#: model:ir.model.fields,field_description:l10n_be_reports.field_res_users__forms_281_50
msgid "281.50 forms"
msgstr "Karten 281.50"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa_ffa_oclpi_pi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa_ffa_oclpi_pi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa_ffa_oclpi_pi
msgid "282 - Participating Interests"
msgstr "282 - Beteiligungen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa_ffa_oclpi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa_ffa_oclpi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa_ffa_oclpi
msgid "282/3 - Other Companies Linked by Participating Interests"
msgstr ""
"282/3 - Andere Unternehmen, mit denen ein Beteiligungsverhältnis besteht"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa_ffa_oclpi_ar
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa_ffa_oclpi_ar
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa_ffa_oclpi_ar
msgid "283 - Amounts Receivable"
msgstr "283 - Forderungen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa_ffa_offa_s
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa_ffa_offa_s
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa_ffa_offa_s
msgid "284 - Shares"
msgstr "284 - Aktien oder Anteile"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa_ffa_offa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa_ffa_offa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa_ffa_offa
msgid "284/8 - Other Financial Fixed Assets"
msgstr "284/8 - Sonstige Finanzanlagen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa_ffa_offa_arcg
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa_ffa_offa_arcg
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa_ffa_offa_arcg
msgid "285/8 - Amounts Receivable and Cash Guarantees"
msgstr "285/8 - Forderungen und gezahlte Kautionen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_ca_aramtoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca_aramtoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_ca_aramtoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_ca_aramtoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_aramtoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_aramtoy
msgid "29 - Amounts Receivable After More Than One Year"
msgstr "29 - Forderungen mit einer Restlaufzeit von mehr als einem Jahr"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_ca
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_ca
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_ca
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca
msgid "29/58 - Current Assets"
msgstr "29/58 - Umlaufvermögen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_ca_aramtoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca_aramtoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_ca_aramtoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_ca_aramtoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_aramtoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_aramtoy_td
msgid "290 - Trade Debtors"
msgstr "290 - Forderungen aus Lieferungen und Leistungen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_ca_aramtoy_oar
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca_aramtoy_oar
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_ca_aramtoy_oar
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_ca_aramtoy_oar
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_aramtoy_oar
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_aramtoy_oar
msgid "291 - Other Amounts Receivable"
msgstr "291 - Sonstige Forderungen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_ca_scp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca_scp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_ca_scp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_ca_scp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_scp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_scp
msgid "3 - Stocks and Contracts in Progress"
msgstr "3 - Vorräte und in Ausführung befindliche Bestellungen"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "3. Identity of the debtor of the income"
msgstr "3. Identität des Schuldners der Einkünfte"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_281_50_pdf
msgid "3. Nature"
msgstr "3. Art"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca_scp_s_rmc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_scp_s_rmc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_scp_s_rmc
msgid "30/31 - Raw Materials and Consumables"
msgstr "30/31 - Roh-, Hilfs- und Betriebsstoffe"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_ca_scp_s
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca_scp_s
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_ca_scp_s
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_ca_scp_s
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_scp_s
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_scp_s
msgid "30/36 - Stocks"
msgstr "30/36 - Vorräte"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca_scp_s_wip
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_scp_s_wip
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_scp_s_wip
msgid "32 - Work in Progress"
msgstr "32 - Unfertige Erzeugnisse"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.form_325_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.form_325_view_tree
msgid "325 Form"
msgstr "Verzeichnis 325"

#. module: l10n_be_reports
#: model:ir.model,name:l10n_be_reports.model_l10n_be_form_325_wizard
msgid "325 Form Wizard"
msgstr "Verzeichnis 325 Assistent"

#. module: l10n_be_reports
#: model:ir.actions.report,name:l10n_be_reports.action_report_325_form_pdf
msgid "325 PDF"
msgstr "325 PDF"

#. module: l10n_be_reports
#: model:ir.actions.act_window,name:l10n_be_reports.action_open_325_tree_view
msgid "325 forms"
msgstr "Verzeichnisse 325"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca_scp_s_fg
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_scp_s_fg
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_scp_s_fg
msgid "33 - Finished Goods"
msgstr "33 - Fertige Erzeugnisse"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca_scp_s_gpr
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_scp_s_gpr
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_scp_s_gpr
msgid "34 - Goods Purchased for Resale"
msgstr "34 - Waren"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca_scp_s_ipis
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_scp_s_ipis
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_scp_s_ipis
msgid "35 - Immovable Property Intended for Sale"
msgstr "35 - Zum Verkauf bestimmte unbewegliche Gegenstände"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca_scp_s_ap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_scp_s_ap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_scp_s_ap
msgid "36 - Advance Payments"
msgstr "36 - Geleistete Anzahlungen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_ca_scp_cp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca_scp_cp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_ca_scp_cp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_ca_scp_cp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_scp_cp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_scp_cp
msgid "37 - Contracts in Progress"
msgstr "37 - In Ausführung befindliche Bestellungen"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_281_50_pdf
msgid "4. Comments:"
msgstr "4. Kommentar:"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid ""
"4. Domicile (natural persons), registered office or main administrative\n"
"                                                office (companies and other "
"institutions)"
msgstr ""
"4. Wohnsitz (natürliche Personen), Gesellschaftssitz oder Hauptniederlassung "
"(Gesellschaften und andere Einrichtungen):"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_ca_arwoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca_arwoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_ca_arwoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_ca_arwoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_arwoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_arwoy_td
msgid "40 - Trade Debtors"
msgstr "40 - Forderungen aus Lieferungen und Leistungen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_ca_arwoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca_arwoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_ca_arwoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_ca_arwoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_arwoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_arwoy
msgid "40/41 - Amounts Receivable Within One Year"
msgstr "40/41 - Forderungen mit einer Restlaufzeit bis zu einem Jahr"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_ca_arwoy_oar
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca_arwoy_oar
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_ca_arwoy_oar
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_ca_arwoy_oar
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_arwoy_oar
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_arwoy_oar
msgid "41 - Other Amounts Receivable"
msgstr "41 - Sonstige Forderungen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apwoy_cpapdwoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apwoy_cpapdwoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apwoy_cpapdwoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apwoy_cpapdwoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apwoy_cpapdwoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apwoy_cpapdwoy
msgid ""
"42 - Current Portion of Amounts Payable After More Than One Year Falling Due "
"Within One Year"
msgstr ""
"42 - Innerhalb eines Jahres fällig werdende Verbindlichkeiten mit einer "
"ursprünglichen Laufzeit von mehr als 1 Jahr"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apwoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apwoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apwoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apwoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apwoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apwoy
msgid "42/48 - Amounts Payable Within One Year"
msgstr "42/48 - Verbindlichkeiten mit einer Restlaufzeit bis zu 1 Jahr"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apwoy_fd
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apwoy_fd
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apwoy_fd
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apwoy_fd
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apwoy_fd
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apwoy_fd
msgid "43 - Financial Debts"
msgstr "43 - Finanzverbindlichkeiten"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apwoy_fd_ci
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apwoy_fd_ci
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apwoy_fd_ci
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apwoy_fd_ci
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apwoy_fd_ci
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apwoy_fd_ci
msgid "430/8 - Credit Institutions"
msgstr "430/8 - Kreditinstitute"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apwoy_fd_ol
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apwoy_fd_ol
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apwoy_fd_ol
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apwoy_fd_ol
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apwoy_fd_ol
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apwoy_fd_ol
msgid "439 - Other Loans"
msgstr "439 - Sonstige Anleihen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apwoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apwoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apwoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apwoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apwoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apwoy_td
msgid "44 - Trade Debts"
msgstr "44 - Verbindlichkeiten aus Lieferungen und Leistungen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apwoy_td_s
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apwoy_td_s
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apwoy_td_s
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apwoy_td_s
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apwoy_td_s
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apwoy_td_s
msgid "440/4 - Suppliers"
msgstr "440/4 - Lieferanten"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apwoy_td_bep
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apwoy_td_bep
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apwoy_td_bep
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apwoy_td_bep
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apwoy_td_bep
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apwoy_td_bep
msgid "441 - Bills of Exchange Payable"
msgstr "441 - Verbindlichkeiten aus Wechseln"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apwoy_trss
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apwoy_trss
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apwoy_trss
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apwoy_trss
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apwoy_trss
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apwoy_trss
msgid "45 - Taxes, Remuneration and Social Security"
msgstr ""
"45 - Verbindlichkeiten aufgrund von Steuern, Arbeitsentgelten und "
"Soziallasten"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apwoy_trss_t
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apwoy_trss_t
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apwoy_trss_t
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apwoy_trss_t
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apwoy_trss_t
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apwoy_trss_t
msgid "450/3 - Taxes"
msgstr "450/3 - Steuern"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apwoy_trss_rss
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apwoy_trss_rss
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apwoy_trss_rss
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apwoy_trss_rss
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apwoy_trss_rss
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apwoy_trss_rss
msgid "454/9 - Remuneration and Social Security"
msgstr "454/9 - Arbeitsentgelte und Soziallasten"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apwoy_apcp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apwoy_apcp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apwoy_apcp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apwoy_apcp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apwoy_apcp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apwoy_apcp
msgid "46 - Advance Payments on Contracts in Progress"
msgstr "46 - Anzahlungen auf Bestellungen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apwoy_oap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apwoy_oap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apwoy_oap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apwoy_oap
msgid "47/48 - Other Amounts Payable"
msgstr "47/48 - Sonstige Verbindlichkeiten"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apwoy_oap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apwoy_oap
msgid "48 - Other Amounts Payable"
msgstr "48 - Sonstige Verbindlichkeiten"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_ca_adi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca_adi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_ca_adi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_ca_adi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_adi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_adi
msgid "490/1 - Accruals and Deferred Income"
msgstr "490/1 - Rechnungsabgrenzungsposten"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_adi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_adi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_adi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_adi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_adi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_adi
msgid "492/3 - Accruals and Deferred Income"
msgstr "492/3 - Rechnungsabgrenzungsposten"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_sa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_sa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_sa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_sa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_sa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_sa
msgid "499 - Suspense Accounts"
msgstr "499 - Wartekonten"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid ""
"5. Administrative or operating headquarters (companies and other\n"
"                                                institutions)\n"
"                                                or operating headquarters "
"(natural persons who have more than one such\n"
"                                                headquarters) when it is a "
"statement specific to that administrative or\n"
"                                                operating headquarters:"
msgstr ""
"5. Verwaltungs- oder Betriebssitz (Gesellschaften und andere Einrichtungen) "
"oder Betriebssitz (natürliche Personen die über mehrere solcher Sitze "
"verfügen), wenn es sich um ein Verzeichnis dieses Verwaltungs- oder "
"Betriebssitzes handelt:"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_ci_os
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_ci_os
msgid "50 - Own Shares"
msgstr "50 - Eigene Anteile"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_ca_ci
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca_ci
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_ca_ci
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_ca_ci
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_ci
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_ci
msgid "50/53 - Current Investments"
msgstr "50/53 - Geldanlagen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_ci_oi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_ci_oi
msgid "51/53 - Other Investments"
msgstr "51/53 - Sonstige Geldanlagen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_ca_cbh
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca_cbh
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_ca_cbh
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_ca_cbh
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_cbh
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_cbh
msgid "54/58 - Cash at Bank and in Hand"
msgstr "54/58 - Flüssige Mittel"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oc_grrmc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_oc_grrmc
msgid "60 - Goods for Resale, Raw Materials and Consumables"
msgstr "60 - Waren, Roh-, Hilfs- und Betriebsstoffe"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_oioc_gm_grrmcsog
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_oioc_gm_grrmcsog
msgid ""
"60/61 - Goods for Resale, Raw Materials, Consumables, Services and Other "
"Goods"
msgstr ""
"60/61 - Waren, Roh-, Hilfs- und Betriebsstoffe, übrige Lieferungen und "
"Leistungen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_oc
msgid "60/66A - Operating Charges"
msgstr "60/66A - Betriebliche Aufwendungen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oc_grrmc_p
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_oc_grrmc_p
msgid "600/8 - Purchases"
msgstr "600/8 - Käufe"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oc_grrmc_s
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_oc_grrmc_s
msgid "609 - Stocks: Decrease (Increase) (+)/(-)"
msgstr "609 - Bestände: Abnahme (Zunahme) (+)/(-)"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oc_sog
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_oc_sog
msgid "61 - Services and Other Goods"
msgstr "61 - Übrige Lieferungen und Leistungen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_oioc_rssp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oc_rssp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_oioc_rssp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_oc_rssp
msgid "62 - Remuneration, Social Security and Pensions (+)/(-)"
msgstr "62 - Arbeitsentgelte, Soziallasten und Pensionen (+)/(-)"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_oioc_aoawdfefa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oc_aoawdfeitfa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_oioc_aoawdfefa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_oc_aoawdfeitfa
msgid ""
"630 - Amortisations of and Other Amounts Written Down on Formation Expenses, "
"Intangible and Tangible Fixed Assets"
msgstr ""
"630 - Abschreibungen und Wertminderungen auf Errichtungs- und "
"Erweiterungsaufwendungen, auf immaterielle Anlagewerte und Sachanlagen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_oioc_awdscptd
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oc_awdscptd
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_oioc_awdscptd
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_oc_awdscptd
msgid ""
"631/4 - Amounts Written Down on Stocks, Contracts in Progress and Trade "
"Debtors: Additions (Write-Backs) (+)/(-)"
msgstr ""
"631/4 - Wertminderungen von Vorräten, in Ausführung befindlichen "
"Bestellungen und Forderungen aus Lieferungen und Leistungen: Zuführungen "
"(Rücknahmen) (+)/(-)"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_oioc_plca
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_oc_plc
msgid ""
"635/8 - Provisions for Liabilities and Charges: Appropriations (Uses and "
"Write-Backs) (+)/(-)"
msgstr ""
"635/8 - Rückstellungen für Risiken und Aufwendungen: Zuführungen (Verbrauch "
"und Auflösungen) (+)/(-)"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_oioc_plca
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oc_plc
msgid ""
"635/9 - Provisions for Liabilities and Charges: Appropriations (Uses and "
"Write-Backs) (+)/(-)"
msgstr ""
"635/9 - Rückstellungen für Risiken und Aufwendungen: Zuführungen (Verbrauch "
"und Auflösungen) (+)/(-)"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_oioc_ooc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oc_ooc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_oioc_ooc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_oc_ooc
msgid "640/8 - Other Operating Charges"
msgstr "640/8 - Sonstige betriebliche Aufwendungen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_oioc_ocraurc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oc_ocrarc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_oioc_ocraurc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_oc_ocrarc
msgid ""
"649 - Operating Charges Reported as Assets Under Restructuring Costs (-)"
msgstr ""
"649 - Auf der Aktivseite als Restrukturierungskosten ausgewiesene "
"betriebliche Aufwendungen (-)"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_fc_rfc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_fc_rfc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_fc_rfc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_comp_fc_rfc
msgid "65 - Recurring Financial Charges"
msgstr "65 - Wiederkehrende Finanzaufwendungen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_fc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_fc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_fc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_comp_fc
msgid "65/66B - Financial Charges"
msgstr "65/66B - Finanzaufwendungen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_fc_rfc_dc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_comp_fc_rfc_dc
msgid "650 - Debt Charges"
msgstr "650 - Aufwendungen für Verbindlichkeiten"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_fc_rfc_awdcaoscptd
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_comp_fc_rfc_awdcaoscptd
msgid ""
"651 - Amounts Written Down on Current Assets Other Than Stocks, Contracts in "
"Progress and Trade Debtors: Additions (Write-Backs) (+)/(-)"
msgstr ""
"651 - Wertminderungen von Gegenständen des Umlaufvermögens mit Ausnahme der "
"Vorräten, in Ausführung befindlichen Bestellungen und Forderungen aus "
"Lieferungen und Leistungen: Zuführungen (Rücknahmen) (+)/(-)"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_fc_rfc_ofc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_comp_fc_rfc_ofc
msgid "652/9 - Other Financial Charges"
msgstr "652/9 - Sonstige Finanzaufwendungen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_oioc_nroc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oc_nroc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_oioc_nroc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_oc_nroc
msgid "66A - Non-Recurring Operating Charges"
msgstr "66A - Nicht wiederkehrende betriebliche Aufwendungen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_fc_nrfc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_fc_nrfc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_fc_nrfc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_comp_fc_nrfc
msgid "66B - Non-Recurring Financial Charges"
msgstr "66B - Nicht wiederkehrende Finanzaufwendungen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_itor
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_itr
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_itor
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_itr
msgid "67/77 - Income Taxes on the Result (+)/(-)"
msgstr "67/77 - Steuern auf das Ergebnis (+)/(-)"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_itr_t
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_itr_t
msgid "670/3 - Taxes"
msgstr "670/3 - Steuern"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_ttdt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_ttdt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_ttdt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_ttdt
msgid "680 - Transfer to Deferred Taxes"
msgstr "680 - Zuführung zu aufgeschobenen Steuern"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_ttur
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_ttur
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_ttur
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_ttur
msgid "689 - Transfer to Untaxed Reserves"
msgstr "689 - Einstellung in die steuerfreien Rücklagen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_a_ate_tc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_a_a_tc
msgid "691 - To Contributions"
msgstr "691 - an der Einlage"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_a_tafor
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_a_tafor
msgid "691 - Transfers to Allocated Funds and Other Reserves"
msgstr ""
"691 - Zuweisungen an die zweckgebundenen Vermögen und sonstige Rücklagen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_a_ate
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_a_a
msgid "691/2 - Appropriations to Equity"
msgstr "691/2 - Zuweisungen an das Eigenkapital"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_a_ate_tlr
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_a_a_tlr
msgid "6920 - To Legal Reserve"
msgstr "6920 - an die gesetzliche Rücklage"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_a_ate_tor
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_a_a_tor
msgid "6921 - To Other Reserves"
msgstr "6921 - an die sonstigen Rücklagen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_a_ptbd_cfc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_a_ptbd_cfc
msgid "694 - Compensation for Contributions"
msgstr "694 - Vergütung der Einlage"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_a_ptbd
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_a_ptbd
msgid "694/7 - Profit to Be Distributed"
msgstr "694/7 - Zu verteilender Gewinn"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_a_ptbd_dom
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_a_ptbd_dom
msgid "695 - Directors or Managers"
msgstr "695 - Verwalter oder Geschäftsführer"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_a_ptbd_e
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_a_ptbd_e
msgid "696 - Employees"
msgstr "696 - Arbeitnehmer"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_a_ptbd_ob
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_a_ptbd_ob
msgid "697 - Other Beneficiaries"
msgstr "697 - Andere Berechtigte"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_oioc_gm_t
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oi_t
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_oioc_gm_t
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_oi_t
msgid "70 - Turnover"
msgstr "70 - Umsatzerlöse"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_oi
msgid "70/76A - Operating Income"
msgstr "70/76A - Betriebliche Erträge"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oi_sfgwcp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_oi_sfgwcp
msgid ""
"71 - Stocks of Finished Goods and Work and Contracts in Progress: Increase "
"(Decrease) (+)/(-)"
msgstr ""
"71 - Bestände an unfertigen und fertigen Erzeugnissen und an in Ausführung "
"befindlichen Bestellungen: Zunahme (Abnahme) (+)/(-)"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oi_pfa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_oi_pfa
msgid "72 - Produced Fixed Assets"
msgstr "72 - Andere aktivierte Eigenleistungen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_oioc_gm_mfgls
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oi_mfgls
msgid "73 - Membership Fees, Gifts, Legacies and Subsidies"
msgstr "73 - Beiträge, Schenkungen, Legate und Subventionen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oi_ooi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_oi_ooi
msgid "74 - Other Operating Income"
msgstr "74 - Sonstige betriebliche Erträge"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_fi_rfi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_fi_rfi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_fi_rfi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_comp_fi_rfi
msgid "75 - Recurring Financial Income"
msgstr "75 - Wiederkehrende Finanzerträge"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_fi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_fi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_fi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_comp_fi
msgid "75/76B - Financial Income"
msgstr "75/76B - Finanzerträge"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_fi_rfi_iffa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_comp_fi_rfi_iffa
msgid "750 - Income From Financial Fixed Assets"
msgstr "750 - Erträge aus Finanzanlagen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_fi_rfi_ica
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_comp_fi_rfi_ica
msgid "751 - Income From Current Assets"
msgstr "751 - Erträge aus Gegenständen des Umlaufvermögens"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_fi_rfi_ofi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_comp_fi_rfi_ofi
msgid "752/9 - Other Financial Income"
msgstr "752/9 - Sonstige Finanzerträge"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_fi_rfi_cis
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_fi_rfi_cis
msgid "753 - Of Which: Capital and Interest Subsidies"
msgstr "753 - Wovon: Kapital- und Zinssubventionen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oi_nroi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_oi_nroi
msgid "76A - Non-Recurring Operating Income"
msgstr "76A - Nicht wiederkehrende betriebliche Erträge"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_oioc_gm_nroi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_oioc_gm_nroi
msgid "76A - Of Which: Non-Recurring Operating Income"
msgstr "76A - Wovon: nicht wiederkehrende betriebliche Erträge"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_fi_nrfi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_fi_nrfi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_fi_nrfi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_comp_fi_nrfi
msgid "76B - Non-Recurring Financial Income"
msgstr "76B - Nicht wiederkehrende Finanzerträge"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_itr_aitwbtp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_itr_aitwbtp
msgid "77 - Adjustment of Income Taxes and Write-Back of Tax Provisions"
msgstr "77 - Steuererstattung und Auflösung von Steuerrückstellungen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_tfdt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_tfdt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_tfdt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_tfdt
msgid "780 - Transfer From Deferred Taxes"
msgstr "780 - Auflösung von aufgeschobenen Steuern"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_tfur
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_tfur
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_tfur
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_tfur
msgid "789 - Transfer From Untaxed Reserves"
msgstr "789 - Entnahmen aus den steuerfreien Rücklagen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_a_tfe_tc
msgid "791 - From Contributions"
msgstr "791 - aus der Einlage"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_a_tfefafor
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_a_tfefafor
msgid "791 - Transfers From Equity: Funds, Allocated Funds and Other Reserves"
msgstr ""
"791 - Entnahmen aus dem Eigenkapital: Vermögen, zweckgebundene Vermögen und "
"sonstige Rücklagen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_a_tfe
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_a_tfe
msgid "791/2 - Transfers From Equity"
msgstr "791/2 - Entnahmen aus dem Eigenkapital"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_a_tfe_fr
msgid "792 - From Reserves"
msgstr "792 - aus den Rücklagen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_a_scirol
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_a_scirol
msgid "794 - Shareholders' Contribution in Respect of Losses"
msgstr "794 - Teilnahme der Gesellschafter am Verlust"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_oioc_gm
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_oioc_gm
msgid "9900 - Gross Margin (+)/(-)"
msgstr "9900 - Brutto-Betriebsmarge (+)/(-)"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_opl
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_opl
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_opl
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_opl
msgid "9901 - Operating Profit (Loss) (+)/(-)"
msgstr "9901 - Betriebsgewinn (Betriebsverlust) (+)/(-)"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_plpbt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_plpbt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_plpbt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_plpbt
msgid "9903 - Profit (Loss) for the Period Before Taxes (+)/(-)"
msgstr "9903 - Gewinn (Verlust) des Geschäftsjahres vor Steuern (+)/(-)"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_plp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_plp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_plp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_plp
msgid "9904 - Profit (Loss) of the Period (+)/(-)"
msgstr "9904 - Gewinn (Verlust) des Geschäftsjahres (+)/(-)"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_plpafa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_plpa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_plpafa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_plpa
msgid "9905 - Profit (Loss) of the Period Available for Appropriation (+)/(-)"
msgstr ""
"9905 - Zu verwendender Gewinn (anzurechnender Verlust) des Geschäftsjahres "
"(+)/(-)"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_a_pltba
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_a_pltba
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_a_pltba
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_a_pltba
msgid "9906 - Profit (Loss) to Be Appropriated (+)/(-)"
msgstr ""
"9906 - Zu verwendender Gewinnsaldo (anzurechnender Verlustsaldo) (+)/(-)"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "AMOUNT TOTAL"
msgstr "GESAMTBETRAG"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_a
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_a
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_a
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_a
msgid "APPROPRIATION ACCOUNT"
msgstr "ERGEBNISVERWENDUNG"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a
msgid "ASSETS"
msgstr "AKTIVA"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__atn
msgid "ATN"
msgstr "VJA"

#. module: l10n_be_reports
#: model:ir.model,name:l10n_be_reports.model_account_chart_template
msgid "Account Chart Template"
msgstr "Kontenplan"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__message_needaction
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__message_needaction
msgid "Action Needed"
msgstr "Aktion erforderlich"

#. module: l10n_be_reports
#: model:ir.model.fields.selection,name:l10n_be_reports.selection__l10n_be_form_325__treatment_type__2
#: model:ir.model.fields.selection,name:l10n_be_reports.selection__l10n_be_form_325_wizard__treatment_type__2
msgid "Add"
msgstr "Hinzufügen"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__partner_address
msgid "Address"
msgstr "Adresse"

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_281_50__partner_address
msgid "Address of the partner when the form was created"
msgstr "Adresse des Partners bei der Erstellung der Karte"

#. module: l10n_be_reports
#: model:account.report.column,name:l10n_be_reports.account_financial_report_ec_sales_amount
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_281_50_pdf
msgid "Amount"
msgstr "Betrag"

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_general_ledger.py:0
msgid "Annual Accounts"
msgstr "Jahresabschluss"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_reports_periodic_vat_xml_export__ask_payment
msgid "Ask Payment"
msgstr "Zahlung verlangen"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_reports_periodic_vat_xml_export__ask_restitution
msgid "Ask Restitution"
msgstr "Rückerstattung verlangen"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.pdf_export_filters
msgid "Ask payment:"
msgstr "Zahlung verlangen:"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.pdf_export_filters
msgid "Ask restitution:"
msgstr "Rückerstattung verlangen:"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__message_attachment_count
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__message_attachment_count
msgid "Attachment Count"
msgstr "Anzahl Anhänge"

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_sales_report.py:0
#: code:addons/l10n_be_reports/models/partner_vat_listing.py:0
msgid "Audit"
msgstr "Prüfung"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__partner_bce_number
msgid "BCE number"
msgstr "Unternehmensnummer"

#. module: l10n_be_reports
#: model:account.report.column,name:l10n_be_reports.account_financial_report_bs_asso_a_column
#: model:account.report.column,name:l10n_be_reports.account_financial_report_bs_asso_f_column
#: model:account.report.column,name:l10n_be_reports.account_financial_report_bs_comp_acap_column
#: model:account.report.column,name:l10n_be_reports.account_financial_report_bs_comp_acon_column
#: model:account.report.column,name:l10n_be_reports.account_financial_report_bs_comp_fcap_column
#: model:account.report.column,name:l10n_be_reports.account_financial_report_bs_comp_fcon_column
#: model:account.report.column,name:l10n_be_reports.account_financial_report_pl_asso_a_column
#: model:account.report.column,name:l10n_be_reports.account_financial_report_pl_asso_f_column
#: model:account.report.column,name:l10n_be_reports.account_financial_report_pl_comp_a_column
#: model:account.report.column,name:l10n_be_reports.account_financial_report_pl_comp_f_column
msgid "Balance"
msgstr "Saldo"

#. module: l10n_be_reports
#: model:account.report,name:l10n_be_reports.account_financial_report_bs_asso_a
msgid "Balance Sheet (Abbr Assoc)"
msgstr "Bilanz (Verk/Mikro-VoG)"

#. module: l10n_be_reports
#: model:account.report,name:l10n_be_reports.account_financial_report_bs_comp_acap
msgid "Balance Sheet (Abbr Cap)"
msgstr "Bilanz (Verk/Mikro-K)"

#. module: l10n_be_reports
#: model:account.report,name:l10n_be_reports.account_financial_report_bs_comp_acon
msgid "Balance Sheet (Abbr Con)"
msgstr "Bilanz (Verk/Mikro-E)"

#. module: l10n_be_reports
#: model:account.report,name:l10n_be_reports.account_financial_report_bs_asso_f
msgid "Balance Sheet (Full Assoc)"
msgstr "Bilanz (Voll-VoG)"

#. module: l10n_be_reports
#: model:account.report,name:l10n_be_reports.account_financial_report_bs_comp_fcap
msgid "Balance Sheet (Full Cap)"
msgstr "Bilanz (Voll-K)"

#. module: l10n_be_reports
#: model:account.report,name:l10n_be_reports.account_financial_report_bs_comp_fcon
msgid "Balance Sheet (Full Con)"
msgstr "Bilanz (Voll-E)"

#. module: l10n_be_reports
#: model:ir.model,name:l10n_be_reports.model_l10n_be_ec_sales_report_handler
msgid "Belgian EC Sales Report Custom Handler"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model,name:l10n_be_reports.model_l10n_be_reports_periodic_vat_xml_export
msgid "Belgian Periodic VAT Report Export Wizard"
msgstr "Belgischer Assistent für den Export periodischer MwSt.-Berichte"

#. module: l10n_be_reports
#: model:ir.model,name:l10n_be_reports.model_l10n_be_tax_report_handler
msgid "Belgian Tax Report Custom Handler"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "CERTIFIED AS ACCURATE"
msgstr "FÜR RICHTIG ERKLÄRT"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_reports_periodic_vat_xml_export__calling_export_wizard_id
msgid "Calling Export Wizard"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields.selection,name:l10n_be_reports.selection__l10n_be_form_325__treatment_type__3
#: model:ir.model.fields.selection,name:l10n_be_reports.selection__l10n_be_form_325_wizard__treatment_type__3
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.view_account_financial_report_export
msgid "Cancel"
msgstr "Abbrechen"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_50_divider_pdf
msgid "Carry over from the previous divider"
msgstr "Übertrag des vorangehenden Zwischenblatts"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.view_account_financial_report_export
msgid "Choose option(s) before exporting XML:"
msgstr "Wählen Sie die Option(en) vor dem Export von XML:"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_res_partner__citizen_identification
#: model:ir.model.fields,field_description:l10n_be_reports.field_res_users__citizen_identification
msgid "Citizen Identification"
msgstr "Nationale Nummer"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__partner_citizen_identification
msgid "Citizen identification number"
msgstr "Nationale Nummer"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__partner_city
msgid "City"
msgstr "Stadt"

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_281_50__partner_city
msgid "City of the partner when the form was created"
msgstr "Gemeinde des Partners zum Zeitpunkt der Erstellung der Karte"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_reports_periodic_vat_xml_export__client_nihil
msgid "Client Nihil"
msgstr "Nihil-Erklärung"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.pdf_export_filters
msgid "Client nihil:"
msgstr "Nihil-Erklärung:"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.form_325_wizard_view_form
msgid "Close"
msgstr "Schließen"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_reports_periodic_vat_xml_export__comment
msgid "Comment"
msgstr "Bemerkungen"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_50_divider_pdf
msgid "Comments"
msgstr "Kommentare"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__commissions
msgid "Commissions"
msgstr "Provisionen"

#. module: l10n_be_reports
#: model:ir.model,name:l10n_be_reports.model_res_company
msgid "Companies"
msgstr "Unternehmen"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__company_id
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325_wizard__company_id
msgid "Company"
msgstr "Unternehmen"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "Company name or exact name (companies and other institutions)"
msgstr ""
"Firma oder genaue Bezeichnung (Gesellschaften und andere Einrichtungen)"

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_report.py:0
msgid "Company: %s"
msgstr "Unternehmen: %s"

#. module: l10n_be_reports
#: model:ir.model,name:l10n_be_reports.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_reports_periodic_vat_xml_export__control_value
msgid "Control Value"
msgstr "Kontrollwert"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__country_id
msgid "Country"
msgstr "Land"

#. module: l10n_be_reports
#: model:account.report.column,name:l10n_be_reports.account_financial_report_ec_sales_country
msgid "Country Code"
msgstr "Landescode"

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_281_50__country_id
msgid "Country of the partner when the form was created"
msgstr "Land des Partners zum Zeitpunkt der Erstellung der Karte"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.form_325_wizard_view_form
msgid "Create 325 Form"
msgstr "Verzeichnis 325 erstellen"

#. module: l10n_be_reports
#: model:ir.actions.act_window,name:l10n_be_reports.action_open_create_325_form
#: model:ir.ui.menu,name:l10n_be_reports.menu_action_create_325_form
msgid "Create 325 form"
msgstr "Verzeichnis 325 erstellen"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__create_uid
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__create_uid
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_reports_periodic_vat_xml_export__create_uid
msgid "Created by"
msgstr "Erstellt von"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__create_date
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__create_date
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325_wizard__create_date
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_reports_periodic_vat_xml_export__create_date
msgid "Created on"
msgstr "Erstellt am"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__currency_id
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__currency_id
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_reports_periodic_vat_xml_export__currency_id
msgid "Currency"
msgstr "Währung"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "DESIGNATION OF THE FORMS"
msgstr "BEZEICHNUNG DER KARTEN"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__debtor_id
msgid "Debtor"
msgstr "Debitor"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__debtor_address
msgid "Debtor Address"
msgstr "Adresse des Schuldners"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__debtor_bce_number
msgid "Debtor BCE Number"
msgstr "Unternehmensnummer des Schuldners"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__debtor_citizen_identification
msgid "Debtor Citizen Identification"
msgstr "Nationale Nummer des Schuldners"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__debtor_city
msgid "Debtor City"
msgstr "Schuldner Gemeinde"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__debtor_country_id
msgid "Debtor Country"
msgstr "Schuldner Land"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__debtor_name
msgid "Debtor Name"
msgstr "Name des Schuldners"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__debtor_phone_number
msgid "Debtor Phone Number"
msgstr "Telefonnummer des Schuldners"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__debtor_zip
msgid "Debtor ZIP"
msgstr "Postleitzahl des Schuldners"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__company_id
msgid "Debtor company"
msgstr "Schuldner Unternehmen"

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_281_50__company_id
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_325__company_id
msgid "Debtor for which the form is created"
msgstr "Schuldner, für den die Karte erstellt wird"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__debtor_is_natural_person
msgid "Debtor is Natural Person"
msgstr "Schuldner ist eine natürliche Person"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__display_name
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__display_name
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325_wizard__display_name
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_reports_periodic_vat_xml_export__display_name
msgid "Display Name"
msgstr "Anzeigename"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_50_divider_pdf
msgid "Divider N°"
msgstr "Zwischenblatt Nr."

#. module: l10n_be_reports
#. odoo-javascript
#: code:addons/l10n_be_reports/static/src/components/tax_report/warnings.xml:0
msgid "Do not forget to submit the"
msgstr "Vergessen Sie nicht, die"

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_281_50_form.py:0
msgid "Download 281.50 Form PDF"
msgstr "Karte 281.50 herunterladen"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.form_281_50_view_form
msgid "Download pdf"
msgstr "PDF herunterladen"

#. module: l10n_be_reports
#: model:ir.model.fields.selection,name:l10n_be_reports.selection__l10n_be_form_325__state__draft
msgid "Draft"
msgstr "Entwurf"

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/partner_vat_listing.py:0
msgid "Duplicate VAT number"
msgstr "Doppelte MwSt.-Nummer"

#. module: l10n_be_reports
#: model:ir.model.fields.selection,name:l10n_be_reports.selection__l10n_be_form_325__sender_lang_code__1
msgid "Dutch"
msgstr "Niederländisch"

#. module: l10n_be_reports
#: model:account.report,name:l10n_be_reports.belgian_ec_sales_report
msgid "EC Sales List"
msgstr "Innergemeinschaftliche Liste"

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_sales_report.py:0
msgid "EC Sales List Audit"
msgstr "Innergemeinschaftliche Liste"

#. module: l10n_be_reports
#. odoo-javascript
#: code:addons/l10n_be_reports/static/src/components/tax_report/warnings.xml:0
msgid "EC Sales list report"
msgstr "Innergemeinschaftliche Liste"

#. module: l10n_be_reports
#. odoo-javascript
#: code:addons/l10n_be_reports/static/src/components/sales_report/warnings.xml:0
msgid ""
"EC Sales taxes report total does not match Tax Report lines 44 + 46L + 46T - "
"48s44 - 48s46L - 48s46T."
msgstr ""
"Gesamtbetrag der innergemeinschaftlichen Liste stimmt nicht mit den "
"Steuerberichtszeilen 44 + 46L + 46T - 48s44 - 48s46L - 48s46T überein."

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el
msgid "EQUITY AND LIABILITIES"
msgstr "PASSIVA"

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_325_form.py:0
msgid ""
"Either there isn't any account nor partner with a 281.50 tag or there isn't "
"any amount to report for this period."
msgstr ""
"Entweder gibt es keinen Partner oder Konto mit einem 281.50-Tag oder es gibt "
"keinen Betrag zu berichten für diesen Zeitraum."

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_281_50_pdf
msgid "Enterprise N°:"
msgstr "Unternehmensnummer:"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.view_account_financial_report_export
msgid "Export Options"
msgstr "Export-Optionen"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.view_account_financial_report_export
msgid "Export XML"
msgstr "XML exportieren"

#. module: l10n_be_reports
#: model:ir.model,name:l10n_be_reports.model_account_reports_export_wizard_format
msgid "Export format for accounting's reports"
msgstr "Exportformat für Buchungsberichte"

#. module: l10n_be_reports
#: model:ir.model,name:l10n_be_reports.model_account_reports_export_wizard
msgid "Export wizard for accounting's reports"
msgstr "Export-Assistent für Buchungsberichte"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__exposed_expenses
msgid "Exposed expenses"
msgstr "Ausgelegte Kosten"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "FINANCES"
msgstr "FINANZEN"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_281_50_pdf
msgid "FORM N° 281.50 (commissions, brokerage, etc) - YEAR"
msgstr "KARTE Nr. 281.50 (Provisionen, Honorare usw.) - JAHR"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "FORMS 325.50"
msgstr "VERZEICHNISSE 325.50"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "Federal Public Service"
msgstr "Föderaler Öffentlicher Dienst"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__fees
msgid "Fees"
msgstr "Gebühren"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__message_follower_ids
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__message_follower_ids
msgid "Followers"
msgstr "Follower"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__message_partner_ids
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__message_partner_ids
msgid "Followers (Partners)"
msgstr "Follower (Partner)"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_50_divider_pdf
msgid "Form 281.50 N°"
msgstr "Nummer der Karte Nr. 281.50"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__form_325_id
msgid "Form 325"
msgstr "Verzeichnis 325"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_res_partner__form_file
#: model:ir.model.fields,field_description:l10n_be_reports.field_res_users__form_file
msgid "Form File"
msgstr "Formular-Datei"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__form_281_50_ids
msgid "Forms 281.50"
msgstr "Karten 281.50"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__form_281_50_count
msgid "Forms 281.50 count"
msgstr "Anzahl der Karten 281.50"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__form_281_50_total_amount
msgid "Forms 281.50 total"
msgstr "Gesamt der Karten 281.50"

#. module: l10n_be_reports
#: model:ir.model.fields.selection,name:l10n_be_reports.selection__l10n_be_form_325__sender_lang_code__2
msgid "French"
msgstr "französisch"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "GENERAL TAX ADMINISTRATION"
msgstr "GENERALVERWALTUNG STEUERWESEN"

#. module: l10n_be_reports
#: model:ir.model,name:l10n_be_reports.model_account_general_ledger_report_handler
msgid "General Ledger Custom Handler"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.form_325_view_form
msgid "Generate 281.50 forms PDF"
msgstr "281.50 Karten als PDF erstellen"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.form_325_view_form
msgid "Generate 281.50 forms XML"
msgstr "281.50 Karten als XML erstellen"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.form_325_wizard_view_form
msgid "Generate 325 form"
msgstr "Verzeichnis 325 erstellen"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.form_325_view_form
msgid "Generate 325 form PDF"
msgstr "Verzeichnis 325 PDF erstellen"

#. module: l10n_be_reports
#: model:ir.model.fields.selection,name:l10n_be_reports.selection__l10n_be_form_325__state__generated
msgid "Generated"
msgstr "Erzeugt"

#. module: l10n_be_reports
#: model:ir.model.fields.selection,name:l10n_be_reports.selection__l10n_be_form_325__sender_lang_code__3
msgid "German"
msgstr "Deutsch"

#. module: l10n_be_reports
#: model:ir.model,name:l10n_be_reports.model_report_l10n_be_reports_report_281_50_pdf
msgid "Get 281.50 report as PDF."
msgstr "Karte 281.50 als PDF holen."

#. module: l10n_be_reports
#: model:ir.model,name:l10n_be_reports.model_report_l10n_be_reports_report_281_50_xml
msgid "Get 281.50 report as XML."
msgstr "Karte 281.50 als XML holen."

#. module: l10n_be_reports
#: model:ir.model,name:l10n_be_reports.model_report_l10n_be_reports_report_325_pdf
msgid "Get 325 Report as PDF"
msgstr "Verzeichnis 325 als PDF holen"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__has_message
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__has_message
msgid "Has Message"
msgstr "Hat eine Nachricht"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__id
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__id
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325_wizard__id
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_reports_periodic_vat_xml_export__id
msgid "ID"
msgstr "ID"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__official_id
msgid "Identification number"
msgstr "Identifikationsnummer"

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_281_50__message_needaction
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_325__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Falls markiert, erfordern neue Nachrichten Ihre Aufmerksamkeit."

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_281_50__message_has_error
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_281_50__message_has_sms_error
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_325__message_has_error
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_325__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""
"Das Senden mancher Nachrichten ist fehlgeschlagen wenn dieses Fenster "
"angekreuzt ist."

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__income_debtor_bce_number
msgid "Income debtor BCE number"
msgstr "Unternehmensnummer des Schuldners der Einkünfte"

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_325__is_test
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_325_wizard__is_test
msgid "Indicates if the 325 is a test"
msgstr "Zeigt an, ob die 325 ein Test ist"

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_325_form.py:0
msgid "Internal reference to the following 281.50 tags are missing:\n"
msgstr "Interne Referenz zu folgenden 281.50-Tags fehlt:\n"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__message_is_follower
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__message_is_follower
msgid "Is Follower"
msgstr "Ist ein Follower"

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_281_50__partner_is_natural_person
msgid "Is the partner a natural person? (as opposed to a moral person)"
msgstr ""
"Ist der Partner eine natürliche Person? (im Gegensatz zu einer moralischen "
"Person)"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__partner_job_position
msgid "Job position"
msgstr "Ausgeübte Tätigkeit"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__write_uid
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__write_uid
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_reports_periodic_vat_xml_export__write_uid
msgid "Last Updated by"
msgstr "Zuletzt aktualisiert von"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__write_date
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__write_date
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325_wizard__write_date
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_reports_periodic_vat_xml_export__write_date
msgid "Last Updated on"
msgstr "Zuletzt aktualisiert am"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "Lastname, Firstname (natural persons)"
msgstr "Name, Vornamen (natürliche Personen)"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_281_50_pdf
msgid ""
"Lastname, Firstname (or denomination) and address of the recipient of the "
"income:"
msgstr ""
"Name und Vornamen (oder Bezeichnung) und Anschrift des Empfängers der "
"Einkünfte:"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_50_divider_pdf
msgid ""
"Lastname, Firstname (or denomination) of beneficiaries\n"
"                                    <br/>\n"
"                                    Street, n°, eventually box\n"
"                                    <br/>\n"
"                                    Zip code and city"
msgstr ""
"Name und Vorname (oder Bezeichnung) der Empfänger\n"
"                                    <br/\n"
"                                    Straße, Hausnummer und eventuell Bk.\n"
"                                    <br/>\n"
"                                    Postleitzahl und Gemeinde"

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_res_partner__forms_281_50
#: model:ir.model.fields,help:l10n_be_reports.field_res_users__forms_281_50
msgid "List of 281.50 forms for this partner"
msgstr "Liste der Karten 281.50 für diesen Partner"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__message_has_error
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__message_has_error
msgid "Message Delivery error"
msgstr "Error beim senden der Nachricht"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__message_ids
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__message_ids
msgid "Messages"
msgstr "Nachrichten"

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/res_partner.py:0
msgid "Missing partner data"
msgstr "Fehlende Partnerdaten"

#. module: l10n_be_reports
#: model:ir.model.fields.selection,name:l10n_be_reports.selection__l10n_be_form_325__treatment_type__1
#: model:ir.model.fields.selection,name:l10n_be_reports.selection__l10n_be_form_325_wizard__treatment_type__1
msgid "Modification"
msgstr "Änderung"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_281_50_pdf
msgid "NIL"
msgstr "NIHIL"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_50_divider_pdf
msgid "NN or NE:"
msgstr "NN oder UN:"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "NUMBER OF FORMS"
msgstr "ANZAHL ERSTELLTER KARTEN"

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_281_50__partner_name
msgid "Name of the partner when the form was created"
msgstr "Name des Partners bei der Erstellung der Karte"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__partner_is_natural_person
msgid "Natural person"
msgstr "Natürliche Person"

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_sales_report.py:0
#: code:addons/l10n_be_reports/models/partner_vat_listing.py:0
msgid "No VAT number associated with your company."
msgstr "Keine Umsatzsteuer-Identifikationsnummer für Ihr Unternehmen."

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_report.py:0
msgid "No email address associated with company %s."
msgstr "Keine E-Mail-Adresse mit dem Unternehmen %s verbunden."

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_sales_report.py:0
#: code:addons/l10n_be_reports/models/partner_vat_listing.py:0
msgid "No email address associated with the company."
msgstr "Keine mit dem Unternehmen verbundene E-Mail-Adresse."

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_report.py:0
msgid "No phone associated with company %s."
msgstr "Keine Telefonnummer für das Unternehmen %s."

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_sales_report.py:0
#: code:addons/l10n_be_reports/models/partner_vat_listing.py:0
msgid "No phone associated with the company."
msgstr "Keine Telefonnummer für das Unternehmen."

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_sales_report.py:0
msgid "No vat number defined for %s."
msgstr "Keine Mehrwertsteuernummer für %s definiert."

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_report.py:0
msgid "Not allowed negative amounts"
msgstr "Kein negativer Betrag erlaubt"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__message_needaction_counter
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__message_needaction_counter
msgid "Number of Actions"
msgstr "Anzahl der Aktionen"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__message_has_error_counter
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__message_has_error_counter
msgid "Number of errors"
msgstr "# Fehler"

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_281_50__message_needaction_counter
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_325__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Anzahl der Nachrichten, die eine Aktion erfordern"

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_281_50__message_has_error_counter
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_325__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Anzahl der Nachrichten mit Zustellungsfehler"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "N° 1"
msgstr "Nr. 1"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "N° 2"
msgstr "Nr. 2"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "N° 3"
msgstr "Nr. 3"

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/res_partner.py:0
#, python-format
msgid ""
"Only users with the access group '%s' can unset the 281.50 category on "
"partners."
msgstr ""
"Nur Benutzer mit der Zugriffsgruppe '%s' können die Kategorie 281.50 "
"bei Partnern aufheben"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.form_325_view_form
msgid "Open"
msgstr "Offen"

#. module: l10n_be_reports
#: model:ir.ui.menu,name:l10n_be_reports.menu_action_open_325_tree_view
msgid "Open 325 forms"
msgstr "Verzeichnisse 325 öffnen"

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/res_partner.py:0
msgid "Open list"
msgstr "Liste öffnen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_oioc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_oioc
msgid "Operating Income and Operating Charges"
msgstr "Betriebliche Erträge und Aufwendungen"

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_reports_periodic_vat_xml_export__calling_export_wizard_id
msgid ""
"Optional field containing the report export wizard calling this wizard, if "
"there is one."
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields.selection,name:l10n_be_reports.selection__l10n_be_form_325__treatment_type__0
#: model:ir.model.fields.selection,name:l10n_be_reports.selection__l10n_be_form_325_wizard__treatment_type__0
msgid "Original"
msgstr "Original"

#. module: l10n_be_reports
#: model:ir.model.fields.selection,name:l10n_be_reports.selection__l10n_be_form_325__sending_type__0
#: model:ir.model.fields.selection,name:l10n_be_reports.selection__l10n_be_form_325_wizard__sending_type__0
msgid "Original send"
msgstr "Original-Senden"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_e_apl_oay
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_e_apl_oay
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_apl_oay
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e_apl_oay
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_apl_oay
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e_apl_oay
msgid "Other Appropriations of the Year"
msgstr "Andere Zuwendungen des Geschäftsjahres"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "PAYROLL DEDUCTIONS"
msgstr "BERUFSSTEUERVORABZUG"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl
msgid "PROFIT AND LOSS ACCOUNT"
msgstr "SCHEMA DER ERGEBNISRECHNUNG"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__paid_amount
msgid "Paid amount"
msgstr "Gezahlter Betrag"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__partner_id
msgid "Partner"
msgstr "Partner"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__partner_name
msgid "Partner Name"
msgstr "Partner Name"

#. module: l10n_be_reports
#: model:account.report,name:l10n_be_reports.l10n_be_partner_vat_listing
#: model:account.report.line,name:l10n_be_reports.l10n_be_partner_vat_listing_line
#: model:ir.actions.client,name:l10n_be_reports.action_account_report_l10n_be_partner_vat_listing
#: model:ir.actions.client,name:l10n_be_reports.action_account_report_partner_vat_listing
#: model:ir.ui.menu,name:l10n_be_reports.menu_action_account_report_l10n_be_partner_vat_listing
msgid "Partner VAT Listing"
msgstr "Jährliche Kundenliste"

#. module: l10n_be_reports
#: model:ir.model,name:l10n_be_reports.model_l10n_be_partner_vat_handler
msgid "Partner VAT Listing Custom Handler"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_281_50__partner_id
msgid "Partner for which this 281.50 form has been created"
msgstr "Partner, für den diese Karte 281.50 erstellt wurde"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.view_partner_281_50_required_fields
msgid "Partner with missing information"
msgstr "Partner mit fehlenden Informationen"

#. module: l10n_be_reports
#. odoo-javascript
#: code:addons/l10n_be_reports/static/src/components/partner_vat_listing/warnings.xml:0
msgid "Partners sharing the same VAT number"
msgstr "Partner, die dieselbe Umsatzsteuer-Identifikationsnummer haben"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_account_reports_export_wizard__l10n_be_reports_periodic_vat_wizard_id
msgid "Periodic VAT Export Wizard"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "Postal code and city"
msgstr "Postleitzahl und Gemeinde"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_281_50_pdf
msgid "Profession:"
msgstr "Ausgeübte Tätigkeit:"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_e_apl_ply
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_e_apl_ply
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_apl_ply
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e_apl_ply
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_apl_ply
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e_apl_ply
msgid "Profit (Loss) of the Year"
msgstr "Gewinne (Verluste) des Geschäftsjahres"

#. module: l10n_be_reports
#: model:ir.actions.client,name:l10n_be_reports.action_account_report_be_pl_asso_a
#: model:ir.actions.client,name:l10n_be_reports.action_account_report_be_pl_asso_f
#: model:ir.actions.client,name:l10n_be_reports.action_account_report_be_pl_comp_a
#: model:ir.actions.client,name:l10n_be_reports.action_account_report_be_pl_comp_f
msgid "Profit and Loss"
msgstr "Schema der Ergebnisrechnung"

#. module: l10n_be_reports
#: model:account.report,name:l10n_be_reports.account_financial_report_pl_asso_a
msgid "Profit and Loss (Abbr Assoc)"
msgstr "Schema der Ergebnisrechnung (Verk/Mikro-VoG)"

#. module: l10n_be_reports
#: model:account.report,name:l10n_be_reports.account_financial_report_pl_comp_a
msgid "Profit and Loss (Abbr)"
msgstr "Schema der Ergebnisrechnung (Verk/Mikro)"

#. module: l10n_be_reports
#: model:account.report,name:l10n_be_reports.account_financial_report_pl_asso_f
msgid "Profit and Loss (Full Assoc)"
msgstr "Schema der Ergebnisrechnung (Voll-VoG)"

#. module: l10n_be_reports
#: model:account.report,name:l10n_be_reports.account_financial_report_pl_comp_f
msgid "Profit and Loss (Full)"
msgstr "Schema der Ergebnisrechnung (Voll)"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_e_apl_plpy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_e_apl_plpy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_apl_plpy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e_apl_plpy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_apl_plpy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e_apl_plpy
msgid "Profits (Losses) from Previous Years"
msgstr "Gewinne (Verluste) aus den Vorjahren"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid ""
"RECEPTION DATE\n"
"                                                        <br/>\n"
"                                                        (For administration "
"use only)"
msgstr ""
"EMPFANGSDATUM\n"
"                                                        <br/>\n"
"                                                        (der Verwaltung "
"vorbehaltener Rahmen)"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__reference_year
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325_wizard__reference_year
msgid "Reference Year"
msgstr "Bezugsjahr"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__reference_year
msgid "Reference year"
msgstr "Bezugsjahr"

#. module: l10n_be_reports
#: model:ir.model,name:l10n_be_reports.model_l10n_be_form_281_50
msgid "Represents a 281.50 form"
msgstr "Steht für eine Karte 281.50"

#. module: l10n_be_reports
#: model:ir.model,name:l10n_be_reports.model_l10n_be_form_325
msgid "Represents a 325 form"
msgstr "Steht für ein Verzeichnis 325"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__message_has_sms_error
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS Zustellungsfehler"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "SUMMARY STATEMENT YEAR"
msgstr "ZUSAMMENFASSENDES VERZEICHNIS JAHR"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "SUMMARY TABLE OF RECORDS AND PAYROLL DEDUCTIONS (1)"
msgstr "ZUSAMMENFASSENDE TABELLE DER KARTEN UND DES BERUFSSTEUERVORABZUGS (1)"

#. module: l10n_be_reports
#: model:ir.model.fields.selection,name:l10n_be_reports.selection__l10n_be_form_325__sending_type__1
#: model:ir.model.fields.selection,name:l10n_be_reports.selection__l10n_be_form_325_wizard__sending_type__1
msgid "Send grouped corrections"
msgstr "Gruppierte Korrekturen senden"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__sender_id
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325_wizard__sender_id
msgid "Sender"
msgstr "Absender"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__sender_address
msgid "Sender Address"
msgstr "Adresse des Absenders"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__sender_bce_number
msgid "Sender BCE Number"
msgstr "Unternehmensnummer des Absenders"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__sender_city
msgid "Sender City"
msgstr "Gemeinde des Absenders"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__sender_lang_code
msgid "Sender Language Code"
msgstr "Sprachcode des Absenders"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__sender_name
msgid "Sender Name"
msgstr "Name des Absenders"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__sender_phone_number
msgid "Sender Phone Number"
msgstr "Telefonnummer des Absenders"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__sender_zip
msgid "Sender ZIP"
msgstr "Postleitzahl des Absenders"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325_wizard__sending_type
msgid "Sending Type"
msgstr "Sendender Typ"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__sending_type
msgid "Sending type"
msgstr "Sendetyp"

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_report.py:0
msgid "Some fields required for the export are missing. Please specify them."
msgstr ""
"Einige für den Export erforderliche Felder fehlen. Bitte geben Sie diese an."

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/res_partner.py:0
msgid ""
"Some partners are not correctly configured. Please be sure that the "
"following pieces of information are set: street, zip code, country%s and vat "
"or citizen identification."
msgstr ""
"Einige Partner sind nicht richtig konfiguriert. Bitte stellen Sie sicher, "
"dass die folgenden Informationen gesetzt sind: Straße, Postleitzahl, Land%s "
"und Umsatzsteuer-Identifikationsnummer order Nationale Registernummer."

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_report.py:0
msgid "Specify"
msgstr "Angeben"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__state
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__state
msgid "State"
msgstr "Staat"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "Street and n°"
msgstr "Straße und Nr."

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "TOTAL AMOUNT"
msgstr "GESAMTBETRAG"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "TOTALS"
msgstr "GESAMTBETRAG"

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_general_ledger.py:0
msgid "TXT"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.column,name:l10n_be_reports.account_financial_report_ec_sales_tax
msgid "Tax Code"
msgstr "Kode"

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_res_partner__form_file
#: model:ir.model.fields,help:l10n_be_reports.field_res_users__form_file
msgid "Technical field to store all forms file."
msgstr "Technisches Feld zum Speichern aller Formulardateien."

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__is_test
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325_wizard__is_test
msgid "Test Form"
msgstr "Test"

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_325_wizard__sender_id
msgid "The company responsible for sending the form."
msgstr "Das für den Versand der Karte verantwortliche Unternehmen."

#. module: l10n_be_reports
#. odoo-javascript
#: code:addons/l10n_be_reports/static/src/components/tax_report/warnings.xml:0
#, fuzzy, python-format
#| msgid "Some controls failed"
msgid "The following controls failed:"
msgstr "Einige Kontrollen fehlgeschlagen"

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/wizard/l10n_be_325_form_wizard.py:0
msgid "The reference year must be a number."
msgstr "Das Bezugsjahr muss eine Zahl sein."

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_res_partner__citizen_identification
#: model:ir.model.fields,help:l10n_be_reports.field_res_users__citizen_identification
msgid ""
"This code corresponds to the personal identification number for the tax "
"authorities."
msgstr ""
"Dieser Code entspricht der Nationalregisternummer für die Steuerbehörden."

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_325__sending_type
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_325_wizard__sending_type
msgid ""
"This field allows to make an original sending(correspond to first send) or a "
"grouped corrections(if you have made some mistakes before)."
msgstr ""
"In diesem Feld können Sie eine Originalsendung (entspricht der ersten "
"Sendung) oder eine gruppierte Korrektur (falls Sie zuvor einige Fehler "
"gemacht haben) vornehmen."

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_281_50__treatment_type
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_325__treatment_type
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_325_wizard__treatment_type
msgid "This field represents the nature of the form."
msgstr "Dieses Feld repräsentiert die Art des Formulars."

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_50_divider_pdf
msgid "To be completed only if this divider is not the first or the only one"
msgstr ""
"Nur auszufüllen, wenn dieses Zwischenblatt weder das erste noch das einzige "
"Zwischenblatt ist."

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_50_divider_pdf
msgid "Total amount or to be transferred to the next divider:"
msgstr "Gesamtbetrag oder zu übertragen auf das folgende Zwischenblatt:"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_50_divider_pdf
msgid "Total mentionned in box 3 e of the form 281.50"
msgstr "Im Rahmen 3, e, der Karte Nr. 281.50 angegebener Gesamtbetrag"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__total_remuneration
msgid "Total remuneration"
msgstr "Gesamtbetrag"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__treatment_type
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__treatment_type
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325_wizard__treatment_type
msgid "Treatment Type"
msgstr "Behandlungstyp"

#. module: l10n_be_reports
#: model:account.report.column,name:l10n_be_reports.l10n_be_partner_vat_listing_turnover
msgid "Turnover"
msgstr "Umsatz"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__user_id
msgid "User"
msgstr "Benutzer"

#. module: l10n_be_reports
#: model:account.report.column,name:l10n_be_reports.l10n_be_partner_vat_listing_vat_amount
msgid "VAT Amount"
msgstr "Mehrwertsteuerbetrag"

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/partner_vat_listing.py:0
msgid "VAT Listing Audit"
msgstr "Jährliche Kundenliste Prüfung"

#. module: l10n_be_reports
#: model:account.report.column,name:l10n_be_reports.account_financial_report_ec_sales_vat
#: model:account.report.column,name:l10n_be_reports.l10n_be_partner_vat_listing_vat_number
msgid "VAT Number"
msgstr "VAT Nummer"

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_sales_report.py:0
#: code:addons/l10n_be_reports/models/partner_vat_listing.py:0
msgid "View Partner"
msgstr "Ansicht Partner"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__website_message_ids
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__website_message_ids
msgid "Website Messages"
msgstr "Website-Nachrichten"

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_281_50__website_message_ids
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_325__website_message_ids
msgid "Website communication history"
msgstr "Website-Kommunikationsverlauf"

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_report.py:0
#: code:addons/l10n_be_reports/models/account_sales_report.py:0
#: code:addons/l10n_be_reports/models/partner_vat_listing.py:0
msgid "XML"
msgstr "XML"

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_report.py:0
msgid "XML Export Options"
msgstr "XML-Exportoptionen"

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_325_form.py:0
msgid "You already generated 281.50 forms for this 325 form."
msgstr "Sie haben bereits Karten 281.50 für dieses Verzeichnis 325 generiert."

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_281_50_form.py:0
#: code:addons/l10n_be_reports/models/account_325_form.py:0
msgid "You can't delete a 281.50 for which its form 325 xml has been generated"
msgstr ""
"Sie können eine 281.50 nicht löschen, für die ihr Verzeichnis 325 XML "
"generiert wurde"

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/wizard/l10n_be_325_form_wizard.py:0
msgid "You can't use a reference year in the future or for the current year."
msgstr ""
"Sie können kein Referenzjahr in der Zukunft oder für das aktuelle Jahr "
"verwenden."

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/wizard/l10n_be_325_form_wizard.py:0
msgid "You must be logged in a Belgian company to use this feature"
msgstr ""
"Sie müssen bei einem belgischen Unternehmen angemeldet sein, um diese "
"Funktion nutzen zu können"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__partner_zip
msgid "Zip"
msgstr "PLZ"

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_281_50__partner_zip
msgid "Zip of the partner when the form was created"
msgstr "Postleitzahl des Partners bei Erstellung der Karte"

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_report.py:0
msgid ""
"[44] < ([00] + [01] + [02] + [03] + [45] + [46] + [47] + [48] + [49]) * 200 "
"if [44] > 99.999"
msgstr ""
"[44] < ([00] + [01] + [02] + [03] + [45] + [46] + [47] + [48] + [49]) * 200, "
"wenn [44] > 99.999"

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_report.py:0
#, python-format
msgid ""
"[44] < ([00] + [01] + [02] + [03] + [45] + [46] + [47] + [48] + [49]) * 200 "
"if [88] > 99.999"
msgstr ""
"[44] < ([00] + [01] + [02] + [03] + [45] + [46] + [47] + [48] + [49]) * 200, "
"wenn [88] > 99.999"

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_report.py:0
msgid "[55] > 0 if [86] > 0 or [88] > 0"
msgstr "[55] > 0, wenn [86] > 0 oder [88] > 0"

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_report.py:0
msgid "[56] + [57] > 0 if [87] > 0"
msgstr "[56] + [57] > 0, wenn [87] > 0"

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_report.py:0
msgid "[88] < ([81] + [82] + [83] + [84]) * 100 if [88] > 99.999"
msgstr "[88] < ([81] + [82] + [83] + [84]) * 100, wenn [88] > 99.999"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_281_50_pdf
msgid ""
"a) Commissions, brokerage, commercial discounts, etc:\n"
"                                                <br/>\n"
"                                                b) Fees or vacations:\n"
"                                                <br/>\n"
"                                                c) Benefits in kind "
"(nature : ................)\n"
"                                                <br/>\n"
"                                                d) Expenses incurred on "
"behalf of the beneficiary:\n"
"                                                <br/>\n"
"                                                e) Total (see also in "
"sections f and g below):"
msgstr ""
"a) Provisionen, Maklergebühren, Handelsrabatte usw.:\n"
"                                                <br/>\n"
"                                                b) Sitzungsgelder oder "
"Honorare:\n"
"                                                <br/>\n"
"                                                c) Vorteile jeglicher Art "
"(Art: ................)\n"
"                                                <br/>\n"
"                                                d) Für Rechnung des "
"Empfängers ausgelegte Kosten:\n"
"                                                <br/>\n"
"                                                e) Gesamtbetrag (siehe auch "
"Rubriken f und g hier unten):"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.res_partner_view_form_inherit
msgid "e.g. 123455 555 6"
msgstr "z.B. 123455 555 6"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.form_325_wizard_view_form
msgid "e.g. 2018"
msgstr "z.B. 2018"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "established by :"
msgstr "ausgestellt durch:"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_281_50_pdf
msgid ""
"f) Enter here, if applicable, the amount included in item e\n"
"                                            above relates to compensation "
"paid to:\n"
"                                            <br/>\n"
"                                            - athletes for their sports "
"performances:\n"
"                                            <br/>\n"
"                                            - trainers, coaches and "
"accompaniers for their activity for the\n"
"                                            benefit of the athletes:"
msgstr ""
"f) Falls der in Rubrik e eingetragene Betrag Entschädigungen an Sportier für "
"sportliche Aktivitäten oder Entschädigungen an Ausbilder, Trainer oder "
"Betreuer für ihre Tätigkeit zugunsten von Sportlern enthält, tragen Sie hier "
"den in diesen Entschädigungen enthaltenen Betrag ein, der gezahlt wurde an:\n"
"                                            <br/>\n"
"                                            - Sportler für ihre sportlichen "
"Aktivitäten:\n"
"                                            <br/>\n"
"                                            - Ausbilder, Trainer oder "
"Begleiter für ihre Tätigkeiten zu Gunsten von Sportlern:"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_281_50_pdf
msgid ""
"g) If the amount indicated in item e above does not coincide with\n"
"                                                the amount actually paid in"
msgstr "g) Wenn der unter e eingetragene Betrag nicht mit dem"

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/partner_vat_listing.py:0
#, python-format
msgid "Missing partners"
msgstr "Fehlende Partner"

#. module: l10n_be_reports
#. odoo-javascript
#: code:addons/l10n_be_reports/static/src/components/partner_vat_listing/warnings.xml:0
#, python-format
msgid ""
"customers with a turnover of more than 250€ or one or more credit notes in "
"the selected period who are not included in the report. Click"
msgstr ""
"Kunden mit einem Umsatz von mehr als 250€ oder einer oder mehreren Gutschriften im "
"ausgewählten Zeitraum, die nicht im Bericht enthalten sind. Klicken Sie"

#. module: l10n_be_reports
#. odoo-javascript
#: code:addons/l10n_be_reports/static/src/components/partner_vat_listing/warnings.xml:0
#, python-format
msgid "You have"
msgstr "Sie haben"

#. module: l10n_be_reports
#. odoo-javascript
#: code:addons/l10n_be_reports/static/src/components/partner_vat_listing/warnings.xml:0
#, python-format
msgid "here"
msgstr "hier"

#. module: l10n_be_reports
#. odoo-javascript
#: code:addons/l10n_be_reports/static/src/components/partner_vat_listing/warnings.xml:0
#, python-format
msgid "to see the list."
msgstr ", um die Liste zu sehen."

#. module: l10n_be_reports
#. odoo-javascript
#: code:addons/l10n_be_reports/static/src/components/partner_vat_listing/warnings.xml:0
msgid "will be grouped in the XML export."
msgstr "werden im XML-Export in Gruppen zusammengefasst."
