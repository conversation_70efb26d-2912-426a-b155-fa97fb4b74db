# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_be_reports
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.5alpha1+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-08 09:35+0000\n"
"PO-Revision-Date: 2024-08-08 09:35+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_a_plcf
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_a_plcf
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_a_plcf
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_a_plcf
msgid "(14) - Profit (Loss) to Be Carried Forward (+)/(-)"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_a_pltba_plpafa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_a_pltba_plpafa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_a_pltba_plpafa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_a_pltba_plpafa
msgid ""
"(9905) - Profit (Loss) of the Period Available for Appropriation (+)/(-)"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_281_50_pdf
msgid "(including amounts that are related to other taxable periods):"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_281_50_pdf
msgid ""
",\n"
"                                                enter the amount actually paid in"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_281_50_pdf
msgid "0.00"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_281_50_pdf
msgid ""
"0.00\n"
"                                                <br/><br/>"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "1. NN ou NE:"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_281_50_pdf
msgid "1. N°"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_e_aff
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_e_aff
msgid "10 - Association or Foundation Funds"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_c_c
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_c_c
msgid "10 - Capital"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_c
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e_c
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_c
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e_c
msgid "10/11 - Contributions"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_e
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_e
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e
msgid "10/15 - Equity"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_tot
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_tot
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_tot
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_tot
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_tot
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_tot
msgid "10/49 - TOTAL LIABILITIES"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_c_c_ic
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_c_c_ic
msgid "100 - Issued Capital"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_c_c_uc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_c_c_uc
msgid "101 - Uncalled Capital"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_c_bc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_c_bc
msgid "11 - Beyond Capital"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e_c_a
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e_c_a
msgid "110 - Available"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_c_bc_spa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_c_bc_spa
msgid "1100/10 - Share Premium Account"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_c_bc_o
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_c_bc_o
msgid "1109/19 - Other"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e_c_na
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e_c_na
msgid "111 - Not Available"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_e_rs
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_e_rs
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_rs
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e_rs
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_rs
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e_rs
msgid "12 - Revaluation Surpluses"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_e_for
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_e_for
msgid "13 - Funds and Other Reserves"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_r
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e_r
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_r
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e_r
msgid "13 - Reserves"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_r_rno_lr
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_r_rno_lr
msgid "130 - Legal Reserve"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_r_rno
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e_r_rno
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_r_rno
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e_r_rno
msgid "130/1 - Reserves Not Available"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_r_rno_rnos
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e_r_rno_rnos
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_r_rno_rnos
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e_r_rno_rnos
msgid "1311 - Reserves Not Available Statutorily"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_r_rno_pos
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e_r_rno_pos
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_r_rno_pos
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e_r_rno_pos
msgid "1312 - Purchase of Own Shares"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_r_rno_fs
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e_r_rno_fs
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_r_rno_fs
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e_r_rno_fs
msgid "1313 - Financial Support"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_r_rno_o
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e_r_rno_o
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_r_rno_o
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e_r_rno_o
msgid "1319 - Other"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_r_ur
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e_r_ur
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_r_ur
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e_r_ur
msgid "132 - Untaxed Reserves"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_r_ar
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e_r_ar
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_r_ar
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e_r_ar
msgid "133 - Available Reserves"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_e_apl
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_e_apl
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_apl
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e_apl
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_apl
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e_apl
msgid "14 - Accumulated Profits (Losses) (+)/(-)"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_a_pltba_plppbf
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_a_pltba_plppbf
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_a_pltba_plppbf
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_a_pltba_plppbf
msgid "14P - Profit (Loss) of the Preceding Period Brought Forward (+)/(-)"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_e_cs
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_e_cs
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_cs
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e_cs
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_cs
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e_cs
msgid "15 - Capital Subsidies"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_pdt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_pdt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_pdt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_pdt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_pdt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_pdt
msgid "16 - Provisions and Deferred Taxes"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_pdt_plc_pso
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_pdt_plc_pso
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_pdt_plc_pso
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_pdt_plc_pso
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_pdt_plc_pso
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_pdt_plc_pso
msgid "160 - Pensions and Similar Obligations"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_pdt_plc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_pdt_plc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_pdt_plc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_pdt_plc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_pdt_plc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_pdt_plc
msgid "160/5 - Provisions for Liabilities and Charges"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_pdt_plc_t
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_pdt_plc_t
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_pdt_plc_t
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_pdt_plc_t
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_pdt_plc_t
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_pdt_plc_t
msgid "161 - Taxes"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_pdt_plc_mrm
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_pdt_plc_mrm
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_pdt_plc_mrm
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_pdt_plc_mrm
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_pdt_plc_mrm
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_pdt_plc_mrm
msgid "162 - Major Repairs and Maintenance"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_pdt_plc_eo
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_pdt_plc_eo
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_pdt_plc_eo
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_pdt_plc_eo
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_pdt_plc_eo
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_pdt_plc_eo
msgid "163 - Environmental Obligations"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_pdt_plc_olc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_pdt_plc_olc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_pdt_plc_olc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_pdt_plc_olc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_pdt_plc_olc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_pdt_plc_olc
msgid "164/5 - Other Liabilities and Charges"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_pdt_plc_pslrgrr
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_pdt_plc_pslrgrr
msgid ""
"167 - Provisions for Subsidies and Legacies to Reimburse and Gifts With a "
"Recovery Right"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_pdt_dt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_pdt_dt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_pdt_dt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_pdt_dt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_pdt_dt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_pdt_dt
msgid "168 - Deferred Taxes"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apamtoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apamtoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apamtoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apamtoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apamtoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apamtoy
msgid "17 - Amounts Payable After More Than One Year"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap
msgid "17/49 - Amounts Payable"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apamtoy_fd_sl
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apamtoy_fd_sl
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apamtoy_fd_sl
msgid "170 - Subordinated Loans"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apamtoy_fd
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apamtoy_fd
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apamtoy_fd
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apamtoy_fd
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apamtoy_fd
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apamtoy_fd
msgid "170/4 - Financial Debts"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apamtoy_fd_ud
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apamtoy_fd_ud
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apamtoy_fd_ud
msgid "171 - Unsubordinated Debentures"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apamtoy_fd_loso
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apamtoy_fd_loso
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apamtoy_fd_loso
msgid "172 - Leasing and Other Similar Obligations"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apamtoy_fd_ciloso
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apamtoy_fd_ciloso
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apamtoy_fd_ciloso
msgid "172/3 - Credit Institutions, Leasing and Other Similar Obligations"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apamtoy_fd_ci
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apamtoy_fd_ci
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apamtoy_fd_ci
msgid "173 - Credit Institutions"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apamtoy_fd_ol
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apamtoy_fd_ol
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apamtoy_fd_ol
msgid "174 - Other Loans"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apamtoy_fd_ol
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apamtoy_fd_ol
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apamtoy_fd_ol
msgid "174/0 - Other Loans"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apamtoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apamtoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apamtoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apamtoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apamtoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apamtoy_td
msgid "175 - Trade Debts"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apamtoy_td_s
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apamtoy_td_s
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apamtoy_td_s
msgid "1750 - Suppliers"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apamtoy_td_bep
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apamtoy_td_bep
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apamtoy_td_bep
msgid "1751 - Bills of Exchange Payable"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apamtoy_apcp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apamtoy_apcp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apamtoy_apcp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apamtoy_apcp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apamtoy_apcp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apamtoy_apcp
msgid "176 - Advance Payments on Contracts in Progress"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apamtoy_oap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apamtoy_oap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apamtoy_oap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apamtoy_oap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apamtoy_oap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apamtoy_oap
msgid "178/9 - Other Amounts Payable"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_asdna
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e_asdna
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_asdna
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e_asdna
msgid "19 - Advance to Shareholders on the Distribution of Net Assets"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_281_50_pdf
msgid "2. Name (or denomination) and address of the debtor of the income:"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "2. Phone number:"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_fe
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fe
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_fe
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_fe
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fe
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fe
msgid "20 - Formation Expenses"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_tot
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_tot
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_tot
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_tot
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_tot
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_tot
msgid "20/58 - TOTAL ASSETS"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_fa_ifa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa_ifa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_fa_ifa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_fa_ifa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa_ifa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa_ifa
msgid "21 - Intangible Fixed Assets"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_fa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_fa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_fa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa
msgid "21/28 - Fixed Assets"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_fa_tfa_lb
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa_tfa_lb
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_fa_tfa_lb
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_fa_tfa_lb
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa_tfa_lb
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa_tfa_lb
msgid "22 - Land and Buildings"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_fa_tfa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa_tfa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_fa_tfa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_fa_tfa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa_tfa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa_tfa
msgid "22/27 - Tangible Fixed Assets"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_fa_tfa_pme
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa_tfa_pme
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_fa_tfa_pme
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_fa_tfa_pme
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa_tfa_pme
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa_tfa_pme
msgid "23 - Plant, Machinery and Equipment"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_fa_tfa_fv
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa_tfa_fv
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_fa_tfa_fv
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_fa_tfa_fv
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa_tfa_fv
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa_tfa_fv
msgid "24 - Furniture and Vehicles"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_fa_tfa_losr
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa_tfa_losr
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_fa_tfa_losr
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_fa_tfa_losr
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa_tfa_losr
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa_tfa_losr
msgid "25 - Leasing and Other Similar Rights"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_fa_tfa_otfa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa_tfa_otfa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_fa_tfa_otfa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_fa_tfa_otfa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa_tfa_otfa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa_tfa_otfa
msgid "26 - Other Tangible Fixed Assets"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_fa_tfa_aucap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa_tfa_aucap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_fa_tfa_aucap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_fa_tfa_aucap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa_tfa_aucap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa_tfa_aucap
msgid "27 - Assets Under Construction and Advance Payments"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_fa_ffa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa_ffa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_fa_ffa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_fa_ffa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa_ffa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa_ffa
msgid "28 - Financial Fixed Assets"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa_ffa_ac_pi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa_ffa_ac_pi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa_ffa_ac_pi
msgid "280 - Participating Interests"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa_ffa_ac
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa_ffa_ac
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa_ffa_ac
msgid "280/1 - Affiliated Companies"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa_ffa_ac_ar
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa_ffa_ac_ar
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa_ffa_ac_ar
msgid "281 - Amounts Receivable"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "281.10"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "281.11"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "281.12"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "281.13"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "281.14"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "281.15"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "281.16"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "281.17"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "281.18"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "281.20"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "281.30"
msgstr ""

#. module: l10n_be_reports
#: model:res.partner.category,name:l10n_be_reports.res_partner_tag_281_50
msgid "281.50"
msgstr ""

#. module: l10n_be_reports
#: model:account.account.tag,name:l10n_be_reports.account_tag_281_50_atn
msgid "281.50 - ATN"
msgstr ""

#. module: l10n_be_reports
#: model:account.account.tag,name:l10n_be_reports.account_tag_281_50_commissions
msgid "281.50 - Commissions"
msgstr ""

#. module: l10n_be_reports
#: model:account.account.tag,name:l10n_be_reports.account_tag_281_50_exposed_expenses
msgid "281.50 - Exposed Expenses"
msgstr ""

#. module: l10n_be_reports
#: model:account.account.tag,name:l10n_be_reports.account_tag_281_50_fees
msgid "281.50 - Fees"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.form_281_50_view_form
msgid "281.50 Form"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.form_325_view_form
msgid "281.50 Forms"
msgstr ""

#. module: l10n_be_reports
#: model:ir.actions.report,name:l10n_be_reports.action_report_partner_281_50_pdf
msgid "281.50 PDF"
msgstr ""

#. module: l10n_be_reports
#: model:ir.actions.report,name:l10n_be_reports.action_report_partner_281_50_xml
msgid "281.50 XML"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_res_partner__forms_281_50
#: model:ir.model.fields,field_description:l10n_be_reports.field_res_users__forms_281_50
msgid "281.50 forms"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa_ffa_oclpi_pi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa_ffa_oclpi_pi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa_ffa_oclpi_pi
msgid "282 - Participating Interests"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa_ffa_oclpi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa_ffa_oclpi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa_ffa_oclpi
msgid "282/3 - Other Companies Linked by Participating Interests"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa_ffa_oclpi_ar
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa_ffa_oclpi_ar
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa_ffa_oclpi_ar
msgid "283 - Amounts Receivable"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa_ffa_offa_s
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa_ffa_offa_s
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa_ffa_offa_s
msgid "284 - Shares"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa_ffa_offa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa_ffa_offa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa_ffa_offa
msgid "284/8 - Other Financial Fixed Assets"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa_ffa_offa_arcg
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa_ffa_offa_arcg
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa_ffa_offa_arcg
msgid "285/8 - Amounts Receivable and Cash Guarantees"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_ca_aramtoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca_aramtoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_ca_aramtoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_ca_aramtoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_aramtoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_aramtoy
msgid "29 - Amounts Receivable After More Than One Year"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_ca
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_ca
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_ca
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca
msgid "29/58 - Current Assets"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_ca_aramtoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca_aramtoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_ca_aramtoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_ca_aramtoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_aramtoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_aramtoy_td
msgid "290 - Trade Debtors"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_ca_aramtoy_oar
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca_aramtoy_oar
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_ca_aramtoy_oar
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_ca_aramtoy_oar
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_aramtoy_oar
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_aramtoy_oar
msgid "291 - Other Amounts Receivable"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_ca_scp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca_scp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_ca_scp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_ca_scp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_scp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_scp
msgid "3 - Stocks and Contracts in Progress"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "3. Identity of the debtor of the income"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_281_50_pdf
msgid "3. Nature"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca_scp_s_rmc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_scp_s_rmc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_scp_s_rmc
msgid "30/31 - Raw Materials and Consumables"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_ca_scp_s
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca_scp_s
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_ca_scp_s
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_ca_scp_s
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_scp_s
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_scp_s
msgid "30/36 - Stocks"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca_scp_s_wip
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_scp_s_wip
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_scp_s_wip
msgid "32 - Work in Progress"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.form_325_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.form_325_view_tree
msgid "325 Form"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model,name:l10n_be_reports.model_l10n_be_form_325_wizard
msgid "325 Form Wizard"
msgstr ""

#. module: l10n_be_reports
#: model:ir.actions.report,name:l10n_be_reports.action_report_325_form_pdf
msgid "325 PDF"
msgstr ""

#. module: l10n_be_reports
#: model:ir.actions.act_window,name:l10n_be_reports.action_open_325_tree_view
msgid "325 forms"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca_scp_s_fg
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_scp_s_fg
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_scp_s_fg
msgid "33 - Finished Goods"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca_scp_s_gpr
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_scp_s_gpr
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_scp_s_gpr
msgid "34 - Goods Purchased for Resale"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca_scp_s_ipis
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_scp_s_ipis
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_scp_s_ipis
msgid "35 - Immovable Property Intended for Sale"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca_scp_s_ap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_scp_s_ap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_scp_s_ap
msgid "36 - Advance Payments"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_ca_scp_cp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca_scp_cp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_ca_scp_cp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_ca_scp_cp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_scp_cp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_scp_cp
msgid "37 - Contracts in Progress"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_281_50_pdf
msgid "4. Comments:"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid ""
"4. Domicile (natural persons), registered office or main administrative\n"
"                                                office (companies and other institutions)"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_ca_arwoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca_arwoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_ca_arwoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_ca_arwoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_arwoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_arwoy_td
msgid "40 - Trade Debtors"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_ca_arwoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca_arwoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_ca_arwoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_ca_arwoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_arwoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_arwoy
msgid "40/41 - Amounts Receivable Within One Year"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_ca_arwoy_oar
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca_arwoy_oar
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_ca_arwoy_oar
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_ca_arwoy_oar
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_arwoy_oar
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_arwoy_oar
msgid "41 - Other Amounts Receivable"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apwoy_cpapdwoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apwoy_cpapdwoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apwoy_cpapdwoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apwoy_cpapdwoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apwoy_cpapdwoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apwoy_cpapdwoy
msgid ""
"42 - Current Portion of Amounts Payable After More Than One Year Falling Due"
" Within One Year"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apwoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apwoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apwoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apwoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apwoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apwoy
msgid "42/48 - Amounts Payable Within One Year"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apwoy_fd
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apwoy_fd
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apwoy_fd
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apwoy_fd
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apwoy_fd
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apwoy_fd
msgid "43 - Financial Debts"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apwoy_fd_ci
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apwoy_fd_ci
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apwoy_fd_ci
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apwoy_fd_ci
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apwoy_fd_ci
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apwoy_fd_ci
msgid "430/8 - Credit Institutions"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apwoy_fd_ol
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apwoy_fd_ol
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apwoy_fd_ol
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apwoy_fd_ol
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apwoy_fd_ol
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apwoy_fd_ol
msgid "439 - Other Loans"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apwoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apwoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apwoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apwoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apwoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apwoy_td
msgid "44 - Trade Debts"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apwoy_td_s
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apwoy_td_s
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apwoy_td_s
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apwoy_td_s
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apwoy_td_s
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apwoy_td_s
msgid "440/4 - Suppliers"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apwoy_td_bep
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apwoy_td_bep
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apwoy_td_bep
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apwoy_td_bep
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apwoy_td_bep
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apwoy_td_bep
msgid "441 - Bills of Exchange Payable"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apwoy_trss
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apwoy_trss
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apwoy_trss
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apwoy_trss
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apwoy_trss
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apwoy_trss
msgid "45 - Taxes, Remuneration and Social Security"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apwoy_trss_t
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apwoy_trss_t
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apwoy_trss_t
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apwoy_trss_t
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apwoy_trss_t
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apwoy_trss_t
msgid "450/3 - Taxes"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apwoy_trss_rss
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apwoy_trss_rss
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apwoy_trss_rss
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apwoy_trss_rss
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apwoy_trss_rss
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apwoy_trss_rss
msgid "454/9 - Remuneration and Social Security"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apwoy_apcp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apwoy_apcp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apwoy_apcp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apwoy_apcp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apwoy_apcp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apwoy_apcp
msgid "46 - Advance Payments on Contracts in Progress"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apwoy_oap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apwoy_oap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apwoy_oap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apwoy_oap
msgid "47/48 - Other Amounts Payable"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apwoy_oap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apwoy_oap
msgid "48 - Other Amounts Payable"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_ca_adi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca_adi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_ca_adi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_ca_adi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_adi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_adi
msgid "490/1 - Accruals and Deferred Income"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_adi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_adi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_adi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_adi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_adi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_adi
msgid "492/3 - Accruals and Deferred Income"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_sa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_sa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_sa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_sa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_sa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_sa
msgid "499 - Suspense Accounts"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid ""
"5. Administrative or operating headquarters (companies and other\n"
"                                                institutions)\n"
"                                                or operating headquarters (natural persons who have more than one such\n"
"                                                headquarters) when it is a statement specific to that administrative or\n"
"                                                operating headquarters:"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_ci_os
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_ci_os
msgid "50 - Own Shares"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_ca_ci
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca_ci
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_ca_ci
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_ca_ci
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_ci
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_ci
msgid "50/53 - Current Investments"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_ci_oi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_ci_oi
msgid "51/53 - Other Investments"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_ca_cbh
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca_cbh
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_ca_cbh
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_ca_cbh
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_cbh
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_cbh
msgid "54/58 - Cash at Bank and in Hand"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oc_grrmc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_oc_grrmc
msgid "60 - Goods for Resale, Raw Materials and Consumables"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_oioc_gm_grrmcsog
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_oioc_gm_grrmcsog
msgid ""
"60/61 - Goods for Resale, Raw Materials, Consumables, Services and Other "
"Goods"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_oc
msgid "60/66A - Operating Charges"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oc_grrmc_p
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_oc_grrmc_p
msgid "600/8 - Purchases"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oc_grrmc_s
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_oc_grrmc_s
msgid "609 - Stocks: Decrease (Increase) (+)/(-)"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oc_sog
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_oc_sog
msgid "61 - Services and Other Goods"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_oioc_rssp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oc_rssp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_oioc_rssp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_oc_rssp
msgid "62 - Remuneration, Social Security and Pensions (+)/(-)"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_oioc_aoawdfefa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oc_aoawdfeitfa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_oioc_aoawdfefa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_oc_aoawdfeitfa
msgid ""
"630 - Amortisations of and Other Amounts Written Down on Formation Expenses,"
" Intangible and Tangible Fixed Assets"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_oioc_awdscptd
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oc_awdscptd
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_oioc_awdscptd
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_oc_awdscptd
msgid ""
"631/4 - Amounts Written Down on Stocks, Contracts in Progress and Trade "
"Debtors: Additions (Write-Backs) (+)/(-)"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_oioc_plca
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_oc_plc
msgid ""
"635/8 - Provisions for Liabilities and Charges: Appropriations (Uses and "
"Write-Backs) (+)/(-)"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_oioc_plca
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oc_plc
msgid ""
"635/9 - Provisions for Liabilities and Charges: Appropriations (Uses and "
"Write-Backs) (+)/(-)"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_oioc_ooc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oc_ooc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_oioc_ooc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_oc_ooc
msgid "640/8 - Other Operating Charges"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_oioc_ocraurc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oc_ocrarc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_oioc_ocraurc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_oc_ocrarc
msgid ""
"649 - Operating Charges Reported as Assets Under Restructuring Costs (-)"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_fc_rfc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_fc_rfc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_fc_rfc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_comp_fc_rfc
msgid "65 - Recurring Financial Charges"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_fc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_fc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_fc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_comp_fc
msgid "65/66B - Financial Charges"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_fc_rfc_dc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_comp_fc_rfc_dc
msgid "650 - Debt Charges"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_fc_rfc_awdcaoscptd
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_comp_fc_rfc_awdcaoscptd
msgid ""
"651 - Amounts Written Down on Current Assets Other Than Stocks, Contracts in"
" Progress and Trade Debtors: Additions (Write-Backs) (+)/(-)"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_fc_rfc_ofc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_comp_fc_rfc_ofc
msgid "652/9 - Other Financial Charges"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_oioc_nroc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oc_nroc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_oioc_nroc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_oc_nroc
msgid "66A - Non-Recurring Operating Charges"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_fc_nrfc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_fc_nrfc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_fc_nrfc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_comp_fc_nrfc
msgid "66B - Non-Recurring Financial Charges"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_itor
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_itr
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_itor
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_itr
msgid "67/77 - Income Taxes on the Result (+)/(-)"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_itr_t
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_itr_t
msgid "670/3 - Taxes"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_ttdt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_ttdt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_ttdt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_ttdt
msgid "680 - Transfer to Deferred Taxes"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_ttur
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_ttur
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_ttur
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_ttur
msgid "689 - Transfer to Untaxed Reserves"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_a_ate_tc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_a_a_tc
msgid "691 - To Contributions"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_a_tafor
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_a_tafor
msgid "691 - Transfers to Allocated Funds and Other Reserves"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_a_ate
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_a_a
msgid "691/2 - Appropriations to Equity"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_a_ate_tlr
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_a_a_tlr
msgid "6920 - To Legal Reserve"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_a_ate_tor
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_a_a_tor
msgid "6921 - To Other Reserves"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_a_ptbd_cfc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_a_ptbd_cfc
msgid "694 - Compensation for Contributions"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_a_ptbd
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_a_ptbd
msgid "694/7 - Profit to Be Distributed"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_a_ptbd_dom
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_a_ptbd_dom
msgid "695 - Directors or Managers"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_a_ptbd_e
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_a_ptbd_e
msgid "696 - Employees"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_a_ptbd_ob
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_a_ptbd_ob
msgid "697 - Other Beneficiaries"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_oioc_gm_t
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oi_t
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_oioc_gm_t
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_oi_t
msgid "70 - Turnover"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_oi
msgid "70/76A - Operating Income"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oi_sfgwcp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_oi_sfgwcp
msgid ""
"71 - Stocks of Finished Goods and Work and Contracts in Progress: Increase "
"(Decrease) (+)/(-)"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oi_pfa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_oi_pfa
msgid "72 - Produced Fixed Assets"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_oioc_gm_mfgls
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oi_mfgls
msgid "73 - Membership Fees, Gifts, Legacies and Subsidies"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oi_ooi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_oi_ooi
msgid "74 - Other Operating Income"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_fi_rfi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_fi_rfi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_fi_rfi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_comp_fi_rfi
msgid "75 - Recurring Financial Income"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_fi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_fi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_fi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_comp_fi
msgid "75/76B - Financial Income"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_fi_rfi_iffa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_comp_fi_rfi_iffa
msgid "750 - Income From Financial Fixed Assets"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_fi_rfi_ica
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_comp_fi_rfi_ica
msgid "751 - Income From Current Assets"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_fi_rfi_ofi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_comp_fi_rfi_ofi
msgid "752/9 - Other Financial Income"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_fi_rfi_cis
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_fi_rfi_cis
msgid "753 - Of Which: Capital and Interest Subsidies"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oi_nroi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_oi_nroi
msgid "76A - Non-Recurring Operating Income"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_oioc_gm_nroi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_oioc_gm_nroi
msgid "76A - Of Which: Non-Recurring Operating Income"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_fi_nrfi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_fi_nrfi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_fi_nrfi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_comp_fi_nrfi
msgid "76B - Non-Recurring Financial Income"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_itr_aitwbtp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_itr_aitwbtp
msgid "77 - Adjustment of Income Taxes and Write-Back of Tax Provisions"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_tfdt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_tfdt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_tfdt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_tfdt
msgid "780 - Transfer From Deferred Taxes"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_tfur
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_tfur
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_tfur
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_tfur
msgid "789 - Transfer From Untaxed Reserves"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_a_tfe_tc
msgid "791 - From Contributions"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_a_tfefafor
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_a_tfefafor
msgid "791 - Transfers From Equity: Funds, Allocated Funds and Other Reserves"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_a_tfe
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_a_tfe
msgid "791/2 - Transfers From Equity"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_a_tfe_fr
msgid "792 - From Reserves"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_a_scirol
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_a_scirol
msgid "794 - Shareholders' Contribution in Respect of Losses"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_oioc_gm
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_oioc_gm
msgid "9900 - Gross Margin (+)/(-)"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_opl
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_opl
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_opl
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_opl
msgid "9901 - Operating Profit (Loss) (+)/(-)"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_plpbt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_plpbt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_plpbt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_plpbt
msgid "9903 - Profit (Loss) for the Period Before Taxes (+)/(-)"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_plp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_plp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_plp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_plp
msgid "9904 - Profit (Loss) of the Period (+)/(-)"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_plpafa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_plpa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_plpafa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_plpa
msgid "9905 - Profit (Loss) of the Period Available for Appropriation (+)/(-)"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_a_pltba
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_a_pltba
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_a_pltba
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_a_pltba
msgid "9906 - Profit (Loss) to Be Appropriated (+)/(-)"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "AMOUNT TOTAL"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_a
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_a
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_a
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_a
msgid "APPROPRIATION ACCOUNT"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a
msgid "ASSETS"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__atn
msgid "ATN"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model,name:l10n_be_reports.model_account_chart_template
msgid "Account Chart Template"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__message_needaction
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__message_needaction
msgid "Action Needed"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields.selection,name:l10n_be_reports.selection__l10n_be_form_325__treatment_type__2
#: model:ir.model.fields.selection,name:l10n_be_reports.selection__l10n_be_form_325_wizard__treatment_type__2
msgid "Add"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__partner_address
msgid "Address"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_281_50__partner_address
msgid "Address of the partner when the form was created"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.column,name:l10n_be_reports.account_financial_report_ec_sales_amount
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_281_50_pdf
msgid "Amount"
msgstr ""

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_general_ledger.py:0
msgid "Annual Accounts"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_reports_periodic_vat_xml_export__ask_payment
msgid "Ask Payment"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_reports_periodic_vat_xml_export__ask_restitution
msgid "Ask Restitution"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.pdf_export_filters
msgid "Ask payment:"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.pdf_export_filters
msgid "Ask restitution:"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__message_attachment_count
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_sales_report.py:0
#: code:addons/l10n_be_reports/models/partner_vat_listing.py:0
#: code:addons/l10n_be_reports/models/partner_vat_listing.py:0
msgid "Audit"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__partner_bce_number
msgid "BCE number"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.column,name:l10n_be_reports.account_financial_report_bs_asso_a_column
#: model:account.report.column,name:l10n_be_reports.account_financial_report_bs_asso_f_column
#: model:account.report.column,name:l10n_be_reports.account_financial_report_bs_comp_acap_column
#: model:account.report.column,name:l10n_be_reports.account_financial_report_bs_comp_acon_column
#: model:account.report.column,name:l10n_be_reports.account_financial_report_bs_comp_fcap_column
#: model:account.report.column,name:l10n_be_reports.account_financial_report_bs_comp_fcon_column
#: model:account.report.column,name:l10n_be_reports.account_financial_report_pl_asso_a_column
#: model:account.report.column,name:l10n_be_reports.account_financial_report_pl_asso_f_column
#: model:account.report.column,name:l10n_be_reports.account_financial_report_pl_comp_a_column
#: model:account.report.column,name:l10n_be_reports.account_financial_report_pl_comp_f_column
msgid "Balance"
msgstr ""

#. module: l10n_be_reports
#: model:account.report,name:l10n_be_reports.account_financial_report_bs_asso_a
msgid "Balance Sheet (Abbr Assoc)"
msgstr ""

#. module: l10n_be_reports
#: model:account.report,name:l10n_be_reports.account_financial_report_bs_comp_acap
msgid "Balance Sheet (Abbr Cap)"
msgstr ""

#. module: l10n_be_reports
#: model:account.report,name:l10n_be_reports.account_financial_report_bs_comp_acon
msgid "Balance Sheet (Abbr Con)"
msgstr ""

#. module: l10n_be_reports
#: model:account.report,name:l10n_be_reports.account_financial_report_bs_asso_f
msgid "Balance Sheet (Full Assoc)"
msgstr ""

#. module: l10n_be_reports
#: model:account.report,name:l10n_be_reports.account_financial_report_bs_comp_fcap
msgid "Balance Sheet (Full Cap)"
msgstr ""

#. module: l10n_be_reports
#: model:account.report,name:l10n_be_reports.account_financial_report_bs_comp_fcon
msgid "Balance Sheet (Full Con)"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model,name:l10n_be_reports.model_l10n_be_ec_sales_report_handler
msgid "Belgian EC Sales Report Custom Handler"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model,name:l10n_be_reports.model_l10n_be_reports_periodic_vat_xml_export
msgid "Belgian Periodic VAT Report Export Wizard"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model,name:l10n_be_reports.model_l10n_be_tax_report_handler
msgid "Belgian Tax Report Custom Handler"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "CERTIFIED AS ACCURATE"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_reports_periodic_vat_xml_export__calling_export_wizard_id
msgid "Calling Export Wizard"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields.selection,name:l10n_be_reports.selection__l10n_be_form_325__treatment_type__3
#: model:ir.model.fields.selection,name:l10n_be_reports.selection__l10n_be_form_325_wizard__treatment_type__3
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.view_account_financial_report_export
msgid "Cancel"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_50_divider_pdf
msgid "Carry over from the previous divider"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.view_account_financial_report_export
msgid "Choose option(s) before exporting XML:"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_res_partner__citizen_identification
#: model:ir.model.fields,field_description:l10n_be_reports.field_res_users__citizen_identification
msgid "Citizen Identification"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__partner_citizen_identification
msgid "Citizen identification number"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__partner_city
msgid "City"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_281_50__partner_city
msgid "City of the partner when the form was created"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_reports_periodic_vat_xml_export__client_nihil
msgid "Client Nihil"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.pdf_export_filters
msgid "Client nihil:"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.form_325_wizard_view_form
msgid "Close"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_reports_periodic_vat_xml_export__comment
msgid "Comment"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_50_divider_pdf
msgid "Comments"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__commissions
msgid "Commissions"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model,name:l10n_be_reports.model_res_company
msgid "Companies"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__company_id
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325_wizard__company_id
msgid "Company"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "Company name or exact name (companies and other institutions)"
msgstr ""

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_report.py:0
msgid "Company: %s"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model,name:l10n_be_reports.model_res_partner
msgid "Contact"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_reports_periodic_vat_xml_export__control_value
msgid "Control Value"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__country_id
msgid "Country"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.column,name:l10n_be_reports.account_financial_report_ec_sales_country
msgid "Country Code"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_281_50__country_id
msgid "Country of the partner when the form was created"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.form_325_wizard_view_form
msgid "Create 325 Form"
msgstr ""

#. module: l10n_be_reports
#: model:ir.actions.act_window,name:l10n_be_reports.action_open_create_325_form
#: model:ir.ui.menu,name:l10n_be_reports.menu_action_create_325_form
msgid "Create 325 form"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__create_uid
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__create_uid
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_reports_periodic_vat_xml_export__create_uid
msgid "Created by"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__create_date
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__create_date
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325_wizard__create_date
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_reports_periodic_vat_xml_export__create_date
msgid "Created on"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__currency_id
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__currency_id
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_reports_periodic_vat_xml_export__currency_id
msgid "Currency"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "DESIGNATION OF THE FORMS"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__debtor_id
msgid "Debtor"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__debtor_address
msgid "Debtor Address"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__debtor_bce_number
msgid "Debtor BCE Number"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__debtor_citizen_identification
msgid "Debtor Citizen Identification"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__debtor_city
msgid "Debtor City"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__debtor_country_id
msgid "Debtor Country"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__debtor_name
msgid "Debtor Name"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__debtor_phone_number
msgid "Debtor Phone Number"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__debtor_zip
msgid "Debtor ZIP"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__company_id
msgid "Debtor company"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_281_50__company_id
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_325__company_id
msgid "Debtor for which the form is created"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__debtor_is_natural_person
msgid "Debtor is Natural Person"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__display_name
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__display_name
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325_wizard__display_name
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_reports_periodic_vat_xml_export__display_name
msgid "Display Name"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_50_divider_pdf
msgid "Divider N°"
msgstr ""

#. module: l10n_be_reports
#. odoo-javascript
#: code:addons/l10n_be_reports/static/src/components/tax_report/warnings.xml:0
msgid "Do not forget to submit the"
msgstr ""

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_281_50_form.py:0
msgid "Download 281.50 Form PDF"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.form_281_50_view_form
msgid "Download pdf"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields.selection,name:l10n_be_reports.selection__l10n_be_form_325__state__draft
msgid "Draft"
msgstr ""

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/partner_vat_listing.py:0
msgid "Duplicate VAT number"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields.selection,name:l10n_be_reports.selection__l10n_be_form_325__sender_lang_code__1
msgid "Dutch"
msgstr ""

#. module: l10n_be_reports
#: model:account.report,name:l10n_be_reports.belgian_ec_sales_report
msgid "EC Sales List"
msgstr ""

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_sales_report.py:0
msgid "EC Sales List Audit"
msgstr ""

#. module: l10n_be_reports
#. odoo-javascript
#: code:addons/l10n_be_reports/static/src/components/tax_report/warnings.xml:0
msgid "EC Sales list report"
msgstr ""

#. module: l10n_be_reports
#. odoo-javascript
#: code:addons/l10n_be_reports/static/src/components/sales_report/warnings.xml:0
msgid ""
"EC Sales taxes report total does not match Tax Report lines 44 + 46L + 46T -"
" 48s44 - 48s46L - 48s46T."
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el
msgid "EQUITY AND LIABILITIES"
msgstr ""

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_325_form.py:0
msgid ""
"Either there isn't any account nor partner with a 281.50 tag or there isn't "
"any amount to report for this period."
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_281_50_pdf
msgid "Enterprise N°:"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.view_account_financial_report_export
msgid "Export Options"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.view_account_financial_report_export
msgid "Export XML"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model,name:l10n_be_reports.model_account_reports_export_wizard_format
msgid "Export format for accounting's reports"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model,name:l10n_be_reports.model_account_reports_export_wizard
msgid "Export wizard for accounting's reports"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__exposed_expenses
msgid "Exposed expenses"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "FINANCES"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_281_50_pdf
msgid "FORM N° 281.50 (commissions, brokerage, etc) - YEAR"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "FORMS 325.50"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "Federal Public Service"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__fees
msgid "Fees"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__message_follower_ids
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__message_follower_ids
msgid "Followers"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__message_partner_ids
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_50_divider_pdf
msgid "Form 281.50 N°"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__form_325_id
msgid "Form 325"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_res_partner__form_file
#: model:ir.model.fields,field_description:l10n_be_reports.field_res_users__form_file
msgid "Form File"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__form_281_50_ids
msgid "Forms 281.50"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__form_281_50_count
msgid "Forms 281.50 count"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__form_281_50_total_amount
msgid "Forms 281.50 total"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields.selection,name:l10n_be_reports.selection__l10n_be_form_325__sender_lang_code__2
msgid "French"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "GENERAL TAX ADMINISTRATION"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model,name:l10n_be_reports.model_account_general_ledger_report_handler
msgid "General Ledger Custom Handler"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.form_325_view_form
msgid "Generate 281.50 forms PDF"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.form_325_view_form
msgid "Generate 281.50 forms XML"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.form_325_wizard_view_form
msgid "Generate 325 form"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.form_325_view_form
msgid "Generate 325 form PDF"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields.selection,name:l10n_be_reports.selection__l10n_be_form_325__state__generated
msgid "Generated"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields.selection,name:l10n_be_reports.selection__l10n_be_form_325__sender_lang_code__3
msgid "German"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model,name:l10n_be_reports.model_report_l10n_be_reports_report_281_50_pdf
msgid "Get 281.50 report as PDF."
msgstr ""

#. module: l10n_be_reports
#: model:ir.model,name:l10n_be_reports.model_report_l10n_be_reports_report_281_50_xml
msgid "Get 281.50 report as XML."
msgstr ""

#. module: l10n_be_reports
#: model:ir.model,name:l10n_be_reports.model_report_l10n_be_reports_report_325_pdf
msgid "Get 325 Report as PDF"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__has_message
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__has_message
msgid "Has Message"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__id
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__id
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325_wizard__id
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_reports_periodic_vat_xml_export__id
msgid "ID"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__official_id
msgid "Identification number"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_281_50__message_needaction
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_325__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_281_50__message_has_error
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_281_50__message_has_sms_error
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_325__message_has_error
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_325__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__income_debtor_bce_number
msgid "Income debtor BCE number"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_325__is_test
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_325_wizard__is_test
msgid "Indicates if the 325 is a test"
msgstr ""

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_325_form.py:0
msgid "Internal reference to the following 281.50 tags are missing:\n"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__message_is_follower
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_281_50__partner_is_natural_person
msgid "Is the partner a natural person? (as opposed to a moral person)"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__partner_job_position
msgid "Job position"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__write_uid
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__write_uid
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_reports_periodic_vat_xml_export__write_uid
msgid "Last Updated by"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__write_date
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__write_date
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325_wizard__write_date
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_reports_periodic_vat_xml_export__write_date
msgid "Last Updated on"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "Lastname, Firstname (natural persons)"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_281_50_pdf
msgid ""
"Lastname, Firstname (or denomination) and address of the recipient of the "
"income:"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_50_divider_pdf
msgid ""
"Lastname, Firstname (or denomination) of beneficiaries\n"
"                                    <br/>\n"
"                                    Street, n°, eventually box\n"
"                                    <br/>\n"
"                                    Zip code and city"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_res_partner__forms_281_50
#: model:ir.model.fields,help:l10n_be_reports.field_res_users__forms_281_50
msgid "List of 281.50 forms for this partner"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__message_has_error
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__message_ids
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__message_ids
msgid "Messages"
msgstr ""

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/res_partner.py:0
msgid "Missing partner data"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields.selection,name:l10n_be_reports.selection__l10n_be_form_325__treatment_type__1
#: model:ir.model.fields.selection,name:l10n_be_reports.selection__l10n_be_form_325_wizard__treatment_type__1
msgid "Modification"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_281_50_pdf
msgid "NIL"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_50_divider_pdf
msgid "NN or NE:"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "NUMBER OF FORMS"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_281_50__partner_name
msgid "Name of the partner when the form was created"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__partner_is_natural_person
msgid "Natural person"
msgstr ""

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_sales_report.py:0
#: code:addons/l10n_be_reports/models/partner_vat_listing.py:0
msgid "No VAT number associated with your company."
msgstr ""

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_report.py:0
msgid "No email address associated with company %s."
msgstr ""

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_sales_report.py:0
#: code:addons/l10n_be_reports/models/partner_vat_listing.py:0
msgid "No email address associated with the company."
msgstr ""

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_report.py:0
msgid "No phone associated with company %s."
msgstr ""

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_sales_report.py:0
#: code:addons/l10n_be_reports/models/partner_vat_listing.py:0
msgid "No phone associated with the company."
msgstr ""

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_sales_report.py:0
msgid "No vat number defined for %s."
msgstr ""

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_report.py:0
msgid "Not allowed negative amounts"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__message_needaction_counter
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__message_has_error_counter
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__message_has_error_counter
msgid "Number of errors"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_281_50__message_needaction_counter
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_325__message_needaction_counter
msgid "Number of messages requiring action"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_281_50__message_has_error_counter
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_325__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "N° 1"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "N° 2"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "N° 3"
msgstr ""

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/res_partner.py:0
#, python-format
msgid ""
"Only users with the access group '%s' can unset the 281.50 category on "
"partners."
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.form_325_view_form
msgid "Open"
msgstr ""

#. module: l10n_be_reports
#: model:ir.ui.menu,name:l10n_be_reports.menu_action_open_325_tree_view
msgid "Open 325 forms"
msgstr ""

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/res_partner.py:0
msgid "Open list"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_oioc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_oioc
msgid "Operating Income and Operating Charges"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_reports_periodic_vat_xml_export__calling_export_wizard_id
msgid ""
"Optional field containing the report export wizard calling this wizard, if "
"there is one."
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields.selection,name:l10n_be_reports.selection__l10n_be_form_325__treatment_type__0
#: model:ir.model.fields.selection,name:l10n_be_reports.selection__l10n_be_form_325_wizard__treatment_type__0
msgid "Original"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields.selection,name:l10n_be_reports.selection__l10n_be_form_325__sending_type__0
#: model:ir.model.fields.selection,name:l10n_be_reports.selection__l10n_be_form_325_wizard__sending_type__0
msgid "Original send"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_e_apl_oay
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_e_apl_oay
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_apl_oay
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e_apl_oay
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_apl_oay
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e_apl_oay
msgid "Other Appropriations of the Year"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "PAYROLL DEDUCTIONS"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl
msgid "PROFIT AND LOSS ACCOUNT"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__paid_amount
msgid "Paid amount"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__partner_id
msgid "Partner"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__partner_name
msgid "Partner Name"
msgstr ""

#. module: l10n_be_reports
#: model:account.report,name:l10n_be_reports.l10n_be_partner_vat_listing
#: model:account.report.line,name:l10n_be_reports.l10n_be_partner_vat_listing_line
#: model:ir.actions.client,name:l10n_be_reports.action_account_report_l10n_be_partner_vat_listing
#: model:ir.actions.client,name:l10n_be_reports.action_account_report_partner_vat_listing
#: model:ir.ui.menu,name:l10n_be_reports.menu_action_account_report_l10n_be_partner_vat_listing
msgid "Partner VAT Listing"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model,name:l10n_be_reports.model_l10n_be_partner_vat_handler
msgid "Partner VAT Listing Custom Handler"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_281_50__partner_id
msgid "Partner for which this 281.50 form has been created"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.view_partner_281_50_required_fields
msgid "Partner with missing information"
msgstr ""

#. module: l10n_be_reports
#. odoo-javascript
#: code:addons/l10n_be_reports/static/src/components/partner_vat_listing/warnings.xml:0
msgid "Partners sharing the same VAT number"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_account_reports_export_wizard__l10n_be_reports_periodic_vat_wizard_id
msgid "Periodic VAT Export Wizard"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "Postal code and city"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_281_50_pdf
msgid "Profession:"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_e_apl_ply
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_e_apl_ply
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_apl_ply
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e_apl_ply
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_apl_ply
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e_apl_ply
msgid "Profit (Loss) of the Year"
msgstr ""

#. module: l10n_be_reports
#: model:ir.actions.client,name:l10n_be_reports.action_account_report_be_pl_asso_a
#: model:ir.actions.client,name:l10n_be_reports.action_account_report_be_pl_asso_f
#: model:ir.actions.client,name:l10n_be_reports.action_account_report_be_pl_comp_a
#: model:ir.actions.client,name:l10n_be_reports.action_account_report_be_pl_comp_f
msgid "Profit and Loss"
msgstr ""

#. module: l10n_be_reports
#: model:account.report,name:l10n_be_reports.account_financial_report_pl_asso_a
msgid "Profit and Loss (Abbr Assoc)"
msgstr ""

#. module: l10n_be_reports
#: model:account.report,name:l10n_be_reports.account_financial_report_pl_comp_a
msgid "Profit and Loss (Abbr)"
msgstr ""

#. module: l10n_be_reports
#: model:account.report,name:l10n_be_reports.account_financial_report_pl_asso_f
msgid "Profit and Loss (Full Assoc)"
msgstr ""

#. module: l10n_be_reports
#: model:account.report,name:l10n_be_reports.account_financial_report_pl_comp_f
msgid "Profit and Loss (Full)"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_e_apl_plpy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_e_apl_plpy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_apl_plpy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e_apl_plpy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_apl_plpy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e_apl_plpy
msgid "Profits (Losses) from Previous Years"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid ""
"RECEPTION DATE\n"
"                                                        <br/>\n"
"                                                        (For administration use only)"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__reference_year
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325_wizard__reference_year
msgid "Reference Year"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__reference_year
msgid "Reference year"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model,name:l10n_be_reports.model_l10n_be_form_281_50
msgid "Represents a 281.50 form"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model,name:l10n_be_reports.model_l10n_be_form_325
msgid "Represents a 325 form"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__message_has_sms_error
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__message_has_sms_error
msgid "SMS Delivery error"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "SUMMARY STATEMENT YEAR"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "SUMMARY TABLE OF RECORDS AND PAYROLL DEDUCTIONS (1)"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields.selection,name:l10n_be_reports.selection__l10n_be_form_325__sending_type__1
#: model:ir.model.fields.selection,name:l10n_be_reports.selection__l10n_be_form_325_wizard__sending_type__1
msgid "Send grouped corrections"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__sender_id
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325_wizard__sender_id
msgid "Sender"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__sender_address
msgid "Sender Address"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__sender_bce_number
msgid "Sender BCE Number"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__sender_city
msgid "Sender City"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__sender_lang_code
msgid "Sender Language Code"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__sender_name
msgid "Sender Name"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__sender_phone_number
msgid "Sender Phone Number"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__sender_zip
msgid "Sender ZIP"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325_wizard__sending_type
msgid "Sending Type"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__sending_type
msgid "Sending type"
msgstr ""

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_report.py:0
msgid "Some fields required for the export are missing. Please specify them."
msgstr ""

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/res_partner.py:0
msgid ""
"Some partners are not correctly configured. Please be sure that the "
"following pieces of information are set: street, zip code, country%s and vat"
" or citizen identification."
msgstr ""

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_report.py:0
msgid "Specify"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__state
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__state
msgid "State"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "Street and n°"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "TOTAL AMOUNT"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "TOTALS"
msgstr ""

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_general_ledger.py:0
msgid "TXT"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.column,name:l10n_be_reports.account_financial_report_ec_sales_tax
msgid "Tax Code"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_res_partner__form_file
#: model:ir.model.fields,help:l10n_be_reports.field_res_users__form_file
msgid "Technical field to store all forms file."
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__is_test
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325_wizard__is_test
msgid "Test Form"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_325_wizard__sender_id
msgid "The company responsible for sending the form."
msgstr ""

#. module: l10n_be_reports
#. odoo-javascript
#: code:addons/l10n_be_reports/static/src/components/tax_report/warnings.xml:0
msgid "The following controls failed:"
msgstr ""

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/wizard/l10n_be_325_form_wizard.py:0
msgid "The reference year must be a number."
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_res_partner__citizen_identification
#: model:ir.model.fields,help:l10n_be_reports.field_res_users__citizen_identification
msgid ""
"This code corresponds to the personal identification number for the tax "
"authorities."
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_325__sending_type
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_325_wizard__sending_type
msgid ""
"This field allows to make an original sending(correspond to first send) or a"
" grouped corrections(if you have made some mistakes before)."
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_281_50__treatment_type
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_325__treatment_type
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_325_wizard__treatment_type
msgid "This field represents the nature of the form."
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_50_divider_pdf
msgid "To be completed only if this divider is not the first or the only one"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_50_divider_pdf
msgid "Total amount or to be transferred to the next divider:"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_50_divider_pdf
msgid "Total mentionned in box 3 e of the form 281.50"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__total_remuneration
msgid "Total remuneration"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__treatment_type
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__treatment_type
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325_wizard__treatment_type
msgid "Treatment Type"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.column,name:l10n_be_reports.l10n_be_partner_vat_listing_turnover
msgid "Turnover"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__user_id
msgid "User"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.column,name:l10n_be_reports.l10n_be_partner_vat_listing_vat_amount
msgid "VAT Amount"
msgstr ""

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/partner_vat_listing.py:0
msgid "VAT Listing Audit"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.column,name:l10n_be_reports.account_financial_report_ec_sales_vat
#: model:account.report.column,name:l10n_be_reports.l10n_be_partner_vat_listing_vat_number
msgid "VAT Number"
msgstr ""

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_sales_report.py:0
#: code:addons/l10n_be_reports/models/partner_vat_listing.py:0
msgid "View Partner"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__website_message_ids
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_281_50__website_message_ids
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_325__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_report.py:0
#: code:addons/l10n_be_reports/models/account_report.py:0
#: code:addons/l10n_be_reports/models/account_sales_report.py:0
#: code:addons/l10n_be_reports/models/account_sales_report.py:0
#: code:addons/l10n_be_reports/models/partner_vat_listing.py:0
#: code:addons/l10n_be_reports/models/partner_vat_listing.py:0
msgid "XML"
msgstr ""

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_report.py:0
msgid "XML Export Options"
msgstr ""

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_325_form.py:0
msgid "You already generated 281.50 forms for this 325 form."
msgstr ""

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_281_50_form.py:0
#: code:addons/l10n_be_reports/models/account_325_form.py:0
msgid ""
"You can't delete a 281.50 for which its form 325 xml has been generated"
msgstr ""

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/wizard/l10n_be_325_form_wizard.py:0
msgid "You can't use a reference year in the future or for the current year."
msgstr ""

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/wizard/l10n_be_325_form_wizard.py:0
msgid "You must be logged in a Belgian company to use this feature"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__partner_zip
msgid "Zip"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_281_50__partner_zip
msgid "Zip of the partner when the form was created"
msgstr ""

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_report.py:0
msgid ""
"[44] < ([00] + [01] + [02] + [03] + [45] + [46] + [47] + [48] + [49]) * 200 "
"if [44] > 99.999"
msgstr ""

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_report.py:0
#, python-format
msgid ""
"[44] < ([00] + [01] + [02] + [03] + [45] + [46] + [47] + [48] + [49]) * 200 "
"if [88] > 99.999"
msgstr ""

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_report.py:0
msgid "[55] > 0 if [86] > 0 or [88] > 0"
msgstr ""

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_report.py:0
msgid "[56] + [57] > 0 if [87] > 0"
msgstr ""

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_report.py:0
msgid "[88] < ([81] + [82] + [83] + [84]) * 100 if [88] > 99.999"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_281_50_pdf
msgid ""
"a) Commissions, brokerage, commercial discounts, etc:\n"
"                                                <br/>\n"
"                                                b) Fees or vacations:\n"
"                                                <br/>\n"
"                                                c) Benefits in kind (nature : ................)\n"
"                                                <br/>\n"
"                                                d) Expenses incurred on behalf of the beneficiary:\n"
"                                                <br/>\n"
"                                                e) Total (see also in sections f and g below):"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.res_partner_view_form_inherit
msgid "e.g. 123455 555 6"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.form_325_wizard_view_form
msgid "e.g. 2018"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "established by :"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_281_50_pdf
msgid ""
"f) Enter here, if applicable, the amount included in item e\n"
"                                            above relates to compensation paid to:\n"
"                                            <br/>\n"
"                                            - athletes for their sports performances:\n"
"                                            <br/>\n"
"                                            - trainers, coaches and accompaniers for their activity for the\n"
"                                            benefit of the athletes:"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_281_50_pdf
msgid ""
"g) If the amount indicated in item e above does not coincide with\n"
"                                                the amount actually paid in"
msgstr ""

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/partner_vat_listing.py:0
#, python-format
msgid "Missing partners"
msgstr ""

#. module: l10n_be_reports
#. odoo-javascript
#: code:addons/l10n_be_reports/static/src/components/partner_vat_listing/warnings.xml:0
#, python-format
msgid ""
"customers with a turnover of more than 250€ or one or more credit notes in "
"the selected period who are not included in the report. Click"
msgstr ""

#. module: l10n_be_reports
#. odoo-javascript
#: code:addons/l10n_be_reports/static/src/components/partner_vat_listing/warnings.xml:0
#, python-format
msgid "You have"
msgstr ""

#. module: l10n_be_reports
#. odoo-javascript
#: code:addons/l10n_be_reports/static/src/components/partner_vat_listing/warnings.xml:0
#, python-format
msgid "here"
msgstr ""

#. module: l10n_be_reports
#. odoo-javascript
#: code:addons/l10n_be_reports/static/src/components/partner_vat_listing/warnings.xml:0
#, python-format
msgid "to see the list."
msgstr ""

#. module: l10n_be_reports
#. odoo-javascript
#: code:addons/l10n_be_reports/static/src/components/partner_vat_listing/warnings.xml:0
msgid "will be grouped in the XML export."
msgstr ""
