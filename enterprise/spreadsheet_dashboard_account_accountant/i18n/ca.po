# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* spreadsheet_dashboard_account_accountant
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# jabiri7, 2024
# <AUTHOR> <EMAIL>, 2024
# Jonatan Gk, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 09:15+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "(Net sales - Total expenses) / Net sales"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "(Net sales – COGS) / Net sales"
msgstr "(Ventes netes – COGS) / Vendes netes"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "< 0.7: company might be stuck with non liquid assets"
msgstr " < 0,7: l'empresa podria quedar atrapada amb actius no líquids"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "< 0.8: income might be too low"
msgstr "< 0,8: els ingressos poden ser massa baixos"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "< 0: company might not be able to meet obligations"
msgstr "< 0: és possible que l'empresa no pugui complir amb les obligacions"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "< 1: weak financial performance"
msgstr "< 1: rendiment financer feble"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "< 2.5: mature company who accumulated a lot of money"
msgstr "< 2,5: empresa madura que va acumular molts diners"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "< 20%: hard to become profitable"
msgstr "< 20%: difícil de ser rendible"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "< 3%: not efficient at generating business"
msgstr "< 3%: no eficient per generar negoci"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "< 45: company gets paid for sales quickly"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "< 45: company liquidates debts to suppliers quickly"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "< 5%: not efficient at operating business"
msgstr "< 5%: no eficient en negoci operatiu"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "> 0: company can meet financial obligations at any time"
msgstr ""
"> 0: l'empresa pot fer front a les obligacions financeres en qualsevol "
"moment"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "> 1.5: strong financial performance"
msgstr "> 1,5: bon rendiment financer"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "> 10%: very efficient"
msgstr "> 10%: molt eficient"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "> 10%: very efficient at operating business"
msgstr "> 10%: molt eficient en negoci operatiu"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "> 1: company in highly solvent position"
msgstr "> 1: empresa en posició altament solvent"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "> 1: income allow to meet financial obligations"
msgstr "> 1: els ingressos permeten fer front a les obligacions econòmiques"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "> 50%: hugely profitable business"
msgstr "> 50%: negoci molt rendible"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "> 5: company owns a lot of debt and not of a lot of own money"
msgstr "> 5: l'empresa té molts deutes i no molts diners propis"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "> 60: company might not get paid for sales quickly enough"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "> 70: company might be slow to pay suppliers"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Account type"
msgstr "Tipus de compte"

#. module: spreadsheet_dashboard_account_accountant
#: model:spreadsheet.dashboard,name:spreadsheet_dashboard_account_accountant.dashboard_accounting
msgid "Accounting"
msgstr "Comptabilitat"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Accounts"
msgstr "Comptes"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Aggregate"
msgstr "Agrega"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Average creditors days"
msgstr "Termini mitjà de cobrament"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Average debtor days"
msgstr "Mitjana de dies deutors"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Average debtors days"
msgstr "Termini mitjà de pagament"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Average payable days"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_sample_dashboard.json:0
msgid "Balance sheet"
msgstr "Full de balanç"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Bank and Cash"
msgstr "Banc i caixa"

#. module: spreadsheet_dashboard_account_accountant
#: model:spreadsheet.dashboard,name:spreadsheet_dashboard_account_accountant.dashboard_benchmark
msgid "Benchmark"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Best if compared with competitors"
msgstr "La millor si es compara amb els competidors"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "COGS"
msgstr "COGS"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_sample_dashboard.json:0
msgid "Cash"
msgstr "Caixa"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Cash asset ratio"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Cash flow"
msgstr "Flux de caixa"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Cash flow / Current liabilities"
msgstr "Flux d'efectiu/Passiu corrent"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Cash flow ratio"
msgstr "Relació de flux de caixa"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Cash received"
msgstr "Recepció d'efectiu"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Cash spent"
msgstr "Efectiu gastat"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Cash surplus"
msgstr "Superàvit d'efectiu"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Closing bank balance"
msgstr "Tancar balanç de bancs"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Code"
msgstr "Codi"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Cost of Revenue"
msgstr "Cost directe de les vendes"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Cost of revenue"
msgstr "Cost dels ingressos"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Credit Card"
msgstr "Targeta de crèdit"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Current Assets"
msgstr "Actius circulants"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Current Liabilities"
msgstr "Passius circulants"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Current assets / Current liabilities"
msgstr "Actius corrents / Passius corrents"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Current assets to liabilities"
msgstr "Actius corrents a passius"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Current assets – Current liabilities"
msgstr "Actius corrents – Passius corrents"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_sample_dashboard.json:0
msgid "Current expense"
msgstr "Despesa corrent"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_sample_dashboard.json:0
msgid "Current income"
msgstr "Ingressos corrents"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Current ratio"
msgstr "Proporció actual"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Debt ratio"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Debt to Equity"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Debt-to-equity"
msgstr "Deute a capital"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Depreciation"
msgstr "Amortització"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "EBIT / Net sales"
msgstr "EBIT / Vendes netes"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Equity"
msgstr "Patrimoni"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Expenses"
msgstr "Despeses"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Financial balance"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Financial independence"
msgstr "Independència financera"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Fixed Assets"
msgstr "Actius fixos"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Formula"
msgstr "Fórmula"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Gross profit"
msgstr "Benefici brut"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Gross profit margin"
msgstr "Marge de benefici brut"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Income"
msgstr "Ingrés"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_sample_dashboard.json:0
msgid "Invoiced"
msgstr "Facturat"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "KPI"
msgstr "KPI"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_sample_dashboard.json:0
msgid "KPIS"
msgstr "KPIS"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_sample_dashboard.json:0
msgid "Liquidity"
msgstr "Liquiditat"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Long term working capital"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Month"
msgstr "Mes"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Month-Year"
msgstr "Mes Any"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Net Credit Purchases / Average accounts payable balance for period"
msgstr "Compres de crèdit net/Saldo mitjà dels comptes a pagar per al període"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Net assets"
msgstr "Actius nets"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Net income / Revenue"
msgstr "Ingressos nets / Ingressos"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Net profit"
msgstr "Benefici net"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Net profit margin"
msgstr "Marge de benefici net"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Net sales"
msgstr "Les vendes netes"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Non-current Assets"
msgstr "Actius no corrents"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Non-current Liabilities"
msgstr "Passius no corrents"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Off-Balance Sheet"
msgstr "Full de sortida"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Offset -1 - Month"
msgstr "Desplaçament -1 - Mes"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Offset -1 - Month-Year"
msgstr "Offset -1 - Mes-Any"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Offset -1 - Start date"
msgstr "Offset -1 - Data d'inici"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Offset -1 - Year"
msgstr "Offset -1 - Any"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Offset -4 - Month"
msgstr "Offset -4 - Mes"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Offset -4 - Month-Year"
msgstr "Offset -4 - Mes-Any"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Offset -4 - Start date"
msgstr "Offset -4 - Data d'inici"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Offset -4 - Year"
msgstr "Offset -4 - Any"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Operating margin"
msgstr "Marge d'explotació"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Other Income"
msgstr "Altre ingrés"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Other variables"
msgstr "Altres variables"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Payable"
msgstr "A pagar"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_sample_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Payables"
msgstr "A Pagar"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Payables / Expenses * 30"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_sample_dashboard.json:0
msgid "Performance"
msgstr "Rendiment"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Permanence"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_sample_dashboard.json:0
msgid "Position"
msgstr "Posició"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Prepayments"
msgstr "Prepagaments"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_sample_dashboard.json:0
msgid "Previous expense"
msgstr "Despesa prèvia"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_sample_dashboard.json:0
msgid "Previous income"
msgstr "Ingressos anteriors"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_sample_dashboard.json:0
msgid "Profitability"
msgstr "Rendibilitat"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Quick assets"
msgstr "Actius ràpids"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Quick assets / Current liabilities"
msgstr "Actius ràpids / Passius corrents"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Quick ratio"
msgstr "Relació ràpida"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Receivable"
msgstr "A cobrar"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_sample_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Receivables"
msgstr "A cobrar"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Receivables / Income * 30"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Return on equity"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Return on investments"
msgstr "Retorn de les inversions"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Sales on account / Average accounts receivable balance for period"
msgstr "Vendes a compte / Saldo mitjà dels comptes a cobrar per al període"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Short term cash forecast"
msgstr "Previsió d'efectiu a curt termini"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_sample_dashboard.json:0
msgid "Solvency"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Start date"
msgstr "Data inicial"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Technical name"
msgstr "Nom tècnic"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Total Current assets"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Total Current liabilities"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Total assets"
msgstr "Els actius totals"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Total expense"
msgstr "Despesa total"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Total income"
msgstr "Ingressos totals"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Total liabilities"
msgstr "Passius totals"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Total liabilities / Total shareholders’ equity"
msgstr "Total passiu / Total patrimonial"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Total shareholder's equity"
msgstr "Patrimoni total de l'accionista"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Value"
msgstr "Valor"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Very dependent on the sector"
msgstr "Molt dependent del sector"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Working capital"
msgstr "Capital circulant"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Year"
msgstr "Any"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "asset_cash"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "asset_current"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "asset_fixed"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "asset_non_current"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "asset_prepayments"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "asset_receivable"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "bank and cash"
msgstr "banc i efectiu"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "cost of revenue"
msgstr "cost dels ingressos"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "equity"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "expense"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "expense_depreciation"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "expense_direct_cost"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "expenses + depreciation + cost of revenue"
msgstr "despeses + amortització + cost dels ingressos"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "income"
msgstr "ingressos"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "income + other income"
msgstr "ingressos + altres ingressos"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "income_other"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_sample_dashboard.json:0
msgid "last period"
msgstr "darrer període"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "liability_credit_card"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "liability_current"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "liability_non_current"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "liability_payable"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "net sales - COGS"
msgstr "vendes netes - COGS"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid ""
"number of times you can pay off current debts with cash generated per year"
msgstr ""
"nombre de vegades que podeu pagar els deutes actuals amb efectiu generat a "
"l'any"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "off_balance"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "payable"
msgstr "a pagar"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "payable + credit card + current liabilities"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "payable + credit card + current liabilities + non-current liabilities"
msgstr "a pagar + targeta de crèdit + passiu corrent + passiu no corrent"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "possible issue in COGS (Cost of Goods sold)"
msgstr "possible problema en COGS (cost de la mercaderia venuda)"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "possible issue in direct and indirect costs"
msgstr "possible problema en costos directes i indirectes"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid ""
"possible issue in payment terms agreement with clients to get paid faster"
msgstr ""
"possible problema en termes de pagament acord amb clients per cobrar més "
"ràpidament"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid ""
"possible issue in providers payments, could lead to loss of suppliers trust"
msgstr ""
"possible problema en els pagaments dels proveïdors, podria provocar la "
"pèrdua de confiança dels proveïdors"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "possible issue in ressources allocation or missed growth opportunities"
msgstr ""
"possible problema en l'assignació de recursos o oportunitats de creixement "
"perdudes"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "possible issue in the business model"
msgstr "possible problema en el model de negoci"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "possible issue with asset distribution and cash availability"
msgstr ""
"possible problema amb la distribució d'actius i la disponibilitat d'efectiu"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "possible issues in cash availability at short term"
msgstr "possibles problemes de disponibilitat d'efectiu a curt termini"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "receivable"
msgstr "a cobrar"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid ""
"receivable + bank and cash + current assets + non-current assets + "
"prepayments + fixed assets"
msgstr ""
"a cobrar + banc i efectiu + actiu corrent + actiu no corrent + pagaments "
"anticipats + immobilitzat"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "receivable + bank and cash + current assets + prepayments"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "receivable + bank and cash + prepayments"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_sample_dashboard.json:0
msgid "to pay"
msgstr "pagar"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_sample_dashboard.json:0
msgid "to receive"
msgstr "rebre"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "total assets - total liabilities"
msgstr "actiu total - passiu total"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "total income - total expense"
msgstr "ingressos totals - despesa total"
