# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* spreadsheet_dashboard_account_accountant
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 09:15+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Hindi (https://app.transifex.com/odoo/teams/41243/hi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "(Net sales - Total expenses) / Net sales"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "(Net sales – COGS) / Net sales"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "< 0.7: company might be stuck with non liquid assets"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "< 0.8: income might be too low"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "< 0: company might not be able to meet obligations"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "< 1: weak financial performance"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "< 2.5: mature company who accumulated a lot of money"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "< 20%: hard to become profitable"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "< 3%: not efficient at generating business"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "< 45: company gets paid for sales quickly"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "< 45: company liquidates debts to suppliers quickly"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "< 5%: not efficient at operating business"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "> 0: company can meet financial obligations at any time"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "> 1.5: strong financial performance"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "> 10%: very efficient"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "> 10%: very efficient at operating business"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "> 1: company in highly solvent position"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "> 1: income allow to meet financial obligations"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "> 50%: hugely profitable business"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "> 5: company owns a lot of debt and not of a lot of own money"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "> 60: company might not get paid for sales quickly enough"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "> 70: company might be slow to pay suppliers"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Account type"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#: model:spreadsheet.dashboard,name:spreadsheet_dashboard_account_accountant.dashboard_accounting
msgid "Accounting"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Accounts"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Aggregate"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Average creditors days"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Average debtor days"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Average debtors days"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Average payable days"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_sample_dashboard.json:0
msgid "Balance sheet"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Bank and Cash"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#: model:spreadsheet.dashboard,name:spreadsheet_dashboard_account_accountant.dashboard_benchmark
msgid "Benchmark"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Best if compared with competitors"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "COGS"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_sample_dashboard.json:0
msgid "Cash"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Cash asset ratio"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Cash flow"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Cash flow / Current liabilities"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Cash flow ratio"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Cash received"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Cash spent"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Cash surplus"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Closing bank balance"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Code"
msgstr "कोड"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Cost of Revenue"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Cost of revenue"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Credit Card"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Current Assets"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Current Liabilities"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Current assets / Current liabilities"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Current assets to liabilities"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Current assets – Current liabilities"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_sample_dashboard.json:0
msgid "Current expense"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_sample_dashboard.json:0
msgid "Current income"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Current ratio"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Debt ratio"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Debt to Equity"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Debt-to-equity"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Depreciation"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "EBIT / Net sales"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Equity"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Expenses"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Financial balance"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Financial independence"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Fixed Assets"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Formula"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Gross profit"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Gross profit margin"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Income"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_sample_dashboard.json:0
msgid "Invoiced"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "KPI"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_sample_dashboard.json:0
msgid "KPIS"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_sample_dashboard.json:0
msgid "Liquidity"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Long term working capital"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Month"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Month-Year"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Net Credit Purchases / Average accounts payable balance for period"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Net assets"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Net income / Revenue"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Net profit"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Net profit margin"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Net sales"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Non-current Assets"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Non-current Liabilities"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Off-Balance Sheet"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Offset -1 - Month"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Offset -1 - Month-Year"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Offset -1 - Start date"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Offset -1 - Year"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Offset -4 - Month"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Offset -4 - Month-Year"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Offset -4 - Start date"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Offset -4 - Year"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Operating margin"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Other Income"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Other variables"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Payable"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_sample_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Payables"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Payables / Expenses * 30"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_sample_dashboard.json:0
msgid "Performance"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Permanence"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_sample_dashboard.json:0
msgid "Position"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Prepayments"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_sample_dashboard.json:0
msgid "Previous expense"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_sample_dashboard.json:0
msgid "Previous income"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_sample_dashboard.json:0
msgid "Profitability"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Quick assets"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Quick assets / Current liabilities"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Quick ratio"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Receivable"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_sample_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Receivables"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Receivables / Income * 30"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Return on equity"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Return on investments"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Sales on account / Average accounts receivable balance for period"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Short term cash forecast"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_sample_dashboard.json:0
msgid "Solvency"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Start date"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Technical name"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Total Current assets"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Total Current liabilities"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Total assets"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Total expense"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Total income"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Total liabilities"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Total liabilities / Total shareholders’ equity"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Total shareholder's equity"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Value"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Very dependent on the sector"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Working capital"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Year"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "asset_cash"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "asset_current"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "asset_fixed"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "asset_non_current"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "asset_prepayments"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "asset_receivable"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "bank and cash"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "cost of revenue"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "equity"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "expense"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "expense_depreciation"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "expense_direct_cost"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "expenses + depreciation + cost of revenue"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "income"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "income + other income"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "income_other"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_sample_dashboard.json:0
msgid "last period"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "liability_credit_card"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "liability_current"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "liability_non_current"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "liability_payable"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "net sales - COGS"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid ""
"number of times you can pay off current debts with cash generated per year"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "off_balance"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "payable"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "payable + credit card + current liabilities"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "payable + credit card + current liabilities + non-current liabilities"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "possible issue in COGS (Cost of Goods sold)"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "possible issue in direct and indirect costs"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid ""
"possible issue in payment terms agreement with clients to get paid faster"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid ""
"possible issue in providers payments, could lead to loss of suppliers trust"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "possible issue in ressources allocation or missed growth opportunities"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "possible issue in the business model"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "possible issue with asset distribution and cash availability"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "possible issues in cash availability at short term"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "receivable"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid ""
"receivable + bank and cash + current assets + non-current assets + "
"prepayments + fixed assets"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "receivable + bank and cash + current assets + prepayments"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "receivable + bank and cash + prepayments"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_sample_dashboard.json:0
msgid "to pay"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_sample_dashboard.json:0
msgid "to receive"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "total assets - total liabilities"
msgstr ""

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "total income - total expense"
msgstr ""
