# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_online_synchronization
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:52+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2025\n"
"Language-Team: Portuguese (Brazil) (https://app.transifex.com/odoo/teams/41243/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid ""
"\n"
"\n"
"If you've already opened a ticket for this issue, don't report it again: a support agent will contact you shortly."
msgstr ""
"\n"
"\n"
"Se você já abriu um chamado para esse problema, não o relate novamente: um agente de suporte entrará em contato com você em breve."

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/wizard/account_journal_duplicate_transactions.py:0
msgid "%s duplicate transactions"
msgstr "%s transações duplicadas"

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/transient_bank_statement_line_list_view/transient_bank_statement_line_list_view.xml:0
msgid ""
").\n"
"                    This might cause duplicate entries."
msgstr ""
").\n"
"                    Isso pode gerar lançamentos duplicados."

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/refresh_spin_journal_widget/refresh_spin_journal_widget.xml:0
msgid "0 transaction fetched"
msgstr "0 transações recuperadas"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_duplicate_transaction_wizard_view_form
msgid "<i class=\"fa fa-trash me-1\"/> Delete Selected"
msgstr "<i class=\"fa fa-trash me-1\"/> Excluir selecionados"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_journal_form
msgid "<i class=\"oi oi-arrow-right me-2\"/> Send Now"
msgstr "<i class=\"oi oi-arrow-right me-2\"/> Enviar agora"

#. module: account_online_synchronization
#: model:mail.template,body_html:account_online_synchronization.email_template_sync_reminder
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #FFFFFF; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\">\n"
"                    <tr>\n"
"                        <td align=\"center\">\n"
"                            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: #FFFFFF; color: #454748; border-collapse:separate;\">\n"
"                                <tbody>\n"
"                                    <!-- CONTENT -->\n"
"                                    <tr>\n"
"                                        <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                                <tr>\n"
"                                                    <td valign=\"top\" style=\"font-size: 13px;\">\n"
"                                                        <div>\n"
"                                                            Hello,<br/><br/>\n"
"                                                            The connection between <b><a t-att-href=\"object.get_base_url()\" t-out=\"object.get_base_url() or ''\">https://yourcompany.odoo.com</a></b> and <t t-out=\"object.account_online_link_id.name or ''\">Belfius</t> <t t-if=\"not object.expiring_synchronization_due_day\">expired.</t><t t-else=\"\">expires in <t t-out=\"object.expiring_synchronization_due_day or ''\">10</t> days.</t><br/>\n"
"                                                            <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                                                                <a t-attf-href=\"{{ website_url }}/renew_consent/{{ object.id }}?access_token={{object.access_token}}\" style=\"background-color: #4caf50; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                                                    Renew Consent\n"
"                                                                </a>\n"
"                                                            </div>\n"
"                                                            Security Tip: Check that the domain name you are redirected to is: <b><a t-att-href=\"object.get_base_url()\" t-out=\"object.get_base_url() or ''\">https://yourcompany.odoo.com</a></b>\n"
"                                                        </div>\n"
"                                                    </td>\n"
"                                                </tr>\n"
"                                                <tr>\n"
"                                                    <td style=\"text-align:center;\">\n"
"                                                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                                    </td>\n"
"                                                </tr>\n"
"                                            </table>\n"
"                                        </td>\n"
"                                    </tr>\n"
"                                </tbody>\n"
"                            </table>\n"
"                        </td>\n"
"                    </tr>\n"
"                    <!-- POWERED BY -->\n"
"                    <tr>\n"
"                        <td align=\"center\" style=\"min-width: 590px;\">\n"
"                            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"                                <tr>\n"
"                                    <td style=\"text-align: center; font-size: 13px;\">\n"
"                                        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=auth\" style=\"color: #875A7B;\">Odoo</a>\n"
"                                    </td>\n"
"                                </tr>\n"
"                            </table>\n"
"                            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"                                <tr>\n"
"                                    <td style=\"text-align: center; font-size: 11px;\">\n"
"                                        PS: This is an automated email sent by Odoo Accounting to remind you before a bank sync consent expiration.\n"
"                                    </td>\n"
"                                </tr>\n"
"                            </table>\n"
"                        </td>\n"
"                    </tr>\n"
"                </table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #FFFFFF; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\">\n"
"                    <tr>\n"
"                        <td align=\"center\">\n"
"                            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: #FFFFFF; color: #454748; border-collapse:separate;\">\n"
"                                <tbody>\n"
"                                    <!-- CONTEÚDO -->\n"
"                                    <tr>\n"
"                                        <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                                <tr>\n"
"                                                    <td valign=\"top\" style=\"font-size: 13px;\">\n"
"                                                        <div>\n"
"                                                            Olá,<br/><br/>\n"
"                                                            A conexão entre <b><a t-att-href=\"object.get_base_url()\" t-out=\"object.get_base_url() or ''\">https://suaempresa.odoo.com</a></b> e <t t-out=\"object.account_online_link_id.name or ''\">Belfius</t> <t t-if=\"not object.expiring_synchronization_due_day\">expirou.</t><t t-else=\"\">expira em <t t-out=\"object.expiring_synchronization_due_day or ''\">10</t> dias.</t><br/>\n"
"                                                            <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                                                                <a t-attf-href=\"{{ website_url }}/renew_consent/{{ object.id }}?access_token={{object.access_token}}\" style=\"background-color: #4caf50; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                                                    Renovar autorização\n"
"                                                                </a>\n"
"                                                            </div>\n"
"                                                            Dica de segurança: verifique se o nome de domínio ao qual você é direcionado é: <b><a t-att-href=\"object.get_base_url()\" t-out=\"object.get_base_url() or ''\">https://suaempresa.odoo.com</a></b>\n"
"                                                        </div>\n"
"                                                    </td>\n"
"                                                </tr>\n"
"                                                <tr>\n"
"                                                    <td style=\"text-align:center;\">\n"
"                                                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                                    </td>\n"
"                                                </tr>\n"
"                                            </table>\n"
"                                        </td>\n"
"                                    </tr>\n"
"                                </tbody>\n"
"                            </table>\n"
"                        </td>\n"
"                    </tr>\n"
"                    <!-- POWERED BY -->\n"
"                    <tr>\n"
"                        <td align=\"center\" style=\"min-width: 590px;\">\n"
"                            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"                                <tr>\n"
"                                    <td style=\"text-align: center; font-size: 13px;\">\n"
"                                        Desenvolvido por <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=auth\" style=\"color: #875A7B;\">Odoo</a>\n"
"                                    </td>\n"
"                                </tr>\n"
"                            </table>\n"
"                            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"                                <tr>\n"
"                                    <td style=\"text-align: center; font-size: 11px;\">\n"
"                                        PS: Este é um e-mail automático enviado pelo Odoo Financeiro para te lembrar da expiração de autorização de uma sincronização bancária.\n"
"                                    </td>\n"
"                                </tr>\n"
"                            </table>\n"
"                        </td>\n"
"                    </tr>\n"
"                </table>\n"
"            "

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__access_token
msgid "Access Token"
msgstr "Token de acesso"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__account_data
msgid "Account Data"
msgstr "Dados da conta"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__name
msgid "Account Name"
msgstr "Nome da conta"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_account__name
msgid "Account Name as provided by third party provider"
msgstr "Nome da conta, conforme fornecido por provedor de terceiros"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__account_number
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__account_number
msgid "Account Number"
msgstr "Número da conta"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_selection__account_online_account_ids
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__online_account_id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_journal__account_online_account_id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__account_online_account_ids
msgid "Account Online Account"
msgstr "Conta online da conta"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_selection__account_online_link_id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line__online_link_id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_journal__account_online_link_id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__account_online_link_id
msgid "Account Online Link"
msgstr "Link online da conta"

#. module: account_online_synchronization
#: model:ir.actions.server,name:account_online_synchronization.online_sync_cron_waiting_synchronization_ir_actions_server
msgid "Account: Journal online Waiting Synchronization"
msgstr "Conta: Diário online aguardando sincronização"

#. module: account_online_synchronization
#: model:ir.actions.server,name:account_online_synchronization.online_sync_cron_ir_actions_server
msgid "Account: Journal online sync"
msgstr "Conta: Sincronização online de diário"

#. module: account_online_synchronization
#: model:ir.actions.server,name:account_online_synchronization.online_sync_unused_connection_cron_ir_actions_server
msgid "Account: Journal online sync cleanup unused connections"
msgstr ""
"Conta: Limpeza de conexões não utilizadas na sincronização online de diário"

#. module: account_online_synchronization
#: model:ir.actions.server,name:account_online_synchronization.online_sync_mail_cron_ir_actions_server
msgid "Account: Journal online sync reminder"
msgstr "Conta: Lembrete de sincronização online de diário"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_needaction
msgid "Action Needed"
msgstr "Requer ação"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__activity_ids
msgid "Activities"
msgstr "Atividades"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Decoração de atividade excepcional"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__activity_state
msgid "Activity State"
msgstr "Status da atividade"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ícone do tipo de atividade"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__amount
msgid "Amount"
msgstr "Valor"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__amount_currency
msgid "Amount in Currency"
msgstr "Valor na moeda"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_attachment_count
msgid "Attachment Count"
msgstr "Total de anexos"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__auto_sync
msgid "Automatic synchronization"
msgstr "Sincronização automática"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__balance
msgid "Balance"
msgstr "Saldo"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_account__balance
msgid "Balance of the account sent by the third party provider"
msgstr "Saldo da conta enviado por provedor de terceiros"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.portal_renew_consent
msgid "Bank"
msgstr "Banco"

#. module: account_online_synchronization
#: model:ir.model,name:account_online_synchronization.model_account_online_link
msgid "Bank Connection"
msgstr "Conexão bancária"

#. module: account_online_synchronization
#: model:ir.model,name:account_online_synchronization.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr "Linha de extrato bancário"

#. module: account_online_synchronization
#: model:mail.activity.type,name:account_online_synchronization.bank_sync_activity_update_consent
msgid "Bank Synchronization: Update consent"
msgstr "Sincronização bancária: atualizar autorização"

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid "Bank Synchronization: Update your consent"
msgstr "Sincronização bancária: atualize sua autorização"

#. module: account_online_synchronization
#: model:mail.template,name:account_online_synchronization.email_template_sync_reminder
msgid "Bank connection expiration reminder"
msgstr "Lembrete de expiração da conexão bancária"

#. module: account_online_synchronization
#: model:ir.model,name:account_online_synchronization.model_bank_rec_widget
msgid "Bank reconciliation widget for a single statement line"
msgstr "Widget de reconciliação bancária para linhas individuais de extrato"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_missing_transaction_wizard_view_form
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.view_account_bank_selection_form_wizard
msgid "Cancel"
msgstr "Cancelar"

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid "Check the documentation"
msgstr "Verifique a documentação"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_duplicate_transaction_wizard_view_form
msgid ""
"Choose a date and a journal from which you want to check the transactions."
msgstr ""
"Selecione uma data e um diário dos quais deseja verificar as transações."

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_missing_transaction_wizard_view_form
msgid "Choose a date and a journal from which you want to fetch transactions"
msgstr "Selecione uma data e um diário de onde você quer recuperar transações"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__client_id
msgid "Client"
msgstr "Cliente"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_online_link_view_form
msgid "Client id"
msgstr "ID do cliente"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_journal__renewal_contact_email
msgid ""
"Comma separated list of email addresses to send consent renewal "
"notifications 15, 3 and 1 days before expiry"
msgstr ""
"Lista de endereços de e-mail separados por vírgula para enviar notificações "
"de renovação de autorização 15 dias, 3 dias e 1 dia antes da expiração"

#. module: account_online_synchronization
#: model:ir.model,name:account_online_synchronization.model_res_company
msgid "Companies"
msgstr "Empresas"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__company_id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__company_id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__company_id
msgid "Company"
msgstr "Empresa"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_online_link_view_form
msgid "Connect"
msgstr "Conectar"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.view_account_bank_selection_form_wizard
msgid "Connect Bank"
msgstr "Conectar banco"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_journal_dashboard_inherit_online_sync
msgid "Connect bank"
msgstr "Conectar banco"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.portal_renew_consent
msgid "Connect my Bank"
msgstr "Conectar meu banco"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.portal_renew_consent
msgid "Connect your bank account to Odoo"
msgstr "Conecte sua conta bancária ao Odoo"

#. module: account_online_synchronization
#: model:ir.model.fields.selection,name:account_online_synchronization.selection__account_online_link__state__connected
msgid "Connected"
msgstr "Conectado"

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/connected_until_widget/connected_until_widget.xml:0
msgid "Connected until"
msgstr "Conectado até"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_journal__renewal_contact_email
msgid "Connection Requests"
msgstr "Solicitações de conexão"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__connection_state_details
msgid "Connection State Details"
msgstr "Detalhes do estado de conexão"

#. module: account_online_synchronization
#: model:mail.message.subtype,name:account_online_synchronization.bank_sync_consent_renewal
msgid "Consent Renewal"
msgstr "Renovação de consentimento"

#. module: account_online_synchronization
#: model:ir.model,name:account_online_synchronization.model_res_partner
msgid "Contact"
msgstr "Contato"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_selection__create_uid
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__create_uid
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_duplicate_transaction_wizard__create_uid
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_missing_transaction_wizard__create_uid
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__create_uid
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__create_uid
msgid "Created by"
msgstr "Criado por"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_selection__create_date
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__create_date
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_duplicate_transaction_wizard__create_date
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_missing_transaction_wizard__create_date
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__create_date
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__create_date
msgid "Created on"
msgstr "Criado em"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__currency_id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__currency_id
msgid "Currency"
msgstr "Moeda"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__date
msgid "Date"
msgstr "Data"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_journal__expiring_synchronization_date
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__expiring_synchronization_date
msgid "Date when the consent for this connection expires"
msgstr "Data de expiração da autorização para essa conexão"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_selection__display_name
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__display_name
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_duplicate_transaction_wizard__display_name
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_missing_transaction_wizard__display_name
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__display_name
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__display_name
msgid "Display Name"
msgstr "Nome exibido"

#. module: account_online_synchronization
#: model:ir.model.fields.selection,name:account_online_synchronization.selection__account_online_account__fetching_status__done
msgid "Done"
msgstr "Concluído"

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_bank_statement.py:0
#: model:ir.model.fields.selection,name:account_online_synchronization.selection__account_online_link__state__error
msgid "Error"
msgstr "Erro"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_journal__expiring_synchronization_date
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__expiring_synchronization_date
msgid "Expiring Synchronization Date"
msgstr "Data de expiração da sincronização"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_journal__expiring_synchronization_due_day
msgid "Expiring Synchronization Due Day"
msgstr "Dia da expiração da sincronização"

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/connected_until_widget/connected_until_widget.xml:0
msgid "Extend Connection"
msgstr "Estender conexão"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_account__account_data
msgid "Extra information needed by third party provider"
msgstr "Informações adicionais necessárias para o provedor de terceiros"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_missing_transaction_wizard_view_form
msgid "Fetch"
msgstr "Recuperar"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_duplicate_transaction_wizard_view_form
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_missing_transaction_wizard_view_form
msgid "Fetch Missing Bank Statements Wizard"
msgstr "Assistente de recuperação de extratos bancários ausentes"

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/refresh_spin_journal_widget/refresh_spin_journal_widget.xml:0
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_online_link_view_form
msgid "Fetch Transactions"
msgstr "Recuperar transações"

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid "Fetched Transactions"
msgstr "Transações recuperadas"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_journal__online_sync_fetching_status
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__fetching_status
msgid "Fetching Status"
msgstr "Status de recuperação"

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/refresh_spin_journal_widget/refresh_spin_journal_widget.xml:0
msgid "Fetching..."
msgstr "Recuperando…"

#. module: account_online_synchronization
#. odoo-javascript
#. odoo-python
#: code:addons/account_online_synchronization/models/account_journal.py:0
#: code:addons/account_online_synchronization/static/src/components/bank_reconciliation/find_duplicate_transactions_cog_menu.xml:0
msgid "Find Duplicate Transactions"
msgstr "Localizar transações duplicadas"

#. module: account_online_synchronization
#. odoo-javascript
#. odoo-python
#: code:addons/account_online_synchronization/models/account_journal.py:0
#: code:addons/account_online_synchronization/static/src/components/bank_reconciliation/fetch_missing_transactions_cog_menu.xml:0
msgid "Find Missing Transactions"
msgstr "Localizar transações ausentes"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_duplicate_transaction_wizard__first_ids_in_group
msgid "First Ids In Group"
msgstr "Primeiros IDs do grupo"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_follower_ids
msgid "Followers"
msgstr "Seguidores"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidores (parceiros)"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Ícone do Font Awesome. Ex.: fa-tasks"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__foreign_currency_id
msgid "Foreign Currency"
msgstr "Moeda estrangeira"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__has_message
msgid "Has Message"
msgstr "Tem mensagens"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__has_unlinked_accounts
msgid "Has Unlinked Accounts"
msgstr "Tem contas desvinculadas"

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid "Here"
msgstr "Aqui"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_selection__id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_duplicate_transaction_wizard__id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_missing_transaction_wizard__id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__id
msgid "ID"
msgstr "ID"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__activity_exception_icon
msgid "Icon"
msgstr "Ícone"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ícone para indicar uma atividade excepcional."

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_account__online_identifier
msgid "Id used to identify account by third party provider"
msgstr "ID usado para identificar a conta pelo provedor de terceiros"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Se marcado, novas mensagens solicitarão sua atenção."

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__message_has_error
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Se marcado, há algumas mensagens com erro de entrega."

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_account__inverse_balance_sign
msgid "If checked, the balance sign will be inverted"
msgstr "Se marcado, o sinal do saldo será invertido"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_account__inverse_transaction_sign
msgid "If checked, the transaction sign will be inverted"
msgstr "Se marcado, o sinal da transação será invertido"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__auto_sync
msgid ""
"If possible, we will try to automatically fetch new transactions for this record\n"
"                \n"
"If the automatic sync is disabled. that will be due to security policy on the bank's end. So, they have to launch the sync manually"
msgstr ""
"If possible, we will try to automatically fetch new transactions for this record\n"
"                \n"
"If the automatic sync is disabled. that will be due to security policy on the bank's end. So, they have to launch the sync manually"

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/transient_bank_statement_line_list_view/transient_bank_statement_line_list_view.xml:0
msgid "Import Transactions"
msgstr "Importar transações"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__provider_data
msgid "Information needed to interact with third party provider"
msgstr "Informações necessárias para interagir com provedor terceirizado"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_bank_selection__institution_name
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__name
msgid "Institution Name"
msgstr "Nome da instituição"

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid "Internal Error"
msgstr "Erro interno"

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid "Invalid URL"
msgstr "URL inválido"

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid "Invalid value for proxy_mode config parameter."
msgstr "Valor inválido para o parâmetro de configuração proxy_mode."

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__inverse_balance_sign
msgid "Inverse Balance Sign"
msgstr "Inverter sinal do saldo"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__inverse_transaction_sign
msgid "Inverse Transaction Sign"
msgstr "Inverter sinal da transação"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_is_follower
msgid "Is Follower"
msgstr "É um seguidor"

#. module: account_online_synchronization
#: model:ir.model,name:account_online_synchronization.model_account_journal
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__journal_id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_duplicate_transaction_wizard__journal_id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_missing_transaction_wizard__journal_id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__journal_ids
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__journal_ids
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.portal_renew_consent
msgid "Journal"
msgstr "Diário"

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid ""
"Journal %(journal_name)s has been set up with a different currency and "
"already has existing entries. You can't link selected bank account in "
"%(currency_name)s to it"
msgstr ""
"O diário %(journal_name)s foi configurado com uma moeda diferente e já tem "
"lançamentos existentes. Você não pode vincular a conta bancária selecionada "
"em %(currency_name)s a ela"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__last_refresh
msgid "Last Refresh"
msgstr "Última atualização"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_selection__write_uid
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__write_uid
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_duplicate_transaction_wizard__write_uid
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_missing_transaction_wizard__write_uid
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__write_uid
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__write_uid
msgid "Last Updated by"
msgstr "Última atualização por"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_selection__write_date
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__write_date
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_duplicate_transaction_wizard__write_date
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_missing_transaction_wizard__write_date
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__write_date
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__write_date
msgid "Last Updated on"
msgstr "Última atualização em"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_online_link_view_form
msgid "Last refresh"
msgstr "Última atualização"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__last_sync
msgid "Last synchronization"
msgstr "Última sincronização"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.portal_renew_consent
msgid "Latest Balance"
msgstr "Último saldo"

#. module: account_online_synchronization
#: model:ir.model,name:account_online_synchronization.model_account_bank_selection
msgid "Link a bank account to the selected journal"
msgstr "Vincular uma conta bancária ao diário selecionado"

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/wizard/account_journal_missing_transactions.py:0
msgid "Manual Bank Statement Lines"
msgstr "Linhas do extrato bancário manual"

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid "Message"
msgstr "Mensagem"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_has_error
msgid "Message Delivery error"
msgstr "Erro na entrega da mensagem"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_ids
msgid "Messages"
msgstr "Mensagens"

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/wizard/account_journal_missing_transactions.py:0
msgid "Missing and Pending Transactions"
msgstr "Transações pendentes e ausentes"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Prazo da minha atividade"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_selection__institution_name
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__name
msgid "Name"
msgstr "Nome"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Próximo evento do calendário de atividades"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Prazo da próxima atividade"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__activity_summary
msgid "Next Activity Summary"
msgstr "Resumo da próxima atividade"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__activity_type_id
msgid "Next Activity Type"
msgstr "Tipo da próxima atividade"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__next_refresh
msgid "Next synchronization"
msgstr "Próxima sincronização"

#. module: account_online_synchronization
#: model:ir.model.fields.selection,name:account_online_synchronization.selection__account_online_link__state__disconnected
msgid "Not Connected"
msgstr "Não conectado"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_needaction_counter
msgid "Number of Actions"
msgstr "Número de ações"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_has_error_counter
msgid "Number of errors"
msgstr "Número de erros"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Número de mensagens que requerem ação"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Número de mensagens com erro de entrega"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.portal_renew_consent
msgid "Odoo"
msgstr "Odoo"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line__online_account_id
msgid "Online Account"
msgstr "Conta online"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_online_link_view_form
msgid "Online Accounts"
msgstr "Contas online"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__online_identifier
msgid "Online Identifier"
msgstr "Identificador online"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_journal__next_link_synchronization
msgid "Online Link Next synchronization"
msgstr "Próxima sincronização do vínculo online"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line__online_partner_information
#: model:ir.model.fields,field_description:account_online_synchronization.field_res_partner__online_partner_information
#: model:ir.model.fields,field_description:account_online_synchronization.field_res_users__online_partner_information
msgid "Online Partner Information"
msgstr "Informação online do usuário"

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_journal.py:0
#: model:ir.actions.act_window,name:account_online_synchronization.action_account_online_link_form
#: model:ir.ui.menu,name:account_online_synchronization.menu_action_online_link_account
#: model_terms:ir.actions.act_window,help:account_online_synchronization.action_account_online_link_form
msgid "Online Synchronization"
msgstr "Sincronização online"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line__online_transaction_identifier
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__online_transaction_identifier
msgid "Online Transaction Identifier"
msgstr "Identificador da transação online"

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_bank_statement.py:0
msgid "Opening statement: first synchronization"
msgstr "Declaração de abertura: primeira sincronização"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__partner_name
msgid "Partner Name"
msgstr "Nome do parceiro"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__payment_ref
msgid "Payment Ref"
msgstr "Ref. de pagamento"

#. module: account_online_synchronization
#: model:ir.model.fields.selection,name:account_online_synchronization.selection__account_bank_statement_line_transient__state__pending
msgid "Pending"
msgstr "Pendente"

#. module: account_online_synchronization
#: model:ir.model.fields.selection,name:account_online_synchronization.selection__account_online_account__fetching_status__planned
msgid "Planned"
msgstr "Planejado"

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/wizard/account_journal_missing_transactions.py:0
msgid "Please enter a valid Starting Date to continue."
msgstr "Insira uma data de início válida para continuar."

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid "Please reconnect your online account."
msgstr "Reconecte a sua conta online."

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/wizard/account_bank_statement_line.py:0
msgid "Please select first the transactions you wish to import."
msgstr "Primeiro selecione as transações que você quer importar."

#. module: account_online_synchronization
#: model:ir.model.fields.selection,name:account_online_synchronization.selection__account_bank_statement_line_transient__state__posted
msgid "Posted"
msgstr "Lançado"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.missing_bank_statement_line_search
msgid "Posted Transactions"
msgstr "Transações lançadas"

#. module: account_online_synchronization
#: model:ir.model.fields.selection,name:account_online_synchronization.selection__account_online_account__fetching_status__processing
msgid "Processing"
msgstr "Processando"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__provider_data
msgid "Provider Data"
msgstr "Dados do provedor"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__provider_type
msgid "Provider Type"
msgstr "Tipo de fornecedor"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__rating_ids
msgid "Ratings"
msgstr "Avaliações"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_online_link_view_form
msgid "Reconnect"
msgstr "Reconectar"

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/connected_until_widget/connected_until_widget.xml:0
#: code:addons/account_online_synchronization/static/src/components/refresh_spin_journal_widget/refresh_spin_journal_widget.xml:0
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_journal_dashboard_inherit_online_sync
msgid "Reconnect Bank"
msgstr "Reconectar banco"

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid "Redirect"
msgstr "Redirecionar"

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/refresh_spin_journal_widget/refresh_spin_journal_widget.xml:0
msgid "Refresh"
msgstr "Atualizar"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__refresh_token
msgid "Refresh Token"
msgstr "Atualizar token"

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_journal.py:0
msgid "Report Issue"
msgstr "Relatar problema"

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid "Report issue"
msgstr "Relatar problema"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__client_id
msgid "Represent a link for a given user towards a banking institution"
msgstr ""
"Representa um link para um determinado usuário em relação a uma instituição "
"bancária"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_online_link_view_form
msgid "Reset"
msgstr "Redefinir"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__activity_user_id
msgid "Responsible User"
msgstr "Usuário responsável"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Erro na entrega do SMS"

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/bank_configure/bank_configure.xml:0
msgid "Search over"
msgstr "Pesquisar em"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.portal_renew_consent
msgid ""
"Security Tip: always check the domain name of this page, before clicking on "
"the button."
msgstr ""
"Dica de segurança: sempre verifique o nome de domínio dessa página antes de "
"clicar no botão."

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/refresh_spin_journal_widget/refresh_spin_journal_widget.xml:0
msgid "See error"
msgstr "Ver erro"

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.view_account_bank_selection_form_wizard
msgid "Select a Bank Account"
msgstr "Selecione uma conta bancária"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.view_account_bank_selection_form_wizard
msgid "Select the"
msgstr "Selecione a"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_selection__selected_account
msgid "Selected Account"
msgstr "Conta selecionada"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__sequence
msgid "Sequence"
msgstr "Sequência"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_account__account_number
msgid "Set if third party provider has the full account number"
msgstr "Definido se o provedor de terceiro tem o número completo da conta"

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/bank_configure/bank_configure.xml:0
msgid "Setup Bank"
msgstr "Configurar banco"

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid "Setup Bank Account"
msgstr "Definir conta bancária"

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/views/account_online_authorization_kanban_controller.xml:0
msgid "Some transactions"
msgstr "Algumas transações"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_duplicate_transaction_wizard__date
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_missing_transaction_wizard__date
msgid "Starting Date"
msgstr "Data de início"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__state
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_journal__account_online_link_state
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__state
msgid "State"
msgstr "Situação"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status baseado em atividades\n"
"Atrasado: data de vencimento já passou\n"
"Hoje: data da atividade é hoje\n"
"Planejado: atividades futuras."

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.portal_renew_consent
msgid "Thank You!"
msgstr "Obrigado!"

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid "The consent for the selected account has expired."
msgstr "O consentimento da conta selecionada expirou."

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid ""
"The online synchronization service is not available at the moment. Please "
"try again later."
msgstr ""
"O serviço de sincronização online não está disponível no momento. Tente "
"novamente mais tarde."

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__provider_type
msgid "Third Party Provider"
msgstr "Provedor terceiro"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_duplicate_transaction_wizard_view_form
msgid ""
"This action will delete all selected transactions. Are you sure you want to "
"proceed?"
msgstr ""
"Essa ação excluirá todas as transações selecionadas. Tem certeza de que "
"deseja prosseguir?"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_online_link_view_form
msgid "This button will reset the fetching status"
msgstr "Esse botão redefinirá o status da busca"

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid ""
"This version of Odoo appears to be outdated and does not support the '%s' "
"sync mode. Installing the latest update might solve this."
msgstr ""
"Esta versão do Odoo parece estar desatualizada e não é compatível com o modo"
" de sincronização '%s'. A instalação da atualização mais recente pode "
"resolver o problema."

#. module: account_online_synchronization
#: model_terms:ir.actions.act_window,help:account_online_synchronization.action_account_online_link_form
msgid ""
"To create a synchronization with your banking institution,<br>\n"
"                please click on <b>Add a Bank Account</b>."
msgstr ""
"Para criar uma sincronização com a sua instituição bancária,<br>\n"
"                clique em <b>Adicionar uma conta bancária</b>."

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__access_token
msgid "Token used to access API."
msgstr "Token utilizado para acessar a API."

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__refresh_token
msgid "Token used to sign API request, Never disclose it"
msgstr "Token usado para assinar a solicitação de API, nunca divulgue-o"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_duplicate_transaction_wizard__transaction_ids
msgid "Transaction"
msgstr "Transação"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__transaction_details
msgid "Transaction Details"
msgstr "Detalhes da transação"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_duplicate_transaction_wizard_view_form
msgid "Transactions"
msgstr "Transações"

#. module: account_online_synchronization
#: model:ir.model,name:account_online_synchronization.model_account_bank_statement_line_transient
msgid "Transient model for bank statement line"
msgstr "Modelo temporário para linhas do extrato bancário"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__has_unlinked_accounts
msgid ""
"True if that connection still has accounts that are not linked to an Odoo "
"journal"
msgstr ""
"Verdadeiro se essa conexão ainda tiver contas que não estejam vinculadas a "
"um diário do Odoo"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipo de atividade de exceção registrada."

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_online_link_view_form
msgid "Update Credentials"
msgstr "Atualizar credenciais"

#. module: account_online_synchronization
#: model:ir.model.fields.selection,name:account_online_synchronization.selection__account_online_account__fetching_status__waiting
msgid "Waiting"
msgstr "Aguardando"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__website_message_ids
msgid "Website Messages"
msgstr "Mensagens do site"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__website_message_ids
msgid "Website communication history"
msgstr "Histórico de comunicação do site"

#. module: account_online_synchronization
#: model:ir.model,name:account_online_synchronization.model_account_duplicate_transaction_wizard
msgid "Wizard for duplicate transactions"
msgstr "Assistente para transações duplicadas"

#. module: account_online_synchronization
#: model:ir.model,name:account_online_synchronization.model_account_missing_transaction_wizard
msgid "Wizard for missing transactions"
msgstr "Assistente para transações ausentes"

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/transient_bank_statement_line_list_view/transient_bank_statement_line_list_view.xml:0
msgid ""
"You are importing transactions before the creation of your online synchronization\n"
"                    ("
msgstr ""
"Você está importando transações antes da criação da sincronização online\n"
"                    ("

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid "You can contact Odoo support"
msgstr "Você pode entrar em contrato com o suporte da Odoo"

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_journal.py:0
msgid "You can only execute this action for bank-synchronized journals."
msgstr ""
"Você só pode executar essa ação com diários sincronizados com o banco."

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_journal.py:0
#: code:addons/account_online_synchronization/wizard/account_journal_missing_transactions.py:0
msgid ""
"You can't find missing transactions for a journal that isn't connected."
msgstr ""
"Não é possível localizar transações ausentes em um diário que não está "
"conectado."

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_journal.py:0
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid "You cannot have two journals associated with the same Online Account."
msgstr "Não é possível ter dois diários associados à mesma conta online."

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/wizard/account_bank_statement_line.py:0
msgid "You cannot import pending transactions."
msgstr "Não é possível importar transações pendentes."

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/transient_bank_statement_line_list_view/transient_bank_statement_line_list_view.xml:0
msgid "You have"
msgstr "Você tem"

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/wizard/account_journal_missing_transactions.py:0
msgid "You have to select one journal to continue."
msgstr "Você precisa selecionar um diário para continuar."

#. module: account_online_synchronization
#: model:mail.template,subject:account_online_synchronization.email_template_sync_reminder
msgid "Your bank connection is expiring soon"
msgstr "A conexão com seu banco expira em breve"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.view_account_bank_selection_form_wizard
msgid "account to connect:"
msgstr "conta a conectar:"

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/bank_configure/bank_configure.xml:0
msgid "banks"
msgstr "bancos"

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/transient_bank_statement_line_list_view/transient_bank_statement_line_list_view.xml:0
msgid "entries"
msgstr "lançamentos"

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/bank_configure/bank_configure.xml:0
msgid "loading..."
msgstr "carregando…"

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/views/account_online_authorization_kanban_controller.xml:0
msgid "may be duplicates."
msgstr "podem ser duplicatas."

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.portal_renew_consent
msgid "on"
msgstr "em"

#. module: account_online_synchronization
#: model:ir.model,name:account_online_synchronization.model_account_online_account
msgid "representation of an online bank account"
msgstr "representação de uma conta bancária online"

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/refresh_spin_journal_widget/refresh_spin_journal_widget.xml:0
msgid "transactions fetched"
msgstr "transações recuperadas"

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/transient_bank_statement_line_list_view/transient_bank_statement_line_list_view.xml:0
msgid ""
"within this period that were not created using the online synchronization. "
"This might cause duplicate entries."
msgstr ""
"neste período que não foram criados usando a sincronização online. Isso pode"
" gerar lançamentos duplicados."
