# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_online_synchronization
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:52+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Marianna <PERSON>, 2025\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid ""
"\n"
"\n"
"If you've already opened a ticket for this issue, don't report it again: a support agent will contact you shortly."
msgstr ""
"\n"
"\n"
"Se hai già aperto un ticket per il problema non farlo di nuovo: un operatore ti contatterà a breve."

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/wizard/account_journal_duplicate_transactions.py:0
msgid "%s duplicate transactions"
msgstr "%s transazioni duplicate"

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/transient_bank_statement_line_list_view/transient_bank_statement_line_list_view.xml:0
msgid ""
").\n"
"                    This might cause duplicate entries."
msgstr ""
").\n"
"                    Questo potrebbe causare la duplicazione delle registrazioni."

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/refresh_spin_journal_widget/refresh_spin_journal_widget.xml:0
msgid "0 transaction fetched"
msgstr "0 transazioni recuperate"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_duplicate_transaction_wizard_view_form
msgid "<i class=\"fa fa-trash me-1\"/> Delete Selected"
msgstr "<i class=\"fa fa-trash me-1\"/> Elimina selezionati"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_journal_form
msgid "<i class=\"oi oi-arrow-right me-2\"/> Send Now"
msgstr "<i class=\"oi oi-arrow-right me-2\"/> Invia ora"

#. module: account_online_synchronization
#: model:mail.template,body_html:account_online_synchronization.email_template_sync_reminder
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #FFFFFF; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\">\n"
"                    <tr>\n"
"                        <td align=\"center\">\n"
"                            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: #FFFFFF; color: #454748; border-collapse:separate;\">\n"
"                                <tbody>\n"
"                                    <!-- CONTENT -->\n"
"                                    <tr>\n"
"                                        <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                                <tr>\n"
"                                                    <td valign=\"top\" style=\"font-size: 13px;\">\n"
"                                                        <div>\n"
"                                                            Hello,<br/><br/>\n"
"                                                            The connection between <b><a t-att-href=\"object.get_base_url()\" t-out=\"object.get_base_url() or ''\">https://yourcompany.odoo.com</a></b> and <t t-out=\"object.account_online_link_id.name or ''\">Belfius</t> <t t-if=\"not object.expiring_synchronization_due_day\">expired.</t><t t-else=\"\">expires in <t t-out=\"object.expiring_synchronization_due_day or ''\">10</t> days.</t><br/>\n"
"                                                            <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                                                                <a t-attf-href=\"{{ website_url }}/renew_consent/{{ object.id }}?access_token={{object.access_token}}\" style=\"background-color: #4caf50; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                                                    Renew Consent\n"
"                                                                </a>\n"
"                                                            </div>\n"
"                                                            Security Tip: Check that the domain name you are redirected to is: <b><a t-att-href=\"object.get_base_url()\" t-out=\"object.get_base_url() or ''\">https://yourcompany.odoo.com</a></b>\n"
"                                                        </div>\n"
"                                                    </td>\n"
"                                                </tr>\n"
"                                                <tr>\n"
"                                                    <td style=\"text-align:center;\">\n"
"                                                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                                    </td>\n"
"                                                </tr>\n"
"                                            </table>\n"
"                                        </td>\n"
"                                    </tr>\n"
"                                </tbody>\n"
"                            </table>\n"
"                        </td>\n"
"                    </tr>\n"
"                    <!-- POWERED BY -->\n"
"                    <tr>\n"
"                        <td align=\"center\" style=\"min-width: 590px;\">\n"
"                            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"                                <tr>\n"
"                                    <td style=\"text-align: center; font-size: 13px;\">\n"
"                                        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=auth\" style=\"color: #875A7B;\">Odoo</a>\n"
"                                    </td>\n"
"                                </tr>\n"
"                            </table>\n"
"                            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"                                <tr>\n"
"                                    <td style=\"text-align: center; font-size: 11px;\">\n"
"                                        PS: This is an automated email sent by Odoo Accounting to remind you before a bank sync consent expiration.\n"
"                                    </td>\n"
"                                </tr>\n"
"                            </table>\n"
"                        </td>\n"
"                    </tr>\n"
"                </table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #FFFFFF; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\">\n"
"                    <tr>\n"
"                        <td align=\"center\">\n"
"                            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: #FFFFFF; color: #454748; border-collapse:separate;\">\n"
"                                <tbody>\n"
"                                    <!-- CONTENT -->\n"
"                                    <tr>\n"
"                                        <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                                <tr>\n"
"                                                    <td valign=\"top\" style=\"font-size: 13px;\">\n"
"                                                        <div>\n"
"                                                            Ciao,<br/><br/>\n"
"                                                            il collegamento tra <b><a t-att-href=\"object.get_base_url()\" t-out=\"object.get_base_url() or ''\">https://yourcompany.odoo.com</a></b> e <t t-out=\"object.account_online_link_id.name or ''\">Belfius</t> <t t-if=\"not object.expiring_synchronization_due_day\">è scaduto.</t><t t-else=\"\">scade tra <t t-out=\"object.expiring_synchronization_due_day or ''\">10</t> giorni.</t><br/>\n"
"                                                            <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                                                                <a t-attf-href=\"{{ website_url }}/renew_consent/{{ object.id }}?access_token={{object.access_token}}\" style=\"background-color: #4caf50; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                                                    Rinnova consenso\n"
"                                                                </a>\n"
"                                                            </div>\n"
"                                                            Consiglio relativo alla sicurezza: verifica che il dominio a cui sei reindirizzato sia <b><a t-att-href=\"object.get_base_url()\" t-out=\"object.get_base_url() or ''\">https://yourcompany.odoo.com</a></b>\n"
"                                                        </div>\n"
"                                                    </td>\n"
"                                                </tr>\n"
"                                                <tr>\n"
"                                                    <td style=\"text-align:center;\">\n"
"                                                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                                    </td>\n"
"                                                </tr>\n"
"                                            </table>\n"
"                                        </td>\n"
"                                    </tr>\n"
"                                </tbody>\n"
"                            </table>\n"
"                        </td>\n"
"                    </tr>\n"
"                    <!-- POWERED BY -->\n"
"                    <tr>\n"
"                        <td align=\"center\" style=\"min-width: 590px;\">\n"
"                            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"                                <tr>\n"
"                                    <td style=\"text-align: center; font-size: 13px;\">\n"
"                                        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=auth\" style=\"color: #875A7B;\">Odoo</a>\n"
"                                    </td>\n"
"                                </tr>\n"
"                            </table>\n"
"                            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"                                <tr>\n"
"                                    <td style=\"text-align: center; font-size: 11px;\">\n"
"                                        PS: e-mail automatica inviata da Odoo Contabilità per ricordarti della scadenza del consenso relativo alla sincronizzazione bancaria.\n"
"                                    </td>\n"
"                                </tr>\n"
"                            </table>\n"
"                        </td>\n"
"                    </tr>\n"
"                </table>\n"
"            "

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__access_token
msgid "Access Token"
msgstr "Token di accesso"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__account_data
msgid "Account Data"
msgstr "Dati conto"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__name
msgid "Account Name"
msgstr "Nome conto"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_account__name
msgid "Account Name as provided by third party provider"
msgstr "Nome del conto come fornito dal fornitore di terza parte"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__account_number
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__account_number
msgid "Account Number"
msgstr "Numero conto"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_selection__account_online_account_ids
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__online_account_id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_journal__account_online_account_id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__account_online_account_ids
msgid "Account Online Account"
msgstr "Account online account"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_selection__account_online_link_id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line__online_link_id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_journal__account_online_link_id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__account_online_link_id
msgid "Account Online Link"
msgstr "Account Online Link"

#. module: account_online_synchronization
#: model:ir.actions.server,name:account_online_synchronization.online_sync_cron_waiting_synchronization_ir_actions_server
msgid "Account: Journal online Waiting Synchronization"
msgstr "Conto: registro online in attesa di sincronizzazione"

#. module: account_online_synchronization
#: model:ir.actions.server,name:account_online_synchronization.online_sync_cron_ir_actions_server
msgid "Account: Journal online sync"
msgstr "Conto: sincronizzazione online dei registri"

#. module: account_online_synchronization
#: model:ir.actions.server,name:account_online_synchronization.online_sync_unused_connection_cron_ir_actions_server
msgid "Account: Journal online sync cleanup unused connections"
msgstr "Conto: pulizia connessioni non utilizzate sinc online registro"

#. module: account_online_synchronization
#: model:ir.actions.server,name:account_online_synchronization.online_sync_mail_cron_ir_actions_server
msgid "Account: Journal online sync reminder"
msgstr "Conto: promemoria sinc online registro"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_needaction
msgid "Action Needed"
msgstr "Azione richiesta"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__activity_ids
msgid "Activities"
msgstr "Attività"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Decorazione eccezione attività"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__activity_state
msgid "Activity State"
msgstr "Stato attività"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icona tipo di attività"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__amount
msgid "Amount"
msgstr "Importo"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__amount_currency
msgid "Amount in Currency"
msgstr "Importo in valuta"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_attachment_count
msgid "Attachment Count"
msgstr "Numero allegati"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__auto_sync
msgid "Automatic synchronization"
msgstr "Sincronizzazione automatica"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__balance
msgid "Balance"
msgstr "Saldo"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_account__balance
msgid "Balance of the account sent by the third party provider"
msgstr "Saldo del conto inviato dal fornitore di terze parti"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.portal_renew_consent
msgid "Bank"
msgstr "Banca"

#. module: account_online_synchronization
#: model:ir.model,name:account_online_synchronization.model_account_online_link
msgid "Bank Connection"
msgstr "Connessione banca"

#. module: account_online_synchronization
#: model:ir.model,name:account_online_synchronization.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr "Riga estratto conto bancario"

#. module: account_online_synchronization
#: model:mail.activity.type,name:account_online_synchronization.bank_sync_activity_update_consent
msgid "Bank Synchronization: Update consent"
msgstr "Sincronizzazione bancaria: aggiorna consenso"

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid "Bank Synchronization: Update your consent"
msgstr "Sincronizzazione bancaria: aggiorna consenso"

#. module: account_online_synchronization
#: model:mail.template,name:account_online_synchronization.email_template_sync_reminder
msgid "Bank connection expiration reminder"
msgstr "Promemoria scadenza connessione bancaria"

#. module: account_online_synchronization
#: model:ir.model,name:account_online_synchronization.model_bank_rec_widget
msgid "Bank reconciliation widget for a single statement line"
msgstr ""
"Widget per la riconciliazione bancaria di una signola voce di rendiconto"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_missing_transaction_wizard_view_form
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.view_account_bank_selection_form_wizard
msgid "Cancel"
msgstr "Annulla"

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid "Check the documentation"
msgstr "Consulta la documentazione"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_duplicate_transaction_wizard_view_form
msgid ""
"Choose a date and a journal from which you want to check the transactions."
msgstr "Scegli una data e un registro da cui verificare le transazioni."

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_missing_transaction_wizard_view_form
msgid "Choose a date and a journal from which you want to fetch transactions"
msgstr "Scegli una data e un registro da cui recuperare le transazioni"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__client_id
msgid "Client"
msgstr "Cliente"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_online_link_view_form
msgid "Client id"
msgstr "Client ID"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_journal__renewal_contact_email
msgid ""
"Comma separated list of email addresses to send consent renewal "
"notifications 15, 3 and 1 days before expiry"
msgstr ""
"Elenco separato da virgole di indirizzi e-mail a cui inviare notifiche "
"relative al rinnovo dell'autorizzazione 15, 3 e 1 giorno prima della "
"scadenza"

#. module: account_online_synchronization
#: model:ir.model,name:account_online_synchronization.model_res_company
msgid "Companies"
msgstr "Aziende"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__company_id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__company_id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__company_id
msgid "Company"
msgstr "Azienda"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_online_link_view_form
msgid "Connect"
msgstr "Collega"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.view_account_bank_selection_form_wizard
msgid "Connect Bank"
msgstr "Connetti banca"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_journal_dashboard_inherit_online_sync
msgid "Connect bank"
msgstr "Collega banca"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.portal_renew_consent
msgid "Connect my Bank"
msgstr "Collega la mia banca"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.portal_renew_consent
msgid "Connect your bank account to Odoo"
msgstr "Collega il tuo conto corrente a Odoo"

#. module: account_online_synchronization
#: model:ir.model.fields.selection,name:account_online_synchronization.selection__account_online_link__state__connected
msgid "Connected"
msgstr "Connesso"

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/connected_until_widget/connected_until_widget.xml:0
msgid "Connected until"
msgstr "Connesso fino al"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_journal__renewal_contact_email
msgid "Connection Requests"
msgstr "Richieste di collegamento"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__connection_state_details
msgid "Connection State Details"
msgstr "Dettagli stato connessione"

#. module: account_online_synchronization
#: model:mail.message.subtype,name:account_online_synchronization.bank_sync_consent_renewal
msgid "Consent Renewal"
msgstr "Consenti rinnovo"

#. module: account_online_synchronization
#: model:ir.model,name:account_online_synchronization.model_res_partner
msgid "Contact"
msgstr "Contatto"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_selection__create_uid
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__create_uid
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_duplicate_transaction_wizard__create_uid
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_missing_transaction_wizard__create_uid
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__create_uid
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__create_uid
msgid "Created by"
msgstr "Creato da"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_selection__create_date
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__create_date
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_duplicate_transaction_wizard__create_date
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_missing_transaction_wizard__create_date
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__create_date
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__create_date
msgid "Created on"
msgstr "Data creazione"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__currency_id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__currency_id
msgid "Currency"
msgstr "Valuta"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__date
msgid "Date"
msgstr "Data"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_journal__expiring_synchronization_date
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__expiring_synchronization_date
msgid "Date when the consent for this connection expires"
msgstr "Data di scadenza del consenso per questa connessione"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_selection__display_name
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__display_name
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_duplicate_transaction_wizard__display_name
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_missing_transaction_wizard__display_name
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__display_name
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__display_name
msgid "Display Name"
msgstr "Nome visualizzato"

#. module: account_online_synchronization
#: model:ir.model.fields.selection,name:account_online_synchronization.selection__account_online_account__fetching_status__done
msgid "Done"
msgstr "Completato"

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_bank_statement.py:0
#: model:ir.model.fields.selection,name:account_online_synchronization.selection__account_online_link__state__error
msgid "Error"
msgstr "Errore"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_journal__expiring_synchronization_date
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__expiring_synchronization_date
msgid "Expiring Synchronization Date"
msgstr "Data di scadenza della sincronizzazione"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_journal__expiring_synchronization_due_day
msgid "Expiring Synchronization Due Day"
msgstr "Scadenza della sincronizzazione"

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/connected_until_widget/connected_until_widget.xml:0
msgid "Extend Connection"
msgstr "Estendi connessione"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_account__account_data
msgid "Extra information needed by third party provider"
msgstr "Informazioni aggiuntive necessarie per il fornitore di terze parti"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_missing_transaction_wizard_view_form
msgid "Fetch"
msgstr "Recupera"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_duplicate_transaction_wizard_view_form
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_missing_transaction_wizard_view_form
msgid "Fetch Missing Bank Statements Wizard"
msgstr "Procedura guidata per il recupero degli estratti conto mancanti"

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/refresh_spin_journal_widget/refresh_spin_journal_widget.xml:0
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_online_link_view_form
msgid "Fetch Transactions"
msgstr "Recupera operazioni"

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid "Fetched Transactions"
msgstr "Transazioni recuperate"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_journal__online_sync_fetching_status
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__fetching_status
msgid "Fetching Status"
msgstr "Stato di recupero"

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/refresh_spin_journal_widget/refresh_spin_journal_widget.xml:0
msgid "Fetching..."
msgstr "In fase di recupero..."

#. module: account_online_synchronization
#. odoo-javascript
#. odoo-python
#: code:addons/account_online_synchronization/models/account_journal.py:0
#: code:addons/account_online_synchronization/static/src/components/bank_reconciliation/find_duplicate_transactions_cog_menu.xml:0
msgid "Find Duplicate Transactions"
msgstr "Trova transazioni duplicate"

#. module: account_online_synchronization
#. odoo-javascript
#. odoo-python
#: code:addons/account_online_synchronization/models/account_journal.py:0
#: code:addons/account_online_synchronization/static/src/components/bank_reconciliation/fetch_missing_transactions_cog_menu.xml:0
msgid "Find Missing Transactions"
msgstr "Cerca operazioni mancanti"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_duplicate_transaction_wizard__first_ids_in_group
msgid "First Ids In Group"
msgstr "Primi ID nel gruppo"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_follower_ids
msgid "Followers"
msgstr "Seguito da"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguito da (partner)"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icona Font Awesome es. fa-tasks"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__foreign_currency_id
msgid "Foreign Currency"
msgstr "Valuta estera"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__has_message
msgid "Has Message"
msgstr "Contiene messaggio"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__has_unlinked_accounts
msgid "Has Unlinked Accounts"
msgstr "Ha conti non collegati"

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid "Here"
msgstr "Qui"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_selection__id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_duplicate_transaction_wizard__id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_missing_transaction_wizard__id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__id
msgid "ID"
msgstr "ID"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__activity_exception_icon
msgid "Icon"
msgstr "Icona"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icona per indicare un'attività eccezione."

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_account__online_identifier
msgid "Id used to identify account by third party provider"
msgstr "Id utilizzato per identificare l'account da un fornitore terzo"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Se selezionata, nuovi messaggi richiedono attenzione."

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__message_has_error
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Se selezionata, alcuni messaggi presentano un errore di consegna."

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_account__inverse_balance_sign
msgid "If checked, the balance sign will be inverted"
msgstr "Se selezionato, il segno del saldo sarà invertito"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_account__inverse_transaction_sign
msgid "If checked, the transaction sign will be inverted"
msgstr "Se selezionato, il segno della transazione sarà invertito"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__auto_sync
msgid ""
"If possible, we will try to automatically fetch new transactions for this record\n"
"                \n"
"If the automatic sync is disabled. that will be due to security policy on the bank's end. So, they have to launch the sync manually"
msgstr ""
"Se possibile, proveremo a recuperare automaticamente le nuove transazioni per questo record.\n"
"                \n"
"Se la sincronizzazione automatica è disabilitata per via dei termini di sicurezza della banca. Di conseguenza, bisogna lanciare la sincronizzazione manualmente."

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/transient_bank_statement_line_list_view/transient_bank_statement_line_list_view.xml:0
msgid "Import Transactions"
msgstr "Importa transazioni"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__provider_data
msgid "Information needed to interact with third party provider"
msgstr "Informazioni necessarie per interagire con fornitori di terze parti"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_bank_selection__institution_name
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__name
msgid "Institution Name"
msgstr "Nome dell'istituzione"

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid "Internal Error"
msgstr "Errore interno"

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid "Invalid URL"
msgstr "URL non valida"

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid "Invalid value for proxy_mode config parameter."
msgstr "Valore non valido per il parametro di configurazione proxy_mode."

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__inverse_balance_sign
msgid "Inverse Balance Sign"
msgstr "Inverti segno del saldo"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__inverse_transaction_sign
msgid "Inverse Transaction Sign"
msgstr "Inverti segno transazione"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_is_follower
msgid "Is Follower"
msgstr "Sta seguendo"

#. module: account_online_synchronization
#: model:ir.model,name:account_online_synchronization.model_account_journal
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__journal_id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_duplicate_transaction_wizard__journal_id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_missing_transaction_wizard__journal_id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__journal_ids
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__journal_ids
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.portal_renew_consent
msgid "Journal"
msgstr "Registro"

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid ""
"Journal %(journal_name)s has been set up with a different currency and "
"already has existing entries. You can't link selected bank account in "
"%(currency_name)s to it"
msgstr ""
"Il registro %(journal_name)s è stato configurato con una valuta diversa e "
"presenta già alcune registraszioni. Non è possibile collegare il conto "
"corrente selezionato in %(currency_name)s ad esso"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__last_refresh
msgid "Last Refresh"
msgstr "Ultimo aggiornamento"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_selection__write_uid
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__write_uid
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_duplicate_transaction_wizard__write_uid
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_missing_transaction_wizard__write_uid
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__write_uid
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__write_uid
msgid "Last Updated by"
msgstr "Ultimo aggiornamento di"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_selection__write_date
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__write_date
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_duplicate_transaction_wizard__write_date
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_missing_transaction_wizard__write_date
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__write_date
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__write_date
msgid "Last Updated on"
msgstr "Ultimo aggiornamento il"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_online_link_view_form
msgid "Last refresh"
msgstr "Ultimo aggiornamento"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__last_sync
msgid "Last synchronization"
msgstr "Ultima sincronizzazione"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.portal_renew_consent
msgid "Latest Balance"
msgstr "Ultimo saldo"

#. module: account_online_synchronization
#: model:ir.model,name:account_online_synchronization.model_account_bank_selection
msgid "Link a bank account to the selected journal"
msgstr "Collega un conto bancario al registro selezionato"

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/wizard/account_journal_missing_transactions.py:0
msgid "Manual Bank Statement Lines"
msgstr "Righe estratto conto bancario manuale"

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid "Message"
msgstr "Messaggio"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_has_error
msgid "Message Delivery error"
msgstr "Errore di consegna messaggio"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_ids
msgid "Messages"
msgstr "Messaggi"

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/wizard/account_journal_missing_transactions.py:0
msgid "Missing and Pending Transactions"
msgstr "Operazioni mancanti e in sospeso"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Scadenza mie attività"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_selection__institution_name
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__name
msgid "Name"
msgstr "Nome"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Prossivo evento calendario attività"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Scadenza prossima attività"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__activity_summary
msgid "Next Activity Summary"
msgstr "Riepilogo prossima attività"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__activity_type_id
msgid "Next Activity Type"
msgstr "Tipologia prossima attività"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__next_refresh
msgid "Next synchronization"
msgstr "Prossima sincronizzazione"

#. module: account_online_synchronization
#: model:ir.model.fields.selection,name:account_online_synchronization.selection__account_online_link__state__disconnected
msgid "Not Connected"
msgstr "Non connesso"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_needaction_counter
msgid "Number of Actions"
msgstr "Numero di azioni"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_has_error_counter
msgid "Number of errors"
msgstr "Numero di errori"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Numero di messaggi che richiedono un'azione"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Numero di messaggi con errore di consegna"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.portal_renew_consent
msgid "Odoo"
msgstr "Odoo"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line__online_account_id
msgid "Online Account"
msgstr "Conto online"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_online_link_view_form
msgid "Online Accounts"
msgstr "Conti online"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__online_identifier
msgid "Online Identifier"
msgstr "Identificativo online"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_journal__next_link_synchronization
msgid "Online Link Next synchronization"
msgstr "Link online Sincronizzazione successiva"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line__online_partner_information
#: model:ir.model.fields,field_description:account_online_synchronization.field_res_partner__online_partner_information
#: model:ir.model.fields,field_description:account_online_synchronization.field_res_users__online_partner_information
msgid "Online Partner Information"
msgstr "Informazioni partner online"

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_journal.py:0
#: model:ir.actions.act_window,name:account_online_synchronization.action_account_online_link_form
#: model:ir.ui.menu,name:account_online_synchronization.menu_action_online_link_account
#: model_terms:ir.actions.act_window,help:account_online_synchronization.action_account_online_link_form
msgid "Online Synchronization"
msgstr "Sincronizzazione online"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line__online_transaction_identifier
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__online_transaction_identifier
msgid "Online Transaction Identifier"
msgstr "Identificatore di transazione online"

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_bank_statement.py:0
msgid "Opening statement: first synchronization"
msgstr "Apertura estratto conto: prima sincronizzazione"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__partner_name
msgid "Partner Name"
msgstr "Nome partner"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__payment_ref
msgid "Payment Ref"
msgstr "Rif. pagamento"

#. module: account_online_synchronization
#: model:ir.model.fields.selection,name:account_online_synchronization.selection__account_bank_statement_line_transient__state__pending
msgid "Pending"
msgstr "In sospeso"

#. module: account_online_synchronization
#: model:ir.model.fields.selection,name:account_online_synchronization.selection__account_online_account__fetching_status__planned
msgid "Planned"
msgstr "Pianificato"

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/wizard/account_journal_missing_transactions.py:0
msgid "Please enter a valid Starting Date to continue."
msgstr "Inserisci una data di inizio valida per continuare."

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid "Please reconnect your online account."
msgstr "Connettere nuovamente il conto online."

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/wizard/account_bank_statement_line.py:0
msgid "Please select first the transactions you wish to import."
msgstr "Seleziona prima le operazioni che vuoi importare."

#. module: account_online_synchronization
#: model:ir.model.fields.selection,name:account_online_synchronization.selection__account_bank_statement_line_transient__state__posted
msgid "Posted"
msgstr "Registrata"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.missing_bank_statement_line_search
msgid "Posted Transactions"
msgstr "Operazioni registrate"

#. module: account_online_synchronization
#: model:ir.model.fields.selection,name:account_online_synchronization.selection__account_online_account__fetching_status__processing
msgid "Processing"
msgstr "In elaborazione"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__provider_data
msgid "Provider Data"
msgstr "Dati fornitore"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__provider_type
msgid "Provider Type"
msgstr "Tipo di fornitore"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__rating_ids
msgid "Ratings"
msgstr "Valutazioni"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_online_link_view_form
msgid "Reconnect"
msgstr "Riconnetti"

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/connected_until_widget/connected_until_widget.xml:0
#: code:addons/account_online_synchronization/static/src/components/refresh_spin_journal_widget/refresh_spin_journal_widget.xml:0
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_journal_dashboard_inherit_online_sync
msgid "Reconnect Bank"
msgstr "Riconnetti banca"

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid "Redirect"
msgstr "Reindirizza"

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/refresh_spin_journal_widget/refresh_spin_journal_widget.xml:0
msgid "Refresh"
msgstr "Aggiorna"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__refresh_token
msgid "Refresh Token"
msgstr "Token di aggiornamento"

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_journal.py:0
msgid "Report Issue"
msgstr "Segnala problema"

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid "Report issue"
msgstr "Segnala problema"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__client_id
msgid "Represent a link for a given user towards a banking institution"
msgstr ""
"Rappresenta un collegamento per un dato utente verso un istituto bancario"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_online_link_view_form
msgid "Reset"
msgstr "Azzera"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__activity_user_id
msgid "Responsible User"
msgstr "Utente responsabile"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Errore di consegna SMS"

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/bank_configure/bank_configure.xml:0
msgid "Search over"
msgstr "Cerca su"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.portal_renew_consent
msgid ""
"Security Tip: always check the domain name of this page, before clicking on "
"the button."
msgstr ""
"Consiglio sicurezza: controlla sempre il nome di dominio di questa pagina "
"prima di fare clic sul pulsante."

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/refresh_spin_journal_widget/refresh_spin_journal_widget.xml:0
msgid "See error"
msgstr "Vedi errore"

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.view_account_bank_selection_form_wizard
msgid "Select a Bank Account"
msgstr "Seleziona un conto bancario"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.view_account_bank_selection_form_wizard
msgid "Select the"
msgstr "Seleziona il"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_selection__selected_account
msgid "Selected Account"
msgstr "Conto selezionato"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__sequence
msgid "Sequence"
msgstr "Sequenza"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_account__account_number
msgid "Set if third party provider has the full account number"
msgstr "Impostato se il fornitore terzo ha il numero di conto completo"

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/bank_configure/bank_configure.xml:0
msgid "Setup Bank"
msgstr "Configura banca"

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid "Setup Bank Account"
msgstr "Configura conto bancario"

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/views/account_online_authorization_kanban_controller.xml:0
msgid "Some transactions"
msgstr "Alcune transazioni"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_duplicate_transaction_wizard__date
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_missing_transaction_wizard__date
msgid "Starting Date"
msgstr "Data inizio"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__state
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_journal__account_online_link_state
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__state
msgid "State"
msgstr "Provincia"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Stato basato sulle attività\n"
"In ritardo: scadenza già superata\n"
"Oggi: attività in data odierna\n"
"Pianificato: attività future."

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.portal_renew_consent
msgid "Thank You!"
msgstr "Grazie!"

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid "The consent for the selected account has expired."
msgstr "Il consenso per il conto selezionato è scaduto."

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid ""
"The online synchronization service is not available at the moment. Please "
"try again later."
msgstr ""
"Il servizio di sincronizzazione online non è attualmente disponibile. "
"Riprovare più tardi."

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__provider_type
msgid "Third Party Provider"
msgstr "Fornitore di terze parti"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_duplicate_transaction_wizard_view_form
msgid ""
"This action will delete all selected transactions. Are you sure you want to "
"proceed?"
msgstr ""
"L'azione eliminerà tutte le transazioni selezionate. Sei sicuro di voler "
"procedere?"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_online_link_view_form
msgid "This button will reset the fetching status"
msgstr "Il pulsante ripristinerà lo stato di recupero"

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid ""
"This version of Odoo appears to be outdated and does not support the '%s' "
"sync mode. Installing the latest update might solve this."
msgstr ""
"Questa versione di Odoo sembra essere obsoleta e non supporta la modalità di"
" sincronizzazione \"%s\". L'installazione dell'aggiornamento più recente "
"potrebbe risolvere il problema."

#. module: account_online_synchronization
#: model_terms:ir.actions.act_window,help:account_online_synchronization.action_account_online_link_form
msgid ""
"To create a synchronization with your banking institution,<br>\n"
"                please click on <b>Add a Bank Account</b>."
msgstr ""
"Per sincronizzare il tuo istituto bancario<br>\n"
"                fai clic su <b>Aggiungi conto bancario</b>."

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__access_token
msgid "Token used to access API."
msgstr "Token usato per accedere alle API."

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__refresh_token
msgid "Token used to sign API request, Never disclose it"
msgstr "Token usato per firmare la richiesta API, non rivelarlo mai"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_duplicate_transaction_wizard__transaction_ids
msgid "Transaction"
msgstr "Operazione"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line_transient__transaction_details
msgid "Transaction Details"
msgstr "Dettagli transazione"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_duplicate_transaction_wizard_view_form
msgid "Transactions"
msgstr "Transazioni"

#. module: account_online_synchronization
#: model:ir.model,name:account_online_synchronization.model_account_bank_statement_line_transient
msgid "Transient model for bank statement line"
msgstr "Modello transitorio per la riga dell'estratto conto"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__has_unlinked_accounts
msgid ""
"True if that connection still has accounts that are not linked to an Odoo "
"journal"
msgstr ""
"Vero se la connessione presenta ancora conti che non sono collegati ad un "
"registro Odoo"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipo di attività eccezione sul record."

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_online_link_view_form
msgid "Update Credentials"
msgstr "Aggiorna credenziali"

#. module: account_online_synchronization
#: model:ir.model.fields.selection,name:account_online_synchronization.selection__account_online_account__fetching_status__waiting
msgid "Waiting"
msgstr "In attesa"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__website_message_ids
msgid "Website Messages"
msgstr "Messaggi sito web"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__website_message_ids
msgid "Website communication history"
msgstr "Cronologia comunicazioni sito web"

#. module: account_online_synchronization
#: model:ir.model,name:account_online_synchronization.model_account_duplicate_transaction_wizard
msgid "Wizard for duplicate transactions"
msgstr "Procedura guidata per transazioni duplicate"

#. module: account_online_synchronization
#: model:ir.model,name:account_online_synchronization.model_account_missing_transaction_wizard
msgid "Wizard for missing transactions"
msgstr "Procedura guidata per operazioni mancanti"

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/transient_bank_statement_line_list_view/transient_bank_statement_line_list_view.xml:0
msgid ""
"You are importing transactions before the creation of your online synchronization\n"
"                    ("
msgstr ""
"Stai importanto le operazioni prima della creazione della sincronizzazione online\n"
"                    ("

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid "You can contact Odoo support"
msgstr "Puoi contattare l'assistenza Odoo"

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_journal.py:0
msgid "You can only execute this action for bank-synchronized journals."
msgstr ""
"Puoi eseguire l'azione solo per i registri sincronizzati con la banca."

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_journal.py:0
#: code:addons/account_online_synchronization/wizard/account_journal_missing_transactions.py:0
msgid ""
"You can't find missing transactions for a journal that isn't connected."
msgstr ""
"Non è possibile trovare le operazioni mancanti per un registro che non è "
"collegato."

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/models/account_journal.py:0
#: code:addons/account_online_synchronization/models/account_online.py:0
msgid "You cannot have two journals associated with the same Online Account."
msgstr ""
"Non si possono avere due registri associati allo stesso account online."

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/wizard/account_bank_statement_line.py:0
msgid "You cannot import pending transactions."
msgstr "Non è possibile importare operazioni in sospeso."

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/transient_bank_statement_line_list_view/transient_bank_statement_line_list_view.xml:0
msgid "You have"
msgstr "Ci sono"

#. module: account_online_synchronization
#. odoo-python
#: code:addons/account_online_synchronization/wizard/account_journal_missing_transactions.py:0
msgid "You have to select one journal to continue."
msgstr "Devi selezionare un registro per continuare."

#. module: account_online_synchronization
#: model:mail.template,subject:account_online_synchronization.email_template_sync_reminder
msgid "Your bank connection is expiring soon"
msgstr "La tua connessione bancaria scadrà presto"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.view_account_bank_selection_form_wizard
msgid "account to connect:"
msgstr "conto da collegare:"

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/bank_configure/bank_configure.xml:0
msgid "banks"
msgstr "banche"

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/transient_bank_statement_line_list_view/transient_bank_statement_line_list_view.xml:0
msgid "entries"
msgstr "registrazioni"

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/bank_configure/bank_configure.xml:0
msgid "loading..."
msgstr "caricamento..."

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/views/account_online_authorization_kanban_controller.xml:0
msgid "may be duplicates."
msgstr "potrebbero essere duplicati."

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.portal_renew_consent
msgid "on"
msgstr "-"

#. module: account_online_synchronization
#: model:ir.model,name:account_online_synchronization.model_account_online_account
msgid "representation of an online bank account"
msgstr "Rappresentazione di un conto bancario online"

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/refresh_spin_journal_widget/refresh_spin_journal_widget.xml:0
msgid "transactions fetched"
msgstr "transazioni recuperate"

#. module: account_online_synchronization
#. odoo-javascript
#: code:addons/account_online_synchronization/static/src/components/transient_bank_statement_line_list_view/transient_bank_statement_line_list_view.xml:0
msgid ""
"within this period that were not created using the online synchronization. "
"This might cause duplicate entries."
msgstr ""
"in questo periodo che non sono stati creati utilizzando la sincronizzazione "
"online. Questo potrebbe causare la duplicazione delle registrazioni."
