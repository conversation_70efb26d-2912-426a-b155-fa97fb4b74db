# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_editor
# 
# Translators:
# <PERSON>, 2024
# <PERSON>, 2024
# Wil <PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:41+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: Jun<PERSON>, 2024\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
msgid "%spx"
msgstr "%spx"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
msgid "%spx (Original)"
msgstr "%spx (Original)"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
msgid "%spx (Suggested)"
msgstr "%spx （推奨）"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/alt_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid ""
"'Alt tag' specifies an alternate text for an image, if the image cannot be "
"displayed (slow connection, missing image, screen reader ...)."
msgstr "画像が表示できない場合 (遅い接続、見つからない画像、スクリーンリーダーなど)、「代替タグ」は画像に代替テキストを特定します。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/alt_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "'Title tag' is shown as a tooltip when you hover the picture."
msgstr "画像の上にマウスを置くと「タイトルタグ」はツールチップとして表示されます。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/alt_dialog.xml:0
msgid "(ALT Tag)"
msgstr "(代替タグ)"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/alt_dialog.xml:0
msgid "(TITLE Tag)"
msgstr "(タイトルタグ)"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
msgid "(URL or Embed)"
msgstr "(URL又は埋め込む)"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "100%"
msgstr "100%"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "1977"
msgstr "1977"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "1x"
msgstr "1x"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "2 columns"
msgstr "2カラム"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "25"
msgstr "25"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "25%"
msgstr "25%"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "2x"
msgstr "2x"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "3 Stars"
msgstr "3星"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "3 columns"
msgstr "3カラム"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "3x"
msgstr "3x"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "4 columns"
msgstr "4カラム"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "4x"
msgstr "4x"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "5 Stars"
msgstr "5星"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "50%"
msgstr "50%"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "5x"
msgstr "5x"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "90"
msgstr "90"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "<span class=\"flex-grow-0 ms-1 text-white-50\">%</span>"
msgstr "<span class=\"flex-grow-0 ms-1 text-white-50\">%</span>"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "<span class=\"flex-grow-0 ms-1 text-white-50\">deg</span>"
msgstr "<span class=\"flex-grow-0 ms-1 text-white-50\">度</span>"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "<span class=\"me-2 ms-3\">Y</span>"
msgstr "<span class=\"me-2 ms-3\">Y</span>"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "<span class=\"me-2\">X</span>"
msgstr "<span class=\"me-2\">X</span>"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "<span class=\"w-100\">Basic</span>"
msgstr "<span class=\"w-100\">ベーシック</span>"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "<span class=\"w-100\">Creative</span>"
msgstr "<span class=\"w-100\">クリエイティブ</span>"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "<span class=\"w-100\">Decorative</span>"
msgstr "<span class=\"w-100\">デコラティブ</span>"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "<span class=\"w-100\">Devices</span>"
msgstr "<span class=\"w-100\">デバイス</span>"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "<span class=\"w-100\">Linear</span>"
msgstr "<span class=\"w-100\">線状</span>"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "ACCESS OPTIONS ANYWAY"
msgstr "いずれにせよアクセスオプション"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_alternatives_dialog.xml:0
msgid "AI Copywriter"
msgstr "AIコピーライター"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "AI Tools"
msgstr "AIツール"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Above"
msgstr "上"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
msgid "Accepts"
msgstr "同意"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/media_dialog.xml:0
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
msgid "Add"
msgstr "追加"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Add Column"
msgstr "カラム追加"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Add Row"
msgstr "行を追加"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/document_selector.js:0
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.js:0
msgid "Add URL"
msgstr "URLを追加"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Add a blockquote section"
msgstr "Blockquoteセクションを追加"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Add a button"
msgstr "ボタンを追加"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Add a code section"
msgstr "コードセクションを追加"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Add a link"
msgstr "リンクを追加"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Add an emoji"
msgstr "絵文字を追加する"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Aden"
msgstr "Aden"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Airy & Zigs"
msgstr "Airy & Zigs"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Airy &amp; Zigs"
msgstr "Airy &amp; Zigs"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.js:0
msgid "Alert"
msgstr "アラート"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Align Center"
msgstr "中央揃え"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Align Left"
msgstr "左揃え"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Align Right"
msgstr "右揃え"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Alignment"
msgstr "配置"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.xml:0
msgid "All"
msgstr "全て"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/document_selector.js:0
msgid "All documents have been loaded"
msgstr "全ての書類が読み込まれました"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.js:0
msgid "All images have been loaded"
msgstr "全ての画像が読み込まれました"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid "Allow users to view and edit the field in HTML."
msgstr "ユーザがHTMLでフィールドを表示、編集できるようにします。"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Alt tag"
msgstr "代替タグ"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.js:0
msgid "An error occurred while fetching the entered URL."
msgstr "入力されたURLの取得中にエラーが発生しました。"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Angle"
msgstr "角度"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
msgid "Animated"
msgstr "アニメーション"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Anonymous"
msgstr "匿名"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Apply"
msgstr "適用"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "Are you sure you want to delete the block %s?"
msgstr "本当にブロック%sを削除しますか?"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.js:0
msgid "Are you sure you want to delete this file?"
msgstr "このファイルを本当に削除しますか？"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Aspect Ratio"
msgstr "アスペクト比"

#. module: web_editor
#: model:ir.model,name:web_editor.model_web_editor_assets
msgid "Assets Utils"
msgstr "アセットユーティリティ"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Autoconvert to Relative Link"
msgstr "相対リンクへの自動変換"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Autoconvert to relative link"
msgstr "相対リンクに自動変換"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.js:0
msgid "Autoplay"
msgstr "自動再生"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Back to one column"
msgstr "1カラム戻る"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Background"
msgstr "背景"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Background Color"
msgstr "背景色"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Background Position"
msgstr "背景位置"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Background Shapes"
msgstr "背景形状"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Banner Danger"
msgstr "バナーの危険性"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Banner Info"
msgstr "バナー情報"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Banner Success"
msgstr "バナー成功"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Banner Warning"
msgstr "バナー警告"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Banners"
msgstr "バナー"

#. module: web_editor
#: model:ir.model,name:web_editor.model_base
msgid "Base"
msgstr "ベース"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Basic blocks"
msgstr "基本ブロック"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Below"
msgstr "下"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Big section heading"
msgstr "大セクション見出し"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Blobs"
msgstr "ブロブ"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "Block"
msgstr "ブロック"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Block &amp; Rainy"
msgstr "ブロック & レイニー"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "Blocks"
msgstr "ブロック"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Blocks & Rainy"
msgstr "ブロック & レイニー"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Blur"
msgstr "ぼかし"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Bold"
msgstr "ボールド"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Border"
msgstr "ふち"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Border Color"
msgstr "ふち色"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Border Style"
msgstr "ボーダースタイル"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Border Width"
msgstr "ふち幅"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Brannan"
msgstr "Brannan"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Brightness"
msgstr "明るさ"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Brushed"
msgstr "ブラシ仕上げ"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Bulleted list"
msgstr "箇条書きリスト"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "Button"
msgstr "ボタン"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link.js:0
msgid "Button Primary"
msgstr "プライマリボタン"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link.js:0
msgid "Button Secondary"
msgstr "セカンダリボタン"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid "CSS Edit"
msgstr "CSS編集"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_alternatives_dialog.xml:0
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_translate_dialog.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "Cancel"
msgstr "キャンセル"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Center"
msgstr "中央"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/alt_dialog.xml:0
msgid "Change media description and tooltip"
msgstr "メディアの説明とツールチップを変更"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "ChatGPT"
msgstr "ChatGPT"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Checklist"
msgstr "チェックリスト"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
msgid "Choose a record..."
msgstr "レコードを選択..."

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/upload_progress_toast/upload_progress_toast.xml:0
msgid "Close"
msgstr "閉じる"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Code"
msgstr "コード"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid "Codeview"
msgstr "コードビュー"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid "Collaborative edition"
msgstr "共同編集"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid "Collaborative trigger"
msgstr "共同トリガ"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_color_widget
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Color"
msgstr "色"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Color Filter"
msgstr "カラーフィルタ"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Colors"
msgstr "色"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Column"
msgstr "カラム"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg_colorpicker.xml:0
msgid "Common colors"
msgstr "一般的な色"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Composites"
msgstr "コンポジット"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Composition"
msgstr "合成"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "Confirm"
msgstr "確認"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/conflict_dialog.xml:0
msgid "Content conflict"
msgstr "衝突コンテンツ"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Content generated"
msgstr "コンテンツが生成されました"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Contrast"
msgstr "コントラスト"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Convert into 2 columns"
msgstr "2カラムに変換"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Convert into 3 columns"
msgstr "3カラムに変換"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Convert into 4 columns"
msgstr "4カラムに変換"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
msgid "Copy-paste your URL or embed code here"
msgstr "ここにURL又は埋め込みコードをコピーペーストしてください"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "Could not install module %s"
msgstr "モジュール%sをインストールできませんでした"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/upload_progress_toast/upload_service.js:0
msgid "Could not load the file \"%s\"."
msgstr "ファイル \"%s\"をロードできませんでした"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Cover"
msgstr "カバー"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
msgid "Create"
msgstr "作成"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Create a list with numbering"
msgstr "番号でリストを作成"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Create a simple bulleted list"
msgstr "簡単な箇条書きリストを作成"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Create an URL."
msgstr "URLを作成。"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__create_uid
msgid "Created by"
msgstr "作成者"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__create_date
msgid "Created on"
msgstr "作成日"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Crop Image"
msgstr "画像を切り抜く"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link.js:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
msgid "Custom"
msgstr "カスタム"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
msgid "Custom %s"
msgstr "カスタム%s"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
msgid "Custom Button"
msgstr "カスタムボタン"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "Customize"
msgstr "カスタマイズ"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
msgid "Dailymotion"
msgstr "Dailymotion"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Dashed"
msgstr "破線"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Decoration"
msgstr "装飾"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Default"
msgstr "デフォルト"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Default + Rounded"
msgstr "デフォルト + 丸い"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Define a custom gradient"
msgstr "カスタムグラデーションを定義"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Delete"
msgstr "削除"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "Delete %s"
msgstr "削除%s"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/alt_dialog.xml:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Description"
msgstr "説明"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Devices"
msgstr "装置"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Direct Download"
msgstr "直接ダウンロード"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/media_dialog.xml:0
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/alt_dialog.xml:0
#: code:addons/web_editor/static/src/xml/add_snippet_dialog.xml:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Discard"
msgstr "破棄"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.xml:0
msgid ""
"Discover a world of awesomeness in our copyright-free image haven. No legal "
"drama, just nice images!"
msgstr "著作権フリーの画像の楽園で、素晴らしい世界を発見して下さい。法的に問題のない、素敵な画像です！"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Display 1"
msgstr "ディスプレイ 1"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Display 2"
msgstr "ディスプレイ 2"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Display 3"
msgstr "ディスプレイ 3"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Display 4"
msgstr "ディスプレイ 4"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__display_name
msgid "Display Name"
msgstr "表示名"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "Do you want to install %s App?"
msgstr " %s アプリをインストールしますか？"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/media_dialog.js:0
msgid "Documents"
msgstr "書類"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Dotted"
msgstr "点線"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Double"
msgstr "ダブル"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Double-click to edit"
msgstr "ダブルクリックして編集"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "Drag and drop the building block."
msgstr "ビルディングブロックをドラックアンドドロップ。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "Duplicate Container"
msgstr "コンテナを複製"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Dynamic Colors"
msgstr "ダイナミック色"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid "Dynamic Placeholder"
msgstr "動的プレースホルダ"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "EarlyBird"
msgstr "EarlyBird"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Edit image"
msgstr "画像を編集"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Edit media description"
msgstr "メディア説明を編集"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Embed Image"
msgstr "埋め込み画像"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Embed Youtube Video"
msgstr "埋め込みYoutube動画"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Embed the image in the document."
msgstr "書類に画像を埋め込みます。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Embed the youtube video in the document."
msgstr "書類にYoutube動画を埋め込みます。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Emoji"
msgstr "絵文字"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Empty quote"
msgstr "絵文字引用"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.js:0
msgid "Error"
msgstr "エラー"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Extend to the closest corner"
msgstr "最も近い隅に伸ばす"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Extend to the closest side"
msgstr "最も近い側に伸ばす"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Extend to the farthest corner"
msgstr "最も遠い隅に伸ばす"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Extend to the farthest side"
msgstr "最も遠い側に伸ばす"

#. module: web_editor
#: model:ir.model,name:web_editor.model_html_field_history_mixin
msgid "Field html History"
msgstr "フリートhtml履歴"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/upload_progress_toast/upload_progress_toast.xml:0
msgid "File has been uploaded"
msgstr "ファイルはアップロードされました"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Fill"
msgstr "塗りつぶし"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Fill + Rounded"
msgstr "塗りつぶし + 丸い"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Fill Color"
msgstr "塗りつぶし"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Filter"
msgstr "フィルター"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
msgid "First Panel"
msgstr "ファーストパネル"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Flat"
msgstr "フラット"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/image_crop.js:0
msgid "Flexible"
msgstr "柔軟"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Flip"
msgstr "反転"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Flip Horizontal"
msgstr "左右に反転"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Flip Vertical"
msgstr "上下に反転"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Floating Shapes"
msgstr "浮遊形状"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid "Focus"
msgstr "フォーカス"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Font Color"
msgstr "フォント色"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Font size"
msgstr "フォントサイズ"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "For technical reasons, this block cannot be dropped here"
msgstr "技術上の理由でこのブロックはここにドロップできません"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Format"
msgstr "フォーマット"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Full screen"
msgstr "全画面"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/backend.xml:0
msgid "Fullscreen"
msgstr "全画面表示"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_prompt_dialog.xml:0
msgid "Generate Text with AI"
msgstr "AIでテキストを生成"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Generate or transform content with AI"
msgstr "AIでコンテンツを作成または変更"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Generate or transform content with AI."
msgstr "AIでコンテンツを作成または変更"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_alternatives_dialog.xml:0
msgid "Generating"
msgstr "生成中"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_alternatives_dialog.xml:0
msgid "Generating an alternative..."
msgstr "代替を生成中..."

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Geometrics"
msgstr "幾何学模様"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Geometrics Panels"
msgstr "幾何学模様パネル"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Geometrics Rounded"
msgstr "丸みを帯びた幾何学模様"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "Gradient"
msgstr "グラデーション"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_http
msgid "HTTP Routing"
msgstr "HTTPルーティング"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Header 1"
msgstr "ヘッダ 1"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Header 1 Display 1"
msgstr "見出し1 ディスプレイ1"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Header 1 Display 2"
msgstr "見出し1 ディスプレイ2"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Header 1 Display 3"
msgstr "見出し1 ディスプレイ3"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Header 1 Display 4"
msgstr "見出し1 ディスプレイ4"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Header 2"
msgstr "ヘッダ 2"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Header 3"
msgstr "ヘッダ 3"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Header 4"
msgstr "ヘッダ 4"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Header 5"
msgstr "ヘッダ 5"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Header 6"
msgstr "ヘッダ 6"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Heading 1"
msgstr "見出し 1"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Heading 2"
msgstr "見出し 2"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Heading 3"
msgstr "見出し 3"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Heading 4"
msgstr "見出し 4"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Heading 5"
msgstr "見出し 5"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Heading 6"
msgstr "見出し 6"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Height"
msgstr "高さ"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.js:0
msgid "Hide Dailymotion logo"
msgstr "Dailymotionロゴを非表示"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.js:0
msgid "Hide fullscreen button"
msgstr "全画面表示ボタンを非表示"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.js:0
msgid "Hide player controls"
msgstr "プレイヤーコントロールを非表示"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.js:0
msgid "Hide sharing button"
msgstr "共有ボタンを非表示"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_html_field_history_mixin__html_field_history
msgid "History data"
msgstr "履歴データ"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_html_field_history_mixin__html_field_history_metadata
msgid "History metadata"
msgstr "履歴メタデータ"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Horizontal mirror"
msgstr "水平ミラー"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid "Html"
msgstr "HTML"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__id
msgid "ID"
msgstr "ID"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "Icon"
msgstr "アイコン"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "Icon Formatting"
msgstr "アイコンフォーマット"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Icon size 1x"
msgstr "アイコンサイズ 1x"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Icon size 2x"
msgstr "アイコンサイズ 2x"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Icon size 3x"
msgstr "アイコンサイズ 3x"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Icon size 4x"
msgstr "アイコンサイズ 4x"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Icon size 5x"
msgstr "アイコンサイズ 5x"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/media_dialog.js:0
msgid "Icons"
msgstr "アイコン"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid ""
"If you discard the current edits, all unsaved changes will be lost. You can "
"cancel to return to edit mode."
msgstr "現在の編集を破棄した場合、未保存の全ての変更が失われます。キャンセルして編集モードに戻られます。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.xml:0
msgid "Illustrations"
msgstr "イラスト"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Image"
msgstr "画像"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "Image Formatting"
msgstr "画像フォーマット"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Image padding"
msgstr "画像パディング"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/media_dialog.js:0
msgid "Images"
msgstr "画像"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Inkwell"
msgstr "Inkwell"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "Inline Text"
msgstr "インラインテキスト"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_alternatives_dialog.xml:0
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_prompt_dialog.xml:0
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_translate_dialog.xml:0
msgid "Insert"
msgstr "挿入"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Insert a Link / Button"
msgstr "リンク/ボタンを挿入"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/add_snippet_dialog.xml:0
msgid "Insert a block"
msgstr "ブロックを挿入"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Insert a danger banner"
msgstr "危険バナーを挿入"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid "Insert a field"
msgstr "フィールドを挿入"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Insert a horizontal rule separator"
msgstr "水平方向の罫線を挿入"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Insert a picture"
msgstr "写真の挿入"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Insert a rating over 3 stars"
msgstr "3星以上の評価を挿入"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Insert a rating over 5 stars"
msgstr "5星以上の評価を挿入"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Insert a success banner"
msgstr "成功バナーを挿入"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Insert a table"
msgstr "表を挿入"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Insert a video"
msgstr "ビデオを挿入"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Insert a warning banner"
msgstr "警告バナーを挿入"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Insert above"
msgstr "上に挿入"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Insert an info banner"
msgstr "情報バナーを挿入"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Insert below"
msgstr "下に挿入"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Insert left"
msgstr "左に挿入"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Insert media"
msgstr "メディアを挿入"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Insert or edit link"
msgstr "リンクを挿入・編集"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Insert right"
msgstr "右に挿入"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Insert your signature"
msgstr "自分の署名を挿入"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "Install"
msgstr "インストール"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/add_snippet_dialog.js:0
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "Install %s"
msgstr "インストール%s"

#. module: web_editor
#. odoo-python
#: code:addons/web_editor/models/ir_ui_view.py:0
msgid "Invalid field value for %(field_name)s: %(value)s"
msgstr "以下のための無効なフィールド値: %(field_name)s: %(value)s"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "Invisible Elements"
msgstr "非表示要素"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
msgid "Item"
msgstr "項目"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Label"
msgstr "ラベル"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Large"
msgstr "大"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__write_uid
msgid "Last Updated by"
msgstr "最終更新者"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__write_date
msgid "Last Updated on"
msgstr "最終更新日"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Layout"
msgstr "レイアウト"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Left"
msgstr "左"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Light"
msgstr "ライト"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Linear"
msgstr "線型"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Link"
msgstr "リンク"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Link Label"
msgstr "リンクラベル"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Link Shape"
msgstr "形をリンク"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Link Size"
msgstr "サイズをリンク"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Link Style"
msgstr "リンクスタイル"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link_popover_widget.js:0
msgid "Link copied to clipboard."
msgstr "リンクはクリップボードにコピーされました。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Link to an uploaded document"
msgstr "アップロード済ドキュメントにリンク"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "List"
msgstr "リスト"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.xml:0
msgid "Load more..."
msgstr "さらに読み込む..."

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.xml:0
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_alternatives_dialog.xml:0
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_prompt_dialog.xml:0
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_translate_dialog.xml:0
#: code:addons/web_editor/static/src/xml/add_snippet_dialog.xml:0
msgid "Loading..."
msgstr "読込中..."

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.js:0
msgid "Loop"
msgstr "ループ"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Main Color"
msgstr "メイン色"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid "Marketing Tools"
msgstr "マーケティングツール"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Maven"
msgstr "Maven"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid "Max height"
msgstr "最大の高さ"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Media"
msgstr "メディア"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Medium"
msgstr "中"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Medium section heading"
msgstr "中セクション見出し"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid "Min height"
msgstr "最小の高さ"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "More info about this app."
msgstr "このアプリについての詳細情報。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Move down"
msgstr "下に移動"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Move left"
msgstr "左に移動"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Move right"
msgstr "右に移動"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Move up"
msgstr "上に移動"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.xml:0
msgid "My Images"
msgstr "自分の画像"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/add_snippet_dialog.xml:0
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__name
msgid "Name"
msgstr "名称"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Navigation"
msgstr "ナビゲーション"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "No"
msgstr "いいえ"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link_popover_widget.js:0
msgid "No URL specified"
msgstr "特定されたURLはありません"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/document_selector.xml:0
msgid "No documents found."
msgstr "書類は見つかりませんでした。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.xml:0
msgid "No images found."
msgstr "画像は見つかりませんでした。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
msgid "No more records"
msgstr "これ以上のレコードはありません"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/icon_selector.xml:0
msgid "No pictograms found."
msgstr "ピクトグラムは見つかりませんでした。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid "No videos"
msgstr "動画なし"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "None"
msgstr "なし"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Normal"
msgstr "通常"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Numbered list"
msgstr "段落番号リスト"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.tests
msgid "Odoo Editor Tests"
msgstr "Odooエディターテスト"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Open in New Window"
msgstr "新規ウィンドウで開く"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Open in new window"
msgstr "新しいウインドウで開く"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.xml:0
msgid "Optimized"
msgstr "最適化済"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Origins"
msgstr "入手元"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Outline"
msgstr "アウトライン"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Outline + Rounded"
msgstr "アウトライン + 丸い"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
msgid "Overlay"
msgstr "オーバーレイ"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Padding"
msgstr "パディング"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "Page Options"
msgstr "ページオプション"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Panels"
msgstr "パネル"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Paragraph block"
msgstr "パラグラフブロック"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Paste as URL"
msgstr "URLとして貼付"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Patterns"
msgstr "パターン"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Position"
msgstr "ポジション"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "Preset #{number}"
msgstr "プリセット #{number}"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Preview"
msgstr "プレビュー"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/upload_progress_toast/upload_progress_toast.xml:0
msgid "Progress bar"
msgstr "進捗バー"

#. module: web_editor
#: model:ir.model.fields.selection,name:web_editor.selection__web_editor_converter_test__selection_str__c
msgid "Qu'est-ce qu'il fout ce maudit pancake, tabernacle ?"
msgstr "Qu'est-ce qu'il fout ce maudit pancake, tabernacle ?"

#. module: web_editor
#: model:ir.model.fields.selection,name:web_editor.selection__web_editor_converter_test__selection_str__a
msgid "Qu'il n'est pas arrivé à Toronto"
msgstr "Qu'il n'est pas arrivé à Toronto"

#. module: web_editor
#: model:ir.model.fields.selection,name:web_editor.selection__web_editor_converter_test__selection_str__b
msgid "Qu'il était supposé arriver à Toronto"
msgstr "Qu'il était supposé arriver à Toronto"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Quality"
msgstr "品質"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Quote"
msgstr "引用"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb
msgid "Qweb"
msgstr "Qweb"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field
msgid "Qweb Field"
msgstr "Qwebフィールド"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_contact
msgid "Qweb Field Contact"
msgstr "Qwebフィールドコンタクト"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_date
msgid "Qweb Field Date"
msgstr "Qwebフィールド日付"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_datetime
msgid "Qweb Field Datetime"
msgstr "Qwebフィールド日時"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_duration
msgid "Qweb Field Duration"
msgstr "Qwebフィールド期間"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_float
msgid "Qweb Field Float"
msgstr "Qwebフィールドフロート"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_html
msgid "Qweb Field HTML"
msgstr "QwebフィールドHTML"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_image
msgid "Qweb Field Image"
msgstr "Qwebフィールド画像"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_integer
msgid "Qweb Field Integer"
msgstr "Qwebフィールド整数"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_many2one
msgid "Qweb Field Many to One"
msgstr "Qwebフィールド多対一"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_monetary
msgid "Qweb Field Monetary"
msgstr "Qwebフィールド金融"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_relative
msgid "Qweb Field Relative"
msgstr "Qwebフィールド相対"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_selection
msgid "Qweb Field Selection"
msgstr "Qwebフィールド選択"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_text
msgid "Qweb Field Text"
msgstr "Qwebフィールドテキスト"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_qweb
msgid "Qweb Field qweb"
msgstr "Qwebフィールドqweb"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "REPLACE BY NEW VERSION"
msgstr "新バージョンに置換え"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Radial"
msgstr "放射状"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Readonly field"
msgstr "読取専用フィールド"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Redirect the user elsewhere when he clicks on the media."
msgstr "メディアをクリックしたユーザーを他のところにリダイレクトします。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Remove (DELETE)"
msgstr "削除 (DELETE)"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "Remove Block"
msgstr "ブロックを削除"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Remove Current"
msgstr "現在のものを削除"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Remove Selected Color"
msgstr "選択した色を削除"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Remove columns"
msgstr "カラムを削除"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "Remove format"
msgstr "フォマットを削除"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Remove link"
msgstr "リンクを削除"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "Rename %s"
msgstr "名前変更%s"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/add_snippet_dialog.xml:0
msgid "Rename the block"
msgstr "ブロック名を変更"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Repeat pattern"
msgstr "パータンを繰り返す"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Replace"
msgstr "置き換える"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Replace media"
msgstr "メディアを置き換える"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Reset"
msgstr "リセット"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Reset Image"
msgstr "画像をリセット"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Reset Size"
msgstr "サイズを再設定"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Reset crop"
msgstr "切り抜きをリセット"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Reset transformation"
msgstr "変形をリセット"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid "Resizable"
msgstr "サイズ変更可能"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Resize Default"
msgstr "サイズ変更デフォルト"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Resize Full"
msgstr "全画面でサイズ変更"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Resize Half"
msgstr "半画面でサイズ変更"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Resize Quarter"
msgstr "四分の一でサイズ変更"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Right"
msgstr "右"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Rotate Left"
msgstr "左に回転"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Rotate Right"
msgstr "右に回転"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Rotate left"
msgstr "左に回転"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Rotate right"
msgstr "右に回転"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Row"
msgstr "行"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid "Sandboxed preview"
msgstr "サンドボックスプレビュー"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Saturation"
msgstr "彩度"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/alt_dialog.xml:0
#: code:addons/web_editor/static/src/xml/add_snippet_dialog.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "Save"
msgstr "保存"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "Save and Install"
msgstr "保存してインストール"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
msgid "Save and Reload"
msgstr "保存して再読込"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/document_selector.js:0
msgid "Search a document"
msgstr "書類を検索"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/icon_selector.xml:0
msgid "Search a pictogram"
msgstr "ピクトグラムを検索"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.js:0
msgid "Search an image"
msgstr "画像を検索"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/add_snippet_dialog.xml:0
msgid "Search for a block"
msgstr "ブロックを検索"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "Search for a block (e.g. numbers, image wall, ...)"
msgstr "ブロックを検索 (例: 番号、画像壁...)"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
msgid "Search for records..."
msgstr "レコードを検索..."

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
msgid "Search more..."
msgstr "もっと検索..."

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
msgid "Search to show more records"
msgstr "検索してさらにレコードを表示"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "Select a block on your page to style it."
msgstr "ページ中のブロックを選択して編集します。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/media_dialog.xml:0
msgid "Select a media"
msgstr "メディアを選択"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_prompt_dialog.xml:0
msgid "Send a message"
msgstr "メッセージを送信"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
msgid "Separator"
msgstr "区切り線"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Sepia"
msgstr "Sepia"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Shadow"
msgstr "シャドウ"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Shape"
msgstr "形状"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Shape: Circle"
msgstr "形状: 円"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Shape: Rounded"
msgstr "形状: 丸い"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Shape: Thumbnail"
msgstr "形状: サムネイル"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Shapes"
msgstr "形"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.xml:0
msgid "Show optimized images"
msgstr "最適化済画像を表示"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Show/Hide on Mobile"
msgstr "モバイルに表示・非表示"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Signature"
msgstr "署名"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Size"
msgstr "サイズ"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Size 1x"
msgstr "サイズ 1x"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Size 2x"
msgstr "サイズ 2x"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Size 3x"
msgstr "サイズ 3x"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Size 4x"
msgstr "サイズ 4x"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Size 5x"
msgstr "サイズ 5x"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Small"
msgstr "小"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Small section heading"
msgstr "小セクション見出し"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/add_snippet_dialog.xml:0
msgid "Snippet name"
msgstr "スニペット名"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid "Snippets"
msgstr "スニペット"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Solid"
msgstr "ソリッド"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Solids"
msgstr "ソリッド"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid ""
"Someone with escalated rights previously modified this area, you are "
"therefore not able to modify it yourself."
msgstr "より高いアクセス権を有するユーザーは最近このエリアを変更しましたのであなたは変更できないようになりました。"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Specials"
msgstr "スペシャル"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid ""
"Specify when the collaboration starts. 'Focus' will start the collaboration "
"session when the user clicks inside the text field (default), 'Start' when "
"the record is loaded (could impact performance if set)."
msgstr ""
"コラボレーションを開始するタイミングを指定します。'フォーカス'はユーザがテキストフィールド内をクリックした時(デフォルト)、'スタート'はレコードが読み込まれた時(設定するとパフォーマンスに影響する可能性があります)にコラボレーションセッションを開始します。"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Speed"
msgstr "速度"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid "Start"
msgstr "開始"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Stretch"
msgstr "ストレッチ"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Structure"
msgstr "給与体系"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Style"
msgstr "スタイル"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
msgid "Suggestions"
msgstr "提案"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Switch direction"
msgstr "方向を変更"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Switch the text's direction"
msgstr "テキスト方向を変更"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Table"
msgstr "表"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Table Options"
msgstr "表オプション"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "Text"
msgstr "テキスト"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Text Color"
msgstr "テキスト色"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Text align"
msgstr "テキスト配置"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Text style"
msgstr "テキストスタイル"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.xml:0
msgid "The URL does not seem to work."
msgstr "URLは無効みたいです。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.xml:0
msgid "The URL seems valid."
msgstr "URLは有効みたいです。"

#. module: web_editor
#. odoo-python
#: code:addons/web_editor/models/ir_qweb_fields.py:0
msgid "The datetime %(value)s does not match the format %(format)s"
msgstr "datetime %(value)sがフォーマット %(format)sに一致しません"

#. module: web_editor
#. odoo-python
#: code:addons/web_editor/tools.py:0
msgid ""
"The document was already saved from someone with a different history for "
"model \"%(model)s\", field \"%(field)s\" with id \"%(id)d\"."
msgstr ""
"ドキュメントはモデル\"%(model)s\", フィールド \"%(field)s\" 、id "
"\"%(id)d\"用に異なる履歴を持つ他の誰かによって既に保存されています。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.js:0
msgid "The provided url does not reference any supported video"
msgstr "提供されたURLはどんな対応動画でも参照してません"

#. module: web_editor
#. odoo-python
#: code:addons/web_editor/tools.py:0
msgid "The provided url is invalid"
msgstr "提供されたURLは無効です"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.js:0
msgid "The provided url is not valid"
msgstr "提供されたURLは無効です"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/conflict_dialog.xml:0
msgid ""
"The version from the database will be used.\n"
"                            If you need to keep your changes, copy the content below and edit the new document."
msgstr ""
"データベースからのバージョンが使われます。\n"
"                    　　変更を保存したい場合は以下の内容をコピーして新規書類を編集して下さい。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "Theme"
msgstr "テーマ"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg_colorpicker.xml:0
msgid "Theme colors"
msgstr "テーマ色"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/conflict_dialog.xml:0
msgid "There is a conflict between your version and the one in the database."
msgstr "お使いのバージョンとデータベースのバージョンは一致ではありません。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_prompt_dialog.xml:0
msgid "Thinking..."
msgstr "思考中..."

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link_popover_widget.js:0
msgid "This URL is invalid. Preview couldn't be updated."
msgstr "このURLは無効です。プレビュー表示が更新できませんでした。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "This block cannot be dropped anywhere on this page."
msgstr "このブロックはこのページのどこにもドロップできません。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "This block is outdated."
msgstr "このブロックは古いです。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "This document is not saved!"
msgstr "この書類は保存されてません！"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.js:0
msgid "This file is a public view attachment."
msgstr "このファイルはパブリックビュー添付ファイルです。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.js:0
msgid "This file is attached to the current record."
msgstr "このファイルは現在のレコードに添付されています。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/image_crop.js:0
msgid "This image is an external image"
msgstr "この画像は外部画像です"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/image_crop.js:0
msgid ""
"This type of image is not supported for cropping.<br/>If you want to crop "
"it, please first download it from the original source and upload it in Odoo."
msgstr "この画像タイプは切抜に対応してません。<br/>切抜したい場合は元のソースから画像をダウンロードしてOdooにアップロードしてください。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "Tip: Esc to preview"
msgstr "ヒント: Escでプレビュー"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "Title"
msgstr "タイトル"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Title tag"
msgstr "タイトルタグ"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
msgid ""
"To save a snippet, we need to save all your previous modifications and "
"reload the page."
msgstr "スニペットを保存するには以前の全変更を保存してページを再読み込みしなければなりません。"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Toaster"
msgstr "Toaster"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Toggle bold"
msgstr "太字を切り替える"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Toggle checklist"
msgstr "チェックリストを切り替える"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Toggle icon spin"
msgstr "アイコン回転を切り替える"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Toggle italic"
msgstr "斜体を切り替える"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Toggle ordered list"
msgstr "順序付きリストを切り替える"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Toggle strikethrough"
msgstr "取消線を切り替える"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Toggle underline"
msgstr "下線を切り替える"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Toggle unordered list"
msgstr "順序なしリストを切り替える"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/alt_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Tooltip"
msgstr "ツールチップ"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Track tasks with a checklist"
msgstr "チェックリストで作業を追跡"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Transform"
msgstr "変形"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Transform the picture"
msgstr "画像を変形"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Transform the picture (click twice to reset transformation)"
msgstr "画像を変形 (2回クリックしてリセットできる)"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/backend.xml:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Translate"
msgstr "翻訳"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_translate_dialog.xml:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Translate with AI"
msgstr "AIで翻訳"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_translate_dialog.xml:0
msgid "Translating..."
msgstr "翻訳中..."

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg_colorpicker.xml:0
msgid "Transparent colors"
msgstr "透明色"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/icon_selector.xml:0
msgid "Try searching with other keywords."
msgstr "他のキーワードで検索してみましょう。"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Type"
msgstr "タイプ"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Type \"/\" for commands"
msgstr "コマンドには「/」を入力してください"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "URL or Email"
msgstr "URL又はEメール"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Unalign"
msgstr "整列なし"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/document_selector.js:0
msgid "Upload a document"
msgstr "書類をアップロード"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.js:0
msgid "Upload an image"
msgstr "画像をアップロード"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.js:0
msgid "Uploaded image's format is not supported. Try with: "
msgstr "アップロードされた画像のフォーマットは対応されてません。他のを試行してください:"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Valencia"
msgstr "Valencia"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Vertical mirror"
msgstr "垂直ミラー"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Video"
msgstr "動画"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "Video Formatting"
msgstr "動画フォーマット"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
msgid "Video code"
msgstr "動画コード"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/media_dialog.js:0
msgid "Videos"
msgstr "ビデオ"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.js:0
msgid "Videos are muted when autoplay is enabled"
msgstr "自動再生を有効化すると動画がミュートされます"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_ui_view
msgid "View"
msgstr "ビュー"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
msgid "Vimeo"
msgstr "Vimeo"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Walden"
msgstr "Walden"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/conflict_dialog.xml:0
msgid ""
"Warning: after closing this dialog, the version you were working on will be "
"discarded and will never be available anymore."
msgstr "警告: このダイアログを閉じた後、現在作業中のバージョンは放棄されて今後も使えなくなります。"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Wavy"
msgstr "波状"

#. module: web_editor
#: model:ir.model,name:web_editor.model_web_editor_converter_test_sub
msgid "Web Editor Converter Subtest"
msgstr "Webエディターコンバーターサブテスト"

#. module: web_editor
#: model:ir.model,name:web_editor.model_web_editor_converter_test
msgid "Web Editor Converter Test"
msgstr "Webエディターコンバーターテスト"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Widgets"
msgstr "ウィジェット"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Width"
msgstr "幅"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid ""
"With the option enabled, all content can only be viewed in a sandboxed "
"iframe or in the code editor."
msgstr "このオプションを有効にすると、全てのコンテンツはサンドボックス化されたiframeまたはコードエディタでのみ閲覧できるようになります。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.xml:0
msgid ""
"Wow, it feels a bit empty in here. Upload from the button in the top right "
"corner!"
msgstr "ここは少し空な印象です。右上のボタンからアップロードしましょう！"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
msgid "Write something..."
msgstr "何か書いて下さい..."

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "XL"
msgstr "XL"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Xpro"
msgstr "Xpro"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "Yes"
msgstr "はい"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.js:0
msgid ""
"You can not replace a field by this image. If you want to use this image, "
"first save it on your computer and then upload it here."
msgstr ""
"この画像をフィールドに置き換えることはできません。この画像を使用したい場合は、まずコンピュータに保存してから、こちらにアップロードして下さい。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.xml:0
msgid "You can not use this image in a field"
msgstr "この画像をフィールド内で使用できません"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "You can still access the block options but it might be ineffective."
msgstr "ブロックオプションにアクセスすることはできるが、効果がないかもしれません。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/document_selector.xml:0
msgid ""
"You can upload documents with the button located in the top left of the "
"screen."
msgstr "画面の左上にあるボタンで書類をアップロードすることができます。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "You might not be able to customize it anymore."
msgstr "もうカスタマイズできないかもしれません。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
msgid "Youku"
msgstr "Youku"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Your URL"
msgstr "あなたのURL"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Your content was successfully generated."
msgstr "コンテンツが正常に生成されました。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
msgid "Youtube"
msgstr "Youtube"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Zoom In"
msgstr "拡大"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Zoom Out"
msgstr "ズームアウト"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "add"
msgstr "追加"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.xml:0
msgid "all"
msgstr "全て"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_alternatives_dialog.xml:0
msgid "alternatives..."
msgstr "代替..."

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
msgid "and"
msgstr "と"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "auto"
msgstr "自動"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "darken"
msgstr "暗くする"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.xml:0
msgid "database"
msgstr "データベース"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "exclusion"
msgstr "除外"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "fill"
msgstr "塗りつぶし"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "fill,rounded-circle"
msgstr "塗りつぶし、円"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "flat"
msgstr "フラット"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "lg"
msgstr "lg"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "lighten"
msgstr "明るくする"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.xml:0
msgid "media-library"
msgstr "メディアライブラリ"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "multiply"
msgstr "掛ける"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "outline"
msgstr "輪郭"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "outline,rounded-circle"
msgstr "輪郭、円形"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "overlay"
msgstr "オーバーレイ"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "px"
msgstr "px"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "rounded-circle"
msgstr "円形"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "screen"
msgstr "画面"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "sm"
msgstr "sm"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "to"
msgstr "から"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
msgid "videos"
msgstr "動画"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_websocket
msgid "websocket message handling"
msgstr "WebSocketメッセージ処理"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "www.example.com"
msgstr "www.example.com"
