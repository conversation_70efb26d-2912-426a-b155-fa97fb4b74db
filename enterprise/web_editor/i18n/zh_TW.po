# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_editor
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:41+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
msgid "%spx"
msgstr "%s像素"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
msgid "%spx (Original)"
msgstr "%spx（原始）"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
msgid "%spx (Suggested)"
msgstr "%spx（建議）"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/alt_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid ""
"'Alt tag' specifies an alternate text for an image, if the image cannot be "
"displayed (slow connection, missing image, screen reader ...)."
msgstr "如果無法顯示圖像，則「Alt tag」指定圖像的替代文本（連接速度慢，圖像遺失，螢幕閱讀器......）。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/alt_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "'Title tag' is shown as a tooltip when you hover the picture."
msgstr "'標題標籤' 當您移動鼠標到圖片上面懸停的時候顯示標記說明."

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/alt_dialog.xml:0
msgid "(ALT Tag)"
msgstr "(ALT 標籤)"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/alt_dialog.xml:0
msgid "(TITLE Tag)"
msgstr "(TITLE 標籤)"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
msgid "(URL or Embed)"
msgstr "（網址或嵌入）"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "100%"
msgstr "100%"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "1977"
msgstr "1977"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "1x"
msgstr "1x"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "2 columns"
msgstr "2 欄"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "25"
msgstr "25"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "25%"
msgstr "25%"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "2x"
msgstr "2x"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "3 Stars"
msgstr "3 粒星"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "3 columns"
msgstr "3 欄"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "3x"
msgstr "3x"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "4 columns"
msgstr "4 欄"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "4x"
msgstr "4x"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "5 Stars"
msgstr "5 粒星"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "50%"
msgstr "50%"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "5x"
msgstr "5x"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "90"
msgstr "90"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "<span class=\"flex-grow-0 ms-1 text-white-50\">%</span>"
msgstr "<span class=\"flex-grow-0 ms-1 text-white-50\">%</span>"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "<span class=\"flex-grow-0 ms-1 text-white-50\">deg</span>"
msgstr "<span class=\"flex-grow-0 ms-1 text-white-50\">度</span>"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "<span class=\"me-2 ms-3\">Y</span>"
msgstr "<span class=\"me-2 ms-3\">Y</span>"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "<span class=\"me-2\">X</span>"
msgstr "<span class=\"me-2\">X</span>"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "<span class=\"w-100\">Basic</span>"
msgstr "<span class=\"w-100\">基本</span>"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "<span class=\"w-100\">Creative</span>"
msgstr "<span class=\"w-100\">創意</span>"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "<span class=\"w-100\">Decorative</span>"
msgstr "<span class=\"w-100\">裝飾</span>"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "<span class=\"w-100\">Devices</span>"
msgstr "<span class=\"w-100\">裝置</span>"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "<span class=\"w-100\">Linear</span>"
msgstr "<span class=\"w-100\">線性</span>"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "ACCESS OPTIONS ANYWAY"
msgstr "繼續存取選項"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_alternatives_dialog.xml:0
msgid "AI Copywriter"
msgstr "AI 人工智能文案"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "AI Tools"
msgstr "人工智能工具"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Above"
msgstr "上方"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
msgid "Accepts"
msgstr " 接受"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/media_dialog.xml:0
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
msgid "Add"
msgstr "加入"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Add Column"
msgstr "增加列"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Add Row"
msgstr "新增一行"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/document_selector.js:0
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.js:0
msgid "Add URL"
msgstr "加入網上圖片"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Add a blockquote section"
msgstr "加入引用文字區塊。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Add a button"
msgstr "加入按鈕。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Add a code section"
msgstr "加入程式碼段落。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Add a link"
msgstr "加入連結。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Add an emoji"
msgstr "加入表情符號"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Aden"
msgstr "Aden"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Airy & Zigs"
msgstr "輕逸、幼線"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Airy &amp; Zigs"
msgstr "輕逸、幼線"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.js:0
msgid "Alert"
msgstr "警示"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Align Center"
msgstr "置中對齊"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Align Left"
msgstr "向左對齊"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Align Right"
msgstr "向右對齊"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Alignment"
msgstr "對齊方式"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.xml:0
msgid "All"
msgstr "所有"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/document_selector.js:0
msgid "All documents have been loaded"
msgstr "已載入所有文件"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.js:0
msgid "All images have been loaded"
msgstr "已完成載入所有圖片"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid "Allow users to view and edit the field in HTML."
msgstr "允許使用者以 HTML 檢視及編輯欄位。"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Alt tag"
msgstr "Alt 標籤"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.js:0
msgid "An error occurred while fetching the entered URL."
msgstr "按輸入的網址讀取網頁時，發生錯誤。"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Angle"
msgstr "角度"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
msgid "Animated"
msgstr "動畫"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Anonymous"
msgstr "匿名"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Apply"
msgstr "套用"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "Are you sure you want to delete the block %s?"
msgstr "確定要刪除內容方塊 %s？"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.js:0
msgid "Are you sure you want to delete this file?"
msgstr "是否確實要刪除此檔？"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Aspect Ratio"
msgstr "寬高比"

#. module: web_editor
#: model:ir.model,name:web_editor.model_web_editor_assets
msgid "Assets Utils"
msgstr "資產使用"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Autoconvert to Relative Link"
msgstr "自動轉換為相對連結"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Autoconvert to relative link"
msgstr "自動轉換為相對連結"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.js:0
msgid "Autoplay"
msgstr "自動播放"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Back to one column"
msgstr "換回單一欄位結構。"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Background"
msgstr "背景"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Background Color"
msgstr "背景顏色"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Background Position"
msgstr "背景位置"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Background Shapes"
msgstr "背景形狀"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Banner Danger"
msgstr "橫額-危險"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Banner Info"
msgstr "橫額-資訊"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Banner Success"
msgstr "橫額-成功"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Banner Warning"
msgstr "橫額-警告"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Banners"
msgstr "橫額"

#. module: web_editor
#: model:ir.model,name:web_editor.model_base
msgid "Base"
msgstr "計稅基數"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Basic blocks"
msgstr "基本區塊"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Below"
msgstr "下方"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Big section heading"
msgstr "大字章節標題。"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Blobs"
msgstr "流體、油污效果"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "Block"
msgstr "區塊"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Block &amp; Rainy"
msgstr "大色塊、灑雨效果"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "Blocks"
msgstr "內容區塊"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Blocks & Rainy"
msgstr "大色塊、灑雨效果"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Blur"
msgstr "模糊"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Bold"
msgstr "粗大色塊"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Border"
msgstr "邊框"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Border Color"
msgstr "邊框顏色"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Border Style"
msgstr "邊框樣式"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Border Width"
msgstr "邊框寬度"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Brannan"
msgstr "Brannan"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Brightness"
msgstr "亮度"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Brushed"
msgstr "磨砂"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Bulleted list"
msgstr "點列清單"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "Button"
msgstr "按鈕"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link.js:0
msgid "Button Primary"
msgstr "主要按鈕"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link.js:0
msgid "Button Secondary"
msgstr "次要按鈕"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid "CSS Edit"
msgstr "CSS 編輯"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_alternatives_dialog.xml:0
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_translate_dialog.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "Cancel"
msgstr "取消"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Center"
msgstr "中間"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/alt_dialog.xml:0
msgid "Change media description and tooltip"
msgstr "更改媒體說明和工具提示"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "ChatGPT"
msgstr "ChatGPT"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Checklist"
msgstr "剔選清單"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
msgid "Choose a record..."
msgstr "選擇一項⋯"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/upload_progress_toast/upload_progress_toast.xml:0
msgid "Close"
msgstr "關閉"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Code"
msgstr "程式碼"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid "Codeview"
msgstr "程式碼檢視"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid "Collaborative edition"
msgstr "協作編輯"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid "Collaborative trigger"
msgstr "協作觸發"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_color_widget
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Color"
msgstr "顏色"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Color Filter"
msgstr "色彩濾鏡"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Colors"
msgstr "色彩組合"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Column"
msgstr "欄"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg_colorpicker.xml:0
msgid "Common colors"
msgstr "通用顏色"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Composites"
msgstr "組合"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Composition"
msgstr "組合"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "Confirm"
msgstr "確認"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/conflict_dialog.xml:0
msgid "Content conflict"
msgstr "內容衝突"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Content generated"
msgstr "生成的內容"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Contrast"
msgstr "對比"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Convert into 2 columns"
msgstr "轉換為 2 欄結構。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Convert into 3 columns"
msgstr "轉換為 3 欄結構。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Convert into 4 columns"
msgstr "轉換為 4 欄結構。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
msgid "Copy-paste your URL or embed code here"
msgstr "複製粘貼您的網址或在這裡嵌入代碼"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "Could not install module %s"
msgstr "未能安裝模組 %s"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/upload_progress_toast/upload_service.js:0
msgid "Could not load the file \"%s\"."
msgstr "未能載入檔案 %s。"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Cover"
msgstr "封面"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
msgid "Create"
msgstr "建立"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Create a list with numbering"
msgstr "加入依序編號清單。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Create a simple bulleted list"
msgstr "加入基本點列式清單。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Create an URL."
msgstr "加入網址。"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__create_uid
msgid "Created by"
msgstr "建立人員"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__create_date
msgid "Created on"
msgstr "建立於"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Crop Image"
msgstr "裁剪圖片"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link.js:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
msgid "Custom"
msgstr "自訂"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
msgid "Custom %s"
msgstr "Custom %s"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
msgid "Custom Button"
msgstr "自訂按鈕"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "Customize"
msgstr "自訂"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
msgid "Dailymotion"
msgstr "Dailymotion"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Dashed"
msgstr "虛線"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Decoration"
msgstr "裝飾"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Default"
msgstr "預設"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Default + Rounded"
msgstr "預設 + 圓角"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Define a custom gradient"
msgstr "設計自訂漸變色"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Delete"
msgstr "刪除"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "Delete %s"
msgstr "刪除 %s"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/alt_dialog.xml:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Description"
msgstr "說明"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Devices"
msgstr "設備"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Direct Download"
msgstr "直接下載"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/media_dialog.xml:0
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/alt_dialog.xml:0
#: code:addons/web_editor/static/src/xml/add_snippet_dialog.xml:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Discard"
msgstr "捨棄"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.xml:0
msgid ""
"Discover a world of awesomeness in our copyright-free image haven. No legal "
"drama, just nice images!"
msgstr "在我們的無版權費圖片天堂，發現精彩世界！沒有法律糾紛，只有美麗動人的圖片！"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Display 1"
msgstr "展示 1"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Display 2"
msgstr "展示 2"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Display 3"
msgstr "展示 3"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Display 4"
msgstr "展示 4"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "Do you want to install %s App?"
msgstr "您要安裝 %s 模組嗎？"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/media_dialog.js:0
msgid "Documents"
msgstr "文件"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Dotted"
msgstr "點點線"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Double"
msgstr "雙實線"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Double-click to edit"
msgstr "點兩下來編輯"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "Drag and drop the building block."
msgstr "只須拖曳內容區塊，放置在頁面上。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "Duplicate Container"
msgstr "複製此部份"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Dynamic Colors"
msgstr "動態顏色"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid "Dynamic Placeholder"
msgstr "動態佔位符"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "EarlyBird"
msgstr "EarlyBird"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Edit image"
msgstr "編輯圖片"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Edit media description"
msgstr "編輯媒體說明"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Embed Image"
msgstr "插入圖片"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Embed Youtube Video"
msgstr "插入 YouTube 影片"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Embed the image in the document."
msgstr "將圖片嵌入至文件。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Embed the youtube video in the document."
msgstr "將 YouTube 影片嵌入至文件。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Emoji"
msgstr "表情符號"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Empty quote"
msgstr "空白引用"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.js:0
msgid "Error"
msgstr "錯誤"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Extend to the closest corner"
msgstr "延伸到最近的角落"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Extend to the closest side"
msgstr "延伸到最近的一側"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Extend to the farthest corner"
msgstr "延伸到最遠的角落"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Extend to the farthest side"
msgstr "延伸到最遠的一側"

#. module: web_editor
#: model:ir.model,name:web_editor.model_html_field_history_mixin
msgid "Field html History"
msgstr "欄位 HTML 歷史"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/upload_progress_toast/upload_progress_toast.xml:0
msgid "File has been uploaded"
msgstr "文件已上傳"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Fill"
msgstr "實色"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Fill + Rounded"
msgstr "填充 + 圓角"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Fill Color"
msgstr "填充顏色"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Filter"
msgstr "濾鏡"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
msgid "First Panel"
msgstr "第一面板"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Flat"
msgstr "平坦"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/image_crop.js:0
msgid "Flexible"
msgstr "靈活"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Flip"
msgstr "反轉"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Flip Horizontal"
msgstr "水平翻轉"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Flip Vertical"
msgstr "垂直翻轉"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Floating Shapes"
msgstr "浮動形狀"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid "Focus"
msgstr "焦點"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Font Color"
msgstr "文字顏色"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Font size"
msgstr "文字大小"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "For technical reasons, this block cannot be dropped here"
msgstr "由於技術原因，此方塊不可放置此處"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Format"
msgstr "格式"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Full screen"
msgstr "全螢幕"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/backend.xml:0
msgid "Fullscreen"
msgstr "全螢幕"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_prompt_dialog.xml:0
msgid "Generate Text with AI"
msgstr "使用 AI 人工智能生成文本"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Generate or transform content with AI"
msgstr "利用人工智能生成或轉換內容"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Generate or transform content with AI."
msgstr "利用人工智能生成或轉換內容。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_alternatives_dialog.xml:0
msgid "Generating"
msgstr "正在生成"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_alternatives_dialog.xml:0
msgid "Generating an alternative..."
msgstr "正在生成替換版本⋯"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Geometrics"
msgstr "幾何圖案"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Geometrics Panels"
msgstr "幾何面板"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Geometrics Rounded"
msgstr "幾何圖形圓角"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "Gradient"
msgstr "漸變"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP 路由"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Header 1"
msgstr "標題1"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Header 1 Display 1"
msgstr "標題 1 展示 1"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Header 1 Display 2"
msgstr "標題 1 展示 2"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Header 1 Display 3"
msgstr "標題 1 展示 3"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Header 1 Display 4"
msgstr "標題 1 展示 4"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Header 2"
msgstr "標題2"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Header 3"
msgstr "標題3"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Header 4"
msgstr "標題4"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Header 5"
msgstr "標題5"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Header 6"
msgstr "標題6"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Heading 1"
msgstr "標題 1"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Heading 2"
msgstr "標題 2"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Heading 3"
msgstr "標題 3"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Heading 4"
msgstr "標題 4"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Heading 5"
msgstr "標題 5"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Heading 6"
msgstr "標題 6"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Height"
msgstr "高"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.js:0
msgid "Hide Dailymotion logo"
msgstr "隱藏Dailymotion標誌"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.js:0
msgid "Hide fullscreen button"
msgstr "隱藏全螢幕按鈕"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.js:0
msgid "Hide player controls"
msgstr "隱藏播放器控件"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.js:0
msgid "Hide sharing button"
msgstr "隱藏分享按鈕"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_html_field_history_mixin__html_field_history
msgid "History data"
msgstr "歷史數據"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_html_field_history_mixin__html_field_history_metadata
msgid "History metadata"
msgstr "歷史元數據"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Horizontal mirror"
msgstr "水平鏡像"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid "Html"
msgstr "Html"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__id
msgid "ID"
msgstr "識別號"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "Icon"
msgstr "圖示"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "Icon Formatting"
msgstr "圖示格式"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Icon size 1x"
msgstr "圖示大小 1x"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Icon size 2x"
msgstr "圖示大小 2x"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Icon size 3x"
msgstr "圖示大小 3x"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Icon size 4x"
msgstr "圖示大小 4x"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Icon size 5x"
msgstr "圖示大小 5x"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/media_dialog.js:0
msgid "Icons"
msgstr "圖示"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid ""
"If you discard the current edits, all unsaved changes will be lost. You can "
"cancel to return to edit mode."
msgstr "若你放棄目前的編輯，所有未儲存的修改將會消失。你可以按「取消」返回編輯模式。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.xml:0
msgid "Illustrations"
msgstr "插圖"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Image"
msgstr "圖片"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "Image Formatting"
msgstr "圖片格式"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Image padding"
msgstr "圖片填充"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/media_dialog.js:0
msgid "Images"
msgstr "圖片"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Inkwell"
msgstr "Inkwell"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "Inline Text"
msgstr "內文文字"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_alternatives_dialog.xml:0
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_prompt_dialog.xml:0
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_translate_dialog.xml:0
msgid "Insert"
msgstr "插入"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Insert a Link / Button"
msgstr "插入連結/按鈕"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/add_snippet_dialog.xml:0
msgid "Insert a block"
msgstr "插入區塊"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Insert a danger banner"
msgstr "插入危險橫額"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid "Insert a field"
msgstr "插入欄位"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Insert a horizontal rule separator"
msgstr "插入水平分隔線。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Insert a picture"
msgstr "插入圖片"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Insert a rating over 3 stars"
msgstr "滿分為 3 星的評分工具。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Insert a rating over 5 stars"
msgstr "滿分為 5 星的評分工具。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Insert a success banner"
msgstr "插入成功橫額"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Insert a table"
msgstr "插入表格。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Insert a video"
msgstr "插入影片。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Insert a warning banner"
msgstr "插入警告橫額"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Insert above"
msgstr "向上插入"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Insert an info banner"
msgstr "插入資訊橫額"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Insert below"
msgstr "向下插入"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Insert left"
msgstr "向左插入"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Insert media"
msgstr "插入媒體"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Insert or edit link"
msgstr "插入或編輯連結"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Insert right"
msgstr "向右插入"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Insert your signature"
msgstr "加入你的電子簽名。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "Install"
msgstr "安裝"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/add_snippet_dialog.js:0
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "Install %s"
msgstr "安裝 %s"

#. module: web_editor
#. odoo-python
#: code:addons/web_editor/models/ir_ui_view.py:0
msgid "Invalid field value for %(field_name)s: %(value)s"
msgstr "欄位值無效。%(field_name)s：%(value)s"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "Invisible Elements"
msgstr "隱藏項目"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
msgid "Item"
msgstr "項目"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Label"
msgstr "標籤"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Large"
msgstr "大"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__write_uid
msgid "Last Updated by"
msgstr "最後更新者"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__write_date
msgid "Last Updated on"
msgstr "最後更新於"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Layout"
msgstr "格式"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Left"
msgstr "左"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Light"
msgstr "Light"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Linear"
msgstr "線性"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Link"
msgstr "連結"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Link Label"
msgstr "連接標籤"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Link Shape"
msgstr "連結形狀"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Link Size"
msgstr "連結大小"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Link Style"
msgstr "連結樣式"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link_popover_widget.js:0
msgid "Link copied to clipboard."
msgstr "連結已復製到剪貼板。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Link to an uploaded document"
msgstr "連結至已上載的文件"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "List"
msgstr "清單"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.xml:0
msgid "Load more..."
msgstr "載入更多⋯"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.xml:0
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_alternatives_dialog.xml:0
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_prompt_dialog.xml:0
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_translate_dialog.xml:0
#: code:addons/web_editor/static/src/xml/add_snippet_dialog.xml:0
msgid "Loading..."
msgstr "載入中⋯"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.js:0
msgid "Loop"
msgstr "循環"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Main Color"
msgstr "主要顏色"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid "Marketing Tools"
msgstr "推廣工具"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Maven"
msgstr "Maven"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid "Max height"
msgstr "最大高度"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Media"
msgstr "媒體"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Medium"
msgstr "媒體"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Medium section heading"
msgstr "中等字號章節標題。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid "Min height"
msgstr "最低高度"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "More info about this app."
msgstr "有關此模組的更多資訊。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Move down"
msgstr "向下移動"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Move left"
msgstr "向左移動"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Move right"
msgstr "向右移動"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Move up"
msgstr "向上移動"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.xml:0
msgid "My Images"
msgstr "我的圖片"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/add_snippet_dialog.xml:0
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__name
msgid "Name"
msgstr "名稱"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Navigation"
msgstr "資訊存取"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "No"
msgstr "否"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link_popover_widget.js:0
msgid "No URL specified"
msgstr "未設定網址"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/document_selector.xml:0
msgid "No documents found."
msgstr "未找到任何文檔。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.xml:0
msgid "No images found."
msgstr "找不到圖片。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
msgid "No more records"
msgstr "沒有更多記錄"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/icon_selector.xml:0
msgid "No pictograms found."
msgstr "找不到象形圖示。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid "No videos"
msgstr "沒有影片"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "None"
msgstr "無"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Normal"
msgstr "正常"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Numbered list"
msgstr "編號清單"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.tests
msgid "Odoo Editor Tests"
msgstr "Odoo 編輯工具測試"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Open in New Window"
msgstr "在新視窗開啟"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Open in new window"
msgstr "在新頁面中打開"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.xml:0
msgid "Optimized"
msgstr "優化"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Origins"
msgstr "基本幾何圖形"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Outline"
msgstr "只有外框"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Outline + Rounded"
msgstr "輪廓+圓角"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
msgid "Overlay"
msgstr "覆蓋"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Padding"
msgstr "內邊距"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "Page Options"
msgstr "頁面選項"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Panels"
msgstr "面板"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Paragraph block"
msgstr "一般文字段落。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Paste as URL"
msgstr "貼上為網址"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Patterns"
msgstr "型式"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Position"
msgstr "位置"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "Preset #{number}"
msgstr "預設設定 #{number}"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Preview"
msgstr "預覽"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/upload_progress_toast/upload_progress_toast.xml:0
msgid "Progress bar"
msgstr "進度列"

#. module: web_editor
#: model:ir.model.fields.selection,name:web_editor.selection__web_editor_converter_test__selection_str__c
msgid "Qu'est-ce qu'il fout ce maudit pancake, tabernacle ?"
msgstr "Qu'est-ce qu'il fout ce maudit pancake, tabernacle ?"

#. module: web_editor
#: model:ir.model.fields.selection,name:web_editor.selection__web_editor_converter_test__selection_str__a
msgid "Qu'il n'est pas arrivé à Toronto"
msgstr "Qu'il n'est pas arrivé à Toronto"

#. module: web_editor
#: model:ir.model.fields.selection,name:web_editor.selection__web_editor_converter_test__selection_str__b
msgid "Qu'il était supposé arriver à Toronto"
msgstr "Qu'il était supposé arriver à Toronto"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Quality"
msgstr "品質"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Quote"
msgstr "引用"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb
msgid "Qweb"
msgstr "Qweb"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field
msgid "Qweb Field"
msgstr "Qweb 欄位"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_contact
msgid "Qweb Field Contact"
msgstr "Qweb 聯繫人欄位"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_date
msgid "Qweb Field Date"
msgstr "Qweb 日期欄位"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_datetime
msgid "Qweb Field Datetime"
msgstr "Qweb 日期時間欄位"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_duration
msgid "Qweb Field Duration"
msgstr "Qweb 期間欄位"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_float
msgid "Qweb Field Float"
msgstr "Qweb 浮點型欄位"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_html
msgid "Qweb Field HTML"
msgstr "Qweb HTML欄位"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_image
msgid "Qweb Field Image"
msgstr "Qweb 圖像欄位"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_integer
msgid "Qweb Field Integer"
msgstr "Qweb 整數欄位"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_many2one
msgid "Qweb Field Many to One"
msgstr "Qweb 多對一欄位"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_monetary
msgid "Qweb Field Monetary"
msgstr "Qweb 貨幣欄位"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_relative
msgid "Qweb Field Relative"
msgstr "Qweb 相關欄位"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_selection
msgid "Qweb Field Selection"
msgstr "Qweb 選擇欄位"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_text
msgid "Qweb Field Text"
msgstr "Qweb 文字欄位"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_qweb
msgid "Qweb Field qweb"
msgstr "Qweb qweb欄位"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "REPLACE BY NEW VERSION"
msgstr "以新版本取代"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Radial"
msgstr "放射性"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Readonly field"
msgstr "唯讀欄位"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Redirect the user elsewhere when he clicks on the media."
msgstr "用戶點按媒體時，重新導向至其他地方。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Remove (DELETE)"
msgstr "刪除（刪除）"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "Remove Block"
msgstr "移除區塊"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Remove Current"
msgstr "刪除所選"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Remove Selected Color"
msgstr "刪除所選顏色"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Remove columns"
msgstr "移除分欄"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "Remove format"
msgstr "刪除格式"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Remove link"
msgstr "移除連結"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "Rename %s"
msgstr "重命名%s"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/add_snippet_dialog.xml:0
msgid "Rename the block"
msgstr "重新命名區塊"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Repeat pattern"
msgstr "重複圖案"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Replace"
msgstr "更換"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Replace media"
msgstr "更換媒體"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Reset"
msgstr "重設"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Reset Image"
msgstr "重設圖片"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Reset Size"
msgstr "重設大小"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Reset crop"
msgstr "重置裁剪"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Reset transformation"
msgstr "重置轉換"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid "Resizable"
msgstr "可調整大小"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Resize Default"
msgstr "調整至預設大小"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Resize Full"
msgstr "調整大小為全螢幕"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Resize Half"
msgstr "調整大小為一半"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Resize Quarter"
msgstr "調整大小為四分之一"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Right"
msgstr "右"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Rotate Left"
msgstr "向左旋轉"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Rotate Right"
msgstr "向右旋轉"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Rotate left"
msgstr "向左旋轉"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Rotate right"
msgstr "向右旋轉"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Row"
msgstr "橫行"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid "Sandboxed preview"
msgstr "沙盒預覽"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Saturation"
msgstr "飽和度"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/alt_dialog.xml:0
#: code:addons/web_editor/static/src/xml/add_snippet_dialog.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "Save"
msgstr "儲存"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "Save and Install"
msgstr "儲存並安裝"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
msgid "Save and Reload"
msgstr "儲存並重新載入"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/document_selector.js:0
msgid "Search a document"
msgstr "搜尋文件"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/icon_selector.xml:0
msgid "Search a pictogram"
msgstr "搜尋圖示"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.js:0
msgid "Search an image"
msgstr "搜尋圖片"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/add_snippet_dialog.xml:0
msgid "Search for a block"
msgstr "搜尋區塊"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "Search for a block (e.g. numbers, image wall, ...)"
msgstr "搜尋內容方塊（例：數字、圖片牆⋯）"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
msgid "Search for records..."
msgstr "搜尋記錄"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
msgid "Search more..."
msgstr "搜尋更多⋯"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
msgid "Search to show more records"
msgstr "搜尋以顯示更多記錄"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "Select a block on your page to style it."
msgstr "在頁面選取區塊，以設定樣式。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/media_dialog.xml:0
msgid "Select a media"
msgstr "選取媒體"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_prompt_dialog.xml:0
msgid "Send a message"
msgstr "發送訊息"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
msgid "Separator"
msgstr "分隔線"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Sepia"
msgstr "泛黃"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Shadow"
msgstr "陰影"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Shape"
msgstr "圖形"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Shape: Circle"
msgstr "形狀：Circle"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Shape: Rounded"
msgstr "形狀：圓角"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Shape: Thumbnail"
msgstr "形狀：Thumbnail"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Shapes"
msgstr "圖形"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.xml:0
msgid "Show optimized images"
msgstr "顯示最佳化圖片"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Show/Hide on Mobile"
msgstr "在流動裝置上顯示/隱藏"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Signature"
msgstr "簽名"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Size"
msgstr "大小"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Size 1x"
msgstr "尺寸 1x"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Size 2x"
msgstr "尺寸 2x"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Size 3x"
msgstr "尺寸 3x"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Size 4x"
msgstr "尺寸 4x"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Size 5x"
msgstr "尺寸 5x"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Small"
msgstr "小"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Small section heading"
msgstr "細字章節標題。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/add_snippet_dialog.xml:0
msgid "Snippet name"
msgstr "小資料名稱"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid "Snippets"
msgstr "小片段"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Solid"
msgstr "實色"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Solids"
msgstr "立體"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid ""
"Someone with escalated rights previously modified this area, you are "
"therefore not able to modify it yourself."
msgstr "擁有更高權限的人之前曾修改過此區域，因此你無法自行修改。"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Specials"
msgstr "特殊動作"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid ""
"Specify when the collaboration starts. 'Focus' will start the collaboration "
"session when the user clicks inside the text field (default), 'Start' when "
"the record is loaded (could impact performance if set)."
msgstr ""
"指定何時開始協作。【焦點】（預設）：使用者在文字欄位內點按時，會啟動協作時段。【開始】：完成載入記錄時啟動。（選用此選項可能會影響系統效能）"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Speed"
msgstr "速度"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid "Start"
msgstr "開始"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Stretch"
msgstr "伸展"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Structure"
msgstr "頁面結構"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Style"
msgstr "樣式"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
msgid "Suggestions"
msgstr "建議"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Switch direction"
msgstr "變換方向"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Switch the text's direction"
msgstr "切換文稿的行文方向。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Table"
msgstr "表格"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Table Options"
msgstr "表格選項"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "Text"
msgstr "文字"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Text Color"
msgstr "文字顏色"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Text align"
msgstr "文字對齊"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Text style"
msgstr "文字樣式"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.xml:0
msgid "The URL does not seem to work."
msgstr "網址看似無效。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.xml:0
msgid "The URL seems valid."
msgstr "網址看似有效。"

#. module: web_editor
#. odoo-python
#: code:addons/web_editor/models/ir_qweb_fields.py:0
msgid "The datetime %(value)s does not match the format %(format)s"
msgstr "日期時間值 %(value)s 不符合 %(format)s 格式"

#. module: web_editor
#. odoo-python
#: code:addons/web_editor/tools.py:0
msgid ""
"The document was already saved from someone with a different history for "
"model \"%(model)s\", field \"%(field)s\" with id \"%(id)d\"."
msgstr "文件已由其他有不同歷史記錄的人儲存。歷史記錄相異項目：模型 %(model)s、欄位 %(field)s 連識別碼 %(id)d。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.js:0
msgid "The provided url does not reference any supported video"
msgstr "提供的網址不存在任何支援的影片"

#. module: web_editor
#. odoo-python
#: code:addons/web_editor/tools.py:0
msgid "The provided url is invalid"
msgstr "提供的網址無效"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.js:0
msgid "The provided url is not valid"
msgstr "提供的網址無效"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/conflict_dialog.xml:0
msgid ""
"The version from the database will be used.\n"
"                            If you need to keep your changes, copy the content below and edit the new document."
msgstr ""
"將使用資料庫內的版本。\n"
"                            如需要保留變更，請複製以下內容，然後在新的文件中編輯。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "Theme"
msgstr "設計主題"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg_colorpicker.xml:0
msgid "Theme colors"
msgstr "主題色彩組合"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/conflict_dialog.xml:0
msgid "There is a conflict between your version and the one in the database."
msgstr "你的版本與資料庫中的版本存有衝突。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_prompt_dialog.xml:0
msgid "Thinking..."
msgstr "思考中⋯"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link_popover_widget.js:0
msgid "This URL is invalid. Preview couldn't be updated."
msgstr "此網址無效。無法更新預覽。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "This block cannot be dropped anywhere on this page."
msgstr "此區塊不能拖曳至此頁面任何位置。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "This block is outdated."
msgstr "此方塊已過時。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "This document is not saved!"
msgstr "本文件沒有被儲存！"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.js:0
msgid "This file is a public view attachment."
msgstr "此檔是公共視圖附件。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.js:0
msgid "This file is attached to the current record."
msgstr "此檔附加到目前記錄。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/image_crop.js:0
msgid "This image is an external image"
msgstr "這個圖像是一個外部圖像"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/image_crop.js:0
msgid ""
"This type of image is not supported for cropping.<br/>If you want to crop "
"it, please first download it from the original source and upload it in Odoo."
msgstr "不支援裁剪這種類型的圖片。<br/>如果您想裁剪它，請先從原始來源下載並在Odoo中上傳。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "Tip: Esc to preview"
msgstr "提示：按 Esc 預覽"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "Title"
msgstr "稱謂"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Title tag"
msgstr "標題標籤"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
msgid ""
"To save a snippet, we need to save all your previous modifications and "
"reload the page."
msgstr "要儲存代碼段，我們需要儲存您之前的所有修改並重新加載頁面。"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Toaster"
msgstr "Toaster"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Toggle bold"
msgstr "粗體"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Toggle checklist"
msgstr "剔選清單"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Toggle icon spin"
msgstr "切換圖示旋轉"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Toggle italic"
msgstr "斜體"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Toggle ordered list"
msgstr "編號清單"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Toggle strikethrough"
msgstr "刪除線"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Toggle underline"
msgstr "底線"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Toggle unordered list"
msgstr "點列清單"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/alt_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Tooltip"
msgstr "工具提示"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Track tasks with a checklist"
msgstr "以剔選方式追蹤任務完成情況。"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Transform"
msgstr "轉換"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Transform the picture"
msgstr "變換圖片"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Transform the picture (click twice to reset transformation)"
msgstr "轉換圖片（雙擊可重設）"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/backend.xml:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Translate"
msgstr "翻譯"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_translate_dialog.xml:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "Translate with AI"
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_translate_dialog.xml:0
msgid "Translating..."
msgstr ""

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg_colorpicker.xml:0
msgid "Transparent colors"
msgstr "透明顏色"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/icon_selector.xml:0
msgid "Try searching with other keywords."
msgstr "請嘗試使用其他關鍵字搜尋。"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Type"
msgstr "類型"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
msgid "Type \"/\" for commands"
msgstr "鍵入斜線 \"/\" 查看格式選項"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "URL or Email"
msgstr "網址或電郵"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Unalign"
msgstr "取消對齊"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/document_selector.js:0
msgid "Upload a document"
msgstr "上傳文件"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.js:0
msgid "Upload an image"
msgstr "上載圖片"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.js:0
msgid "Uploaded image's format is not supported. Try with: "
msgstr "圖片格式未支援。請使用 "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Valencia"
msgstr "華倫西亞"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Vertical mirror"
msgstr "垂直鏡像"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Video"
msgstr "影片"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "Video Formatting"
msgstr "影片格式"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
msgid "Video code"
msgstr "影片代碼"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/media_dialog.js:0
msgid "Videos"
msgstr "影片"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.js:0
msgid "Videos are muted when autoplay is enabled"
msgstr "啟用自動播放時，影片處於靜音狀態"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_ui_view
msgid "View"
msgstr "檢視"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
msgid "Vimeo"
msgstr "Vimeo"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Walden"
msgstr "Walden"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/conflict_dialog.xml:0
msgid ""
"Warning: after closing this dialog, the version you were working on will be "
"discarded and will never be available anymore."
msgstr "警告：關閉此對話框後，你正在編輯的版本將會丟棄，永遠無法再用。"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Wavy"
msgstr "波浪"

#. module: web_editor
#: model:ir.model,name:web_editor.model_web_editor_converter_test_sub
msgid "Web Editor Converter Subtest"
msgstr "Web編輯器轉換器子測試"

#. module: web_editor
#: model:ir.model,name:web_editor.model_web_editor_converter_test
msgid "Web Editor Converter Test"
msgstr "Web編輯器轉換器測試"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Widgets"
msgstr "小工具"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Width"
msgstr "寬"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/backend/html_field.js:0
msgid ""
"With the option enabled, all content can only be viewed in a sandboxed "
"iframe or in the code editor."
msgstr "若啟用該選項，所有內容只可在已作沙盒處理的 iframe，或在程式碼編輯工具中檢視。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.xml:0
msgid ""
"Wow, it feels a bit empty in here. Upload from the button in the top right "
"corner!"
msgstr "咦？這裏感覺有點空。按右上角的按鈕上載吧！"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
msgid "Write something..."
msgstr "寫些東西⋯"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "XL"
msgstr "XL"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Xpro"
msgstr "Xpro"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
msgid "Yes"
msgstr "是"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.js:0
msgid ""
"You can not replace a field by this image. If you want to use this image, "
"first save it on your computer and then upload it here."
msgstr "你不可使用此圖片取代欄位。如果想使用此圖片，請先將它儲存至你的電腦上，然後上載至此處。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/image_selector.xml:0
msgid "You can not use this image in a field"
msgstr "你不可在欄位中使用此圖片"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "You can still access the block options but it might be ineffective."
msgstr "你仍可存取方塊選項,但可能無效。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/document_selector.xml:0
msgid ""
"You can upload documents with the button located in the top left of the "
"screen."
msgstr "您可以使用螢幕左上角的按鈕上傳文件。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/snippets.xml:0
msgid "You might not be able to customize it anymore."
msgstr "你可能再也無法自訂它。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
msgid "Youku"
msgstr "Youku"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Your URL"
msgstr "輸入網址"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
msgid "Your content was successfully generated."
msgstr "成功生成你的內容。"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
msgid "Youtube"
msgstr "Youtube"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Zoom In"
msgstr "放大"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "Zoom Out"
msgstr "縮小"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "add"
msgstr "相加"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.xml:0
msgid "all"
msgstr "所有"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/chatgpt_alternatives_dialog.xml:0
msgid "alternatives..."
msgstr "替代選擇⋯"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
msgid "and"
msgstr "及"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "auto"
msgstr "自動"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "darken"
msgstr "變暗"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.xml:0
msgid "database"
msgstr "資料庫"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "exclusion"
msgstr "排除"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "fill"
msgstr "fill"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "fill,rounded-circle"
msgstr "fill,rounded-circle"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "flat"
msgstr "平坦"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "lg"
msgstr "lg"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "lighten"
msgstr "變亮"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/file_selector.xml:0
msgid "media-library"
msgstr "media-library"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "multiply"
msgstr "相乘"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "outline"
msgstr "outline"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "outline,rounded-circle"
msgstr "outline,rounded-circle"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "overlay"
msgstr "覆蓋"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "px"
msgstr "px"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "rounded-circle"
msgstr "rounded-circle"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "screen"
msgstr "螢幕"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
msgid "sm"
msgstr "sm"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/xml/editor.xml:0
msgid "to"
msgstr "到"

#. module: web_editor
#. odoo-javascript
#: code:addons/web_editor/static/src/components/media_dialog/video_selector.xml:0
msgid "videos"
msgstr "影片"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_websocket
msgid "websocket message handling"
msgstr "WebSocket 訊息處理"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "www.example.com"
msgstr "www.example.com"
