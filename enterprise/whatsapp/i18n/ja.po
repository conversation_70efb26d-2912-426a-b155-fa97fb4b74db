# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* whatsapp
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:24+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_account.py:0
msgid "%(create_count)s were created, %(update_count)s were updated"
msgstr "%(create_count)sが作成され、 %(update_count)sが更新されました "

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "%(original_name)s (copy)"
msgstr "%(original_name)s (コピー)"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "%(template_name)s [%(account_name)s]"
msgstr "%(template_name)s [%(account_name)s]"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "'%(field)s' does not seem to be a valid field path on %(model)s"
msgstr "'%(field)s' は、%(model)s上で有効なフィールドパスではないようです。"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/wizard/whatsapp_composer.py:0
msgid ", ... (%s Others)"
msgstr ", ... (%s他)"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_kanban
msgid ""
"<i class=\"fa fa-whatsapp me-1\" title=\"Messages Count\" aria-"
"label=\"Messages Count\"/>"
msgstr ""
"<i class=\"fa fa-whatsapp me-1\" title=\"Messages Count\" aria-"
"label=\"Messages Count\"/>"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.template_message_preview
msgid ""
"<span class=\"o-whatsapp-font-11\">{{Location name}}</span><br/>\n"
"                            <span class=\"text-600 o-whatsapp-font-9\">{{Address}}</span>"
msgstr ""
"<span class=\"o-whatsapp-font-11\">{{Location name}}</span><br/>\n"
"                            <span class=\"text-600 o-whatsapp-font-9\">{{Address}}</span>"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.view_partner_form
msgid "<span class=\"o_stat_text\">Chats</span>"
msgstr "<span class=\"o_stat_text\">チャット</span>"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.template_message_preview
msgid ""
"<span class=\"position-absolute bottom-0 end-0 o-whatsapp-font-11 py-1 px-2 text-black-50\" area-hidden=\"true\">\n"
"                        06:00\n"
"                    </span>"
msgstr ""
"<span class=\"position-absolute bottom-0 end-0 o-whatsapp-font-11 py-1 px-2 text-black-50\" area-hidden=\"true\">\n"
"                        06:00\n"
"                    </span>"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_composer_view_form
msgid ""
"<strong>Invalid number: </strong>\n"
"                            <span>make sure to set a country on the Contact or to specify the country code.</span>"
msgstr ""
"<strong>無効な番号: </strong>\n"
"                            <span>連絡先で国を設定するか、国コードを指定して下さい。</span>"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/discuss_channel.py:0
msgid "A new WhatsApp channel is created for this document"
msgstr "このドキュメント用に新規 WhatsAppチャネルが作成されました。"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_message.py:0
msgid ""
"A new template was sent on %(record_link)s.<br>Future replies will be "
"transferred to a new chat."
msgstr "新規テンプレートが %(record_link)sに送信されました。<br>今後の返信は新しいチャットに転送されます。"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/discuss_channel.py:0
msgid "A phone number is required for WhatsApp channels %(channel_names)s"
msgstr "WhatsAppチャネル%(channel_names)sに電話番号が必要です。"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__token
msgid "Access Token"
msgstr "アクセストークン"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "Accessible to all Users"
msgstr "全てのユーザがアクセス可"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__wa_account_id
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_search
msgid "Account"
msgstr "勘定科目"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__failure_type__account
msgid "Account Error"
msgstr "アカウントエラー"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__account_uid
msgid "Account ID"
msgstr "アカウントID"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__message_needaction
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__message_needaction
msgid "Action Needed"
msgstr "要アクション"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__active
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__active
msgid "Active"
msgstr "有効化"

#. module: whatsapp
#: model:res.groups,name:whatsapp.group_whatsapp_admin
msgid "Administrator"
msgstr "管理者"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__af
msgid "Afrikaans"
msgstr "アフリカーンス語"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__sq
msgid "Albanian"
msgstr "アルバニア語"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_button.py:0
msgid "All dynamic urls must have a placeholder."
msgstr "全ての動的URLにはプレースホルダーが必要です。"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "Allow Multi"
msgstr "マルチを許可する"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__allowed_company_ids
msgid "Allowed Company"
msgstr "許可された会社"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "Allowed companies"
msgstr "許可された会社"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__app_uid
msgid "App ID"
msgstr "App ID"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__app_secret
msgid "App Secret"
msgstr "App Secret"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__model_id
msgid "Applies to"
msgstr "適用"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__status__approved
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_search
msgid "Approved"
msgstr "承認済"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ar
msgid "Arabic"
msgstr "アラビア語"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_search
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_search
msgid "Archived"
msgstr "アーカイブ済"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__attachment_id
msgid "Attachment"
msgstr "添付ファイル"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__message_attachment_count
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__message_attachment_count
msgid "Attachment Count"
msgstr "添付数"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_message.py:0
msgid "Attachment mimetype is not supported by WhatsApp: %s."
msgstr "WhatsAppは添付ファイルのMIMEタイプをサポートしていません:%s"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__template_type__authentication
msgid "Authentication"
msgstr "認証"

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_whatsapp_template__template_type
msgid ""
"Authentication - One-time passwords that your customers use to authenticate a transaction or login.\n"
"Marketing - Promotions or information about your business, products or services. Or any message that isn't utility or authentication.\n"
"Utility - Messages about a specific transaction, account, order or customer request."
msgstr ""
"認証 - 顧客が取引やログインを認証するために使用するワンタイムパスワード。\n"
"マーケティング - あなたのビジネス、プロダクト、サービスに関するプロモーションや情報。または、実用性や認証以外のメッセージ。\n"
"ユーティリティ - 特定の取引、アカウント、オーダ、顧客のリクエストに関するメッセージ。"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__az
msgid "Azerbaijani"
msgstr "アゼルバイジャン語"

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_base
msgid "Base"
msgstr "ベース"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__bn
msgid "Bengali"
msgstr "ベンガル語"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__failure_type__blacklisted
msgid "Blacklisted Phone Number"
msgstr "ブラックリスト電話番号"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__body
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_variable__line_type__body
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_search
msgid "Body"
msgstr "表示文"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "Body variables should start at 1 and not skip any number, missing %d"
msgstr "ボディ変数は1から始まり、どの番号もスキップしてはいけません、不明:%d"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__state__bounced
msgid "Bounced"
msgstr "不達"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__bg
msgid "Bulgarian"
msgstr "ブルガリア語"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__button_id
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_variable__line_type__button
msgid "Button"
msgstr "ボタン"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__name
msgid "Button Text"
msgstr "ボタンテキスト"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__button_dynamic_url_1
msgid "Button Url 1"
msgstr "ボタン URL 1"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__button_dynamic_url_2
msgid "Button Url 2"
msgstr "ボタン URL 2"

#. module: whatsapp
#: model:ir.model.constraint,message:whatsapp.constraint_whatsapp_template_button_unique_name_per_template
msgid "Button names must be unique in a given template"
msgstr "ボタン名はテンプレート内で一意にして下さい。"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_variable.py:0
msgid "Button variables must be linked to a button."
msgstr "ボタン変数はボタンにリンクされている必要があります。"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__button_ids
msgid "Buttons"
msgstr "ボタン"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_button.py:0
msgid "Buttons may only contain one placeholder."
msgstr "ボタンはプレースホルダーを1つだけ含むことができます。"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__call_number
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_button__button_type__phone_number
msgid "Call Number"
msgstr "番号に電話する"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__callback_url
msgid "Callback URL"
msgstr "コールバックURL"

#. module: whatsapp
#. odoo-javascript
#: code:addons/whatsapp/static/src/core/common/composer_patch.js:0
msgid ""
"Can't send message as it has been 24 hours since the last message of the "
"User."
msgstr "そのユーザの最後のメッセージから24時間が経過しているため、メッセージを送信できません。"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_form
msgid "Cancel"
msgstr "キャンセル"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_tree
msgid "Cancel WhatsApp"
msgstr "WhatsAppをキャンセル"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__state__cancel
msgid "Cancelled"
msgstr "取消済"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ca
msgid "Catalan"
msgstr "カタロニア語"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__template_type
msgid "Category"
msgstr "カテゴリー"

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_discuss_channel_member
msgid "Channel Member"
msgstr "チャネルメンバ"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_discuss_channel__channel_type
msgid "Channel Type"
msgstr "チャネルタイプ"

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_discuss_channel__channel_type
msgid ""
"Chat is private and unique between 2 persons. Group is private among invited"
" persons. Channel can be freely joined (depending on its configuration)."
msgstr ""
"チャットは2人だけのプライベートなものです。グループは招待された人だけのプライベートなものです。チャネルは (設定により)自由に参加できます。"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__zh_cn
msgid "Chinese (CHN)"
msgstr "中国語 (CHN)"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__zh_hk
msgid "Chinese (HKG)"
msgstr "中国語 (HKG)"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__zh_tw
msgid "Chinese (TAI)"
msgstr "中国語 (TAI)"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.ir_actions_server_view_form_whatsapp
msgid "Choose a template..."
msgstr "テンプレートを選択..."

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_composer_view_form
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_preview_view_form
msgid "Close"
msgstr "閉じる"

#. module: whatsapp
#: model:ir.ui.menu,name:whatsapp.whatsapp_configuration_menu
msgid "Configuration"
msgstr "設定"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.res_config_settings_view_form
msgid "Configure Meta Accounts"
msgstr "メタアカウント設定"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/wizard/whatsapp_composer.py:0
msgid "Configure Templates"
msgstr "テンプレート設定"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/tools/whatsapp_api.py:0
msgid "Configure Whatsapp Business Account"
msgstr "Whatsappビジネスアカウントを設定"

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_res_partner
msgid "Contact"
msgstr "連絡先"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
msgid "Create Date"
msgstr "作成日"

#. module: whatsapp
#: model_terms:ir.actions.act_window,help:whatsapp.whatsapp_account_action
msgid "Create an Account on the"
msgstr "以下にアカウントを作成"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.discuss_channel_view_list_whatsapp
msgid "Created On"
msgstr "作成日"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__create_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__create_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__create_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_preview__create_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__create_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__create_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__create_uid
msgid "Created by"
msgstr "作成者"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__create_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__create_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__create_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_preview__create_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__create_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__create_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__create_date
msgid "Created on"
msgstr "作成日"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_account.py:0
msgid "Credentials look good!"
msgstr "認証情報は問題ありません!"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__hr
msgid "Croatian"
msgstr "クロアチア語"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__cs
msgid "Czech"
msgstr "チェコ語"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__da
msgid "Danish"
msgstr "デンマーク語"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "Default Users"
msgstr "デフォルトユーザ"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__status__deleted
msgid "Deleted"
msgstr "削除済"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__state__delivered
msgid "Delivered"
msgstr "配送済"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
msgid "Delivered Messages"
msgstr "配信済メッセージ"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__status__disabled
msgid "Disabled"
msgstr "無効にします"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "Disallow Multi"
msgstr "マルチを不許可"

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_discuss_channel
msgid "Discussion Channel"
msgstr "ディスカッションチャンネル"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__display_name
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__display_name
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__display_name
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_preview__display_name
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__display_name
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__display_name
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__display_name
msgid "Display Name"
msgstr "表示名"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__header_type__document
msgid "Document"
msgstr "ドキュメント"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__res_ids
msgid "Document IDs"
msgstr "ドキュメントID"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__res_model
msgid "Document Model Name"
msgstr "ドキュメントモデル名"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/tools/whatsapp_api.py:0
msgid "Document upload failed, please retry after sometime."
msgstr "ドキュメントのアップロードに失敗しました。もう少し経ってから再試行して下さい。"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/tools/whatsapp_api.py:0
msgid "Document upload session open failed, please retry after sometime."
msgstr "ドキュメントアップロードセッションオープンに失敗しました。もう少し経ってから再試行して下さい。"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__status__draft
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_search
msgid "Draft"
msgstr "ドラフト"

#. module: whatsapp
#: model:ir.model.constraint,message:whatsapp.constraint_whatsapp_template_unique_name_account_template
msgid "Duplicate template is not allowed for one Meta account."
msgstr "メタアカウントでは重複ドキュメントは許可されていません。"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__nl
msgid "Dutch"
msgstr "オランダ語"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_button__url_type__dynamic
msgid "Dynamic"
msgstr "動的"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_variable.py:0
msgid ""
"Dynamic button variable name must be the same as its respective button's "
"name"
msgstr "動的ボタンの変数名は、それぞれのボタン名と同じでなければなりません。"

#. module: whatsapp
#: model:ir.model.constraint,message:whatsapp.constraint_whatsapp_message_unique_msg_uid
msgid "Each whatsapp message should correspond to a single message uuid."
msgstr "各Whatsappメッセージは、単一のメッセージuuidに対応する必要があります"

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_mail_thread
msgid "Email Thread"
msgstr "Eメールスレッド"

#. module: whatsapp
#. odoo-javascript
#: code:addons/whatsapp/static/src/components/phone_field/phone_field.js:0
msgid "Enable WhatsApp"
msgstr "WhatsAppを有効化"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__en
msgid "English"
msgstr "英語"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__en_gb
msgid "English (UK)"
msgstr "英語 (UK)"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__en_us
msgid "English (US)"
msgstr "英語 (US)"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__error_msg
msgid "Error Message"
msgstr "エラーメッセージ"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__et
msgid "Estonian"
msgstr "エストニア語"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__state__error
msgid "Failed"
msgstr "不合格"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
msgid "Failed Messages"
msgstr "失敗メッセージ"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__failure_reason
msgid "Failure Reason"
msgstr "失敗理由"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__failure_type
msgid "Failure Type"
msgstr "故障タイプ"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__field_name
msgid "Field"
msgstr "フィールド"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_variable__field_type__field
msgid "Field of Model"
msgstr "モデルフィールド"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_variable.py:0
msgid ""
"Field template variables %(var_names)s must be associated with a field."
msgstr "フィールドテンプレート変数%(var_names)sはフィールドに関連付けられていなければなりません。"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "File type %(file_type)s not supported for header type %(header_type)s"
msgstr "ファイルタイプ %(file_type)sはヘッダータイプ%(header_type)s用にサポートされていません。"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__fil
msgid "Filipino"
msgstr "フィリピン語"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__fi
msgid "Finnish"
msgstr "フィンランド語"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__message_follower_ids
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__message_follower_ids
msgid "Followers"
msgstr "フォロワー"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__message_partner_ids
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__message_partner_ids
msgid "Followers (Partners)"
msgstr "フォロワー (取引先)"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__footer_text
msgid "Footer Message"
msgstr "フッターメッセージ"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_variable__field_type__free_text
msgid "Free Text"
msgstr "自由記入欄"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__free_text_1
msgid "Free Text 1"
msgstr "フリーテキスト1"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__free_text_10
msgid "Free Text 10"
msgstr "フリーテキスト10"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__free_text_2
msgid "Free Text 2"
msgstr "フリーテキスト2"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__free_text_3
msgid "Free Text 3"
msgstr "フリーテキスト3"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__free_text_4
msgid "Free Text 4"
msgstr "フリーテキスト4"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__free_text_5
msgid "Free Text 5"
msgstr "フリーテキスト5"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__free_text_6
msgid "Free Text 6"
msgstr "フリーテキスト6"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__free_text_7
msgid "Free Text 7"
msgstr "フリーテキスト7"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__free_text_8
msgid "Free Text 8"
msgstr "フリーテキスト8"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__free_text_9
msgid "Free Text 9"
msgstr "フリーテキスト9"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__free_text_json
msgid "Free Text Template Parameters"
msgstr "フリーテキストテンプレートパラメータ"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_variable.py:0
msgid "Free Text template variables must have a demo value."
msgstr "フリーテキストテンプレートの変数はデモ値にして下さい。"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "Free text variable in the header should be {{1}}"
msgstr "ヘッダーのフリーテキスト変数は {{1}}にして下さい。"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__fr
msgid "French"
msgstr "フレンチ"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ka
msgid "Georgian"
msgstr "グルジア語"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__de
msgid "German"
msgstr "ドイツ"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__el
msgid "Greek"
msgstr "ギリシャ語"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__quality__green
msgid "Green"
msgstr "緑"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_search
msgid "Group By"
msgstr "グループ化"

#. module: whatsapp
#: model:ir.model.constraint,message:whatsapp.constraint_discuss_channel_group_public_id_check
msgid ""
"Group authorization and group auto-subscription are only supported on "
"channels and whatsapp."
msgstr "グループ認証とグループ自動サブスクリプションは、チャネルとWhatsappでのみサポートされています。"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__gu
msgid "Gujarati"
msgstr "グジャラート語"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__has_action
msgid "Has Action"
msgstr "アクションあり"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__has_invalid_button_number
msgid "Has Invalid Button Number"
msgstr "無効なボタン番号あり"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__has_invalid_number
msgid "Has Invalid Number"
msgstr "無効な番号あり"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__has_message
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__has_message
msgid "Has Message"
msgstr "メッセージあり"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ha
msgid "Hausa"
msgstr "ハウサ語"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_variable__line_type__header
msgid "Header"
msgstr "ヘッダ"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__header_text_1
msgid "Header Free Text"
msgstr "ヘッダーフリーテキスト"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__header_type
msgid "Header Type"
msgstr "ヘッダータイプ"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "Header document is required"
msgstr "ヘッダードキュメントが必要です"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "Header document or report is required"
msgstr "ヘッダードキュメントまたレポートが必要です"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__he
msgid "Hebrew"
msgstr "ヘブライ語"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "Hello {{1}}, here is your order with the reference {{2}} ..."
msgstr "こんにちは {{1}}、お客様のオーダ、参照 {{2}} です..."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__hi
msgid "Hindi"
msgstr "ヒンディー語"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__hu
msgid "Hungarian"
msgstr "ハンガリー語"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__id
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__id
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__id
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_preview__id
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__id
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__id
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__id
msgid "ID"
msgstr "ID"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__failure_type__whatsapp_recoverable
msgid "Identified Error"
msgstr "特定されたエラー"

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_whatsapp_account__message_needaction
#: model:ir.model.fields,help:whatsapp.field_whatsapp_template__message_needaction
msgid "If checked, new messages require your attention."
msgstr "チェックした場合は、新しいメッセージに注意が必要です。"

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_whatsapp_account__message_has_error
#: model:ir.model.fields,help:whatsapp.field_whatsapp_account__message_has_sms_error
#: model:ir.model.fields,help:whatsapp.field_whatsapp_template__message_has_error
#: model:ir.model.fields,help:whatsapp.field_whatsapp_template__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "チェックした場合は、一部のメッセージに配信エラーが発生されました。"

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_res_users_settings__is_discuss_sidebar_category_whatsapp_open
msgid "If checked, the WhatsApp category is open in the discuss sidebar"
msgstr "チェックした場合、WhatsAppカテゴリがディスカスサイドバーで開きます。"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__header_type__image
msgid "Image"
msgstr "画像"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__status__in_appeal
msgid "In Appeal"
msgstr "要請中"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__state__outgoing
msgid "In Queue"
msgstr "キュー済"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__message_type__inbound
msgid "Inbound"
msgstr "インバウンド"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__id
msgid "Indonesian"
msgstr "インドネシア語"

#. module: whatsapp
#. odoo-javascript
#: code:addons/whatsapp/static/src/components/whatsapp_variables_text_field/whatsapp_variables_text_field.xml:0
msgid "Insert variable"
msgstr "変数を挿入する"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__invalid_phone_number_count
msgid "Invalid Phone Number Count"
msgstr "無効な電話番号数"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ga
msgid "Irish"
msgstr "アイルランド語"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__is_button_dynamic
msgid "Is Button Dynamic"
msgstr "ボタンダイナミック"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__message_is_follower
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__message_is_follower
msgid "Is Follower"
msgstr "フォロー中　"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__is_header_free_text
msgid "Is Header Free Text"
msgstr "ヘッダーはフリーテキストか"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__batch_mode
msgid "Is Multiple Records"
msgstr "複数レコード"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_discuss_channel__whatsapp_channel_active
msgid "Is Whatsapp Channel Active"
msgstr "Whatsappチャネル有効"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__it
msgid "Italian"
msgstr "イタリア語"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ja
msgid "Japanese"
msgstr "日本"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__kn
msgid "Kannada"
msgstr "カンナダ語"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__kk
msgid "Kazakh"
msgstr "カザフスタン語"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__rw_rw
msgid "Kinyarwanda"
msgstr "キニャルワンダ語"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ko
msgid "Korean"
msgstr "韓国語"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ky_kg
msgid "Kyrgyz (Kyrgyzstan)"
msgstr "キルギス語(キルギスタン)"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__lang_code
msgid "Language"
msgstr "言語"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__lo
msgid "Lao"
msgstr "ラオス語"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
msgid "Last Month"
msgstr "先月"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__write_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__write_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__write_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_preview__write_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__write_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__write_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__write_uid
msgid "Last Updated by"
msgstr "最終更新者"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__write_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__write_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__write_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_preview__write_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__write_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__write_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__write_date
msgid "Last Updated on"
msgstr "最終更新日"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_discuss_channel__last_wa_mail_message_id
msgid "Last WA Partner Mail Message"
msgstr "最新Whatsapp取引先メールメッセージ"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
msgid "Last Week"
msgstr "先週"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
msgid "Last Year"
msgstr "昨年"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__lv
msgid "Latvian"
msgstr "ラトビア語"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__status__limit_exceeded
msgid "Limit Exceeded"
msgstr "制限を超過しました"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__lt
msgid "Lithuanian"
msgstr "リトアニア語"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_account.py:0
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__header_type__location
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_variable__line_type__location
msgid "Location"
msgstr "ロケーション"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid ""
"Location Latitude and Longitude %(latitude)s / %(longitude)s is not in "
"proper format."
msgstr "位置の緯度と経度 %(latitude)s / %(longitude)sが適切な形式ではありません。 "

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_variable.py:0
msgid ""
"Location variable should be 'name', 'address', 'latitude' or 'longitude'. "
"Cannot parse '%(placeholder)s'"
msgstr ""
"ロケーション変数には 'name'、'address'、'latitude'、'longitude' "
"のいずれかを指定します。'%(placeholder)s'をパースできません。"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid ""
"Location variables should only exist when a \"location\" header is selected."
msgstr "ロケーション変数は、\"location \"ヘッダーが選択されたときのみ存在します。"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__mk
msgid "Macedonian"
msgstr "マケドニア語"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__mail_message_id
msgid "Mail Message"
msgstr "メールメッセージ"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ms
msgid "Malay"
msgstr "マレー語"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ml
msgid "Malayalam"
msgstr "マラヤーラム語"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__mr
msgid "Marathi"
msgstr "マラーティー語"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__template_type__marketing
msgid "Marketing"
msgstr "マーケティング"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "Maximum 1 Call Number button allowed."
msgstr "最大で1コールボタンが許可されています。"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "Maximum 10 buttons allowed."
msgstr "10ボタンが許可されています。"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "Maximum 2 URL buttons allowed."
msgstr "最大2 URLボタンが許可されています。"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.discuss_channel_view_list_whatsapp
msgid "Members"
msgstr "メンバー"

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_mail_message
msgid "Message"
msgstr "メッセージ"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__templates_count
msgid "Message Count"
msgstr "メッセージ数"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__message_has_error
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__message_has_error
msgid "Message Delivery error"
msgstr "メッセージ配信エラー"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__preview_whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_preview__preview_whatsapp
msgid "Message Preview"
msgstr "メッセージプレビュー"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "Message Statistics Of %(template_name)s"
msgstr "%(template_name)sのメッセージ分析"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__message_type
msgid "Message Type"
msgstr "メッセージタイプ"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__message_ids
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__message_ids
#: model:ir.ui.menu,name:whatsapp.whatsapp_message_menu
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "Messages"
msgstr "メッセージ"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__messages_count
msgid "Messages Count"
msgstr "メッセージ数"

#. module: whatsapp
#: model_terms:ir.actions.act_window,help:whatsapp.whatsapp_account_action
msgid "Meta for Developers"
msgstr "Meta for Developers"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__mobile_number_formatted
msgid "Mobile Number Formatted"
msgstr "携帯番号がフォーマット化されました"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_search
msgid "Model"
msgstr "モデル"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__model
msgid "Model Name"
msgstr "モデル名"

#. module: whatsapp
#: model_terms:ir.actions.act_window,help:whatsapp.whatsapp_message_action
msgid "Monitor all recent outgoing and incoming messages"
msgstr "最近の送受信メッセージを全て監視"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_search
msgid "My Templates"
msgstr "自分のテンプレート"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__name
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__name
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "Name"
msgstr "名前"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__failure_type__network
msgid "Network Error"
msgstr "ネットワークエラー"

#. module: whatsapp
#: model_terms:ir.actions.act_window,help:whatsapp.whatsapp_account_action
msgid "No Account Configured yet!"
msgstr "アカウントがまだ設定されていません!"

#. module: whatsapp
#: model_terms:ir.actions.act_window,help:whatsapp.whatsapp_template_action
msgid "No Templates Found!"
msgstr "テンプレートが見つかりません!"

#. module: whatsapp
#: model_terms:ir.actions.act_window,help:whatsapp.whatsapp_message_action
msgid "No WhatsApp Messages found"
msgstr "WhatsAppメッセージが見つかりません"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/wizard/whatsapp_composer.py:0
msgid "No approved WhatsApp Templates are available for this model."
msgstr "このモデルには承認されたWhatsAppテンプレートはありません。"

#. module: whatsapp
#. odoo-javascript
#: code:addons/whatsapp/static/src/core/web/channel_selector_patch.js:0
msgid "No results found"
msgstr "結果が見つかりません。"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/tools/whatsapp_api.py:0
msgid "Non-descript Error"
msgstr "非記載エラー"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__header_type__none
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__quality__none
msgid "None"
msgstr "なし"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__nb
msgid "Norwegian"
msgstr "ノルウェー語"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "Notifications"
msgstr "通知"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__notify_user_ids
msgid "Notify User"
msgstr "ユーザに通知"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__message_needaction_counter
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__message_needaction_counter
msgid "Number of Actions"
msgstr "アクション数"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__message_has_error_counter
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__message_has_error_counter
msgid "Number of errors"
msgstr "エラー数"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__number_of_free_text
msgid "Number of free text"
msgstr "フリーテキスト数"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__number_of_free_text_button
msgid "Number of free text Buttons"
msgstr "フリーテキストボタン数"

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_whatsapp_account__message_needaction_counter
#: model:ir.model.fields,help:whatsapp.field_whatsapp_template__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "アクションを必要とするメッセージの数"

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_whatsapp_account__message_has_error_counter
#: model:ir.model.fields,help:whatsapp.field_whatsapp_template__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "配信エラーが発生されたメッセージ数"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "Only 10 free text is allowed in body of template"
msgstr "テンプレート本文で許可されているのは10フリーテキストのみです"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_button.py:0
msgid "Only dynamic urls may have a placeholder."
msgstr "ダイナミックURLのみプレースホルダを所有できます"

#. module: whatsapp
#. odoo-javascript
#: code:addons/whatsapp/static/src/core/common/composer_patch.js:0
msgid "Only one attachment is allowed for each message"
msgstr "各メッセージにつき1添付のみ許可されています"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "Only templates using media header types may have header documents"
msgstr "メディアヘッダタイプを使用するテンプレートだけがヘッダ・ドキュメントを持つことができます"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__failure_type__whatsapp_unrecoverable
msgid "Other Technical Error"
msgstr "他の技術エラー"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__message_type__outbound
msgid "Outbound"
msgstr "アウトバウンド"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/res_partner.py:0
msgid "Partner created by incoming WhatsApp message."
msgstr "受信したWhatsAppメッセージによって作成された取引先"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__status__paused
msgid "Paused"
msgstr "停止済"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__status__pending
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_search
msgid "Pending"
msgstr "保留"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__status__pending_deletion
msgid "Pending Deletion"
msgstr "削除保留"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__fa
msgid "Persian"
msgstr "ペルシア語"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__phone
msgid "Phone"
msgstr "電話"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__phone_field
msgid "Phone Field"
msgstr "電話フィールド"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_discuss_channel__whatsapp_number
msgid "Phone Number"
msgstr "電話番号"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__phone_uid
msgid "Phone Number ID"
msgstr "電話番号ID"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/tools/whatsapp_api.py:0
msgid "Phone number Id is wrong."
msgstr "電話番号IDが間違っています"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "Pick an Account..."
msgstr "アカウントを選択..."

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "Pick users to notify..."
msgstr "ユーザを選んで通知..."

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__name
msgid "Placeholder"
msgstr "プレースホルダ"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/res_partner.py:0
msgid ""
"Please enter at least 3 characters when searching a Phone/Mobile number."
msgstr "電話番号/携帯電話番号を検索する場合は、少なくとも3文字以上を入力して下さい。"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__pl
msgid "Polish"
msgstr "ポーランド語"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_variable__field_type__portal_url
msgid "Portal Link"
msgstr "ポータルリンク"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__pt_br
msgid "Portuguese (BR)"
msgstr "ポルトガル語 (BR)"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__pt_pt
msgid "Portuguese (POR)"
msgstr "ポルトガル語 (POR)"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "Preview"
msgstr "プレビュー"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_preview_view_form
msgid "Preview WhatsApp"
msgstr "WhatsAppプレビュー"

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_whatsapp_preview
msgid "Preview template"
msgstr "テンプレートプレビュー"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__pa
msgid "Punjabi"
msgstr "パンジャブ語"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__quality
msgid "Quality"
msgstr "品質"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_button__button_type__quick_reply
msgid "Quick Reply"
msgstr "クイック応答"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__rating_ids
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__rating_ids
msgid "Ratings"
msgstr "評価"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__state__read
msgid "Read"
msgstr "読む"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
msgid "Read Messages"
msgstr "メッセージを読む"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/controller/main.py:0
msgid "Reason : %s"
msgstr "理由: %s"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__state__received
msgid "Received"
msgstr "入荷済"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "Receiving Messages"
msgstr "メッセージ受信中"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__quality__red
msgid "Red"
msgstr "赤"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__status__rejected
msgid "Rejected"
msgstr "拒否済"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/discuss_channel.py:0
msgid "Related %(model_name)s: "
msgstr "関連 %(model_name)s: "

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__model
msgid "Related Document Model"
msgstr "関連ドキュメントモデル"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_mail_mail__wa_message_ids
#: model:ir.model.fields,field_description:whatsapp.field_mail_message__wa_message_ids
msgid "Related WhatsApp Messages"
msgstr "関連WhatsAppメッセージ"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__state__replied
msgid "Replied"
msgstr "返信済"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__report_id
msgid "Report"
msgstr "レポート"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "Reset to draft"
msgstr "ドラフトにリセット"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__parent_id
msgid "Response To"
msgstr "以下へ回答:"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_tree
msgid "Retry"
msgstr "再試行"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ro
msgid "Romanian"
msgstr "ルーマニア語"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ru
msgid "Russian"
msgstr "ロシア語"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__message_has_sms_error
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS配信エラー"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__demo_value
msgid "Sample Value"
msgstr "サンプル値"

#. module: whatsapp
#. odoo-javascript
#: code:addons/whatsapp/static/src/core/public_web/discuss_app_model_patch.js:0
#: code:addons/whatsapp/static/src/core/web/messaging_menu_patch.xml:0
msgid "Search WhatsApp Channel"
msgstr "WhatsAppチャンネル検索"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.template_message_preview
msgid "See all options"
msgstr "全てのオプションを見る"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_composer_view_form
msgid "Send Message"
msgstr "メッセージ送信"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__ir_actions_server__state__whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_composer_view_form
msgid "Send WhatsApp"
msgstr "WhatsApp送信"

#. module: whatsapp
#. odoo-javascript
#: code:addons/whatsapp/static/src/chatter/web/chatter_patch.js:0
#: code:addons/whatsapp/static/src/components/whatsapp_button/whatsapp_button.js:0
msgid "Send WhatsApp Message"
msgstr "WhatsAppメッセージ送信"

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_whatsapp_composer
msgid "Send WhatsApp Wizard"
msgstr "WhatsAppウィザード送信"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.res_config_settings_view_form
msgid "Send and receive message through your WhatsApp Business account."
msgstr "WhatsApp Businessアカウントでメッセージを送受信。"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "Sending Messages"
msgstr "メッセージ送信中"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__state__sent
msgid "Sent"
msgstr "送信日"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
msgid "Sent Messages"
msgstr "送信済メッセージ"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__mobile_number
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
msgid "Sent To"
msgstr "以下へ送信済"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_tree
msgid "Sent to"
msgstr "以下へ送信"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__sequence
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__sequence
msgid "Sequence"
msgstr "シーケンス"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__sr
msgid "Serbian"
msgstr "セルビア語"

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_ir_actions_server
msgid "Server Action"
msgstr "サーバーアクション"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__sk
msgid "Slovak"
msgstr "スロバキア語"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__sl
msgid "Slovenian"
msgstr "スロベニア語"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/tools/whatsapp_api.py:0
msgid ""
"Something went wrong when contacting WhatsApp, please try again later. If "
"this happens frequently, contact support."
msgstr "WhatsApp に連絡した際に問題が発生しました。この現象が頻繁に発生する場合は、サポートまでご連絡下さい。"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__es
msgid "Spanish"
msgstr "スペイン語"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__es_ar
msgid "Spanish (ARG)"
msgstr "スペイン語 (ARG)"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__es_mx
msgid "Spanish (MEX)"
msgstr "スペイン語 (MEX)"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__es_es
msgid "Spanish (SPA)"
msgstr "スペイン語 (SPA)"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__state
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
msgid "State"
msgstr "都道府県・州"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_button__url_type__static
msgid "Static"
msgstr "静的"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__status
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_search
msgid "Status"
msgstr "状態"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "Submit for Approval"
msgstr "承認用に提出済"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__sw
msgid "Swahili"
msgstr "スワヒリ語"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__sv
msgid "Swedish"
msgstr "スウェーデン"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "Sync Template"
msgstr "テンプレート同期"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "Synchronize Templates"
msgstr "テンプレートを同期"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ta
msgid "Tamil"
msgstr "タミル語"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__te
msgid "Telugu"
msgstr "テルグ語"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__wa_template_id
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
msgid "Template"
msgstr "テンプレート"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/wizard/whatsapp_composer.py:0
msgid ""
"Template %(template_name)s holds a wrong configuration for 'phone field'\n"
"%(error_msg)s"
msgstr "テンプレート%(template_name)sの'電話番号フィールド'%(error_msg)sの設定が間違っています。"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_button_view_form
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_variable_view_form
msgid "Template Button"
msgstr "テンプレートボタン"

#. module: whatsapp
#: model_terms:ir.actions.act_window,help:whatsapp.whatsapp_template_action
msgid "Template Guidelines"
msgstr "テンプレートガイドライン"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__header_text
msgid "Template Header Text"
msgstr "テンプレートヘッダーテキスト"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__template_name
msgid "Template Name"
msgstr "テンプレート名"

#. module: whatsapp
#: model:ir.actions.act_window,name:whatsapp.whatsapp_preview_action_from_template
msgid "Template Preview"
msgstr "テンプレートプレビュー"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__failure_type__template
msgid "Template Quality Rating Too Low"
msgstr "テンプレートの品質評価が低すぎます"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__header_attachment_ids
msgid "Template Static Header"
msgstr "テンプレート静的ヘッダー"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__variable_ids
msgid "Template Variables"
msgstr "テンプレート変数"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__body
msgid "Template body"
msgstr "テンプレート本文"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "Template category is missing"
msgstr "テンプレートカテゴリがありません"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_variable.py:0
msgid ""
"Template variable should be in format {{number}}. Cannot parse "
"\"%(placeholder)s\""
msgstr "テンプレート変数は {{number}} の形式でなければなりません。\"%(placeholder)s\"をパースできません。"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_preview__wa_template_id
#: model:ir.ui.menu,name:whatsapp.whatsapp_template_menu
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_search
msgid "Templates"
msgstr "テンプレート"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_account.py:0
msgid "Templates Of %(account_name)s"
msgstr " %(account_name)sのテンプレート"

#. module: whatsapp
#: model_terms:ir.actions.act_window,help:whatsapp.whatsapp_template_action
msgid "Templates created on your"
msgstr "テンプレートが以下で作成されました:"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_account.py:0
msgid "Templates synchronized!"
msgstr "テンプレートが同期されました!"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "Test Credentials"
msgstr "認証情報テスト"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__header_type__text
msgid "Text"
msgstr "テキスト"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__th
msgid "Thai"
msgstr "タイ語"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid ""
"The Header Text must either contain no variable or the first one {{1}}."
msgstr "ヘッダテキストには変数を含まないか、最初の {{1}} を含める必要があります。"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "The phone number set in \"Buttons\" does not look correct."
msgstr "\"ボタン\"で設定した電話番号が正しく表示されません。"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_button.py:0
msgid "The placeholder for a button can only be {{1}}."
msgstr "ボタンのプレースホルダは {{1}}のみ可能です。"

#. module: whatsapp
#: model:ir.model.constraint,message:whatsapp.constraint_whatsapp_account_phone_uid_unique
msgid "The same phone number ID already exists"
msgstr "同じ電話IDが既に存在します"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "There is no record for preparing demo pdf in model %(model)s"
msgstr " %(model)sモデルにはデモpdfを作成した記録はありません。"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "There might be other templates that still need the Multi"
msgstr "他にもまだマルチオプションが必要なテンプレートがあるかもしれません。"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "There should be at most 1 variable in the header of the template."
msgstr "テンプレートのヘッダーには、最大でも1つの変数が必要です。"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/discuss_channel.py:0
msgid "This join method is not possible for regular channels."
msgstr "この結合方法は通常のチャンネルでは不可能です。"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/tools/whatsapp_api.py:0
msgid "To use WhatsApp Configure it first"
msgstr "WhatsAppを使用するには初めに設定して下さい。"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__tr
msgid "Turkish"
msgstr "トルコ語"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_ir_actions_server__state
#: model:ir.model.fields,field_description:whatsapp.field_ir_cron__state
#: model:ir.model.fields,field_description:whatsapp.field_mail_mail__message_type
#: model:ir.model.fields,field_description:whatsapp.field_mail_message__message_type
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__button_type
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__field_type
msgid "Type"
msgstr "タイプ"

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_ir_actions_server__state
#: model:ir.model.fields,help:whatsapp.field_ir_cron__state
msgid ""
"Type of server action. The following values are available:\n"
"- 'Update a Record': update the values of a record\n"
"- 'Create Activity': create an activity (Discuss)\n"
"- 'Send Email': post a message, a note or send an email (Discuss)\n"
"- 'Send SMS': send SMS, log them on documents (SMS)- 'Add/Remove Followers': add or remove followers to a record (Discuss)\n"
"- 'Create Record': create a new record with new values\n"
"- 'Execute Code': a block of Python code that will be executed\n"
"- 'Send Webhook Notification': send a POST request to an external system, also known as a Webhook\n"
"- 'Execute Existing Actions': define an action that triggers several other server actions\n"
msgstr ""
"サーバーアクションのタイプ。以下の値があります:\n"
"- レコードの更新: レコードの値を更新します。\n"
"- 活動の作成: 活動を作成します(ディスカス)\n"
"- メール送信: メッセージやメモを投稿したり、メールを送信したりします。\n"
"- 'SMSの送信': SMSを送信し、ドキュメント(SMS)にそれらを記録します - 'フォロワーの追加/削除': レコードにフォロワーを追加または削除します(ディスカス)\n"
"- レコードの作成': 新しい値で新しいレコードを作成します\n"
"- コードの実行': 実行されるPythonコードのブロックします\n"
"- 'Webhook通知の送信': Webhookとしても知られるPOSTリクエストを外部システムに送信します\n"
"- '既存アクションの実行': 他のサーバーアクションをトリガするアクションを定義します\n"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__uk
msgid "Ukrainian"
msgstr "ウクライナ語"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__failure_type__unknown
msgid "Unknown Error"
msgstr "不明なエラー"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/tools/whatsapp_exception.py:0
msgid "Unknown error when processing whatsapp request."
msgstr "Whatsappリクエスト処理中の不明なエラー"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ur
msgid "Urdu"
msgstr "ウルドゥー語"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__url_type
msgid "Url Type"
msgstr "URLタイプ"

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_mail_mail__message_type
#: model:ir.model.fields,help:whatsapp.field_mail_message__message_type
msgid ""
"Used to categorize message generator\n"
"'email': generated by an incoming email e.g. mailgateway\n"
"'comment': generated by user input e.g. through discuss or composer\n"
"'email_outgoing': generated by a mailing\n"
"'notification': generated by system e.g. tracking messages\n"
"'auto_comment': generated by automated notification mechanism e.g. acknowledgment\n"
"'user_notification': generated for a specific recipient"
msgstr ""
"メッセージジェネレーターの分類に使用されます\n"
"email': mailgatewayなどの受信メールによって生成されます\n"
"comment': ディスカッションやコンポーザーなどのユーザー入力によって生成されます\n"
"email_outgoing': メーリングによって生成されます\n"
"notification」: トラッキングメッセージなど、システムによって生成されます\n"
"auto_comment': 自動通知メカニズム(確認応答など)によって生成されます\n"
"user_notification': 特定の受信者のために生成されます"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_variable__field_type__user_mobile
msgid "User Mobile"
msgstr "ユーザ携帯"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_variable__field_type__user_name
msgid "User Name"
msgstr "ユーザ名"

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_res_users_settings
msgid "User Settings"
msgstr "ユーザ設定"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_message.py:0
msgid "User has been opt out of receiving WhatsApp messages"
msgstr "ユーザがWhatsAppメッセージの受信を拒否しました"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_message.py:0
msgid "User has opted in to receiving WhatsApp messages"
msgstr "ユーザがWhatsAppメッセージの受信を承諾しました"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/wizard/whatsapp_composer.py:0
msgid ""
"User mobile number required in template but no value set on user profile."
msgstr "テンプレートにユーザの携帯電話番号が必要ですが、ユーザプロファイルに値が設定されていません。"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__allowed_user_ids
msgid "Users"
msgstr "ユーザ"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_account.py:0
msgid "Users to notify is required"
msgstr "通知するユーザが必要"

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_whatsapp_account__notify_user_ids
msgid ""
"Users to notify when a message is received and there is no template send in "
"last 15 days"
msgstr "過去15日間にテンプレートが送信されておらず、メッセージが受信された場合にユーザに通知します。"

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_whatsapp_message__failure_reason
msgid "Usually an error message from Whatsapp"
msgstr "通常Whatsappからのエラーメッセージ"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__template_type__utility
msgid "Utility"
msgstr "ユーティリティ"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__uz
msgid "Uzbek"
msgstr "ウズベク語"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__variable_ids
msgid "Variable"
msgstr "可変"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__line_type
msgid "Variable location"
msgstr "可変ロケーション"

#. module: whatsapp
#: model:ir.model.constraint,message:whatsapp.constraint_whatsapp_template_variable_name_type_template_unique
msgid "Variable names must be unique for a given template"
msgstr "変数名は、与えられたテンプレートに対して一意でなければなりません。"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_variable.py:0
msgid ""
"Variables %(field_names)s do not seem to be valid field path for model "
"%(model_name)s."
msgstr "変数%(field_names)sは、モデル%(model_name)sの有効なフィールドパスではないようです。"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__header_type__video
msgid "Video"
msgstr "動画"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__vi
msgid "Vietnamese"
msgstr "ベトナム語"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_button__button_type__url
msgid "Visit Website"
msgstr "ウェブサイトを見る"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__wa_template_id
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__wa_template_id
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__wa_template_id
msgid "Wa Template"
msgstr "Whatsappテンプレート"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__webhook_verify_token
msgid "Webhook Verify Token"
msgstr "Webhook確認トークン"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__website_message_ids
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__website_message_ids
msgid "Website Messages"
msgstr "ウェブサイトメッセージ"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__website_url
msgid "Website URL"
msgstr "サイトURL"

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_whatsapp_account__website_message_ids
#: model:ir.model.fields,help:whatsapp.field_whatsapp_template__website_message_ids
msgid "Website communication history"
msgstr "ウェブサイト通信履歴"

#. module: whatsapp
#. odoo-javascript
#: code:addons/whatsapp/static/src/chatter/web/chatter_patch.xml:0
#: code:addons/whatsapp/static/src/components/whatsapp_button/whatsapp_button.xml:0
#: code:addons/whatsapp/static/src/core/common/thread_icon_patch.xml:0
#: code:addons/whatsapp/static/src/core/public_web/discuss_app_model_patch.js:0
#: code:addons/whatsapp/static/src/core/public_web/messaging_menu_patch.js:0
#: model:ir.model.fields.selection,name:whatsapp.selection__mail_message__message_type__whatsapp_message
#: model:ir.ui.menu,name:whatsapp.whatsapp_menu_main
#: model_terms:ir.ui.view,arch_db:whatsapp.res_config_settings_view_form
msgid "WhatsApp"
msgstr "WhatsApp"

#. module: whatsapp
#: model:ir.actions.server,name:whatsapp.ir_actions_server_resend_whatsapp_queue
msgid "WhatsApp : Resend failed Messages"
msgstr "WhatsApp : 失敗したメッセージを再送"

#. module: whatsapp
#: model:ir.actions.server,name:whatsapp.ir_cron_send_whatsapp_queue_ir_actions_server
msgid "WhatsApp : Send In Queue Messages"
msgstr "WhatsApp : 待機メッセージで送信"

#. module: whatsapp
#: model_terms:ir.actions.act_window,help:whatsapp.whatsapp_template_action
msgid "WhatsApp Account"
msgstr "Whatsappアカウント"

#. module: whatsapp
#: model:ir.actions.act_window,name:whatsapp.whatsapp_account_action
#: model:ir.model,name:whatsapp.model_whatsapp_account
#: model:ir.model.fields,field_description:whatsapp.field_discuss_channel__wa_account_id
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__wa_account_id
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "WhatsApp Business Account"
msgstr "WhatsAppビジネスアカウント"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "WhatsApp Business Account ID"
msgstr "WhatsAppビジネスアカウントID"

#. module: whatsapp
#: model:ir.ui.menu,name:whatsapp.whatsapp_account_menu
msgid "WhatsApp Business Accounts"
msgstr "WhatsAppビジネスアカウント"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_res_users_settings__is_discuss_sidebar_category_whatsapp_open
msgid "WhatsApp Category Open"
msgstr "WhatsAppカテゴリオープン"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_res_partner__wa_channel_count
#: model:ir.model.fields,field_description:whatsapp.field_res_users__wa_channel_count
msgid "WhatsApp Channel Count"
msgstr "WhatsAppチャネル数"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_discuss_channel__whatsapp_channel_valid_until
msgid "WhatsApp Channel Valid Until Datetime"
msgstr "WhatsAppチャネル有効期限日時"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/res_partner.py:0
#: model_terms:ir.ui.view,arch_db:whatsapp.discuss_channel_view_list_whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.view_partner_form
msgid "WhatsApp Chats"
msgstr "WhatsAppチャット"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__discuss_channel__channel_type__whatsapp
msgid "WhatsApp Conversation"
msgstr "WhatsApp会話"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "WhatsApp Message"
msgstr "WhatsAppメッセージ"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__msg_uid
msgid "WhatsApp Message ID"
msgstr "WhatsAppメッセージID"

#. module: whatsapp
#: model:ir.actions.act_window,name:whatsapp.whatsapp_message_action
#: model:ir.model,name:whatsapp.model_whatsapp_message
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_form
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_graph
msgid "WhatsApp Messages"
msgstr "WhatsAppメッセージ"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_discuss_channel__whatsapp_partner_id
msgid "WhatsApp Partner"
msgstr "WhatsApp取引先"

#. module: whatsapp
#: model:ir.actions.act_window,name:whatsapp.whatsapp_template_action
#: model:ir.model,name:whatsapp.model_whatsapp_template
#: model:ir.model.fields,field_description:whatsapp.field_ir_actions_server__wa_template_id
#: model:ir.model.fields,field_description:whatsapp.field_ir_cron__wa_template_id
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_tree
msgid "WhatsApp Template"
msgstr "WhatsAppテンプレート"

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_whatsapp_template_button
msgid "WhatsApp Template Button"
msgstr "WhatsAppテンプレートボタン"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__wa_template_uid
msgid "WhatsApp Template ID"
msgstr "WhatsAppテンプレートID"

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_whatsapp_template_variable
msgid "WhatsApp Template Variable"
msgstr "WhatsAppテンプレート変数"

#. module: whatsapp
#. odoo-javascript
#: code:addons/whatsapp/static/src/core/common/im_status_patch.xml:0
#: code:addons/whatsapp/static/src/discuss/core/common/channel_member_list_patch.xml:0
msgid "WhatsApp User"
msgstr "WhatsAppユーザ"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/tools/whatsapp_exception.py:0
msgid "Whatsapp account is misconfigured or shared."
msgstr "Whatsappアカウントが誤って設定されているか、共有されています"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/tools/whatsapp_exception.py:0
msgid "Whatsapp could not be reached or the query was malformed."
msgstr "Whatsappにアクセスできないか、クエリが不正です"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid ""
"When using a \"location\" header, there should 4 location variables not "
"%(count)d."
msgstr "\"ロケーション\"ヘッダーを使用する場合、4つの\"ロケーション\"変数が必要です%(count)d"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__failure_type__phone_invalid
msgid "Wrong Number Format"
msgstr "不正な番号フォーマット"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__quality__yellow
msgid "Yellow"
msgstr "黄"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid ""
"You are not allowed to use %(field)s in phone field, contact your "
"administrator to configure it."
msgstr "電話番号欄での%(field)sの使用は許可されていませんので、管理者に連絡して設定して下さい。"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_variable.py:0
msgid ""
"You are not allowed to use field %(field)s, contact your administrator."
msgstr "フィールド%(field)sを使用する権限がありません。管理者に連絡して下さい。"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_message.py:0
msgid "You can not cancel message which is in queue."
msgstr "待機中のメッセージを取消できません。"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_message.py:0
msgid "You can not resend message which is not in failed state."
msgstr "失敗ステータスにないメッセージを再送することはできません。"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
#: code:addons/whatsapp/models/whatsapp_template_variable.py:0
msgid "You can not select field of %(model)s."
msgstr "%(model)sのフィールドを選択することはできません。"

#. module: whatsapp
#. odoo-javascript
#: code:addons/whatsapp/static/src/components/whatsapp_variables_text_field/whatsapp_variables_text_field.js:0
msgid "You can set a maximum of 10 variables."
msgstr "最大10個の変数を設定できます。"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/discuss_channel.py:0
msgid ""
"You can't leave this channel. As you are the owner of this WhatsApp channel,"
" you can only delete it."
msgstr ""
"このチャンネルから退出することはできません。あなたはこのWhatsAppチャンネルのオーナーであるため、このチャンネルを削除することしかできません。"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid ""
"You cannot modify a template model when it is linked to server actions."
msgstr "テンプレートモデルがサーバアクションにリンクされている場合は、テンプレートモデルを変更することはできません。"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "You may only use one header attachment for each template"
msgstr "各テンプレートに使用できるヘッダー添付ファイルは1つだけです。"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/controller/main.py:0
msgid "Your Template has been rejected."
msgstr "テンプレートは既に拒否されました"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__zu
msgid "Zulu"
msgstr "ズールー語"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_message.py:0
msgid "another document"
msgstr "他のドキュメント"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "e.g. \"Acme Inc. Business Account\""
msgstr "例: \"Acme Inc. ビジネスアカウント\""

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "e.g. \"Send Order Document\""
msgstr "例: \"オーダドキュメント送信\""

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "e.g. 00112233445566778899aabbccddeeff"
msgstr "例: 00112233445566778899aabbccddeeff"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "e.g. 1234***********"
msgstr "例: 1234***********"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "e.g. EGTRWHRTHETHWRBTEJETHGQEGWRHWR"
msgstr "例: EGTRWHRTHETHWRBTEJETHGQEGWRHWR"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "e.g. Invitation for {{1}}"
msgstr "例:  {{1}}用の招待状"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "e.g. https://www.example.com"
msgstr "e.g. https://www.example.com"

#. module: whatsapp
#: model_terms:ir.actions.act_window,help:whatsapp.whatsapp_account_action
msgid "platform then connect it to your Odoo database"
msgstr "プラットフォーム、そしてそれをOdooデータベースに接続します"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_composer_view_form
msgid "recipients have an invalid phone number and will be skipped."
msgstr "宛先電話番号は無効で、スキップされます。"

#. module: whatsapp
#. odoo-javascript
#: code:addons/whatsapp/static/src/components/whatsapp_variables_text_field/whatsapp_variables_text_field.xml:0
msgid "variable"
msgstr "変数"

#. module: whatsapp
#: model_terms:ir.actions.act_window,help:whatsapp.whatsapp_template_action
msgid ""
"will be visible here once they're synced.\n"
"                You can also write new ones from here and submit them for approval, following the"
msgstr ""
"同期されると表示されます。\n"
"                また、以下に従い、ここから新しいものを書いて、承認用に提出することもできます:"
