# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_payroll_account_iso20022
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:27+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: hr_payroll_account_iso20022
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_iso20022.view_employee_form
msgid ""
"<span class=\"d-flex gap-2 w-50\">\n"
"                            <span invisible=\"is_trusted_bank_account\" class=\"text-muted\">Untrusted</span>\n"
"                            <span invisible=\"not is_trusted_bank_account\" class=\"text-success\">Trusted</span>\n"
"                        </span>"
msgstr ""
"<span class=\"d-flex gap-2 w-50\">\n"
"                            <span invisible=\"is_trusted_bank_account\" class=\"text-muted\">غير موثوق</span>\n"
"                            <span invisible=\"not is_trusted_bank_account\" class=\"text-success\">موثوق</span>\n"
"                        </span> "

#. module: hr_payroll_account_iso20022
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_iso20022.view_employee_form
msgid "Bank Account"
msgstr "الحساب البنكي"

#. module: hr_payroll_account_iso20022
#: model:ir.model.fields,field_description:hr_payroll_account_iso20022.field_hr_payroll_payment_report_wizard__journal_id
msgid "Bank Journal"
msgstr "دفتر يومية البنك"

#. module: hr_payroll_account_iso20022
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_iso20022.hr_payslip_run_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_iso20022.hr_payslip_view_form
msgid "Create Payment Report"
msgstr "إنشاء تقرير دفع "

#. module: hr_payroll_account_iso20022
#: model:ir.model,name:hr_payroll_account_iso20022.model_hr_employee
msgid "Employee"
msgstr "الموظف"

#. module: hr_payroll_account_iso20022
#: model:hr.payroll.dashboard.warning,name:hr_payroll_account_iso20022.hr_payroll_dashboard_warning_employee_invalid_bank_account
msgid "Employees With Invalid Bank Accounts"
msgstr "الموظفون الذين لديهم حسابات بنكية غير صالحة "

#. module: hr_payroll_account_iso20022
#: model:hr.payroll.dashboard.warning,name:hr_payroll_account_iso20022.hr_payroll_dashboard_warning_untrusted_bank_accounts
msgid "Employees with untrusted Bank Account numbers"
msgstr "الموظفون الذين لديهم أرقام حسابات بنكية غير موثوق بها "

#. module: hr_payroll_account_iso20022
#: model:ir.model.fields,field_description:hr_payroll_account_iso20022.field_hr_payroll_payment_report_wizard__export_format
msgid "Export Format"
msgstr "صيغة الملف المصدر "

#. module: hr_payroll_account_iso20022
#: model:ir.model,name:hr_payroll_account_iso20022.model_hr_payroll_payment_report_wizard
msgid "HR Payroll Payment Report Wizard"
msgstr "معالج تقرير دفع المرتبات للموارد البشرية "

#. module: hr_payroll_account_iso20022
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_iso20022.hr_payslip_run_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_iso20022.hr_payslip_view_form
msgid "Mark as paid"
msgstr "التحديد كمدفوع "

#. module: hr_payroll_account_iso20022
#: model:ir.model,name:hr_payroll_account_iso20022.model_hr_payslip
msgid "Pay Slip"
msgstr "إيصال الدفع "

#. module: hr_payroll_account_iso20022
#: model:ir.model,name:hr_payroll_account_iso20022.model_hr_payslip_run
msgid "Payslip Batches"
msgstr "دفعات إيصال الدفع "

#. module: hr_payroll_account_iso20022
#: model:ir.model.fields.selection,name:hr_payroll_account_iso20022.selection__hr_payroll_payment_report_wizard__export_format__sepa
msgid "SEPA"
msgstr "SEPA"

#. module: hr_payroll_account_iso20022
#: model:ir.model.fields,field_description:hr_payroll_account_iso20022.field_hr_employee__is_trusted_bank_account
msgid "Send Money"
msgstr "إرسال المال "

#. module: hr_payroll_account_iso20022
#: model:ir.model.fields,help:hr_payroll_account_iso20022.field_hr_employee__is_trusted_bank_account
msgid ""
"Sending fake invoices with a fraudulent account number is a common phishing "
"practice. To protect yourself, always verify new bank account numbers, "
"preferably by calling the vendor, as phishing usually happens when their "
"emails are compromised. Once verified, you can activate the ability to send "
"money."
msgstr ""
"إرسال فواتير مزيفة مع رقم حساب احتيالي هي ممارسة تصيّد احتيالية شائعة. "
"لحماية نفسك، تأكد دائماً من صحة أرقام الحسابات البنكية الجديدة، ويُفضل "
"القيام بذلك عن طريق المورّد، حيث إن التصيّد الاحتيالي يحدث عادة عندما لا "
"تكون عناوين البريد الإلكتروني الخاصة بهم آمنة. بمجرد أن تتحقق من الصحة، "
"سيكون بوسعك تفعيل إمكانية إرسال المال. "

#. module: hr_payroll_account_iso20022
#. odoo-python
#: code:addons/hr_payroll_account_iso20022/wizard/hr_payroll_payment_report_wizard.py:0
msgid "Some employees (%s) don't have a valid name on the work contact."
msgstr "لا يملك بعض الموظفون (%s) اسماً صالحاً في معلومات تواصل العمل. "

#. module: hr_payroll_account_iso20022
#. odoo-python
#: code:addons/hr_payroll_account_iso20022/wizard/hr_payroll_payment_report_wizard.py:0
msgid "Some employees (%s) don't have a work contact."
msgstr "لا يملك بعض الموظفون (%s) معلومات تواصل للعمل. "

#. module: hr_payroll_account_iso20022
#. odoo-python
#: code:addons/hr_payroll_account_iso20022/models/hr_employee.py:0
msgid ""
"The following employees have invalid bank accounts and could not be trusted:\n"
"%s"
msgstr ""
"الموظفون التاليون لديهم حسابات بنكية غير صالحة ولا يمكن الوثوق بها:\n"
"%s"

#. module: hr_payroll_account_iso20022
#. odoo-python
#: code:addons/hr_payroll_account_iso20022/wizard/hr_payroll_payment_report_wizard.py:0
msgid ""
"The journal '%s' requires a proper IBAN account to pay via SEPA. Please "
"configure it first."
msgstr ""
"يتطلب دفتر اليومية '%s' حساب IBAN مناسب ليتمكن من الدفع عن طريق SEPA. الرجاء"
" تهيئته أولًا. "

#. module: hr_payroll_account_iso20022
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_iso20022.view_employee_form
msgid "Trust Bank Account"
msgstr "الوثوق بالحساب البنكي "

#. module: hr_payroll_account_iso20022
#: model:ir.model.fields,field_description:hr_payroll_account_iso20022.field_hr_payslip__iso20022_uetr
msgid "UETR"
msgstr "UETR"

#. module: hr_payroll_account_iso20022
#: model:ir.model.fields,help:hr_payroll_account_iso20022.field_hr_payslip__iso20022_uetr
msgid "Unique end-to-end transaction reference"
msgstr "مرجع فريد للمعاملات من البداية إلى النهاية "

#. module: hr_payroll_account_iso20022
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_iso20022.view_employee_form
msgid "Untrust Bank Account"
msgstr "عدم الوثوق بالحساب البنكي "

#. module: hr_payroll_account_iso20022
#. odoo-python
#: code:addons/hr_payroll_account_iso20022/models/hr_employee.py:0
msgid "You do not have the right to trust or un-trust a bank account."
msgstr "لا تملك صلاحية الوثوق أو عدم الوثوق بحساب بنكي. "
