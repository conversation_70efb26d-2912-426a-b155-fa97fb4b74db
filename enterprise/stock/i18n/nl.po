# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2025
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"\n"
"\n"
"Transfers %(transfer_list)s: You need to supply a Lot/Serial number for products %(product_list)s."
msgstr ""
"\n"
"\n"
"Overdrachten %(transfer_list)s: Je moet een partij/serienummer ingeven voor de producten %(product_list)s."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid ""
"\n"
"(%(serial_number)s) exists in location %(location)s"
msgstr ""
"\n"
"(%(serial_number)s) bestaat op locatie %(location)s"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid ""
"\n"
"The quantity done for the product %(product)s doesn't respect the rounding precision defined on the unit of measure %(unit)s.\n"
"Please change the quantity done or the rounding precision of your unit of measure."
msgstr ""
"\n"
"Het aantal gereed voor het product %(product)s komt niet overeen met de afrondingsprecisie gedefinieerd op de maateenheid %(unit)s.\n"
"Wijzig het aantal gereed of de afrondingsprecisie van je maateenheid."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__state
msgid ""
" * Draft: The transfer is not confirmed yet. Reservation doesn't apply.\n"
" * Waiting another operation: This transfer is waiting for another operation before being ready.\n"
" * Waiting: The transfer is waiting for the availability of some products.\n"
"(a) The shipping policy is \"As soon as possible\": no product could be reserved.\n"
"(b) The shipping policy is \"When all products are ready\": not all the products could be reserved.\n"
" * Ready: The transfer is ready to be processed.\n"
"(a) The shipping policy is \"As soon as possible\": at least one product has been reserved.\n"
"(b) The shipping policy is \"When all products are ready\": all product have been reserved.\n"
" * Done: The transfer has been processed.\n"
" * Cancelled: The transfer has been cancelled."
msgstr ""
" * Concept: de overdracht is nog niet bevestigd. Reservering is niet van toepassing.\n"
" * Wachten op een andere bewerking: deze overdracht wacht op een andere bewerking voordat deze beschikbaar is.\n"
" * Wachten: de overdracht wacht op de beschikbaarheid van sommige producten.\n"
"(a) Het verzendbeleid is \"Zo snel mogelijk\": er kan geen product worden gereserveerd.\n"
"(b) Het verzendbeleid is \"Wanneer alle producten beschikbaar zijn\": niet alle producten kunnen worden gereserveerd.\n"
" * Beschikbaar: de overdracht kan worden verwerkt.\n"
"(a) Het verzendbeleid is \"Zo snel mogelijk\": er is tenminste één product gereserveerd.\n"
"(b) Het verzendbeleid is \"Wanneer alle producten beschikbaar zijn\": alle producten zijn gereserveerd.\n"
" * Gereed: de overdracht is verwerkt.\n"
" * Geannuleerd: de overdracht is geannuleerd."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_lot.py:0
msgid " - Product: %(product)s, Lot/Serial Number: %(lot)s"
msgstr " - Product: %(product)s, Partij/Serienummer: %(lot)s"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__cyclic_inventory_frequency
#: model:ir.model.fields,help:stock.field_stock_quant__cyclic_inventory_frequency
msgid ""
" When different than 0, inventory count date for products stored at this "
"location will be automatically set at the defined frequency."
msgstr ""
" Indien anders dan 0, wordt de voorraadtellingsdatum voor producten die op "
"deze locatie zijn opgeslagen automatisch ingesteld op de gedefinieerde "
"frequentie."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__return_count
msgid "# Returns"
msgstr "# Retouren"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "%(name)s (copy)(%(id)s)"
msgstr "%(name)s (kopie)(%(id)s)"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "%(name)s Sequence cross dock"
msgstr "%(name)s Volgorde cross dock"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "%(name)s Sequence in"
msgstr "%(name)s Volgorde in"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "%(name)s Sequence internal"
msgstr "%(name)s Volgorde intern"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "%(name)s Sequence out"
msgstr "%(name)s Volgorde uit"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "%(name)s Sequence packing"
msgstr "%(name)s Volgorde verpakking"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "%(name)s Sequence picking"
msgstr "%(name)s Pickingnummer"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "%(name)s Sequence quality control"
msgstr "%(name)s Volgorde kwaliteitscontrole"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "%(name)s Sequence storage"
msgstr "%(name)s Volgorde opslag"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid ""
"%(operations)s have default source or destination locations within warehouse"
" %(warehouse)s, therefore you cannot archive it."
msgstr ""
"%(operations)s  hebben standaard bron- of bestemmingslocaties binnen "
"magazijn %(warehouse)s,  je kunt ze dan ook niet archiveren."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_scrap.py:0
msgid "%(product)s: Insufficient Quantity To Scrap"
msgstr "%(product)s: Onvoldoende aantal om af te keuren"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid ""
"%(product_name)s --> Product UoM is %(product_uom)s "
"(%(product_uom_category)s) - Move UoM is %(move_uom)s "
"(%(move_uom_category)s)"
msgstr ""
"%(product_name)s --> Maateenheid product is %(product_uom)s "
"(%(product_uom_category)s) - Maateenheid verplaatsing is %(move_uom)s "
"(%(move_uom_category)s)"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "%(warehouse)s Sequence %(code)s"
msgstr "%(warehouse)s Volgorde %(code)s"

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/stock_replenishment_info.py:0
msgid ""
"%(warehouse)s can only provide %(free_qty)s %(uom)s, while the quantity to "
"order is %(qty_to_order)s %(uom)s."
msgstr ""
"%(warehouse)s kan slechts %(free_qty)s %(uom)s leveren, terwijl het te "
"bestellen aantal %(qty_to_order)s %(uom)s is."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "%(warehouse)s: Supply Product from %(supplier)s"
msgstr "%(warehouse)s: Product leveren vanaf %(supplier)s"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_location.py:0
#: code:addons/stock/models/stock_package_type.py:0
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/models/stock_rule.py:0
#: code:addons/stock/models/stock_storage_category.py:0
#: code:addons/stock/models/stock_warehouse.py:0
msgid "%s (copy)"
msgstr "%s (kopie)"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid "%s [reverted]"
msgstr "%s [teruggezet]"

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/stock_replenishment_info.py:0
msgid "%s days"
msgstr "%s dagen"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "%s: Can't split: quantities done can't be above demand"
msgstr ""
"%s: Kan niet splitsen: uitgevoerde hoeveelheden kunnen niet boven de vraag "
"liggen"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"%s: Nothing to split, all demand is done. For split you need at least one "
"line not fully fulfilled"
msgstr ""
"%s: Er valt niets te splitsen, de volledige opdracht is uitgevoerd. Om te "
"splitsen heb je op zijn minst één regel nodig die niet volledig is "
"uitgevoerd"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"%s: Nothing to split. Fill the quantities you want in a new transfer in the "
"done quantities"
msgstr ""
"%s: Er valt niets te splitsen. Vul de hoeveelheden in een nieuwe transfer in"
" onder de uitgevoerde hoeveelheden"

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_inventory
msgid "'Count Sheet'"
msgstr "'Telblad'"

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_delivery
msgid ""
"'Delivery Slip - %s - %s' % (object.partner_id.name or '', object.name)"
msgstr ""
"'Leveringsbon - %s - %s' % (object.partner_id.name or '', object.name)"

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_location_barcode
msgid "'Location - %s' % object.name"
msgstr "'Locatie - %s' % object.name"

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_lot_label
msgid "'Lot-Serial - %s' % object.name"
msgstr "'Partij-Serie - %s' % object.name"

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_picking_type_label
msgid "'Operation-type - %s' % object.name"
msgstr "'Bewerkingssoort - %s' % object.name"

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_picking_packages
msgid "'Packages - %s' % (object.name)"
msgstr "'Verpakkingen - %s' % (object.name)"

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_picking
msgid ""
"'Picking Operations - %s - %s' % (object.partner_id.name or '', object.name)"
msgstr ""
"'Pickbewerkingen - %s - %s' % (object.partner_id.name or '', object.name)"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_lot.py:0
msgid "(copy of) %s"
msgstr "(kopie van) %s"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "(document barcode)"
msgstr "(barcode document)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "(package barcode)"
msgstr "(barcode verpakking)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "(product barcode)"
msgstr "(barcode product)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "(serial barcode)"
msgstr "(seriële barcode)"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__state
#: model:ir.model.fields,help:stock.field_stock_move_line__state
msgid ""
"* New: The stock move is created but not confirmed.\n"
"* Waiting Another Move: A linked stock move should be done before this one.\n"
"* Waiting Availability: The stock move is confirmed but the product can't be reserved.\n"
"* Available: The product of the stock move is reserved.\n"
"* Done: The product has been transferred and the transfer has been confirmed."
msgstr ""
"* Nieuw: De voorraadverplaatsing is aangemaakt, maar nog niet bevestigd.\n"
"* In afwachting van een andere verplaatsing: Een gekoppelde voorraadverplaatsing moet voor deze worden uitgevoerd.\n"
"* In afwachting van beschikbaarheid: De voorraadverplaatsing is bevestigd, maar het product kan niet worden gereserveerd.\n"
"* Beschikbaar: Het product van de voorraadverplaatsing is gereserveerd.\n"
"* Gereed: Het product is verplaatst en de verplaatsing is bevestigd."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__usage
#: model:ir.model.fields,help:stock.field_stock_move__location_dest_usage
#: model:ir.model.fields,help:stock.field_stock_move__location_usage
#: model:ir.model.fields,help:stock.field_stock_move_line__location_dest_usage
#: model:ir.model.fields,help:stock.field_stock_move_line__location_usage
msgid ""
"* Vendor Location: Virtual location representing the source location for products coming from your vendors\n"
"* View: Virtual location used to create a hierarchical structures for your warehouse, aggregating its child locations ; can't directly contain products\n"
"* Internal Location: Physical locations inside your own warehouses,\n"
"* Customer Location: Virtual location representing the destination location for products sent to your customers\n"
"* Inventory Loss: Virtual location serving as counterpart for inventory operations used to correct stock levels (Physical inventories)\n"
"* Production: Virtual counterpart location for production operations: this location consumes the components and produces finished products\n"
"* Transit Location: Counterpart location that should be used in inter-company or inter-warehouses operations"
msgstr ""
"* Leverancier locatie: Virtuele locatie die de bronlocatie is voor producten die afkomstig zijn van je leveranciers\n"
"* Weergave: Virtuele locatie welke gebruikt wordt om ​​een hiërarchische structuur te maken voor je magazijn, deze bevat de onderliggende locaties en kan zelf geen producten bevatten                       \n"
"* Interne locatie: Fysieke locaties in je eigen magazijnen                       \n"
"* Klant locatie: Virtuele locatie die de plaats van bestemming aangeeft voor de producten die naar je klanten verzonden worden\n"
"* Voorraadtelling: Virtuele locatie die dient als tegenhanger voor voorraadtellingsactiviteiten gebruikt om de voorraden (Fysieke voorraden) te corrigeren\n"
"* Productie: virtuele locatie die dient als tijdelijke tegenhanger voor productieactiviteiten: deze locatie verbruikt de onderdelen en levert eindproducten\n"
"* Transitlocatie: Tegenlocatie welke moet worden gebruikt bij inter-company of inter-warehouse bewerkingen"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
msgid "+ %d day(s)"
msgstr "+ %d dag(en)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid ", max:"
msgstr ", max:"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "-&gt;"
msgstr "-&gt;"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid ""
".\n"
"            Manual actions may be needed."
msgstr ""
".\n"
"Handmatige acties zijn mogelijk nodig."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_orderpoint_snooze__predefined_date__day
msgid "1 Day"
msgstr "1 dag"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_orderpoint_snooze__predefined_date__month
msgid "1 Month"
msgstr "1 maand"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_orderpoint_snooze__predefined_date__week
msgid "1 Week"
msgstr "1 week"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
msgid "12.0"
msgstr "12.0"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_generic_barcode
msgid "1234567890"
msgstr "1234567890"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_lot_label
msgid "12345678901"
msgstr "12345678901"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__product_label_format__2x7xprice
msgid "2 x 7 with price"
msgstr "2 x 7 met prijs"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
msgid "2021-9-01"
msgstr "2021-9-01"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "2023-01-01"
msgstr "01-01-2023"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "2023-09-24"
msgstr "2023-09-24"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "3.00"
msgstr "3.00"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__lot_label_layout__print_format__4x12
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__product_label_format__4x12
msgid "4 x 12"
msgstr "4 x 12"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__lot_label_format__4x12_lots
msgid "4 x 12 - One per lot/SN"
msgstr "4 x 12 - Eén per lot/SN"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__lot_label_format__4x12_units
msgid "4 x 12 - One per unit"
msgstr "4 x 12 - Eén per eenheid"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__product_label_format__4x12xprice
msgid "4 x 12 with price"
msgstr "4 x 12 met prijs"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__product_label_format__4x7xprice
msgid "4 x 7 with price"
msgstr "4 x 7 met prijs"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
msgid "54326786758"
msgstr "54326786758"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "98"
msgstr "98"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
msgid ""
"<br/>\n"
"                    <strong>Current Inventory: </strong>"
msgstr ""
"<br/>\n"
"                    <strong>Huidige voorraad: </strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.help_message_template
msgid ""
"<br/>\n"
"                Want to speed up operations?"
msgstr ""
"<br/>\n"
"                Wil je de bewerkingen versnellen?"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
msgid ""
"<br>A need is created in <b>%s</b> and a rule will be triggered to fulfill "
"it."
msgstr ""
"<br>Er is een behoefte aangemaakt in <b>%s</b> en er wordt een regel "
"geactiveerd om deze te vervullen."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
msgid ""
"<br>If the products are not available in <b>%s</b>, a rule will be triggered"
" to bring the missing quantity in this location."
msgstr ""
"<br>Als de producten niet beschikbaar zijn in <b>%s</b>, wordt een regel "
"geactiveerd om de ontbrekende aantal naar deze locatie te verplaatsen."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
msgid ""
"<br>The products will be moved towards <b>%(destination)s</b>, <br/> as "
"specified from <b>%(operation)s</b> destination."
msgstr ""
"<br>De producten worden verplaatst naar <b>%(destination)s</b>, <br/> zoals "
"gespecificeerd in de bestemming van <b>%(operation)s</b>."

#. module: stock
#: model:mail.template,body_html:stock.mail_template_data_delivery_confirmation
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Hello <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,<br/><br/>\n"
"        We are glad to inform you that your order has been shipped.\n"
"        <t t-if=\"hasattr(object, 'carrier_tracking_ref') and object.carrier_tracking_ref\">\n"
"            Your tracking reference is\n"
"            <strong>\n"
"            <t t-if=\"object.carrier_tracking_url\">\n"
"                <t t-set=\"multiple_carrier_tracking\" t-value=\"object.get_multiple_carrier_tracking()\"/>\n"
"                <t t-if=\"multiple_carrier_tracking\">\n"
"                    <t t-foreach=\"multiple_carrier_tracking\" t-as=\"line\">\n"
"                        <br/><a t-att-href=\"line[1]\" target=\"_blank\" t-out=\"line[0] or ''\"/>\n"
"                    </t>\n"
"                </t>\n"
"                <t t-else=\"\">\n"
"                    <a t-attf-href=\"{{ object.carrier_tracking_url }}\" target=\"_blank\" t-out=\"object.carrier_tracking_ref or ''\"/>.\n"
"                </t>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                <t t-out=\"object.carrier_tracking_ref or ''\"/>.\n"
"            </t>\n"
"            </strong>\n"
"        </t>\n"
"        <br/><br/>\n"
"        Please find your delivery order attached for more details.<br/><br/>\n"
"        Thank you,\n"
"        <t t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"            <br/>\n"
"            <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"        </t>\n"
"    </p>\n"
"</div>\n"
"        "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Hallo <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,<br/><br/>\n"
"        Goed nieuws! Je bestelling is verzonden.\n"
"        <t t-if=\"hasattr(object, 'carrier_tracking_ref') and object.carrier_tracking_ref\">\n"
"            Volg je bestelling met deze trackingreferentie:\n"
"            <strong>\n"
"            <t t-if=\"object.carrier_tracking_url\">\n"
"                <t t-set=\"multiple_carrier_tracking\" t-value=\"object.get_multiple_carrier_tracking()\"/>\n"
"                <t t-if=\"multiple_carrier_tracking\">\n"
"                    <t t-foreach=\"multiple_carrier_tracking\" t-as=\"line\">\n"
"                        <br/><a t-att-href=\"line[1]\" target=\"_blank\" t-out=\"line[0] or ''\"/>\n"
"                    </t>\n"
"                </t>\n"
"                <t t-else=\"\">\n"
"                    <a t-attf-href=\"{{ object.carrier_tracking_url }}\" target=\"_blank\" t-out=\"object.carrier_tracking_ref or ''\"/>.\n"
"                </t>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                <t t-out=\"object.carrier_tracking_ref or ''\"/>.\n"
"            </t>\n"
"            </strong>\n"
"        </t>\n"
"        <br/><br/>\n"
"        Meer details vind je in het bijgevoegde leveringsdocument.\n"
".<br/><br/>\n"
"        Bedankt,\n"
"        <t t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"            <br/>\n"
"            <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"        </t>\n"
"    </p>\n"
"</div>\n"
"        "

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_view_kanban
msgid "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Date\" title=\"Date\"/>"
msgstr ""
"<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Boekdatum\" "
"title=\"Boekdatum\"/>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid ""
"<i class=\"fa fa-exclamation-triangle\"/>\n"
"                                All products could not be reserved. Click on the \"Check Availability\" button to try to reserve products."
msgstr ""
"<i class=\"fa fa-exclamation-triangle\"/>\n"
"                                Niet alle producten konden worden gereserveerd. Klik op de knop \"Beschikbaarheid controleren\" om te proberen de producten te reserveren."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "<span class=\"col-6\">Back Orders</span>"
msgstr "<span class=\"col-6\">Backorders</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "<span class=\"col-6\">Late</span>"
msgstr "<span class=\"col-6\">Te laat</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "<span class=\"col-6\">Operations</span>"
msgstr "<span class=\"col-6\">Bewerkingen</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "<span class=\"col-6\">Waiting</span>"
msgstr "<span class=\"col-6\">Wachten</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Scannable Package Contents</span>"
msgstr "<span class=\"o_form_label\">Scanbare verpakkingsinhoud</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "<span class=\"o_stat_text\">Allocation</span>"
msgstr "<span class=\"o_stat_text\">Toewijzing</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "<span class=\"o_stat_text\">Forecasted</span>"
msgstr "<span class=\"o_stat_text\">Virtueel</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "<span class=\"o_stat_text\">In:</span>"
msgstr "<span class=\"o_stat_text\">In:</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid "<span class=\"o_stat_text\">Location</span>"
msgstr "<span class=\"o_stat_text\">Locatie</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.view_partner_stock_warnings_form
msgid "<span class=\"o_stat_text\">Lot/Serial Numbers</span>"
msgstr "<span class=\"o_stat_text\">Partij/Serienummer</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "<span class=\"o_stat_text\">Max:</span>"
msgstr "<span class=\"o_stat_text\">Max:</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "<span class=\"o_stat_text\">Min:</span>"
msgstr "<span class=\"o_stat_text\">Min:</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "<span class=\"o_stat_text\">Moves</span>"
msgstr "<span class=\"o_stat_text\">Boekingen</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "<span class=\"o_stat_text\">Next Transfer</span>"
msgstr "<span class=\"o_stat_text\">Volgende verplaatsing</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "<span class=\"o_stat_text\">On Hand</span>"
msgstr "<span class=\"o_stat_text\">Beschikbare voorraad</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "<span class=\"o_stat_text\">Operations</span>"
msgstr "<span class=\"o_stat_text\">Bewerkingen</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "<span class=\"o_stat_text\">Out:</span>"
msgstr "<span class=\"o_stat_text\">Uit:</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view
msgid "<span class=\"o_stat_text\">Product Moves</span>"
msgstr "<span class=\"o_stat_text\">Productverplaatsingen</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "<span class=\"o_stat_text\">Putaway Rules</span>"
msgstr "<span class=\"o_stat_text\">Wegzetregels</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "<span class=\"o_stat_text\">Routes</span>"
msgstr "<span class=\"o_stat_text\">Routes</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "<span class=\"o_stat_text\">Storage Capacities</span>"
msgstr "<span class=\"o_stat_text\">Opslagcapaciteiten</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid "<span class=\"o_stat_text\">Traceability</span>"
msgstr "<span class=\"o_stat_text\">Traceerbaarheid</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<span><strong>Delivery Address:</strong></span>"
msgstr "<span><strong>Afleveradres:</strong></span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_return_slip
msgid "<span>OBTRETU</span>"
msgstr "<span>OBTRETU</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_view_kanban_catalog
msgid "<span>On Hand: </span>"
msgstr "<span>Beschikbare voorraad: </span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "<span>Package Type: </span>"
msgstr "<span>Verpakkingssoort:</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_report_delivery_no_package_section_line
msgid "<span>Products with no package assigned</span>"
msgstr "<span>Producten waaraan geen verpakking is toegewezen</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "<span>Remaining quantities not yet delivered:</span>"
msgstr "<span>Resterende hoeveelheden nog niet geleverd:</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "<span>days</span>"
msgstr "<span>dagen</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "<span>×</span>"
msgstr "<span>×</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_head
msgid ""
"<strong>\n"
"                The done move line has been corrected.\n"
"            </strong>"
msgstr ""
"<strong>\n"
"                De verwerkte verplaatsingsregel is aangepast.\n"
"            </strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Customer Address</strong>"
msgstr "<strong>Klant Adres</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Delivery address</strong>"
msgstr "<strong>Afleveradres</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid ""
"<strong>Due to some stock moves done between your initial update of the "
"quantity and now, the difference of quantity is not consistent "
"anymore.</strong>"
msgstr ""
"<strong>Als gevolg van enkele voorraadverplaatsingen tussen je initiële "
"update van het aantal en nu, is het verschil in aantal niet meer "
"consistent.</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>From</strong>"
msgstr "<strong>Van</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Lot/Serial Number</strong>"
msgstr "<strong>Partij/Serienummer</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_warehouse_orderpoint_kanban
msgid "<strong>Max qty:</strong>"
msgstr "<strong>Max hvh:</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_warehouse_orderpoint_kanban
msgid "<strong>Min qty:</strong>"
msgstr "<strong>Min hvh:</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Order</strong>"
msgstr "<strong>Bestel</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
msgid "<strong>Pack Date:</strong>"
msgstr "<strong>Verpakkingsdatum:</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
msgid "<strong>Package Type:</strong>"
msgstr "<strong>Verpakkingssoort:</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Product Barcode</strong>"
msgstr "<strong>Productbarcode</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Product</strong>"
msgstr "<strong>Product</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Quantity</strong>"
msgstr "<strong>Aantal</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Recipient address</strong>"
msgstr "<strong>Ontvangstadres</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Scheduled Date</strong>"
msgstr "<strong>Geplande datum</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "<strong>Shipping Date</strong>"
msgstr "<strong>Verzenddatum</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "<strong>Signature</strong>"
msgstr "<strong>Handtekening</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Status</strong>"
msgstr "<strong>Status</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_head
msgid "<strong>The initial demand has been updated.</strong>"
msgstr "<strong>De initiële vraag is bijgewerkt.</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>To</strong>"
msgstr "<strong>Naar</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_track_confirmation
msgid "<strong>Tracked product(s):</strong>"
msgstr "<strong>Getraceerd(e) product(en):</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Vendor Address</strong>"
msgstr "<strong>Adres verkoper</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Warehouse Address</strong>"
msgstr "<strong>Adres magazijn</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Warehouse address</strong>"
msgstr "<strong>Magazijnadres</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_package_destination_form_view
msgid "<strong>Where do you want to send the products?</strong>"
msgstr "<strong>Waar wil je de producten heen sturen?</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_scrap_form_view
msgid "? This may lead to inconsistencies in your inventory."
msgstr "? Dit kan leiden tot tegenstrijdigheden in je voorraad."

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_package_type_barcode_uniq
msgid "A barcode can only be assigned to one package type!"
msgstr "Een barcode kan enkel aan één verpakkingsssoort worden toegekend!"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_warehouse_orderpoint_product_location_check
msgid "A replenishment rule already exists for this product on this location."
msgstr "Er bestaat al een aanvullingsregel voor dit product op deze locatie."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__is_storable
#: model:ir.model.fields,help:stock.field_product_template__is_storable
#: model:ir.model.fields,help:stock.field_stock_move__is_storable
msgid "A storable product is a product for which you manage stock."
msgstr "Een houdbaar product is een product waarvan je de voorraad beheert."

#. module: stock
#: model:res.groups,name:stock.group_warning_stock
msgid "A warning can be set on a partner (Stock)"
msgstr "Een waarschuwing kan worden ingesteld op een contact (voorraad)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__action
msgid "Action"
msgstr "Actie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__message_needaction
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_needaction
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_needaction
msgid "Action Needed"
msgstr "Actie gevraagd"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__replenish_location
msgid ""
"Activate this function to get all quantities to replenish at this particular"
" location"
msgstr ""
"Activeer deze functie om alle hoeveelheden op deze specifieke locatie aan te"
" vullen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__active
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__active
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__active
#: model:ir.model.fields,field_description:stock.field_stock_route__active
#: model:ir.model.fields,field_description:stock.field_stock_rule__active
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__active
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__active
msgid "Active"
msgstr "Actief"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__activity_ids
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_ids
msgid "Activities"
msgstr "Activiteiten"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__activity_exception_decoration
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Activiteit uitzondering decoratie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__activity_state
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_state
msgid "Activity State"
msgstr "Activiteitsfase"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__activity_type_icon
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_type_icon
msgid "Activity Type Icon"
msgstr "Activiteitensoort icoon"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_view_activity
msgid "Activity view"
msgstr "Activiteit weergave"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Add a Product"
msgstr "Product toevoegen"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_product_production_lot_form
#: model_terms:ir.actions.act_window,help:stock.action_production_lot_form
msgid "Add a lot/serial number"
msgstr "Een partij/serienummer toevoegen"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_location_form
msgid "Add a new location"
msgstr "Een nieuwe locatie toevoegen"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_routes_form
msgid "Add a new route"
msgstr "Een nieuwe route toevoegen"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_storage_category
msgid "Add a new storage category"
msgstr "Een nieuwe opslagcategorie toevoegen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid ""
"Add an internal note that will be printed on the Picking Operations sheet"
msgstr ""
"Voeg een interne notitie toe die niet geprint wordt op de pickinglijst"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_config_settings__group_stock_adv_location
msgid ""
"Add and customize route operations to process product moves in your warehouse(s): e.g. unload > quality control > stock for incoming products, pick > pack > ship for outgoing products. \n"
" You can also set putaway strategies on warehouse locations in order to send incoming products into specific child locations straight away (e.g. specific bins, racks)."
msgstr ""
"Voeg routes toe en pas ze aan om productverplaatsingen in je magazijn(en) te verwerken: bijv. ontvangst > kwaliteitscontrole > voorraad voor inkomende producten, picken > verpakken > verzenden voor uitgaande producten.\n"
"Je kunt ook opslagstrategieën instellen op magazijnlocaties om inkomende producten meteen naar specifieke onderliggende locaties te sturen (bijvoorbeeld specifieke bakken, rekken)."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Add and customize route operations to process product moves in your "
"warehouse(s): e.g. unload > quality control > stock for incoming products, "
"pick > pack > ship for outgoing products. You can also set putaway "
"strategies on warehouse locations in order to send incoming products into "
"specific child locations straight away (e.g. specific bins, racks)."
msgstr ""
"Voeg routes toe en pas ze aan om productverplaatsingen in je magazijn(en) te verwerken: bijv. Ontvangst > Kwaliteitscontrole > Voorraad voor inkomende producten, picken > verpakken > verzenden voor uitgaande producten.\n"
"Je kunt ook opslagstrategieën instellen op magazijnlocaties om inkomende producten meteen naar specifieke onderliggende locaties te sturen (bijvoorbeeld specifieke bakken, rekken)."

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/fields/stock_move_line_x2_many_field.js:0
msgid "Add line: %s"
msgstr "Regel toevoegen: %s"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Add quality checks to your transfer operations"
msgstr "Voeg kwaliteitscontroles toe aan je overdrachten"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Additional Info"
msgstr "Aanvullende informatie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__comment
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Additional Information"
msgstr "Aanvullende informatie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot_report__address
#: model:ir.model.fields,field_description:stock.field_stock_picking__warehouse_address_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__partner_id
#: model_terms:ir.ui.view,arch_db:stock.search_customer_lot_filter
msgid "Address"
msgstr "Adres"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__partner_address_id
msgid "Address where goods should be delivered. Optional."
msgstr "Adres waar de goederen moeten worden geleverd. Optioneel."

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_adjustments
msgid "Adjustments"
msgstr "Aanpassingen"

#. module: stock
#: model:res.groups,name:stock.group_stock_manager
msgid "Administrator"
msgstr "Beheerder"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Advanced Scheduling"
msgstr "Geavanceerde planning"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__procure_method__make_to_order
msgid "Advanced: Apply Procurement Rules"
msgstr "Geavanceerd: Aanvulregels toepassen"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__search_date_category__after
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "After"
msgstr "Na"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "All"
msgstr "Alle"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "All Categories"
msgstr "Alle categorieën"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_form
msgid "All Companies"
msgstr "Alle bedrijven"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "All Products"
msgstr "Alle producten"

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_picking_action_picking_type
msgid "All Transfers"
msgstr "Alle overdrachten"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/views/search/stock_report_search_panel.xml:0
msgid "All Warehouses"
msgstr "Alle magazijnen"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__procurement_group__move_type__one
msgid "All at once"
msgstr "Lever alles tegelijk"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"All our contractual relations will be governed exclusively by United States "
"law."
msgstr ""
"Al onze contractuele relaties worden uitsluitend beheerst door het recht van"
" de Verenigde Staten."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__returned_move_ids
msgid "All returned moves"
msgstr "Alle retourverplaatsingen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__allow_new_product
msgid "Allow New Product"
msgstr "Nieuw product toestaan"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_storage_category__allow_new_product__mixed
msgid "Allow mixed products"
msgstr "Gemengde producten toestaan"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__allowed_location_ids
msgid "Allowed Location"
msgstr "Toegestane locatie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__allowed_route_ids
#: model:ir.model.fields,field_description:stock.field_stock_replenish_mixin__allowed_route_ids
msgid "Allowed Route"
msgstr "Toegestane route"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__create_backorder__always
msgid "Always"
msgstr "Altijd"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "Andrwep"
msgstr "Andrwep"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Annual Inventory Day and Month"
msgstr "Dag en maand jaarlijkse voorraadtelling"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_company__annual_inventory_month
#: model:ir.model.fields,field_description:stock.field_res_config_settings__annual_inventory_month
msgid "Annual Inventory Month"
msgstr "Maand jaarlijkse voorraadtelling"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_company__annual_inventory_month
#: model:ir.model.fields,help:stock.field_res_config_settings__annual_inventory_month
msgid ""
"Annual inventory month for products not in a location with a cyclic "
"inventory date. Set to no month if no automatic annual inventory."
msgstr ""
"Maand jaarlijkse voorraadtelling voor producten die zich niet op een locatie"
" met een cyclische voorraaddatum bevinden. Laat leeg als er geen "
"automatische jaarlijkse voorraadtelling is."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_location.py:0
msgid ""
"Another parent/sub replenish location %s exists, if you wish to change it, "
"uncheck it first"
msgstr ""
"Er bestaat nog een bovenliggende/onderliggende aanvullocatie %s, als je deze"
" wilt wijzigen, schakel je deze eerst uit"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "Any Category"
msgstr "Elke categorie"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
msgid "Applicability"
msgstr "Toepasbaarheid"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Applicable On"
msgstr "Toepasbaar op"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_route__packaging_selectable
msgid "Applicable on Packaging"
msgstr "Van toepassing op verpakkingen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_route__product_selectable
msgid "Applicable on Product"
msgstr "Van toepassing op product"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_route__product_categ_selectable
msgid "Applicable on Product Category"
msgstr "Van toepassing op productcategorie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_route__warehouse_selectable
msgid "Applicable on Warehouse"
msgstr "Van toepassing op magazijn"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_change_product_quantity
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
#: model_terms:ir.ui.view,arch_db:stock.view_stock_track_confirmation
msgid "Apply"
msgstr "Toepassen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Apply All"
msgstr "Alles toepassen"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_replenish__route_id
#: model:ir.model.fields,help:stock.field_stock_replenish_mixin__route_id
msgid ""
"Apply specific route for the replenishment instead of product's default "
"routes."
msgstr ""
"Pas een specifieke route toe voor de aanvulling in plaats van de standaard "
"routes van het product."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__4
msgid "April"
msgstr "April"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_warehouse_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_filter
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Archived"
msgstr "Gearchiveerd"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Are you sure you want to cancel this transfer?"
msgstr "Weet je zeker dat je deze verplaatsing wilt annuleren?"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__move_type__direct
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__move_type__direct
msgid "As soon as possible"
msgstr "Zo snel mogelijk"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__create_backorder__ask
msgid "Ask"
msgstr "Vraag"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/components/reception_report_line/stock_reception_report_line.xml:0
msgid "Assign"
msgstr "Toewijzen"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/components/reception_report_main/stock_reception_report_main.xml:0
#: code:addons/stock/static/src/components/reception_report_table/stock_reception_report_table.xml:0
#: code:addons/stock/static/src/xml/report_stock_reception.xml:0
msgid "Assign All"
msgstr "Alles toewijzen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__owner_id
msgid "Assign Owner"
msgstr "Eigenaar toewijzen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Assigned Moves"
msgstr "Toegewezen verplaatsingen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__user_id
msgid "Assigned To"
msgstr "Toegewezen aan"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__reservation_method__at_confirm
msgid "At Confirmation"
msgstr "Bij bevestiging"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
msgid "At Customer"
msgstr "Bij de klant"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__message_attachment_count
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_attachment_count
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_attachment_count
msgid "Attachment Count"
msgstr "Aantal bijlagen"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_attribute_action
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Attributes"
msgstr "Kenmerken"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__8
msgid "August"
msgstr "Augustus"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse_orderpoint__trigger__auto
msgid "Auto"
msgstr "Auto"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__auto_print_delivery_slip
msgid "Auto Print Delivery Slip"
msgstr "Leveringsbon automatisch afdrukken"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__auto_print_lot_labels
msgid "Auto Print Lot/SN Labels"
msgstr "Automatisch afdrukken van lot-/SN-labels"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__auto_print_package_label
msgid "Auto Print Package Label"
msgstr "Verpakkingslabel automatisch afdrukken"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__auto_print_packages
msgid "Auto Print Packages"
msgstr "Automatische afdrukken verpakkingen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__auto_print_product_labels
msgid "Auto Print Product Labels"
msgstr "Productlabels automatisch afdrukken"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__auto_print_reception_report
msgid "Auto Print Reception Report"
msgstr "Ontvangstrapport automatisch afdrukken"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__auto_print_reception_report_labels
msgid "Auto Print Reception Report Labels"
msgstr "Ontvangstrapportlabels automatisch afdrukken"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__auto_print_return_slip
msgid "Auto Print Return Slip"
msgstr "Retourbon automatisch afdrukken"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid "Automate"
msgstr "Automatiseren"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__auto
msgid "Automatic Move"
msgstr "Automatische verplaatsing"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__auto__transparent
msgid "Automatic No Step Added"
msgstr "Automatisch, geen stap toegevoegd"

#. module: stock
#. odoo-javascript
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/static/src/widgets/forecast_widget.xml:0
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__assigned
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__products_availability_state__available
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_simple
msgid "Available"
msgstr "Beschikbaar"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_search_form_view_stock
#: model_terms:ir.ui.view,arch_db:stock.stock_product_search_form_view
msgid "Available Products"
msgstr "Beschikbare producten"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__available_quantity
#: model_terms:ir.ui.view,arch_db:stock.replenishment_option_tree_view
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form_editable
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Available Quantity"
msgstr "Beschikbare aantal"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid ""
"Available quantity should be set to zero before changing inventory tracking"
msgstr ""
"De beschikbare aantal moet op nul worden gezet voordat je de "
"voorraadtracering verandert"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__backorder_id
msgid "Back Order of"
msgstr "Backorder van"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__backorder_ids
msgid "Back Orders"
msgstr "Backorders"

#. module: stock
#: model:ir.model,name:stock.model_stock_backorder_confirmation
msgid "Backorder Confirmation"
msgstr "Backorder bevestiging"

#. module: stock
#: model:ir.model,name:stock.model_stock_backorder_confirmation_line
msgid "Backorder Confirmation Line"
msgstr "Backorder bevestigingsregel"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__backorder_confirmation_line_ids
msgid "Backorder Confirmation Lines"
msgstr "Backorder bevestigingsregels"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "Backorder creation"
msgstr "Backorder aanmaak"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_backorder
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Backorders"
msgstr "Backorders"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__barcode
#: model:ir.model.fields,field_description:stock.field_stock_package_type__barcode
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__barcode
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Barcode"
msgstr "Barcode"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "Barcode Demo"
msgstr "Barcode-demo"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_wms_barcode_nomenclature_all
msgid "Barcode Nomenclatures"
msgstr "Barcode nomenclaturen"

#. module: stock
#: model:ir.model,name:stock.model_barcode_rule
msgid "Barcode Rule"
msgstr "Barcode regel"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_stock_barcode
msgid "Barcode Scanner"
msgstr "Barcodescanner"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__valid_ean
msgid "Barcode is valid EAN"
msgstr "Barcode is geldig EAN"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_stock_picking_batch
msgid "Batch, Wave & Cluster Transfers"
msgstr "Batch-, Wave- & Clusterpickings"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__search_date_category__before
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Before"
msgstr "Eerder"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__reservation_method__by_date
msgid "Before scheduled date"
msgstr "Voor geplande datum"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"Below text serves as a suggestion and doesn’t engage Odoo S.A. "
"responsibility."
msgstr ""
"Onderstaande tekst dient als suggestie en houdt Odoo S.A. niet "
"aansprakelijk."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_partner__picking_warn__block
msgid "Blocking Message"
msgstr "Blokkerend bericht"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "Blocking: %s"
msgstr "Blokkeren: %s"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__quant_ids
msgid "Bulk Content"
msgstr "Bulk inhoud"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__weight_bulk
msgid "Bulk Weight"
msgstr "Bruto gewicht"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_template__tracking__lot
msgid "By Lots"
msgstr "Op partijnummer"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_template__tracking__none
msgid "By Quantity"
msgstr "Op aantal"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_template__tracking__serial
msgid "By Unique Serial Number"
msgstr "Op uniek serienummer"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__procure_method
msgid ""
"By default, the system will take from the stock in the source location and "
"passively wait for availability. The other possibility allows you to "
"directly create a procurement on the source location (and thus ignore its "
"current stock) to gather products. If we want to chain moves and have this "
"one to wait for the previous, this second option should be chosen."
msgstr ""
"Standaard zal het systeem de voorraad nemen van de bronlocatie en passief "
"afwachten voor de beschikbaarheid. De andere mogelijkheid is om direct een "
"aanvulling aan te maken op de bronlocatie (en dus de huidige voorraad te "
"negeren) om producten te verzamelen. Als we verplaatsingen willen koppelen "
"en willen dat deze gekoppelde wacht op de vorige dan dient deze tweede optie"
" gekozen te worden."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__active
msgid ""
"By unchecking the active field, you may hide a location without deleting it."
msgstr ""
"Door het uitvinken van dit actief veld, kun je een locatie verbergen zonder "
"deze te verwijderen."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "COPY"
msgstr "KOPIËREN"

#. module: stock
#: model:product.template,name:stock.product_cable_management_box_product_template
msgid "Cable Management Box"
msgstr "Kabelbeheer box"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_calendar
msgid "Calendar View"
msgstr "Kalenderoverzicht"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Can't find any customer or supplier location."
msgstr "Kan geen klant- of leverancierslocatie vinden."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Can't find any generic route %s."
msgstr "Kan geen algemene route vinden %s."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.lot_label_layout_form_picking
#: model_terms:ir.ui.view,arch_db:stock.picking_label_type_form
#: model_terms:ir.ui.view,arch_db:stock.replenishment_option_warning_view
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quantity_history
#: model_terms:ir.ui.view,arch_db:stock.view_stock_return_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rules_report
msgid "Cancel"
msgstr "Annuleren"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__propagate_cancel
msgid "Cancel Next Move"
msgstr "Annuleer volgende verplaatsing"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__cancel
#: model:ir.model.fields.selection,name:stock.selection__stock_package_level__state__cancel
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__state__cancel
msgid "Cancelled"
msgstr "Geannuleerd"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Cannot move an empty package"
msgstr "Kan een lege verpakking niet verplaatsen"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Cannot remove the location of a non empty package"
msgstr "Kan de locatie van een lege verplakking niet verwijderen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__capacity_ids
msgid "Capacity"
msgstr "Capaciteit"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_form
msgid "Capacity by Package"
msgstr "Capaciteit per verpakking"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_form
msgid "Capacity by Product"
msgstr "Capaciteit per product"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__product_uom_category_id
#: model:ir.model.fields,field_description:stock.field_stock_move__product_uom_category_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__product_uom_category_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__product_uom_category_id
#: model_terms:ir.ui.view,arch_db:stock.product_search_form_view_stock_report
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
msgid "Category"
msgstr "Categorie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__route_from_categ_ids
#: model:ir.model.fields,field_description:stock.field_product_template__route_from_categ_ids
msgid "Category Routes"
msgstr "Categorie routes"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"Certain countries apply withholding at source on the amount of invoices, in "
"accordance with their internal legislation. Any withholding at source will "
"be paid by the client to the tax authorities. Under no circumstances can My "
"Company (Chicago) become involved in costs related to a country's "
"legislation. The amount of the invoice will therefore be due to My Company "
"(Chicago) in its entirety and does not include any costs relating to the "
"legislation of the country in which the client is located."
msgstr ""
"Bepaalde landen passen in overeenstemming met hun interne wetgeving een "
"bronheffing toe op het bedrag van de facturen. Elke bronheffing wordt door "
"de opdrachtgever aan de belastingdienst betaald. My Company (Chicago) kan in"
" geen geval betrokken raken bij kosten die verband houden met de wetgeving "
"van een land. Het bedrag van de factuur is dan ook integraal verschuldigd "
"aan My Company (Chicago) en omvat geen kosten die verband houden met de "
"wetgeving van het land waar de klant zicht bevindt."

#. module: stock
#: model:ir.actions.act_window,name:stock.action_change_product_quantity
#: model:ir.model,name:stock.model_stock_change_product_qty
msgid "Change Product Quantity"
msgstr "Productaantal aanpassen"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid ""
"Changing the Lot/Serial number for move lines with different products is not"
" allowed."
msgstr ""
"Je kunt het partij/serienummer niet wijzigen voor boekingsregels met "
"verschillende producten."

#. module: stock
#. odoo-python
#: code:addons/stock/models/product_strategy.py:0
#: code:addons/stock/models/stock_location.py:0
#: code:addons/stock/models/stock_orderpoint.py:0
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/models/stock_warehouse.py:0
msgid ""
"Changing the company of this record is forbidden at this point, you should "
"rather archive it and create a new one."
msgstr ""
"Het wijzigen van het bedrijf van dit record is op dit moment niet "
"toegestaan, je dient het record te archiveren en een nieuw record aan te "
"maken."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Changing the operation type of this record is forbidden at this point."
msgstr ""
"Het wijzigen van het bewerkingssoort van dit record is op dit moment niet "
"toegestaan."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid "Changing the product is only allowed in 'Draft' state."
msgstr "Het product wijzigen is alleen toegestaan in de 'concept' fase."

#. module: stock
#: model:ir.model.fields,help:stock.field_res_config_settings__barcode_separator
msgid ""
"Character(s) used to separate data contained within an aggregate barcode "
"(i.e. a barcode containing multiple barcode encodings)"
msgstr ""
"Teken(s) gebruikt om gegevens in een geaggregeerde barcode te scheiden "
"(d.w.z. een barcode met meerdere barcodecoderingen)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "Check Availability"
msgstr "Controleer beschikbaarheid"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__has_packages
msgid "Check the existence of destination packages on move lines"
msgstr "Controleer het bestaan van bestemmingspaketten op verplaatsingsregels"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__move_line_exist
msgid "Check the existence of pack operation on the picking"
msgstr "Controleer het bestaan van verpakkingbewerkingen op de picking"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__print_label
msgid ""
"Check this box if you want to generate shipping label in this operation."
msgstr ""
"Vink dit vakje aan als je tijdens deze bewerking een verzendlabel wilt "
"genereren."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__scrap_location
#: model:ir.model.fields,help:stock.field_stock_move__scrapped
msgid ""
"Check this box to allow using this location to put scrapped/damaged goods."
msgstr ""
"Vink deze optie aan om deze locatie te gebruiken voor afgekeurde/beschadigde"
" producten."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/wizard/stock_label_type.py:0
msgid "Choose Labels Layout"
msgstr "Label lay-out kiezen"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Choose Type of Labels To Print"
msgstr "Kies het type labels om af te drukken"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quantity_history__inventory_datetime
#: model:ir.model.fields,help:stock.field_stock_request_count__inventory_date
msgid "Choose a date to get the inventory at that date"
msgstr "Kies een datum om de voorraad van die datum op te halen"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Choose destination location"
msgstr "Kies bestemmingslocatie"

#. module: stock
#: model:ir.model,name:stock.model_lot_label_layout
msgid "Choose the sheet layout to print lot labels"
msgstr "Kies de lay-out van het vel om partijlabels af te drukken"

#. module: stock
#: model:ir.model,name:stock.model_product_label_layout
msgid "Choose the sheet layout to print the labels"
msgstr "Kies de lay-out van het vel om de labels af te drukken"

#. module: stock
#: model:ir.model,name:stock.model_picking_label_type
msgid "Choose whether to print product or lot/sn labels"
msgstr "Kies of je product- of partij/serienummerlabels wilt afdrukken"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quantity_history
msgid "Choose your date"
msgstr "Kies je datum"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Clear"
msgstr "Wissen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.duplicated_sn_warning
#: model_terms:ir.ui.view,arch_db:stock.view_stock_replenishment_info
msgid "Close"
msgstr "Afsluiten"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_putaway_rule__sublocation__closest_location
#: model:product.removal,name:stock.removal_closest
msgid "Closest Location"
msgstr "Dichtstbijzijnde locatie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__color
#: model:ir.model.fields,field_description:stock.field_stock_scrap_reason_tag__color
msgid "Color"
msgstr "Kleur"

#. module: stock
#: model:ir.model,name:stock.model_res_company
msgid "Companies"
msgstr "Bedrijven"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__company_id
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__company_id
#: model:ir.model.fields,field_description:stock.field_stock_location__company_id
#: model:ir.model.fields,field_description:stock.field_stock_lot__company_id
#: model:ir.model.fields,field_description:stock.field_stock_move__company_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__company_id
#: model:ir.model.fields,field_description:stock.field_stock_package_level__company_id
#: model:ir.model.fields,field_description:stock.field_stock_package_type__company_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__company_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__company_id
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__company_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__company_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__company_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__company_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__company_id
#: model:ir.model.fields,field_description:stock.field_stock_route__company_id
#: model:ir.model.fields,field_description:stock.field_stock_rule__company_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__company_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__company_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__company_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__company_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__company_id
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
msgid "Company"
msgstr "Bedrijf"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs"
msgstr "Verzendkosten berekenen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with DHL"
msgstr "Bereken verzendkosten en verstuur met DHL"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with Easypost"
msgstr "Bereken verzendkosten en verzend met Easypost"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with FedEx"
msgstr "Bereken verzendkosten en verstuur met FedEx"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with Sendcloud"
msgstr "Verzendkosten berekenen en verzenden met Sendcloud"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with Shiprocket"
msgstr "Verzendkosten berekenen en verzenden met Shiprocket"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with Starshipit"
msgstr "Bereken verzendkosten en verzend met Starshipit"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with UPS"
msgstr "Verzendkosten berekenen en verzenden met UPS"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with USPS"
msgstr "Bereken verzendkosten en verstuur met USPS"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with bpost"
msgstr "Verzendkosten berekenen en verzenden met bpost"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__reservation_date
msgid "Computes when a move should be reserved"
msgstr "Berekent wanneer een verplaatsing moet worden gereserveerd"

#. module: stock
#: model:ir.model,name:stock.model_res_config_settings
msgid "Config Settings"
msgstr "Configuratie instellingen"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_config_settings
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "Configuration"
msgstr "Configuratie"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.lot_label_layout_form_picking
#: model_terms:ir.ui.view,arch_db:stock.package_level_form_edit_view
#: model_terms:ir.ui.view,arch_db:stock.picking_label_type_form
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_request_count_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_package_destination_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_quant_relocate_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_product_replenish
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quantity_history
msgid "Confirm"
msgstr "Bevestigen"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_package_level__state__confirmed
msgid "Confirmed"
msgstr "Bevestigd"

#. module: stock
#: model:ir.model,name:stock.model_stock_inventory_conflict
msgid "Conflict in Inventory"
msgstr "Conflict in voorraad"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Conflict in Inventory Adjustment"
msgstr "Conflict in voorraadaanpassing"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__quant_to_fix_ids
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Conflicts"
msgstr "Conflicten"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__visibility_days
msgid ""
"Consider product forecast these many days in the future upon product replenishment, set to 0 for just-in-time.\n"
"The value depends on the type of the route (Buy or Manufacture)"
msgstr ""
"Houd rekening met de productprognose voor deze vele dagen in de toekomst bij productaanvulling, ingesteld op 0 voor just-in-time.\n"
"De waarde is afhankelijk van het type route (Kopen of Produceren)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_tracking_owner
msgid "Consignment"
msgstr "Consignatie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__consume_line_ids
msgid "Consume Line"
msgstr "Verbruiksregel"

#. module: stock
#: model:ir.model,name:stock.model_res_partner
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_partner_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__partner_id
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Contact"
msgstr "Contact"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__child_ids
msgid "Contains"
msgstr "Bevat"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Content"
msgstr "Inhoud"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
msgid "Contents"
msgstr "Inhoud"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_warning_reset_view
#: model_terms:ir.ui.view,arch_db:stock.inventory_warning_set_view
msgid "Continue"
msgstr "Doorgaan"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
#: code:addons/stock/static/src/components/reception_report_main/stock_reception_report_main.xml:0
msgid "Control panel buttons"
msgstr "Controlepaneel knoppen"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_replenish__product_uom_category_id
#: model:ir.model.fields,help:stock.field_stock_move__product_uom_category_id
#: model:ir.model.fields,help:stock.field_stock_move_line__product_uom_category_id
#: model:ir.model.fields,help:stock.field_stock_scrap__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"Conversie tussen maateenheden kan alleen plaatsvinden als deze behoren tot "
"dezelfde categorie. De conversie wordt gemaakt op basis van ratio's."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__posx
msgid "Corridor (X)"
msgstr "Rij (X)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_request_count__set_count
msgid "Count"
msgstr "Aantal"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_move_ready
msgid "Count Move Ready"
msgstr "Aantal boekingen beschikbaar"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking
msgid "Count Picking"
msgstr "Aantal pickings"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking_backorders
msgid "Count Picking Backorders"
msgstr "Aantal picking backorders"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking_draft
msgid "Count Picking Draft"
msgstr "Aantal concept pickings"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking_late
msgid "Count Picking Late"
msgstr "Aantal te late pickings"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking_ready
msgid "Count Picking Ready"
msgstr "Aantal pickings beschikbaar"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking_waiting
msgid "Count Picking Waiting"
msgstr "Aantal wachtende pickings"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_inventory
msgid "Count Sheet"
msgstr "Tellijst"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__inventory_quantity
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid "Counted Quantity"
msgstr "Getelde aantal"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Counterpart Locations"
msgstr "Tegenboekinglocatie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__create_backorder
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "Create Backorder"
msgstr "Backorder aanmaken"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Create Backorder?"
msgstr "Maak backorder?"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Create New"
msgstr "Maak nieuw"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_type_use_create_lots
#: model:ir.model.fields,field_description:stock.field_stock_picking__use_create_lots
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__use_create_lots
msgid "Create New Lots/Serial Numbers"
msgstr "Maak nieuwe partijen/serienummers aan"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form
msgid "Create Stock"
msgstr "Voorraad maken"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid ""
"Create a backorder if you expect to process the remaining\n"
"                            products later. Do not create a backorder if you will not\n"
"                            process the remaining products."
msgstr ""
"Maak een backorder als je verwacht om de resterende\n"
"                            producten later te verwerken. Maak geen backorder als je de\n"
"                            resterende producten niet zal verwerken."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_picking_type_action
msgid "Create a new operation type"
msgstr "Maak een nieuwe bewerkingssoort aan"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_package_view
msgid "Create a new package"
msgstr "Maak een nieuwe verpakking aan"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Create customizable worksheets for your quality checks"
msgstr "Maak werkbonnen op maat voor je kwaliteitscontroles"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_putaway_tree
msgid ""
"Create new putaway rules to dispatch automatically specific products to "
"their appropriate destination location upon receptions."
msgstr ""
"Maak nieuwe wegzetregels om automatisch specifieke producten naar de juiste "
"bestemmingslocatie te sturen bij ontvangst."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Create products easily by scanning using"
msgstr "Maak eenvoudig producten aan door te scannen aan de hand van"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_product_stock_view
msgid "Create some storable products to see their stock info in this view."
msgstr ""
"Maak een aantal producten die kunnen worden opgeslagen om hun "
"voorraadinformatie in deze weergave te zien."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_lot_label_layout__create_uid
#: model:ir.model.fields,field_description:stock.field_picking_label_type__create_uid
#: model:ir.model.fields,field_description:stock.field_procurement_group__create_uid
#: model:ir.model.fields,field_description:stock.field_product_removal__create_uid
#: model:ir.model.fields,field_description:stock.field_product_replenish__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_location__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_lot__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_move__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_move_line__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_level__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_type__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_picking__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_request_count__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_route__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_rule__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_scrap__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_scrap_reason_tag__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_track_line__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__create_uid
msgid "Created by"
msgstr "Aangemaakt door"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_lot_label_layout__create_date
#: model:ir.model.fields,field_description:stock.field_picking_label_type__create_date
#: model:ir.model.fields,field_description:stock.field_procurement_group__create_date
#: model:ir.model.fields,field_description:stock.field_product_removal__create_date
#: model:ir.model.fields,field_description:stock.field_product_replenish__create_date
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__create_date
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__create_date
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__create_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__create_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__create_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__create_date
#: model:ir.model.fields,field_description:stock.field_stock_location__create_date
#: model:ir.model.fields,field_description:stock.field_stock_lot__create_date
#: model:ir.model.fields,field_description:stock.field_stock_move__create_date
#: model:ir.model.fields,field_description:stock.field_stock_move_line__create_date
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__create_date
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__create_date
#: model:ir.model.fields,field_description:stock.field_stock_package_level__create_date
#: model:ir.model.fields,field_description:stock.field_stock_package_type__create_date
#: model:ir.model.fields,field_description:stock.field_stock_picking__create_date
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__create_date
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__create_date
#: model:ir.model.fields,field_description:stock.field_stock_quant__create_date
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__create_date
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__create_date
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__create_date
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__create_date
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__create_date
#: model:ir.model.fields,field_description:stock.field_stock_request_count__create_date
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__create_date
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__create_date
#: model:ir.model.fields,field_description:stock.field_stock_route__create_date
#: model:ir.model.fields,field_description:stock.field_stock_rule__create_date
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__create_date
#: model:ir.model.fields,field_description:stock.field_stock_scrap__create_date
#: model:ir.model.fields,field_description:stock.field_stock_scrap_reason_tag__create_date
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__create_date
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__create_date
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__create_date
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__create_date
#: model:ir.model.fields,field_description:stock.field_stock_track_line__create_date
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__create_date
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__create_date
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__create_date
msgid "Created on"
msgstr "Aangemaakt op"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid ""
"Creating a new warehouse will automatically activate the Storage Locations "
"setting"
msgstr ""
"Door een nieuw magazijn aan te maken, wordt automatisch de instelling "
"opslaglocaties geactiveerd"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__date
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Creation Date"
msgstr "Aanmaakdatum"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__date
msgid "Creation Date, usually the time of the order"
msgstr "Aanmaakdatum, normaliter de datum van de order"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
msgid "Creation date"
msgstr "Aanmaakdatum"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__date
msgid ""
"Creation date of this move line until updated due to: quantity being "
"increased, 'picked' status has updated, or move line is done."
msgstr ""
"De aanmaakdatum van deze verhuisregel wordt bijgewerkt tot: het aantal is "
"verhoogd, de status 'gepickt' is bijgewerkt of de verhuisregel is klaar."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Cross Dock"
msgstr "Cross-docking"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__xdock_type_id
msgid "Cross Dock Type"
msgstr "Type cross-docking"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Cross-Dock"
msgstr "Cross-docking"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__crossdock_route_id
msgid "Crossdock Route"
msgstr "Cross-dockroute"

#. module: stock
#: model:ir.actions.act_window,name:stock.location_open_quants
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Current Stock"
msgstr "Huidige voorraad"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__qty_available
msgid ""
"Current quantity of products.\n"
"In a context with a single Stock Location, this includes goods stored at this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"stored in the Stock Location of the Warehouse of this Shop, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""
"Werkelijke aantal. \n"
"Bij gebruik van een enkele voorraadlocatie, omvat dit de goederen opgeslagen in deze locatie, of één van de onderliggende locaties. \n"
"Bij gebruik van een enkel magazijn, omvat dit de goederen die in de voorraadlocatie van dit magazijn zijn opgeslagen, of één van de onderliggende locaties.\n"
"die in de voorraadlocatie van het magazijn van deze winkel zijn opgeslagen, of één van de onderliggende locaties. \n"
"Anders, dit omvat goederen die zijn opgeslagen in alle voorraadlocaties van het type 'intern'."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_label_layout__move_quantity__custom
#: model:ir.model.fields.selection,name:stock.selection__stock_orderpoint_snooze__predefined_date__custom
msgid "Custom"
msgstr "Aangepast"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Customer"
msgstr "Klant"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__sale_delay
#: model:ir.model.fields,field_description:stock.field_product_template__sale_delay
msgid "Customer Lead Time"
msgstr "Levertijd klant"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_partner__property_stock_customer
#: model:ir.model.fields,field_description:stock.field_res_users__property_stock_customer
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__customer
msgid "Customer Location"
msgstr "Klantlocatie"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Customer Locations"
msgstr "Klantlocaties"

#. module: stock
#: model:ir.model,name:stock.model_stock_lot_report
msgid "Customer Lot Report"
msgstr "Rapport klantpartij"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_lot_report
msgid "Customer lots"
msgstr "Klantpartijen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "Customizable Desk"
msgstr "Verstelbaar bureau"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Cyclic Counting"
msgstr "Cyclische telling"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "DEMO_DATE"
msgstr "DEMO_DATE"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "DEMO_ORIGIN_DISPLAY_NAME"
msgstr "DEMO_ORIGIN_DISPLAY_NAME"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "DEMO_PARTNER_NAME"
msgstr "DEMO_PARTNER_NAME"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "DEMO_PRODUCT_DISPLAY_NAME"
msgstr "DEMO_PRODUCT_DISPLAY_NAME"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "DEMO_QUANTITY"
msgstr "DEMO_QUANTITY"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "DEMO_SOURCE_DISPLAY_NAME"
msgstr "DEMO_SOURCE_DISPLAY_NAME"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "DEMO_UOM"
msgstr "DEMO_UOM"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_dhl
msgid "DHL Express Connector"
msgstr "DHL Express Connector"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__date
#: model:ir.model.fields,field_description:stock.field_stock_move_line__date
#: model:ir.model.fields,field_description:stock.field_stock_scrap__date_done
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Date"
msgstr "Datum"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__search_date_category
msgid "Date Category"
msgstr "Datum Categorie"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Date Processing"
msgstr "Verwerkingsdatum"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__date
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Date Scheduled"
msgstr "Geplande datum"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_replenish__date_planned
msgid "Date at which the replenishment should take place."
msgstr "Datum waarop de aanvulling plaats moet vinden."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__date_done
msgid "Date at which the transfer has been processed or cancelled."
msgstr "Datum waarop de overdracht verwerkt of geannuleerd is."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__next_inventory_date
msgid "Date for next planned inventory based on cyclic schedule."
msgstr "Datum voor volgende geplande telling op basis van cyclisch schema."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__date_done
msgid "Date of Transfer"
msgstr "Datum van overdracht"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__last_inventory_date
msgid "Date of the last inventory at this location."
msgstr "Datum van de laatste voorraadtelling van deze locatie."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__reservation_date
msgid "Date to Reserve"
msgstr "Datum om te reserveren"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Day and month that annual inventory counts should occur."
msgstr "Dag en maand waarop jaarlijkse voorraadtelling moet plaatsvinden."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_company__annual_inventory_day
#: model:ir.model.fields,field_description:stock.field_res_config_settings__annual_inventory_day
msgid "Day of the month"
msgstr "Dag van de maand"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_company__annual_inventory_day
#: model:ir.model.fields,help:stock.field_res_config_settings__annual_inventory_day
msgid ""
"Day of the month when the annual inventory should occur. If zero or negative, then the first day of the month will be selected instead.\n"
"        If greater than the last day of a month, then the last day of the month will be selected instead."
msgstr ""
"Dag van de maand waarop de jaarlijkse voorraadtelling moet plaatsvinden. Indien nul of negatief, dan wordt in plaats daarvan de eerste dag van de maand geselecteerd.\n"
"        Indien groter dan de laatste dag van een maand, wordt in plaats daarvan de laatste dag van de maand geselecteerd."

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
msgid "Day(s)"
msgstr "Dag(en)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__reservation_days_before
msgid "Days"
msgstr "Dagen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__days_to_order
msgid "Days To Order"
msgstr "Dagen om te bestellen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__reservation_days_before_priority
msgid "Days when starred"
msgstr "Dagen met ster"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__date_deadline
#: model:ir.model.fields,field_description:stock.field_stock_picking__date_deadline
msgid "Deadline"
msgstr "Deadline"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Deadline exceed or/and by the scheduled"
msgstr "Deadline overschrijden of/en door de geplande"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "Deadline updated due to delay on %s"
msgstr "Deadline bijgewerkt wegens vertraging op %s"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__12
msgid "December"
msgstr "December"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_return_slip
msgid "Default Barcode Name"
msgstr "Standaard barccodenaam"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_return_slip
msgid "Default Name"
msgstr "Standaardnaam"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_return_slip
msgid "Default OBTRETU Barcode"
msgstr "Standaard OBTRETU-barcode"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_return_slip
msgid "Default Return Name"
msgstr "Standaard retournaam"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__reception_steps
msgid "Default incoming route to follow"
msgstr "Standaard te volgen inkomende route"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__delivery_steps
msgid "Default outgoing route to follow"
msgstr "Standaard te volgen uitgaande route"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_replenish__forecast_uom_id
#: model:ir.model.fields,help:stock.field_stock_change_product_qty__product_uom_id
#: model:ir.model.fields,help:stock.field_stock_lot__product_uom_id
#: model:ir.model.fields,help:stock.field_stock_quant__product_uom_id
#: model:ir.model.fields,help:stock.field_stock_return_picking_line__uom_id
#: model:ir.model.fields,help:stock.field_stock_storage_category_capacity__product_uom_id
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__product_uom
msgid "Default unit of measure used for all stock operations."
msgstr "Standaard maateenheid gebruikt voor alle voorraadbewegingen."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__procure_method__make_to_stock
msgid "Default: Take From Stock"
msgstr "Standaard: Neem uit voorraad"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__route_ids
msgid "Defaults routes through the warehouse"
msgstr "Standaard routes door het magazijn"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_orderpoint
msgid ""
"Define a minimum stock rule so that Odoo automatically creates requests for "
"quotations or confirmed manufacturing orders to resupply your stock."
msgstr ""
"Definieer een minimumvoorraadregel zodat Odoo automatisch offerteaanvragen "
"of bevestigde productieorders aanmakt om je voorraad te herbevoorraden."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_warehouse_form
msgid "Define a new warehouse"
msgstr "Definieer een nieuw magazijn"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_location_form
msgid ""
"Define your locations to reflect your warehouse structure and\n"
"            organization. Odoo is able to manage physical locations\n"
"            (warehouses, shelves, bin, etc), partner locations (customers,\n"
"            vendors) and virtual locations which are the counterpart of\n"
"            the stock operations like the manufacturing orders\n"
"            consumptions, inventories, etc."
msgstr ""
"Definieer je locaties om je magazijnstructuur en organisatie\n"
"te reflecteren. Odoo kan fysieke locaties beheren\n"
"(magazijnen, planken, bakken, enz), contactlocaties (klanten,\n"
"leveranciers) en virtuele locaties die de tegenhanger zijn van de\n"
"voorraadbewerkingen zoals productieorders\n"
"consumpties, voorraden enz."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__removal_strategy_id
msgid ""
"Defines the default method used for suggesting the exact location (shelf) where to take the products from, which lot etc. for this location. This method can be enforced at the product category level, and a fallback is made on the parent locations if none is set here.\n"
"\n"
"FIFO: products/lots that were stocked first will be moved out first.\n"
"LIFO: products/lots that were stocked last will be moved out first.\n"
"Closet location: products/lots closest to the target location will be moved out first.\n"
"FEFO: products/lots with the closest removal date will be moved out first (the availability of this method depends on the \"Expiration Dates\" setting)."
msgstr ""
"Definieert de standaardmethode die wordt gebruikt voor het voorstellen van de exacte locatie (schap) waar de producten vandaan moeten worden gehaald, welke partij enz. voor deze locatie. Deze methode kan worden afgedwongen op productcategorieniveau en er wordt teruggevallen op de bovenliggende locaties als hier geen is ingesteld.\n"
"\n"
"FIFO: producten/partijen die het eerst op voorraad waren, gaan als eerste de deur uit.\n"
"LIFO: producten/partijen die het laatst op voorraad waren, gaan als eerste de deur uit.\n"
"Dichtstbijzijnde locatie: producten/partijen die zich het dichtst bij de doellocatie bevinden, worden als eerste verplaatst.\n"
"FEFO: producten/partijen met de dichtstbijzijnde verwijderdatum worden als eerste verplaatst (de beschikbaarheid van deze methode hangt af van de instelling \"Vervaldatums\")."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__delay_alert_date
#: model:ir.model.fields,field_description:stock.field_stock_picking__delay_alert_date
msgid "Delay Alert Date"
msgstr "Uitstel waarschuwingsdatum"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
msgid "Delay on %s"
msgstr "Vertraging op %s"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse__delivery_steps__ship_only
msgid "Deliver (1 step)"
msgstr "Afleveren (1 stap)"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Deliver in 1 step (ship)"
msgstr "Leveren in 1 stap (verzenden)"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Deliver in 2 steps (pick + ship)"
msgstr "Leveren in 2 stappen (picken & verzenden)"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Deliver in 3 steps (pick + pack + ship)"
msgstr "Leveren in 3 stappen (picken & verpakken & verzenden)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "Delivered"
msgstr "Geleverd"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid "Delivered Qty"
msgstr "Geleverde hoeveelheid"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_outgoing
#: model:ir.ui.menu,name:stock.out_picking
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Deliveries"
msgstr "Leveringen"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__code__outgoing
#: model:ir.ui.menu,name:stock.menu_delivery
msgid "Delivery"
msgstr "Levering"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Delivery Address"
msgstr "Afleveradres"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot_report__delivery_date
#: model_terms:ir.ui.view,arch_db:stock.search_customer_lot_filter
msgid "Delivery Date"
msgstr "Leverdatum"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery
msgid "Delivery Methods"
msgstr "Verzendwijzes"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
#: model:stock.picking.type,name:stock.picking_type_out
msgid "Delivery Orders"
msgstr "Leveringen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__delivery_route_id
msgid "Delivery Route"
msgstr "Leveringsroute"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_delivery
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Delivery Slip"
msgstr "Leveringsbon"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__move_type
msgid "Delivery Type"
msgstr "Leveringstype"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.search_customer_lot_filter
msgid "Delivery date"
msgstr "Bezorgdatum"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__sale_delay
#: model:ir.model.fields,help:stock.field_product_template__sale_delay
msgid ""
"Delivery lead time, in days. It's the number of days, promised to the "
"customer, between the confirmation of the sales order and the delivery."
msgstr ""
"Levertijd, in dagen. Het is het aantal dagen, beloofd aan de klant, tussen "
"de bevestiging van de verkooporder en de levering."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__delivery_count
msgid "Delivery order count"
msgstr "Aantal leveringen"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_lot.py:0
msgid "Delivery orders of %s"
msgstr "Leveringen van %s"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__product_uom_qty
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_move_tree
msgid "Demand"
msgstr "Gevraagd"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_return_slip
msgid "Demo Address and Name"
msgstr "Demo-adres en naam"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "Demo Display Name"
msgstr "Weergavenaam demo"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_lot_label
msgid "Demo Lot/SN"
msgstr "Demokavel/SN"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_lot_label
msgid "Demo Product"
msgstr "Demoproduct"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_packaging__route_ids
msgid ""
"Depending on the modules installed, this will allow you to define the route "
"of the product in this packaging: whether it will be bought, manufactured, "
"replenished on order, etc."
msgstr ""
"Afhankelijk van de geïnstalleerde modules, kun je hiermee de route van het "
"product in deze verpakking bepalen: of het wordt gekocht, geproduceerd, op "
"order wordt aangevuld, enz."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__route_ids
#: model:ir.model.fields,help:stock.field_product_template__route_ids
msgid ""
"Depending on the modules installed, this will allow you to define the route "
"of the product: whether it will be bought, manufactured, replenished on "
"order, etc."
msgstr ""
"Afhankelijk van de geïnstalleerde modules geeft je dit de mogelijkheid om de"
" route van het product aan te geven: waar het gekocht worden, gemaakt wordt,"
" aangevuld wordt op de order, enz."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__note
#: model:ir.model.fields,field_description:stock.field_stock_move__name
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid "Description"
msgstr "Omschrijving"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Description for Delivery Orders"
msgstr "Omschrijving voor leveringen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Description for Internal Transfers"
msgstr "Omschrijving voor interne verplaatsingen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Description for Receipts"
msgstr "Omschrijving voor ontvangsten"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__description_picking
msgid "Description of Picking"
msgstr "Omschrijving van de picking"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__description_pickingout
#: model:ir.model.fields,field_description:stock.field_product_template__description_pickingout
msgid "Description on Delivery Orders"
msgstr "Omschrijving voor leveringen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__description_picking
#: model:ir.model.fields,field_description:stock.field_product_template__description_picking
msgid "Description on Picking"
msgstr "Omschrijving op picking"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__description_pickingin
#: model:ir.model.fields,field_description:stock.field_product_template__description_pickingin
msgid "Description on Receptions"
msgstr "Omschrijving op ontvangsten"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "Description on transfer"
msgstr "Omschrijving op overdracht"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__description_picking
msgid "Description picking"
msgstr "Omschrijving picking"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__dest_location_id
msgid "Dest Location"
msgstr "Beste locatie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__dest_package_id
msgid "Dest Package"
msgstr "Bestemingsverpakking"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__dest_package_id_domain
msgid "Dest Package Id Domain"
msgstr "Bestemmingsverpakking-ID-domein"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__partner_id
msgid "Destination Address "
msgstr "Bestemmingsadres "

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_location_dest_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__location_dest_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__default_location_dest_id
#: model:ir.model.fields,field_description:stock.field_stock_rule__location_dest_id
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_filter
msgid "Destination Location"
msgstr "Bestemmingslocatie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__location_dest_usage
#: model:ir.model.fields,field_description:stock.field_stock_move_line__location_dest_usage
msgid "Destination Location Type"
msgstr "Bestemmingslocatie soort"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Destination Location:"
msgstr "Bestemmingslocatie:"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__move_dest_ids
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Destination Moves"
msgstr "Bestemming verplaatsingen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__result_package_id
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
msgid "Destination Package"
msgstr "Bestemmingsverpakking"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Destination Package:"
msgstr "Bestemmingsverpakking:"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__location_dest_id
msgid "Destination location"
msgstr "Bestemmingslocatie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__location_dest_from_rule
msgid "Destination location origin from rule"
msgstr "Bestemmingslocatie van regel"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__route_ids
msgid "Destination route"
msgstr "Bestemmingsroute"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
#: code:addons/stock/models/stock_picking.py:0
msgid "Detailed Operations"
msgstr "Gedetailleerde bewerkingen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__show_details_visible
msgid "Details Visible"
msgstr "Details zichtbaar"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__inventory_diff_quantity
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Difference"
msgstr "Verschil"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:stock.inventory_warning_reset_view
#: model_terms:ir.ui.view,arch_db:stock.inventory_warning_set_view
#: model_terms:ir.ui.view,arch_db:stock.package_level_form_edit_view
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_adjustment_name_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_request_count_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_package_destination_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_quant_relocate_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view2
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_scrap_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
#: model_terms:ir.ui.view,arch_db:stock.view_change_product_quantity
#: model_terms:ir.ui.view,arch_db:stock.view_product_replenish
#: model_terms:ir.ui.view,arch_db:stock.view_stock_orderpoint_snooze
#: model_terms:ir.ui.view,arch_db:stock.view_stock_track_confirmation
msgid "Discard"
msgstr "Negeren"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid "Discard and manually resolve the conflict"
msgstr "Het conflict negeren en handmatig oplossen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_stock_fleet
msgid "Dispatch Management System"
msgstr "Verzendbeheersysteem"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__display_assign_serial
msgid "Display Assign Serial"
msgstr "Serienummer toewijzen weergeven"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__display_complete
msgid "Display Complete"
msgstr "Voltooid weergeven"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__display_import_lot
msgid "Display Import Lot"
msgstr "Toon import partij"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_lot_on_delivery_slip
msgid "Display Lots & Serial Numbers on Delivery Slips"
msgstr "Partij & serienummers weergeven op leveringsbonnen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_lot_label_layout__display_name
#: model:ir.model.fields,field_description:stock.field_picking_label_type__display_name
#: model:ir.model.fields,field_description:stock.field_procurement_group__display_name
#: model:ir.model.fields,field_description:stock.field_product_removal__display_name
#: model:ir.model.fields,field_description:stock.field_product_replenish__display_name
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__display_name
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__display_name
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__display_name
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__display_name
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__display_name
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__display_name
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__display_name
#: model:ir.model.fields,field_description:stock.field_stock_location__display_name
#: model:ir.model.fields,field_description:stock.field_stock_lot__display_name
#: model:ir.model.fields,field_description:stock.field_stock_lot_report__display_name
#: model:ir.model.fields,field_description:stock.field_stock_move__display_name
#: model:ir.model.fields,field_description:stock.field_stock_move_line__display_name
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__display_name
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__display_name
#: model:ir.model.fields,field_description:stock.field_stock_package_level__display_name
#: model:ir.model.fields,field_description:stock.field_stock_package_type__display_name
#: model:ir.model.fields,field_description:stock.field_stock_picking__display_name
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__display_name
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__display_name
#: model:ir.model.fields,field_description:stock.field_stock_quant__display_name
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__display_name
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__display_name
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__display_name
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__display_name
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__display_name
#: model:ir.model.fields,field_description:stock.field_stock_request_count__display_name
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__display_name
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__display_name
#: model:ir.model.fields,field_description:stock.field_stock_route__display_name
#: model:ir.model.fields,field_description:stock.field_stock_rule__display_name
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__display_name
#: model:ir.model.fields,field_description:stock.field_stock_scrap__display_name
#: model:ir.model.fields,field_description:stock.field_stock_scrap_reason_tag__display_name
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__display_name
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__display_name
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__display_name
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__display_name
#: model:ir.model.fields,field_description:stock.field_stock_track_line__display_name
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__display_name
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__display_name
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__display_name
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "Display Name"
msgstr "Schermnaam"

#. module: stock
#: model:res.groups,name:stock.group_lot_on_delivery_slip
msgid "Display Serial & Lot Number in Delivery Slips"
msgstr "Serie & partijnummer weergeven op leveringsbonnen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.package_level_tree_view_picking
msgid "Display package content"
msgstr "Toon verpakkingsinhoud"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_quant_package__package_use__disposable
msgid "Disposable Box"
msgstr "Wegwerpdoos"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_scrap_form_view
msgid "Do you confirm you want to scrap"
msgstr "Bevestig je dat je wilt afkeuren"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Documentation"
msgstr "Documentatie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_level__is_done
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__done
#: model:ir.model.fields.selection,name:stock.selection__stock_package_level__state__done
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__state__done
#: model:ir.model.fields.selection,name:stock.selection__stock_scrap__state__done
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Done"
msgstr "Gereed"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree
msgid "Done By"
msgstr "Gedaan door"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__product_packaging_quantity
msgid "Done Packaging Quantity"
msgstr "Verpakkingsaantal voltooid"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_package_level__state__draft
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__state__draft
#: model:ir.model.fields.selection,name:stock.selection__stock_scrap__state__draft
#: model_terms:ir.ui.view,arch_db:stock.report_picking
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Draft"
msgstr "Concept"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Draft Moves"
msgstr "Concept verplaatsingen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_stock_dropshipping
msgid "Dropshipping"
msgstr "Dropship"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid ""
"Due to receipts scheduled in the future, you might end up with "
"excessive stock . Check the Forecasted Report  before reordering"
msgstr ""
"Door in de toekomst geplande ontvangsten kan je een buitensporige voorraad "
"hebben. Controleer het Prognoserapport voor het aanvullen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.duplicated_sn_warning
msgid "Duplicated SN Warning"
msgstr "Waarschuwing voor dubbele SN"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__sn_duplicated
msgid "Duplicated Serial Number"
msgstr "Dubbel serienummer"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__product_label_format__dymo
msgid "Dymo"
msgstr "Dymo"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_easypost
msgid "Easypost Connector"
msgstr "Easypost connector"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_orderpoint.py:0
msgid "Edit Product"
msgstr "Product bewerken"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid ""
"Editing quantities in an Inventory Adjustment location is forbidden,those "
"locations are used as counterpart when correcting the quantities."
msgstr ""
"Het bewerken van hoeveelheden in een voorraadaanpassingslocatie is niet "
"toegestaan, deze locaties worden gebruikt als tegenhanger bij het corrigeren"
" van de hoeveelheden."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "Effective Date"
msgstr "Boekdatum"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Email Confirmation"
msgstr "E-mail configuratie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_company__stock_move_email_validation
#: model:ir.model.fields,field_description:stock.field_res_config_settings__stock_move_email_validation
msgid "Email Confirmation picking"
msgstr "E-mail bij bevestigen picking"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_company__stock_mail_confirmation_template_id
msgid "Email Template confirmation picking"
msgstr "E-mailsjabloon bij bevestigen picking"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_company__stock_mail_confirmation_template_id
msgid "Email sent to the customer once the order is done."
msgstr "E-mail verzonden naar de klant zodra de order is voltooid."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Empty Locations"
msgstr "Lege locaties"

#. module: stock
#: model_terms:digest.tip,tip_description:stock.digest_tip_stock_0
msgid ""
"Enjoy a quick-paced experience with the Odoo barcode app. It is blazing fast"
" and works even without a stable internet connection. It supports all flows:"
" inventory adjustments, batch picking, moving lots or pallets, low inventory"
" checks, etc. Go to the \"Apps\" menu to activate the barcode interface."
msgstr ""
"Geniet van een snelle ervaring met de Odoo barcode-app. Het is razendsnel en"
" werkt zelfs zonder een stabiele internetverbinding. Het ondersteunt alle "
"stromen: voorraadaanpassingen, batches, verplaatsen van partijen of pallets,"
" lage voorraadcontroles, enz. Ga naar het menu \"Apps\" om de barcode-"
"interface te activeren."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__tracking
#: model:ir.model.fields,help:stock.field_product_template__tracking
#: model:ir.model.fields,help:stock.field_stock_move__has_tracking
#: model:ir.model.fields,help:stock.field_stock_move_line__tracking
#: model:ir.model.fields,help:stock.field_stock_quant__tracking
#: model:ir.model.fields,help:stock.field_stock_scrap__tracking
#: model:ir.model.fields,help:stock.field_stock_track_line__tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr ""
"Zorg voor de traceerbaarheid van een product dat kan worden opgeslagen in je"
" magazijn."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_location_form
msgid ""
"Every stock operation in Odoo moves the products from one\n"
"            location to another one.  For instance, if you receive products\n"
"            from a vendor, Odoo will move products from the Vendor\n"
"            location to the Stock location. Each report can be performed on\n"
"            physical, partner or virtual locations."
msgstr ""
"Elke voorraadbewerking in Odoo verplaatst het product van een locatie naar een \n"
"andere. Bijvoorbeeld, als je producten ontvangt van een leverancier, zal Odoo de \n"
"producten verplaatsen van de leverancierslocatie naar de voorraadlocatie. Elk rapport\n"
"kan toegepast worden op fysieke, contact of virtuele locaties."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid "Exception(s) occurred on the picking"
msgstr "Foutmelding(en) opgetreden in de picking"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid "Exception(s):"
msgstr "Foutmelding(en):"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/forecast_widget.xml:0
msgid "Exp"
msgstr "Exp"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Exp %s"
msgstr "Verwacht %s"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__products_availability_state__expected
msgid "Expected"
msgstr "Verwacht"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/components/reception_report_table/stock_reception_report_table.xml:0
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "Expected Delivery:"
msgstr "Verwachte levering:"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_product_expiry
msgid "Expiration Dates"
msgstr "Vervaldatums"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "External note..."
msgstr "Externe notitie..."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_removal__method
msgid "FIFO, LIFO..."
msgstr "FIFO, LIFO..."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__is_favorite
msgid "Favorite"
msgstr "Favoriet"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__favorite_user_ids
msgid "Favorite User"
msgstr "Favoriete gebruiker"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
msgid "Favorites"
msgstr "Favorieten"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__2
msgid "February"
msgstr "Februari"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_fedex
msgid "FedEx Connector"
msgstr "FedEx connector"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__filtered_location
msgid "Filtered Location"
msgstr "Gefilterde locatie"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
msgid "Filters"
msgstr "Filters"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__location_final_id
msgid "Final Location"
msgstr "Definitieve locatie"

#. module: stock
#: model:product.removal,name:stock.removal_fifo
msgid "First In First Out (FIFO)"
msgstr "First In First Out (FIFO)"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "First Lot Number"
msgstr "Eerste partijnummer"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__next_serial
msgid "First SN/Lot"
msgstr "Eerste SN/partijnummer"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "First Serial Number"
msgstr "Eerste serienummer"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__group_propagation_option__fixed
msgid "Fixed"
msgstr "Vast"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__group_id
msgid "Fixed Procurement Group"
msgstr "Vaste aanvulgroep"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__message_follower_ids
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_follower_ids
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_follower_ids
msgid "Followers"
msgstr "Volgers"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__message_partner_ids
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_partner_ids
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_partner_ids
msgid "Followers (Partners)"
msgstr "Volgers (Contacten)"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_lot__activity_type_icon
#: model:ir.model.fields,help:stock.field_stock_picking__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesome icoon bijv. fa-tasks"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_category__removal_strategy_id
msgid "Force Removal Strategy"
msgstr "Forceer verwijderingsstrategie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__qty_forecast
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Forecast"
msgstr "Virtueel"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__forecast_availability
msgid "Forecast Availability"
msgstr "Verwachte beschikbaarheid"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
msgid "Forecast Description"
msgstr "Prognoseomschrijving"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid "Forecast Report"
msgstr "Prognoserapport"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__virtual_available
msgid ""
"Forecast quantity (computed as Quantity On Hand - Outgoing + Incoming)\n"
"In a context with a single Stock Location, this includes goods stored in this location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""
"Verwachte aantal (berekend als: Fysiek beschikbare voorraad - Uitgaand + Inkomend). \n"
"Bij gebruik van een enkele voorraadlocatie, omvat dit de goederen opgeslagen in deze locatie, of één van de onderliggende locaties. \n"
"Bij gebruik van een enkel magazijn, omvat dit de goederen die in de voorraadlocatie van dit magazijn zijn opgeslagen, of één van de onderliggende locaties. \n"
"Anders, omvat dit goederen die zijn opgeslagen in eender welke voorraadlocatie van het type 'intern'."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__free_qty
msgid ""
"Forecast quantity (computed as Quantity On Hand - reserved quantity)\n"
"In a context with a single Stock Location, this includes goods stored in this location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""
"Verwachte aantal (berekend als: Werkelijke aantal - gereserveerde aantal). \n"
"Bij gebruik van een enkele voorraadlocatie, omvat dit de goederen opgeslagen in deze locatie, of één van de onderliggende locaties. \n"
"Bij gebruik van een enkel magazijn, omvat dit de goederen die bij de locatie voorraadlocatie van dit magazijn zijn opgeslagen, of één van de onderliggende locaties. \n"
"Anders, dit omvat goederen die zijn opgeslagen op alle voorraadlocaties van het type 'intern'."

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_header.xml:0
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_product_template_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_product_tree
msgid "Forecasted"
msgstr "Gepland"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
msgid "Forecasted Date"
msgstr "Verwachte datum"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
msgid "Forecasted Date + Visibility Days"
msgstr "Verwachte datum + zichtbare dagen"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__report_stock_quantity__state__out
msgid "Forecasted Deliveries"
msgstr "Verwachte leveringen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__forecast_expected_date
msgid "Forecasted Expected date"
msgstr "Verwachte leveringsdatum"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "Forecasted Inventory"
msgstr "Verwachte voorraad"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
#: model:ir.model.fields,field_description:stock.field_product_product__virtual_available
#: model:ir.model.fields,field_description:stock.field_product_replenish__forecasted_quantity
#: model:ir.model.fields,field_description:stock.field_product_template__virtual_available
#: model:ir.model.fields,field_description:stock.field_stock_move__availability
msgid "Forecasted Quantity"
msgstr "Verwachte aantal"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__report_stock_quantity__state__in
msgid "Forecasted Receipts"
msgstr "Verwachte ontvangsten"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/stock_forecasted.js:0
#: code:addons/stock/static/src/widgets/forecast_widget.xml:0
#: model:ir.actions.client,name:stock.stock_forecasted_product_product_action
#: model:ir.actions.client,name:stock.stock_forecasted_product_template_action
msgid "Forecasted Report"
msgstr "Prognoserapport"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__report_stock_quantity__state__forecast
msgid "Forecasted Stock"
msgstr "Voorspelde voorraad"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__forecast_weight
msgid "Forecasted Weight"
msgstr "Verwacht gewicht"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "Forecasted with Pending"
msgstr "Virtueel en wachtend"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_lot_label_layout__print_format
#: model:ir.model.fields,field_description:stock.field_product_label_layout__print_format
msgid "Format"
msgstr "Formatteer"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__free_qty
msgid "Free Qty"
msgstr "Virtuele hvh"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "Free Stock"
msgstr "Virtuele voorraad"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "Free Stock in Transit"
msgstr "Virtuele voorraad in transit"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__free_qty
msgid "Free To Use Quantity "
msgstr "Vrije voorraad "

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
msgid "Free to Use"
msgstr "Virtueel beschikbaar"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
#: model:ir.model.fields,field_description:stock.field_stock_move_line__location_id
#: model:ir.model.fields,field_description:stock.field_stock_package_level__location_id
#: model_terms:ir.ui.view,arch_db:stock.report_picking
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "From"
msgstr "Van"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__owner_id
msgid "From Owner"
msgstr "Van eigenaar"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__complete_name
msgid "Full Location Name"
msgstr "Volledige locatienaam"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Future Activities"
msgstr "Toekomstige activiteiten"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid "Future Deliveries"
msgstr "Toekomstige leveringen"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid "Future P&L"
msgstr "Toekomstige W&V"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid "Future Productions"
msgstr "Toekomstige productie"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid "Future Receipts"
msgstr "Toekomstige ontvangsten"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "General"
msgstr "Algemeen"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "Generate"
msgstr "Genereren"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/generate_serial.js:0
msgid "Generate Lot numbers"
msgstr "Partijnummers genereren"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/generate_serial.js:0
msgid "Generate Serial numbers"
msgstr "Serienummers genereren"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "Generate Serials/Lots"
msgstr "Genereer serienummer-/partijnummers"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__print_label
msgid "Generate Shipping Labels"
msgstr "Verzendlabels genereren"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Get a full traceability from vendors to customers"
msgstr "Krijg een volledige tracering van leveranciers naar klanten"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Get informative or blocking warnings on partners"
msgstr "Krijg informatieve of blokkerende waarschuwingen op contacten"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_putaway_rule__sequence
msgid ""
"Give to the more specialized category, a higher priority to have them in top"
" of the list."
msgstr ""
"Geef de meer specifieke categorie een hogere prioriteit om deze bovenaan de "
"lijst te krijgen."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__sequence
msgid "Gives the sequence of this line when displaying the warehouses."
msgstr ""
"Geeft de volgorde van deze regel weer wanneer de magazijnen worden "
"weergegeven."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Go to Warehouses"
msgstr "Ga naar magazijnen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.search_customer_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Group By"
msgstr "Groeperen op"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_filter
msgid "Group by..."
msgstr "Groeperen op..."

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/multi_print.js:0
msgid "HTML reports cannot be auto-printed, skipping report: %s"
msgstr ""
"HTML-rapporten kunnen niet automatisch worden afgedrukt, rapport wordt "
"overgeslagen: %s"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Hardware"
msgstr "Hardware"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__has_message
#: model:ir.model.fields,field_description:stock.field_stock_picking__has_message
#: model:ir.model.fields,field_description:stock.field_stock_scrap__has_message
msgid "Has Message"
msgstr "Heeft bericht"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_line_exist
msgid "Has Pack Operations"
msgstr "Heeft verpakkingsbewerking"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__has_packages
msgid "Has Packages"
msgstr "Heeft verpakkingen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot_report__has_return
msgid "Has Return"
msgstr "Is terug"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__has_scrap_move
msgid "Has Scrap Moves"
msgstr "Heeft afgekeurde verplaatsingen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__has_tracking
msgid "Has Tracking"
msgstr "Heeft tracering"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__product_has_variants
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__product_has_variants
msgid "Has variants"
msgstr "Heeft varianten"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "Having Category"
msgstr "Heeft categorie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__height
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "Height"
msgstr "Hoogte"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__posz
msgid "Height (Z)"
msgstr "Hoogte (Z)"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_package_type_positive_height
msgid "Height must be positive"
msgstr "Hoogte moet positief zien"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__snoozed_until
msgid "Hidden until next scheduler."
msgstr "Verborgen tot de volgende planner."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__hide_reservation_method
msgid "Hide Reservation Method"
msgstr "Reserveringsmethode verbergen"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_editable
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "History"
msgstr "Geschiedenis"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/views/search/stock_orderpoint_search_panel.xml:0
msgid "Horizon"
msgstr "Horizon"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__reservation_method
msgid "How products in transfers of this operation type should be reserved."
msgstr ""
"Hoe producten in overdrachten van deze bewerkingssoort moeten worden "
"gereserveerd."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_lot_label_layout__id
#: model:ir.model.fields,field_description:stock.field_picking_label_type__id
#: model:ir.model.fields,field_description:stock.field_procurement_group__id
#: model:ir.model.fields,field_description:stock.field_product_removal__id
#: model:ir.model.fields,field_description:stock.field_product_replenish__id
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__id
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__id
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__id
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__id
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__id
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__id
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__id
#: model:ir.model.fields,field_description:stock.field_stock_location__id
#: model:ir.model.fields,field_description:stock.field_stock_lot__id
#: model:ir.model.fields,field_description:stock.field_stock_lot_report__id
#: model:ir.model.fields,field_description:stock.field_stock_move__id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__id
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__id
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__id
#: model:ir.model.fields,field_description:stock.field_stock_package_level__id
#: model:ir.model.fields,field_description:stock.field_stock_package_type__id
#: model:ir.model.fields,field_description:stock.field_stock_picking__id
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__id
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__id
#: model:ir.model.fields,field_description:stock.field_stock_quant__id
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__id
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__id
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__id
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__id
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__id
#: model:ir.model.fields,field_description:stock.field_stock_request_count__id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__id
#: model:ir.model.fields,field_description:stock.field_stock_route__id
#: model:ir.model.fields,field_description:stock.field_stock_rule__id
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__id
#: model:ir.model.fields,field_description:stock.field_stock_scrap_reason_tag__id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__id
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__id
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__id
#: model:ir.model.fields,field_description:stock.field_stock_track_line__id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__id
msgid "ID"
msgstr "ID"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__activity_exception_icon
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_exception_icon
msgid "Icon"
msgstr "Icoon"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_lot__activity_exception_icon
#: model:ir.model.fields,help:stock.field_stock_picking__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icoon om uitzondering op activiteit aan te geven."

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"If a payment is still outstanding more than sixty (60) days after the due "
"payment date, My Company (Chicago) reserves the right to call on the "
"services of a debt recovery company. All legal expenses will be payable by "
"the client."
msgstr ""
"Indien een betaling meer dan zestig (60) dagen na de vervaldatum nog "
"openstaat, behoudt My Company (Chicago) zich het recht voor om een beroep te"
" doen op een incassobureau. Alle juridische kosten zijn voor rekening van de"
" klant."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"If a separator is defined, a QR code containing all serial numbers contained"
" in package will be generated, using the defined character(s) to separate "
"each numbers"
msgstr ""
"Een QR-code met alle serienummers in de verpakking wordt gegenereerd als een"
" scheidingsteken is gedefinieerd. Elk nummer wordt gescheiden door het (de) "
"gedefinieerde teken(s)"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_storage_category__allow_new_product__same
msgid "If all products are same"
msgstr "Als alle producten hetzelfde zijn"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_lot__message_needaction
#: model:ir.model.fields,help:stock.field_stock_picking__message_needaction
#: model:ir.model.fields,help:stock.field_stock_scrap__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Indien aangevinkt vragen nieuwe berichten je aandacht."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_lot__message_has_error
#: model:ir.model.fields,help:stock.field_stock_lot__message_has_sms_error
#: model:ir.model.fields,help:stock.field_stock_picking__message_has_error
#: model:ir.model.fields,help:stock.field_stock_picking__message_has_sms_error
#: model:ir.model.fields,help:stock.field_stock_scrap__message_has_error
#: model:ir.model.fields,help:stock.field_stock_scrap__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Indien aangevinkt hebben sommige berichten een leveringsfout."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__propagate_cancel
msgid "If checked, when this move is cancelled, cancel the linked move too"
msgstr ""
"Indien aangevinkt en de verplaatsing wordt geannuleerd, annuleer dan tevens "
"de gekoppelde verplaatsing"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__result_package_id
msgid "If set, the operations are packed into this package"
msgstr "Indien ingesteld worden de bewerkingen verpakt in deze verpakking"

#. module: stock
#: model:ir.model.fields,help:stock.field_lot_label_layout__label_quantity
msgid ""
"If the UoM of a lot is not 'units', the lot will be considered as a unit and"
" only one label will be printed for this lot."
msgstr ""
"Als de maateenheid van een partij geen 'stuks' is, wordt de partij als een "
"eenheid beschouwd en wordt er slechts één label voor deze partij afgedrukt."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__active
msgid ""
"If the active field is set to False, it will allow you to hide the "
"orderpoint without removing it."
msgstr ""
"Als het actief veld op False ingesteld is, kun je de minimale voorraadregel "
"verbergen zonder deze te verwijderen."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_route__active
msgid ""
"If the active field is set to False, it will allow you to hide the route "
"without removing it."
msgstr ""
"Indien het actief veld op False is ingesteld, kun je de route verbergen "
"zonder deze te verwijderen."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_storage_category__allow_new_product__empty
msgid "If the location is empty"
msgstr "Als de locatie leeg is"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__sn_duplicated
msgid "If the same SN is in another Quant"
msgstr "Als dezelfde SN in een andere hvh staat"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__auto_print_delivery_slip
msgid ""
"If this checkbox is ticked, Odoo will automatically print the delivery slip "
"of a picking when it is validated."
msgstr ""
"Als dit selectievakje is aangevinkt, zal Odoo automatisch de leveringsbon "
"van een picking afdrukken wanneer deze wordt gevalideerd."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__auto_print_lot_labels
msgid ""
"If this checkbox is ticked, Odoo will automatically print the lot/SN labels "
"of a picking when it is validated."
msgstr ""
"Als dit selectievakje is aangevinkt, zal Odoo automatisch de lot-/SN-labels "
"van een pick-up afdrukken wanneer deze wordt gevalideerd."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__auto_print_package_label
msgid ""
"If this checkbox is ticked, Odoo will automatically print the package label "
"when \"Put in Pack\" button is used."
msgstr ""
"Als dit selectievakje is aangevinkt, zal Odoo automatisch het "
"verpakkingslabel afdrukken wanneer de knop \"Inpakken\" wordt gebruikt."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__auto_print_packages
msgid ""
"If this checkbox is ticked, Odoo will automatically print the packages and "
"their contents of a picking when it is validated."
msgstr ""
"Als dit selectievakje is aangevinkt, zal Odoo automatisch de verpakkingen en"
" de inhoud van een picking afdrukken wanneer deze gevalideerd is."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__auto_print_product_labels
msgid ""
"If this checkbox is ticked, Odoo will automatically print the product labels"
" of a picking when it is validated."
msgstr ""
"Als dit selectievakje is aangevinkt, zal Odoo automatisch de productlabels "
"van een picking afdrukken wanneer deze wordt gevalideerd."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__auto_print_reception_report_labels
msgid ""
"If this checkbox is ticked, Odoo will automatically print the reception "
"report labels of a picking when it is validated."
msgstr ""
"Als dit selectievakje is aangevinkt, zal Odoo automatisch de "
"ontvangstrapportlabels van een orderafhandeling afdrukken wanneer deze wordt"
" gevalideerd."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__auto_print_reception_report
msgid ""
"If this checkbox is ticked, Odoo will automatically print the reception "
"report of a picking when it is validated and has assigned moves."
msgstr ""
"Als dit selectievakje is aangevinkt, zal Odoo automatisch het "
"ontvangstrapport van een picking afdrukken wanneer deze gevalideerd is en "
"verplaatsingen toegewezen heeft."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__auto_print_return_slip
msgid ""
"If this checkbox is ticked, Odoo will automatically print the return slip of"
" a picking when it is validated."
msgstr ""
"Als dit selectievakje is aangevinkt, zal Odoo automatisch de retourbon van "
"een pick-up afdrukken wanneer deze wordt gevalideerd."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__auto_show_reception_report
msgid ""
"If this checkbox is ticked, Odoo will automatically show the reception "
"report (if there are moves to allocate to) when validating."
msgstr ""
"Als dit selectievakje is aangevinkt, zal Odoo automatisch het "
"ontvangstrapport tonen (als er verplaatsingen zijn om aan toe te wijzen) bij"
" het bevestigen."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__show_operations
#: model:ir.model.fields,help:stock.field_stock_picking__show_operations
#: model:ir.model.fields,help:stock.field_stock_picking_type__show_operations
msgid ""
"If this checkbox is ticked, the pickings lines will represent detailed stock"
" operations. If not, the picking lines will represent an aggregate of "
"detailed stock operations."
msgstr ""
"Indien dit is aangevinkt, geven de pickingsregels gedetailleerde "
"voorraadbewerking weer. Zo niet, dan geven de pickingsregels een totaal van "
"gedetaileerde voorraadbewerkingen weer."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__picking_type_use_create_lots
#: model:ir.model.fields,help:stock.field_stock_picking__use_create_lots
#: model:ir.model.fields,help:stock.field_stock_picking_type__use_create_lots
msgid ""
"If this is checked only, it will suppose you want to create new Lots/Serial "
"Numbers, so you can provide them in a text field. "
msgstr ""
"Als enkel dit is aangevinkt, wordt verondersteld dat je nieuwe "
"Serienummer/Partijnummers wilt aanmaken, dus je kunt deze ingeven in het "
"tekstveld. "

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__picking_type_use_existing_lots
#: model:ir.model.fields,help:stock.field_stock_picking__use_existing_lots
#: model:ir.model.fields,help:stock.field_stock_picking_type__use_existing_lots
msgid ""
"If this is checked, you will be able to choose the Lots/Serial Numbers. You "
"can also decide to not put lots in this operation type.  This means it will "
"create stock with no lot or not put a restriction on the lot taken. "
msgstr ""
"Indien aangevinkt kun je serienummers/partijen kiezen. Je kunt ook beslissen"
" om geen partijen in deze bewerkingssoort te gebruiken. Dit betekent dat er "
"voorraad wordt aangemaakt zonder partij of zonder restrictie op de genomen "
"partij. "

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__return_id
msgid ""
"If this picking was created as a return of another picking, this field links"
" to the original picking."
msgstr ""
"Als deze picking is aangemaakt als een retour van een andere picking, linkt "
"dit veld naar de oorspronkelijke picking."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__backorder_id
msgid ""
"If this shipment was split, then this field links to the shipment which "
"contains the already processed part."
msgstr ""
"Als de levering wordt opgedeeld, dan zal dit veld de koppeling bevatten naar"
" de levering welke al is verwerkt."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__picking_type_entire_packs
#: model:ir.model.fields,help:stock.field_stock_move_line__picking_type_entire_packs
#: model:ir.model.fields,help:stock.field_stock_picking__picking_type_entire_packs
#: model:ir.model.fields,help:stock.field_stock_picking_type__show_entire_packs
msgid "If ticked, you will be able to select entire packages to move"
msgstr ""
"Indien aangevinkt kun je hele verpakkingen selecteren om te verplaatsen"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__active
msgid "If unchecked, it will allow you to hide the rule without removing it."
msgstr ""
"Indien uitgevinkt kun je de groep verbergen, zonder deze te hoeven "
"verwijderen."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__backorder_confirmation_id
msgid "Immediate Transfer"
msgstr "Directe overdracht"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Import"
msgstr "Records importeren"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/generate_serial.js:0
msgid "Import Lots"
msgstr "Importeer partijen"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/generate_serial.js:0
msgid "Import Serials"
msgstr "Importeer serienummers"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "Import Serials/Lots"
msgstr "Importeer serienummers/partijen"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Import Template for Inventory Adjustments"
msgstr "Importsjabloon voor voorraadaanpassingen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "In Stock"
msgstr "In voorraad"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__in_type_id
msgid "In Type"
msgstr "In type"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__date_deadline
#: model:ir.model.fields,help:stock.field_stock_picking__date_deadline
msgid ""
"In case of outgoing flow, validate the transfer before this date to allow to deliver at promised date to the customer.\n"
"        In case of incoming flow, validate the transfer before this date in order to have these products in stock at the date promised by the supplier"
msgstr ""
"Valideer in geval van uitgaande flow de overdracht voor deze datum om op de afgesproken datum aan de klant te kunnen leveren.\n"
"        Valideer in geval van inkomende flow de overdracht voor deze datum om deze producten op voorraad te hebben op de door de leverancier afgesproken datum"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
msgid "In internal locations"
msgstr "In interne locaties"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"In order for it to be admissible, My Company (Chicago) must be notified of "
"any claim by means of a letter sent by recorded delivery to its registered "
"office within 8 days of the delivery of the goods or the provision of the "
"services."
msgstr ""
"Om ontvankelijk te zijn, moet My Company (Chicago) binnen 8 dagen na de "
"levering van de goederen of de levering van de diensten op de hoogte worden "
"gesteld van elke claim door middel van een aangetekend schrijven naar haar "
"maatschappelijke zetel."

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_header.xml:0
#: model:ir.model.fields,field_description:stock.field_product_product__incoming_qty
#: model:ir.model.fields,field_description:stock.field_product_template__incoming_qty
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Incoming"
msgstr "Inkomend"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__in_date
msgid "Incoming Date"
msgstr "Inkomende datum"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "Incoming Draft Transfer"
msgstr "Concept inkomende overdracht"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__incoming_move_line_ids
msgid "Incoming Move Line"
msgstr "Inkomende verplaatsingsregel"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__reception_steps
msgid "Incoming Shipments"
msgstr "Ontvangsten"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/multi_print.js:0
msgid "Incorrect type of action submitted as a report, skipping action"
msgstr "Onjuist type actie ingediend als rapport, actie overgeslagen"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__inventory_diff_quantity
msgid ""
"Indicates the gap between the product's theoretical quantity and its counted"
" quantity."
msgstr ""
"Geeft het gat aan tussen het theoretische aantal van het product en het "
"getelde aantal."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_kandan
msgid "Initial Demand"
msgstr "Initiële vraag"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Input"
msgstr "Ontvangst"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__wh_input_stock_loc_id
msgid "Input Location"
msgstr "Ontvangstlocatie"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.help_message_template
msgid "Install"
msgstr "Installeren"

#. module: stock
#: model:ir.actions.server,name:stock.action_install_barcode
msgid "Install Barcode"
msgstr "Installeer Barcode"

#. module: stock
#. odoo-python
#: code:addons/stock/models/res_company.py:0
msgid "Inter-warehouse transit"
msgstr "Transit tussen magazijnen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__location_dest_id
msgid "Intermediate Location"
msgstr "Tussenlocatie"

#. module: stock
#: model:ir.ui.menu,name:stock.int_picking
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Internal"
msgstr "Intern"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__internal
msgid "Internal Location"
msgstr "Interne locatie"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Internal Locations"
msgstr "Interne locaties"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__ref
msgid "Internal Reference"
msgstr "Interne referentie"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__code__internal
msgid "Internal Transfer"
msgstr "Interne overdracht"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
#: model:ir.actions.act_window,name:stock.action_picking_tree_internal
#: model:stock.picking.type,name:stock.picking_type_internal
msgid "Internal Transfers"
msgstr "Interne overdrachten"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_company__internal_transit_location_id
msgid "Internal Transit Location"
msgstr "Interne transitlocatie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__int_type_id
msgid "Internal Type"
msgstr "Interne soort"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__child_internal_location_ids
msgid "Internal locations among descendants"
msgstr "Interne locaties onder afstammelingen"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_location.py:0
msgid "Internal locations having stock can't be converted"
msgstr "Interne locaties met voorraad kunnen niet worden omgezet"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_lot__ref
msgid ""
"Internal reference number in case it differs from the manufacturer's "
"lot/serial number"
msgstr ""
"Interne referentienummer indien deze afwijkt van het serie/partijnummer van "
"de fabrikant"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid "Invalid domain left operand %s"
msgstr "Ongeldig domein linker operand %s"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
#: code:addons/stock/models/stock_lot.py:0
msgid "Invalid domain operator %s"
msgstr "Ongeldig domein operator %s"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
#: code:addons/stock/models/stock_lot.py:0
msgid "Invalid domain right operand '%s'. It must be of type Integer/Float"
msgstr "Ongeldige rechter operand '%s'. Het moet een geheel getal/float zijn"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid ""
"Invalid rule's configuration, the following rule causes an endless loop: %s"
msgstr ""
"Ongeldige regel configuratie, de volgende regel veroorzaakt een eindeloze "
"lus: %s"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__inventory_quantity_auto_apply
msgid "Inventoried Quantity"
msgstr "Aantal in voorraad"

#. module: stock
#: model:ir.actions.server,name:stock.action_view_inventory_tree
#: model:ir.actions.server,name:stock.action_view_quants
#: model:ir.model.fields,field_description:stock.field_stock_move__is_inventory
#: model:ir.model.fields,field_description:stock.field_stock_move_line__is_inventory
#: model:ir.ui.menu,name:stock.menu_stock_root
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_partner_stock_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_pivot
msgid "Inventory"
msgstr "Voorraad"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_inventory_adjustement_name
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
msgid "Inventory Adjustment"
msgstr "Voorraadaanpassing"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
#: model:ir.model,name:stock.model_stock_inventory_adjustment_name
msgid "Inventory Adjustment Reference / Reason"
msgstr "Referentie/reden voor voorraadaanpassing"

#. module: stock
#: model:ir.model,name:stock.model_stock_inventory_warning
msgid "Inventory Adjustment Warning"
msgstr "Waarschuwing voorraadaanpassing"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Inventory Adjustments"
msgstr "Voorraadaanpassingen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "Inventory Count Sheet"
msgstr "Tellijst"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_request_count__inventory_date
msgid "Inventory Date"
msgstr "Teldatum"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__cyclic_inventory_frequency
#: model:ir.model.fields,field_description:stock.field_stock_quant__cyclic_inventory_frequency
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Inventory Frequency"
msgstr "Telfrequentie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__property_stock_inventory
#: model:ir.model.fields,field_description:stock.field_product_template__property_stock_inventory
msgid "Inventory Location"
msgstr "Voorraadlocatie"

#. module: stock
#: model:ir.model,name:stock.model_stock_location
msgid "Inventory Locations"
msgstr "Voorraadlocaties"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__inventory
msgid "Inventory Loss"
msgstr "Voorraadverlies"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_search_view_inherit_stock
msgid "Inventory Management"
msgstr "Voorraadbeheer"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "Inventory On Hand"
msgstr "Beschikbare voorraad"

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_picking_type_action
msgid "Inventory Overview"
msgstr "Voorraadoverzicht"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__inventory_quantity_set
msgid "Inventory Quantity Set"
msgstr "Stel voorraadaantal in"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__inventory_adjustment_name
msgid "Inventory Reason"
msgstr "Reden van voorraad"

#. module: stock
#: model:ir.model,name:stock.model_stock_route
msgid "Inventory Routes"
msgstr "Voorraad routes"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form_editable
msgid "Inventory Valuation"
msgstr "Voorraadwaardering"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_inventory_at_date
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__inventory_datetime
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
msgid "Inventory at Date"
msgstr "Voorraad op datum"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__is_empty
msgid "Is Empty"
msgstr "Is leeg"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__message_is_follower
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_is_follower
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_is_follower
msgid "Is Follower"
msgstr "Is een volger"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_level__is_fresh_package
msgid "Is Fresh Package"
msgstr "Is verse verpakking"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__is_locked
#: model:ir.model.fields,field_description:stock.field_stock_move_line__is_locked
#: model:ir.model.fields,field_description:stock.field_stock_picking__is_locked
msgid "Is Locked"
msgstr "Is vergrendeld"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__is_multi_location
msgid "Is Multi Location"
msgstr "Is meerdere locaties"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__is_partial_package
msgid "Is Partial Package"
msgstr "Is een gedeeltelijke verpakking"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__is_signed
msgid "Is Signed"
msgstr "Is ondertekend"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__scrap_location
msgid "Is a Scrap Location?"
msgstr "Is een afkeurlocatie?"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__is_initial_demand_editable
msgid "Is initial demand editable"
msgstr "Is de initiële aanvraag te bewerken"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__has_deadline_issue
msgid "Is late"
msgstr "Is te laat"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__has_deadline_issue
msgid "Is late or will be late depending on the deadline and scheduled date"
msgstr ""
"Is te laat of zal te laat komen, afhankelijk van de deadline en geplande "
"datum"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__is_quantity_done_editable
msgid "Is quantity done editable"
msgstr "Is het aantal gereed nog bewerkbaar"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid ""
"It is not possible to unreserve more products of %s than you have in stock."
msgstr ""
"Het is niet mogelijk om de reservering ongedaan te maken voor meer producten"
" van %s dan je op voorraad hebt."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__move_type
msgid "It specifies goods to be deliver partially or all at once"
msgstr ""
"Het geeft aan of de goederen in één keer of gedeeltelijk geleverd worden"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__move_type
msgid "It specifies goods to be transferred partially or all at once"
msgstr ""
"Het specificeert goederen die gedeeltelijk of in één keer moeten worden "
"overgedragen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__json_popover
msgid "JSON data for the popover widget"
msgstr "JSON data voor de popover widget"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__1
msgid "January"
msgstr "Januari"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "John Doe"
msgstr "John Doe"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__json_lead_days
msgid "Json Lead Days"
msgstr "Json doorloopdagen"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.js:0
msgid "Json Popup"
msgstr "Json Popup"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__json_replenishment_history
msgid "Json Replenishment History"
msgstr "Json aanvullingsgeschiedenis"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__7
msgid "July"
msgstr "Juli"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__6
msgid "June"
msgstr "Juni"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__kanban_dashboard_graph
msgid "Kanban Dashboard Graph"
msgstr "Kanban dashboard grafiek"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid "Keep Counted Quantity"
msgstr "Behoud getelde aantal"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid "Keep Difference"
msgstr "Verschil behouden"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "Keep current lines"
msgstr "Huidige regels behouden"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid ""
"Keep the <strong>Counted Quantity</strong> (the Difference will be updated)"
msgstr ""
"Behoud het <strong>getelde aantal</strong> (het verschil wordt bijgewerkt)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid ""
"Keep the <strong>Difference</strong> (the Counted Quantity will be updated "
"to reflect the same difference as when you counted)"
msgstr ""
"Behoud het <strong>verschil</strong> (het getelde aantal wordt bijgewerkt om"
" hetzelfde verschil weer te geven als toen je telde)"

#. module: stock
#: model:ir.actions.server,name:stock.action_print_labels
msgid "Labels"
msgstr "Labels"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_picking_label_type__label_type
msgid "Labels to print"
msgstr "Labels om af te drukken"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "Laptop"
msgstr "Laptop"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
msgid "Last 12 Months"
msgstr "Afgelopen 12 maanden"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
msgid "Last 3 Months"
msgstr "Afgelopen 3 maanden"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
msgid "Last 30 Days"
msgstr "Laatste 30 dagen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__last_count_date
msgid "Last Count Date"
msgstr "Laatste teldatum"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__last_delivery_partner_id
msgid "Last Delivery Partner"
msgstr "Laatste levering aan contact"

#. module: stock
#: model:product.removal,name:stock.removal_lifo
msgid "Last In First Out (LIFO)"
msgstr "Last In First Out (LIFO)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__last_inventory_date
msgid "Last Inventory"
msgstr "Laatste telling"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_lot_label_layout__write_uid
#: model:ir.model.fields,field_description:stock.field_picking_label_type__write_uid
#: model:ir.model.fields,field_description:stock.field_procurement_group__write_uid
#: model:ir.model.fields,field_description:stock.field_product_removal__write_uid
#: model:ir.model.fields,field_description:stock.field_product_replenish__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_location__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_lot__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_move__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_move_line__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_level__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_type__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_picking__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_request_count__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_route__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_rule__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_scrap__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_scrap_reason_tag__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_track_line__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_lot_label_layout__write_date
#: model:ir.model.fields,field_description:stock.field_picking_label_type__write_date
#: model:ir.model.fields,field_description:stock.field_procurement_group__write_date
#: model:ir.model.fields,field_description:stock.field_product_removal__write_date
#: model:ir.model.fields,field_description:stock.field_product_replenish__write_date
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__write_date
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__write_date
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__write_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__write_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__write_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__write_date
#: model:ir.model.fields,field_description:stock.field_stock_location__write_date
#: model:ir.model.fields,field_description:stock.field_stock_lot__write_date
#: model:ir.model.fields,field_description:stock.field_stock_move__write_date
#: model:ir.model.fields,field_description:stock.field_stock_move_line__write_date
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__write_date
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__write_date
#: model:ir.model.fields,field_description:stock.field_stock_package_level__write_date
#: model:ir.model.fields,field_description:stock.field_stock_package_type__write_date
#: model:ir.model.fields,field_description:stock.field_stock_picking__write_date
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__write_date
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__write_date
#: model:ir.model.fields,field_description:stock.field_stock_quant__write_date
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__write_date
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__write_date
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__write_date
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__write_date
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__write_date
#: model:ir.model.fields,field_description:stock.field_stock_request_count__write_date
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__write_date
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__write_date
#: model:ir.model.fields,field_description:stock.field_stock_route__write_date
#: model:ir.model.fields,field_description:stock.field_stock_rule__write_date
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__write_date
#: model:ir.model.fields,field_description:stock.field_stock_scrap__write_date
#: model:ir.model.fields,field_description:stock.field_stock_scrap_reason_tag__write_date
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__write_date
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__write_date
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__write_date
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__write_date
#: model:ir.model.fields,field_description:stock.field_stock_track_line__write_date
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__write_date
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__write_date
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__write_date
msgid "Last Updated on"
msgstr "Laatst bijgewerkt op"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_putaway_rule__sublocation__last_used
msgid "Last Used"
msgstr "Laatst gebruikt"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__last_count_date
msgid "Last time the Quantity was Updated"
msgstr "Laatste keer dat het aantal is bijgewerkt"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__products_availability_state__late
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Late"
msgstr "Te laat"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Late Activities"
msgstr "Te late activiteiten"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Late Availability"
msgstr "Te laat beschikbaar"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_late
msgid "Late Transfers"
msgstr "Te late overdrachten"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__products_availability
msgid "Latest product availability status of the picking"
msgstr "Status van de laatste productbeschikbaarheid van de picking"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__lead_days_date
msgid "Lead Days Date"
msgstr "Doorlooptijd datum"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__lead_time
#: model:ir.model.fields,field_description:stock.field_stock_rule__delay
msgid "Lead Time"
msgstr "Levertijd"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
msgid "Lead Times"
msgstr "Doorlooptijden"

#. module: stock
#: model:product.removal,name:stock.removal_least_packages
msgid "Least Packages"
msgstr "Minste verpakkingen"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_request_count__set_count__empty
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__group_propagation_option__none
msgid "Leave Empty"
msgstr "Laat leeg"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_route__company_id
#: model:ir.model.fields,help:stock.field_stock_rule__route_company_id
msgid "Leave this field empty if this route is shared between all companies"
msgstr "Laat dit veld leeg als je deze route wilt delen met alle bedrijven"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Legend"
msgstr "Legenda"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__packaging_length
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "Length"
msgstr "Lengte"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_package_type_positive_length
msgid "Length must be positive"
msgstr "Lengte moet positief zijn"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__length_uom_name
msgid "Length unit of measure label"
msgstr "Lengte maateenheid label"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__company_id
#: model:ir.model.fields,help:stock.field_stock_quant__company_id
#: model:ir.model.fields,help:stock.field_stock_quant_relocate__company_id
msgid "Let this field empty if this location is shared between companies"
msgstr ""
"Laat dit veld leeg als de locatie gedeeld wordt door meerdere bedrijven"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Linked Moves"
msgstr "Gekoppelde verplaatsingen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "List view of detailed operations"
msgstr "Lijstweergave van gedetailleerde bewerkingen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "List view of next transfers"
msgstr "Lijstweergave van volgende overdrachten"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "List view of operations"
msgstr "Lijstweergave van bewerkingen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__location_id
#: model:ir.model.fields,field_description:stock.field_product_template__location_id
#: model:ir.model.fields,field_description:stock.field_stock_lot__location_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__location_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__location_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__location_ids
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__location_id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty__location_id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__location_id
#: model:ir.model.fields.selection,name:stock.selection__barcode_rule__type__location
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_location_tree2
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Location"
msgstr "Locatie"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_location_barcode
msgid "Location Barcode"
msgstr "Locatie barcode"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__name
msgid "Location Name"
msgstr "Locatienaam"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__location_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__lot_stock_id
msgid "Location Stock"
msgstr "Voorraadlocatie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__usage
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Location Type"
msgstr "Locatiesoort"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
msgid "Location: Store to"
msgstr "Locatie: opslaan in"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
msgid "Location: When arrives to"
msgstr "Locatie: wanneer het aankomt in"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
#: model:ir.actions.act_window,name:stock.action_location_form
#: model:ir.actions.act_window,name:stock.action_prod_inv_location_form
#: model:ir.actions.act_window,name:stock.action_storage_category_locations
#: model:ir.ui.menu,name:stock.menu_action_location_form
#: model:ir.ui.menu,name:stock.menu_valuation
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
#: model_terms:ir.ui.view,arch_db:stock.report_location_barcode
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Locations"
msgstr "Locaties"

#. module: stock
#: model:ir.actions.server,name:stock.action_toggle_is_locked
msgid "Lock/Unlock"
msgstr "Vergrendelen/Ontgrendelen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_category_form_view_inherit
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Logistics"
msgstr "Logistiek"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__barcode_rule__type__lot
msgid "Lot"
msgstr "Partij"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.search_customer_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.stock_lot_customer_report_view_list
msgid "Lot / Serial Number"
msgstr "Partij-/serienummer"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__lot_label_format
msgid "Lot Label Format to auto-print"
msgstr "Partijlabelformaat voor automatisch afdrukken"

#. module: stock
#: model:ir.model,name:stock.model_report_stock_label_lot_template_view
msgid "Lot Label Report"
msgstr "Partijlabelrapportage"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__lot_properties_definition
msgid "Lot Properties"
msgstr "Eigenschappen partij"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "Lot numbers"
msgstr "Partijnummers"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__picking_label_type__label_type__lots
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Lot/SN Labels"
msgstr "Serie/Partijnummer labels"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_kanban
msgid "Lot/SN:"
msgstr "Partij/SN:"

#. module: stock
#: model:ir.model,name:stock.model_stock_lot
#: model:ir.model.fields,field_description:stock.field_stock_lot_report__lot_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__lot_id
msgid "Lot/Serial"
msgstr "Partij/Serienummer"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
msgid "Lot/Serial #"
msgstr "Partij/Serie #"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__name
#: model:ir.model.fields,field_description:stock.field_stock_move_line__lot_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__lot_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__lot_id
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
#: model_terms:ir.ui.view,arch_db:stock.search_customer_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_operation_tree
msgid "Lot/Serial Number"
msgstr "Partij/Serienummer"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_lot_label
msgid "Lot/Serial Number (PDF)"
msgstr "Partij/Serienummer (PDF)"

#. module: stock
#: model:ir.actions.report,name:stock.label_lot_template
msgid "Lot/Serial Number (ZPL)"
msgstr "Partij/serienummer (ZPL)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__lot_name
msgid "Lot/Serial Number Name"
msgstr "Partij/Serienummer naam"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_lot.py:0
msgid "Lot/Serial Number Relocated"
msgstr "Lot-/serienummer verplaatst"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Lot/Serial:"
msgstr "Partij/Serienummer:"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_production_lot
msgid "Lots & Serial Numbers"
msgstr "Partijen & serienummers"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Lots & Serial numbers will appear on the delivery slip"
msgstr "Partij- en serienummers worden afgedrukt op de leveringsbon"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_production_lot_form
#: model:ir.ui.menu,name:stock.menu_action_production_lot_form
msgid "Lots / Serial Numbers"
msgstr "Partijen/Serienummers"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__lots_visible
msgid "Lots Visible"
msgstr "Partijen zichtbaar"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_track_confirmation
msgid "Lots or serial numbers were not provided for tracked products"
msgstr ""
"Er zijn geen partij- of serienummers verstrekt voor getraceerde producten"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_product_production_lot_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_tree
msgid "Lots/Serial Numbers"
msgstr "Partij/Serienummers"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_product_production_lot_form
#: model_terms:ir.actions.act_window,help:stock.action_production_lot_form
msgid ""
"Lots/Serial numbers help you tracking the path followed by your products.\n"
"            From their traceability report you will see the full history of their use, as well as their composition."
msgstr ""
"Partijen / serienummers helpen je bij het volgen van het pad dat je producten volgen.\n"
"In hun traceerbaarheidsrapport zie je de volledige geschiedenis van hun gebruik, evenals hun samenstelling."

#. module: stock
#: model:ir.actions.act_window,name:stock.action_product_replenish
msgid "Low on stock? Let's replenish."
msgstr "Te weinig voorraad? Laten we aanvullen."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__mto_pull_id
msgid "MTO rule"
msgstr "MTO-regel"

#. module: stock
#: model:res.groups,name:stock.group_tracking_owner
msgid "Manage Different Stock Owners"
msgstr "Beheer verschillende voorraadeigenaars"

#. module: stock
#: model:res.groups,name:stock.group_production_lot
msgid "Manage Lots / Serial Numbers"
msgstr "Beheer partij/serienummers"

#. module: stock
#: model:res.groups,name:stock.group_stock_multi_locations
msgid "Manage Multiple Stock Locations"
msgstr "Beheer meerdere voorraadlocaties"

#. module: stock
#: model:res.groups,name:stock.group_stock_multi_warehouses
msgid "Manage Multiple Warehouses"
msgstr "Beheer meerdere magazijnen"

#. module: stock
#: model:res.groups,name:stock.group_tracking_lot
msgid "Manage Packages"
msgstr "Beheer verpakkingen"

#. module: stock
#: model:res.groups,name:stock.group_adv_location
msgid "Manage Push and Pull inventory flows"
msgstr "Beheer push en pull voorraad flows"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Manage product packagings (e.g. pack of 6 bottles, box of 10 pieces)"
msgstr ""
"Beheer productverpakkingen (bijvoorbeeld krat van 6 flessen, doos van 10 "
"stuks)"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse_orderpoint__trigger__manual
msgid "Manual"
msgstr "Handmatig"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__auto__manual
msgid "Manual Operation"
msgstr "Handmatige verwerking"

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/product_replenish.py:0
msgid "Manual Replenishment"
msgstr "Handmatige aanvulling"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__reservation_method__manual
msgid "Manually"
msgstr "Handmatig"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
msgid "Manufacturing"
msgstr "Productie"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__3
msgid "March"
msgstr "Maart"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Mark as Todo"
msgstr "Markeren als nog te doen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid "Max"
msgstr "Max"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_max_qty
msgid "Max Quantity"
msgstr "Max aantal"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__max_weight
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__max_weight
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_tree
msgid "Max Weight"
msgstr "Max. gewicht"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_package_type_positive_max_weight
msgid "Max Weight must be positive"
msgstr "Max. gewicht moet positief zien"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_storage_category_positive_max_weight
msgid "Max weight should be a positive number."
msgstr "Het maximale gewicht moet een positief getal zijn."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__reservation_days_before_priority
msgid ""
"Maximum number of days before scheduled date that priority picking products "
"should be reserved."
msgstr ""
"Maximaal aantal dagen vóór de geplande datum dat producten met prioriteit "
"moeten worden gereserveerd."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__reservation_days_before
msgid ""
"Maximum number of days before scheduled date that products should be "
"reserved."
msgstr ""
"Maximaal aantal dagen voor de geplande datum dat producten moeten worden "
"gereserveerd."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_package_type__max_weight
msgid "Maximum weight shippable in this packaging"
msgstr "Maximum gewicht verzendbaar in deze verpakking"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__5
msgid "May"
msgstr "Mei"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__message_has_error
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_has_error
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_has_error
msgid "Message Delivery error"
msgstr "Bericht afleverfout"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_partner__picking_warn_msg
#: model:ir.model.fields,field_description:stock.field_res_users__picking_warn_msg
msgid "Message for Stock Picking"
msgstr "Bericht voor voorraadpicking"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__message_ids
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_ids
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_ids
msgid "Messages"
msgstr "Berichten"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_removal__method
msgid "Method"
msgstr "Methode"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid "Min"
msgstr "Min"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_min_qty
msgid "Min Quantity"
msgstr "Min aantal"

#. module: stock
#: model:ir.model,name:stock.model_stock_warehouse_orderpoint
msgid "Minimum Inventory Rule"
msgstr "Minimale voorraadregel"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__orderpoint_ids
msgid "Minimum Stock Rules"
msgstr "Minimale voorraadregels"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_label_layout__move_ids
#: model:ir.model.fields,field_description:stock.field_stock_package_level__move_ids
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__move_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__move_ids
msgid "Move"
msgstr "Verplaatsingen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_operations
msgid "Move Detail"
msgstr "Verplaatsingdetail"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__picking_type_entire_packs
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_type_entire_packs
#: model:ir.model.fields,field_description:stock.field_stock_picking__picking_type_entire_packs
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__show_entire_packs
msgid "Move Entire Packages"
msgstr "Verplaats volledige verpakkingen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_lot_label_layout__move_line_ids
#: model:ir.model.fields,field_description:stock.field_stock_move__move_line_ids
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__move_line_ids
#: model:ir.model.fields,field_description:stock.field_stock_package_level__move_line_ids
msgid "Move Line"
msgstr "Boekingsregel"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree_detailed
msgid "Move Lines"
msgstr "Verplaatsingen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__move_lines_count
msgid "Move Lines Count"
msgstr "Aantal verplaatsingsregels"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__move_quantity
msgid "Move Quantity"
msgstr "Aantal verplaatsen"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__origin_returned_move_id
msgid "Move that created the return move"
msgstr "Verplaatsing welke de retourzending heeft gemaakt"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__product_return_moves
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree_receipt_picking
msgid "Moves"
msgstr "Verplaatsingen"

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_move_action
#: model:ir.ui.menu,name:stock.stock_move_menu
msgid "Moves Analysis"
msgstr "Verplaatsingen"

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_move_line_action
#: model:ir.ui.menu,name:stock.stock_move_line_menu
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_pivot
msgid "Moves History"
msgstr "Geschiedenis"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__group_id
msgid ""
"Moves created through this orderpoint will be put in this procurement group."
" If none is given, the moves generated by stock rules will be grouped into "
"one big picking."
msgstr ""
"Verplaatsingen die worden aangemaakt door deze aanvulregel worden geplaatst "
"in deze aanvulgroep. Indien niets is ingevoerd, worden de verplaatsingen, "
"gegenereerd door de voorraadregels, gegroepeerd in één grote levering."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_adv_location
msgid "Multi-Step Routes"
msgstr "Routes met meerdere stappen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__qty_multiple
msgid "Multiple Quantity"
msgstr "Veelvoud aantal"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_storage_category_capacity_unique_package_type
msgid "Multiple capacity rules for one package type."
msgstr "Meerdere capaciteitsregels voor één verpakkingssoort."

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_storage_category_capacity_unique_product
msgid "Multiple capacity rules for one product."
msgstr "Meerdere capaciteitsregels voor één product."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__my_activity_date_deadline
#: model:ir.model.fields,field_description:stock.field_stock_picking__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Mijn activiteit deadline"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"My Company (Chicago) undertakes to do its best to supply performant services"
" in due time in accordance with the agreed timeframes. However, none of its "
"obligations can be considered as being an obligation to achieve results. My "
"Company (Chicago) cannot under any circumstances, be required by the client "
"to appear as a third party in the context of any claim for damages filed "
"against the client by an end consumer."
msgstr ""
"My Company (Chicago) verbindt zich ertoe haar best te doen om tijdige en "
"goede diensten te leveren in overeenstemming met de overeengekomen "
"termijnen. Geen van haar verplichtingen kan echter worden beschouwd als een "
"resultaatsverplichting. My Company (Chicago) kan in geen geval door de klant"
" worden verplicht om als derde partij op te treden in het kader van een "
"vordering tot schadevergoeding die door een eindconsument tegen de klant "
"wordt ingediend."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "My Counts"
msgstr "Mijn tellingen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "My Transfers"
msgstr "Mijn overdrachten"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_removal__name
#: model:ir.model.fields,field_description:stock.field_stock_rule__name
#: model:ir.model.fields,field_description:stock.field_stock_scrap_reason_tag__name
#: model:ir.model.fields,field_description:stock.field_stock_track_line__product_display_name
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__name
msgid "Name"
msgstr "Naam"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "Name Demo"
msgstr "Naam demo"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__nbr_moves_in
#: model:ir.model.fields,field_description:stock.field_product_template__nbr_moves_in
msgid "Nbr Moves In"
msgstr "Aantal verplaatsingen in"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__nbr_moves_out
#: model:ir.model.fields,field_description:stock.field_product_template__nbr_moves_out
msgid "Nbr Moves Out"
msgstr "Aantal verplaatsingen uit"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_search_form_view_stock
#: model_terms:ir.ui.view,arch_db:stock.stock_product_search_form_view
msgid "Negative Forecasted Quantity"
msgstr "Negatieve prognose voorraadaantal"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Negative Stock"
msgstr "Negatieve voorraad"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__net_weight
msgid "Net Weight"
msgstr "Netto gewicht"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__create_backorder__never
msgid "Never"
msgstr "Nooit"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__never_product_template_attribute_value_ids
msgid "Never attribute Values"
msgstr "Nooit Waarden toekennen"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_scrap.py:0
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__draft
#: model:ir.model.fields.selection,name:stock.selection__stock_package_level__state__new
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "New"
msgstr "Nieuw"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid "New Move: %(product)s"
msgstr "Nieuwe beweging: %(product)s"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__new_quantity
msgid "New Quantity on Hand"
msgstr "Nieuwe beschikbare voorraad"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_form
msgid "New Transfer"
msgstr "Nieuwe overdracht"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__activity_calendar_event_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Volgende activiteiten kalender Evenement"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__activity_date_deadline
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Volgende activiteit deadline"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__activity_summary
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_summary
msgid "Next Activity Summary"
msgstr "Volgende activiteit overzicht"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__activity_type_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_type_id
msgid "Next Activity Type"
msgstr "Volgende activiteit type"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__next_inventory_date
msgid "Next Expected"
msgstr "Volgende telling"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Next Transfers"
msgstr "Volgende overdrachten"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__inventory_date
msgid "Next date the On Hand Quantity should be counted."
msgstr "De volgende datum waarop de beschikbare voorraad moet worden geteld."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid "Next transfer(s) impacted:"
msgstr "Volgende overdracht(en) beïnvloed:"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_putaway_rule__sublocation__no
msgid "No"
msgstr "Nee"

#. module: stock
#. odoo-python
#: code:addons/stock/report/report_stock_reception.py:0
msgid "No %s selected or a delivery order selected"
msgstr "Geen %s geselecteerd of een leveringsoder geselecteerd"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "No Backorder"
msgstr "Geen backorder"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_partner__picking_warn__no-message
msgid "No Message"
msgstr "Geen bericht"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "No Stock On Hand"
msgstr "Geen beschikbare voorraad"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/components/reception_report_main/stock_reception_report_main.xml:0
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "No allocation need found."
msgstr "Geen toewijzingsbehoefte gevonden."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_lot_report
msgid "No data yet!"
msgstr "Nog geen gegevens!"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.help_message_template
msgid "No delivery to do!"
msgstr "Geen geplande leveringen!"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid "No negative quantities allowed"
msgstr "Geen negatieve hoeveelheden toegestaan"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
msgid "No operation made on this lot."
msgstr "Geen bewerkingen op deze partij."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_get_picking_type_operations
msgid "No operations found. Let's create a transfer!"
msgstr "Geen bewerkingen gevonden. Laten we een overdracht maken!"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "No product found to generate Serials/Lots for."
msgstr ""
"Er is geen product gevonden waarvoor series/partijen kunnen worden "
"gegenereerd."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.product_template_action_product
msgid "No product found. Let's create one!"
msgstr "Geen product gevonden. Maak er één aan!"

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/stock_picking_return.py:0
msgid ""
"No products to return (only lines in Done state and not fully returned yet "
"can be returned)."
msgstr ""
"Geen producten om te retourneren (alleen regels die verwerkt zijn en nog "
"niet volledig geretourneerd zijn kunnen worden geretourneerd)."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_putaway_tree
msgid "No putaway rule found. Let's create one!"
msgstr "Geen wegzetregel gevonden. Maak er één aan!"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.help_message_template
msgid "No receipt yet! Create a new one."
msgstr "Je hebt nog geen ontvangsten! Maak een nieuwe aan."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_orderpoint
msgid "No reordering rule found"
msgstr "Geen aanvulregel gevonden"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
msgid ""
"No rule has been found to replenish \"%(product)s\" in \"%(location)s\".\n"
"Verify the routes configuration on the product."
msgstr ""
"Er is geen regel gevonden die \"%(product)s\" in \"%(location)s\" aanvult.\n"
"Controleer de routeconfiguratie op het product."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
msgid "No source location defined on stock rule: %s!"
msgstr "Geen bronlocatie gedefinieerd op voorraadregel: %s!"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_move_action
msgid "No stock move found"
msgstr "Geen voorraadverplaatsing gevonden"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_product_stock_view
msgid "No stock to show"
msgstr "Geen voorraad om te laten zien"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_picking_type_list
#: model_terms:ir.ui.view,arch_db:stock.help_message_template
msgid "No transfer found. Let's create one!"
msgstr "Geen overdrachten gevonden. Maak er één aan!"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__priority__0
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__priority__0
msgid "Normal"
msgstr "Normaal"

#. module: stock
#. odoo-javascript
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
#: code:addons/stock/static/src/widgets/forecast_widget.xml:0
msgid "Not Available"
msgstr "Niet beschikbaar"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
msgid "Not Snoozed"
msgstr "Niet gesnoozed"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Note"
msgstr "Notitie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__note
msgid "Notes"
msgstr "Notities"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Nothing to check the availability for."
msgstr "Niets om de beschikbaarheid van te controleren."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__11
msgid "November"
msgstr "November"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__message_needaction_counter
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_needaction_counter
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_needaction_counter
msgid "Number of Actions"
msgstr "Aantal acties"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "Number of SN"
msgstr "Aantal serienummers"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__next_serial_count
msgid "Number of SN/Lots"
msgstr "Aantal SN/partijnummers"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__message_has_error_counter
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_has_error_counter
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_has_error_counter
msgid "Number of errors"
msgstr "Aantal fouten"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__nbr_moves_in
#: model:ir.model.fields,help:stock.field_product_template__nbr_moves_in
msgid "Number of incoming stock moves in the past 12 months"
msgstr "Aantal inkomende voorraadverplaatsingen in de afgelopen 12 maanden"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_lot__message_needaction_counter
#: model:ir.model.fields,help:stock.field_stock_picking__message_needaction_counter
#: model:ir.model.fields,help:stock.field_stock_scrap__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Aantal berichten die actie vereisen"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_lot__message_has_error_counter
#: model:ir.model.fields,help:stock.field_stock_picking__message_has_error_counter
#: model:ir.model.fields,help:stock.field_stock_scrap__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Aantal berichten met leveringsfout"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__nbr_moves_out
#: model:ir.model.fields,help:stock.field_product_template__nbr_moves_out
msgid "Number of outgoing stock moves in the past 12 months"
msgstr "Aantal uitgaande voorraadverplaatsingen in de afgelopen 12 maanden"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__days_to_order
msgid "Numbers of days  in advance that replenishments demands are created."
msgstr "Aantal dagen van tevoren dat aanvullingsaanvragen worden aangemaakt."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__10
msgid "October"
msgstr "Oktober"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid ""
"Odoo opens a PDF preview by default. If you (Enterprise users only) want to print instantly,\n"
"                                        install the IoT App on a computer that is on the same local network as the\n"
"                                        barcode operator and configure the routing of the reports.\n"
"                                        <br/>"
msgstr ""
"Odoo opent standaard een pdf-voorbeeld. Als je (alleen voor Enterprise-gebruikers) direct wilt afdrukken:\n"
"                                        installeer de IoT-app op een computer die zich op hetzelfde lokale netwerk bevindt als de\n"
"                                        barcodeoperator en configureer de routering van de rapporten.\n"
"                                        <br/>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "Office Chair"
msgstr "Kantoorstoel"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_header.xml:0
#: model:ir.model.fields,field_description:stock.field_stock_quant__on_hand
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__qty_on_hand
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.view_stock_product_template_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_product_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_simple
msgid "On Hand"
msgstr "Beschikbare voorraad"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__product_qty
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_editable
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "On Hand Quantity"
msgstr "Beschikbare voorraad"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "On hand Quantity"
msgstr "In voorraad Aantal"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__available_quantity
msgid ""
"On hand quantity which hasn't been reserved on a transfer, in the default "
"unit of measure of the product"
msgstr ""
"Beschikbare voorraad die niet is gereserveerd op een overdracht, in de "
"standaard maateenheid van het product"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_kanban_stock_view
msgid "On hand:"
msgstr "Beschikbare voorraad:"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__lot_label_layout__label_quantity__lots
msgid "One per lot/SN"
msgstr "Eén per partij/SN"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__lot_label_layout__label_quantity__units
msgid "One per unit"
msgstr "Eén per eenheid"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "Open"
msgstr "Openen"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_label_layout__move_quantity__move
msgid "Operation Quantities"
msgstr "Bewerkinghoeveelheden"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
#: model:ir.model.fields,field_description:stock.field_stock_move__picking_type_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__picking_type_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__name
#: model:ir.model.fields,field_description:stock.field_stock_rule__picking_type_id
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
msgid "Operation Type"
msgstr "Bewerkingssoort"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__return_picking_type_id
msgid "Operation Type for Returns"
msgstr "Bewerkingssoort voor retouren"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking_type_label
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_tree
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Operation Types"
msgstr "Bewerkingsoorten"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/models/stock_quant.py:0
msgid "Operation not supported"
msgstr "Bewerking niet ondersteund"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_type_id
msgid "Operation type"
msgstr "Bewerkingssoort"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_picking_type_label
msgid "Operation type (PDF)"
msgstr "Bewerkingssoort (PDF)"

#. module: stock
#: model:ir.actions.report,name:stock.label_picking_type
msgid "Operation type (ZPL)"
msgstr "Bewerkingssoort (ZPL)"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_get_picking_type_operations
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_line_ids
#: model:ir.ui.menu,name:stock.menu_stock_warehouse_mgmt
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Operations"
msgstr "Bewerkingen"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_type_list
#: model:ir.ui.menu,name:stock.menu_pickingtype
msgid "Operations Types"
msgstr "Bewerkingsoorten"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_line_ids_without_package
msgid "Operations without package"
msgstr "Bewerkingen zonder verpakking"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Optimize your transfers by grouping operations together and assigning jobs "
"to workers"
msgstr ""
"Optimaliseer je overplaatsingen door bewerkingen te groeperen en taken toe "
"te wijzen aan werknemers"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__partner_id
msgid ""
"Optional address where goods are to be delivered, specifically used for "
"allotment"
msgstr "Optioneel adres waar de goederen moeten worden afgeleverd"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__posx
#: model:ir.model.fields,help:stock.field_stock_location__posy
#: model:ir.model.fields,help:stock.field_stock_location__posz
msgid "Optional localization details, for information purpose only"
msgstr "Optionele lokalisatiegegevens, alleen voor informatieve redenen"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__returned_move_ids
msgid "Optional: all returned moves created from this move"
msgstr "Optioneel: Alle retourzendingen aangemaakt van deze verplaatsing"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__move_dest_ids
msgid "Optional: next stock move when chaining them"
msgstr "Optioneel: volgende voorraadverplaatsing wanneer deze gekoppeld is"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__move_orig_ids
msgid "Optional: previous stock move when chaining them"
msgstr "Optioneel: vorige voorraadverplaatsing wanneer gekoppeld"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
msgid "Options"
msgstr "Opties"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/views/stock_orderpoint_list_view.xml:0
#: model_terms:ir.ui.view,arch_db:stock.replenishment_option_warning_view
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid "Order"
msgstr "Bestel"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/views/stock_orderpoint_list_view.xml:0
msgid "Order To Max"
msgstr "Bestel tot max"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Order signed"
msgstr "Order ondertekend"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Order signed by %s"
msgstr "Order ondertekend door %s"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "Ordered"
msgstr "Besteld"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__orderpoint_ids
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__orderpoint_id
msgid "Orderpoint"
msgstr "Aanvulregel"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Origin"
msgstr "Bron"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Origin Moves"
msgstr "Originele verplaatsingen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__origin_returned_move_id
msgid "Origin return move"
msgstr "Originele retourzending"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__move_orig_ids
msgid "Original Move"
msgstr "Originele verplaatsing"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__orderpoint_id
msgid "Original Reordering Rule"
msgstr "Originele aanvulregel"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Other Information"
msgstr "Overige informatie"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"Our invoices are payable within 21 working days, unless another payment "
"timeframe is indicated on either the invoice or the order. In the event of "
"non-payment by the due date, My Company (Chicago) reserves the right to "
"request a fixed interest payment amounting to 10% of the sum remaining due. "
"My Company (Chicago) will be authorized to suspend any provision of services"
" without prior warning in the event of late payment."
msgstr ""
"Onze facturen zijn betaalbaar binnen 21 werkdagen, tenzij op de factuur of "
"de order een andere betalingstermijn is vermeld. In geval van niet-betaling "
"op de vervaldag, behoudt My Company (Chicago) zich het recht voor om een "
"vaste rentebetaling te vragen ten bedrage van 10% van het resterende bedrag."
" My Company (Chicago) heeft het recht om elke levering van diensten zonder "
"voorafgaande waarschuwing op te schorten in geval van te late betaling."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__out_type_id
msgid "Out Type"
msgstr "Uit type"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_header.xml:0
#: model:ir.model.fields,field_description:stock.field_product_product__outgoing_qty
#: model:ir.model.fields,field_description:stock.field_product_template__outgoing_qty
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Outgoing"
msgstr "Uitgaand"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "Outgoing Draft Transfer"
msgstr "Concept uitgaande overdracht"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__outgoing_move_line_ids
msgid "Outgoing Move Line"
msgstr "Uitgaande verplaatsingsregel"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__delivery_steps
msgid "Outgoing Shipments"
msgstr "Leveringen"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Output"
msgstr "Uitgaand"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__wh_output_stock_loc_id
msgid "Output Location"
msgstr "Leverlocatie"

#. module: stock
#: model:ir.ui.menu,name:stock.stock_picking_type_menu
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rules_report
msgid "Overview"
msgstr "Overzicht"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__owner_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__owner_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__owner_id
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
msgid "Owner"
msgstr "Eigenaar"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__restrict_partner_id
msgid "Owner "
msgstr "Eigenaar "

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Owner:"
msgstr "Eigenaar:"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid "P&L Qty"
msgstr "W&V hvh"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__package_label_to_print__pdf
msgid "PDF"
msgstr "PDF"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Pack"
msgstr "Verpakken"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__pack_date
msgid "Pack Date"
msgstr "Datum inpakken"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "Pack Date Demo"
msgstr "Pack Date-demo"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "Pack Date:"
msgstr "Inpakdatum:"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__pack_type_id
msgid "Pack Type"
msgstr "Verpakkingssoort"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_level__package_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__package_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__package_id
#: model:ir.model.fields.selection,name:stock.selection__barcode_rule__type__package
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
#: model_terms:ir.ui.view,arch_db:stock.report_picking
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_tree
msgid "Package"
msgstr "Verpakking"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "Package A"
msgstr "Verpakking A"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "Package B"
msgstr "Verpakking B"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_quant_package_barcode_small
msgid "Package Barcode (PDF)"
msgstr "Verpakkingsbarcode (PDF)"

#. module: stock
#: model:ir.actions.report,name:stock.label_package_template
msgid "Package Barcode (ZPL)"
msgstr "Verpakkingsbarcode (ZPL)"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_quant_package_barcode
msgid "Package Barcode with Contents"
msgstr "Verpakkingsbarcode met inhoud"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__package_capacity_ids
msgid "Package Capacity"
msgstr "Verpakkingcapaciteit"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_package_level.py:0
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Package Content"
msgstr "Inhoud verpakking"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Package Label"
msgstr "Verpakkingslabel"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__package_label_to_print
msgid "Package Label to Print"
msgstr "Verpakkingslabel om af te drukken"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__package_level_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__package_level_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__package_level_ids
msgid "Package Level"
msgstr "Verpakkingsniveau"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__package_level_ids_details
msgid "Package Level Ids Details"
msgstr "Verpakkingsniveau Ids details"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
msgid "Package Name"
msgstr "Verpakkingsnaam"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__name
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Package Reference"
msgstr "Verpakkingsreferentie"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Package Transfers"
msgstr "Verpakking overdrachten"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_packaging__package_type_id
#: model:ir.model.fields,field_description:stock.field_stock_package_type__name
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__package_type_ids
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__package_type_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__package_type_id
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "Package Type"
msgstr "Verpakking soort"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "Package Type Demo"
msgstr "Verpakkingssoort Demo"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "Package Type:"
msgstr "Verpakkingssoort:"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_package_type_view
#: model:ir.ui.menu,name:stock.menu_packaging_types
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_tree
msgid "Package Types"
msgstr "Verpakkingssoorten"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__package_use
msgid "Package Use"
msgstr "Gebruik verpakking"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Package manually relocated"
msgstr "Verpakking handmatig verplaatst"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__valid_sscc
msgid "Package name is valid SSCC"
msgstr "Verpakkingsnaam is geldig SSCC"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "Package type"
msgstr "Verpakkingssoort"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_package_view
#: model:ir.actions.report,name:stock.action_report_picking_packages
#: model:ir.model,name:stock.model_stock_quant_package
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_tracking_lot
#: model:ir.ui.menu,name:stock.menu_package
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Packages"
msgstr "Verpakkingen"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_package_view
msgid ""
"Packages are usually created via transfers (during pack operation) and can contain different products.\n"
"                Once created, the whole package can be moved at once, or products can be unpacked and moved as single units again."
msgstr ""
"Verpakkingen worden meestal gemaakt via overdrachten (tijdens het verpakken) en kunnen verschillende producten bevatten.\n"
"Eenmaal aangemaakt, kan de hele verpakking in één keer worden verplaatst, of kunnen producten worden uitgepakt en weer als afzonderlijke stuks worden verplaatst."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__product_packaging_id
msgid "Packaging"
msgstr "Verpakking"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_package_type__height
msgid "Packaging Height"
msgstr "Verpakkingshoogte"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_package_type__packaging_length
msgid "Packaging Length"
msgstr "Verpakkingslengte"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_package_type__width
msgid "Packaging Width"
msgstr "Verpakkingsbreedte"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_route__packaging_ids
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Packagings"
msgstr "Verpakkingen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__wh_pack_stock_loc_id
msgid "Packing Location"
msgstr "Inpaklocatie"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Packing Zone"
msgstr "Verpakkingzone"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
msgid "Pallet"
msgstr "Pallet"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__location_id
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Parent Location"
msgstr "Bovenliggende locatie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__parent_path
msgid "Parent Path"
msgstr "Bovenliggend pad"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_category__parent_route_ids
msgid "Parent Routes"
msgstr "Ouderroutes"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__procurement_group__move_type__direct
msgid "Partial"
msgstr "Gedeeltelijk"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__partial_package_names
msgid "Partial Package Names"
msgstr "Gedeeltelijke verpakkingsnamen"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__partially_available
msgid "Partially Available"
msgstr "Gedeeltelijk beschikbaar"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__partner_id
#: model:ir.model.fields,field_description:stock.field_stock_lot_report__partner_id
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Partner"
msgstr "Contact"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__partner_address_id
msgid "Partner Address"
msgstr "Contactadres"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_action_inventory_tree
msgid "Physical Inventory"
msgstr "Fysieke voorraad"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__pick_ids
msgid "Pick"
msgstr "Picken"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__quant_id
msgid "Pick From"
msgstr "Picken van"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__pick_type_id
msgid "Pick Type"
msgstr "Pickingstype"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse__delivery_steps__pick_ship
msgid "Pick then Deliver (2 steps)"
msgstr "Pick then Deliver (2 stappen)"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse__delivery_steps__pick_pack_ship
msgid "Pick, Pack, then Deliver (3 steps)"
msgstr "Picken, verpakken en afleveren (3 stappen)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__picked
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picked
msgid "Picked"
msgstr "Gepicked"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_picking_label_type__picking_ids
#: model:ir.model.fields,field_description:stock.field_stock_lot_report__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_package_level__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__picking_id
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Picking"
msgstr "Picking"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Picking Lists"
msgstr "Pickinglijst"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_picking
msgid "Picking Operations"
msgstr "Pickbewerkingen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__picking_properties_definition
msgid "Picking Properties"
msgstr "Eigenschappen picking"

#. module: stock
#: model:ir.model,name:stock.model_stock_picking_type
msgid "Picking Type"
msgstr "Pickingsoort"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__picking_type_code_domain
msgid "Picking Type Code Domain"
msgstr "Pickingsoort Code Domein"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "Picking list"
msgstr "Verzamellijst"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/stock_rescheduling_popover.xml:0
msgid "Planning Issue"
msgstr "Planningsprobleem"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Planning Issues"
msgstr "Planningsproblemen"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Please create a warehouse for company %s."
msgstr "Maak een magazijn aan voor bedrijf %s."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_return_slip
msgid ""
"Please put this document inside your return parcel.<br/>\n"
"                                Your parcel must be sent to this address:"
msgstr ""
"Steek dit document in je retourpakket.<br/>\n"
"Je pakket moet naar dit adres worden verzonden:"

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/stock_picking_return.py:0
msgid "Please specify at least one non-zero quantity."
msgstr "Specificeer ten minste één waarde welke niet nul is."

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/stock_rescheduling_popover.xml:0
msgid "Preceding operations"
msgstr "Voorgaande bewerkingen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__route_id
#: model:ir.model.fields,field_description:stock.field_stock_replenish_mixin__route_id
msgid "Preferred Route"
msgstr "Voorkeursroute"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__route_ids
msgid "Preferred route"
msgstr "Voorkeursroute"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "Presence depends on the type of operation."
msgstr "Aanwezigheid is afhankelijk van het type bewerking."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid ""
"Press the CREATE button to define quantity for each product in your stock or"
" import them from a spreadsheet throughout Favorites"
msgstr ""
"Druk op de knop NIEUW om het aantal voor elk product in de voorraad te "
"definiëren of importeer ze vanuit een spreadsheet via de actieknop"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
#: code:addons/stock/static/src/components/reception_report_main/stock_reception_report_main.xml:0
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Print"
msgstr "Afdrukken"

#. module: stock
#: model:res.groups,name:stock.group_stock_lot_print_gs1
msgid "Print GS1 Barcodes for Lot & Serial Numbers"
msgstr "GS1-barcodes afdrukken voor partij/serienummer"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_lot_print_gs1
msgid "Print GS1 Barcodes for Lots & Serial Numbers"
msgstr "GS1-barcodes afdrukken voor partijen en serienummers"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/components/reception_report_line/stock_reception_report_line.xml:0
msgid "Print Label"
msgstr "Label afdrukken"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/components/reception_report_main/stock_reception_report_main.xml:0
#: code:addons/stock/static/src/components/reception_report_table/stock_reception_report_table.xml:0
#: code:addons/stock/static/src/xml/report_stock_reception.xml:0
msgid "Print Labels"
msgstr "Labels afdrukken"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Print label as:"
msgstr "Label afdrukken als:"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Print on \"Put in Pack\""
msgstr "Afdrukken op \"In pakket plaatsen\""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Print on Validation"
msgstr "Afdrukken bij bevestigen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__printed
msgid "Printed"
msgstr "Afgedrukt"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__priority
#: model:ir.model.fields,field_description:stock.field_stock_picking__priority
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__sequence
msgid "Priority"
msgstr "Prioriteit"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__delay_alert_date
msgid "Process at this date to be on time"
msgstr "Verwerk op deze datum om op tijd te zijn"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Process operations faster with barcodes"
msgstr "Verwerk je bewerkingen sneller met barcodes"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_procurement
msgid "Procurement"
msgstr "Aanvullen"

#. module: stock
#: model:ir.model,name:stock.model_procurement_group
#: model:ir.model.fields,field_description:stock.field_stock_move__group_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__group_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__group_id
msgid "Procurement Group"
msgstr "Aanvulgroep"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.procurement_group_form_view
msgid "Procurement group"
msgstr "Aanvulgroep"

#. module: stock
#: model:ir.actions.server,name:stock.ir_cron_scheduler_action_ir_actions_server
#: model:ir.ui.menu,name:stock.menu_procurement_compute
msgid "Procurement: run scheduler"
msgstr "Aanvullen: start planner"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__produce_line_ids
msgid "Produce Line"
msgstr "Produceer regel"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid "Produced Qty"
msgstr "Geproduceerd aantal"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
#: model:ir.model,name:stock.model_product_template
#: model:ir.model.fields,field_description:stock.field_product_replenish__product_id
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__product_id
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__product_id
#: model:ir.model.fields,field_description:stock.field_stock_lot__product_id
#: model:ir.model.fields,field_description:stock.field_stock_lot_report__product_id
#: model:ir.model.fields,field_description:stock.field_stock_move__product_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__product_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__product_id
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__product_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__product_id
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__product_id
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__product_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__product_id
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__product_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__product_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__product_id
#: model:ir.model.fields,field_description:stock.field_stock_track_line__product_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty__product_id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__product_id
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.search_customer_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Product"
msgstr "Product"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__products_availability
msgid "Product Availability"
msgstr "Product beschikbaarheid"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__product_capacity_ids
msgid "Product Capacity"
msgstr "Productcapaciteit"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_route__categ_ids
#: model:ir.ui.menu,name:stock.menu_product_category_config_stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Product Categories"
msgstr "Productcategorieën"

#. module: stock
#: model:ir.model,name:stock.model_product_category
#: model:ir.model.fields,field_description:stock.field_stock_move_line__product_category_name
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__category_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__product_categ_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_category_id
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "Product Category"
msgstr "Productcategorie"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_report_label
msgid "Product Display Name"
msgstr "Weergavenaam van product"

#. module: stock
#: model:ir.actions.report,name:stock.label_product_product
msgid "Product Label (ZPL)"
msgstr "Productlabel (ZPL)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__product_label_format
msgid "Product Label Format to auto-print"
msgstr "Productlabelformaat voor automatisch afdrukken"

#. module: stock
#: model:ir.model,name:stock.model_report_stock_label_product_product_view
msgid "Product Label Report"
msgstr "Productlabelrapport"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__picking_label_type__label_type__products
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Product Labels"
msgstr "Productlabels"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
msgid "Product Lots Filter"
msgstr "Productie partijen filter"

#. module: stock
#: model:ir.model,name:stock.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "Productverplaatstingen (voorraadverplaatsingsregels)"

#. module: stock
#: model:ir.model,name:stock.model_product_packaging
msgid "Product Packaging"
msgstr "Productverpakkingen"

#. module: stock
#: model:ir.actions.report,name:stock.label_product_packaging
msgid "Product Packaging (ZPL)"
msgstr "Productverpakking (ZPL)"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_product_packagings
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Product Packagings"
msgstr "Productverpakkingen"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Product Quantity Confirmed"
msgstr "Productaantal bevestigd"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Product Quantity Updated"
msgstr "Productaantal bijgewerkt"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_quant_relocate_view_form
msgid "Product Relocated"
msgstr "Product verplaatst"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_buttons.js:0
#: model:ir.model,name:stock.model_product_replenish
msgid "Product Replenish"
msgstr "Product aanvullen"

#. module: stock
#: model:ir.model,name:stock.model_stock_replenish_mixin
msgid "Product Replenish Mixin"
msgstr "Mixin om product aan te vullen"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_stock_rule
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rules_report
msgid "Product Routes Report"
msgstr "Productroute rapportage"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__product_tmpl_id
#: model:ir.model.fields,field_description:stock.field_stock_move__product_tmpl_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__product_tmpl_id
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__product_tmpl_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_tmpl_id
msgid "Product Template"
msgstr "Productsjabloon"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__product_tmpl_id
msgid "Product Tmpl"
msgstr "Product sjabloon"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_scrap__tracking
msgid "Product Tracking"
msgstr "Product tracering"

#. module: stock
#: model:ir.model,name:stock.model_uom_uom
msgid "Product Unit of Measure"
msgstr "Maateenheid product"

#. module: stock
#: model:ir.model,name:stock.model_product_product
msgid "Product Variant"
msgstr "Productvariant"

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_product_normal_action
#: model:ir.ui.menu,name:stock.product_product_menu
msgid "Product Variants"
msgstr "Productvarianten"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
msgid "Product barcode"
msgstr "Barcode product"

#. module: stock
#. odoo-python
#: code:addons/stock/report/product_label_report.py:0
msgid "Product model not defined, Please contact your administrator."
msgstr "Productmodel niet gedefinieerd. Neem contact op met je beheerder."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid ""
"Product this lot/serial number contains. You cannot change it anymore if it "
"has already been moved."
msgstr ""
"Product dat dit partij/serienummer bevat. Het kan niet meer gewijzigd worden"
" als het al verplaatst is."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_uom_name
msgid "Product unit of measure label"
msgstr "Product maateenheid label"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__has_tracking
msgid "Product with Tracking"
msgstr "Product met tracering"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__production
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Production"
msgstr "Productie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__property_stock_production
#: model:ir.model.fields,field_description:stock.field_product_template__property_stock_production
msgid "Production Location"
msgstr "Productielocatie"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Production Locations"
msgstr "Productielocatie"

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/stock_quantity_history.py:0
#: model:ir.actions.act_window,name:stock.act_product_location_open
#: model:ir.actions.act_window,name:stock.product_template_action_product
#: model:ir.model.fields,field_description:stock.field_stock_route__product_ids
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__product_ids
#: model:ir.ui.menu,name:stock.menu_product_in_config_stock
#: model:ir.ui.menu,name:stock.menu_product_variant_config_stock
#: model:ir.ui.menu,name:stock.menu_stock_inventory_control
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Products"
msgstr "Producten"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__products_availability_state
msgid "Products Availability State"
msgstr "Product beschikbaarheidstatus"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__priority
msgid ""
"Products will be reserved first for the transfers with the highest "
"priorities."
msgstr ""
"Producten worden eerst gereserveerd voor de overdrachten met de hoogste "
"prioriteiten."

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid "Products: %(location)s"
msgstr "Producten: %(location)s"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__group_propagation_option__propagate
msgid "Propagate"
msgstr "Doorgeven"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__propagate_cancel
msgid "Propagate cancel and split"
msgstr "Annuleren en splitsen doorgeven"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
msgid "Propagation"
msgstr "Doorgifte"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__group_propagation_option
msgid "Propagation of Procurement Group"
msgstr "Doorgifte van aanvulgroepen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__propagate_carrier
msgid "Propagation of carrier"
msgstr "Doorgifte van vervoerder"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__lot_properties
#: model:ir.model.fields,field_description:stock.field_stock_picking__picking_properties
#: model:ir.model.fields,field_description:stock.field_stock_quant__lot_properties
msgid "Properties"
msgstr "Eigenschappen"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__action__pull_push
msgid "Pull & Push"
msgstr "Pull & Push"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__action__pull
msgid "Pull From"
msgstr "Pull van"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Pull Rule"
msgstr "Pull regel"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__push_domain
msgid "Push Applicability"
msgstr "Push Toepasbaarheid"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Push Rule"
msgstr "Push regel"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__action__push
msgid "Push To"
msgstr "Push naar"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_detailed_operation_tree
msgid "Put in Pack"
msgstr "Inpakken"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Put your products in packs (e.g. parcels, boxes) and track them"
msgstr ""
"Stop je producten in verpakkingen (bv. pakketten, dozen) en volg ze op"

#. module: stock
#: model:ir.model,name:stock.model_stock_putaway_rule
msgid "Putaway Rule"
msgstr "Wegzetregel"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
#: model:ir.actions.act_window,name:stock.category_open_putaway
#: model:ir.actions.act_window,name:stock.location_open_putaway
#: model:ir.model.fields,field_description:stock.field_product_category__putaway_rule_ids
#: model:ir.model.fields,field_description:stock.field_product_product__putaway_rule_ids
#: model:ir.model.fields,field_description:stock.field_stock_location__putaway_rule_ids
#: model:ir.ui.menu,name:stock.menu_putaway
#: model_terms:ir.ui.view,arch_db:stock.product_category_form_view_inherit
#: model_terms:ir.ui.view,arch_db:stock.product_product_view_form_easy_inherit_stock
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
msgid "Putaway Rules"
msgstr "Wegzetregels"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Putaway:"
msgstr "Wegzetten:"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_putaway_tree
msgid "Putaways Rules"
msgstr "Wegzetregels"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_warehouse_orderpoint_qty_multiple_check
msgid "Qty Multiple must be greater than or equal to zero."
msgstr "Veelvoud van hoeveelheid moet groter of gelijk dan nul zijn."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_quality_control
msgid "Quality"
msgstr "Kwaliteit"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Quality Control"
msgstr "Kwaliteitscontrole"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__wh_qc_stock_loc_id
msgid "Quality Control Location"
msgstr "Kwaliteitscontrole locatie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__qc_type_id
msgid "Quality Control Type"
msgstr "Type kwaliteitscontrole"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_quality_control_worksheet
msgid "Quality Worksheet"
msgstr "Kwaliteitswerkbon"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_location__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_request_count__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__quant_ids
msgid "Quant"
msgstr "Hvh"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Quant's creation is restricted, you can't do this operation."
msgstr ""
"Het aanmaken van hoeveelheden is niet mogelijk, je kunt deze bewerking niet "
"uitvoeren."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Quant's editing is restricted, you can't do this operation."
msgstr ""
"Hoeveelheden bewerken is beperkt, je kunt deze bewerking niet uitvoeren."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Quantities Already Set"
msgstr "Hoeveelheden zijn al ingesteld"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Quantities To Reset"
msgstr "Hoeveelheden om te resetten"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Quantities unpacked"
msgstr "Hoeveelheden uitgepakt"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
#: model:ir.model.fields,field_description:stock.field_product_replenish__quantity
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__product_qty
#: model:ir.model.fields,field_description:stock.field_stock_lot_report__quantity
#: model:ir.model.fields,field_description:stock.field_stock_move__quantity
#: model:ir.model.fields,field_description:stock.field_stock_move_line__quantity
#: model:ir.model.fields,field_description:stock.field_stock_quant__quantity
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__quantity
#: model:ir.model.fields,field_description:stock.field_stock_scrap__scrap_qty
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__quantity
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty__quantity
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__quantity
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_move_kandan
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree_detailed
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_operation_tree
msgid "Quantity"
msgstr "Aantal"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
msgid "Quantity Multiple"
msgstr "Veelvoud van aantal"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__qty_available
#: model:ir.model.fields,field_description:stock.field_product_template__qty_available
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form_editable
msgid "Quantity On Hand"
msgstr "Beschikbare voorraad"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "Quantity Received"
msgstr "Ontvangen aantal"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Quantity Relocated"
msgstr "Aantal verplaatst"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form_editable
msgid "Quantity Reserved"
msgstr "Gereserveerde aantal"

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/stock_replenishment_info.py:0
msgid "Quantity available too low"
msgstr "Beschikbare aantal te laag"

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/stock_change_product_qty.py:0
msgid "Quantity cannot be negative."
msgstr "Aantal mag niet negatief zijn."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__is_outdated
msgid "Quantity has been moved since last count"
msgstr "Aantal is verplaatst sinds de laatste telling"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__quantity_product_uom
msgid "Quantity in Product UoM"
msgstr "Aantal in producteenheid"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__availability
msgid "Quantity in stock that can still be reserved for this move"
msgstr ""
"Aantal op voorraad dat nog steeds kan worden gereserveerd voor deze "
"verplaatsing"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__product_qty
msgid "Quantity in the default UoM of the product"
msgstr "Aantal in de standaard maateenheid van het product"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__incoming_qty
msgid ""
"Quantity of planned incoming products.\n"
"In a context with a single Stock Location, this includes goods arriving to this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods arriving to the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods arriving to any Stock Location with 'internal' type."
msgstr ""
"Aantal producten die gepland zijn om het magazijn binnen te komen. \n"
"Bij gebruik van een enkele voorraadlocatie, omvat dit de goederen die deze locatie binnenkomen, of één van de onderliggende locaties. \n"
"Bij gebruik van een enkel magazijn, omvat dit de goederen die deze voorraadlocatie van dit magazijn binnenkomen, of één van de onderliggende locaties. \n"
"Anders, omvat dit goederen die binnenkomen op alle voorraadlocaties van het type 'intern'."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__outgoing_qty
msgid ""
"Quantity of planned outgoing products.\n"
"In a context with a single Stock Location, this includes goods leaving this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods leaving the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods leaving any Stock Location with 'internal' type."
msgstr ""
"Aantal producten die gepland zijn om het magazijn te verlaten. \n"
"Bij gebruik van een enkele voorraadlocatie, omvat dit de goederen die van deze locatie vertrekken, of één van de onderliggende locaties. \n"
"Bij gebruik van een enkel magazijn, omvat dit de goederen die van de voorraadlocatie van dit magazijn vertrekken, of één van de onderliggende locaties. \n"
"Anders, omvat dit goederen die vertrekken op alle voorraadlocaties van het type 'intern'."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__quantity
msgid ""
"Quantity of products in this quant, in the default unit of measure of the "
"product"
msgstr "Aantal van het product in de standaard maateenheid"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__reserved_quantity
msgid ""
"Quantity of reserved products in this quant, in the default unit of measure "
"of the product"
msgstr "Aantal van het product gereserveerd in de standaard maateenheid"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Quantity or Reserved Quantity should be set."
msgstr "Aantal of het gereserveerde aantal moet worden ingesteld."

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "Quantity per Lot"
msgstr "Aantal per partij"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_storage_category_capacity_positive_quantity
msgid "Quantity should be a positive number."
msgstr "Aantal moet een positief getal zijn."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_lot_label_layout__label_quantity
#: model:ir.model.fields,field_description:stock.field_product_label_layout__move_quantity
msgid "Quantity to print"
msgstr "Aantal om af te drukken"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Quantity:"
msgstr "Aantal:"

#. module: stock
#: model:ir.model,name:stock.model_stock_quant
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_lot__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__quant_ids
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Quants"
msgstr "Aantallen"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid ""
"Quants are auto-deleted when appropriate. If you must manually delete them, "
"please ask a stock manager to do it."
msgstr ""
"De hoeveelheden worden automatisch verwijderd wanneer dat nodig is. Als je "
"ze handmatig moet verwijderen, vraag dan aan een voorraadbeheerder om dit te"
" doen."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Quants cannot be created for consumables or services."
msgstr ""
"Hoeveelheden kunnen niet worden aangemaakt voor verbruiksartikelen of "
"diensten."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_return_slip
msgid "RETURN OF"
msgstr "RETOUR VAN"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__rating_ids
#: model:ir.model.fields,field_description:stock.field_stock_picking__rating_ids
#: model:ir.model.fields,field_description:stock.field_stock_scrap__rating_ids
msgid "Ratings"
msgstr "Beoordelingen"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__state__assigned
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Ready"
msgstr "Beschikbaar"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_get_picking_type_ready_moves
msgid "Ready Moves"
msgstr "Beschikbare verplaatsingen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__product_qty
msgid "Real Quantity"
msgstr "Werkelijke aantal"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "Reason"
msgstr "Reden"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__message
msgid "Reason for relocation"
msgstr "Reden van verhuizing"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__code__incoming
msgid "Receipt"
msgstr "Ontvangst"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__reception_route_id
msgid "Receipt Route"
msgstr "Ontvangstroute"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
#: model:ir.actions.act_window,name:stock.action_picking_tree_incoming
#: model:ir.ui.menu,name:stock.in_picking
#: model:stock.picking.type,name:stock.picking_type_in
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Receipts"
msgstr "Ontvangsten"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Receive From"
msgstr "Ontvangen van"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse__reception_steps__one_step
msgid "Receive and Store (1 step)"
msgstr "Ontvangen en opslaan (1 stap)"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Receive in 1 step (stock)"
msgstr "Ontvangen in 1 stap (voorraad)"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Receive in 2 steps (input + stock)"
msgstr "Ontvangen in 2 stappen (ontvangst + voorraad)"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Receive in 3 steps (input + quality + stock)"
msgstr "Ontvangen in 3 stappen (ontvangst + kwaliteitscontrole + voorraad)"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse__reception_steps__two_steps
msgid "Receive then Store (2 steps)"
msgstr "Ontvangen en dan opslaan (2 stappen)"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse__reception_steps__three_steps
msgid "Receive, Quality Control, then Store (3 steps)"
msgstr "Ontvangen, kwaliteitscontrole en opslaan (3 stappen)"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid "Received Qty"
msgstr "Ontvangen aantal"

#. module: stock
#: model:ir.actions.client,name:stock.stock_reception_action
#: model:ir.actions.report,name:stock.stock_reception_report_action
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_reception_report
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Reception Report"
msgstr "Ontvangstrapport"

#. module: stock
#: model:ir.actions.report,name:stock.label_picking
msgid "Reception Report Label"
msgstr "Ontvangstrapportlabel"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Reception Report Labels"
msgstr "Ontvangstrapportlabels"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.help_message_template
msgid ""
"Reduce stockouts with alerts, barcode app, replenishment propositions,\n"
"                        locations management traceability, quality control, etc."
msgstr ""
"Verminder stockouts met waarschuwingen, de barcode-app, aanvullingsvoorstellen,\n"
"                        traceerbaarheid van locatiebeheer, kwaliteitscontrole, enz."

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
#: model:ir.model.fields,field_description:stock.field_procurement_group__name
#: model:ir.model.fields,field_description:stock.field_stock_move__reference
#: model:ir.model.fields,field_description:stock.field_stock_move_line__reference
#: model:ir.model.fields,field_description:stock.field_stock_picking__name
#: model:ir.model.fields,field_description:stock.field_stock_scrap__name
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree_receipt_picking
msgid "Reference"
msgstr "Referentie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__sequence_id
msgid "Reference Sequence"
msgstr "Referentie reeks"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_picking_name_uniq
msgid "Reference must be unique per company!"
msgstr "Referentie moet uniek zijn per bedrijf!"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__origin
msgid "Reference of the document"
msgstr "Referentie van het document"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
msgid "Reference:"
msgstr "Referentie:"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__stock_move_ids
msgid "Related Stock Moves"
msgstr "Gekoppelde voorraadverplaatsingen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_editable
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Relocate"
msgstr "Verplaatsen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_quant_relocate_view_form
msgid "Relocate your stock"
msgstr "Verplaats je voorraad"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Remaining parts of picking partially processed"
msgstr "Resterende deel van de leveringen zijn gedeeltelijk uitgevoerd"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_removal
msgid "Removal"
msgstr "Verwijdering"

#. module: stock
#: model:ir.model,name:stock.model_product_removal
#: model:ir.model.fields,field_description:stock.field_stock_location__removal_strategy_id
msgid "Removal Strategy"
msgstr "Verwijderingsstrategie"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Removal strategy %s not implemented."
msgstr "Verwijderingsstrategie %s is niet geïmplementeerd."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid ""
"Remove manually entered value and replace by the quantity to order based on "
"the forecasted quantities"
msgstr ""
"Verwijder de handmatig ingevoerde waarde en vervang deze door het te "
"bestellen aantal op basis van de voorspelde aantallen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__reordering_max_qty
#: model:ir.model.fields,field_description:stock.field_product_template__reordering_max_qty
msgid "Reordering Max Qty"
msgstr "Aanvulling max. hoeveelheid"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__reordering_min_qty
#: model:ir.model.fields,field_description:stock.field_product_template__reordering_min_qty
msgid "Reordering Min Qty"
msgstr "Aanvullen min. hoeveelheid"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Reordering Rule"
msgstr "Aanvulregel"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_orderpoint
#: model:ir.model.fields,field_description:stock.field_product_product__nbr_reordering_rules
#: model:ir.model.fields,field_description:stock.field_product_template__nbr_reordering_rules
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid "Reordering Rules"
msgstr "Aanvulregels"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Reordering Rules Search"
msgstr "Aanvulregels zoeken"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_buttons.xml:0
#: code:addons/stock/static/src/views/stock_orderpoint_list_view.xml:0
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_product_view_form_easy_inherit_stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "Replenish"
msgstr "Aanvullen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__replenish_location
msgid "Replenish Location"
msgstr "Locatie aanvullen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_scrap__should_replenish
msgid "Replenish Quantities"
msgstr "Hoeveelheden aanvullen"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
#: model:stock.route,name:stock.route_warehouse0_mto
msgid "Replenish on Order (MTO)"
msgstr "Aanvullen per order (MTO)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_product_replenish
msgid "Replenish wizard"
msgstr "Heraanvulling wizard"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
#: model:ir.actions.act_window,name:stock.action_orderpoint_replenish
#: model:ir.actions.server,name:stock.action_replenishment
#: model:ir.ui.menu,name:stock.menu_reordering_rules_replenish
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_editable
msgid "Replenishment"
msgstr "Aanvullen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__replenishment_info_id
msgid "Replenishment Info"
msgstr "Aanvullingsinformatie"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_replenishment_info
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid "Replenishment Information"
msgstr "Aanvullingsinformatie"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_orderpoint.py:0
msgid "Replenishment Information for %(product)s in %(warehouse)s"
msgstr "Aanvullingsinformatie voor %(product)s in %(warehouse)s"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_orderpoint.py:0
msgid "Replenishment Report"
msgstr "Voorraadaanvullen rapportage"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
msgid "Replenishment Report Search"
msgstr "Voorraadaanvullen rapportage zoek"

#. module: stock
#: model:ir.model,name:stock.model_ir_actions_report
msgid "Report Action"
msgstr "Rapport actie"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/multi_print.js:0
msgid "Report Printing Error"
msgstr "Rapportafdrukfout"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_warehouse_report
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "Reporting"
msgstr "Rapportages"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_request_count
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Request a Count"
msgstr "Een telling aanvragen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Request your vendors to deliver to your customers"
msgstr "Vraag je leveranciers om direct te leveren aan je klanten"

#. module: stock
#: model:res.groups,name:stock.group_stock_sign_delivery
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Require a signature on your delivery orders"
msgstr "Verplicht handtekening op je leveringen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__reservation_method
msgid "Reservation Method"
msgstr "Reserveringsmethode"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Reservations"
msgstr "Reserveringen"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "Reserve"
msgstr "Reserveer"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_category__packaging_reserve_method__full
msgid "Reserve Only Full Packagings"
msgstr "Reserveer alleen volledige verpakkingen"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_category__packaging_reserve_method
msgid ""
"Reserve Only Full Packagings: will not reserve partial packagings. If customer orders 2 pallets of 1000 units each and you only have 1600 in stock, then only 1000 will be reserved\n"
"Reserve Partial Packagings: allow reserving partial packagings. If customer orders 2 pallets of 1000 units each and you only have 1600 in stock, then 1600 will be reserved"
msgstr ""
"Reserveer alleen volle verpakkingen: reserveert geen deelverpakkingen. Als de klant 2 pallets van elk 1000 stuks bestelt en je hebt er slechts 1600 op voorraad, dan worden er slechts 1000 gereserveerd\n"
"Deelverpakkingen reserveren: reserveer gedeeltelijke verpakkingen. Als de klant 2 pallets van elk 1000 stuks bestelt en je hebt er maar 1600 op voorraad, dan wordt er 1600 gereserveerd"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_category__packaging_reserve_method
msgid "Reserve Packagings"
msgstr "Verpakkingen reserveren"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_category__packaging_reserve_method__partial
msgid "Reserve Partial Packagings"
msgstr "Deelverpakkingen reserveren"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_package_level__state__assigned
msgid "Reserved"
msgstr "Gereserveerd"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__product_packaging_qty
#: model:ir.model.fields,field_description:stock.field_stock_move_line__product_packaging_qty
msgid "Reserved Packaging Quantity"
msgstr "Gereserveerde verpakkingsaantal"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__reserved_quantity
msgid "Reserved Quantity"
msgstr "Gereserveerde aantal"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid "Reserving a negative quantity is not allowed."
msgstr "Het reserveren van een negatieve aantal is niet toegestaan."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__responsible_id
#: model:ir.model.fields,field_description:stock.field_product_template__responsible_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__user_id
msgid "Responsible"
msgstr "Verantwoordelijke"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__activity_user_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_user_id
msgid "Responsible User"
msgstr "Verantwoordelijke gebruiker"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Resupply"
msgstr "Bevoorrading"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__resupply_wh_ids
msgid "Resupply From"
msgstr "Bevoorraden vanuit"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__warehouseinfo_ids
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__resupply_route_ids
msgid "Resupply Routes"
msgstr "Aanvulroutes"

#. module: stock
#: model:ir.actions.act_window,name:stock.act_stock_return_picking
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_return_picking_form
msgid "Return"
msgstr "Retour"

#. module: stock
#: model:ir.model,name:stock.model_stock_return_picking
msgid "Return Picking"
msgstr "Retour picking"

#. module: stock
#: model:ir.model,name:stock.model_stock_return_picking_line
msgid "Return Picking Line"
msgstr "Retour pickingregel"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Return Slip"
msgstr "Retourbon"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_return_picking_form
msgid "Return for Exchange"
msgstr "Terugsturen voor omruiling"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__return_id
msgid "Return of"
msgstr "Retour van"

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/stock_picking_return.py:0
msgid "Return of %(picking_name)s"
msgstr "Retour van %(picking_name)s"

#. module: stock
#: model:ir.actions.report,name:stock.return_label_report
msgid "Return slip"
msgstr "Retourbon"

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/stock_picking_return.py:0
msgid "Returned Picking"
msgstr "Geretourneerde picking"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: model:ir.model.fields,field_description:stock.field_stock_picking__return_ids
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Returns"
msgstr "Retouren"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Returns Type"
msgstr "Retourtype"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_quant_package__package_use__reusable
msgid "Reusable Box"
msgstr "Herbruikbare doos"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant_package__package_use
msgid ""
"Reusable boxes are used for batch picking and emptied afterwards to be reused. In the barcode application, scanning a reusable box will add the products in this box.\n"
"        Disposable boxes aren't reused, when scanning a disposable box in the barcode application, the contained products are added to the transfer."
msgstr ""
"Herbruikbare dozen worden gebruikt voor batch en daarna geleegd voor hergebruik. Als je in de barcodetoepassing een herbruikbare doos scant, worden de producten in deze doos toegevoegd.\n"
"         Wegwerpdozen worden niet hergebruikt, bij het scannen van een wegwerpdoos in de barcode-applicatie worden de ingesloten producten toegevoegd aan de overdracht."

#. module: stock
#: model:ir.actions.server,name:stock.action_revert_inventory_adjustment
msgid "Revert Inventory Adjustment"
msgstr "Voorraadaanpassing terugdraaien"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__route_id
#: model:ir.model.fields,field_description:stock.field_stock_route__name
#: model:ir.model.fields,field_description:stock.field_stock_rule__route_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__route_id
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_filter
msgid "Route"
msgstr "Route"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__route_company_id
msgid "Route Company"
msgstr "Route bedrijf"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__route_sequence
msgid "Route Sequence"
msgstr "Route reeks"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_routes_form
#: model:ir.actions.server,name:stock.action_open_routes
#: model:ir.model.fields,field_description:stock.field_product_category__route_ids
#: model:ir.model.fields,field_description:stock.field_product_packaging__route_ids
#: model:ir.model.fields,field_description:stock.field_product_product__route_ids
#: model:ir.model.fields,field_description:stock.field_product_template__route_ids
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__route_ids
#: model:ir.ui.menu,name:stock.menu_routes_config
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_tree
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Routes"
msgstr "Routes"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__has_available_route_ids
#: model:ir.model.fields,field_description:stock.field_product_template__has_available_route_ids
msgid "Routes can be selected on this product"
msgstr "Op dit product kunnen routes worden geselecteerd"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__resupply_wh_ids
msgid ""
"Routes will be created automatically to resupply this warehouse from the "
"warehouses ticked"
msgstr ""
"Routes worden automatisch aangemaakt om dit magazijn te bevoorraden vanuit "
"de aangevinkte magazijnen"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_replenishment_info__warehouseinfo_ids
#: model:ir.model.fields,help:stock.field_stock_warehouse__resupply_route_ids
msgid ""
"Routes will be created for these resupply warehouses and you can select them"
" on products and product categories"
msgstr ""
"Routes worden aangemaakt voor deze aanvul magazijnen en je kunt deze "
"selecteren op producten en productcategorieën"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_location.py:0
#: code:addons/stock/models/stock_rule.py:0
msgid ""
"Rule %(rule)s belongs to %(rule_company)s while the route belongs to "
"%(route_company)s."
msgstr ""
"De regel %(rule)s behoort tot %(rule_company)s en de route behoort tot "
"%(route_company)s."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__rule_message
msgid "Rule Message"
msgstr "Regel bericht"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_rules_form
#: model:ir.model.fields,field_description:stock.field_stock_route__rule_ids
#: model:ir.ui.menu,name:stock.menu_action_rules_form
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_tree
msgid "Rules"
msgstr "Regels"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
msgid "Rules on Categories"
msgstr "Regels op categorieën"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
msgid "Rules on Products"
msgstr "Regels op producten"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__rule_ids
msgid "Rules used"
msgstr "Regels gebruikt"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "S0001"
msgstr "S0001"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_stock_sms
msgid "SMS Confirmation"
msgstr "SMS bevestiging"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__message_has_sms_error
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_has_sms_error
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS fout bij versturen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "SSCC Demo"
msgstr "SSCC-demo"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "SSCC:"
msgstr "SSCC:"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid "STANDARD TERMS AND CONDITIONS OF SALE"
msgstr "ALGEMENE VOORWAARDEN"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
msgid "Sales History"
msgstr "Verkoopgeschiedenis"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Sample data"
msgstr "Voorbeeld data"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__date_planned
#: model:ir.model.fields,field_description:stock.field_stock_move_line__scheduled_date
#: model:ir.model.fields,field_description:stock.field_stock_picking__scheduled_date
#: model:ir.model.fields,field_description:stock.field_stock_quant__inventory_date
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Scheduled Date"
msgstr "Geplande datum"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__date
#: model:ir.model.fields,help:stock.field_stock_move_line__scheduled_date
msgid "Scheduled date until move is done, then date of actual move processing"
msgstr ""
"Geplande datum totdat de verplaatsing is voltooid, vervolgens de datum "
"waarop de daadwerkelijke verplaatsing is verwerkt"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Scheduled or processing date"
msgstr "Geplande datum of verwerkingsdatum"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__scheduled_date
msgid ""
"Scheduled time for the first part of the shipment to be processed. Setting "
"manually a value here would set it as expected date for all the stock moves."
msgstr ""
"De geplande tijd wanneer het eerste deel van levering wordt verwerkt. Het "
"hier instellen van een handmatige waarde zal deze datum instellen als "
"verwachte datum voor alle voorraadverplaatsingen."

#. module: stock
#: model:ir.actions.server,name:stock.action_scrap
#: model:ir.model,name:stock.model_stock_scrap
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__scrap_id
#: model:ir.ui.menu,name:stock.menu_stock_scrap
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view2
msgid "Scrap"
msgstr "Afkeuren"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_scrap__scrap_location_id
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
msgid "Scrap Location"
msgstr "Afkeurlocatie"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_scrap
msgid "Scrap Orders"
msgstr "Afkeurorders"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view2
msgid "Scrap Products"
msgstr "Producten afkeuren"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_scrap__scrap_reason_tag_ids
msgid "Scrap Reason"
msgstr "Afkeurreden"

#. module: stock
#: model:ir.model,name:stock.model_stock_scrap_reason_tag
msgid "Scrap Reason Tag"
msgstr "Afkeurredenlabel"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__scrap_id
msgid "Scrap operation"
msgstr "Bewerking afkeuren"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_stock_scrap
msgid "Scrap products"
msgstr "Afgekeurde producten"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__scrapped
msgid "Scrapped"
msgstr "Afgekeurd"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_stock_scrap
msgid ""
"Scrapping a product will remove it from your stock. The product will\n"
"                end up in a scrap location that can be used for reporting purpose."
msgstr ""
"Door een product af te keuren wordt deze verwijderd uit de voorraad. Het product wordt\n"
"opgenomen in de afkeurlocatie, welke kan worden gebruikt voor rapportages."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Scraps"
msgstr "Afkeuringen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_filter
msgid "Search Procurement"
msgstr "Aanvulling zoeken"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
msgid "Search Scrap"
msgstr "Zoek afgekeurd"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
#: code:addons/stock/models/stock_picking.py:0
msgid "Search not supported without a value."
msgstr "Zoekopdracht niet ondersteund zonder waarde."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.replenishment_option_tree_view
msgid "Select Route"
msgstr "Selecteer route"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Select the places where this route can be selected"
msgstr "Selecteer de plaatsen waar deze route kan worden ingesteld"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product_strategy.py:0
msgid ""
"Selected storage category does not exist in the 'store to' location or any "
"of its sublocations"
msgstr ""
"De geselecteerde opslagcategorie bestaat niet in de locatie 'opslaan in' of "
"in een van de sublocaties daarvan"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_partner__picking_warn
#: model:ir.model.fields,help:stock.field_res_users__picking_warn
msgid ""
"Selecting the \"Warning\" option will notify user with the message, "
"Selecting \"Blocking Message\" will throw an exception with the message and "
"block the flow. The Message has to be written in the next field."
msgstr ""
"Door de 'Waarschuwingsoptie' te selecteren wordt het bericht naar de "
"gebruiker gestuurd. Door het 'Blokkerend bericht' te kiezen wordt een fout "
"gegenereerd met de boodschap en het proces wordt geblokkeerd. Het bericht "
"moet in het volgende veld worden ingevoerd."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "Selection not supported."
msgstr "Niet ondersteunde selectie."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Sell and purchase products in different units of measure"
msgstr "Verkoop- en inkoopproducten in verschillende maateenheden"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Send an automatic confirmation SMS Text Message when Delivery Orders are "
"done"
msgstr ""
"Stuur een automatische bevestiging per SMS-bericht als de leveringen gereed "
"zijn"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Send an automatic confirmation email when Delivery Orders are done"
msgstr ""
"Verzend een automatische bevestiging per e-mail wanneer leveringen gereed "
"zijn"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_lead_mass_mail
msgid "Send email"
msgstr "Verzend e-mail"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_sendcloud
msgid "Sendcloud Connector"
msgstr "Sendcloud connector"

#. module: stock
#: model:mail.template,description:stock.mail_template_data_delivery_confirmation
msgid ""
"Sent to the customers when orders are delivered, if the setting is enabled"
msgstr ""
"Verzonden naar de klanten als orders geleverd zijn, als de parameter "
"geactiveerd is"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__barcode_separator
msgid "Separator"
msgstr "Scheidingsteken"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__9
msgid "September"
msgstr "September"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__sequence
#: model:ir.model.fields,field_description:stock.field_stock_package_type__sequence
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__sequence
#: model:ir.model.fields,field_description:stock.field_stock_route__sequence
#: model:ir.model.fields,field_description:stock.field_stock_rule__sequence
#: model:ir.model.fields,field_description:stock.field_stock_scrap_reason_tag__sequence
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__sequence
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
msgid "Sequence"
msgstr "Reeks"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Sequence %(code)s"
msgstr "Volgorde %(code)s"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__sequence_code
msgid "Sequence Prefix"
msgstr "Reeks voorvoegsel"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__lot_ids
msgid "Serial Numbers"
msgstr "Serienummers"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid ""
"Serial number (%(serial_number)s) already exists in location(s): "
"%(location_list)s. Please correct the serial number encoded."
msgstr ""
"Serienummer (%(serial_number)s) bestaat al in locatie(s): %(location_list)s."
" Corrigeer het gecodeerde serienummer."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid ""
"Serial number (%(serial_number)s) is not located in %(source_location)s, but is located in location(s): %(other_locations)s.\n"
"\n"
"Please correct this to prevent inconsistent data."
msgstr ""
"Serienummer (%(serial_number)s) bevindt zich niet in %(source_location)s, maar bevindt zich in locatie(s): %(other_locations)s.\n"
"\n"
"Corrigeer dit om inconsistente gegevens te voorkomen."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid ""
"Serial number (%(serial_number)s) is not located in %(source_location)s, but is located in location(s): %(other_locations)s.\n"
"\n"
"Source location for this move will be changed to %(recommended_location)s"
msgstr ""
"Serienummer (%(serial_number)s) bevindt zich niet in %(source_location)s, maar bevindt zich in locatie(s): %(other_locations)s.\n"
"\n"
"De basislocatie voor deze boeking wordt gewijzigd naar %(recommended_location)s"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "Serial numbers"
msgstr "Serienummers"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Set"
msgstr "Instellen"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_request_count__set_count__set
msgid "Set Current Value"
msgstr "Huidige waarde instellen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Set Warehouse Routes"
msgstr "Stel magazijnroutes in"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_category__removal_strategy_id
msgid ""
"Set a specific removal strategy that will be used regardless of the source location for this product category.\n"
"\n"
"FIFO: products/lots that were stocked first will be moved out first.\n"
"LIFO: products/lots that were stocked last will be moved out first.\n"
"Closest location: products/lots closest to the target location will be moved out first.\n"
"FEFO: products/lots with the closest removal date will be moved out first (the availability of this method depends on the \"Expiration Dates\" setting).\n"
"Least Packages: FIFO but with the least number of packages possible when there are several packages containing the same product."
msgstr ""
"Stel een specifieke verwijderingsstrategie in die wordt gebruikt, ongeacht de bronlocatie voor deze productcategorie.\n"
"\n"
"FIFO: producten/partijen die het eerst op voorraad waren, gaan als eerste de deur uit.\n"
"LIFO: producten/partijen die het laatst op voorraad waren, gaan als eerste de deur uit.\n"
"Dichtstbijzijnde locatie: producten/partijen die zich het dichtst bij de doellocatie bevinden, worden als eerste verplaatst.\n"
"FEFO: producten/partijen met de dichtstbijzijnde verwijderdatum worden als eerste verplaatst (de beschikbaarheid van deze methode hangt af van de instelling 'Vervaldatums').\n"
"Minst aantal verpakkingen: FIFO, maar met het kleinst mogelijke aantal verpakkingen als er meerdere verpakkingen zijn die hetzelfde product bevatten."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Set expiration dates on lots & serial numbers"
msgstr "Stel vervaldata in op partij- en serienummers"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Set owner on stored products"
msgstr "Stel eigenaar op opgeslagen producten in"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Set product attributes (e.g. color, size) to manage variants"
msgstr ""
"Stel productkenmerken in (bijvoorbeeld kleur, grootte) om varianten te "
"beheren"

#. module: stock
#: model:ir.actions.server,name:stock.action_view_set_to_zero_quants_tree
msgid "Set to 0"
msgstr "Instellen op 0"

#. module: stock
#: model:ir.actions.server,name:stock.action_view_set_quants_tree
msgid "Set to quantity on hand"
msgstr "Instellen op beschikbare aantal"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_config_settings
#: model:ir.ui.menu,name:stock.menu_stock_general_settings
msgid "Settings"
msgstr "Instellingen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "Shelf A"
msgstr "Plank A"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__posy
msgid "Shelves (Y)"
msgstr "Schap (Y)"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_lot_report
msgid "Ship a lot to a customer."
msgstr "Veel naar een klant verzenden."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Shipments"
msgstr "Verzendingen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Shipping"
msgstr "Verzenden"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Shipping Connectors"
msgstr "Verzendconnectoren"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_type
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__move_type
msgid "Shipping Policy"
msgstr "Leveringsbeleid"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__shipping_weight
msgid "Shipping Weight"
msgstr "Aflevergewicht"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Shipping connectors allow to compute accurate shipping costs, print shipping"
" labels and request carrier picking at your warehouse to ship to the "
"customer. Apply shipping connector from delivery methods."
msgstr ""
"Met verzendconnectoren kun je nauwkeurig de verzendkosten berekenen, "
"verzendlabels afdrukken en ophaling in je magazijn aanvragen. Pas een "
"verzendconnector toe op een verzendwijze."

#. module: stock
#: model:mail.template,name:stock.mail_template_data_delivery_confirmation
msgid "Shipping: Send by Email"
msgstr "Verzending: Verzenden per e-mail"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_shiprocket
msgid "Shiprocket Connector"
msgstr "Shiprocket Connector"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__code
msgid "Short Name"
msgstr "Korte naam"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__code
msgid "Short name used to identify your warehouse"
msgstr "Korte naam om je magazijn te identificeren"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_allocation
msgid "Show Allocation"
msgstr "Toewijziging weergeven"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_check_availability
msgid "Show Check Availability"
msgstr "Toon controle beschikbaarheid"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__show_operations
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_operations
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__show_operations
msgid "Show Detailed Operations"
msgstr "Toon gedetailleerde bewerkingen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__show_forecasted_qty_status_button
#: model:ir.model.fields,field_description:stock.field_product_template__show_forecasted_qty_status_button
msgid "Show Forecasted Qty Status Button"
msgstr "Knop voor verwachte hoeveelheid weergeven"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_level__show_lots_m2o
msgid "Show Lots M2O"
msgstr "Toon partijen MTO"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_level__show_lots_text
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_lots_text
msgid "Show Lots Text"
msgstr "Toon partijtekst"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_next_pickings
msgid "Show Next Pickings"
msgstr "Toon volgende pickings"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__show_on_hand_qty_status_button
#: model:ir.model.fields,field_description:stock.field_product_template__show_on_hand_qty_status_button
msgid "Show On Hand Qty Status Button"
msgstr "Knop voor beschikbare hoeveelheid weergeven"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__is_favorite
msgid "Show Operation in Overview"
msgstr "Toon bewerking in overzicht"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__show_picking_type
msgid "Show Picking Type"
msgstr "Toon pickingsoort"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__show_quant
msgid "Show Quant"
msgstr "Toon hoeveelheid"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__auto_show_reception_report
msgid "Show Reception Report at Validation"
msgstr "Ontvangstrapport weergeven bij bevestigen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__show_transfers
msgid "Show Transfers"
msgstr "Overdrachten weergeven"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Show all records which has next action date is before today"
msgstr "Toon alle records welke een actiedatum voor vandaag hebben"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__show_lots_m2o
msgid "Show lot_id"
msgstr "Toon lot_id"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__show_lots_text
msgid "Show lot_name"
msgstr "Toon kavelnaam"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rules_report__warehouse_ids
msgid "Show the routes that apply on selected warehouses."
msgstr ""
"Toon de routes welke van toepassing zijn op de geselecteerde magazijnen."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Sign"
msgstr "Ondertekenen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_sign_delivery
#: model:ir.model.fields,field_description:stock.field_stock_picking__signature
#: model:ir.model.fields,help:stock.field_stock_picking__signature
msgid "Signature"
msgstr "Handtekening"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "Signed"
msgstr "Getekend"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "Size"
msgstr "Grootte"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "Size: Length × Width × Height"
msgstr "Maat: Lengte × Breedte × Hoogte"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/views/stock_orderpoint_list_view.xml:0
#: model:ir.actions.act_window,name:stock.action_orderpoint_snooze
#: model_terms:ir.ui.view,arch_db:stock.view_stock_orderpoint_snooze
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid "Snooze"
msgstr "Snooze"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__snoozed_until
msgid "Snooze Date"
msgstr "Snooze datum"

#. module: stock
#: model:ir.model,name:stock.model_stock_orderpoint_snooze
msgid "Snooze Orderpoint"
msgstr "Snooze aanvulregel"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__predefined_date
msgid "Snooze for"
msgstr "Snooze voor"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__snoozed_until
msgid "Snoozed"
msgstr "Gesnoozed"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_warning_set_view
msgid "Some selected lines already have quantities set, they will be ignored."
msgstr ""
"Voor sommige geselecteerde regels zijn al hoeveelheden ingesteld, deze "
"worden genegeerd."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__origin
msgid "Source"
msgstr "Bron"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__origin
#: model:ir.model.fields,field_description:stock.field_stock_picking__origin
#: model:ir.model.fields,field_description:stock.field_stock_scrap__origin
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Source Document"
msgstr "Brondocument"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
#: model:ir.model.fields,field_description:stock.field_stock_move__location_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_location_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__location_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__default_location_src_id
#: model:ir.model.fields,field_description:stock.field_stock_rule__location_src_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__location_id
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Source Location"
msgstr "Bronlocatie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__location_usage
#: model:ir.model.fields,field_description:stock.field_stock_move_line__location_usage
msgid "Source Location Type"
msgstr "Type bronlocatie"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Source Location:"
msgstr "Bronlocatie:"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_report_label
msgid "Source Name"
msgstr "Naam van bron"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__package_id
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
msgid "Source Package"
msgstr "Bronverpakking"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Source Package:"
msgstr "Bronverpakking:"

#. module: stock
#: model:ir.actions.server,name:stock.stock_split_picking
msgid "Split"
msgstr "Splitsen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Starred"
msgstr "Met ster"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Starred Products"
msgstr "Producten met ster"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_starshipit
msgid "Starshipit Connector"
msgstr "Starshipit-connector"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__state
#: model:ir.model.fields,field_description:stock.field_stock_package_level__state
msgid "State"
msgstr "Status"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__state
#: model:ir.model.fields,field_description:stock.field_stock_move_line__state
#: model:ir.model.fields,field_description:stock.field_stock_picking__state
#: model:ir.model.fields,field_description:stock.field_stock_scrap__state
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Status"
msgstr "Status"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_lot__activity_state
#: model:ir.model.fields,help:stock.field_stock_picking__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status gebaseerd op activiteiten\n"
"Te laat: Datum is al gepasseerd\n"
"Vandaag: Activiteit datum is vandaag\n"
"Gepland: Toekomstige activiteiten."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
#: model:ir.actions.act_window,name:stock.action_product_stock_view
#: model:ir.ui.menu,name:stock.menu_product_stock
msgid "Stock"
msgstr "Voorraad"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_stock_barcode_barcodelookup
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Stock Barcode Database"
msgstr "Voorraad Barcode Database"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "Stock In Transit"
msgstr "Stock in transit"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
#: model_terms:ir.ui.view,arch_db:stock.view_location_tree2
msgid "Stock Location"
msgstr "Voorraadlocatie"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Stock Locations"
msgstr "Voorraadlocaties"

#. module: stock
#: model:ir.model,name:stock.model_stock_move
#: model:ir.model.fields,field_description:stock.field_product_product__stock_move_ids
msgid "Stock Move"
msgstr "Voorraadverplaatsing"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_ids
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_move_tree
msgid "Stock Moves"
msgstr "Verplaatsingen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_graph
#: model_terms:ir.ui.view,arch_db:stock.view_move_pivot
msgid "Stock Moves Analysis"
msgstr "Voorraadverplaatsingsanalyse"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__move_id
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view
msgid "Stock Operation"
msgstr "Voorraadbewerking"

#. module: stock
#: model:ir.model,name:stock.model_stock_package_destination
msgid "Stock Package Destination"
msgstr "Voorraadverpakking bestemming"

#. module: stock
#: model:ir.model,name:stock.model_stock_package_level
msgid "Stock Package Level"
msgstr "Voorraad verpakkingsniveau"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_partner__picking_warn
#: model:ir.model.fields,field_description:stock.field_res_users__picking_warn
msgid "Stock Picking"
msgstr "Voorraadpicking"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__stock_quant_ids
#: model_terms:ir.ui.view,arch_db:stock.stock_quant_view_graph
msgid "Stock Quant"
msgstr "Voorraadhoeveelheid"

#. module: stock
#: model:ir.model,name:stock.model_stock_quantity_history
msgid "Stock Quantity History"
msgstr "Voorraadaantal geschiedenis"

#. module: stock
#: model:ir.model,name:stock.model_stock_quant_relocate
msgid "Stock Quantity Relocation"
msgstr "Verplaatsing van voorraadaantal"

#. module: stock
#: model:ir.model,name:stock.model_report_stock_quantity
msgid "Stock Quantity Report"
msgstr "Voorraadaantallenrapportage"

#. module: stock
#: model:ir.model,name:stock.model_report_stock_report_reception
msgid "Stock Reception Report"
msgstr "Voorraadontvangstrapport"

#. module: stock
#: model:ir.model,name:stock.model_stock_forecasted_product_product
#: model:ir.model,name:stock.model_stock_forecasted_product_template
msgid "Stock Replenishment Report"
msgstr "Voorraadaanvulling rapport"

#. module: stock
#: model:ir.model,name:stock.model_stock_request_count
msgid "Stock Request an Inventory Count"
msgstr "Voorraad Een voorraadtelling aanvragen"

#. module: stock
#: model:ir.model,name:stock.model_stock_rule
#: model:ir.model.fields,field_description:stock.field_stock_move__rule_id
msgid "Stock Rule"
msgstr "Voorraadregel"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_rules_report
msgid "Stock Rules Report"
msgstr "Voorraadregelsrapport"

#. module: stock
#: model:ir.model,name:stock.model_stock_rules_report
msgid "Stock Rules report"
msgstr "Voorraadroutes rapport"

#. module: stock
#: model:ir.model,name:stock.model_stock_track_confirmation
msgid "Stock Track Confirmation"
msgstr "Voorraad traceer bevestiging"

#. module: stock
#: model:ir.model,name:stock.model_stock_track_line
msgid "Stock Track Line"
msgstr "Voorraad traceerregel"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_ids_without_package
msgid "Stock move"
msgstr "Voorraadverplaatsing"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Stock moves that are Available (Ready to process)"
msgstr ""
"Voorraadverplaatsingen welke beschikbaar zijn (gereed voor verwerking)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Stock moves that are Confirmed, Available or Waiting"
msgstr "Voorraadverplaatsingen welke zijn bevestigd, beschikbaar of wachtend"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Stock moves that have been processed"
msgstr "Voorraadverplaatsingen welke zijn verwerkt"

#. module: stock
#: model:ir.model,name:stock.model_stock_package_type
msgid "Stock package type"
msgstr "Verpakkingssoort"

#. module: stock
#: model:ir.model,name:stock.model_report_stock_report_stock_rule
msgid "Stock rule report"
msgstr "Voorraadregelrapportage"

#. module: stock
#: model:ir.model,name:stock.model_stock_replenishment_info
msgid "Stock supplier replenishment information"
msgstr "Informatie over het aanvullen van voorraadleveranciers"

#. module: stock
#: model:ir.model,name:stock.model_stock_replenishment_option
msgid "Stock warehouse replenishment option"
msgstr "Optie om voorraadmagazijn aan te vullen"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Storage"
msgstr "Opslag"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
msgid "Storage Capacities"
msgstr "Opslagcapaciteiten"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_storage_category
#: model:ir.ui.menu,name:stock.menu_storage_categoty_config
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_tree
msgid "Storage Categories"
msgstr "Opslagcategorieën"

#. module: stock
#: model:ir.model,name:stock.model_stock_storage_category
#: model:ir.model.fields,field_description:stock.field_stock_location__storage_category_id
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__storage_category_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__storage_category_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__name
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__storage_category_id
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_form
msgid "Storage Category"
msgstr "Opslagcategorie"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_storage_category_capacity
#: model:ir.model,name:stock.model_stock_storage_category_capacity
#: model:ir.model.fields,field_description:stock.field_product_product__storage_category_capacity_ids
#: model:ir.model.fields,field_description:stock.field_stock_package_type__storage_category_capacity_ids
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_capacity_tree
msgid "Storage Category Capacity"
msgstr "Opslagcategorie capaciteit"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_multi_locations
msgid "Storage Locations"
msgstr "Opslaglocaties"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__store_type_id
msgid "Storage Type"
msgstr "Opslagtype"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_operation_tree
msgid "Store To"
msgstr "Opslaan in"

#. module: stock
#: model_terms:digest.tip,tip_description:stock.digest_tip_stock_1
msgid ""
"Store and retrieve information regarding every Lot/Serial Number (condition,"
" product info, ...)."
msgstr ""
"Informatie over elk lot/serienummer opslaan en opvragen (toestand, "
"productinformatie, ...)."

#. module: stock
#: model:ir.model.fields,help:stock.field_res_config_settings__group_stock_multi_locations
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Store products in specific locations of your warehouse (e.g. bins, racks) "
"and to track inventory accordingly."
msgstr ""
"Sla producten op in een bepaalde locatie in je magazijn (bijv. locatie, rek)"
" en om zo de voorraad te volgen."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "Store to"
msgstr "Opslaan in"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__location_out_id
msgid "Store to sublocation"
msgstr "Opslaan naar sublocatie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__sublocation
msgid "Sublocation"
msgstr "Sublocatie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_route__supplied_wh_id
msgid "Supplied Warehouse"
msgstr "Geleverd magazijn"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__procure_method
#: model:ir.model.fields,field_description:stock.field_stock_rule__procure_method
msgid "Supply Method"
msgstr "Bevoorradingsmethode"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_route__supplier_wh_id
msgid "Supplying Warehouse"
msgstr "Leverend magazijn"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_scrap_reason_tag_name_uniq
msgid "Tag name already exists!"
msgstr "Labelnaam bestaat al!"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__procure_method__make_to_stock
msgid "Take From Stock"
msgstr "Neem uit voorraad"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__procure_method__mts_else_mto
msgid "Take From Stock, if unavailable, Trigger Another Rule"
msgstr "Neem uit voorraad, indien niet beschikbaar, activeer een andere regel"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__procure_method
msgid ""
"Take From Stock: the products will be taken from the available stock of the source location.\n"
"Trigger Another Rule: the system will try to find a stock rule to bring the products in the source location. The available stock will be ignored.\n"
"Take From Stock, if Unavailable, Trigger Another Rule: the products will be taken from the available stock of the source location.If there is no stock available, the system will try to find a  rule to bring the products in the source location."
msgstr ""
"Uit voorraad nemen: de producten worden uit de beschikbare voorraad van de bronlocatie gehaald.\n"
"Een andere regel activeren: het systeem zal proberen een voorraadregel te vinden om de producten naar de bronlocatie te brengen. De beschikbare voorraad wordt genegeerd.\n"
"Uit voorraad nemen, indien niet beschikbaar, een andere regel activeren: de producten worden uit de beschikbare voorraad van de bronlocatie genomen. Als er geen voorraad beschikbaar is, probeert het systeem een regel te vinden om de producten naar de bronlocatie te brengen."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__show_allocation
msgid ""
"Technical Field used to decide whether the button \"Allocation\" should be "
"displayed."
msgstr ""
"Technisch veld dat wordt gebruikt om te beslissen of de knop \"Toewijzing\" "
"moet worden weergegeven."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Technical Information"
msgstr "Technische informatie"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__show_check_availability
msgid ""
"Technical field used to compute whether the button \"Check Availability\" "
"should be displayed."
msgstr ""
"Technisch veld dat wordt gebruikt om te berekenen of de knop "
"\"Beschikbaarheid controleren\" moet worden weergegeven."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__product_tmpl_id
msgid "Template"
msgstr "Sjabloon"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_location.py:0
msgid ""
"The %s location is required by the Inventory app and cannot be deleted, but "
"you can archive it."
msgstr ""
"De %s ocatie is vereist door de Voorraad-app en kan niet worden verwijderd. "
"Je kunt de locatie wel archiveren."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__auto
msgid ""
"The 'Manual Operation' value will create a stock move after the current one."
" With 'Automatic No Step Added', the location is replaced in the original "
"move."
msgstr ""
"De waarde 'Handmatige bewerking' zorgt voor een aanvullende verplaatsing na "
"de huidige. Met 'Automatisch - geen stap toegevoegd' wordt de locatie in de "
"oorspronkelijke verplaatsing vervangen."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "The Lot/Serial number (%s) is linked to another product."
msgstr "Partij/serienummer (%s) is gekoppeld aan een ander product."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid ""
"The Serial Number (%(serial_number)s) is already used in location(s): %(location_list)s.\n"
"\n"
"Is this expected? For example, this can occur if a delivery operation is validated before its corresponding receipt operation is validated. In this case the issue will be solved automatically once all steps are completed. Otherwise, the serial number should be corrected to prevent inconsistent data."
msgstr ""
"Het serienummer (%(serial_number)s) wordt al gebruikt op locatie(s): %(location_list)s.\n"
"\n"
"Wordt dit verwacht? Dit kan bijvoorbeeld gebeuren als een leveringsbewerking wordt bevestigd voordat de bijbehorende ontvangstbewerking wordt bevestigd. In dit geval wordt het probleem automatisch opgelost zodra alle stappen zijn voltooid. Anders moeten de serienummers worden gecorrigeerd om inconsistente gegevens te voorkomen."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "The backorder %s has been created."
msgstr "De backorder %s is aangemaakt."

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_location_barcode_company_uniq
msgid "The barcode for a location must be unique per company!"
msgstr "De barcode voor een locatie moet per bedrijf uniek zijn!"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"The client explicitly waives its own standard terms and conditions, even if "
"these were drawn up after these standard terms and conditions of sale. In "
"order to be valid, any derogation must be expressly agreed to in advance in "
"writing."
msgstr ""
"De klant doet uitdrukkelijk afstand van zijn eigen standaard algemene "
"voorwaarden, ook indien deze zijn opgesteld na deze algemene "
"verkoopvoorwaarden. Om geldig te zijn, moet elke afwijking vooraf "
"uitdrukkelijk schriftelijk worden overeengekomen."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_lot.py:0
msgid ""
"The combination of lot/serial number and product must be unique within a company including when no company is defined.\n"
"The following combinations contain duplicates:\n"
"%(error_lines)s"
msgstr ""
"De combinatie van lot/serienummer en product moet uniek zijn binnen een bedrijf, ook als er geen bedrijf is gedefinieerd.\n"
"De volgende combinaties bevatten duplicaten:\n"
"%(error_lines)s"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__company_id
msgid "The company is automatically set from your user preferences."
msgstr "Het bedrijf is automatisch ingesteld uit gebruikers voorkeuren."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__search_date_category__day_2
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "The day after tomorrow"
msgstr "Overmorgen"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "The deadline has been automatically updated due to a delay on %s."
msgstr "De deadline is automatisch bijgewerkt vanwege een vertraging op %s."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__delay
msgid ""
"The expected date of the created transfer will be computed based on this "
"lead time."
msgstr ""
"De verwachte datum van de aangemaakte overdracht wordt berekend op basis van"
" deze doorlooptijd."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_package_type__sequence
msgid "The first in the sequence is the default one."
msgstr "De eerste in de reeks is de standaard."

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/product_replenish.py:0
msgid "The following replenishment order have been generated"
msgstr "De volgende aanvulopdracht is gegenereerd"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_product_replenish
msgid "The forecasted quantity of"
msgstr "Het voorspelde aantal"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
msgid "The forecasted stock on the"
msgstr "De voorraadprognose op"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_orderpoint.py:0
msgid "The inter-warehouse transfers have been generated"
msgstr "De overdrachten tussen magazijnen zijn gegenereerd"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid "The inventory adjustments have been reverted."
msgstr "De voorraadaanpassingen zijn teruggedraaid."

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_location_inventory_freq_nonneg
msgid "The inventory frequency (days) for a location must be non-negative"
msgstr "De voorraadfrequentie (dagen) voor een locatie mag niet negatief zijn"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_orderpoint.py:0
msgid ""
"The minimum quantity must be less than or equal to the maximum quantity."
msgstr ""
"Het minimumaantal moet kleiner zijn dan of gelijk aan het maximumaantal."

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_warehouse_warehouse_name_uniq
msgid "The name of the warehouse must be unique per company!"
msgstr "De naam van het magazijn moet uniek zijn per bedrijf!"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "The number of Serial Numbers to generate must be greater than zero."
msgstr "Het aantal te genereren serienummers moet groter zijn dan nul."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__location_final_id
msgid ""
"The operation brings the products to the intermediate location.But this "
"operation is part of a chain of operations targeting the final location."
msgstr ""
"De bewerking brengt de producten naar de tussenliggende locatie. Maar deze "
"bewerking maakt deel uit van een keten van bewerkingen die gericht zijn op "
"de eindlocatie."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__location_id
msgid "The operation takes and suggests products from this location."
msgstr "De bewerking neemt en biedt producten aan vanuit deze locatie."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_picking_type_action
msgid ""
"The operation type system allows you to assign each stock\n"
"            operation a specific type which will alter its views accordingly.\n"
"            On the operation type you could e.g. specify if packing is needed by default,\n"
"            if it should show the customer."
msgstr ""
"Met het systeem van het bewerkingssoort kun je elke voorraad toewijzen\n"
"            bewerking een specifiek type dat zijn standpunten dienovereenkomstig zal veranderen.\n"
"            Op de bewerkingssoort kun je bijv. specificeren of de verpakking standaard nodig is,\n"
"            als het de klant moet laten zien."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__location_dest_id
msgid "The operations brings product to this location"
msgstr "De bewerkingen leveren producten aan deze locatie"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__package_id
msgid "The package containing this quant"
msgstr "De verpakking dat deze hoeveelheid bevat"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__location_id
msgid ""
"The parent location that includes this location. Example : The 'Dispatch "
"Zone' is the 'Gate 1' parent location."
msgstr ""
"De bovenliggende locatie waar deze locatie zich bevindt. Bijvoorbeeld: De "
"'Verzendzone' is de bovenliggende van 'Deur 1'."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__qty_multiple
msgid ""
"The procurement quantity will be rounded up to a multiple of this field "
"quantity. If it is 0, it is not rounded."
msgstr ""
"Het aan te vullen aantal wordt naar boven afgerond op een veelvoud van dit "
"veldaantal. Als het 0 is, wordt het niet afgerond."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
msgid "The product is not available in sufficient quantity"
msgstr "Het product is niet in voldoende aantal beschikbaar"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__inventory_quantity
msgid "The product's counted quantity."
msgstr "Het getelde aantal van het product."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_quant_relocate_view_form
msgid ""
"The quantities selected do not all belong to the same location.\n"
"                    You may not assign them a package without moving them to a common location."
msgstr ""
"De geselecteerde hoeveelheden behoren niet allemaal tot dezelfde locatie.\n"
"                    Je mag hen geen verpakking toewijzen zonder ze naar een gemeenschappelijke locatie te verplaatsen."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid ""
"The quantity done for the product \"%(product)s\" doesn't respect the "
"rounding precision defined on the unit of measure \"%(unit)s\". Please "
"change the quantity done or the rounding precision of your unit of measure."
msgstr ""
"Het aantal gereed voor het product \"%(product)s\" komt niet overeen met de "
"afrondingsprecisie gedefinieerd op de maateenheid \"%(unit)s\". Wijzig het "
"aantal gereed van de afrondingsprecisie van je maateenheid."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "The quantity per lot should always be a positive value."
msgstr "het aantal per partij moet altijd positief zijn."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid ""
"The requested operation cannot be processed because of a programming error "
"setting the `product_qty` field instead of the `product_uom_qty`."
msgstr ""
"De gevraagde bewerking kan niet uitgevoerd worden door het fout instellen "
"van het 'product_qty' veld in plaats van het veld 'product_uom_qty'."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_location.py:0
msgid ""
"The search does not support the %(operator)s operator or %(value)s value."
msgstr ""
"De zoekopdracht ondersteunt de %(operator)s operator of %(value)s waarde "
"niet."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_location.py:0
msgid ""
"The selected Inventory Frequency (Days) creates a date too far into the "
"future."
msgstr ""
"De geselecteerde voorraadfrequentie (dagen) maakt een datum die te ver in de"
" toekomst ligt."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid ""
"The serial number has already been assigned: \n"
" Product: %(product)s, Serial Number: %(serial_number)s"
msgstr ""
"Het serienummer is al toegewezen: \n"
" Product: %(product)s, Serienummer: %(serial_number)s"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_warehouse_warehouse_code_uniq
msgid "The short name of the warehouse must be unique per company!"
msgstr "De korte naam van het magazijn moet per bedrijf uniek zijn!"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_partner__property_stock_customer
#: model:ir.model.fields,help:stock.field_res_users__property_stock_customer
msgid ""
"The stock location used as destination when sending goods to this contact."
msgstr ""
"De voorraadlocatie gebruikt als bestemming wanneer je goederen verzend naar "
"deze klant."

#. module: stock
#: model:ir.model.fields,help:stock.field_res_partner__property_stock_supplier
#: model:ir.model.fields,help:stock.field_res_users__property_stock_supplier
msgid ""
"The stock location used as source when receiving goods from this contact."
msgstr ""
"De voorraadlocatie gebruikt als bronbestemming wanneer je goederen ontvangt "
"van deze contact."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__picking_id
msgid "The stock operation where the packing has been made"
msgstr "De voorraadbewerking waar de verpakking is aangemaakt"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__rule_id
msgid "The stock rule that created this stock move"
msgstr "De voorraadregel welke deze verplaatsing heeft aangemaakt"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__propagate_warehouse_id
msgid ""
"The warehouse to propagate on the created move/procurement, which can be "
"different of the warehouse this rule is for (e.g for resupplying rules from "
"another warehouse)"
msgstr ""
"Het magazijn waaraan de aangemaakte verplaatsing/aanvulling wordt "
"doorgegeven. Deze kan afwijkend zijn dan het magazijn waar deze regel voor "
"is (bijv. voor aanvulregels vanuit een ander magazijn)"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid "There are no inventory adjustments to revert."
msgstr "Er zijn geen voorraadaanpassingen om terug te draaien."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"There is nothing eligible to put in a pack. Either there are no quantities "
"to put in a pack or all products are already in a pack."
msgstr ""
"Er komt niets in aanmerking om in een pakket te stoppen. Ofwel zijn er geen "
"hoeveelheden om in een verpakking te stoppen, ofwel zitten alle producten al"
" in een verpakking."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_move_line_action
msgid "There's no product move yet"
msgstr "Er is nog geen productverplaatsing"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.duplicated_sn_warning
msgid "This SN is already in another location."
msgstr "Dit SN bevindt zich al op een andere locatie."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"This adds a dropshipping route to apply on products in order to request your"
" vendors to deliver to your customers. A product to dropship will generate a"
" purchase request for quotation once the sales order confirmed. This is a "
"on-demand flow. The requested delivery address will be the customer delivery"
" address and not your warehouse."
msgstr ""
"Dit voegt een dropshipping-route toe, welke toegepast kan worden op "
"producten om je leveranciers te vragen om direct aan je klanten te leveren. "
"Een dropship product genereert een offerteaanvraag zodra de verkooporder is "
"bevestigd. Het proces is compleet gebaseerd op vraag. Het afleveradres is "
"het afleveradres van de klant en niet van je magazijn."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid ""
"This analysis gives you an overview of the current stock level of your "
"products."
msgstr ""
"Deze analyse geeft je een overzicht van de actuele voorraad van je "
"producten."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__picked
msgid ""
"This checkbox is just indicative, it doesn't validate or generate any "
"product moves."
msgstr ""
"Dit selectievakje is slechts indicatief; het valideert of genereert geen "
"productverplaatsingen."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__name
msgid "This field will fill the packing origin and the name of its moves"
msgstr ""
"Dit veld zal de oorsprong van de verpakking en de naam van de verplaatsingen"
" invullen"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__default_location_dest_id
msgid ""
"This is the default destination location when this operation is manually "
"created. However, it is possible to change it afterwards or that the routes "
"use another one by default."
msgstr ""
"Dit is de standaardbestemmingslocatie wanneer deze bewerking handmatig wordt"
" gemaakt. Het is echter mogelijk om deze later te wijzigen of dat de routes "
"standaard een andere gebruiken."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__default_location_src_id
msgid ""
"This is the default source location when this operation is manually created."
" However, it is possible to change it afterwards or that the routes use "
"another one by default."
msgstr ""
"Dit is de standaardbronlocatie wanneer deze bewerking handmatig wordt "
"gemaakt. Het is echter mogelijk om deze later te wijzigen of dat de routes "
"standaard een andere locatie gebruiken."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__owner_id
msgid "This is the owner of the quant"
msgstr "Dit is de eigenaar van de hoeveelheid"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__product_uom_qty
msgid ""
"This is the quantity of product that is planned to be moved.Lowering this "
"quantity does not generate a backorder.Changing this quantity on assigned "
"moves affects the product reservation, and should be done with care."
msgstr ""
"Dit is het aantal product die volgens de planning zal worden verplaatst. Het"
" verlagen van dit aantal genereert geen nalevering. Het wijzigen van dit "
"aantal bij toegewezen verplaatsingen heeft invloed op de productreservering "
"en moet met zorg gebeuren."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__child_internal_location_ids
msgid ""
"This location (if it's internal) and all its descendants filtered by "
"type=Internal."
msgstr ""
"Deze locatie (als deze intern is) en al zijn afstammelingen gefilterd op "
"type=Intern."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_location.py:0
msgid ""
"This location's usage cannot be changed to view as it contains products."
msgstr ""
"Deze locatietype kan niet aangepast worden omdat de locatie producten bevat."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid ""
"This lot %(lot_name)s is incompatible with this product %(product_name)s"
msgstr ""
"Deze partij %(lot_name)s is niet compatibel met dit product %(product_name)s"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "This lot/serial number is already in another location"
msgstr "Dit partij/serienummer bevindt zich al op een andere locatie"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_move_action
msgid ""
"This menu gives you the full traceability of inventory\n"
"                operations on a specific product. You can filter on the product\n"
"                to see all the past or future movements for the product."
msgstr ""
"Dit menu geeft je de volledige traceerbaarheid van voorraadbewerkingen op een specifiek product. Je kunt op het product\n"
"filteren om alle vorige en toekomstige verplaatsingen voor het product te zien."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_move_line_action
msgid ""
"This menu gives you the full traceability of inventory operations on a specific product.\n"
"                    You can filter on the product to see all the past movements for the product."
msgstr ""
"Dit menu geeft je de volledige traceerbaarheid van voorraadbewerkingen op een specifiek product. Je kunt op het product\n"
"filteren om alle vorige verplaatsingen voor het product te zien."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "This note is added to delivery orders."
msgstr "Deze notitie wordt toegevoegd aan de leveringen."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid ""
"This note is added to internal transfer orders (e.g. where to pick the "
"product in the warehouse)."
msgstr ""
"Deze notitie wordt toegevoegd aan interne overdrachtorders (bijvoorbeeld "
"waar het product in het magazijn moet worden opgehaald)."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid ""
"This note is added to receipt orders (e.g. where to store the product in the"
" warehouse)."
msgstr ""
"Deze notitie wordt toegevoegd aan ontvangsten (bijvoorbeeld waar het product"
" in het magazijn moet worden opgeslagen)."

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid ""
"This product has been used in at least one inventory movement. It is not "
"advised to change the Product Type since it can lead to inconsistencies. A "
"better solution could be to archive the product and create a new one "
"instead."
msgstr ""
"Dit product is in minstens één voorraadbeweging gebruikt. Het wordt niet "
"aangeraden om het producttype te wijzigen, aangezien dit tot inconsistenties"
" kan leiden. Een betere oplossing zou kunnen zijn om het product te "
"archiveren en in plaats daarvan een nieuw product te maken."

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid ""
"This product's company cannot be changed as long as there are quantities of "
"it belonging to another company."
msgstr ""
"Het bedrijf van dit product kan niet worden gewijzigd zolang er hoeveelheden"
" van een ander bedrijf zijn."

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid ""
"This product's company cannot be changed as long as there are stock moves of"
" it belonging to another company."
msgstr ""
"Het bedrijf van dit product kan niet worden gewijzigd zolang er "
"voorraadverplaatsingen van zijn die bij een ander bedrijf horen."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_change_product_qty__new_quantity
msgid ""
"This quantity is expressed in the Default Unit of Measure of the product."
msgstr "Dit aantal is uitgedrukt in de standaard maateenheid van het product."

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/views/list/inventory_report_list_model.js:0
msgid "This record already exists."
msgstr "Dit record bestaat al."

#. module: stock
#. odoo-python
#: code:addons/stock/report/report_stock_reception.py:0
msgid "This report cannot be used for done and not done %s at the same time"
msgstr ""
"Dit rapport kan niet tegelijkertijd worden gebruikt voor gedaan en niet "
"gedaan %s"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"This sequence prefix is already being used by another operation type. It is "
"recommended that you select a unique prefix to avoid issues and/or repeated "
"reference values or assign the existing reference sequence to this operation"
" type."
msgstr ""
"Het voorvoegsel van deze reeks wordt reeds gebruikt door een andere "
"bewerkingssoort. Het wordt aanbevolen om een uniek voorvoegsel te selecteren"
" om problemen en/of herhaalde referentiewaarden te voorkomen of om de "
"bestaande referentiereeks toe te wijzen aan dit type bewerking."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__property_stock_production
#: model:ir.model.fields,help:stock.field_product_template__property_stock_production
msgid ""
"This stock location will be used, instead of the default one, as the source "
"location for stock moves generated by manufacturing orders."
msgstr ""
"Deze voorraadlocatie zal worden gebruikt, in plaats van de standaardlocatie,"
" als bronlocatie voor voorraadverplaatsingen, gegenereerd door "
"productieorders."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__property_stock_inventory
#: model:ir.model.fields,help:stock.field_product_template__property_stock_inventory
msgid ""
"This stock location will be used, instead of the default one, as the source "
"location for stock moves generated when you do an inventory."
msgstr ""
"Deze voorraadlocatie zal worden gebruikt, in plaats van de standaardlocatie,"
" als bronlocatie voor voorraadverplaatsingen gegenereerd door een "
"voorraadtelling."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__responsible_id
#: model:ir.model.fields,help:stock.field_product_template__responsible_id
msgid ""
"This user will be responsible of the next activities related to logistic "
"operations for this product."
msgstr ""
"Deze gebruiker is verantwoordelijk voor het opvolgen van activiteiten met "
"betrekking tot logistieke activiteiten voor dit product."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_warning_reset_view
msgid "This will discard all unapplied counts, do you want to proceed?"
msgstr ""
"Hiermee worden alle niet-toegepaste tellingen genegeerd. Wil je doorgaan?"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_track_confirmation
msgid ""
"Those products you added are tracked but lots/serials were not defined. Once applied those can't be changed.<br/>\n"
"                    Apply anyway?"
msgstr ""
"De producten die je hebt toegevoegd, worden bijgehouden, maar partijen/series zijn niet gedefinieerd. Eenmaal toegepast kunnen deze niet meer worden gewijzigd.<br/>\n"
"                    Toch toepassen?"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
msgid "Time Horizon"
msgstr "Tijdshorizon"

#. module: stock
#: model:digest.tip,name:stock.digest_tip_stock_1
#: model_terms:digest.tip,tip_description:stock.digest_tip_stock_1
msgid "Tip: Monitor Lot details"
msgstr "Tip: Houd partijgegevens in de gaten"

#. module: stock
#: model:digest.tip,name:stock.digest_tip_stock_0
#: model_terms:digest.tip,tip_description:stock.digest_tip_stock_0
msgid "Tip: Speed up inventory operations with barcodes"
msgstr "Tip: Versnel voorraadbewerkingen met barcodes"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
#: model:ir.model.fields,field_description:stock.field_stock_move_line__location_dest_id
#: model:ir.model.fields,field_description:stock.field_stock_package_level__location_dest_id
#: model_terms:ir.ui.view,arch_db:stock.report_picking
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "To"
msgstr "Naar"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "To Apply"
msgstr "Toepassen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__to_backorder
msgid "To Backorder"
msgstr "Naar backorder"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "To Count"
msgstr "Te tellen"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "To Deliver"
msgstr "Te leveren"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_graph
#: model:ir.actions.act_window,name:stock.action_picking_tree_ready
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "To Do"
msgstr "Te doen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_quant_relocate_view_form
msgid "To Location"
msgstr "Naar locatie"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__qty_to_order
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__qty_to_order
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__qty_to_order
msgid "To Order"
msgstr "Te bestellen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__qty_to_order_computed
msgid "To Order Computed"
msgstr "Te bestellen berekend"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__qty_to_order_manual
msgid "To Order Manual"
msgstr "Te bestellen handmatig"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "To Order with Visibility Days"
msgstr "Te bestellen bij Zichtbaarheidsdagen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_quant_relocate_view_form
msgid "To Package"
msgstr "Te verpakken"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "To Process"
msgstr "Te verwerken"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "To Receive"
msgstr "Te ontvangen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
msgid "To Reorder"
msgstr "Te bestellen"

#. module: stock
#. odoo-javascript
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/static/src/widgets/json_widget.xml:0
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__search_date_category__today
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Today"
msgstr "Vandaag"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Today Activities"
msgstr "Activiteiten van vandaag"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__search_date_category__day_1
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Tomorrow"
msgstr "Morgen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_editable
msgid "Total Available"
msgstr "Totaal beschikbaar"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
msgid "Total Demand"
msgstr "Totale vraag"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
msgid "Total Forecasted"
msgstr "Totaal voorspeld"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
msgid "Total Free to Use"
msgstr "Totaal virtueel beschikbaar"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
msgid "Total Incoming"
msgstr "Totaal inkomend"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_editable
msgid "Total On Hand"
msgstr "Totaal beschikbaar"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
msgid "Total Outgoing"
msgstr "Totaal uitgaand"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
msgid "Total Quantity"
msgstr "Totaalaantal"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_editable
msgid "Total Reserved"
msgstr "Totaal gereserveerd"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_category__total_route_ids
msgid "Total routes"
msgstr "Totale routes"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__shipping_weight
msgid ""
"Total weight of packages and products not in a package. Packages with no "
"shipping weight specified will default to their products' total weight. This"
" is the weight used to compute the cost of the shipping."
msgstr ""
"Totaalgewicht van de verpakkingen en producten die niet in een verpakking "
"zitten. Verpakking zonder opgegeven verzendgewicht worden standaard "
"ingesteld op het totale gewicht van hun producten. Dit is het gewicht dat "
"wordt gebruikt om de verzendkosten te berekenen."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__weight_bulk
msgid "Total weight of products which are not in a package."
msgstr "Totaalgewicht van producten die niet in een verpakking zitten."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant_package__shipping_weight
msgid "Total weight of the package."
msgstr "Totaalgewicht van het gewicht."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Traceability"
msgstr "Traceerbaarheid"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.js:0
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
#: model:ir.actions.client,name:stock.action_stock_report
#: model:ir.model,name:stock.model_stock_traceability_report
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
msgid "Traceability Report"
msgstr "Traceerbaarheidsrapport"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__is_storable
#: model:ir.model.fields,field_description:stock.field_product_template__is_storable
#: model:ir.model.fields,field_description:stock.field_stock_move__is_storable
msgid "Track Inventory"
msgstr "Voorraad bijhouden"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_config_settings__module_product_expiry
msgid ""
"Track following dates on lots & serial numbers: best before, removal, end of life, alert. \n"
" Such dates are set automatically at lot/serial number creation based on values set on the product (in days)."
msgstr ""
"Volg datums op partij & serienummers: ten minste houdbaar, verwijdering, einde levensduur, waarschuwing.\n"
"Datums worden automatisch ingesteld bij het maken van een partij/serienummer op basis van waarden die op het product zijn ingesteld (in dagen)."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Track following dates on lots & serial numbers: best before, removal, end of"
" life, alert. Such dates are set automatically at lot/serial number creation"
" based on values set on the product (in days)."
msgstr ""
"Volg datums op partij & serienummers: ten minste houdbaar, verwijdering, einde levensduur, waarschuwing.\n"
"Datums worden automatisch ingesteld bij het maken van een partij/serienummer op basis van waarden die op het product zijn ingesteld (in dagen)."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Track product location in your warehouse"
msgstr "Beheer productlocaties in je magazijn"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.product_template_action_product
msgid "Track your stock quantities by creating storable products."
msgstr ""
"Houd je voorraadhoeveelheden bij door producten te maken die kunnen worden "
"opgeslagen."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Tracked Products in Inventory Adjustment"
msgstr "Getraceerde producten in voorraadaanpassing"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__tracking
#: model:ir.model.fields,field_description:stock.field_product_template__tracking
#: model:ir.model.fields,field_description:stock.field_stock_move_line__tracking
#: model:ir.model.fields,field_description:stock.field_stock_quant__tracking
#: model:ir.model.fields,field_description:stock.field_stock_track_line__tracking
msgid "Tracking"
msgstr "Traceren"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__tracking_line_ids
msgid "Tracking Line"
msgstr "Traceerregel"

#. module: stock
#: model:ir.model,name:stock.model_stock_picking
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_move__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_id
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Transfer"
msgstr "Verplaatsing"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_tree
msgid "Transfer to"
msgstr "Overbrengen naar"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: model:ir.actions.act_window,name:stock.action_picking_tree_all
#: model:ir.model.fields,field_description:stock.field_stock_lot__delivery_ids
#: model:ir.ui.menu,name:stock.menu_stock_transfers
#: model_terms:ir.ui.view,arch_db:stock.procurement_group_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid "Transfers"
msgstr "Verplaatsingen"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Transfers %s: Please add some items to move."
msgstr "Overdrachten %s: voeg een aantal items toe om te verplaatsen."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_get_picking_type_operations
#: model_terms:ir.actions.act_window,help:stock.action_picking_type_list
#: model_terms:ir.ui.view,arch_db:stock.help_message_template
msgid "Transfers allow you to move products from one location to another."
msgstr ""
"Met overdrachten kun je producten van de ene naar de andere locatie "
"verplaatsen."

#. module: stock
#: model:ir.actions.act_window,name:stock.do_view_pickings
msgid "Transfers for Groups"
msgstr "Overdrachten voor groepen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid ""
"Transfers that are late on scheduled time or one of pickings will be late"
msgstr ""
"Overdrachten die te laat zijn op de geplande tijd of een van de pickings, "
"zullen te laat zijn"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__transit
msgid "Transit Location"
msgstr "Transitlocatie"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Transit Locations"
msgstr "Transitlocaties"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Transport management: organize packs in your fleet, or carriers."
msgstr ""
"Transportbeheer: organiseer pakketten in je wagenpark of via vervoerders."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__trigger
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
msgid "Trigger"
msgstr "Activeren"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__procure_method__make_to_order
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Trigger Another Rule"
msgstr "Activeer een andere regel"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Trigger Another Rule If No Stock"
msgstr "Activeer een andere regel als er geen voorraad is"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
msgid "Trigger Manual"
msgstr "Handmatig activeren"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_scrap__should_replenish
msgid "Trigger replenishment for scrapped products"
msgstr "Activeer de anvulling voor afgekeurde producten"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/stock_forecasted.js:0
msgid "Try to add some incoming or outgoing transfers."
msgstr "Probeer enkele inkomende of uitgaande overdrachten toe te voegen."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_barcode_rule__type
msgid "Type"
msgstr "Soort"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_partner_stock_warnings_form
msgid "Type a message..."
msgstr "Schrijf een bericht..."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__picking_code
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_code
#: model:ir.model.fields,field_description:stock.field_stock_package_level__picking_type_code
#: model:ir.model.fields,field_description:stock.field_stock_picking__picking_type_code
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__code
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
msgid "Type of Operation"
msgstr "Soort bewerking"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_lot__activity_exception_decoration
#: model:ir.model.fields,help:stock.field_stock_picking__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Type van de geregistreerde uitzonderingsactiviteit."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_ups
msgid "UPS Connector"
msgstr "UPS connector"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_usps
msgid "USPS Connector"
msgstr "USPS connector"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/components/reception_report_line/stock_reception_report_line.xml:0
msgid "Unassign"
msgstr "Toewijzing ongedaan maken"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid ""
"Unavailable Serial numbers. Please correct the serial numbers encoded: "
"%(serial_numbers_to_locations)s"
msgstr ""
"Niet beschikbare serienummers. Corrigeer de gecodeerde serienummers: "
"%(serial_numbers_to_locations)s"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
msgid "Unfold"
msgstr "Uitvouwen"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_lot__name
msgid "Unique Lot/Serial Number"
msgstr "Unieke partij/serienummer"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_editable
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_simple
msgid "Unit"
msgstr "Eenheid"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__price_unit
msgid "Unit Price"
msgstr "Prijs"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__forecast_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_lot__product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__uom_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_uom
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty__product_uom_name
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__product_uom_name
#: model_terms:ir.ui.view,arch_db:stock.package_level_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree_detailed
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree_receipt_picking
#: model_terms:ir.ui.view,arch_db:stock.view_picking_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_operation_tree
msgid "Unit of Measure"
msgstr "Maateenheid"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__uom
msgid "Unit of Measure Name"
msgstr "Naam maateenheid"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
msgid "Units"
msgstr "Stuks"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Units Of Measure"
msgstr "Maateenheid"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_unit_measure_stock
msgid "Units of Measure"
msgstr "Maateenheden"

#. module: stock
#: model:ir.ui.menu,name:stock.product_uom_menu
msgid "Units of Measures"
msgstr "Maateenheden"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__product_uom_id
msgid "Unity of measure"
msgstr "Maateenheid"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Unknown Pack"
msgstr "Onbekende verpakking"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid ""
"Unless previously specified by the source document, this will be the default"
" picking policy for this operation type."
msgstr ""
"Tenzij eerder aangegeven in het brondocument, is dit het standaard "
"orderpikbeleid voor dit type bewerking."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Unpack"
msgstr "Uitpakken"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
#: model:ir.actions.server,name:stock.action_unreserve_picking
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "Unreserve"
msgstr "Reservering ongedaan maken"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.search_customer_lot_filter
msgid "Unreturned"
msgstr "Niet geretourneerd"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "Unsafe unit of measure"
msgstr "Onveilige maateenheid"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__unwanted_replenish
msgid "Unwanted Replenish"
msgstr "Ongewenste aanvulling"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__product_uom
#: model:ir.ui.menu,name:stock.menu_stock_uom_form_action
#: model_terms:ir.ui.view,arch_db:stock.replenishment_option_tree_view
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid "UoM"
msgstr "Maateenheid"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_uom_categ_form_action
msgid "UoM Categories"
msgstr "Maateenheidcategorieën"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot_report__uom_id
msgid "Uom"
msgstr "Maateenheid"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_change_product_quantity
msgid "Update Product Quantity"
msgstr "Productaantal bijwerken"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_adjustment_name_form_view
msgid "Update Quantities"
msgstr "Hoeveelheden bijwerken"

#. module: stock
#. odoo-javascript
#. odoo-python
#: code:addons/stock/models/product.py:0
#: code:addons/stock/static/src/stock_forecasted/forecasted_buttons.xml:0
#: model:ir.actions.act_window,name:stock.dashboard_open_quants
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_product_view_form_easy_inherit_stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "Update Quantity"
msgstr "Aantal bijwerken"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"Updating the location of this transfer will result in unreservation of the currently assigned items. An attempt to reserve items at the new location will be made and the link with preceding transfers will be discarded.\n"
"\n"
"To avoid this, please discard the source location change before saving."
msgstr ""
"Het wijzigen van de locatie van deze overdracht heeft tot gevolg dat de huidige toegewezen items niet meer worden gereserveerd. Er wordt een poging gedaan om items te reserveren op de nieuwe locatie. De link met voorgaande overdrachten wordt verwijderd.\n"
"\n"
"Gelieve de wijziging van de bronlocatie te verwijderen alvorens op te slaan om dit te vermijden."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__priority__1
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__priority__1
msgid "Urgent"
msgstr "Urgent"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_type_use_existing_lots
#: model:ir.model.fields,field_description:stock.field_stock_picking__use_existing_lots
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__use_existing_lots
msgid "Use Existing Lots/Serial Numbers"
msgstr "Gebruik bestaande partijen/serienummers"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Use Existing ones"
msgstr "Bestaande gebruiken"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Use GS1 nomenclature datamatrix whenever barcodes are printed for lots and "
"serial numbers."
msgstr ""
"Gebruik de GS1-nomenclatuur-datamatrix wanneer barcodes worden afgedrukt "
"voor partij- en serienummers."

#. module: stock
#: model:res.groups,name:stock.group_reception_report
msgid "Use Reception Report"
msgstr "Ontvangstrapport gebruiken"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Use your own routes"
msgstr "Gebruik je eigen routes"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "Used by"
msgstr "Gebruikt door"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__sequence
msgid "Used to order the 'All Operations' kanban view"
msgstr "Wordt gebruikt om de \"Alle bewerkingen\" kanban weergave te sorteren"

#. module: stock
#: model:ir.model,name:stock.model_res_users
#: model:ir.model.fields,field_description:stock.field_stock_request_count__user_id
#: model:res.groups,name:stock.group_stock_user
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "User"
msgstr "Gebruiker"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__user_id
msgid "User assigned to do product count."
msgstr "Gebruiker toegewezen om producttelling te doen."

#. module: stock
#: model:ir.actions.server,name:stock.action_validate_picking
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Validate"
msgstr "Bevestigen"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/xml/inventory_lines.xml:0
msgid "Validate Inventory"
msgstr "Voorraadtelling bevestigen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__product_variant_count
msgid "Variant Count"
msgstr "Aantal varianten"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Vendor"
msgstr "Leverancier"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_partner__property_stock_supplier
#: model:ir.model.fields,field_description:stock.field_res_users__property_stock_supplier
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__supplier
msgid "Vendor Location"
msgstr "Leverancierslocatie"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Vendor Locations"
msgstr "Leverancierslocaties"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__view
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "View"
msgstr "Bekijk"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_view_kanban_catalog
msgid "View Availability"
msgstr "Toon beschikbaarheden"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "View Diagram"
msgstr "Diagram bekijken"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__view_location_id
msgid "View Location"
msgstr "Locatie bekijken"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "View and allocate received quantities."
msgstr "Ontvangen hoeveelheden bekijken en toewijzen."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__visibility_days
msgid "Visibility Days"
msgstr "Zichtdagen"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
msgid "Visibility days"
msgstr "Zichtbaarheidsdagen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__shipping_volume
msgid "Volume for Shipping"
msgstr "Volume voor levering"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "WH/OUT/00001"
msgstr "WH/OUT/00001"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "WH/OUT/0001"
msgstr "WH/OUT/0001"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "WH/Outgoing"
msgstr "WH/Outgoing"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_generic_barcode
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "WH/Stock"
msgstr "WH/Voorraad"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__state__confirmed
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Waiting"
msgstr "In afwachting"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__waiting
msgid "Waiting Another Move"
msgstr "In afwachting van een andere verplaatsing"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__state__waiting
msgid "Waiting Another Operation"
msgstr "In afwachting van een andere bewerking"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__confirmed
msgid "Waiting Availability"
msgstr "In afwachting van beschikbaarheid"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Waiting Moves"
msgstr "Wachtende verplaatsingen"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_waiting
msgid "Waiting Transfers"
msgstr "Wachtende overdrachten"

#. module: stock
#: model:ir.model,name:stock.model_stock_warehouse
#: model:ir.model.fields,field_description:stock.field_product_product__warehouse_id
#: model:ir.model.fields,field_description:stock.field_product_replenish__warehouse_id
#: model:ir.model.fields,field_description:stock.field_product_template__warehouse_id
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_location__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_move__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_rule__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__name
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__warehouse_id
#: model_terms:ir.ui.view,arch_db:stock.replenishment_option_tree_view
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
#: model_terms:ir.ui.view,arch_db:stock.stock_warehouse_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_filter
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_tree
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Warehouse"
msgstr "Magazijn"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Warehouse Configuration"
msgstr "Magazijninstellingen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_route__warehouse_domain_ids
msgid "Warehouse Domain"
msgstr "Magazijn domein"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.replenishment_option_tree_view
msgid "Warehouse Location"
msgstr "Magazijn locatie"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_warehouse_config
msgid "Warehouse Management"
msgstr "Magazijnbeheer"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__warehouse_view_ids
msgid "Warehouse View"
msgstr "Magazijnweergave"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__propagate_warehouse_id
msgid "Warehouse to Propagate"
msgstr "Door te geven magazijn"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Warehouse view location"
msgstr "Magazijn weergavelocatie"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Warehouse's Routes"
msgstr "Magazijnroutes"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_warehouse_filter.xml:0
msgid "Warehouse:"
msgstr "Magazijn:"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/views/search/stock_report_search_panel.xml:0
#: model:ir.actions.act_window,name:stock.action_warehouse_form
#: model:ir.model.fields,field_description:stock.field_stock_route__warehouse_ids
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__warehouse_ids
#: model:ir.ui.menu,name:stock.menu_action_warehouse_form
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_stock_replenishment_info
msgid "Warehouses"
msgstr "Magazijnen"

#. module: stock
#: model:ir.model,name:stock.model_stock_warn_insufficient_qty
msgid "Warn Insufficient Quantity"
msgstr "Waarschuwing onvoldoende aantal"

#. module: stock
#: model:ir.model,name:stock.model_stock_warn_insufficient_qty_scrap
msgid "Warn Insufficient Scrap Quantity"
msgstr "Waarschuwing onvoldoende aantal afgekeurde producten"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product_strategy.py:0
#: code:addons/stock/models/stock_move.py:0
#: code:addons/stock/models/stock_move_line.py:0
#: code:addons/stock/models/stock_quant.py:0
#: code:addons/stock/models/stock_scrap.py:0
#: code:addons/stock/models/stock_warehouse.py:0
#: model:ir.model.fields.selection,name:stock.selection__res_partner__picking_warn__warning
msgid "Warning"
msgstr "Waarschuwing"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Warning Duplicated SN"
msgstr "Waarschuwing gedupliceerde SN"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__warning_message
msgid "Warning Message"
msgstr "Waarschuwingsberichten"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_partner_stock_warnings_form
msgid "Warning on the Picking"
msgstr "Waarschuwing op de picking"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid "Warning!"
msgstr "Waarschuwing!"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Warning: change source location"
msgstr "Waarschuwing: bronlocatie wijzigen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Warnings"
msgstr "Waarschuwingen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_warning_stock
msgid "Warnings for Stock"
msgstr "Waarschuwingen voor voorraad"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__website_message_ids
#: model:ir.model.fields,field_description:stock.field_stock_picking__website_message_ids
#: model:ir.model.fields,field_description:stock.field_stock_scrap__website_message_ids
msgid "Website Messages"
msgstr "Websiteberichten"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_lot__website_message_ids
#: model:ir.model.fields,help:stock.field_stock_picking__website_message_ids
#: model:ir.model.fields,help:stock.field_stock_scrap__website_message_ids
msgid "Website communication history"
msgstr "Website communicatie geschiedenis"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__base_weight
msgid "Weight"
msgstr "Gewicht"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__shipping_weight
msgid "Weight for Shipping"
msgstr "Gewicht voor levering"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_package_type__base_weight
msgid "Weight of the package type"
msgstr "Gewicht van het verpakkingsssoort"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__weight_uom_name
msgid "Weight unit"
msgstr "Gewichtseenheid"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__weight_uom_name
msgid "Weight unit of measure label"
msgstr "Gewicht maateenheid label"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__barcode_rule__type__weight
msgid "Weighted Product"
msgstr "Gewogen product"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__wh_replenishment_option_ids
msgid "Wh Replenishment Option"
msgstr "Magazijn aanvullingsoptie"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_route__warehouse_selectable
msgid ""
"When a warehouse is selected for this route, this route should be seen as "
"the default route when products pass through this warehouse."
msgstr ""
"Als er een magazijn is geselecteerd voor deze route, moet deze route worden "
"gezien als de standaardroute wanneer producten door dit magazijn gaan."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__move_type__one
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__move_type__one
msgid "When all products are ready"
msgstr "Wanneer alle producten beschikbaar zijn"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_route__product_selectable
msgid ""
"When checked, the route will be selectable in the Inventory tab of the "
"Product form."
msgstr ""
"Als deze optie is aangevinkt, kan de route worden geselecteerd op het "
"tabblad voorraad van het productformulier."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_route__product_categ_selectable
msgid "When checked, the route will be selectable on the Product Category."
msgstr ""
"Als deze optie is aangevinkt, kan de route worden geselecteerd in de "
"productcategorie."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_route__packaging_selectable
msgid "When checked, the route will be selectable on the Product Packaging."
msgstr ""
"Indien aangevinkt, kan de route worden geselecteerd op de productverpakking."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__location_in_id
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "When product arrives in"
msgstr "Wanneer product aankomt in"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
msgid ""
"When products are needed in <b>%(destination)s</b>, <br> "
"<b>%(operation)s</b> are created from <b>%(source_location)s</b> to fulfill "
"the need."
msgstr ""
"Wanneer producten nodig zijn in "
"<b>%(destination)s</b><br><b>%(operation)s</b> worden gemaakt van "
"<b>%(source_location)s</b> om aan de behoefte te voldoen."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
msgid ""
"When products arrive in <b>%(source_location)s</b>, <br> "
"<b>%(operation)s</b> are created to send them to <b>%(destination)s</b>."
msgstr ""
"Wanneer de producten binnenkomen in "
"<b>%(source_location)s</b><br><b>%(operation)s</b> aangemaakt om ze naar "
"<b>%(destination)s</b>."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__location_dest_from_rule
msgid ""
"When set to True the destination location of the stock.move will be the "
"rule.Otherwise, it takes it from the picking type."
msgstr ""
"De bestemmingslocatie van de stock.move geldt als de regel als deze is "
"ingesteld op True. Anders geldt de regel van het pickingsoort."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__is_locked
msgid ""
"When the picking is not done this allows changing the initial demand. When "
"the picking is done this allows changing the done quantities."
msgstr ""
"Wanneer een levering nog niet gereed is geeft je dit de mogelijkheid om de "
"initiële aanvraag te wijzigen. Wanneer een levering gereed is geeft je dit "
"de mogelijkheid om de geregistreerde hoeveelheid te wijzigen."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__product_min_qty
msgid ""
"When the virtual stock goes below the Min Quantity specified for this field,"
" Odoo generates a procurement to bring the forecasted quantity above of this"
" Min Quantity."
msgstr ""
"Zodra de virtuele voorraad onder het minimale aantal ligt die voor dit veld "
"is opgegeven, genereert Odoo een aanbesteding om het verwachte aantal boven "
"dit minimale aantal te brengen."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__product_max_qty
msgid ""
"When the virtual stock goes below the Min Quantity, Odoo generates a "
"procurement to bring the forecasted quantity up to (or near to) the Max "
"Quantity specified for this field (or to Min Quantity, whichever is bigger)."
msgstr ""
"Zodra de virtuele voorraad onder het minimale aantal ligt, genereert Odoo "
"een aanbesteding om het te verwachte aantal te verhogen tot (of dicht bij) "
"het maximale aantal dat is opgegeven voor dit veld (of tot het minimale "
"aantal, afhankelijk van welke waarde groter is)."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__propagate_carrier
msgid "When ticked, carrier of shipment will be propagated."
msgstr "Indien aangevinkt, wordt de vervoerder van de zending gepropageerd."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__propagate_cancel
msgid ""
"When ticked, if the move created by this rule is cancelled, the next move "
"will be cancelled too."
msgstr ""
"Indien aangevinkt wordt bij het splitsen of annuleren van deze verplaatsing "
"ook de opvolgende verplaatsingen geannuleerd of gesplitst."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__create_backorder
msgid ""
"When validating a transfer:\n"
" * Ask: users are asked to choose if they want to make a backorder for remaining products\n"
" * Always: a backorder is automatically created for the remaining products\n"
" * Never: remaining products are cancelled"
msgstr ""
"Bij het bevestigen van een overdracht:\n"
" * Vragen: gebruikers wordt gevraagd om te kiezen of ze een nabestelling willen maken voor resterende producten\n"
" * Altijd: er wordt automatisch een backorder aangemaakt voor de overige producten\n"
" *Nooit: resterende producten worden geannuleerd"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__owner_id
msgid ""
"When validating the transfer, the products will be assigned to this owner."
msgstr ""
"Bij het bevestigen van de overdracht worden de producten toegewezen aan deze"
" eigenaar."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__owner_id
msgid ""
"When validating the transfer, the products will be taken from this owner."
msgstr ""
"Bij het bevestigen van de overdracht worden de producten van deze eigenaar "
"afgenomen."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__additional
msgid "Whether the move was added after the picking's confirmation"
msgstr "Of de verplaatsing is toegevoegd na de bevestiging van de levering"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__width
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "Width"
msgstr "Breedte"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_package_type_positive_width
msgid "Width must be positive"
msgstr "Breedte moet positief zien"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__wizard_id
#: model:ir.model.fields,field_description:stock.field_stock_track_line__wizard_id
msgid "Wizard"
msgstr "Wizard"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "Write one lot/serial name per line, followed by the quantity."
msgstr "Schrijf één partij-/serienaam per regel, gevolgd door het aantal."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__search_date_category__yesterday
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Yesterday"
msgstr "Gisteren"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_quant_relocate_view_form
msgid ""
"You are about to move quantities in a package without moving the full package.\n"
"                    Those quantities will be removed from the following package(s):"
msgstr ""
"Je staat op het punt hoeveelheden in een verpakking te verplaatsen zonder het volledige verpakking te verplaatsen.\n"
"                    Deze hoeveelheden worden uit de volgende verpakking(en) verwijderd:"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form
msgid ""
"You are going to pick products that are not referenced\n"
"in this location. That leads to a negative stock."
msgstr ""
"Je staat op het punt producten te picken waarnaar niet verwezen \n"
"wordt in deze locatie. Dit leidt tot een negatieve voorraad."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_orderpoint_replenish
msgid "You are good, no replenishment to perform!"
msgstr "Je bent helemaal bij. Er zijn geen aanvullingen om uit te voeren!"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_lot.py:0
msgid ""
"You are not allowed to change the product linked to a serial or lot number "
"if some stock moves have already been created with that number. This would "
"lead to inconsistencies in your stock."
msgstr ""
"Het is niet toegestaan om het product gekoppeld aan een serienummer of "
"partijnummer te wijzigen als er al een voorraad met dat nummer is "
"aangemaakt. Dit zou leiden tot inconsistenties in je voorraad."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_lot.py:0
msgid ""
"You are not allowed to create a lot or serial number with this operation "
"type. To change this, go on the operation type and tick the box \"Create New"
" Lots/Serial Numbers\"."
msgstr ""
"Het is niet toegestaan om een partij- of serienummer met dit type bewerking "
"te maken. Om dit te wijzigen, ga je naar het bewerkingssoort en vink je het "
"vakje \"Nieuwe partijnummers / serienummers maken\" aan."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_package_destination_form_view
msgid ""
"You are trying to put products going to different locations into the same "
"package"
msgstr ""
"Je probeert producten welke naar verschillende locaties gaan te stoppen in "
"dezelfde verpakking"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid ""
"You are using a unit of measure smaller than the one you are using in order "
"to stock your product. This can lead to rounding problem on reserved "
"quantity. You should use the smaller unit of measure possible in order to "
"valuate your stock or change its rounding precision to a smaller value "
"(example: 0.00001)."
msgstr ""
"Je gebruikt een maateenheid kleiner dan die je gebruikt om je product op te "
"slaan. Dit kan leiden tot afrondingsproblemen bij gereserveerde "
"hoeveelheden. Je moet de kleinere maateenheid gebruiken die mogelijk is om "
"je voorraad te waarderen of de afrondingsprecisie wijzigen in een kleinere "
"waarde (bijvoorbeeld: 0,00001)."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_routes_form
msgid ""
"You can define here the main routes that run through\n"
"                your warehouses and that define the flows of your products. These\n"
"                routes can be assigned to a product, a product category or be fixed\n"
"                on procurement or sales order."
msgstr ""
"Je kunt hier de hoofdproductieroutes definiëren die door je\n"
"magazijn lopen en die de flow van je producten definiëren.  Deze\n"
"productieroutes kunnen toegewezen worden aan een product, een productcategorie of vastgesteld worden op\n"
"aanvulling of verkooporder."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid "You can either :"
msgstr "Je kunt of :"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid ""
"You can not change the inventory tracking of a product that is currently "
"reserved on a stock move. If you need to change the inventory tracking, you "
"should first unreserve the stock move."
msgstr ""
"Je kunt de voorraadtracering van een product niet veranderen voor een "
"product welke gereserveerd is in een voorraadverplaatsing. Wanneer je de "
"voorraadtracering moet veranderen, zul je de reservering voor deze "
"voorraadverplaatsing eerst ongedaan moeten maken."

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid ""
"You can not change the inventory tracking of a product that was already "
"used."
msgstr ""
"Je kunt de voorraadtracering van een product dat al in gebruik is niet "
"wijzigen."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_orderpoint.py:0
msgid ""
"You can not create a snoozed orderpoint that is not manually triggered."
msgstr ""
"Je kunt geen gesnoozede aanvulregel maken dat niet handmatig wordt "
"geactiveerd."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "You can not delete moves linked to another operation"
msgstr ""
"Je kunt geen verplaatsingen verwijderen die gekoppeld zijn aan een andere "
"bewerking"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid ""
"You can not delete product moves if the picking is done. You can only "
"correct the done quantities."
msgstr ""
"Je kunt product verplaatsingen niet verwijderen als de levering verwerkt is."
" Je kunt alleen de verwerkte hoeveelheden corrigeren."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid "You can not enter negative quantities."
msgstr "Je mag geen negatieve waardes ingeven."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_scrap.py:0
msgid "You can only enter positive quantities."
msgstr "Je kunt alleen positieve waardes ingeven."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_lot.py:0
msgid ""
"You can only move a lot/serial to a new location if it exists in a single "
"location."
msgstr ""
"Je kunt een partij/serienummer alleen naar een nieuwe locatie verplaatsen "
"als het op één locatie bestaat."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid ""
"You can only move positive quantities stored in locations used by a single "
"company per relocation."
msgstr ""
"Per verplaatsing kun kun alleen positieve hoeveelheden verplaatsen die zijn "
"opgeslagen op locaties die door één bedrijf worden gebruikt."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid "You can only process 1.0 %s of products with unique serial number."
msgstr ""
"Je kunt slechts 1.0 %s verwerken van producten met een unieke serienummer."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_orderpoint.py:0
msgid ""
"You can only snooze manual orderpoints. You should rather archive 'auto-"
"trigger' orderpoints if you do not want them to be triggered."
msgstr ""
"Je kunt alleen handmatige aanvulregels snoozen. Je kunt beter 'auto-"
"trigger'-aanvulregels archiveren als je niet wilt dat ze worden geactiveerd."

#. module: stock
#. odoo-python
#: code:addons/stock/models/res_config_settings.py:0
msgid ""
"You can't deactivate the multi-location if you have more than once warehouse"
" by company"
msgstr ""
"Je kunt de multi-locatie niet deactiveren als je meer dan één magazijn per "
"bedrijf heeft"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_location.py:0
msgid "You can't disable locations %s because they still contain products."
msgstr ""
"Je kunt locaties %s niet uitschakelen omdat deze nog steeds producten "
"bevatten."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_location.py:0
msgid ""
"You cannot archive location %(location)s because it is used by warehouse "
"%(warehouse)s"
msgstr ""
"Je kunt de locatie %(location)s niet archiveren omdat deze gebruikt wordt in"
" je magazijn %(warehouse)s"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid ""
"You cannot cancel a stock move that has been set to 'Done'. Create a return "
"in order to reverse the moves which took place."
msgstr ""
"Je kunt een voorraadverplaatsing die is ingesteld op 'Gereed' niet "
"annuleren. Maak een terugkeer om de zetten die plaatsvonden terug te "
"draaien."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "You cannot change a cancelled stock move, create a new line instead."
msgstr ""
"Je kunt een geannuleerde voorraadverplaatsing niet wijzigen, maak liever een"
" nieuwe regel aan."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "You cannot change the Scheduled Date on a done or cancelled transfer."
msgstr ""
"Je kunt de geplande datum niet wijzigen na een voltooide of geannuleerde "
"overdracht."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid ""
"You cannot change the UoM for a stock move that has been set to 'Done'."
msgstr ""
"Je kunt de maateenheid niet wijzigen voor een voorraadverplaatsing die is "
"ingesteld op 'Gereed'."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_lot.py:0
msgid ""
"You cannot change the company of a lot/serial number currently in a location"
" belonging to another company."
msgstr ""
"Je kunt het bedrijf van een partij/serienummer dat zich momenteel op een "
"locatie van een ander bedrijf bevindt, niet wijzigen."

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid ""
"You cannot change the ratio of this unit of measure as some products with "
"this UoM have already been moved or are currently reserved."
msgstr ""
"Je kunt de verhouding van deze maateenheid niet wijzigen, aangezien sommige "
"producten met deze hoeveelheidseenheid al zijn verplaatst of momenteel zijn "
"gereserveerd."

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid ""
"You cannot change the unit of measure as there are already stock moves for "
"this product. If you want to change the unit of measure, you should rather "
"archive this product and create a new one."
msgstr ""
"Je kunt de maateenheid niet wijzigen, aangezien er al voorraadverplaatsingen"
" zijn voor dit product. Als je de maateenheid wilt wijzigen, moet je dit "
"product archiveren en een nieuwe aanmaken."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_scrap.py:0
msgid "You cannot delete a scrap which is done."
msgstr ""
"Het is niet mogelijk een afkeuring te verwijderen welke al verwerkt is."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid ""
"You cannot directly pack quantities from different transfers into the same "
"package through this view. Try adding them to a batch picking and pack it "
"there."
msgstr ""
"Via deze weergave kun je hoeveelheden van verschillende verplaatsingen niet "
"rechtstreeks in hetzelfde pakket verpakken. Probeer ze toe te voegen aan een"
" batchpicking en verpak ze daar."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "You cannot duplicate stock quants."
msgstr "Je kunt hoeveelheden voorraad niet kopiëren."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "You cannot modify inventory loss quantity"
msgstr "Je kunt het aantal voorraadverlies niet wijzigen"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid ""
"You cannot move the same package content more than once in the same transfer"
" or split the same package into two location."
msgstr ""
"Je kunt niet dezelfde verpakkingsinhoud meer dan één keer in dezelfde "
"verplaatsing overbrengen of dezelfde verpakking op twee locaties splitsen."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"You cannot pack products into the same package when they are from different "
"transfers with different operation types."
msgstr ""
"Je kunt producten niet in hetzelfde pakket verpakken als ze afkomstig zijn "
"van verschillende verplaatsingen met verschillende bewerkingssoorten."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid ""
"You cannot perform moves because their unit of measure has a different "
"category from their product unit of measure."
msgstr ""
"Je kunt geen verplaatsingen uitvoeren omdat de maateenheid een andere "
"categorie heeft als de maateenheid van het product."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_location.py:0
msgid ""
"You cannot set a location as a scrap location when it is assigned as a "
"destination location for a manufacturing type operation."
msgstr ""
"Je kunt een locatie niet instellen als afkeurlocatie wanneer deze is "
"toegewezen als bestemmingslocatie voor een bewerking van het productietype."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"You cannot set a scrap location as the destination location for a "
"manufacturing type operation."
msgstr ""
"Je kunt geen afkeurlocatie instellen als bestemmingslocatie voor een "
"bewerking van het productietype."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "You cannot split a draft move. It needs to be confirmed first."
msgstr ""
"Het is niet mogelijk een concept regel op te splitsen. Je dient deze eerst "
"te bevestigen."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "You cannot split a stock move that has been set to 'Done' or 'Cancel'."
msgstr ""
"Je kunt een voorraadverplaatsing die ingesteld is op 'Gereed' of 'Annuleren'"
" niet splitsen."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid ""
"You cannot take products from or deliver products to a location of type "
"\"view\" (%s)."
msgstr ""
"Je kunt geen producten meenemen van, of afleveren op een locatie van het "
"type \"weergave\" (%s)."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "You cannot unreserve a stock move that has been set to 'Done'."
msgstr ""
"Het is niet mogelijk om van een voorraadverplaatsing de reservering ongedaan"
" te maken welke in de 'Gereed' fase is."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid ""
"You cannot use the same serial number twice. Please correct the serial "
"numbers encoded."
msgstr ""
"Het is niet toegestaan om hetzelfde serienummer twee keer te gebruiken. "
"Corrigeer de ingegeven serienummers."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"You cannot validate a transfer if no quantities are reserved. To force the "
"transfer, encode quantities."
msgstr ""
"Je kunt een overdracht niet bevestigen als er geen hoeveelheden zijn "
"gereserveerd. Om de overdracht te forceren, codeer je de hoeveelheden."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"You can’t validate an empty transfer. Please add some products to move "
"before proceeding."
msgstr ""
"Je kunt een lege overboeking niet bevestigen. Voeg enkele producten toe om "
"te verplaatsen voordat je doorgaat."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "You have processed less products than the initial demand."
msgstr "Je hebt minder producten verwerkt dan de initiële aanvraag."

#. module: stock
#. odoo-python
#: code:addons/stock/models/res_config_settings.py:0
msgid ""
"You have product(s) in stock that have lot/serial number tracking enabled. \n"
"Switch off tracking on all the products before switching off this setting."
msgstr ""
"Je hebt producten in voorraad waarvoor het traceren via partij/serienummer is ingeschakeld.\n"
"Schakel het traceren uit voor alle producten voordat je deze instelling uitschakelt."

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid ""
"You have product(s) in stock that have no lot/serial number. You can assign "
"lot/serial numbers by doing an inventory adjustment."
msgstr ""
"Je hebt product(en) op voorraad die geen partij/serienummer hebben. Je kunt "
"een partij/serienummers toewijzen door een voorraadaanpassing uit te voeren."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_orderpoint.py:0
msgid ""
"You have to select a product unit of measure that is in the same category as"
" the default unit of measure of the product"
msgstr ""
"Je moet een product-maateenheid selecteren die in dezelfde categorie valt "
"als de standaardmaateenheid van het product"

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/stock_picking_return.py:0
msgid "You may only return Done pickings."
msgstr "Je kunt alleen pickings die gereed zijn retourneren."

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/stock_picking_return.py:0
msgid "You may only return one picking at a time."
msgstr "Je kunt maar één picking tegelijk retourneren."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"You need to activate storage locations to be able to do internal operation "
"types."
msgstr ""
"Je moet opslaglocaties activeren om interne bewerkingssoorten te kunnen "
"uitvoeren."

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/product_replenish.py:0
msgid "You need to select a route to replenish your products"
msgstr "Je moet een route kiezen om je producten aan te vullen"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid ""
"You need to supply a Lot/Serial Number for product:\n"
"%(products)s"
msgstr ""
"Je moet een lot-/serienummer opgeven voor het product:\n"
"%(products)s"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "You need to supply a Lot/Serial number for products %s."
msgstr "Je moet een partij/serienummer ingeven voor het product %s."

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid "You should update this document to reflect your T&amp;C."
msgstr ""
"Je moet dit document bijwerken om je algemene voorwaarden weer te geven."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid ""
"You still have ongoing operations for operation types %(operations)s in "
"warehouse %(warehouse)s"
msgstr ""
"Je hebt nog lopende bewerkingen voor bewerkingsoorten %(operations)s in het "
"magazijn %(warehouse)s"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid ""
"You still have some active reordering rules on this product. Please archive "
"or delete them first."
msgstr ""
"Je hebt nog aanvulregels op dit product. Archiveer of verwijder deze eerst."

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/views/list/inventory_report_list_model.js:0
msgid ""
"You tried to create a record that already exists. The existing record was "
"modified instead."
msgstr ""
"Je hebt geprobeerd een record te maken dat al bestaat. In plaats daarvan is "
"het bestaande record gewijzigd."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_orderpoint_replenish
msgid ""
"You'll find here smart replenishment propositions based on inventory forecasts.\n"
"            Choose the quantity to buy or manufacture and launch orders in a click.\n"
"            To save time in the future, set the rules as \"automated\"."
msgstr ""
"Je vindt hier slimme aanvullingsvoorstellen op basis van voorraadprognoses.\n"
"Kies het aantal dat je wilt kopen of produceren en start aanvulorders met één klik.\n"
"Om in de toekomst tijd te besparen, stel je de regels in als \"Automatiseer orders\"."

#. module: stock
#: model_terms:res.company,lunch_notify_message:stock.res_company_1
msgid ""
"Your lunch has been delivered.\n"
"Enjoy your meal!"
msgstr ""
"Je lunch is geleverd. \n"
"Eet smakelijk!"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Your stock is currently empty"
msgstr "Je hebt momenteel geen voorraad"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__package_label_to_print__zpl
msgid "ZPL"
msgstr "ZPL"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__lot_label_layout__print_format__zpl
#: model:ir.model.fields.selection,name:stock.selection__product_label_layout__print_format__zpl
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__product_label_format__zpl
msgid "ZPL Labels"
msgstr "ZPL-labels"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__lot_label_format__zpl_lots
msgid "ZPL Labels - One per lot/SN"
msgstr "ZPL-labels - Eén per lot/SN"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__lot_label_format__zpl_units
msgid "ZPL Labels - One per unit"
msgstr "ZPL-labels - Eén per eenheid"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_label_layout__print_format__zplxprice
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__product_label_format__zplxprice
msgid "ZPL Labels with price"
msgstr "ZPL-labels met prijs"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "]<br/>min:"
msgstr "]<br/>min:"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "barcodelookup.com"
msgstr "barcodelookup.com"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
msgid "below the inventory"
msgstr "onder de het voorraadniveau van"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_bpost
msgid "bpost Connector"
msgstr "bPost connector"

#. module: stock
#: model:product.removal,method:stock.removal_closest
msgid "closest"
msgstr "dichtstbijzijnde"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/views/search/stock_orderpoint_search_panel.xml:0
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "days"
msgstr "dagen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "days before when starred"
msgstr "dagen voor met ster"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "days before/"
msgstr "dagen voor/"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "e.g. CW"
msgstr "bijv. CM"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "e.g. Central Warehouse"
msgstr "bijv. Centraal magazijn"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "e.g. LOT-PR-00012"
msgstr "bijv. LOT-PR-00012"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid "e.g. LOT/0001/20121"
msgstr "bijv. LOT/0001/20121"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "e.g. Lumber Inc"
msgstr "bijv. Lumber Inc"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "e.g. PACK0000007"
msgstr "bijv. PACK0000007"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "e.g. PO0032"
msgstr "Bijv. PO0032"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "e.g. Physical Locations"
msgstr "bijv. Fysieke locaties"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "e.g. Receptions"
msgstr "bijv. Ontvangsten"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_operation_tree
msgid "e.g. SN000001"
msgstr "bijv. SN000001"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "e.g. Spare Stock"
msgstr "bijv. Reservevoorraad"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "e.g. Two-steps reception"
msgstr "bijv. Ontvangst in twee stappen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "e.g. d7vctmiv2rwgenebha8bxq7irooudn"
msgstr "bijv. d7vctmiv2rwgenebha8bxq7irooudn"

#. module: stock
#: model:product.removal,method:stock.removal_fifo
msgid "fifo"
msgstr "fifo"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_scrap_form_view
msgid "from location"
msgstr "vanuit locatie"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
msgid "in"
msgstr "in"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "in barcode."
msgstr "in barcode."

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
#: model_terms:ir.ui.view,arch_db:stock.view_product_replenish
msgid "is"
msgstr "is"

#. module: stock
#: model:product.removal,method:stock.removal_least_packages
msgid "least_packages"
msgstr "least_packages"

#. module: stock
#: model:product.removal,method:stock.removal_lifo
msgid "lifo"
msgstr "lifo"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
msgid "minimum of"
msgstr "minimaal"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid "of"
msgstr "of"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/stock_rescheduling_popover.xml:0
msgid "planned on"
msgstr "gepland op"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid "processed instead of"
msgstr "verwerkt in plaats van"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_report_view_graph
msgid "report_stock_quantity_graph"
msgstr "report_stock_quantity_graph"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "reserved"
msgstr "gereserveerd"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
msgid "should be replenished"
msgstr "en moet worden aangevuld"

#. module: stock
#: model:ir.actions.server,name:stock.click_dashboard_graph
msgid "stock.click_dashboard_graph"
msgstr "voorraad.click_dashboard_grafiek"

#. module: stock
#: model:ir.actions.server,name:stock.method_action_picking_tree_incoming
msgid "stock.method_action_picking_tree_incoming"
msgstr "stock.method_action_picking_tree_incoming"

#. module: stock
#: model:ir.actions.server,name:stock.method_action_picking_tree_internal
msgid "stock.method_action_picking_tree_internal"
msgstr "stock.method_action_picking_tree_internal"

#. module: stock
#: model:ir.actions.server,name:stock.method_action_picking_tree_outgoing
msgid "stock.method_action_picking_tree_outgoing"
msgstr "stock.method_action_picking_tree_outgoing"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_category__filter_for_stock_putaway_rule
msgid "stock.putaway.rule"
msgstr "voorraad.putaway.regel"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.help_message_template
msgid "the barcode app"
msgstr "de Barcode-app"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__warehouse_id
msgid ""
"the warehouse to consider for the route selection on the next procurement "
"(if any)."
msgstr ""
"het magazijn om te overwegen voor de routekeuze bij de volgende inkoop "
"(indien van toepassing)."

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
msgid "to reach the maximum of"
msgstr "om het maximum te bereiken van"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "units"
msgstr "eenheden"

#. module: stock
#: model:mail.template,subject:stock.mail_template_data_delivery_confirmation
msgid ""
"{{ object.company_id.name }} Delivery Order (Ref {{ object.name or 'n/a' }})"
msgstr ""
"{{ object.company_id.name }} Leveringsorder (Ref {{ object.name or 'nvt' }})"
