# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_hr_recruitment
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: website_hr_recruitment
#. odoo-python
#: code:addons/website_hr_recruitment/models/hr_job.py:0
msgid ""
"\n"
"            <span class=\"text-muted small\">Time to Answer</span>\n"
"            <h6>2 open days</h6>\n"
"            <span class=\"text-muted small\">Process</span>\n"
"            <h6>1 Phone Call</h6>\n"
"            <h6>1 Onsite Interview</h6>\n"
"            <span class=\"text-muted small\">Days to get an Offer</span>\n"
"            <h6>4 Days after Interview</h6>\n"
"        "
msgstr ""
"\n"
"            <span class=\"text-muted small\">Tiempo para responder</span>\n"
"            <h6>2 días abiertos</h6>\n"
"            <span class=\"text-muted small\">Proceso</span>\n"
"            <h6>1 llamada telefónica</h6>\n"
"            <h6>1 entrevista presencial</h6>\n"
"            <span class=\"text-muted small\">Días para recibir una oferta</span>\n"
"            <h6>4 días después de la entrevista</h6>\n"
"        "

#. module: website_hr_recruitment
#. odoo-python
#: code:addons/website_hr_recruitment/controllers/main.py:0
msgid " In case of issue, contact %(contact_infos)s"
msgstr " En caso de problemas, póngase en contacto con %(contact_infos)s"

#. module: website_hr_recruitment
#. odoo-python
#: code:addons/website_hr_recruitment/models/hr_applicant.py:0
msgid "%s's Application"
msgstr "Solicitud de %s"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid "'. Showing results for '"
msgstr "\". Mostrando resultados para \""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_right_side_bar
msgid "+****************"
msgstr "+****************"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "12 days / year, including <br/>6 of your choice."
msgstr "12 días/año, incluyendo <br/>6 de su elección."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.view_hr_job_kanban_referal_extends
msgid ""
"<i class=\"fa fa-fw fa-external-link\" role=\"img\"/>\n"
"                        Job Page"
msgstr ""
"<i class=\"fa fa-fw fa-external-link\" role=\"img\"/>\n"
"                        Página de trabajos"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_pages_kanban_view
msgid "<i class=\"fa fa-globe me-1\" title=\"Website\"/>"
msgstr "<i class=\"fa fa-globe me-1\" title=\"Website\"/>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.detail
msgid "<i class=\"fa fa-long-arrow-left text-primary me-2\"/>All Jobs"
msgstr "<i class=\"fa fa-long-arrow-left text-primary me-2\"/>Todos los trabajos"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid ""
"<i class=\"fa fa-suitcase fa-fw\" title=\"Employment type\" role=\"img\" "
"aria-label=\"Employment type\"/>"
msgstr ""
"<i class=\"fa fa-suitcase fa-fw\" title=\"Employment type\" role=\"img\" "
"aria-label=\"Employment type\"/>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "<i class=\"oi oi-arrow-left\"/> Job Description"
msgstr "<i class=\"oi oi-arrow-left\"/> Descirpción del trabajo "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "<small><b>READ</b></small>"
msgstr "<small><b>LEER</b></small>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_filter_by_offices
msgid "<span class=\"fst-italic\">No address specified</span>"
msgstr "<span class=\"fst-italic\">Sin dirección especificada</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.thankyou_ir_ui_view
msgid ""
"<span class=\"h5 fw-light\">In the meantime,</span><br/>\n"
"                                            <span class=\"h3 mt8 mb32 fw-bold\">Take a look around our website:</span>"
msgstr ""
"<span class=\"h5 fw-light\">Mientras tanto,</span><br/>\n"
"                                            <span class=\"h3 mt8 mb32 fw-bold\">Echele un vistazo a nuestro sitio web:</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid "<span class=\"navbar-brand h5 my-0 me-sm-auto\">Our Job Offers</span>"
msgstr ""
"<span class=\"navbar-brand h5 my-0 me-sm-auto\">Nuestras ofertas de "
"empleo</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "<span class=\"s_website_form_label_content\">Department</span>"
msgstr "<span class=\"s_website_form_label_content\">Departamento</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "<span class=\"s_website_form_label_content\">Job</span>"
msgstr "<span class=\"s_website_form_label_content\">Trabajo</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "<span class=\"s_website_form_label_content\">LinkedIn Profile</span>"
msgstr "<span class=\"s_website_form_label_content\">Perfil de LinkedIn</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "<span class=\"s_website_form_label_content\">Resume</span>"
msgstr "<span class=\"s_website_form_label_content\">Currículum</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "<span class=\"s_website_form_label_content\">Short Introduction</span>"
msgstr "<span class=\"s_website_form_label_content\">Breve introducción</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid ""
"<span class=\"s_website_form_label_content\">Your Email</span>\n"
"                                                <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Su correo electrónico</span>\n"
"                                                <span class=\"s_website_form_mark\"> *</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid ""
"<span class=\"s_website_form_label_content\">Your Name</span>\n"
"                                                <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Su nombre</span>\n"
"                                                <span class=\"s_website_form_mark\"> *</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid ""
"<span class=\"s_website_form_label_content\">Your Phone Number</span>\n"
"                                                <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Su número de teléfono</span>\n"
"                                                <span class=\"s_website_form_mark\"> *</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "<span class=\"text-muted small\">Department</span>"
msgstr "<span class=\"text-muted small\">Departamento</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "<span class=\"text-muted small\">Employment Type</span>"
msgstr "<span class=\"text-muted small\">Tipo de empleo </span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "<span class=\"text-muted small\">Job</span>"
msgstr "<span class=\"text-muted small\">Trabajo</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "<span class=\"text-muted small\">Location</span>"
msgstr "<span class=\"text-muted small\">Ubicación</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid ""
"<span class=\"text-muted small\">Provide either a resume file or a linkedin "
"profile</span>"
msgstr ""
"<span class=\"text-muted small\">Proporcione un archivo con su currículum o "
"un perfil de LinkedIn</span>"

#. module: website_hr_recruitment
#. odoo-javascript
#: code:addons/website_hr_recruitment/static/src/fields/boolean_toggle_labeled_field.js:0
msgid "A clickable label for the toggle. Contains text for the false state."
msgstr ""
"Una etiqueta en la que se puede hacer clic para alternar. Contiene texto del"
" estado falso."

#. module: website_hr_recruitment
#. odoo-javascript
#: code:addons/website_hr_recruitment/static/src/fields/boolean_toggle_labeled_field.js:0
msgid "A clickable label for the toggle. Contains text for the true state."
msgstr ""
"Una etiqueta en la que se puede hacer clic para alternar. Contiene texto del"
" estado verdadero."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "A full-time position <br/>Attractive salary package."
msgstr "Puesto de tiempo completo <br/> Sueldo atractivo."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.thankyou_ir_ui_view
msgid "About Us"
msgstr "Sobre Nosotros"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_right_side_bar
msgid "About us"
msgstr "Sobre nosotros"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Achieve monthly sales objectives"
msgstr "Alcance los objetivos de ventas mensuales"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Additional languages"
msgstr "Idiomas adicionales"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Administrative Work"
msgstr "Trabajo administrativo"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_filter_by_countries
msgid "All Countries"
msgstr "Todos los países"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_filter_by_departments
msgid "All Departments"
msgstr "Todos los departamentos"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_filter_by_offices
msgid "All Offices"
msgstr "Todas las oficinas"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_filter_by_employment_type
msgid "All Types"
msgstr "Todos los tipos"

#. module: website_hr_recruitment
#. odoo-python
#: code:addons/website_hr_recruitment/controllers/main.py:0
msgid ""
"An application already exists for %(value)s. Duplicates might be rejected. "
"%(recruiter_contact)s"
msgstr ""
"Ya existe una aplicación para %(value)s. Es posible que los duplicados se "
"rechacen. %(recruiter_contact)s"

#. module: website_hr_recruitment
#: model:ir.model,name:website_hr_recruitment.model_hr_applicant
msgid "Applicant"
msgstr "Candidato"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.hr_job_form_inherit
msgid "Application Info"
msgstr "Información de solicitud"

#. module: website_hr_recruitment
#. odoo-javascript
#: code:addons/website_hr_recruitment/static/src/js/website_hr_recruitment_editor.js:0
msgid "Applied Job"
msgstr "Trabajo solicitado"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "Apply Job"
msgstr "Solicitar trabajo"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.detail
msgid "Apply Now!"
msgstr "¡Solicitar ahora!"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid ""
"As an employee of our company, you will <b>collaborate with each department\n"
"                        to create and deploy disruptive products.</b> Come work at a growing company\n"
"                        that offers great benefits with opportunities to moving forward and learn\n"
"                        alongside accomplished leaders. We're seeking an experienced and outstanding\n"
"                        member of staff.\n"
"                        <br/><br/>\n"
"                        This position is both <b>creative and rigorous</b> by nature you need to think\n"
"                        outside the box. We expect the candidate to be proactive and have a \"get it done\"\n"
"                        spirit. To be successful, you will have solid solving problem skills."
msgstr ""
"Como empleado de nuestra empresa, <b>colaborará con cada departamento\n"
"                        para crear e implementar productos revolucionarios.</b> Venga a trabajar en una empresa en crecimiento\n"
"                        que ofrece grandes beneficios con oportunidades para avanzar y aprender\n"
"                        junto con líderes consumados. Buscamos un miembro del personal experimentado\n"
"                        y excepcional.\n"
"                        <br/><br/>\n"
"                        Este puesto es <b>creativo y riguroso</b> por naturaleza, debe pensar\n"
"                        fuera de la caja. Esperamos que el candidato sea proactivo y tenga una mentalidad orientada a\n"
"                        resultados. Para tener éxito, debe tener sólidas habilidades para resolver problemas."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Autonomy"
msgstr "Autonomía"

#. module: website_hr_recruitment
#. odoo-javascript
#: code:addons/website_hr_recruitment/static/src/fields/boolean_toggle_labeled_field.js:0
msgid "Autosave"
msgstr "Guardar automáticamente"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Bachelor Degree or Higher"
msgstr "Título de grado o superior"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__can_publish
msgid "Can Publish"
msgstr "Puede publicar"

#. module: website_hr_recruitment
#: model:ir.model.fields,help:website_hr_recruitment.field_hr_job__job_details
msgid "Complementary information that will appear on the job submission page"
msgstr ""
"Información complementaria que aparecerá en la página de envío de trabajo"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.thankyou_ir_ui_view
msgid "Congratulations!"
msgstr "¡Enhorabuena!"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_right_side_bar
msgid "Contact us"
msgstr "Contáctenos"

#. module: website_hr_recruitment
#. odoo-javascript
#: code:addons/website_hr_recruitment/static/src/js/widgets/copy_link_menuitem.js:0
msgid "Copied"
msgstr "Copiado"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.snippet_options
msgid "Countries Filter"
msgstr "Filtro de países"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Create content that will help our users on a daily basis"
msgstr "Crear contenido que ayude a nuestros usuarios a diario"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid ""
"Create new job pages from the <strong>+ <i>New</i></strong> top-right "
"button."
msgstr ""
"Cree nuevas páginas de empleo con el botón <strong>+ <i>Nuevo</i></strong> "
"en la parte superior derecha. "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Customer Relationship"
msgstr "Relación con el cliente"

#. module: website_hr_recruitment
#. odoo-javascript
#: code:addons/website_hr_recruitment/static/src/js/website_hr_recruitment_editor.js:0
#: model:ir.model,name:website_hr_recruitment.model_hr_department
msgid "Department"
msgstr "Departamento"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.snippet_options
msgid "Departments Filter"
msgstr "Filtro de departamentos"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.jobs_searchbar_input_snippet_options
msgid "Description"
msgstr "Descripción"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Discover our products."
msgstr "Descubra nuestros productos."

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_department__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid ""
"Each employee has a chance to see the impact of his work.\n"
"                    You can make a real contribution to the success of the company.\n"
"                    <br/>\n"
"                    Several activities are often organized all over the year, such as weekly\n"
"                    sports sessions, team building events, monthly drink, and much more"
msgstr ""
"Cada empleado tiene la oportunidad de ver el impacto de su trabajo.\n"
"                     Usted puede hacer una contribución real al éxito de la compañía.\n"
"                     <br/>\n"
"                     Se organizan varias actividades a lo largo del año, como actividades deportivas\n"
"                        semanales, eventos para fomentar el trabajo en equipo, una convivencia al mes, ¡y mucho más!"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Eat &amp; Drink"
msgstr "Comer y beber"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.snippet_options
msgid "Employment Types Filter"
msgstr "Filtro de tipos de empleo"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Expand your knowledge of various business industries"
msgstr "Amplíe su conocimiento de diversas industrias comerciales"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Experience in writing online content"
msgstr "Experiencia en redacción de contenido online"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_right_side_bar
msgid "Facebook"
msgstr "Facebook"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_right_side_bar
msgid "Follow us"
msgstr "Síganos"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Fruit, coffee and <br/>snacks provided."
msgstr "Frutas, café y <br/>aperitivos brindados."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Google Adwords experience"
msgstr "Experiencia de Google Adwords"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Great team of smart people, in a friendly and open culture"
msgstr "Gran equipo de gente inteligente, en una cultura amigable y abierta"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Highly creative and autonomous"
msgstr "Altamente creativo y autónomo"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.thankyou_ir_ui_view
msgid "Home"
msgstr "Inicio"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.thankyou_ir_ui_view
msgid ""
"I usually <strong>answer applications within 3 days</strong>.\n"
"                                                <br/><br/>\n"
"                                                The next step is either a call or a meeting in our offices.\n"
"                                                <br/><br/>\n"
"                                                Feel free to <strong>contact me if you want a faster\n"
"                                                feedback</strong> or if you don't get news from me\n"
"                                                quickly enough."
msgstr ""
"Suelo <strong>responder a las solicitudes en 3 días</strong>.\n"
"                                                <br/><br/>\n"
"                                                El siguiente paso es relizar una llamada o tener una reunión en nuestras oficinas.\n"
"                                                <br/><br/>\n"
"                                                Sientase libre de <strong>contactarme si quieren una retroalimentación\n"
"                                                más rápida</strong> o si no tiene noticias de nosotros\n"
"                                                pronto."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "I'm feeling lucky"
msgstr "Me siento afortunado"

#. module: website_hr_recruitment
#. odoo-javascript
#: code:addons/website_hr_recruitment/static/src/fields/boolean_toggle_labeled_field.js:0
msgid ""
"If checked, the record will be saved immediately when the field is modified."
msgstr ""
"Si está seleccionado, el registro se guardará inmediatamente cuando se "
"modifique el campo."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid "Insert a Job Description..."
msgstr "Inserte una descripción del trabajo..."

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__is_published
msgid "Is Published"
msgstr "Está publicado"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "Job Application Form"
msgstr "Formulario de solicitud de empleo"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__description
msgid "Job Description"
msgstr "Descripción del trabajo"

#. module: website_hr_recruitment
#: model:ir.actions.act_window,name:website_hr_recruitment.action_job_pages_list
msgid "Job Pages"
msgstr "Páginas de trabajo"

#. module: website_hr_recruitment
#: model:ir.model,name:website_hr_recruitment.model_hr_job
msgid "Job Position"
msgstr "Puesto de trabajo"

#. module: website_hr_recruitment
#. odoo-python
#: code:addons/website_hr_recruitment/controllers/main.py:0
msgid "Job Title"
msgstr "Título del trabajo"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid "Job not found"
msgstr "Trabajo no encontrado"

#. module: website_hr_recruitment
#. odoo-python
#: code:addons/website_hr_recruitment/models/website.py:0
#: model:ir.ui.menu,name:website_hr_recruitment.menu_job_pages
#: model:website.menu,name:website_hr_recruitment.website_menu_jobs
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.jobs_searchbar_input_snippet_options
msgid "Jobs"
msgstr "Trabajos"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.snippet_options
msgid "Jobs Page"
msgstr "Página de trabajos"

#. module: website_hr_recruitment
#. odoo-javascript
#: code:addons/website_hr_recruitment/static/src/fields/boolean_toggle_labeled_field.js:0
msgid "Label"
msgstr "Descripción"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Lead the entire sales cycle"
msgstr "Dirigir todo el ciclo de ventas"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_right_side_bar
msgid "LinkedIn"
msgstr "LinkedIn"

#. module: website_hr_recruitment
#. odoo-javascript
#: code:addons/website_hr_recruitment/static/src/js/website_hr_recruitment_editor.js:0
msgid "LinkedIn Profile"
msgstr "Perfil de LinkedIn"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Master demos of our software"
msgstr "Ser maestro en demos de nuestro software"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Must Have"
msgstr "Debe tener"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Negotiate and contract"
msgstr "Negociar y contratar"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Nice to have"
msgstr "Deseable"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_filter_by_offices
msgid "No address specified"
msgstr "Sin dirección especificada "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "No dumb managers, no stupid tools to use, no rigid working hours"
msgstr ""
"Sin gerentes tontos, sin herramientas estúpidas para usar, sin horarios de "
"trabajo rígidos"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid "No results found for '"
msgstr "No se han encontrado resultados para \""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid ""
"No waste of time in enterprise processes, real responsibilities and autonomy"
msgstr ""
"Sin pérdida de tiempo en los procesos empresariales, responsabilidades "
"reales y autonomía"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.snippet_options
msgid "Offices Filter"
msgstr "Filtro de oficinas"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "Optional introduction, or any question you might have about the job…"
msgstr ""
"Presentación opcional, o cualquier pregunta que tenga sobre el trabajo..."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_filter_by_departments
msgid "Others"
msgstr "Otros"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Our Product"
msgstr "Nuestro producto"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Passion for software products"
msgstr "Pasión por los productos de software"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Perfect written English"
msgstr "Inglés escrito perfecto"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Perks"
msgstr "Beneficios"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Personal Evolution"
msgstr "Evolución personal"

#. module: website_hr_recruitment
#. odoo-javascript
#: code:addons/website_hr_recruitment/static/src/js/website_hr_recruitment_editor.js:0
msgid "Phone Number"
msgstr "Número de teléfono"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Play any sport with colleagues, <br/>the bill is covered."
msgstr ""
"Practique cualquier deporte con sus colegas <br/>y nosotros lo pagamos."

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__job_details
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.hr_job_form_inherit
msgid "Process Details"
msgstr "Detalles del proceso"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.thankyou_ir_ui_view
msgid "Products"
msgstr "Productos"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.hr_job_search_view_inherit
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.hr_job_website_inherit
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.view_hr_job_tree_inherit_website
msgid "Published"
msgstr "Publicado"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__published_date
msgid "Published Date"
msgstr "Fecha publicada"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Qualify the customer needs"
msgstr "Calificar las necesidades del cliente"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Real responsibilities and challenges in a fast evolving company"
msgstr ""
"Responsabilidades y desafíos reales en una empresa en rápida evolución"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_filter_by_countries
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_filter_by_offices
msgid "Remote"
msgstr "Remoto"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Responsibilities"
msgstr "Responsabilidades"

#. module: website_hr_recruitment
#: model:ir.model.fields,help:website_hr_recruitment.field_hr_job__website_id
msgid "Restrict to a specific website."
msgstr "Restringir a un sitio web específico."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_pages_kanban_view
msgid "SEO Optimized"
msgstr "Optimizado para SEO"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__is_seo_optimized
msgid "SEO optimized"
msgstr "Optimizado para SEO"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.snippet_options
msgid "Search Bar"
msgstr "Barra de búsqueda"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__seo_name
msgid "Seo name"
msgstr "Nombre SEO"

#. module: website_hr_recruitment
#: model:ir.model.fields,help:website_hr_recruitment.field_hr_job__website_published
msgid "Set if the application is published on the website of the company."
msgstr ""
"Establezca si la solicitud se publicará en el sitio web de la empresa."

#. module: website_hr_recruitment
#. odoo-javascript
#: code:addons/website_hr_recruitment/static/src/js/widgets/copy_link_menuitem.js:0
msgid "Share Job"
msgstr "Compartir trabajo"

#. module: website_hr_recruitment
#. odoo-javascript
#: code:addons/website_hr_recruitment/static/src/js/website_hr_recruitment_editor.js:0
msgid "Short Introduction"
msgstr "Breve introducción"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.snippet_options
msgid "Sidebar"
msgstr "Barra lateral"

#. module: website_hr_recruitment
#: model:ir.model,name:website_hr_recruitment.model_hr_recruitment_source
msgid "Source of Applicants"
msgstr "Origen de los candidatos"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Sport Activity"
msgstr "Actividad deportiva"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Strong analytical skills"
msgstr "Habilidades analíticas avanzadas"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Technical Expertise"
msgstr "Conocimientos técnicos"

#. module: website_hr_recruitment
#: model:ir.model.fields,help:website_hr_recruitment.field_hr_job__website_url
msgid "The full URL to access the document through the website."
msgstr "La URL completa para acceder al documento a través del sitio web."

#. module: website_hr_recruitment
#. odoo-javascript
#: code:addons/website_hr_recruitment/static/src/js/widgets/copy_link_menuitem.js:0
msgid "The job link has been copied to the clipboard."
msgstr "El enlace del trabajo se ha copiado al portapapeles."

#. module: website_hr_recruitment
#. odoo-python
#: code:addons/website_hr_recruitment/models/hr_applicant.py:0
msgid "The job offer has been closed."
msgstr "Esta oferta de trabajo está cerrada."

#. module: website_hr_recruitment
#. odoo-javascript
#: code:addons/website_hr_recruitment/static/src/js/website_hr_applicant_form.js:0
msgid "The profile that you gave us doesn't seems like a linkedin profile"
msgstr "Parece que el perfil que nos proporcionó no es de LinkedIn"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid ""
"There are currently no open job opportunities,<br class=\"mb-2\"/>\n"
"                                            but feel free to <span class=\"fw-bold\">contact us</span> for a spontaneous application."
msgstr ""
"No hay oportunidades de empleo en este momento,<br class=\"mb-2\"/>\n"
"                                            pero no dude en <span class=\"fw-bold\">contáctarnos</span> para una solicitud espontánea."

#. module: website_hr_recruitment
#. odoo-javascript
#: code:addons/website_hr_recruitment/static/src/fields/boolean_toggle_labeled_field.js:0
msgid "ToggleLabeled"
msgstr "ToggleLabeled"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_recruitment_source__url
msgid "Tracker URL"
msgstr "URL para rastrear"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Trainings"
msgstr "Entrenamientos"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_filter_by_employment_type
msgid "Unspecified type"
msgstr "Tipo no especificado"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Valid work permit for Belgium"
msgstr "Permiso de trabajo válido para Bélgica"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__website_published
msgid "Visible on current website"
msgstr "Visible en el sitio web actual"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_right_side_bar
msgid ""
"We are a team of passionate people whose goal is to improve everyone's life through disruptive products.\n"
"                                We build great products to solve your business problems."
msgstr ""
"Somos un equipo de personas apasionadas cuyo objetivo es mejorar la vida de todo el mundo a través de nuestros productos revolucionarios.\n"
"                                Creamos grandes productos para resolver sus problemas empresariales."

#. module: website_hr_recruitment
#. odoo-python
#: code:addons/website_hr_recruitment/controllers/main.py:0
msgid ""
"We found a recent application with a similar name, email, phone number. You "
"can continue if it's not a mistake."
msgstr ""
"Encontramos una solicitud reciente con un nombre, correo electrónico y "
"número de teléfono similares. Continúe si no se trata de un error."

#. module: website_hr_recruitment
#. odoo-python
#: code:addons/website_hr_recruitment/controllers/main.py:0
msgid ""
"We've found a previous closed application in our system within the last 6 "
"months. Please consider before applying in order not to duplicate efforts."
msgstr ""
"Encontramos una solicitud anterior cerrada en nuestro sistema dentro de los "
"próximos 6 meses. Considere esto antes de volver a presentarse para no "
"duplicar esfuerzos."

#. module: website_hr_recruitment
#: model:ir.model,name:website_hr_recruitment.model_website
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__website_id
msgid "Website"
msgstr "Sitio web"

#. module: website_hr_recruitment
#: model:ir.actions.act_url,name:website_hr_recruitment.action_open_website
msgid "Website Recruitment Form"
msgstr "Formulario de reclutamiento del sitio web"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__website_url
msgid "Website URL"
msgstr "URL del sitio web"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__website_description
msgid "Website description"
msgstr "Descripción del sitio web"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__website_meta_description
msgid "Website meta description"
msgstr "Descripción meta del sitio web"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__website_meta_keywords
msgid "Website meta keywords"
msgstr "Palabras clave meta del sitio web"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__website_meta_title
msgid "Website meta title"
msgstr "Título meta del sitio web"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__website_meta_og_img
msgid "Website opengraph image"
msgstr "Imagen Open Graph del sitio web"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "What We Offer"
msgstr "Lo que ofrecemos"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "What's great in the job?"
msgstr "¿Qué hay de bueno en el trabajo?"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.thankyou_ir_ui_view
msgid "Your <b>contact</b> information is:"
msgstr "Su infromación de <b>contacto</b> es:"

#. module: website_hr_recruitment
#. odoo-javascript
#: code:addons/website_hr_recruitment/static/src/js/website_hr_recruitment_editor.js:0
msgid "Your Email"
msgstr "Su correo electrónico"

#. module: website_hr_recruitment
#. odoo-javascript
#: code:addons/website_hr_recruitment/static/src/js/website_hr_recruitment_editor.js:0
msgid "Your Name"
msgstr "Su nombre"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.thankyou_ir_ui_view
msgid ""
"Your application has been posted successfully,<br class=\"mb-2\"/>\n"
"                                    We usually respond within 3 days..."
msgstr ""
"Su solicitud se ha enviado correctamente,<br class=\"mb-2\"/>\n"
"                                    Solemos responder en 3 días..."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "breadcrumb"
msgstr "barra de migas"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.view_hr_job_form_inherit_website
msgid ""
"e.g. Summarize the position in one or two lines that will be displayed on "
"the Jobs list page..."
msgstr ""
"p. ej. Resumen del puesto en una o dos líneas que se mostrará en la página "
"de bolsa de trabajo..."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "e.g. https://www.linkedin.com/in/fpodoo"
msgstr "p. ej. https://www.linkedin.com/in/fpodoo"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_right_side_bar
msgid "<EMAIL>"
msgstr "info@sucompañía.example.com"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__full_url
msgid "job URL"
msgstr "URL del trabajo"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid "open positions"
msgstr "puesto(s) disponible(s)"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid "unpublished"
msgstr "no publicado"
