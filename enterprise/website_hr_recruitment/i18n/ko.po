# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_hr_recruitment
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_hr_recruitment
#. odoo-python
#: code:addons/website_hr_recruitment/models/hr_job.py:0
msgid ""
"\n"
"            <span class=\"text-muted small\">Time to Answer</span>\n"
"            <h6>2 open days</h6>\n"
"            <span class=\"text-muted small\">Process</span>\n"
"            <h6>1 Phone Call</h6>\n"
"            <h6>1 Onsite Interview</h6>\n"
"            <span class=\"text-muted small\">Days to get an Offer</span>\n"
"            <h6>4 Days after Interview</h6>\n"
"        "
msgstr ""
"\n"
"            <span class=\"text-muted small\">응답 시간</span>\n"
"            <h6>2 영업일</h6>\n"
"            <span class=\"text-muted small\">프로세스</span>\n"
"            <h6>1 전화 면접</h6>\n"
"            <h6>1 대면 면접</h6>\n"
"            <span class=\"text-muted small\">채용 제안까지의 일수</span>\n"
"            <h6>면접 후 4일</h6>\n"
"        "

#. module: website_hr_recruitment
#. odoo-python
#: code:addons/website_hr_recruitment/controllers/main.py:0
msgid " In case of issue, contact %(contact_infos)s"
msgstr "문제가 발생한 경우 %(contact_infos)s에 문의하세요."

#. module: website_hr_recruitment
#. odoo-python
#: code:addons/website_hr_recruitment/models/hr_applicant.py:0
msgid "%s's Application"
msgstr "%s의 신청서"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid "'. Showing results for '"
msgstr "'. 다음에 대한 결과 표시 중 '"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_right_side_bar
msgid "+1 (650) 555-0187"
msgstr "+1 (650) 555-0187"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "12 days / year, including <br/>6 of your choice."
msgstr "원하는 날짜 6일을 포함하여 <br/>연간 총 12일의 교육을 제공합니다."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.view_hr_job_kanban_referal_extends
msgid ""
"<i class=\"fa fa-fw fa-external-link\" role=\"img\"/>\n"
"                        Job Page"
msgstr ""
"<i class=\"fa fa-fw fa-external-link\" role=\"img\"/>\n"
"                        채용 페이지"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_pages_kanban_view
msgid "<i class=\"fa fa-globe me-1\" title=\"Website\"/>"
msgstr "<i class=\"fa fa-globe me-1\" title=\"Website\"/>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.detail
msgid "<i class=\"fa fa-long-arrow-left text-primary me-2\"/>All Jobs"
msgstr "<i class=\"fa fa-long-arrow-left text-primary me-2\"/>모든 채용"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid ""
"<i class=\"fa fa-suitcase fa-fw\" title=\"Employment type\" role=\"img\" "
"aria-label=\"Employment type\"/>"
msgstr "<i class=\"fa fa-suitcase fa-fw\" title=\"근무 유형\" role=\"img\" aria-label=\"근무 유형\"/>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "<i class=\"oi oi-arrow-left\"/> Job Description"
msgstr "<i class=\"oi oi-arrow-left\"/> 직무 설명"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "<small><b>READ</b></small>"
msgstr "<small><b>읽어보기</b></small>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_filter_by_offices
msgid "<span class=\"fst-italic\">No address specified</span>"
msgstr "<span class=\"fst-italic\">지정된 주소 없음</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.thankyou_ir_ui_view
msgid ""
"<span class=\"h5 fw-light\">In the meantime,</span><br/>\n"
"                                            <span class=\"h3 mt8 mb32 fw-bold\">Take a look around our website:</span>"
msgstr ""
"<span class=\"h5 fw-light\">그동안,</span><br/>\n"
"                                            <span class=\"h3 mt8 mb32 fw-bold\">웹사이트를 둘러보세요:</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid "<span class=\"navbar-brand h5 my-0 me-sm-auto\">Our Job Offers</span>"
msgstr "<span class=\"navbar-brand h5 my-0 me-sm-auto\">채용 정보</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "<span class=\"s_website_form_label_content\">Department</span>"
msgstr "<span class=\"s_website_form_label_content\">부서</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "<span class=\"s_website_form_label_content\">Job</span>"
msgstr "<span class=\"s_website_form_label_content\">직무</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "<span class=\"s_website_form_label_content\">LinkedIn Profile</span>"
msgstr "<span class=\"s_website_form_label_content\">링크드인 프로필</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "<span class=\"s_website_form_label_content\">Resume</span>"
msgstr "<span class=\"s_website_form_label_content\">경력 사항</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "<span class=\"s_website_form_label_content\">Short Introduction</span>"
msgstr "<span class=\"s_website_form_label_content\">간단한 소개</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid ""
"<span class=\"s_website_form_label_content\">Your Email</span>\n"
"                                                <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">이메일 주소</span>\n"
"                                                <span class=\"s_website_form_mark\"> *</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid ""
"<span class=\"s_website_form_label_content\">Your Name</span>\n"
"                                                <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">이름</span>\n"
"                                                 <span class=\"s_website_form_mark\"> *</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid ""
"<span class=\"s_website_form_label_content\">Your Phone Number</span>\n"
"                                                <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">전화번호</span>\n"
"                                                <span class=\"s_website_form_mark\"> *</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "<span class=\"text-muted small\">Department</span>"
msgstr "<span class=\"text-muted small\">부서</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "<span class=\"text-muted small\">Employment Type</span>"
msgstr "<span class=\"text-muted small\">고용 형태</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "<span class=\"text-muted small\">Job</span>"
msgstr "<span class=\"text-muted small\">직무</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "<span class=\"text-muted small\">Location</span>"
msgstr "<span class=\"text-muted small\">위치</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid ""
"<span class=\"text-muted small\">Provide either a resume file or a linkedin "
"profile</span>"
msgstr "<span class=\"text-muted small\">이력서 파일 또는 링크드인 프로필을 입력하세요</span>"

#. module: website_hr_recruitment
#. odoo-javascript
#: code:addons/website_hr_recruitment/static/src/fields/boolean_toggle_labeled_field.js:0
msgid "A clickable label for the toggle. Contains text for the false state."
msgstr "토글의 클릭 가능한 레이블로, 거짓 상태일 때 텍스트를 표시합니다. "

#. module: website_hr_recruitment
#. odoo-javascript
#: code:addons/website_hr_recruitment/static/src/fields/boolean_toggle_labeled_field.js:0
msgid "A clickable label for the toggle. Contains text for the true state."
msgstr "토글에 대한 클릭 가능한 레이블로, 참 상태일 때 텍스트를 표시합니다."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "A full-time position <br/>Attractive salary package."
msgstr "정규직 직책과 <br/> 높은 연봉 수준을 제공합니다."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.thankyou_ir_ui_view
msgid "About Us"
msgstr "회사 소개"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_right_side_bar
msgid "About us"
msgstr "회사 소개"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Achieve monthly sales objectives"
msgstr "월간 매출 목표 달성"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Additional languages"
msgstr "제2외국어 가능"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Administrative Work"
msgstr "행정 업무 능력"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_filter_by_countries
msgid "All Countries"
msgstr "모든 국가"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_filter_by_departments
msgid "All Departments"
msgstr "모든 부서"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_filter_by_offices
msgid "All Offices"
msgstr "모든 사무실"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_filter_by_employment_type
msgid "All Types"
msgstr "모든 유형"

#. module: website_hr_recruitment
#. odoo-python
#: code:addons/website_hr_recruitment/controllers/main.py:0
msgid ""
"An application already exists for %(value)s. Duplicates might be rejected. "
"%(recruiter_contact)s"
msgstr "%(value)s의 지원서가 이미 존재합니다. 중복 지원은 거부될 수 있습니다. %(recruiter_contact)s"

#. module: website_hr_recruitment
#: model:ir.model,name:website_hr_recruitment.model_hr_applicant
msgid "Applicant"
msgstr "지원자"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.hr_job_form_inherit
msgid "Application Info"
msgstr "지원 정보"

#. module: website_hr_recruitment
#. odoo-javascript
#: code:addons/website_hr_recruitment/static/src/js/website_hr_recruitment_editor.js:0
msgid "Applied Job"
msgstr "신청 직무"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "Apply Job"
msgstr "직무 신청"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.detail
msgid "Apply Now!"
msgstr "지금 신청하기!"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid ""
"As an employee of our company, you will <b>collaborate with each department\n"
"                        to create and deploy disruptive products.</b> Come work at a growing company\n"
"                        that offers great benefits with opportunities to moving forward and learn\n"
"                        alongside accomplished leaders. We're seeking an experienced and outstanding\n"
"                        member of staff.\n"
"                        <br/><br/>\n"
"                        This position is both <b>creative and rigorous</b> by nature you need to think\n"
"                        outside the box. We expect the candidate to be proactive and have a \"get it done\"\n"
"                        spirit. To be successful, you will have solid solving problem skills."
msgstr ""
"회사의 일원으로서 여러분은 <b>각 부서와 협력하여 혁신적인 제품을 생산하고 배포하게 됩니다.</b> 뛰어난 역량의 리더들과 함께 배우고 성장할 기회는 물론,\n"
"                        다양한 복지를 제공하는 회사에서 함께 일해보세요.\n"
"                        풍부한 경험과 잠재력을 지닌\n"
"                        인재를 모십니다..\n"
"                        <br/><br/>\n"
"                        해당 직책은 <b>창의성과 엄격함 </b>모두를 요구하며,\n"
"                        틀에 벗어난 사고를 필요로 합니다. 능동적이고 \"해내야 한다\"는 정신을 갖추고 있으며,\n"
"                        탄탄한 문제 해결 능력을 지닌 지원자 여러분을 기다리고 있습니다."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Autonomy"
msgstr "자율성"

#. module: website_hr_recruitment
#. odoo-javascript
#: code:addons/website_hr_recruitment/static/src/fields/boolean_toggle_labeled_field.js:0
msgid "Autosave"
msgstr "자동 저장"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Bachelor Degree or Higher"
msgstr "학사 학위 이상 취득자"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__can_publish
msgid "Can Publish"
msgstr "게시 가능"

#. module: website_hr_recruitment
#: model:ir.model.fields,help:website_hr_recruitment.field_hr_job__job_details
msgid "Complementary information that will appear on the job submission page"
msgstr "직무 제출 페이지에 표시되는 보완적 정보"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.thankyou_ir_ui_view
msgid "Congratulations!"
msgstr "축하합니다!"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_right_side_bar
msgid "Contact us"
msgstr "문의하기"

#. module: website_hr_recruitment
#. odoo-javascript
#: code:addons/website_hr_recruitment/static/src/js/widgets/copy_link_menuitem.js:0
msgid "Copied"
msgstr "복사됨"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.snippet_options
msgid "Countries Filter"
msgstr "국가 필터"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Create content that will help our users on a daily basis"
msgstr "사용자에게 실질적으로 도움이 되는 콘텐츠를 제작합니다."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid ""
"Create new job pages from the <strong>+ <i>New</i></strong> top-right "
"button."
msgstr "오른쪽 상단의 <strong>+ <i>신규</i></strong> 버튼을 눌러 새로운 직무 페이지를 작성합니다."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Customer Relationship"
msgstr "고객 관계"

#. module: website_hr_recruitment
#. odoo-javascript
#: code:addons/website_hr_recruitment/static/src/js/website_hr_recruitment_editor.js:0
#: model:ir.model,name:website_hr_recruitment.model_hr_department
msgid "Department"
msgstr "부서"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.snippet_options
msgid "Departments Filter"
msgstr "부서 필터"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.jobs_searchbar_input_snippet_options
msgid "Description"
msgstr "내용"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Discover our products."
msgstr "제품 알아보기."

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_department__display_name
msgid "Display Name"
msgstr "표시명"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid ""
"Each employee has a chance to see the impact of his work.\n"
"                    You can make a real contribution to the success of the company.\n"
"                    <br/>\n"
"                    Several activities are often organized all over the year, such as weekly\n"
"                    sports sessions, team building events, monthly drink, and much more"
msgstr ""
"각 직원은 본인의 업무 결과가 조직에 어떤 영향을 미치는지 인지함으로써\n"
"                    회사의 성공과 발전에 실질적인 기여를 할 수 있습니다.\n"
"                    <br/>\n"
"                    또한 매주 스포츠 세션과 조직 단합 행사, 매월 진행되는 회식 등\n"
"                    연중 수시로 진행되는 다양한 활동에 참여할 수 있습니다."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Eat &amp; Drink"
msgstr "간식 제공"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.snippet_options
msgid "Employment Types Filter"
msgstr "고용 유형 필터"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Expand your knowledge of various business industries"
msgstr "다양한 비즈니스 산업에 대한 지식을 넓힐 수 있습니다."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Experience in writing online content"
msgstr "온라인 콘텐츠 작성 경험"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_right_side_bar
msgid "Facebook"
msgstr "페이스북"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_right_side_bar
msgid "Follow us"
msgstr "함께하기"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Fruit, coffee and <br/>snacks provided."
msgstr "과일, 커피 및 <br/>각종 간식이 제공됩니다."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Google Adwords experience"
msgstr "구글 애드워즈 경험"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Great team of smart people, in a friendly and open culture"
msgstr "친절하고 개방적인 문화 속에서 스마트한 인재들로 구성된 팀입니다."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Highly creative and autonomous"
msgstr "높은 창의성과 자율성"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.thankyou_ir_ui_view
msgid "Home"
msgstr "홈"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.thankyou_ir_ui_view
msgid ""
"I usually <strong>answer applications within 3 days</strong>.\n"
"                                                <br/><br/>\n"
"                                                The next step is either a call or a meeting in our offices.\n"
"                                                <br/><br/>\n"
"                                                Feel free to <strong>contact me if you want a faster\n"
"                                                feedback</strong> or if you don't get news from me\n"
"                                                quickly enough."
msgstr ""
"일반적인 경우, <strong>지원 결과에 대한 안내를 수일 내로 드리게 됩니다.</strong>\n"
"                                                <br/><br/>\n"
"                                                다음 단계는 전화 면접 또는 사무실 대면 면접으로 이루어질 예정입니다.\n"
"                                                <br/><br/>\n"
"                                                더 빠른 피드백을 원하시거나 <strong>결과에 대한\n"
"                                                안내를 받지 못하신 경우 </strong>언제든지 저희에게 연락주시기 바랍니다."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "I'm feeling lucky"
msgstr "운이 좋은 것 같아요"

#. module: website_hr_recruitment
#. odoo-javascript
#: code:addons/website_hr_recruitment/static/src/fields/boolean_toggle_labeled_field.js:0
msgid ""
"If checked, the record will be saved immediately when the field is modified."
msgstr "이 옵션을 선택하면 필드가 수정될 때 기록이 즉시 저장됩니다."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid "Insert a Job Description..."
msgstr "직무 설명 삽입하기..."

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__is_published
msgid "Is Published"
msgstr "게시 여부"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "Job Application Form"
msgstr "입사지원서 양식"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__description
msgid "Job Description"
msgstr "직무 설명"

#. module: website_hr_recruitment
#: model:ir.actions.act_window,name:website_hr_recruitment.action_job_pages_list
msgid "Job Pages"
msgstr "채용 페이지"

#. module: website_hr_recruitment
#: model:ir.model,name:website_hr_recruitment.model_hr_job
msgid "Job Position"
msgstr "직무 영역"

#. module: website_hr_recruitment
#. odoo-python
#: code:addons/website_hr_recruitment/controllers/main.py:0
msgid "Job Title"
msgstr "직함"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid "Job not found"
msgstr "채용 정보 찾을 수 없음"

#. module: website_hr_recruitment
#. odoo-python
#: code:addons/website_hr_recruitment/models/website.py:0
#: model:ir.ui.menu,name:website_hr_recruitment.menu_job_pages
#: model:website.menu,name:website_hr_recruitment.website_menu_jobs
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.jobs_searchbar_input_snippet_options
msgid "Jobs"
msgstr "직무"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.snippet_options
msgid "Jobs Page"
msgstr "채용 페이지"

#. module: website_hr_recruitment
#. odoo-javascript
#: code:addons/website_hr_recruitment/static/src/fields/boolean_toggle_labeled_field.js:0
msgid "Label"
msgstr "라벨"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Lead the entire sales cycle"
msgstr "전체 영업 주기 리드"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_right_side_bar
msgid "LinkedIn"
msgstr "링크드인"

#. module: website_hr_recruitment
#. odoo-javascript
#: code:addons/website_hr_recruitment/static/src/js/website_hr_recruitment_editor.js:0
msgid "LinkedIn Profile"
msgstr "링크드인 프로필"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Master demos of our software"
msgstr "당사 소프트웨어 데모 습득"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Must Have"
msgstr "지원 자격"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Negotiate and contract"
msgstr "협상 및 계약"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Nice to have"
msgstr "우대 사항"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_filter_by_offices
msgid "No address specified"
msgstr "주소가 지정되지 않음"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "No dumb managers, no stupid tools to use, no rigid working hours"
msgstr "무능한 관리자, 비효율적인 도구의 사용, 경직된 근로 시간 제도가 없습니다."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid "No results found for '"
msgstr "' 에 대한 결과를 찾을 수 없습니다."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid ""
"No waste of time in enterprise processes, real responsibilities and autonomy"
msgstr "엔터프라이즈 프로세스를 사용하여 시간 낭비를 최소화하며, 각 직책에 맞는 실질적인 책임과 자율성을 부여합니다."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.snippet_options
msgid "Offices Filter"
msgstr "사무실 필터"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "Optional introduction, or any question you might have about the job…"
msgstr "자기 소개 또는 직무에 대해 추가로 궁금한 점이 있으면 남겨주세요..…"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_filter_by_departments
msgid "Others"
msgstr "기타"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Our Product"
msgstr "제품"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Passion for software products"
msgstr "소프트웨어 제품에 대한 열정"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Perfect written English"
msgstr "높은 수준의 영어 작문 능력"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Perks"
msgstr "기본 혜택"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Personal Evolution"
msgstr "성장 가능성"

#. module: website_hr_recruitment
#. odoo-javascript
#: code:addons/website_hr_recruitment/static/src/js/website_hr_recruitment_editor.js:0
msgid "Phone Number"
msgstr "전화번호"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Play any sport with colleagues, <br/>the bill is covered."
msgstr "동료와 함께 스포츠 활동을 즐길 경우, <br/>모든 비용은 지원됩니다."

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__job_details
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.hr_job_form_inherit
msgid "Process Details"
msgstr "프로세스 세부 사항"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.thankyou_ir_ui_view
msgid "Products"
msgstr "제품"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.hr_job_search_view_inherit
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.hr_job_website_inherit
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.view_hr_job_tree_inherit_website
msgid "Published"
msgstr "게시 됨"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__published_date
msgid "Published Date"
msgstr "게시한 날"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Qualify the customer needs"
msgstr "고객의 니즈 파악"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Real responsibilities and challenges in a fast evolving company"
msgstr "빠르게 발전하는 기업의 사회적 책임과 도전 과제를 제시합니다."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_filter_by_countries
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_filter_by_offices
msgid "Remote"
msgstr "재택 근무"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Responsibilities"
msgstr "주요 업무"

#. module: website_hr_recruitment
#: model:ir.model.fields,help:website_hr_recruitment.field_hr_job__website_id
msgid "Restrict to a specific website."
msgstr "특정 웹사이트로만 제한."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_pages_kanban_view
msgid "SEO Optimized"
msgstr "SEO 최적화"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__is_seo_optimized
msgid "SEO optimized"
msgstr "SEO 최적화"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.snippet_options
msgid "Search Bar"
msgstr "검색창"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__seo_name
msgid "Seo name"
msgstr "Seo 이름"

#. module: website_hr_recruitment
#: model:ir.model.fields,help:website_hr_recruitment.field_hr_job__website_published
msgid "Set if the application is published on the website of the company."
msgstr "회사 웹사이트에 앱을 게시할지 여부를 설정합니다."

#. module: website_hr_recruitment
#. odoo-javascript
#: code:addons/website_hr_recruitment/static/src/js/widgets/copy_link_menuitem.js:0
msgid "Share Job"
msgstr "채용 정보 공유"

#. module: website_hr_recruitment
#. odoo-javascript
#: code:addons/website_hr_recruitment/static/src/js/website_hr_recruitment_editor.js:0
msgid "Short Introduction"
msgstr "간단한 소개"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.snippet_options
msgid "Sidebar"
msgstr "사이드바"

#. module: website_hr_recruitment
#: model:ir.model,name:website_hr_recruitment.model_hr_recruitment_source
msgid "Source of Applicants"
msgstr "지원자 출처"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Sport Activity"
msgstr "스포츠 활동"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Strong analytical skills"
msgstr "정확한 분석 능력"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Technical Expertise"
msgstr "기술 전문성"

#. module: website_hr_recruitment
#: model:ir.model.fields,help:website_hr_recruitment.field_hr_job__website_url
msgid "The full URL to access the document through the website."
msgstr "웹사이트를 통해 문서에 접근 하는 전체 URL입니다."

#. module: website_hr_recruitment
#. odoo-javascript
#: code:addons/website_hr_recruitment/static/src/js/widgets/copy_link_menuitem.js:0
msgid "The job link has been copied to the clipboard."
msgstr "채용 링크가 클립보드에 복사되었습니다."

#. module: website_hr_recruitment
#. odoo-python
#: code:addons/website_hr_recruitment/models/hr_applicant.py:0
msgid "The job offer has been closed."
msgstr "마감된 채용 공고입니다."

#. module: website_hr_recruitment
#. odoo-javascript
#: code:addons/website_hr_recruitment/static/src/js/website_hr_applicant_form.js:0
msgid "The profile that you gave us doesn't seems like a linkedin profile"
msgstr "입력한 값이 링크드인 프로필과 일치하지 않는 것 같습니다."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid ""
"There are currently no open job opportunities,<br class=\"mb-2\"/>\n"
"                                            but feel free to <span class=\"fw-bold\">contact us</span> for a spontaneous application."
msgstr ""
"현재 진행 중인 채용 공고가 없습니다.<br class=\"mb-2\"/>\n"
"                                            수시 채용을 위한 지원서를 제출하시려면 <span class=\"fw-bold\">문의하기</span> 를 통해 남겨주세요."

#. module: website_hr_recruitment
#. odoo-javascript
#: code:addons/website_hr_recruitment/static/src/fields/boolean_toggle_labeled_field.js:0
msgid "ToggleLabeled"
msgstr "ToggleLabeled"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_recruitment_source__url
msgid "Tracker URL"
msgstr "추적 URL"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Trainings"
msgstr "교육"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_filter_by_employment_type
msgid "Unspecified type"
msgstr "지정하지 않은 유형"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Valid work permit for Belgium"
msgstr "벨기에에서 유효한 취업 허가 소지"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__website_published
msgid "Visible on current website"
msgstr "현재 웹 사이트에 공개"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_right_side_bar
msgid ""
"We are a team of passionate people whose goal is to improve everyone's life through disruptive products.\n"
"                                We build great products to solve your business problems."
msgstr ""
"저희는 모든 사람들이 더 나은 삶을 영위할 수 있도록 혁신 제품을 제작하고자 하는 열정을 가지고 구성된 팀입니다.\n"
"                                고객님이 비즈니스를 운영하는 중 겪을 수 있는 문제를 해결할 훌륭한 제품을 만들기 위해 노력하고 있습니다."

#. module: website_hr_recruitment
#. odoo-python
#: code:addons/website_hr_recruitment/controllers/main.py:0
msgid ""
"We found a recent application with a similar name, email, phone number. You "
"can continue if it's not a mistake."
msgstr "이름, 이메일, 전화번호가 유사한 최근 신청서를 발견했습니다. 오류가 아니라면 계속 진행하실 수 있습니다."

#. module: website_hr_recruitment
#. odoo-python
#: code:addons/website_hr_recruitment/controllers/main.py:0
msgid ""
"We've found a previous closed application in our system within the last 6 "
"months. Please consider before applying in order not to duplicate efforts."
msgstr ""
"최근 6개월 이내에 시스템에서 이전에 마감된 지원서를 확인했습니다. 중복 지원을 피하기 위해 지원 전에 신중하게 검토하시기 바랍니다."

#. module: website_hr_recruitment
#: model:ir.model,name:website_hr_recruitment.model_website
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__website_id
msgid "Website"
msgstr "웹사이트"

#. module: website_hr_recruitment
#: model:ir.actions.act_url,name:website_hr_recruitment.action_open_website
msgid "Website Recruitment Form"
msgstr "웹사이트 모집 양식"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__website_url
msgid "Website URL"
msgstr "웹 사이트 URL"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__website_description
msgid "Website description"
msgstr "웹 사이트 설명"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__website_meta_description
msgid "Website meta description"
msgstr "웹사이트 메타 설명"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__website_meta_keywords
msgid "Website meta keywords"
msgstr "웹사이트 메타 핵심어"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__website_meta_title
msgid "Website meta title"
msgstr "웹사이트 메타 제목"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__website_meta_og_img
msgid "Website opengraph image"
msgstr "웹사이트 오픈그래프 이미지"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "What We Offer"
msgstr "다양한 혜택"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "What's great in the job?"
msgstr "해당 직책의 장점은 무엇입니까?"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.thankyou_ir_ui_view
msgid "Your <b>contact</b> information is:"
msgstr "귀하의 <b>연락처</b> 정보는:"

#. module: website_hr_recruitment
#. odoo-javascript
#: code:addons/website_hr_recruitment/static/src/js/website_hr_recruitment_editor.js:0
msgid "Your Email"
msgstr "이메일"

#. module: website_hr_recruitment
#. odoo-javascript
#: code:addons/website_hr_recruitment/static/src/js/website_hr_recruitment_editor.js:0
msgid "Your Name"
msgstr "성명"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.thankyou_ir_ui_view
msgid ""
"Your application has been posted successfully,<br class=\"mb-2\"/>\n"
"                                    We usually respond within 3 days..."
msgstr ""
"지원서가 성공적으로 제출되었습니다.<br class=\"mb-2\"/>\n"
"                                    3일 이내에 답변 드리겠습니다..."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "breadcrumb"
msgstr "사이트 이동 경로"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.view_hr_job_form_inherit_website
msgid ""
"e.g. Summarize the position in one or two lines that will be displayed on "
"the Jobs list page..."
msgstr "예: 채용 공고 목록 페이지에 표시할 직책에 대한 간략한 요약을 한두 줄로 입력합니다."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "e.g. https://www.linkedin.com/in/fpodoo"
msgstr "예: https://www.linkedin.com/in/fpodoo"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_right_side_bar
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__full_url
msgid "job URL"
msgstr "채용 URL"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid "open positions"
msgstr "공개 위치"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid "unpublished"
msgstr "게시 안함"
