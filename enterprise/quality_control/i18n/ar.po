# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* quality_control
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:25+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_form_inherit_quality_control
msgid "% of Operations"
msgstr "نسبة العمليات "

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_form_inherit_quality_control
msgid "% of Transfers"
msgstr "نسبة التحويلات "

#. module: quality_control
#: model:ir.actions.report,print_report_name:quality_control.quality_check_report
#: model:ir.actions.report,print_report_name:quality_control.quality_check_report_internal
msgid "'Worksheet_%s' % object.name"
msgstr "'Worksheet_%s' % object.name"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.worksheet_page
msgid "15.3 cm"
msgstr "15.3 cm"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.worksheet_page
msgid "2023-08-15"
msgstr "2023-08-15"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_team_dashboard_view_kanban
msgid ""
"<i class=\"fa fa-envelope-o\" role=\"img\" aria-label=\"Domain alias\" "
"title=\"Domain alias\"/>&amp;nbsp;"
msgstr ""
"<i class=\"fa fa-envelope-o\" role=\"img\" aria-label=\"Domain alias\" "
"title=\"لقب النطاق \"/>&amp;nbsp;"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_form_inherit_quality_control
msgid ""
"<span class=\"fa fa-2x\" data-icon=\"∑\" style=\"padding-left: 10px;\" "
"role=\"img\" aria-label=\"Statistics\" title=\"Statistics\"/>"
msgstr ""
"<span class=\"fa fa-2x\" data-icon=\"∑\" style=\"padding-left: 10px;\" "
"role=\"img\" aria-label=\"Statistics\" title=\"الإحصائيات \"/>"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.stock_picking_view_form_inherit_quality
msgid "<span class=\"o_stat_text text-danger\">Quality Checks</span>"
msgstr "<span class=\"o_stat_text text-danger\">فحوصات الجودة</span> "

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.stock_picking_view_form_inherit_quality
msgid "<span class=\"o_stat_text text-success\">Quality Checks</span>"
msgstr "<span class=\"o_stat_text text-success\">فحوصات الجودة</span> "

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_form_inherit_quality_control
msgid ""
"<span class=\"o_stat_text\">AVG:</span>\n"
"                        <span class=\"o_stat_text\">STD:</span>"
msgstr ""
"<span class=\"o_stat_text\">AVG:</span>\n"
"                        <span class=\"o_stat_text\">STD:</span>"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.product_product_form_view_quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.product_template_form_view_quality_control
msgid ""
"<span class=\"o_stat_text\">Pass:</span>\n"
"                        <span class=\"o_stat_text\">Fail:</span>"
msgstr ""
"<span class=\"o_stat_text\">النجاح:</span>\n"
"                        <span class=\"o_stat_text\">الفشل:</span> "

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.stock_picking_view_form_inherit_quality
msgid "<span class=\"o_stat_text\">Quality Alert</span>"
msgstr "<span class=\"o_stat_text\">تنبيه الجودة</span> "

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.stock_production_lot_view_form
msgid "<span class=\"o_stat_text\">Quality Alerts:</span>"
msgstr "<span class=\"o_stat_text\">تنبيهات الجودة:</span> "

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_view_form
msgid "<span class=\"o_stat_text\">Quality Check</span>"
msgstr "<span class=\"o_stat_text\">فحص الجودة </span>"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.stock_picking_view_form_inherit_quality
msgid "<span class=\"o_stat_text\">Quality Checks</span>"
msgstr "<span class=\"o_stat_text\">فحوصات الجودة</span> "

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_form_inherit_quality_control
msgid ""
"<span invisible=\"measure_frequency_type in ('all', 'on_demand')\">Every "
"</span>"
msgstr ""
"<span invisible=\"measure_frequency_type in ('all', 'on_demand')\">كل "
"</span> "

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_form_inherit_quality_control
msgid "<span>% of products</span>"
msgstr "<span>% من المنتجات</span> "

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_form_inherit_quality_control
msgid "<span>Test </span>"
msgstr "<span>اختبار </span>"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_form_inherit_quality_control
msgid "<span>from </span>"
msgstr "<span>من </span>"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_form_inherit_quality_control
msgid "<span>to </span>"
msgstr "<span>إلى </span>"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.worksheet_page
msgid "<strong>Lot/Serial Number: </strong>"
msgstr "<strong>رقم المجموعة/الرقم التسلسلي: </strong> "

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.worksheet_page
msgid "<strong>Measure: </strong>"
msgstr "<strong>قياس: </strong> "

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.worksheet_page
msgid "<strong>Notes: </strong>"
msgstr "<strong>ملاحظات: </strong> "

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.worksheet_page
msgid "<strong>Product: </strong>"
msgstr "<strong>المنتج: </strong>"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.worksheet_page
msgid "<strong>Test Type: </strong>"
msgstr "<strong>نوع الاختبار: </strong>"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.worksheet_page
msgid "<strong>Tested by: </strong>"
msgstr "<strong>تم الاختبار بواسطة: </strong>"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.worksheet_page
msgid "<strong>Tested on: </strong>"
msgstr "<strong>تم الاختبار في: </strong>"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.worksheet_page
msgid "<strong>Transfer: </strong>"
msgstr "<strong>نقل: </strong> "

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.worksheet_page
msgid "<strong>Warning: </strong>"
msgstr "<strong>تحذير: </strong> "

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_team_view_form
msgid "Accept Emails From"
msgstr "قبول رسائل البريد من"

#. module: quality_control
#: model_terms:ir.actions.act_window,help:quality_control.quality_tag_action
msgid "Add a new tag"
msgstr "إضافة علامة تصنيف جديدة "

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__additional_note
msgid "Additional Note"
msgstr "ملاحظة إضافية "

#. module: quality_control
#: model:ir.model.fields,help:quality_control.field_quality_check_wizard__additional_note
msgid "Additional remarks concerning this check."
msgstr "ملاحظات إضافية تتعلق بهذا الفحص. "

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_stock_picking__quality_alert_ids
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_form
msgid "Alerts"
msgstr "التنبيهات"

#. module: quality_control
#: model:ir.model.fields.selection,name:quality_control.selection__quality_point__measure_frequency_type__all
msgid "All"
msgstr "الكل"

#. module: quality_control
#: model:ir.model.fields.selection,name:quality_control.selection__stock_move_line__check_state__pass
msgid "All checks passed"
msgstr "تم تخطي كافة الفحوصات "

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.worksheet_page
msgid "All parameters passed."
msgstr "تم تخطي كافة المعايير. "

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_on_demand__allowed_product_ids
msgid "Allowed Product"
msgstr "المنتج المسموح به "

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_on_demand__allowed_quality_point_ids
msgid "Allowed Quality Point"
msgstr "نقطة مراقبة الجودة المسموح بها "

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_search
msgid "Archived"
msgstr "مؤرشف"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_point__average
msgid "Average"
msgstr "متوسط"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_wizard_form_failure
msgid "Back"
msgstr "العودة"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__check_ids
msgid "Check"
msgstr "فحص "

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_stock_move_line__check_state
msgid "Check State"
msgstr "حالة التحقق "

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_tree
msgid "Checked By"
msgstr "تم فحصه بواسطة"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_search
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_tree
msgid "Checked Date"
msgstr "تاريخ الفحص"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_stock_move_line__check_ids
#: model:ir.model.fields,field_description:quality_control.field_stock_picking__check_ids
msgid "Checks"
msgstr "الفحوصات "

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_team_dashboard_view_kanban
msgid "Checks In Progress"
msgstr "الفحوصات الجارية"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_spreadsheet__company_id
#: model:ir.model.fields,field_description:quality_control.field_quality_spreadsheet_template__company_id
msgid "Company"
msgstr "الشركة "

#. module: quality_control
#: model:ir.ui.menu,name:quality_control.menu_quality_configuration
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_team_dashboard_view_kanban
msgid "Configuration"
msgstr "التهيئة "

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_on_demand_view_form
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_wizard_form_failure
msgid "Confirm"
msgstr "تأكيد"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_wizard_form_failure
msgid "Confirm Measure"
msgstr "تأكيد المقياس"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_point__measure_frequency_type
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_form_inherit_quality_control
msgid "Control Frequency"
msgstr "معدل التحكم"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_form
msgid "Control Person"
msgstr "مسؤول التحكم "

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_search
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_search
msgid "Control Point"
msgstr "نقطة التحكم"

#. module: quality_control
#: model:ir.ui.menu,name:quality_control.menu_quality_control_points
msgid "Control Points"
msgstr "نقاط التحكم"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__measure_on
#: model:ir.model.fields,field_description:quality_control.field_quality_check_on_demand__measure_on
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__measure_on
#: model:ir.model.fields,field_description:quality_control.field_quality_point__measure_on
msgid "Control per"
msgstr "التحكم حسب "

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.view_quality_point_kanban
msgid "Control per:"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_wizard_form_failure
msgid "Correct Measure"
msgstr "المقياس الصحيح"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_view_form
msgid "Corrective Actions"
msgstr "إجراءات تصحيحية"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_form
msgid "Create Alert"
msgstr "إنشاء تنبيه "

#. module: quality_control
#: model_terms:ir.actions.act_window,help:quality_control.quality_alert_action_check
msgid "Create a new quality alert"
msgstr "إنشاء تنبيه جودة جديد"

#. module: quality_control
#: model_terms:ir.actions.act_window,help:quality_control.quality_alert_stage_action
msgid "Create a new quality alert stage"
msgstr "إنشاء مرحلة تنبيه جودة جديدة"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_on_demand__create_uid
#: model:ir.model.fields,field_description:quality_control.field_quality_check_spreadsheet__create_uid
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__create_uid
#: model:ir.model.fields,field_description:quality_control.field_quality_spreadsheet_template__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_on_demand__create_date
#: model:ir.model.fields,field_description:quality_control.field_quality_check_spreadsheet__create_date
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__create_date
#: model:ir.model.fields,field_description:quality_control.field_quality_spreadsheet_template__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__current_check_id
msgid "Current Check"
msgstr "الفحص الحالي "

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_spreadsheet__current_revision_uuid
#: model:ir.model.fields,field_description:quality_control.field_quality_spreadsheet_template__current_revision_uuid
msgid "Current Revision Uuid"
msgstr "المعرّف الفريد عالمياً للمراجعة الحالية "

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_spreadsheet_template_view_list
msgid "Data"
msgstr "البيانات "

#. module: quality_control
#: model:ir.model.fields.selection,name:quality_control.selection__quality_point__measure_frequency_unit__day
msgid "Days"
msgstr "الأيام"

#. module: quality_control
#: model:ir.model.fields,help:quality_control.field_quality_check__uom_id
#: model:ir.model.fields,help:quality_control.field_quality_check_wizard__uom_id
msgid "Default unit of measure used for all stock operations."
msgstr "وحدة القياس الافتراضية المستخدمة لكافة عمليات المخزون. "

#. module: quality_control
#: model_terms:ir.actions.act_window,help:quality_control.quality_check_action_main
#: model_terms:ir.actions.act_window,help:quality_control.quality_check_action_team
msgid ""
"Define Quality Control Points in order to automatically generate\n"
"              quality checks at the right logistic operation: transfers, manufacturing orders."
msgstr ""
"قم بتحديد نقاط فحص الجودة حتى تتمكن من إنشاء\n"
"              فحوصات الجودة تلقائياً في العملية اللوجستية الصحيحة: التحويلات، أوامر التصنيع."

#. module: quality_control
#: model:ir.model.fields,help:quality_control.field_quality_check__testing_percentage_within_lot
#: model:ir.model.fields,help:quality_control.field_quality_check_wizard__testing_percentage_within_lot
#: model:ir.model.fields,help:quality_control.field_quality_point__testing_percentage_within_lot
msgid "Defines the percentage within a lot that should be tested"
msgstr "يقوم بتحديد النسبة ضمن الدفعة التي يجب فحصها "

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_view_form
msgid "Describe the corrective actions you did..."
msgstr "صف الإجراءات التصحيحية التي اتخذتها..."

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_view_form
msgid "Describe the preventive actions you did..."
msgstr "صف الإجراءات الوقائية التي اتخذتها..."

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_view_form
msgid "Description"
msgstr "الوصف"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_view_form
msgid "Description of the issue..."
msgstr "وصف المشكلة..."

#. module: quality_control
#: model:ir.model.fields,help:quality_control.field_quality_check__is_lot_tested_fractionally
#: model:ir.model.fields,help:quality_control.field_quality_check_wizard__is_lot_tested_fractionally
#: model:ir.model.fields,help:quality_control.field_quality_point__is_lot_tested_fractionally
msgid "Determines if only a fraction of the lot should be tested"
msgstr "يقوم بتحديد ما إذا كان يجب فحص جزء صغير من الدفعة "

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.view_quality_check_wizard
msgid "Discard"
msgstr "إهمال "

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_on_demand__display_name
#: model:ir.model.fields,field_description:quality_control.field_quality_check_spreadsheet__display_name
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__display_name
#: model:ir.model.fields,field_description:quality_control.field_quality_spreadsheet_template__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_spreadsheet_template_view_list
msgid "Edit"
msgstr "تحرير"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_team_view_form
msgid "Email Alias"
msgstr "لقب البريد الإلكتروني"

#. module: quality_control
#: model:ir.model.fields,help:quality_control.field_quality_check__product_tracking
#: model:ir.model.fields,help:quality_control.field_quality_check_wizard__product_tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr "تأكد من إمكانية تتبع المنتج القابل للتخزين في مستودعك. "

#. module: quality_control
#: model:ir.model.fields.selection,name:quality_control.selection__quality_check__measure_success__fail
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_form
#: model_terms:ir.ui.view,arch_db:quality_control.view_quality_check_wizard
msgid "Fail"
msgstr "فشل"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_search
msgid "Failed"
msgstr "فشل"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_form
msgid "Failed Quantity"
msgstr "الكمية التي فشلت "

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__failure_location_id
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__failure_location_id
msgid "Failure Location"
msgstr "موقع الفشل "

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__potential_failure_location_ids
#: model:ir.model.fields,field_description:quality_control.field_quality_point__failure_location_ids
msgid "Failure Locations"
msgstr "مواقع الفشل "

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__failure_message
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__failure_message
#: model:ir.model.fields,field_description:quality_control.field_quality_point__failure_message
msgid "Failure Message"
msgstr "رسالة الفشل"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_form_inherit_quality_control
msgid "Frequency"
msgstr "معدل الحدوث "

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_point__measure_frequency_unit_value
msgid "Frequency Unit Value"
msgstr "قيمة وحدة معدل الحدوث"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_search
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_search
msgid "Group By"
msgstr "تجميع حسب"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_on_demand__id
#: model:ir.model.fields,field_description:quality_control.field_quality_check_spreadsheet__id
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__id
#: model:ir.model.fields,field_description:quality_control.field_quality_spreadsheet_template__id
msgid "ID"
msgstr "المُعرف"

#. module: quality_control
#: model:ir.model.fields,help:quality_control.field_quality_check_wizard__potential_failure_location_ids
#: model:ir.model.fields,help:quality_control.field_quality_point__failure_location_ids
msgid ""
"If a quality check fails, a location is chosen from this list for each "
"failed quantity."
msgstr "إذا فشل فحص الجودة، فسيتم اختيار موقع من هذه القائمة لكل كمية فاشلة. "

#. module: quality_control
#: model:ir.model.fields,help:quality_control.field_quality_check_on_demand__lot_id
msgid ""
"If you want to specify a lot/serial number before the transfer validation,"
"                                create a new lot here from this field with "
"the same exact lot name of the move line you want to add a check for."
"                                If you want to create a check for all move "
"lines, leave this field empty."
msgstr ""
"إذا أردت تحديد رقم مجموعة/رقم تسلسلي قبل تصديق الشحنة،"
"                                                         فقم بإنشاء مجموعة "
"جديدة هنا من هذا الحقل باستخدام نفس اسم مجموعة بند الحركة التي ترغب في إضافة"
" فحص جودة لها.                                إذا أردت إضافة فحص جودة لكافة "
"بنود الحركة، اترك هذا الحقل فارغاً. "

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_search
msgid "In Progress"
msgstr "قيد التنفيذ"

#. module: quality_control
#: model:ir.model.fields,help:quality_control.field_quality_check__move_line_id
msgid ""
"In case of Quality Check by Quantity, Move Line on which the Quality Check "
"applies"
msgstr ""
"في حال فحص الجودة حسب الكمية، يكون بند الحركة الذي ينطبق عليه فحص الجودة "

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_form
msgid "Instructions"
msgstr "التعليمات"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__is_last_check
msgid "Is Last Check"
msgstr "آخر فحص "

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.worksheet_page
msgid "John Doe"
msgstr "جون دو "

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.worksheet_page
msgid "LT-00045"
msgstr "LT-00045"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.worksheet_page
msgid "Laptop XYZ"
msgstr "لابتوب XYZ "

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_on_demand__write_uid
#: model:ir.model.fields,field_description:quality_control.field_quality_check_spreadsheet__write_uid
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__write_uid
#: model:ir.model.fields,field_description:quality_control.field_quality_spreadsheet_template__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_on_demand__write_date
#: model:ir.model.fields,field_description:quality_control.field_quality_check_spreadsheet__write_date
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__write_date
#: model:ir.model.fields,field_description:quality_control.field_quality_spreadsheet_template__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__lot_line_id
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__lot_line_id
msgid "Lot Line"
msgstr "بند الدفعة "

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__is_lot_tested_fractionally
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__is_lot_tested_fractionally
#: model:ir.model.fields,field_description:quality_control.field_quality_point__is_lot_tested_fractionally
msgid "Lot Tested Fractionally"
msgstr "تم اختبار المجموعة جزئياً "

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_wizard_form_failure
msgid "Lot/SN"
msgstr "رقم الدفعة/الرقم التسلسلي "

#. module: quality_control
#: model:ir.model,name:quality_control.model_stock_lot
msgid "Lot/Serial"
msgstr "رقم الدفعة/الرقم التسلسلي "

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_on_demand__lot_id
msgid "Lot/Serial Number"
msgstr "رقم المجموعة/الرقم التسلسلي "

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__lot_name
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__lot_name
msgid "Lot/Serial Number Name"
msgstr "اسم الدفعة/الرقم التسلسلي "

#. module: quality_control
#: model:ir.ui.menu,name:quality_control.quality_control_menu_traceability
msgid "Lots/Serial Numbers"
msgstr "أرقام الدفعات/الأرقام التسلسلية "

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__tolerance_max
#: model:ir.model.fields,field_description:quality_control.field_quality_point__tolerance_max
msgid "Max Tolerance"
msgstr "الحد الأقصى المسموح به"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__measure
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__measure
#: model:quality.point.test_type,name:quality_control.test_type_measure
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_form
msgid "Measure"
msgstr "المقياس"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_point__measure_frequency_unit
msgid "Measure Frequency Unit"
msgstr "قياس وحدة التواتر "

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__measure_success
msgid "Measure Success"
msgstr "قياس النجاح"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_form_inherit_quality_control
msgid "Message If Failure"
msgstr "الرسالة في حال الفشل "

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__tolerance_min
#: model:ir.model.fields,field_description:quality_control.field_quality_point__tolerance_min
msgid "Min Tolerance"
msgstr "الحد الأدنى المسموح به "

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_view_form
msgid "Miscellaneous"
msgstr "متفرقات "

#. module: quality_control
#: model:ir.model.fields.selection,name:quality_control.selection__quality_point__measure_frequency_unit__month
msgid "Months"
msgstr "شهور"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_spreadsheet__name
#: model:ir.model.fields,field_description:quality_control.field_quality_spreadsheet_template__name
msgid "Name"
msgstr "الاسم"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__nb_checks
msgid "Nb Checks"
msgstr "عدد الفحوصات "

#. module: quality_control
#. odoo-python
#: code:addons/quality_control/models/quality.py:0
msgid "New"
msgstr "جديد"

#. module: quality_control
#. odoo-javascript
#: code:addons/quality_control/static/src/spreadsheet_bundle/quality_spreadsheet_template_action/quality_spreadsheet_template_action.js:0
msgid "New quality spreadsheet template created"
msgstr "تم إنشاء قالب جدول بيانات جديد لضبط الجودة "

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.view_quality_check_wizard
msgid "Next"
msgstr "التالي"

#. module: quality_control
#: model:ir.model.fields.selection,name:quality_control.selection__stock_move_line__check_state__no_checks
msgid "No checks"
msgstr "لا توجد فحوصات "

#. module: quality_control
#: model_terms:ir.actions.act_window,help:quality_control.quality_check_action_spc
msgid "No data yet!"
msgstr "لا توجد أي بيانات بعد! "

#. module: quality_control
#: model:ir.model.fields.selection,name:quality_control.selection__quality_check__measure_success__none
msgid "No measure"
msgstr "لا يوجد مقياس"

#. module: quality_control
#: model_terms:ir.actions.act_window,help:quality_control.quality_alert_action_report
msgid "No quality alert"
msgstr "لا يوجد تنبيه جودة"

#. module: quality_control
#: model_terms:ir.actions.act_window,help:quality_control.quality_check_action_main
#: model_terms:ir.actions.act_window,help:quality_control.quality_check_action_team
msgid "No quality check found"
msgstr "لم يتم العثور على فحص للجودة "

#. module: quality_control
#: model_terms:ir.actions.act_window,help:quality_control.quality_check_action_report
msgid "No quality checks"
msgstr "لا توجد فحوصات للجودة "

#. module: quality_control
#: model_terms:ir.actions.act_window,help:quality_control.quality_point_action
msgid "No quality control point found"
msgstr "لم يتم العثور على نقطة لمراقبة الجودة "

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_point__norm
msgid "Norm"
msgstr "المعياري"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__norm_unit
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__norm_unit
#: model:ir.model.fields,field_description:quality_control.field_quality_point__norm_unit
msgid "Norm Unit"
msgstr "الوحدة المعيارية"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__note
msgid "Note"
msgstr "الملاحظات"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_form
msgid "Notes"
msgstr "الملاحظات"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_wizard_form_failure
msgid "OK"
msgstr "موافق"

#. module: quality_control
#. odoo-python
#: code:addons/quality_control/models/stock_picking.py:0
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_on_demand_view_form
msgid "On-Demand Quality Check"
msgstr "فحص الجودة عند الطلب "

#. module: quality_control
#: model:ir.model.fields.selection,name:quality_control.selection__quality_point__measure_frequency_type__on_demand
msgid "On-demand"
msgstr "عند الطلب "

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.view_quality_check_wizard
msgid "Open spreadsheet"
msgstr "فتح جدول البيانات "

#. module: quality_control
#: model:ir.model.fields.selection,name:quality_control.selection__quality_check__measure_on__operation
#: model:ir.model.fields.selection,name:quality_control.selection__quality_point__measure_on__operation
msgid "Operation"
msgstr "العملية"

#. module: quality_control
#: model:ir.model.fields,help:quality_control.field_quality_check__measure_on
#: model:ir.model.fields,help:quality_control.field_quality_check_on_demand__measure_on
#: model:ir.model.fields,help:quality_control.field_quality_check_wizard__measure_on
#: model:ir.model.fields,help:quality_control.field_quality_point__measure_on
msgid ""
"Operation = One quality check is requested at the operation level.\n"
"                  Product = A quality check is requested per product.\n"
"                 Quantity = A quality check is requested for each new product quantity registered, with partial quantity checks also possible."
msgstr ""
"العملية = يتم طلب فحص جودة واحد على مستوى العملية.\n"
"                  المنتج = يتم طلب فحص جودة لكل منتج.\n"
"                 الكمية = يتم طلب فحص جودة لكل كمية منتج جديدة مسجلة، مع فحص الجودة الجزئي. "

#. module: quality_control
#. odoo-python
#: code:addons/quality_control/models/stock_picking.py:0
msgid "Operation not supported"
msgstr "العملية غير مدعومة "

#. module: quality_control
#: model:ir.ui.menu,name:quality_control.menu_quality_dashboard
msgid "Overview"
msgstr "نظرة عامة"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_form_inherit_quality_control
msgid "Partial Test"
msgstr "اختبار جزئي "

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_form
msgid "Partner"
msgstr "الشريك"

#. module: quality_control
#: model:ir.model.fields.selection,name:quality_control.selection__quality_check__measure_success__pass
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_form
#: model_terms:ir.ui.view,arch_db:quality_control.view_quality_check_wizard
msgid "Pass"
msgstr "نجاح"

#. module: quality_control
#: model:quality.point.test_type,name:quality_control.test_type_passfail
msgid "Pass - Fail"
msgstr "نجاح - فشل "

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_search
#: model_terms:ir.ui.view,arch_db:quality_control.worksheet_page
msgid "Passed"
msgstr "نجح"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_stock_picking__quality_check_todo
msgid "Pending checks"
msgstr "الفحوصات المُعلقة "

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_point__measure_frequency_value
msgid "Percentage"
msgstr "النسبة"

#. module: quality_control
#: model:ir.model.fields.selection,name:quality_control.selection__quality_point__measure_frequency_type__periodical
msgid "Periodically"
msgstr "دوريًا"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_on_demand__picking_id
msgid "Picking"
msgstr "الانتقاء "

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__picture
msgid "Picture"
msgstr "صورة"

#. module: quality_control
#. odoo-python
#: code:addons/quality_control/models/quality.py:0
msgid "Picture Uploaded"
msgstr "تم رفع الصورة"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__position_current_check
msgid "Position Current Check"
msgstr "وضع الفحص الحالي "

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_view_form
msgid "Preventive Actions"
msgstr "إجراءات وقائية"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.view_quality_check_wizard
msgid "Previous"
msgstr "السابق"

#. module: quality_control
#: model:ir.model,name:quality_control.model_product_template
#: model:ir.model.fields,field_description:quality_control.field_quality_check_on_demand__product_id
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__product_id
#: model:ir.model.fields.selection,name:quality_control.selection__quality_check__measure_on__product
#: model:ir.model.fields.selection,name:quality_control.selection__quality_point__measure_on__product
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_search
msgid "Product"
msgstr "المنتج"

#. module: quality_control
#: model:ir.model,name:quality_control.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "تحركات المنتج (بنود حركة المخزون)"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__uom_id
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__uom_id
msgid "Product Unit of Measure"
msgstr "وحدة قياس المنتج"

#. module: quality_control
#: model:ir.model,name:quality_control.model_product_product
msgid "Product Variant"
msgstr "متغير المنتج "

#. module: quality_control
#: model:ir.ui.menu,name:quality_control.quality_control_menu_product_variant
msgid "Product Variants"
msgstr "متغيرات المنتج "

#. module: quality_control
#: model:ir.ui.menu,name:quality_control.quality_control_menu_product
#: model:ir.ui.menu,name:quality_control.quality_product_menu_product_template
msgid "Products"
msgstr "المنتجات"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.worksheet_page
msgid "QC-00123"
msgstr "QC-00123"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__qty_failed
msgid "Qty Failed"
msgstr "Qty Failed"

#. module: quality_control
#: model:ir.ui.menu,name:quality_control.menu_quality_root
msgid "Quality"
msgstr "الجودة "

#. module: quality_control
#. odoo-python
#: code:addons/quality_control/models/quality.py:0
#: model:ir.actions.server,name:quality_control.stock_picking_action_quality_alert
#: model:ir.model,name:quality_control.model_quality_alert
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_view_calendar
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_view_search_inherit_quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.stock_picking_view_form_inherit_quality
msgid "Quality Alert"
msgstr "تنبيه الجودة"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_view_graph
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_view_pivot
msgid "Quality Alert Analysis"
msgstr "تحليل تنبيه الجودة"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_stock_picking__quality_alert_count
msgid "Quality Alert Count"
msgstr "عدد تنبيهات الجودة"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_stock_lot__quality_alert_qty
msgid "Quality Alert Qty"
msgstr "كمية تنبيه الجودة "

#. module: quality_control
#: model:ir.actions.act_window,name:quality_control.quality_alert_stage_action
#: model:ir.ui.menu,name:quality_control.menu_quality_config_alert_stage
msgid "Quality Alert Stages"
msgstr "مراحل تنبيه الجودة"

#. module: quality_control
#: model_terms:ir.actions.act_window,help:quality_control.quality_alert_stage_action
msgid ""
"Quality Alert stages define the different steps a quality alert should go "
"through."
msgstr "تحدد مراحل تنبيه الجودة الخطوات المختلفة التي سيتبعها تنبيه الجودة."

#. module: quality_control
#: model:ir.actions.act_window,name:quality_control.quality_alert_action_check
#: model:ir.actions.act_window,name:quality_control.quality_alert_action_report
#: model:ir.actions.act_window,name:quality_control.quality_alert_action_team
#: model:ir.ui.menu,name:quality_control.menu_quality_alert
#: model:ir.ui.menu,name:quality_control.menu_quality_alert_report
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_team_dashboard_view_kanban
msgid "Quality Alerts"
msgstr "تنبيهات الجودة"

#. module: quality_control
#. odoo-python
#: code:addons/quality_control/models/quality.py:0
#: model:ir.actions.act_window,name:quality_control.action_quality_check_wizard
#: model:ir.actions.server,name:quality_control.stock_picking_action_quality_check_on_demand
#: model:ir.model,name:quality_control.model_quality_check
#: model_terms:ir.ui.view,arch_db:quality_control.view_stock_move_line_detailed_operation_tree_inherit_quality
msgid "Quality Check"
msgstr "فحص الجودة"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_graph
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_pivot
msgid "Quality Check Analysis"
msgstr "تحليل فحص الجودة"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_stock_picking__quality_check_fail
msgid "Quality Check Fail"
msgstr "فشل فحص الجودة"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_wizard_form_failure
msgid "Quality Check Failed"
msgstr "فشل فحص الجودة"

#. module: quality_control
#. odoo-python
#: code:addons/quality_control/wizard/quality_check_wizard.py:0
msgid "Quality Check Failed for %(product_name)s"
msgstr "فشل فحص الجودة لـ %(product_name)s "

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.worksheet_page
msgid "Quality Check Picture"
msgstr "صورة فحص الجودة "

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_on_demand__quality_point_id
msgid "Quality Check Point"
msgstr "نقطة فحص الجودة "

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_stock_lot__quality_check_qty
msgid "Quality Check Qty"
msgstr "كمية فحص الجودة "

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.worksheet_page
msgid "Quality Check:"
msgstr "فحص الجودة: "

#. module: quality_control
#: model:ir.actions.act_window,name:quality_control.quality_check_action_main
#: model:ir.actions.act_window,name:quality_control.quality_check_action_picking
#: model:ir.actions.act_window,name:quality_control.quality_check_action_production_lot
#: model:ir.actions.act_window,name:quality_control.quality_check_action_report
#: model:ir.actions.act_window,name:quality_control.quality_check_action_team
#: model:ir.ui.menu,name:quality_control.menu_quality_check_report
#: model:ir.ui.menu,name:quality_control.menu_quality_checks
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_form_inherit_quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.stock_picking_view_form_inherit_quality
#: model_terms:ir.ui.view,arch_db:quality_control.stock_picking_view_search_inherit_quality
#: model_terms:ir.ui.view,arch_db:quality_control.stock_production_lot_form_quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.view_quality_check_wizard
msgid "Quality Checks"
msgstr "فحوصات الجودة"

#. module: quality_control
#: model:ir.actions.act_window,name:quality_control.quality_check_action_spc
msgid "Quality Checks SPC"
msgstr "مراقبة العمليات الإحصائية لفحوصات الجودة "

#. module: quality_control
#: model:ir.ui.menu,name:quality_control.menu_quality_control
msgid "Quality Control"
msgstr "مراقبة الجودة"

#. module: quality_control
#: model:ir.model,name:quality_control.model_quality_point
msgid "Quality Control Point"
msgstr "نقطة مراقبة الجودة"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_product_product__quality_control_point_qty
#: model:ir.model.fields,field_description:quality_control.field_product_template__quality_control_point_qty
msgid "Quality Control Point Qty"
msgstr "كمية نقطة مراقبة الجودة "

#. module: quality_control
#: model:ir.actions.act_window,name:quality_control.quality_point_action
msgid "Quality Control Points"
msgstr "نقاط مراقبة الجودة"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_product_product__quality_fail_qty
#: model:ir.model.fields,field_description:quality_control.field_product_template__quality_fail_qty
msgid "Quality Fail Qty"
msgstr "كمية فشل فحص الجودة "

#. module: quality_control
#: model:ir.actions.act_window,name:quality_control.quality_alert_team_action
msgid "Quality Overview"
msgstr "نظرة عامة على الجودة"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_product_product__quality_pass_qty
#: model:ir.model.fields,field_description:quality_control.field_product_template__quality_pass_qty
msgid "Quality Pass Qty"
msgstr "كمية النجاح في فحص الجودة "

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.product_product_form_view_quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.product_template_form_view_quality_control
msgid "Quality Points"
msgstr "نقاط الجودة "

#. module: quality_control
#: model:ir.actions.act_window,name:quality_control.quality_spreadsheet_template_action_config
#: model:ir.ui.menu,name:quality_control.menu_config_quality_spreadsheet_template
msgid "Quality Spreadsheet Templates"
msgstr "قوالب جداول بيانات الجودة "

#. module: quality_control
#: model:ir.actions.act_window,name:quality_control.quality_tag_action
#: model:ir.ui.menu,name:quality_control.menu_config_quality_tags
msgid "Quality Tags"
msgstr "علامات تصنيف الجودة "

#. module: quality_control
#: model:ir.actions.act_window,name:quality_control.quality_alert_team_action_config
#: model:ir.ui.menu,name:quality_control.menu_quality_config_alert_team
msgid "Quality Teams"
msgstr "فِرَق الجودة "

#. module: quality_control
#: model_terms:ir.actions.act_window,help:quality_control.quality_alert_team_action
msgid ""
"Quality Teams group the different quality alerts/checks\n"
"              according to the roles (teams) that need them."
msgstr ""
"تقوم فرق الجودة بتجميع تنبيهات وفحوصات الجودة المختلفة\n"
"              حسب الأدوار (الفرق) التي تحتاجها."

#. module: quality_control
#: model:ir.model,name:quality_control.model_report_quality_control_quality_worksheet_internal
msgid "Quality Worksheet Internal Report"
msgstr "التقرير الداخلي لورقة عمل الجودة "

#. module: quality_control
#: model:ir.model,name:quality_control.model_report_quality_control_quality_worksheet
msgid "Quality Worksheet Report"
msgstr "تقرير ورقة عمل الجودة "

#. module: quality_control
#: model:ir.model,name:quality_control.model_quality_check_spreadsheet
msgid "Quality check spreadsheet"
msgstr "جداول بيانات فحص الجودة "

#. module: quality_control
#: model:ir.model,name:quality_control.model_quality_spreadsheet_template
msgid "Quality check template spreadsheet"
msgstr "قالب جداول بيانات فحص الجودة "

#. module: quality_control
#: model_terms:ir.actions.act_window,help:quality_control.quality_point_action
msgid ""
"Quality control points define the quality checks which should be\n"
"              performed at each operation, for your different products."
msgstr ""
"تحدد نقاط فحص الجودة فحوصات الجودة التي يجب\n"
"              أداؤها في كل عملية، لمنتجاتك المختلفة."

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__qty_line
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__qty_line
#: model:ir.model.fields.selection,name:quality_control.selection__quality_check__measure_on__move_line
#: model:ir.model.fields.selection,name:quality_control.selection__quality_point__measure_on__move_line
msgid "Quantity"
msgstr "الكمية"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__qty_failed
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_wizard_form_failure
msgid "Quantity Failed"
msgstr "الكمية التي فشلت "

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__qty_passed
msgid "Quantity Passed"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__qty_tested
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__qty_tested
msgid "Quantity Tested"
msgstr "الكمية التي تم اختبارها "

#. module: quality_control
#: model:ir.model.fields,help:quality_control.field_quality_check__qty_tested
#: model:ir.model.fields,help:quality_control.field_quality_check_wizard__qty_tested
msgid "Quantity of product tested within the lot"
msgstr "كمية المنتجات التي تم اختبارها ضمن الدفعة "

#. module: quality_control
#: model:ir.model.fields,help:quality_control.field_quality_check__qty_failed
msgid "Quantity of product that failed the quality check"
msgstr "كمية المنتج التي فشلت في اختبار الجودة "

#. module: quality_control
#: model:ir.model.fields,help:quality_control.field_quality_check__qty_passed
msgid "Quantity of product that passed the quality check"
msgstr "كمية المنتج التي اجتازت اختبار الجودة "

#. module: quality_control
#: model:ir.model.fields,help:quality_control.field_quality_check__qty_to_test
#: model:ir.model.fields,help:quality_control.field_quality_check_wizard__qty_to_test
msgid "Quantity of product to test within the lot"
msgstr "كمية المنتجات لاختبارها ضمن الدفعة "

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__qty_to_test
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__qty_to_test
msgid "Quantity to Test"
msgstr "الكمية لاختبارها "

#. module: quality_control
#: model:ir.model.fields.selection,name:quality_control.selection__quality_point__measure_frequency_type__random
msgid "Randomly"
msgstr "عشوائياً "

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__name
msgid "Reference"
msgstr "الرقم المرجعي "

#. module: quality_control
#: model:ir.ui.menu,name:quality_control.menu_quality_reporting
msgid "Reporting"
msgstr "إعداد التقارير "

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_search
msgid "Responsible"
msgstr "المسؤول "

#. module: quality_control
#. odoo-javascript
#: code:addons/quality_control/static/src/spreadsheet_bundle/quality_check_spreadsheet_action/quality_check_spreadsheet_action.xml:0
msgid "Save in"
msgstr "حفظ في "

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_on_demand__show_lot_number
msgid "Show Lot Number"
msgstr "إظهار رقم المجموعة "

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__show_lot_text
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__show_lot_text
msgid "Show Lot Text"
msgstr "إظهار نَص الدفعة "

#. module: quality_control
#: model:ir.model.fields.selection,name:quality_control.selection__stock_move_line__check_state__fail
msgid "Some checks failed"
msgstr "لقد فشلت بعض الفحوصات "

#. module: quality_control
#: model:ir.model.fields.selection,name:quality_control.selection__stock_move_line__check_state__in_progress
msgid "Some checks to be done"
msgstr "بعض الفحوصات لإجرائها "

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__spreadsheet_id
#: model:quality.point.test_type,name:quality_control.test_type_spreadsheet
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_form
msgid "Spreadsheet"
msgstr "جدول البيانات "

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_spreadsheet__spreadsheet_data
#: model:ir.model.fields,field_description:quality_control.field_quality_spreadsheet_template__spreadsheet_data
msgid "Spreadsheet Data"
msgstr "بيانات جداول البيانات "

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_spreadsheet__spreadsheet_file_name
#: model:ir.model.fields,field_description:quality_control.field_quality_spreadsheet_template__spreadsheet_file_name
msgid "Spreadsheet File Name"
msgstr "اسم ملف جدول البيانات "

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_spreadsheet__spreadsheet_revision_ids
#: model:ir.model.fields,field_description:quality_control.field_quality_spreadsheet_template__spreadsheet_revision_ids
msgid "Spreadsheet Revision"
msgstr "مراجعة جدول البيانات "

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_spreadsheet__spreadsheet_snapshot
#: model:ir.model.fields,field_description:quality_control.field_quality_spreadsheet_template__spreadsheet_snapshot
msgid "Spreadsheet Snapshot"
msgstr "لقطة جدول البيانات "

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_point__spreadsheet_template_id
msgid "Spreadsheet Template"
msgstr "قالب جدول البيانات "

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_spreadsheet__spreadsheet_binary_data
#: model:ir.model.fields,field_description:quality_control.field_quality_spreadsheet_template__spreadsheet_binary_data
msgid "Spreadsheet file"
msgstr "ملف جداول البيانات "

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_stage_view_tree
msgid "Stage Name"
msgstr "اسم المرحلة"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_point__standard_deviation
msgid "Standard Deviation"
msgstr "الانحراف المعياري"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__quality_state
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_search
msgid "Status"
msgstr "الحالة"

#. module: quality_control
#: model:ir.model,name:quality_control.model_stock_move
msgid "Stock Move"
msgstr "حركة المخزون"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__move_line_id
msgid "Stock Move Line"
msgstr "بند حركة المخزون "

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__spreadsheet_check_cell
#: model:ir.model.fields,field_description:quality_control.field_quality_check_spreadsheet__check_cell
#: model:ir.model.fields,field_description:quality_control.field_quality_point__spreadsheet_check_cell
#: model:ir.model.fields,field_description:quality_control.field_quality_spreadsheet_template__check_cell
msgid "Success cell"
msgstr "خلية النجاح "

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_tag_view_search
#: model_terms:ir.ui.view,arch_db:quality_control.quality_tag_view_tree
msgid "Tags"
msgstr "علامات التصنيف "

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_search
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_search
msgid "Team"
msgstr "الفريق "

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_team_view_form
msgid "Team Name"
msgstr "اسم الفريق"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.view_quality_point_kanban
msgid "Team:"
msgstr "الفريق: "

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_team_view_tree
msgid "Teams"
msgstr "الفِرَق "

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__test_type
msgid "Technical name"
msgstr "الاسم التقني"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_search
msgid "Test Type"
msgstr "نوع الاختبار"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_tree
msgid "Testing % Within Lot"
msgstr "نسبة الاختبار ضمن الدفعة "

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__testing_percentage_within_lot
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__testing_percentage_within_lot
#: model:ir.model.fields,field_description:quality_control.field_quality_point__testing_percentage_within_lot
msgid "Testing Percentage Within Lot"
msgstr "نسبة الاختبار ضمن الدفعة "

#. module: quality_control
#: model:ir.model.fields,help:quality_control.field_quality_check__spreadsheet_check_cell
#: model:ir.model.fields,help:quality_control.field_quality_check_spreadsheet__check_cell
#: model:ir.model.fields,help:quality_control.field_quality_point__spreadsheet_check_cell
#: model:ir.model.fields,help:quality_control.field_quality_spreadsheet_template__check_cell
msgid ""
"The check is successful if the success cell value is TRUE. If there are "
"several sheets, specify which one you want to use (e.g. Sheet2!C4). If not "
"specified, the first sheet is selected by default."
msgstr ""

#. module: quality_control
#. odoo-python
#: code:addons/quality_control/wizard/on_demand_quality_check_wizard.py:0
msgid "The selected lot/serial number does not exist in the picking."
msgstr "رقم المجموعة/الرقم التسلسلي غير موجود في عملية الانتقاء. "

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_spreadsheet__thumbnail
#: model:ir.model.fields,field_description:quality_control.field_quality_spreadsheet_template__thumbnail
msgid "Thumbnail"
msgstr "صورة مصغرة"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_alert__title
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__title
msgid "Title"
msgstr "العنوان"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_form_inherit_quality_control
msgid "Tolerance"
msgstr "الحد المسموح به "

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__product_tracking
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__product_tracking
msgid "Tracking"
msgstr "التتبع"

#. module: quality_control
#: model:ir.model,name:quality_control.model_stock_picking
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_tree
msgid "Transfer"
msgstr "تحويل "

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.worksheet_page
msgid "Transfer-987"
msgstr "الشحنة-987 "

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_form
msgid "Type"
msgstr "النوع"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.view_quality_point_kanban
msgid "Type:"
msgstr "النوع:"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_form_inherit_quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.view_quality_check_wizard
msgid "Unit of Measure"
msgstr "وحدة القياس"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.view_quality_check_wizard
msgid "Validate"
msgstr "تصديق "

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.worksheet_page
msgid "Value near threshold."
msgstr "قيمة قريبة من الحد الأدنى. "

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.worksheet_page
msgid "Visual Check"
msgstr "فحص بصري "

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__warning_message
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__warning_message
msgid "Warning Message"
msgstr "رسالة تحذير"

#. module: quality_control
#: model:ir.model.fields.selection,name:quality_control.selection__quality_point__measure_frequency_unit__week
msgid "Weeks"
msgstr "أسابيع"

#. module: quality_control
#: model:ir.model,name:quality_control.model_quality_check_wizard
msgid "Wizard for Quality Check Pop Up"
msgstr "معالج لنافذة فحص الجودة "

#. module: quality_control
#: model:ir.model,name:quality_control.model_quality_check_on_demand
msgid "Wizard to select on-demand quality check points"
msgstr "معالج لتحديد نقاط فحص الجودة عند الطلب "

#. module: quality_control
#: model:ir.actions.report,name:quality_control.quality_check_report
msgid "Worksheet Report - External (PDF)"
msgstr "تقرير ورقة العمل - خارجي (PDF) "

#. module: quality_control
#: model:ir.actions.report,name:quality_control.quality_check_report_internal
msgid "Worksheet Report - Internal (PDF)"
msgstr "تقرير ورقة العمل - داخلي (PDF) "

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.view_quality_check_wizard
msgid "Write quality check notes..."
msgstr "اكتب ملاحظات فحص الجودة... "

#. module: quality_control
#. odoo-python
#: code:addons/quality_control/models/stock_picking.py:0
#: code:addons/quality_control/wizard/on_demand_quality_check_wizard.py:0
msgid ""
"You can not create quality check for a draft, done or cancelled transfer."
msgstr "لا يمكنك إنشاء فحص الجودة لشحنة في حالة المسودة أو منتهية أو ملغية. "

#. module: quality_control
#. odoo-python
#: code:addons/quality_control/models/quality.py:0
msgid ""
"You measured %(measure).2f %(unit)s and it should be between "
"%(tolerance_min).2f and %(tolerance_max).2f %(unit)s."
msgstr ""
"قياساتك تُظهِر %(measure).2f %(unit)s بينما كان من المفترض أن تكون بين "
"%(tolerance_min).2f و %(tolerance_max).2f %(unit)s. "

#. module: quality_control
#. odoo-python
#: code:addons/quality_control/wizard/quality_check_wizard.py:0
msgid "You must provide a picture before validating"
msgstr "عليك تقديم صورة قبل التصديق "

#. module: quality_control
#. odoo-python
#: code:addons/quality_control/models/stock_picking.py:0
msgid "You still need to do the quality checks!"
msgstr "لا تزال بحاجة إلى القيام بفحوصات الجودة! "

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_team_view_form
msgid "alias"
msgstr "اللقب "

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_team_view_form
msgid "e.g. The QA Masters"
msgstr "مثال: خبراء تنبيهات الجودة "

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_team_view_form
msgid "e.g. mycompany.com"
msgstr "مثال: mycompany.com "
