# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_forum
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
msgid " Flag"
msgstr " Флаг"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
msgid " Flagged"
msgstr " Отмеченные флажками"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__total_answers
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_tree
msgid "# Answers"
msgstr "# Ответы"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__total_favorites
msgid "# Favorites"
msgstr "# Избранное"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__total_posts
msgid "# Posts"
msgstr "# Posts"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__total_views
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_tree
msgid "# Views"
msgstr "# Просмотров"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to accept or refuse an answer."
msgstr "%d карма, необходимая для принятия или отказа от ответа."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to answer a question."
msgstr "%d карма, необходимая для ответа на вопрос."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to close or reopen a post."
msgstr "%d карма, необходимая для закрытия или повторного открытия сообщения."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to comment."
msgstr "%d для комментирования требуется карма."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to convert a comment to an answer."
msgstr "%d карма, необходимая для преобразования комментария в ответ."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to convert an answer to a comment."
msgstr "%d карма, необходимая для преобразования ответа в комментарий."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to convert your comment to an answer."
msgstr "%d карма, необходимая для преобразования вашего комментария в ответ."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_tag.py:0
msgid "%d karma required to create a new Tag."
msgstr "%d карма, необходимая для создания нового тега."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to create a new question."
msgstr "%d карма, необходимая для создания нового вопроса."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to delete a comment."
msgstr "%d карма, необходимая для удаления комментария."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to delete or reactivate a post."
msgstr "%d карма, необходимая для удаления или повторной активации сообщения."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post_vote.py:0
msgid "%d karma required to downvote."
msgstr "%d карма, необходимая для понижения голоса."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to edit a post."
msgstr "%d карма, необходимая для редактирования сообщения."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to flag a post."
msgstr "%d карма, необходимая для того, чтобы отметить сообщение."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/controllers/website_forum.py:0
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to mark a post as offensive."
msgstr ""
"%d карма, необходимая для того, чтобы отметить сообщение как оскорбительное."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to post an image or link."
msgstr "%d карма, необходимая для размещения изображения или ссылки."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to refuse a post."
msgstr "%d карма, необходимая для отказа от поста."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to retag."
msgstr "%d карма, необходимая для ретагирования."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to unlink a post."
msgstr "%d карма, необходимая для снятия ссылки с поста."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post_vote.py:0
msgid "%d karma required to upvote."
msgstr "%d карма, необходимая для upvote."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to validate a post."
msgstr "%d карма, необходимая для проверки сообщения."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index_tags
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/website_forum_tags_wrapper.xml:0
msgid "'Please enter 2 or more characters'"
msgstr ""

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/website_forum_tags_wrapper.xml:0
msgid "'Tags'"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "(The above section was adapted from Stackoverflow’s FAQ.)"
msgstr "(Приведенный выше раздел был адаптирован из FAQ по StackOverflow.)"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
msgid "(votes - 1) **"
msgstr "(голосов - 1) **"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
msgid "/ (days + 2) **"
msgstr "/ (дни + 2) **"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
msgid "45% of questions shared"
msgstr "45% o из вопросов поделились"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
msgid ""
"65% more chance to get an\n"
"        answer"
msgstr ""
"65% больше шансов получить\n"
"        ответ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid ""
"<b class=\"d-block \">This answer has been flagged</b>\n"
"                            As a moderator, you can either validate or reject this answer."
msgstr ""
"<b class=\"d-block \">Этот ответ был отмечен</b>\n"
"                            Как модератор, вы можете подтвердить или отклонить этот ответ."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid ""
"<b class=\"d-block\">You have a pending post</b>\n"
"                        Please wait for a moderator to validate your previous post to be allowed replying questions."
msgstr ""
"<b class=\"d-block\">У вас есть ожидающее сообщение</b>\n"
"                        Пожалуйста, подождите, пока модератор подтвердит ваше предыдущее сообщение, чтобы получить возможность отвечать на вопросы."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers should not add or expand questions</b>. Instead either edit the "
"question or add a question comment."
msgstr ""
"<b>Ответы не должны добавлять или расширять вопросы</b>. Вместо этого либо "
"измените вопрос, или добавьте вопрос комментарий."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers should not add or expand questions</b>. Instead, either edit the "
"question or add a comment."
msgstr ""
"<b>Ответы не должны дополнять или расширять вопросы</b>. Вместо этого либо "
"отредактируйте вопрос, либо добавьте комментарий."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers should not comment other answers</b>. Instead add a comment on "
"the other answers."
msgstr ""
"<b>Ответы не должны комментировать другие ответы</b>. Вместо этого добавьте "
"комментарий на другие ответы."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers should not start debates</b> This community Q&amp;A is not a "
"discussion group. Please avoid holding debates in your answers as they tend "
"to dilute the essence of questions and answers. For brief discussions please"
" use commenting facility."
msgstr ""
"<b>Ответы не должны вызывать споров</b> Это сообщество вопросов и ответов не"
" является дискуссионной группой. Пожалуйста, не устраивайте дебаты в своих "
"ответах, так как они размывают суть вопросов и ответов. Для коротких "
"обсуждений используйте возможность комментирования."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers shouldn't just point to other Questions</b>. Instead add a "
"question comment indication \"Possible duplicate of...\". However, it's ok "
"to include links to other questions or answers providing relevant additional"
" information."
msgstr ""
"<b>Ответы не должны просто указывать на другие вопросы</b>. Вместо этого "
"добавьте вопрос комментария указание  \"Можно дублировать из...\". Однако "
"это нормально, чтобы включить ссылки на другие вопросы и ответы, представляя"
" соответствующую дополнительную информацию."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers shouldn't just point to other questions</b>.Instead add a comment"
" indicating <i>\"Possible duplicate of...\"</i>. However, it's fine to "
"include links to other questions or answers providing relevant additional "
"information."
msgstr ""
"<b>Ответы не должны просто указывать на другие вопросы</b>. Вместо этого "
"добавьте комментарий <i>\"Возможный дубликат...\"</i>. Однако не помешает "
"включить ссылки на другие вопросы или ответы, содержащие соответствующую "
"дополнительную информацию."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers shouldn't just provide a link a solution</b>. Instead provide the"
" solution description text in your answer, even if it's just a copy/paste. "
"Links are welcome, but should be complementary to answer, referring sources "
"or additional reading."
msgstr ""
"<b>Ответы не должны просто предоставить ссылку решения</b>. Вместо этого "
"предоставьте текст описания решения в вашем ответе, даже если это просто "
"копировать/вставить. Ссылки приветствуются, но должны дополнять ответ, "
"ссылаясь на источники или дополнительное чтение."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Before you ask - please make sure to search for a similar question.</b> "
"You can search questions by their title or tags. It’s also OK to answer your"
" own question."
msgstr ""
"<b>Прежде чем задавать вопрос, убедитесь, что вы искали похожие вопросы.</b>"
" Вы можете искать вопросы по их названию или тегам. Также можно ответить на "
"свой вопрос."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Please avoid asking questions that are too subjective and "
"argumentative</b> or not relevant to this community."
msgstr ""
"<b>Пожалуйста, избегайте задавать слишком субъективные и спорные вопросы</b>"
" или вопросы, не имеющие отношения к данному сообществу."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
msgid ""
"<b>Please try to give a substantial answer.</b> If you wanted to comment on the question or answer, just\n"
"            <b>use the commenting tool.</b> Please remember that you can always <b>revise your answers</b>\n"
"            - no need to answer the same question twice. Also, please <b>don't forget to vote</b>\n"
"            - it really helps to select the best questions and answers!"
msgstr ""
"<b>Пожалуйста, постарайтесь дать содержательный ответ.</b> Если вы хотите прокомментировать вопрос или ответ, просто\n"
"           <b>воспользуйтесь инструментом комментирования.</b> Пожалуйста, помните, что вы всегда можете <b>пересмотреть свои ответы</b>\n"
"            - нет необходимости отвечать на один и тот же вопрос дважды. Также, пожалуйста, <b>не забывайте голосовать</b>\n"
"            - это очень помогает выбрать лучшие вопросы и ответы!"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "<b>Why can other people edit my questions/answers?</b>"
msgstr "<b>Почему другие люди могут редактировать мои вопросы/ответы?</b>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "<b>You already have a pending post.</b><br/>"
msgstr "<b>У вас уже есть отложенное сообщение.</b><br/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.contact
msgid ""
"<b>|</b>\n"
"                    <span class=\"fa fa-trophy o-text-gold ms-2\" role=\"img\" aria-label=\"Gold badge\" title=\"Gold badge\"/>"
msgstr ""
"<b>|</b>\n"
"                    <span class=\"fa fa-trophy o-text-gold ms-2\" role=\"img\" aria-label=\"Gold badge\" title=\"Gold badge\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<br/>by"
msgstr "<br/>на"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "<i class=\"fa fa-angle-left me-2\"/>Back to All Posts"
msgstr "<i class=\"fa fa-angle-left me-2\"/>Назад ко всем постам"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_votes
msgid "<i class=\"fa fa-arrow-right\"/> Go To Forums"
msgstr "<i class=\"fa fa-arrow-right\"/> Перейти на форумы"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "<i class=\"fa fa-bug me-1\"/>Filter Tool"
msgstr "<i class=\"fa fa-bug me-1\"/>Инструмент фильтрации"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.vote
msgid ""
"<i class=\"fa fa-caret-down\" data-bs-toggle=\"tooltip\" data-bs-"
"placement=\"top\" title=\"Downvote\"/>"
msgstr ""
"<i class=\"fa fa-caret-down\" data-bs-toggle=\"tooltip\" data-bs-"
"placement=\"top\" title=\"Downvote\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.vote
msgid ""
"<i class=\"fa fa-caret-up\" data-bs-toggle=\"tooltip\" data-bs-"
"placement=\"top\" title=\"Upvote\"/>"
msgstr ""
"<i class=\"fa fa-caret-up\" data-bs-toggle=\"tooltip\" data-bs-"
"placement=\"top\" title=\"Upvote\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<i class=\"fa fa-check fa-fw me-1\"/>Accept"
msgstr "<i class=\"fa fa-check fa-fw me-1\"/>Принять"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid ""
"<i class=\"fa fa-check fa-fw me-1\"/>Be less specific in your wording for a "
"wider search result."
msgstr ""
"<i class=\"fa fa-check fa-fw me-1\"/>Чтобы получить более широкий результат "
"поиска, используйте менее конкретные формулировки."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "<i class=\"fa fa-check fa-fw me-1\"/>Check your spelling and try again."
msgstr ""
"<i class=\"fa fa-check fa-fw me-1\"/>Проверьте орфографию и попробуйте еще "
"раз."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "<i class=\"fa fa-check fa-fw me-1\"/>Try searching for one or two words."
msgstr ""
"<i class=\"fa fa-check fa-fw me-1\"/>Попробуйте поискать одно или два слова."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "<i class=\"fa fa-check me-1\"/>Solved"
msgstr "<i class=\"fa fa-check me-1\"/>Решено"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.show_flag_validator
msgid "<i class=\"fa fa-check\"/> Accept"
msgstr "<i class=\"fa fa-check\"/> Согласиться"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "<i class=\"fa fa-check\"/> How to configure TPS and TVQ's canadian taxes?"
msgstr "<i class=\"fa fa-check\"/> Как настроить канадские налоги в TPS и TVQ?"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "<i class=\"fa fa-check\"/><span class=\"ms-2\">Accept</span>"
msgstr "<i class=\"fa fa-check\"/><span class=\"ms-2\">Принять</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_kanban
msgid "<i class=\"fa fa-comments-o me-1\" title=\"Forum\"/>"
msgstr "<i class=\"fa fa-comments-o me-1\" title=\"Forum\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_dropdown
msgid ""
"<i class=\"fa fa-ellipsis-h\" data-bs-toggle=\"tooltip\" data-bs-"
"placement=\"top\" title=\"More\"/>"
msgstr ""
"<i class=\"fa fa-ellipsis-h\" data-bs-toggle=\"tooltip\" data-bs-"
"placement=\"top\" title=\"More\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_kanban
msgid "<i class=\"fa fa-eye me-1\" title=\"Views\"/>"
msgstr "<i class=\"fa fa-eye me-1\" title=\"Views\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "<i class=\"fa fa-flag\"/> Country"
msgstr "<i class=\"fa fa-flag\"/> Страна"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "<i class=\"fa fa-font\"/> Text"
msgstr "<i class=\"fa fa-font\"/> Текст"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.follow
msgid "<i class=\"fa fa-fw fa-check me-1\"/>Following"
msgstr "<i class=\"fa fa-fw fa-check me-1\"/>Следуя"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_kanban
msgid "<i class=\"fa fa-globe me-1\" title=\"Website\"/>"
msgstr "<i class=\"fa fa-globe me-1\" title=\"Вебсайт\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_mobile
msgid "<i class=\"fa fa-info-circle fa-fw\"/> About this forum"
msgstr "<i class=\"fa fa-info-circle fa-fw\"/> Об этом форуме"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid ""
"<i class=\"fa fa-pencil\"/>\n"
"                                Edit<span class=\"d-none d-lg-inline\"> your answer</span>"
msgstr ""
"<i class=\"fa fa-pencil\"/>\n"
"                                Отредактируйте<span class=\"d-none d-lg-inline\"> свой ответ</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post
msgid ""
"<i class=\"fa fa-reply me-1 d-lg-none\"/>\n"
"                0"
msgstr ""
"<i class=\"fa fa-reply me-1 d-lg-none\"/>\n"
"                0"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid "<i class=\"fa fa-reply me-1\"/>Reply"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid ""
"<i class=\"fa fa-share-alt\" data-bs-toggle=\"tooltip\" data-bs-"
"placement=\"top\" title=\"Share\"/>"
msgstr ""
"<i class=\"fa fa-share-alt\" data-bs-toggle=\"tooltip\" data-bs-"
"placement=\"top\" title=\"Share\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_body
msgid "<i class=\"fa fa-shield fa-fw opacity-50\"/> Badges"
msgstr "<i class=\"fa fa-shield fa-fw opacity-50\"/> Значки"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid ""
"<i class=\"fa fa-thumbs-down text-danger ms-3\" role=\"img\" aria-"
"label=\"Negative votes\" title=\"Negative votes\"/>"
msgstr ""
"<i class=\"fa fa-thumbs-down text-danger ms-3\" role=\"img\" aria-"
"label=\"Negative votes\" title=\"Negative votes\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid ""
"<i class=\"fa fa-thumbs-up text-success\" role=\"img\" aria-label=\"Positive"
" votes\" title=\"Positive votes\"/>"
msgstr ""
"<i class=\"fa fa-thumbs-up text-success\" role=\"img\" aria-label=\"Positive"
" votes\" title=\"Positive votes\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<i class=\"fa fa-times fa-fw me-1\"/>Reject"
msgstr "<i class=\"fa fa-times fa-fw me-1\"/>Отклонить"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ""
"<i class=\"fa fa-times\"/> Good morning to all! Please, can someone help "
"solve my tax computation problem in Canada? Thanks!"
msgstr ""
"<i class=\"fa fa-times\"/> Доброе утро всем! Пожалуйста, может ли кто-нибудь"
" помочь решить мою проблему с исчислением налогов в Канаде? Спасибо!"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.show_flag_validator
msgid "<i class=\"fa fa-times\"/> Reject"
msgstr "<i class=\"fa fa-times\"/> Отказаться"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "<i class=\"fa fa-times\"/><span class=\"ms-2\">Offensive</span>"
msgstr "<i class=\"fa fa-times\"/><span class=\"ms-2\">Нападение</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "<i class=\"fa fa-times\"/><span class=\"ms-2\">Reject</span>"
msgstr "<i class=\"fa fa-times\"/><span class=\"ms-2\">Отклонить</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "<i class=\"fa fa-user\"/> User"
msgstr "<i class=\"fa fa-user\"/> Пользователь"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_body
msgid "<i class=\"fa fa-users fa-fw opacity-50\"/> People"
msgstr "<i class=\"fa fa-users fa-fw opacity-50\"/> Люди"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "<i class=\"oi oi-arrow-right d-inline-block\"/> Go to Forums"
msgstr "<i class=\"oi oi-arrow-right d-inline-block\"/> Перейти к форумам"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.404
msgid ""
"<i class=\"oi oi-arrow-right display-inline-block\"/> Return to questions "
"list"
msgstr ""
"<i class=\"oi oi-arrow-right display-inline-block\"/> Вернуться к списку "
"вопросов"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_sub_nav
msgid "<i class=\"oi oi-chevron-left small\"/> Back"
msgstr "<i class=\"oi oi-chevron-left small\"/> Назад"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "<small class=\"fw-bold\">Votes</small>"
msgstr "<small class=\"fw-bold\">Голоса</small>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_body
msgid "<small>(View all)</small>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all_all_entries
msgid "<span class=\"badge bg-dark me-1\">Last post:</span>"
msgstr "<span class=\"badge bg-dark me-1\">Последнее сообщение:</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.contact
msgid ""
"<span class=\"fa fa-trophy o-text-bronze ms-2\" role=\"img\" aria-"
"label=\"Bronze badge\" title=\"Bronze badge\"/>"
msgstr ""
"<span class=\"fa fa-trophy o-text-bronze ms-2\" role=\"img\" aria-"
"label=\"Bronze badge\" title=\"Bronze badge\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.contact
msgid ""
"<span class=\"fa fa-trophy o-text-silver ms-2\" role=\"img\" aria-"
"label=\"Silver badge\" title=\"Silver badge\"/>"
msgstr ""
"<span class=\"fa fa-trophy o-text-silver ms-2\" role=\"img\" aria-"
"label=\"Silver badge\" title=\"Silver badge\"/>"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "<span class=\"flex-grow-1\">What kind of questions can I ask here?</span>"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "<span class=\"flex-grow-1\">What should I avoid in my answers?</span>"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "<span class=\"flex-grow-1\">What should I avoid in my questions?</span>"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<span class=\"flex-grow-1\">Why can other people edit my "
"questions/answers?</span>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ""
"<span class=\"form-text small text-muted d-block\">consider adding an "
"example.</span>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ""
"<span class=\"form-text small text-muted d-block\">select text to format "
"it.</span>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ""
"<span class=\"form-text small text-muted d-block\">use '/' to insert "
"images.</span>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ""
"<span class=\"form-text small text-muted me-1\"><i class=\"fa fa-"
"lightbulb-o\"/> Tip:</span>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
msgid "<span class=\"o_stat_text\">Favorites</span>"
msgstr "<span class=\"o_stat_text\">Избранное</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_form
msgid "<span class=\"o_stat_text\">Go to <br/>Website</span>"
msgstr "<span class=\"o_stat_text\">Перейти на сайт <br/></span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
msgid "<span class=\"o_stat_text\">Posts</span>"
msgstr "<span class=\"o_stat_text\">Посты</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_activities
msgid "<span class=\"text-muted\">There is no activity yet.</span>"
msgstr "<span class=\"text-muted\">Пока нет активности.</span>"

#. module: website_forum
#: model_terms:web_tour.tour,rainbow_man_message:website_forum.question
msgid "<span><b>Good job!</b> You went through all steps of this tour.</span>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<span>Be the first to answer this question</span>"
msgstr "<span>Будьте первым, кто ответит на этот вопрос</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "<span>Moderation</span>"
msgstr "<span>Модерация</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid ""
"<span>Please wait for a moderator to validate your previous post to be "
"allowed to reply to questions.</span>"
msgstr ""
"<span>Пожалуйста, дождитесь подтверждения модератором вашего предыдущего "
"сообщения, чтобы получить возможность отвечать на вопросы.</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_header
msgid "<span>You need to be registered to interact with the community.</span>"
msgstr ""
"<span>Чтобы взаимодействовать с сообществом, необходимо "
"зарегистрироваться.</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_answer
msgid "A new answer on"
msgstr "Новый ответ на"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_question
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_validation
msgid "A new question"
msgstr "Новый вопрос"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_answer_accept_own
msgid "Accept an answer on own questions"
msgstr "Принять ответ на собственные вопросы"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_answer_accept_all
msgid "Accept an answer to all questions"
msgstr "Принять ответ на все вопросы"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_search
msgid "Accepted Answer"
msgstr "Принятый ответ"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "Accepted answer removed"
msgstr "Принятый ответ удален"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_answer_accept
msgid "Accepting an answer"
msgstr "Принятие ответа"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
msgid "Access Denied"
msgstr "Доступ запрещён"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_needaction
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_needaction
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_needaction
msgid "Action Needed"
msgstr "Требуются действия"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__active
#: model:ir.model.fields,field_description:website_forum.field_forum_post__active
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post__state__active
msgid "Active"
msgstr "Активный"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Activities"
msgstr "Активность"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
#: model_terms:ir.ui.view,arch_db:website_forum.forum_related_posts
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Activity"
msgstr "Активность"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
msgid "Add a comment"
msgstr "Добавить комментарий"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__allow_share
msgid ""
"After posting the user will be proposed to share its question or answer on "
"social networks, enabling social network propagation of the forum content."
msgstr ""
"После публикации, пользователю будет предложено поделиться своим вопросом "
"или ответом в социальных сетях, позволяя социальной сети распространить "
"содержания форума."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/controllers/website_forum.py:0
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "All"
msgstr "Все"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_body
msgid "All Posts"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "All Topics"
msgstr "Все темы"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "All forums"
msgstr "Все форумы"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "Animation of a pen checking a checkbox"
msgstr "Анимация ручки, проверяющей флажок"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post_vote.py:0
msgid "Answer %s"
msgstr "Ответ %s"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
#: model:mail.message.subtype,description:website_forum.mt_answer_edit
#: model:mail.message.subtype,name:website_forum.mt_answer_edit
msgid "Answer Edited"
msgstr "Ответ отредактирован"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_answer_accepted
msgid "Answer accepted"
msgstr "Ответ принят"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_6
msgid "Answer accepted with 15 or more votes"
msgstr "Ответ принят 15 и более голосами"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_answer_downvote
msgid "Answer downvoted"
msgstr "Ответ против"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_answer_flagged
msgid "Answer flagged"
msgstr "Ответ помечен"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_answer
msgid "Answer questions"
msgstr "Ответ на вопросы"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_answer_upvote
msgid "Answer upvoted"
msgstr "Ответ за"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_4
msgid "Answer voted up 15 times"
msgstr "За ответ проголосовали за 15 раз"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_2
msgid "Answer voted up 4 times"
msgstr "За ответ проголосовали за 4 раза"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_3
msgid "Answer voted up 6 times"
msgstr "За ответ проголосовали за 6 раз"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_5
msgid "Answer was accepted with 3 or more votes"
msgstr "Ответ был принят 3 и более голосами"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Answer:"
msgstr "Ответ:"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__default_order__child_count_desc
msgid "Answered"
msgstr "Отвечено"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_search
msgid "Answered Posts"
msgstr "Отвеченные сообщения"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_form
msgid "Answered by"
msgstr "Отвечено от"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post
msgid "Answered on"
msgstr "Ответил на"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_8
msgid "Answered own question with at least 4 up votes"
msgstr "Отвечен собственный вопрос, по крайней мере 4 за голоса"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__child_count
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_form
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_kanban
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_search
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Answers"
msgstr "Ответы"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_gamification_challenge__challenge_category
msgid "Appears in"
msgstr "Появляется в"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_search
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_form
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_search
msgid "Archived"
msgstr "Архивировано"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
msgid "Are you sure you want to delete this comment?"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "As a moderator, you can either validate or reject this answer."
msgstr "Как модератор, вы можете подтвердить или отклонить этот ответ."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "Ask a new question"
msgstr "Задайте новый вопрос"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "Ask a question"
msgstr "Задать вопрос"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_ask
msgid "Ask questions"
msgstr "Задать вопрос"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_post
msgid "Ask questions without validation"
msgstr "Задавайте вопросы без подтверждения"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Ask your question"
msgstr "Задать вопрос"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_26
msgid "Asked a question and accepted an answer"
msgstr "Задан вопрос и подтвержден ответ"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_1
msgid "Asked a question with at least 150 views"
msgstr "Задан вопрос с минимум 150 просмотрами"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_2
msgid "Asked a question with at least 250 views"
msgstr "Задан вопрос с минимум 250 просмотрами"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_3
msgid "Asked a question with at least 500 views"
msgstr "Задан вопрос с минимум 500 просмотрами"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_7
msgid "Asked first question with at least one up vote"
msgstr "Задан вопрос с минимум 1 голосом за"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__create_date
msgid "Asked on"
msgstr "Спросил"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_question_new
msgid "Asking a question"
msgstr "Задать вопрос"

#. module: website_forum
#: model:ir.model,name:website_forum.model_ir_attachment
msgid "Attachment"
msgstr "Вложение"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_attachment_count
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_attachment_count
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_attachment_count
msgid "Attachment Count"
msgstr "Количество вложений"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_search
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid "Author"
msgstr "Автор"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__authorized_group_id
msgid "Authorized Group"
msgstr "Уполномоченная группа"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_p_1
msgid "Autobiographer"
msgstr "Автобиограф"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.author_box
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_header
msgid "Avatar"
msgstr "Аватар"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Back to Question"
msgstr "Назад к вопросу"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/controllers/website_forum.py:0
msgid "Bad \"filters\" value \"%(filters)s\"."
msgstr "Плохое значение \"фильтры\" \"%(filters)s\"."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/controllers/website_forum.py:0
msgid "Bad \"tag_char\" value \"%(tag_char)s\""
msgstr "Плохое значение \"tag_char\" \"%(tag_char)s\""

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/controllers/website_forum.py:0
msgid "Bad Request"
msgstr "Неправильный запрос"

#. module: website_forum
#: model:ir.ui.menu,name:website_forum.menu_forum_badges
msgid "Badges"
msgstr "Значки"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post_reason__reason_type__basic
msgid "Basic"
msgstr "Базовый"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "Because there are no posts in this forum yet."
msgstr "Потому что в этом форуме еще нет сообщений."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid "Best Answer"
msgstr "Лучший ответ"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
msgid "By sharing you answer, you will get additional"
msgstr "Поделившись своим ответом, вы получите дополнительные"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_accept
msgid "Can Accept"
msgstr "Может принять"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_answer
msgid "Can Answer"
msgstr "Можно ответить"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_ask
msgid "Can Ask"
msgstr "Можно спросить"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_post
msgid "Can Automatically be Validated"
msgstr "Может быть автоматически подтверден"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_close
msgid "Can Close"
msgstr "Можно закрыть"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_comment
msgid "Can Comment"
msgstr "Можно комментировать"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_comment_convert
msgid "Can Convert to Comment"
msgstr "Можно преобразовать в комментарий"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_downvote
msgid "Can Downvote"
msgstr "Можно понизить голос"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_edit
msgid "Can Edit"
msgstr "Можно редактировать"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_flag
msgid "Can Flag"
msgstr "Флаг страны"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_moderate
msgid "Can Moderate"
msgstr "Можно модерировать"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_unlink
msgid "Can Unlink"
msgstr "Можно разблокировать"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_upvote
msgid "Can Upvote"
msgstr "Можно проголосовать"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_use_full_editor
msgid "Can Use Full Editor"
msgstr "Можно использовать полный редактор"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_view
msgid "Can View"
msgstr "Можно просматривать"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_edit_retag
msgid "Change question tags"
msgstr "Изменить тег вопроса"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_p_4
#: model:gamification.challenge,name:website_forum.challenge_chief_commentator
msgid "Chief Commentator"
msgstr "Главный комментатор"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
msgid "Click here to accept this answer."
msgstr "Нажмите здесь, чтобы принять этот ответ."

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
msgid "Click to post your answer."
msgstr "Нажмите, чтобы опубликовать ответ."

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
msgid "Click to post your question."
msgstr "Нажмите, чтобы опубликовать ваш вопрос."

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
msgid "Click to reply."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_dropdown
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_mobile
msgid "Close"
msgstr "Закрыть"

#. module: website_forum
#: model:ir.ui.menu,name:website_forum.menu_forum_post_reasons
msgid "Close Reasons"
msgstr "Близкие причины"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_close_all
msgid "Close all posts"
msgstr "Закрыть все посты"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_close_own
msgid "Close own posts"
msgstr "Закрыть собственные сообщения"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.mark_as_offensive
msgid "Close post"
msgstr "Закрыть пост"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post__state__close
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_body
msgid "Closed"
msgstr "Закрыто"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Closed Posts"
msgstr "Закрытые посты"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__closed_uid
msgid "Closed by"
msgstr "Закрыто"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__closed_date
msgid "Closed on"
msgstr "Chiuso a"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.mark_as_offensive
msgid "Closing"
msgstr "Закрывается"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__name
msgid "Closing Reason"
msgstr "Причина закрытия"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__color
msgid "Color"
msgstr "Цвет"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid "Comment"
msgstr "Комментарий"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_all
msgid "Comment all posts"
msgstr "Комментировать все посты"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_own
msgid "Comment own posts"
msgstr "Комментировать собственные сообщения"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
msgid "Comment this post"
msgstr "Прокомментируйте этот пост"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_p_2
#: model:gamification.challenge,name:website_forum.challenge_commentator
#: model:gamification.goal.definition,name:website_forum.definition_commentator
msgid "Commentator"
msgstr "Комментатор"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all_oe_structure_forum_all_top
msgid "Community Forums"
msgstr "Форумы сообщества"

#. module: website_forum
#: model:gamification.challenge,name:website_forum.challenge_configure_profile
msgid "Complete own biography"
msgstr "Завершите собственную биографию"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_configure_profile
#: model_terms:gamification.badge,description:website_forum.badge_p_1
msgid "Completed own biography"
msgstr "Завершение собственной биографии"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_7
msgid "Contains offensive or malicious remarks"
msgstr "Содержит оскорбительные или злобные высказывания"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__content
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_search
msgid "Content"
msgstr "Содержание"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_convert_all
msgid "Convert all comments to answers"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_accept
msgid "Convert comment to answer"
msgstr "Конвертировать комментарий в ответ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_convert_own
msgid "Convert own comments to answers"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_dropdown
msgid "Convert to Comment"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
msgid "Convert to answer"
msgstr "Преобразование в ответ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__is_correct
msgid "Correct"
msgstr "Правильно"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_post__is_correct
msgid "Correct answer or answer accepted"
msgstr "Правильный ответ или ответ принят"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__create_date
#: model:ir.model.fields,field_description:website_forum.field_res_users__create_date
msgid "Create Date"
msgstr "Дата создания"

#. module: website_forum
#: model_terms:ir.actions.act_window,help:website_forum.forum_post_action
msgid "Create a new forum post"
msgstr "Создайте новое сообщение на форуме"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
msgid "Create a new post in this forum by clicking on the button."
msgstr "Создайте новое сообщение в этом форуме, нажав на кнопку."

#. module: website_forum
#: model_terms:ir.actions.act_window,help:website_forum.forum_tag_action
msgid "Create a new tag"
msgstr "Создать новый тег"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_sign_up_cta
msgid ""
"Create an account today to enjoy exclusive features and engage with our "
"awesome community!"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_tag_create
msgid "Create new tags"
msgstr "Создавать новые теги"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/website_forum_tags_wrapper.xml:0
msgid "Create option \""
msgstr ""

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_32
msgid "Created a tag used by 15 questions"
msgstr "Созданный тег, используемый на 15 вопросах"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__create_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post__create_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__create_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__create_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__create_uid
msgid "Created by"
msgstr "Создано"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__create_date
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__create_date
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__create_date
#: model_terms:ir.ui.view,arch_db:website_forum.display_post
msgid "Created on"
msgstr "Создано"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_4
#: model:gamification.challenge,name:website_forum.challenge_favorite_question_1
msgid "Credible Question"
msgstr "Достоверный вопрос"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_5
#: model:gamification.challenge,name:website_forum.challenge_critic
#: model:gamification.goal.definition,name:website_forum.definition_critic
msgid "Critic"
msgstr "Critic"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_searchbar_input_snippet_options
msgid "Date"
msgstr "Дата"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_searchbar_input_snippet_options
msgid "Date (high to low)"
msgstr "Дата (от максимума к минимуму)"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_searchbar_input_snippet_options
msgid "Date (low to high)"
msgstr "Дата (от минимума к максимуму)"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__default_order
msgid "Default"
msgstr "По умолчанию"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
msgid "Default Sort"
msgstr "Сортировка по умолчанию"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_gamification_challenge__challenge_category
msgid "Define the visibility of the challenge through menus"
msgstr "Определите видимость задачи с помощью меню"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
#: model_terms:ir.ui.view,arch_db:website_forum.post_dropdown
msgid "Delete"
msgstr "Удалить"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_unlink_all
msgid "Delete all comments"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_unlink_all
msgid "Delete all posts"
msgstr "Удалить все сообщения"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_unlink_own
msgid "Delete own comments"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_unlink_own
msgid "Delete own posts"
msgstr "Удаление собственных сообщений"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "Delete the accepted answer"
msgstr "Удалите принятый ответ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "Deleted"
msgstr "Удалено"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_23
msgid "Deleted own post with 3 or more downvotes"
msgstr "Удален собственный пост с 3-мя или более против голосами"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_6
msgid "Deleted own post with 3 or more upvotes"
msgstr "Удален собственный пост с 3-мя или более за голосами"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__description
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.forum_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Description"
msgstr "Описание"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
msgid "Description visible on website"
msgstr "Описание на сайте"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.mark_as_offensive
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
msgid "Discard"
msgstr "Отменить"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_6
#: model:gamification.challenge,name:website_forum.challenge_disciplined
#: model:gamification.goal.definition,name:website_forum.definition_disciplined
msgid "Disciplined"
msgstr "Дисциплинированный"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__mode__discussions
msgid "Discussions (multiple answers)"
msgstr "Обсуждения (несколько ответов)"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_forum.py:0
#: model_terms:forum.forum,welcome_message:website_forum.forum_help
msgid "Dismiss"
msgstr "Отклонить"

#. module: website_forum
#: model_terms:forum.forum,welcome_message:website_forum.forum_help
msgid "Dismiss message"
msgstr "Отклонить сообщение"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__display_name
#: model:ir.model.fields,field_description:website_forum.field_forum_post__display_name
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__display_name
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__display_name
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__display_name
msgid "Display Name"
msgstr "Отображаемое имя"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_user_bio
msgid "Display detailed user biography"
msgstr "Отображение подробной биографии пользователя"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_downvote
#: model_terms:ir.ui.view,arch_db:website_forum.vote
msgid "Downvote"
msgstr "Против"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "Downvote for posting offensive contents"
msgstr "Уменьшение голосов за размещение оскорбительных материалов"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_1
msgid "Duplicate post"
msgstr "Дублирующий пост"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_dropdown
msgid "Edit"
msgstr "Редактировать"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "Edit Answer"
msgstr "Изменить ответ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "Edit Question"
msgstr "Редактировать вопрос"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_edit_all
msgid "Edit all posts"
msgstr "Редактировать все сообщения"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_edit_own
msgid "Edit own posts"
msgstr "Редактирование собственных сообщений"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Edit your Post"
msgstr "Редактировать ваше сообщение"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Edit your answer"
msgstr "Отредактируйте свой ответ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Edit your question"
msgstr "Отредактируйте свой вопрос"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_7
#: model:gamification.challenge,name:website_forum.challenge_editor
#: model:gamification.goal.definition,name:website_forum.definition_editor
msgid "Editor"
msgstr "Редактор"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_editor
msgid "Editor Features: image and links"
msgstr "Особенности редактора: изображение и ссылки"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "Empty box"
msgstr "Пустой блок"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_sign_up_cta
msgid "Enjoying the discussion? Don't just read, join in!"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_5
#: model:gamification.challenge,name:website_forum.challenge_enlightened
#: model:gamification.goal.definition,name:website_forum.definition_enlightened
msgid "Enlightened"
msgstr "Просветленный"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ""
"Example\n"
"                            <i class=\"fa fa-question-circle\"/>"
msgstr ""
"Пример\n"
"                           <i class=\"fa fa-question-circle\"/>"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid "Facebook"
msgstr "Facebook"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_3
#: model:gamification.challenge,name:website_forum.challenge_famous_question
msgid "Famous Question"
msgstr "Знаменитый вопрос"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__favourite_count
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "Favorite"
msgstr "Избранное"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_5
#: model:gamification.challenge,name:website_forum.challenge_favorite_question_5
msgid "Favorite Question"
msgstr "Любимый вопрос"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__favourite_ids
msgid "Favourite"
msgstr "Избранное"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_favorite_question_1
msgid "Favourite Question (1)"
msgstr "Любимый вопрос (1)"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_stellar_question_25
msgid "Favourite Question (25)"
msgstr "Любимый вопрос (25)"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_favorite_question_5
msgid "Favourite Question (5)"
msgstr "Любимый вопрос (5)"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Favourite Questions"
msgstr "Любимые вопросы"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_body
msgid "Favourites"
msgstr "Избранные"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_post__last_activity_date
msgid ""
"Field to keep track of a post's last activity. Updated whenever it is "
"replied to, or when a comment is added on the post or one of its replies."
msgstr ""
"Поле для отслеживания последней активности сообщения. Обновляется каждый "
"раз, когда на него отвечают, или когда к сообщению или одному из ответов на "
"него добавляется комментарий."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Filter by:"
msgstr "Фильтр:"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__relevancy_post_vote
msgid "First Relevance Parameter"
msgstr "Первый параметр релевантности"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_5
msgid "First downvote"
msgstr "Первый против голос"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_7
msgid "First edit"
msgstr "Первая редакция"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_31
msgid "First upvote"
msgstr "Первый за голос"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_dropdown
msgid "Flag"
msgstr "Флаг"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_flag
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_flag
msgid "Flag a post as offensive"
msgstr "Отметить сообщение как оскорбительное"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post__state__flagged
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_body
msgid "Flagged"
msgstr "Отмечено"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__flag_user_id
msgid "Flagged by"
msgstr "Помечен"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.follow
msgid "Follow"
msgstr "Подписаться"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Followed Questions"
msgstr "Отслеживаемые вопросы"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "Followed Tags"
msgstr "Следуемые теги"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_follower_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_follower_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_follower_ids
msgid "Followers"
msgstr "Подписчики"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_partner_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_partner_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_partner_ids
msgid "Followers (Partners)"
msgstr "Подписчики"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"For example, if you ask an interesting question or give a helpful answer, "
"your input will be upvoted. On the other hand if the answer is misleading - "
"it will be downvoted. Each vote in favor will generate 10 points, each vote "
"against will subtract 2 points. There is a limit of 200 points that can be "
"accumulated for a question or answer per day. The table given at the end "
"explains reputation point requirements for each type of moderation task."
msgstr ""
"Например, если вы зададите интересный вопрос или дадите полезный ответ, ваш "
"вклад будет проголосован. С другой стороны, если ответ вводит в заблуждение "
"- он будет проголосован ниже. Каждый голос \"за\" приносит 10 баллов, каждый"
" голос \"против\" отнимает 2 балла. В день за вопрос или ответ может быть "
"накоплено не более 200 баллов. В таблице, приведенной в конце, указаны "
"требования к баллам репутации для каждого типа модераторских заданий."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/website.py:0
#: model:ir.actions.act_url,name:website_forum.action_open_forum
#: model:ir.model,name:website_forum.model_forum_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__forum_id
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__forum_id
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__forum_id
#: model:ir.ui.menu,name:website_forum.menu_website_forum_global
#: model:website.menu,name:website_forum.menu_website_forums
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_search
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_search
#: model_terms:ir.ui.view,arch_db:website_forum.gamification_karma_tracking_view_search
msgid "Forum"
msgstr "Форум"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_website__forum_count
msgid "Forum Count"
msgstr "Форумный счет"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form_add
msgid "Forum Mode"
msgstr "Режим форума"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__name
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form_add
msgid "Forum Name"
msgstr "Название форума"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.snippet_options
msgid "Forum Page"
msgstr "Страница форума"

#. module: website_forum
#: model:ir.model,name:website_forum.model_forum_post
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_form
msgid "Forum Post"
msgstr "Сообщение форума"

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.forum_post_action
msgid "Forum Post Pages"
msgstr "Страницы сообщений форума"

#. module: website_forum
#: model:ir.ui.menu,name:website_forum.menu_forum_post_pages
msgid "Forum Posts"
msgstr "Сообщения на форуме"

#. module: website_forum
#: model:ir.model,name:website_forum.model_forum_tag
msgid "Forum Tag"
msgstr "Тег форума"

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.forum_tag_action
msgid "Forum Tags"
msgstr ""

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.forum_forum_action
#: model:ir.ui.menu,name:website_forum.menu_forum_global
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_tree
#: model_terms:ir.ui.view,arch_db:website_forum.forum_searchbar_input_snippet_options
msgid "Forums"
msgstr "Форумы"

#. module: website_forum
#: model:ir.model,name:website_forum.model_gamification_challenge
msgid "Gamification Challenge"
msgstr "Геймификация испытания"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.follow
msgid "Get notified when there's activity on this post"
msgstr "Получайте уведомления о появлении активности в этом посте"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.follow
msgid "Get notified when this tag is used"
msgstr "Получайте уведомления об использовании этого тега"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
msgid "Give your post title."
msgstr "Дайте название своему посту."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "Go back to the list of"
msgstr "Вернитесь к списку"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "Go enjoy a cup of coffee."
msgstr "Идите и выпейте чашечку кофе."

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_3
#: model:gamification.challenge,name:website_forum.challenge_good_answer
msgid "Good Answer"
msgstr "Хороший ответ"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_good_answer
msgid "Good Answer (6)"
msgstr "Хороший ответ (6)"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_9
#: model:gamification.challenge,name:website_forum.challenge_good_question
msgid "Good Question"
msgstr "Хороший вопрос"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_graph
msgid "Graph of Posts"
msgstr "График публикаций"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_4
#: model:gamification.challenge,name:website_forum.challenge_great_answer
msgid "Great Answer"
msgstr "Отличный ответ"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_great_answer
msgid "Great Answer (15)"
msgstr "Отличный ответ (15)"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_10
#: model:gamification.challenge,name:website_forum.challenge_great_question
msgid "Great Question"
msgstr "Большой вопрос"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.snippet_options
msgid "Grid"
msgstr "Сетка"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_search
msgid "Group By"
msgstr "Группировать по"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__faq
#: model_terms:ir.ui.view,arch_db:website_forum.faq
msgid "Guidelines"
msgstr "Рекомендации"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_6
#: model:gamification.challenge,name:website_forum.challenge_guru
msgid "Guru"
msgstr "Гуру"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_guru
msgid "Guru (15)"
msgstr "Гуру (15)"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__uid_has_answered
msgid "Has Answered"
msgstr "Ответил"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__has_message
#: model:ir.model.fields,field_description:website_forum.field_forum_post__has_message
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__has_message
msgid "Has Message"
msgstr "Есть сообщение"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__has_pending_post
msgid "Has pending post"
msgstr "Имеет отложенный пост"

#. module: website_forum
#: model:forum.forum,name:website_forum.forum_help
msgid "Help"
msgstr "Помощь"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_votes
msgid "Help moderating the forums by upvoting and downvoting posts. <br/>"
msgstr ""
"Помогайте модерировать форумы, повышая и понижая рейтинг сообщений. <br/>"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "Here a table with the privileges and the karma level"
msgstr "Вот таблица с привилегиями и уровнем кармы"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__id
#: model:ir.model.fields,field_description:website_forum.field_forum_post__id
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__id
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__id
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__id
msgid "ID"
msgstr "ID"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_needaction
#: model:ir.model.fields,help:website_forum.field_forum_post__message_needaction
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""
"Если флажок установлен, значит, новые сообщения требуют вашего внимания."

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_has_error
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_has_sms_error
#: model:ir.model.fields,help:website_forum.field_forum_post__message_has_error
#: model:ir.model.fields,help:website_forum.field_forum_post__message_has_sms_error
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_has_error
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Если отмечено, некоторые сообщения имеют ошибку доставки."

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__karma_dofollow
msgid ""
"If the author has not enough karma, a nofollow attribute is added to links"
msgstr ""
"Если автор не имеет достаточной кармы, атрибут nofollow добавляется к "
"ссылкам"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "If this approach is not for you, please respect the community."
msgstr "Если такой подход не для вас, пожалуйста, уважайте сообщество."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.mark_as_offensive
msgid ""
"If you close this post, it will be hidden for most users. Only\n"
"            users having a high karma can see closed posts to moderate\n"
"            them."
msgstr ""
"Если закрыть этот пост, он будет скрыт для большинства пользователей. Только\n"
"          пользователи, имеющие высокую карму смогут видеть закрытые публикации, чтобы модерировать\n"
"          их."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"If you fit in one of these example or if your motivation for asking the "
"question is “I would like to participate in a discussion about ______”, then"
" you should not be asking here but on our mailing lists. However, if your "
"motivation is “I would like others to explain ______ to me”, then you are "
"probably OK."
msgstr ""
"Если вы подходите под один из этих примеров или если ваша мотивация задать "
"вопрос - \"я хотел бы принять участие в обсуждении ______\", то вам следует "
"задавать его не здесь, а в наших списках рассылки. Однако если ваша "
"мотивация - \"я хотел бы, чтобы другие объяснили мне, что такое ______\", то"
" вы, вероятно, не против."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.mark_as_offensive
msgid ""
"If you mark this post as offensive, it will be hidden for most users. Only\n"
"            users having a high karma can see offensive posts to moderate\n"
"            them."
msgstr ""
"Если пометить этот пост как оскорбительные, он будет скрыт для большинства пользователей. Только\n"
"          пользователи имеющие высокую карму могут видеть оскорбительные сообщения, чтобы модерировать\n"
"          их."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__image_1920
msgid "Image"
msgstr "Изображение"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__image_1024
msgid "Image 1024"
msgstr "Изображение 1024"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__image_128
msgid "Image 128"
msgstr "Изображение 128"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__image_256
msgid "Image 256"
msgstr "Изображение 256"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__image_512
msgid "Image 512"
msgstr "Изображение 512"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_13
msgid "Inappropriate and unacceptable statements"
msgstr "Неуместные и неприемлемые высказывания"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
msgid "Insert tags related to your question."
msgstr "Вставьте теги, связанные с вашим вопросом."

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_11
msgid "Insulting and offensive language"
msgstr "Оскорбительные и нецензурные выражения"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__user_favourite
msgid "Is Favourite"
msgstr "Любимый"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_is_follower
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_is_follower
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_is_follower
msgid "Is Follower"
msgstr "Является подписчиком"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__can_moderate
msgid "Is a moderator"
msgstr "Является модератором"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__has_validated_answer
msgid "Is answered"
msgstr "Есть ответ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_display_biography
msgid "Is the author's biography visible from his post"
msgstr "Авторская биография видна из его публикации"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post_vote.py:0
msgid "It is not allowed to modify someone else's vote."
msgstr "Запрещено изменять чужие голоса."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post_vote.py:0
msgid "It is not allowed to vote for its own post."
msgstr "Он не может голосовать за свой собственный пост."

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
msgid "Karma Error"
msgstr "Ошибка кармы"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
msgid "Karma Gains"
msgstr "Сила кармы"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
msgid "Karma Related Rights"
msgstr "Права, связанные с кармой"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_close
msgid "Karma to close"
msgstr "Карма на закрытие"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_comment
msgid "Karma to comment"
msgstr "Карма к комментарию"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_comment_convert
msgid "Karma to convert comment to answer"
msgstr "Карма, чтобы преобразовать комментарий в ответит"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_edit
msgid "Karma to edit"
msgstr "Карма для редактирования"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_unlink
msgid "Karma to unlink"
msgstr "Карма для снятия ссылки"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post
msgid "Last Activity"
msgstr "Последняя активность"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__last_post_id
#: model_terms:ir.ui.view,arch_db:website_forum.snippet_options
msgid "Last Post"
msgstr "Последний пост"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__default_order__last_activity_date_desc
msgid "Last Updated"
msgstr "Последнее обновление"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__write_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__write_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__write_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__write_uid
msgid "Last Updated by"
msgstr "Последнее обновление"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__write_date
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__write_date
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__write_date
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__write_date
msgid "Last Updated on"
msgstr "Последнее обновление"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__last_activity_date
msgid "Last activity on"
msgstr "Последние действия"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.snippet_options
msgid "Layout"
msgstr "Макет"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_25
msgid "Left 10 answers with score of 10 or more"
msgstr "Оставьте 10 ответов с оценкой 10 и более баллов"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_url
msgid "Link to questions with the tag"
msgstr "Ссылки на вопросы с тегом"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid "LinkedIn"
msgstr "LinkedIn"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.snippet_options
msgid "List"
msgstr "Список"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid "Mark as Best Answer"
msgstr "Отметить как лучший ответ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.show_flag_validator
msgid "Mark as Offensive"
msgstr "Отметить как оскорбительное"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.mark_as_offensive
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Mark as offensive"
msgstr "Пометить оскорбительным"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Mark as spam"
msgstr "Отметить как спам"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all_oe_structure_forum_all_top
msgid "Meet our community members"
msgstr "Познакомьтесь с членами нашего сообщества"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_has_error
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_has_error
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_has_error
msgid "Message Delivery error"
msgstr "Ошибка доставки сообщения"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_ids
msgid "Messages"
msgstr "Сообщения"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__mode
msgid "Mode"
msgstr "Режим"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_moderate
msgid "Moderate posts"
msgstr "Модерировать публикации"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_body
msgid "Moderation tools"
msgstr "Инструменты модерации"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "More over:"
msgstr "Больше:"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "Most Used Tags"
msgstr "Самые используемые теги"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__default_order__vote_count_desc
msgid "Most Voted"
msgstr "Самые популярные"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "Most used Tags"
msgstr "Самые используемые теги"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__tag_most_used_ids
msgid "Most used tags"
msgstr "Самые используемые теги"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "My Favorites"
msgstr "Мое избранное"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_body
msgid "My Posts"
msgstr "Мои публикации"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__user_vote
msgid "My Vote"
msgstr "Мой голос"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_body
msgid "My forums"
msgstr "Мои форумы"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_header
msgid "My profile"
msgstr "Мой профиль"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__name
msgid "Name"
msgstr "Имя"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_votes
msgid "Negative vote"
msgstr "Голос против"

#. module: website_forum
#: model:mail.message.subtype,description:website_forum.mt_answer_new
#: model:mail.message.subtype,name:website_forum.mt_answer_new
#: model:mail.message.subtype,name:website_forum.mt_forum_answer_new
msgid "New Answer"
msgstr "Новый ответ"

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.forum_forum_action_add
msgid "New Forum"
msgstr "Новый форум"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "New Post"
msgstr "Новый пост"

#. module: website_forum
#: model:mail.message.subtype,description:website_forum.mt_question_new
#: model:mail.message.subtype,name:website_forum.mt_forum_question_new
#: model:mail.message.subtype,name:website_forum.mt_question_new
msgid "New Question"
msgstr "Новый вопрос"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__default_order__create_date_desc
msgid "Newest"
msgstr "Новые"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_2
#: model:gamification.challenge,name:website_forum.challenge_nice_answer
msgid "Nice Answer"
msgstr "Хороший ответ"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_nice_answer
msgid "Nice Answer (4)"
msgstr "Хороший ответ (4)"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_8
#: model:gamification.challenge,name:website_forum.challenge_nice_question
msgid "Nice Question"
msgstr "Хороший вопрос"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all
msgid "No forum is available yet."
msgstr "Форум пока не доступен."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all_all_entries
msgid "No posts yet"
msgstr "Страница не указана"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_dofollow
msgid "Nofollow links"
msgstr "Не следовать ссылкам"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_4
msgid "Not a real post"
msgstr "Не настоящий пост"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_6
msgid "Not relevant or out dated"
msgstr "Неактуально или устарело"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_2
#: model:gamification.challenge,name:website_forum.challenge_notable_question
msgid "Notable Question"
msgstr "Примечательный вопрос"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_needaction_counter
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_needaction_counter
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_needaction_counter
msgid "Number of Actions"
msgstr "Число действий"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__posts_count
msgid "Number of Posts"
msgstr "Количество записей"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_has_error_counter
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_has_error_counter
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_has_error_counter
msgid "Number of errors"
msgstr "Число ошибок"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__count_flagged_posts
msgid "Number of flagged posts"
msgstr "Количество помеченных публикаций"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_needaction_counter
#: model:ir.model.fields,help:website_forum.field_forum_post__message_needaction_counter
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Количество сообщений, требующих принятия мер"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_has_error_counter
#: model:ir.model.fields,help:website_forum.field_forum_post__message_has_error_counter
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Количество недоставленных сообщений"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__count_posts_waiting_validation
msgid "Number of posts waiting for validation"
msgstr "Количество публикаций в ожидании проверки"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_2
msgid "Off-topic or not relevant"
msgstr "Не по теме или не имеет отношения к делу"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post__state__offensive
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post_reason__reason_type__offensive
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
#: model_terms:ir.ui.view,arch_db:website_forum.mark_as_offensive
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Offensive"
msgstr "Оскорбительное"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
msgid "Offensive Post"
msgstr "Оскорбительный пост"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Offensive Posts"
msgstr "Наступательные посты"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
msgid "Oh no! Please <a href=\"%s\">sign in</a> to perform this action"
msgstr ""

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
msgid "On average,"
msgstr "В среднем,"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "Oops!"
msgstr "Упс!"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
msgid "Options"
msgstr "Опции"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
msgid "Order and Visibility"
msgstr "Порядок и наглядность"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_23
#: model:gamification.challenge,name:website_forum.challenge_peer_pressure
#: model:gamification.goal.definition,name:website_forum.definition_peer_pressure
msgid "Peer Pressure"
msgstr "Давление сверстников"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__plain_content
msgid "Plain Content"
msgstr "Обычное содержание"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ""
"Please wait for a moderator to validate your previous post before "
"continuing."
msgstr ""
"Пожалуйста, подождите, пока модератор подтвердит ваше предыдущее сообщение, "
"прежде чем продолжить."

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_1
#: model:gamification.challenge,name:website_forum.challenge_popular_question
msgid "Popular Question"
msgstr "Популярный вопрос"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_popular_question
msgid "Popular Question (150)"
msgstr "Популярные вопросы (150)"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_notable_question
msgid "Popular Question (250)"
msgstr "Популярные вопросы (250)"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_famous_question
msgid "Popular Question (500)"
msgstr "Популярные вопросы (500)"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_votes
msgid "Positive vote"
msgstr "Голос за"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__post_id
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_search
msgid "Post"
msgstr "Запись"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
msgid "Post Answer"
msgstr "Опубликовать ответ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__child_ids
msgid "Post Answers"
msgstr "Ответы на сообщения"

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.forum_post_reason_action
msgid "Post Close Reason"
msgstr "Причина закрытия поста"

#. module: website_forum
#: model:ir.model,name:website_forum.model_forum_post_reason
msgid "Post Closing Reason"
msgstr "Причина закрытия"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.snippet_options
msgid "Post Count"
msgstr "Число сообщений"

#. module: website_forum
#: model:ir.model,name:website_forum.model_forum_post_vote
msgid "Post Vote"
msgstr "Пост-голосование"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Post Your Question"
msgstr "Опубликовать свой вопрос"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "Post is closed and marked as offensive content"
msgstr "Сообщение закрыто и помечено как оскорбительное"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "Post is closed and marked as spam"
msgstr "Сообщение закрыто и помечено как спам"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.mark_as_offensive
msgid "Post:"
msgstr "Пост:"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_p_2
msgid "Posted 10 comments"
msgstr "Опубликовано 10 комментариев"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_p_4
msgid "Posted 100 comments"
msgstr "Опубликовано 100 комментариев"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "Posting answer on a [Deleted] or [Closed] question is not possible."
msgstr "Размещение ответа на вопрос [Удален] или [Закрыт] невозможно."

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.forum_post_action_forum_main
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__post_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__post_ids
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_search
msgid "Posts"
msgstr "Записи"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__privacy
msgid "Privacy"
msgstr "Конфиденциальность"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__privacy__public
msgid "Public"
msgstr "Публичный"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__privacy
msgid ""
"Public: Forum is public\n"
"Signed In: Forum is visible for signed in users\n"
"Some users: Forum and their content are hidden for non members of selected group"
msgstr ""
"Публичный: Форум является публичным\n"
"Войти: Форум виден только зарегистрированным пользователям\n"
"Некоторые пользователи: Форум и его содержимое скрыты для пользователей, не входящих в выбранную группу"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_25
#: model:gamification.challenge,name:website_forum.challenge_pundit
#: model:gamification.goal.definition,name:website_forum.definition_pundit
msgid "Pundit"
msgstr "Пандит"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
msgid "Put your answer here."
msgstr "Поместите свой ответ сюда."

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
msgid "Put your question here."
msgstr "Поместите свой вопрос здесь."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__parent_id
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Question"
msgstr "Вопрос"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post_vote.py:0
msgid "Question %s"
msgstr "Вопрос %s"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
#: model:mail.message.subtype,description:website_forum.mt_question_edit
#: model:mail.message.subtype,name:website_forum.mt_question_edit
msgid "Question Edited"
msgstr "Вопрос отредактирован"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_question_downvote
msgid "Question downvoted"
msgstr "Вопрос проголосован против"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.404
msgid "Question not found!"
msgstr "Вопрос не найден!"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_4
msgid "Question set as favorite by 1 user"
msgstr "Вопрос установили как любимый 1 пользователь"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_6
msgid "Question set as favorite by 25 users"
msgstr "Вопрос установили как любимый 25 пользователей"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_5
msgid "Question set as favorite by 5 users"
msgstr "Вопрос установили как любимый 5 пользователей"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/controllers/website_forum.py:0
msgid "Question should not be empty."
msgstr "Вопрос не должен быть пустым."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_question_upvote
msgid "Question upvoted"
msgstr "Вопрос с за голосами"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_10
msgid "Question voted up 15 times"
msgstr "За вопрос проголосовали За 15 раз"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_8
msgid "Question voted up 4 times"
msgstr "За вопрос проголосовали За 4 раза"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_9
msgid "Question voted up 6 times"
msgstr "За вопрос проголосовали За 6 раз"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Question:"
msgstr "Вопрос:"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Questions"
msgstr "Вопросы"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__mode__questions
msgid "Questions (1 answer)"
msgstr "Вопросы (1 ответ)"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__mode
msgid ""
"Questions mode: only one answer allowed\n"
" Discussions mode: multiple answers allowed"
msgstr ""
"Режим вопросов: разрешен только один ответ\n"
" Режим обсуждения: разрешено несколько ответов"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_15
msgid "Racist and hate speech"
msgstr "Расизм и разжигание ненависти"

#. module: website_forum
#: model:ir.ui.menu,name:website_forum.menu_forum_rank_global
msgid "Ranks"
msgstr "Ранги"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__rating_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_post__rating_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__rating_ids
msgid "Ratings"
msgstr "Рейтинги"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "Re: %s"
msgstr "Ответ: %s"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Read: #{question.name}"
msgstr "Читать: #{question.name}"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__closed_reason_id
msgid "Reason"
msgstr "Причина"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__reason_type
msgid "Reason Type"
msgstr "Тип причины"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.mark_as_offensive
msgid "Reason:"
msgstr "Причина:"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_reason_view_list
msgid "Reasons"
msgstr "Причины"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_1
msgid "Received at least 3 upvote for an answer for the first time"
msgstr "Получено 3 За голоса для ответа на первый раз"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Refuse"
msgstr "Отказаться"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_related_posts
msgid "Related Posts"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__relevancy
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__default_order__relevancy_desc
msgid "Relevance"
msgstr "Актуальность"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
msgid "Relevance Computation"
msgstr "Вычисление актуальности"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "Remove validated answer"
msgstr "Удалить подтвержденный ответ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
#: model_terms:ir.ui.view,arch_db:website_forum.post_dropdown
msgid "Reopen"
msgstr "Возобновить"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "Reopen a banned question"
msgstr "Повторное открытие запрещенного вопроса"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
#: model_terms:ir.ui.view,arch_db:website_forum.forum_related_posts
#: model_terms:ir.ui.view,arch_db:website_forum.post_stats
msgid "Replies"
msgstr "Ответы"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_stats
msgid "Reply"
msgstr "Ответить"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/controllers/website_forum.py:0
msgid "Reply should not be empty."
msgstr "Ответ не должен быть пустым."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__self_reply
msgid "Reply to own question"
msgstr "Ответить на собственный вопрос"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__website_id
#: model:ir.model.fields,help:website_forum.field_forum_post__website_id
msgid "Restrict to a specific website."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.profile_access_denied
msgid "Return to the forum"
msgstr "Вернуться на форум"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__moderator_id
msgid "Reviewed by"
msgstr "Отзыв от"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__is_seo_optimized
#: model:ir.model.fields,field_description:website_forum.field_forum_post__is_seo_optimized
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__is_seo_optimized
msgid "SEO optimized"
msgstr "SEO-оптимизированный"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_has_sms_error
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_has_sms_error
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Ошибка доставки SMS"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Save Changes"
msgstr "Сохранить изменения"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_26
#: model:gamification.challenge,name:website_forum.challenge_scholar
#: model:gamification.goal.definition,name:website_forum.definition_scholar
msgid "Scholar"
msgstr "Учащийся"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_search
msgid "Search in Post"
msgstr "Поиск в посте"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Search..."
msgstr "Поиск..."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__relevancy_time_decay
msgid "Second Relevance Parameter"
msgstr "Второй параметр релевантности"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/res_users.py:0
msgid "See our Forum"
msgstr "Смотрите наш форум"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_answer
msgid "See post"
msgstr "Следующая запись"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_question
msgid "See question"
msgstr "Смотрить вопрос"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Select All"
msgstr "Выбрать все"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_8
#: model:gamification.challenge,name:website_forum.challenge_self_learner
#: model:gamification.goal.definition,name:website_forum.definition_self_learner
msgid "Self-Learner"
msgstr "Самоучка"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__seo_name
#: model:ir.model.fields,field_description:website_forum.field_forum_post__seo_name
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__seo_name
msgid "Seo name"
msgstr "Имя Seo"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__sequence
msgid "Sequence"
msgstr "Последовательность"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_forum.py:0
#: model_terms:forum.forum,welcome_message:website_forum.forum_help
msgid ""
"Share and discuss the best content and new marketing ideas, build your "
"professional profile and become a better marketer together."
msgstr ""
"Делитесь и обсуждайте лучший контент и новые маркетинговые идеи, повышайте "
"свой профессиональный уровень и становитесь лучшим маркетологом вместе."

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
msgid ""
"Share this content to increase your chances to be featured on the front page"
" and attract more visitors."
msgstr ""
"Поделитесь этой ссылкой на этот материал, чтобы увеличить ваши шансы быть "
"размещеным на главной странице и привлечь больше посетителей."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__allow_share
msgid "Sharing Options"
msgstr "Настройки Поделиться"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index_tags
msgid "Show Tags Starting with"
msgstr "Показать теги, начинающиеся с"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "Showing results for"
msgstr "Отображение результатов для"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_forum.py:0
#: model_terms:forum.forum,welcome_message:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.forum_sign_up_cta
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_header
msgid "Sign up"
msgstr "Регистрация"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__privacy__connected
msgid "Signed In"
msgstr "Вошел"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "Solved"
msgstr "Решено"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__privacy__private
msgid "Some users"
msgstr "Некоторые пользователи"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.404
msgid "Sorry, this question is not available anymore."
msgstr "Извините, этот вопрос больше не доступен."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "Sorry, we could not find any <b>solved</b> results"
msgstr "К сожалению, мы не нашли ни одного <b>решенного</b> результата"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "Sorry, we could not find any <b>unanswered</b> results"
msgstr ""
"Извините, мы не нашли результатов, на которые можно было бы <b>ответить</b> "

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "Sorry, we could not find any <b>unsolved</b> results"
msgstr "К сожалению, мы не нашли ни одного <b>нерешенного</b> результата"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "Sorry, we could not find any results"
msgstr "К сожалению, мы не нашли результатов"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
msgid "Sorry, you cannot select your own posts as best answer"
msgstr ""
"К сожалению, вы не можете выбрать свои собственные сообщения в качестве "
"лучшего ответа"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
msgid "Sorry, you cannot vote for your own posts"
msgstr "Извините, вы не можете голосовать за свои собственные сообщения"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Spam all post"
msgstr "Спам во всех сообщениях"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_8
msgid "Spam or advertising"
msgstr "Спам или реклама"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "Start by creating a post"
msgstr "Начните с создания поста"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__state
msgid "Status"
msgstr "Статус"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_6
#: model:gamification.challenge,name:website_forum.challenge_stellar_question_25
msgid "Stellar Question"
msgstr "Звездный вопрос"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_7
#: model:gamification.challenge,name:website_forum.challenge_student
msgid "Student"
msgstr "Студент"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.follow
msgid "Subscribe"
msgstr "Подписаться"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_31
#: model:gamification.challenge,name:website_forum.challenge_supporter
#: model:gamification.goal.definition,name:website_forum.definition_supporter
msgid "Supporter"
msgstr "Сторонник"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_search
#: model_terms:ir.ui.view,arch_db:website_forum.forum_tag_view_form
msgid "Tag"
msgstr "Тег"

#. module: website_forum
#: model:ir.model.constraint,message:website_forum.constraint_forum_tag_name_uniq
msgid "Tag name already exists!"
msgstr "Название тега уже существует!"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__tag_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_post__tag_ids
#: model:ir.ui.menu,name:website_forum.menu_forum_tag_global
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index_tags
#: model_terms:ir.ui.view,arch_db:website_forum.forum_tag_view_list
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_body
msgid "Tags"
msgstr "Теги"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all_oe_structure_forum_all_top
msgid ""
"Tap into the collective knowledge of our community by asking your questions "
"in our forums,<br/> where helpful members are ready to assist you."
msgstr ""
"Воспользуйтесь коллективными знаниями нашего сообщества, задавая вопросы на "
"наших форумах<br/>, где отзывчивые пользователи готовы помочь вам."

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_32
#: model:gamification.challenge,name:website_forum.challenge_taxonomist
#: model:gamification.goal.definition,name:website_forum.definition_taxonomist
msgid "Taxonomist"
msgstr "Таксономист"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_1
#: model:gamification.challenge,name:website_forum.challenge_teacher
#: model:gamification.goal.definition,name:website_forum.definition_teacher
msgid "Teacher"
msgstr "Преподаватель"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__teaser
msgid "Teaser"
msgstr "Тизер"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
msgid "Thanks for posting!"
msgstr "Спасибо за публикацию!"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "The accepted answer is deleted"
msgstr "Принятый ответ удаляется"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid ""
"The goal of this site is create a relevant knowledge base that would answer "
"questions related to Odoo."
msgstr ""
"Цель этого сайта - создать актуальную базу знаний, которая будет отвечать на"
" вопросы, связанные с Odoo."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "The question has been closed"
msgstr "Вопрос закрыт"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "There are no answers yet"
msgstr "Ответов пока нет"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index_tags
msgid "There are no tags being used in this forum."
msgstr "На этом форуме не используются теги."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index_tags
msgid "There are no tags matching the selected filter"
msgstr "Нет тегов, соответствующих выбранному фильтру"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index_tags
msgid "There are no tags matching the selected search."
msgstr "Нет тегов, соответствующих выбранному поиску."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid ""
"Therefore questions and answers can be edited like wiki pages by experienced"
" users of this site in order to improve the overall quality of the knowledge"
" base content. Such privileges are granted based on user karma level: you "
"will be able to do the same once your karma gets high enough."
msgstr ""
"Поэтому вопросы и ответы могут редактироваться опытными пользователями этого"
" сайта как вики-страницы, чтобы улучшить общее качество содержимого базы "
"знаний. Такие привилегии предоставляются в зависимости от уровня кармы "
"пользователя: вы сможете делать то же самое, как только ваша карма станет "
"достаточно высокой."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"This community is for professional and enthusiast users, partners and "
"programmers. You can ask questions about:"
msgstr ""
"Это сообщество предназначено для профессиональных пользователей и "
"энтузиастов, партнеров и программистов. Вы можете задать вопросы о:"

#. module: website_forum
#: model:forum.forum,description:website_forum.forum_help
msgid ""
"This community is for professionals and enthusiasts of our products and "
"services. Share and discuss the best content and new marketing ideas, build "
"your professional profile and become a better marketer together."
msgstr ""
"Это сообщество для профессионалов и энтузиастов наших продуктов и услуг. "
"Делитесь и обсуждайте лучший контент и новые маркетинговые идеи, построите "
"свой профессиональный профиль и станьте лучшим маркетологом вместе."

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__relevancy_post_vote
msgid ""
"This formula is used in order to sort by relevance. The variable 'votes' "
"represents number of votes for a post, and 'days' is number of days since "
"the post creation"
msgstr ""
"Эта формула используется для того, чтобы сортировать по релевантности. "
"Переменная 'голосов' представляет число голосов на публикацию, и 'дней' "
"является количество дней с момента создания публикации"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "This forum has been archived."
msgstr "Этот форум был отправлен в архив."

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
msgid "This post can not be flagged"
msgstr "Это сообщение не может быть отмечено"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
msgid "This post is already flagged"
msgstr "Это сообщение уже отмечено"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "This post is awaiting validation"
msgstr "Это сообщение ожидает проверки"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid ""
"This post is currently awaiting moderation and is not published yet.<br/>\n"
"                    As a moderator you can either <b>Accept</b> or <b>Reject</b> this post."
msgstr ""
"Это сообщение в настоящее время ожидает модерации и еще не опубликовано.<br/>\n"
"                    Как модератор вы можете <b>принять</b> или <b>отклонить</b> это сообщение."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "This question has been flagged"
msgstr "Этот вопрос был отмечен"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "This user hasn't answered any questions yet. <br/>"
msgstr "Этот пользователь еще не ответил ни на один вопрос. <br/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "This user hasn't posted any questions yet.<br/>"
msgstr "Этот пользователь еще не задавал вопросов.<br/>"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_14
msgid "Threatening language"
msgstr "Угрожающие высказывания"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__name
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Title"
msgstr "Заголовок"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Title must not be empty"
msgstr "Название не должно быть пустым"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/controllers/website_forum.py:0
msgid "Title should not be empty."
msgstr "Заголовок не должен быть пустым."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__recipient_id
msgid "To"
msgstr "Кому"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_body
msgid "To Validate"
msgstr "Проверить"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"To prevent your question from being flagged and possibly removed, avoid "
"asking subjective questions where …"
msgstr ""
"Чтобы ваш вопрос не был отмечен и, возможно, удален, избегайте задавать "
"субъективные вопросы, в которых .."

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_9
msgid "Too localized"
msgstr "Слишком локализовано"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_3
msgid "Too subjective and argumentative"
msgstr "Слишком субъективно и спорно"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index_tags
msgid "Toolbar with button groups"
msgstr "Панель инструментов с группами кнопок"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_tree
msgid "Total Answers"
msgstr "Всего Ответов"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_tree
msgid "Total Posts"
msgstr "Всего записей"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_tree
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_tree
msgid "Total Views"
msgstr "Всего просмотров"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__vote_count
msgid "Total Votes"
msgstr "Всего голосов"

#. module: website_forum
#: model:ir.model,name:website_forum.model_gamification_karma_tracking
msgid "Track Karma Changes"
msgstr "Отслеживание изменений кармы"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid "Twitter"
msgstr "Twitter"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "Unanswered"
msgstr "Без ответов"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_dropdown
msgid "Undelete"
msgstr "Восстановить"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.follow
msgid "Unfollow"
msgstr "Отписаться"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid "Unmark as Best Answer"
msgstr "Отметить как лучший ответ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "Unsolved"
msgstr "Нерешено"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "Unused Tags"
msgstr "Неиспользуемые теги"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__tag_unused_ids
msgid "Unused tags"
msgstr "Неиспользованные теги"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__write_uid
msgid "Updated by"
msgstr "Обновлено"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__write_date
msgid "Updated on"
msgstr "Обновлено"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_upvote
#: model_terms:ir.ui.view,arch_db:website_forum.vote
msgid "Upvote"
msgstr "За"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_student
msgid "Upvoted question (1)"
msgstr "Вопрос проголосовали (1)"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_great_question
msgid "Upvoted question (15)"
msgstr "Вопрос, за который проголосовали (15)"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_nice_question
msgid "Upvoted question (4)"
msgstr "Вопрос проголосовали (4)"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_good_question
msgid "Upvoted question (6)"
msgstr "Вопрос, за который проголосовали (6)"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Use a clear, explicit and concise title"
msgstr "Используйте четкое, ясное и лаконичное название"

#. module: website_forum
#: model:ir.model,name:website_forum.model_res_users
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__user_id
msgid "User"
msgstr "Пользователь"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "User answer accepted"
msgstr "Ответ пользователя принят"

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.forum_post_action_favorites
msgid "Users favorite posts"
msgstr "Любимые посты пользователей"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
#: model_terms:ir.ui.view,arch_db:website_forum.post_dropdown
#: model_terms:ir.ui.view,arch_db:website_forum.show_flag_validator
msgid "Validate"
msgstr "Подтвердить"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "Validate an answer"
msgstr "Удостоверьтесь в правильности ответа"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_validation
msgid "Validate question"
msgstr "Удостоверить вопрос"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_stats
msgid "View"
msgstr "Просмотр"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid "View my answer <i class=\"oi oi-arrow-right ms-1\"/>"
msgstr "Посмотреть мой ответ <i class=\"oi oi-arrow-right ms-1\"/>"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__views
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
#: model_terms:ir.ui.view,arch_db:website_forum.forum_related_posts
#: model_terms:ir.ui.view,arch_db:website_forum.post_stats
msgid "Views"
msgstr "Просмотры"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_12
msgid "Violent language"
msgstr "Язык насилия"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__vote
msgid "Vote"
msgstr "Голос"

#. module: website_forum
#: model:ir.model.constraint,message:website_forum.constraint_forum_post_vote_vote_uniq
msgid "Vote already exists!"
msgstr "Голос уже существует!"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__vote_ids
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Votes"
msgstr "Голоса"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post__state__pending
msgid "Waiting Validation"
msgstr "Ожидает подтверждения"

#. module: website_forum
#: model:ir.model,name:website_forum.model_website
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_id
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_id
msgid "Website"
msgstr "Сайт"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__gamification_challenge__challenge_category__forum
msgid "Website / Forum"
msgstr "Сайт / Форум"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_message_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_message_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_message_ids
msgid "Website Messages"
msgstr "Веб-сайт сообщения"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_url
msgid "Website URL"
msgstr "Адрес сайта"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__website_message_ids
#: model:ir.model.fields,help:website_forum.field_forum_post__website_message_ids
#: model:ir.model.fields,help:website_forum.field_forum_tag__website_message_ids
msgid "Website communication history"
msgstr "История общений с сайта"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_meta_description
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_meta_description
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_meta_description
msgid "Website meta description"
msgstr "Мета-описание веб-сайта"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_meta_keywords
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_meta_keywords
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_meta_keywords
msgid "Website meta keywords"
msgstr "Мета-ключевые слова сайта"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_meta_title
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_meta_title
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_meta_title
msgid "Website meta title"
msgstr "Мета-название Веб-сайта"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_meta_og_img
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_meta_og_img
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_meta_og_img
msgid "Website opengraph image"
msgstr "Изображение на сайте opengraph"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__welcome_message
msgid "Welcome Message"
msgstr "Приветственное сообщение"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_forum.py:0
#: model_terms:forum.forum,welcome_message:website_forum.forum_help
msgid "Welcome!"
msgstr "Добро пожаловать!"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"When a question or answer is upvoted, the user who posted them will gain "
"some points, which are called \"karma points\". These points serve as a "
"rough measure of the community trust to him/her. Various moderation tasks "
"are gradually assigned to the users based on those points."
msgstr ""
"Если вопрос или ответ на него проголосовали, пользователь, опубликовавший "
"их, получает некоторое количество баллов, которые называются \"баллами "
"кармы\". Эти баллы служат приблизительной мерой доверия сообщества к нему. "
"На основе этих баллов пользователям постепенно назначаются различные "
"модераторские задания."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Write a clear, explicit and concise title"
msgstr "Напишите четкий, ясный и лаконичный заголовок"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_header
msgid "XP"
msgstr "XP"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "You already have a pending post"
msgstr "У вас уже есть отложенное сообщение"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
msgid "You can share your question once it has been validated"
msgstr ""
"Вы можете поделиться своим вопросом после того, как он будет подтвержден"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid "You can't vote for your own post"
msgstr "Вы не можете голосовать за свой собственный пост"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "You cannot create recursive forum posts."
msgstr "Вы не можете создавать рекурсивные сообщения на форуме."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
msgid "You cannot post an empty answer"
msgstr "Вы не можете опубликовать пустой ответ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid "You don't have enough karma"
msgstr "У вас недостаточно кармы"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "You have a pending post"
msgstr "У вас есть отложенное сообщение"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "You have not answered any questions yet. <br/>"
msgstr "Вы еще не ответили ни на один вопрос. <br/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "You have not posted any questions yet. <br/>"
msgstr "Вы еще не задавали вопросов. <br/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_votes
msgid "You haven't given any votes yet."
msgstr "Вы еще не отдали ни одного голоса."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "You may now participate in our forums."
msgstr "Теперь вы можете участвовать в наших форумах."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "You need to have sufficient karma to edit tags"
msgstr "У вас должно быть достаточно кармы, чтобы редактировать теги"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"You should only ask practical, answerable questions based on actual problems"
" that you face. Chatty, open-ended questions diminish the usefulness of this"
" site and push other questions off the front page."
msgstr ""
"Вы должны задавать только практические вопросы, на которые можно ответить, "
"основываясь на реальных проблемах, с которыми вы сталкиваетесь. Болтливые, "
"открытые вопросы снижают полезность этого сайта и вытесняют другие вопросы с"
" первой страницы."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "You're following this post"
msgstr "Вы следите за этим постом"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "You've Completely Caught&amp;nbsp;Up!"
msgstr "Вы полностью попали&amp;nbsp;в точку!"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
msgid "Your Answer"
msgstr "Ваш ответ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "Your favourite"
msgstr "Ваш любимый"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "accept any answer"
msgstr "принять любой ответ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "and join this Forum"
msgstr "и присоединяйтесь к этому форуму"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index_tags
msgid "and search"
msgstr "и поиск"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.author_box
msgid "at"
msgstr "в"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "breadcrumb"
msgstr "хлебные крошки"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "by"
msgstr "от"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "close any posts"
msgstr "закрыть все сообщения"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "delete any comment"
msgstr "удалить любой комментарий"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "delete any question or answer"
msgstr "удалите любой вопрос или ответ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "delete own comment"
msgstr "удалить собственный комментарий"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "downvote"
msgstr "против"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post_vote.py:0
msgid "downvoted"
msgstr "против"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
msgid "e.g. Help"
msgstr "например, помощь"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form_add
msgid "e.g. Technical Assistance"
msgstr "например, техническая помощь"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_form
msgid "e.g. When should I plant my tomatoes?"
msgstr "например, \"Когда сажать помидоры?"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "edit any post, view offensive flags"
msgstr "редактировать любые публикации, просматривать оскорбительные флаги"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "every answer is equally valid: “What’s your favorite ______?”"
msgstr "каждый ответ одинаково верен: \"Какой ваш любимый ______?\""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "flag offensive, close own questions"
msgstr "флаг оскорбления, закрыть собственные вопросы"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "for reason:"
msgstr "по причине:"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_validation
msgid ""
"has been posted and require your validation. Click here to access the "
"question :"
msgstr ""
"была опубликован и требуют вашей проверки. Нажмите здесь, чтобы получить "
"доступ к вопросу:"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_answer
msgid "has been posted. Click here to access the post :"
msgstr "был опубликован. Нажмите здесь, чтобы получить доступ к посту:"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_question
msgid "has been posted. Click here to access the question :"
msgstr "был опубликован. Нажмите здесь, чтобы получить доступ к вопросу:"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
msgid "here"
msgstr "здесь"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "how to configure or customize Odoo to specific business needs,"
msgstr ""
"как сконфигурировать или настроить Odoo для конкретных деловых потребностей,"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "how to develop modules for your own need,"
msgstr "как разрабатывать модули для собственных нужд,"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "how to install Odoo on a specific infrastructure,"
msgstr "как установить Odoo на конкретную инфраструктуру,"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
msgid ""
"if your\n"
"        answer is selected as the right one. See what you can do with karma"
msgstr ""
"если ваш\n"
"       ответ будет выбран в качестве правильное. Посмотрите, что вы можете сделать с кармой"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "in your favourites"
msgstr "в избранное"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "in your posts"
msgstr "в ваших постах"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "insert text link, upload files"
msgstr "вставьте текст ссылки, загрузить файлы"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "instead."
msgstr "вместо этого."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "it is a rant disguised as a question: “______ sucks, am I right?”"
msgstr "это громкие слова, замаскированный под вопрос: \"______ отстой, я прав?\""

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
msgid "karma is required to perform this action. "
msgstr "для выполнения этого действия требуется карма."

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
msgid "karma points"
msgstr "баллы кармы"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "matching \""
msgstr "соответствие \""

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post_vote.py:0
msgid "no changes"
msgstr "без изменений"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post_vote.py:0
msgid "no more downvoted"
msgstr "больше не будет голосовать"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post_vote.py:0
msgid "no more upvoted"
msgstr "больше не голосовать"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_question
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_validation
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "on"
msgstr "вкл"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
msgid ""
"on social networks get an answer within\n"
"        5 hours. Questions shared on two social networks have"
msgstr ""
"в социальных сетях получают ответ в течение\n"
"       5 часов. Вопросы, делились на две социальные сети"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all_all_entries
msgid "post"
msgstr "запись"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all_all_entries
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "posts"
msgstr "записи"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "specific questions about Odoo service offers, etc."
msgstr "конкретные вопросы о предложениях услуг Odoo и т.д."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "tag"
msgstr "тег"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index_tags
msgid "tags"
msgstr "теги"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"there is no actual problem to be solved: “I’m curious if other people feel "
"like I do.”"
msgstr ""
"нет никаких фактических проблем, чтобы решить: “Мне любопытно, если другие "
"люди чувствуют себя, как я.”"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "upvote, add comments"
msgstr "проголосовать, добавить комментарий"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post_vote.py:0
msgid "upvoted"
msgstr "за"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "using the"
msgstr "используя"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"we are being asked an open-ended, hypothetical question: “What if ______ "
"happened?”"
msgstr ""
"нам задают конечный, гипотетический вопрос: «Что делать, если ______ "
"случилось?»"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "what's the best way to use Odoo for a specific business need,"
msgstr ""
"какой самый лучший способ использовать Odoo для определенной потребности "
"бизнеса,"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.author_box
msgid "xp"
msgstr "xp"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"your answer is provided along with the question, and you expect more "
"answers: “I use ______ for ______, what do you use?”"
msgstr ""
"ваш ответ предоставляется вместе с вопросом, и вы ожидаете больше ответов: "
"«Я использую ______ для ______, что используете вы?»"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "your biography can be seen as tooltip"
msgstr "ваша биография может быть показана, как всплывающая подсказка"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.follow
msgid "your email..."
msgstr "ваш e-mail..."
