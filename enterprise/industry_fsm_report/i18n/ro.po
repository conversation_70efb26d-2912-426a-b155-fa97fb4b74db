# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* industry_fsm_report
# 
# Translators:
# <PERSON><PERSON><PERSON>, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:27+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: industry_fsm_report
#. odoo-javascript
#: code:addons/industry_fsm_report/static/src/js/tours/industry_fsm_report_tour.js:0
msgid "<b>Review and sign</b> the <b>task report</b> with your customer."
msgstr ""

#. module: industry_fsm_report
#. odoo-javascript
#: code:addons/industry_fsm_report/static/src/js/tours/industry_fsm_report_tour.js:0
msgid "<b>Send your task report</b> to your customer."
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.view_project_kanban_inherit_industry_fsm_report
msgid ""
"<span class=\"fa fa-pencil me-1\" aria-label=\"Worksheet Template\" "
"title=\"Worksheet Template\"/>"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.view_worksheet_template_kanban
msgid "<span class=\"o_label ms-2\">Worksheets</span>"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.view_task_form2_inherit
msgid "<span class=\"o_stat_text\">Worksheet</span>"
msgstr "<span class=\"o_stat_text\">Foaie de lucru</span>"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.view_task_form2_inherit
msgid ""
"<span class=\"o_stat_text\">Worksheet</span>\n"
"                            <span class=\"o_stat_text\">Complete</span>"
msgstr ""
"<span class=\"o_stat_text\">Foaie de lucru</span>\n"
"                            <span class=\"o_stat_text\">Completă</span>"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.project_task_view_gantt_fsm_worksheet
msgid "<strong>Worksheet Template — </strong>"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.report_custom_x_project_task_worksheet_template_3
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.x_project_task_worksheet_template_3_ir_ui_view_1
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.x_project_task_worksheet_template_4_ir_ui_view_1
msgid "Add details about your intervention..."
msgstr "Adaugă detalii despre intervenție.."

#. module: industry_fsm_report
#: model:ir.model,name:industry_fsm_report.model_project_task_burndown_chart_report
msgid "Burndown Chart"
msgstr ""

#. module: industry_fsm_report
#: model:ir.ui.menu,name:industry_fsm_report.project_task_menu_planning_by_project_fsm
msgid "By Worksheet Template"
msgstr ""

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.x_project_task_worksheet_template_3_ir_model_fields_7
#: model:ir.model.fields,field_description:industry_fsm_report.x_project_task_worksheet_template_4_ir_model_fields_7
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.report_custom_x_project_task_worksheet_template_3
msgid "Comments"
msgstr "Comentarii"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.project_project_form_inherit_industry_fsm_report
msgid "Complete custom worksheets for your interventions"
msgstr ""

#. module: industry_fsm_report
#: model:ir.model.fields,help:industry_fsm_report.field_project_task__worksheet_template_id
msgid ""
"Create templates for each type of intervention you have and customize their "
"content with your own custom fields."
msgstr ""

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.field_worksheet_template_load_wizard__create_uid
#: model:ir.model.fields,field_description:industry_fsm_report.x_project_task_worksheet_template_3_ir_model_fields_2
#: model:ir.model.fields,field_description:industry_fsm_report.x_project_task_worksheet_template_4_ir_model_fields_2
msgid "Created by"
msgstr "Creat de"

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.field_worksheet_template_load_wizard__create_date
#: model:ir.model.fields,field_description:industry_fsm_report.x_project_task_worksheet_template_3_ir_model_fields_1
#: model:ir.model.fields,field_description:industry_fsm_report.x_project_task_worksheet_template_4_ir_model_fields_1
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.x_project_task_worksheet_template_3_ir_ui_view_3
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.x_project_task_worksheet_template_4_ir_ui_view_3
msgid "Created on"
msgstr "Creat pe"

#. module: industry_fsm_report
#. odoo-javascript
#: code:addons/industry_fsm_report/static/src/js/tours/industry_fsm_report_tour.js:0
msgid "Customize your <b>layout</b>."
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.report_custom_x_project_task_worksheet_template_4
msgid "Date"
msgstr "Dată"

#. module: industry_fsm_report
#: model:ir.model,name:industry_fsm_report.x_project_task_worksheet_template_3_ir_model_1
msgid "Default Worksheet"
msgstr "Foaie de Lucru Implicită"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.report_custom_x_project_task_worksheet_template_4
msgid "Description of the Intervention"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.res_config_settings_view_form
msgid "Design worksheet templates"
msgstr ""

#. module: industry_fsm_report
#: model:ir.model,name:industry_fsm_report.x_project_task_worksheet_template_4_ir_model_1
msgid "Device Installation and Maintenance"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_template_view_form_footer_design_button
msgid "Discard"
msgstr "Abandonează"

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.field_worksheet_template_load_wizard__display_name
#: model:ir.model.fields,field_description:industry_fsm_report.x_project_task_worksheet_template_3_ir_model_fields_3
#: model:ir.model.fields,field_description:industry_fsm_report.x_project_task_worksheet_template_4_ir_model_fields_3
msgid "Display Name"
msgstr "Nume afișat"

#. module: industry_fsm_report
#. odoo-python
#: code:addons/industry_fsm_report/models/project_task.py:0
msgid "Explore Worksheets Using an Example Template"
msgstr ""

#. module: industry_fsm_report
#. odoo-javascript
#: code:addons/industry_fsm_report/static/src/js/tours/industry_fsm_report_tour.js:0
msgid "Fill in your <b>worksheet</b> with the details of your intervention."
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.view_worksheet_template_load_form
msgid ""
"Give it a try using one of our example templates. You can remove them at any"
" time."
msgstr ""

#. module: industry_fsm_report
#. odoo-javascript
#: code:addons/industry_fsm_report/static/src/js/tours/industry_fsm_report_tour.js:0
msgid "Go back to your Field Service <b>task</b>."
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.report_custom_x_project_task_worksheet_template_4
msgid ""
"I hereby certify that this device meets the requirements of an acceptable "
"device at the time of testing."
msgstr ""
"Certific prin prezenta că acest dispozitiv îndeplinește cerințele unui "
"dispozitiv acceptabil în momentul testării."

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.field_worksheet_template_load_wizard__id
#: model:ir.model.fields,field_description:industry_fsm_report.x_project_task_worksheet_template_3_ir_model_fields_4
#: model:ir.model.fields,field_description:industry_fsm_report.x_project_task_worksheet_template_4_ir_model_fields_4
msgid "ID"
msgstr "ID"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.report_custom_x_project_task_worksheet_template_4
msgid "Intervention Type"
msgstr "Tip Intervenție"

#. module: industry_fsm_report
#. odoo-javascript
#: code:addons/industry_fsm_report/static/src/js/tours/industry_fsm_report_tour.js:0
msgid "Invite your customer to <b>validate and sign your task report</b>."
msgstr ""

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.field_worksheet_template_load_wizard__write_uid
#: model:ir.model.fields,field_description:industry_fsm_report.x_project_task_worksheet_template_3_ir_model_fields_6
#: model:ir.model.fields,field_description:industry_fsm_report.x_project_task_worksheet_template_4_ir_model_fields_6
msgid "Last Updated by"
msgstr "Ultima actualizare făcută de"

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.field_worksheet_template_load_wizard__write_date
#: model:ir.model.fields,field_description:industry_fsm_report.x_project_task_worksheet_template_3_ir_model_fields_5
#: model:ir.model.fields,field_description:industry_fsm_report.x_project_task_worksheet_template_4_ir_model_fields_5
msgid "Last Updated on"
msgstr "Ultima actualizare pe"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.view_worksheet_template_load_form
msgid "Load Template"
msgstr ""

#. module: industry_fsm_report
#: model:ir.model,name:industry_fsm_report.model_worksheet_template_load_wizard
msgid "Load the worksheet template"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.report_custom_x_project_task_worksheet_template_4
msgid "Manufacturer"
msgstr "Producător"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.report_custom_x_project_task_worksheet_template_4
msgid "Model"
msgstr "Model"

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.x_project_task_worksheet_template_3_ir_model_fields_8
#: model:ir.model.fields,field_description:industry_fsm_report.x_project_task_worksheet_template_4_ir_model_fields_8
msgid "Name"
msgstr "Nume"

#. module: industry_fsm_report
#: model_terms:ir.actions.act_window,help:industry_fsm_report.project_task_action_fsm_planning_groupby_worksheet
#: model_terms:ir.actions.act_window,help:industry_fsm_report.project_task_action_fsm_planning_groupby_worksheet2
msgid "No tasks found. Let's create one!"
msgstr "Nu s-au găsit sarcini. Să creăm una!"

#. module: industry_fsm_report
#: model_terms:ir.actions.act_window,help:industry_fsm_report.fsm_worksheets_action_settings
msgid "No worksheet templates found. Let's create one!"
msgstr "Nu s-au găsit șabloane de foaie de lucru. Să creăm unul!"

#. module: industry_fsm_report
#. odoo-javascript
#: code:addons/industry_fsm_report/static/src/js/tours/industry_fsm_report_tour.js:0
msgid ""
"Open your <b>worksheet</b> in order to fill it in with the details of your "
"intervention."
msgstr ""

#. module: industry_fsm_report
#: model:ir.actions.act_window,name:industry_fsm_report.project_task_action_fsm_planning_groupby_worksheet
#: model:ir.actions.act_window,name:industry_fsm_report.project_task_action_fsm_planning_groupby_worksheet2
msgid "Planning by Worksheet Template"
msgstr ""

#. module: industry_fsm_report
#: model:ir.actions.server,name:industry_fsm_report.project_task_fsm_planning_groupby_worksheet_server_action
msgid "Planning by Worksheet Template Server Action"
msgstr ""

#. module: industry_fsm_report
#: model:ir.model,name:industry_fsm_report.model_project_project
msgid "Project"
msgstr "Proiect"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_template_view_form_footer_design_button
msgid "Save & Close"
msgstr "Salvează & Închide"

#. module: industry_fsm_report
#. odoo-javascript
#: code:addons/industry_fsm_report/static/src/js/tours/industry_fsm_report_tour.js:0
msgid "Save time by automatically generating a <b>signature</b>."
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.actions.act_window,help:industry_fsm_report.project_task_action_fsm_planning_groupby_worksheet
#: model_terms:ir.actions.act_window,help:industry_fsm_report.project_task_action_fsm_planning_groupby_worksheet2
msgid "Schedule your tasks and assign them to your workers."
msgstr ""

#. module: industry_fsm_report
#: model:ir.actions.server,name:industry_fsm_report.action_fsm_task_send_report
msgid "Send Report"
msgstr "Trimite raport"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.report_custom_x_project_task_worksheet_template_4
msgid "Serial Number"
msgstr "Număr de serie"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.view_worksheet_template_load_form
msgid "Start Blank"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.view_worksheet_template_load_form
msgid ""
"Tailor worksheet templates to your specific needs. Easily share worksheet "
"content with customers and request their signatures to validate the "
"completed work."
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.actions.act_window,help:industry_fsm_report.fsm_worksheets_action_settings
msgid ""
"Tailor worksheet templates to your specific needs. Easily share worksheet "
"content with customers and request their signatures to validate the "
"completed work.<br>"
msgstr ""

#. module: industry_fsm_report
#: model:ir.model,name:industry_fsm_report.model_project_task
#: model:ir.model.fields,field_description:industry_fsm_report.field_worksheet_template_load_wizard__task_id
#: model:ir.model.fields,field_description:industry_fsm_report.x_project_task_worksheet_template_3_ir_model_fields_9
#: model:ir.model.fields,field_description:industry_fsm_report.x_project_task_worksheet_template_4_ir_model_fields_9
msgid "Task"
msgstr "Sarcină"

#. module: industry_fsm_report
#: model:ir.model,name:industry_fsm_report.model_project_task_recurrence
msgid "Task Recurrence"
msgstr "Repetarea sarcinilor"

#. module: industry_fsm_report
#: model:ir.model,name:industry_fsm_report.model_report_industry_fsm_worksheet_custom
msgid "Task Worksheet Custom Report"
msgstr ""

#. module: industry_fsm_report
#: model:ir.model,name:industry_fsm_report.model_report_project_task_user
msgid "Tasks Analysis"
msgstr "Analiza Sarcinilor"

#. module: industry_fsm_report
#. odoo-javascript
#: code:addons/industry_fsm_report/static/src/js/tours/industry_fsm_report_tour.js:0
msgid "Use the breadcrumbs to return to your <b>task</b>."
msgstr ""

#. module: industry_fsm_report
#. odoo-javascript
#: code:addons/industry_fsm_report/static/src/js/tours/industry_fsm_report_tour.js:0
msgid "Validate the <b>signature</b>."
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.report_custom_x_project_task_worksheet_template_4
msgid "Worker Signature"
msgstr "Semnătură muncitor"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.portal_my_task
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid "Worksheet"
msgstr "Foaie de Lucru"

#. module: industry_fsm_report
#: model:ir.model,name:industry_fsm_report.model_worksheet_template
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_project__worksheet_template_id
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_task__worksheet_template_id
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_task_burndown_chart_report__worksheet_template_id
#: model:ir.model.fields,field_description:industry_fsm_report.field_report_project_task_user__worksheet_template_id
#: model:ir.model.fields,field_description:industry_fsm_report.field_report_project_task_user_fsm__worksheet_template_id
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.project_project_form_inherit_industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.project_task_map_view_inherit_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.project_task_view_search_fsm_report
msgid "Worksheet Template"
msgstr "Șablon fișă de lucru "

#. module: industry_fsm_report
#: model:ir.actions.act_window,name:industry_fsm_report.action_fsm_worksheets
#: model:ir.actions.act_window,name:industry_fsm_report.fsm_worksheets_action_settings
#: model:ir.ui.menu,name:industry_fsm_report.fsm_settings_worksheets
msgid "Worksheet Templates"
msgstr "Șablone Foaie de lucru"

#. module: industry_fsm_report
#: model:ir.actions.act_window,name:industry_fsm_report.x_project_task_worksheet_template_3_ir_actions_act_window_1
#: model:ir.actions.act_window,name:industry_fsm_report.x_project_task_worksheet_template_4_ir_actions_act_window_1
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_project__allow_worksheets
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_task__allow_worksheets
msgid "Worksheets"
msgstr "Foi de lucru"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.project_project_form_inherit_industry_fsm_report
msgid "e.g. Device Installation"
msgstr ""
