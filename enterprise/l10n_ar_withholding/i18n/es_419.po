# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_ar_withholding
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-11-28 09:54+0000\n"
"PO-Revision-Date: 2024-11-28 09:54+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: \n"
"Language: es_419\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: l10n_ar_withholding
#. odoo-python
#: code:addons/l10n_ar_withholding/models/l10n_ar_partner_tax.py:0
msgid "\"From date\" must be lower than \"To date\" on Withholding (AR) taxes."
msgstr ""
"\"<PERSON>cha desde\" debe ser anterior a \"TFecha hasta\" en impuestos de "
"retenciones (AR)."

#. module: l10n_ar_withholding
#: model_terms:ir.ui.view,arch_db:l10n_ar_withholding.view_afip_earnings_table_scale_form
msgid ""
"<b><u>AFIP Sources</u></b><br/>\n"
"                    <span>Calculator of the Withholding Amount: </span>"
msgstr ""
"<b><u>Fuentes de AFIP</u></b><br/>\n"
"                    <span>Calculadora del monto de retención: </span>"

#. module: l10n_ar_withholding
#: model_terms:ir.ui.view,arch_db:l10n_ar_withholding.view_afip_earnings_table_scale_form
msgid "<b><u>Formula Earnings with Scale</u></b><br/>"
msgstr "<b><u>Formula Ganancias con Escala</u></b><br/>"

#. module: l10n_ar_withholding
#: model_terms:ir.ui.view,arch_db:l10n_ar_withholding.view_afip_earnings_table_scale_form
msgid ""
"<br/>\n"
"                    <span>Link to AFIP aditional info: </span>"
msgstr ""
"<br/>\n"
"                    <span>Enlace a AFIP información adicional: </span>"

#. module: l10n_ar_withholding
#: model_terms:ir.ui.view,arch_db:l10n_ar_withholding.report_payment_receipt_document
msgid "<span>Amount</span>"
msgstr "<span>Cantidad</span>"

#. module: l10n_ar_withholding
#: model_terms:ir.ui.view,arch_db:l10n_ar_withholding.report_payment_receipt_document
msgid "<span>Base</span>"
msgstr "<span>Base</span>"

#. module: l10n_ar_withholding
#: model_terms:ir.ui.view,arch_db:l10n_ar_withholding.report_payment_receipt_document
msgid "<span>Tax</span>"
msgstr "<span>Impuesto</span>"

#. module: l10n_ar_withholding
#: model_terms:ir.ui.view,arch_db:l10n_ar_withholding.report_payment_receipt_document
msgid "<span>Withholding number</span>"
msgstr "<span>Número de retención</span>"

#. module: l10n_ar_withholding
#. odoo-python
#: code:addons/l10n_ar_withholding/wizards/account_payment_register.py:0
msgid ""
"A payment cannot have withholding if the payment method has no outstanding "
"accounts"
msgstr ""
"Un pago no puede tener retención si el método de pago no tiene cuentas "
"pendientes."

#. module: l10n_ar_withholding
#: model_terms:ir.ui.view,arch_db:l10n_ar_withholding.view_afip_earnings_table_scale_form
msgid "AFIP Additional info"
msgstr "AFIP Información Adicional"

#. module: l10n_ar_withholding
#: model_terms:ir.ui.view,arch_db:l10n_ar_withholding.view_afip_earnings_table_scale_form
msgid "AFIP Calculator"
msgstr "AFIP Calculadora"

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_account_tax__l10n_ar_code
msgid "AFIP Code"
msgstr "Código AFIP"

#. module: l10n_ar_withholding
#: model:ir.actions.act_window,name:l10n_ar_withholding.act_afip_earnings_table_scale
msgid "AFIP tax"
msgstr "AFIP impuesto"

#. module: l10n_ar_withholding
#: model:ir.model,name:l10n_ar_withholding.model_account_chart_template
msgid "Account Chart Template"
msgstr "Plantilla de plan de cuentas"

#. module: l10n_ar_withholding
#: model:ir.model.fields,help:l10n_ar_withholding.field_res_company__l10n_ar_tax_base_account_id
#: model:ir.model.fields,help:l10n_ar_withholding.field_res_config_settings__l10n_ar_tax_base_account_id
#: model_terms:ir.ui.view,arch_db:l10n_ar_withholding.res_config_settings_view_form
msgid ""
"Account that will be set on lines created to represent the tax base amounts."
msgstr ""
"Cuenta que se mostrará en las líneas creadas para representar las cantidades"
" base de impuestos."

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_earnings_scale_line__percentage
msgid "Add %"
msgstr "Agregar %"

#. module: l10n_ar_withholding
#: model_terms:ir.ui.view,arch_db:l10n_ar_withholding.view_account_payment_register_form
msgid ""
"Adjust total amount or withholdings amount so that the check amount is the "
"correct one."
msgstr ""
"Ajustar la cantidad del total o la cantidad de retenciones para que la "
"cantidad sea correcta."

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_payment_register_withholding__amount
#: model_terms:ir.ui.view,arch_db:l10n_ar_withholding.view_account_payment_form
msgid "Amount"
msgstr "Cantidad"

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_account_tax__l10n_ar_type_tax_use
msgid "Argentina Tax Type"
msgstr "Tipo de impuesto Argentino"

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_account_tax__l10n_ar_withholding_payment_type
msgid "Argentina Withholding Payment Type"
msgstr "Tipo de retención Argentina"

#. module: l10n_ar_withholding
#: model:ir.model,name:l10n_ar_withholding.model_l10n_ar_partner_tax
msgid "Argentinean Partner Taxes"
msgstr "Impuestos argentinos del contacto"

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_res_partner__l10n_ar_partner_tax_ids
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_res_users__l10n_ar_partner_tax_ids
msgid "Argentinean Withholding Taxes"
msgstr "Impuestos de retención de Argentina"

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_payment_register_withholding__base_amount
msgid "Base Amount"
msgstr "Cantidad base"

#. module: l10n_ar_withholding
#: model_terms:ir.ui.view,arch_db:l10n_ar_withholding.view_afip_earnings_table_scale_form
msgid ""
"Base amount = Tax base + tax bases applied this month to the same tax and "
"partner - non-taxable amount"
msgstr ""
"Monto base = Base del impuesto + montos bases de impuesto aplicados este mes"
" al mismo impuesto y al mismo contacto - mínimo no imponible"

#. module: l10n_ar_withholding
#: model:ir.model.fields,help:l10n_ar_withholding.field_l10n_ar_earnings_scale_line__scale_id
msgid ""
"Calculation of the withholding amount: From the taxable amount (tax base + "
"tax bases applied this month to same tax and partner - non-taxable minimum) "
"subtract the immediately previous amount of the column 'S/ Exceeding $' to "
"detect which row to work with and apply the percentage of said row to the "
"result of the subtraction. Then add to this amount the amount of the '$' "
"column."
msgstr ""
"Cálculo del impuesto de retención: Del monto imponible (monto base + montos "
"base aplicados en este mes al mismo impuesto y al mismo contacto - mínimo no"
" imponible) restar el monto inmediato anterior a la columna 'S/ Excedente de"
" $' para detectar con que fila trabajar y aplicar el porcentaje de dicha "
"fila al resultado de la resta. Luego agregar a dicho monto el monto de la "
"columna '$'. "

#. module: l10n_ar_withholding
#: model_terms:ir.ui.view,arch_db:l10n_ar_withholding.view_account_payment_register_form
msgid "Check amount"
msgstr "Importe de Cheques"

#. module: l10n_ar_withholding
#: model:ir.model,name:l10n_ar_withholding.model_res_company
msgid "Companies"
msgstr "Empresas"

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_partner_tax__company_id
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_payment_register_withholding__company_id
msgid "Company"
msgstr "Empresa"

#. module: l10n_ar_withholding
#: model:ir.model,name:l10n_ar_withholding.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustes de configuración"

#. module: l10n_ar_withholding
#: model:ir.model,name:l10n_ar_withholding.model_res_partner
msgid "Contact"
msgstr "Contacto"

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_earnings_scale__create_uid
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_earnings_scale_line__create_uid
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_partner_tax__create_uid
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_payment_register_withholding__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_earnings_scale__create_date
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_earnings_scale_line__create_date
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_partner_tax__create_date
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_payment_register_withholding__create_date
msgid "Created on"
msgstr "Creado el"

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_earnings_scale_line__currency_id
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_payment_register_withholding__currency_id
msgid "Currency"
msgstr "Moneda"

#. module: l10n_ar_withholding
#: model:ir.model.fields.selection,name:l10n_ar_withholding.selection__account_tax__l10n_ar_withholding_payment_type__customer
msgid "Customer Payment"
msgstr "Pago de cliente"

#. module: l10n_ar_withholding
#: model:ir.model.fields.selection,name:l10n_ar_withholding.selection__account_tax__l10n_ar_type_tax_use__customer
msgid "Customer Payment Withholding"
msgstr "Retención Pago de Cliente"

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_earnings_scale__display_name
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_earnings_scale_line__display_name
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_partner_tax__display_name
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_payment_register_withholding__display_name
msgid "Display Name"
msgstr "Nombre para mostrar"

#. module: l10n_ar_withholding
#: model:ir.model.fields.selection,name:l10n_ar_withholding.selection__account_tax__l10n_ar_tax_type__earnings
msgid "Earnings"
msgstr "Ganancias"

#. module: l10n_ar_withholding
#: model:ir.model.fields.selection,name:l10n_ar_withholding.selection__account_tax__l10n_ar_tax_type__earnings_scale
#: model:ir.ui.menu,name:l10n_ar_withholding.menu_action_afip_earnings_table_scale_line
msgid "Earnings Scale"
msgstr "Escala de ganancias"

#. module: l10n_ar_withholding
#: model:ir.model.fields,help:l10n_ar_withholding.field_account_tax__l10n_ar_scale_id
msgid "Earnings table scale if tax type is 'Earnings Scale'."
msgstr ""
"Tabla de escala de ganancias si el tipo de impuesto es 'Escala de "
"ganancias'."

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_earnings_scale_line__from_amount
msgid "From $"
msgstr "Desde $"

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_partner_tax__from_date
msgid "From Date"
msgstr "Fecha desde"

#. module: l10n_ar_withholding
#: model:ir.model.fields,help:l10n_ar_withholding.field_l10n_ar_earnings_scale_line__excess_amount
msgid ""
"From the taxable amount (tax base + tax bases applied this month to same tax"
" and partner - non-taxable minimum) subtract the immediately previous amount"
" of this column to detect which row to work with and apply the percentage of"
" said row to the result of the subtraction."
msgstr ""
"Al monto imponible (monto base + montos bases aplicados este mes al mismo "
"impuesto y contacto - mínimo no imponible) restar el monto inmediato "
"anterior a esta columna para detectar con qué fila trabajar y aplicar el "
"porcentaje de  dicha fila al resultado de la resta."

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_earnings_scale__id
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_earnings_scale_line__id
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_partner_tax__id
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_payment_register_withholding__id
msgid "ID"
msgstr "ID"

#. module: l10n_ar_withholding
#: model:ir.model.fields.selection,name:l10n_ar_withholding.selection__account_tax__l10n_ar_tax_type__iibb_total
msgid "IIBB Total Amount"
msgstr "Ingresos Brutos monto total"

#. module: l10n_ar_withholding
#: model:ir.model.fields.selection,name:l10n_ar_withholding.selection__account_tax__l10n_ar_tax_type__iibb_untaxed
msgid "IIBB Untaxed"
msgstr "Ingresos brutos no gravado"

#. module: l10n_ar_withholding
#: model:ir.model.fields,help:l10n_ar_withholding.field_account_tax__l10n_ar_withholding_sequence_id
#: model:ir.model.fields,help:l10n_ar_withholding.field_l10n_ar_payment_register_withholding__withholding_sequence_id
msgid ""
"If no sequence provided then it will be required for you to enter "
"withholding number when registering one."
msgstr ""
"Si no se ingresa ninguna secuencia, se le pedirá ingresar un número de "
"retención al registrar uno."

#. module: l10n_ar_withholding
#: model_terms:ir.ui.view,arch_db:l10n_ar_withholding.view_afip_earnings_table_scale_form
msgid "If the base amount is lower than 0.0 then the withholding is 0.0"
msgstr "Si el monto base es menor a 0.0 entonces la retención es 0.0"

#. module: l10n_ar_withholding
#: model:ir.model.fields,help:l10n_ar_withholding.field_account_tax__l10n_ar_minimum_threshold
msgid ""
"If the calculated withholding tax amount is lower than minimum withholding "
"threshold then it is 0.0."
msgstr ""
"Si el monto de retención calculado es más chico que el importe mínimo de "
"retención entonces el monto de retención es 0.0."

#. module: l10n_ar_withholding
#: model_terms:ir.ui.view,arch_db:l10n_ar_withholding.view_afip_earnings_table_scale_form
msgid ""
"If the withholding amount is lower than the minimum threshold on the tax "
"then the final withholding amount is 0.0"
msgstr ""
"Si el monto de retención es menor al monto mínimo de retención en el "
"impuesto entonces el monto final de retención es 0.0"

#. module: l10n_ar_withholding
#: model:ir.model,name:l10n_ar_withholding.model_account_move
msgid "Journal Entry"
msgstr "Asiento de diario"

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_account_tax__l10n_ar_state_id
msgid "Jurisdiction"
msgstr "Provincia"

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_account_payment_register__l10n_ar_adjustment_warning
msgid "L10N Ar Adjustment Warning"
msgstr "Advertencia de ajuste par L10N Ar"

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_account_payment_register__l10n_ar_net_amount
msgid "L10N Ar Net Amount"
msgstr "Cantidad neta L10N Ar"

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_earnings_scale__write_uid
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_earnings_scale_line__write_uid
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_partner_tax__write_uid
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_payment_register_withholding__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_earnings_scale__write_date
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_earnings_scale_line__write_date
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_partner_tax__write_date
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_payment_register_withholding__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_earnings_scale__line_ids
msgid "Line"
msgstr "Línea"

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_account_tax__l10n_ar_minimum_threshold
msgid "Minimum Treshold"
msgstr "Retención mínima"

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_earnings_scale__name
msgid "Name"
msgstr "Nombre"

#. module: l10n_ar_withholding
#: model_terms:ir.ui.view,arch_db:l10n_ar_withholding.view_account_payment_register_form
msgid "Net Amount"
msgstr "Cantidad neta"

#. module: l10n_ar_withholding
#: model:ir.model.fields,help:l10n_ar_withholding.field_account_payment_register__l10n_ar_net_amount
msgid "Net amount after withholdings"
msgstr "Cantidad neta después de retenciones"

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_account_tax__l10n_ar_non_taxable_amount
msgid "Non Taxable Amount"
msgstr "Monto no imponible"

#. module: l10n_ar_withholding
#: model:l10n_ar.earnings.scale,name:l10n_ar_withholding.normal_scale
msgid "Normal Scale"
msgstr "Escala Normal"

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_payment_register_withholding__name
msgid "Number"
msgstr "Número"

#. module: l10n_ar_withholding
#: model:ir.model.fields.selection,name:l10n_ar_withholding.selection__account_tax__l10n_ar_type_tax_use__none
msgid "Other"
msgstr "Otro"

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_partner_tax__partner_id
msgid "Partner"
msgstr "Contacto"

#. module: l10n_ar_withholding
#: model:ir.model,name:l10n_ar_withholding.model_account_payment_register
msgid "Pay"
msgstr "Pagar"

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_payment_register_withholding__payment_register_id
msgid "Payment Register"
msgstr "Registro de pago"

#. module: l10n_ar_withholding
#: model:ir.model,name:l10n_ar_withholding.model_l10n_ar_payment_register_withholding
msgid "Payment register withholding lines"
msgstr "Líneas de retención del registro de pago"

#. module: l10n_ar_withholding
#: model:ir.model,name:l10n_ar_withholding.model_account_payment
msgid "Payments"
msgstr "Pagos"

#. module: l10n_ar_withholding
#: model:ir.model.fields,help:l10n_ar_withholding.field_l10n_ar_earnings_scale_line__percentage
msgid ""
"Percentage to apply to the result of the subtraction between the taxable "
"amount (tax base + tax basis of the previous month - non-taxable minimum) "
"and the immediately previous amount of 'S/ Exced. from $' column."
msgstr ""
"Porcentaje a aplicar al resultado de la resta entre el monto imponible  "
"(monto base + montos bases del mismo mes - mínimo no imponible) y el monto "
"inmediato anterior a la columna 'S/ Exced. from $'."

#. module: l10n_ar_withholding
#. odoo-python
#: code:addons/l10n_ar_withholding/wizards/account_payment_register.py:0
msgid "Please enter withholding number for tax %s"
msgstr "Ingrese el número de retención para el impuesto %s"

#. module: l10n_ar_withholding
#: model_terms:ir.ui.view,arch_db:l10n_ar_withholding.view_partner_form
msgid "Purchase Withholding"
msgstr "Retenciones de compras"

#. module: l10n_ar_withholding
#: model:ir.model.fields.selection,name:l10n_ar_withholding.selection__account_tax__l10n_ar_type_tax_use__purchase
msgid "Purchases"
msgstr "Compras"

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_earnings_scale_line__excess_amount
msgid "S/ Exceeding $"
msgstr "S/ Excedente de $"

#. module: l10n_ar_withholding
#: model:ir.model.fields.selection,name:l10n_ar_withholding.selection__account_tax__l10n_ar_type_tax_use__sale
msgid "Sales"
msgstr "Ventas"

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_account_tax__l10n_ar_scale_id
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_earnings_scale_line__scale_id
msgid "Scale"
msgstr "Escala"

#. module: l10n_ar_withholding
#: model:l10n_ar.earnings.scale,name:l10n_ar_withholding.scale_119
msgid "Scale 119"
msgstr "Escala 119"

#. module: l10n_ar_withholding
#: model:ir.model,name:l10n_ar_withholding.model_account_tax
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_partner_tax__tax_id
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_payment_register_withholding__tax_id
#: model_terms:ir.ui.view,arch_db:l10n_ar_withholding.view_account_payment_form
msgid "Tax"
msgstr "Impuesto"

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_res_company__l10n_ar_tax_base_account_id
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_res_config_settings__l10n_ar_tax_base_account_id
msgid "Tax Base Account"
msgstr "Cuenta del impuesto base"

#. module: l10n_ar_withholding
#: model_terms:ir.ui.view,arch_db:l10n_ar_withholding.view_tax_form-l10n_ar
msgid "Tax Type"
msgstr "Tipo de Impuesto"

#. module: l10n_ar_withholding
#: model:ir.model.fields,help:l10n_ar_withholding.field_l10n_ar_payment_register_withholding__currency_id
msgid "The payment's currency."
msgstr "La moneda del pago."

#. module: l10n_ar_withholding
#: model:ir.model.fields,help:l10n_ar_withholding.field_l10n_ar_earnings_scale_line__to_amount
msgid ""
"The taxable amount (tax base + tax bases applied this month to same tax and "
"partner - non-taxable minimum) must be between the amount in the 'S/ Exced' "
"column. of $' and the amount of this column."
msgstr ""
"El monto imponible (monto base + montos bases aplicados este mes al mismo "
"impuesto y al mismo contacto - mínimo no imponible) debe estar entre el "
"monto en columna 'S/ Exced' y monto $' y el monto de esta columna."

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_earnings_scale_line__to_amount
msgid "To $"
msgstr "Hasta $"

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_partner_tax__to_date
msgid "To Date"
msgstr "Fecha hasta"

#. module: l10n_ar_withholding
#: model:ir.model.fields,help:l10n_ar_withholding.field_l10n_ar_earnings_scale_line__fixed_amount
msgid ""
"To obtain the withholding amount first from the taxable amount (tax base + "
"tax bases applied this month to same tax and partner - non-taxable minimum) "
"subtract the immediately previous amount of 'S/ Exced. of $' column to "
"detect which row to work with and apply the percentage of said row to the "
"result of the subtraction. Then add the amount of this column to the result "
"of applying the percentage."
msgstr ""
"Para obtener el monto de retención primero al monto retenible (monto base + "
"montos bases aplicados este mes al mismo impuesto y contacto - mínimo no "
"imponible) restar el monto inmediato anterior de la columna 'S/ Exced. of $'"
" para detectar con que fila trabajar y aplicar el porcentaje de dicha fila "
"al resultado de la resta. Luego sumar el monto de esta columna al resultado "
"de aplicar el porcentaje."

#. module: l10n_ar_withholding
#: model_terms:ir.ui.view,arch_db:l10n_ar_withholding.view_account_payment_form
msgid "Total"
msgstr "Total"

#. module: l10n_ar_withholding
#: model:ir.model.fields,help:l10n_ar_withholding.field_account_tax__l10n_ar_non_taxable_amount
msgid "Until this base amount, the tax is not applied."
msgstr "Hasta este monto el impuesto no es aplicado."

#. module: l10n_ar_withholding
#: model:ir.model.fields.selection,name:l10n_ar_withholding.selection__account_tax__l10n_ar_withholding_payment_type__supplier
msgid "Vendor Payment"
msgstr "Pago de proveedor"

#. module: l10n_ar_withholding
#: model:ir.model.fields.selection,name:l10n_ar_withholding.selection__account_tax__l10n_ar_type_tax_use__supplier
msgid "Vendor Payment Withholding"
msgstr "Retención Pago de Proveedor"

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_account_tax__l10n_ar_withholding_sequence_id
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_payment_register_withholding__withholding_sequence_id
msgid "WTH Sequence"
msgstr "Secuencia de Retención"

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_account_tax__l10n_ar_tax_type
msgid "WTH Tax"
msgstr "Impuesto de retención"

#. module: l10n_ar_withholding
#: model_terms:ir.ui.view,arch_db:l10n_ar_withholding.res_config_settings_view_form
msgid "Withholding"
msgstr "Retención"

#. module: l10n_ar_withholding
#: model_terms:ir.ui.view,arch_db:l10n_ar_withholding.view_account_payment_form
msgid "Withholding Number"
msgstr "Número de retención"

#. module: l10n_ar_withholding
#: model_terms:ir.ui.view,arch_db:l10n_ar_withholding.view_afip_earnings_table_scale_form
msgid ""
"Withholding amount = (Base amount - immediately previous amount of the "
"column 'S/ Exceeding $') * row percentage / 100 + row fixed amount ('$' "
"column)"
msgstr ""
"Monto de retención = (Monto base - monto inmediato previo anterior a la "
"columna 'S/ Excedente de $') * porcentaje de la fila / 100 + monto fijo de "
"la fila (columna '$')"

#. module: l10n_ar_withholding
#: model:ir.model.fields,help:l10n_ar_withholding.field_account_tax__l10n_ar_withholding_payment_type
msgid "Withholding tax for supplier or customer payments."
msgstr "Impuesto de retención para pagos de proveedor o de cliente."

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_account_bank_statement_line__l10n_ar_withholding_ids
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_account_move__l10n_ar_withholding_ids
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_account_payment__l10n_ar_withholding_ids
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_account_payment_register__l10n_ar_withholding_ids
#: model_terms:ir.ui.view,arch_db:l10n_ar_withholding.view_account_payment_form
#: model_terms:ir.ui.view,arch_db:l10n_ar_withholding.view_account_payment_register_form
msgid "Withholdings"
msgstr "Retenciones"

#. module: l10n_ar_withholding
#: model_terms:ir.ui.view,arch_db:l10n_ar_withholding.view_account_payment_register_form
msgid ""
"You can't register withholdings when paying invoices of different partners "
"or same partner without grouping"
msgstr ""
"No puede registrar retenciones al pagar facturas de diferentes contactos o "
"del mismo contacto sin agrupar"

#. module: l10n_ar_withholding
#: model:ir.model,name:l10n_ar_withholding.model_l10n_ar_earnings_scale
msgid "l10n_ar.earnings.scale"
msgstr "l10n_ar.earnings.scale"

#. module: l10n_ar_withholding
#: model:ir.model,name:l10n_ar_withholding.model_l10n_ar_earnings_scale_line
msgid "l10n_ar.earnings.scale.line"
msgstr "l10n_ar.earnings.scale.line"

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_partner_tax__ref
msgid "ref"
msgstr "ref"
