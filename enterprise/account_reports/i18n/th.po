# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_reports
# 
# Translators:
# <PERSON>, 2024
# Wil <PERSON>, 2024
# <PERSON><PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:52+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON>, 2025\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/warnings.xml:0
msgid "\" account balance is affected by"
msgstr "\" ยอดเงินในบัญชีได้รับผลกระทบจาก"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "%(journal)s - %(account)s"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "%(names)s and %(remaining)s others"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "%(names)s and one other"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/res_company.py:0
msgid "%(report_label)s: %(period)s"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/budget.py:0
msgid "%s (copy)"
msgstr "%s (สำเนา)"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "%s is not a numeric value"
msgstr "%s ไม่ใช่ค่าตัวเลข"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "%s selected"
msgstr "เลือก %s แล้ว"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"'Open General Ledger' caret option is only available form report lines "
"targetting accounts."
msgstr ""
"ตัวเลือกคาเร็ต 'เปิดบัญชีแยกประเภททั่วไป' "
"ใช้ได้เฉพาะบรรทัดรายงานแบบฟอร์มที่กำหนดเป้าหมายบัญชีเท่านั้น"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"'View Bank Statement' caret option is only available for report lines "
"targeting bank statements."
msgstr ""
"ตัวเลือกคาเร็ต 'ดูรายการเดินบัญชีธนาคาร' "
"ใช้ได้เฉพาะกับรายการรายงานที่กำหนดเป้าหมายรายการเดินบัญชีจากธนาคารเท่านั้น"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "'external' engine does not support groupby, limit nor offset."
msgstr "กลไก 'ภายนอก' ไม่รองรับการจัดกลุ่มตาม จำกัด หรือออฟเซ็ต"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "(%s lines)"
msgstr "(%s บรรทัด)"

#. module: account_reports
#: model:account.report.line,name:account_reports.outstanding_receipts
msgid "(+) Outstanding Receipts"
msgstr "(+) ใบเสร็จรับเงินคงค้าง"

#. module: account_reports
#: model:account.report.line,name:account_reports.outstanding_payments
msgid "(-) Outstanding Payments"
msgstr "(-) การชำระเงินคงค้าง"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "(1 line)"
msgstr "(1 บรรทัด)"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
msgid "(No %s)"
msgstr "(ไม่ %s)"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "(No Group)"
msgstr "(ไม่มีกลุ่ม)"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/warnings.xml:0
msgid ", leading to an unexplained difference of"
msgstr "นำไปสู่ความแตกต่างอย่างอธิบายไม่ได้ของ"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_move_form_vat_return
msgid "-> Refresh"
msgstr "-> รีเฟรช"

#. module: account_reports
#: model:mail.template,body_html:account_reports.email_template_customer_statement
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                <p style=\"margin: 0px; padding: 0px;\">\n"
"                    <t t-if=\"object.id != object.commercial_partner_id.id\">Dear <t t-out=\"object.name or ''\"/> (<t t-out=\"object.commercial_partner_id.name or ''\"/>),</t>\n"
"                    <t t-else=\"\">Dear <t t-out=\"object.name or ''\"/>,</t>\n"
"                    <br/>\n"
"                    Please find enclosed the statement of your account.\n"
"                    <br/>\n"
"                    Do not hesitate to contact us if you have any questions.\n"
"                    <br/>\n"
"                    Sincerely,\n"
"                    <br/>\n"
"\t                <t t-out=\"object._get_followup_responsible().name if is_html_empty(object._get_followup_responsible().signature) else object._get_followup_responsible().signature\"/>\n"
"                </p>\n"
"            </div>\n"
"        "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                <p style=\"margin: 0px; padding: 0px;\">\n"
"                    <t t-if=\"object.id != object.commercial_partner_id.id\">เรียน <t t-out=\"object.name or ''\"/> (<t t-out=\"object.commercial_partner_id.name or ''\"/>),</t>\n"
"                    <t t-else=\"\">เรียน <t t-out=\"object.name or ''\"/>,</t>\n"
"                    <br/>\n"
"                    โปรดดูเอกสารแนบใบแจ้งยอดบัญชีของคุณ\n"
"                    <br/>\n"
"                    โปรดติดต่อเราหากคุณมีคำถาม\n"
"                    <br/>\n"
"                    ขอแสดงความนับถือ\n"
"                    <br/>\n"
"\t                <t t-out=\"object._get_followup_responsible().name if is_html_empty(object._get_followup_responsible().signature) else object._get_followup_responsible().signature\"/>\n"
"                </p>\n"
"            </div>\n"
"        "

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_send_form
msgid ""
"<i class=\"fa fa-question-circle ml4\" role=\"img\" aria-label=\"Warning\" "
"title=\"The email address is unknown on the partner\" invisible=\"not "
"send_mail_readonly\"/>"
msgstr ""
"<i class=\"fa fa-question-circle ml4\" role=\"รูปภาพ\" aria-"
"label=\"คำเตือน\" title=\"ที่อยู่อีเมลไม่เป็นที่รู้จักกับพาร์ทเนอร์\" "
"invisible=\"not send_mail_readonly\"/>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_report_file_download_error_wizard_form
msgid ""
"<i>Errors marked with <i class=\"fa fa-warning\"/> are critical and prevent "
"the file generation.</i>"
msgstr ""
"<i>ข้อผิดพลาดที่ทำเครื่องหมายไว้ <i class=\"fa fa-warning\"/> "
"ถือเป็นสิ่งสำคัญและขัดขวางการสร้างไฟล์</i>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "<span role=\"separator\">Reconciliation</span>"
msgstr "<span role=\"separator\">การกระทบยอด</span>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_report_file_download_error_wizard_form
msgid "<span>One or more error(s) occurred during file generation:</span>"
msgstr "<span>เกิดข้อผิดพลาดอย่างน้อยหนึ่งรายการระหว่างการสร้างไฟล์:</span>"

#. module: account_reports
#: model:ir.model.constraint,message:account_reports.constraint_account_report_horizontal_group_name_uniq
msgid "A horizontal group with the same name already exists."
msgstr "มีกลุ่มแนวนอนที่ใช้ชื่อเดียวกันนี้อยู่แล้ว"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/widgets/account_report_x2many/account_report_x2many.js:0
msgid "A line with a 'Group By' value cannot have children."
msgstr "บรรทัดที่มีค่า 'จัดกลุ่มตาม' ไม่สามารถมีส่วนย่อยได้"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_tax.py:0
msgid ""
"A tax unit can only be created between companies sharing the same main "
"currency."
msgstr ""
"หน่วยภาษีสามารถสร้างได้ระหว่างบริษัทที่ใช้สกุลเงินหลักเดียวกันเท่านั้น"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_tax.py:0
msgid ""
"A tax unit must contain a minimum of two companies. You might want to delete"
" the unit."
msgstr "หน่วยภาษีต้องมีอย่างน้อย 2 บริษัท คุณอาจต้องการลบหน่วยออก"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "AP %s"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "AR %s"
msgstr ""

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_total_assets0
msgid "ASSETS"
msgstr "สินทรัพย์"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/static/src/components/deferred_reports/groupby.xml:0
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#: model:account.report.column,name:account_reports.aged_payable_report_account_name
#: model:account.report.column,name:account_reports.aged_receivable_report_account_name
#: model:account.report.column,name:account_reports.partner_ledger_report_account_code
#: model:ir.model,name:account_reports.model_account_account
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget_item__account_id
msgid "Account"
msgstr "บัญชี"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_chart_template
msgid "Account Chart Template"
msgstr "เทมเพลตแผนภูมิบัญชี"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/models/account_report.py:0
msgid "Account Code"
msgstr "รหัสบัญชี"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Account Code / Tag"
msgstr "รหัสบัญชี / แท็ก"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_display_representative_field
msgid "Account Display Representative Field"
msgstr "ข้อมูลตัวแทนการแสดงบัญชี"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "Account Label"
msgstr "ป้ายกำกับบัญชี"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_annotation
msgid "Account Report Annotation"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_custom_handler
msgid "Account Report Custom Handler"
msgstr "ตัวจัดการรายงานบัญชีแบบกำหนดเอง"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_tax_report_handler
msgid "Account Report Handler for Tax Reports"
msgstr "ตัวจัดการรายงานบัญชีสำหรับรายงานภาษี"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_send
msgid "Account Report Send"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_config_settings__account_reports_show_per_company_setting
msgid "Account Reports Show Per Company Setting"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_partner__account_represented_company_ids
#: model:ir.model.fields,field_description:account_reports.field_res_users__account_represented_company_ids
msgid "Account Represented Company"
msgstr "บริษัทตัวแทนบัญชี"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_revaluation_journal_id
msgid "Account Revaluation Journal"
msgstr "สมุดรายวันการประเมินค่าบัญชีใหม่"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_account_type.xml:0
msgid "Account:"
msgstr "บัญชี:"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_representative_id
msgid "Accounting Firm"
msgstr "สำนักงานบัญชี"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report
msgid "Accounting Report"
msgstr "รายงานการบัญชี"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_budget
msgid "Accounting Report Budget"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_budget_item
msgid "Accounting Report Budget Item"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_expression
msgid "Accounting Report Expression"
msgstr "นิพจน์รายงานการบัญชี"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_line
msgid "Accounting Report Line"
msgstr "บรรทัดรายงานบัญชี"

#. module: account_reports
#: model:ir.actions.act_window,name:account_reports.action_account_report_tree
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_tree
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_report_search
msgid "Accounting Reports"
msgstr "รายงานทางบัญชี"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_analytic.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_analytic_groupby.xml:0
msgid "Accounts"
msgstr "บัญชี"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
msgid "Accounts Coverage Report"
msgstr "รายงานความครอบคลุมของบัญชี"

#. module: account_reports
#: model:account.report.line,name:account_reports.multicurrency_revaluation_to_adjust
msgid "Accounts To Adjust"
msgstr "บัญชีที่ต้องปรับ"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Accounts coverage"
msgstr "ครอบคลุมบัญชี"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_mail_activity_type__category
msgid "Action"
msgstr "การดำเนินการ"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_file_download_error_wizard__actionable_errors
msgid "Actionable Errors"
msgstr "ข้อผิดพลาดที่สามารถดำเนินการได้"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_mail_activity_type__category
msgid ""
"Actions may trigger specific behavior like opening calendar view or "
"automatically mark as done when a document is uploaded"
msgstr ""
"การดำเนินการอาจทำให้เกิดพฤติกรรมเฉพาะเช่นการเปิดมุมมองปฏิทินหรือทำเครื่องหมายโดยอัตโนมัติว่าเสร็จสิ้นเมื่ออัปโหลดเอกสาร"

#. module: account_reports
#: model:ir.model,name:account_reports.model_mail_activity
msgid "Activity"
msgstr "กิจกรรม"

#. module: account_reports
#: model:ir.model,name:account_reports.model_mail_activity_type
msgid "Activity Type"
msgstr "ประเภทกิจกรรม"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_name/popover/annotations_popover.xml:0
#: code:addons/account_reports/static/src/widgets/account_report_x2many/account_report_x2many.xml:0
msgid "Add a line"
msgstr "เพิ่มรายการ"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_send_form
msgid "Add contacts to notify..."
msgstr "เพิ่มที่ติดต่อเพื่อแจ้งให้ทราบ..."

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__totals_below_sections
#: model:ir.model.fields,field_description:account_reports.field_res_config_settings__totals_below_sections
msgid "Add totals below sections"
msgstr "เพิ่มผลรวมด้านล่างในส่วน"

#. module: account_reports
#: model:account.report.column,name:account_reports.multicurrency_revaluation_report_adjustment
msgid "Adjustment"
msgstr "การปรับ"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_multicurrency_revaluation_report.py:0
msgid "Adjustment Entry"
msgstr "บันทึกปรับปรุง"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Advance Payments received from customers"
msgstr "เงินรับล่วงหน้าจากลูกค้าแล้ว"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Advance payments made to suppliers"
msgstr "เงินจ่ายล่วงหน้าให้กับซัพพลายเออร์"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
msgid "Advanced"
msgstr "ระดับสูง"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_aged_partner_balance_report_handler
msgid "Aged Partner Balance Custom Handler"
msgstr "ตัวจัดการยอดคงเหลือของอายุบัญชีพาร์ทเนอร์แบบกำหนดเอง"

#. module: account_reports
#: model:account.report,name:account_reports.aged_payable_report
#: model:account.report.line,name:account_reports.aged_payable_line
#: model:ir.actions.client,name:account_reports.action_account_report_ap
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_aged_payable
msgid "Aged Payable"
msgstr "อายุบัญชีเจ้าหนี้"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_aged_payable_report_handler
msgid "Aged Payable Custom Handler"
msgstr "ตัวจัดการอายุบัญชีเจ้าหนี้แบบกำหนดเอง"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Aged Payables"
msgstr "อายุบัญชีเจ้าหนี้"

#. module: account_reports
#: model:account.report,name:account_reports.aged_receivable_report
#: model:account.report.line,name:account_reports.aged_receivable_line
#: model:ir.actions.client,name:account_reports.action_account_report_ar
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_aged_receivable
msgid "Aged Receivable"
msgstr "อายุบัญชีลูกหนี้"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_aged_receivable_report_handler
msgid "Aged Receivable Custom Handler"
msgstr "ตัวจัดการอายุบัญชีลูกหนี้แบบกำหนดเอง"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Aged Receivables"
msgstr "อายุบัญชีลูกหนี้"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_fiscal_position.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
#: code:addons/account_reports/static/src/components/sales_report/filters/filters.js:0
msgid "All"
msgstr "ทั้งหมด"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "All Journals"
msgstr "สมุดรายวันทั้งหมด"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "All Payable"
msgstr "เจ้าหนี้ทั้งหมด"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "All Receivable"
msgstr "ลูกหนี้ทั้งหมด"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "All Report Variants"
msgstr "รูปแบบรายงานทั้งหมด"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/tax_report/warnings.xml:0
msgid ""
"All selected companies or branches do not share the same Tax ID. Please "
"check the Tax ID of the selected companies."
msgstr ""
"บริษัทหรือสาขาที่เลือกทั้งหมดไม่ได้ใช้หมายเลขประจำตัวผู้เสียภาษีเดียวกัน "
"โปรดตรวจสอบหมายเลขประจำตัวผู้เสียภาษีของบริษัทที่เลือก"

#. module: account_reports
#: model:account.report.column,name:account_reports.account_financial_report_ec_sales_amount
#: model:account.report.column,name:account_reports.bank_reconciliation_report_amount
#: model:account.report.column,name:account_reports.partner_ledger_amount
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget_item__amount
msgid "Amount"
msgstr "จำนวน"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: model:account.report.column,name:account_reports.aged_payable_report_amount_currency
#: model:account.report.column,name:account_reports.aged_receivable_report_amount_currency
#: model:account.report.column,name:account_reports.bank_reconciliation_report_amount_currency
#: model:account.report.column,name:account_reports.partner_ledger_report_amount_currency
msgid "Amount Currency"
msgstr "สกุลเงินของยอดเงิน"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "Amount in currency: %s"
msgstr "ยอดเงินในสกุลเงิน: %s"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Amounts in Lakhs"
msgstr "จำนวนเงินเป็น Lakhs"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Amounts in Millions"
msgstr "จำนวนเงินเป็นล้าน"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Amounts in Thousands"
msgstr "จำนวนเงินเป็นพัน"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_analytic.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_analytic_groupby.xml:0
msgid "Analytic"
msgstr "การวิเคราะห์"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report__filter_analytic_groupby
msgid "Analytic Group By"
msgstr "กลุ่มวิเคราะห์โดย"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
msgid "Analytic Simulations"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_name/line_name.xml:0
msgid "Annotate"
msgstr "คำอธิบายประกอบ"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_name/popover/annotations_popover.xml:0
msgid "Annotation"
msgstr "คำอธิบายประกอบ"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report__annotations_ids
msgid "Annotations"
msgstr "คำอธิบายประกอบ"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "As of %s"
msgstr "ณ วันที่ %s"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Ascending"
msgstr "จากน้อยไปมาก"

#. module: account_reports
#: model:account.report.column,name:account_reports.aged_payable_report_period0
#: model:account.report.column,name:account_reports.aged_receivable_report_period0
msgid "At Date"
msgstr "ในวันที่"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_send_form
msgid "Attach a file"
msgstr "แนบเอกสาร"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: code:addons/account_reports/static/src/components/journal_report/line_name.xml:0
msgid "Audit"
msgstr "ตรวจสอบบัญชี"

#. module: account_reports
#: model:ir.ui.menu,name:account_reports.account_reports_audit_reports_menu
msgid "Audit Reports"
msgstr "รายงานการตรวจสอบบัญชี"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_avgcre0
msgid "Average creditors days"
msgstr "จำนวนวันของเจ้าหนี้โดยเฉลี่ย"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_avdebt0
msgid "Average debtors days"
msgstr "จำนวนวันลูกหนี้โดยเฉลี่ย"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "B: %s"
msgstr "B: %s"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: model:account.report.column,name:account_reports.balance_sheet_balance
#: model:account.report.column,name:account_reports.cash_flow_report_balance
#: model:account.report.column,name:account_reports.executive_summary_column
#: model:account.report.column,name:account_reports.general_ledger_report_balance
#: model:account.report.column,name:account_reports.journal_report_balance
#: model:account.report.column,name:account_reports.partner_ledger_report_balance
#: model:account.report.column,name:account_reports.profit_and_loss_column
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Balance"
msgstr "ยอดคงเหลือ"

#. module: account_reports
#: model:account.report,name:account_reports.balance_sheet
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_balancesheet0
#: model:ir.actions.client,name:account_reports.action_account_report_bs
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_balance_sheet
msgid "Balance Sheet"
msgstr "งบดุล"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_balance_sheet_report_handler
msgid "Balance Sheet Custom Handler"
msgstr ""

#. module: account_reports
#: model:account.report.column,name:account_reports.multicurrency_revaluation_report_balance_current
msgid "Balance at Current Rate"
msgstr "ยอดคงเหลือในอัตราปัจจุบัน"

#. module: account_reports
#: model:account.report.column,name:account_reports.multicurrency_revaluation_report_balance_operation
msgid "Balance at Operation Rate"
msgstr "ยอดคงเหลือในอัตราการดำเนินงาน"

#. module: account_reports
#: model:account.report.column,name:account_reports.multicurrency_revaluation_report_balance_currency
msgid "Balance in Foreign Currency"
msgstr "ยอดคงเหลือในสกุลเงินต่างประเทศ"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/bank_reconciliation_report.py:0
msgid "Balance of '%s'"
msgstr "ยอดคงเหลือของ '%s'"

#. module: account_reports
#: model:account.report.line,name:account_reports.balance_bank
msgid "Balance of Bank"
msgstr "ยอดคงเหลือของธนาคาร"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Balance tax advance payment account"
msgstr "ยอดคงเหลือบัญชีการชำระภาษีล่วงหน้า"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Balance tax current account (payable)"
msgstr "ยอดคงเหลือภาษีบัญชีกระแสรายวัน (เจ้าหนี้)"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Balance tax current account (receivable)"
msgstr "ยอดคงเหลือภาษีบัญชีกระแสรายวัน (ลูกหนี้)"

#. module: account_reports
#: model:ir.actions.client,name:account_reports.action_account_report_bank_reconciliation
msgid "Bank Reconciliation"
msgstr "Bank Reconciliation Move Presets"

#. module: account_reports
#: model:account.report,name:account_reports.bank_reconciliation_report
msgid "Bank Reconciliation Report"
msgstr "รายงานการกระทบยอดธนาคาร"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_bank_reconciliation_report_handler
msgid "Bank Reconciliation Report Custom Handler"
msgstr "รายงานการกระทบยอดธนาคาร ตัวจัดการแบบกำหนดเอง"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_bank_view0
msgid "Bank and Cash Accounts"
msgstr "บัญชีธนาคารและเงินสด"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_general_ledger.py:0
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_journal_report_taxes_summary
msgid "Base Amount"
msgstr "จำนวนฐาน"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/filter_aging.xml:0
msgid "Based on"
msgstr "อิงตาม"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
msgid "Before"
msgstr "ก่อนหน้า"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_budgets.xml:0
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget_item__budget_id
msgid "Budget"
msgstr "งบประมาณ"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_account__budget_item_ids
msgid "Budget Item"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_budget_form
msgid "Budget Items"
msgstr "รายการงบประมาณ"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_budgets.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_budget_form
msgid "Budget Name"
msgstr "ชื่องบประมาณ"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Budget items can only be edited from account lines."
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/redirectAction/redirectAction.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_send_form
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_multicurrency_revaluation_wizard
#: model_terms:ir.ui.view,arch_db:account_reports.view_report_export_wizard
msgid "Cancel"
msgstr "ยกเลิก"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Cannot audit tax from another model than account.tax."
msgstr "ไม่สามารถตรวจสอบภาษีจากรุ่นอื่นที่ไม่ใช่ account.tax ได้"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Cannot generate carryover values for all fiscal positions at once!"
msgstr "ไม่สามารถสร้างมูลค่ายกยอดสำหรับสถานะทางการเงินทั้งหมดในครั้งเดียวได้!"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_cell/popover/carryover_popover.xml:0
msgid "Carryover"
msgstr "ยกยอด"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Carryover adjustment for tax unit"
msgstr "การปรับยกยอดสำหรับหน่วยภาษี"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Carryover can only be generated for a single column group."
msgstr "สามารถสร้างการยกยอดสำหรับกลุ่มคอลัมน์เดียวได้เท่านั้น"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Carryover from %(date_from)s to %(date_to)s"
msgstr "การยกยอดจาก %(date_from)s ถึง %(date_to)s"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Carryover lines for: %s"
msgstr "บรรทัดยกยอดสำหรับ: %s"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_cash0
msgid "Cash"
msgstr "เงินสด"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_cash_flow_report_handler
msgid "Cash Flow Report Custom Handler"
msgstr "ตัวจัดการรายงานกระแสเงินสดแบบกำหนดเอง"

#. module: account_reports
#: model:account.report,name:account_reports.cash_flow_report
#: model:ir.actions.client,name:account_reports.action_account_report_cs
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_cash_flow
msgid "Cash Flow Statement"
msgstr "งบกระแสเงินสด"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Cash and cash equivalents, beginning of period"
msgstr "เงินสดและรายการเทียบเท่าเงินสดต้นงวด"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Cash and cash equivalents, closing balance"
msgstr "เงินสดและรายการเทียบเท่าเงินสด ยอดปิดบัญชี"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Cash flows from financing activities"
msgstr "กระแสเงินสดจากกิจกรรมระดมทุน"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Cash flows from investing & extraordinary activities"
msgstr "กระแสเงินสดจากการลงทุนและกิจกรรมพิเศษ"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Cash flows from operating activities"
msgstr "กระแสเงินสดจากกิจกรรมดำเนินงาน"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Cash flows from unclassified activities"
msgstr "กระแสเงินสดจากกิจกรรมที่ไม่จัดประเภท"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Cash in"
msgstr "เงินสดเข้า"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Cash out"
msgstr "เงินสดออก"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Cash paid for operating activities"
msgstr "เงินสดชำระสำหรับกิจกรรมดำเนินงาน"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_cash_received0
msgid "Cash received"
msgstr "รับเงินสดแล้ว"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Cash received from operating activities"
msgstr "เงินสดรับจากกิจกรรมดำเนินงาน"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_cash_spent0
msgid "Cash spent"
msgstr "เงินสดที่ใช้ไป"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_cash_surplus0
msgid "Cash surplus"
msgstr "เงินสดส่วนเกิน"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_change_lock_date
msgid "Change Lock Date"
msgstr "เปลี่ยนวันที่ล็อค"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/account_report_send.py:0
msgid "Check Partner(s) Email(s)"
msgstr "ตรวจสอบอีเมลของพาร์ทเนอร์"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/tax_report/warnings.xml:0
msgid "Check them"
msgstr "ตรวจสอบ"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_report_file_download_error_wizard_form
msgid "Close"
msgstr "ปิด"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Closing Entry"
msgstr "ปิดรายการ"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_closing_bank_balance0
msgid "Closing bank balance"
msgstr "ปิดยอดธนาคาร"

#. module: account_reports
#: model:account.report.column,name:account_reports.journal_report_code
msgid "Code"
msgstr "โค้ด"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/sales_report/filters/filter_code.xml:0
msgid "Codes:"
msgstr "โค้ด:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
msgid "Columns"
msgstr "คอลัมน์"

#. module: account_reports
#: model:account.report.column,name:account_reports.general_ledger_report_communication
msgid "Communication"
msgstr "การติดต่อสื่อสาร"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: model:ir.model,name:account_reports.model_res_company
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__company_ids
msgid "Companies"
msgstr "บริษัท"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__company_id
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget__company_id
msgid "Company"
msgstr "บริษัท"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_tax.py:0
msgid ""
"Company %(company)s already belongs to a tax unit in %(country)s. A company "
"can at most be part of one tax unit per country."
msgstr ""
"บริษัท %(company)s เป็นของหน่วยภาษีใน %(country)s แล้ว "
"บริษัทสามารถเป็นส่วนหนึ่งของหนึ่งหน่วยภาษีต่อประเทศได้มากที่สุด"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Company Currency"
msgstr "สกุลเงินของบริษัท"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_tax_unit.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Company Only"
msgstr "บริษัทเท่านั้น"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Company Settings"
msgstr "การตั้งค่าบริษัท"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
msgid "Comparison"
msgstr "การเปรียบเทียบ"

#. module: account_reports
#: model:ir.model,name:account_reports.model_res_config_settings
msgid "Config Settings"
msgstr "ตั้งค่าการกำหนดค่า"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Configure start dates"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Configure your TAX accounts - %s"
msgstr "กำหนดค่าบัญชีภาษีของคุณ - %s"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/res_config_settings.py:0
msgid "Configure your start dates"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Configure your tax accounts"
msgstr "กำหนดค่าบัญชีภาษีของคุณ"

#. module: account_reports
#: model:ir.model,name:account_reports.model_res_partner
msgid "Contact"
msgstr "ติดต่อ"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__mail_body
msgid "Contents"
msgstr "เนื้อหา"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_direct_costs0
msgid "Cost of Revenue"
msgstr "ต้นทุนสินค้า"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Could not expand term %(term)s while evaluating formula "
"%(unexpanded_formula)s"
msgstr "ไม่สามารถขยายคำศัพท์ %(term)s ขณะประเมินสูตร %(unexpanded_formula)s"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Could not parse account_code formula from token '%s'"
msgstr "ไม่สามารถแยกวิเคราะห์สูตร account_code จากโทเค็น '%s' ได้"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__country_id
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_journal_report_taxes_summary
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_report_search
msgid "Country"
msgstr "ประเทศ"

#. module: account_reports
#: model:account.report.column,name:account_reports.account_financial_report_ec_sales_country
msgid "Country Code"
msgstr "รหัสประเทศ"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_budgets.xml:0
msgid "Create"
msgstr "สร้าง"

#. module: account_reports
#: model:ir.actions.server,name:account_reports.action_create_composite_report_list
msgid "Create Composite Report"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_multicurrency_revaluation_wizard
msgid "Create Entry"
msgstr "สร้างรายการ"

#. module: account_reports
#: model:ir.actions.server,name:account_reports.action_create_report_menu
msgid "Create Menu Item"
msgstr "สร้างรายการเมนู"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_annotation__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget_item__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_file_download_error_wizard__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_annotation__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget_item__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_file_download_error_wizard__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#: model:account.report.column,name:account_reports.general_ledger_report_credit
#: model:account.report.column,name:account_reports.journal_report_credit
#: model:account.report.column,name:account_reports.partner_ledger_report_credit
#: model:account.report.column,name:account_reports.trial_balance_report_credit
msgid "Credit"
msgstr "เครดิต"

#. module: account_reports
#: model:account.report.column,name:account_reports.aged_payable_report_currency
#: model:account.report.column,name:account_reports.aged_receivable_report_currency
#: model:account.report.column,name:account_reports.bank_reconciliation_report_currency
#: model:account.report.column,name:account_reports.general_ledger_report_amount_currency
msgid "Currency"
msgstr "สกุลเงิน"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_multicurrency_revaluation_report.py:0
msgid "Currency Rates (%s)"
msgstr "อัตราสกุลเงิน (%s)"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filters
msgid "Currency:"
msgstr "สกุลเงิน:"

#. module: account_reports
#: model:account.report.column,name:account_reports.deferred_expense_current
#: model:account.report.column,name:account_reports.deferred_revenue_current
msgid "Current"
msgstr "ปัจจุบัน"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_current_assets0
#: model:account.report.line,name:account_reports.account_financial_report_current_assets_view0
msgid "Current Assets"
msgstr "สินทรัพย์หมุนเวียน"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_current_liabilities0
#: model:account.report.line,name:account_reports.account_financial_report_current_liabilities1
msgid "Current Liabilities"
msgstr "หนี้สินหมุนเวียน"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_retained_earnings_line_1
msgid "Current Year Retained Earnings"
msgstr ""

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_current_year_earnings0
msgid "Current Year Unallocated Earnings"
msgstr "กำไรที่ไม่ได้ปันส่วนในปีปัจจุบัน"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_ca_to_l0
msgid "Current assets to liabilities"
msgstr "สินทรัพย์หมุนเวียนต่อหนี้สิน"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_date.xml:0
msgid "Custom Dates"
msgstr "วันที่ที่กำหนดเอง"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report__custom_handler_model_id
msgid "Custom Handler Model"
msgstr "โมเดลตัวจัดการแบบกำหนดเอง"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report__custom_handler_model_name
msgid "Custom Handler Model Name"
msgstr "ชื่อโมเดลตัวจัดการแบบกำหนดเอง"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/bank_reconciliation_report.py:0
msgid ""
"Custom engine _report_custom_engine_last_statement_balance_amount does not "
"support groupby"
msgstr ""
"เอ็นจิ้นแบบกำหนดเอง _report_custom_engine_last_statement_balance_amount "
"ไม่รองรับ groupby"

#. module: account_reports
#: model:mail.template,name:account_reports.email_template_customer_statement
msgid "Customer Statement"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_name/popover/annotations_popover.xml:0
#: model:account.report.column,name:account_reports.bank_reconciliation_report_date
#: model:account.report.column,name:account_reports.general_ledger_report_date
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__date
#: model:ir.model.fields,field_description:account_reports.field_account_report_annotation__date
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget_item__date
msgid "Date"
msgstr "วันที่"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Date cannot be empty"
msgstr "วันที่ไม่สามารถเว้นว่างได้"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_report_annotation__date
msgid "Date considered as annotated by the annotation."
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/filter_aging.xml:0
msgid "Days"
msgstr "วัน"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#: model:account.report.column,name:account_reports.general_ledger_report_debit
#: model:account.report.column,name:account_reports.journal_report_debit
#: model:account.report.column,name:account_reports.partner_ledger_report_debit
#: model:account.report.column,name:account_reports.trial_balance_report_debit
msgid "Debit"
msgstr "เดบิต"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_journal_report_taxes_summary
msgid "Deductible"
msgstr "ค่าลดหย่อน"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/deferred_reports/warnings.xml:0
msgid "Deferrals have already been generated."
msgstr "รายการรอการตัดบัญชีได้ถูกสร้างขึ้นแล้ว"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
#: code:addons/account_reports/models/account_report.py:0
msgid "Deferred Entries"
msgstr "รายการที่รอการตัดบัญชี"

#. module: account_reports
#: model:ir.actions.client,name:account_reports.action_account_report_deferred_expense
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_deferred_expense
msgid "Deferred Expense"
msgstr "ค่าใช้จ่ายที่รอตัดบัญชี"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_deferred_expense_report_handler
msgid "Deferred Expense Custom Handler"
msgstr "ตัวจัดการค่าใช้จ่ายรอตัดบัญชีแบบกำหนดเอง"

#. module: account_reports
#: model:account.report,name:account_reports.deferred_expense_report
msgid "Deferred Expense Report"
msgstr "รายงานค่าใช้จ่ายรอตัดบัญชี"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_deferred_report_handler
msgid "Deferred Expense Report Custom Handler"
msgstr "ตัวจัดการรายงานค่าใช้จ่ายรอตัดบัญชีแบบกำหนดเอง"

#. module: account_reports
#: model:ir.actions.client,name:account_reports.action_account_report_deferred_revenue
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_deferred_revenue
msgid "Deferred Revenue"
msgstr "รายได้ที่รอตัดบัญชี"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_deferred_revenue_report_handler
msgid "Deferred Revenue Custom Handler"
msgstr "ตัวจัดการรายได้รอตัดบัญชีแบบกำหนดเอง"

#. module: account_reports
#: model:account.report,name:account_reports.deferred_revenue_report
msgid "Deferred Revenue Report"
msgstr "รายงานรายได้รอตัดบัญชี"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_expression_form
msgid "Definition"
msgstr "คำนิยาม"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_tax_periodicity
msgid "Delay units"
msgstr "หน่วยล่าช้า"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_move.py:0
msgid "Depending moves"
msgstr "ขึ้นอยู่กับการเคลื่อนไหว"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Descending"
msgstr "จากมากไปน้อย"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Difference from rounding taxes"
msgstr "ความแตกต่างจากการปัดเศษภาษี"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_line__display_custom_groupby_warning
msgid "Display Custom Groupby Warning"
msgstr "แสดงคำเตือน Groupby แบบกำหนดเอง"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__display_mail_composer
msgid "Display Mail Composer"
msgstr "แสดงตัวเขียนจดหมาย"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_annotation__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget_item__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_file_download_error_wizard__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__display_name
msgid "Display Name"
msgstr "แสดงชื่อ"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "Document"
msgstr "เอกสาร"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__doc_name
msgid "Documents Name"
msgstr "ชื่อเอกสาร"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__domain
msgid "Domain"
msgstr "โดเมน"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_fiscal_position.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Domestic"
msgstr "ภายในประเทศ"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__checkbox_download
msgid "Download"
msgstr "ดาวน์โหลด"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_report_file_download_error_wizard_form
msgid "Download Anyway"
msgstr "ยังเลือกที่จะดาวน์โหลด"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Download Excel"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Download the Data Inalterability Check Report"
msgstr "ดาวน์โหลดรายงานการตรวจสอบข้อมูลที่ไม่สามารถเปลี่ยนแปลงได้"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
msgid "Draft Entries"
msgstr "รายการฉบับร่าง"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_journal_report_taxes_summary
msgid "Due"
msgstr "ครบกำหนด"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/filter_aging.xml:0
#: model:account.report.column,name:account_reports.partner_ledger_report_date_maturity
msgid "Due Date"
msgstr "วันครบกำหนด"

#. module: account_reports
#: model:ir.actions.client,name:account_reports.action_account_report_sales
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_sales
msgid "EC Sales List"
msgstr "รายการขาย EC"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_ec_sales_report_handler
msgid "EC Sales Report Custom Handler"
msgstr "ตัวจัดการรายงานการขาย EC แบบกำหนดเอง"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_sales_report.py:0
msgid "EC tax on non EC countries"
msgstr "ภาษี EC สำหรับประเทศที่ไม่ใช่ EC"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_sales_report.py:0
msgid "EC tax on same country"
msgstr "ภาษี EC ในประเทศเดียวกัน"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_equity0
msgid "EQUITY"
msgstr "ส่วนของเจ้าของ"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Editing a manual report line is not allowed in multivat setup when "
"displaying data from all fiscal positions."
msgstr ""
"ไม่อนุญาตให้แก้ไขบรรทัดรายงานด้วยตนเองในการตั้งค่า multivat "
"เมื่อแสดงข้อมูลจากตำแหน่งทางการเงินทั้งหมด"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Editing a manual report line is not allowed when multiple companies are "
"selected."
msgstr "ไม่อนุญาตให้แก้ไขบรรทัดรายงานด้วยตนเอง เมื่อมีการเลือกบริษัทหลายแห่ง"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__checkbox_send_mail
msgid "Email"
msgstr "อีเมล"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__mail_template_id
msgid "Email template"
msgstr "เทมเพลตอีเมล"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__enable_download
msgid "Enable Download"
msgstr "เปิดใช้งานการดาวน์โหลด"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Enable Sections"
msgstr "เปิดใช้งานส่วนต่างๆ"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__enable_send_mail
msgid "Enable Send Mail"
msgstr "เปิดใช้งานการส่งจดหมาย"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_variant.xml:0
msgid "Enable more ..."
msgstr "เปิดใช้งานเพิ่มเติม ..."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_trial_balance_report.py:0
msgid "End Balance"
msgstr "ยอดคงเหลือปลายทาง"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "End of Month"
msgstr "สิ้นเดือน"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "End of Quarter"
msgstr "สิ้นไตรมาส"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "End of Year"
msgstr "สิ้นปี"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line/popover/debug_popover.xml:0
msgid "Engine"
msgstr "เครื่องยนต์"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_sales_report.py:0
msgid "Entries with partners with no VAT"
msgstr "รายการร่วมกับพาร์ทเนอร์โดยไม่มีภาษีมูลค่าเพิ่ม"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Error message"
msgstr "ข้อความผิดพลาด"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/multicurrency_revaluation_report/filters/filter_exchange_rate.xml:0
msgid "Exchange Rates"
msgstr "อัตราแลกเปลี่ยน"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_move_line__exclude_bank_lines
msgid "Exclude Bank Lines"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_journal_report_audit_move_line_search
msgid "Exclude Bank lines"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_account__exclude_provision_currency_ids
msgid "Exclude Provision Currency"
msgstr "ไม่รวมสกุลเงินสำรอง"

#. module: account_reports
#: model:account.report.line,name:account_reports.multicurrency_revaluation_excluded
msgid "Excluded Accounts"
msgstr "บัญชีที่ยกเว้น"

#. module: account_reports
#: model:account.report,name:account_reports.executive_summary
#: model:ir.actions.client,name:account_reports.action_account_report_exec_summary
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_exec_summary
msgid "Executive Summary"
msgstr "ข้อมูลสรุป"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__expense_provision_account_id
msgid "Expense Account"
msgstr "บัญชีค่าใช้จ่าย"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_revaluation_expense_provision_account_id
msgid "Expense Provision Account"
msgstr "บัญชีสำรองค่าใช้จ่าย"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
msgid "Expense Provision for %s"
msgstr "สำรองค่าใช้จ่ายสำหรับ %s"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_expenses0
msgid "Expenses"
msgstr "รายจ่าย"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_report_export_wizard
msgid "Export"
msgstr "ส่งออก"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_reports_export_wizard_format
msgid "Export format for accounting's reports"
msgstr "รูปแบบการส่งออกสำหรับรายงานทางบัญชี"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__export_format_ids
msgid "Export to"
msgstr "ส่งออกไปที่"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_reports_export_wizard
msgid "Export wizard for accounting's reports"
msgstr "โปรแกรมส่งออกสำหรับรายงานทางบัญชี"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_expression_form
msgid "Expression"
msgstr "ตัวสั่งงาน"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Expression labelled '%(label)s' of line '%(line)s' is being overwritten when"
" computing the current report. Make sure the cross-report aggregations of "
"this report only reference terms belonging to other reports."
msgstr ""
"นิพจน์ที่มีป้ายกำกับ '%(label)s' ของบรรทัด '%(line)s' "
"กำลังถูกเขียนทับเมื่อคำนวณรายงานปัจจุบัน "
"ตรวจสอบให้แน่ใจว่าการรวมข้ามรายงานของรายงานนี้อ้างอิงเฉพาะคำที่เป็นของรายงานอื่นเท่านั้น"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__field_name
msgid "Field"
msgstr "ฟิลด์"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Field %s does not exist on account.move.line, and is not supported by this "
"report's custom handler."
msgstr ""
"ไม่มีฟิลด์ %s บน account.move.line "
"และไม่ได้รับการสนับสนุนโดยตัวจัดการแบบกำหนดเองของรายงานนี้"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Field %s does not exist on account.move.line."
msgstr "ไม่มีข้อมูล %s บน account.move.line."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Field %s of account.move.line is not stored, and hence cannot be used in a "
"groupby expression"
msgstr ""
"ข้อมูล %s ของ account.move.line จะไม่ถูกจัดเก็บ "
"ดังนั้นจึงไม่สามารถใช้ในตัวสั่งงาน groupby ได้"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Field 'Custom Handler Model' can only reference records inheriting from "
"[%s]."
msgstr ""
"ช่อง 'โมเดลตัวจัดการที่กำหนดเอง' "
"สามารถอ้างอิงได้เฉพาะบันทึกที่สืบทอดมาเท่านั้น [%s]"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_file_download_error_wizard__file_content
msgid "File Content"
msgstr "เนื้อหาไฟล์"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_report_file_download_error_wizard_form
msgid "File Download Errors"
msgstr "ข้อผิดพลาดในการดาวน์โหลดไฟล์"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_file_download_error_wizard__file_name
msgid "File Name"
msgstr "ชื่อไฟล์"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
msgid "Filters"
msgstr "ตัวกรอง"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_aml_ir_filters.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filters
msgid "Filters:"
msgstr "ตัวกรอง:"

#. module: account_reports
#: model:ir.actions.act_window,name:account_reports.action_account_report_budget_tree
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_budget_tree
msgid "Financial Budgets"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_fiscal_position
#: model:ir.model.fields,field_description:account_reports.field_account_report_annotation__fiscal_position_id
msgid "Fiscal Position"
msgstr "ฐานะทางการเงิน"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_fiscal_position.xml:0
msgid "Fiscal Position:"
msgstr "สถานะทางการเงิน:"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__fpos_synced
msgid "Fiscal Positions Synchronised"
msgstr "สถานะทางการเงินซิงโครไนซ์แล้ว"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_tax_unit_form
msgid ""
"Fiscal Positions should apply to all companies of the tax unit. You may want"
" to"
msgstr "สถานะทางการเงินควรใช้กับทุกบริษัทในหน่วยภาษี คุณอาจต้องการ"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
msgid "Foreign currencies adjustment entry as of %s"
msgstr "รายการปรับสกุลเงินต่างประเทศ ณ วันที่ %s"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line/popover/debug_popover.xml:0
msgid "Formula"
msgstr "สูตร"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"From %(date_from)s\n"
"to  %(date_to)s"
msgstr ""
"จาก %(date_from)s\n"
"ถึง  %(date_to)s"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__fun_param
msgid "Function Parameter"
msgstr "ฟังก์ชันพารามิเตอร์"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__fun_to_call
msgid "Function to Call"
msgstr "ฟังก์ชั่นการโทร"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "G %s"
msgstr ""

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/models/account_trial_balance_report.py:0
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/warnings.xml:0
#: code:addons/account_reports/static/src/components/multicurrency_revaluation_report/line_name.xml:0
#: model:account.report,name:account_reports.general_ledger_report
#: model:ir.actions.client,name:account_reports.action_account_report_general_ledger
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_general_ledger
msgid "General Ledger"
msgstr "บัญชีแยกประเภททั่วไป"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_general_ledger_report_handler
msgid "General Ledger Custom Handler"
msgstr "ตัวจัดการบัญชีแยกประเภททั่วไปแบบกำหนดเอง"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
msgid "Generate entry"
msgstr "สร้างรายการ"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/report_export_wizard.py:0
msgid "Generated Documents"
msgstr "เอกสารที่สร้างขึ้น"

#. module: account_reports
#: model:account.report,name:account_reports.generic_ec_sales_report
msgid "Generic EC Sales List"
msgstr "รายการขาย EC ทั่วไป"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_generic_tax_report_handler
msgid "Generic Tax Report Custom Handler"
msgstr "ตัวจัดการรายงานภาษีทั่วไปแบบกำหนดเอง"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_generic_tax_report_handler_account_tax
msgid "Generic Tax Report Custom Handler (Account -> Tax)"
msgstr "ตัวจัดการรายงานภาษีทั่วไปแบบกำหนดเอง (บัญชี -> ภาษี)"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_generic_tax_report_handler_tax_account
msgid "Generic Tax Report Custom Handler (Tax -> Account)"
msgstr "ตัวจัดการรายงานภาษีทั่วไปแบบกำหนดเอง (ภาษี -> บัญชี)"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: model_terms:ir.ui.view,arch_db:account_reports.journal_report_pdf_export_main
msgid "Global Tax Summary"
msgstr "สรุปภาษีทั่วโลก"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_sales_report.py:0
msgid "Goods"
msgstr "สินค้า"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_journal_report_taxes_summary
msgid "Grid"
msgstr "ตาราง"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_gross_profit0
msgid "Gross Profit"
msgstr "กำไรขั้นต้น"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_gross_profit0
msgid "Gross profit"
msgstr "กำไรขั้นต้น"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_gpmargin0
msgid "Gross profit margin (gross profit / operating income)"
msgstr "อัตรากำไรขั้นต้น (กำไรขั้นต้น / รายได้จากการดำเนินงาน)"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_line_form
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_report_search
msgid "Group By"
msgstr "กลุ่มโดย"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_horizontal_group_form
msgid "Group Name"
msgstr "ชื่อกลุ่ม"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/deferred_reports/groupby.xml:0
msgid "Group by"
msgstr "จัดกลุ่มโดย"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
msgid "Grouped Deferral Entry of %s"
msgstr "รายการรอตัดบัญชีแบบกลุ่มของ %s"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/partner_ledger/filter_extra_options.xml:0
msgid "Hide Account"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/partner_ledger/filter_extra_options.xml:0
msgid "Hide Debit/Credit"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
msgid "Hide lines at 0"
msgstr "ซ่อนบรรทัดที่ 0"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
msgid "Hierarchy and Subtotals"
msgstr "ลำดับชั้นและผลรวมย่อย"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__horizontal_group_id
msgid "Horizontal Group"
msgstr "กลุ่มแนวนอน"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_horizontal_groups.xml:0
msgid "Horizontal Group:"
msgstr "กลุ่มแนวนอน:"

#. module: account_reports
#: model:ir.actions.act_window,name:account_reports.action_account_report_horizontal_groups
#: model:ir.model.fields,field_description:account_reports.field_account_report__horizontal_group_ids
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_horizontal_groups
msgid "Horizontal Groups"
msgstr "กลุ่มแนวนอน:"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_horizontal_group
msgid "Horizontal group for reports"
msgstr "กลุ่มแนวนอนสำหรับรายงาน"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_horizontal_group_rule
msgid "Horizontal group rule for reports"
msgstr "กฎกลุ่มแนวนอนสำหรับรายงาน"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filters
msgid "Horizontal:"
msgstr "แนวนอน:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "How often tax returns have to be made"
msgstr "ต้องยื่นแบบแสดงรายการภาษีบ่อยแค่ไหน"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__id
#: model:ir.model.fields,field_description:account_reports.field_account_report_annotation__id
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget__id
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget_item__id
#: model:ir.model.fields,field_description:account_reports.field_account_report_file_download_error_wizard__id
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group__id
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__id
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__id
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__id
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__id
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__id
msgid "ID"
msgstr "ไอดี"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_journal_report_taxes_summary
msgid "Impact On Grid"
msgstr "ผลกระทบต่อกริด"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_journal_report_taxes_summary
msgid "Impacted Tax Grids"
msgstr "ตารางภาษีที่ได้รับผลกระทบ"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "In %s"
msgstr "ใน %s"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_report_search
msgid "Inactive"
msgstr "ปิดใช้งาน"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/journal_report/filter_extra_options.xml:0
msgid "Include Payments"
msgstr "รวมการชำระเงิน"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filter_extra_options_template
msgid "Including Analytic Simulations"
msgstr "รวมถึงการจำลองเชิงวิเคราะห์"

#. module: account_reports
#: model:account.report.line,name:account_reports.no_statement_unreconciled_payments
#: model:account.report.line,name:account_reports.unreconciled_last_statement_payments
msgid "Including Unreconciled Payments"
msgstr "รวมถึงการชำระเงินที่ไม่กระทบยอด"

#. module: account_reports
#: model:account.report.line,name:account_reports.no_statement_unreconciled_receipt
#: model:account.report.line,name:account_reports.unreconciled_last_statement_receipts
msgid "Including Unreconciled Receipts"
msgstr "รวมถึงใบเสร็จรับเงินที่ไม่กระทบยอด"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__income_provision_account_id
msgid "Income Account"
msgstr "บัญชีรายได้"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_revaluation_income_provision_account_id
msgid "Income Provision Account"
msgstr "บัญชีสำรองรายได้"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
msgid "Income Provision for %s"
msgstr "สำรองรายได้สำหรับ%s"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/bank_reconciliation_report.py:0
msgid "Inconsistent Statements"
msgstr "งบที่ไม่สอดคล้องกัน"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Inconsistent data: more than one external value at the same date for a "
"'most_recent' external line."
msgstr ""
"ข้อมูลไม่สอดคล้องกัน: มากกว่าหนึ่งค่าภายนอกในวันเดียวกันสำหรับบรรทัดภายนอก "
"'most_recent'"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Inconsistent report_id in options dictionary. Options says "
"%(options_report)s; report is %(report)s."
msgstr ""
"report_id ไม่สอดคล้องกันในพจนานุกรมตัวเลือก ตัวเลือกบอกว่า "
"%(options_report)s; รายงานคือ %(report)s"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/models/account_trial_balance_report.py:0
msgid "Initial Balance"
msgstr "ยอดเริ่มต้น"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
msgid "Integer Rounding"
msgstr "การปัดเศษจำนวนเต็ม"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/filters.js:0
msgid "Intervals cannot be smaller than 1"
msgstr "ช่วงเวลาต้องไม่ต่ำกว่า 1"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/sales_report/warnings.xml:0
msgid "Intra-community taxes are applied on"
msgstr "มีการเรียกเก็บภาษีภายในคอมมูนิตี้บน"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Invalid domain formula in expression \"%(expression)s\" of line "
"\"%(line)s\": %(formula)s"
msgstr ""
"สูตรโดเมนไม่ถูกต้องในนิพจน์ \"%(expression)s\" ของบรรทัด \"%(line)s\": "
"%(formula)s"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Invalid method “%s”"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Invalid subformula in expression \"%(expression)s\" of line \"%(line)s\": "
"%(subformula)s"
msgstr ""
"สูตรย่อยไม่ถูกต้องในนิพจน์ \"%(expression)s\" ของบรรทัด \"%(line)s\": "
"%(subformula)s"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Invalid token '%(token)s' in account_codes formula '%(formula)s'"
msgstr "โทเค็นไม่ถูกต้อง '%(token)s' ในสูตร account_codes '%(formula)s'"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/filter_aging.xml:0
#: model:account.report.column,name:account_reports.aged_payable_report_invoice_date
#: model:account.report.column,name:account_reports.aged_receivable_report_invoice_date
#: model:account.report.column,name:account_reports.partner_ledger_report_invoicing_date
msgid "Invoice Date"
msgstr "วันที่ใบแจ้งหนี้"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_journal_report_audit_move_line_search
msgid "Invoice lines"
msgstr "รายการใบแจ้งหนี้"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report__is_account_coverage_report_available
msgid "Is Account Coverage Report Available"
msgstr "มีรายงานความครอบคลุมบัญชีหรือไม่"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_move.py:0
msgid "It seems there is some depending closing move to be posted"
msgstr "ดูเหมือนว่าจะมีการโพสต์ย้ายการปิดบัญชีบางส่วน"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid ""
"It's not possible to select a budget with the horizontal group feature."
msgstr "ไม่สามารถเลือกงบประมาณด้วยฟีเจอร์กลุ่มแนวนอนได้"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid ""
"It's not possible to select a horizontal group with the budget feature."
msgstr "ไม่สามารถเลือกกลุ่มแนวนอนโดยใช้ฟีเจอร์งบประมาณได้"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget__item_ids
msgid "Items"
msgstr "รายการ"

#. module: account_reports
#: model:account.report.column,name:account_reports.partner_ledger_report_journal_code
#: model:ir.model,name:account_reports.model_account_journal
#: model:ir.model.fields,field_description:account_reports.field_account_financial_year_op__account_tax_periodicity_journal_id
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__journal_id
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_tax_periodicity_journal_id
#: model:ir.model.fields,field_description:account_reports.field_res_config_settings__account_tax_periodicity_journal_id
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_reports.setup_financial_year_opening_form
msgid "Journal"
msgstr "สมุดรายวัน"

#. module: account_reports
#: model:ir.actions.client,name:account_reports.action_account_report_ja
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_ja
msgid "Journal Audit"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_move
msgid "Journal Entry"
msgstr "รายการสมุดรายวัน"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_move_line
msgid "Journal Item"
msgstr "รายการสมุดรายวัน"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/models/account_trial_balance_report.py:0
#: code:addons/account_reports/models/bank_reconciliation_report.py:0
#: code:addons/account_reports/static/src/components/general_ledger/line_name.xml:0
#: code:addons/account_reports/static/src/components/partner_ledger/line_name.xml:0
msgid "Journal Items"
msgstr "รายการสมุดรายวัน"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "Journal Items for Tax Audit"
msgstr "รายการสมุดรายวันสําหรับการตรวจสอบภาษี"

#. module: account_reports
#: model:account.report,name:account_reports.journal_report
msgid "Journal Report"
msgstr "รายงานสมุดรายวัน"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_journal_report_handler
msgid "Journal Report Custom Handler"
msgstr "ตัวจัดการรายงานสมุดรายวันแบบกำหนดเอง"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Journal items with archived tax tags"
msgstr "รายการสมุดรายวันพร้อมแท็กภาษีที่เก็บถาวร"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Journals"
msgstr "สมุดรายวัน"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filters
msgid "Journals:"
msgstr "สมุดรายวัน:"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_liabilities_view0
msgid "LIABILITIES"
msgstr "หนี้สิน"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_liabilities_and_equity_view0
msgid "LIABILITIES + EQUITY"
msgstr "หนี้สิน + ส่วนของเจ้าของ"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/static/src/components/account_report/line/popover/debug_popover.xml:0
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#: model:account.report.column,name:account_reports.bank_reconciliation_report_label
msgid "Label"
msgstr "ป้าย"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__mail_lang
msgid "Lang"
msgstr "ภาษา"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Last Statement balance + Transactions since statement"
msgstr "ยอดคงเหลือในใบแจ้งยอดล่าสุด + ธุรกรรมตั้งแต่ใบแจ้งยอด"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_annotation__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget_item__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_file_download_error_wizard__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__write_uid
msgid "Last Updated by"
msgstr "อัปเดตครั้งล่าสุดโดย"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_annotation__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget_item__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_file_download_error_wizard__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__write_date
msgid "Last Updated on"
msgstr "อัปเดตครั้งล่าสุดเมื่อ"

#. module: account_reports
#: model:account.report.line,name:account_reports.last_statement_balance
msgid "Last statement balance"
msgstr "ยอดคงเหลือในใบแจ้งยอดครั้งล่าสุด"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
msgid "Later"
msgstr "ภายหลัง"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/line_name/line_name.xml:0
msgid "Ledger"
msgstr "บัญชีแยกประเภท"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_cost_sales0
msgid "Less Costs of Revenue"
msgstr ""

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_expense0
msgid "Less Operating Expenses"
msgstr ""

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_depreciation0
msgid "Less Other Expenses"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_annotation__line_id
msgid "Line"
msgstr "บรรทัด"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
msgid "Lines"
msgstr "บรรทัด"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Load more..."
msgstr "โหลดเพิ่ม..."

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__mail_attachments_widget
msgid "Mail Attachments Widget"
msgstr "วิดเจ็ตไฟล์แนบเมล"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__main_company_id
msgid "Main Company"
msgstr "บริษัทหลัก"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_tax_unit__main_company_id
msgid ""
"Main company of this unit; the one actually reporting and paying the taxes."
msgstr "บริษัทหลักของหน่วยนี้ ผู้ที่รายงานและเสียภาษีจริง"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_multicurrency_revaluation_report.py:0
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_multicurrency_revaluation_wizard
msgid "Make Adjustment Entry"
msgstr "ทำรายการปรับ"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_file_download_error_wizard
msgid "Manage the file generation errors from report exports."
msgstr "จัดการกับข้อผิดพลาดในการสร้างไฟล์จากการส่งออกรายงาน"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Manual value"
msgstr "ค่าที่กำหนดเอง"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Manual values"
msgstr "ค่าที่กำหนดเอง"

#. module: account_reports
#: model:account.report.column,name:account_reports.partner_ledger_report_matching_number
msgid "Matching"
msgstr "การจับคู่"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_tax_unit__company_ids
msgid "Members of this unit"
msgstr "สมาชิกของหน่วยนี้"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Method '%(method_name)s' must start with the '%(prefix)s' prefix."
msgstr "วิธีการ '%(method_name)s' ต้องขึ้นต้นด้วยคำนำหน้า '%(prefix)s'"

#. module: account_reports
#: model:account.report.line,name:account_reports.misc_operations
msgid "Misc. operations"
msgstr "การดำเนินงานเบ็ดเตล็ด"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__mode
msgid "Mode"
msgstr "โหมด"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__res_model_name
msgid "Model"
msgstr "โมเดล"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Month"
msgstr "เดือน"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_budget_form
msgid "Months"
msgstr "เดือน"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filters
msgid "Multi-Ledger:"
msgstr "หลายบัญชีแยกประเภท:"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Multi-ledger"
msgstr "บัญชีแยกประเภทหลายบัญชี"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_multicurrency_revaluation_report_handler
msgid "Multicurrency Revaluation Report Custom Handler"
msgstr "ตัวจัดการรายงานการประเมินค่าหลายสกุลเงินแบบกำหนดเอง"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_multicurrency_revaluation_wizard
msgid "Multicurrency Revaluation Wizard"
msgstr "โปรแกรมการประเมินค่าหลายสกุลเงิน"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__account_report_send__mode__multi
msgid "Multiple Recipients"
msgstr "ผู้รับหลายราย"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/res_company.py:0
msgid ""
"Multiple draft tax closing entries exist for fiscal position %(position)s after %(period_start)s. There should be at most one. \n"
" %(closing_entries)s"
msgstr ""
"มีรายการปิดบัญชีภาษีแบบร่างหลายรายการสำหรับสถานะทางบัญชี %(position)s หลังจาก %( period_start)s ควรมีมากที่สุดอย่างหนึ่ง\n"
"%(closing_entries)s"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/res_company.py:0
msgid ""
"Multiple draft tax closing entries exist for your domestic region after %(period_start)s. There should be at most one. \n"
" %(closing_entries)s"
msgstr ""
"มีรายการปิดภาษีฉบับร่างหลายรายการสำหรับภูมิภาคภายในประเทศของคุณหลังจาก %(term_start)s ควรมีมากที่สุดอย่างหนึ่ง\n"
"%(closing_entries)s"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_general_ledger.py:0
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model:account.report.line,name:account_reports.journal_report_line
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget__name
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group__name
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__name
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__name
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_journal_report_taxes_summary
#: model_terms:ir.ui.view,arch_db:account_reports.view_tax_unit_form
msgid "Name"
msgstr "ชื่อ"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_reports_export_wizard__doc_name
msgid "Name to give to the generated documents."
msgstr "ชื่อที่จะมอบให้กับเอกสารที่สร้างขึ้น"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_profit0
#: model:account.report.line,name:account_reports.account_financial_report_net_profit0
msgid "Net Profit"
msgstr "กำไรสุทธิ"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_net_assets0
msgid "Net assets"
msgstr "สินทรัพย์สุทธิ"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Net increase in cash and cash equivalents"
msgstr "เงินสดและรายการเทียบเท่าเงินสดเพิ่มขึ้นสุทธิ"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_npmargin0
msgid "Net profit margin (net profit / income)"
msgstr "อัตรากำไรสุทธิ (กำไรสุทธิ / รายได้)"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Never miss a tax deadline."
msgstr "ไม่พลาดกำหนดเวลาภาษี"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/static/src/components/account_report/line_cell/popover/edit_popover.xml:0
msgid "No"
msgstr "ไม่"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
msgid "No Comparison"
msgstr "ไม่มีการเปรียบเทียบ"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "No Journal"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "No VAT number associated with your company. Please define one."
msgstr "ไม่มีหมายเลข VAT ที่เกี่ยวข้องกับบริษัทของคุณ โปรดกำหนดหนึ่งรายการ"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
msgid "No adjustment needed"
msgstr "ไม่จำเป็นต้องปรับ"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/account_report.xml:0
msgid "No data to display !"
msgstr "ไม่มีข้อมูลที่จะแสดง !"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/chart_template.py:0
msgid "No default miscellaneous journal could be found for the active company"
msgstr "ไม่พบสมุดรายวันเบ็ดเตล็ดเริ่มต้นสำหรับบริษัทที่ใช้งานอยู่"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
msgid "No entry to generate."
msgstr "ไม่มีรายการที่จะสร้าง"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
msgid "No provision needed was found."
msgstr "ไม่พบข้อกำหนดที่จำเป็น"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Non Trade Partners"
msgstr "พาร์ทเนอร์ที่ไม่ใช่การค้า"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Non Trade Payable"
msgstr "ไม่ใช่เจ้าหนี้การค้า"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Non Trade Receivable"
msgstr "ไม่ใช่ลูกหนี้การค้า"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_journal_report_taxes_summary
msgid "Non-Deductible"
msgstr "ไม่สามารถหักลดหย่อนได้"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_horizontal_groups.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
#: code:addons/account_reports/static/src/components/sales_report/filters/filters.js:0
msgid "None"
msgstr "ไม่มี"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
msgid "Not Started"
msgstr "ยังไม่เริ่ม"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Number of periods cannot be smaller than 1"
msgstr "จำนวนงวดต้องไม่น้อยกว่า 1"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_off_sheet
msgid "OFF BALANCE SHEET ACCOUNTS"
msgstr "ปิดบัญชีงบดุล"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
#: code:addons/account_reports/static/src/components/aged_partner_balance/filters.js:0
msgid "Odoo Warning"
msgstr "คำแจ้งเตือนจาก Odoo"

#. module: account_reports
#: model:account.report.column,name:account_reports.aged_payable_report_period5
#: model:account.report.column,name:account_reports.aged_receivable_report_period5
msgid "Older"
msgstr "เก่ากว่า"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/report_export_wizard.py:0
msgid "One of the formats chosen can not be exported in the DMS"
msgstr "รูปแบบใดรูปแบบหนึ่งที่เลือกไม่สามารถส่งออกใน DMS ได้"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_move.py:0
msgid "Only Billing Administrators are allowed to change lock dates!"
msgstr ""
"เฉพาะผู้ดูแลระบบการเรียกเก็บเงินเท่านั้นที่ได้รับอนุญาตให้เปลี่ยนวันที่ล็อค!"

#. module: account_reports
#: model:ir.actions.server,name:account_reports.action_account_reports_customer_statements
msgid "Open Customer Statements"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_financial_year_op
msgid "Opening Balance of Financial Year"
msgstr "ยอดตั้งต้นสำหรับรอบบัญชีปีนี้"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_operating_income0
msgid "Operating Income (or Loss)"
msgstr ""

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_expression_form
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
msgid "Options"
msgstr "ตัวเลือก"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filter_extra_options_template
msgid "Options:"
msgstr "ตัวเลือก:"

#. module: account_reports
#: model:account.report.line,name:account_reports.outstanding
msgid "Outstanding Receipts/Payments"
msgstr "ใบเสร็จรับเงิน/การชำระเงินคงค้าง"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "PDF"
msgstr "PDF"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__report_id
msgid "Parent Report Id"
msgstr "รหัสรายงานหลัก"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__export_wizard_id
msgid "Parent Wizard"
msgstr "ตัวช่วยหลัก"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/line_name/line_name.xml:0
#: code:addons/account_reports/static/src/components/partner_ledger/line_name.xml:0
#: model:account.report.column,name:account_reports.general_ledger_report_partner_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__partner_ids
msgid "Partner"
msgstr "พาร์ทเนอร์"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Partner Categories"
msgstr "หมวดหมู่พาร์ทเนอร์"

#. module: account_reports
#: model:account.report,name:account_reports.partner_ledger_report
#: model:ir.actions.client,name:account_reports.action_account_report_partner_ledger
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_partner_ledger
msgid "Partner Ledger"
msgstr "สมุดบัญชีแยกประเภทคู่ค้า"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_partner_ledger_report_handler
msgid "Partner Ledger Custom Handler"
msgstr "ตัวจัดการบัญชีแยกประเภทพาร์ทเนอร์แบบกำหนดเอง"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/line_name/line_name.xml:0
#: code:addons/account_reports/static/src/components/partner_ledger/line_name.xml:0
msgid "Partner is bad"
msgstr "พาร์ทเนอร์ที่ไม่ดี"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/line_name/line_name.xml:0
#: code:addons/account_reports/static/src/components/partner_ledger/line_name.xml:0
msgid "Partner is good"
msgstr "พาร์ทเนอร์ที่ดี"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/account_report_send.py:0
msgid "Partner(s) should have an email address."
msgstr "พาร์ทเนอร์ควรมีที่อยู่อีเมล"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_partner.xml:0
msgid "Partners"
msgstr "พาร์ทเนอร์"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filters
msgid "Partners Categories:"
msgstr "หมวดหมู่พาร์ทเนอร์:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filters
msgid "Partners:"
msgstr "พาร์ทเนอร์:"

#. module: account_reports
#: model:mail.activity.type,name:account_reports.mail_activity_type_tax_report_to_pay
msgid "Pay Tax"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_move.py:0
msgid "Pay tax: %s"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_aged_partner_balance.py:0
#: code:addons/account_reports/models/account_report.py:0
msgid "Payable"
msgstr "เจ้าหนี้"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Payable tax amount"
msgstr "จำนวนภาษีที่ต้องชำระ"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_current_liabilities_payable
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_creditors0
msgid "Payables"
msgstr "เจ้าหนี้"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_performance0
msgid "Performance"
msgstr "ประสิทธิภาพ"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Period"
msgstr "ช่วงเวลา"

#. module: account_reports
#: model:account.report.column,name:account_reports.aged_payable_report_period1
#: model:account.report.column,name:account_reports.aged_receivable_report_period1
msgid "Period 1"
msgstr ""

#. module: account_reports
#: model:account.report.column,name:account_reports.aged_payable_report_period2
#: model:account.report.column,name:account_reports.aged_receivable_report_period2
msgid "Period 2"
msgstr ""

#. module: account_reports
#: model:account.report.column,name:account_reports.aged_payable_report_period3
#: model:account.report.column,name:account_reports.aged_receivable_report_period3
msgid "Period 3"
msgstr ""

#. module: account_reports
#: model:account.report.column,name:account_reports.aged_payable_report_period4
#: model:account.report.column,name:account_reports.aged_receivable_report_period4
msgid "Period 4"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
msgid "Period order"
msgstr "ลำดับระยะเวลา"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_config_settings__account_tax_periodicity
#: model:ir.model.fields,help:account_reports.field_account_financial_year_op__account_tax_periodicity
#: model:ir.model.fields,help:account_reports.field_res_company__account_tax_periodicity
#: model:ir.model.fields,help:account_reports.field_res_config_settings__account_tax_periodicity
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Periodicity"
msgstr "เป็นระยะ"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_year_op__account_tax_periodicity
msgid "Periodicity in month"
msgstr "ช่วงเวลาในเดือน"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Periods"
msgstr "ระยะเวลา"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_analytic_groupby.xml:0
msgid "Plans"
msgstr "แผน"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/budget.py:0
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Please enter a valid budget name."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/account_report_send.py:0
msgid "Please select a mail template to send multiple statements."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Please select the main company and its branches in the company selector to "
"proceed."
msgstr "กรุณาเลือกบริษัทหลักและสาขาในตัวเลือกบริษัทเพื่อดำเนินการต่อ"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
msgid "Please set the deferred accounts in the accounting settings."
msgstr "โปรดตั้งค่าบัญชีที่ถูกเลื่อนออกไปในการตั้งค่าการบัญชี"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
msgid "Please set the deferred journal in the accounting settings."
msgstr "โปรดตั้งค่าสมุดรายวันที่รอการตัดบัญชีในการตั้งค่าระบบบัญชี"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Please specify the accounts necessary for the Tax Closing Entry."
msgstr "โปรดระบุบัญชีที่จำเป็นสำหรับรายการปิดบัญชีภาษี"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_fixed_assets_view0
msgid "Plus Fixed Assets"
msgstr "บวกกับสินทรัพย์ถาวร"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_non_current_assets_view0
msgid "Plus Non-current Assets"
msgstr "บวกกับสินทรัพย์ไม่หมุนเวียน"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_non_current_liabilities0
msgid "Plus Non-current Liabilities"
msgstr "บวกกับหนี้สินไม่หมุนเวียน"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_other_income0
msgid "Plus Other Income"
msgstr ""

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_position0
msgid "Position"
msgstr "ตำแหน่ง"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_cell/popover/edit_popover.xml:0
msgid "Post"
msgstr "ผ่านรายการ"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Posted Entries"
msgstr "รายการที่ผ่านรายการแล้ว"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_prepayements0
msgid "Prepayments"
msgstr "ชำระเงินล่วงหน้า"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__preview_data
msgid "Preview Data"
msgstr "ดูตัวอย่างข้อมูล"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
msgid "Previous Period"
msgstr "ช่วงก่อนหน้า"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_retained_earnings_line_2
msgid "Previous Years Retained Earnings"
msgstr ""

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_previous_year_earnings0
msgid "Previous Years Unallocated Earnings"
msgstr "รายได้ที่ไม่ได้ปันส่วนในปีก่อนหน้า"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_send_form
msgid "Print & Send"
msgstr "พิมพ์ & ส่ง"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Proceed"
msgstr "ดำเนินการ"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_multicurrency_revaluation_wizard
msgid ""
"Proceed with caution as there might be an existing adjustment for this "
"period ("
msgstr ""
"โปรดดำเนินการด้วยความระมัดระวัง เนื่องจากอาจมีการปรับเปลี่ยนในช่วงนี้ ("

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
#: code:addons/account_reports/static/src/components/deferred_reports/groupby.xml:0
msgid "Product"
msgstr "สินค้า"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/deferred_reports/groupby.xml:0
msgid "Product Category"
msgstr "หมวดหมู่สินค้า"

#. module: account_reports
#: model:account.report,name:account_reports.profit_and_loss
#: model:ir.actions.client,name:account_reports.action_account_report_pl
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_profit_and_loss
msgid "Profit and Loss"
msgstr "กำไรขาดทุน"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_profitability0
msgid "Profitability"
msgstr "การทำกำไร"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_move_form_vat_return
msgid "Proposition of tax closing journal entry."
msgstr "ข้อเสนอรายการสมุดรายวันการปิดบัญชีภาษี"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
msgid "Provision for %(for_cur)s (1 %(comp_cur)s = %(rate)s %(for_cur)s)"
msgstr "ข้อกำหนดสำหรับ %(for_cur)s (1 %(comp_cur)s = %(rate)s %(for_cur)s)"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Quarter"
msgstr "ไตรมาส"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/multicurrency_revaluation_report/line_name.xml:0
msgid "Rates"
msgstr "อัตรา"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_aged_partner_balance.py:0
#: code:addons/account_reports/models/account_report.py:0
msgid "Receivable"
msgstr "ลูกหนี้"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Receivable tax amount"
msgstr "จำนวนภาษีที่จะได้รับ"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_debtors0
#: model:account.report.line,name:account_reports.account_financial_report_receivable0
msgid "Receivables"
msgstr "ลูกหนี้"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__mail_partner_ids
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_send_form
msgid "Recipients"
msgstr "ผู้รับ"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Reconciliation Report"
msgstr "รายงานการกระทบยอด"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_year_op__account_tax_periodicity_reminder_day
#: model:ir.model.fields,field_description:account_reports.field_res_config_settings__account_tax_periodicity_reminder_day
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_reports.setup_financial_year_opening_form
msgid "Reminder"
msgstr "แจ้งเตือน"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_annotation__report_id
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__account_report_id
msgid "Report"
msgstr "รายงาน"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_line_form
msgid "Report Line"
msgstr "บรรทัดของรายงาน"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
msgid "Report Name"
msgstr "ชื่อรายงาน"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__report_options
msgid "Report Options"
msgstr "ตัวเลือกรายงาน"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Report lines mentioning the account code"
msgstr "บรรทัดรายงานระบุรหัสบัญชี"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_variant.xml:0
msgid "Report:"
msgstr "รายงาน:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Reporting"
msgstr "การรายงาน"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group__report_ids
msgid "Reports"
msgstr "รายงาน"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_line_form
msgid "Reset to Standard"
msgstr "รีเซ็ตเป็นมาตรฐาน"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_retained_earnings0
msgid "Retained Earnings"
msgstr "กำไรสะสม"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_return_investment0
msgid "Return on investments (net profit / assets)"
msgstr "ผลตอบแทนจากการลงทุน (กำไรสุทธิ / สินทรัพย์)"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_income0
#: model:account.report.line,name:account_reports.account_financial_report_revenue0
msgid "Revenue"
msgstr "รายได้"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__reversal_date
msgid "Reversal Date"
msgstr "วันที่กลับรายการ"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
msgid "Reversal of Grouped Deferral Entry of %s"
msgstr "การกลับรายการการรายการที่รอตัดบัญชีแบบกลุ่มของ %s"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
msgid "Reversal of: %s"
msgstr "การกลับรายการของ:%s"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_report_search
msgid "Root Report"
msgstr "รายงานหลัก"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group__rule_ids
msgid "Rules"
msgstr "กฏ"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
msgid "Same Period Last Year"
msgstr "ช่วงเวลาเดียวกันปีที่แล้ว"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/search_bar/search_bar.xml:0
msgid "Search..."
msgstr "ค้นหา..."

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
msgid "Sections"
msgstr "ส่วน"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_partner_ledger.py:0
msgid "Send"
msgstr "ส่ง"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report__send_and_print_values
msgid "Send And Print Values"
msgstr "ส่งและพิมพ์ค่า"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__send_mail_readonly
msgid "Send Mail Readonly"
msgstr "ส่งเมลแบบอ่านอย่างเดียว"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_partner_ledger.py:0
msgid "Send Partner Ledgers"
msgstr ""

#. module: account_reports
#: model:ir.actions.server,name:account_reports.ir_cron_account_report_send_ir_actions_server
msgid "Send account reports automatically"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_move.py:0
msgid "Send tax report: %s"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/account_report_send.py:0
msgid "Sending statements"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget__sequence
msgid "Sequence"
msgstr "ลำดับ"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_sales_report.py:0
msgid "Services"
msgstr "บริการ"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_journal_report_audit_move_line_tree
msgid "Set as Checked"
msgstr "ตั้งค่าเป็นตรวจสอบแล้ว"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_st_cash_forecast0
msgid "Short term cash forecast"
msgstr "ประมาณการเงินสดระยะสั้น"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/filter_extra_options.xml:0
msgid "Show Account"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_budgets.xml:0
msgid "Show All Accounts"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/filter_extra_options.xml:0
msgid "Show Currency"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__show_warning_move_id
msgid "Show Warning Move"
msgstr "แสดงคำเตือนการเคลื่อนย้าย"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__account_report_send__mode__single
msgid "Single Recipient"
msgstr "ผู้รับรายเดียว"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/warnings.xml:0
msgid "Some"
msgstr "บาง"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/tax_report/warnings.xml:0
msgid "Some journal items appear to point to obsolete report lines."
msgstr "รายการสมุดรายวันบางรายการดูเหมือนจะชี้ไปยังบรรทัดรายงานที่ล้าสมัย"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_date.xml:0
msgid "Specific Date"
msgstr "วันที่ระบุ"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_res_company__account_representative_id
msgid ""
"Specify an Accounting Firm that will act as a representative when exporting "
"reports."
msgstr "ระบุสำนักงานบัญชีที่จะทำหน้าที่เป็นตัวแทนในการส่งออกรายงาน"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
msgid "Split Horizontally"
msgstr "แยกตามแนวนอน"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report__tax_closing_start_date
msgid "Start Date"
msgstr "วันที่เริ่ม"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_tax_periodicity_reminder_day
msgid "Start from"
msgstr "เริ่มต้นจาก"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "Starting Balance"
msgstr "ยอดเงินเริ่มต้น"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/account_report_send.py:0
msgid "Statements are being sent in the background."
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line/popover/debug_popover.xml:0
msgid "Subformula"
msgstr "สูตรย่อย"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__mail_subject
msgid "Subject"
msgstr "หัวเรื่อง"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_send_form
msgid "Subject..."
msgstr "หัวเรื่อง..."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "T: %s"
msgstr "T: %s"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_partner.xml:0
msgid "Tags"
msgstr "แท็ก"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_general_ledger.py:0
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_journal_report_taxes_summary
msgid "Tax Amount"
msgstr "ยอดภาษี"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_journal_report_taxes_summary
msgid "Tax Applied"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_bank_statement_line__tax_closing_alert
#: model:ir.model.fields,field_description:account_reports.field_account_move__tax_closing_alert
msgid "Tax Closing Alert"
msgstr "การแจ้งเตือนการปิดภาษี"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_bank_statement_line__tax_closing_report_id
#: model:ir.model.fields,field_description:account_reports.field_account_move__tax_closing_report_id
msgid "Tax Closing Report"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_general_ledger.py:0
msgid "Tax Declaration"
msgstr "การแจ้งภาษี"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "Tax Grids"
msgstr "ตารางภาษี"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__vat
msgid "Tax ID"
msgstr "เลขประจำตัวผู้เสียภาษี"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.company_information
msgid "Tax ID:"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Tax Paid Adjustment"
msgstr "การปรับภาษีที่ชำระ"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/tax_report/filters/filter_date.xml:0
msgid "Tax Period"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Tax Received Adjustment"
msgstr "การปรับภาษีที่ได้รับ"

#. module: account_reports
#: model:mail.activity.type,name:account_reports.tax_closing_activity_type
#: model:mail.activity.type,summary:account_reports.tax_closing_activity_type
#: model_terms:ir.ui.view,arch_db:account_reports.view_move_form_vat_return
msgid "Tax Report"
msgstr "รายงานภาษี"

#. module: account_reports
#: model:mail.activity.type,name:account_reports.mail_activity_type_tax_report_to_be_sent
msgid "Tax Report Ready"
msgstr "รายงานภาษีพร้อมแล้ว"

#. module: account_reports
#: model:ir.actions.client,name:account_reports.action_account_report_gt
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_gt
msgid "Tax Return"
msgstr "การคืนภาษี"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Tax Return Periodicity"
msgstr "ระยะเวลาการคืนภาษี"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_tax_unit
#: model_terms:ir.ui.view,arch_db:account_reports.view_tax_unit_form
msgid "Tax Unit"
msgstr "หน่วยภาษี"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_tax_unit.xml:0
msgid "Tax Unit:"
msgstr "หน่วยภาษี:"

#. module: account_reports
#: model:ir.actions.act_window,name:account_reports.action_view_tax_units
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_tax_unit_ids
#: model:ir.ui.menu,name:account_reports.menu_view_tax_units
msgid "Tax Units"
msgstr "หน่วยภาษี"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_mail_activity__account_tax_closing_params
msgid "Tax closing additional params"
msgstr ""

#. module: account_reports
#: model:mail.activity.type,summary:account_reports.mail_activity_type_tax_report_to_pay
msgid "Tax is ready to be paid"
msgstr "ภาษีก็พร้อมที่จะชำระ"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__mail_activity_type__category__tax_report
msgid "Tax report"
msgstr "รายงานภาษี"

#. module: account_reports
#: model:mail.activity.type,summary:account_reports.mail_activity_type_tax_report_to_be_sent
msgid "Tax report is ready to be sent to the administration"
msgstr "รายงานภาษีพร้อมส่งฝ่ายบริหารแล้ว"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/res_company.py:0
msgid "Tax return"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "Taxes"
msgstr "ภาษี"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
msgid "Taxes Applied"
msgstr "ภาษี"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_tax_unit__fpos_synced
msgid ""
"Technical field indicating whether Fiscal Positions exist for all companies "
"in the unit"
msgstr ""
"ฟิลด์ทางเทคนิคที่ระบุว่าสถานะทางการเงินมีอยู่สำหรับทุกบริษัทในหน่วยหรือไม่"

#. module: account_reports
#: model:ir.model,name:account_reports.model_ir_actions_account_report_download
msgid "Technical model for accounting report downloads"
msgstr "โมเดลทางเทคนิคสำหรับการดาวน์โหลดรายงานทางบัญชี"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/ellipsis/ellipsis.js:0
msgid "Text copied"
msgstr "คัดลอกข้อความแล้ว"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "The Accounts Coverage Report is not available for this report."
msgstr "รายงานความครอบคลุมของบัญชีไม่พร้อมใช้งานสำหรับรายงานนี้"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_name/popover_line/annotation_popover_line.js:0
msgid "The annotation shouldn't have an empty value."
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_annotation__text
msgid "The annotation's content."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_move.py:0
msgid ""
"The attachments of the tax report can be found on the <a href='#' data-oe-"
"model='account.move' data-oe-id='%s'>closing entry</a> of the representative"
" company."
msgstr ""
"เอกสารแนบของรายงานภาษีสามารถดูได้จาก<a href='#' data-oe-model='account.move'"
" data-oe-id='%s'>รายการปิดบัญชี</a>ของบริษัทตัวแทน"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_partner_ledger.py:0
#, python-format
msgid "The column '%s' is not available for this report."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_tax.py:0
msgid ""
"The country detected for this VAT number does not match the one set on this "
"Tax Unit."
msgstr ""
"ประเทศที่ตรวจพบสำหรับหมายเลข VAT นี้ไม่ตรงกับประเทศที่ตั้งไว้ในหน่วยภาษีนี้"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_tax_unit__country_id
msgid ""
"The country in which this tax unit is used to group your companies' tax "
"reports declaration."
msgstr "ประเทศที่ใช้หน่วยภาษีนี้ เพื่อจัดกลุ่มการรายงานภาษีของบริษัทของคุณ"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_multicurrency_revaluation_report.py:0
msgid "The currency rate cannot be equal to zero"
msgstr "อัตราสกุลเงินไม่สามารถเท่ากับศูนย์"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/warnings.xml:0
msgid "The current balance in the"
msgstr "ยอดคงเหลือปัจจุบันใน"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid ""
"The currently selected dates don't match a tax period. The closing entry "
"will be created for the closest-matching period according to your "
"periodicity setup."
msgstr ""
"วันที่ที่เลือกในปัจจุบันไม่ตรงกับช่วงของภาษี "
"รายการปิดบัญชีจะถูกสร้างขึ้นสำหรับช่วงที่ตรงกันมากที่สุดตามการตั้งค่าช่วงระยะเวลาของคุณ"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/deferred_reports/warnings.xml:0
msgid "The entry that will be generated will take them into account."
msgstr "รายการที่จะถูกสร้างขึ้นจะนำมาพิจารณาด้วย"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_report_annotation__fiscal_position_id
msgid "The fiscal position used while annotating."
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_report_annotation__line_id
msgid "The id of the annotated line."
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_report_annotation__report_id
msgid "The id of the annotated report."
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_tax_unit__vat
msgid "The identifier to be used when submitting a report for this unit."
msgstr "ตัวระบุที่จะใช้เมื่อส่งรายงานสำหรับหน่วยนี้"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_tax.py:0
msgid "The main company of a tax unit has to be part of it."
msgstr "บริษัทหลักของหน่วยภาษีจะต้องเป็นส่วนหนึ่งของหน่วยภาษี"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_res_company__account_tax_unit_ids
msgid "The tax units this company belongs to."
msgstr "หน่วยภาษีที่บริษัทนี้เป็นเจ้าของ"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "The used operator is not supported for this expression."
msgstr "ตัวดำเนินการที่ใช้ไม่รองรับตัวสั่งงานนี้"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/warnings.xml:0
msgid "There are"
msgstr "มี"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/account_report_send.py:0
msgid ""
"There are currently reports waiting to be sent, please try again later."
msgstr "ขณะนี้มีรายงานที่กำลังรอการส่ง กรุณาลองอีกครั้งในภายหลัง"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/account_report.xml:0
msgid "There is no data to display for the given filters."
msgstr "ไม่มีข้อมูลที่จะแสดงสำหรับตัวกรองที่กำหนด"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"This account exists in the Chart of Accounts but is not mentioned in any "
"line of the report"
msgstr "บัญชีนี้มีอยู่ในผังบัญชีแต่ไม่ได้กล่าวถึงในบรรทัดใดของรายงาน"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"This account is reported in a line of the report but does not exist in the "
"Chart of Accounts"
msgstr "บัญชีนี้ได้รับการรายงานในบรรทัดของรายงาน แต่ไม่มีอยู่ในผังบัญชี"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "This account is reported in multiple lines of the report"
msgstr "บัญชีนี้มีการรายงานในหลายบรรทัดของรายงาน"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "This account is reported multiple times on the same line of the report"
msgstr "บัญชีนี้ถูกรายงานหลายครั้งในบรรทัดเดียวกันของรายงาน"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid ""
"This allows you to choose the position of totals in your financial reports."
msgstr "ซึ่งจะทำให้คุณสามารถเลือกตำแหน่งของผลรวมในรายงานทางการเงินของคุณได้"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/warnings.xml:0
msgid ""
"This company is part of a tax unit. You're currently not viewing the whole "
"unit."
msgstr "บริษัทนี้เป็นส่วนหนึ่งของหน่วยภาษี ขณะนี้คุณไม่ได้ดูหน่วยทั้งหมด"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/widgets/account_report_x2many/account_report_x2many.js:0
msgid ""
"This line and all its children will be deleted. Are you sure you want to "
"proceed?"
msgstr ""
"บรรทัดนี้และรายการย่อยทั้งหมดจะถูกลบออก "
"คุณแน่ใจหรือไม่ว่าต้องการดำเนินการต่อ?"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_line_form
msgid "This line uses a custom user-defined 'Group By' value."
msgstr "บรรทัดนี้ใช้ค่า 'จัดกลุ่มโดย' ที่ผู้ใช้กำหนดเอง"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
msgid "This option hides lines with a value of 0"
msgstr "ตัวเลือกนี้จะซ่อนบรรทัดที่มีค่า 0"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "This report already has a menuitem."
msgstr "รายงานนี้มีรายการเมนูอยู่แล้ว"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/tax_report/warnings.xml:0
msgid ""
"This report contains inconsistencies. The affected lines are marked with a "
"warning."
msgstr ""
"รายงานนี้มีข้อมูลที่ไม่สอดคล้องกัน "
"บรรทัดที่ได้รับผลกระทบจะมีเครื่องหมายคำเตือน"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/multicurrency_revaluation_report/warnings.xml:0
msgid "This report only displays the data of the active company."
msgstr "รายงานนี้แสดงเฉพาะข้อมูลของบริษัทที่ใช้งานอยู่เท่านั้น"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
msgid ""
"This report uses report-specific code.\n"
"                        You can customize it manually, but any change in the parameters used for its computation could lead to errors."
msgstr ""
"รายงานนี้ใช้รหัสเฉพาะรายงาน\n"
"                        คุณสามารถปรับแต่งด้วยตนเองได้ แต่การเปลี่ยนแปลงใดๆ ในพารามิเตอร์ที่ใช้ในการคำนวณอาจทำให้เกิดข้อผิดพลาดได้"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/warnings.xml:0
msgid ""
"This report uses the CTA conversion method to consolidate multiple companies using different currencies,\n"
"        which can lead the report to be unbalanced."
msgstr ""
"รายงานนี้ใช้การแปลง CTA เพื่อรวมบริษัทหลายแห่งโดยใช้สกุลเงินที่แตกต่างกัน\n"
"        ซึ่งอาจนำไปสู่การรายงานที่ไม่สมดุลได้"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "This subformula references an unknown expression: %s"
msgstr "สูตรย่อยนี้อ้างอิงถึงนิพจน์ที่ไม่รู้จัก:%s"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"This tag is reported in a line of the report but is not linked to any "
"account of the Chart of Accounts"
msgstr ""
"แท็กนี้ได้รับการรายงานในบรรทัดของรายงาน แต่ไม่ได้เชื่อมโยงกับบัญชีใดๆ "
"ของผังบัญชี"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_move_form_vat_return
msgid ""
"This tax closing entry is posted, but the tax lock date is earlier than the "
"covered period's last day. You might need to reset it to draft and refresh "
"its content, in case other entries using taxes have been posted in the "
"meantime."
msgstr ""
"รายการปิดบัญชีภาษีนี้ถูกลงรายการบัญชีแล้ว "
"แต่วันที่ล็อคภาษีอยู่ก่อนหน้าวันสุดท้ายของรอบระยะเวลาที่ครอบคลุม "
"คุณอาจต้องรีเซ็ตเป็นแบบร่างและรีเฟรชเนื้อหา "
"ในกรณีที่มีการลงรายการรายการอื่นๆ ที่ใช้ภาษีในระหว่างนี้"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_date.xml:0
msgid "Today"
msgstr "วันนี้"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
#: code:addons/account_reports/models/account_general_ledger.py:0
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/models/account_partner_ledger.py:0
#: code:addons/account_reports/models/account_sales_report.py:0
#: model:account.report.column,name:account_reports.aged_payable_report_total
#: model:account.report.column,name:account_reports.aged_receivable_report_total
msgid "Total"
msgstr "รวม"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Total %s"
msgstr "รวม %s"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Trade Partners"
msgstr "พาร์ทเนอร์คู่ค้า"

#. module: account_reports
#: model:account.report.line,name:account_reports.transaction_without_statement
msgid "Transactions without statement"
msgstr "ธุรกรรมที่ไม่มีใบแจ้งยอด"

#. module: account_reports
#: model:account.report,name:account_reports.trial_balance_report
#: model:ir.actions.client,name:account_reports.action_account_report_coa
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_coa
msgid "Trial Balance"
msgstr "งบทดลอง"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_trial_balance_report_handler
msgid "Trial Balance Custom Handler"
msgstr "ตัวจัดการงบทดลองแบบกำหนดเอง"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_sales_report.py:0
msgid "Triangular"
msgstr "สามเหลี่ยม"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Trying to dispatch an action on a report not compatible with the provided "
"options."
msgstr ""
"กำลังพยายามส่งการดำเนินการกับรายงานที่ไม่เข้ากันกับตัวเลือกที่ให้ไว้อยู่"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Trying to expand a group for a line which was not generated by a report "
"line: %s"
msgstr "กำลังพยายามขยายกลุ่มสำหรับบรรทัดที่ไม่ได้สร้างโดยบรรทัดรายงาน: %s"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Trying to expand a line without an expansion function."
msgstr "กำลังพยายามขยายบรรทัดโดยไม่มีฟังก์ชันการขยาย"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Trying to expand groupby results on lines without a groupby value."
msgstr "กำลังพยายามขยายผลลัพธ์ groupby บนบรรทัด โดยไม่มีค่า groupby"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_unaffected_earnings0
msgid "Unallocated Earnings"
msgstr "รายได้ที่ไม่ได้ปันส่วน"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
msgid "Unfold All"
msgstr "ขยายทั้งหมด"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Unknown"
msgstr "ไม่ทราบ"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_partner_ledger.py:0
#: code:addons/account_reports/models/account_sales_report.py:0
msgid "Unknown Partner"
msgstr "ไม่ทราบพาร์ทเนอร์"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Unknown bound criterium: %s"
msgstr "ไม่ทราบเกณฑ์ที่ผูกมัด: %s"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Unknown date scope: %s"
msgstr "ไม่ทราบขอบเขตวันที่: %s"

#. module: account_reports
#: model:account.report,name:account_reports.multicurrency_revaluation_report
#: model:ir.actions.client,name:account_reports.action_account_report_multicurrency_revaluation
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_multicurrency_revaluation
msgid "Unrealized Currency Gains/Losses"
msgstr "กำไร/ขาดทุนจากสกุลเงินที่ไม่เกิดขึ้น"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filter_extra_options_template
msgid "Unreconciled Entries"
msgstr "รายการที่ไม่ได้กระทบยอด"

#. module: account_reports
#: model:account.report.column,name:account_reports.account_financial_report_ec_sales_vat
msgid "VAT Number"
msgstr "หมายเลขภาษี"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.setup_financial_year_opening_form
msgid "VAT Periodicity"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line/popover/debug_popover.xml:0
msgid "Value"
msgstr "ค่า"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_move.py:0
msgid "Vat closing from %(date_from)s to %(date_to)s"
msgstr "การปิด Vat จาก %(date_from)s ถึง %(date_to)s"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_journal_report_audit_move_line_tree
msgid "View"
msgstr "ดู"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "View Bank Statement"
msgstr "ดูรายการเดินบัญชีธนาคาร"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_cell/popover/carryover_popover.xml:0
msgid "View Carryover Lines"
msgstr "ดูบรรทัดยกยอด"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "View Journal Entry"
msgstr "ดูรายการสมุดรายวัน"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/models/account_sales_report.py:0
#: model_terms:ir.ui.view,arch_db:account_reports.view_journal_report_audit_move_line_tree
msgid "View Partner"
msgstr "ดูพาร์ทเนอร์"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/account_report_send.py:0
msgid "View Partner(s)"
msgstr "ดูพาร์ทเนอร์"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "View Payment"
msgstr "ดูการชำระเงิน"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__warnings
msgid "Warnings"
msgstr "คำเตือน"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid ""
"When ticked, totals and subtotals appear below the sections of the report"
msgstr "เมื่อทำเครื่องหมาย ผลรวมและผลรวมย่อยจะแสดงด้านล่างส่วนของรายงาน"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_res_company__totals_below_sections
#: model:ir.model.fields,help:account_reports.field_res_config_settings__totals_below_sections
msgid ""
"When ticked, totals and subtotals appear below the sections of the report."
msgstr "เมื่อทำเครื่องหมาย ผลรวมและผลรวมย่อยจะแสดงด้านล่างส่วนของรายงาน"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_account__exclude_provision_currency_ids
msgid ""
"Whether or not we have to make provisions for the selected foreign "
"currencies."
msgstr "เราจะต้องสำรองสกุลเงินต่างประเทศที่เลือกไว้หรือไม่"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filter_extra_options_template
msgid "With Draft Entries"
msgstr "ด้วยรายการฉบับร่าง"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_general_ledger.py:0
msgid "Wrong ID for general ledger line to expand: %s"
msgstr "ID ไม่ถูกต้องสำหรับรายการบัญชีแยกประเภททั่วไปที่จะขยาย: %s"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_partner_ledger.py:0
msgid "Wrong ID for partner ledger line to expand: %s"
msgstr "รหัสไม่ถูกต้องสำหรับรายการบัญชีแยกประเภทพาร์ทเนอร์ที่จะขยาย: %s"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Wrong format for if_other_expr_above/if_other_expr_below formula: %s"
msgstr ""
"รูปแบบไม่ถูกต้องสำหรับสูตร if_other_expr_above/if_other_expr_below: %s"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "XLSX"
msgstr "XLSX"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Year"
msgstr "ปี"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/static/src/components/account_report/line_cell/popover/edit_popover.xml:0
msgid "Yes"
msgstr "ใช่"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/multicurrency_revaluation_report/warnings.xml:0
msgid "You are using custom exchange rates."
msgstr "คุณกำลังใช้อัตราแลกเปลี่ยนที่กำหนดเอง"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_move.py:0
msgid "You can't open a tax report from a move without a VAT closing date."
msgstr "คุณไม่สามารถเปิดรายงานภาษีจากการย้ายโดยไม่มีวันปิดบัญชี VAT ได้"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_move_line.py:0
msgid "You cannot add taxes on a tax closing move line."
msgstr "คุณไม่สามารถเพิ่มภาษีในรายการที่ย้ายการปิดบัญชีภาษีได้"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
msgid ""
"You cannot generate entries for a period that does not end at the end of the"
" month."
msgstr "คุณไม่สามารถสร้างรายการในช่วงเวลาที่ไม่สิ้นสุดเมื่อสิ้นเดือนได้"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
msgid "You cannot generate entries for a period that is locked."
msgstr "คุณไม่สามารถสร้างรายการในช่วงเวลาที่ถูกล็อคได้"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_move.py:0
msgid ""
"You cannot reset this closing entry to draft, as another closing entry has "
"been posted at a later date."
msgstr ""
"คุณไม่สามารถรีเซ็ตรายการปิดนี้เป็นแบบร่างได้ "
"เนื่องจากมีการประกาศรายการปิดอื่นในภายหลัง"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_move.py:0
msgid ""
"You cannot reset this closing entry to draft, as it would delete carryover "
"values impacting the tax report of a locked period. To do this, you first "
"need to modify you tax return lock date."
msgstr ""
"คุณไม่สามารถรีเซ็ตรายการปิดบัญชีนี้เป็นแบบร่างได้ "
"เนื่องจากจะถูกลบค่ายกยอดที่ส่งผลกระทบต่อรายงานภาษีของรอบระยะเวลาที่ถูกล็อคไว้"
" ในการดำเนินการนี้ คุณต้องแก้ไขวันที่ล็อกการคืนภาษีก่อน"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_multicurrency_revaluation_report.py:0
msgid "You need to activate more than one currency to access this report."
msgstr "คุณต้องเปิดใช้งานมากกว่าหนึ่งสกุลเงินเพื่อเข้าถึงรายงานนี้"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid ""
"You're about the generate the closing entries of multiple companies at once."
" Each of them will be created in accordance with its company tax "
"periodicity."
msgstr ""
"คุณกำลังจะสร้างรายการปิดบัญชีของบริษัทหลายแห่งพร้อมกัน "
"แต่ละรายการจะถูกสร้างขึ้นตามระยะเวลาการเสียภาษีของบริษัทนั้นๆ"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.journal_report_pdf_export_main
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_main
msgid "[Draft]"
msgstr "[ฉบับร่าง]"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/sales_report/warnings.xml:0
msgid "addressed to"
msgstr "จ่าหน้าถึง"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/tax_report/warnings.xml:0
msgid "and correct their tax tags if necessary."
msgstr "และแก้ไขแท็กภาษีหากจำเป็น"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__year
msgid "annually"
msgstr "ประจำปี"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_reports.setup_financial_year_opening_form
msgid "days after period"
msgstr "วันหลังจากงวด"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/warnings.xml:0
msgid "doesn't match the balance of your"
msgstr "ไม่ตรงกับยอดคงเหลือของคุณ"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__2_months
msgid "every 2 months"
msgstr "ทุก 2 เดือน"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__4_months
msgid "every 4 months"
msgstr "ทุก 4 เดือน"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/warnings.xml:0
msgid "have a starting balance different from the previous ending balance."
msgstr "มียอดคงเหลือเริ่มต้นแตกต่างจากยอดคงเหลือสุดท้ายก่อนหน้า"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_cell/popover/carryover_popover.xml:0
msgid "in the next period."
msgstr "ในงวดต่อไป"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/sales_report/warnings.xml:0
msgid "invoices"
msgstr "การแจ้งหนี้"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/warnings.xml:0
msgid "journal items"
msgstr "รายการสมุดรายวัน"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/warnings.xml:0
msgid "last bank statement"
msgstr "ใบแจ้งยอดธนาคารล่าสุด"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__monthly
msgid "monthly"
msgstr "รายเดือน"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "n/a"
msgstr "ไม่มีข้อมูล"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/sales_report/warnings.xml:0
msgid "partners"
msgstr "พาร์ทเนอร์"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/warnings.xml:0
msgid "prior or included in this period."
msgstr "ก่อนหน้าหรือรวมอยู่ในช่วงนี้"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__trimester
msgid "quarterly"
msgstr "รายไตรมาส"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__semester
msgid "semi-annually"
msgstr "ทุกครึ่งปี"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/warnings.xml:0
msgid "statements"
msgstr "รายการเดินบัญชี"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_tax_unit_form
msgid "synchronize fiscal positions"
msgstr "ซิงโครไนซ์สถานะทางการเงิน"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_tax.py:0
msgid "tax unit [%s]"
msgstr "หน่วยภาษี [%s]"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/sales_report/warnings.xml:0
msgid "that are not established abroad."
msgstr "ที่ไม่ได้จัดตั้งขึ้นในต่างประเทศ"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_date.xml:0
msgid "to"
msgstr "ถึง"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/warnings.xml:0
msgid "unposted Journal Entries"
msgstr "รายการสมุดรายวันที่ไม่ได้ผ่านรายการ"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_cell/popover/carryover_popover.xml:0
msgid "were carried over to this line from previous period."
msgstr "ได้ถูกยกมาสู่บรรทัดนี้จากงวดที่แล้ว"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/warnings.xml:0
msgid "which don't originate from a bank statement nor payment."
msgstr "ซึ่งไม่ได้มาจากใบแจ้งยอดธนาคารหรือการชำระเงิน"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/sales_report/warnings.xml:0
msgid "who are not established in any of the EC countries."
msgstr "ที่ไม่ได้จัดตั้งขึ้นในประเทศ EC ใด"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_cell/popover/carryover_popover.xml:0
msgid "will be carried over to"
msgstr "จะถูกยกยอดไปยัง"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_cell/popover/carryover_popover.xml:0
msgid "will be carried over to this line in the next period."
msgstr "จะถูกโอนไปยังบรรทัดนี้ในงวดหน้า"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/sales_report/warnings.xml:0
msgid "without a valid intra-community VAT number."
msgstr "โดยไม่มีหมายเลข VAT ภายในคอมมูนิตี้ที่ถูกต้อง"

#. module: account_reports
#: model:mail.template,subject:account_reports.email_template_customer_statement
msgid ""
"{{ (object.company_id or object._get_followup_responsible().company_id).name"
" }} Statement - {{ object.commercial_company_name }}"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/multicurrency_revaluation_report/warnings.xml:0
msgid "⇒ Reset to Odoo’s Rate"
msgstr "⇒ รีเซ็ตเป็นอัตราของ Odoo"
